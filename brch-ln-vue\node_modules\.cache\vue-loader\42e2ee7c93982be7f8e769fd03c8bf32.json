{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addHeatAccount.vue?vue&type=style&index=0&id=840c7782&lang=less&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addHeatAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5teXRhYmxlIC5pdnUtdGFibGUtY2VsbHsKICAgIHBhZGRpbmctbGVmdDogNXB4OwogICAgcGFkZGluZy1yaWdodDogNXB4OwogICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwogICAgd2hpdGUtc3BhY2U6IG5vcm1hbDsKICAgIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsKICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7Cn0KCi5hY2NvdW50RXMgLmZpbHRlci1kaXZpZGVyIHsKICAgIG1hcmdpbjogMHB4OwogICAgdGV4dC1hbGlnbjogY2VudGVyOwp9Ci5hY2NvdW50RXMgLmhlYWRlci1iYXItc2hvdyB7CiAgICBtYXgtaGVpZ2h0OiAzMDBweDsKICAgIHBhZGRpbmctdG9wOiAxNHB4OwogICAgb3ZlcmZsb3c6IGluaGVyaXQ7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZWFlYzsKfQouYWNjb3VudEVzIC5oZWFkZXItYmFyLWhpZGUgewogICAgbWF4LWhlaWdodDogMDsKICAgIHBhZGRpbmctdG9wOiAwOwogICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIGJvcmRlci1ib3R0b206IDA7Cn0KCgoubXl0YWJsZSAubXlzcGFuewogICAgd2lkdGg6IDEwMCU7CiAgICBoZWlnaHQ6IDIwcHg7CiAgICBkaXNwbGF5OmJsb2NrCn0KLm15dGFibGUgLmVycm9yU3RsZXsKICAgIHdpZHRoOiAxMDAlOwogICAgaGVpZ2h0OiAyMHB4OwogICAgZGlzcGxheTpibG9jazsKICAgIGNvbG9yOnJlZDsKfQoubXl0YWJsZSAuaXZ1LXRhYmxlLXJvd3sKICAgIG1heC1oZWlnaHQ6IDM3MHB4IWltcG9ydGFudDsKfQo="}, {"version": 3, "sources": ["addHeatAccount.vue"], "names": [], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addHeatAccount.vue", "sourceRoot": "src/view/account", "sourcesContent": ["<style lang=\"less\">\r\n    .mytable .ivu-table-cell{\r\n        padding-left: 5px;\r\n        padding-right: 5px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: normal;\r\n        word-break: break-all;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .accountEs .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .accountEs .header-bar-show {\r\n        max-height: 300px;\r\n        padding-top: 14px;\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .accountEs .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n\r\n\r\n    .mytable .myspan{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block\r\n    }\r\n    .mytable .errorStle{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block;\r\n        color:red;\r\n    }\r\n    .mytable .ivu-table-row{\r\n        max-height: 370px!important;\r\n    }\r\n</style>\r\n<template>\r\n    <div>\r\n        <div class=\"accountEs\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n                    <Row>\r\n                        <!-- @on-change='accountnoChange' -->\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.accountno\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{ item.name }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"关键字:\" prop=\"heatUseBody\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"accountObj.heatUseBody\" placeholder=\"用能主体关键字模糊查询\" :style=\"formItemWidth\" />\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.company\" @on-change=\"selectChange(accountObj.company)\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"accountObj.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly :style=\"formItemWidth\"/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <div  align=\"right\">\r\n                        <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n                        <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n                    </div>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <div>\r\n                <Row>\r\n                    <Col span=\"12\">\r\n                        <Page size=\"small\" :total=\"pageTotal\" :current=\"pageNum\" :page-size=\"pageSize\" show-elevator show-sizer show-total\r\n                              placement=\"top\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n                    </Col>\r\n                    <Col span=\"12\">\r\n                        <div align=\"right\" class=\"account\">\r\n                            <Button type=\"primary\" @click=\"addNewCoalAccount\">新增</Button>\r\n                            <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n                            <Button type=\"error\" @click=\"remove\">删除</Button>\r\n                            <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n                                <Button type='info' style=\"margin-left: 5px\">加入归集单\r\n                                    <Icon type='ios-arrow-down'></Icon>\r\n                                </Button>\r\n                                <DropdownMenu slot='list'>\r\n                                    <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n                                    <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n                                </DropdownMenu>\r\n                            </Dropdown>\r\n                            <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n                            <Button type=\"primary\" @click=\"loadTemplate\">导入模板下载</Button>\r\n                            <Upload style=\"float:right;\" :on-format-error=\"handleFormatError\"\r\n                                    :before-upload='onExcelUpload' :on-progress=\"handleProgress\"\r\n                                    :on-success=\"handleUploadSuccess\" :max-size=\"10240\" action=\"_blank\"\r\n                                    accept=\".csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\r\n                                    :format=\"['xls','xlsx']\">\r\n                                <Button icon='ios-cloud-upload'>导入</Button>\r\n                            </Upload>\r\n                        </div>\r\n                    </Col>\r\n                </Row>\r\n            </div>\r\n            <Table ref=\"accountEsTable\"\r\n                   border\r\n                   :columns=\"tbAccount.tailColumn\"\r\n                   :data=\"tbAccount.data\"\r\n                   class=\"mytable\">\r\n                <!--备注-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=500 v-model=\"editremark\" :ref=\"'remark'+index+10\" type=\"text\" @on-blur=\"setremark\"\r\n                               v-if=\"editIndex === index && columnsIndex === 10\"/>\r\n                        <Tooltip placement=\"left\" max-width=\"600\" v-else>\r\n                            <span :class=\"myStyle[index].remark\" style=\"width: 60px\" @click=\"selectCall(row,index,10,'remark')\">\r\n                                {{ ellipsis(row.remark) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                    <div v-else>\r\n                        <Tooltip placement=\"bottom\" max-width=\"200\">\r\n                            <span>{{ ellipsis(row.remark)}}</span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--用能主体-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"heatUseBody\" v-if=\"row.total == null\">\r\n                    <div>\r\n                        <Input :maxlength=100 v-model=\"editHeatUseBody\" :ref=\"'heatUseBody'+index+3\" type=\"text\" @on-blur=\"setHeatUseBody\"\r\n                               v-if=\"editIndex === index && columnsIndex === 3\"/>\r\n                            <!-- <span v-else :class=\"myStyle[index].heatUseBody\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\"  @click=\"selectCall(row,index,3,'heatUseBody')\">{{ ellipsis(row.heatUseBody) }}</span> -->\r\n\r\n                        <Tooltip placement=\"right\" max-width=\"200\" v-else>\r\n                            <span :class=\"myStyle[index].heatUseBody\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,3,'heatUseBody')\">\r\n                                {{ ellipsis(row.heatUseBody) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.heatUseBody }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--开始时间-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"startDate\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'startDate'+index+1\" type=\"text\" v-model=\"editStartDate\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 1\" />\r\n                    <span :class=\"myStyle[index].startDate\" @click=\"selectCall(row,index,1,'startDate')\" v-else>{{ row.startDate }}</span>\r\n                </template>\r\n                <!--结束时间-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"endDate\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'endDate'+index+2\" type=\"text\" v-model=\"editEndDate\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 2\" />\r\n                    <span :class=\"myStyle[index].endDate\" @click=\"selectCall(row,index,2,'endDate')\" v-else>{{ row.endDate }}</span>\r\n                </template>\r\n                <!--采暖面积-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"heatAreaSize\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'heatAreaSize'+index+4\" type=\"text\" v-model=\"editHeatAreaSize\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 4\" />\r\n                    <span :class=\"myStyle[index].heatAreaSize\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,4,'heatAreaSize')\" v-else>{{ row.heatAreaSize }}</span>\r\n                </template>\r\n                <!--热力-->\r\n                <!-- <template slot-scope=\"{ row, index }\" slot=\"heatAmount\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'heatAmount'+index+5\" type=\"text\" v-model=\"editHeatAmount\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 5\" />\r\n                    <span :class=\"myStyle[index].heatAmount\" @click=\"selectCall(row,index,5,'heatAmount')\" v-else>{{ row.heatAmount }}</span>\r\n                </template> -->\r\n                <!--单价-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"unitPrice\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'unitPrice'+index+6\" type=\"text\" v-model=\"editUnitPrice\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 6\" />\r\n                    <span :class=\"myStyle[index].unitPrice\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,6,'unitPrice')\" v-else>{{ row.unitPrice }}</span>\r\n                </template>\r\n                <!--票据类型-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"ticketImportType\" v-if=\"row.total == null\">\r\n                    <Select :ref=\"'ticketImportType'+index+7\" type=\"text\" v-model=\"editTicketType\" @on-change=\"setticketImportType\"\r\n                            v-if=\"editIndex === index && columnsIndex === 7\" transfer=\"true\">\r\n                        <Option value=\"专票\" label=\"专票\"></Option>\r\n                        <Option value=\"普票\" label=\"普票\"></Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].ticketImportType\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,7,'ticketImportType')\" v-else>{{ row.ticketImportType }}</span>\r\n                </template>\r\n                <!--专票税率-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"taxRateShow\" v-if=\"row.total == null\">\r\n                    <Select :ref=\"'taxRateShow'+index+8\" type=\"text\" v-model=\"editTaxRate\" @on-change=\"settaxrate\"\r\n                            v-if=\"editIndex === index && columnsIndex === 8\" transfer=\"true\">\r\n                        <Option value=\"1\">1</Option>\r\n                        <Option value=\"3\">3</Option>\r\n                        <Option value=\"6\">6</Option>\r\n                        <Option value=\"13\">13</Option>\r\n                        <Option value=\"16\">16</Option>\r\n                        <Option value=\"17\">17</Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].taxRateShow\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,8,'taxRateShow')\" v-else>{{ row.taxRateShow }}</span>\r\n                </template>\r\n                <!--其他费用-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"otherFee\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'otherFee'+index+9\" type=\"text\" v-model=\"editOtherMoney\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 9\" />\r\n                    <span v-else :class=\"myStyle[index].otherFee\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,9,'otherFee')\">{{ row.otherFee }}</span>\r\n                </template>\r\n            </Table>\r\n            <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n        </div>\r\n        <div>\r\n            <add-bill-per ref=\"addBillPer\"\r\n                          v-on:refreshList=\"refresh\"></add-bill-per >\r\n            <completed-pre-modal ref=\"completedPre\" v-on:refreshList=\"refresh\"></completed-pre-modal>\r\n            <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport {\r\n    _verify_StartDate1,\r\n    judgeNumber,\r\n    _verify_EndDate1,\r\n    _verify_PrevTotalReadings,\r\n    _verify_CurTotalReadings,\r\n    other_no_ammeteror_protocol,\r\n    self_no_ammeteror_protocol,\r\n    HFL_ammeteror,\r\n    judging_editability,\r\n    judging_editability1,\r\n    _verify_Money,\r\n    _calculateUsedReadings,\r\n    _calculateTotalReadings,\r\n    _calculateUnitPriceByUsedMoney,\r\n    _calculateAccountMoney,\r\n    _calculateQuotereadingsratio,\r\n    requiredFieldValidator,\r\n    countTaxamount,\r\n    calculateActualMoney,\r\n    judge_negate,\r\n    judge_recovery,\r\n    judge_yb,\r\n    unitpirceMin,\r\n    unitpirceMax\r\n} from '@/view/account/PowerAccountController';\r\n    import {\r\n        saveHeatAccount,\r\n        removeHeatAccount,\r\n        selectHeatIds\r\n    } from '@/api/coalHeatOilAccount';\r\n    import checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\n    import checkResult from \"@/view/account/check/checkResult\";\r\n    import alarmCheck from \"@/view/account/check/alarmCheck\";\r\n    import {getDates,testNumber,} from '@/view/account/powerAccountHelper';\r\n    import axios from '@/libs/api.request';\r\n    import SelectAmmeter from \"./selectAmmeter\";\r\n    import {_verify_FeeStartDate} from '@/view/account/PowerAccountEs';\r\n    import AddBillPer from \"./addHeatBillPreModal\";\r\n    import {reJoinBillpre} from '@/api/accountBillPer';\r\n    import {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\n    import CompletedPreModal from \"./completedPreModal\";\r\n    import indexData from '@/config/index'\r\n    import CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import {getUserdata,getUserByUserRole,getCountrysdata,getCountryByUserId} from '@/api/basedata/ammeter.js'\r\n    let dates=getDates();\r\n    export default {\r\n        name: 'addCoalAccount',\r\n        components: {alarmCheck, checkResult, checkResultAndResponse,CompletedPreModal, SelectAmmeter,AddBillPer,CountryModal},\r\n        data() {\r\n            let photo = (h, {row, index}) => {\r\n                let that = this\r\n                let str = ''\r\n                if (row.projectname != '小计' && row.projectname != '合计') {\r\n                    str = '上传附件'\r\n                }\r\n                return h(\"div\", [h(\"u\", {\r\n                    on: {\r\n                        click() {\r\n                            //打开弹出框\r\n                            if (row.projectname != '小计' && row.projectname != '合计') {\r\n                                that.uploadFile(row)\r\n                            }\r\n                        }\r\n                    }\r\n                }, str)]);\r\n            };\r\n            return {\r\n                submit:[],\r\n                submit2:[],\r\n                showCheckModel:false,\r\n                showJhModel:false,\r\n                showAlarmModel:false,\r\n                formItemWidth: widthstyle,\r\n                version:'',\r\n                dateList:dates,\r\n                filterColl: true,//搜索面板展开\r\n                editIndex: -1,//当前编辑行\r\n                columnsIndex:-1,//当前编辑列\r\n                myStyle:[],//样式\r\n                editStartDate:'',\r\n                editEndDate:'',\r\n                editHeatUseBody:'',\r\n                editHeatAreaSize:'',\r\n                editHeatAmount:'',\r\n                editUnitPrice:'',\r\n                editTicketType:'',\r\n                editTaxRate:'',\r\n                editOtherMoney:'',\r\n                spinShow:false,//遮罩\r\n                categorys:[],//描述类型\r\n                editremark:'',\r\n                accountStatus:[],\r\n                companies:[],\r\n                coalTypes: [],\r\n                coalUseTypes: [],\r\n                departments:[],\r\n                isAdmin:false,\r\n                company:null,//用户默认公司\r\n                country:null,//用户默认所属部门\r\n                countryName:null,//用户默认所属部门\r\n                accountObj:{\r\n                    accountno:dates[1].code,//期号,默认当前月\r\n                    company:\"\",//分公司\r\n                    country:\"\",//所属部门\r\n                    coalUseBody:null,//用能主体\r\n                    countryName: \"\",\r\n                },\r\n                tbAccount: {\r\n                    loading: true,\r\n                    columns: [],\r\n                    tailColumn: [\r\n                        {type: 'selection', width: 60, align: 'center',},\r\n                        {\r\n                            title: \"期号\",\r\n                            key: \"accountNo\",\r\n                            align: \"center\",\r\n                            width: 90,\r\n                        },\r\n                        {\r\n                            title: \"开始时间\",\r\n                            slot: \"startDate\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"结束时间\",\r\n                            slot: \"endDate\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"用能主体\",\r\n                            slot: \"heatUseBody\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"采暖面积(㎡)\",\r\n                            slot: \"heatAreaSize\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"热力(百万千焦)\",\r\n                            // slot: \"heatAmount\",\r\n                            key: \"heatAmount\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {\r\n                            title: \"单价(元/平方米)\",\r\n                            slot: \"unitPrice\",\r\n                            // key: \"unitPrice\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"票据类型\",\r\n                            slot: \"ticketImportType\",\r\n                            align: \"center\",\r\n                            width: 60,\r\n                        },\r\n                        {\r\n                            title: \"普票含税金额(元)\",\r\n                            key: \"ticketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票含税金额(元)\",\r\n                            key: \"taxTicketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票税率（%）\",\r\n                            slot: \"taxRateShow\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票税额\",\r\n                            key: \"taxAmount\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"其他(元)\",\r\n                            slot: \"otherFee\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"实缴费用(元)含税\",\r\n                            key: \"paidMoney\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        // {title: \"附件\", align: \"center\", render: photo, width: 100},\r\n                        {title: \"备注\", slot: \"remark\",align: \"center\", width: 150},\r\n                    ],\r\n                    data: [],\r\n                },\r\n                pageTotal: 0,\r\n                pageNum: 1,\r\n                pageSize: 10,//当前页\r\n            }\r\n        },\r\n        methods: {\r\n            selectChange(){\r\n                let that = this;\r\n                if (that.accountObj.company != undefined) {\r\n                    if(that.accountObj.company == \"-1\"){\r\n                        that.accountObj.country = -1;\r\n                        that.accountObj.countryName = null;\r\n                    }else{\r\n                        getCountryByUserId(that.accountObj.company).then(res => {\r\n                            if(res.data.departments.length != 0){\r\n                                that.accountObj.country = res.data.departments[0].id;\r\n                                that.accountObj.countryName = res.data.departments[0].name;\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            },\r\n            //选择所属部门开始\r\n            chooseResponseCenter() {\r\n                if(this.accountObj.company == null || this.accountObj.company == \"-1\" ){\r\n                    this.$Message.info(\"请先选择分公司\");return;\r\n                }\r\n                this.$refs.countryModal.choose(this.accountObj.company);//所属部门\r\n            },\r\n            getDataFromModal(data) {\r\n                this.accountObj.country = data.id;\r\n                this.accountObj.countryName = data.name;\r\n                //选择所属部门结束\r\n            },\r\n            getUserData(){\r\n                let that = this;\r\n                getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                    if(res.data.companies.length != 0){\r\n                        let companies = res.data.companies;\r\n                        if(res.data.companies[0].id == \"**********\"){\r\n                            companies = that.companies;\r\n                        }\r\n                        that.company = companies[0].id;\r\n                        that.accountObj.company = companies[0].id;\r\n                    }\r\n                    if(res.data.departments.length != 0){\r\n                        let departments = res.data.departments;\r\n                        if(res.data.companies[0].id == \"**********\" && that.departments.length != 0){\r\n                            departments = that.departments\r\n                        }\r\n                        that.country = departments[0].id;\r\n                        that.countryName = departments[0].name;\r\n                        that.accountObj.country = Number(departments[0].id);\r\n                        that.accountObj.countryName = departments[0].name;\r\n                    }\r\n\r\n                    that.pageNum = 1\r\n                    that.getAccountMessages();\r\n                });\r\n            },\r\n            searchList(){\r\n                if(this.accountObj.countryName == \"\"){\r\n                    this.accountObj.country = \"-1\";\r\n                }\r\n                this.pageNum = 1\r\n                this.getAccountMessages()\r\n            },\r\n            accountnoChange(){\r\n                this.searchList()\r\n            },\r\n            //点击保存\r\n            preserve() {\r\n                let dataL = this.$refs.accountEsTable.getSelection();\r\n                let b = false;\r\n                let array = [];\r\n                for (let i = 0; i < dataL.length; i ++) {\r\n                    b = true;\r\n                    array.push(dataL[i])\r\n                }\r\n                if(b){\r\n                    this.submitData(array);\r\n                }else {\r\n                    this.errorTips('没有可保存数据')\r\n                }\r\n            },\r\n            submitChange(indexList){\r\n                let data=[];\r\n                this.submit2.map((item,index)=>{\r\n                    indexList.map((item2)=>{\r\n                        if(index==item2){\r\n                            data.push(item)\r\n                        }\r\n                    })\r\n                });\r\n                this.submit=data\r\n            },\r\n\r\n            //提交数据\r\n            submitData(data){\r\n                let a = [];\r\n                let that=this;\r\n                if(data != null && data.length > 0){\r\n                    let number = 0;\r\n                    let submitData = [];\r\n                    let str = '';\r\n                    let accountno = this.accountObj.accountno;\r\n                    data.forEach(function (item) {\r\n                        // 校验数据\r\n                        if(item.id == null){\r\n                            item.accountno = accountno\r\n                        }\r\n                        a.push(item.id);\r\n                        submitData.push(item);\r\n                        number ++;\r\n                    });\r\n                    that.ids=a;\r\n                    if(str.length > 0){\r\n                        this.errorTips(str)\r\n                    }\r\n                    if(submitData.length > 0){\r\n                        saveHeatAccount(submitData).then((res) => {\r\n                            if (res.data.code == 0) {\r\n                                this.$Message.info({\r\n                                    content: '提示：成功保存 ' + res.data.num + ' 条数据',\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }\r\n                }\r\n            },\r\n            addNewCoalAccount() {\r\n                // let companyId = this.accountObj.company;\r\n                // let country = this.accountObj.country;\r\n                // if(companyId != null && country != null){\r\n                //     let obj = {\r\n                //         company:companyId,\r\n                //         country:country,\r\n                //         accountno:this.accountObj.accountno,\r\n                //         accountType:'1',\r\n                //         accountestype:1\r\n                //     }\r\n                // }else{\r\n                //     this.errorTips('请选择分公司和部门')\r\n                // }\r\n                const currentDate = new Date();\r\n                const currentYear = currentDate.getFullYear();\r\n                const currentMonth = currentDate.getMonth() + 1;\r\n                if (null == this.tbAccount.data) {\r\n                    this.tbAccount.data = [];\r\n                }\r\n                this.tbAccount.data.unshift({\r\n                    // accountNo: this.accountObj.accountno,\r\n                    accountNo: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined) ? currentYear+\"\"+currentMonth: this.accountObj.accountno,\r\n                    // accountNo:dates[1].code, new Date(year, month, 0);\r\n                    startDate: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined)\r\n                    ?\r\n                    currentYear + \".\" + currentMonth + \".\" + \"01\"\r\n                    :\r\n                    this.accountObj.accountno.slice(0,4) + \".\" + this.accountObj.accountno.slice(4) + \".\" + \"01\",\r\n                    endDate: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined)\r\n                    ?\r\n                    currentYear + \".\" + currentMonth + \".\" +\r\n                        new Date(currentYear, currentMonth, 0).getDate()\r\n                    :\r\n                    this.accountObj.accountno.slice(0,4) + \".\" + this.accountObj.accountno.slice(4) + \".\" +\r\n                        new Date(this.accountObj.accountno.slice(0,4), this.accountObj.accountno.slice(4), 0).getDate(),\r\n                    heatUseBody: \"\",\r\n                    heatAreaSize:\"0\",\r\n                    heatAmount: \"0\",\r\n                    unitPrice:\"0\",\r\n                    ticketImportType:\"\",\r\n                    ticketMoney:\"0\",\r\n                    taxTicketMoney:\"0\",\r\n                    taxRateShow:\"\",\r\n                    taxAmount:\"0\",\r\n                    otherFee:\"0\",\r\n                    paidMoney:\"0\",\r\n                    remark:\"\",\r\n                });\r\n                this.myStyle.push({\r\n                    startDate: 'myspan',\r\n                    endDate: 'myspan',\r\n                    // curtotalreadings: 'myspan',\r\n                    heatUseBody: 'myspan',\r\n                    heatAreaSize: 'myspan',\r\n                    heatAmount: 'myspan',\r\n\r\n                    ticketImportType: 'myspan',\r\n                    ticketMoney:\"myspan\",\r\n                    taxTicketMoney:\"myspan\",\r\n                    taxRateShow: 'myspan',\r\n                    taxAmount: 'myspan',\r\n                    otherFee: 'myspan',\r\n                    paidMoney: 'myspan',\r\n                    unitPrice: 'myspan',\r\n                    remark: 'myspan',\r\n\r\n                    });\r\n            },\r\n            //验证错误弹出提示框\r\n            errorTips(str){\r\n                this.$Notice.error({\r\n                    title: '提示',\r\n                    desc: str,\r\n                    duration: 10\r\n                });\r\n            },\r\n            handlePage(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageNum = value;\r\n                this.getAccountMessages();\r\n            },\r\n            handlePageSize(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageSize = value;\r\n                this.getAccountMessages();\r\n            },\r\n            //向后台请求数据\r\n            getAccountMessages() {\r\n                let postData = this.accountObj;\r\n                postData.pageNum = this.pageNum;\r\n                postData.pageSize = this.pageSize;\r\n                let req = {\r\n                    url : \"/business/heat/account/list\",\r\n                    method : \"get\",\r\n                    params : postData\r\n                };\r\n                this.tbAccount.loading = true\r\n                axios.request(req).then(res => {\r\n                    this.tbAccount.loading = false\r\n                    if (res.data) {\r\n                        let data = res.data.rows;\r\n                        data.forEach(function (item) {\r\n                            item.editType = 0;\r\n                        })\r\n                        // data.push(this.suntotal(data))//小计\r\n                        this.tbAccount.data = data\r\n                        this.pageTotal = res.data.total || 0\r\n                        this.setMyStyle(this.tbAccount.data.length);\r\n\r\n                        this.editIndex = -1;\r\n                        this.columnsIndex = -1;\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            },\r\n            //重置\r\n            onResetHandle(){\r\n                this.accountObj = {\r\n                    accountno:null,\r\n                    company:this.company,\r\n                    heatUseBody:null,\r\n                    country:Number(this.country),\r\n                };\r\n                this.getAccountMessages()\r\n            },\r\n            remove(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                if(data == null || data.length === 0){\r\n                    this.errorTips(\"请选择要删除的数据\")\r\n                    return;\r\n                }\r\n                this.$Modal.confirm({\r\n                    title: '提示',\r\n                    content: '<p>是否确认删除选中信息？</p>',\r\n                    onOk: () => {\r\n                        let b = true;\r\n                        let ids = '';\r\n                        let total = this.pageTotal\r\n                        for(let i=0;i<data.length;i++){\r\n                            let item = data[i];\r\n                            if(item.id != null && item.id.length > 0){\r\n                                if(item.pabriid){\r\n                                    b = false;\r\n                                }\r\n                                ids += item.id + ',';\r\n                            }\r\n                        }\r\n                        this.pageTotal = total;\r\n                        if(b){\r\n                            if(ids.length > 0){\r\n                                removeHeatAccount(ids).then((res) => {\r\n                                    if (res.data.code == 0) {\r\n                                        this.$Message.success('删除成功');\r\n                                        this.getAccountMessages();\r\n                                    }\r\n                                });\r\n                            }\r\n                        }else {\r\n                            this.errorTips('选中信息中有信息还没有跟归集单解除关联，请先解除关联')\r\n                        }\r\n                    },\r\n                    onCancel: () => {\r\n                    }\r\n                });\r\n            },\r\n            openAddBillPerModal(name) {\r\n                if (name === 'current') {\r\n                    this.selectedAccount()\r\n                } else if (name === 'all') {\r\n                    this.selectedAllAccount()\r\n                }\r\n            },\r\n            //加入归集单，全部有效台账\r\n            selectedAllAccount(){\r\n                let that = this\r\n                that.spinShow = true;\r\n                selectHeatIds(this.accountObj).then(res => {\r\n                    that.spinShow = false;\r\n                    if(res.data.length == 0){\r\n                        that.errorTips('无有效数据可加入归集单')\r\n                    }else {\r\n                        let ids = [];\r\n                        for(let i=0;i<res.data.rows.length;i++){\r\n                            let item = res.data.rows[i];\r\n                            ids.push(item.id)\r\n                        }\r\n                        that.$refs.addBillPer.initAmmeter(ids, 19,this.accountObj.country);\r\n                    }\r\n                });\r\n            },\r\n            selectedAccount(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = 1;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要加入归集单的台账')\r\n                } else {\r\n                    let ids = [];\r\n                    data.forEach(function (item) {\r\n                        if(item.status === 5){\r\n                            b = 3\r\n                        }\r\n                        if(item.status === 4){\r\n                            b=4;\r\n                        }\r\n                        ids.push(item.id)\r\n                    });\r\n                    if(b === 1){\r\n                        this.$refs.addBillPer.initAmmeter(ids,19,this.accountObj.country);\r\n                    }else if(b === 2) {\r\n                        this.errorTips('选中的台账中存在临时数据，请先保存再加入归集单！')\r\n                    }else if(b===3){\r\n                        this.errorTips('退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮')\r\n                    }else if(b===4){\r\n                        this.errorTips('选择的台账有已加入归集单的台账，不能加入其他归集单')\r\n                    }\r\n                }\r\n            },\r\n            openCompletedPreModal(){\r\n                this.$refs.completedPre.initAmmeter(this.accountObj.country,2);\r\n            },\r\n            againJoin(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = true;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要重新加入归集单的台账')\r\n                } else {\r\n                    let ids = '';\r\n                    data.forEach(function (item) {\r\n                        let status = item.status;\r\n                        if(status != 5){\r\n                            b = false;\r\n                        }\r\n                        ids+= item.id +','\r\n                    });\r\n                    if(b){\r\n                        reJoinBillpre(ids).then((res) =>{\r\n                            if(res.data.code==0){\r\n                                this.$Message.info({\r\n                                    content:'提示：操作成功' ,\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }else {\r\n                        this.errorTips('只有已退回的台账才能重新加入归集单')\r\n                    }\r\n                }\r\n            },\r\n            refresh(){\r\n                let obj = this\r\n                setTimeout(function () {\r\n                    obj.getAccountMessages()\r\n                },200);\r\n            },\r\n            setticketImportType() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTicketType;\r\n                data.ticketImportType = val;\r\n                console.log(data.ticketImportType, \"data.ticketImportType\")\r\n                if(data.ticketImportType == '专票') {\r\n                    data.ticketMoney = 0;\r\n                    data.taxTicketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = data.taxRateShow*data.taxTicketMoney*1/100;\r\n                    data.editType = 1;\r\n                }else if(data.ticketImportType == '普票') {\r\n                    data.ticketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    data.taxTicketMoney = 0;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = 0;\r\n                    //  data.taxRateShow = \"\";\r\n                    data.editType = 1;\r\n                }\r\n            },\r\n            settaxrate() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxRate;\r\n                data.taxRateShow = val;\r\n                data.taxAmount = val*data.taxTicketMoney*1/100;\r\n                data.editType = 1;\r\n            },\r\n            validate(){\r\n                if(this.columnsIndex != 5){\r\n                    let val = this.enterOperate(this.columnsIndex).data;\r\n                    if(val) {\r\n                        switch (this.columnsIndex) {\r\n                            case 1:\r\n                                this.validateStartdate();\r\n                                break;\r\n                            case 2:\r\n                                this.validateEnddate();\r\n                                break;\r\n                            case 3:\r\n                                this.validateFeeStartDate();\r\n                                break;\r\n                            case 4:\r\n                                this.validateHeatAreaSize();\r\n                                break;\r\n                            case 6:\r\n                                this.validateUnitPrice();\r\n                                break;\r\n                            case 7:\r\n                                this.validateTicketImportType();\r\n                                break;\r\n                            case 8:\r\n                                this.validateTaxRateShow();\r\n                                break;\r\n                            // case 3:\r\n                            //     this.validateFeeStartDate();\r\n                            //     break;\r\n                            case 9:\r\n                                this.validateOtherMoney();\r\n                                break;\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            //验证起始时间\r\n            validateStartdate() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editStartDate;\r\n                let isDian = val[4]=='.' && val[7]=='.' || val[4]=='.' && val[6]=='.' ;\r\n                if(!isDian) {\r\n                    this.errorTips(\"开始时间格式不正确！\");\r\n                    val = \"\";\r\n                }\r\n                // debugger\r\n                // if (val != data.old_startdate) {\r\n                //     // 验证起始时间方法\r\n                //     let result = _verify_StartDate1(data, val);\r\n                //     console.log(result, \"result\");\r\n                //     if (result) {//失败就弹出提示内容，并将数据恢复初始化\r\n                //         this.errorTips(result);\r\n                //         this.myStyle[this.editIndex].startDate = \"errorStle\";\r\n                //     } else {\r\n                //         this.myStyle[this.editIndex].startDate = \"myspan\";\r\n                //         this.startModal = true;\r\n                //     }\r\n                // } else if (val == data.old_startdate) {\r\n                //     data.startDate = val;\r\n                // }else {\r\n                if(data.startDate !=\"\" && data.endDate !=\"\") {\r\n                    let riqiLengh = data.endDate.split(\".\")[2]*1 - data.startDate.split(\".\")[2]*1;\r\n                    data.heatAmount = data.heatAreaSize*riqiLengh*60*0.7*3.6/1000000;\r\n                    console.log(data.heatAmount, \"data.heatAmount\")\r\n                }\r\n                    data.startDate = val;\r\n                    data.editType = 1;\r\n                // }\r\n\r\n            },\r\n            //验证截止时间\r\n            validateEnddate() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editEndDate;\r\n                let isDian = val[4]=='.' && val[7]=='.' || val[4]=='.' && val[6]=='.' ;\r\n                if(!isDian) {\r\n                    this.errorTips(\"结束时间格式不正确！\");\r\n                    val = \"\";\r\n                }\r\n                if(data.startDate !=\"\" && data.endDate !=\"\") {\r\n                    let riqiLengh = data.endDate.split(\".\")[2]*1 - data.startDate.split(\".\")[2]*1;\r\n                    data.heatAmount = data.heatAreaSize*riqiLengh*60*0.7*3.6/1000000;\r\n                    console.log(data.heatAmount, \"data.heatAmount\")\r\n                }\r\n                // if (val != data.old_enddate) {\r\n                //     // 验证截止日期方法\r\n                //     let result = _verify_EndDate1(data, val);\r\n                //     if (result) {\r\n                //         this.errorTips(result);\r\n                //         this.myStyle[this.editIndex].endDate = \"errorStle\";\r\n                //     } else {\r\n                //         this.myStyle[this.editIndex].endDate = \"myspan\";\r\n\r\n                //         this.updateenddate(data, val)\r\n\r\n                //     }\r\n                // } else if (val == data.old_enddate) {\r\n                    data.endDate = val;\r\n                    data.editType = 1;\r\n                // }\r\n            },\r\n            validateHeatAreaSize() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editHeatAreaSize;\r\n                data.heatAreaSize = val;\r\n                console.log(data.heatAreaSize, \"data.heatAreaSize\")\r\n                data.editType = 1;\r\n                console.log(data.startDate.split(\".\")[2]*1, \"data.startDate.split(\")\r\n                console.log(data.endDate.split(\".\")[2]*1, \"data.endDate.split(\")\r\n                if(data.startDate !=\"\" && data.endDate !=\"\") {\r\n                    let riqiLengh = data.endDate.split(\".\")[2]*1 - data.startDate.split(\".\")[2]*1 + 1;\r\n                    console.log(riqiLengh, \"riqiLengh\")\r\n\r\n                    data.heatAmount = (val*riqiLengh*24*60*0.7*3.6/1000000).toFixed(6);\r\n                }\r\n                console.log(data.ticketImportType, \"data.ticketImportType\")\r\n                // debugger\r\n                if(data.ticketImportType == '专票') {\r\n                    data.ticketMoney = 0;\r\n                    data.taxTicketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = data.taxRateShow*data.taxTicketMoney*1/100;\r\n                    data.editType = 1;\r\n                }else if(data.ticketImportType == '普票') {\r\n                    data.ticketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    data.taxTicketMoney = 0;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = 0;\r\n                    data.editType = 1;\r\n                }\r\n                // else {\r\n                //     this.errorTips(\"开始或者结束时间不能为空！\");\r\n                //     data.heatAmount = \"\";\r\n                // }\r\n            },\r\n            validateTicketImportType() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTicketType;\r\n                data.ticketImportType = val;\r\n                data.editType = 1;\r\n            },\r\n            validateTaxRateShow() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxRate;\r\n                data.taxRateShow = val;\r\n                data.editType = 1;\r\n            },\r\n            validateFeeStartDate(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editFeeStartDate;\r\n                let result = _verify_FeeStartDate(data,val);\r\n                if(result){//失败就弹出提示内容，并将数据恢复初始化\r\n                    this.errorTips(result)\r\n                }else{\r\n                    data.feeStartDate = val;\r\n                    data.editType = 1;\r\n                }\r\n            },\r\n            validateUnitPrice() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editUnitPrice;\r\n                data.unitPrice = val;\r\n                console.log(data.ticketImportType, \"data.ticketImportType\")\r\n                // debugger\r\n                if(data.ticketImportType == '专票') {\r\n                    data.ticketMoney = 0;\r\n                    data.taxTicketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = data.taxRateShow*data.taxTicketMoney*1/100;\r\n                    data.editType = 1;\r\n                }else if(data.ticketImportType == '普票') {\r\n                    data.ticketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    data.taxTicketMoney = 0;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = 0;\r\n                    data.editType = 1;\r\n                }\r\n                data.editType = 1;\r\n            },\r\n            validateOtherMoney(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOtherMoney;\r\n                if (!testNumber(val)) {\r\n                    this.errorTips('请输入数字！');\r\n                }\r\n                data.otherFee = val;\r\n                data.editType = 1;\r\n                let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                    data.paidMoney = paidMoney.toFixed(2);\r\n                // debugger\r\n            },\r\n            setremark(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editremark;\r\n                data.remark = val;\r\n                data.editType = 1;\r\n            },\r\n            setHeatUseBody(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editHeatUseBody;\r\n                data.heatUseBody = val;\r\n                data.editType = 1;\r\n            },\r\n            setMyStyle(length){\r\n                this.myStyle=[];\r\n                for(var i=0;i<length;i++){\r\n                    this.myStyle.push({\r\n                        startDate: 'myspan',\r\n                        endDate: 'myspan',\r\n                        coalUseBody:'myspan',\r\n                        feeStartDate:'myspan',\r\n                        coalAmount:'myspan',\r\n                        taxTicketMoney:'myspan',\r\n                        remark:'myspan',\r\n                    });\r\n                }\r\n            },\r\n            //span点击事件将span换成输入框并且获取焦点\r\n            selectCall(row,index,columns,str){\r\n                this.editStartDate = row.startDate;\r\n                this.editEndDate = row.endDate;\r\n                this.editHeatUseBody = row.heatUseBody;\r\n                this.editHeatAreaSize = row.heatAreaSize;\r\n                this.editHeatAmount = row.heatAmount;\r\n                this.editUnitPrice = row.unitPrice;\r\n                this.editTicketType = row.ticketImportType;\r\n                this.editTaxRate = row.taxRateShow;\r\n                this.editOtherMoney = row.otherFee;\r\n                this.editremark = row.remark;\r\n                this.editIndex = index;\r\n                this.columnsIndex = columns;\r\n                let a=this;\r\n                setTimeout(function () {\r\n                    a.$refs[str+index+columns].focus();\r\n                },200);\r\n\r\n            },\r\n            //跳转到下一格\r\n            nextCell(data){\r\n                let index = data.editIndex;\r\n                let columns = data.columnsIndex;\r\n                let row = '';\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                }else if(index > -1 && columns === 5){\r\n                    //当跳转的最后一行最后一格的时候\r\n                    if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n                        index = 0;\r\n                    }else{\r\n                        index ++;\r\n                    }\r\n                    columns = 1;\r\n                }else{\r\n                    columns += 1;\r\n                }\r\n                debugger\r\n                data.editIndex = index;\r\n                data.columnsIndex = columns;\r\n                row = data.tbAccount.data[index];\r\n                if(row){\r\n                    data.editStartDate = row.startDate;\r\n                    data.editEndDate = row.endDate;\r\n                    data.editHeatUseBody = row.heatUseBody;\r\n                    data.editHeatAreaSize = row.heatAreaSize;\r\n                    data.editHeatAmount = row.heatAmount;\r\n                    data.editUnitPrice = row.unitPrice;\r\n                    data.editTicketType = row.ticketImportType;\r\n                    data.editTaxRate = row.taxRateShow;\r\n                    data.editOtherMoney = row.otherFee;\r\n                    data.editremark = row.remark;\r\n                }\r\n                setTimeout(function () {\r\n                    data.$refs[data.enterOperate(columns).str+index+columns].focus();\r\n                },200);\r\n            },\r\n            //根据列号返回对应的列名\r\n            enterOperate(number){\r\n                let str = '';\r\n                let data = null;\r\n                switch (number) {\r\n                    case 1:\r\n                        str = 'startDate';\r\n                        data = this.editStartDate;\r\n                        break;\r\n                    case 2:\r\n                        str = 'endDate';\r\n                        data = this.editEndDate;\r\n                        break;\r\n                    case 3:\r\n                        str = 'heatUseBody'\r\n                        data = this.editHeatUseBody;\r\n                        break;\r\n                    case 4:\r\n                        str = 'heatAreaSize';\r\n                        data = this.editHeatAreaSize;\r\n                        break;\r\n                    case 5:\r\n                        str = 'heatAmount';\r\n                        data = this.editHeatAmount;\r\n                        break;\r\n                    case 6:\r\n                        str = 'unitPrice';\r\n                        data = this.editUnitPrice;\r\n                        break;\r\n                    case 7:\r\n                        str = 'editTicketType';\r\n                        data = this.ticketImportType;\r\n                        break;\r\n                    case 8:\r\n                        str = 'editTaxRate';\r\n                        data = this.taxRateShow;\r\n                        break;\r\n                    case 9:\r\n                        str = 'otherFee';\r\n                        data = this.editOtherMoney;\r\n                        break;\r\n                    case 10:\r\n                        str = 'remark';\r\n                        data = this.editremark;\r\n                        break;\r\n                }\r\n                return {str:str,data:data};\r\n            },\r\n            pred(){\r\n                var lett = this;\r\n                let index = lett.editIndex;\r\n                let columns = lett.columnsIndex;\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                    lett.editIndex = index;\r\n                    lett.columnsIndex = columns;\r\n                    setTimeout(function () {\r\n                        lett.$refs[lett.enterOperate(columns).str+index+columns].focus();\r\n                    },200);\r\n                }else{\r\n                    lett.validate()\r\n                    lett.setremark()\r\n                    lett.nextCell(lett)\r\n                }\r\n            },\r\n            ellipsis (value) {\r\n                if (!value) return ''\r\n                if (value.length > 100) {\r\n                    return value.slice(0,100) + '...'\r\n                }\r\n                return value\r\n            },\r\n            handleProgress(event, file) {\r\n                this.$Message.info({\r\n                    content: file.name + \" 正在上传。\",\r\n                });\r\n            },\r\n            handleUploadSuccess() {\r\n\r\n            },\r\n            handleFormatError(file) {\r\n                this.errorTips(\r\n                    file.name + \" 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件\"\r\n                );\r\n            },\r\n            // 导入\r\n            onExcelUpload(file) {\r\n                if (!file) {\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: '请选择要上传的文件！',\r\n                        duration: 10\r\n                    });\r\n                    return\r\n                }\r\n                let fileName = file.name.lastIndexOf(\".\");//取到文件名开始到最后一个点的长度\r\n                let fileNameLength = file.name.length;//取到文件名长度\r\n                let fileFormat = file.name.substring(fileName + 1, fileNameLength);//截\r\n                if('xls' != fileFormat && 'xlsx' != fileFormat){\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: file.name + ' 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件',\r\n                        duration: 10\r\n                    });\r\n                    return;\r\n                }\r\n                let param = {}\r\n                let excel = {file: file}\r\n                let that = this\r\n                that.spinShow = true\r\n                axios.request({\r\n                    url: '/business/heat/account/import',\r\n                    method: 'post',\r\n                    data: Object.assign({}, param, excel)\r\n                }).then((res) => {\r\n                    that.spinShow = false\r\n                    if (res.data.str) {\r\n                        that.$Message.info({\r\n                            content: res.data.str,\r\n                            duration: 0,\r\n                            closable: true\r\n                        });\r\n\r\n                        that.show = false;\r\n                    }\r\n                    this.getAccountMessages();\r\n                }).catch(err => {\r\n                    that.spinShow = false\r\n                    that.show = false;\r\n                    console.log(err);\r\n                });\r\n                this.getAccountMessages()\r\n                return false\r\n            },\r\n\r\n            // 导入模板下载\r\n            loadTemplate() {\r\n                let req = {\r\n                    url : \"/business/heat/account/template/load\",\r\n                    method : \"get\",\r\n                    responseType: 'blob',\r\n                };\r\n                axios.file(req)\r\n                    .then(res => {\r\n                        this.spinShow = false;\r\n                        const content = res;\r\n                        const blob = new Blob([content]);\r\n                        const fileName = \"热力台账导入模板.xlsx\";\r\n                        if (\"download\" in document.createElement(\"a\")) {\r\n                            // 非IE下载\r\n                            const elink = document.createElement(\"a\");\r\n                            elink.download = fileName;\r\n                            elink.style.display = \"none\";\r\n                            elink.href = URL.createObjectURL(blob);\r\n                            document.body.appendChild(elink);\r\n                            elink.click();\r\n                            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n                            document.body.removeChild(elink);\r\n                        } else {\r\n                            // IE10+下载\r\n                            navigator.msSaveBlob(blob, fileName);\r\n                        }\r\n                    });\r\n            }\r\n        },\r\n        mounted() {\r\n            this.version = indexData.version\r\n            this.tbAccount.columns = this.tbAccount.tailColumn.concat(this.tbAccount.photoColumn).concat(this.tbAccount.remarkColumn);\r\n            let that = this\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                    that.getUserData();\r\n                });\r\n            });\r\n        }\r\n    }\r\n</script>\r\n"]}]}