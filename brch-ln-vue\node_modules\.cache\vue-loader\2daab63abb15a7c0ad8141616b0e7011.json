{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addOilAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addOilAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVXBsb2FkRmlsZU1vZGFsIGZyb20gIkAvdmlldy9hY2NvdW50L3VwbG9hZEZpbGVNb2RhbCI7DQppbXBvcnQgew0KICAgIF92ZXJpZnlfU3RhcnREYXRlLA0KICAgIGp1ZGdlTnVtYmVyLA0KICAgIF92ZXJpZnlfRW5kRGF0ZSwNCiAgICBfdmVyaWZ5X1ByZXZUb3RhbFJlYWRpbmdzLA0KICAgIF92ZXJpZnlfQ3VyVG90YWxSZWFkaW5ncywNCiAgICBvdGhlcl9ub19hbW1ldGVyb3JfcHJvdG9jb2wsDQogICAgc2VsZl9ub19hbW1ldGVyb3JfcHJvdG9jb2wsDQogICAgSEZMX2FtbWV0ZXJvciwNCiAgICBqdWRnaW5nX2VkaXRhYmlsaXR5LA0KICAgIGp1ZGdpbmdfZWRpdGFiaWxpdHkxLA0KICAgIF92ZXJpZnlfTW9uZXksDQogICAgX2NhbGN1bGF0ZVVzZWRSZWFkaW5ncywNCiAgICBfY2FsY3VsYXRlVG90YWxSZWFkaW5ncywNCiAgICBfY2FsY3VsYXRlVW5pdFByaWNlQnlVc2VkTW9uZXksDQogICAgX2NhbGN1bGF0ZUFjY291bnRNb25leSwNCiAgICBfY2FsY3VsYXRlUXVvdGVyZWFkaW5nc3JhdGlvLA0KICAgIHJlcXVpcmVkRmllbGRWYWxpZGF0b3IsDQogICAgY291bnRUYXhhbW91bnQxLA0KICAgIGNvdW50VGF4YW1vdW50LA0KICAgIGNhbGN1bGF0ZUFjdHVhbE1vbmV5LA0KICAgIGp1ZGdlX25lZ2F0ZSwNCiAgICBqdWRnZV9yZWNvdmVyeSwNCiAgICBqdWRnZV95YiwNCiAgICB1bml0cGlyY2VNaW4sDQogICAgdW5pdHBpcmNlTWF4DQp9IGZyb20gJ0Avdmlldy9hY2NvdW50L1Bvd2VyQWNjb3VudENvbnRyb2xsZXInOw0KICAgIGltcG9ydCB7DQogICAgICAgIHNhdmVPaWxBY2NvdW50LA0KICAgICAgICByZW1vdmVPaWxBY2NvdW50LA0KICAgICAgICBzZWxlY3RPaWxJZHMNCiAgICB9IGZyb20gJ0AvYXBpL2NvYWxIZWF0T2lsQWNjb3VudCc7DQogICAgaW1wb3J0IGNoZWNrUmVzdWx0QW5kUmVzcG9uc2UgZnJvbSAiQC92aWV3L2FjY291bnQvY2hlY2svY2hlY2tSZXN1bHRBbmRSZXNwb25zZSI7DQogICAgaW1wb3J0IGNoZWNrUmVzdWx0IGZyb20gIkAvdmlldy9hY2NvdW50L2NoZWNrL2NoZWNrUmVzdWx0IjsNCiAgICBpbXBvcnQgYWxhcm1DaGVjayBmcm9tICJAL3ZpZXcvYWNjb3VudC9jaGVjay9hbGFybUNoZWNrIjsNCiAgICBpbXBvcnQge2dldERhdGVzLHRlc3ROdW1iZXIsfSBmcm9tICdAL3ZpZXcvYWNjb3VudC9wb3dlckFjY291bnRIZWxwZXInOw0KICAgIGltcG9ydCBheGlvcyBmcm9tICdAL2xpYnMvYXBpLnJlcXVlc3QnOw0KICAgIGltcG9ydCBTZWxlY3RBbW1ldGVyIGZyb20gIi4vc2VsZWN0QW1tZXRlciI7DQogICAgaW1wb3J0IHtfdmVyaWZ5X0ZlZVN0YXJ0RGF0ZX0gZnJvbSAnQC92aWV3L2FjY291bnQvUG93ZXJBY2NvdW50RXMnOw0KICAgIGltcG9ydCBBZGRCaWxsUGVyIGZyb20gIi4vYWRkT2lsQmlsbFByZU1vZGFsIjsNCiAgICBpbXBvcnQge3JlSm9pbkJpbGxwcmV9IGZyb20gJ0AvYXBpL2FjY291bnRCaWxsUGVyJzsNCiAgICBpbXBvcnQge2JsaXN0MX0gZnJvbSAiQC9saWJzL3Rvb2xzIjsNCiAgICBpbXBvcnQge3dpZHRoc3R5bGV9IGZyb20gIkAvdmlldy9idXNpbmVzcy9tc3NBY2NvdW50YmlsbC9tc3NBY2NvdW50YmlsbGRhdGEiOw0KICAgIGltcG9ydCBDb21wbGV0ZWRQcmVNb2RhbCBmcm9tICIuL2NvbXBsZXRlZFByZU1vZGFsIjsNCiAgICBpbXBvcnQgaW5kZXhEYXRhIGZyb20gJ0AvY29uZmlnL2luZGV4Jw0KICAgIGltcG9ydCBDb3VudHJ5TW9kYWwgZnJvbSAiQC92aWV3L2Jhc2VkYXRhL2FtbWV0ZXIvY291bnRyeU1vZGFsIjsNCiAgICBpbXBvcnQge2dldFVzZXJkYXRhLGdldFVzZXJCeVVzZXJSb2xlLGdldENvdW50cnlzZGF0YSxnZXRDb3VudHJ5QnlVc2VySWR9IGZyb20gJ0AvYXBpL2Jhc2VkYXRhL2FtbWV0ZXIuanMnDQogICAgbGV0IGRhdGVzPWdldERhdGVzKCk7DQogICAgZXhwb3J0IGRlZmF1bHQgew0KICAgICAgICBuYW1lOiAnYWRkQ29hbEFjY291bnQnLA0KICAgICAgICBjb21wb25lbnRzOiB7VXBsb2FkRmlsZU1vZGFsLCBhbGFybUNoZWNrLCBjaGVja1Jlc3VsdCwgY2hlY2tSZXN1bHRBbmRSZXNwb25zZSxDb21wbGV0ZWRQcmVNb2RhbCwgU2VsZWN0QW1tZXRlcixBZGRCaWxsUGVyLENvdW50cnlNb2RhbH0sDQogICAgICAgIGRhdGEoKSB7DQogICAgICAgICAgICBsZXQgcGhvdG8gPSAoaCwge3JvdywgaW5kZXh9KSA9PiB7DQogICAgICAgICAgICAgICAgbGV0IHRoYXQgPSB0aGlzDQogICAgICAgICAgICAgICAgbGV0IHN0ciA9ICcnDQogICAgICAgICAgICAgICAgaWYgKHJvdy5wcm9qZWN0bmFtZSAhPSAn5bCP6K6hJyAmJiByb3cucHJvamVjdG5hbWUgIT0gJ+WQiOiuoScpIHsNCiAgICAgICAgICAgICAgICAgICAgc3RyID0gJ+S4iuS8oOmZhOS7ticNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgcmV0dXJuIGgoImRpdiIsIFtoKCJ1Iiwgew0KICAgICAgICAgICAgICAgICAgICBvbjogew0KICAgICAgICAgICAgICAgICAgICAgICAgY2xpY2soKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLy/miZPlvIDlvLnlh7rmoYYNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocm93LnByb2plY3RuYW1lICE9ICflsI/orqEnICYmIHJvdy5wcm9qZWN0bmFtZSAhPSAn5ZCI6K6hJykgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGF0LnVwbG9hZEZpbGUocm93KQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0sIHN0cildKTsNCiAgICAgICAgICAgIH07DQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgIHN1Ym1pdDpbXSwNCiAgICAgICAgICAgICAgICBzdWJtaXQyOltdLA0KICAgICAgICAgICAgICAgIHNob3dDaGVja01vZGVsOmZhbHNlLA0KICAgICAgICAgICAgICAgIHNob3dKaE1vZGVsOmZhbHNlLA0KICAgICAgICAgICAgICAgIHNob3dBbGFybU1vZGVsOmZhbHNlLA0KICAgICAgICAgICAgICAgIGZvcm1JdGVtV2lkdGg6IHdpZHRoc3R5bGUsDQogICAgICAgICAgICAgICAgdmVyc2lvbjonJywNCiAgICAgICAgICAgICAgICBkYXRlTGlzdDpkYXRlcywNCiAgICAgICAgICAgICAgICBmaWx0ZXJDb2xsOiB0cnVlLC8v5pCc57Si6Z2i5p2/5bGV5byADQogICAgICAgICAgICAgICAgZWRpdEluZGV4OiAtMSwvL+W9k+WJjee8lui+keihjA0KICAgICAgICAgICAgICAgIGNvbHVtbnNJbmRleDotMSwvL+W9k+WJjee8lui+keWIlw0KICAgICAgICAgICAgICAgIG15U3R5bGU6W10sLy/moLflvI8NCiAgICAgICAgICAgICAgICBlZGl0T2lsVXNlQm9keTonJywNCiAgICAgICAgICAgICAgICBlZGl0RmVlU3RhcnREYXRlOicnLA0KICAgICAgICAgICAgICAgIGVkaXRPaWxUeXBlOicnLA0KICAgICAgICAgICAgICAgIGVkaXRPaWxBbW91bnQ6JycsDQogICAgICAgICAgICAgICAgZWRpdFRpY2tldE1vbmV5OicnLA0KICAgICAgICAgICAgICAgIGVkaXRUYXhUaWNrZXRNb25leTonJywNCiAgICAgICAgICAgICAgICBlZGl0VGF4UmF0ZTonJywNCiAgICAgICAgICAgICAgICBlZGl0T3RoZXJNb25leTonJywNCiAgICAgICAgICAgICAgICBlZGl0T2lsQ2F0ZWdvcnk6JycsDQogICAgICAgICAgICAgICAgc3BpblNob3c6ZmFsc2UsLy/pga7nvakNCiAgICAgICAgICAgICAgICBjYXRlZ29yeXM6W10sLy/mj4/ov7DnsbvlnosNCiAgICAgICAgICAgICAgICBlZGl0cmVtYXJrOicnLA0KICAgICAgICAgICAgICAgIGFjY291bnRTdGF0dXM6W10sDQogICAgICAgICAgICAgICAgY29tcGFuaWVzOltdLA0KICAgICAgICAgICAgICAgIG9pbFR5cGVzOiBbXSwNCiAgICAgICAgICAgICAgICBvaWxDYXRlZ29yeXM6IFtdLA0KICAgICAgICAgICAgICAgIGRlcGFydG1lbnRzOltdLA0KICAgICAgICAgICAgICAgIGlzQWRtaW46ZmFsc2UsDQogICAgICAgICAgICAgICAgY29tcGFueTpudWxsLC8v55So5oi36buY6K6k5YWs5Y+4DQogICAgICAgICAgICAgICAgY291bnRyeTpudWxsLC8v55So5oi36buY6K6k5omA5bGe6YOo6ZeoDQogICAgICAgICAgICAgICAgY291bnRyeU5hbWU6bnVsbCwvL+eUqOaIt+m7mOiupOaJgOWxnumDqOmXqA0KICAgICAgICAgICAgICAgIGFjY291bnRPYmo6ew0KICAgICAgICAgICAgICAgICAgICBhY2NvdW50bm86ZGF0ZXNbMV0uY29kZSwvL+acn+WPtyzpu5jorqTlvZPliY3mnIgNCiAgICAgICAgICAgICAgICAgICAgY29tcGFueToiIiwvL+WIhuWFrOWPuA0KICAgICAgICAgICAgICAgICAgICBjb3VudHJ5OiIiLC8v5omA5bGe6YOo6ZeoDQogICAgICAgICAgICAgICAgICAgIG9pbFVzZUJvZHk6bnVsbCwvL+eUqOayueS4u+S9kw0KICAgICAgICAgICAgICAgICAgICBvaWxDYXRlZ29yeToxLA0KICAgICAgICAgICAgICAgICAgICBvaWxUeXBlOjEsDQogICAgICAgICAgICAgICAgICAgIG9pbEFjY291bnRUeXBlOiAxLA0KICAgICAgICAgICAgICAgICAgICBjb3VudHJ5TmFtZTogIiIsDQoNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIHRiQWNjb3VudDogew0KICAgICAgICAgICAgICAgICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICBjb2x1bW5zOiBbXSwNCiAgICAgICAgICAgICAgICAgICAgdGFpbENvbHVtbjogWw0KICAgICAgICAgICAgICAgICAgICAgICAge3R5cGU6ICdzZWxlY3Rpb24nLCB3aWR0aDogNjAsIGFsaWduOiAnY2VudGVyJyx9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5pyf5Y+3IiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJhY2NvdW50Tm8iLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogOTAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi55So5rK55Li75L2TIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAib2lsVXNlQm9keSIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxNTAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi6LS555So5Y+R55Sf5pelIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAiZmVlU3RhcnREYXRlIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEwMCwNCiAgICAgICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLnlKjmsrnnsbvlnosiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNsb3Q6ICJvaWxJbXBvcnRUeXBlIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDgwLA0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIuWKoOayuemHjyhMKSIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgc2xvdDogIm9pbEFtb3VudCIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiA4MCwNCiAgICAgICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLljZXku7co5YWDKSIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAidW5pdFByaWNlIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDgwLA0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIuaZruelqOWQq+eojumHkeminSjlhYMpIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAidGlja2V0TW9uZXkiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwLA0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIuS4k+elqOWQq+eojumHkeminSjlhYMpIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAidGF4VGlja2V0TW9uZXkiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwLA0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIuS4k+elqOeojueOh++8iCXvvIkiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNsb3Q6ICJ0YXhSYXRlU2hvdyIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiA4MCwNCiAgICAgICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLkuJPnpajnqI7pop0iLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleTogInRheEFtb3VudCIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiA4MCwNCiAgICAgICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLlhbbku5Yo5YWDKSIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgc2xvdDogIm90aGVyRmVlIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDgwLA0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIuWunue8tOi0ueeUqCjlhYMp5ZCr56iOIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJwYWlkTW9uZXkiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTIwLA0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIueUqOayueexu+WIqyIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgc2xvdDogIm9pbEltcG9ydENhdGVnb3J5IiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDgwLA0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHt0aXRsZTogIumZhOS7tiIsIGFsaWduOiAiY2VudGVyIiwgcmVuZGVyOiBwaG90bywgd2lkdGg6IDEwMH0sDQogICAgICAgICAgICAgICAgICAgICAgICB7dGl0bGU6ICLlpIfms6giLCBzbG90OiAicmVtYXJrIixhbGlnbjogImNlbnRlciIsIHdpZHRoOiAxNTB9LA0KICAgICAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIHBhZ2VUb3RhbDogMCwNCiAgICAgICAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiAxMCwvL+W9k+WJjemhtQ0KICAgICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBtZXRob2RzOiB7DQogICAgICAgICAgICBjaGFuZ2UoKSB7DQoNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB1cGxvYWRGaWxlKHJvdykgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKHJvdywgInJvdyIpOw0KICAgICAgICAgICAgICAgIC8vIGxldCBpZDsNCiAgICAgICAgICAgICAgICAvLyBpZighcm93LmlkMikgew0KICAgICAgICAgICAgICAgIC8vICAgICBlZGl0QW1tZXRlcignJywgMCkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgZGVidWdnZXINCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIGNvbnNvbGUubG9nKHJlcywgInJlcyIpOw0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgcm93LmlkMiA9IHJlcy5kYXRhLmlkOw0KDQogICAgICAgICAgICAgICAgLy8gICAgICAgICB0aGlzLmlkMiA9IHJlcy5kYXRhLmlkDQogICAgICAgICAgICAgICAgLy8gICAgICAgICAvLyBkZWJ1Z2dlcg0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgLy8gdGhpcy5maWxlUGFyYW0uYnVzaUlkID0gOw0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgdGhpcy4kcmVmcy51cGxvYWRGaWxlTW9kYWwuY2hvb3NlKHJvdy5pZDIgKyAnJyk7DQogICAgICAgICAgICAgICAgLy8gICAgIH0pDQogICAgICAgICAgICAgICAgLy8gfWVsc2Ugew0KDQogICAgICAgICAgICAgICAgaWYocm93LmlkKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMudXBsb2FkRmlsZU1vZGFsLmNob29zZShyb3cuaWQgKyAnJyk7DQogICAgICAgICAgICAgICAgfWVsc2Ugew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6K+35YWI5L+d5a2Y5ZCO5YaN5LiK5Lyg5paH5Lu277yBIik7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgICAgICAvLyBjb25zb2xlLmxvZyhyb3csICJyb3ciKTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBzZXR0YXhyYXRlKCkgew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdFRheFJhdGU7DQogICAgICAgICAgICAgICAgZGF0YS50YXhSYXRlU2hvdyA9IHZhbDsNCiAgICAgICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgICAgICAgICBkYXRhLnRheEFtb3VudCA9IGRhdGEudGF4UmF0ZVNob3cqZGF0YS50YXhUaWNrZXRNb25leS8xMDA7DQogICAgICAgICAgICAgICAgLy8gbGV0IHBhaWRNb25leSA9IGRhdGEudGlja2V0TW9uZXkqMStkYXRhLnRheFRpY2tldE1vbmV5KjErZGF0YS5vdGhlckZlZSoxOw0KICAgICAgICAgICAgICAgIC8vIGRhdGEucGFpZE1vbmV5ID0gcGFpZE1vbmV5LnRvRml4ZWQoMik7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgc2VsZWN0Q2hhbmdlKCl7DQogICAgICAgICAgICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgICAgICAgICAgIGlmICh0aGF0LmFjY291bnRPYmouY29tcGFueSAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICAgICAgaWYodGhhdC5hY2NvdW50T2JqLmNvbXBhbnkgPT0gIi0xIil7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeSA9IC0xOw0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnlOYW1lID0gbnVsbDsNCiAgICAgICAgICAgICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgICAgICAgICAgICBnZXRDb3VudHJ5QnlVc2VySWQodGhhdC5hY2NvdW50T2JqLmNvbXBhbnkpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5kZXBhcnRtZW50cy5sZW5ndGggIT0gMCl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb3VudHJ5ID0gcmVzLmRhdGEuZGVwYXJ0bWVudHNbMF0uaWQ7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb3VudHJ5TmFtZSA9IHJlcy5kYXRhLmRlcGFydG1lbnRzWzBdLm5hbWU7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jlvIDlp4sNCiAgICAgICAgICAgIGNob29zZVJlc3BvbnNlQ2VudGVyKCkgew0KICAgICAgICAgICAgICAgIGlmKHRoaXMuYWNjb3VudE9iai5jb21wYW55ID09IG51bGwgfHwgdGhpcy5hY2NvdW50T2JqLmNvbXBhbnkgPT0gIi0xIiApew0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIuivt+WFiOmAieaLqeWIhuWFrOWPuCIpO3JldHVybjsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5jb3VudHJ5TW9kYWwuY2hvb3NlKHRoaXMuYWNjb3VudE9iai5jb21wYW55KTsvL+aJgOWxnumDqOmXqA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGdldERhdGFGcm9tTW9kYWwoZGF0YSkgew0KICAgICAgICAgICAgICAgIHRoaXMuYWNjb3VudE9iai5jb3VudHJ5ID0gZGF0YS5pZDsNCiAgICAgICAgICAgICAgICB0aGlzLmFjY291bnRPYmouY291bnRyeU5hbWUgPSBkYXRhLm5hbWU7DQogICAgICAgICAgICAgICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jnu5PmnZ8NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBnZXRVc2VyRGF0YSgpew0KICAgICAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgICAgICAgICAgICBnZXRVc2VyZGF0YSgpLnRoZW4ocmVzID0+IHsvL+W9k+WJjeeZu+W9leeUqOaIt+aJgOWcqOWFrOWPuOWSjOaJgOWxnumDqOmXqA0KICAgICAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5jb21wYW5pZXMubGVuZ3RoICE9IDApew0KICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGNvbXBhbmllcyA9IHJlcy5kYXRhLmNvbXBhbmllczsNCiAgICAgICAgICAgICAgICAgICAgICAgIGlmKHJlcy5kYXRhLmNvbXBhbmllc1swXS5pZCA9PSAiMjYwMDAwMDAwMCIpew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbXBhbmllcyA9IHRoYXQuY29tcGFuaWVzOw0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5jb21wYW55ID0gY29tcGFuaWVzWzBdLmlkOw0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvbXBhbnkgPSBjb21wYW5pZXNbMF0uaWQ7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgaWYocmVzLmRhdGEuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApew0KICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGRlcGFydG1lbnRzID0gcmVzLmRhdGEuZGVwYXJ0bWVudHM7DQogICAgICAgICAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5jb21wYW5pZXNbMF0uaWQgPT0gIjI2MDAwMDAwMDAiICYmIHRoYXQuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlcGFydG1lbnRzID0gdGhhdC5kZXBhcnRtZW50cw0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5jb3VudHJ5ID0gZGVwYXJ0bWVudHNbMF0uaWQ7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGF0LmNvdW50cnlOYW1lID0gZGVwYXJ0bWVudHNbMF0ubmFtZTsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb3VudHJ5ID0gTnVtYmVyKGRlcGFydG1lbnRzWzBdLmlkKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb3VudHJ5TmFtZSA9IGRlcGFydG1lbnRzWzBdLm5hbWU7DQogICAgICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgICAgICB0aGF0LnBhZ2VOdW0gPSAxDQogICAgICAgICAgICAgICAgICAgIHRoYXQuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgc2VhcmNoTGlzdCgpew0KICAgICAgICAgICAgICAgIGlmKHRoaXMuYWNjb3VudE9iai5jb3VudHJ5TmFtZSA9PSAiIil7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuYWNjb3VudE9iai5jb3VudHJ5ID0gIi0xIjsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgdGhpcy5wYWdlTnVtID0gMTsNCiAgICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYWNjb3VudG5vQ2hhbmdlKCl7DQogICAgICAgICAgICAgICAgdGhpcy5zZWFyY2hMaXN0KCkNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvL+eCueWHu+S/neWtmA0KICAgICAgICAgICAgcHJlc2VydmUoKSB7DQogICAgICAgICAgICAgICAgbGV0IGRhdGFMID0gdGhpcy4kcmVmcy5hY2NvdW50RXNUYWJsZS5nZXRTZWxlY3Rpb24oKTsNCiAgICAgICAgICAgICAgICBsZXQgYiA9IGZhbHNlOw0KICAgICAgICAgICAgICAgIGxldCBhcnJheSA9IFtdOw0KICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGF0YUwubGVuZ3RoOyBpICsrKSB7DQogICAgICAgICAgICAgICAgICAgIGIgPSB0cnVlOw0KICAgICAgICAgICAgICAgICAgICBhcnJheS5wdXNoKGRhdGFMW2ldKQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBpZihiKXsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5zdWJtaXREYXRhKGFycmF5KTsNCiAgICAgICAgICAgICAgICB9ZWxzZSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCfmsqHmnInlj6/kv53lrZjmlbDmja4nKQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBzdWJtaXRDaGFuZ2UoaW5kZXhMaXN0KXsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YT1bXTsNCiAgICAgICAgICAgICAgICB0aGlzLnN1Ym1pdDIubWFwKChpdGVtLGluZGV4KT0+ew0KICAgICAgICAgICAgICAgICAgICBpbmRleExpc3QubWFwKChpdGVtMik9PnsNCiAgICAgICAgICAgICAgICAgICAgICAgIGlmKGluZGV4PT1pdGVtMil7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YS5wdXNoKGl0ZW0pDQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgdGhpcy5zdWJtaXQ9ZGF0YQ0KICAgICAgICAgICAgfSwNCg0KICAgICAgICAgICAgLy/mj5DkuqTmlbDmja4NCiAgICAgICAgICAgIHN1Ym1pdERhdGEoZGF0YSl7DQogICAgICAgICAgICAgICAgbGV0IGEgPSBbXTsNCiAgICAgICAgICAgICAgICBsZXQgdGhhdD10aGlzOw0KICAgICAgICAgICAgICAgIGlmKGRhdGEgIT0gbnVsbCAmJiBkYXRhLmxlbmd0aCA+IDApew0KICAgICAgICAgICAgICAgICAgICBsZXQgbnVtYmVyID0gMDsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHN1Ym1pdERhdGEgPSBbXTsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHN0ciA9ICcnOw0KICAgICAgICAgICAgICAgICAgICBsZXQgYWNjb3VudG5vID0gdGhpcy5hY2NvdW50T2JqLmFjY291bnRubzsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAvLyDmoKHpqozmlbDmja4NCiAgICAgICAgICAgICAgICAgICAgICAgIGlmKGl0ZW0uaWQgPT0gbnVsbCl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS5hY2NvdW50bm8gPSBhY2NvdW50bm8NCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIGEucHVzaChpdGVtLmlkKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0ub2lsQWNjb3VudFR5cGUgPSAxOw0KICAgICAgICAgICAgICAgICAgICAgICAgc3VibWl0RGF0YS5wdXNoKGl0ZW0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgbnVtYmVyICsrOw0KICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5pZHM9YTsNCiAgICAgICAgICAgICAgICAgICAgaWYoc3RyLmxlbmd0aCA+IDApew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoc3RyKQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIGlmKHN1Ym1pdERhdGEubGVuZ3RoID4gMCl7DQogICAgICAgICAgICAgICAgICAgICAgICBzYXZlT2lsQWNjb3VudChzdWJtaXREYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEuY29kZSA9PSAwKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbyh7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiAn5o+Q56S677ya5oiQ5Yqf5L+d5a2YICcgKyByZXMuZGF0YS5udW0gKyAnIOadoeaVsOaNricsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTAsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbG9zYWJsZTogdHJ1ZQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGFkZE5ld0NvYWxBY2NvdW50KCkgew0KICAgICAgICAgICAgICAgIC8vIGxldCBjb21wYW55SWQgPSB0aGlzLmFjY291bnRPYmouY29tcGFueTsNCiAgICAgICAgICAgICAgICAvLyBsZXQgY291bnRyeSA9IHRoaXMuYWNjb3VudE9iai5jb3VudHJ5Ow0KICAgICAgICAgICAgICAgIC8vIGlmKGNvbXBhbnlJZCAhPSBudWxsICYmIGNvdW50cnkgIT0gbnVsbCl7DQogICAgICAgICAgICAgICAgLy8gICAgIGxldCBvYmogPSB7DQogICAgICAgICAgICAgICAgLy8gICAgICAgICBjb21wYW55OmNvbXBhbnlJZCwNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIGNvdW50cnk6Y291bnRyeSwNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIGFjY291bnRubzp0aGlzLmFjY291bnRPYmouYWNjb3VudG5vLA0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgYWNjb3VudFR5cGU6JzEnLA0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgYWNjb3VudGVzdHlwZToxDQogICAgICAgICAgICAgICAgLy8gICAgIH0NCiAgICAgICAgICAgICAgICAvLyB9ZWxzZXsNCiAgICAgICAgICAgICAgICAvLyAgICAgdGhpcy5lcnJvclRpcHMoJ+ivt+mAieaLqeWIhuWFrOWPuOWSjOmDqOmXqCcpDQogICAgICAgICAgICAgICAgLy8gfQ0KICAgICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnREYXRlID0gbmV3IERhdGUoKTsNCiAgICAgICAgICAgICAgICBjb25zdCBjdXJyZW50WWVhciA9IGN1cnJlbnREYXRlLmdldEZ1bGxZZWFyKCk7DQogICAgICAgICAgICAgICAgY29uc3QgY3VycmVudE1vbnRoID0gY3VycmVudERhdGUuZ2V0TW9udGgoKSArIDE7DQogICAgICAgICAgICAgICAgaWYgKG51bGwgPT0gdGhpcy50YkFjY291bnQuZGF0YSkgew0KICAgICAgICAgICAgICAgICAgICB0aGlzLnRiQWNjb3VudC5kYXRhID0gW107DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIHRoaXMudGJBY2NvdW50LmRhdGEudW5zaGlmdCh7DQogICAgICAgICAgICAgICAgICAgIC8vIGFjY291bnRObzpkYXRlc1sxXS5jb2RlLA0KICAgICAgICAgICAgICAgICAgICAvLyBhY2NvdW50Tm86IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm8sDQogICAgICAgICAgICAgICAgICAgIGFjY291bnRObzogKHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm8gPT0gLTEgfHwgdGhpcy5hY2NvdW50T2JqLmFjY291bnRubyA9PSB1bmRlZmluZWQpID8gY3VycmVudFllYXIrIiIrY3VycmVudE1vbnRoOiB0aGlzLmFjY291bnRPYmouYWNjb3VudG5vLA0KICAgICAgICAgICAgICAgICAgICBvaWxVc2VCb2R5OiAiIiwNCiAgICAgICAgICAgICAgICAgICAgZmVlU3RhcnREYXRlOiIiLA0KICAgICAgICAgICAgICAgICAgICBvaWxJbXBvcnRUeXBlOiAiIiwNCiAgICAgICAgICAgICAgICAgICAgb2lsQW1vdW50OiIwIiwNCiAgICAgICAgICAgICAgICAgICAgdW5pdFByaWNlOiIwIiwNCiAgICAgICAgICAgICAgICAgICAgdGlja2V0TW9uZXk6IjAiLA0KICAgICAgICAgICAgICAgICAgICB0YXhUaWNrZXRNb25leToiMCIsDQogICAgICAgICAgICAgICAgICAgIHRheFJhdGVTaG93OiIiLA0KICAgICAgICAgICAgICAgICAgICB0YXhBbW91bnQ6IjAiLA0KICAgICAgICAgICAgICAgICAgICBvdGhlckZlZToiMCIsDQogICAgICAgICAgICAgICAgICAgIHBhaWRNb25leToiMCIsDQogICAgICAgICAgICAgICAgICAgIG9pbEltcG9ydENhdGVnb3J5OiIiLA0KICAgICAgICAgICAgICAgICAgICByZW1hcms6IiIsDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgdGhpcy5teVN0eWxlLnB1c2goew0KICAgICAgICAgICAgICAgICAgICBvaWxVc2VCb2R5OiAnbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgZmVlU3RhcnREYXRlOiAnbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgLy8gY3VydG90YWxyZWFkaW5nczogJ215c3BhbicsDQogICAgICAgICAgICAgICAgICAgIG9pbEltcG9ydFR5cGU6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICBvaWxBbW91bnQ6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICB1bml0UHJpY2U6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICB0aWNrZXRNb25leToibXlzcGFuIiwNCiAgICAgICAgICAgICAgICAgICAgdGF4VGlja2V0TW9uZXk6Im15c3BhbiIsDQogICAgICAgICAgICAgICAgICAgIHRheFJhdGVTaG93OiAnbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgdGF4QW1vdW50OiAnbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgb3RoZXJGZWU6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICBwYWlkTW9uZXk6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICBvaWxJbXBvcnRDYXRlZ29yeTogJ215c3BhbicsDQogICAgICAgICAgICAgICAgICAgIHJlbWFyazogJ215c3BhbicsDQoNCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/pqozor4HplJnor6/lvLnlh7rmj5DnpLrmoYYNCiAgICAgICAgICAgIGVycm9yVGlwcyhzdHIpew0KICAgICAgICAgICAgICAgIHRoaXMuJE5vdGljZS5lcnJvcih7DQogICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn5o+Q56S6JywNCiAgICAgICAgICAgICAgICAgICAgZGVzYzogc3RyLA0KICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTANCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBoYW5kbGVQYWdlKHZhbHVlKSB7DQogICAgICAgICAgICAgICAgbGV0IGIgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGENCiAgICAgICAgICAgICAgICBsZXQgYXJyYXkgPSBbXTsNCiAgICAgICAgICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICAgICAgICAgICAgaWYoaXRlbS5lZGl0VHlwZSA9PSAxKXsNCiAgICAgICAgICAgICAgICAgICAgICAgIGIgPSB0cnVlOw0KICAgICAgICAgICAgICAgICAgICAgICAgYXJyYXkucHVzaChpdGVtKQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgaWYoYil7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJE1vZGFsLmNvbmZpcm0oew0KICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfmj5DnpLonLA0KICAgICAgICAgICAgICAgICAgICAgICAgY29udGVudDogJzxwPuaCqOacieW3sue8lui+keS/oeaBr+i/mOayoeacieS/neWtmO+8jOaYr+WQpuS/neWtmO+8nzwvcD4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgb25PazogKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc3VibWl0RGF0YShhcnJheSk7DQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgb25DYW5jZWw6ICgpID0+IHsNCg0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICB0aGlzLnBhZ2VOdW0gPSB2YWx1ZTsNCiAgICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGhhbmRsZVBhZ2VTaXplKHZhbHVlKSB7DQogICAgICAgICAgICAgICAgbGV0IGIgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGENCiAgICAgICAgICAgICAgICBsZXQgYXJyYXkgPSBbXTsNCiAgICAgICAgICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICAgICAgICAgICAgaWYoaXRlbS5lZGl0VHlwZSA9PSAxKXsNCiAgICAgICAgICAgICAgICAgICAgICAgIGIgPSB0cnVlOw0KICAgICAgICAgICAgICAgICAgICAgICAgYXJyYXkucHVzaChpdGVtKQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgaWYoYil7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJE1vZGFsLmNvbmZpcm0oew0KICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfmj5DnpLonLA0KICAgICAgICAgICAgICAgICAgICAgICAgY29udGVudDogJzxwPuaCqOacieW3sue8lui+keS/oeaBr+i/mOayoeacieS/neWtmO+8jOaYr+WQpuS/neWtmO+8nzwvcD4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgb25PazogKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc3VibWl0RGF0YShhcnJheSk7DQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgb25DYW5jZWw6ICgpID0+IHsNCg0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgdGhpcy5wYWdlU2l6ZSA9IHZhbHVlOw0KICAgICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/lkJHlkI7lj7Dor7fmsYLmlbDmja4NCiAgICAgICAgICAgIGdldEFjY291bnRNZXNzYWdlcygpIHsNCiAgICAgICAgICAgICAgICAgbGV0IHBvc3REYXRhID0gdGhpcy5hY2NvdW50T2JqOw0KICAgICAgICAgICAgICAgIHBvc3REYXRhLnBhZ2VOdW0gPSB0aGlzLnBhZ2VOdW07DQogICAgICAgICAgICAgICAgcG9zdERhdGEucGFnZVNpemUgPSB0aGlzLnBhZ2VTaXplOw0KICAgICAgICAgICAgICAgIGxldCByZXEgPSB7DQogICAgICAgICAgICAgICAgICAgIHVybCA6ICIvYnVzaW5lc3Mvb2lsL2FjY291bnQvbGlzdCIsDQogICAgICAgICAgICAgICAgICAgIG1ldGhvZCA6ICJnZXQiLA0KICAgICAgICAgICAgICAgICAgICBwYXJhbXMgOiBwb3N0RGF0YQ0KICAgICAgICAgICAgICAgIH07DQogICAgICAgICAgICAgICAgdGhpcy50YkFjY291bnQubG9hZGluZyA9IHRydWU7DQogICAgICAgICAgICAgICAgYXhpb3MucmVxdWVzdChyZXEpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy50YkFjY291bnQubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBkYXRhID0gcmVzLmRhdGEucm93czsNCiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0uZWRpdFR5cGUgPSAwOw0KICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnRiQWNjb3VudC5kYXRhID0gZGF0YTsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucGFnZVRvdGFsID0gcmVzLmRhdGEudG90YWwgfHwgMDsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0TXlTdHlsZSh0aGlzLnRiQWNjb3VudC5kYXRhLmxlbmd0aCk7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVkaXRJbmRleCA9IC0xOw0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jb2x1bW5zSW5kZXggPSAtMTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGVycik7DQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/ph43nva4NCiAgICAgICAgICAgIG9uUmVzZXRIYW5kbGUoKXsNCiAgICAgICAgICAgICAgICB0aGlzLmFjY291bnRPYmogPSB7DQogICAgICAgICAgICAgICAgICAgIGFjY291bnRubzpudWxsLA0KICAgICAgICAgICAgICAgICAgICBjb21wYW55OnRoaXMuY29tcGFueSwNCiAgICAgICAgICAgICAgICAgICAgb2lsVXNlQm9keTpudWxsLA0KICAgICAgICAgICAgICAgICAgICBjb3VudHJ5Ok51bWJlcih0aGlzLmNvdW50cnkpLA0KICAgICAgICAgICAgICAgICAgICBvaWxDYXRlZ29yeTpudWxsLA0KICAgICAgICAgICAgICAgICAgICBvaWxUeXBlOm51bGwsDQogICAgICAgICAgICAgICAgICAgIG9pbEFjY291bnRUeXBlOjEsDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCkNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvL+iuoeeul+WNleS7tw0KICAgICAgICAgICAgdW5pdFByaWNlKHJvdyl7DQogICAgICAgICAgICAgICAgbGV0IHRpY2tldE1vbmV5ID0gcm93LnRpY2tldE1vbmV5Ow0KICAgICAgICAgICAgICAgIGxldCB0YXhUaWNrZXRNb25leSA9IHJvdy50YXhUaWNrZXRNb25leTsNCiAgICAgICAgICAgICAgICBsZXQgb2lsQW1vdW50ID0gcm93Lm9pbEFtb3VudDsNCiAgICAgICAgICAgICAgICBpZih0aWNrZXRNb25leSAhPSBudWxsIHx8IHRheFRpY2tldE1vbmV5ICE9IG51bGwpew0KICAgICAgICAgICAgICAgICAgICBsZXQgdG90YWwgPSBudWxsOw0KICAgICAgICAgICAgICAgICAgICB0b3RhbCA9IHRpY2tldE1vbmV5ICsgdGF4VGlja2V0TW9uZXk7DQogICAgICAgICAgICAgICAgICAgIHJvdy51bml0cGlyY2UgPSB0b3RhbC9vaWxBbW91bnQudG9GaXhlZCgyKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgcmVtb3ZlKCl7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLiRyZWZzLmFjY291bnRFc1RhYmxlLmdldFNlbGVjdGlvbigpOw0KICAgICAgICAgICAgICAgIGlmKGRhdGEgPT0gbnVsbCB8fCBkYXRhLmxlbmd0aCA9PT0gMCl7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fpgInmi6nopoHliKDpmaTnmoTmlbDmja4iKQ0KICAgICAgICAgICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIHRoaXMuJE1vZGFsLmNvbmZpcm0oew0KICAgICAgICAgICAgICAgICAgICB0aXRsZTogJ+aPkOekuicsDQogICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICc8cD7mmK/lkKbnoa7orqTliKDpmaTpgInkuK3kv6Hmga/vvJ88L3A+JywNCiAgICAgICAgICAgICAgICAgICAgb25PazogKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGIgPSB0cnVlOw0KICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGlkcyA9ICcnOw0KICAgICAgICAgICAgICAgICAgICAgICAgbGV0IHRvdGFsID0gdGhpcy5wYWdlVG90YWwNCiAgICAgICAgICAgICAgICAgICAgICAgIGZvcihsZXQgaT0wO2k8ZGF0YS5sZW5ndGg7aSsrKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbSA9IGRhdGFbaV07DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYoaXRlbS5pZCAhPSBudWxsICYmIGl0ZW0uaWQubGVuZ3RoID4gMCl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmKGl0ZW0ucGFicmlpZCl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWRzICs9IGl0ZW0uaWQgKyAnLCc7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5wYWdlVG90YWwgPSB0b3RhbA0KICAgICAgICAgICAgICAgICAgICAgICAgaWYoYil7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYoaWRzLmxlbmd0aCA+IDApew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZW1vdmVPaWxBY2NvdW50KGlkcykudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEuY29kZSA9PSAwKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICB9ZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+mAieS4reS/oeaBr+S4reacieS/oeaBr+i/mOayoeaciei3n+W9kumbhuWNleino+mZpOWFs+iBlO+8jOivt+WFiOino+mZpOWFs+iBlCcpDQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgIG9uQ2FuY2VsOiAoKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvcGVuQWRkQmlsbFBlck1vZGFsKG5hbWUpIHsNCiAgICAgICAgICAgICAgICBpZiAobmFtZSA9PT0gJ2N1cnJlbnQnKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRBY2NvdW50KCkNCiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5hbWUgPT09ICdhbGwnKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRBbGxBY2NvdW50KCkNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/liqDlhaXlvZLpm4bljZXvvIzlhajpg6jmnInmlYjlj7DotKYNCiAgICAgICAgICAgIHNlbGVjdGVkQWxsQWNjb3VudCgpew0KICAgICAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpcw0KICAgICAgICAgICAgICAgIHRoYXQuc3BpblNob3cgPSB0cnVlOw0KICAgICAgICAgICAgICAgIHNlbGVjdE9pbElkcyh0aGlzLmFjY291bnRPYmopLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5zcGluU2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5sZW5ndGggPT0gMCl7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGF0LmVycm9yVGlwcygn5peg5pyJ5pWI5pWw5o2u5Y+v5Yqg5YWl5b2S6ZuG5Y2VJykNCiAgICAgICAgICAgICAgICAgICAgfWVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGlkcyA9IFtdOw0KICAgICAgICAgICAgICAgICAgICAgICAgZm9yKGxldCBpPTA7aTxyZXMuZGF0YS5yb3dzLmxlbmd0aDtpKyspew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxldCBpdGVtID0gcmVzLmRhdGEucm93c1tpXTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZHMucHVzaChpdGVtLmlkKQ0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC4kcmVmcy5hZGRCaWxsUGVyLmluaXRBbW1ldGVyKGlkcywgMjEsdGhpcy5hY2NvdW50T2JqLmNvdW50cnkpOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgc2VsZWN0ZWRBY2NvdW50KCl7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLiRyZWZzLmFjY291bnRFc1RhYmxlLmdldFNlbGVjdGlvbigpOw0KICAgICAgICAgICAgICAgIGxldCBiID0gMTsNCiAgICAgICAgICAgICAgICBpZihkYXRhID09IG51bGwgfHwgZGF0YS5sZW5ndGggPT0gMCl7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCfor7fpgInmi6nopoHliqDlhaXlvZLpm4bljZXnmoTlj7DotKYnKQ0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgIGxldCBpZHMgPSBbXTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgICAgICAgICAgICAgICBpZihpdGVtLnN0YXR1cyA9PT0gNSl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYiA9IDMNCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIGlmKGl0ZW0uc3RhdHVzID09PSA0KXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBiPTQ7DQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICBpZHMucHVzaChpdGVtLmlkKQ0KICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgaWYoYiA9PT0gMSl7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLmFkZEJpbGxQZXIuaW5pdEFtbWV0ZXIoaWRzLDIxLHRoaXMuYWNjb3VudE9iai5jb3VudHJ5KTsNCiAgICAgICAgICAgICAgICAgICAgfWVsc2UgaWYoYiA9PT0gMikgew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+mAieS4reeahOWPsOi0puS4reWtmOWcqOS4tOaXtuaVsOaNru+8jOivt+WFiOS/neWtmOWGjeWKoOWFpeW9kumbhuWNle+8gScpDQogICAgICAgICAgICAgICAgICAgIH1lbHNlIGlmKGI9PT0zKXsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCfpgIDlm57nmoTlj7DotKbkuI3og73liqDlhaXlhbblroPlvZLpm4bljZXvvIzor7fngrnlh7tb6YeN5paw5Yqg5YWl5b2S6ZuG5Y2VXeaMiemSricpDQogICAgICAgICAgICAgICAgICAgIH1lbHNlIGlmKGI9PT00KXsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCfpgInmi6nnmoTlj7DotKbmnInlt7LliqDlhaXlvZLpm4bljZXnmoTlj7DotKbvvIzkuI3og73liqDlhaXlhbbku5blvZLpm4bljZUnKQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIG9wZW5Db21wbGV0ZWRQcmVNb2RhbCgpew0KICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuY29tcGxldGVkUHJlLmluaXRBbW1ldGVyKHRoaXMuYWNjb3VudE9iai5jb3VudHJ5LDIpOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGFnYWluSm9pbigpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy4kcmVmcy5hY2NvdW50RXNUYWJsZS5nZXRTZWxlY3Rpb24oKTsNCiAgICAgICAgICAgICAgICBsZXQgYiA9IHRydWU7DQogICAgICAgICAgICAgICAgaWYoZGF0YSA9PSBudWxsIHx8IGRhdGEubGVuZ3RoID09IDApew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygn6K+36YCJ5oup6KaB6YeN5paw5Yqg5YWl5b2S6ZuG5Y2V55qE5Y+w6LSmJykNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICBsZXQgaWRzID0gJyc7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgICAgICAgICAgICAgICAgbGV0IHN0YXR1cyA9IGl0ZW0uc3RhdHVzOw0KICAgICAgICAgICAgICAgICAgICAgICAgaWYoc3RhdHVzICE9IDUpew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGIgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIGlkcys9IGl0ZW0uaWQgKycsJw0KICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgaWYoYil7DQogICAgICAgICAgICAgICAgICAgICAgICByZUpvaW5CaWxscHJlKGlkcykudGhlbigocmVzKSA9PnsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5jb2RlPT0wKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6J+aPkOekuu+8muaTjeS9nOaIkOWKnycgLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHVyYXRpb246IDEwLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xvc2FibGU6IHRydWUNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAgICAgfWVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+WPquacieW3sumAgOWbnueahOWPsOi0puaJjeiDvemHjeaWsOWKoOWFpeW9kumbhuWNlScpDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgcmVmcmVzaCgpew0KICAgICAgICAgICAgICAgIGxldCBvYmogPSB0aGlzDQogICAgICAgICAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7DQogICAgICAgICAgICAgICAgICAgIG9iai5nZXRBY2NvdW50TWVzc2FnZXMoKQ0KICAgICAgICAgICAgICAgIH0sMjAwKTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB2YWxpZGF0ZSgpew0KICAgICAgICAgICAgICAgIGlmKHRoaXMuY29sdW1uc0luZGV4ICE9IDUpew0KICAgICAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lbnRlck9wZXJhdGUodGhpcy5jb2x1bW5zSW5kZXgpLmRhdGE7DQogICAgICAgICAgICAgICAgICAgIGlmKHZhbCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgLy8gaWYgKHRlc3ROdW1iZXIodmFsKSkgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN3aXRjaCAodGhpcy5jb2x1bW5zSW5kZXgpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAxOg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlT2lsVXNlQm9keSgpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDI6DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVGZWVTdGFydERhdGUoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAzOg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlT2lsSW1wb3J0VHlwZSgpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDQ6DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVPaWxBbW91bnQoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSA1Og0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlVGlja2V0TW9uZXkyKCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgNjoNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy52YWxpZGF0ZVRheFRpY2tldE1vbmV5KCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgNzoNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy52YWxpZGF0ZVRheFJhdGVTaG93KCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGNhc2UgMzoNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyAgICAgdGhpcy52YWxpZGF0ZUZlZVN0YXJ0RGF0ZSgpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDg6DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVPdGhlck1vbmV5KCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgOToNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy52YWxpZGF0ZU9pbEltcG9ydENhdGVnb3J5KCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIC8vIH1lbHNlew0KICAgICAgICAgICAgICAgICAgICAgICAgLy8gICAgIHRoaXMuZXJyb3JUaXBzKCfor7fovpPlhaXmlbDlrZfvvIEnKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvL+eUqOayueS4u+S9kw0KICAgICAgICAgICAgdmFsaWRhdGVPaWxVc2VCb2R5KCkgew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdE9pbFVzZUJvZHk7DQogICAgICAgICAgICAgICAgZGF0YS5vaWxVc2VCb2R5ID0gdmFsOw0KICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgICAgICAgIC8vIH0NCg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8v6LS555So5Y+R55Sf5pelDQogICAgICAgICAgICB2YWxpZGF0ZUVuZGRhdGUoKSB7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lZGl0RW5kRGF0ZTsNCiAgICAgICAgICAgICAgICBsZXQgaXNEaWFuID0gdmFsWzRdPT0nLicgJiYgdmFsWzddPT0nLicgfHwgdmFsWzRdPT0nLicgJiYgdmFsWzZdPT0nLicgOw0KICAgICAgICAgICAgICAgIGlmKCFpc0RpYW4pIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoIue7k+adn+aXtumXtOagvOW8j+S4jeato+ehru+8gSIpOw0KICAgICAgICAgICAgICAgICAgICB2YWwgPSAiIjsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgLy8gaWYgKHZhbCAhPSBkYXRhLm9sZF9lbmRkYXRlKSB7DQogICAgICAgICAgICAgICAgLy8gICAgIC8vIOmqjOivgeaIquatouaXpeacn+aWueazlQ0KICAgICAgICAgICAgICAgIC8vICAgICBsZXQgcmVzdWx0ID0gX3ZlcmlmeV9FbmREYXRlMShkYXRhLCB2YWwpOw0KICAgICAgICAgICAgICAgIC8vICAgICBpZiAocmVzdWx0KSB7DQogICAgICAgICAgICAgICAgLy8gICAgICAgICB0aGlzLmVycm9yVGlwcyhyZXN1bHQpOw0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgdGhpcy5teVN0eWxlW3RoaXMuZWRpdEluZGV4XS5lbmREYXRlID0gImVycm9yU3RsZSI7DQogICAgICAgICAgICAgICAgLy8gICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgLy8gICAgICAgICB0aGlzLm15U3R5bGVbdGhpcy5lZGl0SW5kZXhdLmVuZERhdGUgPSAibXlzcGFuIjsNCg0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgdGhpcy51cGRhdGVlbmRkYXRlKGRhdGEsIHZhbCkNCg0KICAgICAgICAgICAgICAgIC8vICAgICB9DQogICAgICAgICAgICAgICAgLy8gfSBlbHNlIGlmICh2YWwgPT0gZGF0YS5vbGRfZW5kZGF0ZSkgew0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVuZERhdGUgPSB2YWw7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB2YWxpZGF0ZU9pbEFtb3VudCgpIHsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgICAgICAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRPaWxBbW91bnQ7DQogICAgICAgICAgICAgICAgY29uc29sZS5sb2coZGF0YS5zdGFydERhdGUsICJkYXRhLnN0YXJ0RGF0ZSIpDQoNCg0KICAgICAgICAgICAgICAgIGRhdGEub2lsQW1vdW50ID0gdmFsOw0KICAgICAgICAgICAgICAgIGlmKGRhdGEucGFpZE1vbmV5ICYmIHZhbCE9IiIpIHsNCiAgICAgICAgICAgICAgICAgICAgKGRhdGEucGFpZE1vbmV5L2RhdGEub2lsQW1vdW50KS50b0ZpeGVkKDQpOzsNCiAgICAgICAgICAgICAgICB9ZWxzZSB7DQogICAgICAgICAgICAgICAgICAgIGRhdGEudW5pdFByaWNlID0gIiI7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgICAgICAgIC8vIGNvbnNvbGUubG9nKGRhdGEuc3RhcnREYXRlLnNwbGl0KCIuIilbMl0qMSwgImRhdGEuc3RhcnREYXRlLnNwbGl0KCIpDQogICAgICAgICAgICAgICAgLy8gY29uc29sZS5sb2coZGF0YS5lbmREYXRlLnNwbGl0KCIuIilbMl0qMSwgImRhdGEuZW5kRGF0ZS5zcGxpdCgiKQ0KICAgICAgICAgICAgICAgIC8vIGlmKGRhdGEuc3RhcnREYXRlICE9IiIgJiYgZGF0YS5lbmREYXRlICE9IiIpIHsNCiAgICAgICAgICAgICAgICAvLyAgICAgbGV0IHJpcWlMZW5naCA9IGRhdGEuZW5kRGF0ZS5zcGxpdCgiLiIpWzJdKjEgLSBkYXRhLnN0YXJ0RGF0ZS5zcGxpdCgiLiIpWzJdKjE7DQogICAgICAgICAgICAgICAgLy8gICAgIGRhdGEuaGVhdEFtb3VudCA9IHZhbCpyaXFpTGVuZ2gqNjAqMC43KjMuNi8xMDAwMDAwOw0KICAgICAgICAgICAgICAgIC8vICAgICBjb25zb2xlLmxvZyhkYXRhLmhlYXRBbW91bnQsICJkYXRhLmhlYXRBbW91bnQiKQ0KICAgICAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgICAgICAvLyBlbHNlIHsNCiAgICAgICAgICAgICAgICAvLyAgICAgdGhpcy5lcnJvclRpcHMoIuW8gOWni+aIluiAhee7k+adn+aXtumXtOS4jeiDveS4uuepuu+8gSIpOw0KICAgICAgICAgICAgICAgIC8vICAgICBkYXRhLmhlYXRBbW91bnQgPSAiIjsNCiAgICAgICAgICAgICAgICAvLyB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZ2V0RWRpdE9pbFR5cGVOYW1lKHYpIHsNCiAgICAgICAgICAgICAgICBsZXQgYTsNCiAgICAgICAgICAgICAgICBpZih2ID09IDEpIHsNCiAgICAgICAgICAgICAgICAgICAgYSA9ICfmsb3msrk5Mic7DQogICAgICAgICAgICAgICAgfWVsc2UgaWYodiA9PSAyKSB7DQogICAgICAgICAgICAgICAgICAgIGEgPSAn5rG95rK5OTUnOw0KICAgICAgICAgICAgICAgIH1lbHNlIGlmKHYgPT0gMykgew0KICAgICAgICAgICAgICAgICAgICBhID0gJ+axveayuTk4JzsNCiAgICAgICAgICAgICAgICB9ZWxzZSBpZih2ID09IDQpIHsNCiAgICAgICAgICAgICAgICAgICAgYSA9ICfmn7Tmsrkw5Y+3JzsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgcmV0dXJuIGE7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgdmFsaWRhdGVPaWxJbXBvcnRUeXBlKCkgew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZ2V0RWRpdE9pbFR5cGVOYW1lKHRoaXMuZWRpdE9pbFR5cGUpOw0KDQogICAgICAgICAgICAgICAgLy8gPE9wdGlvbiB2YWx1ZT0iMSIgbGFiZWw9IuaxveayuTkyIj48L09wdGlvbj4NCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIDxPcHRpb24gdmFsdWU9IjIiIGxhYmVsPSLmsb3msrk5NSI+PC9PcHRpb24+DQogICAgICAgICAgICAgICAgLy8gICAgICAgICA8T3B0aW9uIHZhbHVlPSIzIiBsYWJlbD0i5rG95rK5OTgiPjwvT3B0aW9uPg0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgPE9wdGlvbiB2YWx1ZT0iNCIgbGFiZWw9IuaftOayuTDlj7ciPjwvT3B0aW9uPg0KICAgICAgICAgICAgICAgIGRhdGEub2lsSW1wb3J0VHlwZSA9IHZhbDsNCiAgICAgICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvL+mqjOivgeaZruelqA0KICAgICAgICAgICAgLy8gdmFsaWRhdGVUaWNrZXRNb25leTIoKSB7DQogICAgICAgICAgICAvLyAgICAgLy8gZGVidWdnZXINCiAgICAgICAgICAgIC8vICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgICAgICAgLy8gICAgIGxldCB2YWwgPSB0aGlzLmVkaXRUaWNrZXRNb25leTsNCiAgICAgICAgICAgIC8vICAgICAvLyBjb25zb2xlLmxvZyhkYXRhLnBhaWRNb25leSwgZGF0YS5vaWxBbW91bnQsICJkYXRhLnBhaWRNb25leSwgZGF0YS5vaWxBbW91bnQiKTsNCiAgICAgICAgICAgIC8vICAgICAvLyBjb25zb2xlLmxvZyh2YWwsICJ2YWwiKTsNCiAgICAgICAgICAgIC8vICAgICAvLyBpZiAodmFsICE9IGRhdGEub2xkX3RpY2tldG1vbmV5KSB7DQogICAgICAgICAgICAvLyAgICAgICAgIC8vIHZhbCA9IHBhcnNlRmxvYXQodmFsKTsNCiAgICAgICAgICAgIC8vICAgICAgICAgLy8gZGF0YS50aWNrZXRNb25leSA9IF92ZXJpZnlfTW9uZXkoZGF0YSwgdmFsKTsNCiAgICAgICAgICAgIC8vICAgICAgICAgZGF0YS50aWNrZXRNb25leSA9IHZhbDsNCiAgICAgICAgICAgIC8vICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICAvLyAgICAgICAgIC8vIGRhdGEuaW5wdXR0aWNrZXRtb25leSA9IF92ZXJpZnlfTW9uZXkoZGF0YSwgdmFsKQ0KICAgICAgICAgICAgLy8gICAgICAgICAvLyBkYXRhLnRpY2tldG1vbmV5ID0gY2FsY3VsYXRlQWN0dWFsTW9uZXkoZGF0YSx2YWwpDQogICAgICAgICAgICAvLyAgICAgICAgIGxldCBwYWlkTW9uZXkgPSBkYXRhLnRpY2tldE1vbmV5KjErZGF0YS50YXhUaWNrZXRNb25leSoxK2RhdGEub3RoZXJGZWUqMTsNCiAgICAgICAgICAgIC8vICAgICAgICAgIGRhdGEucGFpZE1vbmV5ID0gcGFpZE1vbmV5LnRvRml4ZWQoMik7DQogICAgICAgICAgICAvLyAgICAgICAgIGxldCB1bml0UHJpY2UgPSBkYXRhLnBhaWRNb25leS9kYXRhLm9pbEFtb3VudCoxOw0KICAgICAgICAgICAgLy8gICAgICAgICBjb25zb2xlLmxvZyhkYXRhLnRpY2tldE1vbmV5LCAiZGF0YS50aWNrZXRNb25leSIpDQogICAgICAgICAgICAvLyAgICAgICAgIGRhdGEudW5pdFByaWNlID0gdW5pdFByaWNlLnRvRml4ZWQoNCk7DQogICAgICAgICAgICAvLyAgICAgICAgIC8vIGxldCB1bml0cGlyY2UgPSBkYXRhLm9pbEFtb3VudD8oZGF0YS5wYWlkTW9uZXkvZGF0YS5vaWxBbW91bnQqMSk6MDsNCg0KICAgICAgICAgICAgLy8gICAgICAgICAvLyB0aGlzLmNhbGN1bGF0ZUFsbChkYXRhKTsNCiAgICAgICAgICAgIC8vICAgICAvLyB9IGVsc2UgaWYgKHZhbCA9PSBkYXRhLm9sZF90aWNrZXRtb25leSkgew0KICAgICAgICAgICAgLy8gICAgIC8vICAgICBkYXRhLnRpY2tldE1vbmV5ID0gdmFsOw0KICAgICAgICAgICAgLy8gICAgIC8vICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgICAgIC8vICAgICAvLyAgICAgLy8gZGF0YS5pbnB1dHRpY2tldG1vbmV5ID0gdmFsOw0KICAgICAgICAgICAgLy8gICAgIC8vICAgICAvLyBkYXRhLnRpY2tldG1vbmV5ID0gY2FsY3VsYXRlQWN0dWFsTW9uZXkoZGF0YSx2YWwpDQogICAgICAgICAgICAvLyAgICAgLy8gICAgIC8vIHRoaXMuY2FsY3VsYXRlQWxsKGRhdGEpOw0KICAgICAgICAgICAgLy8gICAgIC8vIH0NCiAgICAgICAgICAgIC8vICAgICAvLyB0aGlzLnZhbGlkYXRlVW5pdFByaWNlKGRhdGEpDQogICAgICAgICAgICAvLyB9LA0KICAgICAgICAgICAgdmFsaWRhdGVUaWNrZXRNb25leTIoKSB7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lZGl0VGlja2V0TW9uZXk7DQogICAgICAgICAgICAgICAgY29uc29sZS5sb2coZGF0YS5wYWlkTW9uZXksIGRhdGEuY29hbEFtb3VudCwgImRhdGEucGFpZE1vbmV5LCBkYXRhLmNvYWxBbW91bnQiKTsNCiAgICAgICAgICAgICAgICBpZiAodmFsICE9IGRhdGEub2xkX3RpY2tldG1vbmV5KSB7DQogICAgICAgICAgICAgICAgICAgIHZhbCA9IHBhcnNlRmxvYXQodmFsKTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS50aWNrZXRNb25leSA9IF92ZXJpZnlfTW9uZXkoZGF0YSwgdmFsKTsNCiAgICAgICAgICAgICAgICAgICAgLy8gZGF0YS5pbnB1dHRpY2tldG1vbmV5ID0gX3ZlcmlmeV9Nb25leShkYXRhLCB2YWwpDQogICAgICAgICAgICAgICAgICAgIC8vIGRhdGEudGlja2V0bW9uZXkgPSBjYWxjdWxhdGVBY3R1YWxNb25leShkYXRhLHZhbCkNCiAgICAgICAgICAgICAgICAgICAgbGV0IHBhaWRNb25leSA9IGRhdGEudGlja2V0TW9uZXkqMStkYXRhLnRheFRpY2tldE1vbmV5KjErZGF0YS5vdGhlckZlZSoxOw0KICAgICAgICAgICAgICAgICAgICAgZGF0YS5wYWlkTW9uZXkgPSBwYWlkTW9uZXkudG9GaXhlZCgyKTsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHVuaXRQcmljZSA9IGRhdGEuY29hbEFtb3VudD8oZGF0YS5wYWlkTW9uZXkvZGF0YS5jb2FsQW1vdW50KjEpOjA7DQogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKHVuaXRQcmljZSwgInVuaXRQcmljZSIpDQogICAgICAgICAgICAgICAgICAgIGRhdGEudW5pdFByaWNlID0gdW5pdFByaWNlLnRvRml4ZWQoNCk7DQogICAgICAgICAgICAgICAgICAgIC8vIGxldCB1bml0cGlyY2UgPSBkYXRhLmNvYWxBbW91bnQ/KGRhdGEucGFpZE1vbmV5L2RhdGEuY29hbEFtb3VudCoxKTowOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgICAgICAgICAgICAgLy8gdGhpcy5jYWxjdWxhdGVBbGwoZGF0YSk7DQogICAgICAgICAgICAgICAgfSBlbHNlIGlmICh2YWwgPT0gZGF0YS5vbGRfdGlja2V0bW9uZXkpIHsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS50aWNrZXRNb25leSA9IHZhbDsNCiAgICAgICAgICAgICAgICAgICAgLy8gZGF0YS5pbnB1dHRpY2tldG1vbmV5ID0gdmFsOw0KICAgICAgICAgICAgICAgICAgICAvLyBkYXRhLnRpY2tldG1vbmV5ID0gY2FsY3VsYXRlQWN0dWFsTW9uZXkoZGF0YSx2YWwpDQogICAgICAgICAgICAgICAgICAgIC8vIHRoaXMuY2FsY3VsYXRlQWxsKGRhdGEpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAvLyB0aGlzLnZhbGlkYXRlVW5pdFByaWNlKGRhdGEpDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/pqozor4HkuJPnpagNCiAgICAgICAgICAgIHZhbGlkYXRlVGF4VGlja2V0TW9uZXkoKSB7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lZGl0VGF4VGlja2V0TW9uZXk7DQogICAgICAgICAgICAgICAgaWYgKHZhbCAhPSBkYXRhLm9sZF90YXh0aWNrZXRtb25leSkgew0KICAgICAgICAgICAgICAgICAgICB2YWwgPSBwYXJzZUZsb2F0KHZhbCk7DQogICAgICAgICAgICAgICAgICAgIGRhdGEudGF4VGlja2V0TW9uZXkgPSBfdmVyaWZ5X01vbmV5KGRhdGEsIHZhbCkNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICAgICAgICAgIGxldCBwYWlkTW9uZXkgPSBkYXRhLnRpY2tldE1vbmV5KjErZGF0YS50YXhUaWNrZXRNb25leSoxK2RhdGEub3RoZXJGZWUqMTsNCiAgICAgICAgICAgICAgICAgICAgIGRhdGEucGFpZE1vbmV5ID0gcGFpZE1vbmV5LnRvRml4ZWQoMik7DQogICAgICAgICAgICAgICAgICAgIC8vIGxldCB1bml0UHJpY2UgPSBkYXRhLm9pbEFtb3VudD8oZGF0YS5wYWlkTW9uZXkvZGF0YS5vaWxBbW91bnQpOjA7DQogICAgICAgICAgICAgICAgICAgIGxldCB1bml0UHJpY2UgPSBkYXRhLnBhaWRNb25leS9kYXRhLm9pbEFtb3VudDsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS51bml0UHJpY2UgPSB1bml0UHJpY2UudG9GaXhlZCg0KTsNCiAgICAgICAgICAgICAgICAgICAgLy8gZGF0YS50YXh0aWNrZXRtb25leSA9IGNhbGN1bGF0ZUFjdHVhbE1vbmV5KGRhdGEsdmFsKQ0KICAgICAgICAgICAgICAgICAgICAvLyBkYXRhLnRheEFtb3VudCA9IGRhdGEudGF4VGlja2V0TW9uZXkqZGF0YS50YXhSYXRlU2hvdzsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS50YXhBbW91bnQgPSBjb3VudFRheGFtb3VudDEoZGF0YSk7DQogICAgICAgICAgICAgICAgICAgIC8vIHRoaXMuY2FsY3VsYXRlQWxsKGRhdGEpOw0KICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAodmFsID09IGRhdGEub2xkX3RheHRpY2tldG1vbmV5KSB7DQogICAgICAgICAgICAgICAgICAgIGRhdGEudGF4VGlja2V0TW9uZXkgPSB2YWw7DQogICAgICAgICAgICAgICAgICAgIC8vIGRhdGEudGF4dGlja2V0bW9uZXkgPSBjYWxjdWxhdGVBY3R1YWxNb25leShkYXRhLHZhbCkNCiAgICAgICAgICAgICAgICAgICAgLy8gZGF0YS50YXhBbW91bnQgPSAoZGF0YS50YXhUaWNrZXRNb25leSoxKSooZGF0YS50YXhSYXRlU2hvdyoxKTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS50YXhBbW91bnQgPSBjb3VudFRheGFtb3VudDEoZGF0YSk7DQogICAgICAgICAgICAgICAgICAgIC8vIHRoaXMuY2FsY3VsYXRlQWxsKGRhdGEpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhkYXRhLnRheFJhdGVTaG93LCAiLnRheFJhdGVTaG93ZGF0YTU1NTU1NTU1NTUiKTsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhkYXRhLnRheFRpY2tldE1vbmV5LCAiLnRheFRpY2tldE1vbmV5Iik7DQogICAgICAgICAgICAgICAgLy8gdGhpcy52YWxpZGF0ZVVuaXRQcmljZShkYXRhKQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHZhbGlkYXRlVGF4UmF0ZVNob3coKSB7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lZGl0VGF4UmF0ZTsNCiAgICAgICAgICAgICAgICBkYXRhLnRheFJhdGVTaG93ID0gdmFsOw0KICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHZhbGlkYXRlRmVlU3RhcnREYXRlKCl7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lZGl0RmVlU3RhcnREYXRlOw0KICAgICAgICAgICAgICAgIGxldCByZXN1bHQgPSBfdmVyaWZ5X0ZlZVN0YXJ0RGF0ZShkYXRhLHZhbCk7DQogICAgICAgICAgICAgICAgaWYocmVzdWx0KXsvL+Wksei0peWwseW8ueWHuuaPkOekuuWGheWuue+8jOW5tuWwhuaVsOaNruaBouWkjeWIneWni+WMlg0KICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcyhyZXN1bHQpDQogICAgICAgICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZmVlU3RhcnREYXRlID0gdmFsOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgdmFsaWRhdGVVbml0UHJpY2UoKSB7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lZGl0VW5pdFByaWNlOw0KICAgICAgICAgICAgICAgIGRhdGEudW5pdFByaWNlID0gdmFsOw0KICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHZhbGlkYXRlT3RoZXJNb25leSgpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdE90aGVyTW9uZXk7DQogICAgICAgICAgICAgICAgaWYgKCF0ZXN0TnVtYmVyKHZhbCkpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+ivt+i+k+WFpeaVsOWtl++8gScpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBkYXRhLm90aGVyRmVlID0gdmFsOw0KICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgICAgICAgIGxldCBwYWlkTW9uZXkgPSBkYXRhLnRpY2tldE1vbmV5KjErZGF0YS50YXhUaWNrZXRNb25leSoxK2RhdGEub3RoZXJGZWUqMTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5wYWlkTW9uZXkgPSBwYWlkTW9uZXkudG9GaXhlZCgyKTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS51bml0UHJpY2UgPSAoZGF0YS5wYWlkTW9uZXkvZGF0YS5vaWxBbW91bnQpLnRvRml4ZWQoNCk7DQogICAgICAgICAgICAgICAgLy8gZGVidWdnZXIpDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgdmFsaWRhdGVPaWxJbXBvcnRDYXRlZ29yeSgpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdE9pbENhdGVnb3J5Ow0KICAgICAgICAgICAgICAgIGRhdGEub2lsSW1wb3J0Q2F0ZWdvcnkgPSB2YWw7DQogICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy8gdmFsaWRhdGVPdGhlck1vbmV5KCl7DQogICAgICAgICAgICAvLyAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgICAgICAgIC8vICAgICBsZXQgdmFsID0gdGhpcy5lZGl0T3RoZXJNb25leTsNCiAgICAgICAgICAgIC8vICAgICBkYXRhLm90aGVyTW9uZXkgPSB2YWw7DQogICAgICAgICAgICAvLyAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICAvLyAgICAgZGVidWdnZXINCiAgICAgICAgICAgIC8vIH0sDQogICAgICAgICAgICBzZXRyZW1hcmsoKXsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgICAgICAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRyZW1hcms7DQogICAgICAgICAgICAgICAgZGF0YS5yZW1hcmsgPSB2YWw7DQogICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgc2V0T2lsVXNlQm9keSgpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdE9pbFVzZUJvZHk7DQogICAgICAgICAgICAgICAgZGF0YS5vaWxVc2VCb2R5ID0gdmFsOw0KICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHNldE15U3R5bGUobGVuZ3RoKXsNCiAgICAgICAgICAgICAgICB0aGlzLm15U3R5bGU9W107DQogICAgICAgICAgICAgICAgZm9yKHZhciBpPTA7aTxsZW5ndGg7aSsrKXsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5teVN0eWxlLnB1c2goew0KICAgICAgICAgICAgICAgICAgICAgICAgY29hbFVzZUJvZHk6J215c3BhbicsDQogICAgICAgICAgICAgICAgICAgICAgICBmZWVTdGFydERhdGU6J215c3BhbicsDQogICAgICAgICAgICAgICAgICAgICAgICBvaWxBbW91bnQ6J215c3BhbicsDQogICAgICAgICAgICAgICAgICAgICAgICB0YXhUaWNrZXRNb25leTonbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlbWFyazonbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8vc3BhbueCueWHu+S6i+S7tuWwhnNwYW7mjaLmiJDovpPlhaXmoYblubbkuJTojrflj5bnhKbngrkNCiAgICAgICAgICAgIHNlbGVjdENhbGwocm93LGluZGV4LGNvbHVtbnMsc3RyKXsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRGZWVTdGFydERhdGUgPSByb3cuZmVlU3RhcnREYXRlOw0KICAgICAgICAgICAgICAgIHRoaXMuZWRpdE9pbFVzZUJvZHkgPSByb3cub2lsVXNlQm9keTsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRPaWxBbW91bnQgPSByb3cub2lsQW1vdW50ID09IG51bGwgfHwgcm93Lm9pbEFtb3VudD09PTA/bnVsbDpyb3cub2lsQW1vdW50Ow0KICAgICAgICAgICAgICAgIHRoaXMuZWRpdE9pbFR5cGUgPSByb3cub2lsSW1wb3J0VHlwZTsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRPaWxDYXRlZ29yeSA9IHJvdy5vaWxJbXBvcnRDYXRlZ29yeTsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRUaWNrZXRNb25leSA9IHJvdy50aWNrZXRNb25leTsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRUYXhUaWNrZXRNb25leSA9IHJvdy50YXhUaWNrZXRNb25leTsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRUYXhSYXRlID0gcm93LnRheFJhdGVTaG93Ow0KICAgICAgICAgICAgICAgIHRoaXMuZWRpdE90aGVyTW9uZXkgPSByb3cub3RoZXJGZWU7DQogICAgICAgICAgICAgICAgdGhpcy5lZGl0cmVtYXJrID0gcm93LnJlbWFyazsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRJbmRleCA9IGluZGV4Ow0KICAgICAgICAgICAgICAgIHRoaXMuY29sdW1uc0luZGV4ID0gY29sdW1uczsNCiAgICAgICAgICAgICAgICBsZXQgYT10aGlzOw0KICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgew0KICAgICAgICAgICAgICAgICAgICBhLiRyZWZzW3N0citpbmRleCtjb2x1bW5zXS5mb2N1cygpOw0KICAgICAgICAgICAgICAgIH0sMjAwKTsNCg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8v6Lez6L2s5Yiw5LiL5LiA5qC8DQogICAgICAgICAgICBuZXh0Q2VsbChkYXRhKXsNCiAgICAgICAgICAgICAgICBsZXQgaW5kZXggPSBkYXRhLmVkaXRJbmRleDsNCiAgICAgICAgICAgICAgICBsZXQgY29sdW1ucyA9IGRhdGEuY29sdW1uc0luZGV4Ow0KICAgICAgICAgICAgICAgIGxldCByb3cgPSAnJzsNCiAgICAgICAgICAgICAgICBpZihpbmRleCA9PT0gLTEgJiYgY29sdW1ucyA9PT0gLTEpew0KICAgICAgICAgICAgICAgICAgICBpbmRleCA9IDA7DQogICAgICAgICAgICAgICAgICAgIGNvbHVtbnMgPSAxOw0KICAgICAgICAgICAgICAgIH1lbHNlIGlmKGluZGV4ID4gLTEgJiYgY29sdW1ucyA9PT0gNSl7DQogICAgICAgICAgICAgICAgICAgIC8v5b2T6Lez6L2s55qE5pyA5ZCO5LiA6KGM5pyA5ZCO5LiA5qC855qE5pe25YCZDQogICAgICAgICAgICAgICAgICAgIGlmIChpbmRleCA+PSBkYXRhLnBhZ2VTaXplIC0gMSB8fCBpbmRleCA+PSBkYXRhLnBhZ2VUb3RhbCAtIDEpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4ID0gMDsNCiAgICAgICAgICAgICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgICAgICAgICAgICBpbmRleCArKzsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICBjb2x1bW5zID0gMTsNCiAgICAgICAgICAgICAgICB9ZWxzZXsNCiAgICAgICAgICAgICAgICAgICAgY29sdW1ucyArPSAxOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBkYXRhLmVkaXRJbmRleCA9IGluZGV4Ow0KICAgICAgICAgICAgICAgIGRhdGEuY29sdW1uc0luZGV4ID0gY29sdW1uczsNCiAgICAgICAgICAgICAgICByb3cgPSBkYXRhLnRiQWNjb3VudC5kYXRhW2luZGV4XTsNCiAgICAgICAgICAgICAgICBpZihyb3cpew0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRGZWVTdGFydERhdGUgPSByb3cuZmVlU3RhcnREYXRlOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRPaWxVc2VCb2R5ID0gcm93Lm9pbFVzZUJvZHk7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZWRpdE9pbEFtb3VudCA9IHJvdy5vaWxBbW91bnQgPT0gbnVsbCB8fCByb3cub2lsQW1vdW50PT09MD9udWxsOnJvdy5vaWxBbW91bnQ7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZWRpdE9pbFR5cGUgPSByb3cub2lsSW1wb3J0VHlwZTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0T2lsQ2F0ZWdvcnkgPSByb3cub2lsSW1wb3J0Q2F0ZWdvcnk7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZWRpdFRpY2tldE1vbmV5ID0gcm93LnRpY2tldE1vbmV5Ow0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRUYXhUaWNrZXRNb25leSA9IHJvdy50YXhUaWNrZXRNb25leTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0VGF4UmF0ZSA9IHJvdy50YXhSYXRlU2hvdzsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0T3RoZXJNb25leSA9IHJvdy5vdGhlckZlZTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0cmVtYXJrID0gcm93LnJlbWFyazsNCg0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS4kcmVmc1tkYXRhLmVudGVyT3BlcmF0ZShjb2x1bW5zKS5zdHIraW5kZXgrY29sdW1uc10uZm9jdXMoKTsNCiAgICAgICAgICAgICAgICB9LDIwMCk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/moLnmja7liJflj7fov5Tlm57lr7nlupTnmoTliJflkI0NCiAgICAgICAgICAgIGVudGVyT3BlcmF0ZShudW1iZXIpew0KICAgICAgICAgICAgICAgIGxldCBzdHIgPSAnJzsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IG51bGw7DQogICAgICAgICAgICAgICAgc3dpdGNoIChudW1iZXIpIHsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSAxOg0KICAgICAgICAgICAgICAgICAgICAgICAgc3RyID0gJ29pbFVzZUJvZHknOw0KICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdE9pbFVzZUJvZHk7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSAyOg0KICAgICAgICAgICAgICAgICAgICAgICAgc3RyID0gJ2ZlZVN0YXJ0RGF0ZSc7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0RmVlU3RhcnREYXRlOw0KICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgIGNhc2UgMzoNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICdvaWxJbXBvcnRUeXBlJw0KICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdE9pbFR5cGU7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSA0Og0KICAgICAgICAgICAgICAgICAgICAgICAgc3RyID0gJ29pbEFtb3VudCc7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0T2lsQW1vdW50Ow0KICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgIGNhc2UgNToNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICd0aWNrZXRNb25leSc7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0VGlja2V0TW9uZXk7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSA2Og0KICAgICAgICAgICAgICAgICAgICAgICAgc3RyID0gJ3RheFRpY2tldE1vbmV5JzsNCiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRUYXhUaWNrZXRNb25leTsNCiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICBjYXNlIDc6DQogICAgICAgICAgICAgICAgICAgICAgICBzdHIgPSAndGF4UmF0ZVNob3cnOw0KICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdFRheFJhdGU7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSA4Og0KICAgICAgICAgICAgICAgICAgICAgICAgc3RyID0gJ290aGVyRmVlJzsNCiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRPdGhlck1vbmV5Ow0KICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgIGNhc2UgOToNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICdvaWxJbXBvcnRDYXRlZ29yeSc7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0T2lsQ2F0ZWdvcnk7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSAxMDoNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICdyZW1hcmsnOw0KICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdHJlbWFyazsNCiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICByZXR1cm4ge3N0cjpzdHIsZGF0YTpkYXRhfTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBwcmVkKCl7DQogICAgICAgICAgICAgICAgdmFyIGxldHQgPSB0aGlzOw0KICAgICAgICAgICAgICAgIGxldCBpbmRleCA9IGxldHQuZWRpdEluZGV4Ow0KICAgICAgICAgICAgICAgIGxldCBjb2x1bW5zID0gbGV0dC5jb2x1bW5zSW5kZXg7DQogICAgICAgICAgICAgICAgaWYoaW5kZXggPT09IC0xICYmIGNvbHVtbnMgPT09IC0xKXsNCiAgICAgICAgICAgICAgICAgICAgaW5kZXggPSAwOw0KICAgICAgICAgICAgICAgICAgICBjb2x1bW5zID0gMTsNCiAgICAgICAgICAgICAgICAgICAgbGV0dC5lZGl0SW5kZXggPSBpbmRleDsNCiAgICAgICAgICAgICAgICAgICAgbGV0dC5jb2x1bW5zSW5kZXggPSBjb2x1bW5zOw0KICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldHQuJHJlZnNbbGV0dC5lbnRlck9wZXJhdGUoY29sdW1ucykuc3RyK2luZGV4K2NvbHVtbnNdLmZvY3VzKCk7DQogICAgICAgICAgICAgICAgICAgIH0sMjAwKTsNCiAgICAgICAgICAgICAgICB9ZWxzZXsNCiAgICAgICAgICAgICAgICAgICAgbGV0dC52YWxpZGF0ZSgpDQogICAgICAgICAgICAgICAgICAgIGxldHQuc2V0cmVtYXJrKCkNCiAgICAgICAgICAgICAgICAgICAgbGV0dC5uZXh0Q2VsbChsZXR0KQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBlbGxpcHNpcyAodmFsdWUpIHsNCiAgICAgICAgICAgICAgICBpZiAoIXZhbHVlKSByZXR1cm4gJycNCiAgICAgICAgICAgICAgICBpZiAodmFsdWUubGVuZ3RoID4gMTAwKSB7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZS5zbGljZSgwLDEwMCkgKyAnLi4uJw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWUNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBoYW5kbGVVcGxvYWRTdWNjZXNzKCkgew0KDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaGFuZGxlRm9ybWF0RXJyb3IoZmlsZSkgew0KICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKA0KICAgICAgICAgICAgICAgICAgICBmaWxlLm5hbWUgKyAiIOagvOW8j+S4jeato+ehruOAguWPquiDveS4iuS8oOWQjue8gOWQjeS4uiB4bHPmiJbogIUgeGxzeCDnmoTmlofku7YiDQogICAgICAgICAgICAgICAgKTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBoYW5kbGVQcm9ncmVzcyhldmVudCwgZmlsZSkgew0KICAgICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbyh7DQogICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6IGZpbGUubmFtZSArICIg5q2j5Zyo5LiK5Lyg44CCIiwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvLyDlr7zlhaUNCiAgICAgICAgICAgIG9uRXhjZWxVcGxvYWQoZmlsZSkgew0KICAgICAgICAgICAgICAgIGlmICghZmlsZSkgew0KICAgICAgICAgICAgICAgICAgICB0aGlzLiROb3RpY2UuZXJyb3Ioew0KICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfmj5DnpLonLA0KICAgICAgICAgICAgICAgICAgICAgICAgZGVzYzogJ+ivt+mAieaLqeimgeS4iuS8oOeahOaWh+S7tu+8gScsDQogICAgICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTANCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBsZXQgZmlsZU5hbWUgPSBmaWxlLm5hbWUubGFzdEluZGV4T2YoIi4iKTsvL+WPluWIsOaWh+S7tuWQjeW8gOWni+WIsOacgOWQjuS4gOS4queCueeahOmVv+W6pg0KICAgICAgICAgICAgICAgIGxldCBmaWxlTmFtZUxlbmd0aCA9IGZpbGUubmFtZS5sZW5ndGg7Ly/lj5bliLDmlofku7blkI3plb/luqYNCiAgICAgICAgICAgICAgICBsZXQgZmlsZUZvcm1hdCA9IGZpbGUubmFtZS5zdWJzdHJpbmcoZmlsZU5hbWUgKyAxLCBmaWxlTmFtZUxlbmd0aCk7Ly/miKoNCiAgICAgICAgICAgICAgICBpZigneGxzJyAhPSBmaWxlRm9ybWF0ICYmICd4bHN4JyAhPSBmaWxlRm9ybWF0KXsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kTm90aWNlLmVycm9yKHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn5o+Q56S6JywNCiAgICAgICAgICAgICAgICAgICAgICAgIGRlc2M6IGZpbGUubmFtZSArICcg5qC85byP5LiN5q2j56Gu44CC5Y+q6IO95LiK5Lyg5ZCO57yA5ZCN5Li6IHhsc+aIluiAhSB4bHN4IOeahOaWh+S7ticsDQogICAgICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTANCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgbGV0IHBhcmFtID0ge30NCiAgICAgICAgICAgICAgICBsZXQgZXhjZWwgPSB7ZmlsZTogZmlsZX0NCiAgICAgICAgICAgICAgICBsZXQgdGhhdCA9IHRoaXMNCiAgICAgICAgICAgICAgICB0aGF0LnNwaW5TaG93ID0gdHJ1ZQ0KICAgICAgICAgICAgICAgIGF4aW9zLnJlcXVlc3Qoew0KICAgICAgICAgICAgICAgICAgICB1cmw6ICcvYnVzaW5lc3Mvb2lsL2FjY291bnQvaW1wb3J0JywNCiAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiAncG9zdCcsDQogICAgICAgICAgICAgICAgICAgIGRhdGE6IE9iamVjdC5hc3NpZ24oe30sIHBhcmFtLCBleGNlbCkNCiAgICAgICAgICAgICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5zcGluU2hvdyA9IGZhbHNlDQogICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdHIpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuJE1lc3NhZ2UuaW5mbyh7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGVudDogcmVzLmRhdGEuc3RyLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAwLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsb3NhYmxlOiB0cnVlDQogICAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuc2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5zcGluU2hvdyA9IGZhbHNlDQogICAgICAgICAgICAgICAgICAgIHRoYXQuc2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlcnIpOw0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICAgICAgfSwNCg0KICAgICAgICAgICAgLy8g5a+85YWl5qih5p2/5LiL6L29DQogICAgICAgICAgICBsb2FkVGVtcGxhdGUoKSB7DQogICAgICAgICAgICAgICAgbGV0IHJlcSA9IHsNCiAgICAgICAgICAgICAgICAgICAgdXJsIDogIi9idXNpbmVzcy9vaWwvYWNjb3VudC90ZW1wbGF0ZS9sb2FkIiwNCiAgICAgICAgICAgICAgICAgICAgbWV0aG9kIDogImdldCIsDQogICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlVHlwZTogJ2Jsb2InLA0KICAgICAgICAgICAgICAgIH07DQogICAgICAgICAgICAgICAgYXhpb3MuZmlsZShyZXEpDQogICAgICAgICAgICAgICAgICAgIC50aGVuKHJlcyA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNwaW5TaG93ID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb250ZW50ID0gcmVzOw0KICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtjb250ZW50XSk7DQogICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBmaWxlTmFtZSA9ICLnlKjmsrnlj7DotKblr7zlhaXmqKHmnb8ueGxzeCI7DQogICAgICAgICAgICAgICAgICAgICAgICBpZiAoImRvd25sb2FkIiBpbiBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJhIikpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDpnZ5JReS4i+i9vQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGVsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsaW5rLmRvd25sb2FkID0gZmlsZU5hbWU7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxpbmsuc3R5bGUuZGlzcGxheSA9ICJub25lIjsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbGluay5ocmVmID0gVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGVsaW5rKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbGluay5jbGljaygpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwoZWxpbmsuaHJlZik7IC8vIOmHiuaUvlVSTCDlr7nosaENCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGVsaW5rKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gSUUxMCvkuIvovb0NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYXZpZ2F0b3IubXNTYXZlQmxvYihibG9iLCBmaWxlTmFtZSk7DQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBtb3VudGVkKCkgew0KICAgICAgICAgICAgdGhpcy52ZXJzaW9uID0gaW5kZXhEYXRhLnZlcnNpb247DQogICAgICAgICAgICB0aGlzLnRiQWNjb3VudC5jb2x1bW5zID0gdGhpcy50YkFjY291bnQudGFpbENvbHVtbjsNCiAgICAgICAgICAgIHRoaXMub2lsVHlwZXMgPSBibGlzdDEoIm9pbFR5cGUiKTsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMub2lsVHlwZXMsICJ0aGlzLm9pbFR5cGVzIik7DQogICAgICAgICAgICAvLyBkZWJ1Z2dlcg0KICAgICAgICAgICAgdGhpcy5vaWxDYXRlZ29yeXMgPSBibGlzdDEoIm9pbENhdGVnb3J5Iik7DQogICAgICAgICAgICB0aGlzLmFjY291bnRPYmoub2lsQ2F0ZWdvcnkgPSB0aGlzLm9pbENhdGVnb3J5c1swXS50eXBlQ29kZTsNCiAgICAgICAgICAgIHRoaXMuYWNjb3VudE9iai5vaWxUeXBlID0gdGhpcy5vaWxUeXBlc1swXS50eXBlQ29kZTsNCiAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpcw0KICAgICAgICAgICAgZ2V0VXNlckJ5VXNlclJvbGUoKS50aGVuKHJlcyA9PiB7Ly/moLnmja7mnYPpmZDojrflj5bliIblhazlj7gNCiAgICAgICAgICAgICAgICB0aGF0LmNvbXBhbmllcyA9IHJlcy5kYXRhLmNvbXBhbmllczsNCiAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5pc0NpdHlBZG1pbiA9PSB0cnVlIHx8IHJlcy5kYXRhLmlzUHJvQWRtaW4gPT0gdHJ1ZSB8fCByZXMuZGF0YS5pc1N1YkFkbWluID09IHRydWUpew0KICAgICAgICAgICAgICAgICAgICB0aGF0LmlzQWRtaW4gPSB0cnVlOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBnZXRDb3VudHJ5c2RhdGEoe29yZ0NvZGU6cmVzLmRhdGEuY29tcGFuaWVzWzBdLmlkfSkudGhlbihyZXMgPT4gey8v5qC55o2u5p2D6ZmQ6I635Y+W5omA5bGe6YOo6ZeoDQogICAgICAgICAgICAgICAgICAgIHRoYXQuZGVwYXJ0bWVudHMgPSByZXMuZGF0YTsNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5nZXRVc2VyRGF0YSgpOw0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICB9DQo="}, {"version": 3, "sources": ["addOilAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addOilAccount.vue", "sourceRoot": "src/view/account", "sourcesContent": ["<style lang=\"less\">\r\n    .mytable .ivu-table-cell{\r\n        padding-left: 5px;\r\n        padding-right: 5px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: normal;\r\n        word-break: break-all;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .accountEs .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .accountEs .header-bar-show {\r\n        max-height: 300px;\r\n        padding-top: 14px;\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .accountEs .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n\r\n\r\n    .mytable .myspan{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block\r\n    }\r\n    .mytable .errorStle{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block;\r\n        color:red;\r\n    }\r\n</style>\r\n<template>\r\n    <div>\r\n        <div class=\"accountEs\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.accountno\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{ item.name }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"关键字:\" prop=\"oilUseBody\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"accountObj.oilUseBody\" placeholder=\"用能主体关键字模糊查询\" :style=\"formItemWidth\" />\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"用油类型:\" prop=\"oilType\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.oilType\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in oilTypes\" :value=\"item.typeCode\" :key=\"item.typeCode\">{{ item.typeName }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"用油类别:\" prop=\"oilCategory\" class=\"form-line-height\">\r\n                                <Select clearable v-model=\"accountObj.oilCategory\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in oilCategorys\" :value=\"item.typeCode\" :key=\"item.typeCode\">{{ item.typeName }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.company\" @on-change=\"selectChange(accountObj.company)\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"accountObj.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly :style=\"formItemWidth\"/>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <div  align=\"right\">\r\n                        <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n                        <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n                    </div>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <div>\r\n                <Row>\r\n                    <Col span=\"12\">\r\n                        <Page size=\"small\" :total=\"pageTotal\" :current=\"pageNum\" :page-size=\"pageSize\" show-elevator show-sizer show-total\r\n                              placement=\"top\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n                    </Col>\r\n                    <Col span=\"12\">\r\n                        <div align=\"right\" class=\"account\">\r\n                            <Button type=\"primary\" @click=\"addNewCoalAccount\">新增</Button>\r\n                            <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n                            <Button type=\"error\" @click=\"remove\">删除</Button>\r\n                            <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n                                <Button type='info' style=\"margin-left: 5px\">加入归集单\r\n                                    <Icon type='ios-arrow-down'></Icon>\r\n                                </Button>\r\n                                <DropdownMenu slot='list'>\r\n                                    <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n                                    <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n                                </DropdownMenu>\r\n                            </Dropdown>\r\n                            <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n                            <Button type=\"primary\" @click=\"loadTemplate\">导入模板下载</Button>\r\n                            <Upload style=\"float:right;\" :on-format-error=\"handleFormatError\"\r\n                                    :before-upload='onExcelUpload' :on-progress=\"handleProgress\"\r\n                                    :on-success=\"handleUploadSuccess\" :max-size=\"10240\" action=\"_blank\"\r\n                                    accept=\".csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\r\n                                    :format=\"['xls','xlsx']\">\r\n                                <Button icon='ios-cloud-upload'>导入</Button>\r\n                            </Upload>\r\n                        </div>\r\n                    </Col>\r\n                </Row>\r\n            </div>\r\n            <Table ref=\"accountEsTable\"\r\n                   border\r\n                   :columns=\"tbAccount.tailColumn\"\r\n                   :data=\"tbAccount.data\"\r\n                   class=\"mytable\">\r\n                <!--备注-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=500 v-model=\"editremark\" :ref=\"'remark'+index+10\" type=\"text\" @on-blur=\"setremark\"\r\n                               v-if=\"editIndex === index && columnsIndex === 10\"/>\r\n                        <Tooltip placement=\"left\" max-width=\"600\" v-else>\r\n                            <span :class=\"myStyle[index].remark\" style=\"width: 60px\" @click=\"selectCall(row,index,10,'remark')\">\r\n                                {{ ellipsis(row.remark) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                    <div v-else>\r\n                        <Tooltip placement=\"bottom\" max-width=\"200\">\r\n                            <span>{{ ellipsis(row.remark)}}</span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--用油主体-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"oilUseBody\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=100 v-model=\"editOilUseBody\" :ref=\"'oilUseBody'+index+1\" type=\"text\" @on-blur=\"validate\"\r\n                               v-if=\"editIndex === index && columnsIndex === 1\"/>\r\n                        <Tooltip placement=\"right\" max-width=\"200\" v-else>\r\n                            <span :class=\"myStyle[index].oilUseBody\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,1,'oilUseBody')\">\r\n                                {{ ellipsis(row.oilUseBody) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.oilUseBody }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--费用发生日-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"feeStartDate\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'feeStartDate'+index+2\" type=\"text\" v-model=\"editFeeStartDate\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 2\" />\r\n                    <span :class=\"myStyle[index].feeStartDate\" @click=\"selectCall(row,index,2,'feeStartDate')\" v-else>{{ row.feeStartDate }}</span>\r\n                </template>\r\n                <!--用油类型-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"oilImportType\" v-if=\"row.total == null\">\r\n                    <Select :ref=\"'oilImportType'+index+3\" type=\"text\" v-model=\"editOilType\" @on-change=\"validate\"\r\n                            v-if=\"editIndex === index && columnsIndex === 3\" transfer=\"true\">\r\n                        <Option value=\"1\" label=\"汽油92\"></Option>\r\n                        <Option value=\"2\" label=\"汽油95\"></Option>\r\n                        <Option value=\"3\" label=\"汽油98\"></Option>\r\n                        <Option value=\"4\" label=\"柴油0号\"></Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].oilImportType\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,3,'oilImportType')\" v-else>{{ row.oilImportType }}</span>\r\n                </template>\r\n                <!--加油量-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"oilAmount\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'oilAmount'+index+4\" type=\"text\" v-model=\"editOilAmount\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 4\"/>\r\n                    <span :class=\"myStyle[index].oilAmount\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,4,'oilAmount')\" v-else>{{ row.oilAmount }}</span>\r\n                </template>\r\n                <!--普票含税金额-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"ticketMoney\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'ticketMoney'+index+5\" type=\"text\" v-model=\"editTicketMoney\" @on-change=\"validateTicketMoney2\"\r\n                           v-if=\"editIndex === index && columnsIndex === 5\"/>\r\n                    <span :class=\"myStyle[index].ticketMoney\"\r\n                     style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\"\r\n                     @click=\"selectCall(row,index,5,'ticketMoney')\" v-else>{{ row.ticketMoney }}</span>\r\n                </template>\r\n                <!--专票含税金额-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"taxTicketMoney\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'taxTicketMoney'+index+6\" type=\"text\" v-model=\"editTaxTicketMoney\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 6\" />\r\n                    <span :class=\"myStyle[index].taxTicketMoney\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,6,'taxTicketMoney')\" v-else>{{ row.taxTicketMoney }}</span>\r\n                </template>\r\n                <!--专票税率-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"taxRateShow\" v-if=\"row.total == null\">\r\n                    <Select :ref=\"'taxRateShow'+index+7\" type=\"text\" v-model=\"editTaxRate\" @on-change=\"settaxrate\"\r\n                            v-if=\"editIndex === index && columnsIndex === 7\" transfer=\"true\">\r\n                        <Option value=\"1\">1</Option>\r\n                        <Option value=\"3\">3</Option>\r\n                        <Option value=\"6\">6</Option>\r\n                        <Option value=\"13\">13</Option>\r\n                        <Option value=\"16\">16</Option>\r\n                        <Option value=\"17\">17</Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].taxRateShow\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,7,'taxRateShow')\" v-else>{{ row.taxRateShow }}</span>\r\n                </template>\r\n                <!--其他费用-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"otherFee\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'otherFee'+index+8\" type=\"text\" v-model=\"editOtherMoney\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 8\" />\r\n                    <span v-else :class=\"myStyle[index].otherFee\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,8,'otherFee')\">{{ row.otherFee }}</span>\r\n                </template>\r\n                <!--用油类别-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"oilImportCategory\" v-if=\"row.total == null\">\r\n                    <Select :ref=\"'oilImportCategory'+index+9\" type=\"text\" v-model=\"editOilCategory\" @on-change=\"validate\"\r\n                            v-if=\"editIndex === index && columnsIndex === 9\" transfer=\"true\">\r\n                        <Option value=\"固定源\" label=\"固定源\"></Option>\r\n                        <Option value=\"移动源\" label=\"移动源\"></Option>\r\n                    </Select>\r\n                    <span v-else :class=\"myStyle[index].oilImportCategory\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,9,'oilImportCategory')\">{{ row.oilImportCategory }}</span>\r\n                </template>\r\n            </Table>\r\n            <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n        </div>\r\n        <div>\r\n            <add-bill-per ref=\"addBillPer\"\r\n                          v-on:refreshList=\"refresh\"></add-bill-per >\r\n            <completed-pre-modal ref=\"completedPre\" v-on:refreshList=\"refresh\"></completed-pre-modal>\r\n            <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n            <upload-file-modal ref=\"uploadFileModal\" v-on:onchange=\"change\"></upload-file-modal>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport UploadFileModal from \"@/view/account/uploadFileModal\";\r\nimport {\r\n    _verify_StartDate,\r\n    judgeNumber,\r\n    _verify_EndDate,\r\n    _verify_PrevTotalReadings,\r\n    _verify_CurTotalReadings,\r\n    other_no_ammeteror_protocol,\r\n    self_no_ammeteror_protocol,\r\n    HFL_ammeteror,\r\n    judging_editability,\r\n    judging_editability1,\r\n    _verify_Money,\r\n    _calculateUsedReadings,\r\n    _calculateTotalReadings,\r\n    _calculateUnitPriceByUsedMoney,\r\n    _calculateAccountMoney,\r\n    _calculateQuotereadingsratio,\r\n    requiredFieldValidator,\r\n    countTaxamount1,\r\n    countTaxamount,\r\n    calculateActualMoney,\r\n    judge_negate,\r\n    judge_recovery,\r\n    judge_yb,\r\n    unitpirceMin,\r\n    unitpirceMax\r\n} from '@/view/account/PowerAccountController';\r\n    import {\r\n        saveOilAccount,\r\n        removeOilAccount,\r\n        selectOilIds\r\n    } from '@/api/coalHeatOilAccount';\r\n    import checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\n    import checkResult from \"@/view/account/check/checkResult\";\r\n    import alarmCheck from \"@/view/account/check/alarmCheck\";\r\n    import {getDates,testNumber,} from '@/view/account/powerAccountHelper';\r\n    import axios from '@/libs/api.request';\r\n    import SelectAmmeter from \"./selectAmmeter\";\r\n    import {_verify_FeeStartDate} from '@/view/account/PowerAccountEs';\r\n    import AddBillPer from \"./addOilBillPreModal\";\r\n    import {reJoinBillpre} from '@/api/accountBillPer';\r\n    import {blist1} from \"@/libs/tools\";\r\n    import {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\n    import CompletedPreModal from \"./completedPreModal\";\r\n    import indexData from '@/config/index'\r\n    import CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import {getUserdata,getUserByUserRole,getCountrysdata,getCountryByUserId} from '@/api/basedata/ammeter.js'\r\n    let dates=getDates();\r\n    export default {\r\n        name: 'addCoalAccount',\r\n        components: {UploadFileModal, alarmCheck, checkResult, checkResultAndResponse,CompletedPreModal, SelectAmmeter,AddBillPer,CountryModal},\r\n        data() {\r\n            let photo = (h, {row, index}) => {\r\n                let that = this\r\n                let str = ''\r\n                if (row.projectname != '小计' && row.projectname != '合计') {\r\n                    str = '上传附件'\r\n                }\r\n                return h(\"div\", [h(\"u\", {\r\n                    on: {\r\n                        click() {\r\n                            //打开弹出框\r\n                            if (row.projectname != '小计' && row.projectname != '合计') {\r\n                                that.uploadFile(row)\r\n                            }\r\n                        }\r\n                    }\r\n                }, str)]);\r\n            };\r\n            return {\r\n                submit:[],\r\n                submit2:[],\r\n                showCheckModel:false,\r\n                showJhModel:false,\r\n                showAlarmModel:false,\r\n                formItemWidth: widthstyle,\r\n                version:'',\r\n                dateList:dates,\r\n                filterColl: true,//搜索面板展开\r\n                editIndex: -1,//当前编辑行\r\n                columnsIndex:-1,//当前编辑列\r\n                myStyle:[],//样式\r\n                editOilUseBody:'',\r\n                editFeeStartDate:'',\r\n                editOilType:'',\r\n                editOilAmount:'',\r\n                editTicketMoney:'',\r\n                editTaxTicketMoney:'',\r\n                editTaxRate:'',\r\n                editOtherMoney:'',\r\n                editOilCategory:'',\r\n                spinShow:false,//遮罩\r\n                categorys:[],//描述类型\r\n                editremark:'',\r\n                accountStatus:[],\r\n                companies:[],\r\n                oilTypes: [],\r\n                oilCategorys: [],\r\n                departments:[],\r\n                isAdmin:false,\r\n                company:null,//用户默认公司\r\n                country:null,//用户默认所属部门\r\n                countryName:null,//用户默认所属部门\r\n                accountObj:{\r\n                    accountno:dates[1].code,//期号,默认当前月\r\n                    company:\"\",//分公司\r\n                    country:\"\",//所属部门\r\n                    oilUseBody:null,//用油主体\r\n                    oilCategory:1,\r\n                    oilType:1,\r\n                    oilAccountType: 1,\r\n                    countryName: \"\",\r\n\r\n                },\r\n                tbAccount: {\r\n                    loading: true,\r\n                    columns: [],\r\n                    tailColumn: [\r\n                        {type: 'selection', width: 60, align: 'center',},\r\n                        {\r\n                            title: \"期号\",\r\n                            key: \"accountNo\",\r\n                            align: \"center\",\r\n                            width: 90,\r\n                        },\r\n                        {\r\n                            title: \"用油主体\",\r\n                            slot: \"oilUseBody\",\r\n                            align: \"center\",\r\n                            width: 150,\r\n                        },\r\n                        {\r\n                            title: \"费用发生日\",\r\n                            slot: \"feeStartDate\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"用油类型\",\r\n                            slot: \"oilImportType\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"加油量(L)\",\r\n                            slot: \"oilAmount\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"单价(元)\",\r\n                            key: \"unitPrice\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"普票含税金额(元)\",\r\n                            slot: \"ticketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票含税金额(元)\",\r\n                            slot: \"taxTicketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票税率（%）\",\r\n                            slot: \"taxRateShow\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"专票税额\",\r\n                            key: \"taxAmount\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"其他(元)\",\r\n                            slot: \"otherFee\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"实缴费用(元)含税\",\r\n                            key: \"paidMoney\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {\r\n                            title: \"用油类别\",\r\n                            slot: \"oilImportCategory\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {title: \"附件\", align: \"center\", render: photo, width: 100},\r\n                        {title: \"备注\", slot: \"remark\",align: \"center\", width: 150},\r\n                    ],\r\n                    data: [],\r\n                },\r\n                pageTotal: 0,\r\n                pageNum: 1,\r\n                pageSize: 10,//当前页\r\n            }\r\n        },\r\n        methods: {\r\n            change() {\r\n\r\n            },\r\n            uploadFile(row) {\r\n                console.log(row, \"row\");\r\n                // let id;\r\n                // if(!row.id2) {\r\n                //     editAmmeter('', 0).then(res => {\r\n                //         debugger\r\n                //         console.log(res, \"res\");\r\n                //         row.id2 = res.data.id;\r\n\r\n                //         this.id2 = res.data.id\r\n                //         // debugger\r\n                //         // this.fileParam.busiId = ;\r\n                //         this.$refs.uploadFileModal.choose(row.id2 + '');\r\n                //     })\r\n                // }else {\r\n\r\n                if(row.id) {\r\n                    this.$refs.uploadFileModal.choose(row.id + '');\r\n                }else {\r\n                    this.errorTips(\"请先保存后再上传文件！\");\r\n                }\r\n                // }\r\n                // console.log(row, \"row\");\r\n            },\r\n            settaxrate() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxRate;\r\n                data.taxRateShow = val;\r\n                data.editType = 1;\r\n                data.taxAmount = data.taxRateShow*data.taxTicketMoney/100;\r\n                // let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                // data.paidMoney = paidMoney.toFixed(2);\r\n            },\r\n            selectChange(){\r\n                let that = this;\r\n                if (that.accountObj.company != undefined) {\r\n                    if(that.accountObj.company == \"-1\"){\r\n                        that.accountObj.country = -1;\r\n                        that.accountObj.countryName = null;\r\n                    }else{\r\n                        getCountryByUserId(that.accountObj.company).then(res => {\r\n                            if(res.data.departments.length != 0){\r\n                                that.accountObj.country = res.data.departments[0].id;\r\n                                that.accountObj.countryName = res.data.departments[0].name;\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            },\r\n            //选择所属部门开始\r\n            chooseResponseCenter() {\r\n                if(this.accountObj.company == null || this.accountObj.company == \"-1\" ){\r\n                    this.$Message.info(\"请先选择分公司\");return;\r\n                }\r\n                this.$refs.countryModal.choose(this.accountObj.company);//所属部门\r\n            },\r\n            getDataFromModal(data) {\r\n                this.accountObj.country = data.id;\r\n                this.accountObj.countryName = data.name;\r\n                //选择所属部门结束\r\n            },\r\n            getUserData(){\r\n                let that = this;\r\n                getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                    if(res.data.companies.length != 0){\r\n                        let companies = res.data.companies;\r\n                        if(res.data.companies[0].id == \"**********\"){\r\n                            companies = that.companies;\r\n                        }\r\n                        that.company = companies[0].id;\r\n                        that.accountObj.company = companies[0].id;\r\n                    }\r\n                    if(res.data.departments.length != 0){\r\n                        let departments = res.data.departments;\r\n                        if(res.data.companies[0].id == \"**********\" && that.departments.length != 0){\r\n                            departments = that.departments\r\n                        }\r\n                        that.country = departments[0].id;\r\n                        that.countryName = departments[0].name;\r\n                        that.accountObj.country = Number(departments[0].id);\r\n                        that.accountObj.countryName = departments[0].name;\r\n                    }\r\n\r\n                    that.pageNum = 1\r\n                    that.getAccountMessages();\r\n                });\r\n            },\r\n            searchList(){\r\n                if(this.accountObj.countryName == \"\"){\r\n                    this.accountObj.country = \"-1\";\r\n                }\r\n                this.pageNum = 1;\r\n                this.getAccountMessages()\r\n            },\r\n            accountnoChange(){\r\n                this.searchList()\r\n            },\r\n            //点击保存\r\n            preserve() {\r\n                let dataL = this.$refs.accountEsTable.getSelection();\r\n                let b = false;\r\n                let array = [];\r\n                for (let i = 0; i < dataL.length; i ++) {\r\n                    b = true;\r\n                    array.push(dataL[i])\r\n                }\r\n                if(b){\r\n                    this.submitData(array);\r\n                }else {\r\n                    this.errorTips('没有可保存数据')\r\n                }\r\n            },\r\n            submitChange(indexList){\r\n                let data=[];\r\n                this.submit2.map((item,index)=>{\r\n                    indexList.map((item2)=>{\r\n                        if(index==item2){\r\n                            data.push(item)\r\n                        }\r\n                    })\r\n                });\r\n                this.submit=data\r\n            },\r\n\r\n            //提交数据\r\n            submitData(data){\r\n                let a = [];\r\n                let that=this;\r\n                if(data != null && data.length > 0){\r\n                    let number = 0;\r\n                    let submitData = [];\r\n                    let str = '';\r\n                    let accountno = this.accountObj.accountno;\r\n                    data.forEach(function (item) {\r\n                        // 校验数据\r\n                        if(item.id == null){\r\n                            item.accountno = accountno\r\n                        }\r\n                        a.push(item.id);\r\n                        item.oilAccountType = 1;\r\n                        submitData.push(item);\r\n                        number ++;\r\n                    });\r\n                    that.ids=a;\r\n                    if(str.length > 0){\r\n                        this.errorTips(str)\r\n                    }\r\n                    if(submitData.length > 0){\r\n                        saveOilAccount(submitData).then((res) => {\r\n                            if (res.data.code == 0) {\r\n                                this.$Message.info({\r\n                                    content: '提示：成功保存 ' + res.data.num + ' 条数据',\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }\r\n                }\r\n            },\r\n            addNewCoalAccount() {\r\n                // let companyId = this.accountObj.company;\r\n                // let country = this.accountObj.country;\r\n                // if(companyId != null && country != null){\r\n                //     let obj = {\r\n                //         company:companyId,\r\n                //         country:country,\r\n                //         accountno:this.accountObj.accountno,\r\n                //         accountType:'1',\r\n                //         accountestype:1\r\n                //     }\r\n                // }else{\r\n                //     this.errorTips('请选择分公司和部门')\r\n                // }\r\n                const currentDate = new Date();\r\n                const currentYear = currentDate.getFullYear();\r\n                const currentMonth = currentDate.getMonth() + 1;\r\n                if (null == this.tbAccount.data) {\r\n                    this.tbAccount.data = [];\r\n                }\r\n                this.tbAccount.data.unshift({\r\n                    // accountNo:dates[1].code,\r\n                    // accountNo: this.accountObj.accountno,\r\n                    accountNo: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined) ? currentYear+\"\"+currentMonth: this.accountObj.accountno,\r\n                    oilUseBody: \"\",\r\n                    feeStartDate:\"\",\r\n                    oilImportType: \"\",\r\n                    oilAmount:\"0\",\r\n                    unitPrice:\"0\",\r\n                    ticketMoney:\"0\",\r\n                    taxTicketMoney:\"0\",\r\n                    taxRateShow:\"\",\r\n                    taxAmount:\"0\",\r\n                    otherFee:\"0\",\r\n                    paidMoney:\"0\",\r\n                    oilImportCategory:\"\",\r\n                    remark:\"\",\r\n                });\r\n                this.myStyle.push({\r\n                    oilUseBody: 'myspan',\r\n                    feeStartDate: 'myspan',\r\n                    // curtotalreadings: 'myspan',\r\n                    oilImportType: 'myspan',\r\n                    oilAmount: 'myspan',\r\n                    unitPrice: 'myspan',\r\n                    ticketMoney:\"myspan\",\r\n                    taxTicketMoney:\"myspan\",\r\n                    taxRateShow: 'myspan',\r\n                    taxAmount: 'myspan',\r\n                    otherFee: 'myspan',\r\n                    paidMoney: 'myspan',\r\n                    oilImportCategory: 'myspan',\r\n                    remark: 'myspan',\r\n\r\n                    });\r\n            },\r\n            //验证错误弹出提示框\r\n            errorTips(str){\r\n                this.$Notice.error({\r\n                    title: '提示',\r\n                    desc: str,\r\n                    duration: 10\r\n                });\r\n            },\r\n            handlePage(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageNum = value;\r\n                this.getAccountMessages();\r\n            },\r\n            handlePageSize(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n                this.pageSize = value;\r\n                this.getAccountMessages();\r\n            },\r\n            //向后台请求数据\r\n            getAccountMessages() {\r\n                 let postData = this.accountObj;\r\n                postData.pageNum = this.pageNum;\r\n                postData.pageSize = this.pageSize;\r\n                let req = {\r\n                    url : \"/business/oil/account/list\",\r\n                    method : \"get\",\r\n                    params : postData\r\n                };\r\n                this.tbAccount.loading = true;\r\n                axios.request(req).then(res => {\r\n                    this.tbAccount.loading = false;\r\n                    if (res.data) {\r\n                        let data = res.data.rows;\r\n                        data.forEach(function (item) {\r\n                            item.editType = 0;\r\n                        });\r\n                        this.tbAccount.data = data;\r\n                        this.pageTotal = res.data.total || 0;\r\n                        this.setMyStyle(this.tbAccount.data.length);\r\n                        this.editIndex = -1;\r\n                        this.columnsIndex = -1;\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            },\r\n            //重置\r\n            onResetHandle(){\r\n                this.accountObj = {\r\n                    accountno:null,\r\n                    company:this.company,\r\n                    oilUseBody:null,\r\n                    country:Number(this.country),\r\n                    oilCategory:null,\r\n                    oilType:null,\r\n                    oilAccountType:1,\r\n                }\r\n                this.getAccountMessages()\r\n            },\r\n            //计算单价\r\n            unitPrice(row){\r\n                let ticketMoney = row.ticketMoney;\r\n                let taxTicketMoney = row.taxTicketMoney;\r\n                let oilAmount = row.oilAmount;\r\n                if(ticketMoney != null || taxTicketMoney != null){\r\n                    let total = null;\r\n                    total = ticketMoney + taxTicketMoney;\r\n                    row.unitpirce = total/oilAmount.toFixed(2);\r\n                }\r\n            },\r\n            remove(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                if(data == null || data.length === 0){\r\n                    this.errorTips(\"请选择要删除的数据\")\r\n                    return;\r\n                }\r\n                this.$Modal.confirm({\r\n                    title: '提示',\r\n                    content: '<p>是否确认删除选中信息？</p>',\r\n                    onOk: () => {\r\n                        let b = true;\r\n                        let ids = '';\r\n                        let total = this.pageTotal\r\n                        for(let i=0;i<data.length;i++){\r\n                            let item = data[i];\r\n                            if(item.id != null && item.id.length > 0){\r\n                                if(item.pabriid){\r\n                                    b = false;\r\n                                }\r\n                                ids += item.id + ',';\r\n                            }\r\n                        }\r\n                        this.pageTotal = total\r\n                        if(b){\r\n                            if(ids.length > 0){\r\n                                removeOilAccount(ids).then((res) => {\r\n                                    if (res.data.code == 0) {\r\n                                        this.$Message.success('删除成功');\r\n                                        this.getAccountMessages();\r\n                                    }\r\n                                });\r\n                            }\r\n                        }else {\r\n                            this.errorTips('选中信息中有信息还没有跟归集单解除关联，请先解除关联')\r\n                        }\r\n                    },\r\n                    onCancel: () => {\r\n                    }\r\n                });\r\n            },\r\n            openAddBillPerModal(name) {\r\n                if (name === 'current') {\r\n                    this.selectedAccount()\r\n                } else if (name === 'all') {\r\n                    this.selectedAllAccount()\r\n                }\r\n            },\r\n            //加入归集单，全部有效台账\r\n            selectedAllAccount(){\r\n                let that = this\r\n                that.spinShow = true;\r\n                selectOilIds(this.accountObj).then(res => {\r\n                    that.spinShow = false;\r\n                    if(res.data.length == 0){\r\n                        that.errorTips('无有效数据可加入归集单')\r\n                    }else {\r\n                        let ids = [];\r\n                        for(let i=0;i<res.data.rows.length;i++){\r\n                            let item = res.data.rows[i];\r\n                            ids.push(item.id)\r\n                        }\r\n                        that.$refs.addBillPer.initAmmeter(ids, 21,this.accountObj.country);\r\n                    }\r\n                });\r\n            },\r\n            selectedAccount(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = 1;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要加入归集单的台账')\r\n                } else {\r\n                    let ids = [];\r\n                    data.forEach(function (item) {\r\n                        if(item.status === 5){\r\n                            b = 3\r\n                        }\r\n                        if(item.status === 4){\r\n                            b=4;\r\n                        }\r\n                        ids.push(item.id)\r\n                    });\r\n                    if(b === 1){\r\n                        this.$refs.addBillPer.initAmmeter(ids,21,this.accountObj.country);\r\n                    }else if(b === 2) {\r\n                        this.errorTips('选中的台账中存在临时数据，请先保存再加入归集单！')\r\n                    }else if(b===3){\r\n                        this.errorTips('退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮')\r\n                    }else if(b===4){\r\n                        this.errorTips('选择的台账有已加入归集单的台账，不能加入其他归集单')\r\n                    }\r\n                }\r\n            },\r\n            openCompletedPreModal(){\r\n                this.$refs.completedPre.initAmmeter(this.accountObj.country,2);\r\n            },\r\n            againJoin(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = true;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要重新加入归集单的台账')\r\n                } else {\r\n                    let ids = '';\r\n                    data.forEach(function (item) {\r\n                        let status = item.status;\r\n                        if(status != 5){\r\n                            b = false;\r\n                        }\r\n                        ids+= item.id +','\r\n                    });\r\n                    if(b){\r\n                        reJoinBillpre(ids).then((res) =>{\r\n                            if(res.data.code==0){\r\n                                this.$Message.info({\r\n                                    content:'提示：操作成功' ,\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }else {\r\n                        this.errorTips('只有已退回的台账才能重新加入归集单')\r\n                    }\r\n                }\r\n            },\r\n            refresh(){\r\n                let obj = this\r\n                setTimeout(function () {\r\n                    obj.getAccountMessages()\r\n                },200);\r\n            },\r\n            validate(){\r\n                if(this.columnsIndex != 5){\r\n                    let val = this.enterOperate(this.columnsIndex).data;\r\n                    if(val) {\r\n                        // if (testNumber(val)) {\r\n                            switch (this.columnsIndex) {\r\n                                case 1:\r\n                                this.validateOilUseBody();\r\n                                break;\r\n                            case 2:\r\n                                this.validateFeeStartDate();\r\n                                break;\r\n                            case 3:\r\n                                this.validateOilImportType();\r\n                                break;\r\n                            case 4:\r\n                                this.validateOilAmount();\r\n                                break;\r\n                            case 5:\r\n                                this.validateTicketMoney2();\r\n                                break;\r\n                            case 6:\r\n                                this.validateTaxTicketMoney();\r\n                                break;\r\n                            case 7:\r\n                                this.validateTaxRateShow();\r\n                                break;\r\n                            // case 3:\r\n                            //     this.validateFeeStartDate();\r\n                            //     break;\r\n                            case 8:\r\n                                this.validateOtherMoney();\r\n                                break;\r\n                            case 9:\r\n                                this.validateOilImportCategory();\r\n                                break;\r\n                            }\r\n                        // }else{\r\n                        //     this.errorTips('请输入数字！');\r\n                        // }\r\n                    }\r\n                }\r\n            },\r\n            //用油主体\r\n            validateOilUseBody() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOilUseBody;\r\n                data.oilUseBody = val;\r\n                data.editType = 1;\r\n                // }\r\n\r\n            },\r\n            //费用发生日\r\n            validateEnddate() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editEndDate;\r\n                let isDian = val[4]=='.' && val[7]=='.' || val[4]=='.' && val[6]=='.' ;\r\n                if(!isDian) {\r\n                    this.errorTips(\"结束时间格式不正确！\");\r\n                    val = \"\";\r\n                }\r\n                // if (val != data.old_enddate) {\r\n                //     // 验证截止日期方法\r\n                //     let result = _verify_EndDate1(data, val);\r\n                //     if (result) {\r\n                //         this.errorTips(result);\r\n                //         this.myStyle[this.editIndex].endDate = \"errorStle\";\r\n                //     } else {\r\n                //         this.myStyle[this.editIndex].endDate = \"myspan\";\r\n\r\n                //         this.updateenddate(data, val)\r\n\r\n                //     }\r\n                // } else if (val == data.old_enddate) {\r\n                    data.endDate = val;\r\n                    data.editType = 1;\r\n                // }\r\n            },\r\n            validateOilAmount() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOilAmount;\r\n                console.log(data.startDate, \"data.startDate\")\r\n\r\n\r\n                data.oilAmount = val;\r\n                if(data.paidMoney && val!=\"\") {\r\n                    (data.paidMoney/data.oilAmount).toFixed(4);;\r\n                }else {\r\n                    data.unitPrice = \"\";\r\n                }\r\n                data.editType = 1;\r\n                // console.log(data.startDate.split(\".\")[2]*1, \"data.startDate.split(\")\r\n                // console.log(data.endDate.split(\".\")[2]*1, \"data.endDate.split(\")\r\n                // if(data.startDate !=\"\" && data.endDate !=\"\") {\r\n                //     let riqiLengh = data.endDate.split(\".\")[2]*1 - data.startDate.split(\".\")[2]*1;\r\n                //     data.heatAmount = val*riqiLengh*60*0.7*3.6/1000000;\r\n                //     console.log(data.heatAmount, \"data.heatAmount\")\r\n                // }\r\n                // else {\r\n                //     this.errorTips(\"开始或者结束时间不能为空！\");\r\n                //     data.heatAmount = \"\";\r\n                // }\r\n            },\r\n            getEditOilTypeName(v) {\r\n                let a;\r\n                if(v == 1) {\r\n                    a = '汽油92';\r\n                }else if(v == 2) {\r\n                    a = '汽油95';\r\n                }else if(v == 3) {\r\n                    a = '汽油98';\r\n                }else if(v == 4) {\r\n                    a = '柴油0号';\r\n                }\r\n                return a;\r\n            },\r\n            validateOilImportType() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.getEditOilTypeName(this.editOilType);\r\n\r\n                // <Option value=\"1\" label=\"汽油92\"></Option>\r\n                //         <Option value=\"2\" label=\"汽油95\"></Option>\r\n                //         <Option value=\"3\" label=\"汽油98\"></Option>\r\n                //         <Option value=\"4\" label=\"柴油0号\"></Option>\r\n                data.oilImportType = val;\r\n                data.editType = 1;\r\n            },\r\n            //验证普票\r\n            // validateTicketMoney2() {\r\n            //     // debugger\r\n            //     let data = this.tbAccount.data[this.editIndex];\r\n            //     let val = this.editTicketMoney;\r\n            //     // console.log(data.paidMoney, data.oilAmount, \"data.paidMoney, data.oilAmount\");\r\n            //     // console.log(val, \"val\");\r\n            //     // if (val != data.old_ticketmoney) {\r\n            //         // val = parseFloat(val);\r\n            //         // data.ticketMoney = _verify_Money(data, val);\r\n            //         data.ticketMoney = val;\r\n            //         data.editType = 1;\r\n            //         // data.inputticketmoney = _verify_Money(data, val)\r\n            //         // data.ticketmoney = calculateActualMoney(data,val)\r\n            //         let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n            //          data.paidMoney = paidMoney.toFixed(2);\r\n            //         let unitPrice = data.paidMoney/data.oilAmount*1;\r\n            //         console.log(data.ticketMoney, \"data.ticketMoney\")\r\n            //         data.unitPrice = unitPrice.toFixed(4);\r\n            //         // let unitpirce = data.oilAmount?(data.paidMoney/data.oilAmount*1):0;\r\n\r\n            //         // this.calculateAll(data);\r\n            //     // } else if (val == data.old_ticketmoney) {\r\n            //     //     data.ticketMoney = val;\r\n            //     //     data.editType = 1;\r\n            //     //     // data.inputticketmoney = val;\r\n            //     //     // data.ticketmoney = calculateActualMoney(data,val)\r\n            //     //     // this.calculateAll(data);\r\n            //     // }\r\n            //     // this.validateUnitPrice(data)\r\n            // },\r\n            validateTicketMoney2() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTicketMoney;\r\n                console.log(data.paidMoney, data.coalAmount, \"data.paidMoney, data.coalAmount\");\r\n                if (val != data.old_ticketmoney) {\r\n                    val = parseFloat(val);\r\n                    data.ticketMoney = _verify_Money(data, val);\r\n                    // data.inputticketmoney = _verify_Money(data, val)\r\n                    // data.ticketmoney = calculateActualMoney(data,val)\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                    let unitPrice = data.coalAmount?(data.paidMoney/data.coalAmount*1):0;\r\n                    console.log(unitPrice, \"unitPrice\")\r\n                    data.unitPrice = unitPrice.toFixed(4);\r\n                    // let unitpirce = data.coalAmount?(data.paidMoney/data.coalAmount*1):0;\r\n                    data.editType = 1;\r\n                    // this.calculateAll(data);\r\n                } else if (val == data.old_ticketmoney) {\r\n                    data.ticketMoney = val;\r\n                    // data.inputticketmoney = val;\r\n                    // data.ticketmoney = calculateActualMoney(data,val)\r\n                    // this.calculateAll(data);\r\n                }\r\n                // this.validateUnitPrice(data)\r\n            },\r\n            //验证专票\r\n            validateTaxTicketMoney() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxTicketMoney;\r\n                if (val != data.old_taxticketmoney) {\r\n                    val = parseFloat(val);\r\n                    data.taxTicketMoney = _verify_Money(data, val)\r\n                    data.editType = 1;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                    // let unitPrice = data.oilAmount?(data.paidMoney/data.oilAmount):0;\r\n                    let unitPrice = data.paidMoney/data.oilAmount;\r\n                    data.unitPrice = unitPrice.toFixed(4);\r\n                    // data.taxticketmoney = calculateActualMoney(data,val)\r\n                    // data.taxAmount = data.taxTicketMoney*data.taxRateShow;\r\n                    data.taxAmount = countTaxamount1(data);\r\n                    // this.calculateAll(data);\r\n                } else if (val == data.old_taxticketmoney) {\r\n                    data.taxTicketMoney = val;\r\n                    // data.taxticketmoney = calculateActualMoney(data,val)\r\n                    // data.taxAmount = (data.taxTicketMoney*1)*(data.taxRateShow*1);\r\n                    data.taxAmount = countTaxamount1(data);\r\n                    // this.calculateAll(data);\r\n                }\r\n                console.log(data.taxRateShow, \".taxRateShowdata5555555555\");\r\n                console.log(data.taxTicketMoney, \".taxTicketMoney\");\r\n                // this.validateUnitPrice(data)\r\n            },\r\n            validateTaxRateShow() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxRate;\r\n                data.taxRateShow = val;\r\n                data.editType = 1;\r\n            },\r\n            validateFeeStartDate(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editFeeStartDate;\r\n                let result = _verify_FeeStartDate(data,val);\r\n                if(result){//失败就弹出提示内容，并将数据恢复初始化\r\n                    this.errorTips(result)\r\n                }else{\r\n                    data.feeStartDate = val;\r\n                    data.editType = 1;\r\n                }\r\n            },\r\n            validateUnitPrice() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editUnitPrice;\r\n                data.unitPrice = val;\r\n                data.editType = 1;\r\n            },\r\n            validateOtherMoney(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOtherMoney;\r\n                if (!testNumber(val)) {\r\n                    this.errorTips('请输入数字！');\r\n                }\r\n                data.otherFee = val;\r\n                data.editType = 1;\r\n                let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                    data.paidMoney = paidMoney.toFixed(2);\r\n                    data.unitPrice = (data.paidMoney/data.oilAmount).toFixed(4);\r\n                // debugger)\r\n            },\r\n            validateOilImportCategory(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOilCategory;\r\n                data.oilImportCategory = val;\r\n                data.editType = 1;\r\n            },\r\n            // validateOtherMoney(){\r\n            //     let data = this.tbAccount.data[this.editIndex];\r\n            //     let val = this.editOtherMoney;\r\n            //     data.otherMoney = val;\r\n            //     data.editType = 1;\r\n            //     debugger\r\n            // },\r\n            setremark(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editremark;\r\n                data.remark = val;\r\n                data.editType = 1;\r\n            },\r\n            setOilUseBody(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOilUseBody;\r\n                data.oilUseBody = val;\r\n                data.editType = 1;\r\n            },\r\n            setMyStyle(length){\r\n                this.myStyle=[];\r\n                for(var i=0;i<length;i++){\r\n                    this.myStyle.push({\r\n                        coalUseBody:'myspan',\r\n                        feeStartDate:'myspan',\r\n                        oilAmount:'myspan',\r\n                        taxTicketMoney:'myspan',\r\n                        remark:'myspan',\r\n                    });\r\n                }\r\n            },\r\n            //span点击事件将span换成输入框并且获取焦点\r\n            selectCall(row,index,columns,str){\r\n                this.editFeeStartDate = row.feeStartDate;\r\n                this.editOilUseBody = row.oilUseBody;\r\n                this.editOilAmount = row.oilAmount == null || row.oilAmount===0?null:row.oilAmount;\r\n                this.editOilType = row.oilImportType;\r\n                this.editOilCategory = row.oilImportCategory;\r\n                this.editTicketMoney = row.ticketMoney;\r\n                this.editTaxTicketMoney = row.taxTicketMoney;\r\n                this.editTaxRate = row.taxRateShow;\r\n                this.editOtherMoney = row.otherFee;\r\n                this.editremark = row.remark;\r\n                this.editIndex = index;\r\n                this.columnsIndex = columns;\r\n                let a=this;\r\n                setTimeout(function () {\r\n                    a.$refs[str+index+columns].focus();\r\n                },200);\r\n\r\n            },\r\n            //跳转到下一格\r\n            nextCell(data){\r\n                let index = data.editIndex;\r\n                let columns = data.columnsIndex;\r\n                let row = '';\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                }else if(index > -1 && columns === 5){\r\n                    //当跳转的最后一行最后一格的时候\r\n                    if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n                        index = 0;\r\n                    }else{\r\n                        index ++;\r\n                    }\r\n                    columns = 1;\r\n                }else{\r\n                    columns += 1;\r\n                }\r\n                data.editIndex = index;\r\n                data.columnsIndex = columns;\r\n                row = data.tbAccount.data[index];\r\n                if(row){\r\n                    data.editFeeStartDate = row.feeStartDate;\r\n                    data.editOilUseBody = row.oilUseBody;\r\n                    data.editOilAmount = row.oilAmount == null || row.oilAmount===0?null:row.oilAmount;\r\n                    data.editOilType = row.oilImportType;\r\n                    data.editOilCategory = row.oilImportCategory;\r\n                    data.editTicketMoney = row.ticketMoney;\r\n                    data.editTaxTicketMoney = row.taxTicketMoney;\r\n                    data.editTaxRate = row.taxRateShow;\r\n                    data.editOtherMoney = row.otherFee;\r\n                    data.editremark = row.remark;\r\n\r\n                }\r\n                setTimeout(function () {\r\n                    data.$refs[data.enterOperate(columns).str+index+columns].focus();\r\n                },200);\r\n            },\r\n            //根据列号返回对应的列名\r\n            enterOperate(number){\r\n                let str = '';\r\n                let data = null;\r\n                switch (number) {\r\n                    case 1:\r\n                        str = 'oilUseBody';\r\n                        data = this.editOilUseBody;\r\n                        break;\r\n                    case 2:\r\n                        str = 'feeStartDate';\r\n                        data = this.editFeeStartDate;\r\n                        break;\r\n                    case 3:\r\n                        str = 'oilImportType'\r\n                        data = this.editOilType;\r\n                        break;\r\n                    case 4:\r\n                        str = 'oilAmount';\r\n                        data = this.editOilAmount;\r\n                        break;\r\n                    case 5:\r\n                        str = 'ticketMoney';\r\n                        data = this.editTicketMoney;\r\n                        break;\r\n                    case 6:\r\n                        str = 'taxTicketMoney';\r\n                        data = this.editTaxTicketMoney;\r\n                        break;\r\n                    case 7:\r\n                        str = 'taxRateShow';\r\n                        data = this.editTaxRate;\r\n                        break;\r\n                    case 8:\r\n                        str = 'otherFee';\r\n                        data = this.editOtherMoney;\r\n                        break;\r\n                    case 9:\r\n                        str = 'oilImportCategory';\r\n                        data = this.editOilCategory;\r\n                        break;\r\n                    case 10:\r\n                        str = 'remark';\r\n                        data = this.editremark;\r\n                        break;\r\n                }\r\n                return {str:str,data:data};\r\n            },\r\n            pred(){\r\n                var lett = this;\r\n                let index = lett.editIndex;\r\n                let columns = lett.columnsIndex;\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                    lett.editIndex = index;\r\n                    lett.columnsIndex = columns;\r\n                    setTimeout(function () {\r\n                        lett.$refs[lett.enterOperate(columns).str+index+columns].focus();\r\n                    },200);\r\n                }else{\r\n                    lett.validate()\r\n                    lett.setremark()\r\n                    lett.nextCell(lett)\r\n                }\r\n            },\r\n            ellipsis (value) {\r\n                if (!value) return ''\r\n                if (value.length > 100) {\r\n                    return value.slice(0,100) + '...'\r\n                }\r\n                return value\r\n            },\r\n            handleUploadSuccess() {\r\n\r\n            },\r\n            handleFormatError(file) {\r\n                this.errorTips(\r\n                    file.name + \" 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件\"\r\n                );\r\n            },\r\n            handleProgress(event, file) {\r\n                this.$Message.info({\r\n                    content: file.name + \" 正在上传。\",\r\n                });\r\n            },\r\n            // 导入\r\n            onExcelUpload(file) {\r\n                if (!file) {\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: '请选择要上传的文件！',\r\n                        duration: 10\r\n                    });\r\n                    return\r\n                }\r\n                let fileName = file.name.lastIndexOf(\".\");//取到文件名开始到最后一个点的长度\r\n                let fileNameLength = file.name.length;//取到文件名长度\r\n                let fileFormat = file.name.substring(fileName + 1, fileNameLength);//截\r\n                if('xls' != fileFormat && 'xlsx' != fileFormat){\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: file.name + ' 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件',\r\n                        duration: 10\r\n                    });\r\n                    return;\r\n                }\r\n                let param = {}\r\n                let excel = {file: file}\r\n                let that = this\r\n                that.spinShow = true\r\n                axios.request({\r\n                    url: '/business/oil/account/import',\r\n                    method: 'post',\r\n                    data: Object.assign({}, param, excel)\r\n                }).then((res) => {\r\n                    that.spinShow = false\r\n                    if (res.data.str) {\r\n                        that.$Message.info({\r\n                            content: res.data.str,\r\n                            duration: 0,\r\n                            closable: true\r\n                        });\r\n                        that.show = false;\r\n                    }\r\n                    this.getAccountMessages();\r\n                }).catch(err => {\r\n                    that.spinShow = false\r\n                    that.show = false;\r\n                    console.log(err);\r\n                });\r\n                return false\r\n            },\r\n\r\n            // 导入模板下载\r\n            loadTemplate() {\r\n                let req = {\r\n                    url : \"/business/oil/account/template/load\",\r\n                    method : \"get\",\r\n                    responseType: 'blob',\r\n                };\r\n                axios.file(req)\r\n                    .then(res => {\r\n                        this.spinShow = false;\r\n                        const content = res;\r\n                        const blob = new Blob([content]);\r\n                        const fileName = \"用油台账导入模板.xlsx\";\r\n                        if (\"download\" in document.createElement(\"a\")) {\r\n                            // 非IE下载\r\n                            const elink = document.createElement(\"a\");\r\n                            elink.download = fileName;\r\n                            elink.style.display = \"none\";\r\n                            elink.href = URL.createObjectURL(blob);\r\n                            document.body.appendChild(elink);\r\n                            elink.click();\r\n                            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n                            document.body.removeChild(elink);\r\n                        } else {\r\n                            // IE10+下载\r\n                            navigator.msSaveBlob(blob, fileName);\r\n                        }\r\n                    });\r\n            }\r\n        },\r\n        mounted() {\r\n            this.version = indexData.version;\r\n            this.tbAccount.columns = this.tbAccount.tailColumn;\r\n            this.oilTypes = blist1(\"oilType\");\r\n            console.log(this.oilTypes, \"this.oilTypes\");\r\n            // debugger\r\n            this.oilCategorys = blist1(\"oilCategory\");\r\n            this.accountObj.oilCategory = this.oilCategorys[0].typeCode;\r\n            this.accountObj.oilType = this.oilTypes[0].typeCode;\r\n            let that = this\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                    that.getUserData();\r\n                });\r\n            });\r\n        }\r\n    }\r\n</script>\r\n"]}]}