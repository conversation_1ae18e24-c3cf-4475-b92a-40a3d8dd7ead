{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\assessRanking.vue?vue&type=style&index=0&id=2fe01e56&lang=less&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\assessRanking.vue", "mtime": 1754285403029}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749178874080}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hc3Nlc3NtZW50LXJhbmtpbmcgew0KICAvLyB3aWR0aDogMTAwJTsNCiAgLy8gd2lkdGg6IDE0NXJlbTsNCiAgaGVpZ2h0OiAzNy44dmg7DQp9DQo="}, {"version": 3, "sources": ["assessRanking.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6UA;AACA;AACA;AACA;AACA", "file": "assessRanking.vue", "sourceRoot": "src/view/carbon/assess/assessReport/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"assessment-ranking\" id=\"assessment-ranking\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import {  mapState } from \"vuex\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      chartOptions: {\r\n        grid: \r\n        // {\r\n        //   right: this.isCollapse ? '1%' : '2%',\r\n          \r\n        // },\r\n      { \r\n          show: false, \r\n          top: \"20%\", \r\n          left: \"4%\", \r\n          right: this.isCollapse ? '1%' : '2%', \r\n          bottom: \"18%\", \r\n        },\r\n        legend: { \r\n          right: this.isCollapse ? '0.5%' : '10%',\r\n          // data: this.companyarr.result.map((item) => item.name),\r\n          textStyle: {\r\n          fontSize: 12,\r\n          color: \"#fff\",\r\n          fontFamily: \"PingFangSC-Regular\",\r\n          },\r\n          itemWidth: 20,\r\n          itemHeight: 9,\r\n          itemGap: 15,\r\n          top: \"2%\",\r\n          // right: this.chartOptions.legend.right,\r\n          },\r\n      },\r\n      companyarr: {},\r\n      series: [],\r\n    };\r\n  },\r\n  props: {\r\n    dataArr: {\r\n      type: Object,\r\n    },\r\n  },\r\n  // computed: {\r\n  //   ...mapState({isCollapse: state => state.common.isCollapse})\r\n  // },\r\n  watch: {\r\n    isCollapse: {\r\n      immediate: true,\r\n      handler(newValue) {\r\n        this.updateGridRight(newValue);\r\n      },\r\n      deep: true\r\n    },\r\n    dataArr: {\r\n      handler(newVal, oldVal) {\r\n        if (newVal == undefined || newVal == null || newVal == \"\") {\r\n          this.companyarr = JSON.parse(\r\n            JSON.stringify({\r\n              result: [],\r\n              xdata: [],\r\n            })\r\n          );\r\n        } else {\r\n          let myChart = this.$echarts.init(\r\n            document.getElementById(\"assessment-ranking\")\r\n          );\r\n          myChart.setOption({ series: [] }, true);\r\n\r\n          this.companyarr = JSON.parse(JSON.stringify(newVal));\r\n        }\r\n        this.$nextTick(() => {\r\n          this.init();\r\n        });\r\n      },\r\n      deep: true, // 深度监听\r\n    },\r\n  },\r\n  methods: {\r\n    init() {\r\n      let _this = this;\r\n      let myChart = this.$echarts.init(\r\n        document.getElementById(\"assessment-ranking\")\r\n      );\r\n      let diamondData = [];\r\n      diamondData = this.companyarr.result.reduce((pre, cur, index) => {\r\n        pre[index] = cur.data.map(\r\n          (el, id) => el + (pre[index - 1] ? pre[index - 1][id] : 0)\r\n        );\r\n        return pre;\r\n      }, []);\r\n      const color = [\r\n        [\r\n          { offset: 0, color: \"#FF7D41\" },\r\n          { offset: 0.5, color: \"#FF7D41\" },\r\n          { offset: 0.5, color: \"#EE7036\" },\r\n          { offset: 1, color: \"#EE7036\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#FBC658\" },\r\n          { offset: 0.5, color: \"#FBC658\" },\r\n          { offset: 0.5, color: \"#FBBB54\" },\r\n          { offset: 1, color: \"#FBBB54\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#4A99FA\" },\r\n          { offset: 0.5, color: \"#4A99FA\" },\r\n          { offset: 0.5, color: \"#1C7CFA\" },\r\n          { offset: 1, color: \"#1C7CFA\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#25C5FB\" },\r\n          { offset: 0.5, color: \"#25C5FB\" },\r\n          { offset: 0.5, color: \"#0EB1FB\" },\r\n          { offset: 1, color: \"#0EB1FB\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#00A083\" },\r\n          { offset: 0.5, color: \"#00A083\" },\r\n          { offset: 0.5, color: \"#027964\" },\r\n          { offset: 1, color: \"#027964\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#22DEBB\" },\r\n          { offset: 0.5, color: \"#22DEBB\" },\r\n          { offset: 0.5, color: \"#00C6A1\" },\r\n          { offset: 1, color: \"#00C6A1\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#ACFFF0\" },\r\n          { offset: 0.5, color: \"#ACFFF0\" },\r\n          { offset: 0.5, color: \"#7FE3D1\" },\r\n          { offset: 1, color: \"#7FE3D1\" },\r\n        ],\r\n      ];\r\n      let series = [];\r\n      series = this.companyarr.result.reduce((p, c, i, array) => {\r\n        p.push(\r\n          {\r\n            z: i + 1,\r\n            stack: \"总量\",\r\n            type: \"bar\",\r\n            name: c.name,\r\n            barWidth: 25,\r\n            data: c.data,\r\n            itemStyle: {\r\n              color: {\r\n                type: \"linear\",\r\n                x: 0,\r\n                x2: 1,\r\n                y: 0,\r\n                y2: 0,\r\n                colorStops: color[i],\r\n              },\r\n            },\r\n          },\r\n          {\r\n            z: i + 1,\r\n            type: \"pictorialBar\",\r\n            symbolPosition: \"end\",\r\n            symbol: \"diamond\",\r\n            symbolOffset: [0, \"-50%\"],\r\n            symbolSize: [25, 10],\r\n            data: diamondData[i],\r\n            itemStyle: {\r\n              color: {\r\n                type: \"linear\",\r\n                x: 0,\r\n                x2: 1,\r\n                y: 0,\r\n                y2: 0,\r\n                colorStops: color[i],\r\n              },\r\n            },\r\n            tooltip: { show: false },\r\n          }\r\n        );\r\n        // 是否最后一个了？\r\n        if (series.length === array.length * 2) {\r\n          p.push({\r\n            z: 20,\r\n            type: \"pictorialBar\",\r\n            symbolPosition: \"start\",\r\n            data: _this.companyarr.result[0].data,\r\n            symbol: \"diamond\",\r\n            symbolOffset: [\"0%\", \"50%\"],\r\n            symbolSize: [30, 10],\r\n            itemStyle: {\r\n              color: {\r\n                type: \"linear\",\r\n                x: 0,\r\n                x2: 1,\r\n                y: 0,\r\n                y2: 0,\r\n                colorStops: color[0],\r\n              },\r\n            },\r\n            tooltip: { show: false },\r\n          });\r\n          return p;\r\n        }\r\n        return p;\r\n      }, []);\r\n\r\n      // tooltip\r\n\r\n      // legend\r\n      // const legend = this.chartOptions.legend\r\n      // {\r\n      //   data: _this.companyarr.result.map((item) => item.name),\r\n      //   textStyle: {\r\n      //     fontSize: 12,\r\n      //     color: \"#fff\",\r\n      //     fontFamily: \"PingFangSC-Regular\",\r\n      //   },\r\n      //   itemWidth: 20,\r\n      //   itemHeight: 9,\r\n      //   itemGap: 15,\r\n      //   top: \"2%\",\r\n      //   right: this.chartOptions.legend.right,\r\n      // };\r\n\r\n      // grid\r\n      // const grid = this.chartOptions.grid;\r\n      // { top: \"20%\", left: \"4%\", right: this.chartOptions.grid.right, bottom: \"18%\" };\r\n\r\n      // xAxis\r\n      const xAxis = {\r\n        axisTick: { show: true },\r\n        axisLine: { lineStyle: { color: \"rgba(255,255,255, .2)\" } },\r\n        axisLabel: {\r\n          textStyle: {\r\n            fontSize: 12,\r\n            color: \"#fff\",\r\n            fontFamily: \"PingFangSC-Regular\",\r\n          },\r\n\r\n          formatter: function (\r\n            value //X轴的内容\r\n          ) {\r\n            return value.replace(/\\s/, \"\\n\");\r\n          },\r\n        },\r\n        data: this.companyarr.xdata,\r\n      };\r\n      const dataZoom = [\r\n        {\r\n        // 设置滚动条的隐藏与显示\r\n          show: false,\r\n          type: \"slider\", //这个dataZoom组件是slider型dataZoom组件\r\n          xAxisIndex: 0, //dataZoom-slider组件控制第一个XAxis\r\n          start: 0, //左边在10%位置\r\n          end: 60, //右边在60%位置\r\n          zoomLock: true,\r\n          height: \"10\",\r\n          bottom: \"4%\",\r\n          borderColor: \"#1B3149\", //滑动通道的边框颜色\r\n          backgroundColor: \"#275277\",\r\n          showDetail: false, // 即拖拽时候是否显示详细数值信息 默认tru\r\n          handleStyle: {\r\n            borderColor: \"#cbdbfd\",\r\n            shadowBlur: 6,\r\n            shadowOffsetX: 1,\r\n            shadowOffsetY: 1,\r\n            shadowColor: \"#cbdbfd\",\r\n          },\r\n        },\r\n        {\r\n          type: \"inside\", //这个dataZoom组件是inside型dataZoom组件\r\n          xAxisIndex: 0, //dataZoom-inslide组件控制第一个XAxis\r\n          start: 10, //左边在10%的位置\r\n          end: 60, //右边在60%的位置\r\n          // showDetail: false, // 即拖拽时候是否显示详细数值信息 默认tru\r\n          // zoomLock: true,\r\n          // // 滚轮是否触发缩放\r\n          zoomOnMouseWheel: false,\r\n          // 鼠标滚轮触发滚动\r\n          moveOnMouseMove: true,\r\n          moveOnMouseWheel: true,\r\n        },\r\n      ];\r\n      // yAxis\r\n      const yAxis = [\r\n        {\r\n          splitLine: { lineStyle: { color: \"rgba(255,255,255, .05)\" } },\r\n          axisLine: { show: false },\r\n          axisLabel: {\r\n            textStyle: {\r\n              fontSize: 12,\r\n              color: \"#fff\",\r\n              fontFamily: \"PingFangSC-Regular\",\r\n            },\r\n          },\r\n        },\r\n      ];\r\n      let option = {\r\n        tooltip: { trigger: \"axis\" },\r\n        xAxis,\r\n        dataZoom,\r\n        yAxis,\r\n        series,\r\n        grid: this.chartOptions.grid,\r\n        legend: this.chartOptions.legend,\r\n      };\r\n      myChart.setOption(option);\r\n      // 渲染\r\n      window.addEventListener(\"resize\", function () {\r\n        myChart.resize();\r\n      });\r\n    },\r\n    updateGridRight(isCollapse) {\r\n      this.chartOptions.grid.right = isCollapse ? '0%' : '2%';\r\n      this.chartOptions.legend.right = isCollapse ? '0.5%' : '10%';\r\n      \r\n      let myChart = this.$echarts.init(\r\n        document.getElementById(\"assessment-ranking\")\r\n      );\r\n      // 使用setOption更新图表配置\r\n      myChart.setOption({\r\n        grid: this.chartOptions.grid,\r\n        legend: this.chartOptions.legend\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.assessment-ranking {\r\n  // width: 100%;\r\n  // width: 145rem;\r\n  height: 37.8vh;\r\n}\r\n</style>\r\n"]}]}