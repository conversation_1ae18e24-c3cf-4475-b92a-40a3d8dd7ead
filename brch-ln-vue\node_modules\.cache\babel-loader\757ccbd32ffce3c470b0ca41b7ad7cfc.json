{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addSelfPowerAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addSelfPowerAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["addSelfPowerAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgvBA,SAAA,QAAA,QAAA,MAAA;AACA,SAAA,cAAA,EAAA,iBAAA,EAAA,qBAAA,QAAA,eAAA;AACA,SACA,mBADA,EAEA,gBAAA,IAAA,iBAFA,EAGA,cAHA,EAIA,OAJA,EAKA,SALA,EAMA,YANA,EAOA,OAPA,EAQA,cARA,EASA,iBATA,EAUA,YAVA,EAWA,oBAXA,EAYA,iBAZA,EAaA,SAbA,QAcA,eAdA;AAeA,SAAA,iBAAA,EAAA,WAAA,QAAA,2BAAA;AACA,SAAA,SAAA,IAAA,UAAA,QAAA,sBAAA;AACA,SAAA,iBAAA,QAAA,gBAAA;AACA,OAAA,sBAAA,MAAA,6CAAA;AACA,OAAA,WAAA,MAAA,kCAAA;AACA,OAAA,UAAA,MAAA,iCAAA;AACA,SACA,QADA,EAEA,gBAFA,EAGA,UAHA,EAIA,WAJA,EAKA,YALA,EAMA,cANA,QAOA,mCAPA;AAQA,SACA,iBADA,EAEA,WAFA,EAGA,eAHA,EAIA,yBAJA,EAKA,wBALA,EAMA,2BANA,EAOA,0BAPA,EAQA,aARA,EASA,mBATA,EAUA,oBAVA,EAWA,aAXA,EAYA,sBAZA,EAaA,+BAbA,EAcA,uBAdA,EAeA,8BAfA,EAgBA,sBAhBA,EAiBA,4BAjBA,EAkBA,sBAlBA,EAmBA,cAnBA,EAoBA,oBApBA,EAqBA,YArBA,EAsBA,cAtBA,EAuBA,QAvBA,EAwBA,YAxBA,EAyBA,YAzBA,EA0BA,aA1BA,QA2BA,uCA3BA;AA4BA,SAAA,UAAA,QAAA,mDAAA;AACA,OAAA,gBAAA,MAAA,iCAAA;AACA,OAAA,eAAA,MAAA,gCAAA;AACA,OAAA,UAAA,MAAA,gCAAA;AACA,OAAA,KAAA,MAAA,cAAA;AACA,SAAA,KAAA,QAAA,cAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AACA,OAAA,SAAA,MAAA,gBAAA;AAEA,OAAA,eAAA,MAAA,qBAAA;AACA,OAAA,OAAA,MAAA,kBAAA;AAEA,IAAA,KAAA,GAAA,QAAA,EAAA;AAEA,eAAA;AACA,EAAA,MAAA,EAAA,CAAA,eAAA,EAAA,OAAA,CADA;AAGA,EAAA,UAAA,EAAA;AACA,IAAA,UAAA,EAAA,UADA;AAEA,IAAA,WAAA,EAAA,WAFA;AAGA,IAAA,sBAAA,EAAA,sBAHA;AAIA,IAAA,eAAA,EAAA,eAJA;AAKA,IAAA,gBAAA,EAAA,gBALA;AAMA,IAAA,UAAA,EAAA;AANA,GAHA;AAWA,EAAA,IAXA,kBAWA;AAAA;;AACA,QAAA,cAAA,GAAA,SAAA,cAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,YAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,6BAAA,KAAA,CAAA,SAAA,8HAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,QAAA,EAAA;AACA,YAAA,YAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,YAAA,CAAA;AACA,KATA;;AAUA,QAAA,sBAAA,GAAA,SAAA,sBAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,gBAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,8BAAA,KAAA,CAAA,iBAAA,mIAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,gBAAA,EAAA;AACA,YAAA,gBAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,gBAAA,CAAA;AACA,KATA;;AAUA,QAAA,KAAA,GAAA,SAAA,KAAA,CAAA,CAAA,QAAA;AAAA,UAAA,GAAA,QAAA,GAAA;AAAA,UAAA,KAAA,QAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA;AACA,UAAA,GAAA,GAAA,EAAA;;AACA,UAAA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,IAAA,IAAA,EAAA;AACA,QAAA,GAAA,GAAA,IAAA;AACA;;AACA,aAAA,CAAA,CAAA,KAAA,EAAA,CACA,CAAA,CACA,GADA,EAEA;AACA,QAAA,EAAA,EAAA;AACA,UAAA,KADA,mBACA;AACA;AACA,gBAAA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,IAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,GAAA;AACA;AACA;AANA;AADA,OAFA,EAYA,GAZA,CADA,CAAA,CAAA;AAgBA,KAtBA;;AAwBA,WAAA;AACA,MAAA,GAAA,EAAA,IADA;AAEA,MAAA,OAAA,EAAA,CAFA;AAGA,MAAA,IAAA,EAAA,EAHA;AAIA,MAAA,OAAA,EAAA,IAJA;AAKA,MAAA,MAAA,EAAA,CALA;AAMA,MAAA,eAAA,EAAA,EANA;AAOA,MAAA,MAAA,EAAA,EAPA;AAQA,MAAA,OAAA,EAAA,EARA;AASA,MAAA,UAAA,EAAA,EATA;AAUA,MAAA,cAAA,EAAA,KAVA;AAWA,MAAA,cAAA,EAAA,KAXA;AAYA,MAAA,WAAA,EAAA,KAZA;AAaA,MAAA,OAAA,EAAA,SAAA,CAAA,OAbA;AAcA,MAAA,SAAA,EAAA,IAdA;AAeA,MAAA,WAAA,EAAA,CAAA,EAAA,CAfA;AAgBA,MAAA,aAAA,EAAA,UAhBA;AAiBA,MAAA,SAAA,EAAA,CAjBA;AAkBA,MAAA,QAAA,EAAA,KAlBA;AAmBA,MAAA,SAAA,EAAA,EAnBA;AAmBA;AACA,MAAA,QAAA,EAAA,KApBA;AAoBA;AACA,MAAA,UAAA,EAAA,IArBA;AAqBA;AACA,MAAA,UAAA,EAAA,KAtBA;AAsBA;AACA,MAAA,OAAA,EAAA,KAvBA;AAuBA;AACA,MAAA,OAAA,EAAA,KAxBA;AAwBA;AACA,MAAA,SAAA,EAAA,KAzBA;AAyBA;AACA,MAAA,QAAA,EAAA,KA1BA;AA0BA;AACA,MAAA,SAAA,EAAA,CAAA,CA3BA;AA2BA;AACA,MAAA,YAAA,EAAA,CAAA,CA5BA;AA4BA;AACA,MAAA,kBAAA,EAAA,EA7BA;AA6BA;AACA,MAAA,eAAA,EAAA,EA9BA;AA8BA;AACA,MAAA,iBAAA,EAAA,EA/BA;AAgCA,MAAA,aAAA,EAAA,EAhCA;AAiCA,MAAA,WAAA,EAAA,EAjCA;AAkCA,MAAA,qBAAA,EAAA,EAlCA;AAmCA,MAAA,oBAAA,EAAA,EAnCA;AAoCA,MAAA,qBAAA,EAAA,EApCA;AAqCA,MAAA,kBAAA,EAAA,EArCA;AAsCA,MAAA,eAAA,EAAA,EAtCA;AAuCA,MAAA,eAAA,EAAA,EAvCA;AAwCA,MAAA,WAAA,EAAA,EAxCA;AAyCA,MAAA,WAAA,EAAA,EAzCA;AA0CA,MAAA,UAAA,EAAA,EA1CA;AA2CA,MAAA,oBAAA,EAAA,CA3CA;AA4CA,MAAA,oBAAA,EAAA,CA5CA;AA6CA,MAAA,mBAAA,EAAA,CA7CA;AA8CA,MAAA,mBAAA,EAAA,CA9CA;AA+CA,MAAA,mBAAA,EAAA,CA/CA;AAgDA,MAAA,kBAAA,EAAA,CAhDA;AAiDA,MAAA,gBAAA,EAAA,CAjDA;AAkDA,MAAA,gBAAA,EAAA,CAlDA;AAmDA,MAAA,eAAA,EAAA,CAnDA;AAoDA,MAAA,OAAA,EAAA,EApDA;AAqDA,MAAA,aAAA,EAAA,EArDA;AAsDA,MAAA,WAAA,EAAA,EAtDA;AAuDA,MAAA,eAAA,EAAA,EAvDA;AAwDA,MAAA,iBAAA,EAAA,EAxDA;AAyDA,MAAA,UAAA,EAAA,EAzDA;AAyDA;AACA,MAAA,OAAA,EAAA,EA1DA;AA0DA;AACA,MAAA,SAAA,EAAA,CA3DA;AA4DA,MAAA,OAAA,EAAA,CA5DA;AA6DA,MAAA,QAAA,EAAA,EA7DA;AA6DA;AACA,MAAA,UAAA,EAAA,EA9DA;AA+DA,MAAA,UAAA,EAAA,KA/DA;AAgEA,MAAA,WAAA,EAAA,IAhEA;AAiEA,MAAA,YAAA,EAAA,EAjEA;AAkEA,MAAA,MAAA,EAAA;AACA,QAAA,GAAA,EAAA,KADA;AACA;AACA,QAAA,IAAA,EAAA,EAFA;AAEA;AACA,QAAA,SAAA,EAAA,CAHA;AAGA;AACA,QAAA,WAAA,EAAA,CAJA;AAIA;AACA,QAAA,OAAA,EAAA,CALA;AAMA,QAAA,IAAA,EAAA;AANA,OAlEA;AA0EA,MAAA,UAAA,EAAA;AAAA,QAAA,SAAA,EAAA,EAAA;AAAA,QAAA,SAAA,EAAA,EAAA;AAAA,QAAA,OAAA,EAAA;AAAA,OA1EA;AA2EA,MAAA,UAAA,EAAA;AACA,QAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IADA;AACA;AACA,QAAA,WAAA,EAAA,EAFA;AAEA;AACA,QAAA,WAAA,EAAA,EAHA;AAGA;AACA,QAAA,iBAAA,EAAA,IAJA;AAIA;AACA,QAAA,WAAA,EAAA,EALA;AAKA;AACA,QAAA,MAAA,EAAA,EANA;AAMA;AACA,QAAA,OAAA,EAAA,EAPA;AAQA,QAAA,OAAA,EAAA,EARA;AASA,QAAA,WAAA,EAAA,EATA;AASA;AACA,QAAA,WAAA,EAAA,GAVA;AAUA;AACA,QAAA,MAAA,EAAA,EAXA;AAYA,QAAA,OAAA,EAAA,EAZA;AAaA,QAAA,uBAAA,EAAA,EAbA;AAcA,QAAA,gBAAA,EAAA,EAdA;AAeA,QAAA,SAAA,EAAA,EAfA;AAgBA,QAAA,SAAA,EAAA,EAhBA;AAiBA,QAAA,OAAA,EAAA;AAjBA,OA3EA;AA+FA,MAAA,QAAA,EAAA,EA/FA;AA+FA;AACA,MAAA,QAAA,EAAA,CAhGA;AAiGA,MAAA,WAAA,EAAA,CAjGA;AAkGA,MAAA,SAAA,EAAA;AACA,QAAA,OAAA,EAAA,KADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,EAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAFA,CAHA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,EAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,GAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,gBAAA,IAAA,GAAA,KAAA;AACA,mBAAA,CAAA,CACA,QADA,EAEA;AACA,cAAA,KAAA,EAAA;AACA,gBAAA,IAAA,EAAA,SADA;AAEA,gBAAA,IAAA,EAAA;AAFA,eADA;AAKA,cAAA,KAAA,EAAA;AACA,6BAAA;AADA,eALA;AAQA,cAAA,EAAA,EAAA;AACA,gBAAA,KADA,mBACA;AACA,kBAAA,IAAA,CAAA,KAAA,CAAA,qBAAA,CAAA,IAAA,GAAA,MAAA,CAAA,GAAA,CAAA,IAAA;AACA,kBAAA,IAAA,CAAA,cAAA,GAAA,IAAA;AACA;AAJA;AARA,aAFA,EAiBA,SAjBA,CAAA;AAmBA,WA1BA;AA2BA,UAAA,QAAA,EAAA,GA3BA;AA4BA,UAAA,QAAA,EAAA;AA5BA,SAFA,EAgCA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAhCA,CAbA;AAqDA,QAAA,UAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SADA,EAQA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,iBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SARA,EAcA;AACA,UAAA,KAAA,EAAA,aADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAdA,EAoBA;AACA,UAAA,KAAA,EAAA,aADA;AAEA,UAAA,GAAA,EAAA,gBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SApBA,EA0BA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,GAAA,EAAA,mBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA1BA,EAgCA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAhCA,EAiCA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,cAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAjCA,EAwCA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA,eAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAxCA,EAyCA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA,eAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAzCA,EA0CA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,GAAA,EAAA,oBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,GAJA;AAKA,UAAA,QAAA,EAAA,IALA;AAMA,UAAA,UAAA,EAAA,oBAAA,CAAA,EAAA,CAAA,EAAA,IAAA,EAAA;AACA,gBAAA,IAAA,KAAA,MAAA,EAAA;AACA,qBAAA,QAAA,CAAA,CAAA,CAAA,GAAA,QAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;AACA,aAFA,MAEA;AACA,qBAAA,QAAA,CAAA,CAAA,CAAA,GAAA,QAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA;AACA;AACA;AAZA,SA1CA,EAwDA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,MAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAxDA,EA+DA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,SAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SA/DA,EAsEA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,mBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAtEA,EA6EA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,kBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SA7EA,EAoFA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,mBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SApFA,EA2FA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,IAAA,EAAA,iBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SA3FA,EAkGA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAlGA,EAyGA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,IAAA,EAAA,kBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAzGA,EAgHA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,IAAA,EAAA,qBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAhHA,EAuHA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,IAAA,EAAA,SAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAvHA,EA8HA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SA9HA,EAqIA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SArIA,EA4IA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,GAAA,EAAA,cAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SA5IA,EAmJA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,GAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAnJA,EA0JA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,GAAA,EAAA,SAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA1JA,EA2JA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,GAAA,EAAA,kBAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,MAAA,EAAA;AALA,SA3JA,CArDA;AAwNA,QAAA,UAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA,MALA;AAMA,UAAA,MAAA,EAAA;AANA,SADA,CAxNA;AAkOA,QAAA,QAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,GAAA,EAAA,yBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SADA,CAlOA;AA2OA,QAAA,QAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SADA,EAQA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,GAAA,EAAA,yBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SARA,CA3OA;AA4PA,QAAA,IAAA,EAAA,EA5PA;AA6PA,QAAA,aAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA,yBAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,KAAA,EAAA,eAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,KAAA,EAAA,eAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAXA,EAYA;AAAA,UAAA,KAAA,EAAA,eAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAZA,EAaA;AAAA,UAAA,KAAA,EAAA,cAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAbA,EAcA;AAAA,UAAA,KAAA,EAAA,eAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAdA,EAeA;AAAA,UAAA,KAAA,EAAA,eAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAfA,EAgBA;AAAA,UAAA,KAAA,EAAA,eAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAhBA,EAiBA;AAAA,UAAA,KAAA,EAAA,cAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAjBA,EAkBA;AAAA,UAAA,KAAA,EAAA,WAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAlBA,EAmBA;AAAA,UAAA,KAAA,EAAA,eAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAnBA,EAoBA;AAAA,UAAA,KAAA,EAAA,aAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SApBA,EAqBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SArBA,EAsBA;AAAA,UAAA,KAAA,EAAA,eAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAtBA,EAuBA;AAAA,UAAA,KAAA,EAAA,WAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAvBA,EAwBA;AAAA,UAAA,KAAA,EAAA,WAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAxBA,EAyBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAzBA,EA0BA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SA1BA,EA2BA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SA3BA,EA4BA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SA5BA,EA6BA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SA7BA,EA8BA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SA9BA,EA+BA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SA/BA,EAgCA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAhCA,EAiCA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAjCA;AA7PA;AAlGA,KAAA;AAoYA,GA5bA;AA6bA,EAAA,OAAA,EAAA;AACA,IAAA,WADA,uBACA,CADA,EACA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,CAAA;AACA,KAHA;AAIA,IAAA,YAJA,wBAIA,CAJA,EAIA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,CAAA;AACA,KANA;AAOA,IAAA,OAPA,mBAOA,CAPA,EAOA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,IAAA,GAAA,CAAA;;AACA,UAAA,KAAA,KAAA,CAAA,cAAA,CAAA,IAAA,IAAA,KAAA,EAAA;AACA,aAAA,OAAA;AACA,aAAA,GAAA,GAAA,CAAA;AACA,OAHA,MAGA,IAAA,KAAA,KAAA,CAAA,cAAA,CAAA,IAAA,IAAA,IAAA,EAAA;AACA,aAAA,OAAA,GAAA,CAAA;AACA,aAAA,GAAA,GAAA,CAAA,CAAA;AACA;;AACA,UAAA,KAAA,GAAA,IAAA,KAAA,OAAA,GAAA,EAAA,EAAA;AACA,aAAA,OAAA,CAAA,CAAA;AACA;AACA,KAnBA;AAoBA,IAAA,SApBA,uBAoBA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,IAAA,EAFA,CAGA;;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,SAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA,CAfA,CAgBA;;AACA,WAAA,WAAA,GAAA,KAAA,CAjBA,CAkBA;;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,YAAA,GAAA,CAAA,CAnBA,CAoBA;AACA,KAzCA;AA0CA,IAAA,UA1CA,wBA0CA;AACA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,IAAA,GAAA,KAAA;AACA,KA9CA;AA+CA,IAAA,WA/CA,yBA+CA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,KAjDA;AAkDA,IAAA,UAlDA,wBAkDA,CAAA,CAlDA;AAmDA,IAAA,YAnDA,0BAmDA;AAAA;;AACA,MAAA,cAAA,CAAA,KAAA,UAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,MAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,EAAA;AACA,OAJA;AAKA,KAzDA;AA0DA;AACA,IAAA,QAAA,EAAA,kBAAA,SAAA,EAAA,SAAA,EAAA,OAAA,EAAA,QAAA,EAAA;AACA,UAAA,SAAA,IAAA,SAAA,IAAA,OAAA,EAAA;AACA,QAAA,cAAA,CAAA,SAAA,EAAA,SAAA,EAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,QAAA,EAAA,QAAA,CAAA,GAAA,CAAA,CAAA,KACA,QAAA;AACA,SAHA;AAIA;AACA,KAlEA;AAmEA;AACA,IAAA,MAAA,EAAA,gBAAA,IAAA,EAAA,QAAA,EAAA;AACA,MAAA,YAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,QAAA,EAAA,QAAA,CAAA,GAAA,CAAA,CAAA,KACA,QAAA;AACA,OAHA;AAIA,KAzEA;AA0EA;AACA,IAAA,UA3EA,sBA2EA,KA3EA,EA2EA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA,KAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAAA;AANA,SAAA;AAQA;;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KAjGA;AAkGA;AACA,IAAA,cAnGA,0BAmGA,KAnGA,EAmGA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA,KAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAAA;AANA,SAAA;AAQA;;AACA,WAAA,QAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KAzHA;AA0HA;AACA,IAAA,kBA3HA,gCA2HA;AAAA;;AACA,WAAA,eAAA;AACA,UAAA,MAAA,GAAA,KAAA,UAAA;AACA,MAAA,MAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA,MAAA,MAAA,CAAA,QAAA,GAAA,KAAA,QAAA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,mCADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,UAAA,KAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,KAAA,CACA,OADA,CACA,GADA,EAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,EAAA;AACA,UAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,EAFA,CAEA;;AACA,UAAA,YAAA,CAAA,MAAA,CAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,gBAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,YAAA,QAAA,CAAA,KAAA,GAAA,IAAA;AACA,YAAA,QAAA,CAAA,WAAA,GAAA,IAAA;AACA,YAAA,QAAA,CAAA,SAAA,GAAA,IAAA;AACA,YAAA,KAAA,CAAA,IAAA,CAAA,QAAA;AACA,WAPA;AAQA,UAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,IAAA,CAAA,CAZA,CAaA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,CAAA,CAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,CAAA,CAAA;AACA;AACA,OAvBA,EAwBA,KAxBA,CAwBA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OA1BA;AA2BA,KAlKA;AAmKA;AACA,IAAA,QApKA,oBAoKA,KApKA,EAoKA;AACA,UAAA,eAAA,GAAA,CAAA;AACA,UAAA,iBAAA,GAAA,CAAA;AACA,UAAA,WAAA,GAAA,CAAA;AACA,UAAA,cAAA,GAAA,CAAA;AACA,UAAA,SAAA,GAAA,CAAA;AACA,UAAA,WAAA,GAAA,CAAA;AACA,UAAA,YAAA,GAAA,CAAA;AACA,UAAA,mBAAA,GAAA,CAAA;AACA,UAAA,gBAAA,GAAA,CAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,KAAA,CAAA,EAAA;AACA,UAAA,eAAA,IAAA,IAAA,CAAA,eAAA;AACA,UAAA,iBAAA,IAAA,IAAA,CAAA,iBAAA;AACA,UAAA,WAAA,IAAA,IAAA,CAAA,WAAA;AACA,UAAA,cAAA,IAAA,IAAA,CAAA,cAAA;AACA,UAAA,SAAA,IAAA,IAAA,CAAA,SAAA;AACA,UAAA,mBAAA,IAAA,IAAA,CAAA,mBAAA;AACA,UAAA,gBAAA,IAAA,IAAA,CAAA,gBAAA;AACA,UAAA,WAAA,IAAA,IAAA,CAAA,WAAA;AACA,UAAA,YAAA,IAAA,IAAA,CAAA,YAAA;AACA;AACA,OAZA;AAaA,aAAA;AACA,QAAA,eAAA,EAAA,eADA;AAEA,QAAA,iBAAA,EAAA,iBAFA;AAGA,QAAA,WAAA,EAAA,WAAA,CAAA,OAAA,CAAA,CAAA,CAHA;AAIA,QAAA,cAAA,EAAA,cAAA,CAAA,OAAA,CAAA,CAAA,CAJA;AAKA,QAAA,SAAA,EAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CALA;AAMA,QAAA,mBAAA,EAAA,mBAAA,CAAA,OAAA,CAAA,CAAA,CANA;AAOA,QAAA,gBAAA,EAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAPA;AAQA,QAAA,WAAA,EAAA,WAAA,CAAA,OAAA,CAAA,CAAA,CARA;AASA,QAAA,YAAA,EAAA,YAAA,CAAA,OAAA,CAAA,CAAA,CATA;AAUA,QAAA,KAAA,EAAA,IAVA;AAWA,QAAA,WAAA,EAAA,IAXA;AAYA,QAAA,SAAA,EAAA;AAZA,OAAA;AAcA,KAzMA;AA0MA,IAAA,UA1MA,wBA0MA;AACA,WAAA,OAAA,GAAA,CAAA;AACA,WAAA,kBAAA;AACA,KA7MA;AA8MA,IAAA,aA9MA,2BA8MA;AACA,WAAA,UAAA,GAAA;AACA,QAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IADA;AACA;AACA,QAAA,UAAA,EAAA,EAFA;AAEA;AACA,QAAA,WAAA,EAAA,EAHA;AAGA;AACA,QAAA,iBAAA,EAAA,IAJA;AAIA;AACA,QAAA,WAAA,EAAA,EALA;AAKA;AACA,QAAA,QAAA,EAAA,EANA;AAMA;AACA,QAAA,OAAA,EAAA,KAAA,WAAA,CAAA,CAAA,EAAA,EAPA;AAQA,QAAA,MAAA,EAAA,EARA;AASA,QAAA,WAAA,EAAA,GATA;AASA;AACA,QAAA,uBAAA,EAAA;AAVA,OAAA;AAaA,WAAA,UAAA,CAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,eAAA,GAAA,EAAA;AACA,WAAA,YAAA;AACA,WAAA,kBAAA;AACA,KAjOA;AAkOA;AACA,IAAA,WAnOA,uBAmOA,IAnOA,EAmOA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAA,iBAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,aAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAA,OAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,gBAAA;AACA,QAAA,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAA,iBAAA;AACA,QAAA,IAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,mBAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,IAAA,CAAA,gBAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,IAAA,CAAA,WAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,gBAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,gBAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAA,eAAA;AAEA,QAAA,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAA,eAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAA,eAAA;AACA,QAAA,IAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,cAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,gBAAA;AAEA,QAAA,IAAA,CAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,KAAA,GAAA,oBAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,CAAA,IAAA,GAAA,mBAAA,CAAA,IAAA,CAAA;AACA,YAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,MAAA,GAAA,EAAA;AACA,YAAA,CAAA,IAAA,CAAA,EAAA,EAAA,IAAA,CAAA,EAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,iBAAA,GAAA,WAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,CAzBA,CA0BA;;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,WAAA,CAAA,IAAA,CAAA,mBAAA,CAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,WAAA,CAAA,IAAA,CAAA,gBAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,WAAA,CAAA,IAAA,CAAA,cAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,IAAA,CAAA,WAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,IAAA,CAAA,WAAA,CAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,WAAA,CAAA,IAAA,CAAA,eAAA,CAAA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,WAAA,CAAA,IAAA,CAAA,YAAA,CAAA;;AACA,YAAA,CAAA,IAAA,CAAA,OAAA,IAAA,IAAA,IAAA,IAAA,CAAA,OAAA,IAAA,CAAA,KAAA,IAAA,CAAA,KAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,YAAA,IAAA,CAAA,OAAA,IAAA,IAAA,CAAA,SAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,GAAA,cAAA,CAAA,IAAA,CAAA;AACA;AACA,OAxCA;AAyCA,KA7QA;AA8QA;AACA,IAAA,YA/QA,wBA+QA,GA/QA,EA+QA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA,EAAA,KAAA;AACA,MAAA,GAAA,CAAA,eAAA,GAAA,sBAAA,CAAA,GAAA,CAAA;AACA,MAAA,GAAA,CAAA,iBAAA,GAAA,uBAAA,CAAA,GAAA,CAAA;;AACA,UAAA,GAAA,CAAA,eAAA,IAAA,CAAA,IAAA,GAAA,CAAA,KAAA,IAAA,CAAA,EAAA;AACA,YAAA,GAAA,CAAA,YAAA,GAAA,CAAA,EAAA;AACA,cAAA,KAAA,GAAA,IAAA,CAAA,GAAA,CAAA,GAAA,CAAA,iBAAA,IAAA,IAAA,CAAA,GAAA,CAAA,GAAA,CAAA,YAAA,CAAA;AACA,cAAA,QAAA,GAAA,GAAA,CAAA,QAAA;AACA,cAAA,UAAA,GAAA,GAAA,CAAA,UAAA,CAHA,CAGA;;AACA,cAAA,YAAA,CAAA,QAAA,CAAA,IAAA,cAAA,CAAA,UAAA,CAAA,EAAA;AACA,YAAA,KAAA,GAAA,CAAA,KAAA;AACA;;AACA,UAAA,GAAA,CAAA,iBAAA,GAAA,KAAA;AACA;;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,MAAA;;AACA,YAAA,MAAA,CAAA,OAAA,CAAA,IAAA,KAAA,CAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,IAAA,gBAAA,GAAA,CAAA,YAAA,GAAA,IAAA;AACA;AACA;;AACA,UAAA,GAAA,CAAA,WAAA,IAAA,GAAA,CAAA,cAAA,EAAA;AACA,QAAA,GAAA,CAAA,YAAA,GAAA,sBAAA,CAAA,GAAA,CAAA;AACA,QAAA,GAAA,CAAA,SAAA,GAAA,8BAAA,CAAA,GAAA,CAAA;AACA;;AACA,MAAA,GAAA,CAAA,kBAAA,GAAA,4BAAA,CAAA,GAAA,CAAA;AACA,KAvSA;AAwSA;AACA,IAAA,gBAzSA,8BAySA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,WAAA,UAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OAJA;AAKA,UAAA,IAAA,GAAA,KAAA,CAPA,CAQA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,GAAA,CAAA;AACA,YAAA,UAAA,GAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,UAAA,CAAA,SAAA;AACA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,QAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,SAAA,CAAA;AACA,UAAA,IAAA,CAAA,SAAA,GAAA,QAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,UAAA,GAAA,QAAA,CAAA,EAAA;AACA,UAAA,QAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,QAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,GAAA,EAAA;AACA,UAAA,UAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,MAAA;AACA,SAVA;;AAWA,YAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,iBAAA,CAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,SAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,KADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;AAKA;;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,YAAA,IAAA,CAAA,kBAAA;AACA,WAVA;AAWA;AACA;AACA,KAhVA;AAiVA;AACA,IAAA,UAlVA,wBAkVA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,WAAA,UAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,GAAA,CAAA,OAAA,CAAA,KAAA,KAAA,CAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA;AACA,OAJA;AAKA,WAAA,KAAA,CAAA,WAAA,CAAA,UAAA,GAAA,GAAA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,KA3VA;AA4VA,IAAA,QA5VA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6VA,gBAAA,KA7VA,GA6VA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EA7VA;AA+VA,gBAAA,CA/VA,GA+VA,KA/VA;AAgWA,gBAAA,IAhWA,GAgWA,KAAA,UAhWA;AAiWA,gBAAA,KAjWA,GAiWA,EAjWA;AAkWA,gBAAA,IAlWA,GAkWA,IAlWA;AAoWA,gBAAA,OApWA,GAoWA,SAAA,CAAA,OApWA;AAqWA,gBAAA,CArWA,GAqWA,CArWA;;AAAA;AAAA,sBAqWA,CAAA,GAAA,KAAA,CAAA,MArWA;AAAA;AAAA;AAAA;;AAAA,sBAsWA,KAAA,CAAA,CAAA,CAAA,CAAA,QAAA,IAAA,CAtWA;AAAA;AAAA;AAAA;;AAuWA,oBAAA,QAAA,OAAA,IAAA,KAAA,CAAA,CAAA,CAAA,CAAA,WAAA,IAAA,KAAA,CAAA,CAAA,CAAA,CAAA,WAAA,GAAA,IAAA,EAAA;AACA,sBACA,KAAA,CAAA,CAAA,CAAA,CAAA,cAAA,IAAA,IAAA,IACA,KAAA,CAAA,CAAA,CAAA,CAAA,cAAA,IAAA,SADA,IAEA,KAAA,CAAA,CAAA,CAAA,CAAA,cAAA,IAAA,EAHA,EAIA;AACA,yBAAA,SAAA,CACA,aACA,KAAA,CAAA,CAAA,CAAA,CAAA,WADA,GAEA,SAFA,GAGA,KAAA,CAAA,CAAA,CAAA,CAAA,WAHA,GAIA,8CALA;AAOA;AACA,iBArXA,CAsXA;;;AAtXA;AAAA,uBAuXA,KAAA,aAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAvXA;;AAAA;AAuXA,gBAAA,MAvXA;;AAAA,qBAwXA,MAxXA;AAAA;AAAA;AAAA;;AAyXA,qBAAA,SAAA,CAAA,MAAA;AAzXA;;AAAA;AA4XA,gBAAA,SA5XA,GA4XA,QAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CA5XA,EA4XA;;AACA,oBAAA,SAAA,IAAA,IAAA,IAAA,SAAA,GAAA,CAAA,EAAA;AACA,sBAAA,KAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,GAAA,SAAA,EAAA;AACA,oBAAA,IAAA,CAAA,SAAA,CAAA,iBAAA,SAAA;AACA,mBAFA,MAEA;AACA,oBAAA,CAAA,GAAA,IAAA;AACA,oBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA;AACA,iBAPA,MAOA;AACA,kBAAA,CAAA,GAAA,IAAA;AACA,kBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA;;AAvYA;AAqWA,gBAAA,CAAA,EArWA;AAAA;AAAA;;AAAA;AA0YA;AACA,oBAAA,CAAA,EAAA;AACA,uBAAA,UAAA,CAAA,KAAA;AACA,iBAFA,MAEA;AACA,uBAAA,SAAA,CAAA,SAAA;AACA;;AA/YA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAiZA,IAAA,eAjZA;AAAA;AAAA;AAAA,gDAiZA,WAjZA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAkZA,qBAAA,aAAA,GAAA,CAAA;AAlZA;AAAA,uBAmZA,iBAAA,CAAA;AAAA,kBAAA,WAAA,EAAA;AAAA,iBAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,EAAA;AACA,oBAAA,MAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA;AACA;AACA,iBAJA,CAnZA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAyZA;AACA,IAAA,UA1ZA,sBA0ZA,IA1ZA,EA0ZA;AACA;AACA,UAAA,CAAA,GAAA,EAAA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,GAAA,CAAA;AACA,YAAA,UAAA,GAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,UAAA,CAAA,SAAA;AACA,YAAA,IAAA,GAAA,IAAA;AACA,YAAA,IAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,GAAA,GAAA,sBAAA,CAAA,IAAA,CAAA;;AACA,cAAA,GAAA,CAAA,MAAA,EAAA;AACA,gBAAA,QAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,SAAA,CAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,QAAA,CAAA,IAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,QAAA,CAAA,EAAA;AACA,YAAA,QAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,QAAA,CAAA,IAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA,EAAA;AACA,YAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,SAAA;AACA,YAAA,UAAA,CAAA,IAAA,CAAA,IAAA;AACA,YAAA,MAAA;AACA,WAVA,MAUA;AACA,YAAA,GAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,cAFA,GAGA,GAAA,CAAA,GAHA,GAIA,IALA;AAMA;;AACA,cAAA,IAAA,CAAA,gBAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,SAFA,GAGA,IAAA,CAAA,aAHA,GAIA,SAJA,GAKA,IAAA,CAAA,YALA,GAMA,gBAPA;AAQA;;AAEA,cAAA,IAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,WAFA,GAGA,IAAA,CAAA,OAHA,GAIA,WAJA,GAKA,IAAA,CAAA,UALA,GAMA,eAPA;AAQA;AACA,SAzCA;AA0CA,QAAA,IAAA,CAAA,UAAA,GAAA,CAAA;;AACA,YAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,GAAA;AACA;;AACA,YAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,IAAA,EAAA,IAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA;AAKA;;AACA,YAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA;AACA,eAAA,MAAA,GAAA,UAAA;AACA,eAAA,OAAA,GAAA,UAAA;AACA,UAAA,OAAA,CAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,SAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,KADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;AAKA;;AAEA,gBAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,YAAA,IAAA,CAAA,kBAAA;AACA,WAdA;AAeA;AACA;AACA,KA/eA;AAgfA,IAAA,YAhfA,wBAgfA,SAhfA,EAgfA;AACA,UAAA,IAAA,GAAA,EAAA;AACA,WAAA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA;AACA,QAAA,SAAA,CAAA,GAAA,CAAA,UAAA,KAAA,EAAA;AACA,cAAA,KAAA,IAAA,KAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,SAJA;AAKA,OANA;AAOA,WAAA,MAAA,GAAA,IAAA;AACA,KA1fA;AA2fA,IAAA,iBA3fA,6BA2fA,IA3fA,EA2fA;AAAA;;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA,OAFA;AAGA,UAAA,KAAA,GAAA;AACA,QAAA,KAAA,EAAA;AADA,OAAA;AAGA,MAAA,qBAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,MAAA,CAAA,eAAA,GAAA,IAAA,CAAA,IAAA;;AAEA,YAAA,MAAA,CAAA,eAAA,IAAA,MAAA,CAAA,eAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,MAAA,CAAA,MAAA;AACA,UAAA,MAAA,CAAA,OAAA,GAAA,IAAA,CAFA,CAGA;AACA,SAJA,MAIA,IAAA,IAAA,CAAA,MAAA,IAAA,MAAA,CAAA,eAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,MAAA;AACA,UAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AACA,SAHA,MAGA,IAAA,IAAA,CAAA,MAAA,IAAA,MAAA,CAAA,eAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,UAAA,MAAA,CAAA,MAAA,GAAA,CAAA;AACA,SAHA,MAGA;AACA,UAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,UAAA,MAAA,CAAA,MAAA,GAAA,CAAA;AACA;;AACA,YAAA,MAAA,CAAA,OAAA,IAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,UAAA,CAAA;AAAA,mBAAA,MAAA,CAAA,iBAAA,CAAA,IAAA,CAAA;AAAA,WAAA,EAAA,IAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,eAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,SAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AAEA,gBAAA,IAAA,CAAA,MAAA,IAAA,IAAA,EAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAA,IAAA,CAAA,gBAAA,CAAA,cAAA,IAAA,GAAA,EAAA;AACA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,kBAAA,IAAA,CAAA,gBAAA,CAAA,iBAAA,IAAA,GAAA,EAAA;AACA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,kBACA,IAAA,CAAA,gBAAA,CAAA,kBAAA,IAAA,GAAA,IACA,IAAA,CAAA,gBAAA,CAAA,kBAAA,IAAA,GADA,IAEA,IAAA,CAAA,gBAAA,CAAA,qBAAA,IAAA,GAFA,IAGA,IAAA,CAAA,gBAAA,CAAA,aAAA,IAAA,GAHA,IAIA;AACA,cAAA,IAAA,CAAA,gBAAA,CAAA,kBAAA,IAAA,GALA,IAMA,IAAA,CAAA,gBAAA,CAAA,mBAAA,IAAA,GAPA,EAQA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA,EADA,CACA;;;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA,eA7BA,CA8BA;;AACA,aA/BA,MA+BA;AACA,mBACA;AACA;AACA,cAAA,IAAA,CAAA,gBAAA,CAAA,eAAA,IAAA,GAHA,CAGA;AAHA,gBAIA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA,iBAPA,MAOA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;AACA;;AACA,gBAAA,MAAA,CAAA,eAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,eAAA,CAAA,MAAA,CAAA,eAAA,CAAA,MAAA,GAAA,CAAA,EAAA,QAAA,GACA,MAAA,CAAA,eAAA,CAAA,MAAA,GAAA,CAAA,GAAA,IAAA,CAAA,MAAA,GAAA,CADA;AAEA;;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,GAAA,GAAA;;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA;AACA,WAtDA;AAuDA;AACA,OA5EA;AA6EA,KAhlBA;AAilBA,IAAA,IAjlBA,gBAilBA,KAjlBA,EAilBA;AACA,UAAA,IAAA,GAAA,IAAA,CADA,CAEA;;AACA,UAAA,KAAA,IAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,IAAA,GAAA,QAAA;AACA,aAAA,iBAAA,CAAA,IAAA,CAAA,MAAA;AACA,OAJA,MAIA;AACA,YAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,cAAA,CAAA,IAAA,CAAA,EAAA;AACA,iBAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EAAA;AACA;;AACA,YAAA,KAAA,IAAA,IAAA,SAAA,EAAA;AACA,cAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,IAAA,CAAA,EAAA;AACA,iBAAA,WAAA;AACA,WAFA,MAEA;AACA,iBAAA,eAAA;AACA;AACA,SANA,MAMA,IAAA,KAAA,IAAA,IAAA,KAAA,EAAA;AACA,cAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,IAAA,CAAA,EAAA;AACA,iBAAA,WAAA;AACA,WAFA,MAEA;AACA,iBAAA,kBAAA;AACA;AACA;AACA;AACA,KA1mBA;AA2mBA;AACA,IAAA,WA5mBA,yBA4mBA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,eAAA;AACA,MAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CACA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UADA,EAEA,CAFA,EAGA,KAAA,UAAA,CAAA,OAHA;AAKA,KApnBA;AAqnBA;AACA,IAAA,UAtnBA,sBAsnBA,GAtnBA,EAsnBA;AACA,UAAA,GAAA,GAAA,GAAA,CAAA,oBAAA;;AACA,UAAA,GAAA,CAAA,MAAA,EAAA;AACA;AACA,OAFA,MAEA,IACA,GAAA,CAAA,eAAA,IAAA,IAAA,IACA,GAAA,CAAA,eAAA,GAAA,CADA,IAEA,KAAA,oBAAA,GAAA,GAAA,CAAA,eAHA,EAIA;AACA,aAAA,SAAA,CAAA,iBAAA,GAAA,CAAA,eAAA;AACA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,oBAAA,GAAA,GAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OAVA,MAUA;AACA,QAAA,GAAA,CAAA,gBAAA,GAAA,KAAA,oBAAA;AACA,QAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,GAAA;;AACA,YAAA,GAAA,CAAA,oBAAA,IAAA,GAAA,CAAA,gBAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,IACA,aACA,GAAA,CAAA,oBADA,GAEA,KAFA,GAGA,GAAA,CAAA,gBAHA,GAIA,IALA;AAMA,SAXA,CAYA;;;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,MAAA;;AACA,YAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,GAAA,MAAA,GAAA,CAAA;AACA;AACA;AACA,KAtpBA;AAupBA;AACA,IAAA,UAxpBA,sBAwpBA,GAxpBA,EAwpBA;AACA,UAAA,GAAA,GAAA,GAAA,CAAA,oBAAA;;AACA,UAAA,GAAA,CAAA,MAAA,EAAA;AACA;AACA,OAFA,MAEA,IACA,GAAA,CAAA,eAAA,IAAA,IAAA,IACA,GAAA,CAAA,eAAA,GAAA,CADA,IAEA,KAAA,oBAAA,GAAA,GAAA,CAAA,eAHA,EAIA;AACA,aAAA,SAAA,CAAA,iBAAA,GAAA,CAAA,eAAA;AAEA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,oBAAA,GAAA,GAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OAXA,MAWA;AACA,QAAA,GAAA,CAAA,gBAAA,GAAA,KAAA,oBAAA;AACA,QAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,GAAA;;AACA,YAAA,GAAA,CAAA,oBAAA,IAAA,GAAA,CAAA,gBAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,IACA,aACA,GAAA,CAAA,oBADA,GAEA,KAFA,GAGA,GAAA,CAAA,gBAHA,GAIA,IALA;AAMA,SAXA,CAYA;;;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,MAAA;;AACA,YAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,GAAA,MAAA,GAAA,CAAA;AACA;AACA;AACA,KAzrBA;AA0rBA;AACA,IAAA,UA3rBA,sBA2rBA,GA3rBA,EA2rBA;AACA,UAAA,GAAA,GAAA,GAAA,CAAA,mBAAA;;AACA,UAAA,GAAA,CAAA,MAAA,EAAA;AACA;AACA,OAFA,MAEA,IACA,GAAA,CAAA,cAAA,IAAA,IAAA,IACA,GAAA,CAAA,cAAA,GAAA,CADA,IAEA,KAAA,mBAAA,GAAA,GAAA,CAAA,cAHA,EAIA;AACA,aAAA,SAAA,CAAA,iBAAA,GAAA,CAAA,cAAA;AAEA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,mBAAA,GAAA,GAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OAXA,MAWA;AACA,QAAA,GAAA,CAAA,eAAA,GAAA,KAAA,mBAAA;AACA,QAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,GAAA;;AACA,YAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,CAAA,eAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,IACA,aACA,GAAA,CAAA,mBADA,GAEA,KAFA,GAGA,GAAA,CAAA,eAHA,GAIA,IALA;AAMA,SAXA,CAYA;;;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,MAAA;;AACA,YAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,GAAA,MAAA,GAAA,CAAA;AACA;AACA;AACA,KA5tBA;AA6tBA,IAAA,kBA7tBA,8BA6tBA,GA7tBA,EA6tBA;AACA,UAAA,GAAA,CAAA,MAAA,EAAA;AACA;AACA,OAFA,MAEA,IAAA,KAAA,mBAAA,GAAA,GAAA,CAAA,gBAAA,EAAA;AACA,aAAA,SAAA,CAAA,iBAAA,GAAA,CAAA,gBAAA;AAEA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,mBAAA,GAAA,GAAA,CAAA,eAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OAPA,MAOA;AACA,QAAA,GAAA,CAAA,eAAA,GAAA,KAAA,mBAAA;AACA,QAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,GAAA;AACA;AACA,KA5uBA;AA6uBA,IAAA,kBA7uBA,8BA6uBA,GA7uBA,EA6uBA;AACA,UAAA,GAAA,CAAA,MAAA,EAAA;AACA;AACA,OAFA,MAEA,IAAA,KAAA,mBAAA,GAAA,GAAA,CAAA,gBAAA,EAAA;AACA,aAAA,SAAA,CAAA,iBAAA,GAAA,CAAA,gBAAA;AAEA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,mBAAA,GAAA,GAAA,CAAA,eAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OAPA,MAOA;AACA,QAAA,GAAA,CAAA,eAAA,GAAA,KAAA,mBAAA;AACA,QAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,GAAA;AACA;AACA,KA5vBA;AA6vBA,IAAA,iBA7vBA,6BA6vBA,GA7vBA,EA6vBA;AACA,UAAA,IAAA,GAAA,GAAA,CAAA,MAAA;;AACA,UAAA,GAAA,CAAA,MAAA,EAAA;AACA;AACA,OAFA,MAEA,IAAA,KAAA,kBAAA,GAAA,GAAA,CAAA,eAAA,EAAA;AACA,aAAA,SAAA,CAAA,iBAAA,GAAA,CAAA,eAAA;AACA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,cAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OANA,MAMA;AACA,QAAA,GAAA,CAAA,cAAA,GAAA,KAAA,kBAAA;AACA,QAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,GAAA;AACA;AACA,KA5wBA;AA6wBA,IAAA,MA7wBA,kBA6wBA,GA7wBA,EA6wBA;AACA,UAAA,IAAA,GAAA;AACA,QAAA,gBAAA,EAAA,GAAA,CAAA,gBADA;AAEA,QAAA,gBAAA,EAAA,GAAA,CAAA,gBAFA;AAGA,QAAA,eAAA,EAAA,GAAA,CAAA,eAHA;AAIA,QAAA,eAAA,EAAA,GAAA,CAAA,eAJA;AAKA,QAAA,eAAA,EAAA,GAAA,CAAA,eALA;AAMA,QAAA,cAAA,EAAA,GAAA,CAAA,cANA;AAOA,QAAA,YAAA,EAAA,UAAA,CAAA,KAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAPA;AAQA,QAAA,YAAA,EAAA,UAAA,CAAA,KAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CARA;AASA,QAAA,WAAA,EAAA,UAAA,CAAA,KAAA,eAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CATA;AAUA,QAAA,aAAA,EAAA,GAAA,CAAA;AAVA,OAAA;;AAYA,UAAA,MAAA,GAAA,+BAAA,CAAA,IAAA,CAAA;;AAEA,UAAA,MAAA,GAAA,CAAA,EAAA;AACA;AACA,aAAA,SAAA,CACA,cACA,MADA,GAEA,gBAFA,GAGA,UAHA,GAIA,UAAA,CAAA,KAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAJA,GAKA,WALA,GAMA,UAAA,CAAA,KAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CANA,GAOA,WAPA,GAQA,UAAA,CAAA,KAAA,eAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CARA,GASA,OATA,GAUA,GAAA,CAAA,eAXA,EAFA,CAeA;AACA;;AACA,MAAA,GAAA,CAAA,YAAA,GAAA,UAAA,CAAA,KAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,GAAA,CAAA,YAAA,GAAA,UAAA,CAAA,KAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,GAAA,CAAA,WAAA,GAAA,UAAA,CAAA,KAAA,eAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,WAAA,YAAA,CAAA,GAAA;AACA,KAlzBA;AAmzBA;AACA,IAAA,SApzBA,uBAozBA;AAAA;;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,iBAFA;AAGA,QAAA,IAAA,EAAA,gBAAA;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,cAAA,MAAA,GAAA,MAAA,CAAA,UAAA;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,IAAA;AACA,iBAAA,MAAA,CAAA,QAAA;AACA,iBAAA,MAAA,CAAA,OAAA;AACA,UAAA,SAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,QAAA;;AACA,cAAA,MAAA,CAAA,UAAA;AACA,aAHA,MAGA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA;AACA,WARA;AASA,SAlBA;AAmBA,QAAA,QAAA,EAAA,oBAAA,CAAA;AAnBA,OAAA;AAqBA,KA10BA;AA20BA;AACA,IAAA,MA50BA,oBA40BA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,WAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,GAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,MAFA,GAGA,IAAA,CAAA,SAHA,GAIA,oBALA;AAMA;;AACA,QAAA,GAAA,IAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA,OAVA;;AAWA,UAAA,GAAA,CAAA,MAAA,GAAA,CAAA,IAAA,GAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,oBAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,SAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,EAAA;AACA,gBAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,kBAAA,OAAA,EAAA,SAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,KADA;AAEA,kBAAA,QAAA,EAAA,EAFA;AAGA,kBAAA,QAAA,EAAA;AAHA,iBAAA;AAKA;;AAEA,kBAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,gBAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,cAAA,IAAA,CAAA,UAAA;AACA,aAbA;AAcA,WAlBA;AAmBA,UAAA,QAAA,EAAA,oBAAA,CAAA;AAnBA,SAAA;AAqBA,OAtBA,MAsBA;AACA,QAAA,IAAA,CAAA,SAAA,CAAA,GAAA;AACA;AACA,KA13BA;AA23BA;AACA,IAAA,UA53BA,sBA43BA,GA53BA,EA43BA,KA53BA,EA43BA,OA53BA,EA43BA,GA53BA,EA43BA;AACA,WAAA,aAAA,GAAA,GAAA,CAAA,SAAA;AACA,WAAA,WAAA,GAAA,GAAA,CAAA,OAAA;AACA,WAAA,qBAAA,GACA,GAAA,CAAA,iBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,iBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,iBAHA;AAIA,WAAA,oBAAA,GACA,GAAA,CAAA,gBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,gBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,gBAHA;AAIA,WAAA,qBAAA,GACA,GAAA,CAAA,iBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,iBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,iBAHA;AAIA,WAAA,kBAAA,GACA,GAAA,CAAA,mBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,mBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,mBAHA;AAIA,WAAA,eAAA,GACA,GAAA,CAAA,gBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,gBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,gBAHA;AAIA,WAAA,eAAA,GACA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,KAAA,CAAA,GAAA,IAAA,GAAA,GAAA,CAAA,WADA;AAEA,WAAA,WAAA,GACA,GAAA,CAAA,OAAA,IAAA,IAAA,IAAA,GAAA,CAAA,OAAA,KAAA,CAAA,GAAA,IAAA,GAAA,QAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,EADA;AAEA,WAAA,UAAA,GAAA,GAAA,CAAA,EAAA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,WAAA,YAAA,GAAA,OAAA;AAEA,UAAA,CAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,YAAA,OAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,CAAA,KAAA,CAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA;AACA,OAJA,EAIA,GAJA,CAAA;AAKA,KAj6BA;AAk6BA;AACA,IAAA,YAn6BA,wBAm6BA,MAn6BA,EAm6BA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,cAAA,MAAA;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,WAAA;AACA,UAAA,IAAA,GAAA,KAAA,aAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,SAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,mBAAA;AACA,UAAA,IAAA,GAAA,KAAA,qBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,kBAAA;AACA,UAAA,IAAA,GAAA,KAAA,oBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,mBAAA;AACA,UAAA,IAAA,GAAA,KAAA,qBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,kBAAA;AACA,UAAA,IAAA,GAAA,KAAA,eAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,qBAAA;AACA,UAAA,IAAA,GAAA,KAAA,kBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,SAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,aAAA;AACA,UAAA,IAAA,GAAA,KAAA,eAAA;AACA;;AACA,aAAA,EAAA;AACA,UAAA,GAAA,GAAA,QAAA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA;AACA;AAxCA;;AA0CA,aAAA;AAAA,QAAA,GAAA,EAAA,GAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA;AACA,KAj9BA;AAk9BA;AACA,IAAA,QAn9BA,sBAm9BA;AACA,UAAA,KAAA,YAAA,KAAA,EAAA,EAAA;AACA,aAAA,cAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,YAAA,CAAA,KAAA,YAAA,EAAA,IAAA;;AACA,UAAA,GAAA,EAAA;AACA,YAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,kBAAA,KAAA,YAAA;AACA,iBAAA,CAAA;AACA,mBAAA,iBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,eAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,yBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,wBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,yBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,mBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,sBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,mBAAA;AACA;AAxBA;AA0BA,SA3BA,MA2BA;AACA,eAAA,SAAA,CAAA,QAAA;AACA;AACA;AACA,KAz/BA;AA0/BA;AACA,IAAA,SA3/BA,qBA2/BA,GA3/BA,EA2/BA;AACA,WAAA,OAAA,CAAA,KAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,GAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA;AAKA,KAjgCA;AAkgCA;AACA,IAAA,QAngCA,oBAmgCA,IAngCA,EAmgCA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,YAAA;AACA,UAAA,GAAA,GAAA,EAAA;;AACA,UAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAA,KAAA,GAAA,CAAA;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OAHA,MAGA,IAAA,KAAA,GAAA,CAAA,CAAA,IAAA,OAAA,KAAA,EAAA,EAAA;AACA;AACA,YAAA,KAAA,IAAA,IAAA,CAAA,QAAA,GAAA,CAAA,IAAA,KAAA,IAAA,IAAA,CAAA,SAAA,GAAA,CAAA,EAAA;AACA,UAAA,KAAA,GAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,KAAA;AACA;;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OARA,MAQA;AACA,QAAA,GAAA,GAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CADA,CAEA;;AACA,YAAA,GAAA,KAAA,GAAA,CAAA,KAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,OAAA,IAAA,CAAA,IAAA,OAAA,IAAA,CAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,EAAA;AACA,YAAA,OAAA,IAAA,CAAA;AACA,WAFA,MAEA;AACA,YAAA,OAAA,IAAA,CAAA;AACA;AACA,SANA,MAMA;AACA,UAAA,OAAA,IAAA,CAAA;AACA,SAXA,CAYA;;;AACA,YAAA,GAAA,CAAA,MAAA,EAAA;AACA,cAAA,OAAA,GAAA,CAAA,EAAA;AACA,YAAA,OAAA,GAAA,CAAA;AACA;AACA;AACA;;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,MAAA,GAAA,GAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA;;AACA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,GAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,OAAA;AACA,QAAA,IAAA,CAAA,qBAAA,GACA,GAAA,CAAA,iBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,iBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,iBAHA;AAIA,QAAA,IAAA,CAAA,oBAAA,GACA,GAAA,CAAA,gBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,gBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,gBAHA;AAIA,QAAA,IAAA,CAAA,qBAAA,GACA,GAAA,CAAA,iBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,iBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,iBAHA;AAIA,QAAA,IAAA,CAAA,kBAAA,GACA,GAAA,CAAA,mBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,mBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,mBAHA;AAIA,QAAA,IAAA,CAAA,eAAA,GACA,GAAA,CAAA,gBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,gBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,gBAHA;AAIA,QAAA,IAAA,CAAA,eAAA,GACA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,KAAA,CAAA,GAAA,IAAA,GAAA,GAAA,CAAA,WADA;AAEA,QAAA,IAAA,CAAA,WAAA,GACA,GAAA,CAAA,OAAA,IAAA,IAAA,IAAA,GAAA,CAAA,OAAA,KAAA,CAAA,GAAA,IAAA,GAAA,QAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,EADA;AAEA,QAAA,IAAA,CAAA,UAAA,GAAA,GAAA,CAAA,EAAA;AACA;;AAEA,MAAA,UAAA,CAAA,YAAA;AACA,YAAA,OAAA,IAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA;AACA,OAJA,EAIA,GAJA,CAAA;AAKA,KA3kCA;AA4kCA;AACA,IAAA,iBA7kCA,+BA6kCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,aAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,aAAA,EAAA;AACA;AACA,YAAA,MAAA,GAAA,iBAAA,CAAA,IAAA,EAAA,GAAA,EAAA,KAAA,CAAA;;AACA,YAAA,MAAA,EAAA;AACA;AACA,eAAA,SAAA,CAAA,MAAA;AACA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,SAAA,GAAA,WAAA;AACA,SAJA,MAIA;AACA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,SAAA,GAAA,QAAA;AACA,eAAA,UAAA,GAAA,IAAA;AACA;AACA,OAXA,MAWA,IAAA,GAAA,IAAA,IAAA,CAAA,aAAA,EAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,GAAA;AACA;AACA,KA9lCA;AA+lCA;AACA,IAAA,eAhmCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAimCA,gBAAA,IAjmCA,GAimCA,KAAA,UAAA,CAAA,KAAA,SAAA,CAjmCA;AAkmCA,gBAAA,GAlmCA,GAkmCA,KAAA,WAlmCA;;AAAA,sBAmmCA,GAAA,IAAA,IAAA,CAAA,WAnmCA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAqmCA,KAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CArmCA;;AAAA;AAqmCA,gBAAA,MArmCA;;AAsmCA,oBAAA,MAAA,EAAA;AACA,uBAAA,SAAA,CAAA,MAAA;AACA,uBAAA,OAAA,CAAA,KAAA,SAAA,EAAA,OAAA,GAAA,WAAA;AACA,iBAHA,MAGA;AACA,uBAAA,OAAA,CAAA,KAAA,SAAA,EAAA,OAAA,GAAA,QAAA;AAEA,uBAAA,aAAA,CAAA,IAAA,EAAA,GAAA;AACA;;AA7mCA;AAAA;;AAAA;AA8mCA,oBAAA,GAAA,IAAA,IAAA,CAAA,WAAA,EAAA;AACA,kBAAA,IAAA,CAAA,OAAA,GAAA,GAAA;AACA;;AAhnCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAknCA;AACA,IAAA,aAnnCA;AAAA;AAAA;AAAA,gDAmnCA,IAnnCA,EAmnCA,GAnnCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAonCA;AACA,gBAAA,KArnCA,GAqnCA,EArnCA;AAsnCA,gBAAA,MAtnCA,GAsnCA,YAAA,CAAA,GAAA,CAtnCA,EAsnCA;;AACA,gBAAA,OAvnCA,GAunCA,YAAA,CAAA,cAAA,EAAA,CAvnCA,EAunCA;;AAvnCA,sBAwnCA,IAAA,CAAA,gBAAA,IAAA,CAAA,IAAA,MAAA,GAAA,OAxnCA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAynCA,KAAA,CACA,OADA,CACA;AACA,kBAAA,GAAA,EAAA,0BADA;AAEA,kBAAA,MAAA,EAAA,MAFA;AAGA,kBAAA,IAAA,EAAA;AACA,oBAAA,QAAA,EAAA,IADA;AAEA,oBAAA,SAAA,EAAA,QAFA;AAGA,oBAAA,MAAA,EAAA,IAAA,CAAA,IAAA,GAAA,EAHA;AAIA,oBAAA,YAAA,EAAA,MAJA;AAKA,oBAAA,OAAA,EAAA,CALA;AAMA,oBAAA,QAAA,EAAA;AANA;AAHA,iBADA,EAaA,IAbA,CAaA,UAAA,GAAA,EAAA;AACA,yBAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,iBAfA,CAznCA;;AAAA;AAynCA,gBAAA,KAznCA;;AAAA,sBAyoCA,KAAA,CAAA,MAAA,IAAA,CAzoCA;AAAA;AAAA;AAAA;;AAAA,kDA0oCA,2BA1oCA;;AAAA;AA4oCA,gBAAA,KAAA,GAAA,MAAA,CA5oCA,CA4oCA;;AA5oCA;AA+oCA;AACA,gBAAA,MAhpCA,GAgpCA,eAAA,CAAA,IAAA,EAAA,GAAA,EAAA,KAAA,CAhpCA;AAAA,kDAipCA,MAjpCA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAmpCA,IAAA,aAnpCA,yBAmpCA,IAnpCA,EAmpCA,GAnpCA,EAmpCA;AAAA;;AACA,MAAA,IAAA,CAAA,OAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CAFA,CAGA;;AACA,WAAA,QAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,OAAA,EAAA,UAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,IAAA,KAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,CAAA;AACA;;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,IAAA;AACA,OAPA;AAQA,WAAA,WAAA,GAAA,WAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,OAAA,CAAA;AACA,KAhqCA;AAiqCA;AACA,IAAA,yBAlqCA,uCAkqCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,qBAAA;AACA,MAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,qBAAA,EAAA;AACA;AACA,YAAA,MAAA,GAAA,yBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;;AACA,YAAA,MAAA,CAAA,MAAA,EAAA;AACA,eAAA,SAAA,CAAA,MAAA,CAAA,MAAA;AACA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,iBAAA,GAAA,WAAA;AACA,SAHA,MAGA;AACA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,iBAAA,GAAA,QAAA;AACA,eAAA,WAAA,GAAA,MAAA,CAAA,CAAA;AACA,eAAA,OAAA,GAAA,IAAA;AACA;AACA,OAXA,MAWA,IAAA,GAAA,IAAA,IAAA,CAAA,qBAAA,EAAA;AACA,QAAA,IAAA,CAAA,iBAAA,GAAA,GAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;AACA,KArrCA;AAsrCA;AACA,IAAA,wBAvrCA,sCAurCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,oBAAA;;AAEA,UAAA,GAAA,IAAA,IAAA,CAAA,oBAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;;AACA,YAAA,MAAA,GAAA,wBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;;AACA,YAAA,MAAA,CAAA,MAAA,EAAA;AACA,eAAA,SAAA,CAAA,MAAA,CAAA,MAAA;AACA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,gBAAA,GAAA,WAAA;AACA,SAHA,MAGA;AACA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,gBAAA,GAAA,QAAA;AAEA,eAAA,sBAAA,CAAA,IAAA,EAAA,GAAA,EAAA,MAAA;AACA;;AAEA,YAAA,aAAA,GAAA,CAAA;AACA,aAAA,UAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA,aAAA,UAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA,aAAA,UAAA,CAAA,OAAA,GAAA,IAAA,CAAA,OAAA;AACA,OAhBA,MAgBA,IAAA,GAAA,IAAA,IAAA,CAAA,oBAAA,EAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,GAAA;AAEA,aAAA,YAAA,CAAA,IAAA;AACA;AACA,KAhtCA;AAitCA,IAAA,sBAjtCA,kCAitCA,IAjtCA,EAitCA,GAjtCA,EAitCA,MAjtCA,EAitCA;AAAA;;AACA,MAAA,IAAA,CAAA,gBAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,UAAA,CAAA,GAAA,MAAA,CAAA,CAAA;;AACA,UAAA,CAAA,KAAA,IAAA,EAAA;AACA,aAAA,OAAA,GAAA,IAAA;AACA,OAFA,MAEA,IAAA,CAAA,KAAA,KAAA,EAAA;AACA,aAAA,SAAA,GAAA,IAAA;AACA,OAFA,MAEA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;;AACA,UAAA,SAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA;AACA,QAAA,KAAA,CACA,OADA,CACA;AACA,UAAA,GAAA,EAAA,kCADA;AAEA,UAAA,MAAA,EAAA,MAFA;AAGA,UAAA,MAAA,EAAA;AAAA,YAAA,IAAA,EAAA,IAAA,CAAA,IAAA;AAAA,YAAA,SAAA,EAAA,IAAA,CAAA;AAAA;AAHA,SADA,EAMA,IANA,CAMA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,GAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA;AACA,cAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,MAAA,CAAA,EACA,GAAA,IAAA,mBAAA;AACA,cAAA,GAAA,IAAA,EAAA,EACA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,IAAA,EAAA,WAAA,IAAA,CAAA,WAAA,GAAA,GAAA,GAAA,GAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA;;AAKA,cAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA,MAAA,CAAA,IAAA,EAAA;AACA,cAAA,YAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,SADA;AAEA,cAAA,kBAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AAFA,aAAA;AAIA;AACA,SAvBA;AAwBA;AACA,KAvvCA;AAwvCA;AACA,IAAA,yBAzvCA,uCAyvCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,qBAAA;AACA,UAAA,IAAA,GAAA,KAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,qBAAA,EAAA;AACA,YACA,GAAA,IAAA,IAAA,CAAA,qBAAA,IACA,SAAA,CAAA,OAAA,IAAA,IADA,IAEA,IAAA,CAAA,eAAA,GAAA,CAFA,IAGA,UAAA,CAAA,UAAA,CAAA,GAAA,CAAA,GAAA,UAAA,CAAA,IAAA,CAAA,eAAA,CAAA,CAAA,GAAA,GAHA,KAIA,IAAA,CAAA,UAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,EAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CAJA,CADA,EAOA,IAAA,GAAA,IAAA;;AACA,YAAA,IAAA,EAAA;AACA,cAAA,MAAA,GAAA,wBAAA;AACA,eAAA,SAAA,CAAA,MAAA;AAEA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,iBAAA,GAAA,WAAA;AACA,UAAA,IAAA,CAAA,iBAAA,GAAA,CAAA;AACA,SANA,MAMA;AACA,UAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,UAAA,IAAA,CAAA,iBAAA,GAAA,GAAA;AACA,UAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,eAAA,YAAA,CAAA,IAAA;AACA;AACA,OArBA,MAqBA,IAAA,GAAA,IAAA,IAAA,CAAA,qBAAA,EAAA;AACA,QAAA,IAAA,CAAA,iBAAA,GAAA,GAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;AACA,KAtxCA;AAuxCA;AACA,IAAA,sBAxxCA,oCAwxCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,kBAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,kBAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,oBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,cAAA,CAAA,IAAA,CAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA,OAPA,MAOA,IAAA,GAAA,IAAA,IAAA,CAAA,kBAAA,EAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,oBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,cAAA,CAAA,IAAA,CAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;;AACA,WAAA,iBAAA,CAAA,IAAA;AACA,WAAA,gBAAA,CAAA,IAAA;AACA,KA1yCA;AA2yCA;AACA,IAAA,mBA5yCA,iCA4yCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,eAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,eAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,oBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA,OANA,MAMA,IAAA,GAAA,IAAA,IAAA,CAAA,eAAA,EAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,oBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;;AACA,WAAA,iBAAA,CAAA,IAAA;AACA,WAAA,gBAAA,CAAA,IAAA;AACA,KA5zCA;AA6zCA;AACA,IAAA,mBA9zCA,iCA8zCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,eAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,eAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA,OALA,MAKA,IAAA,GAAA,IAAA,IAAA,CAAA,eAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;;AACA,WAAA,iBAAA,CAAA,IAAA;AACA,WAAA,gBAAA,CAAA,IAAA;AACA,KA50CA;AA60CA;AACA,IAAA,cA90CA,4BA80CA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,UAAA;AACA,MAAA,IAAA,CAAA,EAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KAn1CA;AAo1CA,IAAA,gBAp1CA,4BAo1CA,IAp1CA,EAo1CA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;;AACA,UAAA,QAAA,OAAA,EAAA;AACA,YAAA,YAAA,GAAA,IAAA,CAAA,YAAA;AACA,YAAA,MAAA,GAAA,WAAA,CAAA,IAAA,CAAA,kBAAA,CAAA;AACA,YAAA,KAAA,WAAA,IAAA,CAAA,EACA,KAAA,WAAA,GAAA,WAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,OAAA,CAAA;;AACA,YAAA,MAAA,IAAA,CAAA,IAAA,CAAA,YAAA,GAAA,KAAA,WAAA,EAAA,OAAA,CAAA,CAAA,IAAA,MAAA,GAAA,CAAA,EAAA;AACA,cAAA,CAAA,CAAA,YAAA,GAAA,KAAA,WAAA,EAAA,OAAA,CAAA,CAAA,IAAA,MAAA,IAAA,MAAA,GAAA,GAAA,EACA,KAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,IAAA,EACA,WACA,IAAA,CAAA,WADA,GAEA,GAFA,GAGA,2BANA;AAOA,YAAA,QAAA,EAAA;AAPA,WAAA;AASA;AACA;AACA,KAx2CA;AAy2CA;AACA,IAAA,iBA12CA,6BA02CA,IA12CA,EA02CA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,QAAA,CAFA,CAEA;;AACA,UAAA,gBAAA,GAAA,IAAA,CAAA,gBAAA,CAHA,CAGA;;AACA,UAAA,UAAA,GAAA,IAAA,CAAA,UAAA,CAJA,CAIA;;AACA,UAAA,SAAA,GAAA,IAAA,CAAA,SAAA,CALA,CAKA;;AACA,UAAA,CAAA,YAAA,CAAA,QAAA,CAAA,IAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,QAAA,CAAA,QAAA,CAAA,EAAA;AACA,YAAA,SAAA,EAAA;AACA,cAAA,SAAA,IAAA,IAAA,IAAA,SAAA,GAAA,aAAA,EAAA;AACA;AACA,iBAAA,SAAA,CACA,yBAAA,SAAA,GAAA,YADA;AAGA,WANA,CAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA,KAz6CA;AA06CA,IAAA,SA16CA,qBA06CA,KA16CA,EA06CA;AACA,WAAA,UAAA,GAAA,IAAA,CADA,CACA;;AACA,UAAA,GAAA,GAAA,KAAA,UAAA,CAAA,KAAA,CAAA;;AACA,UAAA,GAAA,CAAA,SAAA,IAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IAAA,EAAA;AACA,YAAA,GAAA,GAAA;AACA,UAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IADA;AAEA,UAAA,SAAA,EAAA,GAAA,CAAA;AAFA,SAAA;AAIA,QAAA,iBAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,GAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,SAFA;AAGA;;AACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,MAAA,EAAA;AACA,eAAA,QAAA,GAAA,IAAA;AACA,SAFA,MAEA;AACA,eAAA,QAAA,GAAA,KAAA;AACA;;AAEA,aAAA,UAAA,GAAA,GAAA,CAPA,CAOA;;AACA,aAAA,oBAAA,GACA,GAAA,CAAA,gBAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,gBADA;AAEA,aAAA,oBAAA,GACA,GAAA,CAAA,gBAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,gBADA;AAEA,aAAA,mBAAA,GAAA,GAAA,CAAA,eAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,eAAA;AACA,aAAA,mBAAA,GAAA,GAAA,CAAA,eAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,eAAA;AACA,aAAA,mBAAA,GAAA,GAAA,CAAA,eAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,eAAA;AACA,aAAA,kBAAA,GAAA,GAAA,CAAA,cAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,cAAA;;AACA,YAAA,KAAA,OAAA,IAAA,IAAA,EAAA;AACA,eAAA,gBAAA,GAAA,GAAA,CAAA,YAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,YAAA;AACA,eAAA,gBAAA,GAAA,GAAA,CAAA,YAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,YAAA;AACA,eAAA,eAAA,GAAA,GAAA,CAAA,WAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,WAAA;AACA;AACA;AACA,KA58CA;AA68CA,IAAA,mBA78CA,+BA68CA,IA78CA,EA68CA;AACA,WAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,KAAA,SAAA,EAAA;AACA;AACA;AACA;AACA;AACA,aAAA,eAAA,GALA,CAMA;AACA,OAPA,MAOA,IAAA,IAAA,KAAA,KAAA,EAAA;AACA;AACA;AACA;AACA;AACA,aAAA,kBAAA,GALA,CAMA;AACA;AACA,KA99CA;AA+9CA;AACA,IAAA,kBAh+CA,gCAg+CA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,eAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,MAAA,iBAAA,CAAA,KAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA;AAKA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,CAAA,aAAA;AACA,WAFA,MAEA;AACA,YAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CACA,GAAA,CAAA,IAAA,CAAA,GADA,EAEA;AACA,aAHA,EAIA,OAAA,CAAA,UAAA,CAAA,OAJA;AAMA;AACA,SAXA,MAWA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,aAAA;AACA;AACA,OAvBA;AAwBA,KA5/CA;AA6/CA;AACA,IAAA,aA9/CA,2BA8/CA;AACA,UAAA,KAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;;AACA,UAAA,KAAA,IAAA,IAAA,IAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,cAAA;AACA,OAFA,MAEA;AACA,aAAA,eAAA,CAAA,KAAA;AACA;AACA,KArgDA;AAugDA;AACA,IAAA,gBAxgDA,8BAwgDA;AAAA;;AACA,UAAA,MAAA,GAAA,KAAA,UAAA;AACA,MAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AACA,MAAA,MAAA,CAAA,QAAA,GAAA,KAAA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,mCADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,UAAA,KAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;AACA,YAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,QAAA,OAAA,CAAA,eAAA,CAAA,KAAA;AACA,OAJA;AAKA,KAxhDA;AAyhDA;AACA,IAAA,eA1hDA,2BA0hDA,KA1hDA,EA0hDA;AACA,UAAA,CAAA,GAAA,EAAA;AACA,UAAA,CAAA,GAAA,CAAA;AACA,UAAA,IAAA,GAAA,KAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,SAAA,IAAA,CAAA;AAAA,OAAA,CAAA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,EAAA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,GAAA,CAAA;AACA,YAAA,UAAA,GAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,UAAA,CAAA,SAAA;AACA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,GAAA,GAAA,sBAAA,CAAA,IAAA,CAAA;;AACA,cAAA,GAAA,CAAA,MAAA,EAAA;AACA,gBAAA,QAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,SAAA,CAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,QAAA,CAAA,IAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,QAAA,CAAA,EAAA;AACA,YAAA,QAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,QAAA,CAAA,IAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA,EAAA;AACA,YAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,SAAA;AACA,YAAA,UAAA,CAAA,IAAA,CAAA,IAAA;AACA,YAAA,MAAA;AACA,WAVA,MAUA;AACA,YAAA,GAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,cAFA,GAGA,GAAA,CAAA,GAHA,GAIA,IALA;AAMA;;AAEA,cAAA,IAAA,CAAA,gBAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,SAFA,GAGA,IAAA,CAAA,aAHA,GAIA,SAJA,GAKA,IAAA,CAAA,YALA,GAMA,gBAPA;AAQA;;AAEA,cAAA,IAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,WAFA,GAGA,IAAA,CAAA,OAHA,GAIA,WAJA,GAKA,IAAA,CAAA,UALA,GAMA,eAPA;AAQA;;AACA,cAAA,IAAA,CAAA,SAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cACA,QAAA,OAAA,IACA,IAAA,CAAA,SAAA,GAAA,CADA,KAEA,IAAA,CAAA,YAAA,IAAA,IAAA,IAAA,IAAA,CAAA,YAAA,GAAA,CAFA,KAGA,IAAA,CAAA,SAJA,EAKA;AACA,YAAA,CAAA,GAAA,CAAA;AACA,YAAA,GAAA,IAAA,IAAA,CAAA,WAAA,GAAA,GAAA;AACA;AACA,SAzDA;;AA0DA,YAAA,CAAA,IAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,iBAAA,OAAA,CAAA,OAAA,CAAA;AACA,cAAA,KAAA,EAAA,IADA;AAEA,cAAA,IAAA,EAAA,IAFA;AAGA,cAAA,QAAA,EAAA;AAHA,aAAA;AAKA;;AACA,cAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,iBAAA,MAAA,GAAA,UAAA;AACA,iBAAA,OAAA,GAAA,UAAA;AACA,iBAAA,UAAA;AACA;AACA,SAbA,MAaA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,0BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,+BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CACA,cACA,GADA,GAEA,uCAHA;AAKA;;AACA,QAAA,IAAA,CAAA,UAAA,GAAA,CAAA;;AACA,YAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,GAAA;AACA;;AACA,YAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,IAAA,EAAA,IAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA;AAKA;AACA;AACA,KApoDA;AAqoDA;AACA,IAAA,eAtoDA,6BAsoDA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,CAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,cAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,SAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA,SAXA;;AAYA,YAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,EAAA,CAAA,EAAA,KAAA,UAAA,CAAA,OAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,0BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,+BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,2BAAA;AACA;AACA;AACA,KAnqDA;AAoqDA,IAAA,UApqDA,sBAoqDA,MApqDA,EAoqDA;AACA,WAAA,OAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA;AACA,UAAA,SAAA,EAAA,QADA;AAEA,UAAA,OAAA,EAAA,QAFA;AAGA,UAAA,iBAAA,EAAA,QAHA;AAIA,UAAA,gBAAA,EAAA,QAJA;AAKA,UAAA,iBAAA,EAAA,QALA;AAMA,UAAA,mBAAA,EAAA,QANA;AAOA,UAAA,gBAAA,EAAA,QAPA;AAQA,UAAA,WAAA,EAAA,QARA;AASA,UAAA,OAAA,EAAA,QATA;AAUA,UAAA,MAAA,EAAA;AAVA,SAAA;AAYA;AACA,KAprDA;AAqrDA,IAAA,aArrDA,yBAqrDA,IArrDA,EAqrDA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KAvrDA;AAwrDA,IAAA,OAxrDA,qBAwrDA;AACA,UAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,IAAA,CAAA,EAAA;AACA;AACA,YAAA,GAAA,GAAA,IAAA;AACA,QAAA,GAAA,CAAA,cAAA,GAAA,KAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,GAAA,CAAA,kBAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OAPA,MAOA;AACA,aAAA,cAAA,GAAA,IAAA;AACA;AACA,KAnsDA;AAosDA,IAAA,SApsDA,uBAosDA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,gBAAA;AACA,OAFA,MAEA;AACA,YAAA,YAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,MAAA,GAAA,IAAA,CAAA,MAAA;;AACA,cAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,KAAA;AACA,WAFA,MAEA;AACA,YAAA,YAAA,IAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA;AACA,SAPA;;AAQA,YAAA,CAAA,EAAA;AACA,UAAA,UAAA,CAAA,YAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,SADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;AAKA,cAAA,IAAA,CAAA,kBAAA;AACA;AACA,WATA;AAUA,SAXA,MAWA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,mBAAA;AACA;AACA;AACA,KAnuDA;AAouDA,IAAA,cApuDA,0BAouDA,IApuDA,EAouDA,GApuDA,EAouDA;AACA,UAAA,IAAA,GAAA,EAAA;AAAA,UACA,IAAA,GAAA,EADA;;AAEA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,SAAA,CAAA,aAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,IAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA,aAAA,CAAA,CAAA,EAAA,KAAA;AACA,QAAA,IAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA,aAAA,CAAA,CAAA,EAAA,GAAA;AACA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,GAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,SAAA,EAAA,IAJA;AAKA,QAAA,QAAA,EAAA;AALA,OAAA;AAOA,MAAA,KAAA,CAAA,qBAAA,CAAA,MAAA;AACA;AACA,KApvDA;AAqvDA,IAAA,SArvDA,qBAqvDA,IArvDA,EAqvDA;AAAA;;AACA,WAAA,eAAA;AACA,UAAA,MAAA,GAAA,KAAA,UAAA;;AAEA,UAAA,IAAA,KAAA,SAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,KAAA,QAAA;AACA,OAHA,MAGA,IAAA,IAAA,KAAA,KAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,KAAA,MAAA,CAAA,IAAA;AACA;;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,4BADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,WAAA,QAAA,GAAA,IAAA;AACA,MAAA,KAAA,CACA,IADA,CACA,GADA,EAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,QAAA,GAAA,KAAA;AACA,YAAA,OAAA,GAAA,GAAA;AACA,YAAA,IAAA,GAAA,IAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AACA,YAAA,QAAA,GAAA,aAAA,OAAA;;AACA,YAAA,cAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA,EAAA;AACA;AACA,cAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA,QAAA;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,UAAA,KAAA,CAAA,IAAA,GAAA,GAAA,CAAA,eAAA,CAAA,IAAA,CAAA;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,UAAA,KAAA,CAAA,KAAA;AACA,UAAA,GAAA,CAAA,eAAA,CAAA,KAAA,CAAA,IAAA,EARA,CAQA;;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,SAVA,MAUA;AACA;AACA,UAAA,SAAA,CAAA,UAAA,CAAA,IAAA,EAAA,QAAA;AACA;AACA,OArBA,EAsBA,KAtBA,CAsBA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OAxBA;AAyBA,KA/xDA;AAgyDA;AACA,IAAA,UAjyDA,wBAiyDA;AACA,UAAA,GAAA,GAAA,KAAA,WAAA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,cAAA,GAAA,IAAA,CAAA,cAAA;AACA,MAAA,IAAA,CAAA,OAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,cAAA,CAAA,IAAA,CAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KAxyDA;AAyyDA,IAAA,YAzyDA,0BAyyDA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,aAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAHA,CAGA;;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CAJA,CAKA;;AACA,WAAA,QAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,OAAA,EAAA,UAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,IAAA,KAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,CAAA;AACA;;AACA,QAAA,OAAA,CAAA,YAAA,CAAA,IAAA;AACA,OAPA;AAQA,UAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAdA,CAeA;;AACA,UAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,MAAA,GAAA,MAAA,GAAA,CAAA;AACA;;AACA,MAAA,IAAA,CAAA,MAAA,IAAA,aAAA,IAAA,CAAA,aAAA,GAAA,KAAA,GAAA,GAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,IAAA;AACA,KA/zDA;AAg0DA,IAAA,MAh0DA,oBAg0DA;AACA,WAAA,QAAA,CAAA,IAAA;AACA,KAl0DA;AAm0DA,IAAA,aAn0DA,yBAm0DA,IAn0DA,EAm0DA,KAn0DA,EAm0DA;AACA,UAAA,IAAA,GAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA;AACA,UAAA,GAAA,GAAA,IAAA,CAAA,aAAA;;AACA,UAAA,GAAA,EAAA;AACA,YAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,cAAA,GAAA,IAAA,IAAA,CAAA,aAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,GAAA;AAEA,YAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA,WAJA,MAIA;AACA,YAAA,IAAA,CAAA,QAAA;AACA;AACA,SARA,MAQA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,QAAA;AACA,UAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA,OAbA,MAaA;AACA,QAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA,KAt1DA;AAu1DA,IAAA,SAv1DA,uBAu1DA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,qBAAA;AAEA,MAAA,IAAA,CAAA,iBAAA,GAAA,GAAA;AACA,UAAA,CAAA,GAAA,KAAA,WAAA;;AACA,UAAA,CAAA,KAAA,IAAA,EAAA;AACA,aAAA,OAAA,GAAA,IAAA;AACA,OAFA,MAEA,IAAA,CAAA,KAAA,KAAA,EAAA;AACA,aAAA,SAAA,GAAA,IAAA;AACA,OAFA,MAEA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;;AAEA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAfA,CAgBA;;AACA,UAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,MAAA,GAAA,MAAA,GAAA,CAAA;AACA;;AACA,MAAA,IAAA,CAAA,MAAA,IAAA,WAAA,IAAA,CAAA,qBAAA,GAAA,KAAA,GAAA,GAAA,GAAA,IAAA;AAEA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,IAAA;AACA,KA/2DA;AAg3DA,IAAA,SAh3DA,uBAg3DA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,MAAA,IAAA,CAAA,WAAA,GAAA,IAAA;AACA,WAAA,OAAA,GAAA,KAAA;AAEA,WAAA,YAAA,CAAA,IAAA;AACA,KAt3DA;AAu3DA,IAAA,WAv3DA,yBAu3DA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,MAAA,IAAA,CAAA,WAAA,GAAA,KAAA;AACA,WAAA,SAAA,GAAA,KAAA;AAEA,WAAA,YAAA,CAAA,IAAA;AACA,KA73DA;AA83DA,IAAA,qBA93DA,iCA83DA,IA93DA,EA83DA,KA93DA,EA83DA;AACA,UAAA,IAAA,GAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA;AACA,UAAA,GAAA,GAAA,IAAA,CAAA,qBAAA;;AACA,UAAA,GAAA,EAAA;AACA,YAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,cAAA,GAAA,IAAA,IAAA,CAAA,qBAAA,EAAA;AACA,YAAA,IAAA,CAAA,iBAAA,GAAA,GAAA;AACA,YAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA,WAHA,MAGA;AACA,YAAA,IAAA,CAAA,QAAA;AACA;AACA,SAPA,MAOA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,QAAA;AACA,UAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA,OAZA,MAYA;AACA,QAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA,KAh5DA;AAi5DA,IAAA,QAj5DA,sBAi5DA;AACA,UAAA,KAAA,YAAA,KAAA,CAAA,EAAA;AACA,YAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,EAAA,qBAAA;AACA,aAAA,qBAAA,GAAA,IAAA;AACA,aAAA,UAAA,CAAA,KAAA,SAAA,EAAA,iBAAA,GAAA,IAAA;AAEA,aAAA,KAAA,CAAA,qBAAA,KAAA,SAAA,GAAA,KAAA,YAAA,EAAA,KAAA;AACA,OANA,MAMA,IAAA,KAAA,YAAA,KAAA,CAAA,EAAA;AACA,YAAA,KAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,EAAA,oBAAA;AACA,aAAA,oBAAA,GAAA,KAAA;AACA,aAAA,UAAA,CAAA,KAAA,SAAA,EAAA,gBAAA,GAAA,KAAA;AAEA,aAAA,KAAA,CAAA,sBAAA,KAAA,SAAA,GAAA,KAAA,YAAA,EAAA,KAAA;AACA;AACA,KA/5DA;AAg6DA,IAAA,eAh6DA,6BAg6DA;AACA,WAAA,UAAA;AACA,KAl6DA;AAm6DA,IAAA,eAn6DA,6BAm6DA;AACA,UAAA,KAAA,GAAA,KAAA,eAAA;AACA,WAAA,UAAA,CAAA,WAAA,GAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,KAt6DA;AAu6DA,IAAA,IAv6DA,kBAu6DA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,CAAA,UAAA,EAAA;AACA,QAAA,IAAA,CAAA,YAAA;AACA,OAFA,MAEA,IAAA,IAAA,CAAA,OAAA,EAAA;AACA,QAAA,IAAA,CAAA,SAAA;AACA,OAFA,MAEA,IAAA,IAAA,CAAA,OAAA,EAAA;AACA,QAAA,IAAA,CAAA,SAAA;AACA,OAFA,MAEA,IAAA,IAAA,CAAA,SAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA;AACA,OAFA,MAEA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,YAAA,OAAA,GAAA,IAAA,CAAA,YAAA;;AACA,YAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,UAAA,KAAA,GAAA,CAAA;AACA,UAAA,OAAA,GAAA,CAAA;AACA,UAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,UAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAA,UAAA,CAAA,KAAA,EAAA,SAAA;AACA,UAAA,UAAA,CAAA,YAAA;AACA,YAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,WAFA,EAEA,GAFA,CAAA;AAGA,SATA,MASA,IAAA,OAAA,KAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,CAAA,IAAA,EAAA,KAAA;AACA,SAFA,MAEA,IAAA,OAAA,KAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,qBAAA,CAAA,IAAA,EAAA,KAAA;AACA,SAFA,MAEA;AACA,UAAA,IAAA,CAAA,QAAA;AACA,UAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA;AACA,KAt8DA;AAu8DA,IAAA,iBAv8DA,6BAu8DA,IAv8DA,EAu8DA;AACA,WAAA,SAAA,CAAA,IAAA,CAAA,IAAA,GAAA,gCAAA;AACA,KAz8DA;AA08DA,IAAA,cA18DA,0BA08DA,KA18DA,EA08DA,IA18DA,EA08DA;AACA,WAAA,QAAA,CAAA,IAAA,CAAA;AACA,QAAA,OAAA,EAAA,IAAA,CAAA,IAAA,GAAA;AADA,OAAA;AAGA,KA98DA;AA+8DA,IAAA,mBA/8DA,iCA+8DA,CAAA,CA/8DA;AAg9DA,IAAA,aAh9DA,yBAg9DA,IAh9DA,EAg9DA;AACA,UAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,WAAA;AACA;AACA;;AACA,UAAA,CAAA,IAAA,EAAA;AACA,aAAA,SAAA,CAAA,YAAA;AACA;AACA;;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CATA,CASA;;AACA,UAAA,cAAA,GAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAVA,CAUA;;AACA,UAAA,UAAA,GAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA,QAAA,GAAA,CAAA,EAAA,cAAA,CAAA,CAXA,CAWA;;AACA,UAAA,SAAA,UAAA,IAAA,UAAA,UAAA,EAAA;AACA;AACA;;AACA,UAAA,KAAA,GAAA;AAAA,QAAA,OAAA,EAAA,SAAA,CAAA;AAAA,OAAA;AACA,UAAA,KAAA,GAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,MAAA,KAAA,CACA,OADA,CACA;AACA,QAAA,GAAA,EAAA,+BADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAA,EAAA,KAAA;AAHA,OADA,EAMA,IANA,CAMA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,YAAA,OAAA,EAAA,SAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA;AADA,WAAA;AAGA,SAJA,MAIA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,oBAAA;AACA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,MAAA,CAAA,GAAA,GAAA,IAAA;AACA,UAAA,IAAA,CAAA,cAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA,QAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,UAAA,IAAA,CAAA,kBAAA;AACA;AACA,OArBA;AAsBA,aAAA,KAAA;AACA,KA1/DA;AA2/DA,IAAA,gBA3/DA,4BA2/DA,IA3/DA,EA2/DA,IA3/DA,EA2/DA;AACA,WAAA,oBAAA,CAAA,IAAA;AACA,KA7/DA;AA8/DA,IAAA,oBA9/DA,gCA8/DA,IA9/DA,EA8/DA;AACA,UAAA,CAAA,IAAA,EAAA;AACA,YAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAAA;AACA,eAAA,SAAA,CAAA,UAAA;AACA;;AACA,YAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAAA;AACA,eAAA,SAAA,CAAA,SAAA;AACA;;AACA,aAAA,KAAA,CAAA,WAAA,CAAA,KAAA,CAAA,MAAA,GAAA;AACA,UAAA,MAAA,EAAA,KAAA,UAAA,CAAA,OADA;AAEA,UAAA,MAAA,EAAA,KAAA,UAAA,CAAA;AAFA,SAAA,CAPA,CAUA;;AACA,aAAA,KAAA,CAAA,WAAA,CAAA,MAAA,GAXA,CAWA;AACA,OAZA,MAYA;AACA,aAAA,QAAA,GAAA,IAAA,CAAA,IAAA;AACA,aAAA,UAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA;AACA;AACA,KA/gEA;AAghEA,IAAA,QAhhEA,oBAghEA,GAhhEA,EAghEA;AACA,UAAA,KAAA,GAAA,GAAA,CAAA,MAAA,GAAA,GAAA,CAAA,EAAA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,EAAA;;AACA,UAAA,KAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,IAAA,KAAA;AACA;;AACA,aAAA,KAAA;AACA,KAvhEA;AAwhEA,IAAA,UAxhEA,sBAwhEA,GAxhEA,EAwhEA;AACA,WAAA,KAAA,CAAA,eAAA,CAAA,MAAA,CAAA,GAAA,CAAA,IAAA,GAAA,EAAA;AACA;AA1hEA,GA7bA;AAy9EA,EAAA,OAz9EA,qBAy9EA;AAAA;;AACA,SAAA,YAAA,GADA,CACA;;AAEA,SAAA,UAAA,CAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,SAAA,SAAA,GAAA,KAAA,CAAA,iBAAA,CAAA;AACA,SAAA,iBAAA,GAAA,KAAA,CAAA,kBAAA,CAAA;AACA,IAAA,OAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,SAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,OAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;AACA,QAAA,OAAA,CAAA,eAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,CAFA,CAGA;;AACA,QAAA,OAAA,CAAA,UAAA,CAAA,OAAA,GAAA,OAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EAAA;AACA,QAAA,cAAA,CAAA,OAAA,CAAA,UAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA;AACA,UAAA,OAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA;AACA,UAAA,OAAA,CAAA,UAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,EAAA;;AAEA,UAAA,OAAA,CAAA,kBAAA;AACA,SANA;AAOA;;AACA,MAAA,OAAA,CAAA,SAAA,CAAA,OAAA,GAAA,OAAA,CAAA,SAAA,CAAA,WAAA,CACA,MADA,CACA,OAAA,CAAA,SAAA,CAAA,QADA,EAEA,MAFA,CAEA,OAAA,CAAA,SAAA,CAAA,UAFA,CAAA,CAdA,CAiBA;;AACA,MAAA,OAAA,CAAA,SAAA,CAAA,OAAA,GAAA,OAAA,CAAA,SAAA,CAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,SAAA,CAAA,UAAA,CAAA;AACA,KAnBA;AAqBA,IAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,MAAA,OAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,IAAA;AACA,KAHA;AAIA,GAx/EA;AAy/EA,EAAA,QAAA,oBACA,QAAA,CAAA;AACA,IAAA,OAAA,EAAA,iBAAA,KAAA;AAAA,aAAA,KAAA,CAAA,IAAA,CAAA,OAAA;AAAA;AADA,GAAA,CADA;AAz/EA,CAAA", "sourcesContent": ["<!--自有电费台账-->\r\n\r\n<template>\r\n  <div class=\"page-class page-card\">\r\n    <div class=\"query-box\">\r\n      <Row :class=\"filterColl ? 'header-bar-show' : 'header-bar-hide'\">\r\n        <Form ref=\"accountForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.accountno\"\r\n                  @on-change=\"accountnoChange\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"上期止度:\"\r\n                prop=\"prevtotalreadings\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <InputNumber\r\n                  v-model=\"accountObj.prevtotalreadings\"\r\n                  placeholder=\"请输入上期止度\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"局站名称:\" prop=\"stationName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.stationName\"\r\n                  placeholder=\"请输入局站名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"项目名称:\" prop=\"projectname\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.projectname\"\r\n                  placeholder=\"请输入项目名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\" v-if=\"'sc' == accountObj.version\">\r\n              <FormItem\r\n                label=\"电表/协议编码:\"\r\n                prop=\"ammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.ammetercode\"\r\n                  placeholder=\"请输入电表户号/协议编码\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"供电局电表编号:\"\r\n                prop=\"supplybureauammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.supplybureauammetercode\"\r\n                  placeholder=\"请输入供电局电表编号\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"用电类型:\" prop=\"classifications\" class=\"form-line-height\">\r\n                <Cascader\r\n                  clearable\r\n                  :data=\"classificationData\"\r\n                  v-model=\"classifications\"\r\n                  :style=\"formItemWidth\"\r\n                ></Cascader>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"是否退回：\" prop=\"status\" class=\"form-line-height\">\r\n                <Select clearable v-model=\"accountObj.status\" :style=\"formItemWidth\">\r\n                  <Option value=\"\">请选择</Option>\r\n                  <Option value=\"5\">是</Option>\r\n                  <Option value=\"1\">否</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize > 1\">\r\n              <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.company\"\r\n                  @on-change=\"selectChange(accountObj.company)\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option v-for=\"item in CompanyList\" :value=\"item.id\" :key=\"item.id\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize == 1 && resCenterListSize > 1\">\r\n              <FormItem label=\"所属部门：\" prop=\"country\" class=\"form-line-height\">\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option v-for=\"item in resCenterList\" :value=\"item.id\" :key=\"item.id\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\" v-if=\"companyListSize > 1\">\r\n              <FormItem label=\"所属部门：\" prop=\"country\" class=\"form-line-height\">\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option v-for=\"item in resCenterList\" :value=\"item.id\" :key=\"item.id\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize == 1 && resCenterListSize == 1\">\r\n              <FormItem label=\"台账填写人：\" prop=\"country\" class=\"form-line-height\">\r\n                <Input\r\n                  :style=\"formItemWidth\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"userName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  clearable\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize > 1 || resCenterListSize > 1\">\r\n              <FormItem label=\"台账填写人：\" prop=\"country\" class=\"form-line-height\">\r\n                <Input\r\n                  :style=\"formItemWidth\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"userName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  clearable\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"对外结算类型：\"\r\n                prop=\"directsupplyflag\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Select\r\n                  clearable\r\n                  v-model=\"accountObj.directsupplyflag\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option\r\n                    v-for=\"item in directsupplyflags\"\r\n                    :value=\"item.typeCode\"\r\n                    :key=\"item.typeCode\"\r\n                    >{{ item.typeName }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <div align=\"right\">\r\n            <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n            <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n            <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n              <Button type=\"default\" style=\"margin-left: 5px\"\r\n                >导出\r\n                <Icon type=\"ios-arrow-down\"></Icon>\r\n              </Button>\r\n              <DropdownMenu slot=\"list\">\r\n                <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n          </div>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon\r\n          :type=\"filterColl ? 'md-arrow-dropup' : 'md-arrow-dropdown'\"\r\n          size=\"20\"\r\n          @click=\"filterColl = !filterColl\"\r\n          :color=\"filterColl ? '#000' : '#1ab394'\"\r\n        ></icon>\r\n      </div>\r\n      <div>\r\n        <Modal\r\n          v-model=\"meterModal\"\r\n          title=\"峰平谷信息\"\r\n          width=\"50%\"\r\n          @on-ok=\"setFPG(currentRow)\"\r\n        >\r\n          <Form ref=\"meterForm\" :model=\"currentRow\" :label-width=\"80\" inline>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">本期起度</Col>\r\n              <Col span=\"8\" align=\"center\">本期止度</Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">加减</Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"峰:\" prop=\"prevhighreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editprevhighreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"addFremark(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"\" prop=\"curhighreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editcurhighreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"setcurhighreadings(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">\r\n                <FormItem label=\"\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"edithighreadings\"\r\n                    :readonly=\"readonly\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"平:\" prop=\"prevflatreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editprevflatreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"addPremark(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"\" prop=\"curflatreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editcurflatreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"setcurflatreadings(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">\r\n                <FormItem label=\"\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editflatreadings\"\r\n                    :readonly=\"readonly\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"谷:\" prop=\"prevlowreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editprevlowreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"addGremark(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"\" prop=\"curlowreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editcurlowreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"setcurlowreadings(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">\r\n                <FormItem label=\"\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editlowreadings\"\r\n                    :readonly=\"readonly\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n            </Row>\r\n          </Form>\r\n        </Modal>\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"startModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"startModalOk\"\r\n          @on-cancel=\"cancel\"\r\n          ><p>\r\n            是否确定更改本期起始日期？保存后从当前修改的起始日期前无法填入台帐！(可删除保存解除限制)\r\n          </p></Modal\r\n        >\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"qdModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"qdModalOk\"\r\n          @on-cancel=\"cancel\"\r\n          ><p>是否确定更改本期起度?</p></Modal\r\n        >\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"fbModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"fbModalOk\"\r\n          @on-cancel=\"qdcancel\"\r\n          ><p>是否翻表?</p></Modal\r\n        >\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"qxfbModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"qxfbModalOk\"\r\n          @on-cancel=\"qdcancel\"\r\n          ><p>是否取消翻表?</p></Modal\r\n        >\r\n      </div>\r\n    </div>\r\n    <div class=\"cl-table\">\r\n      <Row class=\"button-bar\">\r\n        <div class=\"table-button\">\r\n          <Button type=\"success\" @click=\"preserve()\">保存</Button>\r\n          <Button type=\"error\" @click=\"remove()\">删除</Button>\r\n          <Button type=\"error\" @click=\"deleteAll()\">一键删除</Button>\r\n          <Upload\r\n            style=\"float: right\"\r\n            :on-format-error=\"handleFormatError\"\r\n            :before-upload=\"onExcelUpload\"\r\n            :on-progress=\"handleProgress\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :max-size=\"10240\"\r\n            action=\"_blank\"\r\n            accept=\".csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\r\n            :format=\"['xls', 'xlsx']\"\r\n          >\r\n            <Button icon=\"ios-cloud-upload\">导入Excel</Button>\r\n          </Upload>\r\n          <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n            <Button type=\"info\" style=\"margin-left: 5px\"\r\n              >加入归集单\r\n              <Icon type=\"ios-arrow-down\"></Icon>\r\n            </Button>\r\n            <DropdownMenu slot=\"list\">\r\n              <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n              <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n          <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n        </div>\r\n      </Row>\r\n      <Table\r\n        ref=\"accountTable\"\r\n        border\r\n        :columns=\"accountTb.columns\"\r\n        :data=\"insideData\"\r\n        class=\"mytable\"\r\n        :loading=\"accountTb.loading\"\r\n        :height=\"tableHeight\"\r\n      >\r\n        <div></div>\r\n        <!--项目名称-->\r\n        <template slot-scope=\"{ row }\" slot=\"projectname\">\r\n          <div v-if=\"row.status === 5\">\r\n            <span>{{ row.projectname }}</span\r\n            ><span style=\"color: red\">[退回]</span>\r\n          </div>\r\n          <span v-else>{{ row.projectname }}</span>\r\n        </template>\r\n        <!--起始时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"startdate\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'startdate' + index + 1\"\r\n              class=\"myinput\"\r\n              type=\"text\"\r\n              @on-blur=\"validate\"\r\n              v-model=\"editStartDate\"\r\n              v-if=\"editIndex === index && columnsIndex === 1\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].startdate\"\r\n              @click=\"selectCall(row, index, 1, 'startdate')\"\r\n              v-else\r\n              >{{ row.startdate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.startdate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--截止时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"enddate\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'enddate' + index + 2\"\r\n              type=\"text\"\r\n              v-model=\"editEndDate\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 2\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].enddate\"\r\n              @click=\"selectCall(row, index, 2, 'enddate')\"\r\n              v-else\r\n              >{{ row.enddate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.enddate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--起度-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"prevtotalreadings\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'prevtotalreadings' + index + 3\"\r\n              type=\"text\"\r\n              v-model=\"editPrevtotalreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 3 && !row.isFPG && !row.isWB\"\r\n            />\r\n            <span @click=\"openModal(index)\" v-else-if=\"row.isFPG\">峰平谷</span>\r\n            <span\r\n              :class=\"myStyle[index].prevtotalreadings\"\r\n              @click=\"selectCall(row, index, 3, 'prevtotalreadings')\"\r\n              v-else\r\n              >{{ row.prevtotalreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span @click=\"openModal(index)\" v-if=\"row.isFPG\">峰平谷</span>\r\n            <span v-else>{{ row.prevtotalreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--止度-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"curtotalreadings\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'curtotalreadings' + index + 4\"\r\n              type=\"text\"\r\n              v-model=\"editcurtotalreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 4 && !row.isFPG && !row.isWB\"\r\n            />\r\n            <span @click=\"openModal(index)\" v-else-if=\"row.isFPG\">峰平谷</span>\r\n            <span\r\n              :class=\"myStyle[index].curtotalreadings\"\r\n              style=\"overflow-wrap: break-word\"\r\n              @click=\"selectCall(row, index, 4, 'curtotalreadings')\"\r\n              v-else\r\n              >{{ row.curtotalreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span @click=\"openModal(index)\" v-if=\"row.isFPG\">峰平谷</span>\r\n            <span v-else>{{ row.curtotalreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--电损-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"transformerullage\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'transformerullage' + index + 5\"\r\n              type=\"text\"\r\n              v-model=\"edittransformerullage\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 5 && !row.isWB\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].transformerullage\"\r\n              @click=\"selectCall(row, index, 5, 'transformerullage')\"\r\n              v-else\r\n              >{{ row.transformerullage }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.transformerullage }}</span>\r\n          </div>\r\n        </template>\r\n        <template slot-scope=\"{ row }\" slot=\"curusedreadings\">\r\n          <Tooltip\r\n            v-if=\"row.total == null\"\r\n            placement=\"top\"\r\n            max-width=\"200\"\r\n            :content=\"'上期电量:' + row.curusedreadingsold\"\r\n            :disabled=\"\r\n              row.ammeteruse == '1' &&\r\n              row.curusedreadingsold &&\r\n              (row.curusedreadings - row.curusedreadingsold) / row.curusedreadings > 0.2\r\n                ? false\r\n                : true\r\n            \"\r\n          >\r\n            <span\r\n              :style=\"\r\n                row.ammeteruse == '1' &&\r\n                row.curusedreadingsold &&\r\n                (row.curusedreadings - row.curusedreadingsold) / row.curusedreadings > 0.2\r\n                  ? { color: 'orange', 'font-size': '14px' }\r\n                  : {}\r\n              \"\r\n              >{{ row.curusedreadings }}</span\r\n            >\r\n          </Tooltip>\r\n          <span v-else>{{ row.curusedreadings }}</span>\r\n        </template>\r\n        <template slot-scope=\"{ row }\" slot=\"unitpirce\">\r\n          <Tooltip\r\n            v-if=\"row.total == null\"\r\n            placement=\"top\"\r\n            max-width=\"200\"\r\n            :content=\"'上期单价:' + row.unitpirceold\"\r\n            :disabled=\"\r\n              row.ammeteruse == '1' &&\r\n              row.unitpirceold &&\r\n              (row.unitpirce - row.unitpirceold) / row.unitpirce > 0.2\r\n                ? false\r\n                : true\r\n            \"\r\n          >\r\n            <span\r\n              :style=\"\r\n                row.ammeteruse == '1' &&\r\n                row.unitpirceold &&\r\n                (row.unitpirce - row.unitpirceold) / row.unitpirce > 0.2\r\n                  ? { color: 'orange', 'font-size': '14px' }\r\n                  : {}\r\n              \"\r\n              >{{ row.unitpirce }}</span\r\n            >\r\n          </Tooltip>\r\n          <span v-else>{{ row.unitpirce }}</span>\r\n        </template>\r\n        <!--普票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputticketmoney' + index + 6\"\r\n              type=\"text\"\r\n              v-model=\"editticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 6\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputticketmoney\"\r\n              @click=\"selectCall(row, index, 6, 'inputticketmoney')\"\r\n              v-else\r\n              >{{ row.inputticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--专票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputtaxticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputtaxticketmoney' + index + 7\"\r\n              type=\"text\"\r\n              v-model=\"edittaxticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 7\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputtaxticketmoney\"\r\n              @click=\"selectCall(row, index, 7, 'inputtaxticketmoney')\"\r\n              v-else\r\n              >{{ row.inputtaxticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputtaxticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--专票税率-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"taxrate\">\r\n          <div v-if=\"row.total == null\">\r\n            <Select\r\n              :ref=\"'taxrate' + index + 8\"\r\n              type=\"text\"\r\n              v-model=\"edittaxrate\"\r\n              @on-change=\"settaxrate\"\r\n              v-if=\"editIndex === index && columnsIndex === 8\"\r\n              transfer=\"true\"\r\n            >\r\n              <Option selected value=\"13\">13</Option>\r\n              <Option value=\"1\">1</Option>\r\n              <Option value=\"3\">3</Option>\r\n              <Option value=\"6\">6</Option>\r\n              <Option value=\"16\">16</Option>\r\n              <Option value=\"17\">17</Option>\r\n            </Select>\r\n            <span\r\n              :class=\"myStyle[index].taxrate\"\r\n              @click=\"selectCall(row, index, 8, 'taxrate')\"\r\n              v-else\r\n              >{{ row.taxrate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.taxrate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--其他-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"ullagemoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'ullagemoney' + index + 9\"\r\n              type=\"text\"\r\n              v-model=\"editullagemoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 9\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].ullagemoney\"\r\n              @click=\"selectCall(row, index, 9, 'ullagemoney')\"\r\n              v-else\r\n              >{{ row.ullagemoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.ullagemoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--备注-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              v-model=\"editremark\"\r\n              :ref=\"'remark' + index + 10\"\r\n              type=\"text\"\r\n              @on-blur=\"validateRemark\"\r\n              v-if=\"editIndex === index && columnsIndex === 10\"\r\n            />\r\n            <Tooltip placement=\"bottom\" max-width=\"200\" v-else>\r\n              <span\r\n                :class=\"myStyle[index].remark\"\r\n                style=\"width: 60px\"\r\n                @click=\"selectCall(row, index, 10, 'remark')\"\r\n                >{{ ellipsis(row) }}</span\r\n              >\r\n              <div slot=\"content\">\r\n                {{ row.remark + row.bz }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n          <div v-else>\r\n            <Tooltip placement=\"bottom\" max-width=\"200\">\r\n              <span>{{ ellipsis(row) }}</span>\r\n              <div slot=\"content\">\r\n                {{ row.remark + row.bz }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n        </template>\r\n      </Table>\r\n      <div class=\"table-page\">\r\n        <Page\r\n          size=\"small\"\r\n          :total=\"pageTotal\"\r\n          :current=\"pageNum\"\r\n          :page-size=\"pageSize\"\r\n          show-elevator\r\n          show-sizer\r\n          show-total\r\n          placement=\"top\"\r\n          @on-change=\"handlePage\"\r\n          @on-page-size-change=\"handlePageSize\"\r\n        ></Page>\r\n      </div>\r\n      <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n    </div>\r\n    <add-bill-per\r\n      ref=\"addBillPer\"\r\n      v-on:refreshList=\"refresh\"\r\n      @buttonload2=\"buttonload2\"\r\n      @isButtonload=\"isButtonload\"\r\n    ></add-bill-per>\r\n    <query-people-modal\r\n      ref=\"queryPeople\"\r\n      v-on:getDataFromModal=\"getDataFromModal\"\r\n    ></query-people-modal>\r\n    <upload-file-modal ref=\"uploadFileModal\"></upload-file-modal>\r\n\r\n    <!--    稽核modal-->\r\n    <Modal\r\n      v-model=\"showCheckModel\"\r\n      width=\"80%\"\r\n      title=\"稽核结果及反馈\"\r\n      :mask-closable=\"false\"\r\n    >\r\n      <check-result-and-response ref=\"chekResultAndResponse\"></check-result-and-response>\r\n    </Modal>\r\n    <Modal v-model=\"showJhModel\" width=\"80%\" title=\"稽核结果\" :mask-closable=\"false\">\r\n      <check-result ref=\"checkResult\"></check-result>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button type=\"primary\" @click=\"nextCheck\">已查阅</Button>\r\n        <Button type=\"text\" @click=\"checkCancel\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"showAlarmModel\"\r\n      width=\"80%\"\r\n      title=\"台账预警稽核\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n      class=\"yjjh\"\r\n    >\r\n      <alarm-check\r\n        :ymmc=\"'自有电费台账'\"\r\n        ref=\"showAlarmModel\"\r\n        @submitChange=\"submitChange\"\r\n        @save=\"save\"\r\n        @close=\"alarmClose\"\r\n      ></alarm-check>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button size=\"large\" @click=\"alarmClose\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { mapState } from \"vuex\";\r\nimport { getAuditResult, getAuditResultNew, getAuditResultNew_QXM } from \"@/api/account\";\r\nimport {\r\n  addSelfPowerAccount,\r\n  temporaryStorage,\r\n  getElectrQuota,\r\n  editOwn,\r\n  removeOwn,\r\n  selectByPcid,\r\n  getUser,\r\n  getDepartments,\r\n  selectByAmmeterId,\r\n  accountTotal,\r\n  selectCompletedMoney,\r\n  selectIdsByParams,\r\n  removeAll,\r\n} from \"@/api/account\";\r\nimport { getClassification, powerresult } from \"@/api/basedata/ammeter.js\";\r\nimport { againJoin } from \"@/api/accountBillPer\";\r\nimport { validContractList } from \"@/api/contract\";\r\nimport checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\nimport checkResult from \"@/view/account/check/checkResult\";\r\nimport alarmCheck from \"@/view/account/check/alarmCheck\";\r\nimport {\r\n  getDates,\r\n  cutDate_yyyymmdd,\r\n  testNumber,\r\n  GetDateDiff,\r\n  stringToDate,\r\n  getCurrentDate,\r\n} from \"@/view/account/powerAccountHelper\";\r\nimport {\r\n  _verify_StartDate,\r\n  judgeNumber,\r\n  _verify_EndDate,\r\n  _verify_PrevTotalReadings,\r\n  _verify_CurTotalReadings,\r\n  other_no_ammeteror_protocol,\r\n  self_no_ammeteror_protocol,\r\n  HFL_ammeteror,\r\n  judging_editability,\r\n  judging_editability1,\r\n  _verify_Money,\r\n  _calculateUsedReadings,\r\n  _calculateUsedReadingsForType_2,\r\n  _calculateTotalReadings,\r\n  _calculateUnitPriceByUsedMoney,\r\n  _calculateAccountMoney,\r\n  _calculateQuotereadingsratio,\r\n  requiredFieldValidator,\r\n  countTaxamount,\r\n  calculateActualMoney,\r\n  judge_negate,\r\n  judge_recovery,\r\n  judge_yb,\r\n  unitpirceMin,\r\n  unitpirceMax,\r\n  unitpirceMax1,\r\n} from \"@/view/account/PowerAccountController\";\r\nimport { widthstyle } from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nimport QueryPeopleModal from \"@/view/account/queryPeopleModal\";\r\nimport UploadFileModal from \"@/view/account/uploadFileModal\";\r\nimport AddBillPer from \"@/view/account/addBillPreModal\";\r\nimport excel from \"@/libs/excel\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport axios from \"@/libs/api.request\";\r\nimport indexData from \"@/config/index\";\r\n\r\nimport permissionMixin from \"@/mixins/permission\";\r\nimport pageFun from \"@/mixins/pageFun\";\r\n\r\nlet dates = getDates();\r\n\r\nexport default {\r\n  mixins: [permissionMixin, pageFun],\r\n\r\n  components: {\r\n    alarmCheck,\r\n    checkResult,\r\n    checkResultAndResponse,\r\n    UploadFileModal,\r\n    QueryPeopleModal,\r\n    AddBillPer,\r\n  },\r\n  data() {\r\n    let renderCategory = (h, params) => {\r\n      var categoryname = \"\";\r\n      for (let item of this.categorys) {\r\n        if (item.typeCode == params.row.category) {\r\n          categoryname = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", categoryname);\r\n    };\r\n    let renderDirectsupplyflag = (h, params) => {\r\n      var directsupplyflag = \"\";\r\n      for (let item of this.directsupplyflags) {\r\n        if (item.typeCode == params.row.directsupplyflag) {\r\n          directsupplyflag = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", directsupplyflag);\r\n    };\r\n    let photo = (h, { row, index }) => {\r\n      let that = this;\r\n      let str = \"\";\r\n      if (row.projectname != \"小计\" && row.projectname != \"合计\") {\r\n        str = \"上传\";\r\n      }\r\n      return h(\"div\", [\r\n        h(\r\n          \"u\",\r\n          {\r\n            on: {\r\n              click() {\r\n                //打开弹出框\r\n                if (row.projectname != \"小计\" && row.projectname != \"合计\") {\r\n                  that.uploadFile(row);\r\n                }\r\n              },\r\n            },\r\n          },\r\n          str\r\n        ),\r\n      ]);\r\n    };\r\n\r\n    return {\r\n      isT: true,\r\n      number2: 0,\r\n      name: \"\",\r\n      isQuery: true,\r\n      number: 0,\r\n      auditResultList: [],\r\n      submit: [],\r\n      submit2: [],\r\n      ammeterids: [],\r\n      showCheckModel: false,\r\n      showAlarmModel: false,\r\n      showJhModel: false,\r\n      version: indexData.version,\r\n      valiprice: true,\r\n      formatArray: [\"\"],\r\n      formItemWidth: widthstyle,\r\n      tableName: 1,\r\n      dateList: dates,\r\n      categorys: [], //描述类型\r\n      spinShow: false, //导入数据遮罩\r\n      filterColl: true, //搜索面板展开\r\n      startModal: false, //起始时间修改提示\r\n      qdModal: false, //起度修改提示\r\n      fbModal: false, //翻表提示\r\n      qxfbModal: false, //取消翻表提示\r\n      readonly: false, //封平谷只读\r\n      editIndex: -1, //当前编辑行\r\n      columnsIndex: -1, //当前编辑列\r\n      classificationData: [], //用电类型树\r\n      classifications: [], //选择的用电类型树\r\n      directsupplyflags: [],\r\n      editStartDate: \"\",\r\n      editEndDate: \"\",\r\n      editPrevtotalreadings: \"\",\r\n      editcurtotalreadings: \"\",\r\n      edittransformerullage: \"\",\r\n      edittaxticketmoney: \"\",\r\n      editticketmoney: \"\",\r\n      editullagemoney: \"\",\r\n      editpercent: \"\",\r\n      edittaxrate: \"\",\r\n      editremark: \"\",\r\n      editprevhighreadings: 0,\r\n      editprevflatreadings: 0,\r\n      editprevlowreadings: 0,\r\n      editcurhighreadings: 0,\r\n      editcurflatreadings: 0,\r\n      editcurlowreadings: 0,\r\n      edithighreadings: 0,\r\n      editflatreadings: 0,\r\n      editlowreadings: 0,\r\n      pabriid: \"\",\r\n      resCenterList: [],\r\n      CompanyList: [],\r\n      companyListSize: \"\",\r\n      resCenterListSize: \"\",\r\n      insideData: [], //数据\r\n      myStyle: [], //样式\r\n      pageTotal: 0,\r\n      pageNum: 1,\r\n      pageSize: 10, //当前页\r\n      currentRow: {},\r\n      meterModal: false,\r\n      ifMaxdegree: null,\r\n      againJoinIds: \"\",\r\n      export: {\r\n        run: false, //是否正在执行导出\r\n        data: \"\", //导出数据\r\n        totalPage: 0, //一共多少页\r\n        currentPage: 0, //当前多少页\r\n        percent: 0,\r\n        size: ********,\r\n      },\r\n      Accountqur: { ammeterid: \"\", startdate: \"\", enddate: \"\" },\r\n      accountObj: {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        stationName: \"\", //局站名称\r\n        projectname: \"\", //项目名称\r\n        prevtotalreadings: null, //上期止度\r\n        ammetercode: \"\", //电表户号/协议编码\r\n        status: \"\", //是否退回\r\n        company: \"\",\r\n        country: \"\",\r\n        electrotype: \"\", //用电类型\r\n        accountType: \"1\", //台账类型\r\n        userId: \"\",\r\n        version: \"\",\r\n        supplybureauammetercode: \"\",\r\n        directsupplyflag: \"\",\r\n        ammeterid: \"\",\r\n        startdate: \"\",\r\n        enddate: \"\",\r\n      },\r\n\r\n      userName: \"\", //查询选择人员名称\r\n      avemoney: 0,\r\n      nowdatediff: 0,\r\n      accountTb: {\r\n        loading: false,\r\n        columns: [],\r\n        headColumn: [\r\n          { type: \"selection\", width: 40, align: \"center\", fixed: \"left\" },\r\n          {\r\n            title: \"项目名称\",\r\n            slot: \"projectname\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n        ],\r\n        headColumn2: [\r\n          { type: \"selection\", width: 40, align: \"center\", fixed: \"left\" },\r\n          {\r\n            title: \"稽核结果及反馈\",\r\n            fixed: \"left\",\r\n            key: \"action\",\r\n            align: \"center\",\r\n            render: (h, params) => {\r\n              var that = this;\r\n              return h(\r\n                \"Button\",\r\n                {\r\n                  props: {\r\n                    type: \"primary\",\r\n                    size: \"small\",\r\n                  },\r\n                  style: {\r\n                    \"font-size\": \"10px\",\r\n                  },\r\n                  on: {\r\n                    click() {\r\n                      that.$refs.chekResultAndResponse.pcid = params.row.pcid;\r\n                      that.showCheckModel = true;\r\n                    },\r\n                  },\r\n                },\r\n                \"稽核结果及反馈\"\r\n              );\r\n            },\r\n            minWidth: 100,\r\n            maxWidth: 150,\r\n          },\r\n          {\r\n            title: \"项目名称\",\r\n            slot: \"projectname\",\r\n            align: \"center\",\r\n            width: 90,\r\n            fixed: \"left\",\r\n          },\r\n        ],\r\n        tailColumn: [\r\n          {\r\n            title: \"期号\",\r\n            key: \"accountno\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"用电类型\",\r\n            key: \"electrotypename\",\r\n            align: \"center\",\r\n            width: 94,\r\n          },\r\n          {\r\n            title: \"实际普票含税金额(元)\",\r\n            key: \"ticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n          },\r\n          {\r\n            title: \"实际专票含税金额(元)\",\r\n            key: \"taxticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n          },\r\n          {\r\n            title: \"总电量(度)\",\r\n            key: \"totalusedreadings\",\r\n            align: \"center\",\r\n            width: 60,\r\n          },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 160 },\r\n          {\r\n            title: \"类型描述\",\r\n            key: \"categoryname\",\r\n            align: \"center\",\r\n            width: 94,\r\n            render: renderCategory,\r\n          },\r\n          { title: \"倍率\", key: \"magnification\", align: \"center\", width: 60 },\r\n          { title: \"定额\", key: \"quotareadings\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"浮动比（%）\",\r\n            key: \"quotereadingsratio\",\r\n            align: \"center\",\r\n            width: 100,\r\n            sortable: true,\r\n            sortMethod: (a, b, type) => {\r\n              if (type === \"desc\") {\r\n                return parseInt(a) < parseInt(b) ? 1 : -1;\r\n              } else {\r\n                return parseInt(a) > parseInt(b) ? 1 : -1;\r\n              }\r\n            },\r\n          },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            align: \"center\",\r\n            fixed: \"left\",\r\n            width: 75,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            align: \"center\",\r\n            width: 75,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"本期起度\",\r\n            slot: \"prevtotalreadings\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"本期止度\",\r\n            slot: \"curtotalreadings\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"电损(度)\",\r\n            slot: \"transformerullage\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"用电量(度)\",\r\n            slot: \"curusedreadings\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"电价(元)\",\r\n            slot: \"unitpirce\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"普票含税金额(元)\",\r\n            slot: \"inputticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"专票含税金额(元)\",\r\n            slot: \"inputtaxticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"专票税率（%）\",\r\n            slot: \"taxrate\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"专票税额\",\r\n            key: \"taxamount\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"其他(元)\",\r\n            slot: \"ullagemoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"实缴费用(元)含税\",\r\n            key: \"accountmoney\",\r\n            align: \"center\",\r\n            width: 65,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"备注\",\r\n            slot: \"remark\",\r\n            align: \"center\",\r\n            width: 100,\r\n            fixed: \"left\",\r\n          },\r\n          { title: \"分割比例(%)\", align: \"center\", key: \"percent\", width: 80 },\r\n          {\r\n            title: \"对外结算类型\",\r\n            align: \"center\",\r\n            key: \"directsupplyflag\",\r\n            width: 80,\r\n            render: renderDirectsupplyflag,\r\n          },\r\n        ],\r\n        fileColumn: [\r\n          {\r\n            title: \"附件\",\r\n            slot: \"file\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n            render: photo,\r\n          },\r\n        ],\r\n        lnColumn: [\r\n          {\r\n            title: \"供电局电表编号\",\r\n            key: \"supplybureauammetercode\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n        ],\r\n        scColumn: [\r\n          {\r\n            title: \"电表户号/协议编码\",\r\n            key: \"ammetercode\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"供电局电表编号\",\r\n            key: \"supplybureauammetercode\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n        ],\r\n\r\n        data: [],\r\n        exportcolumns: [\r\n          { title: \"错误信息\", key: \"error\" },\r\n          { title: \"注意信息\", key: \"careful\" },\r\n          { title: \"项目名称\", key: \"projectname\" },\r\n          { title: \"期号\", key: \"accountno\" },\r\n          { title: \"电表/协议id\", key: \"ammeterid\" },\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\" },\r\n          { title: \"起始日期(必填)\", key: \"startdate\" },\r\n          { title: \"截止日期(必填)\", key: \"enddate\" },\r\n          { title: \"本期峰段起度(峰平谷必填)\", key: \"prevhighreadings\" },\r\n          { title: \"本期平段起度(峰平谷必填)\", key: \"prevflatreadings\" },\r\n          { title: \"本期谷段起度(峰平谷必填)\", key: \"prevlowreadings\" },\r\n          { title: \"本期起度(普通电表必填)\", key: \"prevtotalreadings\" },\r\n          { title: \"本期峰段止度(峰平谷必填)\", key: \"curhighreadings\" },\r\n          { title: \"本期平段止度(峰平谷必填)\", key: \"curflatreadings\" },\r\n          { title: \"本期谷段止度(峰平谷必填)\", key: \"curlowreadings\" },\r\n          { title: \"本期止度(普通电表必填)\", key: \"curtotalreadings\" },\r\n          { title: \"电损(度)(可填)\", key: \"transformerullage\" },\r\n          { title: \"专票含税金额(元)(必填)\", key: \"inputtaxticketmoney\" },\r\n          { title: \"专票税率（%）(必填)\", key: \"taxrate\" },\r\n          { title: \"专票税额\", key: \"taxamount\" },\r\n          { title: \"普票含税金额(元)(必填)\", key: \"inputticketmoney\" },\r\n          { title: \"其他(元)(可填)\", key: \"ullagemoney\" },\r\n          { title: \"实缴费用(元)含税\", key: \"accountmoney\" },\r\n          { title: \"类型描述\", key: \"categoryname\" },\r\n          { title: \"倍率\", key: \"magnification\" },\r\n          { title: \"定额\", key: \"quotareadings\" },\r\n          { title: \"浮动比（%）\", key: \"quotereadingsratio\" },\r\n          { title: \"用电量(度)\", key: \"curusedreadings\" },\r\n          { title: \"总电量(度)\", key: \"totalusedreadings\" },\r\n          { title: \"电价(元)\", key: \"unitpirce\" },\r\n          { title: \"备注\", key: \"remark\" },\r\n          { title: \"分割比例(%)\", key: \"percent\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    buttonload2(v) {\r\n      this.$refs.showAlarmModel.buttonload2 = v;\r\n    },\r\n    isButtonload(v) {\r\n      this.$refs.showAlarmModel.buttonload = v;\r\n    },\r\n    isShows(t) {\r\n      this.$refs.showAlarmModel.show = t;\r\n      if (this.$refs.showAlarmModel.show == false) {\r\n        this.number2++;\r\n        this.isT = t;\r\n      } else if (this.$refs.showAlarmModel.show == true) {\r\n        this.number2 = 0;\r\n        this.isT = !t;\r\n      }\r\n      if (this.isT && this.number2 < 10) {\r\n        this.isShows(t);\r\n      }\r\n    },\r\n    nextCheck() {\r\n      this.showAlarmModel = true;\r\n      this.isShows(true);\r\n      // this.$refs.showAlarmModel.show=true\r\n      this.$refs.showAlarmModel.tableData = [];\r\n      this.$refs.showAlarmModel.resultList = [];\r\n      this.$refs.showAlarmModel.tableData1 = [];\r\n      this.$refs.showAlarmModel.resultList1 = [];\r\n      this.$refs.showAlarmModel.tableData2 = [];\r\n      this.$refs.showAlarmModel.resultList2 = [];\r\n      this.$refs.showAlarmModel.tableData3 = [];\r\n      this.$refs.showAlarmModel.resultList3 = [];\r\n      this.$refs.showAlarmModel.tableData4 = [];\r\n      this.$refs.showAlarmModel.resultList4 = [];\r\n      this.$refs.showAlarmModel.tableData5 = [];\r\n      this.$refs.showAlarmModel.resultList5 = [];\r\n      // setTimeout(() => {\r\n      this.showJhModel = false;\r\n      // this.showAlarmModel=true;\r\n      this.$refs.showAlarmModel.activeButton = 6;\r\n      // },100)\r\n    },\r\n    alarmClose() {\r\n      // window.history.go(0);\r\n      this.showAlarmModel = false;\r\n      this.$refs.showAlarmModel.show = false;\r\n    },\r\n    checkCancel() {\r\n      this.showJhModel = false;\r\n    },\r\n    alarmCheck() {},\r\n    selectChange() {\r\n      getDepartments(this.accountObj.company).then((res) => {\r\n        this.resCenterList = res.data;\r\n        this.resCenterListSize = res.data.length;\r\n        this.accountObj.country = res.data[0].id;\r\n      });\r\n    },\r\n    // 计算定额\r\n    getQuota: (ammeterid, startdate, enddate, callback) => {\r\n      if (ammeterid && startdate && enddate) {\r\n        getElectrQuota(ammeterid, startdate, enddate).then((res) => {\r\n          if (callback) callback(res);\r\n          else callback();\r\n        });\r\n      }\r\n    },\r\n    //删除时检查退回台账是否解除与归集单关联\r\n    getTem: (pcid, callback) => {\r\n      selectByPcid(pcid).then((res) => {\r\n        if (callback) callback(res);\r\n        else callback();\r\n      });\r\n    },\r\n    //翻页时先确认数据是否保存\r\n    handlePage(value) {\r\n      let b = false;\r\n      let data = this.insideData;\r\n      let array = [];\r\n      data.forEach(function (item, index, input) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n      this.pageNum = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //改变表格可显示数据数量时先确认数据是否保存\r\n    handlePageSize(value) {\r\n      let b = false;\r\n      let data = this.insideData;\r\n      let array = [];\r\n      data.forEach(function (item, index, input) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n      this.pageSize = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //向后台请求数据\r\n    getAccountMessages() {\r\n      this.setElectroyType();\r\n      let params = this.accountObj;\r\n      params.pageNum = this.pageNum;\r\n      params.pageSize = this.pageSize;\r\n      let req = {\r\n        url: \"/business/account/selfAccountList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      this.accountTb.loading = true;\r\n      axios\r\n        .request(req)\r\n        .then((res) => {\r\n          this.accountTb.loading = false;\r\n          if (res.data) {\r\n            array = res.data.rows;\r\n            array.push(this.suntotal(array)); //小计\r\n            accountTotal(this.accountObj).then((res) => {\r\n              //合计\r\n              let alltotal = res.data;\r\n              alltotal.total = \"合计\";\r\n              alltotal.projectname = \"合计\";\r\n              alltotal._disabled = true;\r\n              array.push(alltotal);\r\n            });\r\n            this.insideData = array;\r\n            this.pageTotal = res.data.total || 0;\r\n            // debugger\r\n            this.setNewField(res.data.rows);\r\n            this.setMyStyle(res.data.rows.length);\r\n            this.editIndex = -1;\r\n            this.columnsIndex = -1;\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //小计\r\n    suntotal(array) {\r\n      let curusedreadings = 0;\r\n      let transformerullage = 0;\r\n      let ticketmoney = 0;\r\n      let taxticketmoney = 0;\r\n      let taxamount = 0;\r\n      let ullagemoney = 0;\r\n      let accountmoney = 0;\r\n      let inputtaxticketmoney = 0;\r\n      let inputticketmoney = 0;\r\n      array.forEach(function (item) {\r\n        if (item.effective === 1) {\r\n          curusedreadings += item.curusedreadings;\r\n          transformerullage += item.transformerullage;\r\n          ticketmoney += item.ticketmoney;\r\n          taxticketmoney += item.taxticketmoney;\r\n          taxamount += item.taxamount;\r\n          inputtaxticketmoney += item.inputtaxticketmoney;\r\n          inputticketmoney += item.inputticketmoney;\r\n          ullagemoney += item.ullagemoney;\r\n          accountmoney += item.accountmoney;\r\n        }\r\n      });\r\n      return {\r\n        curusedreadings: curusedreadings,\r\n        transformerullage: transformerullage,\r\n        ticketmoney: ticketmoney.toFixed(2),\r\n        taxticketmoney: taxticketmoney.toFixed(2),\r\n        taxamount: taxamount.toFixed(2),\r\n        inputtaxticketmoney: inputtaxticketmoney.toFixed(2),\r\n        inputticketmoney: inputticketmoney.toFixed(2),\r\n        ullagemoney: ullagemoney.toFixed(2),\r\n        accountmoney: accountmoney.toFixed(2),\r\n        total: \"小计\",\r\n        projectname: \"小计\",\r\n        _disabled: true,\r\n      };\r\n    },\r\n    searchList() {\r\n      this.pageNum = 1;\r\n      this.getAccountMessages();\r\n    },\r\n    onResetHandle() {\r\n      this.accountObj = {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        substation: \"\", //支局\r\n        projectname: \"\", //项目名称\r\n        prevtotalreadings: null, //上期止度\r\n        ammetercode: \"\", //电表户号/协议编码\r\n        isreturn: \"\", //是否退回\r\n        company: this.CompanyList[0].id,\r\n        userId: \"\",\r\n        accountType: \"1\", //台账类型\r\n        supplybureauammetercode: \"\",\r\n      };\r\n\r\n      this.accountObj.version = indexData.version;\r\n      this.userName = \"\";\r\n      this.classifications = [];\r\n      this.selectChange();\r\n      this.getAccountMessages();\r\n    },\r\n    //保存可编辑表格的初始化数据\r\n    setNewField(data) {\r\n      data.forEach(function (item) {\r\n        item.old_startdate = item.startdate;\r\n        item.old_prevtotalreadings = item.prevtotalreadings;\r\n        item.multtimes = item.magnification;\r\n        item.old_enddate = item.enddate;\r\n        item.old_curtotalreadings = item.curtotalreadings;\r\n        item.old_transformerullage = item.transformerullage;\r\n        item.old_taxticketmoney = item.inputtaxticketmoney;\r\n        item.old_ticketmoney = item.inputticketmoney;\r\n        item.old_ullagemoney = item.ullagemoney;\r\n        item.old_prevhighreadings = item.prevhighreadings;\r\n        item.old_prevflatreadings = item.prevflatreadings;\r\n        item.old_prevlowreadings = item.prevlowreadings;\r\n\r\n        item.old_curhighreadings = item.curhighreadings;\r\n        item.old_curflatreadings = item.curflatreadings;\r\n        item.old_curlowreadings = item.curlowreadings;\r\n        item.old_curtotalreadings = item.curtotalreadings;\r\n\r\n        item.version = indexData.version;\r\n        item.editType = 0;\r\n        item.isFPG = judging_editability1(item);\r\n        item.isWB = judging_editability(item);\r\n        if (!item.remark) item.remark = \"\";\r\n        if (!item.bz) item.bz = \"\";\r\n        item.transformerullage = judgeNumber(item.transformerullage);\r\n        // item.supplybureauammetercode = judgeNumber(item.supplybureauammetercode);\r\n        item.inputtaxticketmoney = judgeNumber(item.inputtaxticketmoney);\r\n        item.inputticketmoney = judgeNumber(item.inputticketmoney);\r\n        item.taxticketmoney = judgeNumber(item.taxticketmoney);\r\n        item.ticketmoney = judgeNumber(item.ticketmoney);\r\n        item.ullagemoney = judgeNumber(item.ullagemoney);\r\n        item.curusedreadings = judgeNumber(item.curusedreadings);\r\n        item.accountmoney = judgeNumber(item.accountmoney);\r\n        if ((item.taxrate == null || item.taxrate == 0) && item.total == null) {\r\n          item.taxrate = \"13\";\r\n        }\r\n        if (item.taxrate && item.taxamount == null) {\r\n          item.taxamount = countTaxamount(item);\r\n        }\r\n      });\r\n    },\r\n    //计算 用电量,总电量,单价,总费用,浮动比.\r\n    calculateAll(row) {\r\n      console.log(row, \"row\");\r\n      row.curusedreadings = _calculateUsedReadings(row);\r\n      row.totalusedreadings = _calculateTotalReadings(row);\r\n      if (row.ischangeammeter == 1 && row.isnew == 1) {\r\n        if (row.oldbillpower > 0) {\r\n          let total = Math.abs(row.totalusedreadings) + Math.abs(row.oldbillpower);\r\n          let category = row.category;\r\n          let ammeteruse = row.ammeteruse; //电表用途\r\n          if (judge_negate(category) || judge_recovery(ammeteruse)) {\r\n            total = -total;\r\n          }\r\n          row.totalusedreadings = total;\r\n        }\r\n        let remark = row.remark;\r\n        if (remark.indexOf(\"换表\") == -1) {\r\n          row.remark += \"换表，结清原电表读数【\" + row.oldbillpower + \"】；\";\r\n        }\r\n      }\r\n      if (row.ticketmoney || row.taxticketmoney) {\r\n        row.accountmoney = _calculateAccountMoney(row);\r\n        row.unitpirce = _calculateUnitPriceByUsedMoney(row);\r\n      }\r\n      row.quotereadingsratio = _calculateQuotereadingsratio(row);\r\n    },\r\n    //暂存\r\n    temporaryStorage() {\r\n      let array = [];\r\n      this.insideData.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          array.push(item);\r\n        }\r\n      });\r\n      let data = array;\r\n      //--------------------------------------\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n          item.startyear = yyyymmdd.yyyy;\r\n          item.startmonth = yyyymmdd.mm;\r\n          yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n          item.endyear = yyyymmdd.yyyy;\r\n          item.endmonth = yyyymmdd.mm;\r\n          item.accountno = no;\r\n          submitData.push(item);\r\n          number++;\r\n        });\r\n        if (submitData.length > 0) {\r\n          temporaryStorage(submitData).then((res) => {\r\n            if (res.data.num > 0) {\r\n              that.$Message.info({\r\n                content: \"成功暂存\" + res.data.num + \"条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n            }\r\n            that.pageNum = 1;\r\n            that.getAccountMessages();\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //四川能耗稽核流程\r\n    preserveSc() {\r\n      let arr = [];\r\n      this.ammeterids.forEach((item1) => {\r\n        if (arr.indexOf(item1) == -1) {\r\n          arr.push(item1);\r\n        }\r\n      });\r\n      this.$refs.checkResult.ammeterids = arr;\r\n      this.showJhModel = true;\r\n    },\r\n    async preserve() {\r\n      let dataL = this.$refs.accountTable.getSelection();\r\n\r\n      let b = false;\r\n      let data = this.insideData;\r\n      let array = [];\r\n      let that = this;\r\n\r\n      let version = indexData.version;\r\n      for (let i = 0; i < dataL.length; i++) {\r\n        if (dataL[i].editType == 1) {\r\n          if (\"sc\" == version && dataL[i].electrotype && dataL[i].electrotype > 1400) {\r\n            if (\r\n              dataL[i].stationcode5gr == null ||\r\n              dataL[i].stationcode5gr == undefined ||\r\n              dataL[i].stationcode5gr == \"\"\r\n            ) {\r\n              this.errorTips(\r\n                \"电表/协议编号【\" +\r\n                  dataL[i].ammetercode +\r\n                  \"】，项目名称【\" +\r\n                  dataL[i].projectname +\r\n                  \"】关联局站的5GR站址为空，请完善局站信息，或者5GR有效性清单失效，请联系无线管理员。\"\r\n              );\r\n            }\r\n          }\r\n          //截止日期校验\r\n          let result = await this.handleEndDate(dataL[i], dataL[i].enddate);\r\n          if (result) {\r\n            this.errorTips(result);\r\n            return;\r\n          }\r\n          let maxdegree = parseInt(dataL[i].maxdegree); //翻表度数,即电表的最大度数\r\n          if (maxdegree != null && maxdegree > 0) {\r\n            if (dataL[i].curtotalreadings > maxdegree) {\r\n              that.errorTips(\"本期止度不能大于翻表值：\" + maxdegree);\r\n            } else {\r\n              b = true;\r\n              array.push(dataL[i]);\r\n            }\r\n          } else {\r\n            b = true;\r\n            array.push(dataL[i]);\r\n          }\r\n        }\r\n      }\r\n      // });\r\n      if (b) {\r\n        this.submitData(array);\r\n      } else {\r\n        this.errorTips(\"没有可保存数据\");\r\n      }\r\n    },\r\n    async getContractInfo(stationCode) {\r\n      this.contractCount = 0;\r\n      await validContractList({ stationCode: stationCode }).then((res) => {\r\n        if (res.data) {\r\n          this.contractCount = res.data.length;\r\n        }\r\n      });\r\n    },\r\n    //提交数据\r\n    submitData(data) {\r\n      // this.ammeterids=[];\r\n      let a = [];\r\n      let str = \"\";\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        let str1 = \"\";\r\n        data.forEach(function (item) {\r\n          let obj = requiredFieldValidator(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n        });\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          that.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n        if (submitData.length > 0) {\r\n          //四川能耗需做稽核流程\r\n          this.submit = submitData;\r\n          this.submit2 = submitData;\r\n          editOwn(submitData).then((res) => {\r\n            if (res.data.num > 0) {\r\n              that.$Message.info({\r\n                content: \"成功保存\" + res.data.num + \"条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n            }\r\n\r\n            if (res.data.str.length > 0) {\r\n              that.errorTips(res.data.str);\r\n            }\r\n            that.pageNum = 1;\r\n            that.getAccountMessages();\r\n          });\r\n        }\r\n      }\r\n    },\r\n    submitChange(indexList) {\r\n      let data = [];\r\n      this.submit2.map((item, index) => {\r\n        indexList.map((item2) => {\r\n          if (index == item2) {\r\n            data.push(item);\r\n          }\r\n        });\r\n      });\r\n      this.submit = data;\r\n    },\r\n    getAuditResultNew(data) {\r\n      let arr = [];\r\n      data.forEach((item) => {\r\n        arr.push(item.pcid);\r\n      });\r\n      let param = {\r\n        pcids: arr,\r\n      };\r\n      getAuditResultNew_QXM(param).then((res2) => {\r\n        this.auditResultList = res2.data;\r\n\r\n        if (this.auditResultList && this.auditResultList.length == 0) {\r\n          this.number++;\r\n          this.isQuery = true;\r\n          // }\r\n        } else if (data.length != this.auditResultList.length) {\r\n          this.number++;\r\n          this.isQuery = true;\r\n        } else if (data.length == this.auditResultList.length) {\r\n          this.isQuery = false;\r\n          this.number = 0;\r\n        } else {\r\n          this.isQuery = false;\r\n          this.number = 0;\r\n        }\r\n        if (this.isQuery && this.number < 5) {\r\n          setTimeout(() => this.getAuditResultNew(data), 6000);\r\n        } else {\r\n          this.auditResultList.forEach((item) => {\r\n            this.$refs.showAlarmModel.resultList.push(item.msg);\r\n            this.$refs.showAlarmModel.tableData.push(item.powerAuditEntity);\r\n\r\n            if (item.staute == \"失败\") {\r\n              // if(item.powerAuditEntity.mutiJtlteCodes=='是'\r\n              // || item.powerAuditEntity.electricityPrices=='否'\r\n              // || item.powerAuditEntity.addressConsistence=='否'\r\n              // || item.powerAuditEntity.reimbursementCycle=='否' || item.powerAuditEntity.electricityContinuity=='否' ||\r\n              // item.powerAuditEntity.shareAccuracy=='否' ||\r\n              // item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n              // item.powerAuditEntity.paymentConsistence=='否'){\r\n              if (item.powerAuditEntity.mutiJtlteCodes == \"是\") {\r\n                //一站多表\r\n                this.$refs.showAlarmModel.tableData4.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList4.push(item.msg);\r\n              }\r\n              if (item.powerAuditEntity.electricityPrices == \"否\") {\r\n                //单价异常\r\n                this.$refs.showAlarmModel.tableData5.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList5.push(item.msg);\r\n              }\r\n              if (\r\n                item.powerAuditEntity.addressConsistence == \"否\" ||\r\n                item.powerAuditEntity.reimbursementCycle == \"否\" ||\r\n                item.powerAuditEntity.electricityContinuity == \"否\" ||\r\n                item.powerAuditEntity.shareAccuracy == \"否\" ||\r\n                // item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n                item.powerAuditEntity.paymentConsistence == \"否\" ||\r\n                item.powerAuditEntity.fluctuateContinuity == \"否\"\r\n              ) {\r\n                this.$refs.showAlarmModel.tableData2.push(item.powerAuditEntity); //其他异常\r\n                this.$refs.showAlarmModel.resultList2.push(item.msg);\r\n              }\r\n              // }\r\n            } else {\r\n              if (\r\n                // item.powerAuditEntity.electricityRationality == \"是\" && //电量合理性(省内大数据)\r\n                // item.powerAuditEntity.exclusiveAccuracy == \"是\" && //局站独享共享设置\r\n                item.powerAuditEntity.periodicAnomaly == \"是\" //台账周期合理性\r\n              ) {\r\n                this.$refs.showAlarmModel.tableData1.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList1.push(item.msg);\r\n              } else {\r\n                this.$refs.showAlarmModel.tableData3.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList3.push(item.msg);\r\n              }\r\n            }\r\n            if (this.auditResultList.length > 0) {\r\n              this.auditResultList[this.auditResultList.length - 1].progress =\r\n                ((this.auditResultList.length * 1) / data.length) * 1;\r\n            }\r\n            this.$refs.showAlarmModel.processData = Number(item.progress) * 100;\r\n            this.$refs.showAlarmModel.scrollList();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    save(value) {\r\n      var that = this;\r\n      //进度条页面链接websoket后调用，传jh字段不走保存流程，走稽核流程\r\n      if (value == 1) {\r\n        that.submit[0].jh = \"1\";\r\n        that.submit[0].ymmc = \"自有电费台账\";\r\n        this.getAuditResultNew(that.submit);\r\n      } else {\r\n        if (that.submit[0].hasOwnProperty(\"jh\")) {\r\n          delete that.submit[0].jh;\r\n        }\r\n        if (this.name == \"current\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAccount();\r\n          }\r\n        } else if (this.name == \"all\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAllAccount();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //加入归集单，全部非强控\r\n    selectedFQK() {\r\n      let that = this;\r\n      that.setElectroyType();\r\n      that.$refs.addBillPer.initAmmeter(\r\n        that.$refs.showAlarmModel.selectIds3,\r\n        1,\r\n        this.accountObj.country\r\n      );\r\n    },\r\n    //添加修改峰平谷值的备注\r\n    addFremark(row) {\r\n      let old = row.old_prevhighreadings;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (\r\n        row.curhighreadings != null &&\r\n        row.curhighreadings > 0 &&\r\n        this.editprevhighreadings > row.curhighreadings\r\n      ) {\r\n        this.errorTips(\"起始峰值不能大于截止峰值\" + row.curhighreadings);\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editprevhighreadings = old;\r\n        }, 200);\r\n      } else {\r\n        row.prevhighreadings = this.editprevhighreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n        if (row.old_prevhighreadings != row.prevhighreadings) {\r\n          row.remark +=\r\n            \"本期起始峰值 从\" +\r\n            row.old_prevhighreadings +\r\n            \"修改为\" +\r\n            row.prevhighreadings +\r\n            \"; \";\r\n        }\r\n        //修改opflag修改起日期 原来数字加\r\n        let opflag = row.opflag;\r\n        if (opflag != 3 && opflag != 5 && opflag != 7 && opflag != 9) {\r\n          row.opflag = opflag + 3;\r\n        }\r\n      }\r\n    },\r\n    //起始平值变换记录备注\r\n    addPremark(row) {\r\n      let old = row.old_prevflatreadings;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (\r\n        row.curflatreadings != null &&\r\n        row.curflatreadings > 0 &&\r\n        this.editprevflatreadings > row.curflatreadings\r\n      ) {\r\n        this.errorTips(\"起始平值不能大于截止平值\" + row.curflatreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editprevflatreadings = old;\r\n        }, 200);\r\n      } else {\r\n        row.prevflatreadings = this.editprevflatreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n        if (row.old_prevflatreadings != row.prevflatreadings) {\r\n          row.remark +=\r\n            \"本期起始平值 从\" +\r\n            row.old_prevflatreadings +\r\n            \"修改为\" +\r\n            row.prevflatreadings +\r\n            \"; \";\r\n        }\r\n        //修改opflag修改起日期 原来数字加\r\n        let opflag = row.opflag;\r\n        if (opflag != 3 && opflag != 5 && opflag != 7 && opflag != 9) {\r\n          row.opflag = opflag + 3;\r\n        }\r\n      }\r\n    },\r\n    //起始谷值变换记录备注\r\n    addGremark(row) {\r\n      let old = row.old_prevlowreadings;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (\r\n        row.curlowreadings != null &&\r\n        row.curlowreadings > 0 &&\r\n        this.editprevlowreadings > row.curlowreadings\r\n      ) {\r\n        this.errorTips(\"起始谷值不能大于截止谷值\" + row.curlowreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editprevlowreadings = old;\r\n        }, 200);\r\n      } else {\r\n        row.prevlowreadings = this.editprevlowreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n        if (row.old_prevlowreadings != row.prevlowreadings) {\r\n          row.remark +=\r\n            \"本期起始谷值 从\" +\r\n            row.old_prevlowreadings +\r\n            \"修改为\" +\r\n            row.prevlowreadings +\r\n            \"; \";\r\n        }\r\n        //修改opflag修改起日期 原来数字加\r\n        let opflag = row.opflag;\r\n        if (opflag != 3 && opflag != 5 && opflag != 7 && opflag != 9) {\r\n          row.opflag = opflag + 3;\r\n        }\r\n      }\r\n    },\r\n    setcurhighreadings(row) {\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (this.editcurhighreadings < row.prevhighreadings) {\r\n        this.errorTips(\"截止峰值不能小于起始峰值\" + row.prevhighreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editcurhighreadings = row.curhighreadings;\r\n        }, 200);\r\n      } else {\r\n        row.curhighreadings = this.editcurhighreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n      }\r\n    },\r\n    setcurflatreadings(row) {\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (this.editcurflatreadings < row.prevflatreadings) {\r\n        this.errorTips(\"截止平值不能小于起始平值\" + row.prevflatreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editcurflatreadings = row.curflatreadings;\r\n        }, 200);\r\n      } else {\r\n        row.curflatreadings = this.editcurflatreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n      }\r\n    },\r\n    setcurlowreadings(row) {\r\n      let next = row.ifNext;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (this.editcurlowreadings < row.prevlowreadings) {\r\n        this.errorTips(\"截止谷值不能小于起始谷值\" + row.prevlowreadings);\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editcurlowreadings = row.curlowreadings;\r\n        }, 200);\r\n      } else {\r\n        row.curlowreadings = this.editcurlowreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n      }\r\n    },\r\n    setFPG(row) {\r\n      let item = {\r\n        prevhighreadings: row.prevhighreadings,\r\n        prevflatreadings: row.prevflatreadings,\r\n        prevlowreadings: row.prevlowreadings,\r\n        curhighreadings: row.curhighreadings,\r\n        curflatreadings: row.curflatreadings,\r\n        curlowreadings: row.curlowreadings,\r\n        highreadings: parseFloat(this.edithighreadings.toFixed(2)),\r\n        flatreadings: parseFloat(this.editflatreadings.toFixed(2)),\r\n        lowreadings: parseFloat(this.editlowreadings.toFixed(2)),\r\n        magnification: row.magnification,\r\n      };\r\n      let amount = _calculateUsedReadingsForType_2(item);\r\n\r\n      if (amount < 0) {\r\n        //计算用电量\r\n        this.errorTips(\r\n          \"计算用电量小于0(\" +\r\n            amount +\r\n            \"),请确认峰平谷加减电量值！\" +\r\n            \"加减电量(峰值)\" +\r\n            parseFloat(this.edithighreadings.toFixed(2)) +\r\n            \",加减电量(平值)\" +\r\n            parseFloat(this.editflatreadings.toFixed(2)) +\r\n            \",加减电量(谷值)\" +\r\n            parseFloat(this.editlowreadings.toFixed(2)) +\r\n            \"当前用电量\" +\r\n            row.curusedreadings\r\n        );\r\n        // return;\r\n      }\r\n      row.highreadings = parseFloat(this.edithighreadings.toFixed(2));\r\n      row.flatreadings = parseFloat(this.editflatreadings.toFixed(2));\r\n      row.lowreadings = parseFloat(this.editlowreadings.toFixed(2));\r\n      row.editType = 1;\r\n      this.calculateAll(row);\r\n    },\r\n    //一键删除数据\r\n    deleteAll() {\r\n      this.$Modal.confirm({\r\n        title: \"提示\",\r\n        content: \"<p>确定一键删除吗？</p>\",\r\n        onOk: () => {\r\n          this.accountTb.loading = true;\r\n          let params = this.accountObj;\r\n          params.removeAllFlag = true;\r\n          delete params.pageSize;\r\n          delete params.pageNum;\r\n          removeAll(params).then((res) => {\r\n            this.accountTb.loading = false;\r\n            if (res.data.num > 0) {\r\n              this.$Message.success(\"一键删除成功\");\r\n              this.searchList();\r\n            } else {\r\n              this.$Message.error(\"一键删除失败\");\r\n            }\r\n          });\r\n        },\r\n        onCancel: () => {},\r\n      });\r\n    },\r\n    //删除行数据\r\n    remove() {\r\n      let version = indexData.version;\r\n      let data = this.$refs.accountTable.getSelection();\r\n      if (data == null || data.length === 0) {\r\n        this.errorTips(\"请选择要删除的台账\");\r\n        return;\r\n      }\r\n      let ids = \"\";\r\n      let that = this;\r\n      let str = \"\";\r\n      data.forEach(function (item) {\r\n        if (item.ifNext) {\r\n          str +=\r\n            \"电表/协议编号为【\" +\r\n            item.ammetercode +\r\n            \"】当期【\" +\r\n            item.accountno +\r\n            \"期】台账之后已有正式数据，不能删除！\";\r\n        }\r\n        ids += item.pcid + \",\";\r\n      });\r\n      if (ids.length > 0 && str.length === 0) {\r\n        that.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>是否确认删除选中信息？</p>\",\r\n          onOk: () => {\r\n            removeOwn(ids).then((res) => {\r\n              if (res.data.num > 0) {\r\n                that.$Message.info({\r\n                  content: \"成功删除\" + res.data.num + \"条数据\",\r\n                  duration: 10,\r\n                  closable: true,\r\n                });\r\n              }\r\n\r\n              if (res.data.str.length > 0) {\r\n                that.errorTips(res.data.str);\r\n              }\r\n              that.searchList();\r\n            });\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      } else {\r\n        that.errorTips(str);\r\n      }\r\n    },\r\n    //span点击事件将span换成输入框并且获取焦点\r\n    selectCall(row, index, columns, str) {\r\n      this.editStartDate = row.startdate;\r\n      this.editEndDate = row.enddate;\r\n      this.editPrevtotalreadings =\r\n        row.prevtotalreadings == null || row.prevtotalreadings === 0\r\n          ? null\r\n          : row.prevtotalreadings;\r\n      this.editcurtotalreadings =\r\n        row.curtotalreadings == null || row.curtotalreadings === 0\r\n          ? null\r\n          : row.curtotalreadings;\r\n      this.edittransformerullage =\r\n        row.transformerullage == null || row.transformerullage === 0\r\n          ? null\r\n          : row.transformerullage;\r\n      this.edittaxticketmoney =\r\n        row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n          ? null\r\n          : row.inputtaxticketmoney;\r\n      this.editticketmoney =\r\n        row.inputticketmoney == null || row.inputticketmoney === 0\r\n          ? null\r\n          : row.inputticketmoney;\r\n      this.editullagemoney =\r\n        row.ullagemoney == null || row.ullagemoney === 0 ? null : row.ullagemoney;\r\n      this.edittaxrate =\r\n        row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n      this.editremark = row.bz;\r\n      this.editIndex = index;\r\n      this.columnsIndex = columns;\r\n\r\n      let a = this;\r\n      setTimeout(function () {\r\n        if (columns != 8) {\r\n          a.$refs[str + index + columns].focus();\r\n        }\r\n      }, 200);\r\n    },\r\n    //根据列号返回对应的列名\r\n    enterOperate(number) {\r\n      let str = \"\";\r\n      let data = null;\r\n      switch (number) {\r\n        case 1:\r\n          str = \"startdate\";\r\n          data = this.editStartDate;\r\n          break;\r\n        case 2:\r\n          str = \"enddate\";\r\n          data = this.editEndDate;\r\n          break;\r\n        case 3:\r\n          str = \"prevtotalreadings\";\r\n          data = this.editPrevtotalreadings;\r\n          break;\r\n        case 4:\r\n          str = \"curtotalreadings\";\r\n          data = this.editcurtotalreadings;\r\n          break;\r\n        case 5:\r\n          str = \"transformerullage\";\r\n          data = this.edittransformerullage;\r\n          break;\r\n        case 6:\r\n          str = \"inputticketmoney\";\r\n          data = this.editticketmoney;\r\n          break;\r\n        case 7:\r\n          str = \"inputtaxticketmoney\";\r\n          data = this.edittaxticketmoney;\r\n          break;\r\n        case 8:\r\n          str = \"taxrate\";\r\n          data = this.edittaxrate;\r\n          break;\r\n        case 9:\r\n          str = \"ullagemoney\";\r\n          data = this.editullagemoney;\r\n          break;\r\n        case 10:\r\n          str = \"remark\";\r\n          data = this.editremark;\r\n          break;\r\n      }\r\n      return { str: str, data: data };\r\n    },\r\n    //输入数据验证\r\n    validate() {\r\n      if (this.columnsIndex === 10) {\r\n        this.validateRemark();\r\n        return;\r\n      }\r\n      let val = this.enterOperate(this.columnsIndex).data;\r\n      if (val) {\r\n        if (testNumber(val)) {\r\n          switch (this.columnsIndex) {\r\n            case 1:\r\n              this.validateStartdate();\r\n              break;\r\n            case 2:\r\n              this.validateEnddate();\r\n              break;\r\n            case 3:\r\n              this.validatePrevtotalreadings();\r\n              break;\r\n            case 4:\r\n              this.validateCurtotalreadings();\r\n              break;\r\n            case 5:\r\n              this.validateTransformerullage();\r\n              break;\r\n            case 6:\r\n              this.validateTicketmoney();\r\n              break;\r\n            case 7:\r\n              this.validateTaxticketmoney();\r\n              break;\r\n            case 9:\r\n              this.validateUllagemoney();\r\n              break;\r\n          }\r\n        } else {\r\n          this.errorTips(\"请输入数字！\");\r\n        }\r\n      }\r\n    },\r\n    //验证错误弹出提示框并跳转到下一格\r\n    errorTips(str) {\r\n      this.$Notice.error({\r\n        title: \"提示\",\r\n        desc: str,\r\n        duration: 10,\r\n      });\r\n    },\r\n    //跳转到下一格\r\n    nextCell(data) {\r\n      let index = data.editIndex;\r\n      let columns = data.columnsIndex;\r\n      let row = \"\";\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n      } else if (index > -1 && columns === 10) {\r\n        //当跳转的最后一行最后一格的时候\r\n        if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n          index = 0;\r\n        } else {\r\n          index++;\r\n        }\r\n        columns = 1;\r\n      } else {\r\n        row = data.insideData[index];\r\n        //无表或峰平谷表的时候\r\n        if (row && (row.isFPG || row.isWB) && columns >= 2 && columns <= 4) {\r\n          if (row.isWB) {\r\n            columns += 4;\r\n          } else {\r\n            columns += 3;\r\n          }\r\n        } else {\r\n          columns += 1;\r\n        }\r\n        //有下期的台账不能改\r\n        if (row.ifNext) {\r\n          if (columns < 5) {\r\n            columns = 5;\r\n          }\r\n        }\r\n      }\r\n      data.editIndex = index;\r\n      data.columnsIndex = columns;\r\n      row = data.insideData[index];\r\n      if (row) {\r\n        data.editStartDate = row.startdate;\r\n        data.editEndDate = row.enddate;\r\n        data.editPrevtotalreadings =\r\n          row.prevtotalreadings == null || row.prevtotalreadings === 0\r\n            ? null\r\n            : row.prevtotalreadings;\r\n        data.editcurtotalreadings =\r\n          row.curtotalreadings == null || row.curtotalreadings === 0\r\n            ? null\r\n            : row.curtotalreadings;\r\n        data.edittransformerullage =\r\n          row.transformerullage == null || row.transformerullage === 0\r\n            ? null\r\n            : row.transformerullage;\r\n        data.edittaxticketmoney =\r\n          row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n            ? null\r\n            : row.inputtaxticketmoney;\r\n        data.editticketmoney =\r\n          row.inputticketmoney == null || row.inputticketmoney === 0\r\n            ? null\r\n            : row.inputticketmoney;\r\n        data.editullagemoney =\r\n          row.ullagemoney == null || row.ullagemoney === 0 ? null : row.ullagemoney;\r\n        data.edittaxrate =\r\n          row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n        data.editremark = row.bz;\r\n      }\r\n\r\n      setTimeout(function () {\r\n        if (columns != 8) {\r\n          data.$refs[data.enterOperate(columns).str + index + columns].focus();\r\n        }\r\n      }, 200);\r\n    },\r\n    //验证起始时间\r\n    validateStartdate() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editStartDate;\r\n      if (val != data.old_startdate) {\r\n        // 验证起始时间方法\r\n        let result = _verify_StartDate(data, val, \"可大于\");\r\n        if (result) {\r\n          //失败就弹出提示内容，并将数据恢复初始化\r\n          this.errorTips(result);\r\n          this.myStyle[this.editIndex].startdate = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].startdate = \"myspan\";\r\n          this.startModal = true;\r\n        }\r\n      } else if (val == data.old_startdate) {\r\n        data.startdate = val;\r\n      }\r\n    },\r\n    //验证截止时间\r\n    async validateEnddate() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editEndDate;\r\n      if (val != data.old_enddate) {\r\n        // 验证截止日期方法\r\n        let result = await this.handleEndDate(data, val);\r\n        if (result) {\r\n          this.errorTips(result);\r\n          this.myStyle[this.editIndex].enddate = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].enddate = \"myspan\";\r\n\r\n          this.updateenddate(data, val);\r\n        }\r\n      } else if (val == data.old_enddate) {\r\n        data.enddate = val;\r\n      }\r\n    },\r\n    //截止日期处理\r\n    async handleEndDate(data, val) {\r\n      //直供电有上传日期才可以修改到月底 directsupplyflag 1直供2转供\r\n      let fType = \"\";\r\n      let curval = stringToDate(val); //输入值\r\n      let nowdate = stringToDate(getCurrentDate()); //当天\r\n      if (data.directsupplyflag == 1 && curval > nowdate) {\r\n        let files = await axios\r\n          .request({\r\n            url: \"/common/attachments/list\",\r\n            method: \"post\",\r\n            data: {\r\n              areaCode: \"sc\",\r\n              busiAlias: \"附件(台账)\",\r\n              busiId: data.pcid + \"\",\r\n              categoryCode: \"file\",\r\n              pageNum: 1,\r\n              pageSize: 20,\r\n            },\r\n          })\r\n          .then((res) => {\r\n            return res.data.rows;\r\n          });\r\n        if (files.length == 0) {\r\n          return \"截止日期需小于等于当前时间，超过当前时间需上传附件\";\r\n        } else {\r\n          fType = \"限制期号\"; //截止日期不限制期号的最后一天（月底）\r\n        }\r\n      }\r\n      // 验证截止日期方法\r\n      let result = _verify_EndDate(data, val, fType);\r\n      return result;\r\n    },\r\n    updateenddate(data, val) {\r\n      data.enddate = val;\r\n      data.editType = 1;\r\n      //计算定额\r\n      this.getQuota(data.ammeterid, data.startdate, data.enddate, (result) => {\r\n        if (result.data.code === 0) {\r\n          data.quotareadings = Math.round(result.data.msg);\r\n        } else {\r\n          data.quotareadings = 0;\r\n        }\r\n        this.calculateAll(data);\r\n      });\r\n      this.nowdatediff = GetDateDiff(data.startdate, data.enddate);\r\n    },\r\n    //验证起度\r\n    validatePrevtotalreadings() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editPrevtotalreadings;\r\n      val = parseFloat(val);\r\n      if (val != data.old_prevtotalreadings) {\r\n        //验证\r\n        let result = _verify_PrevTotalReadings(data, val);\r\n        if (result.string) {\r\n          this.errorTips(result.string);\r\n          this.myStyle[this.editIndex].prevtotalreadings = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].prevtotalreadings = \"myspan\";\r\n          this.ifMaxdegree = result.b;\r\n          this.qdModal = true;\r\n        }\r\n      } else if (val == data.old_prevtotalreadings) {\r\n        data.prevtotalreadings = val;\r\n        this.calculateAll(data);\r\n      }\r\n    },\r\n    //验证止度\r\n    validateCurtotalreadings() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editcurtotalreadings;\r\n\r\n      if (val != data.old_curtotalreadings) {\r\n        val = parseFloat(val);\r\n        let result = _verify_CurTotalReadings(data, val);\r\n        if (result.string) {\r\n          this.errorTips(result.string);\r\n          this.myStyle[this.editIndex].curtotalreadings = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].curtotalreadings = \"myspan\";\r\n\r\n          this.updateCurtotalreadings(data, val, result);\r\n        }\r\n\r\n        let computreading = 0;\r\n        this.Accountqur.ammeterid = data.ammeterid;\r\n        this.Accountqur.startdate = data.startdate;\r\n        this.Accountqur.enddate = data.enddate;\r\n      } else if (val == data.old_curtotalreadings) {\r\n        data.curtotalreadings = val;\r\n\r\n        this.calculateAll(data);\r\n      }\r\n    },\r\n    updateCurtotalreadings(data, val, result) {\r\n      data.curtotalreadings = val;\r\n      data.editType = 1;\r\n      let b = result.b;\r\n      if (b === true) {\r\n        this.fbModal = true;\r\n      } else if (b === false) {\r\n        this.qxfbModal = true;\r\n      } else {\r\n        this.calculateAll(data);\r\n      }\r\n      if (indexData.version == \"sc\") {\r\n        //验证上期台账是否完成报账\r\n        axios\r\n          .request({\r\n            url: \"/business/accountSC/valOldAcount\",\r\n            method: \"post\",\r\n            params: { pcid: data.pcid, ammeterid: data.ammeterid },\r\n          })\r\n          .then((res) => {\r\n            let msg = \"\";\r\n            if (res.data.msg) msg = res.data.msg;\r\n            if (data.startdate.endsWith(\"0101\"))\r\n              msg += \"【该起始日期是默认值，请注意修改】\";\r\n            if (msg != \"\")\r\n              this.$Notice.warning({\r\n                title: \"注意\",\r\n                desc: \"电表/协议【\" + data.ammetercode + \"】\" + msg,\r\n                duration: 10,\r\n              });\r\n            if (res.data.acc) {\r\n              Object.assign(data, {\r\n                unitpirceold: res.data.acc.unitpirce,\r\n                curusedreadingsold: res.data.acc.curusedreadings,\r\n              });\r\n            }\r\n          });\r\n      }\r\n    },\r\n    //验证电损\r\n    validateTransformerullage() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.edittransformerullage;\r\n      let flag = false;\r\n      if (val != data.old_transformerullage) {\r\n        if (\r\n          val != data.old_transformerullage &&\r\n          indexData.version == \"sc\" &&\r\n          data.curusedreadings > 0 &&\r\n          parseFloat(parseFloat(val) / parseFloat(data.curusedreadings)) > 0.1 &&\r\n          (data.ammeteruse == 1 || [1, 3].includes(data.ammeteruse))\r\n        )\r\n          flag = true;\r\n        if (flag) {\r\n          let result = \"电损与实际电量比值已经超过10%，不允许保存\";\r\n          this.errorTips(result);\r\n\r\n          this.myStyle[this.editIndex].transformerullage = \"errorStle\";\r\n          data.transformerullage = 0;\r\n        } else {\r\n          val = parseFloat(val);\r\n          data.transformerullage = val;\r\n          data.editType = 1;\r\n          this.calculateAll(data);\r\n        }\r\n      } else if (val == data.old_transformerullage) {\r\n        data.transformerullage = val;\r\n        this.calculateAll(data);\r\n      }\r\n    },\r\n    //验证专票\r\n    validateTaxticketmoney() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.edittaxticketmoney;\r\n      if (val != data.old_taxticketmoney) {\r\n        val = parseFloat(val);\r\n        data.inputtaxticketmoney = _verify_Money(data, val);\r\n        data.editType = 1;\r\n        data.taxticketmoney = calculateActualMoney(data, val);\r\n        data.taxamount = countTaxamount(data);\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_taxticketmoney) {\r\n        data.inputtaxticketmoney = val;\r\n        data.taxticketmoney = calculateActualMoney(data, val);\r\n        data.taxamount = countTaxamount(data);\r\n        this.calculateAll(data);\r\n      }\r\n      this.validateUnitPrice(data);\r\n      this.validateavemoney(data);\r\n    },\r\n    //验证普票\r\n    validateTicketmoney() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editticketmoney;\r\n      if (val != data.old_ticketmoney) {\r\n        val = parseFloat(val);\r\n        data.inputticketmoney = _verify_Money(data, val);\r\n        data.editType = 1;\r\n        data.ticketmoney = calculateActualMoney(data, val);\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_ticketmoney) {\r\n        data.inputticketmoney = val;\r\n        data.ticketmoney = calculateActualMoney(data, val);\r\n        this.calculateAll(data);\r\n      }\r\n      this.validateUnitPrice(data);\r\n      this.validateavemoney(data);\r\n    },\r\n    //验证其他费用\r\n    validateUllagemoney() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editullagemoney;\r\n      if (val != data.old_ullagemoney) {\r\n        val = parseFloat(val);\r\n        data.ullagemoney = _verify_Money(data, val);\r\n        data.editType = 1;\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_ullagemoney) {\r\n        data.ullagemoney = val;\r\n        this.calculateAll(data);\r\n      }\r\n      this.validateUnitPrice(data);\r\n      this.validateavemoney(data);\r\n    },\r\n    //备注\r\n    validateRemark() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editremark;\r\n      data.bz = val;\r\n      data.editType = 1;\r\n    },\r\n    validateavemoney(data) {\r\n      let version = indexData.version;\r\n      if (\"sc\" == version) {\r\n        let accountmoney = data.accountmoney;\r\n        let aveold = judgeNumber(data.aveaccountmoneyold);\r\n        if (this.nowdatediff == 0)\r\n          this.nowdatediff = GetDateDiff(data.startdate, data.enddate);\r\n        if (aveold != 0 && (accountmoney / this.nowdatediff).toFixed(2) - aveold > 0) {\r\n          if (((accountmoney / this.nowdatediff).toFixed(2) - aveold) / aveold > 0.3)\r\n            this.$Notice.warning({\r\n              title: \"温馨提示\",\r\n              desc:\r\n                \"电表/协议【\" +\r\n                data.ammetercode +\r\n                \"】\" +\r\n                \"日均电费环比值已经超过30%，请注意填写备注说明！\",\r\n              duration: 10,\r\n            });\r\n        }\r\n      }\r\n    },\r\n    //验证单价\r\n    validateUnitPrice(data) {\r\n      let version = indexData.version;\r\n      let category = data.category; //电表描述类型\r\n      let directsupplyflag = data.directsupplyflag; //1直供2转供\r\n      let ammeteruse = data.ammeteruse; //电表用途\r\n      let unitpirce = data.unitpirce; //台账单价\r\n      if (!judge_negate(category) && !judge_recovery(ammeteruse) && judge_yb(category)) {\r\n        if (unitpirce) {\r\n          if (unitpirce != null && unitpirce < unitpirceMax1) {\r\n            // if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n            this.errorTips(\r\n              \"单价范围必须大于0.1元，此台账单价: \" + unitpirce + \"不在范围内，请确认！\"\r\n            );\r\n          }\r\n          // if (directsupplyflag == 1 && ammeteruse == 1) {\r\n          //   if (unitpirce) {\r\n          //     if (unitpirce >= 0.25 && unitpirce < 0.5) {\r\n          //       this.errorTips(\r\n          //         \"直供电单价(\" +\r\n          //           unitpirce +\r\n          //           \")【0.25<=\" +\r\n          //           unitpirce +\r\n          //           \"<0.5】\" +\r\n          //           \"请确认单价是否存在错误\"\r\n          //       );\r\n          //     } else if (unitpirce < 0.25 || unitpirce > 1.2) {\r\n          //       this.errorTips(\r\n          //         \"直供电单价(\" + unitpirce + \")【小于0.25或大于1.20】\" + \"单价，请确认！\"\r\n          //       );\r\n          //     }\r\n          //   }\r\n          // } else if (directsupplyflag == 2) {\r\n          //   if (unitpirce >= 0.3 && unitpirce < 0.6) {\r\n          //     this.errorTips(\r\n          //       \"转供电单价(\" +\r\n          //         unitpirce +\r\n          //         \")【0.3<=\" +\r\n          //         unitpirce +\r\n          //         \"<0.6】\" +\r\n          //         \"请确认单价是否存在错误\"\r\n          //     );\r\n          //   } else if (unitpirce < 0.3) {\r\n          //     this.errorTips(\r\n          //       \"转供电单价(\" +\r\n          //         unitpirce +\r\n          //         \")【\" +\r\n          //         unitpirce +\r\n          //         \"<0.3】\" +\r\n          //         \"单价错误，请确认！\"\r\n          //     );\r\n          //   } else if (unitpirce > 1.5) {\r\n          //     this.errorTips(\r\n          //       \"转供电单价(\" +\r\n          //         unitpirce +\r\n          //         \")【\" +\r\n          //         unitpirce +\r\n          //         \">1.5】\" +\r\n          //         \"请确认单价是否存在错误\"\r\n          //     );\r\n          //   }\r\n          // }\r\n        }\r\n      }\r\n    },\r\n    openModal(index) {\r\n      this.meterModal = true; //弹出框显示\r\n      let row = this.insideData[index];\r\n      if (row.accountno == dates[1].code) {\r\n        let obj = {\r\n          accountno: dates[0].code,\r\n          ammeterid: row.ammeterid,\r\n        };\r\n        selectByAmmeterId(obj).then((res) => {\r\n          row.nextData = res.data;\r\n        });\r\n      }\r\n      if (row) {\r\n        if (row.ifNext) {\r\n          this.readonly = true;\r\n        } else {\r\n          this.readonly = false;\r\n        }\r\n\r\n        this.currentRow = row; //给弹出框绑定数据\r\n        this.editprevhighreadings =\r\n          row.prevhighreadings === null ? 0 : row.prevhighreadings;\r\n        this.editprevflatreadings =\r\n          row.prevflatreadings === null ? 0 : row.prevflatreadings;\r\n        this.editprevlowreadings = row.prevlowreadings === null ? 0 : row.prevlowreadings;\r\n        this.editcurhighreadings = row.curhighreadings === null ? 0 : row.curhighreadings;\r\n        this.editcurflatreadings = row.curflatreadings === null ? 0 : row.curflatreadings;\r\n        this.editcurlowreadings = row.curlowreadings === null ? 0 : row.curlowreadings;\r\n        if (this.version == \"sc\") {\r\n          this.edithighreadings = row.highreadings === null ? 0 : row.highreadings;\r\n          this.editflatreadings = row.flatreadings === null ? 0 : row.flatreadings;\r\n          this.editlowreadings = row.lowreadings === null ? 0 : row.lowreadings;\r\n        }\r\n      }\r\n    },\r\n    openAddBillPerModal(name) {\r\n      this.name = name;\r\n      if (name === \"current\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJ();\r\n        // } else {\r\n        this.selectedAccount();\r\n        // }\r\n      } else if (name === \"all\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJAll();\r\n        // } else {\r\n        this.selectedAllAccount();\r\n        // }\r\n      }\r\n    },\r\n    //加入归集单，全部有效台账\r\n    selectedAllAccount() {\r\n      let that = this;\r\n      that.setElectroyType();\r\n      that.spinShow = true;\r\n      selectIdsByParams(this.accountObj).then((res) => {\r\n        that.spinShow = false;\r\n        if (res.data.str) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: res.data.str,\r\n            duration: 0,\r\n          });\r\n        }\r\n        if (res.data.ids) {\r\n          if (res.data.ids.length == 0) {\r\n            that.errorTips(\"无有效数据可加入归集单\");\r\n          } else {\r\n            that.$refs.addBillPer.initAmmeter(\r\n              res.data.ids,\r\n              // this.$refs.showAlarmModel.selectIds1,\r\n              1,\r\n              this.accountObj.country\r\n            );\r\n          }\r\n        } else {\r\n          that.errorTips(\"无有效数据可加入归集单\");\r\n        }\r\n      });\r\n    },\r\n    //加入归集单\r\n    addPreserveGJ() {\r\n      let dataL = this.$refs.accountTable.getSelection();\r\n      if (dataL == null || dataL.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        this.addSubmitDataGJ(dataL);\r\n      }\r\n    },\r\n\r\n    //加入归集单\r\n    addPreserveGJAll() {\r\n      let params = this.accountObj;\r\n      params.pageNum = 1;\r\n      params.pageSize = 20000;\r\n      let req = {\r\n        url: \"/business/account/selfAccountList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      this.accountTb.loading = true;\r\n      axios.request(req).then((res) => {\r\n        this.accountTb.loading = false;\r\n        let dataL = res.data.rows;\r\n        this.addSubmitDataGJ(dataL);\r\n      });\r\n    },\r\n    //提交归集单数据\r\n    addSubmitDataGJ(data1) {\r\n      let a = [];\r\n      let b = 1;\r\n      let data = data1.filter((item) => item.effective == 1);\r\n      let str = \"\";\r\n      let str1 = \"\";\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let obj = requiredFieldValidator(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status != 1) {\r\n            b = 3;\r\n          }\r\n          if (\r\n            \"sc\" == version &&\r\n            item.unitpirce > 2 &&\r\n            (item.unitpirceold == null || item.unitpirceold < 2) &&\r\n            that.valiprice\r\n          ) {\r\n            b = 4;\r\n            str += item.ammetercode + \",\";\r\n          }\r\n        });\r\n        if (b == 1) {\r\n          if (str1.length > 0) {\r\n            this.$Notice.warning({\r\n              title: \"注意\",\r\n              desc: str1,\r\n              duration: 0,\r\n            });\r\n          }\r\n          if (submitData.length > 0) {\r\n            this.submit = submitData;\r\n            this.submit2 = submitData;\r\n            this.preserveSc();\r\n          }\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\r\n            \"电表/协议编号为【\" +\r\n              str +\r\n              \"】的台账单价已经超过2元，请发OA邮件给省公司审核，通过后才可加入归集单！\"\r\n          );\r\n        }\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          that.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //加入归集单，已选择的台账\r\n    selectedAccount() {\r\n      let data = this.$refs.accountTable.getSelection();\r\n      let b = 1;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        let ids = [];\r\n        data.forEach(function (item) {\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n          ids.push(item.pcid);\r\n        });\r\n        if (b === 1) {\r\n          this.$refs.addBillPer.initAmmeter(ids, 1, this.accountObj.country);\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n      }\r\n    },\r\n    setMyStyle(length) {\r\n      this.myStyle = [];\r\n      for (var i = 0; i < length; i++) {\r\n        this.myStyle.push({\r\n          startdate: \"myspan\",\r\n          enddate: \"myspan\",\r\n          prevtotalreadings: \"myspan\",\r\n          curtotalreadings: \"myspan\",\r\n          transformerullage: \"myspan\",\r\n          inputtaxticketmoney: \"myspan\",\r\n          inputticketmoney: \"myspan\",\r\n          ullagemoney: \"myspan\",\r\n          taxrate: \"myspan\",\r\n          remark: \"myspan\",\r\n        });\r\n      }\r\n    },\r\n    submitChange1(data) {\r\n      this.submit = data;\r\n    },\r\n    refresh() {\r\n      if (this.$refs.showAlarmModel.fromGuijidan != 1) {\r\n        // window.history.go(0);\r\n        let obj = this;\r\n        obj.showAlarmModel = false;\r\n        setTimeout(function () {\r\n          obj.getAccountMessages();\r\n        }, 200);\r\n      } else {\r\n        this.showAlarmModel = true;\r\n      }\r\n    },\r\n    againJoin() {\r\n      let data = this.$refs.accountTable.getSelection();\r\n      let b = true;\r\n      var that = this;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要重新加入归集单的台账\");\r\n      } else {\r\n        let againJoinIds = \"\";\r\n        data.forEach(function (item) {\r\n          let status = item.status;\r\n          if (status != 5) {\r\n            b = false;\r\n          } else {\r\n            againJoinIds += item.pcid + \",\";\r\n          }\r\n        });\r\n        if (b) {\r\n          againJoin(againJoinIds).then((res) => {\r\n            if (res.data.code == 0) {\r\n              that.$Message.info({\r\n                content: \"提示：操作成功\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              that.getAccountMessages();\r\n            }\r\n          });\r\n        } else {\r\n          that.errorTips(\"只有已退回的台账才能重新加入归集单\");\r\n        }\r\n      }\r\n    },\r\n    beforeLoadData(data, str) {\r\n      var cols = [],\r\n        keys = [];\r\n      for (var i = 0; i < this.accountTb.exportcolumns.length; i++) {\r\n        cols.push(this.accountTb.exportcolumns[i].title);\r\n        keys.push(this.accountTb.exportcolumns[i].key);\r\n      }\r\n      const params = {\r\n        title: cols,\r\n        key: keys,\r\n        data: data,\r\n        autoWidth: true,\r\n        filename: str,\r\n      };\r\n      excel.export_array_to_excel(params);\r\n      return;\r\n    },\r\n    exportCsv(name) {\r\n      this.setElectroyType();\r\n      let params = this.accountObj;\r\n\r\n      if (name === \"current\") {\r\n        params.pageNum = this.pageNum;\r\n        params.pageSize = this.pageSize;\r\n      } else if (name === \"all\") {\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n      }\r\n      let req = {\r\n        url: \"/business/account/exportzy\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      this.spinShow = true;\r\n      axios\r\n        .file(req)\r\n        .then((res) => {\r\n          this.spinShow = false;\r\n          const content = res;\r\n          const blob = new Blob([content]);\r\n          const fileName = \"自有台账导出数据\" + \".xlsx\";\r\n          if (\"download\" in document.createElement(\"a\")) {\r\n            // 非IE下载\r\n            const elink = document.createElement(\"a\");\r\n            elink.download = fileName;\r\n            elink.style.display = \"none\";\r\n            elink.href = URL.createObjectURL(blob);\r\n            document.body.appendChild(elink);\r\n            elink.click();\r\n            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n            document.body.removeChild(elink);\r\n          } else {\r\n            // IE10+下载\r\n            navigator.msSaveBlob(blob, fileName);\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //专票税额\r\n    settaxrate() {\r\n      let val = this.edittaxrate;\r\n      let data = this.insideData[this.editIndex];\r\n      let taxticketmoney = data.taxticketmoney;\r\n      data.taxrate = val;\r\n      data.taxamount = countTaxamount(data);\r\n      data.editType = 1;\r\n    },\r\n    startModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editStartDate;\r\n      data.startdate = val; //修改起始日期\r\n      data.editType = 1;\r\n      //计算定额\r\n      this.getQuota(data.ammeterid, data.startdate, data.enddate, (result) => {\r\n        if (result.data.code === 0) {\r\n          data.quotareadings = Math.round(result.data.msg);\r\n        } else {\r\n          data.quotareadings = 0;\r\n        }\r\n        this.calculateAll(data);\r\n      });\r\n      let opflag = data.opflag;\r\n      //修改opflag修改起日期 原来数字加\r\n      if (opflag != 4 && opflag != 6 && opflag != 7 && opflag != 9) {\r\n        data.opflag = opflag + 4;\r\n      }\r\n      data.remark += \"本期起始日期 从\" + data.old_startdate + \"修改为\" + val + \"; \";\r\n      this.startModal = false;\r\n      this.nextCell(this);\r\n    },\r\n    cancel() {\r\n      this.nextCell(this);\r\n    },\r\n    hcyzstartdate(lett, index) {\r\n      let data = lett.insideData[index];\r\n      let val = lett.editStartDate;\r\n      if (val) {\r\n        if (testNumber(val)) {\r\n          if (val == data.old_startdate) {\r\n            data.startdate = val;\r\n\r\n            lett.nextCell(lett);\r\n          } else {\r\n            lett.validate();\r\n          }\r\n        } else {\r\n          lett.errorTips(\"请输入数字！\");\r\n          lett.nextCell(lett);\r\n        }\r\n      } else {\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    qdModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editPrevtotalreadings;\r\n\r\n      data.prevtotalreadings = val;\r\n      let b = this.ifMaxdegree;\r\n      if (b === true) {\r\n        this.fbModal = true;\r\n      } else if (b === false) {\r\n        this.qxfbModal = true;\r\n      } else {\r\n        this.calculateAll(data);\r\n      }\r\n\r\n      data.editType = 1;\r\n      let opflag = data.opflag;\r\n      //增加4 修改起日期 原来数字加\r\n      if (opflag != 2 && opflag != 5 && opflag != 6 && opflag != 9) {\r\n        data.opflag = opflag + 2;\r\n      }\r\n      data.remark += \"本期起度 从\" + data.old_prevtotalreadings + \"修改为\" + val + \"; \";\r\n\r\n      this.qdModal = false;\r\n      this.nextCell(this);\r\n    },\r\n    fbModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      data.ifMaxdegree = true;\r\n      this.fbModal = false;\r\n\r\n      this.calculateAll(data);\r\n    },\r\n    qxfbModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      data.ifMaxdegree = false;\r\n      this.qxfbModal = false;\r\n\r\n      this.calculateAll(data);\r\n    },\r\n    hcyzprevtotalreadings(lett, index) {\r\n      let data = lett.insideData[index];\r\n      let val = lett.editPrevtotalreadings;\r\n      if (val) {\r\n        if (testNumber(val)) {\r\n          if (val == data.old_prevtotalreadings) {\r\n            data.prevtotalreadings = val;\r\n            lett.nextCell(lett);\r\n          } else {\r\n            lett.validate();\r\n          }\r\n        } else {\r\n          lett.errorTips(\"请输入数字！\");\r\n          lett.nextCell(lett);\r\n        }\r\n      } else {\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    qdcancel() {\r\n      if (this.columnsIndex === 4) {\r\n        let data = this.insideData[this.editIndex].old_prevtotalreadings;\r\n        this.editPrevtotalreadings = data;\r\n        this.insideData[this.editIndex].prevtotalreadings = data;\r\n\r\n        this.$refs[\"curtotalreadings\" + this.editIndex + this.columnsIndex].focus();\r\n      } else if (this.columnsIndex === 5) {\r\n        let data = this.insideData[this.editIndex].old_curtotalreadings;\r\n        this.editcurtotalreadings = data;\r\n        this.insideData[this.editIndex].curtotalreadings = data;\r\n\r\n        this.$refs[\"transformerullage\" + this.editIndex + this.columnsIndex].focus();\r\n      }\r\n    },\r\n    accountnoChange() {\r\n      this.searchList();\r\n    },\r\n    setElectroyType() {\r\n      let types = this.classifications;\r\n      this.accountObj.electrotype = types[types.length - 1];\r\n    },\r\n    self() {\r\n      var lett = this;\r\n      if (lett.startModal) {\r\n        lett.startModalOk();\r\n      } else if (lett.qdModal) {\r\n        lett.qdModalOk();\r\n      } else if (lett.fbModal) {\r\n        lett.fbModalOk();\r\n      } else if (lett.qxfbModal) {\r\n        lett.qxfbModalOk();\r\n      } else {\r\n        let index = lett.editIndex;\r\n        let columns = lett.columnsIndex;\r\n        if (index === -1 && columns === -1) {\r\n          index = 0;\r\n          columns = 1;\r\n          lett.editIndex = index;\r\n          lett.columnsIndex = columns;\r\n          lett.editStartDate = lett.insideData[index].startdate;\r\n          setTimeout(function () {\r\n            lett.$refs[lett.enterOperate(columns).str + index + columns].focus();\r\n          }, 200);\r\n        } else if (columns === 1) {\r\n          lett.hcyzstartdate(lett, index);\r\n        } else if (columns === 3) {\r\n          lett.hcyzprevtotalreadings(lett, index);\r\n        } else {\r\n          lett.validate();\r\n          lett.nextCell(lett);\r\n        }\r\n      }\r\n    },\r\n    handleFormatError(file) {\r\n      this.errorTips(file.name + \" 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件\");\r\n    },\r\n    handleProgress(event, file) {\r\n      this.$Message.info({\r\n        content: file.name + \" 正在上传。\",\r\n      });\r\n    },\r\n    handleUploadSuccess() {},\r\n    onExcelUpload(file) {\r\n      if (file.size > 1024 * 1024 * 5) {\r\n        this.errorTips(\"文件大小超过限制！\");\r\n        return;\r\n      }\r\n      if (!file) {\r\n        this.errorTips(\"请选择要上传的文件！\");\r\n        return;\r\n      }\r\n      let fileName = file.name.lastIndexOf(\".\"); //取到文件名开始到最后一个点的长度\r\n      let fileNameLength = file.name.length; //取到文件名长度\r\n      let fileFormat = file.name.substring(fileName + 1, fileNameLength); //截\r\n      if (\"xls\" != fileFormat && \"xlsx\" != fileFormat) {\r\n        return;\r\n      }\r\n      let param = { version: indexData.version };\r\n      let excel = { file: file };\r\n      let that = this;\r\n      that.spinShow = true;\r\n      axios\r\n        .request({\r\n          url: \"/business/account/uploadExcel\",\r\n          method: \"post\",\r\n          data: Object.assign({}, param, excel),\r\n        })\r\n        .then((res) => {\r\n          that.spinShow = false;\r\n          if (res.data.number > 0) {\r\n            that.$Message.info({\r\n              content: \"成功导入\" + res.data.number + \"条数据\",\r\n            });\r\n          } else {\r\n            that.errorTips(\"导入数据失败，请检查数据是否填写正确\");\r\n          }\r\n          if (res.data.list) {\r\n            that.export.run = true;\r\n            that.beforeLoadData(res.data.list, \"导入数据反馈\");\r\n            that.pageNum = 1;\r\n            that.getAccountMessages();\r\n          }\r\n        });\r\n      return false;\r\n    },\r\n    getDataFromModal(data, flag) {\r\n      this.chooseResponseCenter(data);\r\n    },\r\n    chooseResponseCenter(data) {\r\n      if (!data) {\r\n        if (!this.accountObj.company) {\r\n          this.errorTips(\"请选择所属分公司\");\r\n        }\r\n        if (!this.accountObj.country) {\r\n          this.errorTips(\"请选择所属部门\");\r\n        }\r\n        this.$refs.queryPeople.modal.params = {\r\n          deptId: this.accountObj.country,\r\n          copnId: this.accountObj.company,\r\n        }; // 当前部门和分公司\r\n        this.$refs.queryPeople.choose(); //人员\r\n      } else {\r\n        this.userName = data.name;\r\n        this.accountObj.userId = data.id;\r\n      }\r\n    },\r\n    ellipsis(row) {\r\n      let value = row.remark + row.bz;\r\n      if (!value) return \"\";\r\n      if (value.length > 3) {\r\n        return value.slice(0, 3) + \"...\";\r\n      }\r\n      return value;\r\n    },\r\n    uploadFile(row) {\r\n      this.$refs.uploadFileModal.choose(row.pcid + \"\");\r\n    },\r\n  },\r\n  mounted() {\r\n    this.handleHeight(); //table高度自定义\r\n\r\n    this.accountObj.version = indexData.version;\r\n    this.categorys = blist(\"ammeterCategory\");\r\n    this.directsupplyflags = blist(\"directSupplyFlag\");\r\n    getUser().then((res) => {\r\n      if (res.data.companies != null && res.data.companies.length > 0) {\r\n        this.CompanyList = res.data.companies;\r\n        this.companyListSize = res.data.companies.length;\r\n        //初始化默认展示登陆用户的第一个分公司和分公司下的部门\r\n        this.accountObj.company = this.CompanyList[0].id;\r\n        getDepartments(this.accountObj.company).then((res) => {\r\n          this.resCenterList = res.data;\r\n          this.resCenterListSize = res.data.length;\r\n          this.accountObj.country = res.data[0].id;\r\n\r\n          this.getAccountMessages();\r\n        });\r\n      }\r\n      this.accountTb.columns = this.accountTb.headColumn2\r\n        .concat(this.accountTb.scColumn)\r\n        .concat(this.accountTb.tailColumn);\r\n      //开放全省\r\n      this.accountTb.columns = this.accountTb.columns.concat(this.accountTb.fileColumn);\r\n    });\r\n\r\n    getClassification().then((res) => {\r\n      //用电类型\r\n      this.classificationData = res.data;\r\n    });\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      loginId: (state) => state.user.loginId,\r\n    }),\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.page-class {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  .cl-table {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n  .button-bar {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n.yjjh > .ivu-modal-wrap > .ivu-modal {\r\n  top: 20px !important;\r\n}\r\n\r\n.mytable .ivu-table-cell {\r\n  padding-left: 1px;\r\n  padding-right: 1px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: normal;\r\n  word-break: break-all;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.mytable .myspan {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n}\r\n\r\n.mytable .errorStle {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n  color: red;\r\n}\r\n\r\n.account .filter-divider {\r\n  margin: 0px;\r\n  text-align: center;\r\n}\r\n\r\n.account .header-bar-show {\r\n  max-height: 300px;\r\n  padding-top: 14px;\r\n  overflow: inherit;\r\n  border-bottom: 1px solid #e8eaec;\r\n}\r\n\r\n.account .header-bar-hide {\r\n  max-height: 0;\r\n  padding-top: 0;\r\n  overflow: hidden;\r\n  border-bottom: 0;\r\n}\r\n\r\n.mymodal p {\r\n  font-weight: bold;\r\n  font-size: 140%;\r\n  padding-left: 20px;\r\n}\r\n\r\n.account button {\r\n  margin-right: 10px;\r\n}\r\n::v-deep .cl-table .ivu-table-cell {\r\n  padding: 6px 4px !important;\r\n  .ivu-input {\r\n    padding: 4px !important;\r\n  }\r\n}\r\n</style>\r\n"], "sourceRoot": "src/view/account/homePageAccount"}]}