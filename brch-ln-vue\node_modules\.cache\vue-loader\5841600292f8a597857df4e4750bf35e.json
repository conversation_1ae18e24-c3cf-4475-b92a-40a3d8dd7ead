{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\editAmmeter.vue?vue&type=template&id=01daab64&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\editAmmeter.vue", "mtime": 1754285403018}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}