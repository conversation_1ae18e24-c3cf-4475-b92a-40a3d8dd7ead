{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\statistics\\energymeter\\list-station.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\statistics\\energymeter\\list-station.vue", "mtime": 1754285403055}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": 1741317456427}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["list-station.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGA,SAAA,iBAAA,QAAA,2BAAA;AACA,SAAA,cAAA,QAAA,wBAAA;AACA,OAAA,OAAA,MAAA,kBAAA;AACA,SAAA,KAAA,EAAA,KAAA,QAAA,cAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AACA,OAAA,SAAA,MAAA,cAAA;AAEA,eAAA;AACA,EAAA,MAAA,EAAA,CAAA,OAAA,CADA;AAEA,EAAA,KAAA,EAAA,CAAA,MAAA,CAFA;AAGA,EAAA,UAAA,EAAA;AAAA,IAAA,SAAA,EAAA;AAAA,GAHA;AAIA,EAAA,IAJA,kBAIA;AACA,WAAA;AACA;AACA,MAAA,UAAA,EAAA,IAFA;AAEA;AACA,MAAA,WAAA,EAAA;AACA;AACA,QAAA,OAAA,EAAA,IAFA;AAGA,QAAA,cAAA,EAAA,IAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OAHA;AASA,MAAA,aAAA,EAAA,EATA;AASA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,OAAA,EAAA,EADA;AACA;AACA,QAAA,cAAA,EAAA,EAFA;AAEA;AACA,QAAA,MAAA,EAAA,EAHA,CAGA;;AAHA,OAVA;AAeA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,OAAA,EAAA,KADA;AAEA,QAAA,QAAA,EAAA,IAFA;AAGA,QAAA,SAAA,EAAA,CAHA;AAIA,QAAA,UAAA,EAAA,CAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAKA;AACA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,GAAA,EAAA,SAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,KAAA,EAAA,GAJA;AAKA,UAAA,KAAA,EAAA;AALA,SADA;AANA,OAhBA;AAgCA,MAAA,SAAA,EAAA,EAhCA;AAiCA,MAAA,QAAA,EAAA;AAjCA,KAAA;AAmCA,GAxCA;AAyCA,EAAA,OAzCA,qBAyCA;AACA,SAAA,YAAA,GADA,CACA;;AACA,SAAA,YAAA;AACA,SAAA,QAAA,GAHA,CAGA;AACA,GA7CA;AA8CA,EAAA,OAAA,EAAA;AACA,IAAA,YADA,0BACA;AACA,UAAA,KAAA,GAAA,CACA;AACA,QAAA,IAAA,EAAA,WADA;AAEA,QAAA,GAAA,EAAA,yBAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OADA,EAMA;AACA,QAAA,IAAA,EAAA,WADA;AAEA,QAAA,GAAA,EAAA,yBAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OANA,EAWA;AACA,QAAA,IAAA,EAAA,qBADA;AAEA,QAAA,GAAA,EAAA,yBAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OAXA,EAgBA;AACA,QAAA,IAAA,EAAA,yBADA;AAEA,QAAA,GAAA,EAAA,yBAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OAhBA,EAqBA;AACA,QAAA,IAAA,EAAA,gBADA;AAEA,QAAA,GAAA,EAAA,yBAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OArBA,EA0BA;AACA,QAAA,IAAA,EAAA,SADA;AAEA,QAAA,GAAA,EAAA,yBAFA;AAGA,QAAA,WAAA,EAAA,CAAA;AAHA,OA1BA,EA+BA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,GAAA,EAAA,4BAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OA/BA,EAoCA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,GAAA,EAAA,4BAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OApCA,EAyCA;AACA,QAAA,IAAA,EAAA,UADA;AAEA,QAAA,GAAA,EAAA,4BAFA;AAGA,QAAA,WAAA,EAAA,CAAA;AAHA,OAzCA,CAAA;AA+CA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA;AACA,UAAA,KAAA,EAAA,IAAA,CAAA,IADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,QAAA,EAAA,CACA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,KAAA,EAAA,QAFA;AAGA,YAAA,KAAA,EAAA,GAHA;AAIA,YAAA,GAAA,YAAA,IAAA,CAAA,GAAA,CAJA;AAKA,YAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,kBAAA,MAAA,GAAA,MAAA,CAAA,MAAA,CAAA,GAAA;AACA,kBAAA,GAAA,GAAA,MAAA,CAAA,GAAA;AACA,kBAAA,IAAA,GAAA,CAAA,CACA,GADA,EAEA;AACA,gBAAA,KAAA,EAAA;AACA,kBAAA,KAAA,EAAA;AADA,iBADA;AAIA,gBAAA,EAAA,EAAA;AACA,kBAAA,KADA,mBACA;AACA,oBAAA,IAAA,CAAA,WAAA,CAAA,GAAA,EAAA,IAAA;AACA;AAHA;AAJA,eAFA,EAYA,GAAA,CAAA,MAAA,CAZA,CAAA;AAcA,kBAAA,KAAA,GAAA,CAAA,CAAA,MAAA,EAAA,EAAA,EAAA,GAAA,CAAA,MAAA,CAAA,CAAA;;AACA,kBAAA,GAAA,CAAA,KAAA,EAAA;AACA,uBAAA,CAAA,CAAA,KAAA,EAAA,CAAA,IAAA,CAAA,CAAA;AACA,eAFA,MAEA;AACA,uBAAA,CAAA,CAAA,KAAA,EAAA,CAAA,KAAA,CAAA,CAAA;AACA;AACA;AA5BA,WADA,EA+BA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,KAAA,EAAA,QAFA;AAGA,YAAA,KAAA,EAAA,GAHA;AAIA,YAAA,GAAA,YAAA,IAAA,CAAA,GAAA;AAJA,WA/BA;AAHA,SAAA;AA0CA,OA3CA;AA4CA,WAAA,QAAA,CAAA,OAAA,GAAA,KAAA,QAAA,CAAA,OAAA,CAAA,MAAA,CAAA,GAAA,CAAA;AACA,KAhGA;AAiGA;AACA,IAAA,WAlGA,uBAkGA,GAlGA,EAkGA,IAlGA,EAkGA;AACA,UAAA,MAAA,GAAA;AACA,QAAA,OAAA,EAAA,GAAA,CAAA,KADA;AAEA,QAAA,cAAA,EAAA,KAAA,aAAA,CAAA,cAFA;AAGA,QAAA,MAAA,EAAA,KAAA,aAAA,CAAA,MAHA;AAIA,QAAA,WAAA,EAAA,IAAA,CAAA;AAJA,OAAA;AAMA,WAAA,KAAA,CAAA,MAAA,EAAA,SAAA,CAAA,MAAA;AACA,KA1GA;AA2GA;AACA,IAAA,cA5GA,4BA4GA;AACA,WAAA,KAAA,CAAA,WAAA,EAAA,WAAA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,KAAA,KAAA,CAAA,OAAA,CAAA,CAAA,KAAA,KAAA,KAAA,CAAA,OAAA,CAAA,CAAA,EAAA,EAAA;;AACA,WAAA,eAAA,GAHA,CAGA;;AACA,KAhHA;AAiHA;AACA,IAAA,eAlHA,6BAkHA;AACA,WAAA,aAAA,qBAAA,KAAA,WAAA;AACA,WAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,KAAA,aAAA;AACA,KArHA;AAsHA;AACA,IAAA,UAvHA,sBAuHA,MAvHA,EAuHA;AAAA;;AACA,MAAA,MAAA,CAAA,MAAA,CAAA,MAAA,EAAA;AACA,QAAA,OAAA,EAAA,MAAA,CAAA,OAAA,IAAA,IAAA,GAAA,EAAA,GAAA,MAAA,CAAA,OADA;AAEA,QAAA,UAAA,EAAA,MAAA,CAAA;AAFA,OAAA;AAIA,aAAA,MAAA,CAAA,OAAA;AACA,WAAA,QAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,cAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,OAAA,GAAA,KAAA;AACA,YAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,SAAA,GAAA,IAAA;AACA,OALA;AAMA,KApIA;AAqIA;AACA,IAAA,SAtIA,uBAsIA;AAAA;;AACA,UAAA,KAAA,QAAA,CAAA,KAAA,IAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA;AACA;;AACA,WAAA,aAAA;AACA,UAAA,MAAA,GAAA,KAAA,KAAA,CAAA,OAAA,CAAA,iBAAA;AACA,MAAA,MAAA,CAAA,UAAA,GAAA,CAAA;AACA,MAAA,MAAA,CAAA,QAAA,GAAA,KAAA,QAAA,CAAA,KAAA;AACA,MAAA,KAAA,CACA,IADA,CACA;AACA,QAAA,GAAA,EAAA,uCADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OADA,EAMA,IANA,CAMA,UAAA,GAAA,EAAA;AACA,YAAA,OAAA,GAAA,GAAA;AACA,YAAA,IAAA,GAAA,IAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AAEA,YAAA,QAAA,2DAAA;;AACA,YAAA,cAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA,EAAA;AACA;AACA,cAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA,QAAA;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,UAAA,KAAA,CAAA,IAAA,GAAA,GAAA,CAAA,eAAA,CAAA,IAAA,CAAA;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,UAAA,KAAA,CAAA,KAAA;AACA,UAAA,GAAA,CAAA,eAAA,CAAA,KAAA,CAAA,IAAA,EARA,CAQA;;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,SAVA,MAUA;AACA;AACA,UAAA,SAAA,CAAA,UAAA,CAAA,IAAA,EAAA,QAAA;AACA;;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,IAAA;AACA,OA1BA;AA2BA,KA1KA;AA2KA,IAAA,aA3KA,2BA2KA;AACA,WAAA,KAAA,CAAA,IAAA,CAAA;AACA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA;AACA,iBAAA,CAAA,CAAA,KAAA,EAAA,CACA,CAAA,CAAA,UAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,KAAA,EAAA;AADA;AADA,WAAA,CADA,EAMA,CAAA,CAAA,KAAA,EAAA,kBAAA,CANA,CAAA,CAAA;AAQA;AAVA,OAAA;AAYA,KAxLA;AAyLA;AACA,IAAA,QA1LA,sBA0LA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,GAAA,KAAA,CAAA,QAAA,CAAA,CADA,CACA;;AACA,WAAA,KAAA,CAAA,cAAA,GAAA,CACA;AACA,QAAA,QAAA,EAAA,GADA;AAEA,QAAA,QAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,QAAA,EAAA,GADA;AAEA,QAAA,QAAA,EAAA;AAFA,OALA,CAAA,CAFA,CAWA;AAEA;;AACA,MAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA,wBACA,GAAA,CAAA,IADA;AAAA,YACA,SADA,aACA,SADA;AAAA,YACA,UADA,aACA,UADA;;AAEA,YAAA,UAAA,EAAA;AACA,UAAA,SAAA,CAAA,OAAA,CAAA;AACA,YAAA,IAAA,EAAA,IADA;AAEA,YAAA,EAAA,EAAA;AAFA,WAAA;AAIA;;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,OAAA,GAAA,SAAA;;AACA,QAAA,MAAA,CAAA,cAAA,GATA,CASA;;AACA,OAVA;AAWA;AAnNA;AA9CA,CAAA", "sourcesContent": ["<template>\r\n  <!-- 电表/局站分析报表 -->\r\n  <div class=\"page-class page-card common-wh\">\r\n    <div class=\"query-box\">\r\n      <Row :class=\"filterColl ? 'header-bar-show' : 'header-bar-hide'\">\r\n        <Form ref=\"queryform\" :model=\"queryParams\" :label-width=\"120\">\r\n          <Row class=\"form-row\">\r\n            <Col span=\"5\">\r\n              <FormItem label=\"数据部门:\" prop=\"company\">\r\n                <Select v-model=\"queryParams.company\" clearable>\r\n                  <Option\r\n                    v-for=\"(item, index) in dicts['company']\"\r\n                    :key=\"index\"\r\n                    :value=\"item.id\"\r\n                    >{{ item.name }}</Option\r\n                  >\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"5\">\r\n              <FormItem label=\"状态:\" prop=\"status\">\r\n                <Select v-model=\"queryParams.status\" clearable>\r\n                  <Option\r\n                    v-for=\"(item, index) in dicts['status']\"\r\n                    :key=\"index\"\r\n                    :value=\"item.typeCode\"\r\n                    >{{ item.typeName }}</Option\r\n                  >\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"5\">\r\n              <FormItem label=\"是否大工业用电:\" prop=\"isbigfactories\">\r\n                <Select v-model=\"queryParams.isbigfactories\" clearable>\r\n                  <Option\r\n                    v-for=\"(item, index) in dicts['isbigfactories']\"\r\n                    :key=\"index\"\r\n                    :value=\"item.typeCode\"\r\n                    >{{ item.typeName }}</Option\r\n                  >\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <div style=\"float: right; margin-right: 10px\">\r\n              <Button\r\n                style=\"margin-left: 5px; width: 69px\"\r\n                type=\"success\"\r\n                icon=\"ios-search\"\r\n                @click=\"_onSearchHandle\"\r\n                >搜索\r\n              </Button>\r\n              <Button\r\n                style=\"margin-left: 5px; width: 69px\"\r\n                type=\"info\"\r\n                icon=\"ios-redo\"\r\n                @click=\"_onResetHandle\"\r\n                >重置</Button\r\n              >\r\n            </div>\r\n          </Row>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon\r\n          :type=\"filterColl ? 'md-arrow-dropup' : 'md-arrow-dropdown'\"\r\n          size=\"20\"\r\n          @click=\"filterColl = !filterColl\"\r\n          :color=\"filterColl ? '#000' : '#1ab394'\"\r\n        ></icon>\r\n      </div>\r\n    </div>\r\n    <cl-table\r\n      ref=\"clTable\"\r\n      title=\"列表\"\r\n      :height=\"tableHeight\"\r\n      :query-params=\"queryParams\"\r\n      :columns=\"tableSet.columns\"\r\n      :loading=\"tableSet.loading\"\r\n      :total=\"tableSet.total\"\r\n      :pageSize=\"tableSet.pageSize\"\r\n      :showPage=\"tableSet.showPage\"\r\n      :data=\"tableList\"\r\n      :sum-columns=\"[]\"\r\n      @on-query=\"tableQuery\"\r\n      :searchable=\"false\"\r\n      :exportable=\"false\"\r\n    >\r\n      <div slot=\"buttons\" class=\"table-btns\">\r\n        <Button type=\"default\" @click=\"exportCsv\">导出</Button>\r\n      </div>\r\n    </cl-table>\r\n    <!-- 弹窗：明细 -->\r\n    <modal-list title=\"局站明细\" ref=\"list\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { getUserByUserRole } from \"@/api/basedata/ammeter.js\";\r\nimport { getStationList } from \"@/api/statistics/index\";\r\nimport pageFun from \"@/mixins/pageFun\";\r\nimport { blist, btext } from \"@/libs/tools\";\r\nimport axios from \"@/libs/api.request\";\r\nimport modalList from \"./modal-list\";\r\n\r\nexport default {\r\n  mixins: [pageFun],\r\n  props: [\"flag\"],\r\n  components: { modalList },\r\n  data() {\r\n    return {\r\n      //搜索面板\r\n      filterColl: true, //搜索面板展开\r\n      queryParams: {\r\n        //查询参数\r\n        company: null,\r\n        isbigfactories: null,\r\n        status: null,\r\n      },\r\n      queryedParams: {}, //搜索后的参数\r\n      dicts: {\r\n        company: [], //数据部门\r\n        isbigfactories: [], //是否大工业用电\r\n        status: [], //状态\r\n      },\r\n      //--搜索面板end--\r\n      tableSet: {\r\n        loading: false,\r\n        showPage: true,\r\n        pageTotal: 0,\r\n        pageNumber: 1,\r\n        pageSize: 10, //当前页\r\n        columns: [\r\n          {\r\n            title: \"地市\",\r\n            key: \"orgName\",\r\n            fixed: \"left\",\r\n            width: 150,\r\n            align: \"center\",\r\n          },\r\n        ],\r\n      },\r\n      tableList: [],\r\n      spinShow: false,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.handleHeight(); //table高度自定义\r\n    this.handleColumn();\r\n    this.getDicts(); //部门下拉\r\n  },\r\n  methods: {\r\n    handleColumn() {\r\n      let dicts = [\r\n        {\r\n          name: \"生产用房-通信机房\",\r\n          key: \"productionBuilding10001\",\r\n          stationtype: 10001,\r\n        },\r\n        {\r\n          name: \"生产用房-移动基站\",\r\n          key: \"productionBuilding10002\",\r\n          stationtype: 10002,\r\n        },\r\n        {\r\n          name: \"生产用房-数据中心-对外IDC机柜机房\",\r\n          key: \"productionBuilding10003\",\r\n          stationtype: 10003,\r\n        },\r\n        {\r\n          name: \"生产用房-数据中心-自用业务平台和IT支撑用房\",\r\n          key: \"productionBuilding10004\",\r\n          stationtype: 10004,\r\n        },\r\n        {\r\n          name: \"生产用房-接入局所及室外机柜\",\r\n          key: \"productionBuilding10005\",\r\n          stationtype: 10005,\r\n        },\r\n        {\r\n          name: \"生产用房-其他\",\r\n          key: \"productionBuildingOther\",\r\n          stationtype: -1,\r\n        },\r\n        {\r\n          name: \"非生产用房-管理用房\",\r\n          key: \"nonProductionBuilding20001\",\r\n          stationtype: 20001,\r\n        },\r\n        {\r\n          name: \"非生产用房-渠道用房\",\r\n          key: \"nonProductionBuilding20002\",\r\n          stationtype: 20002,\r\n        },\r\n        {\r\n          name: \"非生产用房-其他\",\r\n          key: \"nonProductionBuildingOther\",\r\n          stationtype: -2,\r\n        },\r\n      ];\r\n      let arr = [];\r\n      let that = this;\r\n      dicts.forEach((item) => {\r\n        arr.push({\r\n          title: item.name,\r\n          align: \"center\",\r\n          children: [\r\n            {\r\n              title: \"数量\",\r\n              align: \"center\",\r\n              width: 130,\r\n              key: `${item.key}`,\r\n              render: (h, params) => {\r\n                let column = params.column.key;\r\n                let row = params.row;\r\n                let info = h(\r\n                  \"u\",\r\n                  {\r\n                    style: {\r\n                      color: \"green\",\r\n                    },\r\n                    on: {\r\n                      click() {\r\n                        that.toOpenModal(row, item);\r\n                      },\r\n                    },\r\n                  },\r\n                  row[column]\r\n                );\r\n                let info2 = h(\"span\", {}, row[column]);\r\n                if (row.orgId) {\r\n                  return h(\"div\", [info]);\r\n                } else {\r\n                  return h(\"div\", [info2]);\r\n                }\r\n              },\r\n            },\r\n            {\r\n              title: \"占比\",\r\n              align: \"center\",\r\n              width: 100,\r\n              key: `${item.key}Percentage`,\r\n            },\r\n          ],\r\n        });\r\n      });\r\n      this.tableSet.columns = this.tableSet.columns.concat(arr);\r\n    },\r\n    //打开弹窗\r\n    toOpenModal(row, item) {\r\n      let params = {\r\n        company: row.orgId,\r\n        isbigfactories: this.queryedParams.isbigfactories,\r\n        status: this.queryedParams.status,\r\n        stationtype: item.stationtype,\r\n      };\r\n      this.$refs[\"list\"].openModal(params);\r\n    },\r\n    //表格-筛选-重置\r\n    _onResetHandle() {\r\n      this.$refs[\"queryform\"].resetFields();\r\n      this.queryParams.company = this.dicts.company[0] && this.dicts.company[0].id;\r\n      this._onSearchHandle(); //搜索列表\r\n    },\r\n    //表格-筛选-搜索\r\n    _onSearchHandle() {\r\n      this.queryedParams = { ...this.queryParams };\r\n      this.$refs.clTable.query(this.queryedParams);\r\n    },\r\n    //查询\r\n    tableQuery(params) {\r\n      Object.assign(params, {\r\n        company: params.company == \"-1\" ? \"\" : params.company,\r\n        pageNumber: params.pageNum,\r\n      });\r\n      delete params.pageNum;\r\n      this.tableSet.loading = true;\r\n      getStationList(params).then((res) => {\r\n        this.tableSet.loading = false;\r\n        let data = res.data.rows;\r\n        this.tableSet.total = res.data.total;\r\n        this.tableList = data;\r\n      });\r\n    },\r\n    //导出\r\n    exportCsv() {\r\n      if (this.tableSet.total == 0) {\r\n        this.$Message.warning(\"暂无数据可导出\");\r\n        return;\r\n      }\r\n      this.exportLoading();\r\n      let params = this.$refs.clTable.insideQueryParams;\r\n      params.pageNumber = 1;\r\n      params.pageSize = this.tableSet.total;\r\n      axios\r\n        .file({\r\n          url: \"/business/cost/extAndTransElec/export\",\r\n          method: \"get\",\r\n          params: params,\r\n        })\r\n        .then((res) => {\r\n          const content = res;\r\n          const blob = new Blob([content]);\r\n\r\n          let fileName = `电表/局站分析报表.xlsx`;\r\n          if (\"download\" in document.createElement(\"a\")) {\r\n            // 非IE下载\r\n            const elink = document.createElement(\"a\");\r\n            elink.download = fileName;\r\n            elink.style.display = \"none\";\r\n            elink.href = URL.createObjectURL(blob);\r\n            document.body.appendChild(elink);\r\n            elink.click();\r\n            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n            document.body.removeChild(elink);\r\n          } else {\r\n            // IE10+下载\r\n            navigator.msSaveBlob(blob, fileName);\r\n          }\r\n          this.$Spin.hide();\r\n        });\r\n    },\r\n    exportLoading() {\r\n      this.$Spin.show({\r\n        render: (h) => {\r\n          return h(\"div\", [\r\n            h(\"Progress\", {\r\n              style: {\r\n                width: \"800px\",\r\n              },\r\n            }),\r\n            h(\"div\", \"导出中，请勿刷新页面......\"),\r\n          ]);\r\n        },\r\n      });\r\n    },\r\n    //下拉选项\r\n    getDicts() {\r\n      this.dicts.status = blist(\"status\"); //状态\r\n      this.dicts.isbigfactories = [\r\n        {\r\n          typeCode: \"1\",\r\n          typeName: \"是\",\r\n        },\r\n        {\r\n          typeCode: \"0\",\r\n          typeName: \"否\",\r\n        },\r\n      ]; //是否大工业用电\r\n\r\n      //数据部门/所属分公司\r\n      getUserByUserRole().then((res) => {\r\n        let { companies, isProAdmin } = res.data;\r\n        if (isProAdmin) {\r\n          companies.unshift({\r\n            name: \"全省\",\r\n            id: \"-1\",\r\n          });\r\n        }\r\n        this.dicts.company = companies;\r\n        this._onResetHandle(); //搜索列表\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.page-class {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/view/statistics/energymeter"}]}