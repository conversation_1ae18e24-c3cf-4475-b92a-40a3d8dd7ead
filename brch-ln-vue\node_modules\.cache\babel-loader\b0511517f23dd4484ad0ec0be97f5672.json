{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\check\\cityAccountCheckList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\check\\cityAccountCheckList.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON><PERSON>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"}, {"version": 3, "sources": ["cityAccountCheckList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FA,OAAA,KAAA,MAAA,wCAAA;AACA,OAAA,YAAA,MAAA,sCAAA;AACA,OAAA,MAAA,MAAA,gBAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AACA,SAAA,KAAA,QAAA,cAAA;AACA,OAAA,SAAA,MAAA,wBAAA;AACA,OAAA,GAAA,MAAA,iBAAA;AACA,SAAA,iBAAA,EAAA,kBAAA,EAAA,eAAA,EAAA,aAAA,EAAA,cAAA,EAAA,WAAA,QAAA,2BAAA;AACA,SAAA,UAAA,QAAA,mDAAA;AACA,eAAA;AACA,EAAA,IAAA,EAAA,sBADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,KAAA,EAAA,KAAA;AAAA,IAAA,YAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AAAA;;AACA,QAAA,WAAA,GAAA,SAAA,WAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,GAAA,GAAA,MAAA,CAAA,GAAA;AACA,UAAA,IAAA,GAAA,KAAA;;AACA,UAAA,GAAA,CAAA,IAAA,IAAA,IAAA,EAAA;AACA,eAAA,CAAA,CAAA,OAAA,EAAA;AACA,UAAA,KAAA,EAAA;AAEA,YAAA,IAAA,EAAA,aAFA;AAGA,YAAA,WAAA,EAAA,QAHA;AAIA,YAAA,QAAA,EAAA;AAJA,WADA;AAOA,UAAA,EAAA,EAAA;AACA,wBAAA,mBAAA;AACA,cAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,KAAA,IAAA,GAAA;;AACA,cAAA,KAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,MAAA;AACA;AAJA;AAPA,SAAA,CAAA;AAcA,OAfA,MAeA;AACA,eAAA,CAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA;AACA,KArBA;;AAsBA,QAAA,YAAA,GAAA,SAAA,YAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,KAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,6BAAA,KAAA,CAAA,UAAA,8HAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,UAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA,WAHA,MAIA,CAEA;AACA;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAWA,aAAA,CAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,KAZA;;AAaA,QAAA,OAAA,GAAA,SAAA,OAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA;AACA,UAAA,IAAA;AAAA,UAAA,IAAA,GAAA,EAAA;;AACA,UAAA,MAAA,CAAA,GAAA,CAAA,MAAA,EAAA;AACA,QAAA,IAAA,GAAA,IAAA;AACA,QAAA,IAAA,GAAA,SAAA;AACA,OAHA,MAGA;AACA,QAAA,IAAA,GAAA,IAAA;AACA,QAAA,IAAA,GAAA,SAAA;AACA;;AACA,aAAA,CAAA,CAAA,QAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,IAAA,EAAA,IADA;AACA,UAAA,IAAA,EAAA;AADA,SADA;AAGA,QAAA,EAAA,EAAA;AACA,UAAA,KADA,mBACA;AACA,gBAAA,MAAA,CAAA,GAAA,CAAA,aAAA,EAAA;AACA,cAAA,IAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,EAAA,EAAA,MAAA,CAAA,GAAA,CAAA,aAAA,EAAA,MAAA,CAAA,GAAA,CAAA,WAAA;AACA,aAFA,MAEA;AACA,kBAAA,IAAA,CAAA,aAAA,IAAA,MAAA,CAAA,GAAA,CAAA,aAAA,EAAA;AACA;AACA,gBAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA,gBAAA,gBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,IAAA,CAAA,OAAA,GAAA,KAAA;;AACA,sBAAA,GAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,oBAAA,IAAA,CAAA,SAAA,CAAA,MAAA,CAAA,GAAA,CAAA,EAAA,EAAA,MAAA,CAAA,GAAA,CAAA,aAAA,EAAA,MAAA,CAAA,GAAA,CAAA,WAAA;AACA,mBAFA,MAEA;AACA,oBAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA;AACA,sBAAA,QAAA,EAAA,CADA;AAEA,sBAAA,KAAA,EAAA,UAAA,MAAA,CAAA,GAAA,CAAA,aAFA;AAGA,sBAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA;AAHA,qBAAA;AAKA;AACA,iBAXA;AAYA,eAfA,MAgBA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,QAAA,MAAA,CAAA,GAAA,CAAA,UAAA,GAAA,MAAA;AACA;AACA;AAvBA;AAHA,OAAA,EA4BA,IA5BA,CAAA;AA6BA,KAvCA;;AAwCA,WAAA;AACA,MAAA,iBAAA,EAAA,EADA;AAEA,MAAA,YAAA,EAAA,EAFA;AAGA,MAAA,gBAAA,EAAA,EAHA;AAIA,MAAA,YAAA,EAAA,KAJA;AAKA,MAAA,YAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,KADA;AAEA,QAAA,GAAA,EAAA;AAFA,OADA,CALA;AAeA,MAAA,aAAA,EAAA,UAfA;AAgBA,MAAA,YAAA,EAAA,CAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,CAhBA;AAiBA,MAAA,QAAA,EAAA,IAjBA;AAkBA,MAAA,OAAA,EAAA,MAAA,CAAA,OAlBA;AAmBA,MAAA,GAAA,EAAA,6CAnBA;AAoBA,MAAA,WAAA,EAAA;AAAA,QAAA,OAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA,QAAA,WAAA,EAAA,EAAA;AAAA,QAAA,YAAA,EAAA,EAAA;AAAA,QAAA,IAAA,EAAA,EAAA;AAAA,QAAA,OAAA,EAAA;AAAA,OApBA;AAqBA,MAAA,MAAA,EAAA,EArBA;AAsBA,MAAA,UAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA,MAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,GAAA,EAAA,OAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA,kBAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,WAAA;AAAA,QAAA,GAAA,EAAA,MAAA;AAAA,QAAA,QAAA,EAAA,GAAA;AACA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,iBAAA,CAAA,CAAA,MAAA,EAAA;AACA,qBAAA;AACA,uBAAA;AADA,aADA;AAIA,YAAA,EAAA,EAAA;AACA,cAAA,KAAA,EAAA,iBAAA;AACA,gBAAA,KAAA,CAAA,UAAA,CAAA,SAAA,EAAA,MAAA,CAAA,GAAA,EAAA,SAAA;AACA;AAHA;AAJA,WAAA,EASA,MAAA,CAAA,GAAA,CAAA,IATA,CAAA;AAUA;AAZA,OAJA,EAkBA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,KAAA,EAAA,QAAA;AACA,QAAA,QAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,WAAA;AAAA,UAAA,GAAA,EAAA,gBAAA;AAAA,UAAA,QAAA,EAAA,GAAA;AACA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,mBAAA,CAAA,CAAA,MAAA,EAAA;AACA,uBAAA;AACA,yBAAA,MAAA,CAAA,GAAA,CAAA,cAAA,IAAA,GAAA,GAAA,SAAA,GAAA;AADA,eADA;AAIA,cAAA,EAAA,EAAA;AACA,gBAAA,KAAA,EAAA,iBAAA;AACA,sBAAA,MAAA,CAAA,GAAA,CAAA,cAAA,IAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,UAAA,CAAA,WAAA,EAAA,MAAA,CAAA,GAAA,EAAA,WAAA;AACA;AACA;AALA;AAJA,aAAA,EAWA,MAAA,CAAA,GAAA,CAAA,cAXA,CAAA;AAYA;AAdA,SADA,EAiBA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,GAAA,EAAA,mBAAA;AAAA,UAAA,QAAA,EAAA,GAAA;AACA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,mBAAA,CAAA,CAAA,MAAA,EAAA;AACA,uBAAA;AACA,yBAAA,MAAA,CAAA,GAAA,CAAA,iBAAA,IAAA,GAAA,GAAA,SAAA,GAAA;AADA,eADA;AAIA,cAAA,EAAA,EAAA;AACA,gBAAA,KAAA,EAAA,iBAAA;AACA,sBAAA,MAAA,CAAA,GAAA,CAAA,iBAAA,IAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,UAAA,CAAA,OAAA,EAAA,MAAA,CAAA,GAAA,EAAA,OAAA;AACA;AACA;AALA;AAJA,aAAA,EAWA,MAAA,CAAA,GAAA,CAAA,iBAXA,CAAA;AAYA;AAdA,SAjBA,EAiCA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA,oBAAA;AAAA,UAAA,QAAA,EAAA,GAAA;AACA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,mBAAA,CAAA,CAAA,MAAA,EAAA;AACA,uBAAA;AACA,yBAAA,MAAA,CAAA,GAAA,CAAA,kBAAA,IAAA,GAAA,GAAA,SAAA,GAAA;AADA,eADA;AAIA,cAAA,EAAA,EAAA;AACA,gBAAA,KAAA,EAAA,iBAAA;AACA,sBAAA,MAAA,CAAA,GAAA,CAAA,kBAAA,IAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,UAAA,CAAA,SAAA,EAAA,MAAA,CAAA,GAAA,EAAA,SAAA;AACA;AACA;AALA;AAJA,aAAA,EAWA,MAAA,CAAA,GAAA,CAAA,kBAXA,CAAA;AAYA;AAdA,SAjCA,EAiDA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA,iBAAA;AAAA,UAAA,QAAA,EAAA,GAAA;AACA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,mBAAA,CAAA,CAAA,MAAA,EAAA;AACA,uBAAA;AACA,yBAAA,MAAA,CAAA,GAAA,CAAA,eAAA,IAAA,GAAA,GAAA,SAAA,GAAA;AADA,eADA;AAIA,cAAA,EAAA,EAAA;AACA,gBAAA,KAAA,EAAA,iBAAA;AACA,sBAAA,MAAA,CAAA,GAAA,CAAA,eAAA,IAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,UAAA,CAAA,SAAA,EAAA,MAAA,CAAA,GAAA,EAAA,SAAA;AACA;AACA;AALA;AAJA,aAAA,EAWA,MAAA,CAAA,GAAA,CAAA,eAXA,CAAA;AAYA;AAdA,SAjDA,EAiEA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA,uBAAA;AAAA,UAAA,QAAA,EAAA,GAAA;AACA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,mBAAA,CAAA,CAAA,MAAA,EAAA;AACA,uBAAA;AACA,yBAAA,MAAA,CAAA,GAAA,CAAA,qBAAA,IAAA,GAAA,GAAA,SAAA,GAAA;AADA,eADA;AAIA,cAAA,EAAA,EAAA;AACA,gBAAA,KAAA,EAAA,iBAAA;AACA,sBAAA,MAAA,CAAA,GAAA,CAAA,qBAAA,IAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,UAAA,CAAA,SAAA,EAAA,MAAA,CAAA,GAAA,EAAA,SAAA;AACA;AACA;AALA;AAJA,aAAA,EAWA,MAAA,CAAA,GAAA,CAAA,qBAXA,CAAA;AAYA;AAdA,SAjEA,EAiFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,UAAA,KAAA,EAAA,mBAAA;AAAA,UAAA,GAAA,EAAA,qBAAA;AAAA,UAAA,QAAA,EAAA,GAAA;AACA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,mBAAA,CAAA,CAAA,MAAA,EAAA;AACA,uBAAA;AACA,yBAAA,MAAA,CAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,GAAA,SAAA,GAAA;AADA,eADA;AAIA,cAAA,EAAA,EAAA;AACA,gBAAA,KAAA,EAAA,iBAAA;AACA,sBAAA,MAAA,CAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,UAAA,CAAA,mBAAA,EAAA,MAAA,CAAA,GAAA,EAAA,mBAAA;AACA;AACA;AALA;AAJA,aAAA,EAWA,MAAA,CAAA,GAAA,CAAA,mBAXA,CAAA;AAYA;AAdA,SAjGA,EAiHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA,eAAA;AAAA,UAAA,QAAA,EAAA,GAAA;AACA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,mBAAA,CAAA,CAAA,MAAA,EAAA;AACA,uBAAA;AACA,yBAAA,MAAA,CAAA,GAAA,CAAA,aAAA,IAAA,GAAA,GAAA,SAAA,GAAA;AADA,eADA;AAIA,cAAA,EAAA,EAAA;AACA,gBAAA,KAAA,EAAA,iBAAA;AACA,sBAAA,MAAA,CAAA,GAAA,CAAA,aAAA,IAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,UAAA,CAAA,SAAA,EAAA,MAAA,CAAA,GAAA,EAAA,SAAA;AACA;AACA;AALA;AAJA,aAAA,EAWA,MAAA,CAAA,GAAA,CAAA,aAXA,CAAA;AAYA;AAdA,SA/HA,EA+IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA,oBAAA;AAAA,UAAA,QAAA,EAAA,GAAA;AACA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,mBAAA,CAAA,CAAA,MAAA,EAAA;AACA,uBAAA;AACA,yBAAA,MAAA,CAAA,GAAA,CAAA,kBAAA,IAAA,GAAA,GAAA,SAAA,GAAA;AADA,eADA;AAIA,cAAA,EAAA,EAAA;AACA,gBAAA,KAAA,EAAA,iBAAA;AACA,sBAAA,MAAA,CAAA,GAAA,CAAA,kBAAA,IAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,UAAA,CAAA,SAAA,EAAA,MAAA,CAAA,GAAA,EAAA,SAAA;AACA;AACA;AALA;AAJA,aAAA,EAWA,MAAA,CAAA,GAAA,CAAA,kBAXA,CAAA;AAYA;AAdA,SA/JA;AADA,OAlBA,CAtBA;AA2NA,MAAA,KAAA,EAAA,EA3NA;AA4NA,MAAA,MAAA,EAAA;AACA,QAAA,OAAA,EAAA,KADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,CAHA;AAIA,QAAA,OAAA,EAAA,CAJA;AAKA,QAAA,QAAA,EAAA;AALA,OA5NA;AAmOA,MAAA,SAAA,EAAA,EAnOA;AAoOA,MAAA,WAAA,EAAA,EApOA;AAqOA,MAAA,MAAA,EAAA,IArOA;AAsOA,MAAA,UAAA,EAAA,IAtOA;AAuOA,MAAA,OAAA,EAAA,KAvOA;AAwOA,MAAA,OAAA,EAAA,CAxOA;AAyOA,MAAA,UAAA,EAAA,EAzOA;AA0OA,MAAA,OAAA,EAAA,KA1OA;AA2OA,MAAA,UAAA,EAAA,KA3OA;AA2OA;AACA,MAAA,WAAA,EAAA,KA5OA;AA6OA,MAAA,UAAA,EAAA,KA7OA;AA8OA,MAAA,QAAA,EAAA,IA9OA;AA+OA,MAAA,MAAA,EAAA;AACA,QAAA,GAAA,EAAA,KADA;AACA;AACA,QAAA,IAAA,EAAA,EAFA;AAEA;AACA,QAAA,SAAA,EAAA,CAHA;AAGA;AACA,QAAA,WAAA,EAAA,CAJA;AAIA;AACA,QAAA,OAAA,EAAA,CALA;AAMA,QAAA,IAAA,EAAA;AANA,OA/OA;AAuPA,MAAA,aAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAJA,EAKA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OALA,EAMA;AAAA,QAAA,KAAA,EAAA,YAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OANA,EAOA;AAAA,QAAA,KAAA,EAAA,WAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAPA,EAQA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OARA,EASA;AAAA,QAAA,KAAA,EAAA,WAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OATA;AAvPA,KAAA;AAqQA,GApVA;AAqVA,EAAA,OAAA,EAAA;AACA,IAAA,MADA,oBACA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,OAAA,EAAA,cAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,YAAA,EAAA,mBAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,gBAAA,EAAA,uBAAA;AACA,WAAA,WAAA,CAAA,KAAA,GAAA,KAAA,KAAA,EAAA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,KAAA,YAAA;AACA,WAAA,WAAA,CAAA,WAAA,GAAA,KAAA,gBAAA;AACA,WAAA,WAAA,CAAA,YAAA,GAAA,KAAA,iBAAA;AACA,KAVA;AAWA,IAAA,KAXA,mBAWA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,EAAA,CADA,CAEA;;AACA,UAAA,IAAA,GAAA,IAAA,CAAA,WAAA,EAAA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,QAAA,KAAA,CAAA;AACA,MAAA,KAAA,GAAA,KAAA,GAAA,EAAA,GAAA,MAAA,KAAA,GAAA,KAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,QAAA,KAAA,KAAA,CAAA,QAAA,EAAA;AACA,aAAA,OAAA;AACA,KAnBA;AAoBA,IAAA,UApBA,sBAoBA,IApBA,EAoBA,KApBA,EAoBA,IApBA,EAoBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA;AACA,WAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,KAAA,CAAA,KAAA,CAAA,QAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,KAAA,CAAA,QAAA,GAAA,KAAA,CAAA,QAAA;AACA,WAAA,KAAA,CAAA,KAAA,CAAA,eAAA,GAAA,KAAA,CAAA,mBAAA;AACA,WAAA,KAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,KAAA,CAAA,gBAAA;AACA,WAAA,KAAA,CAAA,KAAA,CAAA,QAAA,GAAA,KAAA,CAAA,IAAA;AACA,WAAA,KAAA,CAAA,KAAA,CAAA,QAAA,GAAA,KAAA,CAAA,QAAA;AACA,WAAA,KAAA,CAAA,KAAA,CAAA,KAAA,GAAA,KAAA,CAAA,KAAA;AACA,WAAA,KAAA,CAAA,KAAA,CAAA,UAAA,GAAA,IAAA;AACA,WAAA,KAAA,CAAA,KAAA,CAAA,UAAA,GAAA,IAAA;AACA,WAAA,KAAA,CAAA,KAAA,CAAA,UAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA;AACA,KAnCA;AAoCA,IAAA,UApCA,wBAoCA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KAtCA;AAuCA,IAAA,gBAvCA,4BAuCA,IAvCA,EAuCA;AACA;AACA,WAAA,WAAA,CAAA,WAAA,GAAA,IAAA,CAAA,IAAA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,WAAA,CAAA,YAAA,GAAA,IAAA,CAAA,IAAA;AACA,WAAA,IAAA,CAAA,KAAA,WAAA,EAAA,aAAA,EAAA,IAAA,CAAA,IAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,WAAA,CAAA,WAAA,EANA,CAOA;AACA;AACA;AACA,KAjDA;AAkDA,IAAA,YAlDA,wBAkDA,KAlDA,EAkDA;AAAA;;AACA,UAAA,KAAA,WAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,QAAA,kBAAA,CAAA,KAAA,WAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CADA,CAEA;AACA;AACA;AACA;AACA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA;AACA,SAVA;AAWA,OAbA,CAcA;;;AACA,UAAA,KAAA,IAAA,CAAA,CAAA,EAAA;AACA,aAAA,WAAA,CAAA,OAAA,GAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,aAAA,WAAA,CAAA,YAAA,GAAA,IAAA;AACA;AACA,KAtEA;AAuEA;AACA,IAAA,OAxEA,qBAwEA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA,CADA,CAEA;;AACA,UAAA,MAAA,GAAA,EAAA;AAGA,MAAA,MAAA,CAAA,OAAA,GAAA,KAAA,MAAA,CAAA,OAAA;AACA,MAAA,MAAA,CAAA,QAAA,GAAA,KAAA,MAAA,CAAA,QAAA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,KAAA,IADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA;AAKA,WAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,IAAA,GAAA,IAAA;AACA,QAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;;AACA,QAAA,MAAA,CAAA,KAAA;AACA,OALA,EAKA,KALA,CAKA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OAPA;AAQA,KA9FA;AA+FA,IAAA,KA/FA,mBA+FA;AACA,WAAA,SAAA;;AACA,UAAA,KAAA,WAAA,CAAA,YAAA,IAAA,EAAA,EAAA;AACA,aAAA,WAAA,CAAA,OAAA,GAAA,EAAA;AACA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,QAAA,EAAA,KAAA,WAAA,CAAA,OADA;AAEA,QAAA,gBAAA,EAAA,KAAA,WAAA,CAAA,YAFA;AAGA,QAAA,mBAAA,EAAA,KAAA,WAAA,CAAA,OAHA;AAIA,QAAA,QAAA,EAAA,KAAA,WAAA,CAAA,QAJA;AAKA,QAAA,KAAA,EAAA,KAAA,WAAA,CAAA;AALA,OAAA;AAOA,WAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,MAAA;AACA,KA5GA;AA6GA,IAAA,SA7GA,uBA6GA;AACA,WAAA,MAAA,GAAA,KAAA,UAAA;AACA,KA/GA;AAgHA,IAAA,aAhHA,2BAgHA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,KAAA,WAAA,CAAA,OAAA,IAAA,IAAA,IAAA,KAAA,WAAA,CAAA,OAAA,GAAA,CAAA,EACA;AAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA,mCAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,cAAA,CAAA,IAAA,CAAA,WAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA,CAAA;AAEA,aAFA;AAGA;AAPA,SAAA;AASA,OAXA,MAaA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,YAAA;AACA;AACA,KAlIA;AAmIA,IAAA,oBAnIA,kCAmIA;AACA,UAAA,KAAA,WAAA,CAAA,OAAA,IAAA,IAAA,IAAA,KAAA,WAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AACA;AACA;;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,KAAA,WAAA,CAAA,OAAA,EALA,CAKA;AACA,KAzIA;AA0IA,IAAA,0BA1IA,sCA0IA,IA1IA,EA0IA;AACA,WAAA,MAAA,CAAA,IAAA,CAAA,KAAA,WAAA,EAAA,EAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,MAAA,CAAA,IAAA,CAAA,KAAA,WAAA,EAAA,kBAAA,GAAA,IAAA,CAAA,MAAA;AACA,WAAA,MAAA,CAAA,IAAA,CAAA,KAAA,WAAA,EAAA,kBAAA,GAAA,IAAA,CAAA,MAAA;AACA,WAAA,MAAA,CAAA,IAAA,CAAA,KAAA,WAAA,EAAA,UAAA,GAAA,GAAA;AACA,KA/IA;AAgJA,IAAA,SAhJA,uBAgJA;AACA,UAAA,GAAA,GAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,GAAA,GAAA,CAAA,WAAA,EAAA;AACA,UAAA,KAAA,GAAA,GAAA,CAAA,QAAA,KAAA,CAAA;AACA,UAAA,GAAA,GAAA,GAAA,CAAA,OAAA,EAAA,CAJA,CAKA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,gBAAA,EAAA,KAAA,WAAA,CAAA,WADA;AAEA,QAAA,mBAAA,EAAA,KAAA,WAAA,CAAA,OAFA;AAGA,QAAA,QAAA,EAAA,KAAA,WAAA,CAAA,QAHA;AAIA,QAAA,QAAA,EAAA,KAAA,WAAA,CAAA,OAJA;AAKA,QAAA,QAAA,EAAA,MALA;AAMA,QAAA,KAAA,EAAA,KAAA,WAAA,CAAA,KANA;AAOA,QAAA,IAAA,EAAA;AAPA,OAAA;AASA,MAAA,MAAA,CAAA,IAAA,GAAA,MAAA;AACA,MAAA,MAAA,CAAA,QAAA,GAAA,MAAA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,sCADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA;AAKA,MAAA,KAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,OAAA,GAAA,GAAA;AACA,YAAA,IAAA,GAAA,IAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AACA,YAAA,QAAA,kCAAA;;AACA,YAAA,cAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA,EAAA;AACA;AACA,cAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA,QAAA;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,UAAA,KAAA,CAAA,IAAA,GAAA,GAAA,CAAA,eAAA,CAAA,IAAA,CAAA;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,UAAA,KAAA,CAAA,KAAA;AACA,UAAA,GAAA,CAAA,eAAA,CAAA,KAAA,CAAA,IAAA,EARA,CAQA;;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,SAVA,MAUA;AACA;AACA,UAAA,SAAA,CAAA,UAAA,CAAA,IAAA,EAAA,QAAA;AACA;AACA,OAlBA,EAmBA,KAnBA,CAmBA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OArBA;AAsBA,KA5LA;AA6LA,IAAA,cA7LA,0BA6LA,IA7LA,EA6LA;AACA,UAAA,KAAA,MAAA,IAAA,KAAA,MAAA,CAAA,GAAA,EAAA;AACA,YAAA,OAAA,GAAA,GAAA,CAAA,KAAA,aAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,CAAA;AACA,QAAA,SAAA,CAAA,QAAA,CAAA,aAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,MAAA,KAAA,GAAA,GAAA,CAAA,CAAA,GAAA,MAAA,EAAA,OAAA;AACA,aAAA,MAAA,CAAA,GAAA,GAAA,KAAA;AACA,aAAA,KAAA,CAAA,IAAA;AACA;AACA,KApMA;AAqMA,IAAA,gBArMA,4BAqMA,MArMA,EAsMA;AAEA,WAAA,WAAA,GAAA,MAAA,CAAA,MAAA;AACA,WAAA,KAAA,CAAA,gBAAA,CAAA,YAAA,CAAA,KAAA,MAAA,EAAA,MAAA;AACA,KA1MA;AA2MA;AACA,IAAA,kBA5MA,gCA4MA;AAAA;;AACA,UAAA,KAAA,WAAA,CAAA,WAAA,IAAA,EAAA,EAAA;AACA,aAAA,WAAA,CAAA,OAAA,GAAA,EAAA;AACA;;AAEA,UAAA,MAAA,GAAA;AACA,QAAA,QAAA,EAAA,KAAA,WAAA,CAAA,OADA;AAEA,QAAA,gBAAA,EAAA,KAAA,WAAA,CAAA,WAFA;AAGA,QAAA,mBAAA,EAAA,KAAA,WAAA,CAAA,OAHA;AAIA,QAAA,QAAA,EAAA,KAAA,WAAA,CAAA,QAJA;AAKA,QAAA,KAAA,EAAA,KAAA,WAAA,CAAA;AALA,OAAA;AAOA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,KAAA,GADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA;AAKA,UAAA,KAAA,GAAA,EAAA;AACA,WAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,MAAA,CAAA,OAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,IAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AAEA;AACA,OAPA,EAOA,KAPA,CAOA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OATA;AAUA,KAzOA;AA0OA,IAAA,WA1OA,yBA0OA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,WAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,EAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA;;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,WAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,IAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA;;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,WAAA,CAAA,OAAA,GAAA,MAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACA,UAAA,IAAA,CAAA,WAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,WAAA,CAAA,YAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA;;AAEA,QAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,kBAAA;AACA,OAxBA,EAFA,CA2BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA7QA,GArVA;AAmmBA,EAAA,OAnmBA,qBAmmBA;AAAA;;AACA,SAAA,UAAA,GAAA,KAAA,CAAA,YAAA,CAAA;AACA,SAAA,KAAA,GAAA,EAAA;AACA,QAAA,IAAA,GAAA,IAAA;AACA,SAAA,WAAA,CAAA,QAAA,GAAA,GAAA;AACA,SAAA,WAAA,CAAA,KAAA,GAAA,KAAA,KAAA,EAAA;AACA,IAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,MAAA,eAAA,CAAA;AAAA,QAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA;AAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,MAAA,CAAA,gBAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA;AACA,QAAA,MAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA;AACA,QAAA,MAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA;AACA,OANA;AAOA,KAZA;AAcA;AAvnBA,CAAA", "sourcesContent": ["<template>\r\n  <Card class=\"menu-card\" dis-hover>\r\n    <!--    <Spin size=\"large\" fix v-if=\"loading\"></Spin>-->\r\n    <h3 slot=\"title\">站址关联查询\r\n      <!--<Button type=\"primary\" @click=\"openFLow\" style=\"float: right;margin-left: 5px;\">查看流程</Button>-->\r\n    </h3>\r\n    <Form :model=\"queryParams\" ref=\"queryParamsForm\" :label-width=\"110\" class=\"margin-right-width\">\r\n      <Row class=\"form-panel\">\r\n\r\n\r\n      </Row>\r\n      <Row class=\"form-panel\">\r\n        <Col span=\"6\">\r\n          <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n            <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\"\r\n            >\r\n              <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n              <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}\r\n              </Option>\r\n            </Select>\r\n          </FormItem>\r\n        </Col>\r\n        <Col span=\"6\">\r\n          <FormItem label=\"运营分局：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n            <Input :clearable=true icon=\"ios-archive\" v-model=\"queryParams.countryName2\"\r\n                   placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly\r\n            >\r\n            </Input>\r\n          </FormItem>\r\n          <FormItem label=\"运营分局：\" prop=\"country\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n            <Select v-model=\"queryParams.countryName\" :style=\"formItemWidth\">\r\n              <Option value=\"-1\">全部</Option>\r\n              <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}\r\n              </Option>\r\n            </Select>\r\n          </FormItem>\r\n        </Col>\r\n        <Col span=\"6\">\r\n          <FormItem label=\"站址类型：\" class=\"form-line-height\">\r\n            <Select v-model=\"queryParams.siteType\" :style=\"formItemWidth\">\r\n              <Option v-for=\"item in siteTypeList\" :value=\"item.num\" :key=\"item.name\">{{item.name}}\r\n              </Option>\r\n            </Select>\r\n          </FormItem>\r\n        </Col>\r\n        <Col span=\"6\">\r\n          <FormItem label=\"账期：\" prop=\"evaluationDate\">\r\n            <cl-date-picker :clearable=\"true\" type=\"month\"\r\n                            format=\"yyyyMM\"\r\n                            v-model=\"queryParams.month\"></cl-date-picker>\r\n          </FormItem>\r\n        </Col>\r\n        <!--                <Col span=\"6\">\r\n                            <FormItem label=\"发送状态：\" prop=\"status\">\r\n                                <Select v-model=\"queryParams.sendstatus\">\r\n                                    <Option v-for=\"item in sendstatus\" :value=\"item.typeCode\">\r\n                                        {{item.typeName}}\r\n                                    </Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>-->\r\n\r\n      </Row>\r\n      <div align=\"right\">\r\n        <Button style=\"margin-left: 5px;width:69px;\" type=\"success\" icon=\"ios-search\" @click=\"query()\">搜索    </Button>\r\n        <Button style=\"margin-left: 5px;width:69px;\" type=\"info\" icon=\"ios-redo\" @click=\"reset1()\">重置    </Button>\r\n        <!--                <Button type=\"warning\" @click=\"sendJtStation()\">集团同步</Button>-->\r\n        <Button type=\"text\" @click=\"exportCsv\">导出</Button>\r\n      </div>\r\n    </Form>\r\n    <cl-table title=\"查看明细\" method=\"post\" class=\"unionTable\" :showPage=\"false\" :url=\"url\" ref=\"table\"\r\n              :searchable=\"false\" :exportable=\"false\" :columns=\"column\" :data=\"result.data\" :total=\"result.total\" :loading=\"result.loading\" :pageSize=\"result.pageSize\" height=\"500\">\r\n      <div slot=\"buttons\">\r\n\r\n      </div>\r\n    </cl-table>\r\n    <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n    <Modal\r\n        v-model=\"abnomalModel\"\r\n        width=\"80%\"\r\n        title=\"预警稽核\"\r\n    >\r\n      <query ref=\"query\"></query>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button type=\"text\" @click=\"queryClose\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n  </Card>\r\n</template>\r\n<script>\r\nimport query from \"@/view/account/check/queryAbnormalCity\";\r\nimport countryModal from \"@/view/basedata/ammeter/countryModal\";\r\nimport config from '@/config/index';\r\nimport axios from '@/libs/api.request';\r\nimport {blist} from \"@/libs/tools\";\r\nimport ExportCsv from '_c/cl/table/export-csv';\r\nimport Csv from '_c/cl/table/csv';\r\nimport {getUserByUserRole, getCountryByUserId, getCountrysdata,listStationjt,sendjtstations,getUserdata} from '@/api/basedata/ammeter.js'\r\nimport {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nexport default {\r\n  name: \"cityAccountCheckList\",\r\n  components:{ query,countryModal},\r\n  data() {\r\n    let renderLteID = (h, params) => {\r\n      let row = params.row;\r\n      let that = this;\r\n      if (row.r_id == null) {\r\n        return h('Input', {\r\n          props: {\r\n\r\n            icon: \"ios-archive\",\r\n            placeholder: \"点击图标选择\",\r\n            readonly: true,\r\n          },\r\n          on: {\r\n            'on-click': () => {\r\n              this.result.data[params.index]= row;\r\n              this.chooseLteStation(row, params);\r\n            }\r\n          }\r\n        })\r\n      } else {\r\n        return h(\"div\",'已关联');\r\n      }\r\n    };\r\n    let renderStatus = (h, params) => {\r\n      let value = \"\";\r\n      for (let item of this.sendstatus) {\r\n        if (item.typeCode == params.row.sendstatus) {\r\n          value = item.typeName;\r\n          break;\r\n        }\r\n        else{\r\n\r\n        }\r\n      }\r\n      return h(\"div\", value);\r\n    };\r\n    let renderW = (h, params) => {\r\n      var that = this;\r\n      var text, type = \"\";\r\n      if (params.row.billid) {\r\n        text = \"查看\";\r\n        type = \"success\";\r\n      } else {\r\n        text = \"提交\";\r\n        type = \"primary\";\r\n      }\r\n      return h(\"Button\", {\r\n        props: {\r\n          type: type, size: \"small\"\r\n        }, on: {\r\n          click() {\r\n            if (params.row.processinstid) {\r\n              that.showFlow(params.row.id, params.row.processinstid, params.row.companyCode);\r\n            } else {\r\n              if (that.fillInNameNow == params.row.fillInAccount) {\r\n                // 验证\r\n                that.loading = true;\r\n                saveCheckAccount(params.row.id).then(res => {\r\n                  that.loading = false;\r\n                  if (res.data.success) {\r\n                    that.startFlow(params.row.id, params.row.abstractValue, params.row.companyCode);\r\n                  } else {\r\n                    that.$Notice.error({\r\n                      duration: 8,\r\n                      title: '验证错误:' + params.row.abstractValue,\r\n                      desc: res.data.msg\r\n                    });\r\n                  }\r\n                })\r\n              } else\r\n                that.$Message.info(\"只有(\" + params.row.fillInName + \")能提交\");\r\n            }\r\n          }\r\n        }\r\n      }, text);\r\n    };\r\n    return {\r\n      firstCountryname2: \"\",\r\n      firstCountry: \"\",\r\n      firstCountryname: \"\",\r\n      abnomalModel:false,\r\n      siteTypeList:[\r\n        {\r\n          name:'基站类',\r\n          num:'1'\r\n        },\r\n        // {\r\n        //   name:'非基站类',\r\n        //   num:'0'\r\n        // }\r\n      ],\r\n      formItemWidth: widthstyle,\r\n      electrotypes:[1411,1412,1421,1422,1431,1432],\r\n      nodeName:null,\r\n      version: config.version,\r\n      url: \"/business/poweraudit/getPowerAuditCompanies\",\r\n      queryParams: {company: 0,month:'',countryName:'',countryName2:'',city: '', country: ''},\r\n      column: [],\r\n      headColumn: [\r\n        {title: \"所属部门\", key: \"city\", minWidth: 150},\r\n        {title: \"时间(月)\", key: \"month\",  minWidth: 100},\r\n        {title: \"运营分局\", key: \"operationsBranch\",  minWidth: 100},\r\n        {title: \"异常电表总表(个)\", key: \"sums\", minWidth: 130,\r\n          render:(h,params)=>{\r\n            return h('span',{\r\n              'style':{\r\n                'color':'#f83333'\r\n              },\r\n              on:{\r\n                click:()=>{\r\n                  this.openDialog('地市和运营分局',params.row,'地市和运营分局');\r\n                }\r\n              }\r\n            },params.row.sums)\r\n          }\r\n        },\r\n        {title: \"异常电表数\", align: 'center',\r\n          children: [\r\n            {title: \"一站多表/多站多表\", key: \"mutiJtlteCodes\",  minWidth: 150,\r\n              render:(h,params)=>{\r\n                return h('span',{\r\n                  'style':{\r\n                    'color':params.row.mutiJtlteCodes == '0'?'#515a6e':'#f83333'\r\n                  },\r\n                  on:{\r\n                    click:()=>{\r\n                      if(params.row.mutiJtlteCodes != '0'){\r\n                        this.openDialog('一站多表/多站多表',params.row,'一站多表/多站多表');\r\n                      }\r\n                    }\r\n                  }\r\n                },params.row.mutiJtlteCodes)\r\n              }\r\n            },\r\n            {title: \"电价合理性\", key: \"electricityPrices\",  minWidth: 100,\r\n              render:(h,params)=>{\r\n                  return h('span',{\r\n                    'style':{\r\n                      'color':params.row.electricityPrices == '0'?'#515a6e':'#f83333'\r\n                    },\r\n                    on:{\r\n                      click:()=>{\r\n                        if(params.row.electricityPrices != '0'){\r\n                          this.openDialog('电价合理性',params.row,'电价合理性');\r\n                        }\r\n                      }\r\n                    }\r\n                  },params.row.electricityPrices)\r\n              },\r\n            },\r\n            {title: \"电表站址一致性\", key: \"addressConsistence\",  minWidth: 130,\r\n              render:(h,params)=>{\r\n                  return h('span',{\r\n                    'style':{\r\n                      'color':params.row.addressConsistence == '0'?'#515a6e':'#f83333'\r\n                    },\r\n                    on: {\r\n                      click: () => {\r\n                        if(params.row.addressConsistence != '0'){\r\n                          this.openDialog('电表站址一致性',params.row,'电表站址一致性');\r\n                        }\r\n                      }\r\n                    }\r\n                  },params.row.addressConsistence)\r\n              },\r\n            },\r\n            {title: \"台账周期连续性\", key: \"periodicAnomaly\", minWidth: 130,\r\n              render:(h,params)=>{\r\n                  return h('span',{\r\n                    'style':{\r\n                      'color':params.row.periodicAnomaly == '0'?'#515a6e':'#f83333'\r\n                    },\r\n                    on: {\r\n                      click: () => {\r\n                        if(params.row.periodicAnomaly != '0'){\r\n                          this.openDialog('台账周期连续性',params.row,\"台账周期连续性\");\r\n                        }\r\n                      }\r\n                    }\r\n                  },params.row.periodicAnomaly)\r\n              },\r\n            },\r\n            {title: \"电表度数连续性\", key: \"electricityContinuity\", minWidth: 130,\r\n              render:(h,params)=> {\r\n                return h('span', {\r\n                  'style': {\r\n                    'color': params.row.electricityContinuity == '0'?'#515a6e':'#f83333'\r\n                  },\r\n                  on: {\r\n                    click: () => {\r\n                      if(params.row.electricityContinuity != '0'){\r\n                        this.openDialog('电表度数连续性', params.row, '电表度数连续性');\r\n                      }\r\n                    }\r\n                  }\r\n                }, params.row.electricityContinuity)\r\n              }\r\n            },\r\n            // {title: \"电量合理性(省内大数据)\", key: \"electricityRationality\", minWidth: 170,\r\n            //   render:(h,params)=>{\r\n            //       return h('span',{\r\n            //         'style':{\r\n            //           'color':params.row.electricityRationality == '0'?'#515a6e':'#f83333'\r\n            //         },\r\n            //         on: {\r\n            //           click: () => {\r\n            //             if(params.row.electricityRationality != '0'){\r\n            //               this.openDialog('电量合理性(省内大数据)',params.row,'电量合理性(省内大数据)');\r\n            //             }\r\n            //           }\r\n            //         }\r\n            //       },params.row.electricityRationality)\r\n            //   },\r\n            // },\r\n            {title: \"日均电量的波动合理性(集团5gr)\", key: \"fluctuateContinuity\", minWidth: 230,\r\n              render:(h,params)=>{\r\n                  return h('span',{\r\n                    'style':{\r\n                      'color':params.row.fluctuateContinuity == '0'?'#515a6e':'#f83333'\r\n                    },\r\n                    on: {\r\n                      click: () => {\r\n                        if(params.row.fluctuateContinuity != '0'){\r\n                          this.openDialog('日均电量的波动合理性(集团5gr)',params.row,'日均电量的波动合理性(集团5gr)');\r\n                        }\r\n                      }\r\n                    }\r\n                  },params.row.fluctuateContinuity)\r\n              },\r\n            },\r\n            // {title: \"日均耗电量合理性(无线大数据)\", key: \"consumeContinuity\", minWidth: 230,\r\n            //   render:(h,params)=>{\r\n            //     return h('span',{\r\n            //       'style':{\r\n            //         'color':'#f83333'\r\n            //       },\r\n            //       on: {\r\n            //         click: () => {\r\n            //           this.openDialog('日均耗电量合理性',params.row,'日均耗电量合理性');\r\n            //         }\r\n            //       }\r\n            //     },params.row.consumeContinuity)\r\n            //   },\r\n            // },\r\n            {title: \"分摊比例准确性\", key: \"shareAccuracy\", minWidth: 160,\r\n              render:(h,params)=> {\r\n                return h('span', {\r\n                  'style': {\r\n                    'color': params.row.shareAccuracy == '0'?'#515a6e':'#f83333'\r\n                  },\r\n                  on: {\r\n                    click: () => {\r\n                      if(params.row.shareAccuracy != '0'){\r\n                        this.openDialog('分摊比例准确性', params.row, '分摊比例准确性');\r\n                      }\r\n                    }\r\n                  }\r\n                }, params.row.shareAccuracy)\r\n              }\r\n            },\r\n            // {title: \"局站独享共享设置\", key: \"exclusiveAccuracy\", minWidth: 160,\r\n            //   render:(h,params)=>{\r\n            //       return h('span',{\r\n            //         'style':{\r\n            //           'color':params.row.exclusiveAccuracy == '0'?'#515a6e':'#f83333'\r\n            //         },\r\n            //         on: {\r\n            //           click: () => {\r\n            //             if(params.row.exclusiveAccuracy != '0'){\r\n            //               this.openDialog('局站独享共享设置',params.row,'局站独享共享设置');\r\n            //             }\r\n            //           }\r\n            //         }\r\n            //       },params.row.exclusiveAccuracy)\r\n            //     }\r\n            // },\r\n            {title: \"台账周期合理性\", key: \"reimbursementCycle\", minWidth: 130,\r\n              render:(h,params)=>{\r\n                  return h('span',{\r\n                    'style':{\r\n                      'color':params.row.reimbursementCycle == '0'?'#515a6e':'#f83333'\r\n                    },\r\n                    on: {\r\n                      click: () => {\r\n                        if(params.row.reimbursementCycle != '0'){\r\n                          this.openDialog('台账周期合理性',params.row,'台账周期合理性');\r\n                        }\r\n                      }\r\n                    }\r\n                  },params.row.reimbursementCycle)\r\n              },\r\n            },\r\n          ]\r\n        },\r\n      ],\r\n      sdata:[],\r\n      result: {\r\n        loading: false,\r\n        data: [],\r\n        total: 0,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      companies: [],\r\n      departments: [],\r\n      billid:null,\r\n      selectedow:null,\r\n      loading: false,\r\n      company: 0,\r\n      sendstatus: [],\r\n      isAdmin:false,\r\n      isSubAdmin:false,//县能耗管理员\r\n      isCityAdmin:false,\r\n      isProAdmin:false,\r\n      proAdmin:null,\r\n      export: {\r\n        run: false,//是否正在执行导出\r\n        data: \"\",//导出数据\r\n        totalPage: 0,//一共多少页\r\n        currentPage: 0,//当前多少页\r\n        percent: 0,\r\n        size: 50000\r\n      },\r\n      exportColumns: [\r\n        {title: \"单位\", key: \"parentcountryname\"},\r\n        {title: \"部门\", key: \"countryname\"},\r\n        {title: \"能耗站址编码\", key: \"resstationcode\"},\r\n        {title: \"能耗站址名称\", key: \"resstationname\"},\r\n        {title: \"详细地址\", key: \"address\"},\r\n        {title: \"能耗站址关联基站id\", key: \"nmcodes\"},\r\n        {title: \"LTE集团站址名称\", key: \"ltestationAddrName\"},\r\n        {title: 'LTE集团编码', key: 'ltestationAddrCode'},\r\n        {title: 'LTE集团铁塔编码', key: 'ltestationAddrTaCode'},\r\n      ]\r\n\r\n\r\n    }\r\n  }, \r\n  methods: {\r\n    reset1() {\r\n      console.log(this.company, \"this.company\");\r\n      console.log(this.firstCountry, \"this.firstCountry\");\r\n      console.log(this.firstCountryname, \"this.firstCountryname\");\r\n      this.queryParams.month = this.getno();\r\n      this.queryParams.company = this.company;\r\n      this.queryParams.country = this.firstCountry;\r\n      this.queryParams.countryName = this.firstCountryname;\r\n      this.queryParams.countryName2 = this.firstCountryname2;\r\n    },\r\n    getno() {\r\n      let date = new Date();\r\n      //获取当前年月\r\n      let year = date.getFullYear();\r\n      let month = date.getMonth() + 1;\r\n      month = (month < 10 ? \"0\" + month : month);\r\n      let curDate = (year.toString() + month.toString());\r\n      return curDate;\r\n    },\r\n    openDialog(type,value,name){\r\n      console.log(value);\r\n      this.$refs.query.pageNum = 1;\r\n      this.$refs.query.pageSize = 10;\r\n      this.$refs.query.cityCode=value.cityCode\r\n      this.$refs.query.countyCompanies=value.countyCompaniesCode\r\n      this.$refs.query.operationsBranch=value.operationsBranch\r\n      this.$refs.query.cityName=value.city\r\n      this.$refs.query.siteType=value.siteType\r\n      this.$refs.query.month=value.month\r\n      this.$refs.query.exportName=name\r\n      this.$refs.query.activeName=type\r\n      this.$refs.query.checktable()\r\n      this.abnomalModel=true\r\n      console.log(type);\r\n    },\r\n    queryClose(){\r\n      this.abnomalModel=false;\r\n    },\r\n    getDataFromModal(data) {\r\n      //修改\r\n      this.queryParams.countryName = data.name;\r\n      this.queryParams.country = data.id;\r\n      this.queryParams.countryName2 = data.name;\r\n      this.$set(this.queryParams,'countryName',data.name);\r\n      console.log(this.queryParams.countryName);\r\n      // this.queryParams.city = \"成都\";\r\n      // this.queryParams.countyCompanies = data.name;\r\n      //选择所属部门结束\r\n    },\r\n    selectChange(value){\r\n      if (this.queryParams.company != undefined) {\r\n        getCountryByUserId(this.queryParams.company).then(res => {\r\n          this.departments = res.data.departments;\r\n          // this.queryParams.country = res.data.departments[0].id;\r\n          // this.queryParams.countryName = this.departments[0].name;\r\n          // this.queryParams.countryname2 = this.departments[0].name;\r\n          // this.$set(this.queryParams,'countryName',res.data.departments[0].id);\r\n          // this.$set(this.queryParams,'countryName2',res.data.departments[0].name);\r\n          this.queryParams.country = res.data.departments[0].id;\r\n          this.queryParams.countryName = res.data.departments[0].name;\r\n          this.queryParams.countryName2 = res.data.departments[0].name;\r\n        });\r\n      }\r\n      //分公司选择全部，部门清空\r\n      if(value==-1){\r\n        this.queryParams.country=null;\r\n        this.queryParams.countryName =null;\r\n        this.queryParams.countryName2 =null;\r\n      }\r\n    },\r\n    //合计接口\r\n    sumData(){\r\n      let that =this;\r\n      // let params = this.queryParams;\r\n      let params = {\r\n\r\n      };\r\n      params.pageNum = this.result.pageNum;\r\n      params.pageSize = this.result.pageSize;\r\n      let req = {\r\n        url: this.url2,\r\n        method: \"post\",\r\n        data: params\r\n      };\r\n      this.result.loading = true;\r\n      axios.request(req).then(res => {\r\n        res.data.city='合计';\r\n        this.sumRow=res.data;\r\n        console.log(res);\r\n        this.query();\r\n      }).catch(err => {\r\n        console.log(err);\r\n      });\r\n    },\r\n    query() {\r\n      this.setColumn();\r\n      if (this.queryParams.countryName2 == \"\") {\r\n        this.queryParams.country = \"\";\r\n      }\r\n      let params={\r\n        cityCode:this.queryParams.company,\r\n        operationsBranch:this.queryParams.countryName2,\r\n        countyCompaniesCode: this.queryParams.country,\r\n        siteType:this.queryParams.siteType,\r\n        month:this.queryParams.month,\r\n      }\r\n      this.$refs.table.query(params);\r\n    },\r\n    setColumn() {\r\n      this.column = this.headColumn;\r\n    },\r\n    sendJtStation(){\r\n      let that=this;\r\n      if (this.queryParams.company!=null&&this.queryParams.company>0)\r\n      {/** this.$Message.info(\"暂定为规定时间同步！\");8**/\r\n      this.$Modal.confirm({\r\n        title: '温馨提示',\r\n        content: '<p>集团同步数据需要时间，请勿频繁发送，请确认是否同步？</p>',\r\n        onOk: () => {\r\n          sendjtstations(that.queryParams.company).then(res => {//\r\n\r\n          });\r\n        },\r\n      });\r\n      }\r\n      else\r\n      {\r\n        this.$Message.info(\"请先选择 所属分公司\");\r\n      }\r\n    },\r\n    chooseResponseCenter() {\r\n      if (this.queryParams.company == null || this.queryParams.company == \"-1\") {\r\n        this.$Message.info(\"请先选择分公司\");\r\n        return;\r\n      }\r\n      this.$refs.countryModal.choose(this.queryParams.company);//所属部门\r\n    },\r\n    getDataFromlteStationModal(data){\r\n      this.result.data[this.selectedrow].id=data.id;\r\n      this.result.data[this.selectedrow].ltestationAddrCode=data.jtcode;\r\n      this.result.data[this.selectedrow].ltestationAddrName=data.jtname;\r\n      this.result.data[this.selectedrow].sendstatus=\"0\";\r\n    },\r\n    exportCsv() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1;\r\n      const day = now.getDate();\r\n      // let params = this.queryParams;\r\n      let params ={\r\n        operationsBranch:this.queryParams.countryName,\r\n        countyCompaniesCode: this.queryParams.country,\r\n        siteType:this.queryParams.siteType,\r\n        cityCode:this.queryParams.company,\r\n        fileName: \"详单导出\",\r\n        month:this.queryParams.month,\r\n        type:'运营分局'\r\n      }\r\n      params.type='运营分局';\r\n      params.fileName='详单导出';\r\n      let req = {\r\n        url: \"business/poweraudit/exportPowerAudit\",\r\n        method: \"post\",\r\n        data: params,\r\n      };\r\n      axios.file(req).then((res) => {\r\n        const content = res;\r\n        const blob = new Blob([content]);\r\n        const fileName = `详单导出.xlsx`;\r\n        if (\"download\" in document.createElement(\"a\")) {\r\n          // 非IE下载\r\n          const elink = document.createElement(\"a\");\r\n          elink.download = fileName;\r\n          elink.style.display = \"none\";\r\n          elink.href = URL.createObjectURL(blob);\r\n          document.body.appendChild(elink);\r\n          elink.click();\r\n          URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n          document.body.removeChild(elink);\r\n        } else {\r\n          // IE10+下载\r\n          navigator.msSaveBlob(blob, fileName);\r\n        }\r\n      })\r\n          .catch((err) => {\r\n            console.log(err);\r\n          });\r\n    },\r\n    beforeLoadData(data) {\r\n      if (this.export && this.export.run) {\r\n        let csvData = Csv(this.exportColumns, data, {}, false)\r\n        ExportCsv.download(\"集团关联导出数据\" + Math.floor(Math.random() * 100 + 1) + \".csv\", csvData)\r\n        this.export.run = false\r\n        this.$Spin.hide()\r\n      }\r\n    },\r\n    chooseLteStation(params)\r\n    {\r\n\r\n      this.selectedrow=params._index;\r\n      this.$refs.ltestationSelect.initDataList(this.billid,params);\r\n    },\r\n    //向后台请求数据\r\n    getAccountMessages() {\r\n      if (this.queryParams.countryName == \"\") {\r\n        this.queryParams.country = \"\";\r\n      }\r\n\r\n      let params={\r\n        cityCode:this.queryParams.company,\r\n        operationsBranch:this.queryParams.countryName,\r\n        countyCompaniesCode: this.queryParams.country,\r\n        siteType:this.queryParams.siteType,\r\n        month:this.queryParams.month,\r\n      }\r\n      let req = {\r\n        url: this.url,\r\n        method: \"post\",\r\n        data: params\r\n      };\r\n      let array = [];\r\n      this.result.loading = true;\r\n      axios.request(req).then(res => {\r\n        this.result.loading = false;\r\n        if (res.data) {\r\n          this.result.total = res.data.total;\r\n          this.result.data = Object.assign([], res.data.rows)\r\n\r\n        }\r\n      }).catch(err => {\r\n        console.log(err);\r\n      });\r\n    },\r\n    getUserData() {\r\n      let that = this;\r\n      getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n        if (res.data.companies.length != 0) {\r\n          let companies = res.data.companies;\r\n          if (res.data.companies[0].id == \"**********\") {\r\n            companies = that.companies;\r\n          }\r\n          that.company = companies[0].id;\r\n          that.queryParams.company = companies[0].id;\r\n        }\r\n        if (res.data.departments.length != 0) {\r\n          let departments = res.data.departments;\r\n          if (res.data.companies[0].id == \"**********\" && that.departments.length != 0) {\r\n            departments = that.departments\r\n          }\r\n          that.country = departments[0].id;\r\n          that.countryName = departments[0].name;\r\n          that.queryParams.country = Number(departments[0].id);\r\n          that.queryParams.countryName = departments[0].name;\r\n          that.queryParams.countryName2 = departments[0].name;\r\n        }\r\n\r\n        that.pageNum = 1\r\n        that.setColumn();\r\n        that.getAccountMessages();\r\n      });\r\n      // getCountryByUserId().then(res => {\r\n      //     // this.departments = res.data.departments;\r\n      //     // this.queryParams.country = res.data.departments[0].id;\r\n      //     // this.queryParams.countryname = this.departments[0].name;\r\n      //     this.country = res.data.departments[0].id;\r\n      //     this.countryname = res.data.departments[0].name;\r\n      //     this.countryname2 = res.data.departments[0].name;\r\n      //   });\r\n    }},\r\n    mounted() {\r\n    this.sendstatus = blist(\"sendstatus\");\r\n    this.sdata = [];\r\n    let that = this;\r\n    this.queryParams.siteType = '1';\r\n    this.queryParams.month = this.getno();\r\n    getUserByUserRole().then(res => {//根据权限获取分公司\r\n      that.companies = res.data.companies;\r\n      if (res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true) {\r\n        that.isAdmin = true;\r\n      }\r\n      getCountrysdata({orgCode: res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n        that.departments = res.data;\r\n        this.firstCountryname = res.data[0].name;\r\n        this.firstCountryname2 = res.data[0].name;\r\n        this.firstCountry= res.data[0].id;\r\n        that.getUserData();\r\n      });\r\n    });\r\n\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "sourceRoot": "src/view/account/check"}]}