{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\addAmmeter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\addAmmeter.vue", "mtime": 1754285403016}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRBbW1ldGVyLA0KICBsaXN0RWxlY3RyaWNUeXBlLA0KICBlZGl0QW1tZXRlciwNCiAgZWRpdEFtbWV0ZXJSZWNvcmQsDQogIHVwZGF0ZUFtbWV0ZXIsDQogIGNoZWNrUHJvamVjdE5hbWVFeGlzdCwNCiAgY2hlY2tBbW1ldGVyQnlTdGF0aW9uLA0KICBnZXRDbGFzc2lmaWNhdGlvbiwNCiAgZ2V0Q2xhc3NpZmljYXRpb25JZCwNCiAgZ2V0VXNlcmRhdGEsDQogIGNoZWNrQ2xhc3NpZmljYXRpb25MZXZlbCwNCiAgbGlzdEVsZWN0cmljVHlwZVJhdGlvLA0KICBjaGVja0FtbWV0ZXJFeGlzdCwNCiAgZ2V0VXNlckJ5VXNlclJvbGUsDQogIGdldENvdW50cnlCeVVzZXJJZCwNCiAgZ2V0Q291bnRyeXNkYXRhLA0KICByZW1vdmVBdHRhY2gsDQogIGF0dGNoTGlzdCwNCiAgZ2V0QmFua0NhcmQsDQp9IGZyb20gIkAvYXBpL2Jhc2VkYXRhL2FtbWV0ZXIuanMiOw0KaW1wb3J0IHsgYmxpc3QsIGJ0ZXh0IH0gZnJvbSAiQC9saWJzL3Rvb2xzIjsNCmltcG9ydCBhdHRhY2hGaWxlIGZyb20gIi4vLi4vcHJvdG9jb2wvYXR0YWNoRmlsZSI7DQppbXBvcnQgU2VsZWN0RWxlY3RyaWNUeXBlIGZyb20gIi4vc2VsZWN0RWxlY3RyaWNUeXBlIjsNCmltcG9ydCBjb3VudHJ5TW9kYWwgZnJvbSAiLi9jb3VudHJ5TW9kYWwiOw0KaW1wb3J0IHN0YXRpb25Nb2RhbCBmcm9tICIuL3N0YXRpb25Nb2RhbCI7DQppbXBvcnQgeyBtYXBNdXRhdGlvbnMgfSBmcm9tICJ2dWV4IjsNCmltcG9ydCB7IGlzRW1wdHkgfSBmcm9tICJAL2xpYnMvdmFsaWRhdGUiOw0KaW1wb3J0IHJvdXRlcnMgZnJvbSAiQC9yb3V0ZXIvcm91dGVycyI7DQppbXBvcnQgeyBnZXRIb21lUm91dGUgfSBmcm9tICJAL2xpYnMvdXRpbCI7DQppbXBvcnQgQW1tZXRlclByb3RvY29sTGlzdCBmcm9tICJAL3ZpZXcvYmFzZWRhdGEvcXVvdGEvbGlzdEFtbWV0ZXJQcm90b2NvbCI7DQppbXBvcnQgY3VzdG9tZXJMaXN0IGZyb20gIi4vY3VzdG9tZXJNb2RhbCI7DQppbXBvcnQgQ2hvb3NlQW1tZXRlck1vZGVsIGZyb20gIkAvdmlldy9iYXNlZGF0YS9hbW1ldGVyL2Nob29zZUFtbWV0ZXJNb2RlbCI7DQppbXBvcnQgQ2hvb3NlTW9kYWwgZnJvbSAiQC92aWV3L2J1c2luZXNzL2dhc0J1c2luZXNzL2Nob29zZU1vZGFsIjsNCmltcG9ydCBheGlvcyBmcm9tICJAL2xpYnMvYXBpLnJlcXVlc3QiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJhZGRBbW1ldGVyIiwNCiAgY29tcG9uZW50czogew0KICAgIGF0dGFjaEZpbGUsDQogICAgQW1tZXRlclByb3RvY29sTGlzdCwNCiAgICBjdXN0b21lckxpc3QsDQogICAgc3RhdGlvbk1vZGFsLA0KICAgIGNvdW50cnlNb2RhbCwNCiAgICBTZWxlY3RFbGVjdHJpY1R5cGUsDQogICAgQ2hvb3NlQW1tZXRlck1vZGVsLA0KICAgIENob29zZU1vZGFsLA0KICB9LA0KICBkYXRhKCkgew0KICAgIC8v5LiN6IO96L6T5YWl5rGJ5a2XDQogICAgY29uc3QgY2hlY2tEYXRhID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgaWYgKHZhbHVlKSB7DQogICAgICAgIGlmICgvW1x1NEUwMC1cdTlGQTVdL2cudGVzdCh2YWx1ZSkpIHsNCiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIue8lueggeS4jeiDvei+k+WFpeaxieWtlyEiKSk7DQogICAgICAgIH0gZWxzZSBpZiAoZXNjYXBlKHZhbHVlKS5pbmRleE9mKCIldSIpID49IDApIHsNCiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIue8lueggeS4jeiDvei+k+WFpeS4reaWh+Wtl+espiEiKSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9Ow0KICAgIGNvbnN0IHZhbGlkYXRvck51bWJlciA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGlmICh2YWx1ZS5sZW5ndGggPD0gMCkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuS4jeiDveS4uuepuiIpKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICB9DQogICAgfTsNCiAgICBjb25zdCB2YWxpZGF0b3JOdW1iZXJaZXJvID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgaWYgKHZhbHVlICE9IG51bGwgJiYgdmFsdWUgPT0gMCkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuWPquiDvei+k+WFpeWkp+S6jjDnmoTmlbAiKSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjYWxsYmFjaygpOw0KICAgICAgfQ0KICAgIH07DQogICAgY29uc3QgdmFsaWRhdGVDbGFzc2lmaWNhdGlvbnMgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBpZiAodmFsdWUgPT0gdW5kZWZpbmVkIHx8IHZhbHVlID09IG51bGwpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLkuI3og73kuLrnqboiKSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBpZiAodmFsdWUubGVuZ3RoIDw9IDApIHsNCiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuS4jeiDveS4uuepuiIpKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9Ow0KDQogICAgY29uc3QgdmFsaWRhdGVQcmljZSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGlmICh2YWx1ZSA9PSB1bmRlZmluZWQgfHwgdmFsdWUgPT0gbnVsbCkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuS4jeiDveS4uuepuiIpKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmICh2YWx1ZSA8PSAwKSB7DQogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLljZXku7co5ZCr56iOKeS4jeiDveWwj+S6jjAiKSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGNhbGxiYWNrKCk7DQogICAgfTsNCiAgICBjb25zdCB2YWxpZGF0ZWx1bXBzdGFydGRhdGUgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMuYW1tZXRlcjsNCiAgICAgIGxldCBzdGFydCA9IGRhdGEubHVtcHN0YXJ0ZGF0ZTsNCiAgICAgIGxldCBlbmQgPSBkYXRhLmx1bXBlbmRkYXRlOw0KICAgICAgaWYgKHN0YXJ0ID09IG51bGwpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLkuI3og73kuLrnqboiKSk7DQogICAgICB9DQogICAgICBpZiAoc3RhcnQgIT0gbnVsbCAmJiBlbmQgIT0gbnVsbCkgew0KICAgICAgICBpZiAoZW5kIDw9IHN0YXJ0KSB7DQogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLljIXlubLotbflp4vml6XmnJ/kuI3og73lpKfkuo7nrYnkuo7miKrmraLml6XmnJ8iKSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGNhbGxiYWNrKCk7DQogICAgfTsNCiAgICBjb25zdCB2YWxpZGF0ZWx1bXBlbmRkYXRlID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLmFtbWV0ZXI7DQogICAgICBsZXQgc3RhcnQgPSBkYXRhLmx1bXBzdGFydGRhdGU7DQogICAgICBsZXQgZW5kID0gZGF0YS5sdW1wZW5kZGF0ZTsNCiAgICAgIGlmIChlbmQgPT0gbnVsbCkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuS4jeiDveS4uuepuiIpKTsNCiAgICAgIH0NCiAgICAgIGlmIChzdGFydCAhPSBudWxsICYmIGVuZCAhPSBudWxsKSB7DQogICAgICAgIGlmIChlbmQgPD0gc3RhcnQpIHsNCiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuWMheW5suaIquatouaXpeacn+S4jeiDveWwj+S6juetieS6jui1t+Wni+aXpeacnyIpKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9Ow0KICAgIC8v5pu05pS55qCH6aKY5ZCN56ew5Y+K5qC35byPDQogICAgbGV0IHJlbmRlckhlYWRlciA9IChoLCBwYXJhbXMpID0+IHsNCiAgICAgIGxldCB0ID0gaCgNCiAgICAgICAgInNwYW4iLA0KICAgICAgICB7DQogICAgICAgICAgc3R5bGU6IHsNCiAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICJub3JtYWwiLA0KICAgICAgICAgICAgY29sb3I6ICIjZWQ0MDE0IiwNCiAgICAgICAgICAgIGZvbnRTaXplOiAiMTJweCIsDQogICAgICAgICAgICBmb250RmFtaWx5OiAiU2ltU3VuIiwNCiAgICAgICAgICAgIG1hcmdpblJpZ2h0OiAiNHB4IiwNCiAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDEsDQogICAgICAgICAgICBkaXNwbGF5OiAiaW5saW5lLWJsb2NrIiwNCiAgICAgICAgICB9LA0KICAgICAgICB9LA0KICAgICAgICAiKiINCiAgICAgICk7DQogICAgICByZXR1cm4gaCgiZGl2IiwgW3QsIGgoInNwYW4iLCB7fSwgIuaJgOWNoOavlOS+iyglKSIpXSk7DQogICAgfTsNCiAgICByZXR1cm4gew0KICAgICAgaXNSZXF1aXJlRmxhZ3M6IGZhbHNlLA0KICAgICAgcHJvcGVydHlyaWdodDogbnVsbCwgLy/lsYDnq5nkuqfmnYMNCiAgICAgIGlzUmVxdWlyZUZsYWc6IGZhbHNlLCAvL+WxgOermeaYr+WQpuW/heWhqw0KICAgICAgaXNjaGVja1N0YXRpb246IGZhbHNlLCAvL+aYr+WQpumcgOimgemqjOivgeWxgOermeWPquiDveWFs+iBlDXkuKoNCiAgICAgIGlzQ0RDb21wYW55OiBmYWxzZSwgLy/mmK/lkKbmmK/miJDpg73liIblhazlj7gNCiAgICAgIGNvbmZpZ1ZlcnNpb246IG51bGwsIC8v54mI5pysDQogICAgICBpc01vYmlsZUJhc2U6IGZhbHNlLCAvL+aYr+WQpueUn+S6p+eUqOeUtS3np7vliqjln7rnq5kNCiAgICAgIHByb3BlcnR5UmVhZG9ubHk6IHRydWUsDQogICAgICBwcm9wZXJ0eUxpc3Q6IFtdLA0KICAgICAgaXNEaXNhYmxlOiBmYWxzZSwNCiAgICAgIHdvcmtGbG93UGFyYW1zOiB7fSwNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgaXNMb2FkaW5nOiBudWxsLA0KDQogICAgICBpc0Vycm9yOiBmYWxzZSwgLy/nlKjnlLXnsbvlnovmr5Tkvovpqozor4ENCiAgICAgIGlzRXJyb3IxOiBmYWxzZSwgLy/nlKjnlLXnsbvlnovmr5Tkvovpqozor4ENCg0KICAgICAgc2hvd01vZGVsOiBmYWxzZSwNCiAgICAgIGlzQ2xhc3NpZmljYXRpb246IGZhbHNlLA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgaXNFZGl0QnlDb3VudHJ5OiBmYWxzZSwNCiAgICAgIGlzQ2l0eUFkbWluOiBmYWxzZSwNCiAgICAgIGlzQWRtaW46IGZhbHNlLA0KICAgICAgY2hvb3NlSW5kZXg6IG51bGwsDQogICAgICBlbGVjdHJvUm93TnVtOiBudWxsLCAvL+WFs+iBlOeUqOeUteexu+Wei+eahOW9k+WJjeihjA0KICAgICAgZWxlY3RyaWNUeXBlTW9kZWw6IGZhbHNlLA0KICAgICAgY29tcGFuaWVzOiBbXSwNCiAgICAgIGRlcGFydG1lbnRzOiBbXSwNCiAgICAgIGNsYXNzaWZpY2F0aW9uRGF0YTogW10sIC8v55So55S157G75Z6LDQogICAgICByZWNlaXB0YWNjb3VudG5hbWVMaXN0OiBbXSwgLy/pk7booYzljaHliJfooagNCiAgICAgIGZpbGVQYXJhbTogew0KICAgICAgICBidXNpSWQ6ICIiLA0KICAgICAgICBidXNpQWxpYXM6ICLpmYTku7Yo5Y2P6K6u566h55CGKSIsDQogICAgICAgIGNhdGVnb3J5Q29kZTogImZpbGUiLA0KICAgICAgICBhcmVhQ29kZTogImxuIiwNCiAgICAgIH0sDQogICAgICBtdWx0aUZpbGVzOiBudWxsLA0KICAgICAgZmlsZXM6IFtdLA0KICAgICAgcmVtb3ZlSWRzOiBbXSwNCiAgICAgIGF0dGFjaERhdGE6IFtdLA0KICAgICAgb2xkRGF0YTogW10sDQogICAgICBvbGRDYXRlZ29yeTogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRQYWNrYWdldHlwZTogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRQYXlwZXJpb2Q6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkUGF5dHlwZTogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRFbGVjdHJvbmF0dXJlOiAiIiwgLy/ljp/lp4vmlbDmja4NCiAgICAgIG9sZEVsZWN0cm92YWxlbmNlbmF0dXJlOiAiIiwgLy/ljp/lp4vmlbDmja4NCiAgICAgIG9sZEVsZWN0cm90eXBlOiAiIiwgLy/ljp/lp4vmlbDmja4NCiAgICAgIG9sZFN0YXR1czogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRQcm9wZXJ0eTogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRBbW1ldGVydHlwZTogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRTdGF0aW9uc3RhdHVzOiAiIiwgLy/ljp/lp4vmlbDmja4NCiAgICAgIG9sZFN0YXRpb250eXBlOiAiIiwgLy/ljp/lp4vmlbDmja4NCiAgICAgIG9sZEFtbWV0ZXJ1c2U6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkRGlyZWN0c3VwcGx5ZmxhZzogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBydWxlVmFsaWRhdGU6IHsNCiAgICAgICAgaXNlbnRpdHlhbW1ldGVyOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UsYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgcHJvamVjdG5hbWU6IFsNCiAgICAgICAgICAvL+mhueebruWQjeensA0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY291bnRyeU5hbWU6IFsNCiAgICAgICAgICAvL+aJgOWxnumDqOmXqA0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY291bnRyeTogWw0KICAgICAgICAgIC8v5omA5bGe6YOo6ZeoDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHlwZTogIm51bWJlciIsIHZhbGlkYXRvcjogdmFsaWRhdG9yTnVtYmVyLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29tcGFueTogW3sgcmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogdmFsaWRhdG9yTnVtYmVyLCB0cmlnZ2VyOiAiYmx1ciIgfV0sDQogICAgICAgIHBheXR5cGU6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIHR5cGU6ICJudW1iZXIiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlLGJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIHBheXBlcmlvZDogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgdHlwZTogIm51bWJlciIsDQogICAgICAgICAgICBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UsYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgYW1tZXRlcnVzZTogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgdHlwZTogIm51bWJlciIsDQogICAgICAgICAgICBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UsYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgcHJvcGVydHk6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIHR5cGU6ICJudW1iZXIiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlLGJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGFtbWV0ZXJ0eXBlOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICB0eXBlOiAibnVtYmVyIiwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSxibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBlbGVjdHJvdmFsZW5jZW5hdHVyZTogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgdHlwZTogIm51bWJlciIsDQogICAgICAgICAgICBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UsYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY2xhc3NpZmljYXRpb25zOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB2YWxpZGF0ZUNsYXNzaWZpY2F0aW9ucywgdHJpZ2dlcjogImNoYW5nZSxibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBtYWduaWZpY2F0aW9uOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHlwZTogIm51bWJlciIsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBwYXR0ZXJuOiAvXigoWzEtOV1cZHswLDE0fSl8MCkoXC5cZHswLDJ9KT8kLywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlj6rog73kv53nlZnkuKTkvY3lsI/mlbAiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGRpcmVjdHN1cHBseWZsYWc6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIHR5cGU6ICJudW1iZXIiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlLGJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIHByaWNlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHlwZTogIm51bWJlciIsDQogICAgICAgICAgICB2YWxpZGF0b3I6IHZhbGlkYXRlUHJpY2UsDQogICAgICAgICAgICBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgcGF0dGVybjogL14oKFsxLTldXGR7MCwxNH0pfDApKFwuXGR7MCwyfSk/JC8sDQogICAgICAgICAgICBtZXNzYWdlOiAi5Y+q6IO95L+d55WZ5Lik5L2N5bCP5pWwIiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBwYWNrYWdldHlwZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHR5cGU6ICJudW1iZXIiLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvbnRyYWN0T3RoUGFydDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sDQogICAgICAgIHN0YXRpb25OYW1lOiBbXSwNCiAgICAgICAgc3RhdHVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICB0eXBlOiAibnVtYmVyIiwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSxibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICB0ZWxlcGhvbmU6IFt7IHBhdHRlcm46IC9eMVxkezEwfSQvLCBtZXNzYWdlOiAi5qC85byP5LiN5q2j56GuIiwgdHJpZ2dlcjogImJsdXIiIH1dLA0KICAgICAgICBwZXJjZW50OiBbDQogICAgICAgICAgeyB0eXBlOiAibnVtYmVyIiwgdmFsaWRhdG9yOiB2YWxpZGF0b3JOdW1iZXJaZXJvLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBwYXR0ZXJuOiAvXigoWzAtOV1cZHswLDEyfSkpKFwuXGR7MCw0fSk/JC8sDQogICAgICAgICAgICBtZXNzYWdlOiAi5Y+q6IO95L+d55WZ5Zub5L2N5bCP5pWwIiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBsdW1wc3RhcnRkYXRlOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsDQogICAgICAgICAgICB2YWxpZGF0b3I6IHZhbGlkYXRlbHVtcHN0YXJ0ZGF0ZSwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBsdW1wZW5kZGF0ZTogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgdHlwZTogImRhdGUiLA0KICAgICAgICAgICAgdmFsaWRhdG9yOiB2YWxpZGF0ZWx1bXBlbmRkYXRlLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGZlZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHR5cGU6ICJudW1iZXIiLCB2YWxpZGF0b3I6IHZhbGlkYXRvck51bWJlciwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgcGF0dGVybjogL14oKFsxLTldXGR7MCwxNH0pfDApKFwuXGR7MCwyfSk/JC8sDQogICAgICAgICAgICBtZXNzYWdlOiAi5Y+q6IO95L+d55WZ5Lik5L2N5bCP5pWwIiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBzdXBwbHlidXJlYXVhbW1ldGVyY29kZTogW10sDQogICAgICAgIHRyYW5zZGlzdHJpY29tcGFueTogW10sDQogICAgICAgIHZvbHRhZ2VDbGFzczogW10sDQogICAgICB9LA0KICAgICAgZWxlY3Rybzogew0KICAgICAgICBjb2x1bW5zOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLluo/lj7ciLA0KICAgICAgICAgICAgdHlwZTogImluZGV4IiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi55So55S157G75Z6LIiwNCiAgICAgICAgICAgIGtleTogInR5cGVOYW1lIiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5omA5Y2g5q+U5L6LKCUpIiwNCiAgICAgICAgICAgIGtleTogInJhdGlvIiwNCiAgICAgICAgICAgIHJlbmRlckhlYWRlcjogcmVuZGVySGVhZGVyLA0KICAgICAgICAgICAgcmVuZGVyOiAoaCwgcGFyYW1zKSA9PiB7DQogICAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgICAgICAgICAgbGV0IHJhdGlvID0gcGFyYW1zLnJvdy5yYXRpbzsNCiAgICAgICAgICAgICAgbGV0IGlzRXJyb3IxID0gcGFyYW1zLnJvdy5pZEVycm9yMTsNCiAgICAgICAgICAgICAgbGV0IGVycm9yID0gaCgNCiAgICAgICAgICAgICAgICAibGFiZWwiLA0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgIHN0eWxlOiB7DQogICAgICAgICAgICAgICAgICAgIGNvbG9yOiAiI2VkNDAxNCIsDQogICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAiMTJweCIsDQogICAgICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6ICJTaW1TdW4iLA0KICAgICAgICAgICAgICAgICAgICBwYWRkaW5nVG9wOiAiNnB4IiwNCiAgICAgICAgICAgICAgICAgICAgbGluZUhlaWdodDogMSwNCiAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogImJvbGQiLA0KICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBudWxsICE9IHJhdGlvID8gIm5vbmUiIDogImlubGluZS1ibG9jayIsDQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgIuS4jeiDveS4uuepuiINCiAgICAgICAgICAgICAgKTsNCiAgICAgICAgICAgICAgbGV0IGVycm9yMSA9IGgoDQogICAgICAgICAgICAgICAgImxhYmVsIiwNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICBzdHlsZTogew0KICAgICAgICAgICAgICAgICAgICBjb2xvcjogIiNlZDQwMTQiLA0KICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogIjEycHgiLA0KICAgICAgICAgICAgICAgICAgICBmb250RmFtaWx5OiAiU2ltU3VuIiwNCiAgICAgICAgICAgICAgICAgICAgcGFkZGluZ1RvcDogIjZweCIsDQogICAgICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDEsDQogICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICJib2xkIiwNCiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogaXNFcnJvcjEgPT0gdHJ1ZSA/ICJpbmxpbmUtYmxvY2siIDogIm5vbmUiLA0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICLovpPlhaXmr5TkvovkuI3lkIjmoLzopoHmsYIiDQogICAgICAgICAgICAgICk7DQogICAgICAgICAgICAgIGxldCByZXN1bHQgPSBoKCJJbnB1dE51bWJlciIsIHsNCiAgICAgICAgICAgICAgICBzdHlsZTogew0KICAgICAgICAgICAgICAgICAgYm9yZGVyOiBudWxsID09IHJhdGlvIHx8IGlzRXJyb3IxID09IHRydWUgPyAiMXB4IHNvbGlkICNlZDQwMTQiIDogIiIsDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBwcm9wczogew0KICAgICAgICAgICAgICAgICAgdmFsdWU6IHJhdGlvLA0KICAgICAgICAgICAgICAgICAgbWF4OiAxMDAsDQogICAgICAgICAgICAgICAgICBtaW46IDAuMSwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIG9uOiB7DQogICAgICAgICAgICAgICAgICAib24tY2hhbmdlIjogKHYpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgaWYgKHYgPT0gdW5kZWZpbmVkIHx8IHYgPT0gbnVsbCkgew0KICAgICAgICAgICAgICAgICAgICAgIHRoYXQuaXNFcnJvciA9IHRydWU7DQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgdGhhdC5pc0Vycm9yID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgLy/nu5lkYXRh6YeN5paw6LWL5YC8DQogICAgICAgICAgICAgICAgICAgIC8vIGxldCByZWcgPSAvXig/OlsxLTldP1xkfDEwMCkkLzsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHJlZyA9IC9eLT8oKFsxLTldWzAtOV0qKXwoKFswXVwuXGR7MSwyfXxbMS05XVswLTldKlwuXGR7MSwyfSkpKSQvOw0KICAgICAgICAgICAgICAgICAgICBpZiAodiAhPSB1bmRlZmluZWQgJiYgdiAhPSBudWxsICYmICFyZWcudGVzdCh2KSkgew0KICAgICAgICAgICAgICAgICAgICAgIHBhcmFtcy5yb3cuaWRFcnJvcjEgPSB0cnVlOw0KICAgICAgICAgICAgICAgICAgICAgIHRoYXQuaXNFcnJvcjEgPSB0cnVlOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgIHBhcmFtcy5yb3cuaWRFcnJvcjEgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICB0aGF0LmlzRXJyb3IxID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgcGFyYW1zLnJvdy5yYXRpbyA9IHY7DQogICAgICAgICAgICAgICAgICAgIHRoYXQuZWxlY3Ryby5kYXRhW3BhcmFtcy5yb3cuX2luZGV4XSA9IHBhcmFtcy5yb3c7DQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICByZXR1cm4gaCgiZGl2IiwgW3Jlc3VsdCwgZXJyb3IsIGVycm9yMV0pOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5YWz6IGU5bGA56uZIiwNCiAgICAgICAgICAgIGtleTogInN0YXRpb25OYW1lIiwNCiAgICAgICAgICAgIHJlbmRlcjogKGgsIHBhcmFtcykgPT4gew0KICAgICAgICAgICAgICBsZXQgc3RhdGlvbk5hbWUgPSBwYXJhbXMucm93LnN0YXRpb25OYW1lOw0KICAgICAgICAgICAgICBsZXQgZGlzYWJsZWQgPSBwYXJhbXMucm93Ll9kaXNhYmxlZDsNCiAgICAgICAgICAgICAgaWYgKGRpc2FibGVkICE9IHVuZGVmaW5lZCAmJiBkaXNhYmxlZCA9PSB0cnVlKSB7DQogICAgICAgICAgICAgICAgcmV0dXJuIGgoIklucHV0Iiwgew0KICAgICAgICAgICAgICAgICAgcHJvcHM6IHsNCiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHN0YXRpb25OYW1lLA0KICAgICAgICAgICAgICAgICAgICByZWFkb25seTogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgcmV0dXJuIGgoIklucHV0Iiwgew0KICAgICAgICAgICAgICAgICAgcHJvcHM6IHsNCiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHN0YXRpb25OYW1lLA0KICAgICAgICAgICAgICAgICAgICBpY29uOiAiaW9zLWFyY2hpdmUiLA0KICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcjogIueCueWHu+Wbvuagh+mAieaLqSIsDQogICAgICAgICAgICAgICAgICAgIHJlYWRvbmx5OiB0cnVlLA0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgIG9uOiB7DQogICAgICAgICAgICAgICAgICAgICJvbi1jbGljayI6ICh2KSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgdGhpcy5jaG9vc2VSZXNwb25zZUNlbnRlcigyLCBwYXJhbXMsIHBhcmFtcy5yb3cuX2luZGV4KTsNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgZGF0YTogW10sDQogICAgICB9LA0KICAgICAgYW1tZXRlcjogew0KICAgICAgICBtYXhkZWdyZWU6IG51bGwsDQogICAgICAgIGFtbWV0ZXJuYW1lOiAiIiwNCiAgICAgICAgdXNlcnVuaXQ6ICIiLA0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgY291bnRyeTogbnVsbCwNCiAgICAgICAgY29tcGFueTogbnVsbCwNCiAgICAgICAgY291bnRyeU5hbWU6IG51bGwsDQogICAgICAgIGJpbGxTdGF0dXM6IDAsDQogICAgICAgIGVsZWN0cmljVHlwZXM6IFtdLA0KICAgICAgICBlbGVjdHJvOiBbXSwNCiAgICAgICAgY2xhc3NpZmljYXRpb25zOiBbXSwgLy/nlKjnlLXnsbvlnosNCiAgICAgICAgaXN6Z3o6IDAsDQogICAgICAgIHVzZXJ1bml0Q29kZTogMCwNCiAgICAgICAgZGlyZWN0RmxhZzogMCwNCiAgICAgICAgb2ZmaWNlRmxhZzogMCwNCiAgICAgICAgdHJhbnNkaXN0cmljb21wYW55OiAxLA0KICAgICAgICB2b2x0YWdlQ2xhc3M6ICIiLA0KICAgICAgICBpc3NtYXJ0YW1tZXRlcjogIjAiLA0KICAgICAgICBwcmljZTogIiIsDQogICAgICAgIHN0YXRpb25jb2RlNWdyOiBudWxsLA0KICAgICAgICBzdGF0aW9ubmFtZTVncjogbnVsbCwNCiAgICAgIH0sDQogICAgICBpc3pnek9ubHk6IGZhbHNlLA0KICAgIH07DQogIH0sDQoNCiAgbWV0aG9kczogew0KICAgIC4uLm1hcE11dGF0aW9ucyhbImNsb3NlVGFnIiwgImNsb3NlVGFnQnlOYW1lIl0pLA0KICAgIGdldFByb3BlcnR5KHYpIHt9LA0KICAgIE9LKHR5cGUpIHsNCiAgICAgIC8vIGlmICgNCiAgICAgIC8vICAgdGhpcy5hbW1ldGVyLnByaWNlID09IHVuZGVmaW5lZCB8fA0KICAgICAgLy8gICB0aGlzLmFtbWV0ZXIucHJpY2UgPT0gbnVsbCB8fA0KICAgICAgLy8gICB0aGlzLmFtbWV0ZXIucHJpY2UgPT0gIiINCiAgICAgIC8vICkgew0KICAgICAgLy8gICB0aGlzLiRNZXNzYWdlLmVycm9yKCLljZXku7co5ZCr56iO5YWDKeS4jeiDveS4uuepuiIpOw0KICAgICAgLy8gICByZXR1cm47DQogICAgICAvLyB9DQogICAgICAvLyBpZiAoDQogICAgICAvLyAgIHRoaXMuYW1tZXRlci5tYWduaWZpY2F0aW9uID09IHVuZGVmaW5lZCB8fA0KICAgICAgLy8gICB0aGlzLmFtbWV0ZXIubWFnbmlmaWNhdGlvbiA9PSBudWxsIHx8DQogICAgICAvLyAgIHRoaXMuYW1tZXRlci5tYWduaWZpY2F0aW9uID09ICIiDQogICAgICAvLyApIHsNCiAgICAgIC8vICAgdGhpcy4kTWVzc2FnZS5lcnJvcigi5YCN546H5LiN6IO95Li656m6Iik7DQogICAgICAvLyAgIHJldHVybjsNCiAgICAgIC8vIH0NCiAgICAgIC8vIGlmICh0aGlzLmFtbWV0ZXIuc3RhdHVzID09IDEpIHsNCiAgICAgIC8vICAgaWYgKA0KICAgICAgLy8gICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uTmFtZSA9PSB1bmRlZmluZWQgfHwNCiAgICAgIC8vICAgICB0aGlzLmFtbWV0ZXIuc3RhdGlvbk5hbWUgPT0gbnVsbCB8fA0KICAgICAgLy8gICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uTmFtZSA9PSAiIg0KICAgICAgLy8gICApIHsNCiAgICAgIC8vICAgICB0aGlzLiRNZXNzYWdlLmVycm9yKCLlsYAo56uZKeWQjeensOS4jeiDveS4uuepuiIpOw0KICAgICAgLy8gICAgIHJldHVybjsNCiAgICAgIC8vICAgfQ0KICAgICAgLy8gfQ0KICAgICAgaWYgKHR5cGUgPT0gMSkgew0KICAgICAgICB0aGlzLmlzTG9hZGluZyA9IDE7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmlzTG9hZGluZyA9IDA7DQogICAgICB9DQogICAgICBpZiAodGhpcy5sb2FkaW5nID09IHRydWUpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIHRoaXMuYW1tZXRlci5lbGVjdHJpY1R5cGVzID0gdGhpcy5lbGVjdHJvLmRhdGE7DQogICAgICB0aGlzLiRyZWZzLmFtbWV0ZXIudmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMuJHJlZnMuYW1tZXRlcjEudmFsaWRhdGUoKHZhbGlkMSkgPT4gew0KICAgICAgICAgICAgaWYgKHZhbGlkMSkgew0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLmFtbWV0ZXIyLnZhbGlkYXRlKCh2YWxpZDIpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAodmFsaWQyKSB7DQogICAgICAgICAgICAgICAgICB0aGlzLmNoZWNrRGF0YSh0eXBlKTsNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5lcnJvcigi5Lia5Li75L+h5oGv6aqM6K+B5rKh6YCa6L+HIik7DQogICAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5lcnJvcigi5YWz6IGU5bGA56uZ5L+h5oGv6aqM6K+B5rKh6YCa6L+HIik7DQogICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIHRoaXMuJE1lc3NhZ2UuZXJyb3IoIuWfuuacrOS/oeaBr+mqjOivgeayoemAmui/hyIpOw0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgb25Nb2RhbE9LKHR5cGUpIHsNCiAgICAgIHRoaXMuaXNEaXNhYmxlID0gdHJ1ZTsNCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLmlzRGlzYWJsZSA9IGZhbHNlOyAvL+eCueWHu+S4gOasoeaXtumalOS4pOenkuWQjuaJjeiDveWGjeasoeeCueWHuw0KICAgICAgfSwgMzAwMCk7DQogICAgICBsZXQgYXR0YWNoRGF0YSA9IFtdOw0KICAgICAgdGhpcy5PSyh0eXBlKTsNCiAgICB9LA0KDQogICAgc2V0QXR0YWNoRGF0YShkYXRhKSB7DQogICAgICB0aGlzLm11bHRpRmlsZXMgPSBkYXRhLmRhdGE7DQogICAgICB0aGlzLnJlbW92ZUlkcyA9IGRhdGEuaWRzOw0KICAgICAgaWYgKHRoaXMucmVtb3ZlSWRzLmxlbmd0aCAhPSAwICYmIGRhdGEudHlwZSA9PSAicmVtb3ZlIikgew0KICAgICAgICB0aGlzLnJlbW92ZUF0dGFjaCgpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy51cGxvYWQoKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHJlbW92ZUF0dGFjaCgpIHsNCiAgICAgIHJlbW92ZUF0dGFjaCh7IGlkczogdGhpcy5yZW1vdmVJZHMuam9pbigpIH0pLnRoZW4oKCkgPT4ge30pOw0KICAgIH0sDQogICAgdXBsb2FkKCkgew0KICAgICAgaWYgKHRoaXMuYXR0YWNoRGF0YS5sZW5ndGggIT0gMCAmJiB0aGlzLm11bHRpRmlsZXMubGVuZ3RoICE9IDApIHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgYXhpb3MNCiAgICAgICAgICAucmVxdWVzdCh7DQogICAgICAgICAgICB1cmw6ICIvY29tbW9uL2F0dGFjaG1lbnRzL3VwbG9hZE11bHRpRmlsZSIsDQogICAgICAgICAgICBtZXRob2Q6ICJwb3N0IiwNCiAgICAgICAgICAgIGRhdGE6IHRoaXMubXVsdGlGaWxlcywNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuZGF0YS5jb2RlICE9IDApIHsNCiAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICAgICAgICBhdHRjaExpc3QoeyBidXNpSWQ6IHRoYXQuZmlsZVBhcmFtLmJ1c2lJZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgdGhhdC5hdHRhY2hEYXRhID0gT2JqZWN0LmFzc2lnbihbXSwgcmVzLmRhdGEucm93cyk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy/pqozor4HmlbDmja4NCiAgICBjaGVja0RhdGEodHlwZSkgew0KICAgICAgbGV0IHR5cGVzID0gdGhpcy5hbW1ldGVyLmNsYXNzaWZpY2F0aW9uczsNCiAgICAgIHRoaXMuYW1tZXRlci5lbGVjdHJvdHlwZSA9IHR5cGVzW3R5cGVzLmxlbmd0aCAtIDFdOw0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgaWYgKA0KICAgICAgICB0aGlzLmFtbWV0ZXIuc3RhdHVzID09PSAxICYmDQogICAgICAgICh0aGlzLmNvbmZpZ1ZlcnNpb24gPT0gInNjIiB8fCB0aGlzLmNvbmZpZ1ZlcnNpb24gPT0gIlNDIikNCiAgICAgICkgew0KICAgICAgICAvL+WcqOeUqOeKtuaAgeS4i+mqjOivgeWxgOermeWcsOWdgOS4jeiDveS4uuepug0KICAgICAgICBpZiAoDQogICAgICAgICAgdGhpcy5hbW1ldGVyLnN0YXRpb25hZGRyZXNzID09IG51bGwgfHwNCiAgICAgICAgICB0aGlzLmFtbWV0ZXIuc3RhdGlvbmFkZHJlc3MgPT0gdW5kZWZpbmVkDQogICAgICAgICkgew0KICAgICAgICAgIHRoaXMuJE1vZGFsLndhcm5pbmcoew0KICAgICAgICAgICAgdGl0bGU6ICLmuKnppqjmj5DnpLoiLA0KICAgICAgICAgICAgY29udGVudDogIuWxgOermeWcsOWdgOS4jeiDveS4uuepuu+8jOivt+WcqOWxgOermeeuoeeQhue7tOaKpOivpeWxgOermeS/oeaBr++8gSIsDQogICAgICAgICAgfSk7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAodGhpcy5jaGVja0VsZWN0cmljVHlwZUl0ZW0oKSkgew0KICAgICAgICBpZiAodGhpcy5hbW1ldGVyLmFtbWV0ZXJuYW1lICE9IHVuZGVmaW5lZCB8fCB0aGlzLmFtbWV0ZXIuYW1tZXRlcm5hbWUgIT0gbnVsbCkgew0KICAgICAgICAgIGNoZWNrQW1tZXRlckV4aXN0KHRoaXMuYW1tZXRlci5pZCwgdGhpcy5hbW1ldGVyLmFtbWV0ZXJuYW1lLCAwKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIC8v6aqM6K+B55S16KGo5piv5ZCm5a2Y5ZyoDQogICAgICAgICAgICBsZXQgY29kZSA9IHJlcy5kYXRhLmNvZGU7DQogICAgICAgICAgICBpZiAoY29kZSA9PSAwKSB7DQogICAgICAgICAgICAgIHRoaXMuY2hlY2tlZERhdGUodHlwZSk7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGF0LmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmNoZWNrZWREYXRlKHR5cGUpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBjaGVja2VkRGF0ZSh0eXBlKSB7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICBjaGVja1Byb2plY3ROYW1lRXhpc3QodGhhdC5hbW1ldGVyLmlkLCB0aGF0LmFtbWV0ZXIucHJvamVjdG5hbWUsIDApLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAvL+mqjOivgemhueebruWQjeensOaYr+WQpuWtmOWcqA0KICAgICAgICBsZXQgY29kZSA9IHJlcy5kYXRhLmNvZGU7DQogICAgICAgIGlmIChjb2RlID09IDApIHsNCiAgICAgICAgICBpZiAodGhhdC5pc0NoZWNrU3RhdGlvbiA9PSB0cnVlKSB7DQogICAgICAgICAgICB0aGF0LmlzQ2hlY2tTdGF0aW9uKHR5cGUpOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGF0LnNhdmVEYXRhKHR5cGUpOyAvL+S/neWtmOaVsOaNrg0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGF0LmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBpc0NoZWNrU3RhdGlvbih0eXBlKSB7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICBjaGVja0FtbWV0ZXJCeVN0YXRpb24oew0KICAgICAgICBpZDogdGhhdC5hbW1ldGVyLmlkLA0KICAgICAgICB0eXBlOiAwLA0KICAgICAgICBzdGF0aW9uY29kZTogdGhhdC5hbW1ldGVyLnN0YXRpb25jb2RlLA0KICAgICAgICBlbGVjdHJvdHlwZTogdGhhdC5hbW1ldGVyLmVsZWN0cm90eXBlLA0KICAgICAgICBhbW1ldGVydXNlOiB0aGF0LmFtbWV0ZXIuYW1tZXRlcnVzZSwNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBsZXQgY29kZSA9IHJlcy5kYXRhLmNvZGU7DQogICAgICAgIGlmIChjb2RlID09ICJlcnJvciIpIHsNCiAgICAgICAgICB0aGF0LiRNb2RhbC53YXJuaW5nKHsgdGl0bGU6ICLmuKnppqjmj5DnpLoiLCBjb250ZW50OiByZXMuZGF0YS5tc2cgfSk7DQogICAgICAgICAgdGhhdC5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhhdC5zYXZlRGF0YSh0eXBlKTsgLy/kv53lrZjmlbDmja4NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBzYXZlRGF0YSh0eXBlKSB7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICB0aGlzLmNsZWFyRGF0YUJ5Q29uZGl0aW9uKCk7DQogICAgICB0aGF0LmFtbWV0ZXIuY2F0ZWdvcnkgPSAxOyAvL+eUteihqA0KICAgICAgYWRkQW1tZXRlcih0aGF0LmFtbWV0ZXIpDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLmRhdGEgIT0gbnVsbCAmJiByZXMuZGF0YSAhPSAtMSAmJiByZXMuZGF0YS5zdWNjZXNzID09ICIxIikgew0KICAgICAgICAgICAgaWYgKHR5cGUgPT0gMSkgew0KICAgICAgICAgICAgICB0aGF0LnN0YXJ0RmxvdyhyZXMuZGF0YSk7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLmNsb3NlVGFnKHsgcm91dGU6IHRoaXMuJHJvdXRlIH0pOw0KICAgICAgICAgICAgICB0aGF0Lndhcm4oKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhhdC5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB0aGF0LiROb3RpY2UuZXJyb3IoeyB0aXRsZTogIuaPkOekuiIsIGRlc2M6IHJlcy5kYXRhLm1zZywgZHVyYXRpb246IDEwIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnIpID0+IHsNCiAgICAgICAgICB0aGF0LmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICAvL+agueaNruadoeS7tuWIpOaWreaVsOaNruaYr+WQpuivpea4hemZpA0KICAgIGNsZWFyRGF0YUJ5Q29uZGl0aW9uKCkgew0KICAgICAgaWYgKHRoaXMuYW1tZXRlci5wcm9wZXJ0eSAhPT0gMiAmJiB0aGlzLmFtbWV0ZXIucHJvcGVydHkgIT09IDQpIHsNCiAgICAgICAgLy/nq5nlnYDkuqfmnYPlvZLlsZ7kuLrpk4HloZQg5riF6Zmk5YiG5Ymy5q+U5L6LY2hlY2tBbW1ldGVyQnlTdGF0aW9u77yM5piv5ZCm6ZOB5aGU5oyJUlJV5YyF5bmyDQogICAgICAgIHRoaXMuYW1tZXRlci5wZXJjZW50ID0gbnVsbDsNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmFtbWV0ZXIuYW1tZXRlcnVzZSAhPT0gMykgew0KICAgICAgICAvL+eUteihqOeUqOmAlOS4jeaYr+WbnuaUtueUtei0ue+8jOa4hemZpOeItueUteihqOS/oeaBrw0KICAgICAgICB0aGlzLmFtbWV0ZXIucGFyZW50SWQgPSBudWxsOw0KICAgICAgICB0aGlzLmFtbWV0ZXIuY3VzdG9tZXJJZCA9IG51bGw7DQogICAgICB9DQogICAgICBpZiAodGhpcy5hbW1ldGVyLmRpcmVjdHN1cHBseWZsYWcgIT0gMSkgew0KICAgICAgICAvL+WPquacieWvueWklue7k+eul+exu+Wei+S4uuebtOS+m+eUteaJjeWhq+WGmeivpeWtl+aute+8jOi9rOS+m+eUteS4jemcgOWhq+WGmQ0KICAgICAgICB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3ZhbGVuY2VuYXR1cmUgPSBudWxsOw0KICAgICAgfQ0KICAgICAgaWYgKCF0aGlzLmlzQ0RDb21wYW55KSB7DQogICAgICAgIC8v5oiQ6YO95YiG5YWs5Y+45pi+56S65ZCI5ZCM5a+55pa5562J77yM5LiN5piv77yM5bCx5riF6Zmk5pWw5o2uDQogICAgICAgIHRoaXMuYW1tZXRlci5jb250cmFjdE90aFBhcnQgPSBudWxsOw0KICAgICAgICB0aGlzLmFtbWV0ZXIubm1DY29kZSA9IG51bGw7DQogICAgICAgIHRoaXMuYW1tZXRlci5ubUwyMTAwID0gbnVsbDsNCiAgICAgICAgdGhpcy5hbW1ldGVyLm5tTDE4MDAgPSBudWxsOw0KICAgICAgICB0aGlzLmFtbWV0ZXIubm1DbDgwMG0gPSBudWxsOw0KICAgICAgfQ0KICAgIH0sDQogICAgd2FybigpIHsNCiAgICAgIHRoaXMuJE1vZGFsLndhcm5pbmcoew0KICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsDQogICAgICAgIGNvbnRlbnQ6ICLkv53lrZjlkI7nmoTmlbDmja7opoHmj5DkuqTlrqHmibnmiY3og73nlJ/mlYjvvIEiLA0KICAgICAgfSk7DQogICAgfSwNCiAgICByZWZyZXNoRGF0YSgpIHsNCiAgICAgIHRoaXMuaW5pdERhdGEoKTsNCiAgICB9LA0KICAgIGluaXREYXRhKCkgew0KICAgICAgdGhpcy5jb3VudHJ5TmFtZSA9ICIiOw0KICAgICAgdGhpcy5lbGVjdHJvLmRhdGEgPSBbXTsNCiAgICAgIHRoaXMub2xkRGF0YSA9IFtdOw0KICAgICAgdGhpcy5yZW1vdmVJZHMgPSBbXTsNCiAgICAgIHRoaXMubXVsdGlGaWxlcyA9IFtdOw0KICAgICAgdGhpcy5maWxlcyA9IFtdOw0KICAgICAgdGhpcy5pc0NpdHlBZG1pbiA9IGZhbHNlOw0KICAgICAgdGhpcy5pc0FkbWluID0gZmFsc2U7DQogICAgICB0aGlzLmlzRWRpdEJ5Q291bnRyeSA9IGZhbHNlOw0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLiRyZWZzLmFtbWV0ZXIucmVzZXRGaWVsZHMoKTsgLy8gdGhpcy4kcmVmcy5hZGR1c2VyZm9ybS5yZXNldEZpZWxkcygpOw0KICAgICAgICB0aGlzLiRyZWZzLmFtbWV0ZXIxLnJlc2V0RmllbGRzKCk7IC8vIHRoaXMuJHJlZnMuYWRkdXNlcmZvcm0ucmVzZXRGaWVsZHMoKTsNCiAgICAgICAgdGhpcy4kcmVmcy5hbW1ldGVyMi5yZXNldEZpZWxkcygpOyAvLyB0aGlzLiRyZWZzLmFkZHVzZXJmb3JtLnJlc2V0RmllbGRzKCk7DQogICAgICB9KTsNCiAgICAgIHRoaXMuc2hvd01vZGVsID0gZmFsc2U7DQogICAgICB0aGlzLmVsZWN0cmljVHlwZU1vZGVsID0gZmFsc2U7DQogICAgICB0aGlzLmRpcmVjdEZsYWcgPSAwOw0KICAgICAgdGhpcy5vZmZpY2VGbGFnID0gMDsNCiAgICAgIHRoaXMudHJhbnNkaXN0cmljb21wYW55ID0gMTsNCiAgICB9LA0KICAgIG9uTW9kYWxDYW5jZWwoKSB7DQogICAgICB0aGlzLmluaXREYXRhKCk7DQogICAgfSwNCiAgICAvKuWIneWni+WMliovDQogICAgaW5pdEFtbWV0ZXIoaWQpIHsNCiAgICAgIHRoaXMuaW5pdERhdGEoKTsNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIHRoYXQudGl0bGUgPSAi5re75Yqg55S16KGoIjsNCiAgICAgIGVkaXRBbW1ldGVyKCIiLCAwKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhhdC5zZXRBbW1ldGVyKE9iamVjdC5hc3NpZ24oe30sIHJlcy5kYXRhKSk7DQogICAgICAgLy8gdGhpcy5hbW1ldGVyLnByaWNlID0gMDsNCiAgICAgICAgdGhhdC5nZXRVc2VyKCk7DQogICAgICB9KTsNCiAgICAgIGdldENsYXNzaWZpY2F0aW9uKCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIC8v55So55S157G75Z6LDQogICAgICAgIHRoYXQuY2xhc3NpZmljYXRpb25EYXRhID0gcmVzLmRhdGE7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGNoYW5nZVN0YXR1cygpIHsNCiAgICAgIGlmICh0aGlzLmFtbWV0ZXIuc3RhdHVzID09IDEpIHsNCiAgICAgICAgdGhpcy5pc1JlcXVpcmVGbGFnID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5ydWxlVmFsaWRhdGUuc3RhdGlvbk5hbWUgPSBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UsYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuaXNSZXF1aXJlRmxhZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLnJ1bGVWYWxpZGF0ZS5zdGF0aW9uTmFtZSA9IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogZmFsc2UsDQogICAgICAgICAgICBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UsYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHNlbGVjdENoYW5nZSgpIHsNCiAgICAgIGlmICh0aGlzLmFtbWV0ZXIuY29tcGFueSAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgaWYgKHRoaXMuYW1tZXRlci5jb21wYW55ID09ICIxMDAwMDg1Iikgew0KICAgICAgICAgIHRoaXMuaXNDRENvbXBhbnkgPSB0cnVlOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuaXNDRENvbXBhbnkgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgICBnZXRDb3VudHJ5QnlVc2VySWQodGhpcy5hbW1ldGVyLmNvbXBhbnkpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIHRoaXMuZGVwYXJ0bWVudHMgPSByZXMuZGF0YS5kZXBhcnRtZW50czsNCiAgICAgICAgICB0aGlzLmFtbWV0ZXIuY291bnRyeSA9IHJlcy5kYXRhLmRlcGFydG1lbnRzWzBdLmlkOw0KICAgICAgICAgIHRoaXMuYW1tZXRlci5jb3VudHJ5TmFtZSA9IHRoaXMuZGVwYXJ0bWVudHNbMF0ubmFtZTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBnZXRVc2VyKCkgew0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgZ2V0VXNlckJ5VXNlclJvbGUoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgLy/lvZPliY3nmbvlvZXnlKjmiLfmiYDlnKjlhazlj7gNCiAgICAgICAgdGhhdC5jb21wYW5pZXMgPSByZXMuZGF0YS5jb21wYW5pZXM7DQogICAgICAgIHRoYXQuaXNDaXR5QWRtaW4gPSByZXMuZGF0YS5pc0VkaXRBZG1pbjsNCiAgICAgICAgaWYgKA0KICAgICAgICAgIHJlcy5kYXRhLmlzQ2l0eUFkbWluID09IHRydWUgfHwNCiAgICAgICAgICByZXMuZGF0YS5pc1Byb0FkbWluID09IHRydWUgfHwNCiAgICAgICAgICByZXMuZGF0YS5pc1N1YkFkbWluID09IHRydWUNCiAgICAgICAgKSB7DQogICAgICAgICAgdGhhdC5pc0FkbWluID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgICBnZXRDb3VudHJ5c2RhdGEoeyBvcmdDb2RlOiByZXMuZGF0YS5jb21wYW5pZXNbMF0uaWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgLy/moLnmja7mnYPpmZDojrflj5bmiYDlsZ7pg6jpl6gNCiAgICAgICAgICB0aGF0LmRlcGFydG1lbnRzID0gcmVzLmRhdGE7DQogICAgICAgICAgdGhhdC5nZXRVc2VyRGF0YSgpOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0VXNlckRhdGEoKSB7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICBnZXRVc2VyZGF0YSgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAvL+W9k+WJjeeZu+W9leeUqOaIt+aJgOWcqOWFrOWPuOWSjOaJgOWxnumDqOmXqA0KICAgICAgICBsZXQgY29tcGFuaWVzID0gdGhhdC5jb21wYW5pZXM7DQogICAgICAgIGlmIChyZXMuZGF0YS5jb21wYW5pZXMgIT0gbnVsbCAmJiByZXMuZGF0YS5jb21wYW5pZXMubGVuZ3RoICE9IDApIHsNCiAgICAgICAgICBpZiAocmVzLmRhdGEuY29tcGFuaWVzWzBdLmlkICE9ICIyNjAwMDAwMDAwIikgew0KICAgICAgICAgICAgY29tcGFuaWVzID0gcmVzLmRhdGEuY29tcGFuaWVzOw0KICAgICAgICAgICAgLy8gOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICB0aGF0LmNvbXBhbnkgPSBjb21wYW5pZXNbMF0uaWQ7DQogICAgICAgIGlmICh0aGF0LmNvbXBhbnkgPT0gIjEwMDAwODUiKSB7DQogICAgICAgICAgdGhhdC5pc0NEQ29tcGFueSA9IHRydWU7DQogICAgICAgIH0NCiAgICAgICAgdGhhdC5hbW1ldGVyLmNvbXBhbnkgPSBjb21wYW5pZXNbMF0uaWQ7DQoNCiAgICAgICAgbGV0IGRlcGFydG1lbnRzID0gdGhhdC5kZXBhcnRtZW50czsNCiAgICAgICAgaWYgKHJlcy5kYXRhLmRlcGFydG1lbnRzICE9IG51bGwgJiYgcmVzLmRhdGEuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApIHsNCiAgICAgICAgICBpZiAocmVzLmRhdGEuY29tcGFuaWVzWzBdLmlkICE9ICIyNjAwMDAwMDAwIikgew0KICAgICAgICAgICAgZGVwYXJ0bWVudHMgPSByZXMuZGF0YS5kZXBhcnRtZW50czsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgdGhhdC5jb3VudHJ5ID0gZGVwYXJ0bWVudHNbMF0uaWQ7DQogICAgICAgIHRoYXQuY291bnRyeU5hbWUgPSBkZXBhcnRtZW50c1swXS5uYW1lOw0KICAgICAgICB0aGF0LmFtbWV0ZXIuY291bnRyeSA9IE51bWJlcihkZXBhcnRtZW50c1swXS5pZCk7DQogICAgICAgIHRoYXQuYW1tZXRlci5jb3VudHJ5TmFtZSA9IGRlcGFydG1lbnRzWzBdLm5hbWU7DQogICAgICB9KTsNCiAgICB9LA0KICAgIHNldE9sZERhdGEoZGF0YSkgew0KICAgICAgdGhpcy5vbGRDYXRlZ29yeSA9IGJ0ZXh0KHsNCiAgICAgICAgY2F0ZWdvcnk6ICJhbW1ldGVyQ2F0ZWdvcnkiLA0KICAgICAgICB2OiBkYXRhLmNhdGVnb3J5LA0KICAgICAgICB2YWx1ZUZpZWxkOiAidHlwZUNvZGUiLA0KICAgICAgICBsYWJlbEZpZWxkOiAidHlwZU5hbWUiLA0KICAgICAgfSk7DQogICAgICB0aGlzLm9sZFBhY2thZ2V0eXBlID0gYnRleHQoew0KICAgICAgICBjYXRlZ29yeTogInBhY2thZ2VUeXBlIiwNCiAgICAgICAgdjogZGF0YS5wYWNrYWdldHlwZSwNCiAgICAgICAgdmFsdWVGaWVsZDogInR5cGVDb2RlIiwNCiAgICAgICAgbGFiZWxGaWVsZDogInR5cGVOYW1lIiwNCiAgICAgIH0pOw0KICAgICAgdGhpcy5vbGRQYXlwZXJpb2QgPSBidGV4dCh7DQogICAgICAgIGNhdGVnb3J5OiAicGF5UGVyaW9kIiwNCiAgICAgICAgdjogZGF0YS5wYXlwZXJpb2QsDQogICAgICAgIHZhbHVlRmllbGQ6ICJ0eXBlQ29kZSIsDQogICAgICAgIGxhYmVsRmllbGQ6ICJ0eXBlTmFtZSIsDQogICAgICB9KTsNCiAgICAgIHRoaXMub2xkUGF5dHlwZSA9IGJ0ZXh0KHsNCiAgICAgICAgY2F0ZWdvcnk6ICJwYXlUeXBlIiwNCiAgICAgICAgdjogZGF0YS5wYXl0eXBlLA0KICAgICAgICB2YWx1ZUZpZWxkOiAidHlwZUNvZGUiLA0KICAgICAgICBsYWJlbEZpZWxkOiAidHlwZU5hbWUiLA0KICAgICAgfSk7DQogICAgICB0aGlzLm9sZEVsZWN0cm9uYXR1cmUgPSBidGV4dCh7DQogICAgICAgIGNhdGVnb3J5OiAiZWxlY3Ryb05hdHVyZSIsDQogICAgICAgIHY6IGRhdGEuZWxlY3Ryb25hdHVyZSwNCiAgICAgICAgdmFsdWVGaWVsZDogInR5cGVDb2RlIiwNCiAgICAgICAgbGFiZWxGaWVsZDogInR5cGVOYW1lIiwNCiAgICAgIH0pOw0KICAgICAgdGhpcy5vbGRFbGVjdHJvdmFsZW5jZW5hdHVyZSA9IGJ0ZXh0KHsNCiAgICAgICAgY2F0ZWdvcnk6ICJlbGVjdHJvdmFsZW5jZU5hdHVyZSIsDQogICAgICAgIHY6IGRhdGEuZWxlY3Ryb3ZhbGVuY2VuYXR1cmUsDQogICAgICAgIHZhbHVlRmllbGQ6ICJ0eXBlQ29kZSIsDQogICAgICAgIGxhYmVsRmllbGQ6ICJ0eXBlTmFtZSIsDQogICAgICB9KTsNCiAgICAgIHRoaXMub2xkRWxlY3Ryb3R5cGUgPSBidGV4dCh7DQogICAgICAgIGNhdGVnb3J5OiAiZWxlY3Ryb1R5cGUiLA0KICAgICAgICB2OiBkYXRhLmVsZWN0cm90eXBlLA0KICAgICAgICB2YWx1ZUZpZWxkOiAidHlwZUNvZGUiLA0KICAgICAgICBsYWJlbEZpZWxkOiAidHlwZU5hbWUiLA0KICAgICAgfSk7DQogICAgICB0aGlzLm9sZFN0YXR1cyA9IGJ0ZXh0KHsNCiAgICAgICAgY2F0ZWdvcnk6ICJzdGF0dXMiLA0KICAgICAgICB2OiBkYXRhLnN0YXR1cywNCiAgICAgICAgdmFsdWVGaWVsZDogInR5cGVDb2RlIiwNCiAgICAgICAgbGFiZWxGaWVsZDogInR5cGVOYW1lIiwNCiAgICAgIH0pOw0KICAgICAgdGhpcy5vbGRQcm9wZXJ0eSA9IGJ0ZXh0KHsNCiAgICAgICAgY2F0ZWdvcnk6ICJwcm9wZXJ0eSIsDQogICAgICAgIHY6IGRhdGEucHJvcGVydHksDQogICAgICAgIHZhbHVlRmllbGQ6ICJ0eXBlQ29kZSIsDQogICAgICAgIGxhYmVsRmllbGQ6ICJ0eXBlTmFtZSIsDQogICAgICB9KTsNCiAgICAgIHRoaXMub2xkQW1tZXRlcnR5cGUgPSBidGV4dCh7DQogICAgICAgIGNhdGVnb3J5OiAiYW1tZXRlclR5cGUiLA0KICAgICAgICB2OiBkYXRhLmFtbWV0ZXJ0eXBlLA0KICAgICAgICB2YWx1ZUZpZWxkOiAidHlwZUNvZGUiLA0KICAgICAgICBsYWJlbEZpZWxkOiAidHlwZU5hbWUiLA0KICAgICAgfSk7DQogICAgICB0aGlzLm9sZFN0YXRpb25zdGF0dXMgPSBidGV4dCh7DQogICAgICAgIGNhdGVnb3J5OiAic3RhdGlvblN0YXR1cyIsDQogICAgICAgIHY6IGRhdGEuc3RhdGlvbnN0YXR1cywNCiAgICAgICAgdmFsdWVGaWVsZDogInR5cGVDb2RlIiwNCiAgICAgICAgbGFiZWxGaWVsZDogInR5cGVOYW1lIiwNCiAgICAgIH0pOw0KICAgICAgdGhpcy5vbGRTdGF0aW9udHlwZSA9IGJ0ZXh0KHsNCiAgICAgICAgY2F0ZWdvcnk6ICJCVVJfU1RBTkRfVFlQRSIsDQogICAgICAgIHY6IGRhdGEuc3RhdGlvbnR5cGUsDQogICAgICAgIHZhbHVlRmllbGQ6ICJ0eXBlQ29kZSIsDQogICAgICAgIGxhYmVsRmllbGQ6ICJ0eXBlTmFtZSIsDQogICAgICB9KTsNCiAgICAgIHRoaXMub2xkQW1tZXRlcnVzZSA9IGJ0ZXh0KHsNCiAgICAgICAgY2F0ZWdvcnk6ICJhbW1ldGVyVXNlIiwNCiAgICAgICAgdjogZGF0YS5hbW1ldGVydXNlLA0KICAgICAgICB2YWx1ZUZpZWxkOiAidHlwZUNvZGUiLA0KICAgICAgICBsYWJlbEZpZWxkOiAidHlwZU5hbWUiLA0KICAgICAgfSk7DQogICAgICB0aGlzLm9sZERpcmVjdHN1cHBseWZsYWcgPSBidGV4dCh7DQogICAgICAgIGNhdGVnb3J5OiAiZGlyZWN0U3VwcGx5RmxhZyIsDQogICAgICAgIHY6IGRhdGEuZGlyZWN0c3VwcGx5ZmxhZywNCiAgICAgICAgdmFsdWVGaWVsZDogInR5cGVDb2RlIiwNCiAgICAgICAgbGFiZWxGaWVsZDogInR5cGVOYW1lIiwNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBzZXRBbW1ldGVyKGZvcm0pIHsNCiAgICAgIGlmIChmb3JtLnN0YXR1cyA9PSBudWxsKSB7DQogICAgICAgIGZvcm0uc3RhdHVzID0gMTsNCiAgICAgICAgdGhpcy5pc1JlcXVpcmVGbGFnID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5ydWxlVmFsaWRhdGUuc3RhdGlvbk5hbWUgPSBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdOw0KICAgICAgfQ0KICAgICAgZm9ybS5pc3NtYXJ0YW1tZXRlciA9IGZvcm0uaXNzbWFydGFtbWV0ZXIgPT0gbnVsbCA/ICIwIiA6IGZvcm0uaXNzbWFydGFtbWV0ZXIgKyAiIjsNCiAgICAgIGZvcm0uaXN6Z3ogPSBmb3JtLmlzemd6ID09IG51bGwgPyAiMCIgOiBmb3JtLmlzemd6ICsgIiI7DQogICAgICBmb3JtLmRpcmVjdEZsYWcgPSBmb3JtLmRpcmVjdEZsYWcgPT0gbnVsbCA/ICIwIiA6IGZvcm0uZGlyZWN0RmxhZyArICIiOw0KICAgICAgZm9ybS5vZmZpY2VGbGFnID0gZm9ybS5vZmZpY2VGbGFnID09IG51bGwgPyAiMCIgOiBmb3JtLm9mZmljZUZsYWcgKyAiIjsNCiAgICAgIGZvcm0uaXNhaXJjb25kaXRpb25pbmcgPQ0KICAgICAgICBmb3JtLmlzYWlyY29uZGl0aW9uaW5nID09IG51bGwgPyAiMCIgOiBmb3JtLmlzYWlyY29uZGl0aW9uaW5nICsgIiI7DQogICAgICBmb3JtLmlzbHVtcHN1bSA9IGZvcm0uaXNsdW1wc3VtID09IG51bGwgPyAiMCIgOiBmb3JtLmlzbHVtcHN1bSArICIiOw0KICAgICAgdGhpcy5hbW1ldGVyID0gZm9ybTsNCiAgICAgIGxldCBlbGVjdHJvdHlwZSA9IHRoaXMuYW1tZXRlci5lbGVjdHJvdHlwZTsNCiAgICAgIGlmICgNCiAgICAgICAgZWxlY3Ryb3R5cGUgPT09IDExMSB8fA0KICAgICAgICBlbGVjdHJvdHlwZSA9PT0gMTEyIHx8DQogICAgICAgIGVsZWN0cm90eXBlID09PSAxMTMgfHwNCiAgICAgICAgZWxlY3Ryb3R5cGUgPT09IDINCiAgICAgICkgew0KICAgICAgICB0aGlzLmlzQ2xhc3NpZmljYXRpb24gPSB0cnVlOw0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuYW1tZXRlci5tYWduaWZpY2F0aW9uID09IG51bGwgfHwgdGhpcy5hbW1ldGVyLm1hZ25pZmljYXRpb24gPT0gdW5kZWZpbmVkKSB7DQogICAgICAgIHRoaXMuYW1tZXRlci5tYWduaWZpY2F0aW9uID0gMTsNCiAgICAgIH0NCiAgICAgIC8vIGlmICh0aGlzLmFtbWV0ZXIubWF4ZGVncmVlID09IG51bGwgfHwgdGhpcy5hbW1ldGVyLm1heGRlZ3JlZSA9PSB1bmRlZmluZWQpIHsNCiAgICAgIC8vICAgdGhpcy5hbW1ldGVyLm1heGRlZ3JlZSA9IDE7DQogICAgICAvLyB9DQogICAgICAvLyBpZiAodGhpcy5hbW1ldGVyLnByaWNlID09IG51bGwgfHwgdGhpcy5hbW1ldGVyLnByaWNlID09IHVuZGVmaW5lZCkgew0KICAgICAgLy8gICB0aGlzLmFtbWV0ZXIucHJpY2UgPSAxOw0KICAgICAgLy8gfQ0KICAgICAgaWYgKHRoaXMuYW1tZXRlci5jb21wYW55ICE9IG51bGwpIHsNCiAgICAgICAgdGhpcy5hbW1ldGVyLmNvbXBhbnkgPSB0aGlzLmFtbWV0ZXIuY29tcGFueSArICIiOw0KICAgICAgfQ0KICAgICAgdGhpcy5maWxlUGFyYW0uYnVzaUlkID0gdGhpcy5hbW1ldGVyLmlkOw0KICAgIH0sDQoNCiAgICAvL+S/ruaUueeUteihqOOAgeWNj+iurueahOeUqOeUteexu+Wei+aXtu+8jOWmgueUqOeUteexu+Wei+S4jeWGjeS4juWOn+WFiOmAieaLqeeahOWxgOermeeahOWxgOermeexu+Wei+WMuemFjeaXtu+8jOezu+e7n+iHquWKqOa4heepuuWOn+WFs+iBlOWxgOerme+8jOmcgOeUqOaIt+mHjeaWsOWGjeWFs+iBlOWxgOermeOAgg0KICAgIGNoYW5nZUNsYXNzaWZpY2F0aW9ucyh2YWx1ZSkgew0KICAgICAgdGhpcy5jbGVhclN0YXRpb24oKTsNCiAgICAgIHRoaXMuaXNDbGFzc2lmaWNhdGlvbiA9IGZhbHNlOw0KICAgICAgaWYgKHZhbHVlLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHRoaXMuYW1tZXRlci5wcm9wZXJ0eSA9IG51bGw7DQogICAgICAgIHRoaXMucHJvcGVydHlSZWFkb25seSA9IHRydWU7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnByb3BlcnR5UmVhZG9ubHkgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5hbW1ldGVyLmVsZWN0cm90eXBlID0gdmFsdWVbdmFsdWUubGVuZ3RoIC0gMV07DQogICAgICAgIGxldCBlbGVjdHJvdHlwZSA9IHRoaXMuYW1tZXRlci5lbGVjdHJvdHlwZTsNCiAgICAgICAgdGhpcy5pc01vYmlsZUJhc2UgPSBlbGVjdHJvdHlwZSA+IDE0MDAgPyB0cnVlIDogZmFsc2U7DQogICAgICAgIGlmIChlbGVjdHJvdHlwZSA9PSAxNDExIHx8IGVsZWN0cm90eXBlID09IDE0MTIpIHsNCiAgICAgICAgICAvL+aOp+WItuS6p+adg+W9kuWxng0KICAgICAgICAgIHRoaXMuYW1tZXRlci5wcm9wZXJ0eSA9IDI7DQogICAgICAgICAgdGhpcy5wcm9wZXJ0eVJlYWRvbmx5ID0gdHJ1ZTsNCiAgICAgICAgICB0aGlzLnJ1bGVWYWxpZGF0ZS5zdXBwbHlidXJlYXVhbW1ldGVyY29kZSA9IFsNCiAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICBdOw0KICAgICAgICB9IGVsc2UgaWYgKGVsZWN0cm90eXBlID09IDE0MjEgfHwgZWxlY3Ryb3R5cGUgPT0gMTQyMikgew0KICAgICAgICAgIHRoaXMucHJvcGVydHlSZWFkb25seSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuYW1tZXRlci5wcm9wZXJ0eSA9IDQ7DQogICAgICAgICAgdGhpcy5ydWxlVmFsaWRhdGUucHJvcGVydHkgPSBbDQogICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0eXBlOiAibnVtYmVyIiwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIF07DQogICAgICAgIH0gZWxzZSBpZiAoZWxlY3Ryb3R5cGUgPT0gMTQzMSB8fCBlbGVjdHJvdHlwZSA9PSAxNDMyKSB7DQogICAgICAgICAgdGhpcy5wcm9wZXJ0eVJlYWRvbmx5ID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5hbW1ldGVyLnByb3BlcnR5ID0gMSArICIiOw0KICAgICAgICAgIHRoaXMucnVsZVZhbGlkYXRlLnByb3BlcnR5ID0gWw0KICAgICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHlwZTogIm51bWJlciIsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICBdOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMucHJvcGVydHlSZWFkb25seSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuYW1tZXRlci5wcm9wZXJ0eSA9IG51bGw7DQogICAgICAgICAgdGhpcy5ydWxlVmFsaWRhdGUuc3VwcGx5YnVyZWF1YW1tZXRlcmNvZGUgPSBbDQogICAgICAgICAgICB7IHJlcXVpcmVkOiBmYWxzZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIF07DQogICAgICAgICAgdGhpcy5ydWxlVmFsaWRhdGUucHJvcGVydHkgPSBbDQogICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0eXBlOiAibnVtYmVyIiwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIF07DQogICAgICAgIH0NCiAgICAgICAgbGV0IHN0YXRpb250eXBlID0gdGhpcy5hbW1ldGVyLnN0YXRpb250eXBlOw0KICAgICAgICBpZiAoZWxlY3Ryb3R5cGUgPT09IDExMSB8fCBlbGVjdHJvdHlwZSA9PT0gMTEyIHx8IGVsZWN0cm90eXBlID09PSAxMTMpIHsNCiAgICAgICAgICB0aGlzLmlzQ2xhc3NpZmljYXRpb24gPSB0cnVlOw0KICAgICAgICAgIGlmIChzdGF0aW9udHlwZSAhPT0gMTAwMDEpIHsNCiAgICAgICAgICAgIHRoaXMuY2xlYXJTdGF0aW9uKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2UgaWYgKGVsZWN0cm90eXBlID09PSAxMjEgfHwgZWxlY3Ryb3R5cGUgPT09IDExMikgew0KICAgICAgICAgIGlmIChzdGF0aW9udHlwZSAhPT0gMTAwMDMgJiYgc3RhdGlvbnR5cGUgIT09IDEwMDA0KSB7DQogICAgICAgICAgICB0aGlzLmNsZWFyU3RhdGlvbigpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIGlmIChlbGVjdHJvdHlwZSA9PT0gMTMxIHx8IGVsZWN0cm90eXBlID09PSAxMzIgfHwgZWxlY3Ryb3R5cGUgPT09IDEzMykgew0KICAgICAgICAgIGlmIChzdGF0aW9udHlwZSAhPT0gMTAwMDUpIHsNCiAgICAgICAgICAgIHRoaXMuY2xlYXJTdGF0aW9uKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2UgaWYgKGVsZWN0cm90eXBlID09PSAxNDExIHx8IGVsZWN0cm90eXBlID09PSAxNDEyKSB7DQogICAgICAgICAgaWYgKA0KICAgICAgICAgICAgc3RhdGlvbnR5cGUgIT09IDEwMDAyIHx8DQogICAgICAgICAgICAoc3RhdGlvbnR5cGUgPT0gMTAwMDIgJiYgdGhpcy5wcm9wZXJ0eXJpZ2h0ICE9PSAzKQ0KICAgICAgICAgICkgew0KICAgICAgICAgICAgdGhpcy5jbGVhclN0YXRpb24oKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSBpZiAoDQogICAgICAgICAgZWxlY3Ryb3R5cGUgPT09IDE0MjEgfHwNCiAgICAgICAgICBlbGVjdHJvdHlwZSA9PT0gMTQyMiB8fA0KICAgICAgICAgIGVsZWN0cm90eXBlID09PSAxNDMxIHx8DQogICAgICAgICAgZWxlY3Ryb3R5cGUgPT09IDE0MzINCiAgICAgICAgKSB7DQogICAgICAgICAgaWYgKHN0YXRpb250eXBlICE9PSAxMDAwMikgew0KICAgICAgICAgICAgdGhpcy5jbGVhclN0YXRpb24oKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSBpZiAoZWxlY3Ryb3R5cGUgPT09IDIpIHsNCiAgICAgICAgICB0aGlzLmlzQ2xhc3NpZmljYXRpb24gPSB0cnVlOw0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLmNvbmZpZ1ZlcnNpb24gPT0gInNjIiAmJiB0aGlzLmFtbWV0ZXIuc3RhdGlvbmFkZHJlc3Njb2RlKSB7DQogICAgICAgICAgLy/igJw1MeKAneW8gOWktOmTgeWhlOermeWdgOe8lueggeaOp+WItg0KICAgICAgICAgIGlmICgNCiAgICAgICAgICAgIFsxNDExLCAxNDEyXS5pbmNsdWRlcyhlbGVjdHJvdHlwZSkgJiYNCiAgICAgICAgICAgICF0aGlzLmFtbWV0ZXIuc3RhdGlvbmFkZHJlc3Njb2RlLnN0YXJ0c1dpdGgoIjUxIikNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIHRoaXMuY2xlYXJTdGF0aW9uKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBjaGFuZ2VkaXJlY3RzdXBwbHkodmFsdWUpIHsNCiAgICAgIGlmICh0aGlzLmFtbWV0ZXIuZGlyZWN0c3VwcGx5ZmxhZyA9PSAxKSB7DQogICAgICAgIHRoaXMuaXNSZXF1aXJlRmxhZ3MgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5ydWxlVmFsaWRhdGUuc3VwcGx5YnVyZWF1YW1tZXRlcmNvZGUgPSBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5pc1JlcXVpcmVGbGFncyA9IHRydWU7DQogICAgICAgIHRoaXMucnVsZVZhbGlkYXRlLnN1cHBseWJ1cmVhdWFtbWV0ZXJjb2RlID0gWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGNsZWFyU3RhdGlvbigpIHsNCiAgICAgIC8v5riF6Zmk5bGA56uZ5L+h5oGvDQogICAgICB0aGlzLmFtbWV0ZXIuc3RhdGlvbk5hbWUgPSBudWxsOw0KICAgICAgdGhpcy5hbW1ldGVyLnN0YXRpb25jb2RlID0gbnVsbDsNCiAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uc3RhdHVzID0gbnVsbDsNCiAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9udHlwZSA9IG51bGw7DQogICAgICB0aGlzLmFtbWV0ZXIuc3RhdGlvbmFkZHJlc3MgPSBudWxsOw0KICAgICAgdGhpcy5hbW1ldGVyLnN0YXRpb25hZGRyZXNzY29kZSA9IG51bGw7DQogICAgfSwNCg0KICAgIC8v6YCJ5oup5omA5bGe6YOo6Zeo5byA5aeLDQogICAgY2hvb3NlUmVzcG9uc2VDZW50ZXIoaW5kZXgsIHBhcmFtcywgZWxlY3Ryb1Jvd051bSkgew0KICAgICAgdGhpcy5jaG9vc2VJbmRleCA9IGluZGV4Ow0KICAgICAgdGhpcy5lbGVjdHJvUm93TnVtID0gZWxlY3Ryb1Jvd051bTsNCiAgICAgIGlmIChpbmRleCA9PSAxIHx8IGluZGV4ID09IDIpIHsNCiAgICAgICAgbGV0IHR5cGVzID0gdGhpcy5hbW1ldGVyLmNsYXNzaWZpY2F0aW9uczsNCiAgICAgICAgaWYgKHR5cGVzLmxlbmd0aCA9PSAwKSB7DQogICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7IHRpdGxlOiAi5rip6aao5o+Q56S6IiwgY29udGVudDogIuivt+WFiOmAieaLqeeUqOeUteexu+Wei++8gSIgfSk7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMuYW1tZXRlci5hbW1ldGVydXNlID09IG51bGwpIHsNCiAgICAgICAgICB0aGlzLiRNb2RhbC53YXJuaW5nKHsgdGl0bGU6ICLmuKnppqjmj5DnpLoiLCBjb250ZW50OiAi6K+35YWI6YCJ5oup55S16KGo55So6YCU77yBIiB9KTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgaWYgKHRoaXMuYW1tZXRlci5jb21wYW55ID09IG51bGwpIHsNCiAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbygi6K+35YWI6YCJ5oup5YiG5YWs5Y+4Iik7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuYW1tZXRlci5lbGVjdHJvdHlwZSA9IHR5cGVzW3R5cGVzLmxlbmd0aCAtIDFdOw0KICAgICAgICAgIHRoaXMuJHJlZnMuc3RhdGlvbk1vZGFsLmFtbWV0ZXJpZCA9IHRoaXMuYW1tZXRlci5pZDsNCiAgICAgICAgICB0aGlzLiRyZWZzLnN0YXRpb25Nb2RhbC5pbml0RGF0YUxpc3QoDQogICAgICAgICAgICB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUsDQogICAgICAgICAgICAwLA0KICAgICAgICAgICAgdGhpcy5hbW1ldGVyLmFtbWV0ZXJ1c2UsDQogICAgICAgICAgICB0aGlzLmFtbWV0ZXIuY29tcGFueSwNCiAgICAgICAgICAgIHBhcmFtcw0KICAgICAgICAgICk7IC8v5bGA56uZDQogICAgICAgICAgLy8gfQ0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICBpZiAodGhpcy5hbW1ldGVyLmNvbXBhbnkgPT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbygi6K+35YWI6YCJ5oup5YiG5YWs5Y+4Iik7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICAgIHRoaXMuJHJlZnMuY291bnRyeU1vZGFsLmNob29zZSh0aGlzLmFtbWV0ZXIuY29tcGFueSk7IC8v5omA5bGe6YOo6ZeoDQogICAgICB9DQogICAgfSwNCiAgICAvL+mDqOmXqA0KICAgIGdldERhdGFGcm9tTW9kYWwoZGF0YSkgew0KICAgICAgdGhpcy5hbW1ldGVyLmNvdW50cnkgPSBkYXRhLmlkOw0KICAgICAgdGhpcy5hbW1ldGVyLmNvdW50cnlOYW1lID0gZGF0YS5uYW1lOw0KICAgICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jnu5PmnZ8NCiAgICB9LA0KICAgIC8vDQogICAgZ2V0RGF0YUZyb21Nb2RhbE9iamVjdChkYXRhLCBmbGFnKSB7DQogICAgICB0aGlzLmhhbmRsZUNob29zZVN1cChkYXRhKTsgLy8g5LygIHRydWUg6K6+572uIOWbnuiwg+WAvA0KICAgIH0sDQogICAgb25DaGFuZ2Uodikgew0KICAgICAgdGhpcy5yZWNlaXB0YWNjb3VudG5hbWVMaXN0LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgaWYgKGl0ZW0ua29pbmggPT09IHYpIHsNCiAgICAgICAgICB0aGlzLmFtbWV0ZXIucmVjZWlwdGFjY291bnRiYW5rID0gaXRlbS5iYW5rYTsNCiAgICAgICAgICB0aGlzLmFtbWV0ZXIucmVjZWlwdGFjY291bnRzID0gaXRlbS5iYW5rbjsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvL+mAieaLqeWvueaWueWNleS9jQ0KICAgIGhhbmRsZUNob29zZVN1cChkYXRhKSB7DQogICAgICBpZiAoIWRhdGEpIHsNCiAgICAgICAgdGhpcy4kcmVmcy5jaG9vc2VNb2RhbFN1cC5jaG9vc2UoMSk7IC8v5omT5byA5qih5oCB5qGGDQogICAgICB9IGVsc2Ugew0KICAgICAgICAodGhpcy5hbW1ldGVyLnVzZXJ1bml0ID0gZGF0YS5uYW1lKSwNCiAgICAgICAgICBnZXRCYW5rQ2FyZCh7IGxpZm5yOiBkYXRhLmlkIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgdGhpcy5yZWNlaXB0YWNjb3VudG5hbWVMaXN0ID0gcmVzLmRhdGEucm93czsNCiAgICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8v6I635Y+W5bGA56uZ5pWw5o2uIGZsYWc9dHJ1ZemcgOimgemqjOivgeWunumZheaKpei0puS4quaVsCxmbGFnPWZhbHNl5LiN6ZyA6KaB6aqM6K+B5a6e6ZmF5oql6LSm5Liq5pWwDQogICAgZ2V0RGF0YUZyb21TdGF0aW9uTW9kYWwoZGF0YSwgZmxhZykgew0KICAgICAgdGhpcy5pc2NoZWNrU3RhdGlvbiA9IGZsYWc7DQogICAgICBpZiAodGhpcy5jaG9vc2VJbmRleCA9PSAyKSB7DQogICAgICAgIHRoaXMuZWxlY3Ryby5kYXRhW3RoaXMuZWxlY3Ryb1Jvd051bV0uc3RhdGlvbklkID0gZGF0YS5pZDsNCiAgICAgICAgdGhpcy5lbGVjdHJvLmRhdGFbdGhpcy5lbGVjdHJvUm93TnVtXS5zdGF0aW9uTmFtZSA9IGRhdGEuc3RhdGlvbm5hbWU7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnByb3BlcnR5cmlnaHQgPSBkYXRhLnByb3BlcnR5cmlnaHQ7DQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uTmFtZSA9IGRhdGEuc3RhdGlvbm5hbWU7DQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uY29kZSA9IGRhdGEuaWQ7DQogICAgICAgIC8v5omA5pyJ55qE5LiL5ouJ6buY6K6k5YC86YO96KaB5a2X56ym5Liy5omN6IO95pi+56S65LqGIOi3n+S5i+WJjeWujOWFqOWPmOS6hg0KICAgICAgICB0aGlzLmFtbWV0ZXIuc3RhdGlvbnN0YXR1cyA9IE51bWJlcigNCiAgICAgICAgICBkYXRhLnN0YXR1cyA9PSB1bmRlZmluZWQgPyBkYXRhLlNUQVRVUyA6IGRhdGEuc3RhdHVzDQogICAgICAgICk7DQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9udHlwZSA9IGRhdGEuc3RhdGlvbnR5cGU7DQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uYWRkcmVzcyA9IGRhdGEuYWRkcmVzczsNCiAgICAgICAgdGhpcy5hbW1ldGVyLnN0YXRpb25uYW1lNWdyID0gZGF0YS5zdGF0aW9ubmFtZTVncjsNCiAgICAgICAgdGhpcy5hbW1ldGVyLnN0YXRpb25jb2RlNWdyID0gZGF0YS5zdGF0aW9uY29kZWludGlkOw0KICAgICAgICB0aGlzLmFtbWV0ZXIubWFwID0gZGF0YS5tYXA7DQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uYWRkcmVzc2NvZGUgPSBkYXRhLnJlc3N0YXRpb25jb2RlOw0KICAgICAgICB0aGlzLmFtbWV0ZXIucmVzc3RhdGlvbmNvZGUgPSBkYXRhLnJlc3N0YXRpb25jb2RlOw0KICAgICAgICAvL+m7mOiupOeUn+aIkOS4gOadoeWFs+iBlOeUqOeUteexu+Weiw0KICAgICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICAgIGxpc3RFbGVjdHJpY1R5cGUoeyBpZDogZGF0YS5zdGF0aW9udHlwZSB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGF0LmVsZWN0cm8uZGF0YSA9IE9iamVjdC5hc3NpZ24oW10sIHJlcy5kYXRhLnJvd3MpOw0KICAgICAgICAgIHRoaXMuZWxlY3Ryby5kYXRhWzBdLnN0YXRpb25JZCA9IGRhdGEuaWQ7DQogICAgICAgICAgdGhpcy5lbGVjdHJvLmRhdGFbMF0uc3RhdGlvbk5hbWUgPSBkYXRhLnN0YXRpb25uYW1lOw0KICAgICAgICAgIHRoaXMuZWxlY3Ryby5kYXRhWzBdLl9kaXNhYmxlZCA9IHRydWU7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKua3u+WKoOeUteihqOWFs+iBlOeUqOeUteexu+Wei+avlOeOhyovDQogICAgYWRkRWxlY3RyaWNUeXBlKCkgew0KICAgICAgdGhpcy4kcmVmcy5zZWxlY3RFbGVjdHJpY1R5cGUuaW5pdEVsZWN0cmljVHlwZSgpOw0KICAgIH0sDQoNCiAgICAvKuenu+mZpOmAieS4reeahOeUqOeUteexu+Wei+avlOeOhyovDQogICAgcmVtb3ZlRWxlY3RyaWNUeXBlKCkgew0KICAgICAgbGV0IHJvd3MgPSB0aGlzLiRyZWZzLmFtbWV0ZXJUYWJsZS5nZXRTZWxlY3Rpb24oKTsNCiAgICAgIGxldCBkYXRhcyA9IHRoaXMuZWxlY3Ryby5kYXRhOw0KICAgICAgcm93cy5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGlmIChpdGVtLl9pbmRleCAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICBkYXRhcy5zcGxpY2UoaXRlbS5faW5kZXgsIDEpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGRhdGFzLmZvckVhY2goKGRhdGEpID0+IHsNCiAgICAgICAgICAgIGlmIChkYXRhLmlkID09PSBpdGVtLmlkKSB7DQogICAgICAgICAgICAgIGxldCBpbmRleCA9IGRhdGFzLmluZGV4T2YoZGF0YSk7DQogICAgICAgICAgICAgIGRhdGFzLnNwbGljZShpbmRleCwgMSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgdGhpcy5lbGVjdHJvLmRhdGEgPSBkYXRhczsNCiAgICB9LA0KDQogICAgLyog6K6+572u55So55S157G75Z6L5YiX6KGoKi8NCiAgICBzZXRFbGVjdHJpY0RhdGE6IGZ1bmN0aW9uIChkYXRhKSB7DQogICAgICBsZXQgb3JpZ2luID0gdGhpcy5lbGVjdHJvLmRhdGE7DQogICAgICBpZiAob3JpZ2luLmxlbmd0aCA8IDEpIHsNCiAgICAgICAgdGhpcy5lbGVjdHJvLmRhdGEgPSBkYXRhOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGV0IHRlbSA9IGRhdGE7DQogICAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgb3JpZ2luLmxlbmd0aDsgaisrKSB7DQogICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhLmxlbmd0aDsgaSsrKSB7DQogICAgICAgICAgICBsZXQgdHlwZUlkID0NCiAgICAgICAgICAgICAgb3JpZ2luW2pdLmVsZWN0cm9UeXBlSWQgIT0gdW5kZWZpbmVkDQogICAgICAgICAgICAgICAgPyBvcmlnaW5bal0uZWxlY3Ryb1R5cGVJZA0KICAgICAgICAgICAgICAgIDogb3JpZ2luW2pdLmlkOw0KICAgICAgICAgICAgaWYgKGRhdGFbaV0uaWQgPT09IHR5cGVJZCkgew0KICAgICAgICAgICAgICB0ZW0uc3BsaWNlKHRlbS5pbmRleE9mKGRhdGFbaV0pLCAxKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5lbGVjdHJvLmRhdGEgPSB0aGlzLmVsZWN0cm8uZGF0YS5jb25jYXQodGVtKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy/nlKjnlLXnsbvlnovmr5TkvovmoKHpqowNCiAgICBjaGVja0VsZWN0cmljVHlwZUl0ZW0oKSB7DQogICAgICBsZXQgaXRlbXMgPSB0aGlzLmVsZWN0cm8uZGF0YTsNCiAgICAgIC8v5b2T4oCc55So55S157G75Z6L4oCd6YCJ5oup4oCcMTExIEHnsbvmnLrmpbzvvIjmnLrmiL/vvInvvIwxMTIgQuexu+acuualvO+8iOacuuaIv++8ie+8jDExMyBD57G75py65qW877yI5py65oi/77yJIOKAneaIluKAnDIg566h55CG5Yqe5YWs55So55S14oCd5pe277yM5omN6ZyA5aGr55So55S157G75Z6L5YiG5q+U5LiU5b+F5aGr77yM55So55S157G75Z6L5q+U5L6L5LmL5ZKM5b+F6aG7562J5LqOMTAwJQ0KICAgICAgaWYgKA0KICAgICAgICB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPT09IDExMSB8fA0KICAgICAgICB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPT09IDExMiB8fA0KICAgICAgICB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPT09IDExMyB8fA0KICAgICAgICB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPT09IDINCiAgICAgICkgew0KICAgICAgICBsZXQgc3VtUmF0aW8gPSBpdGVtcy5yZWR1Y2UoKHRvdGFsLCBpdGVtKSA9PiB7DQogICAgICAgICAgcmV0dXJuIHRvdGFsICsgaXRlbS5yYXRpbzsNCiAgICAgICAgfSwgMCk7DQogICAgICAgIGlmIChzdW1SYXRpbyAhPT0gMTAwKSB7DQogICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7DQogICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsDQogICAgICAgICAgICBjb250ZW50OiAi55So55S157G75Z6L5omA5Y2g5q+U5L6L5ZKM5b+F6aG75Li6MTAwJe+8jOW9k+WJjeWAvOS4uiIgKyBzdW1SYXRpbyArICIlIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiB0cnVlOw0KICAgIH0sDQogICAgc3RhcnRGbG93KGRhdGEpIHsNCiAgICAgIGxldCBidXNpVGl0bGUgPSAi5paw5aKe55S16KGoKCIgKyBkYXRhLnByb2plY3RuYW1lICsgIinlrqHmibkiOw0KICAgICAgdGhpcy53b3JrRmxvd1BhcmFtcyA9IHsNCiAgICAgICAgYnVzaUlkOiBkYXRhLmlkLA0KICAgICAgICBidXNpQWxpYXM6ICJBRERfQU1NIiwNCiAgICAgICAgYnVzaVRpdGxlOiBidXNpVGl0bGUsDQogICAgICB9Ow0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7DQogICAgICAgIHRoYXQuJHJlZnMuY2x3ZmJ0bi5vbkNsaWNrKCk7DQogICAgICB9LCAyMDApOw0KICAgIH0sDQogICAgZG9Xb3JrRmxvdyhkYXRhKSB7DQogICAgICAvL+a1geeoi+Wbnuiwgw0KICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB0aGlzLmNsb3NlVGFnKHsgcm91dGU6IHRoaXMuJHJvdXRlIH0pOw0KICAgICAgaWYgKGRhdGEgPT0gMCkgew0KICAgICAgICB0aGlzLndhcm4oKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8q6YCJ5oup55S16KGoL+WNj+iuriovDQogICAgYWRkQW1tZXRlclByb3RvY29sKCkgew0KICAgICAgdGhpcy4kcmVmcy5zZWxlY3RBbW1ldGVyUHJvdG9jb2wuaW5pdERhdGFMaXN0KDEsIG51bGwpOw0KICAgIH0sDQogICAgLyog6YCJ5oup55S16KGo5oi35Y+3L+WNj+iurue8luWPtyovDQogICAgc2V0QW1tZXRlclByb3JvY29sRGF0YTogZnVuY3Rpb24gKGRhdGEpIHsNCiAgICAgIHRoaXMuYW1tZXRlci5wYXJlbnRJZCA9IGRhdGEuaWQ7DQogICAgICBpZiAoZGF0YS5wcm90b2NvbG5hbWUgIT0gbnVsbCAmJiBkYXRhLnByb3RvY29sbmFtZS5sZW5ndGggIT0gMCkgew0KICAgICAgICB0aGlzLmFtbWV0ZXIucGFyZW50Q29kZSA9IGRhdGEucHJvdG9jb2xuYW1lOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5hbW1ldGVyLnBhcmVudENvZGUgPSBkYXRhLmFtbWV0ZXJuYW1lOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyrpgInmi6nlrqLmiLcqLw0KICAgIGFkZEN1c3RvbWVyKCkgew0KICAgICAgdGhpcy4kcmVmcy5jdXN0b21lckxpc3QuY2hvb3NlKDIpOyAvL+aJk+W8gOaooeaAgeahhg0KICAgIH0sDQogICAgZ2V0RGF0YUZyb21DdXN0b21lck1vZGFsOiBmdW5jdGlvbiAoZGF0YSkgew0KICAgICAgdGhpcy5hbW1ldGVyLmN1c3RvbWVySWQgPSBkYXRhLmlkOw0KICAgICAgdGhpcy5hbW1ldGVyLmN1c3RvbWVyTmFtZSA9IGRhdGEubmFtZTsNCiAgICB9LA0KICAgIC8v6YCJ5oup5YyF5bmy55qE5pe25YCZ5L+u5pS56buY6K6k5YyF5bmy57G75Z6LDQogICAgdXBkYXRlcGFja2FnZXR5cGUoKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMuYW1tZXRlcjsNCiAgICAgIGRhdGEucGFja2FnZXR5cGUgPSBudWxsOw0KICAgIH0sDQogICAgaXN6Z3pjaGFuZ2UoKSB7DQogICAgICBpZiAodGhpcy5hbW1ldGVyLmlzemd6ID09ICIxIikgew0KICAgICAgICB0aGlzLmFtbWV0ZXIuZGlyZWN0c3VwcGx5ZmxhZyA9IDE7DQogICAgICAgIHRoaXMuaXN6Z3pPbmx5ID0gdHJ1ZTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuaXN6Z3pPbmx5ID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICBjaG9vc2VvbGRhbW1ldGVybmFtZSgpIHsNCiAgICAgIHRoaXMuJHJlZnMuY2hvb3NlQW1tZXRlck1vZGVsLm1vZGFsLnF1ZXJ5cGFyYW1zLnN0YXR1cyA9IDA7DQogICAgICB0aGlzLiRyZWZzLmNob29zZUFtbWV0ZXJNb2RlbC5tb2RhbC5xdWVyeXBhcmFtcy5hbW1ldGVydXNlID0gMTsNCiAgICAgIHRoaXMuJHJlZnMuY2hvb3NlQW1tZXRlck1vZGVsLm1vZGFsLnF1ZXJ5cGFyYW1zLnR5cGUgPSAzOw0KICAgICAgdGhpcy4kcmVmcy5jaG9vc2VBbW1ldGVyTW9kZWwubW9kYWwucXVlcnlwYXJhbXMuY29tcGFueSA9IHRoaXMuYW1tZXRlci5jb21wYW55Ow0KICAgICAgdGhpcy4kcmVmcy5jaG9vc2VBbW1ldGVyTW9kZWwubW9kYWwucXVlcnlwYXJhbXMuY291bnRyeSA9IHRoaXMuYW1tZXRlci5jb3VudHJ5Ow0KICAgICAgdGhpcy4kcmVmcy5jaG9vc2VBbW1ldGVyTW9kZWwubW9kYWwucXVlcnlwYXJhbXMuZGlyZWN0c3VwcGx5ZmxhZyA9IDI7DQogICAgICB0aGlzLiRyZWZzLmNob29zZUFtbWV0ZXJNb2RlbC5tb2RhbC5zaG93ID0gdHJ1ZTsNCiAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbygi5Y+M5Ye76YCJ5oup77yB77yBIik7DQogICAgfSwNCiAgICBnZXRBbW1ldGVyTW9kZWxNb2RhbChkYXRhKSB7DQogICAgICB0aGlzLmFtbWV0ZXIub2xkYW1tZXRlcm5hbWUgPSBkYXRhLm5hbWUgKyAiLCIgKyBkYXRhLmlkOw0KICAgIH0sDQogICAgcHJvamVjdE5hbWVDaGFuZ2UodmFsKSB7DQogICAgICBpZiAoDQogICAgICAgICEvXi4qKFteXHUwMDAwLVx1MDBmZl0r6LevKS4qJC8udGVzdCh2YWwpICYmDQogICAgICAgICEvXi4qKFteXHUwMDAwLVx1MDBmZl0qKShbMC05XSrlj7cpLiokLy50ZXN0KHZhbCkgJiYNCiAgICAgICAgIS9eLiooW15cdTAwMDAtXHUwMGZmXSvmpbznlLXooagpLiokLy50ZXN0KHZhbCkNCiAgICAgICkgew0KICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIua4qemmqOaPkOekuu+8mumbhuWbouimgeaxguagvOW8j+S4uigqKui3ryoq5Y+3KirmpbznlLXooagpIik7DQogICAgICB9DQogICAgfSwNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAvL+ebtOaOpeS7juWJjeWPsOWPlg0KICAgIHRoaXMuY2F0ZWdvcnlzID0gew0KICAgICAgZGlyZWN0c3VwcGx5ZmxhZzogYmxpc3QoImRpcmVjdFN1cHBseUZsYWciKSwNCiAgICB9Ow0KICAgIHRoaXMucHJvcGVydHlMaXN0ID0gYmxpc3QoInByb3BlcnR5Iik7DQogICAgdGhpcy5pbml0QW1tZXRlcih0aGlzLiRyb3V0ZS5xdWVyeS5pZCk7DQoNCiAgICB0aGlzLmNvbmZpZ1ZlcnNpb24gPSB0aGlzLiRjb25maWcudmVyc2lvbjsNCiAgICBpZiAodGhpcy5jb25maWdWZXJzaW9uICE9ICJsbiIgJiYgdGhpcy5jb25maWdWZXJzaW9uICE9ICJMTiIpIHsNCiAgICAgIHRoaXMucnVsZVZhbGlkYXRlLmFtbWV0ZXJuYW1lID0gWw0KICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICBdOw0KICAgICAgdGhpcy5ydWxlVmFsaWRhdGUuY3VzdG9tZXJOYW1lID0gWw0KICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICBdOw0KICAgICAgdGhpcy5ydWxlVmFsaWRhdGUudXNlcnVuaXQgPSBbDQogICAgICAgIHsNCiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwNCg0KICAgICAgICAgIHRyaWdnZXI6ICJibHVyLGNoYW5nZSIsDQogICAgICAgIH0sDQogICAgICBdOw0KICAgIH0NCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["addAmmeter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA09CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "addAmmeter.vue", "sourceRoot": "src/view/basedata/ammeter", "sourcesContent": ["<template>\r\n  <!-- *****添加电表  <AUTHOR> -->\r\n  <div class=\"testaa\">\r\n    <!--重点就是下面的代码了-->\r\n    <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n    <div solt=\"header\">\r\n      <Row>\r\n        <Col span=\"24\" style=\"text-align: right; right: 10px\">\r\n          <Button\r\n            type=\"success\"\r\n            :loading=\"isLoading == 0 ? loading : false\"\r\n            :disabled=\"isDisable\"\r\n            @click=\"onModalOK(0)\"\r\n            >保存</Button\r\n          >\r\n          <Button\r\n            type=\"primary\"\r\n            :loading=\"isLoading == 1 ? loading : false\"\r\n            :disabled=\"isDisable\"\r\n            @click=\"onModalOK(1)\"\r\n            >提交</Button\r\n          >\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n    <cl-wf-btn\r\n      ref=\"clwfbtn\"\r\n      :isStart=\"true\"\r\n      :params=\"workFlowParams\"\r\n      @on-ok=\"doWorkFlow\"\r\n      v-show=\"false\"\r\n    ></cl-wf-btn>\r\n    <select-electric-type\r\n      ref=\"selectElectricType\"\r\n      v-on:listenToSetElectricType=\"setElectricData\"\r\n    ></select-electric-type>\r\n    <country-modal\r\n      ref=\"countryModal\"\r\n      v-on:getDataFromModal=\"getDataFromModal\"\r\n    ></country-modal>\r\n    <!--      选择关联局站-->\r\n    <station-modal\r\n      ref=\"stationModal\"\r\n      v-on:getDataFromStationModal=\"getDataFromStationModal\"\r\n    ></station-modal>\r\n    <ammeter-protocol-list\r\n      ref=\"selectAmmeterProtocol\"\r\n      v-on:listenToSetAmmeterProrocol=\"setAmmeterProrocolData\"\r\n    ></ammeter-protocol-list>\r\n    <customer-list\r\n      ref=\"customerList\"\r\n      v-on:getDataFromCustomerModal=\"getDataFromCustomerModal\"\r\n    ></customer-list>\r\n    <!--        <Modal v-model=\"showModel\" width=\"80%\" :title=\"title\">-->\r\n    <Card class=\"menu-card\">\r\n      <Collapse :value=\"['Panel1', 'Panel2', 'Panel3', 'Panel4', 'Panel5']\">\r\n        <Panel name=\"Panel1\"\r\n          >基本信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"项目名称：\" prop=\"projectname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.projectname\"\r\n                        placeholder=\"**路**号**楼电表\"\r\n                        @on-blur=\"projectNameChange\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.projectname != null &&\r\n                          oldData.projectname != ammeter.projectname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.projectname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag === 1\">\r\n                    <FormItem\r\n                      label=\"供电局电表编号(下户户号)：\"\r\n                      prop=\"supplybureauammetercode\"\r\n                    >\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauammetercode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauammetercode != null &&\r\n                          oldData.supplybureauammetercode !=\r\n                            ammeter.supplybureauammetercode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauammetercode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag === 2\">\r\n                    <FormItem\r\n                      label=\"供电局电表编号(电表编号)：\"\r\n                      prop=\"supplybureauammetercode\"\r\n                    >\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauammetercode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauammetercode != null &&\r\n                          oldData.supplybureauammetercode !=\r\n                            ammeter.supplybureauammetercode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauammetercode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否智能电表：\" prop=\"issmartammeter\">\r\n                      <RadioGroup v-model=\"ammeter.issmartammeter\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.issmartammeter != null &&\r\n                          oldData.issmartammeter != ammeter.issmartammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.issmartammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否实体电表：\" prop=\"isentityammeter\">\r\n                      <RadioGroup v-model=\"ammeter.isentityammeter\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.isentityammeter != null &&\r\n                          oldData.isentityammeter != ammeter.isentityammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.isentityammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"电表用途：\" prop=\"ammeteruse\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.ammeteruse\"\r\n                        category=\"ammeterUse\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammeteruse != null &&\r\n                          oldData.ammeteruse != ammeter.ammeteruse\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldAmmeteruse }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"关联实际报账电表：\"\r\n                      prop=\"parentCode\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.parentCode\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addAmmeterProtocol\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.parentCode != null &&\r\n                          oldData.parentCode != ammeter.parentCode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.parentCode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"供电局名称：\" prop=\"supplybureauname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauname != null &&\r\n                          oldData.supplybureauname != ammeter.supplybureauname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"电表类型：\" prop=\"ammetertype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.ammetertype\"\r\n                        category=\"ammeterType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetertype != null &&\r\n                          oldData.ammetertype != ammeter.ammetertype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldAmmetertype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"包干类型：\" prop=\"packagetype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.packagetype\"\r\n                        :disabled=\"ammeter.islumpsum == 1 ? false : true\"\r\n                        category=\"packageType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      >\r\n                      </cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.packagetype != null &&\r\n                          oldData.packagetype != ammeter.packagetype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPackagetype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"所属分公司：\" prop=\"company\">\r\n                      <Select\r\n                        v-model=\"ammeter.company\"\r\n                        @on-change=\"selectChange(ammeter.company)\"\r\n                      >\r\n                        <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">\r\n                          {{ item.name }}\r\n                        </Option>\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.company != null && oldData.company != ammeter.company\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.companyName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <!--                                            <FormItem label=\"所属部门：\" prop=\"countryName\">-->\r\n                    <!--                                                <Input icon=\"ios-archive\" v-model=\"ammeter.countryName\"-->\r\n                    <!--                                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly/>-->\r\n                    <!--                                            </FormItem>-->\r\n                    <FormItem\r\n                      label=\"所属部门：\"\r\n                      prop=\"countryName\"\r\n                      v-if=\"isAdmin == true\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-if=\"isCityAdmin == true || isEditByCountry == false\"\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseResponseCenter()\"\r\n                        readonly\r\n                      />\r\n                      <Input\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-else-if=\"isEditByCountry == true && isCityAdmin == false\"\r\n                        readonly\r\n                      />\r\n                      <!--                                                <label v-if=\"oldData.countryName != null &&oldData.countryName != ammeter.countryName\" style=\"color: red;\">历史数据：{{oldData.countryName}}</label>-->\r\n                    </FormItem>\r\n                    <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\">\r\n                      <Select v-model=\"ammeter.country\" v-if=\"isEditByCountry == false\">\r\n                        <Option\r\n                          v-for=\"item in departments\"\r\n                          :value=\"item.id\"\r\n                          :key=\"item.id\"\r\n                        >\r\n                          {{ item.name }}\r\n                        </Option>\r\n                      </Select>\r\n                      <Input\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-else-if=\"isEditByCountry == true\"\r\n                        readonly\r\n                      />\r\n                      <!--                                                <label v-if=\"oldData.countryName != null &&oldData.countryName != ammeter.countryName\" style=\"color: red;\">历史数据：{{oldData.countryName}}</label>-->\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"所属分局或支局：\" prop=\"substation\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.substation\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.substation != null &&\r\n                          oldData.substation != ammeter.substation\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.substation }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"详细地址：\" prop=\"address\">\r\n                      <cl-input :maxlength=\"100\" v-model=\"ammeter.address\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.address != null && oldData.address != ammeter.address\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.address }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费名称：\" prop=\"payname\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.payname\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.payname != null && oldData.payname != ammeter.payname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.payname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费类型：\" prop=\"payperiod\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.payperiod\"\r\n                        category=\"payPeriod\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.payperiod != null &&\r\n                          oldData.payperiod != ammeter.payperiod\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPayperiod }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费经办人：\" prop=\"paymanager\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.paymanager\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.paymanager != null &&\r\n                          oldData.paymanager != ammeter.paymanager\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.paymanager }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"付费方式：\" prop=\"paytype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.paytype\"\r\n                        category=\"payType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.paytype != null && oldData.paytype != ammeter.paytype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPaytype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"管理负责人：\" prop=\"ammetermanager\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.ammetermanager\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetermanager != null &&\r\n                          oldData.ammetermanager != ammeter.ammetermanager\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetermanager }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"单价(含税元)：\" prop=\"price\">\r\n                      <InputNumber\r\n                        :maxlength=\"20\"\r\n                        :min=\"0\"\r\n                        v-model=\"ammeter.price\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.price != null && oldData.price != ammeter.price\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.price }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"用电类型：\" prop=\"classifications\">\r\n                      <Cascader\r\n                        :data=\"classificationData\"\r\n                        :change-on-select=\"true\"\r\n                        v-model=\"ammeter.classifications\"\r\n                        @on-change=\"changeClassifications\"\r\n                      ></Cascader>\r\n                      <label\r\n                        v-if=\"\r\n                          ammeter.classifications !== undefined &&\r\n                          ammeter.classifications.length > 0\r\n                        \"\r\n                      >\r\n                        <label v-for=\"(item, i) in ammeter.classifications\" :key=\"i\">\r\n                          <label\r\n                            v-if=\"\r\n                              i === ammeter.classifications.length - 1 &&\r\n                              oldData.electrotype != null &&\r\n                              oldData.electrotype != item\r\n                            \"\r\n                            style=\"color: red\"\r\n                            >历史数据：{{ oldData.electrotypename }}</label\r\n                          >\r\n                        </label>\r\n                      </label>\r\n                      <label\r\n                        v-else-if=\"\r\n                          ammeter.classifications !== undefined &&\r\n                          ammeter.classifications.length <= 0\r\n                        \"\r\n                      >\r\n                        <label v-if=\"oldData.electrotype != null\" style=\"color: red\"\r\n                          >历史数据：{{ oldData.electrotypename }}</label\r\n                        >\r\n                      </label>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"倍率：\" prop=\"magnification\">\r\n                      <InputNumber\r\n                        :min=\"1\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.magnification\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.magnification != null &&\r\n                          oldData.magnification != ammeter.magnification\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.magnification }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"对外结算类型：\" prop=\"directsupplyflag\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.directsupplyflag\"\r\n                        category=\"directSupplyFlag\"\r\n                        :disabled=\"iszgzOnly\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                        @on-change=\"changedirectsupply\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.directsupplyflag != null &&\r\n                          oldData.directsupplyflag != ammeter.directsupplyflag\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldDirectsupplyflag }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem label=\"电价性质：\" prop=\"electrovalencenature\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.electrovalencenature\"\r\n                        category=\"electrovalenceNature\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.electrovalencenature != null &&\r\n                          oldData.electrovalencenature != ammeter.electrovalencenature\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldElectrovalencenature }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"翻表读数(度)：\" prop=\"maxdegree\">\r\n                      <InputNumber\r\n                        :maxlength=\"20\"\r\n                        v-model=\"ammeter.maxdegree\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.maxdegree != null &&\r\n                          oldData.maxdegree != ammeter.maxdegree\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.maxdegree }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"状态：\" prop=\"status\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.status\"\r\n                        @on-change=\"changeStatus\"\r\n                        category=\"status\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"oldData.status != null && oldData.status != ammeter.status\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStatus }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"占局(站)定额电量比例(%)：\" prop=\"quotapowerratio\">\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :min=\"1\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.quotapowerratio\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.quotapowerratio != null &&\r\n                          oldData.quotapowerratio != ammeter.quotapowerratio\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.quotapowerratio }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"合同对方：\" prop=\"contractOthPart\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.contractOthPart\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.contractOthPart != null &&\r\n                          oldData.contractOthPart != ammeter.contractOthPart\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.contractOthPart }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"!isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select\r\n                        v-model=\"ammeter.property\"\r\n                        :disabled=\"propertyReadonly\"\r\n                        @on-change=\"getProperty\"\r\n                      >\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}\r\n                        </Option>\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\">\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}\r\n                        </Option>\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"分割比例(%)：\"\r\n                      prop=\"percent\"\r\n                      v-if=\"ammeter.property === 2 || ammeter.property === 4\"\r\n                    >\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.percent\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.percent != null && oldData.percent != ammeter.percent\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.percent }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"收款客户名称：\"\r\n                      prop=\"customerName\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.customerName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addCustomer\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.customerName != null &&\r\n                          oldData.customerName != ammeter.customerName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.customerName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN')\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"是否铁塔包干：\" prop=\"islumpsum\">\r\n                      <RadioGroup\r\n                        v-model=\"ammeter.islumpsum\"\r\n                        @on-change=\"updatepackagetype\"\r\n                      >\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.islumpsum != null &&\r\n                          oldData.islumpsum != ammeter.islumpsum\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.islumpsum == \"0\" ? \"否\" : \"是\" }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"月包干电量(度)：\" prop=\"ybgPower\">\r\n                      <cl-input :maxlength=\"20\" v-model=\"ammeter.ybgPower\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ybgPower != null && oldData.ybgPower != ammeter.ybgPower\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ybgPower }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干起始日期：\" prop=\"lumpstartdate\">\r\n                      <cl-date-picker\r\n                        type=\"date\"\r\n                        placeholder=\"包干起始日期\"\r\n                        v-model=\"ammeter.lumpstartdate\"\r\n                        style=\"width: 160px\"\r\n                      ></cl-date-picker>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.lumpstartdate != null &&\r\n                          oldData.lumpstartdate != ammeter.lumpstartdate\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.lumpstartdate }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干截止日期：\" prop=\"lumpenddate\">\r\n                      <cl-date-picker\r\n                        type=\"date\"\r\n                        placeholder=\"包干截止日期\"\r\n                        v-model=\"ammeter.lumpenddate\"\r\n                        style=\"width: 160px\"\r\n                      ></cl-date-picker>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.lumpenddate != null &&\r\n                          oldData.lumpenddate != ammeter.lumpenddate\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.lumpenddate }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.packagetype != null &&\r\n                      ammeter.packagetype != 4 &&\r\n                      (configVersion == 'sc' || configVersion == 'SC')\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干费用：\" prop=\"fee\">\r\n                      <InputNumber\r\n                        :min=\"0\"\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.fee\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.fee != null && oldData.fee != ammeter.fee\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.fee }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"月包干电费：\" prop=\"fee\">\r\n                      <InputNumber\r\n                        :min=\"0\"\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.fee\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.fee != null && oldData.fee != ammeter.fee\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.fee }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc' || configVersion == 'SC'\">\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"输配电公司：\"\r\n                      prop=\"transdistricompany\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: true,\r\n                          type: 'number',\r\n                          message: '请选择',\r\n                          trigger: 'change,blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <cl-select\r\n                        v-model=\"ammeter.transdistricompany\"\r\n                        category=\"transdistricompany\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.transdistricompany != null &&\r\n                          oldData.transdistricompany != ammeter.transdistricompany\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldtransdistricompany }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.directsupplyflag == 1 && ammeter.transdistricompany == 1\r\n                    \"\r\n                  >\r\n                    <FormItem\r\n                      label=\"电压等级：\"\r\n                      prop=\"voltageClass\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: true,\r\n                          type: 'number',\r\n                          message: '请选择',\r\n                          trigger: 'change,blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <cl-select\r\n                        v-model=\"ammeter.voltageClass\"\r\n                        category=\"voltageClass\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.voltageClass != null &&\r\n                          oldData.voltageClass != ammeter.voltageClass\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldvoltageClass }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc'\">\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"是否转改直：\"\r\n                      prop=\"iszgz\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.iszgz\" @on-change=\"iszgzchange\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"原转供电表编号：\"\r\n                      prop=\"oldammetername\"\r\n                      v-if=\"ammeter.iszgz == '1'\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: ammeter.iszgz == '1',\r\n                          message: '不能为空',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        :value=\"\r\n                          ammeter.oldammetername\r\n                            ? ammeter.oldammetername.split(',')[0]\r\n                            : null\r\n                        \"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseoldammetername\"\r\n                      />\r\n                    </FormItem>\r\n                    <ChooseAmmeterModel\r\n                      ref=\"chooseAmmeterModel\"\r\n                      v-if=\"ammeter.iszgz == '1'\"\r\n                      v-on:getAmmeterModelModal=\"getAmmeterModelModal\"\r\n                    />\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"是否直购电：\"\r\n                      prop=\"directFlag\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.directFlag\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"是否含办公：\"\r\n                      prop=\"officeFlag\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.officeFlag\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel2\"\r\n          >关联局站信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter1\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"局(站)名称：\"\r\n                      prop=\"stationName\"\r\n                      :class=\"{ requireStar: isRequireFlag }\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.stationName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseResponseCenter(1)\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationName != null &&\r\n                          oldData.stationName != ammeter.stationName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)编码：\" prop=\"stationcode\">\r\n                      <cl-input readonly v-model=\"ammeter.stationcode\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationcode != null &&\r\n                          oldData.stationcode != ammeter.stationcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)状态：\" prop=\"stationstatus\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.stationstatus\"\r\n                        filterable\r\n                        category=\"stationStatus\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationstatus != null &&\r\n                          oldData.stationstatus != ammeter.stationstatus\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStationstatus }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)类型：\" prop=\"stationtype\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.stationtype\"\r\n                        filterable\r\n                        category=\"BUR_STAND_TYPE\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationtype != null &&\r\n                          oldData.stationtype != ammeter.stationtype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStationtype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)地址：\" prop=\"stationaddress\">\r\n                      <cl-input readonly v-model=\"ammeter.stationaddress\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationaddress != null &&\r\n                          oldData.stationaddress != ammeter.stationaddress\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationaddress }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"资源局站id：\" prop=\"stationaddresscode\">\r\n                      <cl-input\r\n                        readonly\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.resstationcode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.resstationcode != null &&\r\n                          oldData.resstationcode != ammeter.resstationcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.resstationcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否有空调：\" prop=\"isairconditioning\">\r\n                      <RadioGroup v-model=\"ammeter.isairconditioning\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.isairconditioning != null &&\r\n                          oldData.isairconditioning != ammeter.isairconditioning\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.isairconditioning == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"核定电量：\" prop=\"vouchelectricity\">\r\n                      <InputNumber\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.vouchelectricity\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.vouchelectricity != null &&\r\n                          oldData.vouchelectricity != ammeter.vouchelectricity\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.vouchelectricity }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"isCDCompany\">\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管C网编号：\" prop=\"nmCcode\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmCcode\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmCcode != null && oldData.nmCcode != ammeter.nmCcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmCcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号L2.1G：\" prop=\"nmL2100\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmL2100\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmL2100 != null && oldData.nmL2100 != ammeter.nmL2100\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmL2100 }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号L1.8G：\" prop=\"nmL1800\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmL1800\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmL1800 != null && oldData.nmL1800 != ammeter.nmL1800\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmL1800 }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号C+L800M：\" prop=\"nmCl800m\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmCl800m\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmCl800m != null && oldData.nmCl800m != ammeter.nmCl800m\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmCl800m }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc' && isMobileBase\">\r\n                  <Col span=\"12\">\r\n                    <FormItem label=\"5GR站址编码：\" prop=\"stationcode5gr\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.stationcode5gr\"\r\n                        readonly\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationcode5gr != null &&\r\n                          oldData.stationcode5gr != ammeter.stationcode5gr\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationcode5gr }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"12\">\r\n                    <FormItem label=\"5GR站址名称：\" prop=\"stationname5gr\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.stationname5gr\"\r\n                        readonly\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationname5gr != null &&\r\n                          oldData.stationname5gr != ammeter.stationname5gr\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationname5gr }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel3\"\r\n          >关联用电类型比例\r\n          <div slot=\"content\" v-if=\"isClassification && ammeter.stationcode != null\">\r\n            <cl-table\r\n              ref=\"ammeterTable\"\r\n              strip\r\n              :columns=\"electro.columns\"\r\n              :data=\"electro.data\"\r\n              :searchable=\"false\"\r\n              :showPage=\"false\"\r\n              selectEnabled\r\n              selectMultiple\r\n            >\r\n              <div slot=\"buttons\">\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"md-add\"\r\n                  type=\"primary\"\r\n                  @click=\"addElectricType\"\r\n                  >新增\r\n                </Button>\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"ios-trash-outline\"\r\n                  type=\"warning\"\r\n                  @click=\"removeElectricType\"\r\n                  >删除\r\n                </Button>\r\n              </div>\r\n            </cl-table>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel4\"\r\n          >业主信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter2\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"联系人：\" prop=\"contractname\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.contractname\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.contractname != null &&\r\n                          oldData.contractname != ammeter.contractname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.contractname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"具体位置：\" prop=\"location\">\r\n                      <cl-input :maxlength=\"100\" v-model=\"ammeter.location\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.location != null && oldData.location != ammeter.location\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.location }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"办公电话：\" prop=\"officephone\">\r\n                      <cl-input :maxlength=\"15\" v-model=\"ammeter.officephone\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.officephone != null &&\r\n                          oldData.officephone != ammeter.officephone\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.officephone }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"移动电话：\" prop=\"telephone\">\r\n                      <cl-input :maxlength=\"15\" v-model=\"ammeter.telephone\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.telephone != null &&\r\n                          oldData.telephone != ammeter.telephone\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.telephone }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"对方单位：\" prop=\"userunit\">\r\n                      <cl-input\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.userunit\"\r\n                      ></cl-input>\r\n                      <ChooseModal\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        ref=\"chooseModalSup\"\r\n                        v-on:getDataFromModal=\"getDataFromModalObject\"\r\n                      />\r\n                      <Input\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        :maxlength=\"50\"\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.userunit\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"handleChooseSup()\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.userunit != null && oldData.userunit != ammeter.userunit\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.userunit }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款账户：\" prop=\"receiptaccountname\">\r\n                      <Select\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        v-model=\"ammeter.receiptaccountname\"\r\n                        style=\"width: 100%\"\r\n                        @on-change=\"onChange\"\r\n                      >\r\n                        <Option\r\n                          v-for=\"item in receiptaccountnameList\"\r\n                          :value=\"item.koinh\"\r\n                          :label=\"item.koinh\"\r\n                          :key=\"item.iid\"\r\n                        ></Option>\r\n                      </Select>\r\n                      <cl-input\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.receiptaccountname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccountname != null &&\r\n                          oldData.receiptaccountname != ammeter.receiptaccountname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccountname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款开户支行：\" prop=\"receiptaccountbank\">\r\n                      <cl-input\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        :maxlength=\"50\"\r\n                        readonly\r\n                        v-model=\"ammeter.receiptaccountbank\"\r\n                      ></cl-input>\r\n                      <cl-input\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.receiptaccountbank\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccountbank != null &&\r\n                          oldData.receiptaccountbank != ammeter.receiptaccountbank\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccountbank }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款账号：\" prop=\"receiptaccounts\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        readonly\r\n                        v-model=\"ammeter.receiptaccounts\"\r\n                      ></cl-input>\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        v-model=\"ammeter.receiptaccounts\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccounts != null &&\r\n                          oldData.receiptaccounts != ammeter.receiptaccounts\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccounts }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"24\">\r\n                    <FormItem label=\"说明：\" prop=\"memo\">\r\n                      <cl-input\r\n                        type=\"textarea\"\r\n                        :rows=\"3\"\r\n                        v-model=\"ammeter.memo\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"oldData.memo != null && oldData.memo != ammeter.memo\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.memo }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel5\"\r\n          >附件信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <attach-file\r\n                :param=\"fileParam\"\r\n                :attachData=\"attachData\"\r\n                v-on:setAttachData=\"setAttachData\"\r\n              />\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n      </Collapse>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addAmmeter,\r\n  listElectricType,\r\n  editAmmeter,\r\n  editAmmeterRecord,\r\n  updateAmmeter,\r\n  checkProjectNameExist,\r\n  checkAmmeterByStation,\r\n  getClassification,\r\n  getClassificationId,\r\n  getUserdata,\r\n  checkClassificationLevel,\r\n  listElectricTypeRatio,\r\n  checkAmmeterExist,\r\n  getUserByUserRole,\r\n  getCountryByUserId,\r\n  getCountrysdata,\r\n  removeAttach,\r\n  attchList,\r\n  getBankCard,\r\n} from \"@/api/basedata/ammeter.js\";\r\nimport { blist, btext } from \"@/libs/tools\";\r\nimport attachFile from \"./../protocol/attachFile\";\r\nimport SelectElectricType from \"./selectElectricType\";\r\nimport countryModal from \"./countryModal\";\r\nimport stationModal from \"./stationModal\";\r\nimport { mapMutations } from \"vuex\";\r\nimport { isEmpty } from \"@/libs/validate\";\r\nimport routers from \"@/router/routers\";\r\nimport { getHomeRoute } from \"@/libs/util\";\r\nimport AmmeterProtocolList from \"@/view/basedata/quota/listAmmeterProtocol\";\r\nimport customerList from \"./customerModal\";\r\nimport ChooseAmmeterModel from \"@/view/basedata/ammeter/chooseAmmeterModel\";\r\nimport ChooseModal from \"@/view/business/gasBusiness/chooseModal\";\r\nimport axios from \"@/libs/api.request\";\r\n\r\nexport default {\r\n  name: \"addAmmeter\",\r\n  components: {\r\n    attachFile,\r\n    AmmeterProtocolList,\r\n    customerList,\r\n    stationModal,\r\n    countryModal,\r\n    SelectElectricType,\r\n    ChooseAmmeterModel,\r\n    ChooseModal,\r\n  },\r\n  data() {\r\n    //不能输入汉字\r\n    const checkData = (rule, value, callback) => {\r\n      if (value) {\r\n        if (/[\\u4E00-\\u9FA5]/g.test(value)) {\r\n          callback(new Error(\"编码不能输入汉字!\"));\r\n        } else if (escape(value).indexOf(\"%u\") >= 0) {\r\n          callback(new Error(\"编码不能输入中文字符!\"));\r\n        } else {\r\n          callback();\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatorNumber = (rule, value, callback) => {\r\n      if (value.length <= 0) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatorNumberZero = (rule, value, callback) => {\r\n      if (value != null && value == 0) {\r\n        callback(new Error(\"只能输入大于0的数\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validateClassifications = (rule, value, callback) => {\r\n      if (value == undefined || value == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        if (value.length <= 0) {\r\n          callback(new Error(\"不能为空\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n\r\n    const validatePrice = (rule, value, callback) => {\r\n      if (value == undefined || value == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        if (value <= 0) {\r\n          callback(new Error(\"单价(含税)不能小于0\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatelumpstartdate = (rule, value, callback) => {\r\n      let data = this.ammeter;\r\n      let start = data.lumpstartdate;\r\n      let end = data.lumpenddate;\r\n      if (start == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      }\r\n      if (start != null && end != null) {\r\n        if (end <= start) {\r\n          callback(new Error(\"包干起始日期不能大于等于截止日期\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatelumpenddate = (rule, value, callback) => {\r\n      let data = this.ammeter;\r\n      let start = data.lumpstartdate;\r\n      let end = data.lumpenddate;\r\n      if (end == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      }\r\n      if (start != null && end != null) {\r\n        if (end <= start) {\r\n          callback(new Error(\"包干截止日期不能小于等于起始日期\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    //更改标题名称及样式\r\n    let renderHeader = (h, params) => {\r\n      let t = h(\r\n        \"span\",\r\n        {\r\n          style: {\r\n            fontWeight: \"normal\",\r\n            color: \"#ed4014\",\r\n            fontSize: \"12px\",\r\n            fontFamily: \"SimSun\",\r\n            marginRight: \"4px\",\r\n            lineHeight: 1,\r\n            display: \"inline-block\",\r\n          },\r\n        },\r\n        \"*\"\r\n      );\r\n      return h(\"div\", [t, h(\"span\", {}, \"所占比例(%)\")]);\r\n    };\r\n    return {\r\n      isRequireFlags: false,\r\n      propertyright: null, //局站产权\r\n      isRequireFlag: false, //局站是否必填\r\n      ischeckStation: false, //是否需要验证局站只能关联5个\r\n      isCDCompany: false, //是否是成都分公司\r\n      configVersion: null, //版本\r\n      isMobileBase: false, //是否生产用电-移动基站\r\n      propertyReadonly: true,\r\n      propertyList: [],\r\n      isDisable: false,\r\n      workFlowParams: {},\r\n      loading: false,\r\n      isLoading: null,\r\n\r\n      isError: false, //用电类型比例验证\r\n      isError1: false, //用电类型比例验证\r\n\r\n      showModel: false,\r\n      isClassification: false,\r\n      title: \"\",\r\n      isEditByCountry: false,\r\n      isCityAdmin: false,\r\n      isAdmin: false,\r\n      chooseIndex: null,\r\n      electroRowNum: null, //关联用电类型的当前行\r\n      electricTypeModel: false,\r\n      companies: [],\r\n      departments: [],\r\n      classificationData: [], //用电类型\r\n      receiptaccountnameList: [], //银行卡列表\r\n      fileParam: {\r\n        busiId: \"\",\r\n        busiAlias: \"附件(协议管理)\",\r\n        categoryCode: \"file\",\r\n        areaCode: \"ln\",\r\n      },\r\n      multiFiles: null,\r\n      files: [],\r\n      removeIds: [],\r\n      attachData: [],\r\n      oldData: [],\r\n      oldCategory: \"\", //原始数据\r\n      oldPackagetype: \"\", //原始数据\r\n      oldPayperiod: \"\", //原始数据\r\n      oldPaytype: \"\", //原始数据\r\n      oldElectronature: \"\", //原始数据\r\n      oldElectrovalencenature: \"\", //原始数据\r\n      oldElectrotype: \"\", //原始数据\r\n      oldStatus: \"\", //原始数据\r\n      oldProperty: \"\", //原始数据\r\n      oldAmmetertype: \"\", //原始数据\r\n      oldStationstatus: \"\", //原始数据\r\n      oldStationtype: \"\", //原始数据\r\n      oldAmmeteruse: \"\", //原始数据\r\n      oldDirectsupplyflag: \"\", //原始数据\r\n      ruleValidate: {\r\n        isentityammeter: [\r\n          { required: true, message: \"不能为空\", trigger: \"change,blur\" },\r\n        ],\r\n        projectname: [\r\n          //项目名称\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        countryName: [\r\n          //所属部门\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        country: [\r\n          //所属部门\r\n          { required: true, type: \"number\", validator: validatorNumber, trigger: \"blur\" },\r\n        ],\r\n        company: [{ required: true, validator: validatorNumber, trigger: \"blur\" }],\r\n        paytype: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        payperiod: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        ammeteruse: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        property: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        ammetertype: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        electrovalencenature: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        classifications: [\r\n          { required: true, validator: validateClassifications, trigger: \"change,blur\" },\r\n        ],\r\n        magnification: [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        directsupplyflag: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          { required: true, type: \"number\",\r\n            validator: validatePrice,\r\n            message: \"不能为空\", trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        packagetype: [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contractOthPart: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\r\n        stationName: [],\r\n        status: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        telephone: [{ pattern: /^1\\d{10}$/, message: \"格式不正确\", trigger: \"blur\" }],\r\n        percent: [\r\n          { type: \"number\", validator: validatorNumberZero, trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([0-9]\\d{0,12}))(\\.\\d{0,4})?$/,\r\n            message: \"只能保留四位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lumpstartdate: [\r\n          {\r\n            required: true,\r\n            type: \"date\",\r\n            validator: validatelumpstartdate,\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lumpenddate: [\r\n          {\r\n            required: true,\r\n            type: \"date\",\r\n            validator: validatelumpenddate,\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        fee: [\r\n          { required: true, type: \"number\", validator: validatorNumber, trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        supplybureauammetercode: [],\r\n        transdistricompany: [],\r\n        voltageClass: [],\r\n      },\r\n      electro: {\r\n        columns: [\r\n          {\r\n            title: \"序号\",\r\n            type: \"index\",\r\n          },\r\n          {\r\n            title: \"用电类型\",\r\n            key: \"typeName\",\r\n          },\r\n          {\r\n            title: \"所占比例(%)\",\r\n            key: \"ratio\",\r\n            renderHeader: renderHeader,\r\n            render: (h, params) => {\r\n              let that = this;\r\n              let ratio = params.row.ratio;\r\n              let isError1 = params.row.idError1;\r\n              let error = h(\r\n                \"label\",\r\n                {\r\n                  style: {\r\n                    color: \"#ed4014\",\r\n                    fontSize: \"12px\",\r\n                    fontFamily: \"SimSun\",\r\n                    paddingTop: \"6px\",\r\n                    lineHeight: 1,\r\n                    fontWeight: \"bold\",\r\n                    display: null != ratio ? \"none\" : \"inline-block\",\r\n                  },\r\n                },\r\n                \"不能为空\"\r\n              );\r\n              let error1 = h(\r\n                \"label\",\r\n                {\r\n                  style: {\r\n                    color: \"#ed4014\",\r\n                    fontSize: \"12px\",\r\n                    fontFamily: \"SimSun\",\r\n                    paddingTop: \"6px\",\r\n                    lineHeight: 1,\r\n                    fontWeight: \"bold\",\r\n                    display: isError1 == true ? \"inline-block\" : \"none\",\r\n                  },\r\n                },\r\n                \"输入比例不合格要求\"\r\n              );\r\n              let result = h(\"InputNumber\", {\r\n                style: {\r\n                  border: null == ratio || isError1 == true ? \"1px solid #ed4014\" : \"\",\r\n                },\r\n                props: {\r\n                  value: ratio,\r\n                  max: 100,\r\n                  min: 0.1,\r\n                },\r\n                on: {\r\n                  \"on-change\": (v) => {\r\n                    if (v == undefined || v == null) {\r\n                      that.isError = true;\r\n                    } else {\r\n                      that.isError = false;\r\n                    }\r\n                    //给data重新赋值\r\n                    // let reg = /^(?:[1-9]?\\d|100)$/;\r\n                    let reg = /^-?(([1-9][0-9]*)|(([0]\\.\\d{1,2}|[1-9][0-9]*\\.\\d{1,2})))$/;\r\n                    if (v != undefined && v != null && !reg.test(v)) {\r\n                      params.row.idError1 = true;\r\n                      that.isError1 = true;\r\n                    } else {\r\n                      params.row.idError1 = false;\r\n                      that.isError1 = false;\r\n                    }\r\n                    params.row.ratio = v;\r\n                    that.electro.data[params.row._index] = params.row;\r\n                  },\r\n                },\r\n              });\r\n              return h(\"div\", [result, error, error1]);\r\n            },\r\n          },\r\n          {\r\n            title: \"关联局站\",\r\n            key: \"stationName\",\r\n            render: (h, params) => {\r\n              let stationName = params.row.stationName;\r\n              let disabled = params.row._disabled;\r\n              if (disabled != undefined && disabled == true) {\r\n                return h(\"Input\", {\r\n                  props: {\r\n                    value: stationName,\r\n                    readonly: true,\r\n                  },\r\n                });\r\n              } else {\r\n                return h(\"Input\", {\r\n                  props: {\r\n                    value: stationName,\r\n                    icon: \"ios-archive\",\r\n                    placeholder: \"点击图标选择\",\r\n                    readonly: true,\r\n                  },\r\n                  on: {\r\n                    \"on-click\": (v) => {\r\n                      this.chooseResponseCenter(2, params, params.row._index);\r\n                    },\r\n                  },\r\n                });\r\n              }\r\n            },\r\n          },\r\n        ],\r\n        data: [],\r\n      },\r\n      ammeter: {\r\n        maxdegree: null,\r\n        ammetername: \"\",\r\n        userunit: \"\",\r\n        id: null,\r\n        country: null,\r\n        company: null,\r\n        countryName: null,\r\n        billStatus: 0,\r\n        electricTypes: [],\r\n        electro: [],\r\n        classifications: [], //用电类型\r\n        iszgz: 0,\r\n        userunitCode: 0,\r\n        directFlag: 0,\r\n        officeFlag: 0,\r\n        transdistricompany: 1,\r\n        voltageClass: \"\",\r\n        issmartammeter: \"0\",\r\n        price: \"\",\r\n        stationcode5gr: null,\r\n        stationname5gr: null,\r\n      },\r\n      iszgzOnly: false,\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    ...mapMutations([\"closeTag\", \"closeTagByName\"]),\r\n    getProperty(v) {},\r\n    OK(type) {\r\n      // if (\r\n      //   this.ammeter.price == undefined ||\r\n      //   this.ammeter.price == null ||\r\n      //   this.ammeter.price == \"\"\r\n      // ) {\r\n      //   this.$Message.error(\"单价(含税元)不能为空\");\r\n      //   return;\r\n      // }\r\n      // if (\r\n      //   this.ammeter.magnification == undefined ||\r\n      //   this.ammeter.magnification == null ||\r\n      //   this.ammeter.magnification == \"\"\r\n      // ) {\r\n      //   this.$Message.error(\"倍率不能为空\");\r\n      //   return;\r\n      // }\r\n      // if (this.ammeter.status == 1) {\r\n      //   if (\r\n      //     this.ammeter.stationName == undefined ||\r\n      //     this.ammeter.stationName == null ||\r\n      //     this.ammeter.stationName == \"\"\r\n      //   ) {\r\n      //     this.$Message.error(\"局(站)名称不能为空\");\r\n      //     return;\r\n      //   }\r\n      // }\r\n      if (type == 1) {\r\n        this.isLoading = 1;\r\n      } else {\r\n        this.isLoading = 0;\r\n      }\r\n      if (this.loading == true) {\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      this.ammeter.electricTypes = this.electro.data;\r\n      this.$refs.ammeter.validate((valid) => {\r\n        if (valid) {\r\n          this.$refs.ammeter1.validate((valid1) => {\r\n            if (valid1) {\r\n              this.$refs.ammeter2.validate((valid2) => {\r\n                if (valid2) {\r\n                  this.checkData(type);\r\n                } else {\r\n                  this.$Message.error(\"业主信息验证没通过\");\r\n                  this.loading = false;\r\n                }\r\n              });\r\n            } else {\r\n              this.$Message.error(\"关联局站信息验证没通过\");\r\n              this.loading = false;\r\n            }\r\n          });\r\n        } else {\r\n          // this.$Message.error(\"基本信息验证没通过\");\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n\r\n    onModalOK(type) {\r\n      this.isDisable = true;\r\n      setTimeout(() => {\r\n        this.isDisable = false; //点击一次时隔两秒后才能再次点击\r\n      }, 3000);\r\n      let attachData = [];\r\n      this.OK(type);\r\n    },\r\n\r\n    setAttachData(data) {\r\n      this.multiFiles = data.data;\r\n      this.removeIds = data.ids;\r\n      if (this.removeIds.length != 0 && data.type == \"remove\") {\r\n        this.removeAttach();\r\n      } else {\r\n        this.upload();\r\n      }\r\n    },\r\n    removeAttach() {\r\n      removeAttach({ ids: this.removeIds.join() }).then(() => {});\r\n    },\r\n    upload() {\r\n      if (this.attachData.length != 0 && this.multiFiles.length != 0) {\r\n        this.loading = true;\r\n        axios\r\n          .request({\r\n            url: \"/common/attachments/uploadMultiFile\",\r\n            method: \"post\",\r\n            data: this.multiFiles,\r\n          })\r\n          .then((res) => {\r\n            if (res.data.code != 0) {\r\n              this.loading = false;\r\n            }\r\n            let that = this;\r\n            attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n              that.attachData = Object.assign([], res.data.rows);\r\n            });\r\n          });\r\n      }\r\n    },\r\n\r\n    //验证数据\r\n    checkData(type) {\r\n      let types = this.ammeter.classifications;\r\n      this.ammeter.electrotype = types[types.length - 1];\r\n      let that = this;\r\n      if (\r\n        this.ammeter.status === 1 &&\r\n        (this.configVersion == \"sc\" || this.configVersion == \"SC\")\r\n      ) {\r\n        //在用状态下验证局站地址不能为空\r\n        if (\r\n          this.ammeter.stationaddress == null ||\r\n          this.ammeter.stationaddress == undefined\r\n        ) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"局站地址不能为空，请在局站管理维护该局站信息！\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      if (this.checkElectricTypeItem()) {\r\n        if (this.ammeter.ammetername != undefined || this.ammeter.ammetername != null) {\r\n          checkAmmeterExist(this.ammeter.id, this.ammeter.ammetername, 0).then((res) => {\r\n            //验证电表是否存在\r\n            let code = res.data.code;\r\n            if (code == 0) {\r\n              this.checkedDate(type);\r\n            } else {\r\n              that.loading = false;\r\n            }\r\n          });\r\n        } else {\r\n          this.checkedDate(type);\r\n        }\r\n      }\r\n    },\r\n    checkedDate(type) {\r\n      let that = this;\r\n      checkProjectNameExist(that.ammeter.id, that.ammeter.projectname, 0).then((res) => {\r\n        //验证项目名称是否存在\r\n        let code = res.data.code;\r\n        if (code == 0) {\r\n          if (that.isCheckStation == true) {\r\n            that.isCheckStation(type);\r\n          } else {\r\n            that.saveData(type); //保存数据\r\n          }\r\n        } else {\r\n          that.loading = false;\r\n        }\r\n      });\r\n    },\r\n    isCheckStation(type) {\r\n      let that = this;\r\n      checkAmmeterByStation({\r\n        id: that.ammeter.id,\r\n        type: 0,\r\n        stationcode: that.ammeter.stationcode,\r\n        electrotype: that.ammeter.electrotype,\r\n        ammeteruse: that.ammeter.ammeteruse,\r\n      }).then((res) => {\r\n        let code = res.data.code;\r\n        if (code == \"error\") {\r\n          that.$Modal.warning({ title: \"温馨提示\", content: res.data.msg });\r\n          that.loading = false;\r\n        } else {\r\n          that.saveData(type); //保存数据\r\n        }\r\n      });\r\n    },\r\n    saveData(type) {\r\n      let that = this;\r\n      this.clearDataByCondition();\r\n      that.ammeter.category = 1; //电表\r\n      addAmmeter(that.ammeter)\r\n        .then((res) => {\r\n          if (res.data != null && res.data != -1 && res.data.success == \"1\") {\r\n            if (type == 1) {\r\n              that.startFlow(res.data);\r\n            } else {\r\n              this.closeTag({ route: this.$route });\r\n              that.warn();\r\n            }\r\n          } else {\r\n            that.loading = false;\r\n            that.$Notice.error({ title: \"提示\", desc: res.data.msg, duration: 10 });\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          that.loading = false;\r\n        });\r\n    },\r\n    //根据条件判断数据是否该清除\r\n    clearDataByCondition() {\r\n      if (this.ammeter.property !== 2 && this.ammeter.property !== 4) {\r\n        //站址产权归属为铁塔 清除分割比例checkAmmeterByStation，是否铁塔按RRU包干\r\n        this.ammeter.percent = null;\r\n      }\r\n      if (this.ammeter.ammeteruse !== 3) {\r\n        //电表用途不是回收电费，清除父电表信息\r\n        this.ammeter.parentId = null;\r\n        this.ammeter.customerId = null;\r\n      }\r\n      if (this.ammeter.directsupplyflag != 1) {\r\n        //只有对外结算类型为直供电才填写该字段，转供电不需填写\r\n        this.ammeter.electrovalencenature = null;\r\n      }\r\n      if (!this.isCDCompany) {\r\n        //成都分公司显示合同对方等，不是，就清除数据\r\n        this.ammeter.contractOthPart = null;\r\n        this.ammeter.nmCcode = null;\r\n        this.ammeter.nmL2100 = null;\r\n        this.ammeter.nmL1800 = null;\r\n        this.ammeter.nmCl800m = null;\r\n      }\r\n    },\r\n    warn() {\r\n      this.$Modal.warning({\r\n        title: \"温馨提示\",\r\n        content: \"保存后的数据要提交审批才能生效！\",\r\n      });\r\n    },\r\n    refreshData() {\r\n      this.initData();\r\n    },\r\n    initData() {\r\n      this.countryName = \"\";\r\n      this.electro.data = [];\r\n      this.oldData = [];\r\n      this.removeIds = [];\r\n      this.multiFiles = [];\r\n      this.files = [];\r\n      this.isCityAdmin = false;\r\n      this.isAdmin = false;\r\n      this.isEditByCountry = false;\r\n      this.$nextTick(() => {\r\n        this.$refs.ammeter.resetFields(); // this.$refs.adduserform.resetFields();\r\n        this.$refs.ammeter1.resetFields(); // this.$refs.adduserform.resetFields();\r\n        this.$refs.ammeter2.resetFields(); // this.$refs.adduserform.resetFields();\r\n      });\r\n      this.showModel = false;\r\n      this.electricTypeModel = false;\r\n      this.directFlag = 0;\r\n      this.officeFlag = 0;\r\n      this.transdistricompany = 1;\r\n    },\r\n    onModalCancel() {\r\n      this.initData();\r\n    },\r\n    /*初始化*/\r\n    initAmmeter(id) {\r\n      this.initData();\r\n      let that = this;\r\n      that.title = \"添加电表\";\r\n      editAmmeter(\"\", 0).then((res) => {\r\n        that.setAmmeter(Object.assign({}, res.data));\r\n       // this.ammeter.price = 0;\r\n        that.getUser();\r\n      });\r\n      getClassification().then((res) => {\r\n        //用电类型\r\n        that.classificationData = res.data;\r\n      });\r\n    },\r\n    changeStatus() {\r\n      if (this.ammeter.status == 1) {\r\n        this.isRequireFlag = true;\r\n        this.ruleValidate.stationName = [\r\n          {\r\n            required: true,\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ];\r\n      } else {\r\n        this.isRequireFlag = false;\r\n        this.ruleValidate.stationName = [\r\n          {\r\n            required: false,\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ];\r\n      }\r\n    },\r\n    selectChange() {\r\n      if (this.ammeter.company != undefined) {\r\n        if (this.ammeter.company == \"1000085\") {\r\n          this.isCDCompany = true;\r\n        } else {\r\n          this.isCDCompany = false;\r\n        }\r\n        getCountryByUserId(this.ammeter.company).then((res) => {\r\n          this.departments = res.data.departments;\r\n          this.ammeter.country = res.data.departments[0].id;\r\n          this.ammeter.countryName = this.departments[0].name;\r\n        });\r\n      }\r\n    },\r\n    getUser() {\r\n      let that = this;\r\n      getUserByUserRole().then((res) => {\r\n        //当前登录用户所在公司\r\n        that.companies = res.data.companies;\r\n        that.isCityAdmin = res.data.isEditAdmin;\r\n        if (\r\n          res.data.isCityAdmin == true ||\r\n          res.data.isProAdmin == true ||\r\n          res.data.isSubAdmin == true\r\n        ) {\r\n          that.isAdmin = true;\r\n        }\r\n        getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {\r\n          //根据权限获取所属部门\r\n          that.departments = res.data;\r\n          that.getUserData();\r\n        });\r\n      });\r\n    },\r\n    getUserData() {\r\n      let that = this;\r\n      getUserdata().then((res) => {\r\n        //当前登录用户所在公司和所属部门\r\n        let companies = that.companies;\r\n        if (res.data.companies != null && res.data.companies.length != 0) {\r\n          if (res.data.companies[0].id != \"2600000000\") {\r\n            companies = res.data.companies;\r\n            // ;\r\n          }\r\n        }\r\n        that.company = companies[0].id;\r\n        if (that.company == \"1000085\") {\r\n          that.isCDCompany = true;\r\n        }\r\n        that.ammeter.company = companies[0].id;\r\n\r\n        let departments = that.departments;\r\n        if (res.data.departments != null && res.data.departments.length != 0) {\r\n          if (res.data.companies[0].id != \"2600000000\") {\r\n            departments = res.data.departments;\r\n          }\r\n        }\r\n        that.country = departments[0].id;\r\n        that.countryName = departments[0].name;\r\n        that.ammeter.country = Number(departments[0].id);\r\n        that.ammeter.countryName = departments[0].name;\r\n      });\r\n    },\r\n    setOldData(data) {\r\n      this.oldCategory = btext({\r\n        category: \"ammeterCategory\",\r\n        v: data.category,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldPackagetype = btext({\r\n        category: \"packageType\",\r\n        v: data.packagetype,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldPayperiod = btext({\r\n        category: \"payPeriod\",\r\n        v: data.payperiod,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldPaytype = btext({\r\n        category: \"payType\",\r\n        v: data.paytype,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldElectronature = btext({\r\n        category: \"electroNature\",\r\n        v: data.electronature,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldElectrovalencenature = btext({\r\n        category: \"electrovalenceNature\",\r\n        v: data.electrovalencenature,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldElectrotype = btext({\r\n        category: \"electroType\",\r\n        v: data.electrotype,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldStatus = btext({\r\n        category: \"status\",\r\n        v: data.status,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldProperty = btext({\r\n        category: \"property\",\r\n        v: data.property,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldAmmetertype = btext({\r\n        category: \"ammeterType\",\r\n        v: data.ammetertype,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldStationstatus = btext({\r\n        category: \"stationStatus\",\r\n        v: data.stationstatus,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldStationtype = btext({\r\n        category: \"BUR_STAND_TYPE\",\r\n        v: data.stationtype,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldAmmeteruse = btext({\r\n        category: \"ammeterUse\",\r\n        v: data.ammeteruse,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldDirectsupplyflag = btext({\r\n        category: \"directSupplyFlag\",\r\n        v: data.directsupplyflag,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n    },\r\n\r\n    setAmmeter(form) {\r\n      if (form.status == null) {\r\n        form.status = 1;\r\n        this.isRequireFlag = true;\r\n        this.ruleValidate.stationName = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n      form.issmartammeter = form.issmartammeter == null ? \"0\" : form.issmartammeter + \"\";\r\n      form.iszgz = form.iszgz == null ? \"0\" : form.iszgz + \"\";\r\n      form.directFlag = form.directFlag == null ? \"0\" : form.directFlag + \"\";\r\n      form.officeFlag = form.officeFlag == null ? \"0\" : form.officeFlag + \"\";\r\n      form.isairconditioning =\r\n        form.isairconditioning == null ? \"0\" : form.isairconditioning + \"\";\r\n      form.islumpsum = form.islumpsum == null ? \"0\" : form.islumpsum + \"\";\r\n      this.ammeter = form;\r\n      let electrotype = this.ammeter.electrotype;\r\n      if (\r\n        electrotype === 111 ||\r\n        electrotype === 112 ||\r\n        electrotype === 113 ||\r\n        electrotype === 2\r\n      ) {\r\n        this.isClassification = true;\r\n      }\r\n      if (this.ammeter.magnification == null || this.ammeter.magnification == undefined) {\r\n        this.ammeter.magnification = 1;\r\n      }\r\n      // if (this.ammeter.maxdegree == null || this.ammeter.maxdegree == undefined) {\r\n      //   this.ammeter.maxdegree = 1;\r\n      // }\r\n      // if (this.ammeter.price == null || this.ammeter.price == undefined) {\r\n      //   this.ammeter.price = 1;\r\n      // }\r\n      if (this.ammeter.company != null) {\r\n        this.ammeter.company = this.ammeter.company + \"\";\r\n      }\r\n      this.fileParam.busiId = this.ammeter.id;\r\n    },\r\n\r\n    //修改电表、协议的用电类型时，如用电类型不再与原先选择的局站的局站类型匹配时，系统自动清空原关联局站，需用户重新再关联局站。\r\n    changeClassifications(value) {\r\n      this.clearStation();\r\n      this.isClassification = false;\r\n      if (value.length == 0) {\r\n        this.ammeter.property = null;\r\n        this.propertyReadonly = true;\r\n      } else {\r\n        this.propertyReadonly = false;\r\n        this.ammeter.electrotype = value[value.length - 1];\r\n        let electrotype = this.ammeter.electrotype;\r\n        this.isMobileBase = electrotype > 1400 ? true : false;\r\n        if (electrotype == 1411 || electrotype == 1412) {\r\n          //控制产权归属\r\n          this.ammeter.property = 2;\r\n          this.propertyReadonly = true;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else if (electrotype == 1421 || electrotype == 1422) {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = 4;\r\n          this.ruleValidate.property = [\r\n            { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else if (electrotype == 1431 || electrotype == 1432) {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = 1 + \"\";\r\n          this.ruleValidate.property = [\r\n            { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = null;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n          this.ruleValidate.property = [\r\n            { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        }\r\n        let stationtype = this.ammeter.stationtype;\r\n        if (electrotype === 111 || electrotype === 112 || electrotype === 113) {\r\n          this.isClassification = true;\r\n          if (stationtype !== 10001) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 121 || electrotype === 112) {\r\n          if (stationtype !== 10003 && stationtype !== 10004) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 131 || electrotype === 132 || electrotype === 133) {\r\n          if (stationtype !== 10005) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 1411 || electrotype === 1412) {\r\n          if (\r\n            stationtype !== 10002 ||\r\n            (stationtype == 10002 && this.propertyright !== 3)\r\n          ) {\r\n            this.clearStation();\r\n          }\r\n        } else if (\r\n          electrotype === 1421 ||\r\n          electrotype === 1422 ||\r\n          electrotype === 1431 ||\r\n          electrotype === 1432\r\n        ) {\r\n          if (stationtype !== 10002) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 2) {\r\n          this.isClassification = true;\r\n        }\r\n        if (this.configVersion == \"sc\" && this.ammeter.stationaddresscode) {\r\n          //“51”开头铁塔站址编码控制\r\n          if (\r\n            [1411, 1412].includes(electrotype) &&\r\n            !this.ammeter.stationaddresscode.startsWith(\"51\")\r\n          ) {\r\n            this.clearStation();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    changedirectsupply(value) {\r\n      if (this.ammeter.directsupplyflag == 1) {\r\n        this.isRequireFlags = false;\r\n        this.ruleValidate.supplybureauammetercode = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      } else {\r\n        this.isRequireFlags = true;\r\n        this.ruleValidate.supplybureauammetercode = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n    },\r\n    clearStation() {\r\n      //清除局站信息\r\n      this.ammeter.stationName = null;\r\n      this.ammeter.stationcode = null;\r\n      this.ammeter.stationstatus = null;\r\n      this.ammeter.stationtype = null;\r\n      this.ammeter.stationaddress = null;\r\n      this.ammeter.stationaddresscode = null;\r\n    },\r\n\r\n    //选择所属部门开始\r\n    chooseResponseCenter(index, params, electroRowNum) {\r\n      this.chooseIndex = index;\r\n      this.electroRowNum = electroRowNum;\r\n      if (index == 1 || index == 2) {\r\n        let types = this.ammeter.classifications;\r\n        if (types.length == 0) {\r\n          this.$Modal.warning({ title: \"温馨提示\", content: \"请先选择用电类型！\" });\r\n          return;\r\n        } else if (this.ammeter.ammeteruse == null) {\r\n          this.$Modal.warning({ title: \"温馨提示\", content: \"请先选择电表用途！\" });\r\n          return;\r\n        } else {\r\n          if (this.ammeter.company == null) {\r\n            this.$Message.info(\"请先选择分公司\");\r\n            return;\r\n          }\r\n          this.ammeter.electrotype = types[types.length - 1];\r\n          this.$refs.stationModal.ammeterid = this.ammeter.id;\r\n          this.$refs.stationModal.initDataList(\r\n            this.ammeter.electrotype,\r\n            0,\r\n            this.ammeter.ammeteruse,\r\n            this.ammeter.company,\r\n            params\r\n          ); //局站\r\n          // }\r\n        }\r\n      } else {\r\n        if (this.ammeter.company == null) {\r\n          this.$Message.info(\"请先选择分公司\");\r\n          return;\r\n        }\r\n        this.$refs.countryModal.choose(this.ammeter.company); //所属部门\r\n      }\r\n    },\r\n    //部门\r\n    getDataFromModal(data) {\r\n      this.ammeter.country = data.id;\r\n      this.ammeter.countryName = data.name;\r\n      //选择所属部门结束\r\n    },\r\n    //\r\n    getDataFromModalObject(data, flag) {\r\n      this.handleChooseSup(data); // 传 true 设置 回调值\r\n    },\r\n    onChange(v) {\r\n      this.receiptaccountnameList.forEach((item) => {\r\n        if (item.koinh === v) {\r\n          this.ammeter.receiptaccountbank = item.banka;\r\n          this.ammeter.receiptaccounts = item.bankn;\r\n        }\r\n      });\r\n    },\r\n    //选择对方单位\r\n    handleChooseSup(data) {\r\n      if (!data) {\r\n        this.$refs.chooseModalSup.choose(1); //打开模态框\r\n      } else {\r\n        (this.ammeter.userunit = data.name),\r\n          getBankCard({ lifnr: data.id }).then((res) => {\r\n            this.receiptaccountnameList = res.data.rows;\r\n          });\r\n      }\r\n    },\r\n    //获取局站数据 flag=true需要验证实际报账个数,flag=false不需要验证实际报账个数\r\n    getDataFromStationModal(data, flag) {\r\n      this.ischeckStation = flag;\r\n      if (this.chooseIndex == 2) {\r\n        this.electro.data[this.electroRowNum].stationId = data.id;\r\n        this.electro.data[this.electroRowNum].stationName = data.stationname;\r\n      } else {\r\n        this.propertyright = data.propertyright;\r\n        this.ammeter.stationName = data.stationname;\r\n        this.ammeter.stationcode = data.id;\r\n        //所有的下拉默认值都要字符串才能显示了 跟之前完全变了\r\n        this.ammeter.stationstatus = Number(\r\n          data.status == undefined ? data.STATUS : data.status\r\n        );\r\n        this.ammeter.stationtype = data.stationtype;\r\n        this.ammeter.stationaddress = data.address;\r\n        this.ammeter.stationname5gr = data.stationname5gr;\r\n        this.ammeter.stationcode5gr = data.stationcodeintid;\r\n        this.ammeter.map = data.map;\r\n        this.ammeter.stationaddresscode = data.resstationcode;\r\n        this.ammeter.resstationcode = data.resstationcode;\r\n        //默认生成一条关联用电类型\r\n        let that = this;\r\n        listElectricType({ id: data.stationtype }).then((res) => {\r\n          that.electro.data = Object.assign([], res.data.rows);\r\n          this.electro.data[0].stationId = data.id;\r\n          this.electro.data[0].stationName = data.stationname;\r\n          this.electro.data[0]._disabled = true;\r\n        });\r\n      }\r\n    },\r\n\r\n    /*添加电表关联用电类型比率*/\r\n    addElectricType() {\r\n      this.$refs.selectElectricType.initElectricType();\r\n    },\r\n\r\n    /*移除选中的用电类型比率*/\r\n    removeElectricType() {\r\n      let rows = this.$refs.ammeterTable.getSelection();\r\n      let datas = this.electro.data;\r\n      rows.forEach((item) => {\r\n        if (item._index != undefined) {\r\n          datas.splice(item._index, 1);\r\n        } else {\r\n          datas.forEach((data) => {\r\n            if (data.id === item.id) {\r\n              let index = datas.indexOf(data);\r\n              datas.splice(index, 1);\r\n            }\r\n          });\r\n        }\r\n      });\r\n      this.electro.data = datas;\r\n    },\r\n\r\n    /* 设置用电类型列表*/\r\n    setElectricData: function (data) {\r\n      let origin = this.electro.data;\r\n      if (origin.length < 1) {\r\n        this.electro.data = data;\r\n      } else {\r\n        let tem = data;\r\n        for (let j = 0; j < origin.length; j++) {\r\n          for (let i = 0; i < data.length; i++) {\r\n            let typeId =\r\n              origin[j].electroTypeId != undefined\r\n                ? origin[j].electroTypeId\r\n                : origin[j].id;\r\n            if (data[i].id === typeId) {\r\n              tem.splice(tem.indexOf(data[i]), 1);\r\n            }\r\n          }\r\n        }\r\n        this.electro.data = this.electro.data.concat(tem);\r\n      }\r\n    },\r\n\r\n    //用电类型比例校验\r\n    checkElectricTypeItem() {\r\n      let items = this.electro.data;\r\n      //当“用电类型”选择“111 A类机楼（机房），112 B类机楼（机房），113 C类机楼（机房） ”或“2 管理办公用电”时，才需填用电类型分比且必填，用电类型比例之和必须等于100%\r\n      if (\r\n        this.ammeter.electrotype === 111 ||\r\n        this.ammeter.electrotype === 112 ||\r\n        this.ammeter.electrotype === 113 ||\r\n        this.ammeter.electrotype === 2\r\n      ) {\r\n        let sumRatio = items.reduce((total, item) => {\r\n          return total + item.ratio;\r\n        }, 0);\r\n        if (sumRatio !== 100) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型所占比例和必须为100%，当前值为\" + sumRatio + \"%\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    startFlow(data) {\r\n      let busiTitle = \"新增电表(\" + data.projectname + \")审批\";\r\n      this.workFlowParams = {\r\n        busiId: data.id,\r\n        busiAlias: \"ADD_AMM\",\r\n        busiTitle: busiTitle,\r\n      };\r\n      let that = this;\r\n      setTimeout(function () {\r\n        that.$refs.clwfbtn.onClick();\r\n      }, 200);\r\n    },\r\n    doWorkFlow(data) {\r\n      //流程回调\r\n      this.loading = false;\r\n      this.closeTag({ route: this.$route });\r\n      if (data == 0) {\r\n        this.warn();\r\n      }\r\n    },\r\n    /*选择电表/协议*/\r\n    addAmmeterProtocol() {\r\n      this.$refs.selectAmmeterProtocol.initDataList(1, null);\r\n    },\r\n    /* 选择电表户号/协议编号*/\r\n    setAmmeterProrocolData: function (data) {\r\n      this.ammeter.parentId = data.id;\r\n      if (data.protocolname != null && data.protocolname.length != 0) {\r\n        this.ammeter.parentCode = data.protocolname;\r\n      } else {\r\n        this.ammeter.parentCode = data.ammetername;\r\n      }\r\n    },\r\n    /*选择客户*/\r\n    addCustomer() {\r\n      this.$refs.customerList.choose(2); //打开模态框\r\n    },\r\n    getDataFromCustomerModal: function (data) {\r\n      this.ammeter.customerId = data.id;\r\n      this.ammeter.customerName = data.name;\r\n    },\r\n    //选择包干的时候修改默认包干类型\r\n    updatepackagetype() {\r\n      let data = this.ammeter;\r\n      data.packagetype = null;\r\n    },\r\n    iszgzchange() {\r\n      if (this.ammeter.iszgz == \"1\") {\r\n        this.ammeter.directsupplyflag = 1;\r\n        this.iszgzOnly = true;\r\n      } else {\r\n        this.iszgzOnly = false;\r\n      }\r\n    },\r\n    chooseoldammetername() {\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.status = 0;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.ammeteruse = 1;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.type = 3;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.company = this.ammeter.company;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.country = this.ammeter.country;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.directsupplyflag = 2;\r\n      this.$refs.chooseAmmeterModel.modal.show = true;\r\n      this.$Message.info(\"双击选择！！\");\r\n    },\r\n    getAmmeterModelModal(data) {\r\n      this.ammeter.oldammetername = data.name + \",\" + data.id;\r\n    },\r\n    projectNameChange(val) {\r\n      if (\r\n        !/^.*([^\\u0000-\\u00ff]+路).*$/.test(val) &&\r\n        !/^.*([^\\u0000-\\u00ff]*)([0-9]*号).*$/.test(val) &&\r\n        !/^.*([^\\u0000-\\u00ff]+楼电表).*$/.test(val)\r\n      ) {\r\n        this.$Message.info(\"温馨提示：集团要求格式为(**路**号**楼电表)\");\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    //直接从前台取\r\n    this.categorys = {\r\n      directsupplyflag: blist(\"directSupplyFlag\"),\r\n    };\r\n    this.propertyList = blist(\"property\");\r\n    this.initAmmeter(this.$route.query.id);\r\n\r\n    this.configVersion = this.$config.version;\r\n    if (this.configVersion != \"ln\" && this.configVersion != \"LN\") {\r\n      this.ruleValidate.ammetername = [\r\n        { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n      ];\r\n      this.ruleValidate.customerName = [\r\n        { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n      ];\r\n      this.ruleValidate.userunit = [\r\n        {\r\n          required: true,\r\n          message: \"不能为空\",\r\n\r\n          trigger: \"blur,change\",\r\n        },\r\n      ];\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.margin-right-width {\r\n  margin-right: 10px;\r\n}\r\n\r\n.testaa .ivu-row {\r\n  margin-left: 5px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.testaa .requireStar .ivu-form-item-label:before {\r\n  content: \"*\";\r\n  display: inline-block;\r\n  margin-right: 4px;\r\n  line-height: 1;\r\n  font-family: SimSun;\r\n  font-size: 12px;\r\n  color: #ed4014;\r\n}\r\n</style>\r\n"]}]}