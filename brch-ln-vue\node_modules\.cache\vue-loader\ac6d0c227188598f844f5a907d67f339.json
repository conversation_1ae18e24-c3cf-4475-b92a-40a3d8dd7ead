{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\business\\mssAccountbill\\basicMes.vue?vue&type=style&index=0&id=a60bd272&lang=less&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\business\\mssAccountbill\\basicMes.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749*********}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZmxvYXRyaWdodCB7DQogIG1hcmdpbjogNXB4Ow0KfQ0KDQouaXZ1LXRhYmxlIC5kZW1vLXRhYmxlLWluZm8tY2VsbC1zdW0gew0KICAvKiBiYWNrZ3JvdW5kLWNvbG9yOiAjZmY2NjAwOyAqLw0KICBjb2xvcjogI2ZmZjsNCn0NCg0KLml2dS10YWJsZSAuZGVtby10YWJsZS1pbmZvLWNlbGwtaW5wdXRUYXhTdW0gew0KICBiYWNrZ3JvdW5kLWNvbG9yOiBncmVlbjsNCiAgY29sb3I6ICNmZmY7DQp9DQoNCi5pdnUtdGFibGUgLmRlbW8tdGFibGUtZXJyb3Igew0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZWQ7DQogIGNvbG9yOiAjZmZmOw0KfQ0KDQouY2xhc3NOYW1lU3VtIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzJkYjdmNTsNCiAgY29sb3I6ICNmZmY7DQp9DQo="}, {"version": 3, "sources": ["basicMes.vue"], "names": [], "mappings": ";AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "basicMes.vue", "sourceRoot": "src/view/business/mssAccountbill", "sourcesContent": ["<style lang=\"less\">\r\n.floatright {\r\n  margin: 5px;\r\n}\r\n\r\n.ivu-table .demo-table-info-cell-sum {\r\n  /* background-color: #ff6600; */\r\n  color: #fff;\r\n}\r\n\r\n.ivu-table .demo-table-info-cell-inputTaxSum {\r\n  background-color: green;\r\n  color: #fff;\r\n}\r\n\r\n.ivu-table .demo-table-error {\r\n  background-color: red;\r\n  color: #fff;\r\n}\r\n\r\n.classNameSum {\r\n  background-color: #2db7f5;\r\n  color: #fff;\r\n}\r\n</style>\r\n<template>\r\n  <div>\r\n    <!-- <Spin size=\"large\" fix v-if=\"basicMes.id==null\"> -->\r\n    <!-- <Icon type=\"ios-loading\" class=\"demo-spin-icon-load\"></Icon> -->\r\n    <!-- </Spin> -->\r\n    <Collapse v-model=\"collapsevalue\">\r\n      <Panel name=\"1\">\r\n        基本信息\r\n        <!-- <h3 slot=\"title\">基本信息</h3> -->\r\n        <div slot=\"content\">\r\n          <Form\r\n            ref=\"basicMesForm\"\r\n            :model=\"basicMes\"\r\n            :rules=\"ruleValidate\"\r\n            :label-width=\"100\"\r\n            inline\r\n          >\r\n            <FormItem label=\"报账单类型:\" prop=\"billtype\">\r\n              <Select\r\n                @on-change=\"setTdlable\"\r\n                v-model=\"basicMes.billtype\"\r\n                :style=\"formItemWidth\"\r\n                :disabled=\"billtypedisabled\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in categorys.billtype\"\r\n                  :key=\"item.typeCode\"\r\n                  :value=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                >\r\n                  {{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"报账人:\" prop=\"fillInName\">\r\n              <cl-input v-model=\"basicMes.fillInName\" readonly :style=\"formItemWidth\" />\r\n            </FormItem>\r\n            <FormItem label=\"公司代码：\" prop=\"companyNameTxt\">\r\n              <!--                            <Input v-model=\"basicMes.companyNameTxt\" :style=\"formItemWidth\"/>-->\r\n              <Select\r\n                v-model=\"basicMes.companyNameTxt\"\r\n                :disabled=\"disableCompanyNameTxt\"\r\n                :style=\"formItemWidth\"\r\n              >\r\n                <Option v-for=\"item in companyNameList\" :value=\"item\" :key=\"item\">{{\r\n                  item\r\n                }}</Option>\r\n              </Select>\r\n              <!--                          <Input v-model=\"basicMes.companyNameTxt\" :style=\"formItemWidth\" icon=\"ios-archive\"-->\r\n              <!--                                 @on-click=\"getFileorgCode\"/>-->\r\n            </FormItem>\r\n            <FormItem label=\"报账单位(财辅):\" prop=\"fillInCostCenterName\">\r\n              <!--fillInDep-->\r\n              <Input\r\n                v-model=\"basicMes.fillInCostCenterName\"\r\n                readonly\r\n                :style=\"formItemWidth\"\r\n                placeholder=\"点击公司代码选择\"\r\n              />\r\n            </FormItem>\r\n            <FormItem label=\"报账人电话:\" prop=\"telephone\">\r\n              <cl-input\r\n                v-model=\"basicMes.telephone\"\r\n                placeholder=\"请输入电话\"\r\n                :style=\"formItemWidth\"\r\n              />\r\n            </FormItem>\r\n            <FormItem label=\"附单据张数:\" prop=\"formAmount\">\r\n              <InputNumber\r\n                v-model=\"basicMes.formAmount\"\r\n                :step=\"1\"\r\n                :min=\"1\"\r\n                placeholder=\"请输入数量\"\r\n                :style=\"formItemWidth\"\r\n              />\r\n            </FormItem>\r\n            <FormItem label=\"报账期间:\" prop=\"budgetsetname\">\r\n              <cl-date-picker\r\n                type=\"month\"\r\n                format=\"yyyy-MM\"\r\n                placeholder=\"请选择报账期间\"\r\n                v-model=\"basicMes.budgetsetname\"\r\n                :style=\"formItemWidth\"\r\n              ></cl-date-picker>\r\n            </FormItem>\r\n            <FormItem label=\"费用发生日:\" prop=\"happenDate\">\r\n              <cl-date-picker\r\n                type=\"date\"\r\n                placeholder=\"请选择费用发生日\"\r\n                v-model=\"basicMes.happenDate\"\r\n                :style=\"formItemWidth\"\r\n              ></cl-date-picker>\r\n            </FormItem>\r\n            <FormItem label=\"收支方式:\" prop=\"paymentType\">\r\n              <Select v-model=\"basicMes.paymentType\" :style=\"formItemWidth\">\r\n                <Option\r\n                  v-for=\"item in categorys.paymentType\"\r\n                  :key=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                  :value=\"item.typeCode * 1\"\r\n                  >{{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"业务类型：\" prop=\"bizTypeCode\">\r\n              <Select v-model=\"basicMes.bizTypeCode\" :style=\"formItemWidth\">\r\n                <Option\r\n                  v-for=\"item in categorys.bizTypeCode\"\r\n                  :key=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                  :value=\"item.typeCode * 1\"\r\n                  >{{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"业务场景：\" prop=\"pickingMode\">\r\n              <Select\r\n                v-model=\"basicMes.pickingMode\"\r\n                :style=\"formItemWidth\"\r\n                @on-change=\"setbudgetTypeDefault\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in categorys.pickingMode\"\r\n                  :key=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                  :value=\"item.typeCode * 1\"\r\n                  >{{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n              <!--<cl-select :value=\"basicMes.pickingMode\" category=\"pickingMode\" labelField=\"typeName\"\r\n                                       valueField=\"typeCode\" style=\"width:160px;\"/>-->\r\n            </FormItem>\r\n            <FormItem label=\"票据类型：\" prop=\"invoiceType\">\r\n              <Select\r\n                v-model=\"basicMes.invoiceType\"\r\n                :style=\"formItemWidth\"\r\n                @on-change=\"invoiceTypeChange\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in categorys.invoiceType\"\r\n                  :key=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                  :value=\"item.typeCode * 1\"\r\n                  >{{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"转售金额(不含税价)：\"\r\n              prop=\"kindGiftSum\"\r\n              :rules=\"{\r\n                required: basicMes.isExistKindGift == '1',\r\n                message: '不能为空',\r\n                trigger: 'blur',\r\n              }\"\r\n            >\r\n              <Input\r\n                v-model=\"basicMes.kindGiftSum\"\r\n                :style=\"formItemWidth\"\r\n                @on-blur=\"kindGiftSumBlur\"\r\n                :disabled=\"basicMes.isExistKindGift == '0'\"\r\n              />\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"转售税额：\"\r\n              prop=\"kindGiftTaxSum\"\r\n              :rules=\"{\r\n                required: basicMes.isExistKindGift == '1',\r\n                message: '不能为空',\r\n                trigger: 'blur',\r\n              }\"\r\n            >\r\n              <Input\r\n                v-model=\"basicMes.kindGiftTaxSum\"\r\n                :style=\"formItemWidth\"\r\n                :disabled=\"basicMes.isExistKindGift == '0'\"\r\n              />\r\n              <!--:readonly=\"version=='sc'\"-->\r\n            </FormItem>\r\n            <!--<FormItem label=\"是否对外开具专票：\" prop=\"isGdtelInvoice\"\r\n                                  v-if=\"version=='sc' && basicMes.invoiceType == 1\">\r\n                            <RadioGroup v-model=\"basicMes.isGdtelInvoice\">\r\n                                <Radio label=\"0\">\r\n                                    <span>否</span>\r\n                                </Radio>\r\n                                <Radio label=\"1\">\r\n                                    <span>是</span>\r\n                                </Radio>\r\n                            </RadioGroup>\r\n                        </FormItem>-->\r\n            <FormItem label=\"是否员工代垫：\" prop=\"isStaffPayment\">\r\n              <RadioGroup v-model=\"basicMes.isStaffPayment\">\r\n                <Radio label=\"0\">\r\n                  <span>否</span>\r\n                </Radio>\r\n                <Radio label=\"1\">\r\n                  <span>是</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"业务发生时间:\" prop=\"busihappendtimeflag\">\r\n              <RadioGroup v-model=\"basicMes.busihappendtimeflag\">\r\n                <Radio label=\"1\">\r\n                  <Icon type=\"ios-calendar\" />\r\n                  <span>营改增日期前</span>\r\n                </Radio>\r\n                <Radio label=\"2\">\r\n                  <Icon type=\"ios-calendar-outline\" />\r\n                  <span>营改增日期后</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"是否转售水电：\" prop=\"isExistKindGift\">\r\n              <RadioGroup v-model=\"basicMes.isExistKindGift\">\r\n                <Radio label=\"0\" :disabled=\"version == 'sc'\">\r\n                  <span>否</span>\r\n                </Radio>\r\n                <Radio label=\"1\" :disabled=\"version == 'sc'\">\r\n                  <span>是</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"纳税属性：\" prop=\"paytaxattr\">\r\n              <RadioGroup v-model=\"basicMes.paytaxattr\">\r\n                <Radio label=\"1\">\r\n                  <span>属地纳税</span>\r\n                </Radio>\r\n                <Radio label=\"2\">\r\n                  <span>汇总纳税</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"是否加急：\" prop=\"isEmergency\">\r\n              <RadioGroup v-model=\"basicMes.isEmergency\">\r\n                <Radio label=\"0\" :disabled=\"version == 'sc'\">\r\n                  <span>否</span>\r\n                </Radio>\r\n                <Radio label=\"1\" :disabled=\"version == 'sc'\">\r\n                  <span>是</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"是否涉及进项税转出：\"\r\n              prop=\"isInputTax\"\r\n              v-if=\"basicMes.isExistKindGift == '0'\"\r\n            >\r\n              <RadioGroup v-model=\"basicMes.isInputTax\">\r\n                <Radio label=\"0\">\r\n                  <span>否</span>\r\n                </Radio>\r\n                <Radio label=\"1\">\r\n                  <span>是</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"进项税转出金额：\"\r\n              prop=\"inputTaxTurnSum\"\r\n              v-if=\"basicMes.isInputTax == 1 && basicMes.isExistKindGift == '0'\"\r\n            >\r\n              <InputNumber\r\n                v-model=\"basicMes.inputTaxTurnSum\"\r\n                :step=\"0.1\"\r\n                placeholder=\"请输入进项税转出金额\"\r\n                :style=\"formItemWidth\"\r\n              />\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"进项税转出业务类型：\"\r\n              prop=\"inputTaxTurnBizType\"\r\n              v-if=\"basicMes.isInputTax == 1 && basicMes.isExistKindGift == '0'\"\r\n            >\r\n              <Select v-model=\"basicMes.inputTaxTurnBizType\" :style=\"formItemWidth\">\r\n                <Option value=\"1\">免税项目</Option>\r\n                <Option value=\"2\">非免税项目</Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"说明：\" prop=\"abstractValue\">\r\n              <cl-input\r\n                v-model=\"basicMes.abstractValue\"\r\n                type=\"textarea\"\r\n                style=\"width: 400px\"\r\n              />\r\n            </FormItem>\r\n          </Form>\r\n        </div>\r\n      </Panel>\r\n      <Panel name=\"2\">\r\n        供应商信息\r\n        <div slot=\"content\">\r\n          <ChooseModal ref=\"chooseModalSup\" v-on:getDataFromModal=\"getDataFromModal\" />\r\n          <Button class=\"floatright\" type=\"success\" @click=\"handleChooseSup()\"\r\n            >选择供应商</Button\r\n          >\r\n          <Button class=\"floatright\" type=\"success\" @click=\"handleChooseMas()\"\r\n            >选择客户</Button\r\n          >\r\n          <!--<span style=\"color: blue;float: right;\" v-if=\"basicMes.billtype == 9 && version == 'sc'\">#预估报账请选择供应商【G951100081】(其他支付对象-成本结算业务预估)#</span>-->\r\n          <span\r\n            style=\"color: blue; float: right\"\r\n            v-if=\"basicMes.billtype == 8 && version == 'ln'\"\r\n            >#报账【收款】供应商不是必选项#</span\r\n          >\r\n          <Table\r\n            :show-page=\"false\"\r\n            :searchable=\"false\"\r\n            ref=\"testTable\"\r\n            :columns=\"supplier.columns\"\r\n            :data=\"supplier.data\"\r\n          ></Table>\r\n        </div>\r\n      </Panel>\r\n      <Panel name=\"Panel6\"\r\n        >发票信息\r\n        <div slot=\"content\">\r\n          <Row class=\"form-panel\">\r\n            <invoice-file\r\n              :param=\"invoiceParam\"\r\n              :attachData=\"invoiceData\"\r\n              v-on:setAttachData=\"setInvoiceData\"\r\n            />\r\n          </Row>\r\n        </div>\r\n      </Panel>\r\n      <Panel name=\"3\">\r\n        合同信息\r\n        <!--  <h3 slot=\"title\">合同信息</h3> -->\r\n        <div slot=\"content\">\r\n          <Form\r\n            ref=\"htMesForm\"\r\n            :model=\"basicMes\"\r\n            :rules=\"ruleValidateht\"\r\n            :label-width=\"140\"\r\n            inline\r\n          >\r\n            <FormItem label=\"合同编码：\" prop=\"contractno\">\r\n              <cl-input v-model=\"basicMes.contractno\" readonly />\r\n            </FormItem>\r\n            <FormItem label=\"合同名称：\" prop=\"contractName\">\r\n              <cl-input v-model=\"basicMes.contractName\" style=\"width: 300px\" readonly />\r\n            </FormItem>\r\n            <FormItem style=\"width: 100px\">\r\n              <Button type=\"success\" @click=\"handleChooseHt()\">选择合同</Button>\r\n            </FormItem>\r\n          </Form>\r\n        </div>\r\n      </Panel>\r\n      <Panel name=\"4\">\r\n        外部收款人信息\r\n        <div slot=\"content\">\r\n          <cl-table\r\n            ref=\"itemTable\"\r\n            v-if=\"itemTable.show\"\r\n            :url=\"itemTable.url\"\r\n            :searchable=\"false\"\r\n            :query-params=\"itemTable.query\"\r\n            :columns=\"itemTable.columns\"\r\n            select-enabled\r\n            :pageSize=\"5\"\r\n          ></cl-table>\r\n        </div>\r\n      </Panel>\r\n      <Panel v-if=\"attachShow\" name=\"Panel5\"\r\n        >附件信息\r\n        <div slot=\"content\">\r\n          <Row class=\"form-panel\">\r\n            <attach-file\r\n              :param=\"fileParam\"\r\n              :attachData=\"attachData\"\r\n              v-on:setAttachData=\"setAttachData\"\r\n            />\r\n          </Row>\r\n        </div>\r\n      </Panel>\r\n    </Collapse>\r\n  </div>\r\n</template>\r\n<script>\r\nimport config from \"@/config/index\";\r\nimport { ruleValidatebillBasic, widthstyle, numberRule } from \"./mssAccountbilldata\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport { deepClone } from \"@/libs/util\";\r\nimport ChooseModal from \"./chooseModal\";\r\nimport attachFile from \"@/view/basedata/protocol/attachFile\";\r\nimport invoiceFile from \"./invoiceFile\";\r\nimport axios from \"@/libs/api.request\";\r\nimport { attchList, removeAttach } from \"@/api/basedata/ammeter.js\";\r\nimport {\r\n  billViewById,\r\n  billEditById,\r\n  billEditSave,\r\n  billaddData,\r\n  getCategorys,\r\n  billaddDataLn,\r\n} from \"@/api/mssaccountbill/data\";\r\n\r\nexport default {\r\n  name: \"basicMes\",\r\n  components: { ChooseModal, attachFile,invoiceFile },\r\n  props: [\"tdlable\"],\r\n  data() {\r\n    let renderSup = (h, params) => {\r\n      var that = this;\r\n      var value = that.switchProp(params); //获取值\r\n      if (\"supplierCode\" == params.column.key || \"supplierName\" == params.column.key) {\r\n        return h(\"div\", [\r\n          h(\"Input\", {\r\n            props: {\r\n              value: value,\r\n              readonly: true,\r\n              placeholder: \"请选择供应商/客户\",\r\n            },\r\n            on: {\r\n              \"on-change\"(event) {\r\n                that.switchrowProp(params, event.target.value);\r\n                that.supplier.data[params.index] = params.row;\r\n                that.setSupplierTobill();\r\n              },\r\n            },\r\n          }),\r\n        ]);\r\n      } else {\r\n        return h(\"div\", [\r\n          h(\"InputNumber\", {\r\n            props: {\r\n              value: value,\r\n              step: 0.1,\r\n              // min: 0,\r\n              placeholder: \"请填写金额\",\r\n            },\r\n            on: {\r\n              \"on-change\"(value) {\r\n                that.switchrowProp(params, value);\r\n                that.supplier.data[params.index] = params.row;\r\n                that.setSupplierTobill();\r\n              },\r\n            },\r\n          }),\r\n        ]);\r\n      }\r\n    };\r\n    let renderSum = (h, params) => {\r\n      var that = this;\r\n      return h(\"div\", [\r\n        h(\"InputNumber\", {\r\n          props: {\r\n            value: params.row.sum,\r\n            step: 0.1,\r\n            // min: 0,\r\n            placeholder: \"请输入金额\",\r\n          },\r\n          on: {\r\n            \"on-change\"(value) {\r\n              params.row.sum = value;\r\n              that.itemTable.data[params.index] = params.row;\r\n            },\r\n          },\r\n        }),\r\n      ]);\r\n    };\r\n    return {\r\n      disableCompanyNameTxt: false,\r\n      companyNameList: [\"A006\", \"B006\"],\r\n      isExistKindGiftDisabled: false,\r\n      version: config.version,\r\n      accountCode: null,\r\n      formItemWidth: widthstyle,\r\n      billtypedisabled: false,\r\n      formValid: { basicMesForm: false, htMesForm: false },\r\n      collapsevalue: [\"1\", \"2\", \"3\", \"4\"],\r\n      basicMes: {\r\n        paymentType: null,\r\n        pickingMode: null,\r\n        invoiceType: null,\r\n        billtype: null,\r\n        fillInName: null,\r\n        fillInDep: null,\r\n        fillInCostCenterName: null,\r\n        busihappendtimeflag: null,\r\n        isGdtelInvoice: null,\r\n        telephone: null,\r\n        formAmount: null,\r\n        budgetsetname: null,\r\n        happenDate: null,\r\n        companyNameTxt: null,\r\n        bizTypeCode: null,\r\n        inputTaxTurnSum: null,\r\n        inputTaxTurnBizType: null,\r\n        isExistKindGift: null,\r\n        kindGiftTaxSum: null,\r\n        kindGiftSum: null,\r\n        isStaffPayment: null,\r\n        paytaxattr: null,\r\n        isEmergency: null,\r\n        isInputTax: null,\r\n        abstractValue: null,\r\n        contractno: null,\r\n        contractName: null,\r\n      },\r\n      categorys: {},\r\n      personType: null,\r\n      attachShow: false,\r\n      ifHasUnion: false,\r\n      supplier: {\r\n        loading: false,\r\n        columns: [\r\n          {\r\n            title: \"供应商/客户编码\",\r\n            key: \"supplierCode\",\r\n            render: renderSup,\r\n          },\r\n          {\r\n            title: \"供应商/客户名称\",\r\n            key: \"supplierName\",\r\n            render: renderSup,\r\n          },\r\n          {\r\n            title: \"是否关联方\",\r\n            key: \"ApproveMoney\",\r\n          },\r\n          {\r\n            title: \"一般纳税人\",\r\n            key: \"isRelease\",\r\n          },\r\n          {\r\n            title: \"报账金额（不含税价）\",\r\n            key: \"sum\",\r\n            render: renderSup,\r\n          },\r\n          {\r\n            title: \"税额\",\r\n            key: \"inputTaxSum\", //, render: renderSup\r\n          },\r\n        ],\r\n        data: [\r\n          {\r\n            supplierCode: null,\r\n            supplierName: null,\r\n            ApproveMoney: null,\r\n            isRelease: null,\r\n            sum: null,\r\n            inputTaxSum: null,\r\n            cellClassName: {\r\n              // supplierCode: \"demo-table-info-cell-sum\",\r\n              // supplierName: \"demo-table-info-cell-sum\",\r\n              // sum: 'demo-table-info-cell-inputTaxSum'\r\n              // ,inputTaxSum: 'demo-table-info-cell-inputTaxSum'\r\n            },\r\n          },\r\n        ],\r\n        total: 0,\r\n      },\r\n      ruleValidate: ruleValidatebillBasic,\r\n      ruleValidateht: {\r\n        /*contractno: [{required: true, message: \"不能为空\", trigger: \"blur\"}],\r\n                     contractName: [{required: true, message: \"不能为空\", trigger: \"blur\"}]*/\r\n      },\r\n      itemTable: { show: false, data: [] },\r\n      itemTable1: {\r\n        show: true,\r\n        data: [],\r\n        url: \"mssaccount/mssSupplierItem2/list\",\r\n        query: null,\r\n        columns: [\r\n          {\r\n            title: \"收款方名称\",\r\n            key: \"koinh\",\r\n          },\r\n          {\r\n            title: \"收款方类型\",\r\n            key: \"bvtyp\",\r\n          },\r\n          {\r\n            title: \"开户行\",\r\n            key: \"banka\",\r\n          },\r\n          {\r\n            title: \"银行账号\",\r\n            key: \"bankn\",\r\n          },\r\n          {\r\n            title: \"所在省\",\r\n            key: \"provz\",\r\n          },\r\n          {\r\n            title: \"所在市\",\r\n            key: \"ort01\",\r\n          },\r\n          {\r\n            title: \"所属银行\",\r\n            key: \"bvtyp\",\r\n          },\r\n          {\r\n            title: \"开户行行号\",\r\n            key: \"brnch\",\r\n          },\r\n          {\r\n            title: \"金额（含税）\",\r\n            key: \"sum\",\r\n            render: renderSum,\r\n          },\r\n        ],\r\n      },\r\n      fileParam: {\r\n        busiId: \"\",\r\n        busiAlias: \"附件(预提)\",\r\n        categoryCode: \"file\",\r\n        areaCode: \"ln\",\r\n      },\r\n      attachData: [],\r\n      invoiceParam: {\r\n        busiId: \"\",\r\n        busiAlias: \"附件(报账单发票)\",\r\n        categoryCode: \"file\",\r\n        areaCode: \"ln\",\r\n        invoiceFlag: \"1\"\r\n      },\r\n      invoiceData: [],\r\n      itemTable2: {\r\n        show: true,\r\n        data: [],\r\n        url: \"mssaccount/mssAbccustomerBank/list\",\r\n        query: null,\r\n        columns: [\r\n          {\r\n            title: \"客户编号\",\r\n            key: \"kunnr\",\r\n          },\r\n          {\r\n            title: \"收款方类型\",\r\n            key: \"koinh\",\r\n          },\r\n          {\r\n            title: \"开户行\",\r\n            key: \"zbanka\",\r\n          },\r\n          {\r\n            title: \"所在省\",\r\n            key: \"provz\",\r\n          },\r\n          {\r\n            title: \"所在市\",\r\n            key: \"city\",\r\n          },\r\n          {\r\n            title: \"银行账号\",\r\n            key: \"bankn\",\r\n          },\r\n          {\r\n            title: \"所属银行\",\r\n            key: \"bgrup\",\r\n          },\r\n          {\r\n            title: \"开户行行号\",\r\n            key: \"bankl\",\r\n          },\r\n          {\r\n            title: \"金额（含税）\",\r\n            key: \"sum\",\r\n            render: renderSum,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    // 强制更新文本框的值\r\n    changeMessage() {\r\n      this.$forceUpdate();\r\n    },\r\n    getFileorgCode() {\r\n      this.$emit(\"getFileorgCode\");\r\n    },\r\n    handleChooseSup(data) {\r\n      if (!data) {\r\n        if (this.basicMes.accountCode == \"********\") {\r\n          // 铁塔报账 只能 查询铁塔\r\n          this.$refs.chooseModalSup.modal1.queryparams.name1 = \"中国铁塔\";\r\n        }\r\n        this.$refs.chooseModalSup.choose(1); //打开模态框\r\n      } else {\r\n        //判断选择的供应商是否和已上传发票的供应商是否一致\r\n        if(this.invoiceData.length > 0){\r\n          if(data.name != this.invoiceData[0].invoiceSupplier){\r\n            this.$Modal.confirm({\r\n              title: '供应商确认选择', content: '<p class=\"warning\"> 当前选择的供应商与已上传发票的供应商不一致，是否确认选择该供应商</p>', onOk: () => {\r\n                this.basicMes.supplierCode = null;\r\n                this.basicMes.supplierName = null;\r\n                this.basicMes.supplierCode = data.id;\r\n                this.basicMes.supplierName = data.name;\r\n                this.supplier.data[0].supplierCode = data.id;\r\n                this.supplier.data[0].supplierName = data.name;\r\n                this.toShowPayee(); //触发改变外部收款人信息\r\n              }\r\n            });\r\n            return;\r\n          }\r\n        } \r\n        this.basicMes.supplierCode = null;\r\n        this.basicMes.supplierName = null;\r\n        this.basicMes.supplierCode = data.id;\r\n        this.basicMes.supplierName = data.name;\r\n        this.supplier.data[0].supplierCode = data.id;\r\n        this.supplier.data[0].supplierName = data.name;\r\n        this.toShowPayee(); //触发改变外部收款人信息\r\n      }\r\n    },\r\n    handleChooseMas(data) {\r\n      if (!data) {\r\n        this.$refs.chooseModalSup.choose(2); //打开模态框\r\n      } else {\r\n      }\r\n    },\r\n    handleChooseHt(data) {\r\n      if (!data) {\r\n        this.$refs.chooseModalSup.choose(3); //打开模态框\r\n      } else {\r\n        this.basicMes.contractno = data.id;\r\n        this.basicMes.contractName = data.name;\r\n      }\r\n    },\r\n    getDataFromModal(data, flag) {\r\n      if (flag == 1) {\r\n        this.personType = 1;\r\n        this.basicMes.suppliertype = \"1\";\r\n        this.handleChooseSup(data); // 传 true 设置 回调值\r\n      } else if (flag == 2) {\r\n        this.personType = 2;\r\n        this.basicMes.suppliertype = \"2\";\r\n        this.handleChooseSup(data); // 传 true 设置 回调值\r\n      } else if (flag == 3) {\r\n        this.handleChooseHt(data); // 传 true 设置 回调值\r\n      }\r\n    },\r\n    setTdlable() {\r\n      var type = this.basicMes.billtype;\r\n      this.$emit(\"setTdlable\", type);\r\n      if (type == 1) {\r\n        //选择“报销”时，【业务类型】固定为“列并付”，【业务场景】固定为“挂账并付款”-报销单，【收支方式】可选“集中支付”和“已支付-本地已主动支付”\r\n        this.setcategorysCase([2], [9], [2, 4, 5]);\r\n        this.basicMes.paymentType = 4; // 报销，预付，挂账支付，收付方式缺省都改为“集中支付”\r\n      } else if (type == 2) {\r\n        //选择“挂账”时，【业务类型】固定“列账”，【业务场景】固定为“挂账不付款（含预列）”-挂账单，【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [7], [8]);\r\n      } else if (type == 3) {\r\n        //选择“挂账支付”时，【业务类型】固定为“付款”，【业务场景】固定为“挂账后付款（清偿应付款）” ，【收支方式】可选“集中支付”和“已支付-本地已主动支付”\r\n        this.setcategorysCase([1], [2], [2, 4, 5]);\r\n        this.basicMes.paymentType = 4; // 报销，预付，挂账支付，收付方式缺省都改为“集中支付”\r\n      } else if (type == 4) {\r\n        //选择“预付”时，【业务类型】固定为“付款”，【业务场景】固定为“预付款” ，【收支方式】可选“集中支付”和“已支付-本地已主动支付”，【票据类型】固定为“无发票”\r\n        this.setcategorysCase([1], [8], [2, 4, 5]);\r\n        this.basicMes.paymentType = 4; // 报销，预付，挂账支付，收付方式缺省都改为“集中支付”\r\n        //【票据类型】固定为“无发票”\r\n        this.setCategorys(this.categorys.invoiceType, [9]);\r\n        this.basicMes.invoiceType = 9;\r\n      } else if (type == 5) {\r\n        //选择“预付冲销”时，【业务类型】固定为“列账”，【业务场景】固定为“冲前期借款(预付款)” ，【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [1], [8]);\r\n      } else if (type == 7) {\r\n        //选择“前期预付冲销”时，【业务类型】固定为“列账”，【业务场景】固定为“冲前期借款(预付款)” ，【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [1], [8]);\r\n      } else if (type == 10) {\r\n        //选择“预估冲销”时，【业务类型】固定为“列账”，【业务场景】固定为“冲预列(冲暂估)” ，【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [3], [8]);\r\n      } else if (type == 9) {\r\n        //选择 “预估”时，【业务类型】固定为“列账”，【业务场景】固定为“挂账不付款(含预列)”。【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [7], [8]);\r\n      } else if (type == 8) {\r\n        //选择 “收款”时，支付方式只有“集中收款”或“本地收款”\r\n        this.clearCategorys(this.categorys.invoiceType); // 不控制\r\n        if (this.version == \"sc\") {\r\n          /*if (ifHasUnion)\r\n                        this.setcategorysCase([0], [6], [6, 7]);\r\n                       else*/\r\n          this.setcategorysCase([0], [4, 6], [6, 7]);\r\n        } else {\r\n          this.setcategorysCase([0], [6], [6, 7]);\r\n          this.basicMes.invoiceType = 9;\r\n          if (this.supplier.data[0].supplierCode == null) {\r\n            this.supplier.data[0].supplierCode = \"9221000000\";\r\n            this.supplier.data[0].supplierName = \"收款-其他\";\r\n          }\r\n        }\r\n      } else if (type == 11) {\r\n        //调账：支付方式为“不涉及银行收付”，业务类型为\"列账”，业务场景为“往来转销”，\r\n        // 明细列账属性：“不使用成本预算”\r\n        this.setcategorysCase([0], [5], [8]);\r\n      } else {\r\n        this.clearCategorys(this.categorys.bizTypeCode);\r\n        this.clearCategorys(this.categorys.pickingMode);\r\n        this.clearCategorys(this.categorys.paymentType);\r\n        this.clearCategorys(this.categorys.invoiceType);\r\n        this.basicMes.bizTypeCode = null;\r\n        this.basicMes.pickingMode = null;\r\n        this.basicMes.paymentType = null;\r\n        this.basicMes.invoiceType = null;\r\n      }\r\n      // this.$nextTick(()=>{\r\n      //     this.$refs[\"basicMesForm\"].clearValidate(['billtype']);\r\n      // })\r\n\r\n      // this.$refs.basicMesForm.resetFields();\r\n    },\r\n    setcategorysCase(falg1, flag2, flag3) {\r\n      this.setCategorys(this.categorys.bizTypeCode, falg1);\r\n      this.basicMes.bizTypeCode = falg1[0]; //设置 业务类型 默认选择\r\n      this.setCategorys(this.categorys.pickingMode, flag2);\r\n      this.basicMes.pickingMode = flag2[0]; //设置 业务场景 默认选择\r\n      this.setCategorys(this.categorys.paymentType, flag3);\r\n      this.basicMes.paymentType = flag3[0]; //设置 收支方式 默认选择\r\n      ////\r\n      this.clearCategorys(this.categorys.invoiceType);\r\n      // this.basicMes.invoiceType = null;\r\n    },\r\n    setCategorys(data, flags) {\r\n      for (let item of data) {\r\n        // if (flags.indexOf(item.typeCode) > -1) {\r\n        if (flags.indexOf(Number(item.typeCode)) > -1) {\r\n          item.deletedFlag = 0;\r\n        } else {\r\n          item.deletedFlag = 1;\r\n        }\r\n      }\r\n    },\r\n    //设置字典值:自有报账单不要预提\r\n    setDictsOpts(key, val) {\r\n      this.categorys[key] = this.categorys[key].filter((d) => d.typeCode != val);\r\n    },\r\n    clearCategorys(data) {\r\n      for (let item of data) {\r\n        item.deletedFlag = 0;\r\n      }\r\n    },\r\n    valibasicMesForm(formname) {\r\n      this.$refs[formname].validate((valid) => {\r\n        this.formValid[formname] = valid;\r\n      });\r\n    },\r\n    setSupplierTobill() {\r\n      this.basicMes.supplierCode = this.supplier.data[0].supplierCode;\r\n      this.basicMes.supplierName = this.supplier.data[0].supplierName;\r\n      this.basicMes.sum = this.supplier.data[0].sum;\r\n      this.basicMes.inputTaxSum = this.supplier.data[0].inputTaxSum;\r\n      if (this.basicMes.inputTaxSum != null) {\r\n        this.$emit(\"setInputTaxSum\", this.basicMes.inputTaxSum);\r\n      }\r\n    },\r\n    switchProp(params) {\r\n      var value = null;\r\n      switch (params.column.key) {\r\n        case \"sum\":\r\n          value = params.row.sum;\r\n          break;\r\n        case \"inputTaxSum\":\r\n          value = params.row.inputTaxSum;\r\n          break;\r\n        case \"supplierCode\":\r\n          value = params.row.supplierCode;\r\n          break;\r\n        case \"supplierName\":\r\n          value = params.row.supplierName;\r\n          break;\r\n      }\r\n      return value;\r\n    },\r\n    switchrowProp(params, value) {\r\n      switch (params.column.key) {\r\n        case \"sum\":\r\n          params.row.sum = value;\r\n          break;\r\n        case \"inputTaxSum\":\r\n          params.row.inputTaxSum = value;\r\n          break;\r\n        case \"supplierCode\":\r\n          params.row.supplierCode = value;\r\n          break;\r\n        case \"supplierName\":\r\n          params.row.supplierName = value;\r\n          break;\r\n      }\r\n    },\r\n    validHandel() {\r\n      var mes = \"\";\r\n      var supplierCode = this.supplier.data[0].supplierCode;\r\n      var inputTaxSum = this.supplier.data[0].inputTaxSum;\r\n      var sum = this.supplier.data[0].sum;\r\n      if (this.basicMes.isStaffPayment == 0 && supplierCode == null) {\r\n        mes += \"请选择供应商/客户\";\r\n      } else if (sum == null) {\r\n        this.supplier.data[0].cellClassName.sum = \"demo-table-error\";\r\n        mes += \"供应商/客户 报账金额（不含税价）不能为空\";\r\n      } else if (sum && !numberRule.test(sum)) {\r\n        this.supplier.data[0].cellClassName.sum = \"demo-table-error\";\r\n        mes += \"供应商/客户 报账金额（不含税价）输入有误\";\r\n      } else if (inputTaxSum == null) {\r\n        this.supplier.data[0].inputTaxSum = 0;\r\n        // this.supplier.data[0].cellClassName.inputTaxSum = 'demo-table-error';\r\n        // mes += \"供应商/客户 税额 不能为空\";\r\n      } else if (inputTaxSum && !numberRule.test(inputTaxSum)) {\r\n        this.supplier.data[0].cellClassName.inputTaxSum = \"demo-table-error\";\r\n        mes += \"供应商/客户 税额 输入有误\";\r\n      }\r\n      if (sum && numberRule.test(sum)) {\r\n        this.supplier.data[0].cellClassName.sum = \"demo-table-info-cell-inputTaxSum\";\r\n      }\r\n      if (inputTaxSum && numberRule.test(inputTaxSum)) {\r\n        this.supplier.data[0].cellClassName.inputTaxSum =\r\n          \"demo-table-info-cell-inputTaxSum\";\r\n      }\r\n      return mes;\r\n    },\r\n    validItemTable() {\r\n      if (this.itemTable.data.length == 0) {\r\n        // 外部收款人 可为空\r\n        return true;\r\n      }\r\n      let _itemTable = [];\r\n      let flag = false;\r\n      for (var i = 0; i < this.itemTable.data.length; i++) {\r\n        var ik = this.itemTable.data[i];\r\n        if (ik) {\r\n          var value = ik.sum;\r\n          if (value && numberRule.test(value)) {\r\n            _itemTable.push(this.itemTable.data[i]);\r\n            flag = true;\r\n          }\r\n        }\r\n      }\r\n      if (!flag) {\r\n        this.collapsevalue = [\"4\"];\r\n        this.$Message.error(\"至少填写一项（含税）金额,或者金额有误\");\r\n      } else {\r\n        if (this.personType == 1) {\r\n          this.basicMes.supplierItem2 = _itemTable;\r\n        } else {\r\n          this.basicMes.customerBank = _itemTable;\r\n        }\r\n      }\r\n      return flag;\r\n    },\r\n    invoiceTypeChange() {\r\n      // debugger\r\n      // if (this.version == 'sc') {\r\n      // 当票据类型选择“增值税专用发票”时，带出“是否对外开具专票”选项，默认选择是，\r\n      // 控制“转售金额(含税价)”和“转售税额”必填。如果选择否，此两项改为非必填。\r\n      // if (this.basicMes.invoiceType == 1) {\r\n      //     this.basicMes.isGdtelInvoice = \"1\";\r\n      // } else {\r\n      //     this.basicMes.isGdtelInvoice = \"0\";\r\n      // }\r\n      //  票据类型选“增值税专票”，明细有代垫外单位电费，“是否转售水电”默认为“是”\r\n      //     this.$emit(\"setTdlable\", this.basicMes.billtype);\r\n      // }\r\n      // else {\r\n      //     this.basicMes.invoiceType\r\n      // }\r\n    },\r\n    kindGiftSumBlur() {\r\n      if (this.version == \"sc\") {\r\n        // 转售税额根据用户填写的转售金额(含税价)自动计算，计算规则：\r\n        // 转售税额=转售金额(含税价)*0.13/1.13\r\n        if (numberRule.test(this.basicMes.kindGiftSum)) {\r\n          ////和“转售税额”金额不再自动计算，由用户自己填写\r\n          // let sum = parseFloat(this.basicMes.kindGiftSum);\r\n          // this.basicMes.kindGiftTaxSum = (sum * 0.13 / 1.13).toFixed(2);\r\n        } else {\r\n          this.basicMes.kindGiftSum = null;\r\n          this.basicMes.kindGiftTaxSum = null;\r\n          this.$Message.info(\"转售金额(含税价)--请输入数字\");\r\n        }\r\n      }\r\n    },\r\n    setbudgetTypeDefault() {\r\n      this.$emit(\"setbudgetTypeDefault\");\r\n    },\r\n    setAttachData(data) {\r\n      this.multiFiles = data.data;\r\n      this.removeIds = data.ids;\r\n      if (this.removeIds.length != 0 && data.type == \"remove\") {\r\n        this.removeAttach();\r\n      } else {\r\n        this.upload();\r\n      }\r\n    },\r\n    upload() {\r\n      if (this.attachData.length != 0 && this.multiFiles.length != 0) {\r\n        // this.$Message.info(\"提示:上传文件过大可能导致上传失败！\");\r\n        this.loading = true;\r\n        axios\r\n          .request({\r\n            url: \"/common/attachments/uploadMultiFile\",\r\n            method: \"post\",\r\n            data: this.multiFiles,\r\n          })\r\n          .then((res) => {\r\n            if (res.data.code != 0) {\r\n              this.loading = false;\r\n            }\r\n            let that = this;\r\n            attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n              that.attachData = Object.assign([], res.data.rows);\r\n            });\r\n          });\r\n      }\r\n    },\r\n    removeAttach() {\r\n      removeAttach({ ids: this.removeIds.join() }).then(() => {});\r\n    },\r\n    setInvoiceData(data) {\r\n      if(data.type == 'upload'){\r\n        //上传的发票的供应商在能耗系统是否存在 1 存在 0 不存在\r\n        if(data.supplierExist == '1'){\r\n          //自动填充供应商信息\r\n          if(this.basicMes.supplierCode != data.supplierCode && this.basicMes.supplierName != data.supplierName){\r\n            if(this.basicMes.supplierCode && this.basicMes.supplierName){\r\n              //当已选择供应商时,提示是否需要替换\r\n              this.$Modal.confirm({\r\n                title: '替换供应商', content: '<p class=\"warning\"> 当前选择的供应商与上传发票的供应商不一致，是否替换供应商</p>', onOk: () => {\r\n                  this.basicMes.supplierCode = data.supplierCode;\r\n                  this.basicMes.supplierName = data.supplierName;\r\n                  this.supplier.data[0].supplierCode = data.supplierCode;\r\n                  this.supplier.data[0].supplierName = data.supplierName;\r\n                  this.personType = 1;\r\n                  this.toShowPayee(); //触发改变外部收款人信息\r\n                }\r\n              });\r\n            } else{\r\n              //当未选择供应商时，直接填充\r\n              this.basicMes.supplierCode = data.supplierCode;\r\n              this.basicMes.supplierName = data.supplierName;\r\n              this.supplier.data[0].supplierCode = data.supplierCode;\r\n              this.supplier.data[0].supplierName = data.supplierName;\r\n              this.personType = 1;\r\n              this.toShowPayee(); //触发改变外部收款人信息\r\n            }\r\n          }\r\n        } else {\r\n          //提示发票供应商信息不存在\r\n          this.$Message.warning(`上传的发票的供应商信息不存在`);\r\n        }\r\n      }\r\n      attchList({ busiId: this.invoiceParam.busiId, invoiceFlag: this.invoiceParam.invoiceFlag }).then((res) => {\r\n        this.invoiceData = Object.assign([], res.data.rows);\r\n      });\r\n    },\r\n    //选择供应商/供应商有值->改变外部收款人信息\r\n    toShowPayee() {\r\n      this.itemTable.query = null;\r\n      if (this.basicMes.supplierCode != null) {\r\n        if (this.personType == 1) {\r\n          this.itemTable1.query = { lifnr: this.basicMes.supplierCode };\r\n          this.itemTable = deepClone(this.itemTable1);\r\n        } else {\r\n          this.itemTable2.query = { kunnr: this.basicMes.supplierCode };\r\n          this.itemTable = deepClone(this.itemTable2);\r\n        }\r\n      }\r\n    },\r\n  },\r\n  watch: {\r\n    basicMes: {\r\n      deep: true, //深度监听\r\n      handler(val, oldVal) {\r\n        this.toShowPayee(); //触发改变外部收款人信息\r\n        if (this.version == \"sc\") {\r\n          // 四川\r\n          //报账单业务场景为“挂账并付款”时，票据类型不能选择到“无发票”选项。\r\n          if (val.pickingMode == 9) {\r\n            for (let item of this.categorys.invoiceType) {\r\n              if (item.typeCode == 9) {\r\n                item.deletedFlag = 1;\r\n              } else {\r\n                item.deletedFlag = 0;\r\n              }\r\n            }\r\n          } else {\r\n            for (let item of this.categorys.invoiceType) {\r\n              if (item.typeCode == 9) {\r\n                item.deletedFlag = 0;\r\n              }\r\n            }\r\n          }\r\n          //收支方式选择“集中支付”时，是否加急固化为“是”，不可修改；\r\n          // 选择其他收支方式时，是否加急固化为“否”，不可修改\r\n          if (val.paymentType == 4) {\r\n            //集中支付\r\n            val.isEmergency = \"1\"; //是\r\n          } else {\r\n            val.isEmergency = \"0\"; //否\r\n          }\r\n        } else {\r\n          if (val.isExistKindGift == \"0\") {\r\n            val.inputTaxTurnSum = null;\r\n            val.inputTaxTurnBizType = null;\r\n          }\r\n        }\r\n      },\r\n    },\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.$refs.basicMesForm.resetFields(); // this.$refs.adduserform.resetFields();\r\n      // this.$refs.chooseModalSup.resetFields();       // this.$refs.adduserform.resetFields();\r\n      this.$refs.htMesForm.resetFields();\r\n      if (this.itemTable.show) {\r\n        this.$refs.itemTable.resetFields(); // this.$refs.adduserform.resetFields();\r\n      }\r\n    });\r\n\r\n    if (this.version == \"ln\")\r\n      billaddDataLn(this.$route.query.data).then((res) => {\r\n        // this.basicMes = res.data;\r\n        let data = res.data || {};\r\n        Object.assign(this.basicMes, data);\r\n        // debugger\r\n        this.basicMes.formAmount = 1;\r\n        this.basicMes.isExistKindGift = \"0\";\r\n        this.basicMes.accountCode = this.accountCode;\r\n        if (res.data.companyNameTxt) {\r\n          this.disableCompanyNameTxt = true;\r\n        } else {\r\n          this.disableCompanyNameTxt = false;\r\n        }\r\n        this.$emit(\"setBillId\", this.basicMes.id);\r\n        this.fileParam.busiId = this.basicMes.id;\r\n        this.invoiceParam.busiId = this.basicMes.id;\r\n        if (this.version == \"ln\") this.attachShow = true;\r\n        // this.basicMes.billtype=parseInt(this.basicMes.billtype);\r\n        // this.basicMes.invoiceType = parseInt(this.basicMes.invoiceType);\r\n        // this.basicMes.bizTypeCode = parseInt(this.basicMes.bizTypeCode);\r\n        // this.basicMes.pickingMode = parseInt(this.basicMes.pickingMode);\r\n        // this.basicMes.isStaffPayment = parseInt(this.basicMes.isStaffPayment);\r\n        //\r\n        if (this.version == \"ln\" && this.basicMes.accountCode == \"********\") {\r\n          //\r\n          // 辽宁那边要求控制下自有报账单那边屏蔽调报账单预提，预提冲销的选项\r\n          // 只有铁塔报账单才能选预提，预提冲销\r\n          //自有的预提屏蔽，自有预提冲销开着，这边从8月份开始自有不允许走预提单\r\n          this.setCategorys(this.categorys.billtype, [1, 2, 3, 4, 5, 6, 7, 8, 10, 11]);\r\n        }\r\n      });\r\n    else\r\n      billaddData().then((res) => {\r\n        // this.basicMes = res.data;\r\n        let data = res.data || {};\r\n        Object.assign(this.basicMes, data);\r\n        this.basicMes.accountCode = this.accountCode;\r\n        this.$emit(\"setBillId\", this.basicMes.id);\r\n        this.fileParam.busiId = this.basicMes.id;\r\n        this.invoiceParam.busiId = this.basicMes.id;\r\n        if (this.version == \"ln\") this.attachShow = true;\r\n        // this.basicMes.billtype=parseInt(this.basicMes.billtype);\r\n        // this.basicMes.invoiceType = parseInt(this.basicMes.invoiceType);\r\n        // this.basicMes.bizTypeCode = parseInt(this.basicMes.bizTypeCode);\r\n        // this.basicMes.pickingMode = parseInt(this.basicMes.pickingMode);\r\n        // this.basicMes.isStaffPayment = parseInt(this.basicMes.isStaffPayment);\r\n        //\r\n        if (this.version == \"ln\" && this.basicMes.accountCode == \"********\") {\r\n          //\r\n          // 辽宁那边要求控制下自有报账单那边屏蔽调报账单预提，预提冲销的选项\r\n          // 只有铁塔报账单才能选预提，预提冲销\r\n          //自有的预提屏蔽，自有预提冲销开着，这边从8月份开始自有不允许走预提单\r\n          this.setCategorys(this.categorys.billtype, [1, 2, 3, 4, 5, 6, 7, 8, 10, 11]);\r\n        }\r\n      });\r\n  },\r\n  created() {\r\n    // getCategorys().then(res => {\r\n    //   this.categorys = res.data.categorys;\r\n    // });\r\n    //直接从前台取\r\n    /*        if (this.$route.query.data)\r\n          console.log(this.$route.query.data);*/\r\n    this.categorys = {\r\n      billtype: blist(\"billtype\"),\r\n      paymentType: blist(\"paymentType\"),\r\n      invoiceType: blist(\"invoiceType\"),\r\n      bizTypeCode: blist(\"bizType\"),\r\n      pickingMode: blist(\"pickingMode\"),\r\n      isStaffPayment: blist(\"isStaffPayment\"),\r\n    };\r\n  },\r\n};\r\n</script>\r\n"]}]}