{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\eslint-loader\\index.js??ref--14-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\quota\\listQuota.vue", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\quota\\listQuota.vue", "mtime": 1754285403021}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\eslint-loader\\index.js", "mtime": 1753757453575}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfSBmcm9tICIuL2xpc3RRdW90YS52dWU/dnVlJnR5cGU9dGVtcGxhdGUmaWQ9MmQzNzMwMzAmIgppbXBvcnQgc2NyaXB0IGZyb20gIi4vbGlzdFF1b3RhLnZ1ZT92dWUmdHlwZT1zY3JpcHQmbGFuZz1qcyYiCmV4cG9ydCAqIGZyb20gIi4vbGlzdFF1b3RhLnZ1ZT92dWUmdHlwZT1zY3JpcHQmbGFuZz1qcyYiCmltcG9ydCBzdHlsZTAgZnJvbSAiLi9saXN0UXVvdGEudnVlP3Z1ZSZ0eXBlPXN0eWxlJmluZGV4PTAmaWQ9MmQzNzMwMzAmbGFuZz1sZXNzJiIKCgovKiBub3JtYWxpemUgY29tcG9uZW50ICovCmltcG9ydCBub3JtYWxpemVyIGZyb20gIiEuLi8uLi8uLi8uLi9ub2RlX21vZHVsZXMvdnVlLWxvYWRlci9saWIvcnVudGltZS9jb21wb25lbnROb3JtYWxpemVyLmpzIgp2YXIgY29tcG9uZW50ID0gbm9ybWFsaXplcigKICBzY3JpcHQsCiAgcmVuZGVyLAogIHN0YXRpY1JlbmRlckZucywKICBmYWxzZSwKICBudWxsLAogIG51bGwsCiAgbnVsbAogIAopCgovKiBob3QgcmVsb2FkICovCmlmIChtb2R1bGUuaG90KSB7CiAgdmFyIGFwaSA9IHJlcXVpcmUoIkU6XFxjbC1wcm9qZWN0XFxsbi1uZW5naGFvXFxicmNoLWxuLXZ1ZVxcbm9kZV9tb2R1bGVzXFx2dWUtaG90LXJlbG9hZC1hcGlcXGRpc3RcXGluZGV4LmpzIikKICBhcGkuaW5zdGFsbChyZXF1aXJlKCd2dWUnKSkKICBpZiAoYXBpLmNvbXBhdGlibGUpIHsKICAgIG1vZHVsZS5ob3QuYWNjZXB0KCkKICAgIGlmICghYXBpLmlzUmVjb3JkZWQoJzJkMzczMDMwJykpIHsKICAgICAgYXBpLmNyZWF0ZVJlY29yZCgnMmQzNzMwMzAnLCBjb21wb25lbnQub3B0aW9ucykKICAgIH0gZWxzZSB7CiAgICAgIGFwaS5yZWxvYWQoJzJkMzczMDMwJywgY29tcG9uZW50Lm9wdGlvbnMpCiAgICB9CiAgICBtb2R1bGUuaG90LmFjY2VwdCgiLi9saXN0UXVvdGEudnVlP3Z1ZSZ0eXBlPXRlbXBsYXRlJmlkPTJkMzczMDMwJiIsIGZ1bmN0aW9uICgpIHsKICAgICAgYXBpLnJlcmVuZGVyKCcyZDM3MzAzMCcsIHsKICAgICAgICByZW5kZXI6IHJlbmRlciwKICAgICAgICBzdGF0aWNSZW5kZXJGbnM6IHN0YXRpY1JlbmRlckZucwogICAgICB9KQogICAgfSkKICB9Cn0KY29tcG9uZW50Lm9wdGlvbnMuX19maWxlID0gInNyYy92aWV3L2Jhc2VkYXRhL3F1b3RhL2xpc3RRdW90YS52dWUiCmV4cG9ydCBkZWZhdWx0IGNvbXBvbmVudC5leHBvcnRz"}]}