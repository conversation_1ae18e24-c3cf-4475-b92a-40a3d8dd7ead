{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\warnAnalysis.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\warnAnalysis.vue", "mtime": 1754285403030}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": 1741317456427}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXM2LnJlZ2V4cC50by1zdHJpbmciOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lczYuZnVuY3Rpb24ubmFtZSI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzNi5hcnJheS5zb3J0IjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KaW1wb3J0ICJlY2hhcnRzLWdsIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJjaXR5R3JlZW5MYW5kIiwKICBjb21wb25lbnRzOiB7fSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZWxlY3RyaWN0VG90YWw6ICIiLAogICAgICBpc0Z1bGxzY3JlZW46IGZhbHNlLAogICAgICBvcHRpb25EYXRhOiBbewogICAgICAgIG5hbWU6ICLogIPmoLjmiJDnu6npq5jkuo7pooTorablgLwiLAogICAgICAgIHZhbHVlOiAyMjExNgogICAgICB9LCB7CiAgICAgICAgbmFtZTogIuiAg+aguOaIkOe7qeS9juS6jumihOitpuWAvCIsCiAgICAgICAgdmFsdWU6IDE2NjE2CiAgICAgIH1dLAogICAgICBub3dTdGVwOiAwLAogICAgICBtYXhTdGVwOiAzNSwKICAgICAgaGVpZ2h0OiA5NQogICAgfTsKICB9LAogIHByb3BzOiB7CiAgICBlbGVjdHJpY1N0cmN1Y3RPYmo6IHsKICAgICAgdHlwZTogT2JqZWN0CiAgICB9LAogICAgd2FyblZhbHVlOiB7CiAgICAgIHR5cGU6IFN0cmluZwogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIGVsZWN0cmljU3RyY3VjdE9iajogewogICAgICBpbW1lZGlhdGU6IHRydWUsCiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsKICAgICAgICB2YXIgX3RoaXMyID0gdGhpczsKCiAgICAgICAgaWYgKG5ld1ZhbCA9PSB1bmRlZmluZWQgfHwgbmV3VmFsID09IG51bGwgfHwgbmV3VmFsID09ICIiKSByZXR1cm47IC8vIOaAu+aVsOmHjwoKICAgICAgICB0aGlzLmVsZWN0cmljdFRvdGFsID0gbmV3VmFsLmVsZWN0cmljdFRvdGFsOyAvLyAvLyDlpJbotK3nu7/nlLUKICAgICAgICAvLyB0aGlzLiRzZXQodGhpcy5vcHRpb25EYXRhWzBdLCAidmFsdWUiLCBuZXdWYWwub3V0c291cmNpbmdHcmVlblBvd2VyKTsKICAgICAgICAvLyDlpJbotK3ngavnlLUKCiAgICAgICAgdGhpcy4kc2V0KHRoaXMub3B0aW9uRGF0YVswXSwgInZhbHVlIiwgbmV3VmFsLnRoYW5XYXJuTnVtKTsKICAgICAgICB0aGlzLiRzZXQodGhpcy5vcHRpb25EYXRhWzFdLCAidmFsdWUiLCBuZXdWYWwubG93V2Fybk51bSk7CiAgICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXMyLmluaXQoKTsKICAgICAgICB9KTsKICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZSAvLyDmt7Hluqbnm5HlkKwKCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBpbml0OiBmdW5jdGlvbiBpbml0KCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOyAvL+aehOW7ujNk6aW854q25Zu+CgoKICAgICAgdmFyIG15Q2hhcnQgPSB0aGlzLiRlY2hhcnRzLmluaXQoZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoInN0cnVjdHVyZTNELWNoYXJ0cyIpKTsgLy8g5Lyg5YWl5pWw5o2u55Sf5oiQIG9wdGlvbgoKICAgICAgdGhpcy5vcHRpb24gPSB0aGlzLmdldFBpZTNEKF90aGlzLm9wdGlvbkRhdGEsIDAuNzUpOwogICAgICBteUNoYXJ0LnNldE9wdGlvbih0aGlzLm9wdGlvbik7IC8v5piv5ZCm6ZyA6KaBbGFiZWzmjIflvJXnur/vvIzlpoLmnpzopoHlsLHmt7vliqDkuIDkuKrpgI/mmI7nmoQyZOmlvOeKtuWbvuW5tuiwg+aVtOinkuW6puS9v+W+l2xhYmVsTGluZeWSjDNk55qE6aW854q25Zu+5a+56b2Q77yM5bm25YaN5qyhc2V0T3B0aW9uCgogICAgICB0aGlzLm9wdGlvbi5zZXJpZXMucHVzaCh7CiAgICAgICAgbmFtZTogInBpZTJkIiwKICAgICAgICB0eXBlOiAicGllIiwKICAgICAgICBsYWJlbDogewogICAgICAgICAgbm9ybWFsOiB7CiAgICAgICAgICAgIHBvc2l0aW9uOiAiaW5uZXIiLAogICAgICAgICAgICBzaG93OiBmYWxzZQogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgbGFiZWxMaW5lOiB7CiAgICAgICAgICBzaG93OiBmYWxzZSwKICAgICAgICAgIGxlbmd0aDogMTUsCiAgICAgICAgICBsZW5ndGgyOiA0MAogICAgICAgIH0sCiAgICAgICAgc3RhcnRBbmdsZTogMCwKICAgICAgICAvL+i1t+Wni+inkuW6pu+8jOaUr+aMgeiMg+WbtFswLCAzNjBd44CCCiAgICAgICAgY2xvY2t3aXNlOiBmYWxzZSwKICAgICAgICAvL+mlvOWbvueahOaJh+WMuuaYr+WQpuaYr+mhuuaXtumSiOaOkuW4g+OAguS4iui/sOi/meS4pOmhuemFjee9ruS4u+imgeaYr+S4uuS6huWvuem9kDNk55qE5qC35byPCiAgICAgICAgcmFkaXVzOiBbIjc4JSIsICI3NCUiXSwKICAgICAgICBjZW50ZXI6IFsiNTAlIiwgIjUwJSJdLAogICAgICAgIGRhdGE6IF90aGlzLm9wdGlvbkRhdGEsCiAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICBvcGFjaXR5OiAwCiAgICAgICAgfQogICAgICB9KTsKICAgICAgbXlDaGFydC5zZXRPcHRpb24odGhpcy5vcHRpb24pOwogICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigicmVzaXplIiwgZnVuY3Rpb24gKCkgewogICAgICAgIG15Q2hhcnQucmVzaXplKCk7CiAgICAgIH0pOyAvLyB0aGlzLmJpbmRMaXN0ZW4obXlDaGFydCk7CiAgICB9LAogICAgZ2V0UGllM0Q6IGZ1bmN0aW9uIGdldFBpZTNEKHBpZURhdGEsIGludGVybmFsRGlhbWV0ZXJSYXRpbykgewogICAgICAvL2ludGVybmFsRGlhbWV0ZXJSYXRpbzrpgI/mmI7nmoTnqbrlv4PljaDmr5QKICAgICAgdmFyIHRoYXQgPSB0aGlzOwogICAgICB2YXIgc2VyaWVzID0gW107CiAgICAgIHZhciBzdW1WYWx1ZSA9IDE7CiAgICAgIHZhciBzdGFydFZhbHVlID0gMTsKICAgICAgdmFyIGVuZFZhbHVlID0gMTsKICAgICAgdmFyIGxlZ2VuZERhdGEgPSBbXTsKICAgICAgdmFyIGxlZ2VuZEJmYiA9IFtdOwogICAgICB2YXIgayA9IDEgLSBpbnRlcm5hbERpYW1ldGVyUmF0aW87CiAgICAgIHBpZURhdGEuc29ydChmdW5jdGlvbiAoYSwgYikgewogICAgICAgIHJldHVybiBiLnZhbHVlIC0gYS52YWx1ZTsKICAgICAgfSk7IC8vIOS4uuavj+S4gOS4qumlvOWbvuaVsOaNru+8jOeUn+aIkOS4gOS4qiBzZXJpZXMtc3VyZmFjZSDphY3nva4KCiAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgcGllRGF0YS5sZW5ndGg7IGkrKykgewogICAgICAgIHN1bVZhbHVlICs9IHBpZURhdGFbaV0udmFsdWU7CiAgICAgICAgdmFyIHNlcmllc0l0ZW0gPSB7CiAgICAgICAgICBuYW1lOiB0eXBlb2YgcGllRGF0YVtpXS5uYW1lID09PSAidW5kZWZpbmVkIiA/ICJzZXJpZXMiLmNvbmNhdChpKSA6IHBpZURhdGFbaV0ubmFtZSwKICAgICAgICAgIHR5cGU6ICJzdXJmYWNlIiwKICAgICAgICAgIHBhcmFtZXRyaWM6IHRydWUsCiAgICAgICAgICB3aXJlZnJhbWU6IHsKICAgICAgICAgICAgc2hvdzogZmFsc2UKICAgICAgICAgIH0sCiAgICAgICAgICBwaWVEYXRhOiBwaWVEYXRhW2ldLAogICAgICAgICAgcGllU3RhdHVzOiB7CiAgICAgICAgICAgIHNlbGVjdGVkOiBmYWxzZSwKICAgICAgICAgICAgaG92ZXJlZDogZmFsc2UsCiAgICAgICAgICAgIGs6IGsKICAgICAgICAgIH0gLy8gY2VudGVyOiBbIjEwJSIsICI1MCUiXSwKCiAgICAgICAgfTsKCiAgICAgICAgaWYgKHR5cGVvZiBwaWVEYXRhW2ldLml0ZW1TdHlsZSAhPSAidW5kZWZpbmVkIikgewogICAgICAgICAgdmFyIGl0ZW1TdHlsZSA9IHt9OwogICAgICAgICAgdHlwZW9mIHBpZURhdGFbaV0uaXRlbVN0eWxlLm9wYWNpdHkgIT0gInVuZGVmaW5lZCIgPyBpdGVtU3R5bGUub3BhY2l0eSA9IHBpZURhdGFbaV0uaXRlbVN0eWxlLm9wYWNpdHkgOiBudWxsOwogICAgICAgICAgc2VyaWVzSXRlbS5pdGVtU3R5bGUgPSBpdGVtU3R5bGU7CiAgICAgICAgfQoKICAgICAgICBzZXJpZXMucHVzaChzZXJpZXNJdGVtKTsKICAgICAgfSAvLyDkvb/nlKjkuIrkuIDmrKHpgY3ljobml7bvvIzorqHnrpflh7rnmoTmlbDmja7lkowgc3VtVmFsdWXvvIzosIPnlKggZ2V0UGFyYW1ldHJpY0VxdWF0aW9uIOWHveaVsO+8jAogICAgICAvLyDlkJHmr4/kuKogc2VyaWVzLXN1cmZhY2Ug5Lyg5YWl5LiN5ZCM55qE5Y+C5pWw5pa556iLIHNlcmllcy1zdXJmYWNlLnBhcmFtZXRyaWNFcXVhdGlvbu+8jOS5n+WwseaYr+WunueOsOavj+S4gOS4quaJh+W9ouOAggoKCiAgICAgIGxlZ2VuZERhdGEgPSBbXTsKICAgICAgbGVnZW5kQmZiID0gW107CgogICAgICBmb3IgKHZhciBfaSA9IDA7IF9pIDwgc2VyaWVzLmxlbmd0aDsgX2krKykgewogICAgICAgIGVuZFZhbHVlID0gc3RhcnRWYWx1ZSArIHNlcmllc1tfaV0ucGllRGF0YS52YWx1ZTsKICAgICAgICBzZXJpZXNbX2ldLnBpZURhdGEuc3RhcnRSYXRpbyA9IHN0YXJ0VmFsdWUgLyBzdW1WYWx1ZTsKICAgICAgICBzZXJpZXNbX2ldLnBpZURhdGEuZW5kUmF0aW8gPSBlbmRWYWx1ZSAvIHN1bVZhbHVlOwogICAgICAgIHNlcmllc1tfaV0ucGFyYW1ldHJpY0VxdWF0aW9uID0gdGhpcy5nZXRQYXJhbWV0cmljRXF1YXRpb24oc2VyaWVzW19pXS5waWVEYXRhLnN0YXJ0UmF0aW8sIHNlcmllc1tfaV0ucGllRGF0YS5lbmRSYXRpbywgZmFsc2UsIGZhbHNlLCBrLCBzZXJpZXNbX2ldLnBpZURhdGEudmFsdWUpOwogICAgICAgIHN0YXJ0VmFsdWUgPSBlbmRWYWx1ZTsKICAgICAgICB2YXIgYmZiID0gdGhhdC5mb21hdEZsb2F0KHNlcmllc1tfaV0ucGllRGF0YS52YWx1ZSAvIHN1bVZhbHVlLCA0KTsKICAgICAgICBsZWdlbmREYXRhLnB1c2goewogICAgICAgICAgbmFtZTogc2VyaWVzW19pXS5uYW1lLAogICAgICAgICAgdmFsdWU6IGJmYgogICAgICAgIH0pOwogICAgICAgIGxlZ2VuZEJmYi5wdXNoKHsKICAgICAgICAgIG5hbWU6IHNlcmllc1tfaV0ubmFtZSwKICAgICAgICAgIHZhbHVlOiBzZXJpZXNbX2ldLnBpZURhdGEudmFsdWUKICAgICAgICB9KTsKICAgICAgfQoKICAgICAgdmFyIGJveEhlaWdodCA9IHRoaXMuZ2V0SGVpZ2h0M0Qoc2VyaWVzLCAxNSk7IC8v6YCa6L+H5Lyg5Y+C6K6+5a6aM2Tppbwv546v55qE6auY5bqm77yMMjbku6PooagyNnB4CiAgICAgIC8vIOWHhuWkh+W+hei/lOWbnueahOmFjee9rumhue+8jOaKiuWHhuWkh+WlveeahCBsZWdlbmREYXRh44CBc2VyaWVzIOS8oOWFpeOAggoKICAgICAgdmFyIG9wdGlvbiA9IHsKICAgICAgICBjb2xvcjogWyIjMDBGRUVCIiwgIiNGRjhEMzMiLCAiICM5NEIxQTgiXSwKICAgICAgICBsZWdlbmQ6IHsKICAgICAgICAgIG9yaWVudDogInZlcnRpY2FsIiwKICAgICAgICAgIGRhdGE6IGxlZ2VuZERhdGEsCiAgICAgICAgICB0eXBlOiAicGxhaW4iLAogICAgICAgICAgcmlnaHQ6ICI3JSIsCiAgICAgICAgICB0b3A6ICIxJSIsCiAgICAgICAgICBpdGVtR2FwOiAxMCwKICAgICAgICAgIGl0ZW1XaWR0aDogMTAsCiAgICAgICAgICBpdGVtSGVpZ2h0OiAxMCwKICAgICAgICAgIHRleHRTdHlsZTogewogICAgICAgICAgICBjb2xvcjogIiNBMUUyRkYiLAogICAgICAgICAgICByaWNoOiB7CiAgICAgICAgICAgICAgbmFtZTogewogICAgICAgICAgICAgICAgYWxpZ246ICJsZWZ0IiwKICAgICAgICAgICAgICAgIHdpZHRoOiA5MCwKICAgICAgICAgICAgICAgIHBhZGRpbmc6IFswLCAwLCAwLCAwXSwKICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxMywKICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6ICJQaW5nRmFuZ1NDLVJlZ3VsYXIiLAogICAgICAgICAgICAgICAgY29sb3I6ICIjMzAzYjUwIgogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgYmZzOiB7CiAgICAgICAgICAgICAgICBmb250U2l6ZTogMTMsCiAgICAgICAgICAgICAgICBmb250RmFtaWx5OiAiUGluZ0ZhbmdTQy1SZWd1bGFyIiwKICAgICAgICAgICAgICAgIGNvbG9yOiAiIzMwM2I1MCIsCiAgICAgICAgICAgICAgICAvLyBhbGlnbjogInJpZ2h0IiwKICAgICAgICAgICAgICAgIHBhZGRpbmc6IFswLCAwLCAwLCAzMF0KICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHdhbjogewogICAgICAgICAgICAgICAgZm9udFNpemU6IDEyLAogICAgICAgICAgICAgICAgY29sb3I6ICIjNEFFNUUzIiwKICAgICAgICAgICAgICAgIHBhZGRpbmc6IFswLCAwLCAwLCAxXQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIC8vIGRhdGE6IFsKICAgICAgICAgIC8vICAgewogICAgICAgICAgLy8gICAgIG5hbWU6ICLlpJbotK3nu7/nlLUiLAogICAgICAgICAgLy8gICAgIGljb246IGBpbWFnZTovLyR7bGlnaHRuaW5nfWAsCiAgICAgICAgICAvLyAgIH0sCiAgICAgICAgICAvLyAgIHsgbmFtZTogIuWklui0reeBq+eUtSIsIGljb246IGBpbWFnZTovLyR7b3V0c291cmNpbmd9YCB9LAogICAgICAgICAgLy8gICB7IG5hbWU6ICLoh6rmnInmlrDog73mupDlj5HnlLUiLCBpY29uOiBgaW1hZ2U6Ly8ke25ld2VuZXJneX1gIH0sCiAgICAgICAgICAvLyBdLAogICAgICAgICAgc2hvdzogdHJ1ZSwKICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gZm9ybWF0dGVyKHBhcmFtKSB7CiAgICAgICAgICAgIHZhciBpdGVtID0gbGVnZW5kQmZiLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgIHJldHVybiBpdGVtLm5hbWUgPT0gcGFyYW07CiAgICAgICAgICAgIH0pWzBdOwogICAgICAgICAgICByZXR1cm4gIntuYW1lfCIuY29uY2F0KGl0ZW0ubmFtZSwgIiB9IHtiZnN8ICAiKS5jb25jYXQoaXRlbS52YWx1ZSwgIiB9Iik7CiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBsYWJlbDogewogICAgICAgICAgc2hvdzogZmFsc2UsCiAgICAgICAgICB0ZXh0U3R5bGU6IHsKICAgICAgICAgICAgY29sb3I6ICIjMDAwIgogICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgeEF4aXMzRDogewogICAgICAgICAgbWluOiAtMSwKICAgICAgICAgIG1heDogMQogICAgICAgIH0sCiAgICAgICAgeUF4aXMzRDogewogICAgICAgICAgbWluOiAtMSwKICAgICAgICAgIG1heDogMQogICAgICAgIH0sCiAgICAgICAgekF4aXMzRDogewogICAgICAgICAgbWluOiAtMSwKICAgICAgICAgIG1heDogMQogICAgICAgIH0sCiAgICAgICAgZ3JpZDNEOiB7CiAgICAgICAgICBzaG93OiBmYWxzZSwKICAgICAgICAgIGxlZnQ6ICItMjUlIiwKICAgICAgICAgIHRvcDogIi0xMCUiLAogICAgICAgICAgYm94SGVpZ2h0OiBib3hIZWlnaHQsCiAgICAgICAgICAvL+WchueOr+eahOmrmOW6pgogICAgICAgICAgdmlld0NvbnRyb2w6IHsKICAgICAgICAgICAgLy8zZOaViOaenOWPr+S7peaUvuWkp+OAgeaXi+i9rOetie+8jOivt+iHquW3seWOu+afpeeci+WumOaWuemFjee9rgogICAgICAgICAgICBhbHBoYTogMjcsCiAgICAgICAgICAgIC8v6KeS5bqmCiAgICAgICAgICAgIGJldGE6IDAsCiAgICAgICAgICAgIGRpc3RhbmNlOiAyMjAsCiAgICAgICAgICAgIC8v6LCD5pW06KeG6KeS5Yiw5Li75L2T55qE6Led56a777yM57G75Ly86LCD5pW0em9vbQogICAgICAgICAgICByb3RhdGVTZW5zaXRpdml0eTogMCwKICAgICAgICAgICAgLy/orr7nva7kuLow5peg5rOV5peL6L2sCiAgICAgICAgICAgIHpvb21TZW5zaXRpdml0eTogMCwKICAgICAgICAgICAgLy/orr7nva7kuLow5peg5rOV57yp5pS+CiAgICAgICAgICAgIHBhblNlbnNpdGl2aXR5OiAwLAogICAgICAgICAgICAvL+iuvue9ruS4ujDml6Dms5XlubPnp7sKICAgICAgICAgICAgYXV0b1JvdGF0ZTogdHJ1ZSAvL+iHquWKqOaXi+i9rAoKICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIHNlcmllczogc2VyaWVzCiAgICAgIH07CiAgICAgIHJldHVybiBvcHRpb247CiAgICB9LAogICAgLy/ojrflj5YzZOS4meWbvueahOacgOmrmOaJh+WMuueahOmrmOW6pgogICAgZ2V0SGVpZ2h0M0Q6IGZ1bmN0aW9uIGdldEhlaWdodDNEKHNlcmllcywgaGVpZ2h0KSB7CiAgICAgIHNlcmllcy5zb3J0KGZ1bmN0aW9uIChhLCBiKSB7CiAgICAgICAgcmV0dXJuIGIucGllRGF0YS52YWx1ZSAtIGEucGllRGF0YS52YWx1ZTsKICAgICAgfSk7CiAgICAgIHJldHVybiBoZWlnaHQgKiAyMCAvIHNlcmllc1swXS5waWVEYXRhLnZhbHVlOwogICAgfSwKICAgIC8vIOeUn+aIkOaJh+W9oueahOabsumdouWPguaVsOaWueeoi++8jOeUqOS6jiBzZXJpZXMtc3VyZmFjZS5wYXJhbWV0cmljRXF1YXRpb24KICAgIGdldFBhcmFtZXRyaWNFcXVhdGlvbjogZnVuY3Rpb24gZ2V0UGFyYW1ldHJpY0VxdWF0aW9uKHN0YXJ0UmF0aW8sIGVuZFJhdGlvLCBpc1NlbGVjdGVkLCBpc0hvdmVyZWQsIGssIGgpIHsKICAgICAgLy8g6K6h566XCiAgICAgIHZhciBtaWRSYXRpbyA9IChzdGFydFJhdGlvICsgZW5kUmF0aW8pIC8gMjsKICAgICAgdmFyIHN0YXJ0UmFkaWFuID0gc3RhcnRSYXRpbyAqIE1hdGguUEkgKiAyOwogICAgICB2YXIgZW5kUmFkaWFuID0gZW5kUmF0aW8gKiBNYXRoLlBJICogMjsKICAgICAgdmFyIG1pZFJhZGlhbiA9IG1pZFJhdGlvICogTWF0aC5QSSAqIDI7IC8vIOWmguaenOWPquacieS4gOS4quaJh+W9ou+8jOWImeS4jeWunueOsOmAieS4reaViOaenOOAggogICAgICAvLyBpZiAoc3RhcnRSYXRpbyA9PT0gMCAmJiBlbmRSYXRpbyA9PT0gMSkgewogICAgICAvLyAgIGlzU2VsZWN0ZWQgPSBmYWxzZTsKICAgICAgLy8gfQogICAgICAvLyDpgJrov4fmiYflvaLlhoXlvoQv5aSW5b6E55qE5YC877yM5o2i566X5Ye66L6F5Yqp5Y+C5pWwIGvvvIjpu5jorqTlgLwgMS8z77yJCgogICAgICBrID0gdHlwZW9mIGsgIT09ICJ1bmRlZmluZWQiID8gayA6IDEgLyAzOyAvLyDorqHnrpfpgInkuK3mlYjmnpzliIbliKvlnKggeCDovbTjgIF5IOi9tOaWueWQkeS4iueahOS9jeenu++8iOacqumAieS4re+8jOWImeS9jeenu+Wdh+S4uiAw77yJCgogICAgICB2YXIgb2Zmc2V0WCA9IGlzU2VsZWN0ZWQgPyBNYXRoLmNvcyhtaWRSYWRpYW4pICogMC4xIDogMDsKICAgICAgdmFyIG9mZnNldFkgPSBpc1NlbGVjdGVkID8gTWF0aC5zaW4obWlkUmFkaWFuKSAqIDAuMSA6IDA7IC8vIOiuoeeul+mrmOS6ruaViOaenOeahOaUvuWkp+avlOS+i++8iOacqumrmOS6ru+8jOWImeavlOS+i+S4uiAx77yJCgogICAgICB2YXIgaG92ZXJSYXRlID0gaXNIb3ZlcmVkID8gMS4wNSA6IDE7IC8vIOi/lOWbnuabsumdouWPguaVsOaWueeoiwoKICAgICAgcmV0dXJuIHsKICAgICAgICB1OiB7CiAgICAgICAgICBtaW46IC1NYXRoLlBJLAogICAgICAgICAgbWF4OiBNYXRoLlBJICogMywKICAgICAgICAgIHN0ZXA6IE1hdGguUEkgLyAzMgogICAgICAgIH0sCiAgICAgICAgdjogewogICAgICAgICAgbWluOiAwLAogICAgICAgICAgbWF4OiBNYXRoLlBJICogMiwKICAgICAgICAgIHN0ZXA6IE1hdGguUEkgLyAyMAogICAgICAgIH0sCiAgICAgICAgeDogZnVuY3Rpb24geCh1LCB2KSB7CiAgICAgICAgICBpZiAodSA8IHN0YXJ0UmFkaWFuKSB7CiAgICAgICAgICAgIHJldHVybiBvZmZzZXRYICsgTWF0aC5jb3Moc3RhcnRSYWRpYW4pICogKDEgKyBNYXRoLmNvcyh2KSAqIGspICogaG92ZXJSYXRlOwogICAgICAgICAgfQoKICAgICAgICAgIGlmICh1ID4gZW5kUmFkaWFuKSB7CiAgICAgICAgICAgIHJldHVybiBvZmZzZXRYICsgTWF0aC5jb3MoZW5kUmFkaWFuKSAqICgxICsgTWF0aC5jb3ModikgKiBrKSAqIGhvdmVyUmF0ZTsKICAgICAgICAgIH0KCiAgICAgICAgICByZXR1cm4gb2Zmc2V0WCArIE1hdGguY29zKHUpICogKDEgKyBNYXRoLmNvcyh2KSAqIGspICogaG92ZXJSYXRlOwogICAgICAgIH0sCiAgICAgICAgeTogZnVuY3Rpb24geSh1LCB2KSB7CiAgICAgICAgICBpZiAodSA8IHN0YXJ0UmFkaWFuKSB7CiAgICAgICAgICAgIHJldHVybiBvZmZzZXRZICsgTWF0aC5zaW4oc3RhcnRSYWRpYW4pICogKDEgKyBNYXRoLmNvcyh2KSAqIGspICogaG92ZXJSYXRlOwogICAgICAgICAgfQoKICAgICAgICAgIGlmICh1ID4gZW5kUmFkaWFuKSB7CiAgICAgICAgICAgIHJldHVybiBvZmZzZXRZICsgTWF0aC5zaW4oZW5kUmFkaWFuKSAqICgxICsgTWF0aC5jb3ModikgKiBrKSAqIGhvdmVyUmF0ZTsKICAgICAgICAgIH0KCiAgICAgICAgICByZXR1cm4gb2Zmc2V0WSArIE1hdGguc2luKHUpICogKDEgKyBNYXRoLmNvcyh2KSAqIGspICogaG92ZXJSYXRlOwogICAgICAgIH0sCiAgICAgICAgejogZnVuY3Rpb24geih1LCB2KSB7CiAgICAgICAgICBpZiAodSA8IC1NYXRoLlBJICogMC41KSB7CiAgICAgICAgICAgIHJldHVybiBNYXRoLnNpbih1KTsKICAgICAgICAgIH0KCiAgICAgICAgICBpZiAodSA+IE1hdGguUEkgKiAyLjUpIHsKICAgICAgICAgICAgcmV0dXJuIE1hdGguc2luKHUpICogaCAqIDAuMTsKICAgICAgICAgIH0KCiAgICAgICAgICByZXR1cm4gTWF0aC5zaW4odikgPiAwID8gMSAqIGggKiAwLjEgOiAtMTsKICAgICAgICB9CiAgICAgIH07CiAgICB9LAogICAgZm9tYXRGbG9hdDogZnVuY3Rpb24gZm9tYXRGbG9hdChudW0sIG4pIHsKICAgICAgdmFyIGYgPSBwYXJzZUZsb2F0KG51bSk7CgogICAgICBpZiAoaXNOYU4oZikpIHsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KCiAgICAgIGYgPSBNYXRoLnJvdW5kKG51bSAqIE1hdGgucG93KDEwLCBuKSkgLyBNYXRoLnBvdygxMCwgbik7IC8vIG4g5bmCCgogICAgICB2YXIgcyA9IGYudG9TdHJpbmcoKTsKICAgICAgdmFyIHJzID0gcy5pbmRleE9mKCIuIik7IC8v5Yik5a6a5aaC5p6c5piv5pW05pWw77yM5aKe5Yqg5bCP5pWw54K55YaN6KGlMAoKICAgICAgaWYgKHJzIDwgMCkgewogICAgICAgIHJzID0gcy5sZW5ndGg7CiAgICAgICAgcyArPSAiLiI7CiAgICAgIH0KCiAgICAgIHdoaWxlIChzLmxlbmd0aCA8PSBycyArIG4pIHsKICAgICAgICBzICs9ICIwIjsKICAgICAgfQoKICAgICAgcmV0dXJuIHM7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "sources": ["warnAnalysis.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;AAYA,OAAA,YAAA;AACA,eAAA;AACA,EAAA,IAAA,EAAA,eADA;AAEA,EAAA,UAAA,EAAA,EAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,cAAA,EAAA,EADA;AAEA,MAAA,YAAA,EAAA,KAFA;AAGA,MAAA,UAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,WADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,IAAA,EAAA,WADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,CAHA;AAaA,MAAA,OAAA,EAAA,CAbA;AAcA,MAAA,OAAA,EAAA,EAdA;AAeA,MAAA,MAAA,EAAA;AAfA,KAAA;AAiBA,GArBA;AAsBA,EAAA,KAAA,EAAA;AACA,IAAA,kBAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA,KADA;AAIA,IAAA,SAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA;AAJA,GAtBA;AA+BA,EAAA,KAAA,EAAA;AACA,IAAA,kBAAA,EAAA;AACA,MAAA,SAAA,EAAA,IADA;AAEA,MAAA,OAFA,mBAEA,MAFA,EAEA,MAFA,EAEA;AAAA;;AACA,YAAA,MAAA,IAAA,SAAA,IAAA,MAAA,IAAA,IAAA,IAAA,MAAA,IAAA,EAAA,EAAA,OADA,CAEA;;AACA,aAAA,cAAA,GAAA,MAAA,CAAA,cAAA,CAHA,CAIA;AACA;AACA;;AACA,aAAA,IAAA,CAAA,KAAA,UAAA,CAAA,CAAA,CAAA,EAAA,OAAA,EAAA,MAAA,CAAA,WAAA;AACA,aAAA,IAAA,CAAA,KAAA,UAAA,CAAA,CAAA,CAAA,EAAA,OAAA,EAAA,MAAA,CAAA,UAAA;AACA,aAAA,SAAA,CAAA,YAAA;AACA,UAAA,MAAA,CAAA,IAAA;AACA,SAFA;AAGA,OAdA;AAeA,MAAA,IAAA,EAAA,IAfA,CAeA;;AAfA;AADA,GA/BA;AAmDA,EAAA,OAAA,EAAA;AACA,IAAA,IADA,kBACA;AACA,UAAA,KAAA,GAAA,IAAA,CADA,CAEA;;;AACA,UAAA,OAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CAAA,QAAA,CAAA,cAAA,CAAA,oBAAA,CAAA,CAAA,CAHA,CAIA;;AACA,WAAA,MAAA,GAAA,KAAA,QAAA,CAAA,KAAA,CAAA,UAAA,EAAA,IAAA,CAAA;AACA,MAAA,OAAA,CAAA,SAAA,CAAA,KAAA,MAAA,EANA,CAOA;;AACA,WAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,KAFA;AAGA,QAAA,KAAA,EAAA;AACA,UAAA,MAAA,EAAA;AACA,YAAA,QAAA,EAAA,OADA;AAEA,YAAA,IAAA,EAAA;AAFA;AADA,SAHA;AASA,QAAA,SAAA,EAAA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,MAAA,EAAA,EAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SATA;AAUA,QAAA,UAAA,EAAA,CAVA;AAUA;AACA,QAAA,SAAA,EAAA,KAXA;AAWA;AACA,QAAA,MAAA,EAAA,CAAA,KAAA,EAAA,KAAA,CAZA;AAaA,QAAA,MAAA,EAAA,CAAA,KAAA,EAAA,KAAA,CAbA;AAcA,QAAA,IAAA,EAAA,KAAA,CAAA,UAdA;AAeA,QAAA,SAAA,EAAA;AACA,UAAA,OAAA,EAAA;AADA;AAfA,OAAA;AAoBA,MAAA,OAAA,CAAA,SAAA,CAAA,KAAA,MAAA;AACA,MAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,YAAA;AACA,QAAA,OAAA,CAAA,MAAA;AACA,OAFA,EA7BA,CAgCA;AACA,KAlCA;AAoCA,IAAA,QApCA,oBAoCA,OApCA,EAoCA,qBApCA,EAoCA;AACA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,UAAA,MAAA,GAAA,EAAA;AACA,UAAA,QAAA,GAAA,CAAA;AACA,UAAA,UAAA,GAAA,CAAA;AACA,UAAA,QAAA,GAAA,CAAA;AACA,UAAA,UAAA,GAAA,EAAA;AACA,UAAA,SAAA,GAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA,qBAAA;AACA,MAAA,OAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,eAAA,CAAA,CAAA,KAAA,GAAA,CAAA,CAAA,KAAA;AACA,OAFA,EAVA,CAaA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,QAAA,IAAA,OAAA,CAAA,CAAA,CAAA,CAAA,KAAA;AACA,YAAA,UAAA,GAAA;AACA,UAAA,IAAA,EAAA,OAAA,OAAA,CAAA,CAAA,CAAA,CAAA,IAAA,KAAA,WAAA,mBAAA,CAAA,IAAA,OAAA,CAAA,CAAA,CAAA,CAAA,IADA;AAEA,UAAA,IAAA,EAAA,SAFA;AAGA,UAAA,UAAA,EAAA,IAHA;AAIA,UAAA,SAAA,EAAA;AACA,YAAA,IAAA,EAAA;AADA,WAJA;AAOA,UAAA,OAAA,EAAA,OAAA,CAAA,CAAA,CAPA;AAQA,UAAA,SAAA,EAAA;AACA,YAAA,QAAA,EAAA,KADA;AAEA,YAAA,OAAA,EAAA,KAFA;AAGA,YAAA,CAAA,EAAA;AAHA,WARA,CAaA;;AAbA,SAAA;;AAgBA,YAAA,OAAA,OAAA,CAAA,CAAA,CAAA,CAAA,SAAA,IAAA,WAAA,EAAA;AACA,cAAA,SAAA,GAAA,EAAA;AAEA,iBAAA,OAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,OAAA,IAAA,WAAA,GACA,SAAA,CAAA,OAAA,GAAA,OAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,OADA,GAEA,IAFA;AAGA,UAAA,UAAA,CAAA,SAAA,GAAA,SAAA;AACA;;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,UAAA;AACA,OAzCA,CA2CA;AACA;;;AACA,MAAA,UAAA,GAAA,EAAA;AACA,MAAA,SAAA,GAAA,EAAA;;AACA,WAAA,IAAA,EAAA,GAAA,CAAA,EAAA,EAAA,GAAA,MAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;AACA,QAAA,QAAA,GAAA,UAAA,GAAA,MAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,UAAA,GAAA,UAAA,GAAA,QAAA;AACA,QAAA,MAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,QAAA,GAAA,QAAA,GAAA,QAAA;AACA,QAAA,MAAA,CAAA,EAAA,CAAA,CAAA,kBAAA,GAAA,KAAA,qBAAA,CACA,MAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,UADA,EAEA,MAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,QAFA,EAGA,KAHA,EAIA,KAJA,EAKA,CALA,EAMA,MAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,KANA,CAAA;AAQA,QAAA,UAAA,GAAA,QAAA;AACA,YAAA,GAAA,GAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,KAAA,GAAA,QAAA,EAAA,CAAA,CAAA;AACA,QAAA,UAAA,CAAA,IAAA,CAAA;AACA,UAAA,IAAA,EAAA,MAAA,CAAA,EAAA,CAAA,CAAA,IADA;AAEA,UAAA,KAAA,EAAA;AAFA,SAAA;AAIA,QAAA,SAAA,CAAA,IAAA,CAAA;AACA,UAAA,IAAA,EAAA,MAAA,CAAA,EAAA,CAAA,CAAA,IADA;AAEA,UAAA,KAAA,EAAA,MAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA;AAFA,SAAA;AAIA;;AACA,UAAA,SAAA,GAAA,KAAA,WAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAtEA,CAsEA;AACA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,KAAA,EAAA,CAAA,SAAA,EAAA,SAAA,EAAA,UAAA,CADA;AAEA,QAAA,MAAA,EAAA;AACA,UAAA,MAAA,EAAA,UADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,IAAA,EAAA,OAHA;AAIA,UAAA,KAAA,EAAA,IAJA;AAKA,UAAA,GAAA,EAAA,IALA;AAMA,UAAA,OAAA,EAAA,EANA;AAOA,UAAA,SAAA,EAAA,EAPA;AAQA,UAAA,UAAA,EAAA,EARA;AASA,UAAA,SAAA,EAAA;AACA,YAAA,KAAA,EAAA,SADA;AAEA,YAAA,IAAA,EAAA;AACA,cAAA,IAAA,EAAA;AACA,gBAAA,KAAA,EAAA,MADA;AAEA,gBAAA,KAAA,EAAA,EAFA;AAGA,gBAAA,OAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAHA;AAIA,gBAAA,QAAA,EAAA,EAJA;AAKA,gBAAA,UAAA,EAAA,oBALA;AAMA,gBAAA,KAAA,EAAA;AANA,eADA;AAUA,cAAA,GAAA,EAAA;AACA,gBAAA,QAAA,EAAA,EADA;AAEA,gBAAA,UAAA,EAAA,oBAFA;AAGA,gBAAA,KAAA,EAAA,SAHA;AAIA;AACA,gBAAA,OAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA;AALA,eAVA;AAkBA,cAAA,GAAA,EAAA;AACA,gBAAA,QAAA,EAAA,EADA;AAEA,gBAAA,KAAA,EAAA,SAFA;AAGA,gBAAA,OAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA;AAHA;AAlBA;AAFA,WATA;AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAA,IAAA,EAAA,IA5CA;AA6CA,UAAA,SAAA,EAAA,mBAAA,KAAA,EAAA;AACA,gBAAA,IAAA,GAAA,SAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,qBAAA,IAAA,CAAA,IAAA,IAAA,KAAA;AAAA,aAAA,EAAA,CAAA,CAAA;AAEA,mCAAA,IAAA,CAAA,IAAA,uBAAA,IAAA,CAAA,KAAA;AACA;AAjDA,SAFA;AAsDA,QAAA,KAAA,EAAA;AACA,UAAA,IAAA,EAAA,KADA;AAEA,UAAA,SAAA,EAAA;AACA,YAAA,KAAA,EAAA;AADA;AAFA,SAtDA;AA6DA,QAAA,OAAA,EAAA;AACA,UAAA,GAAA,EAAA,CAAA,CADA;AAEA,UAAA,GAAA,EAAA;AAFA,SA7DA;AAiEA,QAAA,OAAA,EAAA;AACA,UAAA,GAAA,EAAA,CAAA,CADA;AAEA,UAAA,GAAA,EAAA;AAFA,SAjEA;AAqEA,QAAA,OAAA,EAAA;AACA,UAAA,GAAA,EAAA,CAAA,CADA;AAEA,UAAA,GAAA,EAAA;AAFA,SArEA;AAyEA,QAAA,MAAA,EAAA;AACA,UAAA,IAAA,EAAA,KADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,GAAA,EAAA,MAHA;AAIA,UAAA,SAAA,EAAA,SAJA;AAIA;AACA,UAAA,WAAA,EAAA;AACA;AACA,YAAA,KAAA,EAAA,EAFA;AAEA;AACA,YAAA,IAAA,EAAA,CAHA;AAIA,YAAA,QAAA,EAAA,GAJA;AAIA;AACA,YAAA,iBAAA,EAAA,CALA;AAKA;AACA,YAAA,eAAA,EAAA,CANA;AAMA;AACA,YAAA,cAAA,EAAA,CAPA;AAOA;AACA,YAAA,UAAA,EAAA,IARA,CAQA;;AARA;AALA,SAzEA;AAyFA,QAAA,MAAA,EAAA;AAzFA,OAAA;AA2FA,aAAA,MAAA;AACA,KAxMA;AA0MA;AACA,IAAA,WA3MA,uBA2MA,MA3MA,EA2MA,MA3MA,EA2MA;AACA,MAAA,MAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,eAAA,CAAA,CAAA,OAAA,CAAA,KAAA,GAAA,CAAA,CAAA,OAAA,CAAA,KAAA;AACA,OAFA;AAGA,aAAA,MAAA,GAAA,EAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,KAAA;AACA,KAhNA;AAkNA;AACA,IAAA,qBAnNA,iCAmNA,UAnNA,EAmNA,QAnNA,EAmNA,UAnNA,EAmNA,SAnNA,EAmNA,CAnNA,EAmNA,CAnNA,EAmNA;AACA;AACA,UAAA,QAAA,GAAA,CAAA,UAAA,GAAA,QAAA,IAAA,CAAA;AACA,UAAA,WAAA,GAAA,UAAA,GAAA,IAAA,CAAA,EAAA,GAAA,CAAA;AACA,UAAA,SAAA,GAAA,QAAA,GAAA,IAAA,CAAA,EAAA,GAAA,CAAA;AACA,UAAA,SAAA,GAAA,QAAA,GAAA,IAAA,CAAA,EAAA,GAAA,CAAA,CALA,CAMA;AACA;AACA;AACA;AACA;;AACA,MAAA,CAAA,GAAA,OAAA,CAAA,KAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,CAXA,CAYA;;AACA,UAAA,OAAA,GAAA,UAAA,GAAA,IAAA,CAAA,GAAA,CAAA,SAAA,IAAA,GAAA,GAAA,CAAA;AACA,UAAA,OAAA,GAAA,UAAA,GAAA,IAAA,CAAA,GAAA,CAAA,SAAA,IAAA,GAAA,GAAA,CAAA,CAdA,CAeA;;AACA,UAAA,SAAA,GAAA,SAAA,GAAA,IAAA,GAAA,CAAA,CAhBA,CAiBA;;AACA,aAAA;AACA,QAAA,CAAA,EAAA;AACA,UAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EADA;AAEA,UAAA,GAAA,EAAA,IAAA,CAAA,EAAA,GAAA,CAFA;AAGA,UAAA,IAAA,EAAA,IAAA,CAAA,EAAA,GAAA;AAHA,SADA;AAMA,QAAA,CAAA,EAAA;AACA,UAAA,GAAA,EAAA,CADA;AAEA,UAAA,GAAA,EAAA,IAAA,CAAA,EAAA,GAAA,CAFA;AAGA,UAAA,IAAA,EAAA,IAAA,CAAA,EAAA,GAAA;AAHA,SANA;AAWA,QAAA,CAAA,EAAA,WAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA,CAAA,GAAA,WAAA,EAAA;AACA,mBAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA,WAAA,KAAA,IAAA,IAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,IAAA,SAAA;AACA;;AACA,cAAA,CAAA,GAAA,SAAA,EAAA;AACA,mBAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA,SAAA,KAAA,IAAA,IAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,IAAA,SAAA;AACA;;AACA,iBAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,KAAA,IAAA,IAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,IAAA,SAAA;AACA,SAnBA;AAoBA,QAAA,CAAA,EAAA,WAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA,CAAA,GAAA,WAAA,EAAA;AACA,mBAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA,WAAA,KAAA,IAAA,IAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,IAAA,SAAA;AACA;;AACA,cAAA,CAAA,GAAA,SAAA,EAAA;AACA,mBAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA,SAAA,KAAA,IAAA,IAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,IAAA,SAAA;AACA;;AACA,iBAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,KAAA,IAAA,IAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,IAAA,SAAA;AACA,SA5BA;AA6BA,QAAA,CAAA,EAAA,WAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA,CAAA,GAAA,CAAA,IAAA,CAAA,EAAA,GAAA,GAAA,EAAA;AACA,mBAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA;;AACA,cAAA,CAAA,GAAA,IAAA,CAAA,EAAA,GAAA,GAAA,EAAA;AACA,mBAAA,IAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,GAAA,GAAA;AACA;;AACA,iBAAA,IAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,GAAA,GAAA,GAAA,CAAA,CAAA;AACA;AArCA,OAAA;AAuCA,KA5QA;AA8QA,IAAA,UA9QA,sBA8QA,GA9QA,EA8QA,CA9QA,EA8QA;AACA,UAAA,CAAA,GAAA,UAAA,CAAA,GAAA,CAAA;;AACA,UAAA,KAAA,CAAA,CAAA,CAAA,EAAA;AACA,eAAA,KAAA;AACA;;AACA,MAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,EAAA,EAAA,CAAA,CAAA,IAAA,IAAA,CAAA,GAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CALA,CAKA;;AACA,UAAA,CAAA,GAAA,CAAA,CAAA,QAAA,EAAA;AACA,UAAA,EAAA,GAAA,CAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAPA,CAQA;;AACA,UAAA,EAAA,GAAA,CAAA,EAAA;AACA,QAAA,EAAA,GAAA,CAAA,CAAA,MAAA;AACA,QAAA,CAAA,IAAA,GAAA;AACA;;AACA,aAAA,CAAA,CAAA,MAAA,IAAA,EAAA,GAAA,CAAA,EAAA;AACA,QAAA,CAAA,IAAA,GAAA;AACA;;AACA,aAAA,CAAA;AACA;AA/RA;AAnDA,CAAA", "sourcesContent": ["<template>\r\n  <div class=\"water-eval-container\" style=\"position: relative\">\r\n    <div class=\"cityGreenLand-charts\" id=\"structure3D-charts\"></div>\r\n    <div class=\"total_power\">\r\n      <p>\r\n        考核预警值<span>{{ warnValue }}%</span>\r\n      </p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport \"echarts-gl\";\r\nexport default {\r\n  name: \"cityGreenLand\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      electrictTotal: \"\",\r\n      isFullscreen: false,\r\n      optionData: [\r\n        {\r\n          name: \"考核成绩高于预警值\",\r\n          value: 22116,\r\n        },\r\n        {\r\n          name: \"考核成绩低于预警值\",\r\n          value: 16616,\r\n        },\r\n      ],\r\n      nowStep: 0,\r\n      maxStep: 35,\r\n      height: 95,\r\n    };\r\n  },\r\n  props: {\r\n    electricStrcuctObj: {\r\n      type: Object,\r\n    },\r\n    warnValue: {\r\n      type: String,\r\n    },\r\n  },\r\n\r\n  watch: {\r\n    electricStrcuctObj: {\r\n      immediate: true,\r\n      handler(newVal, oldVal) {\r\n        if (newVal == undefined || newVal == null || newVal == \"\") return;\r\n        // 总数量\r\n        this.electrictTotal = newVal.electrictTotal;\r\n        // // 外购绿电\r\n        // this.$set(this.optionData[0], \"value\", newVal.outsourcingGreenPower);\r\n        // 外购火电\r\n        this.$set(this.optionData[0], \"value\", newVal.thanWarnNum);\r\n        this.$set(this.optionData[1], \"value\", newVal.lowWarnNum);\r\n        this.$nextTick(() => {\r\n          this.init();\r\n        });\r\n      },\r\n      deep: true, // 深度监听\r\n    },\r\n  },\r\n\r\n  methods: {\r\n    init() {\r\n      let _this = this;\r\n      //构建3d饼状图\r\n      let myChart = this.$echarts.init(document.getElementById(\"structure3D-charts\"));\r\n      // 传入数据生成 option\r\n      this.option = this.getPie3D(_this.optionData, 0.75);\r\n      myChart.setOption(this.option);\r\n      //是否需要label指引线，如果要就添加一个透明的2d饼状图并调整角度使得labelLine和3d的饼状图对齐，并再次setOption\r\n      this.option.series.push({\r\n        name: \"pie2d\",\r\n        type: \"pie\",\r\n        label: {\r\n          normal: {\r\n            position: \"inner\",\r\n            show: false,\r\n          },\r\n        },\r\n        labelLine: { show: false, length: 15, length2: 40 },\r\n        startAngle: 0, //起始角度，支持范围[0, 360]。\r\n        clockwise: false, //饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式\r\n        radius: [\"78%\", \"74%\"],\r\n        center: [\"50%\", \"50%\"],\r\n        data: _this.optionData,\r\n        itemStyle: {\r\n          opacity: 0,\r\n        },\r\n      });\r\n\r\n      myChart.setOption(this.option);\r\n      window.addEventListener(\"resize\", function () {\r\n        myChart.resize();\r\n      });\r\n      // this.bindListen(myChart);\r\n    },\r\n\r\n    getPie3D(pieData, internalDiameterRatio) {\r\n      //internalDiameterRatio:透明的空心占比\r\n      let that = this;\r\n      let series = [];\r\n      let sumValue = 1;\r\n      let startValue = 1;\r\n      let endValue = 1;\r\n      let legendData = [];\r\n      let legendBfb = [];\r\n      let k = 1 - internalDiameterRatio;\r\n      pieData.sort((a, b) => {\r\n        return b.value - a.value;\r\n      });\r\n      // 为每一个饼图数据，生成一个 series-surface 配置\r\n      for (let i = 0; i < pieData.length; i++) {\r\n        sumValue += pieData[i].value;\r\n        let seriesItem = {\r\n          name: typeof pieData[i].name === \"undefined\" ? `series${i}` : pieData[i].name,\r\n          type: \"surface\",\r\n          parametric: true,\r\n          wireframe: {\r\n            show: false,\r\n          },\r\n          pieData: pieData[i],\r\n          pieStatus: {\r\n            selected: false,\r\n            hovered: false,\r\n            k: k,\r\n          },\r\n          // center: [\"10%\", \"50%\"],\r\n        };\r\n\r\n        if (typeof pieData[i].itemStyle != \"undefined\") {\r\n          let itemStyle = {};\r\n\r\n          typeof pieData[i].itemStyle.opacity != \"undefined\"\r\n            ? (itemStyle.opacity = pieData[i].itemStyle.opacity)\r\n            : null;\r\n          seriesItem.itemStyle = itemStyle;\r\n        }\r\n        series.push(seriesItem);\r\n      }\r\n\r\n      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，\r\n      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。\r\n      legendData = [];\r\n      legendBfb = [];\r\n      for (let i = 0; i < series.length; i++) {\r\n        endValue = startValue + series[i].pieData.value;\r\n        series[i].pieData.startRatio = startValue / sumValue;\r\n        series[i].pieData.endRatio = endValue / sumValue;\r\n        series[i].parametricEquation = this.getParametricEquation(\r\n          series[i].pieData.startRatio,\r\n          series[i].pieData.endRatio,\r\n          false,\r\n          false,\r\n          k,\r\n          series[i].pieData.value\r\n        );\r\n        startValue = endValue;\r\n        let bfb = that.fomatFloat(series[i].pieData.value / sumValue, 4);\r\n        legendData.push({\r\n          name: series[i].name,\r\n          value: bfb,\r\n        });\r\n        legendBfb.push({\r\n          name: series[i].name,\r\n          value: series[i].pieData.value,\r\n        });\r\n      }\r\n      let boxHeight = this.getHeight3D(series, 15); //通过传参设定3d饼/环的高度，26代表26px\r\n      // 准备待返回的配置项，把准备好的 legendData、series 传入。\r\n      let option = {\r\n        color: [\"#00FEEB\", \"#FF8D33\", \" #94B1A8\"],\r\n        legend: {\r\n          orient: \"vertical\",\r\n          data: legendData,\r\n          type: \"plain\",\r\n          right: \"7%\",\r\n          top: \"1%\",\r\n          itemGap: 10,\r\n          itemWidth: 10,\r\n          itemHeight: 10,\r\n          textStyle: {\r\n            color: \"#A1E2FF\",\r\n            rich: {\r\n              name: {\r\n                align: \"left\",\r\n                width: 90,\r\n                padding: [0, 0, 0, 0],\r\n                fontSize: 13,\r\n                fontFamily: \"PingFangSC-Regular\",\r\n                color: \"#303b50\",\r\n              },\r\n\r\n              bfs: {\r\n                fontSize: 13,\r\n                fontFamily: \"PingFangSC-Regular\",\r\n                color: \"#303b50\",\r\n                // align: \"right\",\r\n                padding: [0, 0, 0, 30],\r\n              },\r\n\r\n              wan: {\r\n                fontSize: 12,\r\n                color: \"#4AE5E3\",\r\n                padding: [0, 0, 0, 1],\r\n              },\r\n            },\r\n          },\r\n          // data: [\r\n          //   {\r\n          //     name: \"外购绿电\",\r\n          //     icon: `image://${lightning}`,\r\n          //   },\r\n          //   { name: \"外购火电\", icon: `image://${outsourcing}` },\r\n          //   { name: \"自有新能源发电\", icon: `image://${newenergy}` },\r\n          // ],\r\n          show: true,\r\n          formatter: function (param) {\r\n            let item = legendBfb.filter((item) => item.name == param)[0];\r\n\r\n            return `{name|${item.name} } {bfs|  ${item.value} }`;\r\n          },\r\n        },\r\n\r\n        label: {\r\n          show: false,\r\n          textStyle: {\r\n            color: \"#000\",\r\n          },\r\n        },\r\n\r\n        xAxis3D: {\r\n          min: -1,\r\n          max: 1,\r\n        },\r\n        yAxis3D: {\r\n          min: -1,\r\n          max: 1,\r\n        },\r\n        zAxis3D: {\r\n          min: -1,\r\n          max: 1,\r\n        },\r\n        grid3D: {\r\n          show: false,\r\n          left: \"-25%\",\r\n          top: \"-10%\",\r\n          boxHeight: boxHeight, //圆环的高度\r\n          viewControl: {\r\n            //3d效果可以放大、旋转等，请自己去查看官方配置\r\n            alpha: 27, //角度\r\n            beta: 0,\r\n            distance: 220, //调整视角到主体的距离，类似调整zoom\r\n            rotateSensitivity: 0, //设置为0无法旋转\r\n            zoomSensitivity: 0, //设置为0无法缩放\r\n            panSensitivity: 0, //设置为0无法平移\r\n            autoRotate: true, //自动旋转\r\n          },\r\n        },\r\n        series: series,\r\n      };\r\n      return option;\r\n    },\r\n\r\n    //获取3d丙图的最高扇区的高度\r\n    getHeight3D(series, height) {\r\n      series.sort((a, b) => {\r\n        return b.pieData.value - a.pieData.value;\r\n      });\r\n      return (height * 20) / series[0].pieData.value;\r\n    },\r\n\r\n    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation\r\n    getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {\r\n      // 计算\r\n      let midRatio = (startRatio + endRatio) / 2;\r\n      let startRadian = startRatio * Math.PI * 2;\r\n      let endRadian = endRatio * Math.PI * 2;\r\n      let midRadian = midRatio * Math.PI * 2;\r\n      // 如果只有一个扇形，则不实现选中效果。\r\n      // if (startRatio === 0 && endRatio === 1) {\r\n      //   isSelected = false;\r\n      // }\r\n      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）\r\n      k = typeof k !== \"undefined\" ? k : 1 / 3;\r\n      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）\r\n      let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;\r\n      let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;\r\n      // 计算高亮效果的放大比例（未高亮，则比例为 1）\r\n      let hoverRate = isHovered ? 1.05 : 1;\r\n      // 返回曲面参数方程\r\n      return {\r\n        u: {\r\n          min: -Math.PI,\r\n          max: Math.PI * 3,\r\n          step: Math.PI / 32,\r\n        },\r\n        v: {\r\n          min: 0,\r\n          max: Math.PI * 2,\r\n          step: Math.PI / 20,\r\n        },\r\n        x: function (u, v) {\r\n          if (u < startRadian) {\r\n            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n          }\r\n          if (u > endRadian) {\r\n            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n          }\r\n          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;\r\n        },\r\n        y: function (u, v) {\r\n          if (u < startRadian) {\r\n            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n          }\r\n          if (u > endRadian) {\r\n            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n          }\r\n          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;\r\n        },\r\n        z: function (u, v) {\r\n          if (u < -Math.PI * 0.5) {\r\n            return Math.sin(u);\r\n          }\r\n          if (u > Math.PI * 2.5) {\r\n            return Math.sin(u) * h * 0.1;\r\n          }\r\n          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;\r\n        },\r\n      };\r\n    },\r\n\r\n    fomatFloat(num, n) {\r\n      var f = parseFloat(num);\r\n      if (isNaN(f)) {\r\n        return false;\r\n      }\r\n      f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n); // n 幂\r\n      var s = f.toString();\r\n      var rs = s.indexOf(\".\");\r\n      //判定如果是整数，增加小数点再补0\r\n      if (rs < 0) {\r\n        rs = s.length;\r\n        s += \".\";\r\n      }\r\n      while (s.length <= rs + n) {\r\n        s += \"0\";\r\n      }\r\n      return s;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n#structure3D-charts {\r\n  height: 300px;\r\n  width: 53rem;\r\n  letter-spacing: 0.1rem;\r\n}\r\n.total_power {\r\n  width: 16rem;\r\n  height: 2.5vh;\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 5rem;\r\n  display: flex;\r\n\r\n  & > p:nth-of-type(1) {\r\n    font-size: 15px;\r\n    font-family: PingFangSC-Medium;\r\n    font-weight: 400;\r\n    color: #303b50;\r\n    span {\r\n      color: #00ecc0;\r\n      font-size: 16px;\r\n      margin-left: 0.3rem;\r\n      font-family: Alibaba-PuHuiTi-Bold;\r\n    }\r\n  }\r\n}\r\n\r\n/deep/ canvas {\r\n  z-index: 9 !important;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/view/carbon/assess/assessReport/components"}]}