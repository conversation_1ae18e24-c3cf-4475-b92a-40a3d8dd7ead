{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\check\\cityAccountCheckList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\check\\cityAccountCheckList.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["cityAccountCheckList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "cityAccountCheckList.vue", "sourceRoot": "src/view/account/check", "sourcesContent": ["<template>\r\n  <Card class=\"menu-card\" dis-hover>\r\n    <!--    <Spin size=\"large\" fix v-if=\"loading\"></Spin>-->\r\n    <h3 slot=\"title\">站址关联查询\r\n      <!--<Button type=\"primary\" @click=\"openFLow\" style=\"float: right;margin-left: 5px;\">查看流程</Button>-->\r\n    </h3>\r\n    <Form :model=\"queryParams\" ref=\"queryParamsForm\" :label-width=\"110\" class=\"margin-right-width\">\r\n      <Row class=\"form-panel\">\r\n\r\n\r\n      </Row>\r\n      <Row class=\"form-panel\">\r\n        <Col span=\"6\">\r\n          <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n            <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\"\r\n            >\r\n              <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n              <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}\r\n              </Option>\r\n            </Select>\r\n          </FormItem>\r\n        </Col>\r\n        <Col span=\"6\">\r\n          <FormItem label=\"运营分局：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n            <Input :clearable=true icon=\"ios-archive\" v-model=\"queryParams.countryName2\"\r\n                   placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly\r\n            >\r\n            </Input>\r\n          </FormItem>\r\n          <FormItem label=\"运营分局：\" prop=\"country\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n            <Select v-model=\"queryParams.countryName\" :style=\"formItemWidth\">\r\n              <Option value=\"-1\">全部</Option>\r\n              <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}\r\n              </Option>\r\n            </Select>\r\n          </FormItem>\r\n        </Col>\r\n        <Col span=\"6\">\r\n          <FormItem label=\"站址类型：\" class=\"form-line-height\">\r\n            <Select v-model=\"queryParams.siteType\" :style=\"formItemWidth\">\r\n              <Option v-for=\"item in siteTypeList\" :value=\"item.num\" :key=\"item.name\">{{item.name}}\r\n              </Option>\r\n            </Select>\r\n          </FormItem>\r\n        </Col>\r\n        <Col span=\"6\">\r\n          <FormItem label=\"账期：\" prop=\"evaluationDate\">\r\n            <cl-date-picker :clearable=\"true\" type=\"month\"\r\n                            format=\"yyyyMM\"\r\n                            v-model=\"queryParams.month\"></cl-date-picker>\r\n          </FormItem>\r\n        </Col>\r\n        <!--                <Col span=\"6\">\r\n                            <FormItem label=\"发送状态：\" prop=\"status\">\r\n                                <Select v-model=\"queryParams.sendstatus\">\r\n                                    <Option v-for=\"item in sendstatus\" :value=\"item.typeCode\">\r\n                                        {{item.typeName}}\r\n                                    </Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>-->\r\n\r\n      </Row>\r\n      <div align=\"right\">\r\n        <Button style=\"margin-left: 5px;width:69px;\" type=\"success\" icon=\"ios-search\" @click=\"query()\">搜索    </Button>\r\n        <Button style=\"margin-left: 5px;width:69px;\" type=\"info\" icon=\"ios-redo\" @click=\"reset1()\">重置    </Button>\r\n        <!--                <Button type=\"warning\" @click=\"sendJtStation()\">集团同步</Button>-->\r\n        <Button type=\"text\" @click=\"exportCsv\">导出</Button>\r\n      </div>\r\n    </Form>\r\n    <cl-table title=\"查看明细\" method=\"post\" class=\"unionTable\" :showPage=\"false\" :url=\"url\" ref=\"table\"\r\n              :searchable=\"false\" :exportable=\"false\" :columns=\"column\" :data=\"result.data\" :total=\"result.total\" :loading=\"result.loading\" :pageSize=\"result.pageSize\" height=\"500\">\r\n      <div slot=\"buttons\">\r\n\r\n      </div>\r\n    </cl-table>\r\n    <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n    <Modal\r\n        v-model=\"abnomalModel\"\r\n        width=\"80%\"\r\n        title=\"预警稽核\"\r\n    >\r\n      <query ref=\"query\"></query>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button type=\"text\" @click=\"queryClose\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n  </Card>\r\n</template>\r\n<script>\r\nimport query from \"@/view/account/check/queryAbnormalCity\";\r\nimport countryModal from \"@/view/basedata/ammeter/countryModal\";\r\nimport config from '@/config/index';\r\nimport axios from '@/libs/api.request';\r\nimport {blist} from \"@/libs/tools\";\r\nimport ExportCsv from '_c/cl/table/export-csv';\r\nimport Csv from '_c/cl/table/csv';\r\nimport {getUserByUserRole, getCountryByUserId, getCountrysdata,listStationjt,sendjtstations,getUserdata} from '@/api/basedata/ammeter.js'\r\nimport {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nexport default {\r\n  name: \"cityAccountCheckList\",\r\n  components:{ query,countryModal},\r\n  data() {\r\n    let renderLteID = (h, params) => {\r\n      let row = params.row;\r\n      let that = this;\r\n      if (row.r_id == null) {\r\n        return h('Input', {\r\n          props: {\r\n\r\n            icon: \"ios-archive\",\r\n            placeholder: \"点击图标选择\",\r\n            readonly: true,\r\n          },\r\n          on: {\r\n            'on-click': () => {\r\n              this.result.data[params.index]= row;\r\n              this.chooseLteStation(row, params);\r\n            }\r\n          }\r\n        })\r\n      } else {\r\n        return h(\"div\",'已关联');\r\n      }\r\n    };\r\n    let renderStatus = (h, params) => {\r\n      let value = \"\";\r\n      for (let item of this.sendstatus) {\r\n        if (item.typeCode == params.row.sendstatus) {\r\n          value = item.typeName;\r\n          break;\r\n        }\r\n        else{\r\n\r\n        }\r\n      }\r\n      return h(\"div\", value);\r\n    };\r\n    let renderW = (h, params) => {\r\n      var that = this;\r\n      var text, type = \"\";\r\n      if (params.row.billid) {\r\n        text = \"查看\";\r\n        type = \"success\";\r\n      } else {\r\n        text = \"提交\";\r\n        type = \"primary\";\r\n      }\r\n      return h(\"Button\", {\r\n        props: {\r\n          type: type, size: \"small\"\r\n        }, on: {\r\n          click() {\r\n            if (params.row.processinstid) {\r\n              that.showFlow(params.row.id, params.row.processinstid, params.row.companyCode);\r\n            } else {\r\n              if (that.fillInNameNow == params.row.fillInAccount) {\r\n                // 验证\r\n                that.loading = true;\r\n                saveCheckAccount(params.row.id).then(res => {\r\n                  that.loading = false;\r\n                  if (res.data.success) {\r\n                    that.startFlow(params.row.id, params.row.abstractValue, params.row.companyCode);\r\n                  } else {\r\n                    that.$Notice.error({\r\n                      duration: 8,\r\n                      title: '验证错误:' + params.row.abstractValue,\r\n                      desc: res.data.msg\r\n                    });\r\n                  }\r\n                })\r\n              } else\r\n                that.$Message.info(\"只有(\" + params.row.fillInName + \")能提交\");\r\n            }\r\n          }\r\n        }\r\n      }, text);\r\n    };\r\n    return {\r\n      firstCountryname2: \"\",\r\n      firstCountry: \"\",\r\n      firstCountryname: \"\",\r\n      abnomalModel:false,\r\n      siteTypeList:[\r\n        {\r\n          name:'基站类',\r\n          num:'1'\r\n        },\r\n        // {\r\n        //   name:'非基站类',\r\n        //   num:'0'\r\n        // }\r\n      ],\r\n      formItemWidth: widthstyle,\r\n      electrotypes:[1411,1412,1421,1422,1431,1432],\r\n      nodeName:null,\r\n      version: config.version,\r\n      url: \"/business/poweraudit/getPowerAuditCompanies\",\r\n      queryParams: {company: 0,month:'',countryName:'',countryName2:'',city: '', country: ''},\r\n      column: [],\r\n      headColumn: [\r\n        {title: \"所属部门\", key: \"city\", minWidth: 150},\r\n        {title: \"时间(月)\", key: \"month\",  minWidth: 100},\r\n        {title: \"运营分局\", key: \"operationsBranch\",  minWidth: 100},\r\n        {title: \"异常电表总表(个)\", key: \"sums\", minWidth: 130,\r\n          render:(h,params)=>{\r\n            return h('span',{\r\n              'style':{\r\n                'color':'#f83333'\r\n              },\r\n              on:{\r\n                click:()=>{\r\n                  this.openDialog('地市和运营分局',params.row,'地市和运营分局');\r\n                }\r\n              }\r\n            },params.row.sums)\r\n          }\r\n        },\r\n        {title: \"异常电表数\", align: 'center',\r\n          children: [\r\n            {title: \"一站多表/多站多表\", key: \"mutiJtlteCodes\",  minWidth: 150,\r\n              render:(h,params)=>{\r\n                return h('span',{\r\n                  'style':{\r\n                    'color':params.row.mutiJtlteCodes == '0'?'#515a6e':'#f83333'\r\n                  },\r\n                  on:{\r\n                    click:()=>{\r\n                      if(params.row.mutiJtlteCodes != '0'){\r\n                        this.openDialog('一站多表/多站多表',params.row,'一站多表/多站多表');\r\n                      }\r\n                    }\r\n                  }\r\n                },params.row.mutiJtlteCodes)\r\n              }\r\n            },\r\n            {title: \"电价合理性\", key: \"electricityPrices\",  minWidth: 100,\r\n              render:(h,params)=>{\r\n                  return h('span',{\r\n                    'style':{\r\n                      'color':params.row.electricityPrices == '0'?'#515a6e':'#f83333'\r\n                    },\r\n                    on:{\r\n                      click:()=>{\r\n                        if(params.row.electricityPrices != '0'){\r\n                          this.openDialog('电价合理性',params.row,'电价合理性');\r\n                        }\r\n                      }\r\n                    }\r\n                  },params.row.electricityPrices)\r\n              },\r\n            },\r\n            {title: \"电表站址一致性\", key: \"addressConsistence\",  minWidth: 130,\r\n              render:(h,params)=>{\r\n                  return h('span',{\r\n                    'style':{\r\n                      'color':params.row.addressConsistence == '0'?'#515a6e':'#f83333'\r\n                    },\r\n                    on: {\r\n                      click: () => {\r\n                        if(params.row.addressConsistence != '0'){\r\n                          this.openDialog('电表站址一致性',params.row,'电表站址一致性');\r\n                        }\r\n                      }\r\n                    }\r\n                  },params.row.addressConsistence)\r\n              },\r\n            },\r\n            {title: \"台账周期连续性\", key: \"periodicAnomaly\", minWidth: 130,\r\n              render:(h,params)=>{\r\n                  return h('span',{\r\n                    'style':{\r\n                      'color':params.row.periodicAnomaly == '0'?'#515a6e':'#f83333'\r\n                    },\r\n                    on: {\r\n                      click: () => {\r\n                        if(params.row.periodicAnomaly != '0'){\r\n                          this.openDialog('台账周期连续性',params.row,\"台账周期连续性\");\r\n                        }\r\n                      }\r\n                    }\r\n                  },params.row.periodicAnomaly)\r\n              },\r\n            },\r\n            {title: \"电表度数连续性\", key: \"electricityContinuity\", minWidth: 130,\r\n              render:(h,params)=> {\r\n                return h('span', {\r\n                  'style': {\r\n                    'color': params.row.electricityContinuity == '0'?'#515a6e':'#f83333'\r\n                  },\r\n                  on: {\r\n                    click: () => {\r\n                      if(params.row.electricityContinuity != '0'){\r\n                        this.openDialog('电表度数连续性', params.row, '电表度数连续性');\r\n                      }\r\n                    }\r\n                  }\r\n                }, params.row.electricityContinuity)\r\n              }\r\n            },\r\n            // {title: \"电量合理性(省内大数据)\", key: \"electricityRationality\", minWidth: 170,\r\n            //   render:(h,params)=>{\r\n            //       return h('span',{\r\n            //         'style':{\r\n            //           'color':params.row.electricityRationality == '0'?'#515a6e':'#f83333'\r\n            //         },\r\n            //         on: {\r\n            //           click: () => {\r\n            //             if(params.row.electricityRationality != '0'){\r\n            //               this.openDialog('电量合理性(省内大数据)',params.row,'电量合理性(省内大数据)');\r\n            //             }\r\n            //           }\r\n            //         }\r\n            //       },params.row.electricityRationality)\r\n            //   },\r\n            // },\r\n            {title: \"日均电量的波动合理性(集团5gr)\", key: \"fluctuateContinuity\", minWidth: 230,\r\n              render:(h,params)=>{\r\n                  return h('span',{\r\n                    'style':{\r\n                      'color':params.row.fluctuateContinuity == '0'?'#515a6e':'#f83333'\r\n                    },\r\n                    on: {\r\n                      click: () => {\r\n                        if(params.row.fluctuateContinuity != '0'){\r\n                          this.openDialog('日均电量的波动合理性(集团5gr)',params.row,'日均电量的波动合理性(集团5gr)');\r\n                        }\r\n                      }\r\n                    }\r\n                  },params.row.fluctuateContinuity)\r\n              },\r\n            },\r\n            // {title: \"日均耗电量合理性(无线大数据)\", key: \"consumeContinuity\", minWidth: 230,\r\n            //   render:(h,params)=>{\r\n            //     return h('span',{\r\n            //       'style':{\r\n            //         'color':'#f83333'\r\n            //       },\r\n            //       on: {\r\n            //         click: () => {\r\n            //           this.openDialog('日均耗电量合理性',params.row,'日均耗电量合理性');\r\n            //         }\r\n            //       }\r\n            //     },params.row.consumeContinuity)\r\n            //   },\r\n            // },\r\n            {title: \"分摊比例准确性\", key: \"shareAccuracy\", minWidth: 160,\r\n              render:(h,params)=> {\r\n                return h('span', {\r\n                  'style': {\r\n                    'color': params.row.shareAccuracy == '0'?'#515a6e':'#f83333'\r\n                  },\r\n                  on: {\r\n                    click: () => {\r\n                      if(params.row.shareAccuracy != '0'){\r\n                        this.openDialog('分摊比例准确性', params.row, '分摊比例准确性');\r\n                      }\r\n                    }\r\n                  }\r\n                }, params.row.shareAccuracy)\r\n              }\r\n            },\r\n            // {title: \"局站独享共享设置\", key: \"exclusiveAccuracy\", minWidth: 160,\r\n            //   render:(h,params)=>{\r\n            //       return h('span',{\r\n            //         'style':{\r\n            //           'color':params.row.exclusiveAccuracy == '0'?'#515a6e':'#f83333'\r\n            //         },\r\n            //         on: {\r\n            //           click: () => {\r\n            //             if(params.row.exclusiveAccuracy != '0'){\r\n            //               this.openDialog('局站独享共享设置',params.row,'局站独享共享设置');\r\n            //             }\r\n            //           }\r\n            //         }\r\n            //       },params.row.exclusiveAccuracy)\r\n            //     }\r\n            // },\r\n            {title: \"台账周期合理性\", key: \"reimbursementCycle\", minWidth: 130,\r\n              render:(h,params)=>{\r\n                  return h('span',{\r\n                    'style':{\r\n                      'color':params.row.reimbursementCycle == '0'?'#515a6e':'#f83333'\r\n                    },\r\n                    on: {\r\n                      click: () => {\r\n                        if(params.row.reimbursementCycle != '0'){\r\n                          this.openDialog('台账周期合理性',params.row,'台账周期合理性');\r\n                        }\r\n                      }\r\n                    }\r\n                  },params.row.reimbursementCycle)\r\n              },\r\n            },\r\n          ]\r\n        },\r\n      ],\r\n      sdata:[],\r\n      result: {\r\n        loading: false,\r\n        data: [],\r\n        total: 0,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      companies: [],\r\n      departments: [],\r\n      billid:null,\r\n      selectedow:null,\r\n      loading: false,\r\n      company: 0,\r\n      sendstatus: [],\r\n      isAdmin:false,\r\n      isSubAdmin:false,//县能耗管理员\r\n      isCityAdmin:false,\r\n      isProAdmin:false,\r\n      proAdmin:null,\r\n      export: {\r\n        run: false,//是否正在执行导出\r\n        data: \"\",//导出数据\r\n        totalPage: 0,//一共多少页\r\n        currentPage: 0,//当前多少页\r\n        percent: 0,\r\n        size: 50000\r\n      },\r\n      exportColumns: [\r\n        {title: \"单位\", key: \"parentcountryname\"},\r\n        {title: \"部门\", key: \"countryname\"},\r\n        {title: \"能耗站址编码\", key: \"resstationcode\"},\r\n        {title: \"能耗站址名称\", key: \"resstationname\"},\r\n        {title: \"详细地址\", key: \"address\"},\r\n        {title: \"能耗站址关联基站id\", key: \"nmcodes\"},\r\n        {title: \"LTE集团站址名称\", key: \"ltestationAddrName\"},\r\n        {title: 'LTE集团编码', key: 'ltestationAddrCode'},\r\n        {title: 'LTE集团铁塔编码', key: 'ltestationAddrTaCode'},\r\n      ]\r\n\r\n\r\n    }\r\n  }, \r\n  methods: {\r\n    reset1() {\r\n      console.log(this.company, \"this.company\");\r\n      console.log(this.firstCountry, \"this.firstCountry\");\r\n      console.log(this.firstCountryname, \"this.firstCountryname\");\r\n      this.queryParams.month = this.getno();\r\n      this.queryParams.company = this.company;\r\n      this.queryParams.country = this.firstCountry;\r\n      this.queryParams.countryName = this.firstCountryname;\r\n      this.queryParams.countryName2 = this.firstCountryname2;\r\n    },\r\n    getno() {\r\n      let date = new Date();\r\n      //获取当前年月\r\n      let year = date.getFullYear();\r\n      let month = date.getMonth() + 1;\r\n      month = (month < 10 ? \"0\" + month : month);\r\n      let curDate = (year.toString() + month.toString());\r\n      return curDate;\r\n    },\r\n    openDialog(type,value,name){\r\n      console.log(value);\r\n      this.$refs.query.pageNum = 1;\r\n      this.$refs.query.pageSize = 10;\r\n      this.$refs.query.cityCode=value.cityCode\r\n      this.$refs.query.countyCompanies=value.countyCompaniesCode\r\n      this.$refs.query.operationsBranch=value.operationsBranch\r\n      this.$refs.query.cityName=value.city\r\n      this.$refs.query.siteType=value.siteType\r\n      this.$refs.query.month=value.month\r\n      this.$refs.query.exportName=name\r\n      this.$refs.query.activeName=type\r\n      this.$refs.query.checktable()\r\n      this.abnomalModel=true\r\n      console.log(type);\r\n    },\r\n    queryClose(){\r\n      this.abnomalModel=false;\r\n    },\r\n    getDataFromModal(data) {\r\n      //修改\r\n      this.queryParams.countryName = data.name;\r\n      this.queryParams.country = data.id;\r\n      this.queryParams.countryName2 = data.name;\r\n      this.$set(this.queryParams,'countryName',data.name);\r\n      console.log(this.queryParams.countryName);\r\n      // this.queryParams.city = \"成都\";\r\n      // this.queryParams.countyCompanies = data.name;\r\n      //选择所属部门结束\r\n    },\r\n    selectChange(value){\r\n      if (this.queryParams.company != undefined) {\r\n        getCountryByUserId(this.queryParams.company).then(res => {\r\n          this.departments = res.data.departments;\r\n          // this.queryParams.country = res.data.departments[0].id;\r\n          // this.queryParams.countryName = this.departments[0].name;\r\n          // this.queryParams.countryname2 = this.departments[0].name;\r\n          // this.$set(this.queryParams,'countryName',res.data.departments[0].id);\r\n          // this.$set(this.queryParams,'countryName2',res.data.departments[0].name);\r\n          this.queryParams.country = res.data.departments[0].id;\r\n          this.queryParams.countryName = res.data.departments[0].name;\r\n          this.queryParams.countryName2 = res.data.departments[0].name;\r\n        });\r\n      }\r\n      //分公司选择全部，部门清空\r\n      if(value==-1){\r\n        this.queryParams.country=null;\r\n        this.queryParams.countryName =null;\r\n        this.queryParams.countryName2 =null;\r\n      }\r\n    },\r\n    //合计接口\r\n    sumData(){\r\n      let that =this;\r\n      // let params = this.queryParams;\r\n      let params = {\r\n\r\n      };\r\n      params.pageNum = this.result.pageNum;\r\n      params.pageSize = this.result.pageSize;\r\n      let req = {\r\n        url: this.url2,\r\n        method: \"post\",\r\n        data: params\r\n      };\r\n      this.result.loading = true;\r\n      axios.request(req).then(res => {\r\n        res.data.city='合计';\r\n        this.sumRow=res.data;\r\n        console.log(res);\r\n        this.query();\r\n      }).catch(err => {\r\n        console.log(err);\r\n      });\r\n    },\r\n    query() {\r\n      this.setColumn();\r\n      if (this.queryParams.countryName2 == \"\") {\r\n        this.queryParams.country = \"\";\r\n      }\r\n      let params={\r\n        cityCode:this.queryParams.company,\r\n        operationsBranch:this.queryParams.countryName2,\r\n        countyCompaniesCode: this.queryParams.country,\r\n        siteType:this.queryParams.siteType,\r\n        month:this.queryParams.month,\r\n      }\r\n      this.$refs.table.query(params);\r\n    },\r\n    setColumn() {\r\n      this.column = this.headColumn;\r\n    },\r\n    sendJtStation(){\r\n      let that=this;\r\n      if (this.queryParams.company!=null&&this.queryParams.company>0)\r\n      {/** this.$Message.info(\"暂定为规定时间同步！\");8**/\r\n      this.$Modal.confirm({\r\n        title: '温馨提示',\r\n        content: '<p>集团同步数据需要时间，请勿频繁发送，请确认是否同步？</p>',\r\n        onOk: () => {\r\n          sendjtstations(that.queryParams.company).then(res => {//\r\n\r\n          });\r\n        },\r\n      });\r\n      }\r\n      else\r\n      {\r\n        this.$Message.info(\"请先选择 所属分公司\");\r\n      }\r\n    },\r\n    chooseResponseCenter() {\r\n      if (this.queryParams.company == null || this.queryParams.company == \"-1\") {\r\n        this.$Message.info(\"请先选择分公司\");\r\n        return;\r\n      }\r\n      this.$refs.countryModal.choose(this.queryParams.company);//所属部门\r\n    },\r\n    getDataFromlteStationModal(data){\r\n      this.result.data[this.selectedrow].id=data.id;\r\n      this.result.data[this.selectedrow].ltestationAddrCode=data.jtcode;\r\n      this.result.data[this.selectedrow].ltestationAddrName=data.jtname;\r\n      this.result.data[this.selectedrow].sendstatus=\"0\";\r\n    },\r\n    exportCsv() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1;\r\n      const day = now.getDate();\r\n      // let params = this.queryParams;\r\n      let params ={\r\n        operationsBranch:this.queryParams.countryName,\r\n        countyCompaniesCode: this.queryParams.country,\r\n        siteType:this.queryParams.siteType,\r\n        cityCode:this.queryParams.company,\r\n        fileName: \"详单导出\",\r\n        month:this.queryParams.month,\r\n        type:'运营分局'\r\n      }\r\n      params.type='运营分局';\r\n      params.fileName='详单导出';\r\n      let req = {\r\n        url: \"business/poweraudit/exportPowerAudit\",\r\n        method: \"post\",\r\n        data: params,\r\n      };\r\n      axios.file(req).then((res) => {\r\n        const content = res;\r\n        const blob = new Blob([content]);\r\n        const fileName = `详单导出.xlsx`;\r\n        if (\"download\" in document.createElement(\"a\")) {\r\n          // 非IE下载\r\n          const elink = document.createElement(\"a\");\r\n          elink.download = fileName;\r\n          elink.style.display = \"none\";\r\n          elink.href = URL.createObjectURL(blob);\r\n          document.body.appendChild(elink);\r\n          elink.click();\r\n          URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n          document.body.removeChild(elink);\r\n        } else {\r\n          // IE10+下载\r\n          navigator.msSaveBlob(blob, fileName);\r\n        }\r\n      })\r\n          .catch((err) => {\r\n            console.log(err);\r\n          });\r\n    },\r\n    beforeLoadData(data) {\r\n      if (this.export && this.export.run) {\r\n        let csvData = Csv(this.exportColumns, data, {}, false)\r\n        ExportCsv.download(\"集团关联导出数据\" + Math.floor(Math.random() * 100 + 1) + \".csv\", csvData)\r\n        this.export.run = false\r\n        this.$Spin.hide()\r\n      }\r\n    },\r\n    chooseLteStation(params)\r\n    {\r\n\r\n      this.selectedrow=params._index;\r\n      this.$refs.ltestationSelect.initDataList(this.billid,params);\r\n    },\r\n    //向后台请求数据\r\n    getAccountMessages() {\r\n      if (this.queryParams.countryName == \"\") {\r\n        this.queryParams.country = \"\";\r\n      }\r\n\r\n      let params={\r\n        cityCode:this.queryParams.company,\r\n        operationsBranch:this.queryParams.countryName,\r\n        countyCompaniesCode: this.queryParams.country,\r\n        siteType:this.queryParams.siteType,\r\n        month:this.queryParams.month,\r\n      }\r\n      let req = {\r\n        url: this.url,\r\n        method: \"post\",\r\n        data: params\r\n      };\r\n      let array = [];\r\n      this.result.loading = true;\r\n      axios.request(req).then(res => {\r\n        this.result.loading = false;\r\n        if (res.data) {\r\n          this.result.total = res.data.total;\r\n          this.result.data = Object.assign([], res.data.rows)\r\n\r\n        }\r\n      }).catch(err => {\r\n        console.log(err);\r\n      });\r\n    },\r\n    getUserData() {\r\n      let that = this;\r\n      getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n        if (res.data.companies.length != 0) {\r\n          let companies = res.data.companies;\r\n          if (res.data.companies[0].id == \"**********\") {\r\n            companies = that.companies;\r\n          }\r\n          that.company = companies[0].id;\r\n          that.queryParams.company = companies[0].id;\r\n        }\r\n        if (res.data.departments.length != 0) {\r\n          let departments = res.data.departments;\r\n          if (res.data.companies[0].id == \"**********\" && that.departments.length != 0) {\r\n            departments = that.departments\r\n          }\r\n          that.country = departments[0].id;\r\n          that.countryName = departments[0].name;\r\n          that.queryParams.country = Number(departments[0].id);\r\n          that.queryParams.countryName = departments[0].name;\r\n          that.queryParams.countryName2 = departments[0].name;\r\n        }\r\n\r\n        that.pageNum = 1\r\n        that.setColumn();\r\n        that.getAccountMessages();\r\n      });\r\n      // getCountryByUserId().then(res => {\r\n      //     // this.departments = res.data.departments;\r\n      //     // this.queryParams.country = res.data.departments[0].id;\r\n      //     // this.queryParams.countryname = this.departments[0].name;\r\n      //     this.country = res.data.departments[0].id;\r\n      //     this.countryname = res.data.departments[0].name;\r\n      //     this.countryname2 = res.data.departments[0].name;\r\n      //   });\r\n    }},\r\n    mounted() {\r\n    this.sendstatus = blist(\"sendstatus\");\r\n    this.sdata = [];\r\n    let that = this;\r\n    this.queryParams.siteType = '1';\r\n    this.queryParams.month = this.getno();\r\n    getUserByUserRole().then(res => {//根据权限获取分公司\r\n      that.companies = res.data.companies;\r\n      if (res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true) {\r\n        that.isAdmin = true;\r\n      }\r\n      getCountrysdata({orgCode: res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n        that.departments = res.data;\r\n        this.firstCountryname = res.data[0].name;\r\n        this.firstCountryname2 = res.data[0].name;\r\n        this.firstCountry= res.data[0].id;\r\n        that.getUserData();\r\n      });\r\n    });\r\n\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}