{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\effectAnalysis.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\effectAnalysis.vue", "mtime": 1754285403029}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": 1741317456427}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["effectAnalysis.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAOA,OAAA,YAAA;AACA,eAAA;AACA,EAAA,IAAA,EAAA,eADA;AAEA,EAAA,UAAA,EAAA,EAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,cAAA,EAAA,EADA;AAEA,MAAA,YAAA,EAAA,KAFA;AAGA,MAAA,UAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,IAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,IAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,CAHA;AAiBA,MAAA,OAAA,EAAA,CAjBA;AAkBA,MAAA,OAAA,EAAA,EAlBA;AAmBA,MAAA,MAAA,EAAA;AAnBA,KAAA;AAqBA,GAzBA;AA0BA,EAAA,KAAA,EAAA;AACA,IAAA,WAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA;AADA,GA1BA;AAgCA,EAAA,KAAA,EAAA;AACA,IAAA,WAAA,EAAA;AACA,MAAA,SAAA,EAAA,IADA;AAEA,MAAA,OAFA,mBAEA,MAFA,EAEA,MAFA,EAEA;AAAA;;AACA,YAAA,MAAA,IAAA,SAAA,IAAA,MAAA,IAAA,IAAA,IAAA,MAAA,IAAA,EAAA,EAAA;AACA,aAAA,SAAA,CAAA,YAAA;AACA,UAAA,KAAA,CAAA,IAAA;AACA,SAFA;AAGA,OAPA;AAQA,MAAA,IAAA,EAAA,IARA,CAQA;;AARA;AADA,GAhCA;AA6CA,EAAA,OAAA,EAAA;AACA,IAAA,IADA,kBACA;AAAA;;AACA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA,CAFA,CAGA;AACA;AACA;;AACA,UAAA,UAAA,GAAA,EAAA,CANA,CAOA;;AACA,UAAA,UAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA;AACA,YAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,QAAA,GAAA,MAAA;AACA,YAAA,IAAA;;AACA,YAAA,GAAA,GAAA,CAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,GAAA,GAAA;AACA,SAFA,MAEA,IAAA,GAAA,GAAA,CAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,GAAA,GAAA;AACA,SAFA,MAEA,IAAA,GAAA,GAAA,CAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,GAAA,GAAA;AACA;;AACA,QAAA,UAAA,CAAA,IAAA,CAAA;AACA,UAAA,IAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA,KAAA,GAAA,EAFA;AAGA,UAAA,KAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAHA;AAIA,UAAA,QAAA,EAAA,CACA;AACA,YAAA,IAAA,EAAA,QADA;AAEA,YAAA,KAAA,EAAA;AACA,cAAA,EAAA,EAAA,CADA;AAEA,cAAA,EAAA,EAAA,CAFA;AAGA,cAAA,CAAA,EAAA;AAHA,aAFA;AAOA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,EACA,KAAA,KAAA,CAAA,GACA,SADA,GAEA,KAAA,KAAA,CAAA,GACA,SADA,GAEA,KAAA,KAAA,CAAA,GACA,SADA,GAEA;AARA;AAPA,WADA,EAmBA;AACA,YAAA,IAAA,EAAA,MADA;AAEA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,EAAA,IAAA,CAAA,IADA;AAEA,cAAA,UAAA,EAAA,oBAFA;AAGA,cAAA,IAAA,EAAA,MAHA;AAIA,cAAA,QAAA,EAAA,EAJA;AAKA,cAAA,CAAA,EAAA,EALA;AAMA,cAAA,CAAA,EAAA;AANA;AAFA,WAnBA,EA8BA;AACA,YAAA,IAAA,EAAA,MADA;AAEA,YAAA,IAAA,EAAA,IAAA,CAAA,IAFA;AAGA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,EAAA,IAAA,CAAA,GADA;AAEA,cAAA,IAAA,EAAA,MAFA;AAGA,cAAA,UAAA,EAAA,sBAHA;AAIA,cAAA,QAAA,EAAA,EAJA;AAKA,cAAA,CAAA,EAAA,EALA;AAMA,cAAA,CAAA,EAAA,CAAA;AANA;AAHA,WA9BA,EA0CA;AACA,YAAA,IAAA,EAAA,MADA;AAEA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,EAAA,GADA;AAEA,cAAA,UAAA,EAAA,oBAFA;AAGA,cAAA,IAAA,EAAA,MAHA;AAIA,cAAA,QAAA,EAAA,EAJA;AAKA,cAAA,CAAA,EAAA,IALA;AAMA,cAAA,CAAA,EAAA;AANA;AAFA,WA1CA;AAJA,SAAA;;AA4DA,YAAA,KAAA,KAAA,CAAA,EAAA;AACA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,EAAA,IAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,cAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CACA;AAAA,gBAAA,MAAA,EAAA,CAAA;AAAA,gBAAA,KAAA,EAAA;AAAA,eADA,EAEA;AAAA,gBAAA,MAAA,EAAA,CAAA;AAAA,gBAAA,KAAA,EAAA;AAAA,eAFA,CAAA;AADA;AADA,aAOA,IAPA;AASA,SAVA,MAUA,IAAA,KAAA,KAAA,CAAA,EAAA;AACA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,EAAA,IAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,cAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CACA;AAAA,gBAAA,MAAA,EAAA,CAAA;AAAA,gBAAA,KAAA,EAAA;AAAA,eADA,EAEA;AAAA,gBAAA,MAAA,EAAA,CAAA;AAAA,gBAAA,KAAA,EAAA;AAAA,eAFA,CAAA;AADA;AADA,aAOA,IAPA;AASA,SAVA,MAUA,IAAA,KAAA,KAAA,CAAA,EAAA;AACA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,EAAA,IAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,cAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CACA;AAAA,gBAAA,MAAA,EAAA,CAAA;AAAA,gBAAA,KAAA,EAAA;AAAA,eADA,EAEA;AAAA,gBAAA,MAAA,EAAA,CAAA;AAAA,gBAAA,KAAA,EAAA;AAAA,eAFA,CAAA;AADA;AADA,aAOA,IAPA;AASA,SAVA,MAUA,IAAA,KAAA,KAAA,CAAA,EAAA;AACA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,EAAA,IAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,cAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CACA;AAAA,gBAAA,MAAA,EAAA,CAAA;AAAA,gBAAA,KAAA,EAAA;AAAA,eADA,EAEA;AAAA,gBAAA,MAAA,EAAA,CAAA;AAAA,gBAAA,KAAA,EAAA;AAAA,eAFA,CAAA;AADA;AADA,aAOA,IAPA;AASA;AACA,OA/GA,CAAA,CARA,CAwHA;;AACA,UAAA,OAAA,GAAA,KAAA,YAAA,CACA,UADA,EAEA,QAAA,CAAA,cAAA,CAAA,sBAAA,CAFA,CAAA;AAIA,UAAA,OAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CAAA,QAAA,CAAA,cAAA,CAAA,sBAAA,CAAA,CAAA;AACA,UAAA,MAAA,GAAA;AACA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,IAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,IAFA;AAGA,UAAA,GAAA,EAAA,KAHA;AAIA,UAAA,KAAA,EAAA,CAAA,GAAA,EAAA,GAAA,CAJA;AAKA,UAAA,OAAA,EAAA,iBAAA,MAAA,EAAA,CAAA,CALA;AAMA,UAAA,QAAA,EAAA;AANA,SADA,EASA;AACA,UAAA,IAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,KAFA;AAGA,UAAA,GAAA,EAAA,KAHA;AAIA,UAAA,KAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAJA;AAKA,UAAA,OAAA,EAAA,iBAAA,MAAA,EAAA,CAAA,CALA;AAMA,UAAA,QAAA,EAAA;AANA,SATA,CADA;AAmBA,QAAA,MAAA,EAAA;AAnBA,OAAA;AAqBA,MAAA,OAAA,CAAA,SAAA,CAAA,MAAA;AACA,MAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,YAAA;AACA,QAAA,OAAA,CAAA,MAAA;AACA,OAFA;AAGA,KAxJA;AAyJA,IAAA,YAzJA,0BAyJA;AAAA,UAAA,IAAA,uEAAA,EAAA;AAAA,UAAA,GAAA;AAAA,UAAA,MAAA,uEAAA,EAAA;AACA,UAAA,SAAA,GAAA,GAAA,GAAA,GAAA,CAAA,YAAA,GAAA,CAAA;AACA,UAAA,QAAA,GAAA,GAAA,GAAA,GAAA,CAAA,WAAA,GAAA,CAAA,CAFA,CAGA;;AACA,UAAA,WAAA,GAAA,SAAA,GAAA,QAAA,GAAA,GAAA,GAAA,QAAA,GAAA,GAAA,GAAA,SAAA,CAJA,CAKA;;AACA,UAAA,YAAA,GAAA,MAAA,CAAA,MAAA,CACA;AACA,QAAA,OAAA,EAAA,CADA;AACA;AACA,QAAA,QAAA,EAAA,WAFA;AAEA;AACA,QAAA,SAAA,EAAA,WAHA;AAGA;AACA,QAAA,MAAA,EAAA,EAJA,CAIA;;AAJA,OADA,EAOA,MAPA,CAAA;;AASA,UAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,QAAA,YAAA,CAAA,OAAA,GAAA,EAAA;AACA;;AACA,UAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,QAAA,YAAA,CAAA,OAAA,GAAA,EAAA;AACA;;AACA,UAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,QAAA,YAAA,CAAA,OAAA,GAAA,EAAA;AACA,OAvBA,CAwBA;;;AACA,MAAA,YAAA,CAAA,SAAA,GAAA,YAAA,CAAA,SAAA,GAAA,YAAA,CAAA,MAAA,CAzBA,CA0BA;;AACA,UAAA,YAAA,GAAA,YAAA,CAAA,QAAA,GAAA,CAAA,CA3BA,CA4BA;;AACA,UAAA,UAAA,GAAA,EAAA,CA7BA,CA8BA;;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA,IAAA,CAAA;AAAA,OAAA,CAAA,CA/BA,CAgCA;;AACA,UAAA,UAAA,GACA,QAAA,CAAA,MAAA,GAAA,CAAA,IACA,QAAA,CAAA,MAAA,CAAA,UAAA,IAAA,EAAA,IAAA,EAAA;AACA,eAAA,MAAA,CAAA,IAAA,IAAA,CAAA,CAAA,GAAA,MAAA,CAAA,IAAA,IAAA,CAAA,CAAA;AACA,OAFA,CAFA,CAjCA,CAsCA;;AACA,UAAA,YAAA,GAAA,EAAA;AACA,UAAA,aAAA,GAAA,CAAA,GAAA,CAAA,CAxCA,CAyCA;;AACA,MAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA,GAAA,EAAA;AACA,YAAA,QAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA,CAAA;;AACA,YAAA,QAAA,EAAA;AACA,UAAA,aAAA,CAAA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA,CAAA,CAAA,EADA,CACA;AACA,SAJA,CAKA;;;AACA,YAAA,KAAA,GACA,IAAA,CAAA,KAAA,CAAA,IAAA,GAAA,UAAA,GAAA,YAAA,CAAA,SAAA,GAAA,IAAA,IAAA,IADA;AAEA,QAAA,YAAA,CAAA,IAAA,CAAA,KAAA;AACA,OATA,EA1CA,CAoDA;;AACA,UAAA,cAAA,GAAA,CAAA;AACA,UAAA,mBAAA,GAAA,EAAA,CAtDA,CAuDA;;AACA,UAAA,aAAA,GAAA,YAAA,GAAA,YAAA,CAAA,SAAA;AACA,MAAA,YAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,cAAA,GAAA,cAAA,GAAA,IAAA;AACA,QAAA,mBAAA,CAAA,IAAA,CAAA,cAAA;AACA,OAHA,EAzDA,CA6DA;;AACA,UAAA,mBAAA,GAAA,mBAAA,CAAA,mBAAA,CAAA,MAAA,GAAA,CAAA,CAAA,IAAA,CAAA,CA9DA,CA+DA;;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA;AACA,YAAA,MAAA,GAAA,EAAA;AACA,YAAA,sBAAA,GAAA,mBAAA,CAAA,KAAA,CAAA;AACA,YAAA,0BAAA,GAAA,mBAAA,CAAA,KAAA,GAAA,CAAA,CAAA,IAAA,CAAA;AACA,YAAA,gBAAA,GAAA,aAAA,CAAA,KAAA,CAAA;AACA,YAAA,oBAAA,GAAA,aAAA,CAAA,KAAA,GAAA,CAAA,CAAA,IAAA,CAAA,CALA,CAMA;;AACA,YAAA,KAAA,KAAA,CAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CACA,CAAA,CAAA,EAAA,CAAA,CADA,EAEA,CACA,CAAA,aAAA,IACA,sBAAA,GAAA,YAAA,CAAA,OAAA,GAAA,gBADA,CADA,EAGA,sBAAA,GAAA,YAAA,CAAA,OAAA,GAAA,gBAHA,CAFA,EAOA,CAAA,CAAA,EAAA,sBAAA,CAPA,EAQA,CACA,aAAA,IACA,sBAAA,GAAA,YAAA,CAAA,OAAA,GAAA,gBADA,CADA,EAGA,sBAAA,GAAA,YAAA,CAAA,OAAA,GAAA,gBAHA,CARA;AAcA,SAfA,MAeA;AACA,UAAA,MAAA,CAAA,IAAA,CACA,CAAA,CAAA,EAAA,0BAAA,CADA,EAEA,CACA,CAAA,aAAA,IACA,0BAAA,GACA,YAAA,CAAA,OAAA,GAAA,oBAFA,CADA,EAIA,0BAAA,GAAA,YAAA,CAAA,OAAA,GAAA,oBAJA,CAFA,EAQA,CACA,CAAA,aAAA,IACA,sBAAA,GAAA,YAAA,CAAA,OAAA,GAAA,gBADA,CADA,EAGA,sBAAA,GAAA,YAAA,CAAA,OAAA,GAAA,gBAHA,CARA,EAaA,CAAA,CAAA,EAAA,sBAAA,CAbA,EAcA,CACA,aAAA,IACA,sBAAA,GAAA,YAAA,CAAA,OAAA,GAAA,gBADA,CADA,EAGA,sBAAA,GAAA,YAAA,CAAA,OAAA,GAAA,gBAHA,CAdA,EAmBA,CACA,aAAA,IACA,0BAAA,GACA,YAAA,CAAA,OAAA,GAAA,oBAFA,CADA,EAIA,0BAAA,GAAA,YAAA,CAAA,OAAA,GAAA,oBAJA,CAnBA;AA0BA;;AACA,QAAA,UAAA,CAAA,IAAA,CAAA;AACA,UAAA,IAAA,EAAA,SADA;AAEA,UAAA,CAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA;AACA,YAAA,MAAA,EAAA;AADA,WAHA;AAMA,UAAA,IAAA,EAAA,IAAA,CAAA,IANA;AAOA,UAAA,KAAA,EAAA,IAAA,CAAA;AAPA,SAAA;AASA,OA3DA,EAhEA,CA4HA;;AACA,MAAA,UAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,UADA;AAEA,QAAA,KAAA,EAAA;AACA,UAAA,MAAA,EAAA,CACA,CAAA,CAAA,EAAA,CAAA,CADA,EAEA,CAAA,CAAA,EAAA,mBAAA,CAFA;AADA,SAFA;AAQA,QAAA,KAAA,EAAA;AACA,UAAA,MAAA,EAAA,SADA;AAEA,UAAA,OAAA,EAAA,GAFA;AAGA,UAAA,SAAA,EAAA;AAHA,SARA;AAaA,QAAA,CAAA,EAAA;AAbA,OAAA,EA7HA,CA4IA;;AACA,aAAA,UAAA;AACA;AAvSA;AA7CA,CAAA", "sourcesContent": ["<template>\r\n  <div class=\"water-eval-container\" style=\"position: relative\">\r\n    <div class=\"effectiveness-charts\" id=\"effectiveness-charts\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport \"echarts-gl\";\r\nexport default {\r\n  name: \"cityGreenLand\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      electrictTotal: \"\",\r\n      isFullscreen: false,\r\n      optionData: [\r\n        {\r\n          name: \"外购绿电\",\r\n          value: 30000,\r\n        },\r\n        {\r\n          name: \"外购火电\",\r\n          value: 22116,\r\n        },\r\n        {\r\n          name: \"自有新能源发电\",\r\n          value: 16616,\r\n        },\r\n      ],\r\n      nowStep: 0,\r\n      maxStep: 35,\r\n      height: 95,\r\n    };\r\n  },\r\n  props: {\r\n    examineList: {\r\n      type: Array,\r\n    },\r\n  },\r\n\r\n  watch: {\r\n    examineList: {\r\n      immediate: true,\r\n      handler(newVal, oldVal) {\r\n        if (newVal == undefined || newVal == null || newVal == \"\") return;\r\n        this.$nextTick(() => {\r\n          this.init();\r\n        });\r\n      },\r\n      deep: true, // 深度监听\r\n    },\r\n  },\r\n\r\n  methods: {\r\n    init() {\r\n      // 原始数据\r\n      let data = this.examineList;\r\n      // 将数据根据从小到大排序\r\n      // let newdata = sortObject(data);\r\n      // 图例数据\r\n      let lengthData = [];\r\n      // 返回数据\r\n      let resultData = data.map((item, index) => {\r\n        let len = item.num.toString().length;\r\n        let graw;\r\n        if (len * 1 < 2) {\r\n          graw = 100;\r\n        } else if (len * 1 < 3) {\r\n          graw = 114;\r\n        } else if (len * 1 < 4) {\r\n          graw = 124;\r\n        }\r\n        lengthData.push({\r\n          type: \"group\",\r\n          top: index * 35,\r\n          scale: [1, 1],\r\n          children: [\r\n            {\r\n              type: \"circle\",\r\n              shape: {\r\n                cx: 0,\r\n                cy: 9,\r\n                r: 5,\r\n              },\r\n              style: {\r\n                fill:\r\n                  index === 0\r\n                    ? \"#ACFFF0\"\r\n                    : index === 1\r\n                    ? \"#00ECC0\"\r\n                    : index === 2\r\n                    ? \"#00D96C\"\r\n                    : \"#006452\",\r\n              },\r\n            },\r\n            {\r\n              type: \"text\",\r\n              style: {\r\n                text: item.name,\r\n                fontFamily: \"PingFangSC-Regular\",\r\n                fill: \"#fff\",\r\n                fontSize: 14,\r\n                x: 14,\r\n                y: 2,\r\n              },\r\n            },\r\n            {\r\n              type: \"text\",\r\n              name: item.name,\r\n              style: {\r\n                text: item.num,\r\n                fill: \"#fff\",\r\n                fontFamily: \"Alibaba-PuHuiTi-Bold\",\r\n                fontSize: 18,\r\n                x: 85,\r\n                y: -2,\r\n              },\r\n            },\r\n            {\r\n              type: \"text\",\r\n              style: {\r\n                text: \"个\",\r\n                fontFamily: \"PingFangSC-Regular\",\r\n                fill: \"#fff\",\r\n                fontSize: 14,\r\n                x: graw,\r\n                y: 2,\r\n              },\r\n            },\r\n          ],\r\n        });\r\n\r\n        if (index === 0) {\r\n          return {\r\n            style: {\r\n              fill: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [\r\n                { offset: 0, color: \"#ACFFF0\" },\r\n                { offset: 1, color: \"#00ECC0\" },\r\n              ]),\r\n            },\r\n            ...item,\r\n          };\r\n        } else if (index === 1) {\r\n          return {\r\n            style: {\r\n              fill: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [\r\n                { offset: 0, color: \"#00D96C\" },\r\n                { offset: 1, color: \"#00ECC0\" },\r\n              ]),\r\n            },\r\n            ...item,\r\n          };\r\n        } else if (index === 2) {\r\n          return {\r\n            style: {\r\n              fill: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [\r\n                { offset: 0, color: \"#00D96C\" },\r\n                { offset: 1, color: \"#006452\" },\r\n              ]),\r\n            },\r\n            ...item,\r\n          };\r\n        } else if (index === 3) {\r\n          return {\r\n            style: {\r\n              fill: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [\r\n                { offset: 0, color: \"#006452\" },\r\n                { offset: 1, color: \"#006452\" },\r\n              ]),\r\n            },\r\n            ...item,\r\n          };\r\n        }\r\n      });\r\n      // 获取计算的数据\r\n      let getData = this.pyramidChart(\r\n        resultData,\r\n        document.getElementById(\"effectiveness-charts\")\r\n      );\r\n      let myChart = this.$echarts.init(document.getElementById(\"effectiveness-charts\"));\r\n      let option = {\r\n        graphic: [\r\n          {\r\n            type: \"group\",\r\n            left: \"8%\",\r\n            top: \"16%\",\r\n            scale: [0.9, 0.9],\r\n            onclick: function (params) {},\r\n            children: getData,\r\n          },\r\n          {\r\n            type: \"group\",\r\n            left: \"60%\",\r\n            top: \"20%\",\r\n            scale: [1, 1],\r\n            onclick: function (params) {},\r\n            children: lengthData,\r\n          },\r\n        ],\r\n        series: [],\r\n      };\r\n      myChart.setOption(option);\r\n      window.addEventListener(\"resize\", function () {\r\n        myChart.resize();\r\n      });\r\n    },\r\n    pyramidChart(data = [], dom, option = {}) {\r\n      let domHeight = dom ? dom.clientHeight : 0;\r\n      let domWidth = dom ? dom.clientWidth : 0;\r\n      // 默认获取一个正方形空间\r\n      let maxDistance = domHeight > domWidth / 2.3 ? domWidth / 2.3 : domHeight;\r\n      // 合并设置\r\n      let resultOption = Object.assign(\r\n        {\r\n          slanted: 1, // 每层底部的倾斜度\r\n          maxWidth: maxDistance, // 金字塔最大宽度\r\n          maxHeight: maxDistance, // 金字塔最大高度\r\n          offset: 35, //偏差\r\n        },\r\n        option\r\n      );\r\n      if (data.length === 1) {\r\n        resultOption.slanted = 50;\r\n      }\r\n      if (data.length === 2) {\r\n        resultOption.slanted = 25;\r\n      }\r\n      if (data.length === 3) {\r\n        resultOption.slanted = 10;\r\n      }\r\n      // 减去多余的误差边距\r\n      resultOption.maxHeight = resultOption.maxHeight - resultOption.offset;\r\n      // 一半最大宽度,用于计算左右边距\r\n      let halfMaxWidth = resultOption.maxWidth / 2;\r\n      // 数据最终\r\n      let resultData = [];\r\n      // 数据值 数组\r\n      let dataNums = data.map((item) => item.value || 0);\r\n      // 计算数据总和\r\n      let dataNumSum =\r\n        dataNums.length > 0 &&\r\n        dataNums.reduce(function (prev, curr) {\r\n          return Number(prev || 0) + Number(curr || 0);\r\n        });\r\n      // 中间数据点坐标数组 根据长度比值算出\r\n      let midlinePoint = [];\r\n      let multipleLayer = [0.6];\r\n      // 计算倍数等基础数据\r\n      dataNums.forEach((item, index, arr) => {\r\n        let itemNext = arr[index + 1];\r\n        if (itemNext) {\r\n          multipleLayer.push(itemNext / dataNums[0]); // 计算倍数\r\n        }\r\n        // 计算点坐标 长度\r\n        let point =\r\n          Math.round((item / dataNumSum) * resultOption.maxHeight * 1000) / 1000;\r\n        midlinePoint.push(point);\r\n      });\r\n      // 三角形的高度\r\n      let triangleHeight = 0;\r\n      let triangleHeightLayer = [];\r\n      // 三角形tan角度\r\n      let triangleRatio = halfMaxWidth / resultOption.maxHeight;\r\n      midlinePoint.forEach((item) => {\r\n        triangleHeight = triangleHeight + item;\r\n        triangleHeightLayer.push(triangleHeight);\r\n      });\r\n      // 中间数据点 最后的数据长度\r\n      let midlinePointFinally = triangleHeightLayer[triangleHeightLayer.length - 1] || 0;\r\n      // 开始拼接数据\r\n      data.forEach((item, index) => {\r\n        let arrObj = [];\r\n        let triangleHeightLayerOne = triangleHeightLayer[index];\r\n        let triangleHeightLayerOneLast = triangleHeightLayer[index - 1] || 0;\r\n        let multipleLayerOne = multipleLayer[index];\r\n        let multipleLayerOneLast = multipleLayer[index - 1] || 0;\r\n        // 第一层数据单独处理\r\n        if (index === 0) {\r\n          arrObj.push(\r\n            [0, 0],\r\n            [\r\n              -triangleRatio *\r\n                (triangleHeightLayerOne - resultOption.slanted * multipleLayerOne),\r\n              triangleHeightLayerOne - resultOption.slanted * multipleLayerOne,\r\n            ],\r\n            [0, triangleHeightLayerOne],\r\n            [\r\n              triangleRatio *\r\n                (triangleHeightLayerOne - resultOption.slanted * multipleLayerOne),\r\n              triangleHeightLayerOne - resultOption.slanted * multipleLayerOne,\r\n            ]\r\n          );\r\n        } else {\r\n          arrObj.push(\r\n            [0, triangleHeightLayerOneLast],\r\n            [\r\n              -triangleRatio *\r\n                (triangleHeightLayerOneLast -\r\n                  resultOption.slanted * multipleLayerOneLast),\r\n              triangleHeightLayerOneLast - resultOption.slanted * multipleLayerOneLast,\r\n            ],\r\n            [\r\n              -triangleRatio *\r\n                (triangleHeightLayerOne - resultOption.slanted * multipleLayerOne),\r\n              triangleHeightLayerOne - resultOption.slanted * multipleLayerOne,\r\n            ],\r\n            [0, triangleHeightLayerOne],\r\n            [\r\n              triangleRatio *\r\n                (triangleHeightLayerOne - resultOption.slanted * multipleLayerOne),\r\n              triangleHeightLayerOne - resultOption.slanted * multipleLayerOne,\r\n            ],\r\n            [\r\n              triangleRatio *\r\n                (triangleHeightLayerOneLast -\r\n                  resultOption.slanted * multipleLayerOneLast),\r\n              triangleHeightLayerOneLast - resultOption.slanted * multipleLayerOneLast,\r\n            ]\r\n          );\r\n        }\r\n        resultData.push({\r\n          type: \"polygon\",\r\n          z: 1,\r\n          shape: {\r\n            points: arrObj,\r\n          },\r\n          name: item.name,\r\n          style: item.style,\r\n        });\r\n      });\r\n      // 添加线\r\n      resultData.push({\r\n        type: \"polyline\",\r\n        shape: {\r\n          points: [\r\n            [0, 0],\r\n            [0, midlinePointFinally],\r\n          ],\r\n        },\r\n        style: {\r\n          stroke: \"#f2f2f2\",\r\n          opacity: 0.2,\r\n          lineWidth: 1,\r\n        },\r\n        z: 2,\r\n      });\r\n      // 返回\r\n      return resultData;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n#effectiveness-charts {\r\n  height: 300px;\r\n  width: 53rem;\r\n  letter-spacing: 0.1rem;\r\n}\r\n/deep/ canvas {\r\n  z-index: 9 !important;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/view/carbon/assess/assessReport/components"}]}