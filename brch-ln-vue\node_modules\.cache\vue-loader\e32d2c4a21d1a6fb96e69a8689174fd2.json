{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\energyview\\modifyReport.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\energyview\\modifyReport.vue", "mtime": 1754285403042}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["modifyReport.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "modifyReport.vue", "sourceRoot": "src/view/carbon/discharge/energyview", "sourcesContent": ["<template>\r\n  <div id=\"modularForm\">\r\n    <header>\r\n      <el-button type=\"success\" @click=\"goBack\">返回</el-button>\r\n      能源数据汇总查看/修改记录\r\n    </header>\r\n\r\n    <div id=\"modularForm_bg\" style=\"overflow-x: hidden;\">\r\n      <ul v-for=\"item in list\" :key=\"item.id\">\r\n        <li style=\"margin: 2rem;\">\r\n          <ul style=\"width: 50%; display: flex; color: #ffffff; justify-content: space-between; font-size: 1.5rem; line-height: 6rem; height: 6rem;\">\r\n            <li style=\"width: 30%;\"><span>{{ item.operateType == 1 ? item.reportTime:item.createTime }}</span></li>\r\n            <li style=\"width: 30%;\"><span>操作人：{{ item.createName }}</span></li>\r\n            <li style=\"width: 20%;\"><span>{{ item.operateType == 1 ?'上报':'修改'}} </span></li>\r\n            <li style=\"width: 20%;\">\r\n              <el-button type=\"primary\" @click.prevent=\"getObj(item)\">{{ !item.show?'查看数据':'收起数据' }}</el-button>\r\n              <!-- <el-button type=\"primary\" @click.prevent=\"getObj(item)\">{{ item.names }}</el-button> -->\r\n            </li>\r\n          </ul>\r\n          <div v-show=\"item.show == true\" style=\"color: #ffffff;\">\r\n\r\n\t<avue-crud v-show=\"item.show == true\"\r\n            ref=\"crud\"\r\n            :data=\"item.tableData\"\r\n            :table-loading=\"tableLoading\"\r\n            :option=\"tableOption\"\r\n          :cell-class-name=\"dataStyle\"\r\n            >\r\n            <!-- <template slot=\"groupDataForm\" slot-scope=\"{ row,index}\">\r\n                  <el-input :class=\"[row.groupEdit ? 'el_input_class' : '']\"\r\n                            v-model=\"row.groupData\"\r\n                            :disabled=\"disabledList.groupList.indexOf(index) == -1 ? false : true\"\r\n                            :type=\"noInputList.groupList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                            @change=\"handleGroupChange(row,index)\"\r\n                            @keydown.native=\"inputLimit\"\r\n                            :placeholder=\"formulaList.groupList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                            @mousewheel.native.prevent\r\n                  ></el-input>\r\n              </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                  <el-input :class=\"[row.groupEdit ? 'el_input_class' : '']\"\r\n                  ></el-input>\r\n              </template>\r\n              <!-- <template slot=\"stockDataForm\" slot-scope=\"{ row, index}\">\r\n                <el-input :class=\"[row.stockEdit ? 'el_input_class' : '']\"\r\n                          :disabled=\"disabledList.stockList.indexOf(index) == -1 ? false : true\"\r\n                          :type=\"noInputList.stockList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                          @change=\"handleStockChange(row,index)\"\r\n                          @keydown.native=\"inputLimit\"\r\n                          :placeholder=\"formulaList.stockList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                          @mousewheel.native.prevent\r\n                ></el-input>\r\n              </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                <el-input :class=\"[row.stockEdit ? 'el_input_class' : '']\"\r\n                ></el-input>\r\n              </template>\r\n              <!-- <template slot=\"largeDataForm\" slot-scope=\"{ row, index}\">\r\n                <el-input :class=\"[row.largeEdit ? 'el_input_class' : '']\"\r\n                          v-model=\"row.largeData\"\r\n                          :disabled=\"disabledList.largeList.indexOf(index) == -1 ? false : true\"\r\n                          :type=\"noInputList.largeList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                          @change=\"handleLargeChange(row,index)\"\r\n                          @keydown.native=\"inputLimit\"\r\n                          :placeholder=\"formulaList.largeList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                          @mousewheel.native.prevent\r\n                ></el-input>\r\n              </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                <el-input :class=\"[row.largeEdit ? 'el_input_class' : '']\"\r\n                ></el-input>\r\n              </template>\r\n              <!-- <template slot=\"mediumDataForm\" slot-scope=\"{ row, index }\">\r\n                <el-input :class=\"[row.mediumEdit ? 'el_input_class' : '']\"\r\n                          v-model=\"row.mediumData\"\r\n                          :disabled=\"disabledList.mediumList.indexOf(index) == -1 ? false : true\"\r\n                          :type=\"noInputList.mediumList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                          @change=\"handleMediumChange(row,index)\"\r\n                          @keydown.native=\"inputLimit\"\r\n                          :placeholder=\"formulaList.mediumList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                          @mousewheel.native.prevent\r\n                ></el-input>\r\n              </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                <el-input :class=\"[row.mediumEdit ? 'el_input_class' : '']\"\r\n                ></el-input>\r\n              </template>\r\n              <!-- <template slot=\"mobileDataForm\" slot-scope=\"{ row, index }\">\r\n                <el-input :class=\"[row.mobileEdit ? 'el_input_class' : '']\"\r\n                          v-model=\"row.mobileData\"\r\n                          :disabled=\"disabledList.mobileList.indexOf(index) == -1 ? false : true\"\r\n                          :type=\"noInputList.mobileList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                          @change=\"handleMobileChange(row,index)\"\r\n                          @keydown.native=\"inputLimit\"\r\n                          :placeholder=\"formulaList.mobileList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                          @mousewheel.native.prevent\r\n                ></el-input>\r\n            </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                <el-input :class=\"[row.mobileEdit ? 'el_input_class' : '']\"\r\n                ></el-input>\r\n            </template>\r\n          </avue-crud>\r\n          </div>\r\n\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\n// import emptyStrate from \"@/components/empty\";\r\nimport delects from \"@/components/delects/index\";\r\n// import {mapGetters} from \"vuex\";\r\n// import {dateFormat} from \"@/util/date\";\r\n// import { downloadFile } from \"@/util/ruoyi\";\r\nimport {energyList, energyUpdateList,getEnergyUpdateRecordList,energyDataUpdateList} from \"@/api/carbon/discharge/energy\";\r\nimport jituanEnergyReportLog from \"./jituanEnergyReportLog\";\r\nexport default {\r\n  components: {\r\n    // emptyStrate,\r\n    delects,\r\n    jituanEnergyReportLog,\r\n  },\r\n  data() {\r\n    return {\r\n      objName: '查看数据',\r\n      tableLoading: false,\r\n      isShow: false,\r\n      list: [],\r\n      tableOption: {\r\n        border: false,\r\n        index: false,\r\n        height: \"auto\",\r\n        calcHeight: 35,\r\n        stripe: true,\r\n        menuAlign: \"center\",\r\n        align: \"center\",\r\n        refreshBtn: false,\r\n        showClomnuBtn: false,\r\n        searchMenuSpan: 4,\r\n        searchSize: \"mini\",\r\n        card: true,\r\n        addBtn: false,\r\n        editBtn: false,\r\n        delBtn: false,\r\n        columnBtn: false,\r\n        searchBtn: false,\r\n        emptyBtn: false,\r\n        menu: false,\r\n        dialogWidth: 500,\r\n        dialogMenuPosition: \"center\",\r\n        dialogCustomClass: \"singleRowDialog\",\r\n        labelWidth: 100,\r\n        column: [\r\n          {\r\n            label: \"指标\",\r\n            prop: \"indicatorName\",\r\n            overHidden: true,\r\n            width: 750,\r\n            align: \"left\"\r\n          },\r\n          {\r\n            label: \"集团\",\r\n            prop: \"groupData\",\r\n            formatter:(val,value,label)=>{\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: false,\r\n          },\r\n          {\r\n            label: \"股份\",\r\n            prop: \"stockData\",\r\n            formatter:(val,value,label)=>{\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: false,\r\n          },\r\n          {\r\n            label: '数据中心',\r\n            children: [{\r\n              label: '大型',\r\n              prop: 'largeData',\r\n              formatter:(val,value,label)=>{\r\n                return this.formatDisplayData(value);\r\n              },\r\n              overHidden: true,\r\n              cell: true,\r\n              slot: true\r\n            }, {\r\n              label: '中小型',\r\n              prop: 'mediumData',\r\n              formatter:(val,value,label)=>{\r\n                return this.formatDisplayData(value);\r\n              },\r\n              cell: true,\r\n              slot: true,\r\n              overHidden: true\r\n            }]\r\n          },\r\n          {\r\n            label: \"移动业务\",\r\n            prop: \"mobileData\",\r\n            formatter:(val,value,label)=>{\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: false,\r\n          },\r\n        ],\r\n      },\r\n      companyName: undefined,\r\n      tableData: [],\r\n      // 原表格数据\r\n      originalData: [],\r\n      // 需改的下标数组\r\n      updateList:[],\r\n      energyData: [],\r\n      disabledList: {\r\n        groupList: [0, 4, 5, 7, 18, 23, 27, 57],\r\n        stockList: [0, 4, 5, 7, 18, 23, 27, 57],\r\n        largeList: [0, 4, 5, 6, 7, 8, 9, 10, 17, 18, 19, 20, 23, 27, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        mediumList: [0, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 23, 27, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        mobileList: [0, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 24, 25, 28, 29, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57],\r\n      },\r\n      formulaList: {\r\n        groupList: [0, 4, 5, 7, 18, 23, 27],\r\n        stockList: [0, 4, 5, 7, 18, 23, 27],\r\n        largeList: [0, 4, 23, 27],\r\n        mediumList: [0, 4, 11, 12, 13, 14, 15, 16, 23, 27],\r\n        mobileList: [0, 4, 7, 8, 9, 10],\r\n      },\r\n      noInputList: {\r\n        groupList: [57],\r\n        stockList: [57],\r\n        largeList: [5, 6, 7, 8, 9, 10, 17, 18, 19, 20, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        mediumList: [5, 6, 7, 8, 9, 10, 17, 18, 19, 20, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        mobileList: [2, 5, 6, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 24, 25, 28, 29, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57],\r\n      },\r\n      requireInputList: {\r\n        groupList: [1, 2, 3, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38],\r\n        stockList: [1, 2, 3, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38],\r\n        largeList: [1, 2, 3, 6, 11, 12, 13, 14, 15, 16, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 57],\r\n        mediumList: [1, 2, 3, 6, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 57],\r\n        mobileList: [1, 3, 21, 22, 23, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37],\r\n      },\r\n      noRequireInputList: {\r\n        groupList: [39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        stockList: [39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n      },\r\n      backgroundList: {\r\n        groupList: [],\r\n        stockList: [],\r\n        largeList: [],\r\n        mediumList: [],\r\n        mobileList: [],\r\n      },\r\n      formData: {\r\n        reportTime: undefined,\r\n        companyId: undefined,\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n\r\n  },\r\n  created() {\r\n    if (this.$route.query) {\r\n\r\n      this.formData.reportTime = this.$route.query.reportTime;\r\n      this.companyName = this.$route.query.companyName;\r\n      this.formData.companyId = this.$route.query.companyId;\r\n      // this.getList();\r\n      this.getUpdRecordList()\r\n    }\r\n  },\r\n  methods: {\r\ndataStyle({row,column,rowIndex,columnIndex}){\r\n      if(columnIndex === 1 && row.groupEdit){\r\n        return 'cell-color';\r\n      }\r\n      if(columnIndex === 2 && row.stockEdit){\r\n        return 'cell-color';\r\n      }\r\n      if(columnIndex === 3 && row.largeEdit){\r\n        return 'cell-color';\r\n      }\r\n      if(columnIndex === 4 && row.mediumEdit){\r\n        return 'cell-color';\r\n      }\r\n      if(columnIndex === 5 && row.mobileEdit){\r\n        return 'cell-color';\r\n      }\r\n    },\r\n    getUpdRecordList() {\r\n      getEnergyUpdateRecordList(Object.assign({companyId:this.formData.companyId,reportTime:this.formData.reportTime})).then((res) => {\r\n        if (res.data.code == 200) {\r\n          this.list = res.data.data\r\n          this.list.forEach(items => {\r\n            items.names = '查看数据';\r\n          })\r\n        }\r\n      });\r\n    },\r\n\r\n    //数据禁止输入e\r\n    inputLimit(event) {\r\n      if (event.key === 'e' || event.key === '-') {\r\n        event.returnValue = false;\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    goBack() {\r\n      this.$router.push({\r\n        path: \"/dataView/energy/index\",\r\n        query: {\r\n          reportTime: this.formData.reportTime,\r\n        },\r\n      });\r\n    },\r\n    // 查询默认列表\r\n    getList(item) {\r\n      this.tableLoading = true;\r\n      energyList(this.formData).then((res) => {\r\n        if (res.data.code == 200) {\r\n          this.tableLoading = false;\r\n          item.tableData = res.data.data;\r\n          this.checkIndicatorInput();\r\n          this.tableData.forEach((item) => {\r\n            item.unit = item.unitDescription + \"(\" + item.unitName + \")\";\r\n            item.$cellEdit = true;\r\n            item.groupData = item.groupData == null ? \"\\\\\" : item.groupData;\r\n            item.stockData = item.stockData == null ? \"\\\\\" : item.stockData;\r\n            item.largeData = item.largeData == null ? \"\\\\\" : item.largeData;\r\n            item.mediumData = item.mediumData == null ? \"\\\\\" : item.mediumData;\r\n            item.mobileData = item.mobileData == null ? \"\\\\\" : item.mobileData;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 查询修改的列表\r\n    getEnergyUpdateRecordList(item) {\r\n      this.tableLoading = true;\r\n      this.formData.updateRecordId = item.id\r\n      energyDataUpdateList(this.formData).then((res) => {\r\n        if (res.data.code == 200) {\r\n          this.tableLoading = false;\r\n          item.tableData = res.data.data;\r\n          this.checkIndicatorInput();\r\n          this.tableData.forEach((item) => {\r\n            item.unit = item.unitDescription + \"(\" + item.unitName + \")\";\r\n            item.$cellEdit = true;\r\n            item.groupData = item.groupData == null ? \"\\\\\" : item.groupData;\r\n            item.stockData = item.stockData == null ? \"\\\\\" : item.stockData;\r\n            item.largeData = item.largeData == null ? \"\\\\\" : item.largeData;\r\n            item.mediumData = item.mediumData == null ? \"\\\\\" : item.mediumData;\r\n            item.mobileData = item.mobileData == null ? \"\\\\\" : item.mobileData;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    saveReportData() {\r\n      if (this.validReportData() == false) {\r\n        this.alertError(\"请输入全部数据\");\r\n        return;\r\n      }\r\n      let data = {}\r\n      let saveList = [];\r\n      this.tableData.forEach((item) => {\r\n        let saveItem = {};\r\n        saveItem.reportTime = this.formData.reportTime;\r\n        saveItem.companyId = this.formData.companyId;\r\n        saveItem.reportFlag = \"2\";\r\n        saveItem.id = item.id;\r\n        saveItem.indicatorName = item.indicatorName;\r\n        saveItem.groupData = item.groupData == '\\\\' || item.groupData == '/' ? null : item.groupData;\r\n        saveItem.stockData = item.stockData == '\\\\' || item.stockData == '/' ? null : item.stockData;\r\n        saveItem.largeData = item.largeData == '\\\\' || item.largeData == '/' ? null : item.largeData;\r\n        saveItem.mediumData = item.mediumData == '\\\\' || item.mediumData == '/' ? null : item.mediumData;\r\n        saveItem.mobileData = item.mobileData == '\\\\' || item.mobileData == '/' ? null : item.mobileData;\r\n        saveList.push(saveItem);\r\n      });\r\n      data.companyId = this.formData.companyId\r\n      data.dischargeEnergyIndicatorVos = saveList\r\n      data.dischargeDataEnergyUpdateLogList = this.updateList\r\n      energyUpdateList(data).then((res) => {\r\n        if (res.data.code == 200) {\r\n          this.alertSuccess(\"操作成功\");\r\n          this.goBack();\r\n        }\r\n      });\r\n    },\r\n    //校验上报数据\r\n    validReportData() {\r\n      let ret = true;\r\n      this.backgroundList.groupList = [];\r\n      this.backgroundList.stockList = [];\r\n      this.backgroundList.largeList = [];\r\n      this.backgroundList.mediumList = [];\r\n      this.backgroundList.mobileList = [];\r\n      for (let i = 0; i < this.tableData.length; i++) {\r\n        if (this.tableData[i].groupData == undefined\r\n          || this.tableData[i].groupData == null\r\n          || this.tableData[i].groupData.length == 0) {\r\n          if (this.requireInputList.groupList.indexOf(i) > -1) {\r\n            this.backgroundList.groupList.push(i);\r\n            ret = false;\r\n          } else {\r\n            this.tableData[i].groupData = \"0\";\r\n          }\r\n        }\r\n        if (this.tableData[i].stockData == undefined\r\n          || this.tableData[i].stockData == null\r\n          || this.tableData[i].stockData.length == 0) {\r\n          if (this.requireInputList.stockList.indexOf(i) > -1) {\r\n            this.backgroundList.stockList.push(i);\r\n            ret = false;\r\n          } else {\r\n            this.tableData[i].stockData = \"0\";\r\n          }\r\n          ret = false;\r\n        }\r\n        if (this.tableData[i].largeData == undefined\r\n          || this.tableData[i].largeData == null\r\n          || this.tableData[i].largeData.length == 0) {\r\n          this.backgroundList.largeList.push(i);\r\n          ret = false;\r\n        }\r\n        if (this.tableData[i].mediumData == undefined\r\n          || this.tableData[i].mediumData == null\r\n          || this.tableData[i].mediumData.length == 0) {\r\n          this.backgroundList.mediumList.push(i);\r\n          ret = false;\r\n        }\r\n        if (this.tableData[i].mobileData == undefined\r\n          || this.tableData[i].mobileData == null\r\n          || this.tableData[i].mobileData.length == 0) {\r\n          this.backgroundList.mobileList.push(i);\r\n          ret = false;\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n    // 保存修改记录的方法\r\n    saveChangeIndex(data,index,indexName,num,id){\r\n      const predata = this.originalData[index][indexName]\r\n    // 若是修改了数据，则记录下标\r\n    if(this.areFloatsEqual(data,this.originalData[index][indexName])){\r\n        const filteredArray = this.updateList.filter(obj => obj.id !== id);\r\n        this.updateList = filteredArray\r\n      } else {\r\n        // 使用findIndex()方法查找数组中具有特定id的对象的索引\r\n        const index = this.updateList.findIndex(obj => obj.id === id);\r\n        // 如果索引大于等于0，则表示找到了对象，可以删除\r\n        if (index >= 0) {\r\n            // 使用splice()方法删除数组中指定索引的对象\r\n            this.updateList.splice(index, 1);\r\n        }\r\n        this.updateList.push({energyId:id,num:num,previousData:predata,nowData:data})\r\n      }\r\n    },\r\n\r\n    // handleGroupChange(row,index) {\r\n    //   this.saveChangeIndex(row.groupData,index,'groupData',1,row.id)\r\n    //   if (this.noInputList.groupList.indexOf(index) == -1 && this.formulaList.groupList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['groupData'].toString()) < 0) {\r\n    //       this.tableData[index]['groupData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['groupData'] != undefined && this.tableData[index]['groupData'] != '') {\r\n    //       let j = this.backgroundList.groupList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.groupList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     if (index == 6 || index == 11 || index == 17) {\r\n    //       this.tableData[5]['groupData'] = (this.cellToNumber(this.tableData[6]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[7]['groupData']) + this.cellToNumber(this.tableData[11]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[17]['groupData'])).toString();\r\n    //       this.tableData[4]['groupData'] = (this.cellToNumber(this.tableData[5]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[18]['groupData']) + this.cellToNumber(this.tableData[21]['groupData'])).toString();\r\n    //     } else if (index == 8 || index == 9 || index == 10) {\r\n    //       this.tableData[7]['groupData'] = (this.cellToNumber(this.tableData[8]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[9]['groupData']) + this.cellToNumber(this.tableData[10]['groupData'])).toString();\r\n    //       this.tableData[5]['groupData'] = (this.cellToNumber(this.tableData[6]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[7]['groupData']) + this.cellToNumber(this.tableData[11]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[17]['groupData'])).toString();\r\n    //       this.tableData[4]['groupData'] = (this.cellToNumber(this.tableData[5]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[18]['groupData']) + this.cellToNumber(this.tableData[21]['groupData'])).toString();\r\n    //     } else if (index == 19 || index == 20) {\r\n    //       this.tableData[18]['groupData'] = (this.cellToNumber(this.tableData[19]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[20]['groupData'])).toString();\r\n    //       this.tableData[4]['groupData'] = (this.cellToNumber(this.tableData[5]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[18]['groupData']) + this.cellToNumber(this.tableData[21]['groupData'])).toString();\r\n    //     } else if (index == 21) {\r\n    //       this.tableData[4]['groupData'] = (this.cellToNumber(this.tableData[5]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[18]['groupData']) + this.cellToNumber(this.tableData[21]['groupData'])).toString();\r\n    //     } else if (index == 24 || index == 25) {\r\n    //       this.tableData[23]['groupData'] = (this.cellToNumber(this.tableData[24]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[25]['groupData'])).toString();\r\n    //     } else if (index == 28 || index == 29) {\r\n    //       this.tableData[27]['groupData'] = (this.cellToNumber(this.tableData[28]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[29]['groupData'])).toString();\r\n    //     }\r\n    //     this.countData('groupData');\r\n    //   }\r\n    // },\r\n    // handleStockChange(row,index) {\r\n    //   this.saveChangeIndex(row.stockData,index,'stockData',2,row.id)\r\n    //   if (this.noInputList.stockList.indexOf(index) == -1 && this.formulaList.stockList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['stockData'].toString()) < 0) {\r\n    //       this.tableData[index]['stockData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['stockData'] != undefined && this.tableData[index]['stockData'] != '') {\r\n    //       let j = this.backgroundList.stockList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.stockList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     if (index == 6 || index == 11 || index == 17) {\r\n    //       this.tableData[5]['stockData'] = (this.cellToNumber(this.tableData[6]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[7]['stockData']) + this.cellToNumber(this.tableData[11]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[17]['stockData'])).toString();\r\n    //       this.tableData[4]['stockData'] = (this.cellToNumber(this.tableData[5]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[18]['stockData']) + this.cellToNumber(this.tableData[21]['stockData'])).toString();\r\n    //     } else if (index == 8 || index == 9 || index == 10) {\r\n    //       this.tableData[index]['mediumData'] = this.tableData[index]['stockData'];\r\n    //       this.tableData[index]['mobileData'] = this.tableData[index]['stockData'];\r\n    //       this.tableData[7]['stockData'] = (this.cellToNumber(this.tableData[8]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[9]['stockData']) + this.cellToNumber(this.tableData[10]['stockData'])).toString();\r\n    //       this.tableData[7]['mediumData'] = this.tableData[7]['stockData'];\r\n    //       this.tableData[4]['mediumData'] = this.tableData[7]['stockData'];\r\n    //       this.tableData[7]['mobileData'] = this.tableData[7]['stockData'];\r\n    //       this.tableData[4]['mobileData'] = this.tableData[7]['stockData'];\r\n    //       this.tableData[5]['stockData'] = (this.cellToNumber(this.tableData[6]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[7]['stockData']) + this.cellToNumber(this.tableData[11]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[17]['stockData'])).toString();\r\n    //       this.tableData[4]['stockData'] = (this.cellToNumber(this.tableData[5]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[18]['stockData']) + this.cellToNumber(this.tableData[21]['stockData'])).toString();\r\n    //       this.countData('mediumData');\r\n    //       this.countData('mobileData');\r\n    //     } else if (index > 12 && index < 17) {\r\n    //       this.tableData[index]['mediumData'] = this.tableData[index]['stockData'];\r\n    //       if (index == 13 || index == 15) {\r\n    //         this.tableData[11]['mediumData'] = (this.cellToNumber(this.tableData[13]['stockData']) +\r\n    //           this.cellToNumber(this.tableData[15]['stockData'])).toString();\r\n    //         this.tableData[4]['mediumData'] = this.tableData[11]['mediumData'];\r\n    //         this.countData('mediumData');\r\n    //       } else {\r\n    //         this.tableData[12]['mediumData'] = (this.cellToNumber(this.tableData[14]['stockData']) +\r\n    //           this.cellToNumber(this.tableData[16]['stockData'])).toString();\r\n    //       }\r\n\r\n    //     } else if (index == 19 || index == 20) {\r\n    //       this.tableData[18]['stockData'] = (this.cellToNumber(this.tableData[19]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[20]['stockData'])).toString();\r\n    //       this.tableData[4]['stockData'] = (this.cellToNumber(this.tableData[5]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[18]['stockData']) + this.cellToNumber(this.tableData[21]['stockData'])).toString();\r\n    //     } else if (index == 21) {\r\n    //       this.tableData[4]['stockData'] = (this.cellToNumber(this.tableData[5]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[18]['stockData']) + this.cellToNumber(this.tableData[21]['stockData'])).toString();\r\n    //     } else if (index == 24 || index == 25) {\r\n    //       this.tableData[23]['stockData'] = (this.cellToNumber(this.tableData[24]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[25]['stockData'])).toString();\r\n    //     } else if (index == 28 || index == 29) {\r\n    //       this.tableData[27]['stockData'] = (this.cellToNumber(this.tableData[28]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[29]['stockData'])).toString();\r\n    //     }\r\n    //     this.countData('stockData');\r\n    //   }\r\n    // },\r\n    // handleLargeChange(row,index) {\r\n    //   this.saveChangeIndex(row.largeData,index,'largeData',3,row.id)\r\n    //   if (this.noInputList.largeList.indexOf(index) == -1 && this.formulaList.largeList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['largeData'].toString()) < 0) {\r\n    //       this.tableData[index]['largeData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['largeData'] != undefined && this.tableData[index]['largeData'] != '') {\r\n    //       let j = this.backgroundList.largeList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.largeList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     if (index == 11) {\r\n    //       this.tableData[4]['largeData'] = this.tableData[11]['largeData'];\r\n\r\n    //     } else if (index == 24 || index == 25) {\r\n    //       this.tableData[23]['largeData'] = (this.cellToNumber(this.tableData[24]['largeData']) +\r\n    //         this.cellToNumber(this.tableData[25]['largeData'])).toString();\r\n    //     } else if (index == 28 || index == 29) {\r\n    //       this.tableData[27]['largeData'] = (this.cellToNumber(this.tableData[28]['largeData']) +\r\n    //         this.cellToNumber(this.tableData[29]['largeData'])).toString();\r\n    //     }\r\n    //     this.countData('largeData');\r\n    //   }\r\n    // },\r\n    // handleMediumChange(row,index) {\r\n    //   this.saveChangeIndex(row.mediumData,index,'mediumData',4,row.id)\r\n    //   if (this.noInputList.mediumList.indexOf(index) == -1 && this.formulaList.mediumList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['mediumData'].toString()) < 0) {\r\n    //       this.tableData[index]['mediumData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['mediumData'] != undefined && this.tableData[index]['mediumData'] != '') {\r\n    //       let j = this.backgroundList.mediumList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.mediumList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     if (index == 24 || index == 25) {\r\n    //       this.tableData[23]['mediumData'] = (this.cellToNumber(this.tableData[24]['mediumData']) +\r\n    //         this.cellToNumber(this.tableData[25]['mediumData'])).toString();\r\n    //     } else if (index == 28 || index == 29) {\r\n    //       this.tableData[27]['mediumData'] = (this.cellToNumber(this.tableData[28]['mediumData']) +\r\n    //         this.cellToNumber(this.tableData[29]['mediumData'])).toString();\r\n    //     }\r\n    //     this.countData('mediumData');\r\n    //   }\r\n    // },\r\n    // handleMobileChange(row,index) {\r\n    //   this.saveChangeIndex(row.mobileData,index,'mediumData',5,row.id)\r\n    //   if (this.noInputList.mobileList.indexOf(index) == -1 && this.formulaList.mobileList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['mobileData'].toString()) < 0) {\r\n    //       this.tableData[index]['mobileData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['mobileData'] != undefined && this.tableData[index]['mobileData'] != '') {\r\n    //       let j = this.backgroundList.mobileList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.mobileList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     this.countData('mobileData')\r\n    //   }\r\n    // },\r\n    countData(label) {\r\n      this.tableData[0][label] = (Math.round((\r\n          this.cellToNumber(this.tableData[1][label]) * this.tableData[1].coefficient +\r\n          this.cellToNumber(this.tableData[3][label]) * this.tableData[3].coefficient +\r\n          this.cellToNumber(this.tableData[4][label]) * this.tableData[4].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[21][label]) * this.tableData[21].coefficient +\r\n          this.cellToNumber(this.tableData[22][label]) * this.tableData[22].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[25][label]) * this.tableData[25].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[26][label]) * this.tableData[26].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[29][label]) * this.tableData[29].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[30][label]) * this.tableData[30].coefficient +\r\n          this.cellToNumber(this.tableData[31][label]) * this.tableData[31].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[32][label]) * this.tableData[32].coefficient +\r\n          this.cellToNumber(this.tableData[33][label]) +\r\n          this.cellToNumber(this.tableData[34][label]) * this.tableData[34].coefficient / 1000 -\r\n          this.cellToNumber(this.tableData[35][label])\r\n      ) * 1000000) / 1000000).toString();\r\n    },\r\n    //根据指标名称确定输入框状态\r\n    checkIndicatorInput() {\r\n      Object.keys(this.disabledList).forEach(key => this.disabledList[key] = []);\r\n      Object.keys(this.formulaList).forEach(key => this.formulaList[key] = []);\r\n      Object.keys(this.noInputList).forEach(key => this.noInputList[key] = []);\r\n      Object.keys(this.requireInputList).forEach(key => this.requireInputList[key] = []);\r\n      Object.keys(this.noRequireInputList).forEach(key => this.noRequireInputList[key] = []);\r\n      for (let i = 0; i < this.tableData.length; i ++) {\r\n        if ('1、能源消费总量(吨标煤)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          Object.keys(this.formulaList).forEach(key => this.formulaList[key].push(i));\r\n        } else if ('1.1、 煤炭(吨)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.requireInputList).forEach(key => this.requireInputList[key].push(i));\r\n        } else if ('1.1.1、其中发电用煤(吨)' == this.tableData[i].indicatorName) {\r\n          this.disabledList.mobileList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n          Object.keys(this.requireInputList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.requireInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.2、焦炭(吨)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.requireInputList).forEach(key => this.requireInputList[key].push(i));\r\n        } else if ('1.3、耗电量（总）(千瓦时)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          Object.keys(this.formulaList).forEach(key => this.formulaList[key].push(i));\r\n        } else if ('1.3.1、生产用房耗电量(千瓦时)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          this.formulaList.groupList.push(i);\r\n          this.formulaList.stockList.push(i);\r\n          this.noInputList.largeList.push(i);\r\n          this.noInputList.mediumList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n        } else if ('1.3.1.1、其中：通信机房耗电量(千瓦时)' == this.tableData[i].indicatorName) {\r\n          this.disabledList.largeList.push(i);\r\n          this.disabledList.mediumList.push(i);\r\n          this.disabledList.mobileList.push(i);\r\n          this.noInputList.largeList.push(i);\r\n          this.noInputList.mediumList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n          Object.keys(this.requireInputList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.requireInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.3.1.2、其中：基站耗电量(千瓦时)' == this.tableData[i].indicatorName) {  //7\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          Object.keys(this.formulaList).forEach(key => {\r\n            if (key != 'largeList' && key != 'mediumList') {\r\n              this.formulaList[key].push(i);\r\n            }\r\n          });\r\n          this.noInputList.largeList.push(i);\r\n          this.noInputList.mediumList.push(i);\r\n        } else if ('1.3.1.2.1、其中：铁塔公司基站耗电量（包括室内分布、室外站等） (千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.1.2.2、其中：第三方租赁基站耗电量（包括室内分布、室外站等） (千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.1.2.3、其中：自有产权基站耗电量（包括室内分布、室外站等） (千瓦时)' == this.tableData[i].indicatorName) { //8 - 10\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          this.formulaList.mobileList.push(i);\r\n          this.noInputList.largeList.push(i);\r\n          this.noInputList.mediumList.push(i);\r\n          this.requireInputList.groupList.push(i);\r\n          this.requireInputList.stockList.push(i);\r\n        } else if ('1.3.1.3、其中：数据中心耗电量(千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.1.3.1、其中：数据中心IT设备总耗电量（千瓦时）' == this.tableData[i].indicatorName||\r\n          '1.3.1.3.2、其中：对外IDC机房耗电量(千瓦时)' == this.tableData[i].indicatorName||\r\n          '1.3.1.3.2.1、其中：对外IDC机房IT设备耗电量（千瓦时)' == this.tableData[i].indicatorName||\r\n          '1.3.1.3.3、其中：自用业务平台和IT支撑用房耗电量(千瓦时)' == this.tableData[i].indicatorName||\r\n          '1.3.1.3.3.1、其中：自用业务平台IT设备耗电量（千瓦时）' == this.tableData[i].indicatorName) { //11 - 16\r\n          this.disabledList.mediumList.push(i);\r\n          this.disabledList.mobileList.push(i);\r\n          this.formulaList.mediumList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n          Object.keys(this.requireInputList).forEach(key => {\r\n            if (key != 'mediumList' && key != 'mobileList') {\r\n              this.requireInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.3.1.4、其中：接入局所及室外机柜耗电量(千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.2.1、其中：管理用房耗电量(千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.2.2、其中：渠道用房耗电量(千瓦时)' == this.tableData[i].indicatorName ||\r\n          '2.5、废水排放量(吨)' == this.tableData[i].indicatorName) { //17 19-20 38\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noInputList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.noInputList[key].push(i);\r\n            }\r\n          });\r\n          this.requireInputList.groupList.push(i);\r\n          this.requireInputList.stockList.push(i);\r\n        } else if ('1.3.2、非生产用房耗电量(千瓦时)' == this.tableData[i].indicatorName) { //18\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          this.formulaList.groupList.push(i);\r\n          this.formulaList.stockList.push(i);\r\n          Object.keys(this.noInputList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.noInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.3.3、 可再生能源使用量(千瓦时)（仅限填写自发自用绿电）' == this.tableData[i].indicatorName ||\r\n          '1.4、原油(吨)' == this.tableData[i].indicatorName) { //21 22\r\n          Object.keys(this.requireInputList).forEach(key => this.requireInputList[key].push(i));\r\n        } else if ('1.5、汽油消耗量(升)' == this.tableData[i].indicatorName) { //23\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.formulaList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.formulaList[key].push(i);\r\n            }\r\n          });\r\n          this.requireInputList.mobileList.push(i);\r\n        } else if ('1.5.1、其中：移动源（升）' == this.tableData[i].indicatorName ||\r\n          '1.5.2、其中：固定源（升）' == this.tableData[i].indicatorName ||\r\n          '1.7.1、其中：移动源（升）' == this.tableData[i].indicatorName ||\r\n          '1.7.2、其中：固定源（升）' == this.tableData[i].indicatorName) { //24 25 28 29\r\n          this.disabledList.mobileList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n          Object.keys(this.requireInputList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.requireInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.6、煤油(升)' == this.tableData[i].indicatorName ||\r\n          '1.8、燃料油(升)' == this.tableData[i].indicatorName ||\r\n          '1.9、液化石油气(吨）' == this.tableData[i].indicatorName ||\r\n          '2.0、天然气消耗量(立方米)' == this.tableData[i].indicatorName ||\r\n          '2.1、热力(十亿焦)' == this.tableData[i].indicatorName ||\r\n          '2.2、 其他能源(吨标准煤)' == this.tableData[i].indicatorName ||\r\n          '2.3、新水用量(吨)' == this.tableData[i].indicatorName ||\r\n          '2.4、节能量(吨标准煤)' == this.tableData[i].indicatorName ||\r\n          '2.4.1、其中：节电量(千瓦时)' == this.tableData[i].indicatorName) { //26  30-37\r\n          Object.keys(this.requireInputList).forEach(key => this.requireInputList[key].push(i));\r\n        } else if ('1.7、柴油消耗量(升)' == this.tableData[i].indicatorName) { //27\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.formulaList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.formulaList[key].push(i);\r\n            }\r\n          });\r\n          this.requireInputList.mobileList.push(i);\r\n        } else if ('2.6、一般固体废物产生量(吨)' == this.tableData[i].indicatorName ||\r\n          '2.7、一般固体废物综合利用量(吨)' == this.tableData[i].indicatorName ||\r\n          '2.8、综合利用往年贮存量(吨)' == this.tableData[i].indicatorName ||\r\n          '2.9、危险废物产生量(吨)' == this.tableData[i].indicatorName ||\r\n          '3.0、危险废物处置量(吨)' == this.tableData[i].indicatorName ||\r\n          '3.1、处置往年贮存量(吨)' == this.tableData[i].indicatorName ||\r\n          '3.2、土壤污染治理面积(公顷)' == this.tableData[i].indicatorName ||\r\n          '3.3、土壤污染需要治理面积(公顷)' == this.tableData[i].indicatorName ||\r\n          '3.4、矿山（或生态）修复治理面积(公顷)' == this.tableData[i].indicatorName ||\r\n          '3.5、矿山（或生态）需要修复治理面积(公顷)' == this.tableData[i].indicatorName ||\r\n          '3.6、废气治理设施数(套)' == this.tableData[i].indicatorName ||\r\n          '3.7、废气治理设施处理能力(立方米/月)' == this.tableData[i].indicatorName ||\r\n          '3.8、废水治理设施数(套)' == this.tableData[i].indicatorName ||\r\n          '3.9、废水治理设施处理能力(吨/月)' == this.tableData[i].indicatorName ||\r\n          '4.0、生态环境污染源(个)' == this.tableData[i].indicatorName ||\r\n          '4.1、生态环境风险点（个）' == this.tableData[i].indicatorName ||\r\n          '4.2、节能投入(元)' == this.tableData[i].indicatorName ||\r\n          '4.3、环保投入(元)' == this.tableData[i].indicatorName) { //39-56\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noInputList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.noInputList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noRequireInputList).forEach(key => this.noRequireInputList[key].push(i));\r\n        } else if ('4.4、数据中心标准机架数量' == this.tableData[i].indicatorName) { //57\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'largeList' && key != 'mediumList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noInputList).forEach(key => {\r\n            if (key != 'largeList' && key != 'mediumList') {\r\n              this.noInputList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noRequireInputList).forEach(key => this.noRequireInputList[key].push(i));\r\n        }\r\n      }\r\n    },\r\n    handleImportExcel(file, fileList) {\r\n      try{\r\n        let tableField = ['上报指标', '集团', '股份', '大型', '中小型', '移动业务'];\r\n        let m = this;\r\n        importExcel(file, tableField).then(res => {\r\n          if (res.length > this.tableData.length) {\r\n            for (let i = 0; i < this.tableData.length; i ++) {\r\n              if (this.tableData[i].indicatorName == res[i+1][\"上报指标\"].trim()) {\r\n                if (this.checkExcelLineData(res[i+1], i) == true) {\r\n                  this.tableData[i].groupData = res[i + 1]['集团'].toString();\r\n                  this.tableData[i].stockData = res[i + 1]['股份'].toString();\r\n                  this.tableData[i].largeData = res[i + 1]['大型'].toString();\r\n                  this.tableData[i].mediumData = res[i + 1]['中小型'].toString();\r\n                  this.tableData[i].mobileData = res[i + 1]['移动业务'].toString();\r\n                } else {\r\n                  m.alertError(\"第\" + Math.floor(i + 3) + \"行有非法数据数据，请检查文档！\");\r\n                  return;\r\n                }\r\n              } else {\r\n                m.alertError(\"第\" + Math.floor(i + 3) + \"行上报指标名称错误，请参考填报模板！\");\r\n                return;\r\n              }\r\n            }\r\n          } else {\r\n            m.alertError(\"数据行数过少，请参考填报模板！\");\r\n            return;\r\n          }\r\n        })\r\n      }catch(exception){             //抓住throw抛出的错误\r\n        this.alertError(\"数据格式错误\");\r\n      }\r\n    },\r\n    checkExcelLineData(data, line) {\r\n      let ret = true;\r\n      if (data['集团'] == undefined || data['集团'] == null\r\n        || data['股份'] == undefined || data['股份'] == null\r\n        || data['大型'] == undefined || data['大型'] == null\r\n        || data['中小型'] == undefined || data['中小型'] == null\r\n        || data['移动业务'] == undefined || data['移动业务'] == null) {\r\n        ret = false;\r\n        return ret;\r\n      } else {\r\n        if (this.noInputList.groupList.indexOf(line) == -1) {\r\n          let groupData = parseFloat(data['集团']);\r\n          if (isNaN(groupData) || groupData == null || groupData < 0) {\r\n            // if (isNaN(groupData) || groupData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.noInputList.stockList.indexOf(line) == -1) {\r\n          let stockData = parseFloat(data['股份']);\r\n          if (isNaN(stockData) || stockData == null || stockData < 0) {\r\n            // if (isNaN(stockData) || stockData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.noInputList.largeList.indexOf(line) == -1) {\r\n          let largeData = parseFloat(data['大型']);\r\n          if (isNaN(largeData) || largeData == null || largeData < 0) {\r\n            // if (isNaN(largeData) || largeData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.noInputList.mediumList.indexOf(line) == -1) {\r\n          let mediumData = parseFloat(data['中小型']);\r\n          if (isNaN(mediumData) || mediumData == null || mediumData < 0) {\r\n            // if (isNaN(mediumData) || mediumData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.noInputList.mobileList.indexOf(line) == -1) {\r\n          let mobileData = parseFloat(data['移动业务']);\r\n          if (isNaN(mobileData) || mobileData == null || mobileData < 0) {\r\n            // if (isNaN(mobileData) || mobileData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n    cellToNumber(cell) {\r\n      let num = 0;\r\n      if (cell == undefined || cell == null)\r\n      {\r\n        num = 0;\r\n      } else {\r\n        num = Math.round(parseFloat(cell)*1000000)/1000000;\r\n        if (isNaN(num) || num == null) {\r\n          num = 0;\r\n        }\r\n      }\r\n      return num;\r\n    },\r\n    // 判断两个值是否相等\r\n    areFloatsEqual(float1, float2) {\r\n    // 定义一个非常小的误差阈值\r\n    const epsilon = 0.000001; // 适当调整此值以满足你的需求\r\n\r\n    // 计算两个浮点数的差值\r\n    const difference = Math.abs(float1 - float2);\r\n\r\n    // 如果差值小于误差阈值，就认为两个浮点数相等\r\n    return difference < epsilon;\r\n    } ,\r\n    getObj(item) {\r\n      item.show = !item.show;\r\n      if(item.operateType == 1){\r\n        this.getList(item)\r\n      } else {\r\n        this.getEnergyUpdateRecordList(item)\r\n      }\r\n      // if(item.isShow) {\r\n      //   item.names = '收起数据';\r\n      // }else {\r\n      //   item.names = '查看数据';\r\n      // }\r\n    },\r\n    formatDisplayData(value) {\r\n      let data = parseFloat(value);\r\n      if (isNaN(value)) {\r\n        return value;\r\n      } else {\r\n        return data.toFixed(2);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n/deep/.el-table {\r\n  height: auto!important;\r\n  //$properties: max-height;\r\n  //@each $prop in $properties {\r\n  //  #{$prop}: unset!important;\r\n  //}\r\n  & > .el-table__body-wrapper {\r\n    height: auto!important;\r\n  }\r\n}\r\n#modularForm_bg::-webkit-scrollbar {\r\n    // display: none;\r\n    width: 2px;\r\n  }\r\n\r\n/* 滚动条轨道 */\r\n#modularForm_bg::-webkit-scrollbar-track {\r\n  background: #f1f1f1; /* 设置滚动条轨道的背景色 */\r\n}\r\n\r\n/* 滚动条滑块 */\r\n#modularForm_bg::-webkit-scrollbar-thumb {\r\n  background: #888; /* 设置滚动条滑块的背景色 */\r\n}\r\n\r\n/* 滚动条滑块在鼠标悬停时的样式 */\r\n#modularForm_bg::-webkit-scrollbar-thumb:hover {\r\n  background: #555; /* 设置鼠标悬停时滚动条滑块的背景色 */\r\n}\r\n\r\n/deep/.cell-color {\r\n  color:red !important;\r\n  cursor: pointer;\r\n}\r\n\r\n.foot_btn {\r\n  width: 88%;\r\n  height: 7vh;\r\n  position: fixed;\r\n  bottom: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #111d30;\r\n}\r\n\r\np {\r\n  margin: 0;\r\n}\r\n/deep/.avue-crud__menu {\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n.curd-header {\r\n  width: 97.6%;\r\n  height: 5vh;\r\n  margin: 2vh auto 0;\r\n  display: flex;\r\n  background: #12537a;\r\n  position: relative;\r\n  border-bottom: 0.1rem solid #04223b;\r\n  justify-content: space-between;\r\n  & > .reporting {\r\n    // width: 69rem;\r\n    display: flex;\r\n    margin-left: 1rem;\r\n    align-items: center;\r\n    p {\r\n      font-size: 1.4rem;\r\n      line-height: 5vh;\r\n      margin-right: 1rem;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n    /deep/.el-date-editor.el-input,\r\n    .el-date-editor.el-input__inner {\r\n      width: 20rem !important;\r\n    }\r\n  }\r\n  & > .companyName {\r\n    margin-right: 12rem;\r\n    font-size: 1.4rem;\r\n    line-height: 5vh;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #00ecc0;\r\n  }\r\n}\r\n\r\n.el_input_class {\r\n  /deep/.el-input__inner{\r\n    background: red;\r\n  }\r\n}\r\n.el-input.is-disabled /deep/ .el-input__inner {\r\n  background: #626f7a !important;\r\n}\r\n::v-deep input::-webkit-outer-spin-button,\r\n::v-deep input::-webkit-inner-spin-button {\r\n  -webkit-appearance: none !important;\r\n}\r\n::v-deep input[type='number'] {\r\n  -moz-appearance: textfield !important;\r\n}\r\n</style>\r\n"]}]}