{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\effectAnalysis.vue?vue&type=style&index=0&id=2de9aece&lang=less&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\effectAnalysis.vue", "mtime": 1754285403029}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749178874080}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KI2VmZmVjdGl2ZW5lc3MtY2hhcnRzIHsNCiAgaGVpZ2h0OiAzMDBweDsNCiAgd2lkdGg6IDUzcmVtOw0KICBsZXR0ZXItc3BhY2luZzogMC4xcmVtOw0KfQ0KL2RlZXAvIGNhbnZhcyB7DQogIHotaW5kZXg6IDkgIWltcG9ydGFudDsNCn0NCg=="}, {"version": 3, "sources": ["effectAnalysis.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "effectAnalysis.vue", "sourceRoot": "src/view/carbon/assess/assessReport/components", "sourcesContent": ["<template>\r\n  <div class=\"water-eval-container\" style=\"position: relative\">\r\n    <div class=\"effectiveness-charts\" id=\"effectiveness-charts\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport \"echarts-gl\";\r\nexport default {\r\n  name: \"cityGreenLand\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      electrictTotal: \"\",\r\n      isFullscreen: false,\r\n      optionData: [\r\n        {\r\n          name: \"外购绿电\",\r\n          value: 30000,\r\n        },\r\n        {\r\n          name: \"外购火电\",\r\n          value: 22116,\r\n        },\r\n        {\r\n          name: \"自有新能源发电\",\r\n          value: 16616,\r\n        },\r\n      ],\r\n      nowStep: 0,\r\n      maxStep: 35,\r\n      height: 95,\r\n    };\r\n  },\r\n  props: {\r\n    examineList: {\r\n      type: Array,\r\n    },\r\n  },\r\n\r\n  watch: {\r\n    examineList: {\r\n      immediate: true,\r\n      handler(newVal, oldVal) {\r\n        if (newVal == undefined || newVal == null || newVal == \"\") return;\r\n        this.$nextTick(() => {\r\n          this.init();\r\n        });\r\n      },\r\n      deep: true, // 深度监听\r\n    },\r\n  },\r\n\r\n  methods: {\r\n    init() {\r\n      // 原始数据\r\n      let data = this.examineList;\r\n      // 将数据根据从小到大排序\r\n      // let newdata = sortObject(data);\r\n      // 图例数据\r\n      let lengthData = [];\r\n      // 返回数据\r\n      let resultData = data.map((item, index) => {\r\n        let len = item.num.toString().length;\r\n        let graw;\r\n        if (len * 1 < 2) {\r\n          graw = 100;\r\n        } else if (len * 1 < 3) {\r\n          graw = 114;\r\n        } else if (len * 1 < 4) {\r\n          graw = 124;\r\n        }\r\n        lengthData.push({\r\n          type: \"group\",\r\n          top: index * 35,\r\n          scale: [1, 1],\r\n          children: [\r\n            {\r\n              type: \"circle\",\r\n              shape: {\r\n                cx: 0,\r\n                cy: 9,\r\n                r: 5,\r\n              },\r\n              style: {\r\n                fill:\r\n                  index === 0\r\n                    ? \"#ACFFF0\"\r\n                    : index === 1\r\n                    ? \"#00ECC0\"\r\n                    : index === 2\r\n                    ? \"#00D96C\"\r\n                    : \"#006452\",\r\n              },\r\n            },\r\n            {\r\n              type: \"text\",\r\n              style: {\r\n                text: item.name,\r\n                fontFamily: \"PingFangSC-Regular\",\r\n                fill: \"#fff\",\r\n                fontSize: 14,\r\n                x: 14,\r\n                y: 2,\r\n              },\r\n            },\r\n            {\r\n              type: \"text\",\r\n              name: item.name,\r\n              style: {\r\n                text: item.num,\r\n                fill: \"#fff\",\r\n                fontFamily: \"Alibaba-PuHuiTi-Bold\",\r\n                fontSize: 18,\r\n                x: 85,\r\n                y: -2,\r\n              },\r\n            },\r\n            {\r\n              type: \"text\",\r\n              style: {\r\n                text: \"个\",\r\n                fontFamily: \"PingFangSC-Regular\",\r\n                fill: \"#fff\",\r\n                fontSize: 14,\r\n                x: graw,\r\n                y: 2,\r\n              },\r\n            },\r\n          ],\r\n        });\r\n\r\n        if (index === 0) {\r\n          return {\r\n            style: {\r\n              fill: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [\r\n                { offset: 0, color: \"#ACFFF0\" },\r\n                { offset: 1, color: \"#00ECC0\" },\r\n              ]),\r\n            },\r\n            ...item,\r\n          };\r\n        } else if (index === 1) {\r\n          return {\r\n            style: {\r\n              fill: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [\r\n                { offset: 0, color: \"#00D96C\" },\r\n                { offset: 1, color: \"#00ECC0\" },\r\n              ]),\r\n            },\r\n            ...item,\r\n          };\r\n        } else if (index === 2) {\r\n          return {\r\n            style: {\r\n              fill: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [\r\n                { offset: 0, color: \"#00D96C\" },\r\n                { offset: 1, color: \"#006452\" },\r\n              ]),\r\n            },\r\n            ...item,\r\n          };\r\n        } else if (index === 3) {\r\n          return {\r\n            style: {\r\n              fill: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [\r\n                { offset: 0, color: \"#006452\" },\r\n                { offset: 1, color: \"#006452\" },\r\n              ]),\r\n            },\r\n            ...item,\r\n          };\r\n        }\r\n      });\r\n      // 获取计算的数据\r\n      let getData = this.pyramidChart(\r\n        resultData,\r\n        document.getElementById(\"effectiveness-charts\")\r\n      );\r\n      let myChart = this.$echarts.init(document.getElementById(\"effectiveness-charts\"));\r\n      let option = {\r\n        graphic: [\r\n          {\r\n            type: \"group\",\r\n            left: \"8%\",\r\n            top: \"16%\",\r\n            scale: [0.9, 0.9],\r\n            onclick: function (params) {},\r\n            children: getData,\r\n          },\r\n          {\r\n            type: \"group\",\r\n            left: \"60%\",\r\n            top: \"20%\",\r\n            scale: [1, 1],\r\n            onclick: function (params) {},\r\n            children: lengthData,\r\n          },\r\n        ],\r\n        series: [],\r\n      };\r\n      myChart.setOption(option);\r\n      window.addEventListener(\"resize\", function () {\r\n        myChart.resize();\r\n      });\r\n    },\r\n    pyramidChart(data = [], dom, option = {}) {\r\n      let domHeight = dom ? dom.clientHeight : 0;\r\n      let domWidth = dom ? dom.clientWidth : 0;\r\n      // 默认获取一个正方形空间\r\n      let maxDistance = domHeight > domWidth / 2.3 ? domWidth / 2.3 : domHeight;\r\n      // 合并设置\r\n      let resultOption = Object.assign(\r\n        {\r\n          slanted: 1, // 每层底部的倾斜度\r\n          maxWidth: maxDistance, // 金字塔最大宽度\r\n          maxHeight: maxDistance, // 金字塔最大高度\r\n          offset: 35, //偏差\r\n        },\r\n        option\r\n      );\r\n      if (data.length === 1) {\r\n        resultOption.slanted = 50;\r\n      }\r\n      if (data.length === 2) {\r\n        resultOption.slanted = 25;\r\n      }\r\n      if (data.length === 3) {\r\n        resultOption.slanted = 10;\r\n      }\r\n      // 减去多余的误差边距\r\n      resultOption.maxHeight = resultOption.maxHeight - resultOption.offset;\r\n      // 一半最大宽度,用于计算左右边距\r\n      let halfMaxWidth = resultOption.maxWidth / 2;\r\n      // 数据最终\r\n      let resultData = [];\r\n      // 数据值 数组\r\n      let dataNums = data.map((item) => item.value || 0);\r\n      // 计算数据总和\r\n      let dataNumSum =\r\n        dataNums.length > 0 &&\r\n        dataNums.reduce(function (prev, curr) {\r\n          return Number(prev || 0) + Number(curr || 0);\r\n        });\r\n      // 中间数据点坐标数组 根据长度比值算出\r\n      let midlinePoint = [];\r\n      let multipleLayer = [0.6];\r\n      // 计算倍数等基础数据\r\n      dataNums.forEach((item, index, arr) => {\r\n        let itemNext = arr[index + 1];\r\n        if (itemNext) {\r\n          multipleLayer.push(itemNext / dataNums[0]); // 计算倍数\r\n        }\r\n        // 计算点坐标 长度\r\n        let point =\r\n          Math.round((item / dataNumSum) * resultOption.maxHeight * 1000) / 1000;\r\n        midlinePoint.push(point);\r\n      });\r\n      // 三角形的高度\r\n      let triangleHeight = 0;\r\n      let triangleHeightLayer = [];\r\n      // 三角形tan角度\r\n      let triangleRatio = halfMaxWidth / resultOption.maxHeight;\r\n      midlinePoint.forEach((item) => {\r\n        triangleHeight = triangleHeight + item;\r\n        triangleHeightLayer.push(triangleHeight);\r\n      });\r\n      // 中间数据点 最后的数据长度\r\n      let midlinePointFinally = triangleHeightLayer[triangleHeightLayer.length - 1] || 0;\r\n      // 开始拼接数据\r\n      data.forEach((item, index) => {\r\n        let arrObj = [];\r\n        let triangleHeightLayerOne = triangleHeightLayer[index];\r\n        let triangleHeightLayerOneLast = triangleHeightLayer[index - 1] || 0;\r\n        let multipleLayerOne = multipleLayer[index];\r\n        let multipleLayerOneLast = multipleLayer[index - 1] || 0;\r\n        // 第一层数据单独处理\r\n        if (index === 0) {\r\n          arrObj.push(\r\n            [0, 0],\r\n            [\r\n              -triangleRatio *\r\n                (triangleHeightLayerOne - resultOption.slanted * multipleLayerOne),\r\n              triangleHeightLayerOne - resultOption.slanted * multipleLayerOne,\r\n            ],\r\n            [0, triangleHeightLayerOne],\r\n            [\r\n              triangleRatio *\r\n                (triangleHeightLayerOne - resultOption.slanted * multipleLayerOne),\r\n              triangleHeightLayerOne - resultOption.slanted * multipleLayerOne,\r\n            ]\r\n          );\r\n        } else {\r\n          arrObj.push(\r\n            [0, triangleHeightLayerOneLast],\r\n            [\r\n              -triangleRatio *\r\n                (triangleHeightLayerOneLast -\r\n                  resultOption.slanted * multipleLayerOneLast),\r\n              triangleHeightLayerOneLast - resultOption.slanted * multipleLayerOneLast,\r\n            ],\r\n            [\r\n              -triangleRatio *\r\n                (triangleHeightLayerOne - resultOption.slanted * multipleLayerOne),\r\n              triangleHeightLayerOne - resultOption.slanted * multipleLayerOne,\r\n            ],\r\n            [0, triangleHeightLayerOne],\r\n            [\r\n              triangleRatio *\r\n                (triangleHeightLayerOne - resultOption.slanted * multipleLayerOne),\r\n              triangleHeightLayerOne - resultOption.slanted * multipleLayerOne,\r\n            ],\r\n            [\r\n              triangleRatio *\r\n                (triangleHeightLayerOneLast -\r\n                  resultOption.slanted * multipleLayerOneLast),\r\n              triangleHeightLayerOneLast - resultOption.slanted * multipleLayerOneLast,\r\n            ]\r\n          );\r\n        }\r\n        resultData.push({\r\n          type: \"polygon\",\r\n          z: 1,\r\n          shape: {\r\n            points: arrObj,\r\n          },\r\n          name: item.name,\r\n          style: item.style,\r\n        });\r\n      });\r\n      // 添加线\r\n      resultData.push({\r\n        type: \"polyline\",\r\n        shape: {\r\n          points: [\r\n            [0, 0],\r\n            [0, midlinePointFinally],\r\n          ],\r\n        },\r\n        style: {\r\n          stroke: \"#f2f2f2\",\r\n          opacity: 0.2,\r\n          lineWidth: 1,\r\n        },\r\n        z: 2,\r\n      });\r\n      // 返回\r\n      return resultData;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n#effectiveness-charts {\r\n  height: 300px;\r\n  width: 53rem;\r\n  letter-spacing: 0.1rem;\r\n}\r\n/deep/ canvas {\r\n  z-index: 9 !important;\r\n}\r\n</style>\r\n"]}]}