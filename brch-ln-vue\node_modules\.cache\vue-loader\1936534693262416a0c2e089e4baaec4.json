{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addSelfPowerAccount.vue?vue&type=style&index=0&id=25549dd2&lang=less&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addSelfPowerAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnBhZ2UtY2xhc3Mgew0KICBoZWlnaHQ6IDEwMCU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIG92ZXJmbG93OiBoaWRkZW47DQogIC5jbC10YWJsZSB7DQogICAgZmxleDogMTsNCiAgICBvdmVyZmxvdzogaGlkZGVuOw0KICB9DQogIC5idXR0b24tYmFyIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7DQogIH0NCn0NCi55ampoID4gLml2dS1tb2RhbC13cmFwID4gLml2dS1tb2RhbCB7DQogIHRvcDogMjBweCAhaW1wb3J0YW50Ow0KfQ0KDQoubXl0YWJsZSAuaXZ1LXRhYmxlLWNlbGwgew0KICBwYWRkaW5nLWxlZnQ6IDFweDsNCiAgcGFkZGluZy1yaWdodDogMXB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgd2hpdGUtc3BhY2U6IG5vcm1hbDsNCiAgd29yZC1icmVhazogYnJlYWstYWxsOw0KICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KfQ0KDQoubXl0YWJsZSAubXlzcGFuIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMjBweDsNCiAgZGlzcGxheTogYmxvY2s7DQp9DQoNCi5teXRhYmxlIC5lcnJvclN0bGUgew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAyMHB4Ow0KICBkaXNwbGF5OiBibG9jazsNCiAgY29sb3I6IHJlZDsNCn0NCg0KLmFjY291bnQgLmZpbHRlci1kaXZpZGVyIHsNCiAgbWFyZ2luOiAwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLmFjY291bnQgLmhlYWRlci1iYXItc2hvdyB7DQogIG1heC1oZWlnaHQ6IDMwMHB4Ow0KICBwYWRkaW5nLXRvcDogMTRweDsNCiAgb3ZlcmZsb3c6IGluaGVyaXQ7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZThlYWVjOw0KfQ0KDQouYWNjb3VudCAuaGVhZGVyLWJhci1oaWRlIHsNCiAgbWF4LWhlaWdodDogMDsNCiAgcGFkZGluZy10b3A6IDA7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGJvcmRlci1ib3R0b206IDA7DQp9DQoNCi5teW1vZGFsIHAgew0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZm9udC1zaXplOiAxNDAlOw0KICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQp9DQoNCi5hY2NvdW50IGJ1dHRvbiB7DQogIG1hcmdpbi1yaWdodDogMTBweDsNCn0NCjo6di1kZWVwIC5jbC10YWJsZSAuaXZ1LXRhYmxlLWNlbGwgew0KICBwYWRkaW5nOiA2cHggNHB4ICFpbXBvcnRhbnQ7DQogIC5pdnUtaW5wdXQgew0KICAgIHBhZGRpbmc6IDRweCAhaW1wb3J0YW50Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["addSelfPowerAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0zGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addSelfPowerAccount.vue", "sourceRoot": "src/view/account/homePageAccount", "sourcesContent": ["<!--自有电费台账-->\r\n\r\n<template>\r\n  <div class=\"page-class page-card\">\r\n    <div class=\"query-box\">\r\n      <Row :class=\"filterColl ? 'header-bar-show' : 'header-bar-hide'\">\r\n        <Form ref=\"accountForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.accountno\"\r\n                  @on-change=\"accountnoChange\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"上期止度:\"\r\n                prop=\"prevtotalreadings\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <InputNumber\r\n                  v-model=\"accountObj.prevtotalreadings\"\r\n                  placeholder=\"请输入上期止度\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"局站名称:\" prop=\"stationName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.stationName\"\r\n                  placeholder=\"请输入局站名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"项目名称:\" prop=\"projectname\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.projectname\"\r\n                  placeholder=\"请输入项目名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\" v-if=\"'sc' == accountObj.version\">\r\n              <FormItem\r\n                label=\"电表/协议编码:\"\r\n                prop=\"ammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.ammetercode\"\r\n                  placeholder=\"请输入电表户号/协议编码\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"供电局电表编号:\"\r\n                prop=\"supplybureauammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.supplybureauammetercode\"\r\n                  placeholder=\"请输入供电局电表编号\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"用电类型:\" prop=\"classifications\" class=\"form-line-height\">\r\n                <Cascader\r\n                  clearable\r\n                  :data=\"classificationData\"\r\n                  v-model=\"classifications\"\r\n                  :style=\"formItemWidth\"\r\n                ></Cascader>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"是否退回：\" prop=\"status\" class=\"form-line-height\">\r\n                <Select clearable v-model=\"accountObj.status\" :style=\"formItemWidth\">\r\n                  <Option value=\"\">请选择</Option>\r\n                  <Option value=\"5\">是</Option>\r\n                  <Option value=\"1\">否</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize > 1\">\r\n              <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.company\"\r\n                  @on-change=\"selectChange(accountObj.company)\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option v-for=\"item in CompanyList\" :value=\"item.id\" :key=\"item.id\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize == 1 && resCenterListSize > 1\">\r\n              <FormItem label=\"所属部门：\" prop=\"country\" class=\"form-line-height\">\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option v-for=\"item in resCenterList\" :value=\"item.id\" :key=\"item.id\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\" v-if=\"companyListSize > 1\">\r\n              <FormItem label=\"所属部门：\" prop=\"country\" class=\"form-line-height\">\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option v-for=\"item in resCenterList\" :value=\"item.id\" :key=\"item.id\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize == 1 && resCenterListSize == 1\">\r\n              <FormItem label=\"台账填写人：\" prop=\"country\" class=\"form-line-height\">\r\n                <Input\r\n                  :style=\"formItemWidth\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"userName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  clearable\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize > 1 || resCenterListSize > 1\">\r\n              <FormItem label=\"台账填写人：\" prop=\"country\" class=\"form-line-height\">\r\n                <Input\r\n                  :style=\"formItemWidth\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"userName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  clearable\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"对外结算类型：\"\r\n                prop=\"directsupplyflag\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Select\r\n                  clearable\r\n                  v-model=\"accountObj.directsupplyflag\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option\r\n                    v-for=\"item in directsupplyflags\"\r\n                    :value=\"item.typeCode\"\r\n                    :key=\"item.typeCode\"\r\n                    >{{ item.typeName }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <div align=\"right\">\r\n            <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n            <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n            <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n              <Button type=\"default\" style=\"margin-left: 5px\"\r\n                >导出\r\n                <Icon type=\"ios-arrow-down\"></Icon>\r\n              </Button>\r\n              <DropdownMenu slot=\"list\">\r\n                <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n          </div>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon\r\n          :type=\"filterColl ? 'md-arrow-dropup' : 'md-arrow-dropdown'\"\r\n          size=\"20\"\r\n          @click=\"filterColl = !filterColl\"\r\n          :color=\"filterColl ? '#000' : '#1ab394'\"\r\n        ></icon>\r\n      </div>\r\n      <div>\r\n        <Modal\r\n          v-model=\"meterModal\"\r\n          title=\"峰平谷信息\"\r\n          width=\"50%\"\r\n          @on-ok=\"setFPG(currentRow)\"\r\n        >\r\n          <Form ref=\"meterForm\" :model=\"currentRow\" :label-width=\"80\" inline>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">本期起度</Col>\r\n              <Col span=\"8\" align=\"center\">本期止度</Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">加减</Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"峰:\" prop=\"prevhighreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editprevhighreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"addFremark(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"\" prop=\"curhighreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editcurhighreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"setcurhighreadings(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">\r\n                <FormItem label=\"\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"edithighreadings\"\r\n                    :readonly=\"readonly\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"平:\" prop=\"prevflatreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editprevflatreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"addPremark(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"\" prop=\"curflatreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editcurflatreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"setcurflatreadings(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">\r\n                <FormItem label=\"\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editflatreadings\"\r\n                    :readonly=\"readonly\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"谷:\" prop=\"prevlowreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editprevlowreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"addGremark(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"\" prop=\"curlowreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editcurlowreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"setcurlowreadings(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">\r\n                <FormItem label=\"\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editlowreadings\"\r\n                    :readonly=\"readonly\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n            </Row>\r\n          </Form>\r\n        </Modal>\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"startModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"startModalOk\"\r\n          @on-cancel=\"cancel\"\r\n          ><p>\r\n            是否确定更改本期起始日期？保存后从当前修改的起始日期前无法填入台帐！(可删除保存解除限制)\r\n          </p></Modal\r\n        >\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"qdModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"qdModalOk\"\r\n          @on-cancel=\"cancel\"\r\n          ><p>是否确定更改本期起度?</p></Modal\r\n        >\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"fbModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"fbModalOk\"\r\n          @on-cancel=\"qdcancel\"\r\n          ><p>是否翻表?</p></Modal\r\n        >\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"qxfbModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"qxfbModalOk\"\r\n          @on-cancel=\"qdcancel\"\r\n          ><p>是否取消翻表?</p></Modal\r\n        >\r\n      </div>\r\n    </div>\r\n    <div class=\"cl-table\">\r\n      <Row class=\"button-bar\">\r\n        <div class=\"table-button\">\r\n          <Button type=\"success\" @click=\"preserve()\">保存</Button>\r\n          <Button type=\"error\" @click=\"remove()\">删除</Button>\r\n          <Button type=\"error\" @click=\"deleteAll()\">一键删除</Button>\r\n          <Upload\r\n            style=\"float: right\"\r\n            :on-format-error=\"handleFormatError\"\r\n            :before-upload=\"onExcelUpload\"\r\n            :on-progress=\"handleProgress\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :max-size=\"10240\"\r\n            action=\"_blank\"\r\n            accept=\".csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\r\n            :format=\"['xls', 'xlsx']\"\r\n          >\r\n            <Button icon=\"ios-cloud-upload\">导入Excel</Button>\r\n          </Upload>\r\n          <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n            <Button type=\"info\" style=\"margin-left: 5px\"\r\n              >加入归集单\r\n              <Icon type=\"ios-arrow-down\"></Icon>\r\n            </Button>\r\n            <DropdownMenu slot=\"list\">\r\n              <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n              <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n          <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n        </div>\r\n      </Row>\r\n      <Table\r\n        ref=\"accountTable\"\r\n        border\r\n        :columns=\"accountTb.columns\"\r\n        :data=\"insideData\"\r\n        class=\"mytable\"\r\n        :loading=\"accountTb.loading\"\r\n        :height=\"tableHeight\"\r\n      >\r\n        <div></div>\r\n        <!--项目名称-->\r\n        <template slot-scope=\"{ row }\" slot=\"projectname\">\r\n          <div v-if=\"row.status === 5\">\r\n            <span>{{ row.projectname }}</span\r\n            ><span style=\"color: red\">[退回]</span>\r\n          </div>\r\n          <span v-else>{{ row.projectname }}</span>\r\n        </template>\r\n        <!--起始时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"startdate\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'startdate' + index + 1\"\r\n              class=\"myinput\"\r\n              type=\"text\"\r\n              @on-blur=\"validate\"\r\n              v-model=\"editStartDate\"\r\n              v-if=\"editIndex === index && columnsIndex === 1\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].startdate\"\r\n              @click=\"selectCall(row, index, 1, 'startdate')\"\r\n              v-else\r\n              >{{ row.startdate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.startdate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--截止时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"enddate\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'enddate' + index + 2\"\r\n              type=\"text\"\r\n              v-model=\"editEndDate\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 2\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].enddate\"\r\n              @click=\"selectCall(row, index, 2, 'enddate')\"\r\n              v-else\r\n              >{{ row.enddate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.enddate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--起度-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"prevtotalreadings\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'prevtotalreadings' + index + 3\"\r\n              type=\"text\"\r\n              v-model=\"editPrevtotalreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 3 && !row.isFPG && !row.isWB\"\r\n            />\r\n            <span @click=\"openModal(index)\" v-else-if=\"row.isFPG\">峰平谷</span>\r\n            <span\r\n              :class=\"myStyle[index].prevtotalreadings\"\r\n              @click=\"selectCall(row, index, 3, 'prevtotalreadings')\"\r\n              v-else\r\n              >{{ row.prevtotalreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span @click=\"openModal(index)\" v-if=\"row.isFPG\">峰平谷</span>\r\n            <span v-else>{{ row.prevtotalreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--止度-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"curtotalreadings\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'curtotalreadings' + index + 4\"\r\n              type=\"text\"\r\n              v-model=\"editcurtotalreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 4 && !row.isFPG && !row.isWB\"\r\n            />\r\n            <span @click=\"openModal(index)\" v-else-if=\"row.isFPG\">峰平谷</span>\r\n            <span\r\n              :class=\"myStyle[index].curtotalreadings\"\r\n              style=\"overflow-wrap: break-word\"\r\n              @click=\"selectCall(row, index, 4, 'curtotalreadings')\"\r\n              v-else\r\n              >{{ row.curtotalreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span @click=\"openModal(index)\" v-if=\"row.isFPG\">峰平谷</span>\r\n            <span v-else>{{ row.curtotalreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--电损-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"transformerullage\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'transformerullage' + index + 5\"\r\n              type=\"text\"\r\n              v-model=\"edittransformerullage\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 5 && !row.isWB\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].transformerullage\"\r\n              @click=\"selectCall(row, index, 5, 'transformerullage')\"\r\n              v-else\r\n              >{{ row.transformerullage }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.transformerullage }}</span>\r\n          </div>\r\n        </template>\r\n        <template slot-scope=\"{ row }\" slot=\"curusedreadings\">\r\n          <Tooltip\r\n            v-if=\"row.total == null\"\r\n            placement=\"top\"\r\n            max-width=\"200\"\r\n            :content=\"'上期电量:' + row.curusedreadingsold\"\r\n            :disabled=\"\r\n              row.ammeteruse == '1' &&\r\n              row.curusedreadingsold &&\r\n              (row.curusedreadings - row.curusedreadingsold) / row.curusedreadings > 0.2\r\n                ? false\r\n                : true\r\n            \"\r\n          >\r\n            <span\r\n              :style=\"\r\n                row.ammeteruse == '1' &&\r\n                row.curusedreadingsold &&\r\n                (row.curusedreadings - row.curusedreadingsold) / row.curusedreadings > 0.2\r\n                  ? { color: 'orange', 'font-size': '14px' }\r\n                  : {}\r\n              \"\r\n              >{{ row.curusedreadings }}</span\r\n            >\r\n          </Tooltip>\r\n          <span v-else>{{ row.curusedreadings }}</span>\r\n        </template>\r\n        <template slot-scope=\"{ row }\" slot=\"unitpirce\">\r\n          <Tooltip\r\n            v-if=\"row.total == null\"\r\n            placement=\"top\"\r\n            max-width=\"200\"\r\n            :content=\"'上期单价:' + row.unitpirceold\"\r\n            :disabled=\"\r\n              row.ammeteruse == '1' &&\r\n              row.unitpirceold &&\r\n              (row.unitpirce - row.unitpirceold) / row.unitpirce > 0.2\r\n                ? false\r\n                : true\r\n            \"\r\n          >\r\n            <span\r\n              :style=\"\r\n                row.ammeteruse == '1' &&\r\n                row.unitpirceold &&\r\n                (row.unitpirce - row.unitpirceold) / row.unitpirce > 0.2\r\n                  ? { color: 'orange', 'font-size': '14px' }\r\n                  : {}\r\n              \"\r\n              >{{ row.unitpirce }}</span\r\n            >\r\n          </Tooltip>\r\n          <span v-else>{{ row.unitpirce }}</span>\r\n        </template>\r\n        <!--普票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputticketmoney' + index + 6\"\r\n              type=\"text\"\r\n              v-model=\"editticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 6\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputticketmoney\"\r\n              @click=\"selectCall(row, index, 6, 'inputticketmoney')\"\r\n              v-else\r\n              >{{ row.inputticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--专票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputtaxticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputtaxticketmoney' + index + 7\"\r\n              type=\"text\"\r\n              v-model=\"edittaxticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 7\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputtaxticketmoney\"\r\n              @click=\"selectCall(row, index, 7, 'inputtaxticketmoney')\"\r\n              v-else\r\n              >{{ row.inputtaxticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputtaxticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--专票税率-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"taxrate\">\r\n          <div v-if=\"row.total == null\">\r\n            <Select\r\n              :ref=\"'taxrate' + index + 8\"\r\n              type=\"text\"\r\n              v-model=\"edittaxrate\"\r\n              @on-change=\"settaxrate\"\r\n              v-if=\"editIndex === index && columnsIndex === 8\"\r\n              transfer=\"true\"\r\n            >\r\n              <Option selected value=\"13\">13</Option>\r\n              <Option value=\"1\">1</Option>\r\n              <Option value=\"3\">3</Option>\r\n              <Option value=\"6\">6</Option>\r\n              <Option value=\"16\">16</Option>\r\n              <Option value=\"17\">17</Option>\r\n            </Select>\r\n            <span\r\n              :class=\"myStyle[index].taxrate\"\r\n              @click=\"selectCall(row, index, 8, 'taxrate')\"\r\n              v-else\r\n              >{{ row.taxrate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.taxrate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--其他-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"ullagemoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'ullagemoney' + index + 9\"\r\n              type=\"text\"\r\n              v-model=\"editullagemoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 9\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].ullagemoney\"\r\n              @click=\"selectCall(row, index, 9, 'ullagemoney')\"\r\n              v-else\r\n              >{{ row.ullagemoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.ullagemoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--备注-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              v-model=\"editremark\"\r\n              :ref=\"'remark' + index + 10\"\r\n              type=\"text\"\r\n              @on-blur=\"validateRemark\"\r\n              v-if=\"editIndex === index && columnsIndex === 10\"\r\n            />\r\n            <Tooltip placement=\"bottom\" max-width=\"200\" v-else>\r\n              <span\r\n                :class=\"myStyle[index].remark\"\r\n                style=\"width: 60px\"\r\n                @click=\"selectCall(row, index, 10, 'remark')\"\r\n                >{{ ellipsis(row) }}</span\r\n              >\r\n              <div slot=\"content\">\r\n                {{ row.remark + row.bz }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n          <div v-else>\r\n            <Tooltip placement=\"bottom\" max-width=\"200\">\r\n              <span>{{ ellipsis(row) }}</span>\r\n              <div slot=\"content\">\r\n                {{ row.remark + row.bz }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n        </template>\r\n      </Table>\r\n      <div class=\"table-page\">\r\n        <Page\r\n          size=\"small\"\r\n          :total=\"pageTotal\"\r\n          :current=\"pageNum\"\r\n          :page-size=\"pageSize\"\r\n          show-elevator\r\n          show-sizer\r\n          show-total\r\n          placement=\"top\"\r\n          @on-change=\"handlePage\"\r\n          @on-page-size-change=\"handlePageSize\"\r\n        ></Page>\r\n      </div>\r\n      <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n    </div>\r\n    <add-bill-per\r\n      ref=\"addBillPer\"\r\n      v-on:refreshList=\"refresh\"\r\n      @buttonload2=\"buttonload2\"\r\n      @isButtonload=\"isButtonload\"\r\n    ></add-bill-per>\r\n    <query-people-modal\r\n      ref=\"queryPeople\"\r\n      v-on:getDataFromModal=\"getDataFromModal\"\r\n    ></query-people-modal>\r\n    <upload-file-modal ref=\"uploadFileModal\"></upload-file-modal>\r\n\r\n    <!--    稽核modal-->\r\n    <Modal\r\n      v-model=\"showCheckModel\"\r\n      width=\"80%\"\r\n      title=\"稽核结果及反馈\"\r\n      :mask-closable=\"false\"\r\n    >\r\n      <check-result-and-response ref=\"chekResultAndResponse\"></check-result-and-response>\r\n    </Modal>\r\n    <Modal v-model=\"showJhModel\" width=\"80%\" title=\"稽核结果\" :mask-closable=\"false\">\r\n      <check-result ref=\"checkResult\"></check-result>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button type=\"primary\" @click=\"nextCheck\">已查阅</Button>\r\n        <Button type=\"text\" @click=\"checkCancel\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"showAlarmModel\"\r\n      width=\"80%\"\r\n      title=\"台账预警稽核\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n      class=\"yjjh\"\r\n    >\r\n      <alarm-check\r\n        :ymmc=\"'自有电费台账'\"\r\n        ref=\"showAlarmModel\"\r\n        @submitChange=\"submitChange\"\r\n        @save=\"save\"\r\n        @close=\"alarmClose\"\r\n      ></alarm-check>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button size=\"large\" @click=\"alarmClose\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { mapState } from \"vuex\";\r\nimport { getAuditResult, getAuditResultNew, getAuditResultNew_QXM } from \"@/api/account\";\r\nimport {\r\n  addSelfPowerAccount,\r\n  temporaryStorage,\r\n  getElectrQuota,\r\n  editOwn,\r\n  removeOwn,\r\n  selectByPcid,\r\n  getUser,\r\n  getDepartments,\r\n  selectByAmmeterId,\r\n  accountTotal,\r\n  selectCompletedMoney,\r\n  selectIdsByParams,\r\n  removeAll,\r\n} from \"@/api/account\";\r\nimport { getClassification, powerresult } from \"@/api/basedata/ammeter.js\";\r\nimport { againJoin } from \"@/api/accountBillPer\";\r\nimport { validContractList } from \"@/api/contract\";\r\nimport checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\nimport checkResult from \"@/view/account/check/checkResult\";\r\nimport alarmCheck from \"@/view/account/check/alarmCheck\";\r\nimport {\r\n  getDates,\r\n  cutDate_yyyymmdd,\r\n  testNumber,\r\n  GetDateDiff,\r\n  stringToDate,\r\n  getCurrentDate,\r\n} from \"@/view/account/powerAccountHelper\";\r\nimport {\r\n  _verify_StartDate,\r\n  judgeNumber,\r\n  _verify_EndDate,\r\n  _verify_PrevTotalReadings,\r\n  _verify_CurTotalReadings,\r\n  other_no_ammeteror_protocol,\r\n  self_no_ammeteror_protocol,\r\n  HFL_ammeteror,\r\n  judging_editability,\r\n  judging_editability1,\r\n  _verify_Money,\r\n  _calculateUsedReadings,\r\n  _calculateUsedReadingsForType_2,\r\n  _calculateTotalReadings,\r\n  _calculateUnitPriceByUsedMoney,\r\n  _calculateAccountMoney,\r\n  _calculateQuotereadingsratio,\r\n  requiredFieldValidator,\r\n  countTaxamount,\r\n  calculateActualMoney,\r\n  judge_negate,\r\n  judge_recovery,\r\n  judge_yb,\r\n  unitpirceMin,\r\n  unitpirceMax,\r\n  unitpirceMax1,\r\n} from \"@/view/account/PowerAccountController\";\r\nimport { widthstyle } from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nimport QueryPeopleModal from \"@/view/account/queryPeopleModal\";\r\nimport UploadFileModal from \"@/view/account/uploadFileModal\";\r\nimport AddBillPer from \"@/view/account/addBillPreModal\";\r\nimport excel from \"@/libs/excel\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport axios from \"@/libs/api.request\";\r\nimport indexData from \"@/config/index\";\r\n\r\nimport permissionMixin from \"@/mixins/permission\";\r\nimport pageFun from \"@/mixins/pageFun\";\r\n\r\nlet dates = getDates();\r\n\r\nexport default {\r\n  mixins: [permissionMixin, pageFun],\r\n\r\n  components: {\r\n    alarmCheck,\r\n    checkResult,\r\n    checkResultAndResponse,\r\n    UploadFileModal,\r\n    QueryPeopleModal,\r\n    AddBillPer,\r\n  },\r\n  data() {\r\n    let renderCategory = (h, params) => {\r\n      var categoryname = \"\";\r\n      for (let item of this.categorys) {\r\n        if (item.typeCode == params.row.category) {\r\n          categoryname = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", categoryname);\r\n    };\r\n    let renderDirectsupplyflag = (h, params) => {\r\n      var directsupplyflag = \"\";\r\n      for (let item of this.directsupplyflags) {\r\n        if (item.typeCode == params.row.directsupplyflag) {\r\n          directsupplyflag = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", directsupplyflag);\r\n    };\r\n    let photo = (h, { row, index }) => {\r\n      let that = this;\r\n      let str = \"\";\r\n      if (row.projectname != \"小计\" && row.projectname != \"合计\") {\r\n        str = \"上传\";\r\n      }\r\n      return h(\"div\", [\r\n        h(\r\n          \"u\",\r\n          {\r\n            on: {\r\n              click() {\r\n                //打开弹出框\r\n                if (row.projectname != \"小计\" && row.projectname != \"合计\") {\r\n                  that.uploadFile(row);\r\n                }\r\n              },\r\n            },\r\n          },\r\n          str\r\n        ),\r\n      ]);\r\n    };\r\n\r\n    return {\r\n      isT: true,\r\n      number2: 0,\r\n      name: \"\",\r\n      isQuery: true,\r\n      number: 0,\r\n      auditResultList: [],\r\n      submit: [],\r\n      submit2: [],\r\n      ammeterids: [],\r\n      showCheckModel: false,\r\n      showAlarmModel: false,\r\n      showJhModel: false,\r\n      version: indexData.version,\r\n      valiprice: true,\r\n      formatArray: [\"\"],\r\n      formItemWidth: widthstyle,\r\n      tableName: 1,\r\n      dateList: dates,\r\n      categorys: [], //描述类型\r\n      spinShow: false, //导入数据遮罩\r\n      filterColl: true, //搜索面板展开\r\n      startModal: false, //起始时间修改提示\r\n      qdModal: false, //起度修改提示\r\n      fbModal: false, //翻表提示\r\n      qxfbModal: false, //取消翻表提示\r\n      readonly: false, //封平谷只读\r\n      editIndex: -1, //当前编辑行\r\n      columnsIndex: -1, //当前编辑列\r\n      classificationData: [], //用电类型树\r\n      classifications: [], //选择的用电类型树\r\n      directsupplyflags: [],\r\n      editStartDate: \"\",\r\n      editEndDate: \"\",\r\n      editPrevtotalreadings: \"\",\r\n      editcurtotalreadings: \"\",\r\n      edittransformerullage: \"\",\r\n      edittaxticketmoney: \"\",\r\n      editticketmoney: \"\",\r\n      editullagemoney: \"\",\r\n      editpercent: \"\",\r\n      edittaxrate: \"\",\r\n      editremark: \"\",\r\n      editprevhighreadings: 0,\r\n      editprevflatreadings: 0,\r\n      editprevlowreadings: 0,\r\n      editcurhighreadings: 0,\r\n      editcurflatreadings: 0,\r\n      editcurlowreadings: 0,\r\n      edithighreadings: 0,\r\n      editflatreadings: 0,\r\n      editlowreadings: 0,\r\n      pabriid: \"\",\r\n      resCenterList: [],\r\n      CompanyList: [],\r\n      companyListSize: \"\",\r\n      resCenterListSize: \"\",\r\n      insideData: [], //数据\r\n      myStyle: [], //样式\r\n      pageTotal: 0,\r\n      pageNum: 1,\r\n      pageSize: 10, //当前页\r\n      currentRow: {},\r\n      meterModal: false,\r\n      ifMaxdegree: null,\r\n      againJoinIds: \"\",\r\n      export: {\r\n        run: false, //是否正在执行导出\r\n        data: \"\", //导出数据\r\n        totalPage: 0, //一共多少页\r\n        currentPage: 0, //当前多少页\r\n        percent: 0,\r\n        size: ********,\r\n      },\r\n      Accountqur: { ammeterid: \"\", startdate: \"\", enddate: \"\" },\r\n      accountObj: {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        stationName: \"\", //局站名称\r\n        projectname: \"\", //项目名称\r\n        prevtotalreadings: null, //上期止度\r\n        ammetercode: \"\", //电表户号/协议编码\r\n        status: \"\", //是否退回\r\n        company: \"\",\r\n        country: \"\",\r\n        electrotype: \"\", //用电类型\r\n        accountType: \"1\", //台账类型\r\n        userId: \"\",\r\n        version: \"\",\r\n        supplybureauammetercode: \"\",\r\n        directsupplyflag: \"\",\r\n        ammeterid: \"\",\r\n        startdate: \"\",\r\n        enddate: \"\",\r\n      },\r\n\r\n      userName: \"\", //查询选择人员名称\r\n      avemoney: 0,\r\n      nowdatediff: 0,\r\n      accountTb: {\r\n        loading: false,\r\n        columns: [],\r\n        headColumn: [\r\n          { type: \"selection\", width: 40, align: \"center\", fixed: \"left\" },\r\n          {\r\n            title: \"项目名称\",\r\n            slot: \"projectname\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n        ],\r\n        headColumn2: [\r\n          { type: \"selection\", width: 40, align: \"center\", fixed: \"left\" },\r\n          {\r\n            title: \"稽核结果及反馈\",\r\n            fixed: \"left\",\r\n            key: \"action\",\r\n            align: \"center\",\r\n            render: (h, params) => {\r\n              var that = this;\r\n              return h(\r\n                \"Button\",\r\n                {\r\n                  props: {\r\n                    type: \"primary\",\r\n                    size: \"small\",\r\n                  },\r\n                  style: {\r\n                    \"font-size\": \"10px\",\r\n                  },\r\n                  on: {\r\n                    click() {\r\n                      that.$refs.chekResultAndResponse.pcid = params.row.pcid;\r\n                      that.showCheckModel = true;\r\n                    },\r\n                  },\r\n                },\r\n                \"稽核结果及反馈\"\r\n              );\r\n            },\r\n            minWidth: 100,\r\n            maxWidth: 150,\r\n          },\r\n          {\r\n            title: \"项目名称\",\r\n            slot: \"projectname\",\r\n            align: \"center\",\r\n            width: 90,\r\n            fixed: \"left\",\r\n          },\r\n        ],\r\n        tailColumn: [\r\n          {\r\n            title: \"期号\",\r\n            key: \"accountno\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"用电类型\",\r\n            key: \"electrotypename\",\r\n            align: \"center\",\r\n            width: 94,\r\n          },\r\n          {\r\n            title: \"实际普票含税金额(元)\",\r\n            key: \"ticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n          },\r\n          {\r\n            title: \"实际专票含税金额(元)\",\r\n            key: \"taxticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n          },\r\n          {\r\n            title: \"总电量(度)\",\r\n            key: \"totalusedreadings\",\r\n            align: \"center\",\r\n            width: 60,\r\n          },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 160 },\r\n          {\r\n            title: \"类型描述\",\r\n            key: \"categoryname\",\r\n            align: \"center\",\r\n            width: 94,\r\n            render: renderCategory,\r\n          },\r\n          { title: \"倍率\", key: \"magnification\", align: \"center\", width: 60 },\r\n          { title: \"定额\", key: \"quotareadings\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"浮动比（%）\",\r\n            key: \"quotereadingsratio\",\r\n            align: \"center\",\r\n            width: 100,\r\n            sortable: true,\r\n            sortMethod: (a, b, type) => {\r\n              if (type === \"desc\") {\r\n                return parseInt(a) < parseInt(b) ? 1 : -1;\r\n              } else {\r\n                return parseInt(a) > parseInt(b) ? 1 : -1;\r\n              }\r\n            },\r\n          },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            align: \"center\",\r\n            fixed: \"left\",\r\n            width: 75,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            align: \"center\",\r\n            width: 75,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"本期起度\",\r\n            slot: \"prevtotalreadings\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"本期止度\",\r\n            slot: \"curtotalreadings\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"电损(度)\",\r\n            slot: \"transformerullage\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"用电量(度)\",\r\n            slot: \"curusedreadings\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"电价(元)\",\r\n            slot: \"unitpirce\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"普票含税金额(元)\",\r\n            slot: \"inputticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"专票含税金额(元)\",\r\n            slot: \"inputtaxticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"专票税率（%）\",\r\n            slot: \"taxrate\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"专票税额\",\r\n            key: \"taxamount\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"其他(元)\",\r\n            slot: \"ullagemoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"实缴费用(元)含税\",\r\n            key: \"accountmoney\",\r\n            align: \"center\",\r\n            width: 65,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"备注\",\r\n            slot: \"remark\",\r\n            align: \"center\",\r\n            width: 100,\r\n            fixed: \"left\",\r\n          },\r\n          { title: \"分割比例(%)\", align: \"center\", key: \"percent\", width: 80 },\r\n          {\r\n            title: \"对外结算类型\",\r\n            align: \"center\",\r\n            key: \"directsupplyflag\",\r\n            width: 80,\r\n            render: renderDirectsupplyflag,\r\n          },\r\n        ],\r\n        fileColumn: [\r\n          {\r\n            title: \"附件\",\r\n            slot: \"file\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n            render: photo,\r\n          },\r\n        ],\r\n        lnColumn: [\r\n          {\r\n            title: \"供电局电表编号\",\r\n            key: \"supplybureauammetercode\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n        ],\r\n        scColumn: [\r\n          {\r\n            title: \"电表户号/协议编码\",\r\n            key: \"ammetercode\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"供电局电表编号\",\r\n            key: \"supplybureauammetercode\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n        ],\r\n\r\n        data: [],\r\n        exportcolumns: [\r\n          { title: \"错误信息\", key: \"error\" },\r\n          { title: \"注意信息\", key: \"careful\" },\r\n          { title: \"项目名称\", key: \"projectname\" },\r\n          { title: \"期号\", key: \"accountno\" },\r\n          { title: \"电表/协议id\", key: \"ammeterid\" },\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\" },\r\n          { title: \"起始日期(必填)\", key: \"startdate\" },\r\n          { title: \"截止日期(必填)\", key: \"enddate\" },\r\n          { title: \"本期峰段起度(峰平谷必填)\", key: \"prevhighreadings\" },\r\n          { title: \"本期平段起度(峰平谷必填)\", key: \"prevflatreadings\" },\r\n          { title: \"本期谷段起度(峰平谷必填)\", key: \"prevlowreadings\" },\r\n          { title: \"本期起度(普通电表必填)\", key: \"prevtotalreadings\" },\r\n          { title: \"本期峰段止度(峰平谷必填)\", key: \"curhighreadings\" },\r\n          { title: \"本期平段止度(峰平谷必填)\", key: \"curflatreadings\" },\r\n          { title: \"本期谷段止度(峰平谷必填)\", key: \"curlowreadings\" },\r\n          { title: \"本期止度(普通电表必填)\", key: \"curtotalreadings\" },\r\n          { title: \"电损(度)(可填)\", key: \"transformerullage\" },\r\n          { title: \"专票含税金额(元)(必填)\", key: \"inputtaxticketmoney\" },\r\n          { title: \"专票税率（%）(必填)\", key: \"taxrate\" },\r\n          { title: \"专票税额\", key: \"taxamount\" },\r\n          { title: \"普票含税金额(元)(必填)\", key: \"inputticketmoney\" },\r\n          { title: \"其他(元)(可填)\", key: \"ullagemoney\" },\r\n          { title: \"实缴费用(元)含税\", key: \"accountmoney\" },\r\n          { title: \"类型描述\", key: \"categoryname\" },\r\n          { title: \"倍率\", key: \"magnification\" },\r\n          { title: \"定额\", key: \"quotareadings\" },\r\n          { title: \"浮动比（%）\", key: \"quotereadingsratio\" },\r\n          { title: \"用电量(度)\", key: \"curusedreadings\" },\r\n          { title: \"总电量(度)\", key: \"totalusedreadings\" },\r\n          { title: \"电价(元)\", key: \"unitpirce\" },\r\n          { title: \"备注\", key: \"remark\" },\r\n          { title: \"分割比例(%)\", key: \"percent\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    buttonload2(v) {\r\n      this.$refs.showAlarmModel.buttonload2 = v;\r\n    },\r\n    isButtonload(v) {\r\n      this.$refs.showAlarmModel.buttonload = v;\r\n    },\r\n    isShows(t) {\r\n      this.$refs.showAlarmModel.show = t;\r\n      if (this.$refs.showAlarmModel.show == false) {\r\n        this.number2++;\r\n        this.isT = t;\r\n      } else if (this.$refs.showAlarmModel.show == true) {\r\n        this.number2 = 0;\r\n        this.isT = !t;\r\n      }\r\n      if (this.isT && this.number2 < 10) {\r\n        this.isShows(t);\r\n      }\r\n    },\r\n    nextCheck() {\r\n      this.showAlarmModel = true;\r\n      this.isShows(true);\r\n      // this.$refs.showAlarmModel.show=true\r\n      this.$refs.showAlarmModel.tableData = [];\r\n      this.$refs.showAlarmModel.resultList = [];\r\n      this.$refs.showAlarmModel.tableData1 = [];\r\n      this.$refs.showAlarmModel.resultList1 = [];\r\n      this.$refs.showAlarmModel.tableData2 = [];\r\n      this.$refs.showAlarmModel.resultList2 = [];\r\n      this.$refs.showAlarmModel.tableData3 = [];\r\n      this.$refs.showAlarmModel.resultList3 = [];\r\n      this.$refs.showAlarmModel.tableData4 = [];\r\n      this.$refs.showAlarmModel.resultList4 = [];\r\n      this.$refs.showAlarmModel.tableData5 = [];\r\n      this.$refs.showAlarmModel.resultList5 = [];\r\n      // setTimeout(() => {\r\n      this.showJhModel = false;\r\n      // this.showAlarmModel=true;\r\n      this.$refs.showAlarmModel.activeButton = 6;\r\n      // },100)\r\n    },\r\n    alarmClose() {\r\n      // window.history.go(0);\r\n      this.showAlarmModel = false;\r\n      this.$refs.showAlarmModel.show = false;\r\n    },\r\n    checkCancel() {\r\n      this.showJhModel = false;\r\n    },\r\n    alarmCheck() {},\r\n    selectChange() {\r\n      getDepartments(this.accountObj.company).then((res) => {\r\n        this.resCenterList = res.data;\r\n        this.resCenterListSize = res.data.length;\r\n        this.accountObj.country = res.data[0].id;\r\n      });\r\n    },\r\n    // 计算定额\r\n    getQuota: (ammeterid, startdate, enddate, callback) => {\r\n      if (ammeterid && startdate && enddate) {\r\n        getElectrQuota(ammeterid, startdate, enddate).then((res) => {\r\n          if (callback) callback(res);\r\n          else callback();\r\n        });\r\n      }\r\n    },\r\n    //删除时检查退回台账是否解除与归集单关联\r\n    getTem: (pcid, callback) => {\r\n      selectByPcid(pcid).then((res) => {\r\n        if (callback) callback(res);\r\n        else callback();\r\n      });\r\n    },\r\n    //翻页时先确认数据是否保存\r\n    handlePage(value) {\r\n      let b = false;\r\n      let data = this.insideData;\r\n      let array = [];\r\n      data.forEach(function (item, index, input) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n      this.pageNum = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //改变表格可显示数据数量时先确认数据是否保存\r\n    handlePageSize(value) {\r\n      let b = false;\r\n      let data = this.insideData;\r\n      let array = [];\r\n      data.forEach(function (item, index, input) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n      this.pageSize = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //向后台请求数据\r\n    getAccountMessages() {\r\n      this.setElectroyType();\r\n      let params = this.accountObj;\r\n      params.pageNum = this.pageNum;\r\n      params.pageSize = this.pageSize;\r\n      let req = {\r\n        url: \"/business/account/selfAccountList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      this.accountTb.loading = true;\r\n      axios\r\n        .request(req)\r\n        .then((res) => {\r\n          this.accountTb.loading = false;\r\n          if (res.data) {\r\n            array = res.data.rows;\r\n            array.push(this.suntotal(array)); //小计\r\n            accountTotal(this.accountObj).then((res) => {\r\n              //合计\r\n              let alltotal = res.data;\r\n              alltotal.total = \"合计\";\r\n              alltotal.projectname = \"合计\";\r\n              alltotal._disabled = true;\r\n              array.push(alltotal);\r\n            });\r\n            this.insideData = array;\r\n            this.pageTotal = res.data.total || 0;\r\n            // debugger\r\n            this.setNewField(res.data.rows);\r\n            this.setMyStyle(res.data.rows.length);\r\n            this.editIndex = -1;\r\n            this.columnsIndex = -1;\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //小计\r\n    suntotal(array) {\r\n      let curusedreadings = 0;\r\n      let transformerullage = 0;\r\n      let ticketmoney = 0;\r\n      let taxticketmoney = 0;\r\n      let taxamount = 0;\r\n      let ullagemoney = 0;\r\n      let accountmoney = 0;\r\n      let inputtaxticketmoney = 0;\r\n      let inputticketmoney = 0;\r\n      array.forEach(function (item) {\r\n        if (item.effective === 1) {\r\n          curusedreadings += item.curusedreadings;\r\n          transformerullage += item.transformerullage;\r\n          ticketmoney += item.ticketmoney;\r\n          taxticketmoney += item.taxticketmoney;\r\n          taxamount += item.taxamount;\r\n          inputtaxticketmoney += item.inputtaxticketmoney;\r\n          inputticketmoney += item.inputticketmoney;\r\n          ullagemoney += item.ullagemoney;\r\n          accountmoney += item.accountmoney;\r\n        }\r\n      });\r\n      return {\r\n        curusedreadings: curusedreadings,\r\n        transformerullage: transformerullage,\r\n        ticketmoney: ticketmoney.toFixed(2),\r\n        taxticketmoney: taxticketmoney.toFixed(2),\r\n        taxamount: taxamount.toFixed(2),\r\n        inputtaxticketmoney: inputtaxticketmoney.toFixed(2),\r\n        inputticketmoney: inputticketmoney.toFixed(2),\r\n        ullagemoney: ullagemoney.toFixed(2),\r\n        accountmoney: accountmoney.toFixed(2),\r\n        total: \"小计\",\r\n        projectname: \"小计\",\r\n        _disabled: true,\r\n      };\r\n    },\r\n    searchList() {\r\n      this.pageNum = 1;\r\n      this.getAccountMessages();\r\n    },\r\n    onResetHandle() {\r\n      this.accountObj = {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        substation: \"\", //支局\r\n        projectname: \"\", //项目名称\r\n        prevtotalreadings: null, //上期止度\r\n        ammetercode: \"\", //电表户号/协议编码\r\n        isreturn: \"\", //是否退回\r\n        company: this.CompanyList[0].id,\r\n        userId: \"\",\r\n        accountType: \"1\", //台账类型\r\n        supplybureauammetercode: \"\",\r\n      };\r\n\r\n      this.accountObj.version = indexData.version;\r\n      this.userName = \"\";\r\n      this.classifications = [];\r\n      this.selectChange();\r\n      this.getAccountMessages();\r\n    },\r\n    //保存可编辑表格的初始化数据\r\n    setNewField(data) {\r\n      data.forEach(function (item) {\r\n        item.old_startdate = item.startdate;\r\n        item.old_prevtotalreadings = item.prevtotalreadings;\r\n        item.multtimes = item.magnification;\r\n        item.old_enddate = item.enddate;\r\n        item.old_curtotalreadings = item.curtotalreadings;\r\n        item.old_transformerullage = item.transformerullage;\r\n        item.old_taxticketmoney = item.inputtaxticketmoney;\r\n        item.old_ticketmoney = item.inputticketmoney;\r\n        item.old_ullagemoney = item.ullagemoney;\r\n        item.old_prevhighreadings = item.prevhighreadings;\r\n        item.old_prevflatreadings = item.prevflatreadings;\r\n        item.old_prevlowreadings = item.prevlowreadings;\r\n\r\n        item.old_curhighreadings = item.curhighreadings;\r\n        item.old_curflatreadings = item.curflatreadings;\r\n        item.old_curlowreadings = item.curlowreadings;\r\n        item.old_curtotalreadings = item.curtotalreadings;\r\n\r\n        item.version = indexData.version;\r\n        item.editType = 0;\r\n        item.isFPG = judging_editability1(item);\r\n        item.isWB = judging_editability(item);\r\n        if (!item.remark) item.remark = \"\";\r\n        if (!item.bz) item.bz = \"\";\r\n        item.transformerullage = judgeNumber(item.transformerullage);\r\n        // item.supplybureauammetercode = judgeNumber(item.supplybureauammetercode);\r\n        item.inputtaxticketmoney = judgeNumber(item.inputtaxticketmoney);\r\n        item.inputticketmoney = judgeNumber(item.inputticketmoney);\r\n        item.taxticketmoney = judgeNumber(item.taxticketmoney);\r\n        item.ticketmoney = judgeNumber(item.ticketmoney);\r\n        item.ullagemoney = judgeNumber(item.ullagemoney);\r\n        item.curusedreadings = judgeNumber(item.curusedreadings);\r\n        item.accountmoney = judgeNumber(item.accountmoney);\r\n        if ((item.taxrate == null || item.taxrate == 0) && item.total == null) {\r\n          item.taxrate = \"13\";\r\n        }\r\n        if (item.taxrate && item.taxamount == null) {\r\n          item.taxamount = countTaxamount(item);\r\n        }\r\n      });\r\n    },\r\n    //计算 用电量,总电量,单价,总费用,浮动比.\r\n    calculateAll(row) {\r\n      console.log(row, \"row\");\r\n      row.curusedreadings = _calculateUsedReadings(row);\r\n      row.totalusedreadings = _calculateTotalReadings(row);\r\n      if (row.ischangeammeter == 1 && row.isnew == 1) {\r\n        if (row.oldbillpower > 0) {\r\n          let total = Math.abs(row.totalusedreadings) + Math.abs(row.oldbillpower);\r\n          let category = row.category;\r\n          let ammeteruse = row.ammeteruse; //电表用途\r\n          if (judge_negate(category) || judge_recovery(ammeteruse)) {\r\n            total = -total;\r\n          }\r\n          row.totalusedreadings = total;\r\n        }\r\n        let remark = row.remark;\r\n        if (remark.indexOf(\"换表\") == -1) {\r\n          row.remark += \"换表，结清原电表读数【\" + row.oldbillpower + \"】；\";\r\n        }\r\n      }\r\n      if (row.ticketmoney || row.taxticketmoney) {\r\n        row.accountmoney = _calculateAccountMoney(row);\r\n        row.unitpirce = _calculateUnitPriceByUsedMoney(row);\r\n      }\r\n      row.quotereadingsratio = _calculateQuotereadingsratio(row);\r\n    },\r\n    //暂存\r\n    temporaryStorage() {\r\n      let array = [];\r\n      this.insideData.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          array.push(item);\r\n        }\r\n      });\r\n      let data = array;\r\n      //--------------------------------------\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n          item.startyear = yyyymmdd.yyyy;\r\n          item.startmonth = yyyymmdd.mm;\r\n          yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n          item.endyear = yyyymmdd.yyyy;\r\n          item.endmonth = yyyymmdd.mm;\r\n          item.accountno = no;\r\n          submitData.push(item);\r\n          number++;\r\n        });\r\n        if (submitData.length > 0) {\r\n          temporaryStorage(submitData).then((res) => {\r\n            if (res.data.num > 0) {\r\n              that.$Message.info({\r\n                content: \"成功暂存\" + res.data.num + \"条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n            }\r\n            that.pageNum = 1;\r\n            that.getAccountMessages();\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //四川能耗稽核流程\r\n    preserveSc() {\r\n      let arr = [];\r\n      this.ammeterids.forEach((item1) => {\r\n        if (arr.indexOf(item1) == -1) {\r\n          arr.push(item1);\r\n        }\r\n      });\r\n      this.$refs.checkResult.ammeterids = arr;\r\n      this.showJhModel = true;\r\n    },\r\n    async preserve() {\r\n      let dataL = this.$refs.accountTable.getSelection();\r\n\r\n      let b = false;\r\n      let data = this.insideData;\r\n      let array = [];\r\n      let that = this;\r\n\r\n      let version = indexData.version;\r\n      for (let i = 0; i < dataL.length; i++) {\r\n        if (dataL[i].editType == 1) {\r\n          if (\"sc\" == version && dataL[i].electrotype && dataL[i].electrotype > 1400) {\r\n            if (\r\n              dataL[i].stationcode5gr == null ||\r\n              dataL[i].stationcode5gr == undefined ||\r\n              dataL[i].stationcode5gr == \"\"\r\n            ) {\r\n              this.errorTips(\r\n                \"电表/协议编号【\" +\r\n                  dataL[i].ammetercode +\r\n                  \"】，项目名称【\" +\r\n                  dataL[i].projectname +\r\n                  \"】关联局站的5GR站址为空，请完善局站信息，或者5GR有效性清单失效，请联系无线管理员。\"\r\n              );\r\n            }\r\n          }\r\n          //截止日期校验\r\n          let result = await this.handleEndDate(dataL[i], dataL[i].enddate);\r\n          if (result) {\r\n            this.errorTips(result);\r\n            return;\r\n          }\r\n          let maxdegree = parseInt(dataL[i].maxdegree); //翻表度数,即电表的最大度数\r\n          if (maxdegree != null && maxdegree > 0) {\r\n            if (dataL[i].curtotalreadings > maxdegree) {\r\n              that.errorTips(\"本期止度不能大于翻表值：\" + maxdegree);\r\n            } else {\r\n              b = true;\r\n              array.push(dataL[i]);\r\n            }\r\n          } else {\r\n            b = true;\r\n            array.push(dataL[i]);\r\n          }\r\n        }\r\n      }\r\n      // });\r\n      if (b) {\r\n        this.submitData(array);\r\n      } else {\r\n        this.errorTips(\"没有可保存数据\");\r\n      }\r\n    },\r\n    async getContractInfo(stationCode) {\r\n      this.contractCount = 0;\r\n      await validContractList({ stationCode: stationCode }).then((res) => {\r\n        if (res.data) {\r\n          this.contractCount = res.data.length;\r\n        }\r\n      });\r\n    },\r\n    //提交数据\r\n    submitData(data) {\r\n      // this.ammeterids=[];\r\n      let a = [];\r\n      let str = \"\";\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        let str1 = \"\";\r\n        data.forEach(function (item) {\r\n          let obj = requiredFieldValidator(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n        });\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          that.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n        if (submitData.length > 0) {\r\n          //四川能耗需做稽核流程\r\n          this.submit = submitData;\r\n          this.submit2 = submitData;\r\n          editOwn(submitData).then((res) => {\r\n            if (res.data.num > 0) {\r\n              that.$Message.info({\r\n                content: \"成功保存\" + res.data.num + \"条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n            }\r\n\r\n            if (res.data.str.length > 0) {\r\n              that.errorTips(res.data.str);\r\n            }\r\n            that.pageNum = 1;\r\n            that.getAccountMessages();\r\n          });\r\n        }\r\n      }\r\n    },\r\n    submitChange(indexList) {\r\n      let data = [];\r\n      this.submit2.map((item, index) => {\r\n        indexList.map((item2) => {\r\n          if (index == item2) {\r\n            data.push(item);\r\n          }\r\n        });\r\n      });\r\n      this.submit = data;\r\n    },\r\n    getAuditResultNew(data) {\r\n      let arr = [];\r\n      data.forEach((item) => {\r\n        arr.push(item.pcid);\r\n      });\r\n      let param = {\r\n        pcids: arr,\r\n      };\r\n      getAuditResultNew_QXM(param).then((res2) => {\r\n        this.auditResultList = res2.data;\r\n\r\n        if (this.auditResultList && this.auditResultList.length == 0) {\r\n          this.number++;\r\n          this.isQuery = true;\r\n          // }\r\n        } else if (data.length != this.auditResultList.length) {\r\n          this.number++;\r\n          this.isQuery = true;\r\n        } else if (data.length == this.auditResultList.length) {\r\n          this.isQuery = false;\r\n          this.number = 0;\r\n        } else {\r\n          this.isQuery = false;\r\n          this.number = 0;\r\n        }\r\n        if (this.isQuery && this.number < 5) {\r\n          setTimeout(() => this.getAuditResultNew(data), 6000);\r\n        } else {\r\n          this.auditResultList.forEach((item) => {\r\n            this.$refs.showAlarmModel.resultList.push(item.msg);\r\n            this.$refs.showAlarmModel.tableData.push(item.powerAuditEntity);\r\n\r\n            if (item.staute == \"失败\") {\r\n              // if(item.powerAuditEntity.mutiJtlteCodes=='是'\r\n              // || item.powerAuditEntity.electricityPrices=='否'\r\n              // || item.powerAuditEntity.addressConsistence=='否'\r\n              // || item.powerAuditEntity.reimbursementCycle=='否' || item.powerAuditEntity.electricityContinuity=='否' ||\r\n              // item.powerAuditEntity.shareAccuracy=='否' ||\r\n              // item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n              // item.powerAuditEntity.paymentConsistence=='否'){\r\n              if (item.powerAuditEntity.mutiJtlteCodes == \"是\") {\r\n                //一站多表\r\n                this.$refs.showAlarmModel.tableData4.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList4.push(item.msg);\r\n              }\r\n              if (item.powerAuditEntity.electricityPrices == \"否\") {\r\n                //单价异常\r\n                this.$refs.showAlarmModel.tableData5.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList5.push(item.msg);\r\n              }\r\n              if (\r\n                item.powerAuditEntity.addressConsistence == \"否\" ||\r\n                item.powerAuditEntity.reimbursementCycle == \"否\" ||\r\n                item.powerAuditEntity.electricityContinuity == \"否\" ||\r\n                item.powerAuditEntity.shareAccuracy == \"否\" ||\r\n                // item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n                item.powerAuditEntity.paymentConsistence == \"否\" ||\r\n                item.powerAuditEntity.fluctuateContinuity == \"否\"\r\n              ) {\r\n                this.$refs.showAlarmModel.tableData2.push(item.powerAuditEntity); //其他异常\r\n                this.$refs.showAlarmModel.resultList2.push(item.msg);\r\n              }\r\n              // }\r\n            } else {\r\n              if (\r\n                // item.powerAuditEntity.electricityRationality == \"是\" && //电量合理性(省内大数据)\r\n                // item.powerAuditEntity.exclusiveAccuracy == \"是\" && //局站独享共享设置\r\n                item.powerAuditEntity.periodicAnomaly == \"是\" //台账周期合理性\r\n              ) {\r\n                this.$refs.showAlarmModel.tableData1.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList1.push(item.msg);\r\n              } else {\r\n                this.$refs.showAlarmModel.tableData3.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList3.push(item.msg);\r\n              }\r\n            }\r\n            if (this.auditResultList.length > 0) {\r\n              this.auditResultList[this.auditResultList.length - 1].progress =\r\n                ((this.auditResultList.length * 1) / data.length) * 1;\r\n            }\r\n            this.$refs.showAlarmModel.processData = Number(item.progress) * 100;\r\n            this.$refs.showAlarmModel.scrollList();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    save(value) {\r\n      var that = this;\r\n      //进度条页面链接websoket后调用，传jh字段不走保存流程，走稽核流程\r\n      if (value == 1) {\r\n        that.submit[0].jh = \"1\";\r\n        that.submit[0].ymmc = \"自有电费台账\";\r\n        this.getAuditResultNew(that.submit);\r\n      } else {\r\n        if (that.submit[0].hasOwnProperty(\"jh\")) {\r\n          delete that.submit[0].jh;\r\n        }\r\n        if (this.name == \"current\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAccount();\r\n          }\r\n        } else if (this.name == \"all\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAllAccount();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //加入归集单，全部非强控\r\n    selectedFQK() {\r\n      let that = this;\r\n      that.setElectroyType();\r\n      that.$refs.addBillPer.initAmmeter(\r\n        that.$refs.showAlarmModel.selectIds3,\r\n        1,\r\n        this.accountObj.country\r\n      );\r\n    },\r\n    //添加修改峰平谷值的备注\r\n    addFremark(row) {\r\n      let old = row.old_prevhighreadings;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (\r\n        row.curhighreadings != null &&\r\n        row.curhighreadings > 0 &&\r\n        this.editprevhighreadings > row.curhighreadings\r\n      ) {\r\n        this.errorTips(\"起始峰值不能大于截止峰值\" + row.curhighreadings);\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editprevhighreadings = old;\r\n        }, 200);\r\n      } else {\r\n        row.prevhighreadings = this.editprevhighreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n        if (row.old_prevhighreadings != row.prevhighreadings) {\r\n          row.remark +=\r\n            \"本期起始峰值 从\" +\r\n            row.old_prevhighreadings +\r\n            \"修改为\" +\r\n            row.prevhighreadings +\r\n            \"; \";\r\n        }\r\n        //修改opflag修改起日期 原来数字加\r\n        let opflag = row.opflag;\r\n        if (opflag != 3 && opflag != 5 && opflag != 7 && opflag != 9) {\r\n          row.opflag = opflag + 3;\r\n        }\r\n      }\r\n    },\r\n    //起始平值变换记录备注\r\n    addPremark(row) {\r\n      let old = row.old_prevflatreadings;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (\r\n        row.curflatreadings != null &&\r\n        row.curflatreadings > 0 &&\r\n        this.editprevflatreadings > row.curflatreadings\r\n      ) {\r\n        this.errorTips(\"起始平值不能大于截止平值\" + row.curflatreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editprevflatreadings = old;\r\n        }, 200);\r\n      } else {\r\n        row.prevflatreadings = this.editprevflatreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n        if (row.old_prevflatreadings != row.prevflatreadings) {\r\n          row.remark +=\r\n            \"本期起始平值 从\" +\r\n            row.old_prevflatreadings +\r\n            \"修改为\" +\r\n            row.prevflatreadings +\r\n            \"; \";\r\n        }\r\n        //修改opflag修改起日期 原来数字加\r\n        let opflag = row.opflag;\r\n        if (opflag != 3 && opflag != 5 && opflag != 7 && opflag != 9) {\r\n          row.opflag = opflag + 3;\r\n        }\r\n      }\r\n    },\r\n    //起始谷值变换记录备注\r\n    addGremark(row) {\r\n      let old = row.old_prevlowreadings;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (\r\n        row.curlowreadings != null &&\r\n        row.curlowreadings > 0 &&\r\n        this.editprevlowreadings > row.curlowreadings\r\n      ) {\r\n        this.errorTips(\"起始谷值不能大于截止谷值\" + row.curlowreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editprevlowreadings = old;\r\n        }, 200);\r\n      } else {\r\n        row.prevlowreadings = this.editprevlowreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n        if (row.old_prevlowreadings != row.prevlowreadings) {\r\n          row.remark +=\r\n            \"本期起始谷值 从\" +\r\n            row.old_prevlowreadings +\r\n            \"修改为\" +\r\n            row.prevlowreadings +\r\n            \"; \";\r\n        }\r\n        //修改opflag修改起日期 原来数字加\r\n        let opflag = row.opflag;\r\n        if (opflag != 3 && opflag != 5 && opflag != 7 && opflag != 9) {\r\n          row.opflag = opflag + 3;\r\n        }\r\n      }\r\n    },\r\n    setcurhighreadings(row) {\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (this.editcurhighreadings < row.prevhighreadings) {\r\n        this.errorTips(\"截止峰值不能小于起始峰值\" + row.prevhighreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editcurhighreadings = row.curhighreadings;\r\n        }, 200);\r\n      } else {\r\n        row.curhighreadings = this.editcurhighreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n      }\r\n    },\r\n    setcurflatreadings(row) {\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (this.editcurflatreadings < row.prevflatreadings) {\r\n        this.errorTips(\"截止平值不能小于起始平值\" + row.prevflatreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editcurflatreadings = row.curflatreadings;\r\n        }, 200);\r\n      } else {\r\n        row.curflatreadings = this.editcurflatreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n      }\r\n    },\r\n    setcurlowreadings(row) {\r\n      let next = row.ifNext;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (this.editcurlowreadings < row.prevlowreadings) {\r\n        this.errorTips(\"截止谷值不能小于起始谷值\" + row.prevlowreadings);\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editcurlowreadings = row.curlowreadings;\r\n        }, 200);\r\n      } else {\r\n        row.curlowreadings = this.editcurlowreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n      }\r\n    },\r\n    setFPG(row) {\r\n      let item = {\r\n        prevhighreadings: row.prevhighreadings,\r\n        prevflatreadings: row.prevflatreadings,\r\n        prevlowreadings: row.prevlowreadings,\r\n        curhighreadings: row.curhighreadings,\r\n        curflatreadings: row.curflatreadings,\r\n        curlowreadings: row.curlowreadings,\r\n        highreadings: parseFloat(this.edithighreadings.toFixed(2)),\r\n        flatreadings: parseFloat(this.editflatreadings.toFixed(2)),\r\n        lowreadings: parseFloat(this.editlowreadings.toFixed(2)),\r\n        magnification: row.magnification,\r\n      };\r\n      let amount = _calculateUsedReadingsForType_2(item);\r\n\r\n      if (amount < 0) {\r\n        //计算用电量\r\n        this.errorTips(\r\n          \"计算用电量小于0(\" +\r\n            amount +\r\n            \"),请确认峰平谷加减电量值！\" +\r\n            \"加减电量(峰值)\" +\r\n            parseFloat(this.edithighreadings.toFixed(2)) +\r\n            \",加减电量(平值)\" +\r\n            parseFloat(this.editflatreadings.toFixed(2)) +\r\n            \",加减电量(谷值)\" +\r\n            parseFloat(this.editlowreadings.toFixed(2)) +\r\n            \"当前用电量\" +\r\n            row.curusedreadings\r\n        );\r\n        // return;\r\n      }\r\n      row.highreadings = parseFloat(this.edithighreadings.toFixed(2));\r\n      row.flatreadings = parseFloat(this.editflatreadings.toFixed(2));\r\n      row.lowreadings = parseFloat(this.editlowreadings.toFixed(2));\r\n      row.editType = 1;\r\n      this.calculateAll(row);\r\n    },\r\n    //一键删除数据\r\n    deleteAll() {\r\n      this.$Modal.confirm({\r\n        title: \"提示\",\r\n        content: \"<p>确定一键删除吗？</p>\",\r\n        onOk: () => {\r\n          this.accountTb.loading = true;\r\n          let params = this.accountObj;\r\n          params.removeAllFlag = true;\r\n          delete params.pageSize;\r\n          delete params.pageNum;\r\n          removeAll(params).then((res) => {\r\n            this.accountTb.loading = false;\r\n            if (res.data.num > 0) {\r\n              this.$Message.success(\"一键删除成功\");\r\n              this.searchList();\r\n            } else {\r\n              this.$Message.error(\"一键删除失败\");\r\n            }\r\n          });\r\n        },\r\n        onCancel: () => {},\r\n      });\r\n    },\r\n    //删除行数据\r\n    remove() {\r\n      let version = indexData.version;\r\n      let data = this.$refs.accountTable.getSelection();\r\n      if (data == null || data.length === 0) {\r\n        this.errorTips(\"请选择要删除的台账\");\r\n        return;\r\n      }\r\n      let ids = \"\";\r\n      let that = this;\r\n      let str = \"\";\r\n      data.forEach(function (item) {\r\n        if (item.ifNext) {\r\n          str +=\r\n            \"电表/协议编号为【\" +\r\n            item.ammetercode +\r\n            \"】当期【\" +\r\n            item.accountno +\r\n            \"期】台账之后已有正式数据，不能删除！\";\r\n        }\r\n        ids += item.pcid + \",\";\r\n      });\r\n      if (ids.length > 0 && str.length === 0) {\r\n        that.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>是否确认删除选中信息？</p>\",\r\n          onOk: () => {\r\n            removeOwn(ids).then((res) => {\r\n              if (res.data.num > 0) {\r\n                that.$Message.info({\r\n                  content: \"成功删除\" + res.data.num + \"条数据\",\r\n                  duration: 10,\r\n                  closable: true,\r\n                });\r\n              }\r\n\r\n              if (res.data.str.length > 0) {\r\n                that.errorTips(res.data.str);\r\n              }\r\n              that.searchList();\r\n            });\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      } else {\r\n        that.errorTips(str);\r\n      }\r\n    },\r\n    //span点击事件将span换成输入框并且获取焦点\r\n    selectCall(row, index, columns, str) {\r\n      this.editStartDate = row.startdate;\r\n      this.editEndDate = row.enddate;\r\n      this.editPrevtotalreadings =\r\n        row.prevtotalreadings == null || row.prevtotalreadings === 0\r\n          ? null\r\n          : row.prevtotalreadings;\r\n      this.editcurtotalreadings =\r\n        row.curtotalreadings == null || row.curtotalreadings === 0\r\n          ? null\r\n          : row.curtotalreadings;\r\n      this.edittransformerullage =\r\n        row.transformerullage == null || row.transformerullage === 0\r\n          ? null\r\n          : row.transformerullage;\r\n      this.edittaxticketmoney =\r\n        row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n          ? null\r\n          : row.inputtaxticketmoney;\r\n      this.editticketmoney =\r\n        row.inputticketmoney == null || row.inputticketmoney === 0\r\n          ? null\r\n          : row.inputticketmoney;\r\n      this.editullagemoney =\r\n        row.ullagemoney == null || row.ullagemoney === 0 ? null : row.ullagemoney;\r\n      this.edittaxrate =\r\n        row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n      this.editremark = row.bz;\r\n      this.editIndex = index;\r\n      this.columnsIndex = columns;\r\n\r\n      let a = this;\r\n      setTimeout(function () {\r\n        if (columns != 8) {\r\n          a.$refs[str + index + columns].focus();\r\n        }\r\n      }, 200);\r\n    },\r\n    //根据列号返回对应的列名\r\n    enterOperate(number) {\r\n      let str = \"\";\r\n      let data = null;\r\n      switch (number) {\r\n        case 1:\r\n          str = \"startdate\";\r\n          data = this.editStartDate;\r\n          break;\r\n        case 2:\r\n          str = \"enddate\";\r\n          data = this.editEndDate;\r\n          break;\r\n        case 3:\r\n          str = \"prevtotalreadings\";\r\n          data = this.editPrevtotalreadings;\r\n          break;\r\n        case 4:\r\n          str = \"curtotalreadings\";\r\n          data = this.editcurtotalreadings;\r\n          break;\r\n        case 5:\r\n          str = \"transformerullage\";\r\n          data = this.edittransformerullage;\r\n          break;\r\n        case 6:\r\n          str = \"inputticketmoney\";\r\n          data = this.editticketmoney;\r\n          break;\r\n        case 7:\r\n          str = \"inputtaxticketmoney\";\r\n          data = this.edittaxticketmoney;\r\n          break;\r\n        case 8:\r\n          str = \"taxrate\";\r\n          data = this.edittaxrate;\r\n          break;\r\n        case 9:\r\n          str = \"ullagemoney\";\r\n          data = this.editullagemoney;\r\n          break;\r\n        case 10:\r\n          str = \"remark\";\r\n          data = this.editremark;\r\n          break;\r\n      }\r\n      return { str: str, data: data };\r\n    },\r\n    //输入数据验证\r\n    validate() {\r\n      if (this.columnsIndex === 10) {\r\n        this.validateRemark();\r\n        return;\r\n      }\r\n      let val = this.enterOperate(this.columnsIndex).data;\r\n      if (val) {\r\n        if (testNumber(val)) {\r\n          switch (this.columnsIndex) {\r\n            case 1:\r\n              this.validateStartdate();\r\n              break;\r\n            case 2:\r\n              this.validateEnddate();\r\n              break;\r\n            case 3:\r\n              this.validatePrevtotalreadings();\r\n              break;\r\n            case 4:\r\n              this.validateCurtotalreadings();\r\n              break;\r\n            case 5:\r\n              this.validateTransformerullage();\r\n              break;\r\n            case 6:\r\n              this.validateTicketmoney();\r\n              break;\r\n            case 7:\r\n              this.validateTaxticketmoney();\r\n              break;\r\n            case 9:\r\n              this.validateUllagemoney();\r\n              break;\r\n          }\r\n        } else {\r\n          this.errorTips(\"请输入数字！\");\r\n        }\r\n      }\r\n    },\r\n    //验证错误弹出提示框并跳转到下一格\r\n    errorTips(str) {\r\n      this.$Notice.error({\r\n        title: \"提示\",\r\n        desc: str,\r\n        duration: 10,\r\n      });\r\n    },\r\n    //跳转到下一格\r\n    nextCell(data) {\r\n      let index = data.editIndex;\r\n      let columns = data.columnsIndex;\r\n      let row = \"\";\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n      } else if (index > -1 && columns === 10) {\r\n        //当跳转的最后一行最后一格的时候\r\n        if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n          index = 0;\r\n        } else {\r\n          index++;\r\n        }\r\n        columns = 1;\r\n      } else {\r\n        row = data.insideData[index];\r\n        //无表或峰平谷表的时候\r\n        if (row && (row.isFPG || row.isWB) && columns >= 2 && columns <= 4) {\r\n          if (row.isWB) {\r\n            columns += 4;\r\n          } else {\r\n            columns += 3;\r\n          }\r\n        } else {\r\n          columns += 1;\r\n        }\r\n        //有下期的台账不能改\r\n        if (row.ifNext) {\r\n          if (columns < 5) {\r\n            columns = 5;\r\n          }\r\n        }\r\n      }\r\n      data.editIndex = index;\r\n      data.columnsIndex = columns;\r\n      row = data.insideData[index];\r\n      if (row) {\r\n        data.editStartDate = row.startdate;\r\n        data.editEndDate = row.enddate;\r\n        data.editPrevtotalreadings =\r\n          row.prevtotalreadings == null || row.prevtotalreadings === 0\r\n            ? null\r\n            : row.prevtotalreadings;\r\n        data.editcurtotalreadings =\r\n          row.curtotalreadings == null || row.curtotalreadings === 0\r\n            ? null\r\n            : row.curtotalreadings;\r\n        data.edittransformerullage =\r\n          row.transformerullage == null || row.transformerullage === 0\r\n            ? null\r\n            : row.transformerullage;\r\n        data.edittaxticketmoney =\r\n          row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n            ? null\r\n            : row.inputtaxticketmoney;\r\n        data.editticketmoney =\r\n          row.inputticketmoney == null || row.inputticketmoney === 0\r\n            ? null\r\n            : row.inputticketmoney;\r\n        data.editullagemoney =\r\n          row.ullagemoney == null || row.ullagemoney === 0 ? null : row.ullagemoney;\r\n        data.edittaxrate =\r\n          row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n        data.editremark = row.bz;\r\n      }\r\n\r\n      setTimeout(function () {\r\n        if (columns != 8) {\r\n          data.$refs[data.enterOperate(columns).str + index + columns].focus();\r\n        }\r\n      }, 200);\r\n    },\r\n    //验证起始时间\r\n    validateStartdate() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editStartDate;\r\n      if (val != data.old_startdate) {\r\n        // 验证起始时间方法\r\n        let result = _verify_StartDate(data, val, \"可大于\");\r\n        if (result) {\r\n          //失败就弹出提示内容，并将数据恢复初始化\r\n          this.errorTips(result);\r\n          this.myStyle[this.editIndex].startdate = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].startdate = \"myspan\";\r\n          this.startModal = true;\r\n        }\r\n      } else if (val == data.old_startdate) {\r\n        data.startdate = val;\r\n      }\r\n    },\r\n    //验证截止时间\r\n    async validateEnddate() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editEndDate;\r\n      if (val != data.old_enddate) {\r\n        // 验证截止日期方法\r\n        let result = await this.handleEndDate(data, val);\r\n        if (result) {\r\n          this.errorTips(result);\r\n          this.myStyle[this.editIndex].enddate = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].enddate = \"myspan\";\r\n\r\n          this.updateenddate(data, val);\r\n        }\r\n      } else if (val == data.old_enddate) {\r\n        data.enddate = val;\r\n      }\r\n    },\r\n    //截止日期处理\r\n    async handleEndDate(data, val) {\r\n      //直供电有上传日期才可以修改到月底 directsupplyflag 1直供2转供\r\n      let fType = \"\";\r\n      let curval = stringToDate(val); //输入值\r\n      let nowdate = stringToDate(getCurrentDate()); //当天\r\n      if (data.directsupplyflag == 1 && curval > nowdate) {\r\n        let files = await axios\r\n          .request({\r\n            url: \"/common/attachments/list\",\r\n            method: \"post\",\r\n            data: {\r\n              areaCode: \"sc\",\r\n              busiAlias: \"附件(台账)\",\r\n              busiId: data.pcid + \"\",\r\n              categoryCode: \"file\",\r\n              pageNum: 1,\r\n              pageSize: 20,\r\n            },\r\n          })\r\n          .then((res) => {\r\n            return res.data.rows;\r\n          });\r\n        if (files.length == 0) {\r\n          return \"截止日期需小于等于当前时间，超过当前时间需上传附件\";\r\n        } else {\r\n          fType = \"限制期号\"; //截止日期不限制期号的最后一天（月底）\r\n        }\r\n      }\r\n      // 验证截止日期方法\r\n      let result = _verify_EndDate(data, val, fType);\r\n      return result;\r\n    },\r\n    updateenddate(data, val) {\r\n      data.enddate = val;\r\n      data.editType = 1;\r\n      //计算定额\r\n      this.getQuota(data.ammeterid, data.startdate, data.enddate, (result) => {\r\n        if (result.data.code === 0) {\r\n          data.quotareadings = Math.round(result.data.msg);\r\n        } else {\r\n          data.quotareadings = 0;\r\n        }\r\n        this.calculateAll(data);\r\n      });\r\n      this.nowdatediff = GetDateDiff(data.startdate, data.enddate);\r\n    },\r\n    //验证起度\r\n    validatePrevtotalreadings() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editPrevtotalreadings;\r\n      val = parseFloat(val);\r\n      if (val != data.old_prevtotalreadings) {\r\n        //验证\r\n        let result = _verify_PrevTotalReadings(data, val);\r\n        if (result.string) {\r\n          this.errorTips(result.string);\r\n          this.myStyle[this.editIndex].prevtotalreadings = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].prevtotalreadings = \"myspan\";\r\n          this.ifMaxdegree = result.b;\r\n          this.qdModal = true;\r\n        }\r\n      } else if (val == data.old_prevtotalreadings) {\r\n        data.prevtotalreadings = val;\r\n        this.calculateAll(data);\r\n      }\r\n    },\r\n    //验证止度\r\n    validateCurtotalreadings() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editcurtotalreadings;\r\n\r\n      if (val != data.old_curtotalreadings) {\r\n        val = parseFloat(val);\r\n        let result = _verify_CurTotalReadings(data, val);\r\n        if (result.string) {\r\n          this.errorTips(result.string);\r\n          this.myStyle[this.editIndex].curtotalreadings = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].curtotalreadings = \"myspan\";\r\n\r\n          this.updateCurtotalreadings(data, val, result);\r\n        }\r\n\r\n        let computreading = 0;\r\n        this.Accountqur.ammeterid = data.ammeterid;\r\n        this.Accountqur.startdate = data.startdate;\r\n        this.Accountqur.enddate = data.enddate;\r\n      } else if (val == data.old_curtotalreadings) {\r\n        data.curtotalreadings = val;\r\n\r\n        this.calculateAll(data);\r\n      }\r\n    },\r\n    updateCurtotalreadings(data, val, result) {\r\n      data.curtotalreadings = val;\r\n      data.editType = 1;\r\n      let b = result.b;\r\n      if (b === true) {\r\n        this.fbModal = true;\r\n      } else if (b === false) {\r\n        this.qxfbModal = true;\r\n      } else {\r\n        this.calculateAll(data);\r\n      }\r\n      if (indexData.version == \"sc\") {\r\n        //验证上期台账是否完成报账\r\n        axios\r\n          .request({\r\n            url: \"/business/accountSC/valOldAcount\",\r\n            method: \"post\",\r\n            params: { pcid: data.pcid, ammeterid: data.ammeterid },\r\n          })\r\n          .then((res) => {\r\n            let msg = \"\";\r\n            if (res.data.msg) msg = res.data.msg;\r\n            if (data.startdate.endsWith(\"0101\"))\r\n              msg += \"【该起始日期是默认值，请注意修改】\";\r\n            if (msg != \"\")\r\n              this.$Notice.warning({\r\n                title: \"注意\",\r\n                desc: \"电表/协议【\" + data.ammetercode + \"】\" + msg,\r\n                duration: 10,\r\n              });\r\n            if (res.data.acc) {\r\n              Object.assign(data, {\r\n                unitpirceold: res.data.acc.unitpirce,\r\n                curusedreadingsold: res.data.acc.curusedreadings,\r\n              });\r\n            }\r\n          });\r\n      }\r\n    },\r\n    //验证电损\r\n    validateTransformerullage() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.edittransformerullage;\r\n      let flag = false;\r\n      if (val != data.old_transformerullage) {\r\n        if (\r\n          val != data.old_transformerullage &&\r\n          indexData.version == \"sc\" &&\r\n          data.curusedreadings > 0 &&\r\n          parseFloat(parseFloat(val) / parseFloat(data.curusedreadings)) > 0.1 &&\r\n          (data.ammeteruse == 1 || [1, 3].includes(data.ammeteruse))\r\n        )\r\n          flag = true;\r\n        if (flag) {\r\n          let result = \"电损与实际电量比值已经超过10%，不允许保存\";\r\n          this.errorTips(result);\r\n\r\n          this.myStyle[this.editIndex].transformerullage = \"errorStle\";\r\n          data.transformerullage = 0;\r\n        } else {\r\n          val = parseFloat(val);\r\n          data.transformerullage = val;\r\n          data.editType = 1;\r\n          this.calculateAll(data);\r\n        }\r\n      } else if (val == data.old_transformerullage) {\r\n        data.transformerullage = val;\r\n        this.calculateAll(data);\r\n      }\r\n    },\r\n    //验证专票\r\n    validateTaxticketmoney() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.edittaxticketmoney;\r\n      if (val != data.old_taxticketmoney) {\r\n        val = parseFloat(val);\r\n        data.inputtaxticketmoney = _verify_Money(data, val);\r\n        data.editType = 1;\r\n        data.taxticketmoney = calculateActualMoney(data, val);\r\n        data.taxamount = countTaxamount(data);\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_taxticketmoney) {\r\n        data.inputtaxticketmoney = val;\r\n        data.taxticketmoney = calculateActualMoney(data, val);\r\n        data.taxamount = countTaxamount(data);\r\n        this.calculateAll(data);\r\n      }\r\n      this.validateUnitPrice(data);\r\n      this.validateavemoney(data);\r\n    },\r\n    //验证普票\r\n    validateTicketmoney() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editticketmoney;\r\n      if (val != data.old_ticketmoney) {\r\n        val = parseFloat(val);\r\n        data.inputticketmoney = _verify_Money(data, val);\r\n        data.editType = 1;\r\n        data.ticketmoney = calculateActualMoney(data, val);\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_ticketmoney) {\r\n        data.inputticketmoney = val;\r\n        data.ticketmoney = calculateActualMoney(data, val);\r\n        this.calculateAll(data);\r\n      }\r\n      this.validateUnitPrice(data);\r\n      this.validateavemoney(data);\r\n    },\r\n    //验证其他费用\r\n    validateUllagemoney() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editullagemoney;\r\n      if (val != data.old_ullagemoney) {\r\n        val = parseFloat(val);\r\n        data.ullagemoney = _verify_Money(data, val);\r\n        data.editType = 1;\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_ullagemoney) {\r\n        data.ullagemoney = val;\r\n        this.calculateAll(data);\r\n      }\r\n      this.validateUnitPrice(data);\r\n      this.validateavemoney(data);\r\n    },\r\n    //备注\r\n    validateRemark() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editremark;\r\n      data.bz = val;\r\n      data.editType = 1;\r\n    },\r\n    validateavemoney(data) {\r\n      let version = indexData.version;\r\n      if (\"sc\" == version) {\r\n        let accountmoney = data.accountmoney;\r\n        let aveold = judgeNumber(data.aveaccountmoneyold);\r\n        if (this.nowdatediff == 0)\r\n          this.nowdatediff = GetDateDiff(data.startdate, data.enddate);\r\n        if (aveold != 0 && (accountmoney / this.nowdatediff).toFixed(2) - aveold > 0) {\r\n          if (((accountmoney / this.nowdatediff).toFixed(2) - aveold) / aveold > 0.3)\r\n            this.$Notice.warning({\r\n              title: \"温馨提示\",\r\n              desc:\r\n                \"电表/协议【\" +\r\n                data.ammetercode +\r\n                \"】\" +\r\n                \"日均电费环比值已经超过30%，请注意填写备注说明！\",\r\n              duration: 10,\r\n            });\r\n        }\r\n      }\r\n    },\r\n    //验证单价\r\n    validateUnitPrice(data) {\r\n      let version = indexData.version;\r\n      let category = data.category; //电表描述类型\r\n      let directsupplyflag = data.directsupplyflag; //1直供2转供\r\n      let ammeteruse = data.ammeteruse; //电表用途\r\n      let unitpirce = data.unitpirce; //台账单价\r\n      if (!judge_negate(category) && !judge_recovery(ammeteruse) && judge_yb(category)) {\r\n        if (unitpirce) {\r\n          if (unitpirce != null && unitpirce < unitpirceMax1) {\r\n            // if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n            this.errorTips(\r\n              \"单价范围必须大于0.1元，此台账单价: \" + unitpirce + \"不在范围内，请确认！\"\r\n            );\r\n          }\r\n          // if (directsupplyflag == 1 && ammeteruse == 1) {\r\n          //   if (unitpirce) {\r\n          //     if (unitpirce >= 0.25 && unitpirce < 0.5) {\r\n          //       this.errorTips(\r\n          //         \"直供电单价(\" +\r\n          //           unitpirce +\r\n          //           \")【0.25<=\" +\r\n          //           unitpirce +\r\n          //           \"<0.5】\" +\r\n          //           \"请确认单价是否存在错误\"\r\n          //       );\r\n          //     } else if (unitpirce < 0.25 || unitpirce > 1.2) {\r\n          //       this.errorTips(\r\n          //         \"直供电单价(\" + unitpirce + \")【小于0.25或大于1.20】\" + \"单价，请确认！\"\r\n          //       );\r\n          //     }\r\n          //   }\r\n          // } else if (directsupplyflag == 2) {\r\n          //   if (unitpirce >= 0.3 && unitpirce < 0.6) {\r\n          //     this.errorTips(\r\n          //       \"转供电单价(\" +\r\n          //         unitpirce +\r\n          //         \")【0.3<=\" +\r\n          //         unitpirce +\r\n          //         \"<0.6】\" +\r\n          //         \"请确认单价是否存在错误\"\r\n          //     );\r\n          //   } else if (unitpirce < 0.3) {\r\n          //     this.errorTips(\r\n          //       \"转供电单价(\" +\r\n          //         unitpirce +\r\n          //         \")【\" +\r\n          //         unitpirce +\r\n          //         \"<0.3】\" +\r\n          //         \"单价错误，请确认！\"\r\n          //     );\r\n          //   } else if (unitpirce > 1.5) {\r\n          //     this.errorTips(\r\n          //       \"转供电单价(\" +\r\n          //         unitpirce +\r\n          //         \")【\" +\r\n          //         unitpirce +\r\n          //         \">1.5】\" +\r\n          //         \"请确认单价是否存在错误\"\r\n          //     );\r\n          //   }\r\n          // }\r\n        }\r\n      }\r\n    },\r\n    openModal(index) {\r\n      this.meterModal = true; //弹出框显示\r\n      let row = this.insideData[index];\r\n      if (row.accountno == dates[1].code) {\r\n        let obj = {\r\n          accountno: dates[0].code,\r\n          ammeterid: row.ammeterid,\r\n        };\r\n        selectByAmmeterId(obj).then((res) => {\r\n          row.nextData = res.data;\r\n        });\r\n      }\r\n      if (row) {\r\n        if (row.ifNext) {\r\n          this.readonly = true;\r\n        } else {\r\n          this.readonly = false;\r\n        }\r\n\r\n        this.currentRow = row; //给弹出框绑定数据\r\n        this.editprevhighreadings =\r\n          row.prevhighreadings === null ? 0 : row.prevhighreadings;\r\n        this.editprevflatreadings =\r\n          row.prevflatreadings === null ? 0 : row.prevflatreadings;\r\n        this.editprevlowreadings = row.prevlowreadings === null ? 0 : row.prevlowreadings;\r\n        this.editcurhighreadings = row.curhighreadings === null ? 0 : row.curhighreadings;\r\n        this.editcurflatreadings = row.curflatreadings === null ? 0 : row.curflatreadings;\r\n        this.editcurlowreadings = row.curlowreadings === null ? 0 : row.curlowreadings;\r\n        if (this.version == \"sc\") {\r\n          this.edithighreadings = row.highreadings === null ? 0 : row.highreadings;\r\n          this.editflatreadings = row.flatreadings === null ? 0 : row.flatreadings;\r\n          this.editlowreadings = row.lowreadings === null ? 0 : row.lowreadings;\r\n        }\r\n      }\r\n    },\r\n    openAddBillPerModal(name) {\r\n      this.name = name;\r\n      if (name === \"current\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJ();\r\n        // } else {\r\n        this.selectedAccount();\r\n        // }\r\n      } else if (name === \"all\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJAll();\r\n        // } else {\r\n        this.selectedAllAccount();\r\n        // }\r\n      }\r\n    },\r\n    //加入归集单，全部有效台账\r\n    selectedAllAccount() {\r\n      let that = this;\r\n      that.setElectroyType();\r\n      that.spinShow = true;\r\n      selectIdsByParams(this.accountObj).then((res) => {\r\n        that.spinShow = false;\r\n        if (res.data.str) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: res.data.str,\r\n            duration: 0,\r\n          });\r\n        }\r\n        if (res.data.ids) {\r\n          if (res.data.ids.length == 0) {\r\n            that.errorTips(\"无有效数据可加入归集单\");\r\n          } else {\r\n            that.$refs.addBillPer.initAmmeter(\r\n              res.data.ids,\r\n              // this.$refs.showAlarmModel.selectIds1,\r\n              1,\r\n              this.accountObj.country\r\n            );\r\n          }\r\n        } else {\r\n          that.errorTips(\"无有效数据可加入归集单\");\r\n        }\r\n      });\r\n    },\r\n    //加入归集单\r\n    addPreserveGJ() {\r\n      let dataL = this.$refs.accountTable.getSelection();\r\n      if (dataL == null || dataL.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        this.addSubmitDataGJ(dataL);\r\n      }\r\n    },\r\n\r\n    //加入归集单\r\n    addPreserveGJAll() {\r\n      let params = this.accountObj;\r\n      params.pageNum = 1;\r\n      params.pageSize = 20000;\r\n      let req = {\r\n        url: \"/business/account/selfAccountList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      this.accountTb.loading = true;\r\n      axios.request(req).then((res) => {\r\n        this.accountTb.loading = false;\r\n        let dataL = res.data.rows;\r\n        this.addSubmitDataGJ(dataL);\r\n      });\r\n    },\r\n    //提交归集单数据\r\n    addSubmitDataGJ(data1) {\r\n      let a = [];\r\n      let b = 1;\r\n      let data = data1.filter((item) => item.effective == 1);\r\n      let str = \"\";\r\n      let str1 = \"\";\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let obj = requiredFieldValidator(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status != 1) {\r\n            b = 3;\r\n          }\r\n          if (\r\n            \"sc\" == version &&\r\n            item.unitpirce > 2 &&\r\n            (item.unitpirceold == null || item.unitpirceold < 2) &&\r\n            that.valiprice\r\n          ) {\r\n            b = 4;\r\n            str += item.ammetercode + \",\";\r\n          }\r\n        });\r\n        if (b == 1) {\r\n          if (str1.length > 0) {\r\n            this.$Notice.warning({\r\n              title: \"注意\",\r\n              desc: str1,\r\n              duration: 0,\r\n            });\r\n          }\r\n          if (submitData.length > 0) {\r\n            this.submit = submitData;\r\n            this.submit2 = submitData;\r\n            this.preserveSc();\r\n          }\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\r\n            \"电表/协议编号为【\" +\r\n              str +\r\n              \"】的台账单价已经超过2元，请发OA邮件给省公司审核，通过后才可加入归集单！\"\r\n          );\r\n        }\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          that.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //加入归集单，已选择的台账\r\n    selectedAccount() {\r\n      let data = this.$refs.accountTable.getSelection();\r\n      let b = 1;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        let ids = [];\r\n        data.forEach(function (item) {\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n          ids.push(item.pcid);\r\n        });\r\n        if (b === 1) {\r\n          this.$refs.addBillPer.initAmmeter(ids, 1, this.accountObj.country);\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n      }\r\n    },\r\n    setMyStyle(length) {\r\n      this.myStyle = [];\r\n      for (var i = 0; i < length; i++) {\r\n        this.myStyle.push({\r\n          startdate: \"myspan\",\r\n          enddate: \"myspan\",\r\n          prevtotalreadings: \"myspan\",\r\n          curtotalreadings: \"myspan\",\r\n          transformerullage: \"myspan\",\r\n          inputtaxticketmoney: \"myspan\",\r\n          inputticketmoney: \"myspan\",\r\n          ullagemoney: \"myspan\",\r\n          taxrate: \"myspan\",\r\n          remark: \"myspan\",\r\n        });\r\n      }\r\n    },\r\n    submitChange1(data) {\r\n      this.submit = data;\r\n    },\r\n    refresh() {\r\n      if (this.$refs.showAlarmModel.fromGuijidan != 1) {\r\n        // window.history.go(0);\r\n        let obj = this;\r\n        obj.showAlarmModel = false;\r\n        setTimeout(function () {\r\n          obj.getAccountMessages();\r\n        }, 200);\r\n      } else {\r\n        this.showAlarmModel = true;\r\n      }\r\n    },\r\n    againJoin() {\r\n      let data = this.$refs.accountTable.getSelection();\r\n      let b = true;\r\n      var that = this;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要重新加入归集单的台账\");\r\n      } else {\r\n        let againJoinIds = \"\";\r\n        data.forEach(function (item) {\r\n          let status = item.status;\r\n          if (status != 5) {\r\n            b = false;\r\n          } else {\r\n            againJoinIds += item.pcid + \",\";\r\n          }\r\n        });\r\n        if (b) {\r\n          againJoin(againJoinIds).then((res) => {\r\n            if (res.data.code == 0) {\r\n              that.$Message.info({\r\n                content: \"提示：操作成功\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              that.getAccountMessages();\r\n            }\r\n          });\r\n        } else {\r\n          that.errorTips(\"只有已退回的台账才能重新加入归集单\");\r\n        }\r\n      }\r\n    },\r\n    beforeLoadData(data, str) {\r\n      var cols = [],\r\n        keys = [];\r\n      for (var i = 0; i < this.accountTb.exportcolumns.length; i++) {\r\n        cols.push(this.accountTb.exportcolumns[i].title);\r\n        keys.push(this.accountTb.exportcolumns[i].key);\r\n      }\r\n      const params = {\r\n        title: cols,\r\n        key: keys,\r\n        data: data,\r\n        autoWidth: true,\r\n        filename: str,\r\n      };\r\n      excel.export_array_to_excel(params);\r\n      return;\r\n    },\r\n    exportCsv(name) {\r\n      this.setElectroyType();\r\n      let params = this.accountObj;\r\n\r\n      if (name === \"current\") {\r\n        params.pageNum = this.pageNum;\r\n        params.pageSize = this.pageSize;\r\n      } else if (name === \"all\") {\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n      }\r\n      let req = {\r\n        url: \"/business/account/exportzy\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      this.spinShow = true;\r\n      axios\r\n        .file(req)\r\n        .then((res) => {\r\n          this.spinShow = false;\r\n          const content = res;\r\n          const blob = new Blob([content]);\r\n          const fileName = \"自有台账导出数据\" + \".xlsx\";\r\n          if (\"download\" in document.createElement(\"a\")) {\r\n            // 非IE下载\r\n            const elink = document.createElement(\"a\");\r\n            elink.download = fileName;\r\n            elink.style.display = \"none\";\r\n            elink.href = URL.createObjectURL(blob);\r\n            document.body.appendChild(elink);\r\n            elink.click();\r\n            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n            document.body.removeChild(elink);\r\n          } else {\r\n            // IE10+下载\r\n            navigator.msSaveBlob(blob, fileName);\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //专票税额\r\n    settaxrate() {\r\n      let val = this.edittaxrate;\r\n      let data = this.insideData[this.editIndex];\r\n      let taxticketmoney = data.taxticketmoney;\r\n      data.taxrate = val;\r\n      data.taxamount = countTaxamount(data);\r\n      data.editType = 1;\r\n    },\r\n    startModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editStartDate;\r\n      data.startdate = val; //修改起始日期\r\n      data.editType = 1;\r\n      //计算定额\r\n      this.getQuota(data.ammeterid, data.startdate, data.enddate, (result) => {\r\n        if (result.data.code === 0) {\r\n          data.quotareadings = Math.round(result.data.msg);\r\n        } else {\r\n          data.quotareadings = 0;\r\n        }\r\n        this.calculateAll(data);\r\n      });\r\n      let opflag = data.opflag;\r\n      //修改opflag修改起日期 原来数字加\r\n      if (opflag != 4 && opflag != 6 && opflag != 7 && opflag != 9) {\r\n        data.opflag = opflag + 4;\r\n      }\r\n      data.remark += \"本期起始日期 从\" + data.old_startdate + \"修改为\" + val + \"; \";\r\n      this.startModal = false;\r\n      this.nextCell(this);\r\n    },\r\n    cancel() {\r\n      this.nextCell(this);\r\n    },\r\n    hcyzstartdate(lett, index) {\r\n      let data = lett.insideData[index];\r\n      let val = lett.editStartDate;\r\n      if (val) {\r\n        if (testNumber(val)) {\r\n          if (val == data.old_startdate) {\r\n            data.startdate = val;\r\n\r\n            lett.nextCell(lett);\r\n          } else {\r\n            lett.validate();\r\n          }\r\n        } else {\r\n          lett.errorTips(\"请输入数字！\");\r\n          lett.nextCell(lett);\r\n        }\r\n      } else {\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    qdModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editPrevtotalreadings;\r\n\r\n      data.prevtotalreadings = val;\r\n      let b = this.ifMaxdegree;\r\n      if (b === true) {\r\n        this.fbModal = true;\r\n      } else if (b === false) {\r\n        this.qxfbModal = true;\r\n      } else {\r\n        this.calculateAll(data);\r\n      }\r\n\r\n      data.editType = 1;\r\n      let opflag = data.opflag;\r\n      //增加4 修改起日期 原来数字加\r\n      if (opflag != 2 && opflag != 5 && opflag != 6 && opflag != 9) {\r\n        data.opflag = opflag + 2;\r\n      }\r\n      data.remark += \"本期起度 从\" + data.old_prevtotalreadings + \"修改为\" + val + \"; \";\r\n\r\n      this.qdModal = false;\r\n      this.nextCell(this);\r\n    },\r\n    fbModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      data.ifMaxdegree = true;\r\n      this.fbModal = false;\r\n\r\n      this.calculateAll(data);\r\n    },\r\n    qxfbModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      data.ifMaxdegree = false;\r\n      this.qxfbModal = false;\r\n\r\n      this.calculateAll(data);\r\n    },\r\n    hcyzprevtotalreadings(lett, index) {\r\n      let data = lett.insideData[index];\r\n      let val = lett.editPrevtotalreadings;\r\n      if (val) {\r\n        if (testNumber(val)) {\r\n          if (val == data.old_prevtotalreadings) {\r\n            data.prevtotalreadings = val;\r\n            lett.nextCell(lett);\r\n          } else {\r\n            lett.validate();\r\n          }\r\n        } else {\r\n          lett.errorTips(\"请输入数字！\");\r\n          lett.nextCell(lett);\r\n        }\r\n      } else {\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    qdcancel() {\r\n      if (this.columnsIndex === 4) {\r\n        let data = this.insideData[this.editIndex].old_prevtotalreadings;\r\n        this.editPrevtotalreadings = data;\r\n        this.insideData[this.editIndex].prevtotalreadings = data;\r\n\r\n        this.$refs[\"curtotalreadings\" + this.editIndex + this.columnsIndex].focus();\r\n      } else if (this.columnsIndex === 5) {\r\n        let data = this.insideData[this.editIndex].old_curtotalreadings;\r\n        this.editcurtotalreadings = data;\r\n        this.insideData[this.editIndex].curtotalreadings = data;\r\n\r\n        this.$refs[\"transformerullage\" + this.editIndex + this.columnsIndex].focus();\r\n      }\r\n    },\r\n    accountnoChange() {\r\n      this.searchList();\r\n    },\r\n    setElectroyType() {\r\n      let types = this.classifications;\r\n      this.accountObj.electrotype = types[types.length - 1];\r\n    },\r\n    self() {\r\n      var lett = this;\r\n      if (lett.startModal) {\r\n        lett.startModalOk();\r\n      } else if (lett.qdModal) {\r\n        lett.qdModalOk();\r\n      } else if (lett.fbModal) {\r\n        lett.fbModalOk();\r\n      } else if (lett.qxfbModal) {\r\n        lett.qxfbModalOk();\r\n      } else {\r\n        let index = lett.editIndex;\r\n        let columns = lett.columnsIndex;\r\n        if (index === -1 && columns === -1) {\r\n          index = 0;\r\n          columns = 1;\r\n          lett.editIndex = index;\r\n          lett.columnsIndex = columns;\r\n          lett.editStartDate = lett.insideData[index].startdate;\r\n          setTimeout(function () {\r\n            lett.$refs[lett.enterOperate(columns).str + index + columns].focus();\r\n          }, 200);\r\n        } else if (columns === 1) {\r\n          lett.hcyzstartdate(lett, index);\r\n        } else if (columns === 3) {\r\n          lett.hcyzprevtotalreadings(lett, index);\r\n        } else {\r\n          lett.validate();\r\n          lett.nextCell(lett);\r\n        }\r\n      }\r\n    },\r\n    handleFormatError(file) {\r\n      this.errorTips(file.name + \" 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件\");\r\n    },\r\n    handleProgress(event, file) {\r\n      this.$Message.info({\r\n        content: file.name + \" 正在上传。\",\r\n      });\r\n    },\r\n    handleUploadSuccess() {},\r\n    onExcelUpload(file) {\r\n      if (file.size > 1024 * 1024 * 5) {\r\n        this.errorTips(\"文件大小超过限制！\");\r\n        return;\r\n      }\r\n      if (!file) {\r\n        this.errorTips(\"请选择要上传的文件！\");\r\n        return;\r\n      }\r\n      let fileName = file.name.lastIndexOf(\".\"); //取到文件名开始到最后一个点的长度\r\n      let fileNameLength = file.name.length; //取到文件名长度\r\n      let fileFormat = file.name.substring(fileName + 1, fileNameLength); //截\r\n      if (\"xls\" != fileFormat && \"xlsx\" != fileFormat) {\r\n        return;\r\n      }\r\n      let param = { version: indexData.version };\r\n      let excel = { file: file };\r\n      let that = this;\r\n      that.spinShow = true;\r\n      axios\r\n        .request({\r\n          url: \"/business/account/uploadExcel\",\r\n          method: \"post\",\r\n          data: Object.assign({}, param, excel),\r\n        })\r\n        .then((res) => {\r\n          that.spinShow = false;\r\n          if (res.data.number > 0) {\r\n            that.$Message.info({\r\n              content: \"成功导入\" + res.data.number + \"条数据\",\r\n            });\r\n          } else {\r\n            that.errorTips(\"导入数据失败，请检查数据是否填写正确\");\r\n          }\r\n          if (res.data.list) {\r\n            that.export.run = true;\r\n            that.beforeLoadData(res.data.list, \"导入数据反馈\");\r\n            that.pageNum = 1;\r\n            that.getAccountMessages();\r\n          }\r\n        });\r\n      return false;\r\n    },\r\n    getDataFromModal(data, flag) {\r\n      this.chooseResponseCenter(data);\r\n    },\r\n    chooseResponseCenter(data) {\r\n      if (!data) {\r\n        if (!this.accountObj.company) {\r\n          this.errorTips(\"请选择所属分公司\");\r\n        }\r\n        if (!this.accountObj.country) {\r\n          this.errorTips(\"请选择所属部门\");\r\n        }\r\n        this.$refs.queryPeople.modal.params = {\r\n          deptId: this.accountObj.country,\r\n          copnId: this.accountObj.company,\r\n        }; // 当前部门和分公司\r\n        this.$refs.queryPeople.choose(); //人员\r\n      } else {\r\n        this.userName = data.name;\r\n        this.accountObj.userId = data.id;\r\n      }\r\n    },\r\n    ellipsis(row) {\r\n      let value = row.remark + row.bz;\r\n      if (!value) return \"\";\r\n      if (value.length > 3) {\r\n        return value.slice(0, 3) + \"...\";\r\n      }\r\n      return value;\r\n    },\r\n    uploadFile(row) {\r\n      this.$refs.uploadFileModal.choose(row.pcid + \"\");\r\n    },\r\n  },\r\n  mounted() {\r\n    this.handleHeight(); //table高度自定义\r\n\r\n    this.accountObj.version = indexData.version;\r\n    this.categorys = blist(\"ammeterCategory\");\r\n    this.directsupplyflags = blist(\"directSupplyFlag\");\r\n    getUser().then((res) => {\r\n      if (res.data.companies != null && res.data.companies.length > 0) {\r\n        this.CompanyList = res.data.companies;\r\n        this.companyListSize = res.data.companies.length;\r\n        //初始化默认展示登陆用户的第一个分公司和分公司下的部门\r\n        this.accountObj.company = this.CompanyList[0].id;\r\n        getDepartments(this.accountObj.company).then((res) => {\r\n          this.resCenterList = res.data;\r\n          this.resCenterListSize = res.data.length;\r\n          this.accountObj.country = res.data[0].id;\r\n\r\n          this.getAccountMessages();\r\n        });\r\n      }\r\n      this.accountTb.columns = this.accountTb.headColumn2\r\n        .concat(this.accountTb.scColumn)\r\n        .concat(this.accountTb.tailColumn);\r\n      //开放全省\r\n      this.accountTb.columns = this.accountTb.columns.concat(this.accountTb.fileColumn);\r\n    });\r\n\r\n    getClassification().then((res) => {\r\n      //用电类型\r\n      this.classificationData = res.data;\r\n    });\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      loginId: (state) => state.user.loginId,\r\n    }),\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.page-class {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  .cl-table {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n  .button-bar {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n.yjjh > .ivu-modal-wrap > .ivu-modal {\r\n  top: 20px !important;\r\n}\r\n\r\n.mytable .ivu-table-cell {\r\n  padding-left: 1px;\r\n  padding-right: 1px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: normal;\r\n  word-break: break-all;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.mytable .myspan {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n}\r\n\r\n.mytable .errorStle {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n  color: red;\r\n}\r\n\r\n.account .filter-divider {\r\n  margin: 0px;\r\n  text-align: center;\r\n}\r\n\r\n.account .header-bar-show {\r\n  max-height: 300px;\r\n  padding-top: 14px;\r\n  overflow: inherit;\r\n  border-bottom: 1px solid #e8eaec;\r\n}\r\n\r\n.account .header-bar-hide {\r\n  max-height: 0;\r\n  padding-top: 0;\r\n  overflow: hidden;\r\n  border-bottom: 0;\r\n}\r\n\r\n.mymodal p {\r\n  font-weight: bold;\r\n  font-size: 140%;\r\n  padding-left: 20px;\r\n}\r\n\r\n.account button {\r\n  margin-right: 10px;\r\n}\r\n::v-deep .cl-table .ivu-table-cell {\r\n  padding: 6px 4px !important;\r\n  .ivu-input {\r\n    padding: 4px !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}