{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\business\\mssAccountbill\\basicMes.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\business\\mssAccountbill\\basicMes.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["basicMes.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoZA,OAAA,MAAA,MAAA,gBAAA;AACA,SAAA,qBAAA,EAAA,UAAA,EAAA,UAAA,QAAA,sBAAA;AACA,SAAA,KAAA,QAAA,cAAA;AACA,SAAA,SAAA,QAAA,aAAA;AACA,OAAA,WAAA,MAAA,eAAA;AACA,OAAA,UAAA,MAAA,qCAAA;AACA,OAAA,WAAA,MAAA,eAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AACA,SAAA,SAAA,EAAA,YAAA,IAAA,aAAA,QAAA,2BAAA;AACA,SACA,YADA,EAEA,YAFA,EAGA,YAHA,EAIA,WAJA,EAKA,YALA,EAMA,aANA,QAOA,2BAPA;AASA,eAAA;AACA,EAAA,IAAA,EAAA,UADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,WAAA,EAAA,WAAA;AAAA,IAAA,UAAA,EAAA,UAAA;AAAA,IAAA,WAAA,EAAA;AAAA,GAFA;AAGA,EAAA,KAAA,EAAA,CAAA,SAAA,CAHA;AAIA,EAAA,IAJA,kBAIA;AAAA;;AACA,QAAA,SAAA,GAAA,SAAA,SAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAFA,CAEA;;AACA,UAAA,kBAAA,MAAA,CAAA,MAAA,CAAA,GAAA,IAAA,kBAAA,MAAA,CAAA,MAAA,CAAA,GAAA,EAAA;AACA,eAAA,CAAA,CAAA,KAAA,EAAA,CACA,CAAA,CAAA,OAAA,EAAA;AACA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA,KADA;AAEA,YAAA,QAAA,EAAA,IAFA;AAGA,YAAA,WAAA,EAAA;AAHA,WADA;AAMA,UAAA,EAAA,EAAA;AACA,uBADA,oBACA,KADA,EACA;AACA,cAAA,IAAA,CAAA,aAAA,CAAA,MAAA,EAAA,KAAA,CAAA,MAAA,CAAA,KAAA;AACA,cAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,MAAA,CAAA,KAAA,IAAA,MAAA,CAAA,GAAA;AACA,cAAA,IAAA,CAAA,iBAAA;AACA;AALA;AANA,SAAA,CADA,CAAA,CAAA;AAgBA,OAjBA,MAiBA;AACA,eAAA,CAAA,CAAA,KAAA,EAAA,CACA,CAAA,CAAA,aAAA,EAAA;AACA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA,KADA;AAEA,YAAA,IAAA,EAAA,GAFA;AAGA;AACA,YAAA,WAAA,EAAA;AAJA,WADA;AAOA,UAAA,EAAA,EAAA;AACA,uBADA,oBACA,KADA,EACA;AACA,cAAA,IAAA,CAAA,aAAA,CAAA,MAAA,EAAA,KAAA;AACA,cAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,MAAA,CAAA,KAAA,IAAA,MAAA,CAAA,GAAA;AACA,cAAA,IAAA,CAAA,iBAAA;AACA;AALA;AAPA,SAAA,CADA,CAAA,CAAA;AAiBA;AACA,KAvCA;;AAwCA,QAAA,SAAA,GAAA,SAAA,SAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA;AACA,aAAA,CAAA,CAAA,KAAA,EAAA,CACA,CAAA,CAAA,aAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,KAAA,EAAA,MAAA,CAAA,GAAA,CAAA,GADA;AAEA,UAAA,IAAA,EAAA,GAFA;AAGA;AACA,UAAA,WAAA,EAAA;AAJA,SADA;AAOA,QAAA,EAAA,EAAA;AACA,qBADA,oBACA,KADA,EACA;AACA,YAAA,MAAA,CAAA,GAAA,CAAA,GAAA,GAAA,KAAA;AACA,YAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,MAAA,CAAA,KAAA,IAAA,MAAA,CAAA,GAAA;AACA;AAJA;AAPA,OAAA,CADA,CAAA,CAAA;AAgBA,KAlBA;;AAmBA,WAAA;AACA,MAAA,qBAAA,EAAA,KADA;AAEA,MAAA,eAAA,EAAA,CAAA,MAAA,EAAA,MAAA,CAFA;AAGA,MAAA,uBAAA,EAAA,KAHA;AAIA,MAAA,OAAA,EAAA,MAAA,CAAA,OAJA;AAKA,MAAA,WAAA,EAAA,IALA;AAMA,MAAA,aAAA,EAAA,UANA;AAOA,MAAA,gBAAA,EAAA,KAPA;AAQA,MAAA,SAAA,EAAA;AAAA,QAAA,YAAA,EAAA,KAAA;AAAA,QAAA,SAAA,EAAA;AAAA,OARA;AASA,MAAA,aAAA,EAAA,CAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,CATA;AAUA,MAAA,QAAA,EAAA;AACA,QAAA,WAAA,EAAA,IADA;AAEA,QAAA,WAAA,EAAA,IAFA;AAGA,QAAA,WAAA,EAAA,IAHA;AAIA,QAAA,QAAA,EAAA,IAJA;AAKA,QAAA,UAAA,EAAA,IALA;AAMA,QAAA,SAAA,EAAA,IANA;AAOA,QAAA,oBAAA,EAAA,IAPA;AAQA,QAAA,mBAAA,EAAA,IARA;AASA,QAAA,cAAA,EAAA,IATA;AAUA,QAAA,SAAA,EAAA,IAVA;AAWA,QAAA,UAAA,EAAA,IAXA;AAYA,QAAA,aAAA,EAAA,IAZA;AAaA,QAAA,UAAA,EAAA,IAbA;AAcA,QAAA,cAAA,EAAA,IAdA;AAeA,QAAA,WAAA,EAAA,IAfA;AAgBA,QAAA,eAAA,EAAA,IAhBA;AAiBA,QAAA,mBAAA,EAAA,IAjBA;AAkBA,QAAA,eAAA,EAAA,IAlBA;AAmBA,QAAA,cAAA,EAAA,IAnBA;AAoBA,QAAA,WAAA,EAAA,IApBA;AAqBA,QAAA,cAAA,EAAA,IArBA;AAsBA,QAAA,UAAA,EAAA,IAtBA;AAuBA,QAAA,WAAA,EAAA,IAvBA;AAwBA,QAAA,UAAA,EAAA,IAxBA;AAyBA,QAAA,aAAA,EAAA,IAzBA;AA0BA,QAAA,UAAA,EAAA,IA1BA;AA2BA,QAAA,YAAA,EAAA;AA3BA,OAVA;AAuCA,MAAA,SAAA,EAAA,EAvCA;AAwCA,MAAA,UAAA,EAAA,IAxCA;AAyCA,MAAA,UAAA,EAAA,KAzCA;AA0CA,MAAA,UAAA,EAAA,KA1CA;AA2CA,MAAA,QAAA,EAAA;AACA,QAAA,OAAA,EAAA,KADA;AAEA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,UADA;AAEA,UAAA,GAAA,EAAA,cAFA;AAGA,UAAA,MAAA,EAAA;AAHA,SADA,EAMA;AACA,UAAA,KAAA,EAAA,UADA;AAEA,UAAA,GAAA,EAAA,cAFA;AAGA,UAAA,MAAA,EAAA;AAHA,SANA,EAWA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA;AAFA,SAXA,EAeA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA;AAFA,SAfA,EAmBA;AACA,UAAA,KAAA,EAAA,YADA;AAEA,UAAA,GAAA,EAAA,KAFA;AAGA,UAAA,MAAA,EAAA;AAHA,SAnBA,EAwBA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,GAAA,EAAA,aAFA,CAEA;;AAFA,SAxBA,CAFA;AA+BA,QAAA,IAAA,EAAA,CACA;AACA,UAAA,YAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA,IAFA;AAGA,UAAA,YAAA,EAAA,IAHA;AAIA,UAAA,SAAA,EAAA,IAJA;AAKA,UAAA,GAAA,EAAA,IALA;AAMA,UAAA,WAAA,EAAA,IANA;AAOA,UAAA,aAAA,EAAA,CACA;AACA;AACA;AACA;AAJA;AAPA,SADA,CA/BA;AA+CA,QAAA,KAAA,EAAA;AA/CA,OA3CA;AA4FA,MAAA,YAAA,EAAA,qBA5FA;AA6FA,MAAA,cAAA,EAAA;AACA;;AADA,OA7FA;AAiGA,MAAA,SAAA,EAAA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAjGA;AAkGA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,GAAA,EAAA,kCAHA;AAIA,QAAA,KAAA,EAAA,IAJA;AAKA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA;AAFA,SADA,EAKA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA;AAFA,SALA,EASA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,GAAA,EAAA;AAFA,SATA,EAaA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA;AAFA,SAbA,EAiBA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,GAAA,EAAA;AAFA,SAjBA,EAqBA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,GAAA,EAAA;AAFA,SArBA,EAyBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA;AAFA,SAzBA,EA6BA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA;AAFA,SA7BA,EAiCA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,GAAA,EAAA,KAFA;AAGA,UAAA,MAAA,EAAA;AAHA,SAjCA;AALA,OAlGA;AA+IA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,QAFA;AAGA,QAAA,YAAA,EAAA,MAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OA/IA;AAqJA,MAAA,UAAA,EAAA,EArJA;AAsJA,MAAA,YAAA,EAAA;AACA,QAAA,MAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,WAFA;AAGA,QAAA,YAAA,EAAA,MAHA;AAIA,QAAA,QAAA,EAAA,IAJA;AAKA,QAAA,WAAA,EAAA;AALA,OAtJA;AA6JA,MAAA,WAAA,EAAA,EA7JA;AA8JA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,GAAA,EAAA,oCAHA;AAIA,QAAA,KAAA,EAAA,IAJA;AAKA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA;AAFA,SADA,EAKA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA;AAFA,SALA,EASA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,GAAA,EAAA;AAFA,SATA,EAaA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,GAAA,EAAA;AAFA,SAbA,EAiBA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,GAAA,EAAA;AAFA,SAjBA,EAqBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA;AAFA,SArBA,EAyBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA;AAFA,SAzBA,EA6BA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA;AAFA,SA7BA,EAiCA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,GAAA,EAAA,KAFA;AAGA,UAAA,MAAA,EAAA;AAHA,SAjCA;AALA;AA9JA,KAAA;AA4MA,GA5QA;AA6QA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,aAFA,2BAEA;AACA,WAAA,YAAA;AACA,KAJA;AAKA,IAAA,cALA,4BAKA;AACA,WAAA,KAAA,CAAA,gBAAA;AACA,KAPA;AAQA,IAAA,eARA,2BAQA,IARA,EAQA;AAAA;;AACA,UAAA,CAAA,IAAA,EAAA;AACA,YAAA,KAAA,QAAA,CAAA,WAAA,IAAA,UAAA,EAAA;AACA;AACA,eAAA,KAAA,CAAA,cAAA,CAAA,MAAA,CAAA,WAAA,CAAA,KAAA,GAAA,MAAA;AACA;;AACA,aAAA,KAAA,CAAA,cAAA,CAAA,MAAA,CAAA,CAAA,EALA,CAKA;AACA,OANA,MAMA;AACA;AACA,YAAA,KAAA,WAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,IAAA,IAAA,KAAA,WAAA,CAAA,CAAA,EAAA,eAAA,EAAA;AACA,iBAAA,MAAA,CAAA,OAAA,CAAA;AACA,cAAA,KAAA,EAAA,SADA;AACA,cAAA,OAAA,EAAA,0DADA;AACA,cAAA,IAAA,EAAA,gBAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,YAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,YAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,YAAA,GAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,YAAA,GAAA,IAAA,CAAA,IAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA,GAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA,GAAA,IAAA,CAAA,IAAA;;AACA,gBAAA,MAAA,CAAA,WAAA,GAPA,CAOA;;AACA;AATA,aAAA;AAWA;AACA;AACA;;AACA,aAAA,QAAA,CAAA,YAAA,GAAA,IAAA;AACA,aAAA,QAAA,CAAA,YAAA,GAAA,IAAA;AACA,aAAA,QAAA,CAAA,YAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,YAAA,GAAA,IAAA,CAAA,IAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA,GAAA,IAAA,CAAA,IAAA;AACA,aAAA,WAAA,GAxBA,CAwBA;AACA;AACA,KAzCA;AA0CA,IAAA,eA1CA,2BA0CA,IA1CA,EA0CA;AACA,UAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,cAAA,CAAA,MAAA,CAAA,CAAA,EADA,CACA;AACA,OAFA,MAEA,CACA;AACA,KA/CA;AAgDA,IAAA,cAhDA,0BAgDA,IAhDA,EAgDA;AACA,UAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,cAAA,CAAA,MAAA,CAAA,CAAA,EADA,CACA;AACA,OAFA,MAEA;AACA,aAAA,QAAA,CAAA,UAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,YAAA,GAAA,IAAA,CAAA,IAAA;AACA;AACA,KAvDA;AAwDA,IAAA,gBAxDA,4BAwDA,IAxDA,EAwDA,IAxDA,EAwDA;AACA,UAAA,IAAA,IAAA,CAAA,EAAA;AACA,aAAA,UAAA,GAAA,CAAA;AACA,aAAA,QAAA,CAAA,YAAA,GAAA,GAAA;AACA,aAAA,eAAA,CAAA,IAAA,EAHA,CAGA;AACA,OAJA,MAIA,IAAA,IAAA,IAAA,CAAA,EAAA;AACA,aAAA,UAAA,GAAA,CAAA;AACA,aAAA,QAAA,CAAA,YAAA,GAAA,GAAA;AACA,aAAA,eAAA,CAAA,IAAA,EAHA,CAGA;AACA,OAJA,MAIA,IAAA,IAAA,IAAA,CAAA,EAAA;AACA,aAAA,cAAA,CAAA,IAAA,EADA,CACA;AACA;AACA,KApEA;AAqEA,IAAA,UArEA,wBAqEA;AACA,UAAA,IAAA,GAAA,KAAA,QAAA,CAAA,QAAA;AACA,WAAA,KAAA,CAAA,YAAA,EAAA,IAAA;;AACA,UAAA,IAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AACA,aAAA,QAAA,CAAA,WAAA,GAAA,CAAA,CAHA,CAGA;AACA,OAJA,MAIA,IAAA,IAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA,OAHA,MAGA,IAAA,IAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AACA,aAAA,QAAA,CAAA,WAAA,GAAA,CAAA,CAHA,CAGA;AACA,OAJA,MAIA,IAAA,IAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA;AACA,aAAA,QAAA,CAAA,WAAA,GAAA,CAAA,CAHA,CAGA;AACA;;AACA,aAAA,YAAA,CAAA,KAAA,SAAA,CAAA,WAAA,EAAA,CAAA,CAAA,CAAA;AACA,aAAA,QAAA,CAAA,WAAA,GAAA,CAAA;AACA,OAPA,MAOA,IAAA,IAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA,OAHA,MAGA,IAAA,IAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA,OAHA,MAGA,IAAA,IAAA,IAAA,EAAA,EAAA;AACA;AACA,aAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA,OAHA,MAGA,IAAA,IAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA,OAHA,MAGA,IAAA,IAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,cAAA,CAAA,KAAA,SAAA,CAAA,WAAA,EAFA,CAEA;;AACA,YAAA,KAAA,OAAA,IAAA,IAAA,EAAA;AACA;;;AAGA,eAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AACA,SALA,MAKA;AACA,eAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AACA,eAAA,QAAA,CAAA,WAAA,GAAA,CAAA;;AACA,cAAA,KAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA,IAAA,IAAA,EAAA;AACA,iBAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA,GAAA,YAAA;AACA,iBAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA,GAAA,OAAA;AACA;AACA;AACA,OAhBA,MAgBA,IAAA,IAAA,IAAA,EAAA,EAAA;AACA;AACA;AACA,aAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA,OAJA,MAIA;AACA,aAAA,cAAA,CAAA,KAAA,SAAA,CAAA,WAAA;AACA,aAAA,cAAA,CAAA,KAAA,SAAA,CAAA,WAAA;AACA,aAAA,cAAA,CAAA,KAAA,SAAA,CAAA,WAAA;AACA,aAAA,cAAA,CAAA,KAAA,SAAA,CAAA,WAAA;AACA,aAAA,QAAA,CAAA,WAAA,GAAA,IAAA;AACA,aAAA,QAAA,CAAA,WAAA,GAAA,IAAA;AACA,aAAA,QAAA,CAAA,WAAA,GAAA,IAAA;AACA,aAAA,QAAA,CAAA,WAAA,GAAA,IAAA;AACA,OA9DA,CA+DA;AACA;AACA;AAEA;;AACA,KAzIA;AA0IA,IAAA,gBA1IA,4BA0IA,KA1IA,EA0IA,KA1IA,EA0IA,KA1IA,EA0IA;AACA,WAAA,YAAA,CAAA,KAAA,SAAA,CAAA,WAAA,EAAA,KAAA;AACA,WAAA,QAAA,CAAA,WAAA,GAAA,KAAA,CAAA,CAAA,CAAA,CAFA,CAEA;;AACA,WAAA,YAAA,CAAA,KAAA,SAAA,CAAA,WAAA,EAAA,KAAA;AACA,WAAA,QAAA,CAAA,WAAA,GAAA,KAAA,CAAA,CAAA,CAAA,CAJA,CAIA;;AACA,WAAA,YAAA,CAAA,KAAA,SAAA,CAAA,WAAA,EAAA,KAAA;AACA,WAAA,QAAA,CAAA,WAAA,GAAA,KAAA,CAAA,CAAA,CAAA,CANA,CAMA;AACA;;AACA,WAAA,cAAA,CAAA,KAAA,SAAA,CAAA,WAAA,EARA,CASA;AACA,KApJA;AAqJA,IAAA,YArJA,wBAqJA,IArJA,EAqJA,KArJA,EAqJA;AAAA;AAAA;AAAA;;AAAA;AACA,6BAAA,IAAA,8HAAA;AAAA,cAAA,IAAA;;AACA;AACA,cAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,WAAA,GAAA,CAAA;AACA,WAFA,MAEA;AACA,YAAA,IAAA,CAAA,WAAA,GAAA,CAAA;AACA;AACA;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KA9JA;AA+JA;AACA,IAAA,YAhKA,wBAgKA,GAhKA,EAgKA,GAhKA,EAgKA;AACA,WAAA,SAAA,CAAA,GAAA,IAAA,KAAA,SAAA,CAAA,GAAA,EAAA,MAAA,CAAA,UAAA,CAAA;AAAA,eAAA,CAAA,CAAA,QAAA,IAAA,GAAA;AAAA,OAAA,CAAA;AACA,KAlKA;AAmKA,IAAA,cAnKA,0BAmKA,IAnKA,EAmKA;AAAA;AAAA;AAAA;;AAAA;AACA,8BAAA,IAAA,mIAAA;AAAA,cAAA,IAAA;AACA,UAAA,IAAA,CAAA,WAAA,GAAA,CAAA;AACA;AAHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAvKA;AAwKA,IAAA,gBAxKA,4BAwKA,QAxKA,EAwKA;AAAA;;AACA,WAAA,KAAA,CAAA,QAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,QAAA,IAAA,KAAA;AACA,OAFA;AAGA,KA5KA;AA6KA,IAAA,iBA7KA,+BA6KA;AACA,WAAA,QAAA,CAAA,YAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA;AACA,WAAA,QAAA,CAAA,YAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA;AACA,WAAA,QAAA,CAAA,GAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,GAAA;AACA,WAAA,QAAA,CAAA,WAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,WAAA;;AACA,UAAA,KAAA,QAAA,CAAA,WAAA,IAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,gBAAA,EAAA,KAAA,QAAA,CAAA,WAAA;AACA;AACA,KArLA;AAsLA,IAAA,UAtLA,sBAsLA,MAtLA,EAsLA;AACA,UAAA,KAAA,GAAA,IAAA;;AACA,cAAA,MAAA,CAAA,MAAA,CAAA,GAAA;AACA,aAAA,KAAA;AACA,UAAA,KAAA,GAAA,MAAA,CAAA,GAAA,CAAA,GAAA;AACA;;AACA,aAAA,aAAA;AACA,UAAA,KAAA,GAAA,MAAA,CAAA,GAAA,CAAA,WAAA;AACA;;AACA,aAAA,cAAA;AACA,UAAA,KAAA,GAAA,MAAA,CAAA,GAAA,CAAA,YAAA;AACA;;AACA,aAAA,cAAA;AACA,UAAA,KAAA,GAAA,MAAA,CAAA,GAAA,CAAA,YAAA;AACA;AAZA;;AAcA,aAAA,KAAA;AACA,KAvMA;AAwMA,IAAA,aAxMA,yBAwMA,MAxMA,EAwMA,KAxMA,EAwMA;AACA,cAAA,MAAA,CAAA,MAAA,CAAA,GAAA;AACA,aAAA,KAAA;AACA,UAAA,MAAA,CAAA,GAAA,CAAA,GAAA,GAAA,KAAA;AACA;;AACA,aAAA,aAAA;AACA,UAAA,MAAA,CAAA,GAAA,CAAA,WAAA,GAAA,KAAA;AACA;;AACA,aAAA,cAAA;AACA,UAAA,MAAA,CAAA,GAAA,CAAA,YAAA,GAAA,KAAA;AACA;;AACA,aAAA,cAAA;AACA,UAAA,MAAA,CAAA,GAAA,CAAA,YAAA,GAAA,KAAA;AACA;AAZA;AAcA,KAvNA;AAwNA,IAAA,WAxNA,yBAwNA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,YAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA;AACA,UAAA,WAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,WAAA;AACA,UAAA,GAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,GAAA;;AACA,UAAA,KAAA,QAAA,CAAA,cAAA,IAAA,CAAA,IAAA,YAAA,IAAA,IAAA,EAAA;AACA,QAAA,GAAA,IAAA,WAAA;AACA,OAFA,MAEA,IAAA,GAAA,IAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,aAAA,CAAA,GAAA,GAAA,kBAAA;AACA,QAAA,GAAA,IAAA,uBAAA;AACA,OAHA,MAGA,IAAA,GAAA,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,aAAA,CAAA,GAAA,GAAA,kBAAA;AACA,QAAA,GAAA,IAAA,uBAAA;AACA,OAHA,MAGA,IAAA,WAAA,IAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,WAAA,GAAA,CAAA,CADA,CAEA;AACA;AACA,OAJA,MAIA,IAAA,WAAA,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA,WAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,aAAA,CAAA,WAAA,GAAA,kBAAA;AACA,QAAA,GAAA,IAAA,gBAAA;AACA;;AACA,UAAA,GAAA,IAAA,UAAA,CAAA,IAAA,CAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,aAAA,CAAA,GAAA,GAAA,kCAAA;AACA;;AACA,UAAA,WAAA,IAAA,UAAA,CAAA,IAAA,CAAA,WAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,aAAA,CAAA,WAAA,GACA,kCADA;AAEA;;AACA,aAAA,GAAA;AACA,KArPA;AAsPA,IAAA,cAtPA,4BAsPA;AACA,UAAA,KAAA,SAAA,CAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA;AACA,eAAA,IAAA;AACA;;AACA,UAAA,UAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA;;AACA,YAAA,EAAA,EAAA;AACA,cAAA,KAAA,GAAA,EAAA,CAAA,GAAA;;AACA,cAAA,KAAA,IAAA,UAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,YAAA,UAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA;;AACA,YAAA,IAAA,GAAA,IAAA;AACA;AACA;AACA;;AACA,UAAA,CAAA,IAAA,EAAA;AACA,aAAA,aAAA,GAAA,CAAA,GAAA,CAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,qBAAA;AACA,OAHA,MAGA;AACA,YAAA,KAAA,UAAA,IAAA,CAAA,EAAA;AACA,eAAA,QAAA,CAAA,aAAA,GAAA,UAAA;AACA,SAFA,MAEA;AACA,eAAA,QAAA,CAAA,YAAA,GAAA,UAAA;AACA;AACA;;AACA,aAAA,IAAA;AACA,KAlRA;AAmRA,IAAA,iBAnRA,+BAmRA,CACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAnSA;AAoSA,IAAA,eApSA,6BAoSA;AACA,UAAA,KAAA,OAAA,IAAA,IAAA,EAAA;AACA;AACA;AACA,YAAA,UAAA,CAAA,IAAA,CAAA,KAAA,QAAA,CAAA,WAAA,CAAA,EAAA,CACA;AACA;AACA;AACA,SAJA,MAIA;AACA,eAAA,QAAA,CAAA,WAAA,GAAA,IAAA;AACA,eAAA,QAAA,CAAA,cAAA,GAAA,IAAA;AACA,eAAA,QAAA,CAAA,IAAA,CAAA,kBAAA;AACA;AACA;AACA,KAlTA;AAmTA,IAAA,oBAnTA,kCAmTA;AACA,WAAA,KAAA,CAAA,sBAAA;AACA,KArTA;AAsTA,IAAA,aAtTA,yBAsTA,IAtTA,EAsTA;AACA,WAAA,UAAA,GAAA,IAAA,CAAA,IAAA;AACA,WAAA,SAAA,GAAA,IAAA,CAAA,GAAA;;AACA,UAAA,KAAA,SAAA,CAAA,MAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,IAAA,QAAA,EAAA;AACA,aAAA,YAAA;AACA,OAFA,MAEA;AACA,aAAA,MAAA;AACA;AACA,KA9TA;AA+TA,IAAA,MA/TA,oBA+TA;AAAA;;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,IAAA,CAAA,IAAA,KAAA,UAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,OAAA,GAAA,IAAA;AACA,QAAA,KAAA,CACA,OADA,CACA;AACA,UAAA,GAAA,EAAA,qCADA;AAEA,UAAA,MAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,KAAA;AAHA,SADA,EAMA,IANA,CAMA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;;AACA,cAAA,IAAA,GAAA,MAAA;AACA,UAAA,SAAA,CAAA;AAAA,YAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,WAFA;AAGA,SAdA;AAeA;AACA,KAnVA;AAoVA,IAAA,YApVA,0BAoVA;AACA,MAAA,aAAA,CAAA;AAAA,QAAA,GAAA,EAAA,KAAA,SAAA,CAAA,IAAA;AAAA,OAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA;AACA,KAtVA;AAuVA,IAAA,cAvVA,0BAuVA,IAvVA,EAuVA;AAAA;;AACA,UAAA,IAAA,CAAA,IAAA,IAAA,QAAA,EAAA;AACA;AACA,YAAA,IAAA,CAAA,aAAA,IAAA,GAAA,EAAA;AACA;AACA,cAAA,KAAA,QAAA,CAAA,YAAA,IAAA,IAAA,CAAA,YAAA,IAAA,KAAA,QAAA,CAAA,YAAA,IAAA,IAAA,CAAA,YAAA,EAAA;AACA,gBAAA,KAAA,QAAA,CAAA,YAAA,IAAA,KAAA,QAAA,CAAA,YAAA,EAAA;AACA;AACA,mBAAA,MAAA,CAAA,OAAA,CAAA;AACA,gBAAA,KAAA,EAAA,OADA;AACA,gBAAA,OAAA,EAAA,sDADA;AACA,gBAAA,IAAA,EAAA,gBAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,YAAA,GAAA,IAAA,CAAA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,YAAA,GAAA,IAAA,CAAA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA,GAAA,IAAA,CAAA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA,GAAA,IAAA,CAAA,YAAA;AACA,kBAAA,MAAA,CAAA,UAAA,GAAA,CAAA;;AACA,kBAAA,MAAA,CAAA,WAAA,GANA,CAMA;;AACA;AARA,eAAA;AAUA,aAZA,MAYA;AACA;AACA,mBAAA,QAAA,CAAA,YAAA,GAAA,IAAA,CAAA,YAAA;AACA,mBAAA,QAAA,CAAA,YAAA,GAAA,IAAA,CAAA,YAAA;AACA,mBAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA,GAAA,IAAA,CAAA,YAAA;AACA,mBAAA,QAAA,CAAA,IAAA,CAAA,CAAA,EAAA,YAAA,GAAA,IAAA,CAAA,YAAA;AACA,mBAAA,UAAA,GAAA,CAAA;AACA,mBAAA,WAAA,GAPA,CAOA;AACA;AACA;AACA,SAzBA,MAyBA;AACA;AACA,eAAA,QAAA,CAAA,OAAA;AACA;AACA;;AACA,MAAA,SAAA,CAAA;AAAA,QAAA,MAAA,EAAA,KAAA,YAAA,CAAA,MAAA;AAAA,QAAA,WAAA,EAAA,KAAA,YAAA,CAAA;AAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,OAFA;AAGA,KA3XA;AA4XA;AACA,IAAA,WA7XA,yBA6XA;AACA,WAAA,SAAA,CAAA,KAAA,GAAA,IAAA;;AACA,UAAA,KAAA,QAAA,CAAA,YAAA,IAAA,IAAA,EAAA;AACA,YAAA,KAAA,UAAA,IAAA,CAAA,EAAA;AACA,eAAA,UAAA,CAAA,KAAA,GAAA;AAAA,YAAA,KAAA,EAAA,KAAA,QAAA,CAAA;AAAA,WAAA;AACA,eAAA,SAAA,GAAA,SAAA,CAAA,KAAA,UAAA,CAAA;AACA,SAHA,MAGA;AACA,eAAA,UAAA,CAAA,KAAA,GAAA;AAAA,YAAA,KAAA,EAAA,KAAA,QAAA,CAAA;AAAA,WAAA;AACA,eAAA,SAAA,GAAA,SAAA,CAAA,KAAA,UAAA,CAAA;AACA;AACA;AACA;AAxYA,GA7QA;AAupBA,EAAA,KAAA,EAAA;AACA,IAAA,QAAA,EAAA;AACA,MAAA,IAAA,EAAA,IADA;AACA;AACA,MAAA,OAFA,mBAEA,GAFA,EAEA,MAFA,EAEA;AACA,aAAA,WAAA,GADA,CACA;;AACA,YAAA,KAAA,OAAA,IAAA,IAAA,EAAA;AACA;AACA;AACA,cAAA,GAAA,CAAA,WAAA,IAAA,CAAA,EAAA;AAAA;AAAA;AAAA;;AAAA;AACA,oCAAA,KAAA,SAAA,CAAA,WAAA,mIAAA;AAAA,oBAAA,IAAA;;AACA,oBAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,kBAAA,IAAA,CAAA,WAAA,GAAA,CAAA;AACA,iBAFA,MAEA;AACA,kBAAA,IAAA,CAAA,WAAA,GAAA,CAAA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,WARA,MAQA;AAAA;AAAA;AAAA;;AAAA;AACA,oCAAA,KAAA,SAAA,CAAA,WAAA,mIAAA;AAAA,oBAAA,KAAA;;AACA,oBAAA,KAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,kBAAA,KAAA,CAAA,WAAA,GAAA,CAAA;AACA;AACA;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,WAjBA,CAkBA;AACA;;;AACA,cAAA,GAAA,CAAA,WAAA,IAAA,CAAA,EAAA;AACA;AACA,YAAA,GAAA,CAAA,WAAA,GAAA,GAAA,CAFA,CAEA;AACA,WAHA,MAGA;AACA,YAAA,GAAA,CAAA,WAAA,GAAA,GAAA,CADA,CACA;AACA;AACA,SA1BA,MA0BA;AACA,cAAA,GAAA,CAAA,eAAA,IAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,eAAA,GAAA,IAAA;AACA,YAAA,GAAA,CAAA,mBAAA,GAAA,IAAA;AACA;AACA;AACA;AApCA;AADA,GAvpBA;AA+rBA,EAAA,OA/rBA,qBA+rBA;AAAA;;AACA,SAAA,SAAA,CAAA,YAAA;AACA,MAAA,MAAA,CAAA,KAAA,CAAA,YAAA,CAAA,WAAA,GADA,CACA;AACA;;;AACA,MAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,WAAA;;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,IAAA,EAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,WAAA,GADA,CACA;;AACA;AACA,KAPA;AASA,QAAA,KAAA,OAAA,IAAA,IAAA,EACA,aAAA,CAAA,KAAA,MAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,UAAA,IAAA,GAAA,GAAA,CAAA,IAAA,IAAA,EAAA;AACA,MAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,QAAA,EAAA,IAAA,EAHA,CAIA;;AACA,MAAA,MAAA,CAAA,QAAA,CAAA,UAAA,GAAA,CAAA;AACA,MAAA,MAAA,CAAA,QAAA,CAAA,eAAA,GAAA,GAAA;AACA,MAAA,MAAA,CAAA,QAAA,CAAA,WAAA,GAAA,MAAA,CAAA,WAAA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,cAAA,EAAA;AACA,QAAA,MAAA,CAAA,qBAAA,GAAA,IAAA;AACA,OAFA,MAEA;AACA,QAAA,MAAA,CAAA,qBAAA,GAAA,KAAA;AACA;;AACA,MAAA,MAAA,CAAA,KAAA,CAAA,WAAA,EAAA,MAAA,CAAA,QAAA,CAAA,EAAA;;AACA,MAAA,MAAA,CAAA,SAAA,CAAA,MAAA,GAAA,MAAA,CAAA,QAAA,CAAA,EAAA;AACA,MAAA,MAAA,CAAA,YAAA,CAAA,MAAA,GAAA,MAAA,CAAA,QAAA,CAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,IAAA,IAAA,EAAA,MAAA,CAAA,UAAA,GAAA,IAAA,CAhBA,CAiBA;AACA;AACA;AACA;AACA;AACA;;AACA,UAAA,MAAA,CAAA,OAAA,IAAA,IAAA,IAAA,MAAA,CAAA,QAAA,CAAA,WAAA,IAAA,UAAA,EAAA;AACA;AACA;AACA;AACA;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,MAAA,CAAA,SAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AACA;AACA,KA9BA,EADA,KAiCA,WAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,UAAA,IAAA,GAAA,GAAA,CAAA,IAAA,IAAA,EAAA;AACA,MAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,QAAA,EAAA,IAAA;AACA,MAAA,MAAA,CAAA,QAAA,CAAA,WAAA,GAAA,MAAA,CAAA,WAAA;;AACA,MAAA,MAAA,CAAA,KAAA,CAAA,WAAA,EAAA,MAAA,CAAA,QAAA,CAAA,EAAA;;AACA,MAAA,MAAA,CAAA,SAAA,CAAA,MAAA,GAAA,MAAA,CAAA,QAAA,CAAA,EAAA;AACA,MAAA,MAAA,CAAA,YAAA,CAAA,MAAA,GAAA,MAAA,CAAA,QAAA,CAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,IAAA,IAAA,EAAA,MAAA,CAAA,UAAA,GAAA,IAAA,CARA,CASA;AACA;AACA;AACA;AACA;AACA;;AACA,UAAA,MAAA,CAAA,OAAA,IAAA,IAAA,IAAA,MAAA,CAAA,QAAA,CAAA,WAAA,IAAA,UAAA,EAAA;AACA;AACA;AACA;AACA;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,MAAA,CAAA,SAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AACA;AACA,KAtBA;AAuBA,GAjwBA;AAkwBA,EAAA,OAlwBA,qBAkwBA;AACA;AACA;AACA;AACA;;AACA;;AAEA,SAAA,SAAA,GAAA;AACA,MAAA,QAAA,EAAA,KAAA,CAAA,UAAA,CADA;AAEA,MAAA,WAAA,EAAA,KAAA,CAAA,aAAA,CAFA;AAGA,MAAA,WAAA,EAAA,KAAA,CAAA,aAAA,CAHA;AAIA,MAAA,WAAA,EAAA,KAAA,CAAA,SAAA,CAJA;AAKA,MAAA,WAAA,EAAA,KAAA,CAAA,aAAA,CALA;AAMA,MAAA,cAAA,EAAA,KAAA,CAAA,gBAAA;AANA,KAAA;AAQA;AAjxBA,CAAA", "sourcesContent": ["<style lang=\"less\">\r\n.floatright {\r\n  margin: 5px;\r\n}\r\n\r\n.ivu-table .demo-table-info-cell-sum {\r\n  /* background-color: #ff6600; */\r\n  color: #fff;\r\n}\r\n\r\n.ivu-table .demo-table-info-cell-inputTaxSum {\r\n  background-color: green;\r\n  color: #fff;\r\n}\r\n\r\n.ivu-table .demo-table-error {\r\n  background-color: red;\r\n  color: #fff;\r\n}\r\n\r\n.classNameSum {\r\n  background-color: #2db7f5;\r\n  color: #fff;\r\n}\r\n</style>\r\n<template>\r\n  <div>\r\n    <!-- <Spin size=\"large\" fix v-if=\"basicMes.id==null\"> -->\r\n    <!-- <Icon type=\"ios-loading\" class=\"demo-spin-icon-load\"></Icon> -->\r\n    <!-- </Spin> -->\r\n    <Collapse v-model=\"collapsevalue\">\r\n      <Panel name=\"1\">\r\n        基本信息\r\n        <!-- <h3 slot=\"title\">基本信息</h3> -->\r\n        <div slot=\"content\">\r\n          <Form\r\n            ref=\"basicMesForm\"\r\n            :model=\"basicMes\"\r\n            :rules=\"ruleValidate\"\r\n            :label-width=\"100\"\r\n            inline\r\n          >\r\n            <FormItem label=\"报账单类型:\" prop=\"billtype\">\r\n              <Select\r\n                @on-change=\"setTdlable\"\r\n                v-model=\"basicMes.billtype\"\r\n                :style=\"formItemWidth\"\r\n                :disabled=\"billtypedisabled\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in categorys.billtype\"\r\n                  :key=\"item.typeCode\"\r\n                  :value=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                >\r\n                  {{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"报账人:\" prop=\"fillInName\">\r\n              <cl-input v-model=\"basicMes.fillInName\" readonly :style=\"formItemWidth\" />\r\n            </FormItem>\r\n            <FormItem label=\"公司代码：\" prop=\"companyNameTxt\">\r\n              <!--                            <Input v-model=\"basicMes.companyNameTxt\" :style=\"formItemWidth\"/>-->\r\n              <Select\r\n                v-model=\"basicMes.companyNameTxt\"\r\n                :disabled=\"disableCompanyNameTxt\"\r\n                :style=\"formItemWidth\"\r\n              >\r\n                <Option v-for=\"item in companyNameList\" :value=\"item\" :key=\"item\">{{\r\n                  item\r\n                }}</Option>\r\n              </Select>\r\n              <!--                          <Input v-model=\"basicMes.companyNameTxt\" :style=\"formItemWidth\" icon=\"ios-archive\"-->\r\n              <!--                                 @on-click=\"getFileorgCode\"/>-->\r\n            </FormItem>\r\n            <FormItem label=\"报账单位(财辅):\" prop=\"fillInCostCenterName\">\r\n              <!--fillInDep-->\r\n              <Input\r\n                v-model=\"basicMes.fillInCostCenterName\"\r\n                readonly\r\n                :style=\"formItemWidth\"\r\n                placeholder=\"点击公司代码选择\"\r\n              />\r\n            </FormItem>\r\n            <FormItem label=\"报账人电话:\" prop=\"telephone\">\r\n              <cl-input\r\n                v-model=\"basicMes.telephone\"\r\n                placeholder=\"请输入电话\"\r\n                :style=\"formItemWidth\"\r\n              />\r\n            </FormItem>\r\n            <FormItem label=\"附单据张数:\" prop=\"formAmount\">\r\n              <InputNumber\r\n                v-model=\"basicMes.formAmount\"\r\n                :step=\"1\"\r\n                :min=\"1\"\r\n                placeholder=\"请输入数量\"\r\n                :style=\"formItemWidth\"\r\n              />\r\n            </FormItem>\r\n            <FormItem label=\"报账期间:\" prop=\"budgetsetname\">\r\n              <cl-date-picker\r\n                type=\"month\"\r\n                format=\"yyyy-MM\"\r\n                placeholder=\"请选择报账期间\"\r\n                v-model=\"basicMes.budgetsetname\"\r\n                :style=\"formItemWidth\"\r\n              ></cl-date-picker>\r\n            </FormItem>\r\n            <FormItem label=\"费用发生日:\" prop=\"happenDate\">\r\n              <cl-date-picker\r\n                type=\"date\"\r\n                placeholder=\"请选择费用发生日\"\r\n                v-model=\"basicMes.happenDate\"\r\n                :style=\"formItemWidth\"\r\n              ></cl-date-picker>\r\n            </FormItem>\r\n            <FormItem label=\"收支方式:\" prop=\"paymentType\">\r\n              <Select v-model=\"basicMes.paymentType\" :style=\"formItemWidth\">\r\n                <Option\r\n                  v-for=\"item in categorys.paymentType\"\r\n                  :key=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                  :value=\"item.typeCode * 1\"\r\n                  >{{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"业务类型：\" prop=\"bizTypeCode\">\r\n              <Select v-model=\"basicMes.bizTypeCode\" :style=\"formItemWidth\">\r\n                <Option\r\n                  v-for=\"item in categorys.bizTypeCode\"\r\n                  :key=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                  :value=\"item.typeCode * 1\"\r\n                  >{{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"业务场景：\" prop=\"pickingMode\">\r\n              <Select\r\n                v-model=\"basicMes.pickingMode\"\r\n                :style=\"formItemWidth\"\r\n                @on-change=\"setbudgetTypeDefault\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in categorys.pickingMode\"\r\n                  :key=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                  :value=\"item.typeCode * 1\"\r\n                  >{{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n              <!--<cl-select :value=\"basicMes.pickingMode\" category=\"pickingMode\" labelField=\"typeName\"\r\n                                       valueField=\"typeCode\" style=\"width:160px;\"/>-->\r\n            </FormItem>\r\n            <FormItem label=\"票据类型：\" prop=\"invoiceType\">\r\n              <Select\r\n                v-model=\"basicMes.invoiceType\"\r\n                :style=\"formItemWidth\"\r\n                @on-change=\"invoiceTypeChange\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in categorys.invoiceType\"\r\n                  :key=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                  :value=\"item.typeCode * 1\"\r\n                  >{{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"转售金额(不含税价)：\"\r\n              prop=\"kindGiftSum\"\r\n              :rules=\"{\r\n                required: basicMes.isExistKindGift == '1',\r\n                message: '不能为空',\r\n                trigger: 'blur',\r\n              }\"\r\n            >\r\n              <Input\r\n                v-model=\"basicMes.kindGiftSum\"\r\n                :style=\"formItemWidth\"\r\n                @on-blur=\"kindGiftSumBlur\"\r\n                :disabled=\"basicMes.isExistKindGift == '0'\"\r\n              />\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"转售税额：\"\r\n              prop=\"kindGiftTaxSum\"\r\n              :rules=\"{\r\n                required: basicMes.isExistKindGift == '1',\r\n                message: '不能为空',\r\n                trigger: 'blur',\r\n              }\"\r\n            >\r\n              <Input\r\n                v-model=\"basicMes.kindGiftTaxSum\"\r\n                :style=\"formItemWidth\"\r\n                :disabled=\"basicMes.isExistKindGift == '0'\"\r\n              />\r\n              <!--:readonly=\"version=='sc'\"-->\r\n            </FormItem>\r\n            <!--<FormItem label=\"是否对外开具专票：\" prop=\"isGdtelInvoice\"\r\n                                  v-if=\"version=='sc' && basicMes.invoiceType == 1\">\r\n                            <RadioGroup v-model=\"basicMes.isGdtelInvoice\">\r\n                                <Radio label=\"0\">\r\n                                    <span>否</span>\r\n                                </Radio>\r\n                                <Radio label=\"1\">\r\n                                    <span>是</span>\r\n                                </Radio>\r\n                            </RadioGroup>\r\n                        </FormItem>-->\r\n            <FormItem label=\"是否员工代垫：\" prop=\"isStaffPayment\">\r\n              <RadioGroup v-model=\"basicMes.isStaffPayment\">\r\n                <Radio label=\"0\">\r\n                  <span>否</span>\r\n                </Radio>\r\n                <Radio label=\"1\">\r\n                  <span>是</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"业务发生时间:\" prop=\"busihappendtimeflag\">\r\n              <RadioGroup v-model=\"basicMes.busihappendtimeflag\">\r\n                <Radio label=\"1\">\r\n                  <Icon type=\"ios-calendar\" />\r\n                  <span>营改增日期前</span>\r\n                </Radio>\r\n                <Radio label=\"2\">\r\n                  <Icon type=\"ios-calendar-outline\" />\r\n                  <span>营改增日期后</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"是否转售水电：\" prop=\"isExistKindGift\">\r\n              <RadioGroup v-model=\"basicMes.isExistKindGift\">\r\n                <Radio label=\"0\" :disabled=\"version == 'sc'\">\r\n                  <span>否</span>\r\n                </Radio>\r\n                <Radio label=\"1\" :disabled=\"version == 'sc'\">\r\n                  <span>是</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"纳税属性：\" prop=\"paytaxattr\">\r\n              <RadioGroup v-model=\"basicMes.paytaxattr\">\r\n                <Radio label=\"1\">\r\n                  <span>属地纳税</span>\r\n                </Radio>\r\n                <Radio label=\"2\">\r\n                  <span>汇总纳税</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"是否加急：\" prop=\"isEmergency\">\r\n              <RadioGroup v-model=\"basicMes.isEmergency\">\r\n                <Radio label=\"0\" :disabled=\"version == 'sc'\">\r\n                  <span>否</span>\r\n                </Radio>\r\n                <Radio label=\"1\" :disabled=\"version == 'sc'\">\r\n                  <span>是</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"是否涉及进项税转出：\"\r\n              prop=\"isInputTax\"\r\n              v-if=\"basicMes.isExistKindGift == '0'\"\r\n            >\r\n              <RadioGroup v-model=\"basicMes.isInputTax\">\r\n                <Radio label=\"0\">\r\n                  <span>否</span>\r\n                </Radio>\r\n                <Radio label=\"1\">\r\n                  <span>是</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"进项税转出金额：\"\r\n              prop=\"inputTaxTurnSum\"\r\n              v-if=\"basicMes.isInputTax == 1 && basicMes.isExistKindGift == '0'\"\r\n            >\r\n              <InputNumber\r\n                v-model=\"basicMes.inputTaxTurnSum\"\r\n                :step=\"0.1\"\r\n                placeholder=\"请输入进项税转出金额\"\r\n                :style=\"formItemWidth\"\r\n              />\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"进项税转出业务类型：\"\r\n              prop=\"inputTaxTurnBizType\"\r\n              v-if=\"basicMes.isInputTax == 1 && basicMes.isExistKindGift == '0'\"\r\n            >\r\n              <Select v-model=\"basicMes.inputTaxTurnBizType\" :style=\"formItemWidth\">\r\n                <Option value=\"1\">免税项目</Option>\r\n                <Option value=\"2\">非免税项目</Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"说明：\" prop=\"abstractValue\">\r\n              <cl-input\r\n                v-model=\"basicMes.abstractValue\"\r\n                type=\"textarea\"\r\n                style=\"width: 400px\"\r\n              />\r\n            </FormItem>\r\n          </Form>\r\n        </div>\r\n      </Panel>\r\n      <Panel name=\"2\">\r\n        供应商信息\r\n        <div slot=\"content\">\r\n          <ChooseModal ref=\"chooseModalSup\" v-on:getDataFromModal=\"getDataFromModal\" />\r\n          <Button class=\"floatright\" type=\"success\" @click=\"handleChooseSup()\"\r\n            >选择供应商</Button\r\n          >\r\n          <Button class=\"floatright\" type=\"success\" @click=\"handleChooseMas()\"\r\n            >选择客户</Button\r\n          >\r\n          <!--<span style=\"color: blue;float: right;\" v-if=\"basicMes.billtype == 9 && version == 'sc'\">#预估报账请选择供应商【G951100081】(其他支付对象-成本结算业务预估)#</span>-->\r\n          <span\r\n            style=\"color: blue; float: right\"\r\n            v-if=\"basicMes.billtype == 8 && version == 'ln'\"\r\n            >#报账【收款】供应商不是必选项#</span\r\n          >\r\n          <Table\r\n            :show-page=\"false\"\r\n            :searchable=\"false\"\r\n            ref=\"testTable\"\r\n            :columns=\"supplier.columns\"\r\n            :data=\"supplier.data\"\r\n          ></Table>\r\n        </div>\r\n      </Panel>\r\n      <Panel name=\"Panel6\"\r\n        >发票信息\r\n        <div slot=\"content\">\r\n          <Row class=\"form-panel\">\r\n            <invoice-file\r\n              :param=\"invoiceParam\"\r\n              :attachData=\"invoiceData\"\r\n              v-on:setAttachData=\"setInvoiceData\"\r\n            />\r\n          </Row>\r\n        </div>\r\n      </Panel>\r\n      <Panel name=\"3\">\r\n        合同信息\r\n        <!--  <h3 slot=\"title\">合同信息</h3> -->\r\n        <div slot=\"content\">\r\n          <Form\r\n            ref=\"htMesForm\"\r\n            :model=\"basicMes\"\r\n            :rules=\"ruleValidateht\"\r\n            :label-width=\"140\"\r\n            inline\r\n          >\r\n            <FormItem label=\"合同编码：\" prop=\"contractno\">\r\n              <cl-input v-model=\"basicMes.contractno\" readonly />\r\n            </FormItem>\r\n            <FormItem label=\"合同名称：\" prop=\"contractName\">\r\n              <cl-input v-model=\"basicMes.contractName\" style=\"width: 300px\" readonly />\r\n            </FormItem>\r\n            <FormItem style=\"width: 100px\">\r\n              <Button type=\"success\" @click=\"handleChooseHt()\">选择合同</Button>\r\n            </FormItem>\r\n          </Form>\r\n        </div>\r\n      </Panel>\r\n      <Panel name=\"4\">\r\n        外部收款人信息\r\n        <div slot=\"content\">\r\n          <cl-table\r\n            ref=\"itemTable\"\r\n            v-if=\"itemTable.show\"\r\n            :url=\"itemTable.url\"\r\n            :searchable=\"false\"\r\n            :query-params=\"itemTable.query\"\r\n            :columns=\"itemTable.columns\"\r\n            select-enabled\r\n            :pageSize=\"5\"\r\n          ></cl-table>\r\n        </div>\r\n      </Panel>\r\n      <Panel v-if=\"attachShow\" name=\"Panel5\"\r\n        >附件信息\r\n        <div slot=\"content\">\r\n          <Row class=\"form-panel\">\r\n            <attach-file\r\n              :param=\"fileParam\"\r\n              :attachData=\"attachData\"\r\n              v-on:setAttachData=\"setAttachData\"\r\n            />\r\n          </Row>\r\n        </div>\r\n      </Panel>\r\n    </Collapse>\r\n  </div>\r\n</template>\r\n<script>\r\nimport config from \"@/config/index\";\r\nimport { ruleValidatebillBasic, widthstyle, numberRule } from \"./mssAccountbilldata\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport { deepClone } from \"@/libs/util\";\r\nimport ChooseModal from \"./chooseModal\";\r\nimport attachFile from \"@/view/basedata/protocol/attachFile\";\r\nimport invoiceFile from \"./invoiceFile\";\r\nimport axios from \"@/libs/api.request\";\r\nimport { attchList, removeAttach } from \"@/api/basedata/ammeter.js\";\r\nimport {\r\n  billViewById,\r\n  billEditById,\r\n  billEditSave,\r\n  billaddData,\r\n  getCategorys,\r\n  billaddDataLn,\r\n} from \"@/api/mssaccountbill/data\";\r\n\r\nexport default {\r\n  name: \"basicMes\",\r\n  components: { ChooseModal, attachFile,invoiceFile },\r\n  props: [\"tdlable\"],\r\n  data() {\r\n    let renderSup = (h, params) => {\r\n      var that = this;\r\n      var value = that.switchProp(params); //获取值\r\n      if (\"supplierCode\" == params.column.key || \"supplierName\" == params.column.key) {\r\n        return h(\"div\", [\r\n          h(\"Input\", {\r\n            props: {\r\n              value: value,\r\n              readonly: true,\r\n              placeholder: \"请选择供应商/客户\",\r\n            },\r\n            on: {\r\n              \"on-change\"(event) {\r\n                that.switchrowProp(params, event.target.value);\r\n                that.supplier.data[params.index] = params.row;\r\n                that.setSupplierTobill();\r\n              },\r\n            },\r\n          }),\r\n        ]);\r\n      } else {\r\n        return h(\"div\", [\r\n          h(\"InputNumber\", {\r\n            props: {\r\n              value: value,\r\n              step: 0.1,\r\n              // min: 0,\r\n              placeholder: \"请填写金额\",\r\n            },\r\n            on: {\r\n              \"on-change\"(value) {\r\n                that.switchrowProp(params, value);\r\n                that.supplier.data[params.index] = params.row;\r\n                that.setSupplierTobill();\r\n              },\r\n            },\r\n          }),\r\n        ]);\r\n      }\r\n    };\r\n    let renderSum = (h, params) => {\r\n      var that = this;\r\n      return h(\"div\", [\r\n        h(\"InputNumber\", {\r\n          props: {\r\n            value: params.row.sum,\r\n            step: 0.1,\r\n            // min: 0,\r\n            placeholder: \"请输入金额\",\r\n          },\r\n          on: {\r\n            \"on-change\"(value) {\r\n              params.row.sum = value;\r\n              that.itemTable.data[params.index] = params.row;\r\n            },\r\n          },\r\n        }),\r\n      ]);\r\n    };\r\n    return {\r\n      disableCompanyNameTxt: false,\r\n      companyNameList: [\"A006\", \"B006\"],\r\n      isExistKindGiftDisabled: false,\r\n      version: config.version,\r\n      accountCode: null,\r\n      formItemWidth: widthstyle,\r\n      billtypedisabled: false,\r\n      formValid: { basicMesForm: false, htMesForm: false },\r\n      collapsevalue: [\"1\", \"2\", \"3\", \"4\"],\r\n      basicMes: {\r\n        paymentType: null,\r\n        pickingMode: null,\r\n        invoiceType: null,\r\n        billtype: null,\r\n        fillInName: null,\r\n        fillInDep: null,\r\n        fillInCostCenterName: null,\r\n        busihappendtimeflag: null,\r\n        isGdtelInvoice: null,\r\n        telephone: null,\r\n        formAmount: null,\r\n        budgetsetname: null,\r\n        happenDate: null,\r\n        companyNameTxt: null,\r\n        bizTypeCode: null,\r\n        inputTaxTurnSum: null,\r\n        inputTaxTurnBizType: null,\r\n        isExistKindGift: null,\r\n        kindGiftTaxSum: null,\r\n        kindGiftSum: null,\r\n        isStaffPayment: null,\r\n        paytaxattr: null,\r\n        isEmergency: null,\r\n        isInputTax: null,\r\n        abstractValue: null,\r\n        contractno: null,\r\n        contractName: null,\r\n      },\r\n      categorys: {},\r\n      personType: null,\r\n      attachShow: false,\r\n      ifHasUnion: false,\r\n      supplier: {\r\n        loading: false,\r\n        columns: [\r\n          {\r\n            title: \"供应商/客户编码\",\r\n            key: \"supplierCode\",\r\n            render: renderSup,\r\n          },\r\n          {\r\n            title: \"供应商/客户名称\",\r\n            key: \"supplierName\",\r\n            render: renderSup,\r\n          },\r\n          {\r\n            title: \"是否关联方\",\r\n            key: \"ApproveMoney\",\r\n          },\r\n          {\r\n            title: \"一般纳税人\",\r\n            key: \"isRelease\",\r\n          },\r\n          {\r\n            title: \"报账金额（不含税价）\",\r\n            key: \"sum\",\r\n            render: renderSup,\r\n          },\r\n          {\r\n            title: \"税额\",\r\n            key: \"inputTaxSum\", //, render: renderSup\r\n          },\r\n        ],\r\n        data: [\r\n          {\r\n            supplierCode: null,\r\n            supplierName: null,\r\n            ApproveMoney: null,\r\n            isRelease: null,\r\n            sum: null,\r\n            inputTaxSum: null,\r\n            cellClassName: {\r\n              // supplierCode: \"demo-table-info-cell-sum\",\r\n              // supplierName: \"demo-table-info-cell-sum\",\r\n              // sum: 'demo-table-info-cell-inputTaxSum'\r\n              // ,inputTaxSum: 'demo-table-info-cell-inputTaxSum'\r\n            },\r\n          },\r\n        ],\r\n        total: 0,\r\n      },\r\n      ruleValidate: ruleValidatebillBasic,\r\n      ruleValidateht: {\r\n        /*contractno: [{required: true, message: \"不能为空\", trigger: \"blur\"}],\r\n                     contractName: [{required: true, message: \"不能为空\", trigger: \"blur\"}]*/\r\n      },\r\n      itemTable: { show: false, data: [] },\r\n      itemTable1: {\r\n        show: true,\r\n        data: [],\r\n        url: \"mssaccount/mssSupplierItem2/list\",\r\n        query: null,\r\n        columns: [\r\n          {\r\n            title: \"收款方名称\",\r\n            key: \"koinh\",\r\n          },\r\n          {\r\n            title: \"收款方类型\",\r\n            key: \"bvtyp\",\r\n          },\r\n          {\r\n            title: \"开户行\",\r\n            key: \"banka\",\r\n          },\r\n          {\r\n            title: \"银行账号\",\r\n            key: \"bankn\",\r\n          },\r\n          {\r\n            title: \"所在省\",\r\n            key: \"provz\",\r\n          },\r\n          {\r\n            title: \"所在市\",\r\n            key: \"ort01\",\r\n          },\r\n          {\r\n            title: \"所属银行\",\r\n            key: \"bvtyp\",\r\n          },\r\n          {\r\n            title: \"开户行行号\",\r\n            key: \"brnch\",\r\n          },\r\n          {\r\n            title: \"金额（含税）\",\r\n            key: \"sum\",\r\n            render: renderSum,\r\n          },\r\n        ],\r\n      },\r\n      fileParam: {\r\n        busiId: \"\",\r\n        busiAlias: \"附件(预提)\",\r\n        categoryCode: \"file\",\r\n        areaCode: \"ln\",\r\n      },\r\n      attachData: [],\r\n      invoiceParam: {\r\n        busiId: \"\",\r\n        busiAlias: \"附件(报账单发票)\",\r\n        categoryCode: \"file\",\r\n        areaCode: \"ln\",\r\n        invoiceFlag: \"1\"\r\n      },\r\n      invoiceData: [],\r\n      itemTable2: {\r\n        show: true,\r\n        data: [],\r\n        url: \"mssaccount/mssAbccustomerBank/list\",\r\n        query: null,\r\n        columns: [\r\n          {\r\n            title: \"客户编号\",\r\n            key: \"kunnr\",\r\n          },\r\n          {\r\n            title: \"收款方类型\",\r\n            key: \"koinh\",\r\n          },\r\n          {\r\n            title: \"开户行\",\r\n            key: \"zbanka\",\r\n          },\r\n          {\r\n            title: \"所在省\",\r\n            key: \"provz\",\r\n          },\r\n          {\r\n            title: \"所在市\",\r\n            key: \"city\",\r\n          },\r\n          {\r\n            title: \"银行账号\",\r\n            key: \"bankn\",\r\n          },\r\n          {\r\n            title: \"所属银行\",\r\n            key: \"bgrup\",\r\n          },\r\n          {\r\n            title: \"开户行行号\",\r\n            key: \"bankl\",\r\n          },\r\n          {\r\n            title: \"金额（含税）\",\r\n            key: \"sum\",\r\n            render: renderSum,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    // 强制更新文本框的值\r\n    changeMessage() {\r\n      this.$forceUpdate();\r\n    },\r\n    getFileorgCode() {\r\n      this.$emit(\"getFileorgCode\");\r\n    },\r\n    handleChooseSup(data) {\r\n      if (!data) {\r\n        if (this.basicMes.accountCode == \"********\") {\r\n          // 铁塔报账 只能 查询铁塔\r\n          this.$refs.chooseModalSup.modal1.queryparams.name1 = \"中国铁塔\";\r\n        }\r\n        this.$refs.chooseModalSup.choose(1); //打开模态框\r\n      } else {\r\n        //判断选择的供应商是否和已上传发票的供应商是否一致\r\n        if(this.invoiceData.length > 0){\r\n          if(data.name != this.invoiceData[0].invoiceSupplier){\r\n            this.$Modal.confirm({\r\n              title: '供应商确认选择', content: '<p class=\"warning\"> 当前选择的供应商与已上传发票的供应商不一致，是否确认选择该供应商</p>', onOk: () => {\r\n                this.basicMes.supplierCode = null;\r\n                this.basicMes.supplierName = null;\r\n                this.basicMes.supplierCode = data.id;\r\n                this.basicMes.supplierName = data.name;\r\n                this.supplier.data[0].supplierCode = data.id;\r\n                this.supplier.data[0].supplierName = data.name;\r\n                this.toShowPayee(); //触发改变外部收款人信息\r\n              }\r\n            });\r\n            return;\r\n          }\r\n        } \r\n        this.basicMes.supplierCode = null;\r\n        this.basicMes.supplierName = null;\r\n        this.basicMes.supplierCode = data.id;\r\n        this.basicMes.supplierName = data.name;\r\n        this.supplier.data[0].supplierCode = data.id;\r\n        this.supplier.data[0].supplierName = data.name;\r\n        this.toShowPayee(); //触发改变外部收款人信息\r\n      }\r\n    },\r\n    handleChooseMas(data) {\r\n      if (!data) {\r\n        this.$refs.chooseModalSup.choose(2); //打开模态框\r\n      } else {\r\n      }\r\n    },\r\n    handleChooseHt(data) {\r\n      if (!data) {\r\n        this.$refs.chooseModalSup.choose(3); //打开模态框\r\n      } else {\r\n        this.basicMes.contractno = data.id;\r\n        this.basicMes.contractName = data.name;\r\n      }\r\n    },\r\n    getDataFromModal(data, flag) {\r\n      if (flag == 1) {\r\n        this.personType = 1;\r\n        this.basicMes.suppliertype = \"1\";\r\n        this.handleChooseSup(data); // 传 true 设置 回调值\r\n      } else if (flag == 2) {\r\n        this.personType = 2;\r\n        this.basicMes.suppliertype = \"2\";\r\n        this.handleChooseSup(data); // 传 true 设置 回调值\r\n      } else if (flag == 3) {\r\n        this.handleChooseHt(data); // 传 true 设置 回调值\r\n      }\r\n    },\r\n    setTdlable() {\r\n      var type = this.basicMes.billtype;\r\n      this.$emit(\"setTdlable\", type);\r\n      if (type == 1) {\r\n        //选择“报销”时，【业务类型】固定为“列并付”，【业务场景】固定为“挂账并付款”-报销单，【收支方式】可选“集中支付”和“已支付-本地已主动支付”\r\n        this.setcategorysCase([2], [9], [2, 4, 5]);\r\n        this.basicMes.paymentType = 4; // 报销，预付，挂账支付，收付方式缺省都改为“集中支付”\r\n      } else if (type == 2) {\r\n        //选择“挂账”时，【业务类型】固定“列账”，【业务场景】固定为“挂账不付款（含预列）”-挂账单，【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [7], [8]);\r\n      } else if (type == 3) {\r\n        //选择“挂账支付”时，【业务类型】固定为“付款”，【业务场景】固定为“挂账后付款（清偿应付款）” ，【收支方式】可选“集中支付”和“已支付-本地已主动支付”\r\n        this.setcategorysCase([1], [2], [2, 4, 5]);\r\n        this.basicMes.paymentType = 4; // 报销，预付，挂账支付，收付方式缺省都改为“集中支付”\r\n      } else if (type == 4) {\r\n        //选择“预付”时，【业务类型】固定为“付款”，【业务场景】固定为“预付款” ，【收支方式】可选“集中支付”和“已支付-本地已主动支付”，【票据类型】固定为“无发票”\r\n        this.setcategorysCase([1], [8], [2, 4, 5]);\r\n        this.basicMes.paymentType = 4; // 报销，预付，挂账支付，收付方式缺省都改为“集中支付”\r\n        //【票据类型】固定为“无发票”\r\n        this.setCategorys(this.categorys.invoiceType, [9]);\r\n        this.basicMes.invoiceType = 9;\r\n      } else if (type == 5) {\r\n        //选择“预付冲销”时，【业务类型】固定为“列账”，【业务场景】固定为“冲前期借款(预付款)” ，【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [1], [8]);\r\n      } else if (type == 7) {\r\n        //选择“前期预付冲销”时，【业务类型】固定为“列账”，【业务场景】固定为“冲前期借款(预付款)” ，【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [1], [8]);\r\n      } else if (type == 10) {\r\n        //选择“预估冲销”时，【业务类型】固定为“列账”，【业务场景】固定为“冲预列(冲暂估)” ，【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [3], [8]);\r\n      } else if (type == 9) {\r\n        //选择 “预估”时，【业务类型】固定为“列账”，【业务场景】固定为“挂账不付款(含预列)”。【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [7], [8]);\r\n      } else if (type == 8) {\r\n        //选择 “收款”时，支付方式只有“集中收款”或“本地收款”\r\n        this.clearCategorys(this.categorys.invoiceType); // 不控制\r\n        if (this.version == \"sc\") {\r\n          /*if (ifHasUnion)\r\n                        this.setcategorysCase([0], [6], [6, 7]);\r\n                       else*/\r\n          this.setcategorysCase([0], [4, 6], [6, 7]);\r\n        } else {\r\n          this.setcategorysCase([0], [6], [6, 7]);\r\n          this.basicMes.invoiceType = 9;\r\n          if (this.supplier.data[0].supplierCode == null) {\r\n            this.supplier.data[0].supplierCode = \"9221000000\";\r\n            this.supplier.data[0].supplierName = \"收款-其他\";\r\n          }\r\n        }\r\n      } else if (type == 11) {\r\n        //调账：支付方式为“不涉及银行收付”，业务类型为\"列账”，业务场景为“往来转销”，\r\n        // 明细列账属性：“不使用成本预算”\r\n        this.setcategorysCase([0], [5], [8]);\r\n      } else {\r\n        this.clearCategorys(this.categorys.bizTypeCode);\r\n        this.clearCategorys(this.categorys.pickingMode);\r\n        this.clearCategorys(this.categorys.paymentType);\r\n        this.clearCategorys(this.categorys.invoiceType);\r\n        this.basicMes.bizTypeCode = null;\r\n        this.basicMes.pickingMode = null;\r\n        this.basicMes.paymentType = null;\r\n        this.basicMes.invoiceType = null;\r\n      }\r\n      // this.$nextTick(()=>{\r\n      //     this.$refs[\"basicMesForm\"].clearValidate(['billtype']);\r\n      // })\r\n\r\n      // this.$refs.basicMesForm.resetFields();\r\n    },\r\n    setcategorysCase(falg1, flag2, flag3) {\r\n      this.setCategorys(this.categorys.bizTypeCode, falg1);\r\n      this.basicMes.bizTypeCode = falg1[0]; //设置 业务类型 默认选择\r\n      this.setCategorys(this.categorys.pickingMode, flag2);\r\n      this.basicMes.pickingMode = flag2[0]; //设置 业务场景 默认选择\r\n      this.setCategorys(this.categorys.paymentType, flag3);\r\n      this.basicMes.paymentType = flag3[0]; //设置 收支方式 默认选择\r\n      ////\r\n      this.clearCategorys(this.categorys.invoiceType);\r\n      // this.basicMes.invoiceType = null;\r\n    },\r\n    setCategorys(data, flags) {\r\n      for (let item of data) {\r\n        // if (flags.indexOf(item.typeCode) > -1) {\r\n        if (flags.indexOf(Number(item.typeCode)) > -1) {\r\n          item.deletedFlag = 0;\r\n        } else {\r\n          item.deletedFlag = 1;\r\n        }\r\n      }\r\n    },\r\n    //设置字典值:自有报账单不要预提\r\n    setDictsOpts(key, val) {\r\n      this.categorys[key] = this.categorys[key].filter((d) => d.typeCode != val);\r\n    },\r\n    clearCategorys(data) {\r\n      for (let item of data) {\r\n        item.deletedFlag = 0;\r\n      }\r\n    },\r\n    valibasicMesForm(formname) {\r\n      this.$refs[formname].validate((valid) => {\r\n        this.formValid[formname] = valid;\r\n      });\r\n    },\r\n    setSupplierTobill() {\r\n      this.basicMes.supplierCode = this.supplier.data[0].supplierCode;\r\n      this.basicMes.supplierName = this.supplier.data[0].supplierName;\r\n      this.basicMes.sum = this.supplier.data[0].sum;\r\n      this.basicMes.inputTaxSum = this.supplier.data[0].inputTaxSum;\r\n      if (this.basicMes.inputTaxSum != null) {\r\n        this.$emit(\"setInputTaxSum\", this.basicMes.inputTaxSum);\r\n      }\r\n    },\r\n    switchProp(params) {\r\n      var value = null;\r\n      switch (params.column.key) {\r\n        case \"sum\":\r\n          value = params.row.sum;\r\n          break;\r\n        case \"inputTaxSum\":\r\n          value = params.row.inputTaxSum;\r\n          break;\r\n        case \"supplierCode\":\r\n          value = params.row.supplierCode;\r\n          break;\r\n        case \"supplierName\":\r\n          value = params.row.supplierName;\r\n          break;\r\n      }\r\n      return value;\r\n    },\r\n    switchrowProp(params, value) {\r\n      switch (params.column.key) {\r\n        case \"sum\":\r\n          params.row.sum = value;\r\n          break;\r\n        case \"inputTaxSum\":\r\n          params.row.inputTaxSum = value;\r\n          break;\r\n        case \"supplierCode\":\r\n          params.row.supplierCode = value;\r\n          break;\r\n        case \"supplierName\":\r\n          params.row.supplierName = value;\r\n          break;\r\n      }\r\n    },\r\n    validHandel() {\r\n      var mes = \"\";\r\n      var supplierCode = this.supplier.data[0].supplierCode;\r\n      var inputTaxSum = this.supplier.data[0].inputTaxSum;\r\n      var sum = this.supplier.data[0].sum;\r\n      if (this.basicMes.isStaffPayment == 0 && supplierCode == null) {\r\n        mes += \"请选择供应商/客户\";\r\n      } else if (sum == null) {\r\n        this.supplier.data[0].cellClassName.sum = \"demo-table-error\";\r\n        mes += \"供应商/客户 报账金额（不含税价）不能为空\";\r\n      } else if (sum && !numberRule.test(sum)) {\r\n        this.supplier.data[0].cellClassName.sum = \"demo-table-error\";\r\n        mes += \"供应商/客户 报账金额（不含税价）输入有误\";\r\n      } else if (inputTaxSum == null) {\r\n        this.supplier.data[0].inputTaxSum = 0;\r\n        // this.supplier.data[0].cellClassName.inputTaxSum = 'demo-table-error';\r\n        // mes += \"供应商/客户 税额 不能为空\";\r\n      } else if (inputTaxSum && !numberRule.test(inputTaxSum)) {\r\n        this.supplier.data[0].cellClassName.inputTaxSum = \"demo-table-error\";\r\n        mes += \"供应商/客户 税额 输入有误\";\r\n      }\r\n      if (sum && numberRule.test(sum)) {\r\n        this.supplier.data[0].cellClassName.sum = \"demo-table-info-cell-inputTaxSum\";\r\n      }\r\n      if (inputTaxSum && numberRule.test(inputTaxSum)) {\r\n        this.supplier.data[0].cellClassName.inputTaxSum =\r\n          \"demo-table-info-cell-inputTaxSum\";\r\n      }\r\n      return mes;\r\n    },\r\n    validItemTable() {\r\n      if (this.itemTable.data.length == 0) {\r\n        // 外部收款人 可为空\r\n        return true;\r\n      }\r\n      let _itemTable = [];\r\n      let flag = false;\r\n      for (var i = 0; i < this.itemTable.data.length; i++) {\r\n        var ik = this.itemTable.data[i];\r\n        if (ik) {\r\n          var value = ik.sum;\r\n          if (value && numberRule.test(value)) {\r\n            _itemTable.push(this.itemTable.data[i]);\r\n            flag = true;\r\n          }\r\n        }\r\n      }\r\n      if (!flag) {\r\n        this.collapsevalue = [\"4\"];\r\n        this.$Message.error(\"至少填写一项（含税）金额,或者金额有误\");\r\n      } else {\r\n        if (this.personType == 1) {\r\n          this.basicMes.supplierItem2 = _itemTable;\r\n        } else {\r\n          this.basicMes.customerBank = _itemTable;\r\n        }\r\n      }\r\n      return flag;\r\n    },\r\n    invoiceTypeChange() {\r\n      // debugger\r\n      // if (this.version == 'sc') {\r\n      // 当票据类型选择“增值税专用发票”时，带出“是否对外开具专票”选项，默认选择是，\r\n      // 控制“转售金额(含税价)”和“转售税额”必填。如果选择否，此两项改为非必填。\r\n      // if (this.basicMes.invoiceType == 1) {\r\n      //     this.basicMes.isGdtelInvoice = \"1\";\r\n      // } else {\r\n      //     this.basicMes.isGdtelInvoice = \"0\";\r\n      // }\r\n      //  票据类型选“增值税专票”，明细有代垫外单位电费，“是否转售水电”默认为“是”\r\n      //     this.$emit(\"setTdlable\", this.basicMes.billtype);\r\n      // }\r\n      // else {\r\n      //     this.basicMes.invoiceType\r\n      // }\r\n    },\r\n    kindGiftSumBlur() {\r\n      if (this.version == \"sc\") {\r\n        // 转售税额根据用户填写的转售金额(含税价)自动计算，计算规则：\r\n        // 转售税额=转售金额(含税价)*0.13/1.13\r\n        if (numberRule.test(this.basicMes.kindGiftSum)) {\r\n          ////和“转售税额”金额不再自动计算，由用户自己填写\r\n          // let sum = parseFloat(this.basicMes.kindGiftSum);\r\n          // this.basicMes.kindGiftTaxSum = (sum * 0.13 / 1.13).toFixed(2);\r\n        } else {\r\n          this.basicMes.kindGiftSum = null;\r\n          this.basicMes.kindGiftTaxSum = null;\r\n          this.$Message.info(\"转售金额(含税价)--请输入数字\");\r\n        }\r\n      }\r\n    },\r\n    setbudgetTypeDefault() {\r\n      this.$emit(\"setbudgetTypeDefault\");\r\n    },\r\n    setAttachData(data) {\r\n      this.multiFiles = data.data;\r\n      this.removeIds = data.ids;\r\n      if (this.removeIds.length != 0 && data.type == \"remove\") {\r\n        this.removeAttach();\r\n      } else {\r\n        this.upload();\r\n      }\r\n    },\r\n    upload() {\r\n      if (this.attachData.length != 0 && this.multiFiles.length != 0) {\r\n        // this.$Message.info(\"提示:上传文件过大可能导致上传失败！\");\r\n        this.loading = true;\r\n        axios\r\n          .request({\r\n            url: \"/common/attachments/uploadMultiFile\",\r\n            method: \"post\",\r\n            data: this.multiFiles,\r\n          })\r\n          .then((res) => {\r\n            if (res.data.code != 0) {\r\n              this.loading = false;\r\n            }\r\n            let that = this;\r\n            attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n              that.attachData = Object.assign([], res.data.rows);\r\n            });\r\n          });\r\n      }\r\n    },\r\n    removeAttach() {\r\n      removeAttach({ ids: this.removeIds.join() }).then(() => {});\r\n    },\r\n    setInvoiceData(data) {\r\n      if(data.type == 'upload'){\r\n        //上传的发票的供应商在能耗系统是否存在 1 存在 0 不存在\r\n        if(data.supplierExist == '1'){\r\n          //自动填充供应商信息\r\n          if(this.basicMes.supplierCode != data.supplierCode && this.basicMes.supplierName != data.supplierName){\r\n            if(this.basicMes.supplierCode && this.basicMes.supplierName){\r\n              //当已选择供应商时,提示是否需要替换\r\n              this.$Modal.confirm({\r\n                title: '替换供应商', content: '<p class=\"warning\"> 当前选择的供应商与上传发票的供应商不一致，是否替换供应商</p>', onOk: () => {\r\n                  this.basicMes.supplierCode = data.supplierCode;\r\n                  this.basicMes.supplierName = data.supplierName;\r\n                  this.supplier.data[0].supplierCode = data.supplierCode;\r\n                  this.supplier.data[0].supplierName = data.supplierName;\r\n                  this.personType = 1;\r\n                  this.toShowPayee(); //触发改变外部收款人信息\r\n                }\r\n              });\r\n            } else{\r\n              //当未选择供应商时，直接填充\r\n              this.basicMes.supplierCode = data.supplierCode;\r\n              this.basicMes.supplierName = data.supplierName;\r\n              this.supplier.data[0].supplierCode = data.supplierCode;\r\n              this.supplier.data[0].supplierName = data.supplierName;\r\n              this.personType = 1;\r\n              this.toShowPayee(); //触发改变外部收款人信息\r\n            }\r\n          }\r\n        } else {\r\n          //提示发票供应商信息不存在\r\n          this.$Message.warning(`上传的发票的供应商信息不存在`);\r\n        }\r\n      }\r\n      attchList({ busiId: this.invoiceParam.busiId, invoiceFlag: this.invoiceParam.invoiceFlag }).then((res) => {\r\n        this.invoiceData = Object.assign([], res.data.rows);\r\n      });\r\n    },\r\n    //选择供应商/供应商有值->改变外部收款人信息\r\n    toShowPayee() {\r\n      this.itemTable.query = null;\r\n      if (this.basicMes.supplierCode != null) {\r\n        if (this.personType == 1) {\r\n          this.itemTable1.query = { lifnr: this.basicMes.supplierCode };\r\n          this.itemTable = deepClone(this.itemTable1);\r\n        } else {\r\n          this.itemTable2.query = { kunnr: this.basicMes.supplierCode };\r\n          this.itemTable = deepClone(this.itemTable2);\r\n        }\r\n      }\r\n    },\r\n  },\r\n  watch: {\r\n    basicMes: {\r\n      deep: true, //深度监听\r\n      handler(val, oldVal) {\r\n        this.toShowPayee(); //触发改变外部收款人信息\r\n        if (this.version == \"sc\") {\r\n          // 四川\r\n          //报账单业务场景为“挂账并付款”时，票据类型不能选择到“无发票”选项。\r\n          if (val.pickingMode == 9) {\r\n            for (let item of this.categorys.invoiceType) {\r\n              if (item.typeCode == 9) {\r\n                item.deletedFlag = 1;\r\n              } else {\r\n                item.deletedFlag = 0;\r\n              }\r\n            }\r\n          } else {\r\n            for (let item of this.categorys.invoiceType) {\r\n              if (item.typeCode == 9) {\r\n                item.deletedFlag = 0;\r\n              }\r\n            }\r\n          }\r\n          //收支方式选择“集中支付”时，是否加急固化为“是”，不可修改；\r\n          // 选择其他收支方式时，是否加急固化为“否”，不可修改\r\n          if (val.paymentType == 4) {\r\n            //集中支付\r\n            val.isEmergency = \"1\"; //是\r\n          } else {\r\n            val.isEmergency = \"0\"; //否\r\n          }\r\n        } else {\r\n          if (val.isExistKindGift == \"0\") {\r\n            val.inputTaxTurnSum = null;\r\n            val.inputTaxTurnBizType = null;\r\n          }\r\n        }\r\n      },\r\n    },\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.$refs.basicMesForm.resetFields(); // this.$refs.adduserform.resetFields();\r\n      // this.$refs.chooseModalSup.resetFields();       // this.$refs.adduserform.resetFields();\r\n      this.$refs.htMesForm.resetFields();\r\n      if (this.itemTable.show) {\r\n        this.$refs.itemTable.resetFields(); // this.$refs.adduserform.resetFields();\r\n      }\r\n    });\r\n\r\n    if (this.version == \"ln\")\r\n      billaddDataLn(this.$route.query.data).then((res) => {\r\n        // this.basicMes = res.data;\r\n        let data = res.data || {};\r\n        Object.assign(this.basicMes, data);\r\n        // debugger\r\n        this.basicMes.formAmount = 1;\r\n        this.basicMes.isExistKindGift = \"0\";\r\n        this.basicMes.accountCode = this.accountCode;\r\n        if (res.data.companyNameTxt) {\r\n          this.disableCompanyNameTxt = true;\r\n        } else {\r\n          this.disableCompanyNameTxt = false;\r\n        }\r\n        this.$emit(\"setBillId\", this.basicMes.id);\r\n        this.fileParam.busiId = this.basicMes.id;\r\n        this.invoiceParam.busiId = this.basicMes.id;\r\n        if (this.version == \"ln\") this.attachShow = true;\r\n        // this.basicMes.billtype=parseInt(this.basicMes.billtype);\r\n        // this.basicMes.invoiceType = parseInt(this.basicMes.invoiceType);\r\n        // this.basicMes.bizTypeCode = parseInt(this.basicMes.bizTypeCode);\r\n        // this.basicMes.pickingMode = parseInt(this.basicMes.pickingMode);\r\n        // this.basicMes.isStaffPayment = parseInt(this.basicMes.isStaffPayment);\r\n        //\r\n        if (this.version == \"ln\" && this.basicMes.accountCode == \"********\") {\r\n          //\r\n          // 辽宁那边要求控制下自有报账单那边屏蔽调报账单预提，预提冲销的选项\r\n          // 只有铁塔报账单才能选预提，预提冲销\r\n          //自有的预提屏蔽，自有预提冲销开着，这边从8月份开始自有不允许走预提单\r\n          this.setCategorys(this.categorys.billtype, [1, 2, 3, 4, 5, 6, 7, 8, 10, 11]);\r\n        }\r\n      });\r\n    else\r\n      billaddData().then((res) => {\r\n        // this.basicMes = res.data;\r\n        let data = res.data || {};\r\n        Object.assign(this.basicMes, data);\r\n        this.basicMes.accountCode = this.accountCode;\r\n        this.$emit(\"setBillId\", this.basicMes.id);\r\n        this.fileParam.busiId = this.basicMes.id;\r\n        this.invoiceParam.busiId = this.basicMes.id;\r\n        if (this.version == \"ln\") this.attachShow = true;\r\n        // this.basicMes.billtype=parseInt(this.basicMes.billtype);\r\n        // this.basicMes.invoiceType = parseInt(this.basicMes.invoiceType);\r\n        // this.basicMes.bizTypeCode = parseInt(this.basicMes.bizTypeCode);\r\n        // this.basicMes.pickingMode = parseInt(this.basicMes.pickingMode);\r\n        // this.basicMes.isStaffPayment = parseInt(this.basicMes.isStaffPayment);\r\n        //\r\n        if (this.version == \"ln\" && this.basicMes.accountCode == \"********\") {\r\n          //\r\n          // 辽宁那边要求控制下自有报账单那边屏蔽调报账单预提，预提冲销的选项\r\n          // 只有铁塔报账单才能选预提，预提冲销\r\n          //自有的预提屏蔽，自有预提冲销开着，这边从8月份开始自有不允许走预提单\r\n          this.setCategorys(this.categorys.billtype, [1, 2, 3, 4, 5, 6, 7, 8, 10, 11]);\r\n        }\r\n      });\r\n  },\r\n  created() {\r\n    // getCategorys().then(res => {\r\n    //   this.categorys = res.data.categorys;\r\n    // });\r\n    //直接从前台取\r\n    /*        if (this.$route.query.data)\r\n          console.log(this.$route.query.data);*/\r\n    this.categorys = {\r\n      billtype: blist(\"billtype\"),\r\n      paymentType: blist(\"paymentType\"),\r\n      invoiceType: blist(\"invoiceType\"),\r\n      bizTypeCode: blist(\"bizType\"),\r\n      pickingMode: blist(\"pickingMode\"),\r\n      isStaffPayment: blist(\"isStaffPayment\"),\r\n    };\r\n  },\r\n};\r\n</script>\r\n"], "sourceRoot": "src/view/business/mssAccountbill"}]}