{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\changeAmmeter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\changeAmmeter.vue", "mtime": 1754285403017}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": 1741317456427}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["changeAmmeter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAggDA,SAAA,aAAA,QAAA,iCAAA;AACA,SACA,gBADA,EAEA,iBAFA,EAGA,eAHA,EAIA,UAJA,EAKA,WALA,EAMA,iBANA,EAOA,aAPA,EAQA,qBARA,EASA,qBATA,EAUA,iBAVA,EAWA,mBAXA,EAYA,WAZA,EAaA,wBAbA,EAcA,qBAAA,IAAA,sBAdA,EAeA,mBAfA,EAgBA,iBAhBA,EAiBA,kBAjBA,EAkBA,SAlBA,EAmBA,YAAA,IAAA,aAnBA,EAoBA,kBApBA,QAqBA,2BArBA;AAsBA,SAAA,KAAA,EAAA,KAAA,QAAA,cAAA;AACA,OAAA,kBAAA,MAAA,sBAAA;AACA,OAAA,YAAA,MAAA,gBAAA;AACA,OAAA,YAAA,MAAA,gBAAA;AACA,SAAA,YAAA,QAAA,MAAA;AACA,OAAA,oBAAA,MAAA,mDAAA;AACA,OAAA,mBAAA,MAAA,2CAAA;AACA,OAAA,YAAA,MAAA,iBAAA;AACA,OAAA,kBAAA,MAAA,4CAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AACA,SAAA,OAAA,QAAA,iBAAA;AACA,OAAA,UAAA,MAAA,oCAAA;AACA,eAAA;AACA,EAAA,IAAA,EAAA,gBADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,YAAA,EAAA,YADA;AAEA,IAAA,YAAA,EAAA,YAFA;AAGA,IAAA,YAAA,EAAA,YAHA;AAIA,IAAA,kBAAA,EAAA,kBAJA;AAKA,IAAA,oBAAA,EAAA,oBALA;AAMA,IAAA,mBAAA,EAAA,mBANA;AAOA,IAAA,kBAAA,EAAA,kBAPA;AAQA,IAAA,UAAA,EAAA;AARA,GAFA;AAYA,EAAA,IAZA,kBAYA;AAAA;;AACA;AACA,QAAA,SAAA,GAAA,SAAA,SAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,EAAA;AACA,YAAA,mBAAA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,UAAA,QAAA,CAAA,IAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AACA,SAFA,MAEA,IAAA,MAAA,CAAA,KAAA,CAAA,CAAA,OAAA,CAAA,IAAA,KAAA,CAAA,EAAA;AACA,UAAA,QAAA,CAAA,IAAA,KAAA,CAAA,aAAA,CAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,QAAA;AACA;AACA;;AACA,MAAA,QAAA;AACA,KAXA;;AAYA,QAAA,eAAA,GAAA,SAAA,eAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KANA;;AAOA,QAAA,mBAAA,GAAA,SAAA,mBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,IAAA,IAAA,IAAA,KAAA,IAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KANA;;AAOA,QAAA,oBAAA,GAAA,SAAA,oBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,IAAA,IAAA,IAAA,KAAA,GAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,aAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KANA;;AAOA,QAAA,uBAAA,GAAA,SAAA,uBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,IAAA,SAAA,IAAA,KAAA,IAAA,IAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,YAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA;AACA;;AACA,MAAA,QAAA;AACA,KATA;;AAUA,QAAA,qBAAA,GAAA,SAAA,qBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA,CAAA,OAAA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,aAAA;AACA,UAAA,GAAA,GAAA,IAAA,CAAA,WAAA;;AACA,UAAA,KAAA,IAAA,IAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA;;AACA,UAAA,KAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,EAAA;AACA,YAAA,GAAA,IAAA,KAAA,EAAA;AACA,UAAA,QAAA,CAAA,IAAA,KAAA,CAAA,kBAAA,CAAA,CAAA;AACA;AACA;;AACA,MAAA,QAAA;AACA,KAbA;;AAcA,QAAA,mBAAA,GAAA,SAAA,mBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA,CAAA,OAAA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,aAAA;AACA,UAAA,GAAA,GAAA,IAAA,CAAA,WAAA;;AACA,UAAA,GAAA,IAAA,IAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA;;AACA,UAAA,KAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,EAAA;AACA,YAAA,GAAA,IAAA,KAAA,EAAA;AACA,UAAA,QAAA,CAAA,IAAA,KAAA,CAAA,kBAAA,CAAA,CAAA;AACA;AACA;;AACA,MAAA,QAAA;AACA,KAbA,CA3DA,CAyEA;;;AACA,QAAA,YAAA,GAAA,SAAA,YAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,CAAA,GAAA,CAAA,CACA,MADA,EAEA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,UAAA,EAAA,QADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,QAAA,EAAA,MAHA;AAIA,UAAA,UAAA,EAAA,QAJA;AAKA,UAAA,WAAA,EAAA,KALA;AAMA,UAAA,UAAA,EAAA,CANA;AAOA,UAAA,OAAA,EAAA;AAPA;AADA,OAFA,EAaA,GAbA,CAAA;AAeA,aAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,EAAA,EAAA,EAAA,SAAA,CAAA,CAAA,CAAA;AACA,KAjBA;;AAkBA,WAAA;AACA,MAAA,aAAA,EAAA,IADA;AACA;AACA,MAAA,aAAA,EAAA,KAFA;AAEA;AACA,MAAA,MAAA,EAAA,KAHA;AAIA,MAAA,gBAAA,EAAA,IAJA;AAKA,MAAA,cAAA,EAAA,KALA;AAKA;AACA,MAAA,iBAAA,EAAA,IANA;AAMA;AACA,MAAA,WAAA,EAAA,KAPA;AAOA;AACA,MAAA,aAAA,EAAA,IARA;AAQA;AACA,MAAA,YAAA,EAAA,EATA;AAUA,MAAA,gBAAA,EAAA,IAVA;AAYA,MAAA,cAAA,EAAA,EAZA;AAaA,MAAA,SAAA,EAAA,EAbA;AAcA,MAAA,UAAA,EAAA,KAdA;AAeA,MAAA,YAAA,EAAA,KAfA;AAgBA,MAAA,QAAA,EAAA,IAhBA;AAkBA,MAAA,OAAA,EAAA,KAlBA;AAkBA;AACA,MAAA,QAAA,EAAA,KAnBA;AAmBA;AAEA,MAAA,OAAA,EAAA,KArBA;AAsBA,MAAA,SAAA,EAAA,IAtBA;AAwBA,MAAA,SAAA,EAAA,KAxBA;AAyBA,MAAA,gBAAA,EAAA,KAzBA;AA0BA,MAAA,KAAA,EAAA,EA1BA;AA2BA,MAAA,eAAA,EAAA,KA3BA;AA4BA,MAAA,WAAA,EAAA,KA5BA;AA6BA,MAAA,OAAA,EAAA,KA7BA;AA8BA,MAAA,WAAA,EAAA,IA9BA;AA+BA,MAAA,aAAA,EAAA,IA/BA;AA+BA;AACA,MAAA,iBAAA,EAAA,KAhCA;AAiCA,MAAA,SAAA,EAAA,EAjCA;AAkCA,MAAA,WAAA,EAAA,EAlCA;AAmCA,MAAA,kBAAA,EAAA,EAnCA;AAmCA;AAEA,MAAA,OAAA,EAAA,EArCA;AAsCA,MAAA,WAAA,EAAA,EAtCA;AAsCA;AACA,MAAA,cAAA,EAAA,EAvCA;AAuCA;AACA,MAAA,YAAA,EAAA,EAxCA;AAwCA;AACA,MAAA,UAAA,EAAA,EAzCA;AAyCA;AACA,MAAA,gBAAA,EAAA,EA1CA;AA0CA;AACA,MAAA,uBAAA,EAAA,EA3CA;AA2CA;AACA,MAAA,cAAA,EAAA,EA5CA;AA4CA;AACA,MAAA,SAAA,EAAA,EA7CA;AA6CA;AACA,MAAA,WAAA,EAAA,EA9CA;AA8CA;AACA,MAAA,cAAA,EAAA,EA/CA;AA+CA;AACA,MAAA,gBAAA,EAAA,EAhDA;AAgDA;AACA,MAAA,cAAA,EAAA,EAjDA;AAiDA;AACA,MAAA,aAAA,EAAA,EAlDA;AAkDA;AACA,MAAA,mBAAA,EAAA,EAnDA;AAmDA;AACA,MAAA,UAAA,EAAA,EApDA;AAqDA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,UAFA;AAGA,QAAA,YAAA,EAAA,MAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OArDA;AA2DA,MAAA,YAAA,EAAA;AACA,QAAA,eAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,WAAA,EAAA,CACA;AACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,CAJA;AAQA,QAAA,WAAA,EAAA,CACA;AACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,CARA;AAYA,QAAA,OAAA,EAAA,CACA;AACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,SAAA,EAAA,eAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,CAZA;AAgBA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,SAAA,EAAA,eAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAhBA;AAiBA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAjBA;AAyBA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAzBA;AAiCA,QAAA,UAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAjCA;AAyCA,QAAA,WAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAzCA;AAiDA,QAAA,oBAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAjDA;AAyDA,QAAA,eAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,SAAA,EAAA,uBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAzDA;AA4DA,QAAA,aAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,OAAA,EAAA,mCADA;AAEA,UAAA,OAAA,EAAA,UAFA;AAGA,UAAA,OAAA,EAAA;AAHA,SAFA,CA5DA;AAoEA,QAAA,gBAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CApEA;AA4EA,QAAA,KAAA,EAAA,CACA;AACA,UAAA,IAAA,EAAA,QADA;AAEA,UAAA,OAAA,EAAA,mCAFA;AAGA,UAAA,OAAA,EAAA,UAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CA5EA;AAoFA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CApFA;AAuFA,QAAA,eAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAvFA;AAwFA,QAAA,WAAA,EAAA,EAxFA;AAyFA,QAAA,MAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAzFA;AAiGA,QAAA,SAAA,EAAA,CAAA;AAAA,UAAA,OAAA,EAAA,WAAA;AAAA,UAAA,OAAA,EAAA,OAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAjGA;AAkGA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,SAAA,EAAA,mBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,OAAA,EAAA,iCADA;AAEA,UAAA,OAAA,EAAA,UAFA;AAGA,UAAA,OAAA,EAAA;AAHA,SAFA,CAlGA;AA0GA,QAAA,aAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,SAAA,EAAA,qBAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CA1GA;AAkHA,QAAA,WAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,SAAA,EAAA,mBAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAlHA;AA0HA,QAAA,GAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,SAAA,EAAA,eAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,OAAA,EAAA,mCADA;AAEA,UAAA,OAAA,EAAA,UAFA;AAGA,UAAA,OAAA,EAAA;AAHA,SAFA,CA1HA;AAkIA,QAAA,uBAAA,EAAA,EAlIA;AAmIA,QAAA,kBAAA,EAAA,EAnIA;AAoIA,QAAA,YAAA,EAAA;AApIA,OA3DA;AAiMA,MAAA,OAAA,EAAA;AACA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA;AAFA,SADA,EAKA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA;AAFA,SALA,EAUA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,GAAA,EAAA,OAFA;AAGA,UAAA,YAAA,EAAA,YAHA;AAIA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,gBAAA,IAAA,GAAA,KAAA;AACA,gBAAA,KAAA,GAAA,MAAA,CAAA,GAAA,CAAA,KAAA;AACA,gBAAA,QAAA,GAAA,MAAA,CAAA,GAAA,CAAA,QAAA;AACA,gBAAA,KAAA,GAAA,CAAA,CACA,OADA,EAEA;AACA,cAAA,KAAA,EAAA;AACA,gBAAA,KAAA,EAAA,SADA;AAEA,gBAAA,QAAA,EAAA,MAFA;AAGA,gBAAA,UAAA,EAAA,QAHA;AAIA,gBAAA,UAAA,EAAA,KAJA;AAKA,gBAAA,UAAA,EAAA,CALA;AAMA,gBAAA,UAAA,EAAA,MANA;AAOA,gBAAA,OAAA,EAAA,QAAA,KAAA,GAAA,MAAA,GAAA;AAPA;AADA,aAFA,EAaA,MAbA,CAAA;AAeA,gBAAA,MAAA,GAAA,CAAA,CACA,OADA,EAEA;AACA,cAAA,KAAA,EAAA;AACA,gBAAA,KAAA,EAAA,SADA;AAEA,gBAAA,QAAA,EAAA,MAFA;AAGA,gBAAA,UAAA,EAAA,QAHA;AAIA,gBAAA,UAAA,EAAA,KAJA;AAKA,gBAAA,UAAA,EAAA,CALA;AAMA,gBAAA,UAAA,EAAA,MANA;AAOA,gBAAA,OAAA,EAAA,QAAA,IAAA,IAAA,GAAA,cAAA,GAAA;AAPA;AADA,aAFA,EAaA,WAbA,CAAA;AAeA,gBAAA,MAAA,GAAA,CAAA,CAAA,aAAA,EAAA;AACA,cAAA,KAAA,EAAA;AACA,gBAAA,MAAA,EAAA,QAAA,KAAA,IAAA,QAAA,IAAA,IAAA,GAAA,mBAAA,GAAA;AADA,eADA;AAIA,cAAA,KAAA,EAAA;AACA,gBAAA,KAAA,EAAA,KADA;AAEA,gBAAA,GAAA,EAAA,GAFA;AAGA,gBAAA,GAAA,EAAA;AAHA,eAJA;AASA,cAAA,EAAA,EAAA;AACA,6BAAA,kBAAA,CAAA,EAAA;AACA,sBAAA,CAAA,IAAA,SAAA,IAAA,CAAA,IAAA,IAAA,EAAA;AACA,oBAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA,mBAFA,MAEA;AACA,oBAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA,mBALA,CAMA;AACA;;;AACA,sBAAA,GAAA,GAAA,2DAAA;;AACA,sBAAA,CAAA,IAAA,SAAA,IAAA,CAAA,IAAA,IAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA;AACA,oBAAA,MAAA,CAAA,GAAA,CAAA,QAAA,GAAA,IAAA;AACA,oBAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,mBAHA,MAGA;AACA,oBAAA,MAAA,CAAA,GAAA,CAAA,QAAA,GAAA,KAAA;AACA,oBAAA,IAAA,CAAA,QAAA,GAAA,KAAA;AACA;;AACA,kBAAA,MAAA,CAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA,kBAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA,CAAA,MAAA,IAAA,MAAA,CAAA,GAAA;AACA;AAnBA;AATA,aAAA,CAAA;AA+BA,mBAAA,CAAA,CAAA,KAAA,EAAA,CAAA,MAAA,EAAA,KAAA,EAAA,MAAA,CAAA,CAAA;AACA;AAtEA,SAVA,EAkFA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,gBAAA,WAAA,GAAA,MAAA,CAAA,GAAA,CAAA,WAAA;AACA,gBAAA,QAAA,GAAA,MAAA,CAAA,GAAA,CAAA,SAAA;;AACA,gBAAA,QAAA,IAAA,SAAA,IAAA,QAAA,IAAA,IAAA,EAAA;AACA,qBAAA,CAAA,CAAA,OAAA,EAAA;AACA,gBAAA,KAAA,EAAA;AACA,kBAAA,KAAA,EAAA,WADA;AAEA,kBAAA,QAAA,EAAA;AAFA;AADA,eAAA,CAAA;AAMA,aAPA,MAOA;AACA,qBAAA,CAAA,CAAA,OAAA,EAAA;AACA,gBAAA,KAAA,EAAA;AACA,kBAAA,KAAA,EAAA,WADA;AAEA,kBAAA,IAAA,EAAA,aAFA;AAGA,kBAAA,WAAA,EAAA,QAHA;AAIA,kBAAA,QAAA,EAAA;AAJA,iBADA;AAOA,gBAAA,EAAA,EAAA;AACA,8BAAA,iBAAA,CAAA,EAAA;AACA,oBAAA,KAAA,CAAA,oBAAA,CAAA,CAAA,EAAA,MAAA,EAAA,MAAA,CAAA,GAAA,CAAA,MAAA;AACA;AAHA;AAPA,eAAA,CAAA;AAaA;AACA;AA5BA,SAlFA,CADA;AAkHA,QAAA,IAAA,EAAA;AAlHA,OAjMA;AAqTA,MAAA,OAAA,EAAA;AACA,QAAA,EAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,IAFA;AAGA,QAAA,OAAA,EAAA,IAHA;AAIA,QAAA,WAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,OAAA,EAAA,EANA;AAOA,QAAA,eAAA,EAAA,EAPA,CAOA;;AAPA,OArTA;AA8TA,MAAA,SAAA,EAAA,KA9TA;AA+TA,MAAA,aAAA,EAAA,KA/TA;AAgUA,MAAA,YAAA,EAAA;AACA,QAAA,OAAA,EAAA,KADA;AAEA,QAAA,MAAA,EAAA,CACA;AACA,UAAA,YAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,KAAA,EAAA,GAJA;AAKA,UAAA,IAAA,EAAA;AALA,SADA,CAFA;AAWA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,OAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SADA,EAOA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,GAAA,EAAA,IAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAPA,EAaA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,UAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAbA,CAXA;AA+BA,QAAA,IAAA,EAAA,EA/BA;AAgCA,QAAA,KAAA,EAAA,CAhCA;AAiCA,QAAA,QAAA,EAAA;AAjCA,OAhUA;AAmWA,MAAA,YAAA,EAAA,SAnWA,CAmWA;;AAnWA,KAAA;AAqWA,GA7cA;AA+cA,EAAA,OAAA,oBACA,YAAA,CAAA,CAAA,UAAA,EAAA,gBAAA,CAAA,CADA;AAEA,IAAA,SAFA,qBAEA,IAFA,EAEA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EADA,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,UAAA,IAAA,GAAA,KAAA;AACA,UAAA,KAAA,GAAA,KAAA,CAbA,CAcA;;AACA,WAAA,KAAA,CAAA,OAAA,CAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,YAAA;AACA,UAAA,IAAA,GAAA,IAAA;AACA;AACA,OALA;AAMA,WAAA,KAAA,CAAA,QAAA,CAAA,QAAA,CAAA,UAAA,MAAA,EAAA;AACA,YAAA,MAAA,EAAA;AACA,UAAA,KAAA,GAAA,IAAA;AACA;AACA,OAJA;;AAKA,UAAA,IAAA,IAAA,KAAA,IAAA,CAAA,KAAA,OAAA,IAAA,CAAA,KAAA,QAAA,EAAA;AACA,aAAA,SAAA,CAAA,IAAA;AACA,OAFA,MAEA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,OAAA;AACA,aAAA,OAAA,GAAA,KAAA;AACA;AACA,KAlCA;AAmCA;AACA,IAAA,SApCA,qBAoCA,IApCA,EAoCA;AACA,UAAA,KAAA,GAAA,KAAA,OAAA,CAAA,eAAA;AACA,WAAA,OAAA,CAAA,WAAA,GAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UACA,KAAA,OAAA,CAAA,MAAA,KAAA,CAAA,KACA,KAAA,aAAA,IAAA,IAAA,IAAA,KAAA,aAAA,IAAA,IADA,CADA,EAGA;AACA;AACA,YACA,KAAA,OAAA,CAAA,cAAA,IAAA,IAAA,IACA,KAAA,OAAA,CAAA,cAAA,IAAA,SAFA,EAGA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA;;AACA,UAAA,KAAA,gBAAA,EAAA,EAAA;AACA;AACA,YAAA,KAAA,qBAAA,EAAA,EAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAA,IAAA,CAAA,WAAA,CAAA,IAAA,EAXA,CAYA;AACA;AACA;AACA,KA1EA;AA2EA,IAAA,gBA3EA,8BA2EA;AACA,UAAA,WAAA,GAAA,KAAA,OAAA,CAAA,WAAA;AACA,UAAA,WAAA,GAAA,KAAA,OAAA,CAAA,WAAA;;AACA,UAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,EAAA;AACA,YAAA,WAAA,KAAA,KAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA,OATA,MASA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,EAAA;AACA,YAAA,WAAA,KAAA,KAAA,IAAA,WAAA,KAAA,KAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA,OATA,MASA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,EAAA;AACA,YAAA,WAAA,KAAA,KAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA,OATA,MASA,IACA,WAAA,KAAA,IAAA,IACA,WAAA,KAAA,IADA,IAEA,WAAA,KAAA,IAFA,IAGA,WAAA,KAAA,IAHA,IAIA,WAAA,KAAA,IAJA,IAKA,WAAA,KAAA,IANA,EAOA;AACA,YAAA,WAAA,KAAA,KAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA;;AACA,UAAA,KAAA,aAAA,IAAA,IAAA,IAAA,KAAA,OAAA,CAAA,kBAAA,EAAA;AACA;AACA,YACA,CAAA,IAAA,EAAA,IAAA,EAAA,QAAA,CAAA,WAAA,KACA,CAAA,KAAA,OAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,IAAA,CAFA,EAGA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA;;AACA,aAAA,IAAA;AACA,KAzIA;AA0IA,IAAA,OA1IA,qBA0IA;AACA;AACA,WAAA,iBAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,KAAA,gBAAA,EAHA,CAGA;AACA,KA9IA;AA+IA,IAAA,WA/IA,yBA+IA;AACA,WAAA,iBAAA,GAAA,IAAA;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,OAAA,EAAA,KAAA;AAAA,OAAA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,KAnJA;AAoJA,IAAA,WApJA,uBAoJA,IApJA,EAoJA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,qBAAA,CAAA,IAAA,CAAA,YAAA,EAAA,IAAA,CAAA,OAAA,CAAA,WAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CACA,UAAA,GAAA,EAAA;AACA;AACA,YAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,YAAA,IAAA,IAAA,CAAA,EAAA;AACA,cACA,IAAA,CAAA,OAAA,CAAA,WAAA,IAAA,SAAA,IACA,IAAA,CAAA,OAAA,CAAA,WAAA,IAAA,IADA,KAEA,IAAA,CAAA,OAAA,CAAA,WAAA,IAAA,IAAA,IAAA,IAAA,CAAA,OAAA,CAAA,WAAA,IAAA,IAFA,CADA,EAIA;AACA;AACA,gBAAA,IAAA,CAAA,aAAA,IAAA,IAAA,EAAA;AACA;AACA,cAAA,aAAA,CAAA,IAAA,CAAA,OAAA,CAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,gBAAA,IAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA,CAAA,aAAA;;AACA,oBAAA,IAAA,CAAA,aAAA,IAAA,CAAA,EAAA;AACA,kBAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AACA,oBAAA,KAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;;AAIA,kBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,iBANA,MAMA;AACA,kBAAA,IAAA,CAAA,cAAA,CAAA,IAAA;AACA;AACA,eAZA;AAaA,aAfA,MAeA;AACA,kBAAA,IAAA,CAAA,aAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AACA,kBAAA,KAAA,EAAA,MADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AAIA,gBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,eANA,MAMA;AACA,gBAAA,IAAA,CAAA,cAAA,CAAA,IAAA;AACA;AACA;AACA,WAhCA,MAgCA;AACA,YAAA,IAAA,CAAA,cAAA,CAAA,IAAA;AACA;AACA;AACA,OAzCA;AA2CA,KAjMA;AAkMA,IAAA,cAlMA,0BAkMA,IAlMA,EAkMA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA,CADA,CAEA;;AACA,MAAA,qBAAA,CAAA;AACA,QAAA,EAAA,EAAA,IAAA,CAAA,OAAA,CAAA,EADA;AAEA,QAAA,IAAA,EAAA,CAFA;AAGA,QAAA,WAAA,EAAA,IAAA,CAAA,OAAA,CAAA,WAHA;AAIA,QAAA,WAAA,EAAA,IAAA,CAAA,OAAA,CAAA,WAJA;AAKA,QAAA,UAAA,EAAA,IAAA,CAAA,OAAA,CAAA;AALA,OAAA,CAAA,CAMA,IANA,CAMA,UAAA,GAAA,EAAA;AACA,YAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,YAAA,IAAA,IAAA,OAAA,EAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA;;AACA,cACA,IAAA,CAAA,iBAAA,IAAA,IAAA,IACA,IAAA,CAAA,OAAA,CAAA,WAAA,IAAA,KADA,IAEA,GAAA,CAAA,IAAA,CAAA,KAHA,EAIA;AACA;AACA,YAAA,IAAA,CAAA,MAAA,GAAA,IAAA;AACA,WAPA,MAOA;AACA,YAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AAAA,cAAA,KAAA,EAAA,MAAA;AAAA,cAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA;AAAA,aAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA;AACA,SAbA,MAaA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,IAAA,EADA,CACA;AACA;AACA,OAxBA;AAyBA,KA9NA;AA+NA,IAAA,UA/NA,sBA+NA,IA/NA,EA+NA;AACA,UAAA,IAAA,GAAA,IAAA,CADA,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAA,IAAA,CAAA,YAAA,CAAA,IAAA,EAZA,CAaA;AACA,KA7OA;AA8OA,IAAA,YA9OA,wBA8OA,IA9OA,EA8OA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,IAAA,GAAA,CAAA;;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,QAAA,GAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,QAAA,GAAA,CAAA;AACA;;AACA,MAAA,IAAA,CAAA,QAAA,CAAA,IAAA,EARA,CAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA9PA;AA+PA,IAAA,QA/PA,oBA+PA,IA/PA,EA+PA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,WAAA,oBAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,QAAA,GAAA,CAAA,CAHA,CAGA;;AACA,MAAA,UAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,IAAA,CAAA,CAAA,IAAA,GAAA,CAAA,IAAA,CAAA,OAAA,IAAA,GAAA,EAAA;AACA,cAAA,IAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA;AACA,WAFA,MAEA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AAAA,cAAA,KAAA,EAAA,MAAA,CAAA;AAAA,aAAA;;AACA,YAAA,IAAA,CAAA,IAAA;AACA;AACA,SAPA,MAOA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA;AAAA,YAAA,QAAA,EAAA;AAAA,WAAA;AACA;AACA,OAbA,EAcA,KAdA,CAcA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OAjBA;AAkBA,KArRA;AAsRA;AACA,IAAA,oBAvRA,kCAuRA;AACA,UAAA,KAAA,OAAA,CAAA,QAAA,KAAA,CAAA,IAAA,KAAA,OAAA,CAAA,QAAA,KAAA,CAAA,EAAA;AACA;AACA,aAAA,OAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,UAAA,KAAA,OAAA,CAAA,UAAA,KAAA,CAAA,EAAA;AACA;AACA,aAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AACA,aAAA,OAAA,CAAA,UAAA,GAAA,IAAA;AACA;;AACA,UAAA,KAAA,OAAA,CAAA,gBAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,OAAA,CAAA,oBAAA,GAAA,IAAA;AACA;;AACA,UAAA,CAAA,KAAA,WAAA,EAAA;AACA;AACA,aAAA,OAAA,CAAA,eAAA,GAAA,IAAA;AACA,aAAA,OAAA,CAAA,OAAA,GAAA,IAAA;AACA,aAAA,OAAA,CAAA,OAAA,GAAA,IAAA;AACA,aAAA,OAAA,CAAA,OAAA,GAAA,IAAA;AACA,aAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AACA;AACA,KA7SA;AA8SA,IAAA,IA9SA,kBA8SA;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,OAAA,EAAA;AAFA,OAAA;AAIA,KAnTA;AAoTA,IAAA,WApTA,yBAoTA;AACA,WAAA,QAAA;AACA,KAtTA;AAuTA,IAAA,QAvTA,sBAuTA;AAAA;;AACA,WAAA,WAAA,GAAA,EAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,EAAA;AACA,WAAA,OAAA,GAAA,EAAA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,eAAA,GAAA,KAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,OAAA,CAAA,WAAA,GADA,CACA;;;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,WAAA,GAFA,CAEA;;;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,WAAA,GAHA,CAGA;;AACA,OAJA;AAKA,WAAA,SAAA,GAAA,KAAA;AACA,WAAA,iBAAA,GAAA,KAAA;AACA,KArUA;AAsUA,IAAA,aAtUA,2BAsUA;AACA,WAAA,QAAA;AACA,KAxUA;;AAyUA;AACA,IAAA,WA1UA,uBA0UA,EA1UA,EA0UA;AAAA;;AACA,WAAA,YAAA,GAAA,EAAA;AACA,WAAA,QAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,EAAA,IAAA,SAAA,EAAA;AACA,aAAA,KAAA,GAAA,MAAA;AACA,aAAA,eAAA,GAAA,IAAA,CAFA,CAGA;;AACA,QAAA,iBAAA,CAAA;AAAA,UAAA,EAAA,EAAA,EAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,EAAA,IAAA,SAAA,IAAA,GAAA,CAAA,IAAA,CAAA,EAAA,IAAA,IAAA,EAAA;AACA,gBAAA,QAAA,GAAA,CAAA,IAAA,CAAA,SAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,GAAA,UAAA,CAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA;AACA;;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA;;AACA,YAAA,kBAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,CAAA,EAAA,GAAA,GAAA,CAAA,IAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,SAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA,CAAA,EAAA;AACA,aAHA;;AAIA,YAAA,MAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,WAAA;;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,EAAA,GAAA,IAAA;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,YAAA,GAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,WAAA,GAAA,IAAA;AACA,YAAA,IAAA,CAAA,SAAA,CAAA,MAAA,GAAA,EAAA;AACA,YAAA,mBAAA,CAAA,MAAA,CAAA,OAAA,CAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,MAAA,CAAA,OAAA,CAAA,eAAA,GAAA,GAAA,CAAA,IAAA;AACA,aAFA;AAGA,YAAA,SAAA,CAAA;AAAA,cAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AAAA,aAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,aAFA;AAGA,WApBA,MAoBA;AACA,YAAA,WAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,kBAAA,QAAA,IAAA,CAAA,IAAA,CAAA,SAAA,EAAA;AACA,gBAAA,IAAA,CAAA,IAAA,CAAA,SAAA,GAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA;AACA;;AACA,cAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA;;AACA,cAAA,MAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,IAAA,EAAA,IAAA,CAAA,IAAA,CAAA,WAAA;;AACA,cAAA,IAAA,CAAA,OAAA,CAAA,YAAA,GAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,CAAA,WAAA,GAAA,IAAA;AACA,cAAA,IAAA,CAAA,OAAA,CAAA,EAAA,GAAA,IAAA;AACA,cAAA,IAAA,CAAA,SAAA,CAAA,MAAA,GAAA,EAAA;AACA,cAAA,mBAAA,CAAA,MAAA,CAAA,OAAA,CAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,OAAA,CAAA,eAAA,GAAA,GAAA,CAAA,IAAA;AACA,eAFA;AAGA,cAAA,SAAA,CAAA;AAAA,gBAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AAAA,eAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,IAAA,CAAA,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,eAFA;AAGA,aAhBA;AAiBA;AACA,SAxCA;AAyCA,aAAA,OAAA;AACA,OA9CA,MA8CA;AACA,aAAA,KAAA,GAAA,MAAA;AACA,QAAA,WAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAHA;AAIA;;AACA,MAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,MAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAHA;AAIA,KAvYA;AAwYA,IAAA,qBAxYA,iCAwYA,EAxYA,EAwYA,QAxYA,EAwYA,WAxYA,EAwYA;AAAA;;AACA,MAAA,sBAAA,CAAA;AAAA,QAAA,SAAA,EAAA,EAAA;AAAA,QAAA,eAAA,EAAA;AAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,SAAA,IAAA,IAAA,IAAA,IAAA,CAAA,SAAA,IAAA,SAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA;AACA,YAAA,IAAA,CAAA,WAAA,GAAA,IAAA;AACA,WAHA,MAGA,IAAA,IAAA,CAAA,SAAA,IAAA,WAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA;AACA;AACA,SAPA;AAQA,QAAA,MAAA,CAAA,OAAA,CAAA,IAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,OAVA;AAWA,KApZA;AAqZA,IAAA,YArZA,0BAqZA;AACA,UAAA,KAAA,OAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,aAAA,GAAA,IAAA;AACA,aAAA,YAAA,CAAA,WAAA,GAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAAA;AAGA,OALA,MAKA;AACA,aAAA,aAAA,GAAA,KAAA;AACA,aAAA,YAAA,CAAA,WAAA,GAAA,CACA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAAA;AAGA;AACA,KAjaA;AAkaA,IAAA,YAlaA,0BAkaA;AAAA;;AACA,UAAA,KAAA,OAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,YAAA,KAAA,OAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,eAAA,WAAA,GAAA,IAAA;AACA,SAFA,MAEA;AACA,eAAA,WAAA,GAAA,KAAA;AACA;;AACA,QAAA,kBAAA,CAAA,KAAA,OAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,WAAA,GAAA,MAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA;AACA,SAJA;AAKA;AACA,KA/aA;AAgbA,IAAA,OAhbA,qBAgbA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA;;AACA,YACA,GAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,IACA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IADA,IAEA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAHA,EAIA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,QAAA,eAAA,CAAA;AAAA,UAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA;AAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,UAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,SAHA;AAIA,OAfA;AAgBA,KAlcA;AAmcA,IAAA,UAncA,sBAmcA,IAncA,EAmcA;AACA,WAAA,WAAA,GAAA,KAAA,CAAA,iBAAA,EAAA,IAAA,CAAA,QAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,WAAA,cAAA,GAAA,KAAA,CACA,aADA,EAEA,IAAA,CAAA,WAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,YAAA,GAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,SAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,WAAA,UAAA,GAAA,KAAA,CAAA,SAAA,EAAA,IAAA,CAAA,OAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,WAAA,gBAAA,GAAA,KAAA,CACA,eADA,EAEA,IAAA,CAAA,aAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,uBAAA,GAAA,KAAA,CACA,sBADA,EAEA,IAAA,CAAA,oBAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,cAAA,GAAA,KAAA,CACA,aADA,EAEA,IAAA,CAAA,WAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,SAAA,GAAA,KAAA,CAAA,QAAA,EAAA,IAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,WAAA,WAAA,GAAA,KAAA,CAAA,UAAA,EAAA,IAAA,CAAA,QAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,WAAA,cAAA,GAAA,KAAA,CACA,aADA,EAEA,IAAA,CAAA,WAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,gBAAA,GAAA,KAAA,CACA,eADA,EAEA,IAAA,CAAA,aAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,cAAA,GAAA,KAAA,CACA,gBADA,EAEA,IAAA,CAAA,WAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,mBAAA,GAAA,KAAA,CACA,kBADA,EAEA,IAAA,CAAA,gBAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,aAAA,GAAA,KAAA,CAAA,YAAA,EAAA,IAAA,CAAA,UAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,KA1fA;AA4fA,IAAA,UA5fA,sBA4fA,IA5fA,EA4fA;AACA;AACA,UAAA,KAAA,aAAA,IAAA,IAAA,IAAA,KAAA,aAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,uBAAA,GAAA,IAAA;AACA;;AACA,UAAA,IAAA,CAAA,QAAA,KAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAA,WAAA;AACA,OAFA,MAEA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAA,YAAA;AACA;;AACA,MAAA,IAAA,CAAA,eAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,EAAA,CAXA,CAYA;;AACA,UAAA,IAAA,CAAA,MAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AACA,aAAA,aAAA,GAAA,IAAA;AACA,aAAA,YAAA,CAAA,WAAA,GAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAAA;AAGA,OANA,MAMA;AACA,aAAA,aAAA,GAAA,KAAA;AACA,aAAA,YAAA,CAAA,WAAA,GAAA,CACA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAAA;AAGA;;AACA,UAAA,IAAA,CAAA,oBAAA,IAAA,CAAA,IAAA,IAAA,CAAA,oBAAA,IAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA;AACA,OA3BA,CA4BA;AACA;;;AACA,MAAA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAA,cAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,cAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,eAAA,GACA,IAAA,CAAA,eAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA,eAAA,GAAA,EADA;AAEA,MAAA,IAAA,CAAA,iBAAA,GACA,IAAA,CAAA,iBAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,iBAAA,GAAA,EADA;AAEA,MAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,SAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,KAAA,GAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,IAAA,GAAA,EAAA,KAAA,aAAA,GAAA,IAAA;AACA,WAAA,OAAA,GAAA,IAAA;AACA,UAAA,WAAA,GAAA,KAAA,OAAA,CAAA,WAAA;;AACA,UACA,WAAA,KAAA,GAAA,IACA,WAAA,KAAA,GADA,IAEA,WAAA,KAAA,GAFA,IAGA,WAAA,KAAA,CAJA,EAKA;AACA,aAAA,gBAAA,GAAA,IAAA;AACA;;AACA,UACA,WAAA,IAAA,IAAA,IAAA,WAAA,KAAA,IAAA,IAAA,WAAA,KAAA,IAAA,IACA,KAAA,OAAA,CAAA,QAAA,KAAA,CAFA,EAGA;AACA,aAAA,gBAAA,GAAA,KAAA;AACA;;AACA,UAAA,KAAA,OAAA,CAAA,aAAA,IAAA,IAAA,EAAA;AACA,aAAA,OAAA,CAAA,aAAA,GAAA,CAAA;AACA;;AACA,UAAA,KAAA,OAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,aAAA,OAAA,CAAA,OAAA,GAAA,KAAA,OAAA,CAAA,OAAA,GAAA,EAAA;;AACA,YAAA,KAAA,OAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,eAAA,WAAA,GAAA,IAAA;AACA;AACA;;AACA,UAAA,KAAA,OAAA,CAAA,aAAA,IAAA,IAAA,EAAA;AACA,aAAA,UAAA,GAAA,IAAA;AACA;;AACA,WAAA,QAAA,GAAA,KAAA,OAAA,CAAA,WAAA,CAlEA,CAkEA;;AACA,WAAA,SAAA,GAAA,IAAA;AACA,KAhkBA;AAkkBA;AACA,IAAA,qBAnkBA,iCAmkBA,KAnkBA,EAmkBA;AACA,WAAA,gBAAA,GAAA,KAAA;;AACA,UAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,YAAA;AACA,aAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AACA,aAAA,gBAAA,GAAA,IAAA;AACA,OAJA,MAIA;AACA,aAAA,gBAAA,GAAA,KAAA;AACA,aAAA,OAAA,CAAA,WAAA,GAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,YAAA,WAAA,GAAA,KAAA,OAAA,CAAA,WAAA;;AACA,YAAA,WAAA,KAAA,IAAA,IAAA,WAAA,KAAA,IAAA,EAAA;AACA;AACA,eAAA,OAAA,CAAA,QAAA,GAAA,CAAA;AACA,eAAA,gBAAA,GAAA,IAAA;AACA,eAAA,YAAA,CAAA,uBAAA,GAAA,CACA;AAAA,YAAA,QAAA,EAAA,IAAA;AAAA,YAAA,OAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WADA,CAAA;AAGA,SAPA,MAOA,IAAA,WAAA,KAAA,IAAA,IAAA,WAAA,KAAA,IAAA,EAAA;AACA,eAAA,gBAAA,GAAA,KAAA;AACA,eAAA,OAAA,CAAA,QAAA,GAAA,CAAA;AACA,eAAA,YAAA,CAAA,uBAAA,GAAA,CACA;AAAA,YAAA,QAAA,EAAA,IAAA;AAAA,YAAA,OAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WADA,CAAA;AAGA,SANA,MAMA,IAAA,WAAA,KAAA,IAAA,IAAA,WAAA,KAAA,IAAA,EAAA;AACA,eAAA,gBAAA,GAAA,KAAA;AACA,eAAA,OAAA,CAAA,QAAA,GAAA,CAAA;AACA,eAAA,YAAA,CAAA,uBAAA,GAAA,CACA;AAAA,YAAA,QAAA,EAAA,IAAA;AAAA,YAAA,OAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WADA,CAAA;AAGA,SANA,MAMA;AACA,eAAA,gBAAA,GAAA,KAAA;AACA,eAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AACA,eAAA,YAAA,CAAA,uBAAA,GAAA,CACA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,YAAA,OAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WADA,CAAA;AAGA,SA7BA,CA8BA;AACA;AACA;;;AACA,YAAA,WAAA,GAAA,KAAA,OAAA,CAAA,WAAA;;AACA,YAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,EAAA;AACA,eAAA,gBAAA,GAAA,IAAA;;AACA,cAAA,WAAA,KAAA,KAAA,EAAA;AACA,iBAAA,YAAA;AACA;AACA,SALA,MAKA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,EAAA;AACA,cAAA,WAAA,KAAA,KAAA,IAAA,WAAA,KAAA,KAAA,EAAA;AACA,iBAAA,YAAA;AACA;AACA,SAJA,MAIA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,EAAA;AACA,cAAA,WAAA,KAAA,KAAA,EAAA;AACA,iBAAA,YAAA;AACA;AACA,SAJA,MAIA,IAAA,WAAA,KAAA,IAAA,IAAA,WAAA,KAAA,IAAA,EAAA;AACA,cACA,WAAA,KAAA,KAAA,IACA,WAAA,IAAA,KAAA,IAAA,KAAA,aAAA,KAAA,CAFA,EAGA;AACA,iBAAA,YAAA;AACA;AACA,SAPA,MAOA,IACA,WAAA,KAAA,IAAA,IACA,WAAA,KAAA,IADA,IAEA,WAAA,KAAA,IAFA,IAGA,WAAA,KAAA,IAJA,EAKA;AACA,cAAA,WAAA,KAAA,KAAA,EAAA;AACA,iBAAA,YAAA;AACA;AACA,SATA,MASA,IAAA,WAAA,KAAA,CAAA,EAAA;AACA,eAAA,gBAAA,GAAA,IAAA,CADA,CAEA;AACA;AACA;AACA;AACA;AACA,SAtEA,CAuEA;AACA;;;AACA,YAAA,KAAA,aAAA,IAAA,IAAA,IAAA,KAAA,OAAA,CAAA,kBAAA,EAAA;AACA;AACA,cACA,CAAA,IAAA,EAAA,IAAA,EAAA,QAAA,CAAA,WAAA,KACA,CAAA,KAAA,OAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,IAAA,CAFA,EAGA;AACA,iBAAA,YAAA;AACA;AACA;AACA;AACA,KA5pBA;AA6pBA,IAAA,aA7pBA,yBA6pBA,IA7pBA,EA6pBA;AACA,WAAA,UAAA,GAAA,IAAA,CAAA,IAAA;AACA,WAAA,SAAA,GAAA,IAAA,CAAA,GAAA;;AACA,UAAA,KAAA,SAAA,CAAA,MAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,IAAA,QAAA,EAAA;AACA,aAAA,YAAA;AACA,OAFA,MAEA;AACA,aAAA,MAAA;AACA;AACA,KArqBA;AAsqBA,IAAA,MAtqBA,oBAsqBA;AAAA;;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,IAAA,CAAA,IAAA,KAAA,UAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,OAAA,GAAA,IAAA;AACA,QAAA,KAAA,CACA,OADA,CACA;AACA,UAAA,GAAA,EAAA,qCADA;AAEA,UAAA,MAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,KAAA;AAHA,SADA,EAMA,IANA,CAMA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;;AACA,cAAA,IAAA,GAAA,MAAA;AACA,UAAA,SAAA,CAAA;AAAA,YAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,WAFA;AAGA,SAdA;AAeA;AACA,KA1rBA;AA2rBA,IAAA,YA3rBA,0BA2rBA;AACA,MAAA,aAAA,CAAA;AAAA,QAAA,GAAA,EAAA,KAAA,SAAA,CAAA,IAAA;AAAA,OAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA;AACA,KA7rBA;AA8rBA,IAAA,YA9rBA,0BA8rBA;AACA;AACA,WAAA,OAAA,CAAA,WAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,WAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,aAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,WAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,cAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,kBAAA,GAAA,IAAA;AACA,KAtsBA;AAusBA;AACA,IAAA,oBAxsBA,gCAwsBA,KAxsBA,EAwsBA,MAxsBA,EAwsBA,aAxsBA,EAwsBA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,WAAA,aAAA,GAAA,aAAA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,KAAA,IAAA,CAAA,EAAA;AACA,YAAA,KAAA,GAAA,KAAA,OAAA,CAAA,eAAA;;AACA,YAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WAAA;AACA;AACA,SAHA,MAGA,IAAA,KAAA,OAAA,CAAA,UAAA,IAAA,IAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WAAA;AACA;AACA,SAHA,MAGA;AACA,cAAA,KAAA,OAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,iBAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AACA;AACA;;AACA,eAAA,OAAA,CAAA,WAAA,GAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CALA,CAMA;AACA;AACA;AACA;;AACA,eAAA,KAAA,CAAA,YAAA,CAAA,SAAA,GAAA,KAAA,OAAA,CAAA,EAAA;AACA,eAAA,KAAA,CAAA,YAAA,CAAA,YAAA,CACA,KAAA,OAAA,CAAA,WADA,EAEA,CAFA,EAGA,KAAA,OAAA,CAAA,UAHA,EAIA,KAAA,OAAA,CAAA,OAJA,EAKA,MALA,EAXA,CAiBA;AACA;AACA;AACA,OA5BA,MA4BA;AACA,YAAA,KAAA,OAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,eAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AACA;AACA;;AACA,aAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,KAAA,OAAA,CAAA,OAAA,EALA,CAKA;AACA;AACA,KA9uBA;AA+uBA,IAAA,gBA/uBA,4BA+uBA,IA/uBA,EA+uBA,IA/uBA,EA+uBA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,OAAA,CAAA,WAAA,GAAA,IAAA,CAAA,IAAA,CAFA,CAGA;AACA;AACA,KApvBA;AAqvBA;AACA,IAAA,uBAtvBA,mCAsvBA,IAtvBA,EAsvBA,IAtvBA,EAsvBA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,iBAAA,GAAA,IAAA;;AACA,UAAA,KAAA,WAAA,IAAA,CAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA,KAAA,aAAA,EAAA,SAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA,KAAA,aAAA,EAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,OAHA,MAGA;AACA,aAAA,aAAA,GAAA,IAAA,CAAA,aAAA;AACA,aAAA,OAAA,CAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,aAAA,OAAA,CAAA,WAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,OAAA,CAAA,aAAA,GAAA,MAAA,CACA,IAAA,CAAA,MAAA,IAAA,SAAA,GAAA,IAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MADA,CAAA;AAGA,aAAA,OAAA,CAAA,WAAA,GAAA,MAAA,CAAA,IAAA,CAAA,WAAA,CAAA;AACA,aAAA,OAAA,CAAA,cAAA,GAAA,IAAA,CAAA,OAAA,CARA,CASA;;AACA,aAAA,OAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,cAAA,CAVA,CAWA;AACA;;AACA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,gBAAA,CAAA;AAAA,UAAA,EAAA,EAAA,IAAA,CAAA;AAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,MAAA,GAAA,IAAA,CAAA,OAAA,CAAA,IAAA;AACA,cAAA,WAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,cAAA,KAAA,GAAA,CAAA;;AACA,cAAA,MAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,KAAA;AACA,WAFA,MAEA;AACA,YAAA,MAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,WAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;AACA,oBAAA,IAAA,CAAA,EAAA,KAAA,KAAA,CAAA,EAAA,EAAA;AACA,kBAAA,WAAA,CAAA,CAAA,CAAA,CAAA,SAAA,GAAA,IAAA,CAAA,EAAA;AACA,kBAAA,WAAA,CAAA,CAAA,CAAA,CAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,kBAAA,WAAA,CAAA,CAAA,CAAA,CAAA,SAAA,GAAA,IAAA;AACA,sBAAA,KAAA,GAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,kBAAA,MAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA;AACA,iBANA,MAMA;AACA,kBAAA,KAAA;AACA;AACA,eAVA;AAWA,aAZA;AAaA;;AACA,cAAA,KAAA,GAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,IAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,EAAA,SAAA,GAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,EAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,EAAA,SAAA,GAAA,IAAA;AACA,WALA,MAKA;AACA,YAAA,MAAA,CAAA,OAAA,CAAA,WAAA,CAAA,CAAA,CAAA;AACA;AACA,SA7BA;AA8BA;AACA,KAzyBA;;AA2yBA;AACA,IAAA,eA5yBA,6BA4yBA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,gBAAA;AACA,KA9yBA;;AAgzBA;AACA,IAAA,kBAjzBA,gCAizBA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;AACA,UAAA,KAAA,GAAA,KAAA,OAAA,CAAA,IAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,MAAA,IAAA,SAAA,EAAA;AACA,UAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,EAAA,KAAA,IAAA,CAAA,EAAA,EAAA;AACA,kBAAA,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,cAAA,KAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA;AACA;AACA,WALA;AAMA;AACA,OAXA;AAYA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA;AACA,KAj0BA;;AAm0BA;AACA,IAAA,eAAA,EAAA,yBAAA,IAAA,EAAA;AACA,UAAA,MAAA,GAAA,KAAA,OAAA,CAAA,IAAA;;AACA,UAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,GAAA,IAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,IAAA;;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,MAAA,GACA,MAAA,CAAA,CAAA,CAAA,CAAA,aAAA,IAAA,SAAA,GACA,MAAA,CAAA,CAAA,CAAA,CAAA,aADA,GAEA,MAAA,CAAA,CAAA,CAAA,CAAA,EAHA;;AAIA,gBAAA,IAAA,CAAA,CAAA,CAAA,CAAA,EAAA,KAAA,MAAA,EAAA;AACA,cAAA,GAAA,CAAA,MAAA,CAAA,GAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACA;AACA;AACA;;AACA,aAAA,OAAA,CAAA,IAAA,GAAA,KAAA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA,CAAA;AACA;AACA,KAv1BA;AAy1BA;AACA,IAAA,qBA11BA,mCA01BA;AACA,UAAA,KAAA,GAAA,KAAA,OAAA,CAAA,IAAA,CADA,CAEA;;AACA,UACA,KAAA,OAAA,CAAA,WAAA,KAAA,GAAA,IACA,KAAA,OAAA,CAAA,WAAA,KAAA,GADA,IAEA,KAAA,OAAA,CAAA,WAAA,KAAA,GAFA,IAGA,KAAA,OAAA,CAAA,WAAA,KAAA,CAJA,EAKA;AACA,YAAA,QAAA,GAAA,KAAA,CAAA,MAAA,CAAA,UAAA,KAAA,EAAA,IAAA,EAAA;AACA,iBAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,SAFA,EAEA,CAFA,CAAA;;AAGA,YAAA,QAAA,KAAA,GAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA,0BAAA,QAAA,GAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA;;AACA,aAAA,IAAA;AACA,KAh3BA;AAi3BA,IAAA,QAj3BA,sBAi3BA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,WAAA,SAAA,GAAA;AACA,QAAA,MAAA,EAAA,IAAA,CAAA,OAAA,CAAA,EADA;AAEA,QAAA,QAAA,EAAA,IAAA,CAAA,OAAA,CAAA,SAFA;AAGA,QAAA,UAAA,EAAA,IAAA,CAAA,OAAA,CAAA;AAHA,OAAA;AAKA,KAz3BA;AA03BA,IAAA,SA13BA,qBA03BA,IA13BA,EA03BA;AACA,UAAA,SAAA,GAAA,UAAA,IAAA,CAAA,WAAA,GAAA,KAAA;AACA,WAAA,cAAA,GAAA;AACA,QAAA,MAAA,EAAA,IAAA,CAAA,EADA;AAEA,QAAA,SAAA,EAAA,gBAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OAAA;AAKA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,OAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA,KAr4BA;AAs4BA,IAAA,UAt4BA,sBAs4BA,IAt4BA,EAs4BA;AACA;AACA,WAAA,QAAA,CAAA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,OAAA;;AACA,UAAA,IAAA,IAAA,CAAA,EAAA;AACA,aAAA,IAAA;AACA;AACA,KA54BA;;AA64BA;AACA,IAAA,kBA94BA,gCA84BA;AACA,WAAA,KAAA,CAAA,qBAAA,CAAA,YAAA,CAAA,CAAA,EAAA,KAAA,OAAA,CAAA,EAAA;AACA,KAh5BA;;AAi5BA;AACA,IAAA,sBAAA,EAAA,gCAAA,IAAA,EAAA;AACA,WAAA,OAAA,CAAA,QAAA,GAAA,IAAA,CAAA,EAAA;;AACA,UAAA,IAAA,CAAA,YAAA,IAAA,IAAA,IAAA,IAAA,CAAA,YAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,OAAA,CAAA,UAAA,GAAA,IAAA,CAAA,YAAA;AACA,OAFA,MAEA;AACA,aAAA,OAAA,CAAA,UAAA,GAAA,IAAA,CAAA,WAAA;AACA;AACA,KAz5BA;;AA05BA;AACA,IAAA,WA35BA,yBA25BA;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,CAAA,EADA,CACA;AACA,KA75BA;AA85BA,IAAA,wBAAA,EAAA,kCAAA,IAAA,EAAA;AACA,WAAA,OAAA,CAAA,UAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,OAAA,CAAA,YAAA,GAAA,IAAA,CAAA,IAAA;AACA,KAj6BA;AAk6BA,IAAA,WAl6BA,yBAk6BA;AACA,UAAA,KAAA,OAAA,CAAA,KAAA,IAAA,GAAA,EAAA;AACA,aAAA,OAAA,CAAA,gBAAA,GAAA,CAAA;AACA,aAAA,OAAA,CAAA,cAAA,GAAA,KAAA,OAAA,CAAA,cAAA;AACA,aAAA,SAAA,GAAA,IAAA;AACA,OAJA,MAIA;AACA,aAAA,SAAA,GAAA,KAAA;AACA;AACA,KA16BA;AA26BA,IAAA,oBA36BA,kCA26BA;AACA,UAAA,KAAA,aAAA,EAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,MAAA,GAAA,CAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,UAAA,GAAA,CAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,IAAA,GAAA,CAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,OAAA,GAAA,KAAA,OAAA,CAAA,OAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,OAAA,GAAA,KAAA,OAAA,CAAA,OAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,gBAAA,GAAA,CAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,IAAA,GAAA,IAAA;AACA,WAAA,QAAA,CAAA,IAAA,CAAA,QAAA;AACA,KAr7BA;AAs7BA,IAAA,oBAt7BA,gCAs7BA,IAt7BA,EAs7BA;AACA,WAAA,OAAA,CAAA,cAAA,GAAA,IAAA,CAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,EAAA;AACA,KAx7BA;AAy7BA,IAAA,iBAz7BA,6BAy7BA,GAz7BA,EAy7BA;AACA;AACA,UACA,CAAA,6BAAA,IAAA,CAAA,GAAA,CAAA,IACA,CAAA,qCAAA,IAAA,CAAA,GAAA,CADA,IAEA,CAAA,+BAAA,IAAA,CAAA,GAAA,CAHA,EAIA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,2BAAA;AACA;AACA;AAl8BA,IA/cA;AAm5CA,EAAA,OAn5CA,qBAm5CA;AACA;AACA,SAAA,SAAA,GAAA;AACA,MAAA,gBAAA,EAAA,KAAA,CAAA,kBAAA;AADA,KAAA;AAGA,SAAA,YAAA,GAAA,KAAA,CAAA,UAAA,CAAA;AACA,SAAA,WAAA,CAAA,KAAA,MAAA,CAAA,KAAA,CAAA,EAAA;AAEA,SAAA,aAAA,GAAA,KAAA,OAAA,CAAA,OAAA;;AACA,QAAA,KAAA,aAAA,IAAA,IAAA,IAAA,KAAA,aAAA,IAAA,IAAA,EAAA;AACA,WAAA,YAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AACA,QAAA,QAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,MAFA;AAGA,QAAA,OAAA,EAAA;AAHA,OAAA;AAKA,WAAA,YAAA,CAAA,YAAA,GAAA;AACA,QAAA,QAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,MAFA;AAGA,QAAA,OAAA,EAAA;AAHA,OAAA;AAKA;AACA;AAx6CA,CAAA", "sourcesContent": ["<template>\r\n  <div class=\"testaa\">\r\n    <!--重点就是下面的代码了-->\r\n    <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n    <div solt=\"header\">\r\n      <Row>\r\n        <Col span=\"24\" style=\"text-align: right; right: 10px\">\r\n          <Button\r\n            type=\"success\"\r\n            :loading=\"isLoading == 0 ? loading : false\"\r\n            @click=\"onModalOK(0)\"\r\n            >保存</Button\r\n          >\r\n          <Button\r\n            type=\"primary\"\r\n            :loading=\"isLoading == 1 ? loading : false\"\r\n            @click=\"onModalOK(1)\"\r\n            >提交</Button\r\n          >\r\n          <!--                    <Button v-if=\"isShowFlow\" type=\"success\" @click=\"showFlow\">流程图</Button>-->\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n    <Modal\r\n      v-model=\"modal1\"\r\n      title=\"温馨提示\"\r\n      okText=\"是\"\r\n      cancelText=\"否\"\r\n      @on-ok=\"okModel\"\r\n      @on-cancel=\"cancelModel\"\r\n    >\r\n      <p style=\"margin: 25px 0 25px 40px\">是否新型室分?</p>\r\n    </Modal>\r\n    <cl-wf-btn\r\n      ref=\"clwfbtn\"\r\n      :isStart=\"true\"\r\n      :params=\"workFlowParams\"\r\n      @on-ok=\"doWorkFlow\"\r\n      v-show=\"false\"\r\n    ></cl-wf-btn>\r\n    <!-- 查看流程 -->\r\n    <Modal v-model=\"showWorkFlow\" title=\"电表流程及审批意见跟踪表\" :width=\"800\">\r\n      <WorkFlowInfoComponet\r\n        :wfHisParams=\"hisParams\"\r\n        v-if=\"showWorkFlow\"\r\n      ></WorkFlowInfoComponet>\r\n    </Modal>\r\n    <select-electric-type\r\n      ref=\"selectElectricType\"\r\n      v-on:listenToSetElectricType=\"setElectricData\"\r\n    ></select-electric-type>\r\n    <country-modal\r\n      ref=\"countryModal\"\r\n      v-on:getDataFromModal=\"getDataFromModal\"\r\n    ></country-modal>\r\n    <station-modal\r\n      ref=\"stationModal\"\r\n      v-on:getDataFromStationModal=\"getDataFromStationModal\"\r\n    ></station-modal>\r\n    <ammeter-protocol-list\r\n      ref=\"selectAmmeterProtocol\"\r\n      v-on:listenToSetAmmeterProrocol=\"setAmmeterProrocolData\"\r\n    ></ammeter-protocol-list>\r\n    <customer-list\r\n      ref=\"customerList\"\r\n      v-on:getDataFromCustomerModal=\"getDataFromCustomerModal\"\r\n    ></customer-list>\r\n    <!--        <Modal v-model=\"showModel\" width=\"80%\" :title=\"title\">-->\r\n    <Card class=\"menu-card\">\r\n      <Collapse :value=\"['Panel1', 'Panel2', 'Panel3', 'Panel4', 'Panel6']\">\r\n        <Panel name=\"Panel1\"\r\n          >基本信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"项目名称：\" prop=\"projectname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.projectname\"\r\n                        placeholder=\"**路**号**楼电表\"\r\n                        @on-blur=\"projectNameChange\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.projectname != null &&\r\n                          oldData.projectname != ammeter.projectname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.projectname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <!--                                        <Col span=\"6\">-->\r\n                  <!--                                            <FormItem label=\"电表户号：\" prop=\"ammetername\">-->\r\n                  <!--                                                <cl-input :maxlength=30 v-model=\"ammeter.ammetername\"></cl-input>-->\r\n                  <!--                                                <label v-if=\"oldData.ammetername != null &&oldData.ammetername != ammeter.ammetername\" style=\"color: red;\">历史数据：{{oldData.ammetername}}</label>-->\r\n                  <!--                                            </FormItem>-->\r\n                  <!--                                        </Col>-->\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag === 1\">\r\n                    <FormItem\r\n                      label=\"供电局电表编号(下户户号)：\"\r\n                      prop=\"supplybureauammetercode\"\r\n                    >\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauammetercode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauammetercode != null &&\r\n                          oldData.supplybureauammetercode !=\r\n                            ammeter.supplybureauammetercode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauammetercode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag === 2\">\r\n                    <FormItem\r\n                      label=\"供电局电表编号(电表编号)：\"\r\n                      prop=\"supplybureauammetercode\"\r\n                    >\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauammetercode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauammetercode != null &&\r\n                          oldData.supplybureauammetercode !=\r\n                            ammeter.supplybureauammetercode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauammetercode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否智能电表：\" prop=\"issmartammeter\">\r\n                      <RadioGroup v-model=\"ammeter.issmartammeter\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.issmartammeter != null &&\r\n                          oldData.issmartammeter != ammeter.issmartammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.issmartammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否实体电表：\" prop=\"isentityammeter\">\r\n                      <RadioGroup v-model=\"ammeter.isentityammeter\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.isentityammeter != null &&\r\n                          oldData.isentityammeter != ammeter.isentityammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.isentityammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\" v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n                    <FormItem label=\"电表编号：\">\r\n                      <cl-input\r\n                        disabled\r\n                        readonly\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.ammetername\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetername != null &&\r\n                          oldData.ammetername != ammeter.ammetername\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetername }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"configVersion != 'ln' && configVersion != 'LN'\">\r\n                    <FormItem label=\"电表编号：\" prop=\"ammetername\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.ammetername\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetername != null &&\r\n                          oldData.ammetername != ammeter.ammetername\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetername }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"电表用途：\" prop=\"ammeteruse\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.ammeteruse\"\r\n                        category=\"ammeterUse\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammeteruse != null &&\r\n                          oldData.ammeteruse != ammeter.ammeteruse\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldAmmeteruse }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"关联实际报账电表：\"\r\n                      prop=\"parentCode\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.parentCode\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addAmmeterProtocol\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.parentCode != null &&\r\n                          oldData.parentCode != ammeter.parentCode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.parentCode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"供电局名称：\" prop=\"supplybureauname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauname != null &&\r\n                          oldData.supplybureauname != ammeter.supplybureauname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"电表类型2222：\" prop=\"ammetertype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.ammetertype\"\r\n                        category=\"ammeterType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetertype != null &&\r\n                          oldData.ammetertype != ammeter.ammetertype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldAmmetertype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"包干类型：\" prop=\"packagetype\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.packagetype\"\r\n                        category=\"packageType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      >\r\n                      </cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.packagetype != null &&\r\n                          oldData.packagetype != ammeter.packagetype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPackagetype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"所属分公司：\" prop=\"company\">\r\n                      <Select\r\n                        v-model=\"ammeter.company\"\r\n                        @on-change=\"selectChange(ammeter.company)\"\r\n                      >\r\n                        <Option\r\n                          v-for=\"item in companies\"\r\n                          :value=\"item.id\"\r\n                          :key=\"item.id\"\r\n                          >{{ item.name }}</Option\r\n                        >\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.company != null && oldData.company != ammeter.company\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.companyName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <!--                                            <FormItem label=\"所属部门\" prop=\"countryName\">-->\r\n                    <!--                                                <Input icon=\"ios-archive\" v-model=\"ammeter.countryName\"-->\r\n                    <!--                                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly/>-->\r\n                    <!--                                            </FormItem>-->\r\n                    <FormItem\r\n                      label=\"所属部门：\"\r\n                      prop=\"countryName\"\r\n                      v-if=\"isAdmin == true\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-if=\"isCityAdmin == true || isEditByCountry == false\"\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseResponseCenter()\"\r\n                        readonly\r\n                      />\r\n                      <Input\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-else-if=\"isEditByCountry == true && isCityAdmin == false\"\r\n                        readonly\r\n                      />\r\n                      <!--                                                <label v-if=\"oldData.countryName != null &&oldData.countryName != ammeter.countryName\" style=\"color: red;\">历史数据：{{oldData.countryName}}</label>-->\r\n                    </FormItem>\r\n                    <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\">\r\n                      <Select v-model=\"ammeter.country\" v-if=\"isEditByCountry == false\">\r\n                        <Option\r\n                          v-for=\"item in departments\"\r\n                          :value=\"item.id\"\r\n                          :key=\"item.id\"\r\n                          >{{ item.name }}</Option\r\n                        >\r\n                      </Select>\r\n                      <Input\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-else-if=\"isEditByCountry == true\"\r\n                        readonly\r\n                      />\r\n                      <!--                                                <label v-if=\"oldData.countryName != null &&oldData.countryName != ammeter.countryName\" style=\"color: red;\">历史数据：{{oldData.countryName}}</label>-->\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"所属分局或支局：\" prop=\"substation\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.substation\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.substation != null &&\r\n                          oldData.substation != ammeter.substation\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.substation }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"详细地址：\" prop=\"address\">\r\n                      <cl-input :maxlength=\"100\" v-model=\"ammeter.address\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.address != null && oldData.address != ammeter.address\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.address }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费名称：\" prop=\"payname\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.payname\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.payname != null && oldData.payname != ammeter.payname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.payname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费类型：\" prop=\"payperiod\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.payperiod\"\r\n                        category=\"payPeriod\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.payperiod != null &&\r\n                          oldData.payperiod != ammeter.payperiod\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPayperiod }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费经办人：\" prop=\"paymanager\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.paymanager\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.paymanager != null &&\r\n                          oldData.paymanager != ammeter.paymanager\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.paymanager }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"付费方式：\" prop=\"paytype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.paytype\"\r\n                        category=\"payType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.paytype != null && oldData.paytype != ammeter.paytype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPaytype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"管理负责人：\" prop=\"ammetermanager\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.ammetermanager\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetermanager != null &&\r\n                          oldData.ammetermanager != ammeter.ammetermanager\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetermanager }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"单价(元)：\" prop=\"price\">\r\n                      <InputNumber :maxlength=\"20\" v-model=\"ammeter.price\"></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.price != null && oldData.price != ammeter.price\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.price }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"用电类型：\" prop=\"classifications\">\r\n                      <Cascader\r\n                        :data=\"classificationData\"\r\n                        :change-on-select=\"true\"\r\n                        v-model=\"ammeter.classifications\"\r\n                        @on-change=\"changeClassifications\"\r\n                      ></Cascader>\r\n                      <label\r\n                        v-if=\"\r\n                          ammeter.classifications !== undefined &&\r\n                          ammeter.classifications.length > 0\r\n                        \"\r\n                      >\r\n                        <label v-for=\"(item, i) in ammeter.classifications\" :key=\"i\">\r\n                          <label\r\n                            v-if=\"\r\n                              i === ammeter.classifications.length - 1 &&\r\n                              oldData.electrotype != null &&\r\n                              oldData.electrotype != item\r\n                            \"\r\n                            style=\"color: red\"\r\n                            >历史数据：{{ oldData.electrotypename }}</label\r\n                          >\r\n                        </label>\r\n                      </label>\r\n                      <label\r\n                        v-else-if=\"\r\n                          ammeter.classifications !== undefined &&\r\n                          ammeter.classifications.length <= 0\r\n                        \"\r\n                      >\r\n                        <label v-if=\"oldData.electrotype != null\" style=\"color: red\"\r\n                          >历史数据：{{ oldData.electrotypename }}</label\r\n                        >\r\n                      </label>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"倍率：\"\r\n                      prop=\"magnification\"\r\n                      :class=\"{ requireStar: true }\"\r\n                    >\r\n                      <InputNumber\r\n                        :min=\"1\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.magnification\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.magnification != null &&\r\n                          oldData.magnification != ammeter.magnification\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.magnification }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"对外结算类型：\" prop=\"directsupplyflag\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.directsupplyflag\"\r\n                        category=\"directSupplyFlag\"\r\n                        :disabled=\"iszgzOnly\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.directsupplyflag != null &&\r\n                          oldData.directsupplyflag != ammeter.directsupplyflag\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldDirectsupplyflag }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem label=\"电价性质：\" prop=\"electrovalencenature\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.electrovalencenature\"\r\n                        category=\"electrovalenceNature\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.electrovalencenature != null &&\r\n                          oldData.electrovalencenature != ammeter.electrovalencenature\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldElectrovalencenature }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"翻表读数(度)：\" prop=\"maxdegree\">\r\n                      <InputNumber\r\n                        :maxlength=\"20\"\r\n                        v-model=\"ammeter.maxdegree\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.maxdegree != null &&\r\n                          oldData.maxdegree != ammeter.maxdegree\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.maxdegree }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"状态：\" prop=\"status\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.status\"\r\n                        @on-change=\"changeStatus\"\r\n                        category=\"status\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"oldData.status != null && oldData.status != ammeter.status\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStatus }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"占局(站)定额电量比例(%)：\" prop=\"quotapowerratio\">\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :min=\"1\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.quotapowerratio\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.quotapowerratio != null &&\r\n                          oldData.quotapowerratio != ammeter.quotapowerratio\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.quotapowerratio }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"合同对方：\" prop=\"contractOthPart\">\r\n                      <cl-input\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.contractOthPart\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.contractOthPart != null &&\r\n                          oldData.contractOthPart != ammeter.contractOthPart\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.contractOthPart }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-else-if=\"!isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\">\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}</Option\r\n                        >\r\n                      </Select>\r\n                      <!--                                            <cl-select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\"-->\r\n                      <!--                                                       category=\"property\"-->\r\n                      <!--                                                       labelField=\"typeName\" valueField=\"typeCode\"></cl-select>-->\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\">\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}</Option\r\n                        >\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否换表：\" prop=\"ischangeammeter\">\r\n                      <RadioGroup v-model=\"ammeter.ischangeammeter\">\r\n                        <Radio label=\"0\" disabled>\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\" disabled>\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ischangeammeter != null &&\r\n                          oldData.ischangeammeter != ammeter.ischangeammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.ischangeammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"原电表/协议编号：\" prop=\"oldAmmeterName\">\r\n                      <cl-input v-model=\"ammeter.oldAmmeterName\" readonly></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.oldAmmeterName != null &&\r\n                          oldData.oldAmmeterName != ammeter.oldAmmeterName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.oldAmmeterName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"原电表还需报账电量(度)：\" prop=\"oldBillPower\">\r\n                      <cl-input\r\n                        :maxlength=\"20\"\r\n                        v-model=\"ammeter.oldBillPower\"\r\n                        :placeholder=\"\r\n                          [2, 4].includes(ammeter.property) ? '需填写【分摊后电量】' : ''\r\n                        \"\r\n                      ></cl-input>\r\n                      <label style=\"color: red; margin-left: -100px; margin-top: 5px\"\r\n                        >注意：该电量将计入新表的总电量(分割后电量)，没有请填0</label\r\n                      >\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.oldBillPower != null &&\r\n                          oldData.oldBillPower != ammeter.oldBillPower\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.oldBillPower }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"!isCDCompany\">\r\n                    <FormItem\r\n                      label=\"分割比例(%)：\"\r\n                      prop=\"percent\"\r\n                      v-if=\"ammeter.property === 2 || ammeter.property === 4\"\r\n                    >\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.percent\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.percent != null && oldData.percent != ammeter.percent\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.percent }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem\r\n                      label=\"分割比例(%)：\"\r\n                      prop=\"percent\"\r\n                      v-if=\"ammeter.property === 2 || ammeter.property === 4\"\r\n                    >\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.percent\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.percent != null && oldData.percent != ammeter.percent\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.percent }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"收款客户名称：\"\r\n                      prop=\"customerName\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.customerName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addCustomer\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.customerName != null &&\r\n                          oldData.customerName != ammeter.customerName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.customerName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN')\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"是否铁塔包干：\" prop=\"islumpsum\">\r\n                      <RadioGroup\r\n                        v-model=\"ammeter.islumpsum\"\r\n                        @on-change=\"updatepackagetype\"\r\n                      >\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.islumpsum != null &&\r\n                          oldData.islumpsum != ammeter.islumpsum\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.islumpsum == \"0\" ? \"否\" : \"是\" }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"月包干电量(度)：\" prop=\"ybgPower\">\r\n                      <cl-input :maxlength=\"20\" v-model=\"ammeter.ybgPower\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ybgPower != null && oldData.ybgPower != ammeter.ybgPower\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ybgPower }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干起始日期：\" prop=\"lumpstartdate\">\r\n                      <cl-date-picker\r\n                        type=\"date\"\r\n                        placeholder=\"包干起始日期\"\r\n                        v-model=\"ammeter.lumpstartdate\"\r\n                        style=\"width: 160px\"\r\n                      ></cl-date-picker>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.lumpstartdate != null &&\r\n                          oldData.lumpstartdate != ammeter.lumpstartdate\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.lumpstartdate }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干截止日期：\" prop=\"lumpenddate\">\r\n                      <cl-date-picker\r\n                        type=\"date\"\r\n                        placeholder=\"包干截止日期\"\r\n                        v-model=\"ammeter.lumpenddate\"\r\n                        style=\"width: 160px\"\r\n                      ></cl-date-picker>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.lumpenddate != null &&\r\n                          oldData.lumpenddate != ammeter.lumpenddate\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.lumpenddate }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"月包干电费：\" prop=\"fee\">\r\n                      <InputNumber\r\n                        :min=\"0\"\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.fee\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.fee != null && oldData.fee != ammeter.fee\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.fee }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc' || configVersion == 'SC'\">\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"输配电公司：\"\r\n                      prop=\"transdistricompany\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: true,\r\n                          type: 'number',\r\n                          message: '请选择',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <cl-select\r\n                        v-model=\"ammeter.transdistricompany\"\r\n                        category=\"transdistricompany\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.transdistricompany != null &&\r\n                          oldData.transdistricompany != ammeter.transdistricompany\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldtransdistricompany }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.directsupplyflag == 1 && ammeter.transdistricompany == 1\r\n                    \"\r\n                  >\r\n                    <FormItem\r\n                      label=\"电压等级：\"\r\n                      prop=\"voltageClass\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: true,\r\n                          type: 'number',\r\n                          message: '请选择',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <cl-select\r\n                        v-model=\"ammeter.voltageClass\"\r\n                        category=\"voltageClass\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.voltageClass != null &&\r\n                          oldData.voltageClass != ammeter.voltageClass\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldvoltageClass }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc'\">\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"是否转改直：\"\r\n                      prop=\"iszgz\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.iszgz\" @on-change=\"iszgzchange\">\r\n                        <Radio label=\"0\" :disabled=\"disablediszgz\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"原转供电表编号：\"\r\n                      prop=\"oldammetername\"\r\n                      v-if=\"ammeter.iszgz == '1'\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: ammeter.iszgz == '1',\r\n                          message: '不能为空',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        :value=\"\r\n                          ammeter.oldammetername\r\n                            ? ammeter.oldammetername.split(',')[0]\r\n                            : null\r\n                        \"\r\n                        readonly\r\n                        :disabled=\"disablediszgz\"\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseoldammetername\"\r\n                      />\r\n                    </FormItem>\r\n                    <ChooseAmmeterModel\r\n                      ref=\"chooseAmmeterModel\"\r\n                      v-if=\"ammeter.iszgz == '1'\"\r\n                      v-on:getAmmeterModelModal=\"getAmmeterModelModal\"\r\n                    />\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"是否直购电：\"\r\n                      prop=\"directFlag\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.directFlag\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"是否含办公：\"\r\n                      prop=\"officeFlag\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.officeFlag\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel2\"\r\n          >关联局站信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter1\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"局(站)名称：\"\r\n                      prop=\"stationName\"\r\n                      :class=\"{ requireStar: isRequireFlag }\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.stationName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseResponseCenter(1)\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationName != null &&\r\n                          oldData.stationName != ammeter.stationName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)编码：\" prop=\"stationcode\">\r\n                      <cl-input readonly v-model=\"ammeter.stationcode\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationcode != null &&\r\n                          oldData.stationcode != ammeter.stationcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <!--                                            <FormItem label=\"局(站)状态\" prop=\"stationstatus\">-->\r\n                    <!--                                                <cl-select v-model=\"ammeter.stationstatus\"-->\r\n                    <!--                                                           category=\"status\"-->\r\n                    <!--                                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>-->\r\n                    <!--                                                <label v-if=\"oldData.stationstatus != null &&oldData.stationstatus != ammeter.stationstatus\"-->\r\n                    <!--                                                       style=\"color: red;\">历史数据：{{oldStationstatus}}</label>-->\r\n                    <!--                                            </FormItem>-->\r\n                    <FormItem label=\"局(站)状态：\" prop=\"stationstatus\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.stationstatus\"\r\n                        filterable\r\n                        category=\"stationStatus\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationstatus != null &&\r\n                          oldData.stationstatus != ammeter.stationstatus\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStationstatus }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)类型：\" prop=\"stationtype\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.stationtype\"\r\n                        filterable\r\n                        category=\"BUR_STAND_TYPE\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationtype != null &&\r\n                          oldData.stationtype != ammeter.stationtype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStationtype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)地址：\" prop=\"stationaddress\">\r\n                      <cl-input readonly v-model=\"ammeter.stationaddress\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationaddress != null &&\r\n                          oldData.stationaddress != ammeter.stationaddress\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationaddress }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"资源局站id：\" prop=\"termname\">\r\n                      <cl-input\r\n                        readonly\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.termname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.termname != null &&\r\n                          oldData.termname != ammeter.termname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.termname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否有空调：\" prop=\"isairconditioning\">\r\n                      <RadioGroup v-model=\"ammeter.isairconditioning\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.isairconditioning != null &&\r\n                          oldData.isairconditioning != ammeter.isairconditioning\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.isairconditioning == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"核定电量：\" prop=\"vouchelectricity\">\r\n                      <InputNumber\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.vouchelectricity\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.vouchelectricity != null &&\r\n                          oldData.vouchelectricity != ammeter.vouchelectricity\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.vouchelectricity }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"isCDCompany\">\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管C网编号：\" prop=\"nmCcode\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmCcode\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmCcode != null && oldData.nmCcode != ammeter.nmCcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmCcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号L2.1G：\" prop=\"nmL2100\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmL2100\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmL2100 != null && oldData.nmL2100 != ammeter.nmL2100\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmL2100 }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号L1.8G：\" prop=\"nmL1800\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmL1800\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmL1800 != null && oldData.nmL1800 != ammeter.nmL1800\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmL1800 }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号C+L800M：\" prop=\"nmCl800m\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmCl800m\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmCl800m != null && oldData.nmCl800m != ammeter.nmCl800m\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmCl800m }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel3\"\r\n          >关联用电类型比例\r\n          <!--                        <span style=\"font-size: 10px;color:red\">（用电类型为：A类机楼、B类机楼、C类机楼、管理办公用电时必须关联用电类型比例）</span>-->\r\n          <div slot=\"content\" v-if=\"isClassification && ammeter.stationcode != null\">\r\n            <cl-table\r\n              ref=\"ammeterTable\"\r\n              strip\r\n              :columns=\"electro.columns\"\r\n              :data=\"electro.data\"\r\n              :searchable=\"false\"\r\n              :showPage=\"false\"\r\n              selectEnabled\r\n              selectMultiple\r\n            >\r\n              <div slot=\"buttons\">\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"md-add\"\r\n                  type=\"primary\"\r\n                  @click=\"addElectricType\"\r\n                  >新增\r\n                </Button>\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"ios-trash-outline\"\r\n                  type=\"warning\"\r\n                  @click=\"removeElectricType\"\r\n                  >删除\r\n                </Button>\r\n              </div>\r\n            </cl-table>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel4\"\r\n          >业主信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter2\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"联系人：\" prop=\"contractname\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.contractname\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.contractname != null &&\r\n                          oldData.contractname != ammeter.contractname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.contractname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"具体位置：\" prop=\"location\">\r\n                      <cl-input :maxlength=\"100\" v-model=\"ammeter.location\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.location != null && oldData.location != ammeter.location\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.location }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"办公电话：\" prop=\"officephone\">\r\n                      <cl-input :maxlength=\"15\" v-model=\"ammeter.officephone\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.officephone != null &&\r\n                          oldData.officephone != ammeter.officephone\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.officephone }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"移动电话：\" prop=\"telephone\">\r\n                      <cl-input :maxlength=\"15\" v-model=\"ammeter.telephone\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.telephone != null &&\r\n                          oldData.telephone != ammeter.telephone\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.telephone }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"对方单位：\" prop=\"userunit\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.userunit\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.userunit != null && oldData.userunit != ammeter.userunit\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.userunit }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款账户：\" prop=\"receiptaccountname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.receiptaccountname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccountname != null &&\r\n                          oldData.receiptaccountname != ammeter.receiptaccountname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccountname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款开户支行：\" prop=\"receiptaccountbank\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.receiptaccountbank\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccountbank != null &&\r\n                          oldData.receiptaccountbank != ammeter.receiptaccountbank\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccountbank }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款账号：\" prop=\"receiptaccounts\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.receiptaccounts\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccounts != null &&\r\n                          oldData.receiptaccounts != ammeter.receiptaccounts\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccounts }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"24\">\r\n                    <FormItem label=\"说明：\" prop=\"memo\">\r\n                      <cl-input\r\n                        type=\"textarea\"\r\n                        :rows=\"3\"\r\n                        v-model=\"ammeter.memo\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"oldData.memo != null && oldData.memo != ammeter.memo\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.memo }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel6\"\r\n          >附件信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <attach-file\r\n                :param=\"fileParam\"\r\n                :attachData=\"attachData\"\r\n                v-on:setAttachData=\"setAttachData\"\r\n              />\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n      </Collapse>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getstationold } from \"@/api/alertcontrol/alertcontrol\";\r\nimport {\r\n  listElectricType,\r\n  checkAmmeterExist,\r\n  getCountrysdata,\r\n  addAmmeter,\r\n  editAmmeter,\r\n  editAmmeterRecord,\r\n  updateAmmeter,\r\n  checkProjectNameExist,\r\n  checkAmmeterByStation,\r\n  getClassification,\r\n  getClassificationId,\r\n  getUserdata,\r\n  checkClassificationLevel,\r\n  listElectricTypeRatio,\r\n  checkAcountByUpdate,\r\n  getUserByUserRole,\r\n  getCountryByUserId,\r\n  attchList,\r\n  removeAttach,\r\n  getChangeAmmeterid,\r\n} from \"@/api/basedata/ammeter.js\";\r\nimport { blist, btext } from \"@/libs/tools\";\r\nimport SelectElectricType from \"./selectElectricType\";\r\nimport countryModal from \"./countryModal\";\r\nimport stationModal from \"./stationModal\";\r\nimport { mapMutations } from \"vuex\";\r\nimport WorkFlowInfoComponet from \"@/view/basic/system/workflow/workFlowInfoComponet\";\r\nimport AmmeterProtocolList from \"@/view/basedata/quota/listAmmeterProtocol\";\r\nimport customerList from \"./customerModal\";\r\nimport ChooseAmmeterModel from \"@/view/basedata/ammeter/chooseAmmeterModel\";\r\nimport axios from \"@/libs/api.request\";\r\nimport { isEmpty } from \"@/libs/validate\";\r\nimport attachFile from \"@/view/basedata/ammeter/attachFile\";\r\nexport default {\r\n  name: \"change1Ammeter\",\r\n  components: {\r\n    stationModal,\r\n    customerList,\r\n    countryModal,\r\n    SelectElectricType,\r\n    WorkFlowInfoComponet,\r\n    AmmeterProtocolList,\r\n    ChooseAmmeterModel,\r\n    attachFile,\r\n  },\r\n  data() {\r\n    //不能输入汉字\r\n    const checkData = (rule, value, callback) => {\r\n      if (value) {\r\n        if (/[\\u4E00-\\u9FA5]/g.test(value)) {\r\n          callback(new Error(\"编码不能输入汉字!\"));\r\n        } else if (escape(value).indexOf(\"%u\") >= 0) {\r\n          callback(new Error(\"编码不能输入中文字符!\"));\r\n        } else {\r\n          callback();\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatorNumber = (rule, value, callback) => {\r\n      if (value.length <= 0) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatorNumberZero = (rule, value, callback) => {\r\n      if (value != null && value == 0) {\r\n        callback(new Error(\"只能输入大于0的数\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatorNumberZero1 = (rule, value, callback) => {\r\n      if (value != null && value < 0) {\r\n        callback(new Error(\"只能输入大于等于0的数\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validateClassifications = (rule, value, callback) => {\r\n      if (value == undefined || value == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        if (value.length <= 0) {\r\n          callback(new Error(\"不能为空\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatelumpstartdate = (rule, value, callback) => {\r\n      let data = this.ammeter;\r\n      let start = data.lumpstartdate;\r\n      let end = data.lumpenddate;\r\n      if (start == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      }\r\n      if (start != null && end != null) {\r\n        if (end <= start) {\r\n          callback(new Error(\"包干起始日期不能大于等于截止日期\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatelumpenddate = (rule, value, callback) => {\r\n      let data = this.ammeter;\r\n      let start = data.lumpstartdate;\r\n      let end = data.lumpenddate;\r\n      if (end == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      }\r\n      if (start != null && end != null) {\r\n        if (end <= start) {\r\n          callback(new Error(\"包干截止日期不能小于等于起始日期\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    //更改标题名称及样式\r\n    let renderHeader = (h, params) => {\r\n      let t = h(\r\n        \"span\",\r\n        {\r\n          style: {\r\n            fontWeight: \"normal\",\r\n            color: \"#ed4014\",\r\n            fontSize: \"12px\",\r\n            fontFamily: \"SimSun\",\r\n            marginRight: \"4px\",\r\n            lineHeight: 1,\r\n            display: \"inline-block\",\r\n          },\r\n        },\r\n        \"*\"\r\n      );\r\n      return h(\"div\", [t, h(\"span\", {}, \"所占比例(%)\")]);\r\n    };\r\n    return {\r\n      propertyright: null, //局站产权\r\n      isRequireFlag: false, //局站是否必填\r\n      modal1: false,\r\n      checkStationType: null,\r\n      ischeckStation: false, //是否需要验证局站只能关联5个\r\n      isoldcheckStation: null, //判断用户关联局站没有,默认没有\r\n      isCDCompany: false, //是否是成都分公司\r\n      configVersion: null, //版本\r\n      propertyList: [],\r\n      propertyReadonly: true,\r\n\r\n      workFlowParams: {},\r\n      hisParams: {},\r\n      isShowFlow: false,\r\n      showWorkFlow: false,\r\n      flowName: null,\r\n\r\n      isError: false, //用电类型比例验证\r\n      isError1: false, //用电类型比例验证\r\n\r\n      loading: false,\r\n      isLoading: null,\r\n\r\n      showModel: false,\r\n      isClassification: false,\r\n      title: \"\",\r\n      isEditByCountry: false,\r\n      isCityAdmin: false,\r\n      isAdmin: false,\r\n      chooseIndex: null,\r\n      electroRowNum: null, //关联用电类型的当前行\r\n      electricTypeModel: false,\r\n      companies: [],\r\n      departments: [],\r\n      classificationData: [], //用电类型\r\n\r\n      oldData: [],\r\n      oldCategory: \"\", //原始数据\r\n      oldPackagetype: \"\", //原始数据\r\n      oldPayperiod: \"\", //原始数据\r\n      oldPaytype: \"\", //原始数据\r\n      oldElectronature: \"\", //原始数据\r\n      oldElectrovalencenature: \"\", //原始数据\r\n      oldElectrotype: \"\", //原始数据\r\n      oldStatus: \"\", //原始数据\r\n      oldProperty: \"\", //原始数据\r\n      oldAmmetertype: \"\", //原始数据\r\n      oldStationstatus: \"\", //原始数据\r\n      oldStationtype: \"\", //原始数据\r\n      oldAmmeteruse: \"\", //原始数据\r\n      oldDirectsupplyflag: \"\", //原始数据\r\n      attachData: [],\r\n      fileParam: {\r\n        busiId: \"\",\r\n        busiAlias: \"附件(协议管理)\",\r\n        categoryCode: \"file\",\r\n        areaCode: \"ln\",\r\n      },\r\n      ruleValidate: {\r\n        isentityammeter: [\r\n          { required: true, message: \"不能为空\", trigger: \"change,blur\" },\r\n        ],\r\n        projectname: [\r\n          //项目名称\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        countryName: [\r\n          //所属部门\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        country: [\r\n          //所属部门\r\n          { required: true, type: \"number\", validator: validatorNumber, trigger: \"blur\" },\r\n        ],\r\n        company: [{ required: true, validator: validatorNumber, trigger: \"blur\" }],\r\n        paytype: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        payperiod: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        ammeteruse: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        ammetertype: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        electrovalencenature: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        classifications: [\r\n          { required: true, validator: validateClassifications, trigger: \"change,blur\" },\r\n        ],\r\n        magnification: [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        directsupplyflag: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            type: \"number\",\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        packagetype: [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contractOthPart: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\r\n        stationName: [],\r\n        status: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        telephone: [{ pattern: /^1\\d{10}$/, message: \"格式不正确\", trigger: \"blur\" }],\r\n        percent: [\r\n          { type: \"number\", validator: validatorNumberZero, trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([0-9]\\d{0,12}))(\\.\\d{0,4})?$/,\r\n            message: \"只能保留四位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lumpstartdate: [\r\n          {\r\n            required: true,\r\n            type: \"date\",\r\n            validator: validatelumpstartdate,\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lumpenddate: [\r\n          {\r\n            required: true,\r\n            type: \"date\",\r\n            validator: validatelumpenddate,\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        fee: [\r\n          { required: true, type: \"number\", validator: validatorNumber, trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        supplybureauammetercode: [],\r\n        transdistricompany: [],\r\n        voltageClass: [],\r\n      },\r\n      electro: {\r\n        columns: [\r\n          {\r\n            title: \"序号\",\r\n            type: \"index\",\r\n          },\r\n          {\r\n            title: \"用电类型\",\r\n            key: \"typeName\",\r\n          },\r\n\r\n          {\r\n            title: \"所占比例(%)\",\r\n            key: \"ratio\",\r\n            renderHeader: renderHeader,\r\n            render: (h, params) => {\r\n              let that = this;\r\n              let ratio = params.row.ratio;\r\n              let isError1 = params.row.idError1;\r\n              let error = h(\r\n                \"label\",\r\n                {\r\n                  style: {\r\n                    color: \"#ed4014\",\r\n                    fontSize: \"12px\",\r\n                    fontFamily: \"SimSun\",\r\n                    paddingTop: \"6px\",\r\n                    lineHeight: 1,\r\n                    fontWeight: \"bold\",\r\n                    display: null != ratio ? \"none\" : \"inline-block\",\r\n                  },\r\n                },\r\n                \"不能为空\"\r\n              );\r\n              let error1 = h(\r\n                \"label\",\r\n                {\r\n                  style: {\r\n                    color: \"#ed4014\",\r\n                    fontSize: \"12px\",\r\n                    fontFamily: \"SimSun\",\r\n                    paddingTop: \"6px\",\r\n                    lineHeight: 1,\r\n                    fontWeight: \"bold\",\r\n                    display: isError1 == true ? \"inline-block\" : \"none\",\r\n                  },\r\n                },\r\n                \"输入比例不合格要求\"\r\n              );\r\n              let result = h(\"InputNumber\", {\r\n                style: {\r\n                  border: null == ratio || isError1 == true ? \"1px solid #ed4014\" : \"\",\r\n                },\r\n                props: {\r\n                  value: ratio,\r\n                  max: 100,\r\n                  min: 0.1,\r\n                },\r\n                on: {\r\n                  \"on-change\": (v) => {\r\n                    if (v == undefined || v == null) {\r\n                      that.isError = true;\r\n                    } else {\r\n                      that.isError = false;\r\n                    }\r\n                    //给data重新赋值\r\n                    // let reg = /^(?:[1-9]?\\d|100)$/;\r\n                    let reg = /^-?(([1-9][0-9]*)|(([0]\\.\\d{1,2}|[1-9][0-9]*\\.\\d{1,2})))$/;\r\n                    if (v != undefined && v != null && !reg.test(v)) {\r\n                      params.row.idError1 = true;\r\n                      that.isError1 = true;\r\n                    } else {\r\n                      params.row.idError1 = false;\r\n                      that.isError1 = false;\r\n                    }\r\n                    params.row.ratio = v;\r\n                    that.electro.data[params.row._index] = params.row;\r\n                  },\r\n                },\r\n              });\r\n              return h(\"div\", [result, error, error1]);\r\n            },\r\n          },\r\n          {\r\n            title: \"关联局站\",\r\n            key: \"stationName\",\r\n            render: (h, params) => {\r\n              let stationName = params.row.stationName;\r\n              let disabled = params.row._disabled;\r\n              if (disabled != undefined && disabled == true) {\r\n                return h(\"Input\", {\r\n                  props: {\r\n                    value: stationName,\r\n                    readonly: true,\r\n                  },\r\n                });\r\n              } else {\r\n                return h(\"Input\", {\r\n                  props: {\r\n                    value: stationName,\r\n                    icon: \"ios-archive\",\r\n                    placeholder: \"点击图标选择\",\r\n                    readonly: true,\r\n                  },\r\n                  on: {\r\n                    \"on-click\": (v) => {\r\n                      this.chooseResponseCenter(2, params, params.row._index);\r\n                    },\r\n                  },\r\n                });\r\n              }\r\n            },\r\n          },\r\n        ],\r\n        data: [],\r\n      },\r\n      ammeter: {\r\n        id: null,\r\n        country: null,\r\n        company: null,\r\n        countryName: \"\",\r\n        electricTypes: [],\r\n        electro: [],\r\n        classifications: [], //用电类型\r\n      },\r\n      iszgzOnly: false,\r\n      disablediszgz: false,\r\n      electricType: {\r\n        loading: false,\r\n        filter: [\r\n          {\r\n            formItemType: \"input\",\r\n            prop: \"name\",\r\n            label: \"用电类型\",\r\n            width: 100,\r\n            size: \"small\",\r\n          },\r\n        ],\r\n        columns: [\r\n          {\r\n            title: \"序号\",\r\n            type: \"index\",\r\n            align: \"center\",\r\n            width: 70,\r\n          },\r\n          {\r\n            title: \"id\",\r\n            key: \"id\",\r\n            align: \"center\",\r\n            width: 80,\r\n          },\r\n          {\r\n            title: \"用电类型\",\r\n            key: \"typeName\",\r\n            align: \"center\",\r\n            width: 80,\r\n          },\r\n        ],\r\n        data: [],\r\n        total: 0,\r\n        pageSize: 10,\r\n      },\r\n      oldAmmeterId: undefined, //换表原表ID\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    ...mapMutations([\"closeTag\", \"closeTagByName\"]),\r\n    onModalOK(type) {\r\n      console.log(\"@@@@@@@@@@@@\");\r\n      // this.checkStationType = type;\r\n      // if(type == 1){\r\n      //     this.isLoading = 1;\r\n      // }else{\r\n      //     this.isLoading = 0;\r\n      // }\r\n      // if(this.loading == true){\r\n      //     return ;\r\n      // }\r\n      // this.loading = true;\r\n      let flag = false;\r\n      let flag1 = false;\r\n      // this.ammeter.electricTypes = this.electro.data;\r\n      this.$refs.ammeter.validate((valid) => {\r\n        if (valid) {\r\n          console.log(\"1111111111\");\r\n          flag = true;\r\n        }\r\n      });\r\n      this.$refs.ammeter1.validate((valid1) => {\r\n        if (valid1) {\r\n          flag1 = true;\r\n        }\r\n      });\r\n      if (flag && flag1 && !this.isError && !this.isError1) {\r\n        this.checkData(type);\r\n      } else {\r\n        this.$Message.error(\"验证没通过\");\r\n        this.loading = false;\r\n      }\r\n    },\r\n    //验证数据\r\n    checkData(type) {\r\n      let types = this.ammeter.classifications;\r\n      this.ammeter.electrotype = types[types.length - 1];\r\n      let that = this;\r\n      if (\r\n        this.ammeter.status === 1 &&\r\n        (this.configVersion == \"sc\" || this.configVersion == \"SC\")\r\n      ) {\r\n        //在用状态下验证局站地址不能为空\r\n        if (\r\n          this.ammeter.stationaddress == null ||\r\n          this.ammeter.stationaddress == undefined\r\n        ) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"局站地址不能为空，请在局站管理维护该局站信息！\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      if (this.checkStationElec()) {\r\n        //验证用电类型和局站类型是否匹配\r\n        if (this.checkElectricTypeItem()) {\r\n          // if(this.configVersion != \"ln\" && this.configVersion != \"LN\" ) {\r\n          //     checkAmmeterExist(this.ammeter.id, this.ammeter.ammetername, 0).then(res => {//验证电表是否存在\r\n          //         let code = res.data.code;\r\n          //         if (code == 0) {\r\n          //             that.checkedDate(type);\r\n          //         } else {\r\n          //             that.loading = false;\r\n          //         }\r\n          //     });\r\n          // }else{\r\n          that.checkedDate(type);\r\n          // }\r\n        }\r\n      }\r\n    },\r\n    checkStationElec() {\r\n      let electrotype = this.ammeter.electrotype;\r\n      let stationtype = this.ammeter.stationtype;\r\n      if (electrotype === 111 || electrotype === 112 || electrotype === 113) {\r\n        if (stationtype !== 10001) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      } else if (electrotype === 121 || electrotype === 112) {\r\n        if (stationtype !== 10003 && stationtype !== 10004) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      } else if (electrotype === 131 || electrotype === 132 || electrotype === 133) {\r\n        if (stationtype !== 10005) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      } else if (\r\n        electrotype === 1411 ||\r\n        electrotype === 1412 ||\r\n        electrotype === 1421 ||\r\n        electrotype === 1422 ||\r\n        electrotype === 1431 ||\r\n        electrotype === 1432\r\n      ) {\r\n        if (stationtype !== 10002) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      if (this.configVersion == \"sc\" && this.ammeter.stationaddresscode) {\r\n        //“51”开头铁塔站址编码控制\r\n        if (\r\n          [1411, 1412].includes(electrotype) &&\r\n          !this.ammeter.stationaddresscode.startsWith(\"51\")\r\n        ) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站站址编码不匹配(51开头为铁塔站址编码)，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    okModel() {\r\n      //不验证个数\r\n      this.isoldcheckStation = false;\r\n      this.saveData(this.checkStationType); //保存数据\r\n    },\r\n    cancelModel() {\r\n      this.isoldcheckStation = null;\r\n      this.$Modal.warning({ title: \"温馨提示\", content: this.errorMessage });\r\n      this.loading = false;\r\n    },\r\n    checkedDate(type) {\r\n      let that = this;\r\n      checkProjectNameExist(that.oldAmmeterId, that.ammeter.projectname, 0).then(\r\n        (res) => {\r\n          //验证项目名称是否存在\r\n          let code = res.data.code;\r\n          if (code == 0) {\r\n            if (\r\n              that.ammeter.stationcode != undefined &&\r\n              that.ammeter.stationcode != null &&\r\n              (that.ammeter.electrotype == 1411 || that.ammeter.electrotype == 1412)\r\n            ) {\r\n              //判断是否铁塔\r\n              if (that.propertyright == null) {\r\n                //判断是否铁塔\r\n                getstationold(that.ammeter.stationcode).then((res) => {\r\n                  //验证项目名称是否存在\r\n                  that.propertyright = res.data.propertyright;\r\n                  if (that.propertyright != 3) {\r\n                    this.$Modal.warning({\r\n                      title: \"温馨提示\",\r\n                      content: \"用电类型和局站类型或产权不匹配，请确认\",\r\n                    });\r\n                    this.loading = false;\r\n                  } else {\r\n                    that.isCheckStation(type);\r\n                  }\r\n                });\r\n              } else {\r\n                if (that.propertyright != 3) {\r\n                  this.$Modal.warning({\r\n                    title: \"温馨提示\",\r\n                    content: \"用电类型和局站类型或产权不匹配，请确认\",\r\n                  });\r\n                  this.loading = false;\r\n                } else {\r\n                  that.isCheckStation(type);\r\n                }\r\n              }\r\n            } else {\r\n              that.isCheckStation(type);\r\n            }\r\n          }\r\n        }\r\n      );\r\n    },\r\n    isCheckStation(type) {\r\n      let that = this;\r\n      //换表 type = 3\r\n      checkAmmeterByStation({\r\n        id: that.ammeter.id,\r\n        type: 3,\r\n        electrotype: that.ammeter.electrotype,\r\n        stationcode: that.ammeter.stationcode,\r\n        ammeteruse: that.ammeter.ammeteruse,\r\n      }).then((res) => {\r\n        let code = res.data.code;\r\n        if (code == \"error\") {\r\n          this.errorMessage = res.data.msg;\r\n          if (\r\n            that.isoldcheckStation == null &&\r\n            that.ammeter.stationtype == 10002 &&\r\n            res.data.flag5\r\n          ) {\r\n            //编辑数据时判断是否选择关联局站，没有关联弹出是否室分\r\n            that.modal1 = true;\r\n          } else {\r\n            that.$Modal.warning({ title: \"温馨提示\", content: res.data.msg });\r\n            that.loading = false;\r\n          }\r\n        } else {\r\n          that.checkOther(type); //保存数据\r\n        }\r\n      });\r\n    },\r\n    checkOther(type) {\r\n      let that = this;\r\n      // if(that.ammeter.ammetername != undefined || that.ammeter.ammetername != null){\r\n      //     checkAmmeterExist(that.ammeter.id, that.ammeter.ammetername,0).then(res => {//验证电表是否存在\r\n      //         let code = res.data.code;\r\n      //         if (code == 0) {\r\n      //             that.checkedFiles(type);\r\n      //         }else{\r\n      //             that.loading = false;\r\n      //         }\r\n      //     });\r\n      // }else{\r\n      that.checkedFiles(type);\r\n      // }\r\n    },\r\n    checkedFiles(type) {\r\n      let that = this;\r\n      that.ammeter.type = 0;\r\n      if (that.attachData.length != 0) {\r\n        that.ammeter.isAttach = 1;\r\n      } else {\r\n        that.ammeter.isAttach = 0;\r\n      }\r\n      that.saveData(type); //保存数据\r\n      // if (that.attachData.length != 0 && that.multiFiles.length != 0) {\r\n      //     if(that.upload() != false){\r\n      //         that.saveData(type);//保存数据\r\n      //     };\r\n      // }else{\r\n      //     that.saveData(type);//保存数据\r\n      // }\r\n    },\r\n    saveData(type) {\r\n      let that = this;\r\n      this.clearDataByCondition();\r\n      that.ammeter.category = 1; //电表\r\n      addAmmeter(that.ammeter)\r\n        .then((res) => {\r\n          if (res.data != null && res.data != -1 && res.data.success == \"1\") {\r\n            if (type == 1) {\r\n              that.startFlow(res.data);\r\n            } else {\r\n              this.closeTag({ route: this.$route });\r\n              that.warn();\r\n            }\r\n          } else {\r\n            that.loading = false;\r\n            that.$Notice.error({ title: \"提示\", desc: res.data.msg, duration: 10 });\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          that.loading = false;\r\n          console.log(err);\r\n        });\r\n    },\r\n    //根据条件判断数据是否该清除\r\n    clearDataByCondition() {\r\n      if (this.ammeter.property !== 2 && this.ammeter.property !== 4) {\r\n        //站址产权归属为铁塔 清除分割比例checkAmmeterByStation，是否铁塔按RRU包干\r\n        this.ammeter.percent = null;\r\n      }\r\n      if (this.ammeter.ammeteruse !== 3) {\r\n        //电表用途不是回收电费，清除父电表信息\r\n        this.ammeter.parentId = null;\r\n        this.ammeter.customerId = null;\r\n      }\r\n      if (this.ammeter.directsupplyflag != 1) {\r\n        //只有对外结算类型为直供电才填写该字段，转供电不需填写\r\n        this.ammeter.electrovalencenature = null;\r\n      }\r\n      if (!this.isCDCompany) {\r\n        //成都分公司显示合同对方等，不是，就清除数据\r\n        this.ammeter.contractOthPart = null;\r\n        this.ammeter.nmCcode = null;\r\n        this.ammeter.nmL2100 = null;\r\n        this.ammeter.nmL1800 = null;\r\n        this.ammeter.nmCl800m = null;\r\n      }\r\n    },\r\n    warn() {\r\n      this.$Modal.warning({\r\n        title: \"温馨提示\",\r\n        content: \"保存后的数据要提交审批才能生效！\",\r\n      });\r\n    },\r\n    refreshData() {\r\n      this.initData();\r\n    },\r\n    initData() {\r\n      this.countryName = \"\";\r\n      this.electro.data = [];\r\n      this.oldData = [];\r\n      this.isCityAdmin = false;\r\n      this.isAdmin = false;\r\n      this.isEditByCountry = false;\r\n      this.$nextTick(() => {\r\n        this.$refs.ammeter.resetFields(); // this.$refs.adduserform.resetFields();\r\n        this.$refs.ammeter1.resetFields(); // this.$refs.adduserform.resetFields();\r\n        this.$refs.ammeter2.resetFields(); // this.$refs.adduserform.resetFields();\r\n      });\r\n      this.showModel = false;\r\n      this.electricTypeModel = false;\r\n    },\r\n    onModalCancel() {\r\n      this.initData();\r\n    },\r\n    /*初始化*/\r\n    initAmmeter(id) {\r\n      this.oldAmmeterId = id;\r\n      this.initData();\r\n      let that = this;\r\n      if (id != undefined) {\r\n        this.title = \"修改电表\";\r\n        this.isEditByCountry = true;\r\n        //获取上一次修改历史\r\n        editAmmeterRecord({ id: id, type: 1 }).then((res) => {\r\n          if (res.data.id != undefined && res.data.id != null) {\r\n            if (null != res.data.maxdegree) {\r\n              res.data.maxdegree = parseFloat(res.data.maxdegree);\r\n            }\r\n            this.setAmmeter(Object.assign({}, res.data));\r\n            getChangeAmmeterid(\"\", 1).then((res) => {\r\n              that.ammeter.id = res.data.id;\r\n              that.fileParam.busiId = res.data.id;\r\n            });\r\n            this.listElectricTypeRatio(id, res.data.id, res.data.stationcode);\r\n            that.ammeter.id = null;\r\n            that.ammeter.oldAmmeterId = id;\r\n            that.ammeter.ammetername = null;\r\n            that.fileParam.busiId = id;\r\n            getClassificationId(this.ammeter.electrotype).then((res) => {\r\n              this.ammeter.classifications = res.data;\r\n            });\r\n            attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n              that.attachData = Object.assign([], res.data.rows);\r\n            });\r\n          } else {\r\n            editAmmeter(id).then((res1) => {\r\n              if (null != res1.data.maxdegree) {\r\n                res1.data.maxdegree = parseFloat(res1.data.maxdegree);\r\n              }\r\n              this.setAmmeter(res1.data);\r\n              this.listElectricTypeRatio(id, null, res1.data.stationcode);\r\n              that.ammeter.oldAmmeterId = id;\r\n              that.ammeter.ammetername = null;\r\n              that.ammeter.id = null;\r\n              that.fileParam.busiId = id;\r\n              getClassificationId(this.ammeter.electrotype).then((res) => {\r\n                this.ammeter.classifications = res.data;\r\n              });\r\n              attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n                that.attachData = Object.assign([], res.data.rows);\r\n              });\r\n            });\r\n          }\r\n        });\r\n        this.getUser();\r\n      } else {\r\n        this.title = \"添加电表\";\r\n        editAmmeter(\"\", 0).then((res) => {\r\n          this.setAmmeter(Object.assign({}, res.data));\r\n          this.getUser();\r\n        });\r\n      }\r\n      getClassification().then((res) => {\r\n        //用电类型\r\n        this.classificationData = res.data;\r\n      });\r\n    },\r\n    listElectricTypeRatio(id, recordId, stationcode) {\r\n      listElectricTypeRatio({ ammeterId: id, ammeterRecordId: recordId }).then((res) => {\r\n        res.data.rows.forEach((item) => {\r\n          if (item.stationId == null || item.stationId == undefined) {\r\n            item.stationId = null;\r\n            item.stationName = null;\r\n          } else if (item.stationId == stationcode) {\r\n            item._disabled = true;\r\n          }\r\n        });\r\n        this.electro.data = Object.assign([], res.data.rows);\r\n      });\r\n    },\r\n    changeStatus() {\r\n      if (this.ammeter.status == 1) {\r\n        this.isRequireFlag = true;\r\n        this.ruleValidate.stationName = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      } else {\r\n        this.isRequireFlag = false;\r\n        this.ruleValidate.stationName = [\r\n          { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n    },\r\n    selectChange() {\r\n      if (this.ammeter.company != undefined) {\r\n        if (this.ammeter.company == \"1000085\") {\r\n          this.isCDCompany = true;\r\n        } else {\r\n          this.isCDCompany = false;\r\n        }\r\n        getCountryByUserId(this.ammeter.company).then((res) => {\r\n          this.departments = res.data.departments;\r\n          this.ammeter.country = res.data.departments[0].id;\r\n          this.ammeter.countryName = this.departments[0].name;\r\n        });\r\n      }\r\n    },\r\n    getUser() {\r\n      let that = this;\r\n      getUserByUserRole().then((res) => {\r\n        //当前登录用户所在公司\r\n        that.companies = res.data.companies;\r\n        that.isCityAdmin = res.data.isEditAdmin;\r\n        if (\r\n          res.data.isCityAdmin == true ||\r\n          res.data.isProAdmin == true ||\r\n          res.data.isSubAdmin == true\r\n        ) {\r\n          that.isAdmin = true;\r\n        }\r\n        getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {\r\n          //根据权限获取所属部门\r\n          that.departments = res.data;\r\n        });\r\n      });\r\n    },\r\n    setOldData(data) {\r\n      this.oldCategory = btext(\"ammeterCategory\", data.category, \"typeCode\", \"typeName\");\r\n      this.oldPackagetype = btext(\r\n        \"packageType\",\r\n        data.packagetype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldPayperiod = btext(\"payPeriod\", data.payperiod, \"typeCode\", \"typeName\");\r\n      this.oldPaytype = btext(\"payType\", data.paytype, \"typeCode\", \"typeName\");\r\n      this.oldElectronature = btext(\r\n        \"electroNature\",\r\n        data.electronature,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldElectrovalencenature = btext(\r\n        \"electrovalenceNature\",\r\n        data.electrovalencenature,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldElectrotype = btext(\r\n        \"electroType\",\r\n        data.electrotype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldStatus = btext(\"status\", data.status, \"typeCode\", \"typeName\");\r\n      this.oldProperty = btext(\"property\", data.property, \"typeCode\", \"typeName\");\r\n      this.oldAmmetertype = btext(\r\n        \"ammeterType\",\r\n        data.ammetertype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldStationstatus = btext(\r\n        \"stationStatus\",\r\n        data.stationstatus,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldStationtype = btext(\r\n        \"BUR_STAND_TYPE\",\r\n        data.stationtype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldDirectsupplyflag = btext(\r\n        \"directSupplyFlag\",\r\n        data.directsupplyflag,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldAmmeteruse = btext(\"ammeterUse\", data.ammeteruse, \"typeCode\", \"typeName\");\r\n    },\r\n\r\n    setAmmeter(form) {\r\n      //换表初始化数据开始-----\r\n      if (this.configVersion == \"ln\" || this.configVersion == \"LN\") {\r\n        form.supplybureauammetercode = null;\r\n      }\r\n      if (form.category === 1) {\r\n        form.oldAmmeterName = form.ammetername;\r\n      } else {\r\n        form.oldAmmeterName = form.protocolname;\r\n      }\r\n      form.ischangeammeter = \"1\";\r\n      form.oldBillPower = \"\";\r\n      //换表初始化数据结束-----\r\n      if (form.status == null || form.status === 1) {\r\n        form.status = 1;\r\n        this.isRequireFlag = true;\r\n        this.ruleValidate.stationName = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      } else {\r\n        this.isRequireFlag = false;\r\n        this.ruleValidate.stationName = [\r\n          { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n      if (form.electrovalencenature != 1 && form.electrovalencenature != 2) {\r\n        form.electrovalencenature = null;\r\n      }\r\n      // form.price = form.price\r\n      // debugger\r\n      form.issmartammeter = form.issmartammeter == null ? \"0\" : form.issmartammeter + \"\";\r\n      form.isentityammeter =\r\n        form.isentityammeter == null ? null : form.isentityammeter + \"\";\r\n      form.isairconditioning =\r\n        form.isairconditioning == null ? \"0\" : form.isairconditioning + \"\";\r\n      form.islumpsum = form.islumpsum == null ? \"0\" : form.islumpsum + \"\";\r\n      form.iszgz = form.iszgz == null ? \"0\" : form.iszgz + \"\";\r\n      if (form.iszgz == \"1\") this.disablediszgz = true;\r\n      this.ammeter = form;\r\n      let electrotype = this.ammeter.electrotype;\r\n      if (\r\n        electrotype === 111 ||\r\n        electrotype === 112 ||\r\n        electrotype === 113 ||\r\n        electrotype === 2\r\n      ) {\r\n        this.isClassification = true;\r\n      }\r\n      if (\r\n        (electrotype != null && electrotype !== 1411 && electrotype !== 1412) ||\r\n        this.ammeter.property !== 2\r\n      ) {\r\n        this.propertyReadonly = false;\r\n      }\r\n      if (this.ammeter.magnification == null) {\r\n        this.ammeter.magnification = 1;\r\n      }\r\n      if (this.ammeter.company != null) {\r\n        this.ammeter.company = this.ammeter.company + \"\";\r\n        if (this.ammeter.company == \"1000085\") {\r\n          this.isCDCompany = true;\r\n        }\r\n      }\r\n      if (this.ammeter.processinstId != null) {\r\n        this.isShowFlow = true;\r\n      }\r\n      this.flowName = this.ammeter.projectname; //用于提交流程使用原项目名称\r\n      this.showModel = true;\r\n    },\r\n\r\n    //修改电表、协议的用电类型时，如用电类型不再与原先选择的局站的局站类型匹配时，系统自动清空原关联局站，需用户重新再关联局站。\r\n    changeClassifications(value) {\r\n      this.isClassification = false;\r\n      if (value.length == 0) {\r\n        this.clearStation();\r\n        this.ammeter.property = null;\r\n        this.propertyReadonly = true;\r\n      } else {\r\n        this.propertyReadonly = false;\r\n        this.ammeter.electrotype = value[value.length - 1];\r\n        let electrotype = this.ammeter.electrotype;\r\n        if (electrotype === 1411 || electrotype === 1412) {\r\n          //控制产权归属\r\n          this.ammeter.property = 2;\r\n          this.propertyReadonly = true;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else if (electrotype === 1421 || electrotype === 1422) {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = 4;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else if (electrotype === 1431 || electrotype === 1432) {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = 1;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = null;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        }\r\n        // checkClassificationLevel(this.ammeter.electrotype).then(res => {\r\n        //     let code = res.data.msg;\r\n        //     if (code !== '1') {\r\n        let stationtype = this.ammeter.stationtype;\r\n        if (electrotype === 111 || electrotype === 112 || electrotype === 113) {\r\n          this.isClassification = true;\r\n          if (stationtype !== 10001) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 121 || electrotype === 112) {\r\n          if (stationtype !== 10003 && stationtype !== 10004) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 131 || electrotype === 132 || electrotype === 133) {\r\n          if (stationtype !== 10005) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 1411 || electrotype === 1412) {\r\n          if (\r\n            stationtype !== 10002 ||\r\n            (stationtype == 10002 && this.propertyright !== 3)\r\n          ) {\r\n            this.clearStation();\r\n          }\r\n        } else if (\r\n          electrotype === 1421 ||\r\n          electrotype === 1422 ||\r\n          electrotype === 1431 ||\r\n          electrotype === 1432\r\n        ) {\r\n          if (stationtype !== 10002) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 2) {\r\n          this.isClassification = true;\r\n          //     if(stationtype !== 20001){ this.clearStation();}\r\n          // }else if(electrotype === 31 || electrotype === 32 || electrotype === 33){\r\n          //     if(stationtype !== 20002 || stationtype !== -2){ this.clearStation();}\r\n          // }else if(electrotype === 4){\r\n          //     if(stationtype !== -1 || stationtype !== -2){ this.clearStation();}\r\n        }\r\n        //     }\r\n        // });\r\n        if (this.configVersion == \"sc\" && this.ammeter.stationaddresscode) {\r\n          //“51”开头铁塔站址编码控制\r\n          if (\r\n            [1411, 1412].includes(electrotype) &&\r\n            !this.ammeter.stationaddresscode.startsWith(\"51\")\r\n          ) {\r\n            this.clearStation();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    setAttachData(data) {\r\n      this.multiFiles = data.data;\r\n      this.removeIds = data.ids;\r\n      if (this.removeIds.length != 0 && data.type == \"remove\") {\r\n        this.removeAttach();\r\n      } else {\r\n        this.upload();\r\n      }\r\n    },\r\n    upload() {\r\n      if (this.attachData.length != 0 && this.multiFiles.length != 0) {\r\n        // this.$Message.info(\"提示:上传文件过大可能导致上传失败！\");\r\n        this.loading = true;\r\n        axios\r\n          .request({\r\n            url: \"/common/attachments/uploadMultiFile\",\r\n            method: \"post\",\r\n            data: this.multiFiles,\r\n          })\r\n          .then((res) => {\r\n            if (res.data.code != 0) {\r\n              this.loading = false;\r\n            }\r\n            let that = this;\r\n            attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n              that.attachData = Object.assign([], res.data.rows);\r\n            });\r\n          });\r\n      }\r\n    },\r\n    removeAttach() {\r\n      removeAttach({ ids: this.removeIds.join() }).then(() => {});\r\n    },\r\n    clearStation() {\r\n      //清除局站信息\r\n      this.ammeter.stationName = null;\r\n      this.ammeter.stationcode = null;\r\n      this.ammeter.stationstatus = null;\r\n      this.ammeter.stationtype = null;\r\n      this.ammeter.stationaddress = null;\r\n      this.ammeter.stationaddresscode = null;\r\n    },\r\n    //选择所属部门开始\r\n    chooseResponseCenter(index, params, electroRowNum) {\r\n      this.chooseIndex = index;\r\n      this.electroRowNum = electroRowNum;\r\n      if (index == 1 || index == 2) {\r\n        let types = this.ammeter.classifications;\r\n        if (types.length == 0) {\r\n          this.$Modal.warning({ title: \"温馨提示\", content: \"请先选择用电类型！\" });\r\n          return;\r\n        } else if (this.ammeter.ammeteruse == null) {\r\n          this.$Modal.warning({ title: \"温馨提示\", content: \"请先选择电表用途！\" });\r\n          return;\r\n        } else {\r\n          if (this.ammeter.company == null) {\r\n            this.$Message.info(\"请先选择分公司\");\r\n            return;\r\n          }\r\n          this.ammeter.electrotype = types[types.length - 1];\r\n          // if(this.configVersion=='ln' || this.configVersion =='LN'){\r\n          //     this.$refs.stationModalLN.initDataList(this.ammeter.electrotype,0,this.ammeter.ammeteruse,params);//局站\r\n          // }else{\r\n          //换表 type = 3\r\n          this.$refs.stationModal.ammeterid = this.ammeter.id;\r\n          this.$refs.stationModal.initDataList(\r\n            this.ammeter.electrotype,\r\n            3,\r\n            this.ammeter.ammeteruse,\r\n            this.ammeter.company,\r\n            params\r\n          ); //局站\r\n          // }\r\n        }\r\n      } else {\r\n        if (this.ammeter.company == null) {\r\n          this.$Message.info(\"请先选择分公司\");\r\n          return;\r\n        }\r\n        this.$refs.countryModal.choose(this.ammeter.company); //所属部门\r\n      }\r\n    },\r\n    getDataFromModal(data, flag) {\r\n      this.ammeter.country = data.id;\r\n      this.ammeter.countryName = data.name;\r\n      //this.chooseResponseCenter(4, data);\r\n      //选择所属部门结束\r\n    },\r\n    //获取局站数据\r\n    getDataFromStationModal(data, flag) {\r\n      this.ischeckStation = flag;\r\n      this.isoldcheckStation = flag;\r\n      if (this.chooseIndex == 2) {\r\n        this.electro.data[this.electroRowNum].stationId = data.id;\r\n        this.electro.data[this.electroRowNum].stationName = data.stationname;\r\n      } else {\r\n        this.propertyright = data.propertyright;\r\n        this.ammeter.stationName = data.stationname;\r\n        this.ammeter.stationcode = data.id;\r\n        this.ammeter.stationstatus = Number(\r\n          data.status == undefined ? data.STATUS : data.status\r\n        );\r\n        this.ammeter.stationtype = Number(data.stationtype);\r\n        this.ammeter.stationaddress = data.address;\r\n        // if (data.stationtype == 10002 && data.propertyright == 3) {//只有当局站类型为‘生产用房-移动基站’且产权为‘租用’时，存放站址编码\r\n        this.ammeter.stationaddresscode = data.resstationcode;\r\n        // }\r\n        //默认生成一条关联用电类型\r\n        let that = this;\r\n        listElectricType({ id: data.stationtype }).then((res) => {\r\n          let result = that.electro.data;\r\n          let electroData = Object.assign([], res.data.rows);\r\n          let count = 0;\r\n          if (result.length == 0) {\r\n            count++;\r\n          } else {\r\n            result.forEach((item) => {\r\n              electroData.forEach((item1) => {\r\n                if (item.id === item1.id) {\r\n                  electroData[0].stationId = data.id;\r\n                  electroData[0].stationName = data.stationname;\r\n                  electroData[0]._disabled = true;\r\n                  let index = result.indexOf(item);\r\n                  result.splice(index, 1);\r\n                } else {\r\n                  count++;\r\n                }\r\n              });\r\n            });\r\n          }\r\n          if (count > 0) {\r\n            that.electro.data = Object.assign([], res.data.rows);\r\n            that.electro.data[0].stationId = data.id;\r\n            that.electro.data[0].stationName = data.stationname;\r\n            that.electro.data[0]._disabled = true;\r\n          } else {\r\n            result.unshift(electroData[0]);\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    /*添加电表关联用电类型比率*/\r\n    addElectricType() {\r\n      this.$refs.selectElectricType.initElectricType();\r\n    },\r\n\r\n    /*移除选中的用电类型比率*/\r\n    removeElectricType() {\r\n      let rows = this.$refs.ammeterTable.getSelection();\r\n      let datas = this.electro.data;\r\n      rows.forEach((item) => {\r\n        if (item._index != undefined) {\r\n          datas.splice(item._index, 1);\r\n        } else {\r\n          datas.forEach((data) => {\r\n            if (data.id === item.id) {\r\n              let index = datas.indexOf(data);\r\n              datas.splice(index, 1);\r\n            }\r\n          });\r\n        }\r\n      });\r\n      this.electro.data = datas;\r\n    },\r\n\r\n    /* 设置用电类型列表*/\r\n    setElectricData: function (data) {\r\n      let origin = this.electro.data;\r\n      if (origin.length < 1) {\r\n        this.electro.data = data;\r\n      } else {\r\n        let tem = data;\r\n        for (let j = 0; j < origin.length; j++) {\r\n          for (let i = 0; i < data.length; i++) {\r\n            let typeId =\r\n              origin[j].electroTypeId != undefined\r\n                ? origin[j].electroTypeId\r\n                : origin[j].id;\r\n            if (data[i].id === typeId) {\r\n              tem.splice(tem.indexOf(data[i]), 1);\r\n            }\r\n          }\r\n        }\r\n        this.electro.data = this.electro.data.concat(tem);\r\n      }\r\n    },\r\n\r\n    //用电类型比例校验\r\n    checkElectricTypeItem() {\r\n      let items = this.electro.data;\r\n      //当“用电类型”选择“111 A类机楼（机房），112 B类机楼（机房），113 C类机楼（机房） ”或“2 管理办公用电”时，才需填用电类型分比且必填，用电类型比例之和必须等于100%\r\n      if (\r\n        this.ammeter.electrotype === 111 ||\r\n        this.ammeter.electrotype === 112 ||\r\n        this.ammeter.electrotype === 113 ||\r\n        this.ammeter.electrotype === 2\r\n      ) {\r\n        let sumRatio = items.reduce((total, item) => {\r\n          return total + item.ratio;\r\n        }, 0);\r\n        if (sumRatio !== 100) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型所占比例和必须为100%，当前值为\" + sumRatio + \"%\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    showFlow() {\r\n      this.showWorkFlow = true;\r\n      let that = this;\r\n      this.hisParams = {\r\n        busiId: that.ammeter.id,\r\n        busiType: that.ammeter.busiAlias,\r\n        procInstId: that.ammeter.processinstId,\r\n      };\r\n    },\r\n    startFlow(data) {\r\n      let busiTitle = \"电表换表(\" + data.projectname + \")审批\";\r\n      this.workFlowParams = {\r\n        busiId: data.id,\r\n        busiAlias: \"AMM_SWITCH_AMM\",\r\n        busiTitle: busiTitle,\r\n      };\r\n      let that = this;\r\n      setTimeout(function () {\r\n        that.$refs.clwfbtn.onClick();\r\n      }, 200);\r\n    },\r\n    doWorkFlow(data) {\r\n      //流程回调\r\n      this.closeTag({ route: this.$route });\r\n      if (data == 0) {\r\n        this.warn();\r\n      }\r\n    },\r\n    /*选择电表/协议*/\r\n    addAmmeterProtocol() {\r\n      this.$refs.selectAmmeterProtocol.initDataList(1, this.ammeter.id);\r\n    },\r\n    /* 选择电表户号/协议编号*/\r\n    setAmmeterProrocolData: function (data) {\r\n      this.ammeter.parentId = data.id;\r\n      if (data.protocolname != null && data.protocolname.length != 0) {\r\n        this.ammeter.parentCode = data.protocolname;\r\n      } else {\r\n        this.ammeter.parentCode = data.ammetername;\r\n      }\r\n    },\r\n    /*选择客户*/\r\n    addCustomer() {\r\n      this.$refs.customerList.choose(2); //打开模态框\r\n    },\r\n    getDataFromCustomerModal: function (data) {\r\n      this.ammeter.customerId = data.id;\r\n      this.ammeter.customerName = data.name;\r\n    },\r\n    iszgzchange() {\r\n      if (this.ammeter.iszgz == \"1\") {\r\n        this.ammeter.directsupplyflag = 1;\r\n        this.ammeter.oldammetername = this.ammeter.oldAmmeterName;\r\n        this.iszgzOnly = true;\r\n      } else {\r\n        this.iszgzOnly = false;\r\n      }\r\n    },\r\n    chooseoldammetername() {\r\n      if (this.disablediszgz) return;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.status = 0;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.ammeteruse = 1;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.type = 3;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.company = this.ammeter.company;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.country = this.ammeter.country;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.directsupplyflag = 2;\r\n      this.$refs.chooseAmmeterModel.modal.show = true;\r\n      this.$Message.info(\"双击选择！！\");\r\n    },\r\n    getAmmeterModelModal(data) {\r\n      this.ammeter.oldammetername = data.name + \",\" + data.id;\r\n    },\r\n    projectNameChange(val) {\r\n      // var patt=/^([^\\u0000-\\u00ff]+路)([^\\u0000-\\u00ff]*)([0-9]*号)([^\\u0000-\\u00ff]+楼电表)$/;\r\n      if (\r\n        !/^.*([^\\u0000-\\u00ff]+路).*$/.test(val) &&\r\n        !/^.*([^\\u0000-\\u00ff]*)([0-9]*号).*$/.test(val) &&\r\n        !/^.*([^\\u0000-\\u00ff]+楼电表).*$/.test(val)\r\n      ) {\r\n        this.$Message.info(\"温馨提示：集团要求格式为(**路**号**楼电表)\");\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    //直接从前台取\r\n    this.categorys = {\r\n      directsupplyflag: blist(\"directSupplyFlag\"),\r\n    };\r\n    this.propertyList = blist(\"property\");\r\n    this.initAmmeter(this.$route.query.id);\r\n\r\n    this.configVersion = this.$config.version;\r\n    if (this.configVersion != \"ln\" && this.configVersion != \"LN\") {\r\n      this.ruleValidate.ammetername.push({\r\n        required: true,\r\n        message: \"不能为空\",\r\n        trigger: \"blur\",\r\n      });\r\n      this.ruleValidate.customerName = {\r\n        required: true,\r\n        message: \"不能为空\",\r\n        trigger: \"blur\",\r\n      };\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.margin-right-width {\r\n  margin-right: 10px;\r\n}\r\n.testaa .ivu-row {\r\n  margin-left: 5px;\r\n  margin-right: 5px;\r\n}\r\n.testaa .requireStar .ivu-form-item-label:before {\r\n  content: \"*\";\r\n  display: inline-block;\r\n  margin-right: 4px;\r\n  line-height: 1;\r\n  font-family: SimSun;\r\n  font-size: 12px;\r\n  color: #ed4014;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/view/basedata/ammeter"}]}