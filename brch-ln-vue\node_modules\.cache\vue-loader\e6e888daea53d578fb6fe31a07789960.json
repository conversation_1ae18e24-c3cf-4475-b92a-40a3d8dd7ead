{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\assessRanking.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\assessRanking.vue", "mtime": 1754285403029}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["assessRanking.vue"], "names": [], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "assessRanking.vue", "sourceRoot": "src/view/carbon/assess/assessReport/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"assessment-ranking\" id=\"assessment-ranking\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import {  mapState } from \"vuex\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      chartOptions: {\r\n        grid: \r\n        // {\r\n        //   right: this.isCollapse ? '1%' : '2%',\r\n          \r\n        // },\r\n      { \r\n          show: false, \r\n          top: \"20%\", \r\n          left: \"4%\", \r\n          right: this.isCollapse ? '1%' : '2%', \r\n          bottom: \"18%\", \r\n        },\r\n        legend: { \r\n          right: this.isCollapse ? '0.5%' : '10%',\r\n          // data: this.companyarr.result.map((item) => item.name),\r\n          textStyle: {\r\n          fontSize: 12,\r\n          color: \"#fff\",\r\n          fontFamily: \"PingFangSC-Regular\",\r\n          },\r\n          itemWidth: 20,\r\n          itemHeight: 9,\r\n          itemGap: 15,\r\n          top: \"2%\",\r\n          // right: this.chartOptions.legend.right,\r\n          },\r\n      },\r\n      companyarr: {},\r\n      series: [],\r\n    };\r\n  },\r\n  props: {\r\n    dataArr: {\r\n      type: Object,\r\n    },\r\n  },\r\n  // computed: {\r\n  //   ...mapState({isCollapse: state => state.common.isCollapse})\r\n  // },\r\n  watch: {\r\n    isCollapse: {\r\n      immediate: true,\r\n      handler(newValue) {\r\n        this.updateGridRight(newValue);\r\n      },\r\n      deep: true\r\n    },\r\n    dataArr: {\r\n      handler(newVal, oldVal) {\r\n        if (newVal == undefined || newVal == null || newVal == \"\") {\r\n          this.companyarr = JSON.parse(\r\n            JSON.stringify({\r\n              result: [],\r\n              xdata: [],\r\n            })\r\n          );\r\n        } else {\r\n          let myChart = this.$echarts.init(\r\n            document.getElementById(\"assessment-ranking\")\r\n          );\r\n          myChart.setOption({ series: [] }, true);\r\n\r\n          this.companyarr = JSON.parse(JSON.stringify(newVal));\r\n        }\r\n        this.$nextTick(() => {\r\n          this.init();\r\n        });\r\n      },\r\n      deep: true, // 深度监听\r\n    },\r\n  },\r\n  methods: {\r\n    init() {\r\n      let _this = this;\r\n      let myChart = this.$echarts.init(\r\n        document.getElementById(\"assessment-ranking\")\r\n      );\r\n      let diamondData = [];\r\n      diamondData = this.companyarr.result.reduce((pre, cur, index) => {\r\n        pre[index] = cur.data.map(\r\n          (el, id) => el + (pre[index - 1] ? pre[index - 1][id] : 0)\r\n        );\r\n        return pre;\r\n      }, []);\r\n      const color = [\r\n        [\r\n          { offset: 0, color: \"#FF7D41\" },\r\n          { offset: 0.5, color: \"#FF7D41\" },\r\n          { offset: 0.5, color: \"#EE7036\" },\r\n          { offset: 1, color: \"#EE7036\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#FBC658\" },\r\n          { offset: 0.5, color: \"#FBC658\" },\r\n          { offset: 0.5, color: \"#FBBB54\" },\r\n          { offset: 1, color: \"#FBBB54\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#4A99FA\" },\r\n          { offset: 0.5, color: \"#4A99FA\" },\r\n          { offset: 0.5, color: \"#1C7CFA\" },\r\n          { offset: 1, color: \"#1C7CFA\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#25C5FB\" },\r\n          { offset: 0.5, color: \"#25C5FB\" },\r\n          { offset: 0.5, color: \"#0EB1FB\" },\r\n          { offset: 1, color: \"#0EB1FB\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#00A083\" },\r\n          { offset: 0.5, color: \"#00A083\" },\r\n          { offset: 0.5, color: \"#027964\" },\r\n          { offset: 1, color: \"#027964\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#22DEBB\" },\r\n          { offset: 0.5, color: \"#22DEBB\" },\r\n          { offset: 0.5, color: \"#00C6A1\" },\r\n          { offset: 1, color: \"#00C6A1\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#ACFFF0\" },\r\n          { offset: 0.5, color: \"#ACFFF0\" },\r\n          { offset: 0.5, color: \"#7FE3D1\" },\r\n          { offset: 1, color: \"#7FE3D1\" },\r\n        ],\r\n      ];\r\n      let series = [];\r\n      series = this.companyarr.result.reduce((p, c, i, array) => {\r\n        p.push(\r\n          {\r\n            z: i + 1,\r\n            stack: \"总量\",\r\n            type: \"bar\",\r\n            name: c.name,\r\n            barWidth: 25,\r\n            data: c.data,\r\n            itemStyle: {\r\n              color: {\r\n                type: \"linear\",\r\n                x: 0,\r\n                x2: 1,\r\n                y: 0,\r\n                y2: 0,\r\n                colorStops: color[i],\r\n              },\r\n            },\r\n          },\r\n          {\r\n            z: i + 1,\r\n            type: \"pictorialBar\",\r\n            symbolPosition: \"end\",\r\n            symbol: \"diamond\",\r\n            symbolOffset: [0, \"-50%\"],\r\n            symbolSize: [25, 10],\r\n            data: diamondData[i],\r\n            itemStyle: {\r\n              color: {\r\n                type: \"linear\",\r\n                x: 0,\r\n                x2: 1,\r\n                y: 0,\r\n                y2: 0,\r\n                colorStops: color[i],\r\n              },\r\n            },\r\n            tooltip: { show: false },\r\n          }\r\n        );\r\n        // 是否最后一个了？\r\n        if (series.length === array.length * 2) {\r\n          p.push({\r\n            z: 20,\r\n            type: \"pictorialBar\",\r\n            symbolPosition: \"start\",\r\n            data: _this.companyarr.result[0].data,\r\n            symbol: \"diamond\",\r\n            symbolOffset: [\"0%\", \"50%\"],\r\n            symbolSize: [30, 10],\r\n            itemStyle: {\r\n              color: {\r\n                type: \"linear\",\r\n                x: 0,\r\n                x2: 1,\r\n                y: 0,\r\n                y2: 0,\r\n                colorStops: color[0],\r\n              },\r\n            },\r\n            tooltip: { show: false },\r\n          });\r\n          return p;\r\n        }\r\n        return p;\r\n      }, []);\r\n\r\n      // tooltip\r\n\r\n      // legend\r\n      // const legend = this.chartOptions.legend\r\n      // {\r\n      //   data: _this.companyarr.result.map((item) => item.name),\r\n      //   textStyle: {\r\n      //     fontSize: 12,\r\n      //     color: \"#fff\",\r\n      //     fontFamily: \"PingFangSC-Regular\",\r\n      //   },\r\n      //   itemWidth: 20,\r\n      //   itemHeight: 9,\r\n      //   itemGap: 15,\r\n      //   top: \"2%\",\r\n      //   right: this.chartOptions.legend.right,\r\n      // };\r\n\r\n      // grid\r\n      // const grid = this.chartOptions.grid;\r\n      // { top: \"20%\", left: \"4%\", right: this.chartOptions.grid.right, bottom: \"18%\" };\r\n\r\n      // xAxis\r\n      const xAxis = {\r\n        axisTick: { show: true },\r\n        axisLine: { lineStyle: { color: \"rgba(255,255,255, .2)\" } },\r\n        axisLabel: {\r\n          textStyle: {\r\n            fontSize: 12,\r\n            color: \"#fff\",\r\n            fontFamily: \"PingFangSC-Regular\",\r\n          },\r\n\r\n          formatter: function (\r\n            value //X轴的内容\r\n          ) {\r\n            return value.replace(/\\s/, \"\\n\");\r\n          },\r\n        },\r\n        data: this.companyarr.xdata,\r\n      };\r\n      const dataZoom = [\r\n        {\r\n        // 设置滚动条的隐藏与显示\r\n          show: false,\r\n          type: \"slider\", //这个dataZoom组件是slider型dataZoom组件\r\n          xAxisIndex: 0, //dataZoom-slider组件控制第一个XAxis\r\n          start: 0, //左边在10%位置\r\n          end: 60, //右边在60%位置\r\n          zoomLock: true,\r\n          height: \"10\",\r\n          bottom: \"4%\",\r\n          borderColor: \"#1B3149\", //滑动通道的边框颜色\r\n          backgroundColor: \"#275277\",\r\n          showDetail: false, // 即拖拽时候是否显示详细数值信息 默认tru\r\n          handleStyle: {\r\n            borderColor: \"#cbdbfd\",\r\n            shadowBlur: 6,\r\n            shadowOffsetX: 1,\r\n            shadowOffsetY: 1,\r\n            shadowColor: \"#cbdbfd\",\r\n          },\r\n        },\r\n        {\r\n          type: \"inside\", //这个dataZoom组件是inside型dataZoom组件\r\n          xAxisIndex: 0, //dataZoom-inslide组件控制第一个XAxis\r\n          start: 10, //左边在10%的位置\r\n          end: 60, //右边在60%的位置\r\n          // showDetail: false, // 即拖拽时候是否显示详细数值信息 默认tru\r\n          // zoomLock: true,\r\n          // // 滚轮是否触发缩放\r\n          zoomOnMouseWheel: false,\r\n          // 鼠标滚轮触发滚动\r\n          moveOnMouseMove: true,\r\n          moveOnMouseWheel: true,\r\n        },\r\n      ];\r\n      // yAxis\r\n      const yAxis = [\r\n        {\r\n          splitLine: { lineStyle: { color: \"rgba(255,255,255, .05)\" } },\r\n          axisLine: { show: false },\r\n          axisLabel: {\r\n            textStyle: {\r\n              fontSize: 12,\r\n              color: \"#fff\",\r\n              fontFamily: \"PingFangSC-Regular\",\r\n            },\r\n          },\r\n        },\r\n      ];\r\n      let option = {\r\n        tooltip: { trigger: \"axis\" },\r\n        xAxis,\r\n        dataZoom,\r\n        yAxis,\r\n        series,\r\n        grid: this.chartOptions.grid,\r\n        legend: this.chartOptions.legend,\r\n      };\r\n      myChart.setOption(option);\r\n      // 渲染\r\n      window.addEventListener(\"resize\", function () {\r\n        myChart.resize();\r\n      });\r\n    },\r\n    updateGridRight(isCollapse) {\r\n      this.chartOptions.grid.right = isCollapse ? '0%' : '2%';\r\n      this.chartOptions.legend.right = isCollapse ? '0.5%' : '10%';\r\n      \r\n      let myChart = this.$echarts.init(\r\n        document.getElementById(\"assessment-ranking\")\r\n      );\r\n      // 使用setOption更新图表配置\r\n      myChart.setOption({\r\n        grid: this.chartOptions.grid,\r\n        legend: this.chartOptions.legend\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.assessment-ranking {\r\n  // width: 100%;\r\n  // width: 145rem;\r\n  height: 37.8vh;\r\n}\r\n</style>\r\n"]}]}