{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\eslint-loader\\index.js??ref--14-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\changeAmmeter.vue", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\changeAmmeter.vue", "mtime": 1754285403017}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\eslint-loader\\index.js", "mtime": 1753757453575}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfSBmcm9tICIuL2NoYW5nZUFtbWV0ZXIudnVlP3Z1ZSZ0eXBlPXRlbXBsYXRlJmlkPTNlNDkzYzI4JiIKaW1wb3J0IHNjcmlwdCBmcm9tICIuL2NoYW5nZUFtbWV0ZXIudnVlP3Z1ZSZ0eXBlPXNjcmlwdCZsYW5nPWpzJiIKZXhwb3J0ICogZnJvbSAiLi9jaGFuZ2VBbW1ldGVyLnZ1ZT92dWUmdHlwZT1zY3JpcHQmbGFuZz1qcyYiCmltcG9ydCBzdHlsZTAgZnJvbSAiLi9jaGFuZ2VBbW1ldGVyLnZ1ZT92dWUmdHlwZT1zdHlsZSZpbmRleD0wJmlkPTNlNDkzYzI4Jmxhbmc9Y3NzJiIKCgovKiBub3JtYWxpemUgY29tcG9uZW50ICovCmltcG9ydCBub3JtYWxpemVyIGZyb20gIiEuLi8uLi8uLi8uLi9ub2RlX21vZHVsZXMvdnVlLWxvYWRlci9saWIvcnVudGltZS9jb21wb25lbnROb3JtYWxpemVyLmpzIgp2YXIgY29tcG9uZW50ID0gbm9ybWFsaXplcigKICBzY3JpcHQsCiAgcmVuZGVyLAogIHN0YXRpY1JlbmRlckZucywKICBmYWxzZSwKICBudWxsLAogIG51bGwsCiAgbnVsbAogIAopCgovKiBob3QgcmVsb2FkICovCmlmIChtb2R1bGUuaG90KSB7CiAgdmFyIGFwaSA9IHJlcXVpcmUoIkU6XFxjbC1wcm9qZWN0XFxsbi1uZW5naGFvXFxicmNoLWxuLXZ1ZVxcbm9kZV9tb2R1bGVzXFx2dWUtaG90LXJlbG9hZC1hcGlcXGRpc3RcXGluZGV4LmpzIikKICBhcGkuaW5zdGFsbChyZXF1aXJlKCd2dWUnKSkKICBpZiAoYXBpLmNvbXBhdGlibGUpIHsKICAgIG1vZHVsZS5ob3QuYWNjZXB0KCkKICAgIGlmICghYXBpLmlzUmVjb3JkZWQoJzNlNDkzYzI4JykpIHsKICAgICAgYXBpLmNyZWF0ZVJlY29yZCgnM2U0OTNjMjgnLCBjb21wb25lbnQub3B0aW9ucykKICAgIH0gZWxzZSB7CiAgICAgIGFwaS5yZWxvYWQoJzNlNDkzYzI4JywgY29tcG9uZW50Lm9wdGlvbnMpCiAgICB9CiAgICBtb2R1bGUuaG90LmFjY2VwdCgiLi9jaGFuZ2VBbW1ldGVyLnZ1ZT92dWUmdHlwZT10ZW1wbGF0ZSZpZD0zZTQ5M2MyOCYiLCBmdW5jdGlvbiAoKSB7CiAgICAgIGFwaS5yZXJlbmRlcignM2U0OTNjMjgnLCB7CiAgICAgICAgcmVuZGVyOiByZW5kZXIsCiAgICAgICAgc3RhdGljUmVuZGVyRm5zOiBzdGF0aWNSZW5kZXJGbnMKICAgICAgfSkKICAgIH0pCiAgfQp9CmNvbXBvbmVudC5vcHRpb25zLl9fZmlsZSA9ICJzcmMvdmlldy9iYXNlZGF0YS9hbW1ldGVyL2NoYW5nZUFtbWV0ZXIudnVlIgpleHBvcnQgZGVmYXVsdCBjb21wb25lbnQuZXhwb3J0cw=="}]}