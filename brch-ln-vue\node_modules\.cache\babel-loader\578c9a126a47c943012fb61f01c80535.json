{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addHeatAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addHeatAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["addHeatAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoPA,SACA,kBADA,EAEA,WAFA,EAGA,gBAHA,EAIA,yBAJA,EAKA,wBALA,EAMA,2BANA,EAOA,0BAPA,EAQA,aARA,EASA,mBATA,EAUA,oBAVA,EAWA,aAXA,EAYA,sBAZA,EAaA,uBAbA,EAcA,8BAdA,EAeA,sBAfA,EAgBA,4BAhBA,EAiBA,sBAjBA,EAkBA,cAlBA,EAmBA,oBAnBA,EAoBA,YApBA,EAqBA,cArBA,EAsBA,QAtBA,EAuBA,YAvBA,EAwBA,YAxBA,QAyBA,uCAzBA;AA0BA,SACA,eADA,EAEA,iBAFA,EAGA,aAHA,QAIA,0BAJA;AAKA,OAAA,sBAAA,MAAA,6CAAA;AACA,OAAA,WAAA,MAAA,kCAAA;AACA,OAAA,UAAA,MAAA,iCAAA;AACA,SAAA,QAAA,EAAA,UAAA,QAAA,mCAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AACA,OAAA,aAAA,MAAA,iBAAA;AACA,SAAA,oBAAA,QAAA,+BAAA;AACA,OAAA,UAAA,MAAA,uBAAA;AACA,SAAA,aAAA,QAAA,sBAAA;AACA,SAAA,UAAA,QAAA,mDAAA;AACA,OAAA,iBAAA,MAAA,qBAAA;AACA,OAAA,SAAA,MAAA,gBAAA;AACA,OAAA,YAAA,MAAA,sCAAA;AACA,SAAA,WAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,kBAAA,QAAA,2BAAA;AACA,IAAA,KAAA,GAAA,QAAA,EAAA;AACA,eAAA;AACA,EAAA,IAAA,EAAA,gBADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,UAAA,EAAA,UAAA;AAAA,IAAA,WAAA,EAAA,WAAA;AAAA,IAAA,sBAAA,EAAA,sBAAA;AAAA,IAAA,iBAAA,EAAA,iBAAA;AAAA,IAAA,aAAA,EAAA,aAAA;AAAA,IAAA,UAAA,EAAA,UAAA;AAAA,IAAA,YAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AAAA;;AACA,QAAA,KAAA,GAAA,SAAA,KAAA,CAAA,CAAA,QAAA;AAAA,UAAA,GAAA,QAAA,GAAA;AAAA,UAAA,KAAA,QAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA;AACA,UAAA,GAAA,GAAA,EAAA;;AACA,UAAA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,IAAA,IAAA,EAAA;AACA,QAAA,GAAA,GAAA,MAAA;AACA;;AACA,aAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,GAAA,EAAA;AACA,QAAA,EAAA,EAAA;AACA,UAAA,KADA,mBACA;AACA;AACA,gBAAA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,IAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,GAAA;AACA;AACA;AANA;AADA,OAAA,EASA,GATA,CAAA,CAAA,CAAA;AAUA,KAhBA;;AAiBA,WAAA;AACA,MAAA,MAAA,EAAA,EADA;AAEA,MAAA,OAAA,EAAA,EAFA;AAGA,MAAA,cAAA,EAAA,KAHA;AAIA,MAAA,WAAA,EAAA,KAJA;AAKA,MAAA,cAAA,EAAA,KALA;AAMA,MAAA,aAAA,EAAA,UANA;AAOA,MAAA,OAAA,EAAA,EAPA;AAQA,MAAA,QAAA,EAAA,KARA;AASA,MAAA,UAAA,EAAA,IATA;AASA;AACA,MAAA,SAAA,EAAA,CAAA,CAVA;AAUA;AACA,MAAA,YAAA,EAAA,CAAA,CAXA;AAWA;AACA,MAAA,OAAA,EAAA,EAZA;AAYA;AACA,MAAA,aAAA,EAAA,EAbA;AAcA,MAAA,WAAA,EAAA,EAdA;AAeA,MAAA,eAAA,EAAA,EAfA;AAgBA,MAAA,gBAAA,EAAA,EAhBA;AAiBA,MAAA,cAAA,EAAA,EAjBA;AAkBA,MAAA,aAAA,EAAA,EAlBA;AAmBA,MAAA,cAAA,EAAA,EAnBA;AAoBA,MAAA,WAAA,EAAA,EApBA;AAqBA,MAAA,cAAA,EAAA,EArBA;AAsBA,MAAA,QAAA,EAAA,KAtBA;AAsBA;AACA,MAAA,SAAA,EAAA,EAvBA;AAuBA;AACA,MAAA,UAAA,EAAA,EAxBA;AAyBA,MAAA,aAAA,EAAA,EAzBA;AA0BA,MAAA,SAAA,EAAA,EA1BA;AA2BA,MAAA,SAAA,EAAA,EA3BA;AA4BA,MAAA,YAAA,EAAA,EA5BA;AA6BA,MAAA,WAAA,EAAA,EA7BA;AA8BA,MAAA,OAAA,EAAA,KA9BA;AA+BA,MAAA,OAAA,EAAA,IA/BA;AA+BA;AACA,MAAA,OAAA,EAAA,IAhCA;AAgCA;AACA,MAAA,WAAA,EAAA,IAjCA;AAiCA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IADA;AACA;AACA,QAAA,OAAA,EAAA,EAFA;AAEA;AACA,QAAA,OAAA,EAAA,EAHA;AAGA;AACA,QAAA,WAAA,EAAA,IAJA;AAIA;AACA,QAAA,WAAA,EAAA;AALA,OAlCA;AAyCA,MAAA,SAAA,EAAA;AACA,QAAA,OAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAFA,EAQA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SARA,EAcA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,SAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAdA,EAoBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SApBA,EA0BA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,IAAA,EAAA,cAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA1BA,EAgCA;AACA,UAAA,KAAA,EAAA,UADA;AAEA;AACA,UAAA,GAAA,EAAA,YAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAhCA,EAuCA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA;AACA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAvCA,EA8CA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,kBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA9CA,EAoDA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SApDA,EA0DA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,GAAA,EAAA,gBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA1DA,EAgEA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,IAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAhEA,EAsEA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAtEA,EA4EA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA5EA,EAkFA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAlFA,EAwFA;AACA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAzFA,CAHA;AA8FA,QAAA,IAAA,EAAA;AA9FA,OAzCA;AAyIA,MAAA,SAAA,EAAA,CAzIA;AA0IA,MAAA,OAAA,EAAA,CA1IA;AA2IA,MAAA,QAAA,EAAA,EA3IA,CA2IA;;AA3IA,KAAA;AA6IA,GAlKA;AAmKA,EAAA,OAAA,EAAA;AACA,IAAA,YADA,0BACA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,YAAA,IAAA,CAAA,UAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,CAAA,CAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,IAAA;AACA,SAHA,MAGA;AACA,UAAA,kBAAA,CAAA,IAAA,CAAA,UAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA;AACA;AACA,WALA;AAMA;AACA;AACA,KAhBA;AAiBA;AACA,IAAA,oBAlBA,kCAkBA;AACA,UAAA,KAAA,UAAA,CAAA,OAAA,IAAA,IAAA,IAAA,KAAA,UAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AAAA;AACA;;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAJA,CAIA;AACA,KAvBA;AAwBA,IAAA,gBAxBA,4BAwBA,IAxBA,EAwBA;AACA,WAAA,UAAA,CAAA,OAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,UAAA,CAAA,WAAA,GAAA,IAAA,CAAA,IAAA,CAFA,CAGA;AACA,KA5BA;AA6BA,IAAA,WA7BA,yBA6BA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,WAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,EAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA;;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,IAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA;;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,MAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA;;AAEA,QAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,kBAAA;AACA,OAtBA;AAuBA,KAtDA;AAuDA,IAAA,UAvDA,wBAuDA;AACA,UAAA,KAAA,UAAA,CAAA,WAAA,IAAA,EAAA,EAAA;AACA,aAAA,UAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,WAAA,OAAA,GAAA,CAAA;AACA,WAAA,kBAAA;AACA,KA7DA;AA8DA,IAAA,eA9DA,6BA8DA;AACA,WAAA,UAAA;AACA,KAhEA;AAiEA;AACA,IAAA,QAlEA,sBAkEA;AACA,UAAA,KAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,KAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,CAAA,GAAA,IAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA;;AACA,UAAA,CAAA,EAAA;AACA,aAAA,UAAA,CAAA,KAAA;AACA,OAFA,MAEA;AACA,aAAA,SAAA,CAAA,SAAA;AACA;AACA,KA/EA;AAgFA,IAAA,YAhFA,wBAgFA,SAhFA,EAgFA;AACA,UAAA,IAAA,GAAA,EAAA;AACA,WAAA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA;AACA,QAAA,SAAA,CAAA,GAAA,CAAA,UAAA,KAAA,EAAA;AACA,cAAA,KAAA,IAAA,KAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,SAJA;AAKA,OANA;AAOA,WAAA,MAAA,GAAA,IAAA;AACA,KA1FA;AA4FA;AACA,IAAA,UA7FA,sBA6FA,IA7FA,EA6FA;AAAA;;AACA,UAAA,CAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,GAAA,CAAA;AACA,YAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,YAAA,SAAA,GAAA,KAAA,UAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA;AACA,cAAA,IAAA,CAAA,EAAA,IAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,SAAA;AACA;;AACA,UAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,UAAA,UAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,MAAA;AACA,SARA;AASA,QAAA,IAAA,CAAA,GAAA,GAAA,CAAA;;AACA,YAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,GAAA;AACA;;AACA,YAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,eAAA,CAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,aAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,MADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;;AAKA,cAAA,MAAA,CAAA,kBAAA;AACA;AACA,WATA;AAUA;AACA;AACA,KA/HA;AAgIA,IAAA,iBAhIA,+BAgIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAA,WAAA,GAAA,IAAA,IAAA,EAAA;AACA,UAAA,WAAA,GAAA,WAAA,CAAA,WAAA,EAAA;AACA,UAAA,YAAA,GAAA,WAAA,CAAA,QAAA,KAAA,CAAA;;AACA,UAAA,QAAA,KAAA,SAAA,CAAA,IAAA,EAAA;AACA,aAAA,SAAA,CAAA,IAAA,GAAA,EAAA;AACA;;AACA,WAAA,SAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AACA;AACA,QAAA,SAAA,EAAA,KAAA,UAAA,CAAA,SAAA,IAAA,CAAA,CAAA,IAAA,KAAA,UAAA,CAAA,SAAA,IAAA,SAAA,GAAA,WAAA,GAAA,EAAA,GAAA,YAAA,GAAA,KAAA,UAAA,CAAA,SAFA;AAGA;AACA,QAAA,SAAA,EAAA,KAAA,UAAA,CAAA,SAAA,IAAA,CAAA,CAAA,IAAA,KAAA,UAAA,CAAA,SAAA,IAAA,SAAA,GAEA,WAAA,GAAA,GAAA,GAAA,YAAA,GAAA,GAAA,GAAA,IAFA,GAIA,KAAA,UAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,IAAA,GAAA,GAAA,KAAA,UAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,GAAA,GAAA,IARA;AASA,QAAA,OAAA,EAAA,KAAA,UAAA,CAAA,SAAA,IAAA,CAAA,CAAA,IAAA,KAAA,UAAA,CAAA,SAAA,IAAA,SAAA,GAEA,WAAA,GAAA,GAAA,GAAA,YAAA,GAAA,GAAA,GACA,IAAA,IAAA,CAAA,WAAA,EAAA,YAAA,EAAA,CAAA,EAAA,OAAA,EAHA,GAKA,KAAA,UAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,IAAA,GAAA,GAAA,KAAA,UAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,GAAA,GACA,IAAA,IAAA,CAAA,KAAA,UAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,KAAA,UAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,OAAA,EAfA;AAgBA,QAAA,WAAA,EAAA,EAhBA;AAiBA,QAAA,YAAA,EAAA,GAjBA;AAkBA,QAAA,UAAA,EAAA,GAlBA;AAmBA,QAAA,SAAA,EAAA,GAnBA;AAoBA,QAAA,gBAAA,EAAA,EApBA;AAqBA,QAAA,WAAA,EAAA,GArBA;AAsBA,QAAA,cAAA,EAAA,GAtBA;AAuBA,QAAA,WAAA,EAAA,EAvBA;AAwBA,QAAA,SAAA,EAAA,GAxBA;AAyBA,QAAA,QAAA,EAAA,GAzBA;AA0BA,QAAA,SAAA,EAAA,GA1BA;AA2BA,QAAA,MAAA,EAAA;AA3BA,OAAA;AA6BA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,SAAA,EAAA,QADA;AAEA,QAAA,OAAA,EAAA,QAFA;AAGA;AACA,QAAA,WAAA,EAAA,QAJA;AAKA,QAAA,YAAA,EAAA,QALA;AAMA,QAAA,UAAA,EAAA,QANA;AAQA,QAAA,gBAAA,EAAA,QARA;AASA,QAAA,WAAA,EAAA,QATA;AAUA,QAAA,cAAA,EAAA,QAVA;AAWA,QAAA,WAAA,EAAA,QAXA;AAYA,QAAA,SAAA,EAAA,QAZA;AAaA,QAAA,QAAA,EAAA,QAbA;AAcA,QAAA,SAAA,EAAA,QAdA;AAeA,QAAA,SAAA,EAAA,QAfA;AAgBA,QAAA,MAAA,EAAA;AAhBA,OAAA;AAmBA,KApMA;AAqMA;AACA,IAAA,SAtMA,qBAsMA,GAtMA,EAsMA;AACA,WAAA,OAAA,CAAA,KAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,GAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA;AAKA,KA5MA;AA6MA,IAAA,UA7MA,sBA6MA,KA7MA,EA6MA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAEA;AARA,SAAA;AAUA;;AAEA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KAtOA;AAuOA,IAAA,cAvOA,0BAuOA,KAvOA,EAuOA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAEA;AARA,SAAA;AAUA;;AAEA,WAAA,QAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KAhQA;AAiQA;AACA,IAAA,kBAlQA,gCAkQA;AAAA;;AACA,UAAA,QAAA,GAAA,KAAA,UAAA;AACA,MAAA,QAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA,MAAA,QAAA,CAAA,QAAA,GAAA,KAAA,QAAA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,6BADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,WAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,EAAA;AACA,cAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,WAFA,EAFA,CAKA;;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,IAAA,CAAA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,SAAA,CAAA,IAAA,CAAA,MAAA;;AAEA,UAAA,MAAA,CAAA,SAAA,GAAA,CAAA,CAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,CAAA,CAAA;AACA;AACA,OAfA,EAeA,KAfA,CAeA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OAjBA;AAkBA,KA9RA;AA+RA;AACA,IAAA,aAhSA,2BAgSA;AACA,WAAA,UAAA,GAAA;AACA,QAAA,SAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,KAAA,OAFA;AAGA,QAAA,WAAA,EAAA,IAHA;AAIA,QAAA,OAAA,EAAA,MAAA,CAAA,KAAA,OAAA;AAJA,OAAA;AAMA,WAAA,kBAAA;AACA,KAxSA;AAySA,IAAA,MAzSA,oBAySA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,WAAA;AACA;AACA;;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,oBAFA;AAGA,QAAA,IAAA,EAAA,gBAAA;AACA,cAAA,CAAA,GAAA,IAAA;AACA,cAAA,GAAA,GAAA,EAAA;AACA,cAAA,KAAA,GAAA,MAAA,CAAA,SAAA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,IAAA,GAAA,IAAA,CAAA,CAAA,CAAA;;AACA,gBAAA,IAAA,CAAA,EAAA,IAAA,IAAA,IAAA,IAAA,CAAA,EAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,kBAAA,IAAA,CAAA,OAAA,EAAA;AACA,gBAAA,CAAA,GAAA,KAAA;AACA;;AACA,cAAA,GAAA,IAAA,IAAA,CAAA,EAAA,GAAA,GAAA;AACA;AACA;;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,KAAA;;AACA,cAAA,CAAA,EAAA;AACA,gBAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,cAAA,iBAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,oBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,kBAAA,MAAA,CAAA,kBAAA;AACA;AACA,eALA;AAMA;AACA,WATA,MASA;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,4BAAA;AACA;AACA,SA7BA;AA8BA,QAAA,QAAA,EAAA,oBAAA,CACA;AA/BA,OAAA;AAiCA,KAhVA;AAiVA,IAAA,mBAjVA,+BAiVA,IAjVA,EAiVA;AACA,UAAA,IAAA,KAAA,SAAA,EAAA;AACA,aAAA,eAAA;AACA,OAFA,MAEA,IAAA,IAAA,KAAA,KAAA,EAAA;AACA,aAAA,kBAAA;AACA;AACA,KAvVA;AAwVA;AACA,IAAA,kBAzVA,gCAyVA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,MAAA,aAAA,CAAA,KAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,aAAA;AACA,SAFA,MAEA;AACA,cAAA,GAAA,GAAA,EAAA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA;;AACA,UAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,EAAA,EAAA,EAAA,MAAA,CAAA,UAAA,CAAA,OAAA;AACA;AACA,OAZA;AAaA,KAzWA;AA0WA,IAAA,eA1WA,6BA0WA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,CAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,cAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SARA;;AASA,YAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,EAAA,EAAA,EAAA,KAAA,UAAA,CAAA,OAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,0BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,+BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,2BAAA;AACA;AACA;AACA,KApYA;AAqYA,IAAA,qBArYA,mCAqYA;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,WAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAAA,CAAA;AACA,KAvYA;AAwYA,IAAA,SAxYA,uBAwYA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,gBAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,MAAA,GAAA,IAAA,CAAA,MAAA;;AACA,cAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,KAAA;AACA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,EAAA,GAAA,GAAA;AACA,SANA;;AAOA,YAAA,CAAA,EAAA;AACA,UAAA,aAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,SADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;;AAKA,cAAA,MAAA,CAAA,kBAAA;AACA;AACA,WATA;AAUA,SAXA,MAWA;AACA,eAAA,SAAA,CAAA,mBAAA;AACA;AACA;AACA,KAraA;AAsaA,IAAA,OAtaA,qBAsaA;AACA,UAAA,GAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,GAAA,CAAA,kBAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA,KA3aA;AA4aA,IAAA,mBA5aA,iCA4aA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,cAAA;AACA,MAAA,IAAA,CAAA,gBAAA,GAAA,GAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,uBAAA;;AACA,UAAA,IAAA,CAAA,gBAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,SAAA,GAAA,CAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,OAPA,MAOA,IAAA,IAAA,CAAA,gBAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,SAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,CAAA;;AACA,YAAA,UAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,UAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,CAAA,CALA,CAMA;;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA;AACA,KAjcA;AAkcA,IAAA,UAlcA,wBAkcA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,WAAA;AACA,MAAA,IAAA,CAAA,WAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KAxcA;AAycA,IAAA,QAzcA,sBAycA;AACA,UAAA,KAAA,YAAA,IAAA,CAAA,EAAA;AACA,YAAA,GAAA,GAAA,KAAA,YAAA,CAAA,KAAA,YAAA,EAAA,IAAA;;AACA,YAAA,GAAA,EAAA;AACA,kBAAA,KAAA,YAAA;AACA,iBAAA,CAAA;AACA,mBAAA,iBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,eAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,oBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,oBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,iBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,wBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,mBAAA;AACA;AACA;AACA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,kBAAA;AACA;AA3BA;AA6BA;AACA;AACA,KA5eA;AA6eA;AACA,IAAA,iBA9eA,+BA8eA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,aAAA;AACA,UAAA,MAAA,GAAA,GAAA,CAAA,CAAA,CAAA,IAAA,GAAA,IAAA,GAAA,CAAA,CAAA,CAAA,IAAA,GAAA,IAAA,GAAA,CAAA,CAAA,CAAA,IAAA,GAAA,IAAA,GAAA,CAAA,CAAA,CAAA,IAAA,GAAA;;AACA,UAAA,CAAA,MAAA,EAAA;AACA,aAAA,SAAA,CAAA,YAAA;AACA,QAAA,GAAA,GAAA,EAAA;AACA,OAPA,CAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,UAAA,IAAA,CAAA,SAAA,IAAA,EAAA,IAAA,IAAA,CAAA,OAAA,IAAA,EAAA,EAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,YAAA,GAAA,SAAA,GAAA,EAAA,GAAA,GAAA,GAAA,GAAA,GAAA,OAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,UAAA,EAAA,iBAAA;AACA;;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CA7BA,CA8BA;AAEA,KA9gBA;AA+gBA;AACA,IAAA,eAhhBA,6BAghBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,WAAA;AACA,UAAA,MAAA,GAAA,GAAA,CAAA,CAAA,CAAA,IAAA,GAAA,IAAA,GAAA,CAAA,CAAA,CAAA,IAAA,GAAA,IAAA,GAAA,CAAA,CAAA,CAAA,IAAA,GAAA,IAAA,GAAA,CAAA,CAAA,CAAA,IAAA,GAAA;;AACA,UAAA,CAAA,MAAA,EAAA;AACA,aAAA,SAAA,CAAA,YAAA;AACA,QAAA,GAAA,GAAA,EAAA;AACA;;AACA,UAAA,IAAA,CAAA,SAAA,IAAA,EAAA,IAAA,IAAA,CAAA,OAAA,IAAA,EAAA,EAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,YAAA,GAAA,SAAA,GAAA,EAAA,GAAA,GAAA,GAAA,GAAA,GAAA,OAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,UAAA,EAAA,iBAAA;AACA,OAZA,CAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;;;AACA,MAAA,IAAA,CAAA,OAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CA3BA,CA4BA;AACA,KA7iBA;AA8iBA,IAAA,oBA9iBA,kCA8iBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,gBAAA;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,GAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,YAAA,EAAA,mBAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,uBAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,qBAAA;;AACA,UAAA,IAAA,CAAA,SAAA,IAAA,EAAA,IAAA,IAAA,CAAA,OAAA,IAAA,EAAA,EAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,WAAA;AAEA,QAAA,IAAA,CAAA,UAAA,GAAA,CAAA,GAAA,GAAA,SAAA,GAAA,EAAA,GAAA,EAAA,GAAA,GAAA,GAAA,GAAA,GAAA,OAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AACA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,uBAAA,EAdA,CAeA;;AACA,UAAA,IAAA,CAAA,gBAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,SAAA,GAAA,CAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,OAPA,MAOA,IAAA,IAAA,CAAA,gBAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,SAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,CAAA;;AACA,YAAA,WAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,WAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,OA9BA,CA+BA;AACA;AACA;AACA;;AACA,KAjlBA;AAklBA,IAAA,wBAllBA,sCAklBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,cAAA;AACA,MAAA,IAAA,CAAA,gBAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KAvlBA;AAwlBA,IAAA,mBAxlBA,iCAwlBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,WAAA;AACA,MAAA,IAAA,CAAA,WAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KA7lBA;AA8lBA,IAAA,oBA9lBA,kCA8lBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,gBAAA;;AACA,UAAA,MAAA,GAAA,oBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;;AACA,UAAA,MAAA,EAAA;AAAA;AACA,aAAA,SAAA,CAAA,MAAA;AACA,OAFA,MAEA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA;AACA,KAxmBA;AAymBA,IAAA,iBAzmBA,+BAymBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,aAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,uBAAA,EAJA,CAKA;;AACA,UAAA,IAAA,CAAA,gBAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,SAAA,GAAA,CAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,OAPA,MAOA,IAAA,IAAA,CAAA,gBAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,SAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,CAAA;;AACA,YAAA,WAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,WAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA;;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KA/nBA;AAgoBA,IAAA,kBAhoBA,gCAgoBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,cAAA;;AACA,UAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,QAAA;AACA;;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,UAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CATA,CAUA;AACA,KA3oBA;AA4oBA,IAAA,SA5oBA,uBA4oBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,UAAA;AACA,MAAA,IAAA,CAAA,MAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KAjpBA;AAkpBA,IAAA,cAlpBA,4BAkpBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,eAAA;AACA,MAAA,IAAA,CAAA,WAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KAvpBA;AAwpBA,IAAA,UAxpBA,sBAwpBA,MAxpBA,EAwpBA;AACA,WAAA,OAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA;AACA,UAAA,SAAA,EAAA,QADA;AAEA,UAAA,OAAA,EAAA,QAFA;AAGA,UAAA,WAAA,EAAA,QAHA;AAIA,UAAA,YAAA,EAAA,QAJA;AAKA,UAAA,UAAA,EAAA,QALA;AAMA,UAAA,cAAA,EAAA,QANA;AAOA,UAAA,MAAA,EAAA;AAPA,SAAA;AASA;AACA,KArqBA;AAsqBA;AACA,IAAA,UAvqBA,sBAuqBA,GAvqBA,EAuqBA,KAvqBA,EAuqBA,OAvqBA,EAuqBA,GAvqBA,EAuqBA;AACA,WAAA,aAAA,GAAA,GAAA,CAAA,SAAA;AACA,WAAA,WAAA,GAAA,GAAA,CAAA,OAAA;AACA,WAAA,eAAA,GAAA,GAAA,CAAA,WAAA;AACA,WAAA,gBAAA,GAAA,GAAA,CAAA,YAAA;AACA,WAAA,cAAA,GAAA,GAAA,CAAA,UAAA;AACA,WAAA,aAAA,GAAA,GAAA,CAAA,SAAA;AACA,WAAA,cAAA,GAAA,GAAA,CAAA,gBAAA;AACA,WAAA,WAAA,GAAA,GAAA,CAAA,WAAA;AACA,WAAA,cAAA,GAAA,GAAA,CAAA,QAAA;AACA,WAAA,UAAA,GAAA,GAAA,CAAA,MAAA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,WAAA,YAAA,GAAA,OAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,CAAA,CAAA,KAAA,CAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAIA,KAzrBA;AA0rBA;AACA,IAAA,QA3rBA,oBA2rBA,IA3rBA,EA2rBA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,YAAA;AACA,UAAA,GAAA,GAAA,EAAA;;AACA,UAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAA,KAAA,GAAA,CAAA;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OAHA,MAGA,IAAA,KAAA,GAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,EAAA;AACA;AACA,YAAA,KAAA,IAAA,IAAA,CAAA,QAAA,GAAA,CAAA,IAAA,KAAA,IAAA,IAAA,CAAA,SAAA,GAAA,CAAA,EAAA;AACA,UAAA,KAAA,GAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,KAAA;AACA;;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OARA,MAQA;AACA,QAAA,OAAA,IAAA,CAAA;AACA;;AACA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,MAAA,GAAA,GAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,KAAA,CAAA;;AACA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,GAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,OAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,GAAA,CAAA,WAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,GAAA,CAAA,YAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,GAAA,CAAA,UAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,GAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,GAAA,CAAA,gBAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,WAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,GAAA,CAAA,QAAA;AACA,QAAA,IAAA,CAAA,UAAA,GAAA,GAAA,CAAA,MAAA;AACA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA,KAhuBA;AAiuBA;AACA,IAAA,YAluBA,wBAkuBA,MAluBA,EAkuBA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,cAAA,MAAA;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,WAAA;AACA,UAAA,IAAA,GAAA,KAAA,aAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,SAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,aAAA;AACA,UAAA,IAAA,GAAA,KAAA,eAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,cAAA;AACA,UAAA,IAAA,GAAA,KAAA,gBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,YAAA;AACA,UAAA,IAAA,GAAA,KAAA,cAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,WAAA;AACA,UAAA,IAAA,GAAA,KAAA,aAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,gBAAA;AACA,UAAA,IAAA,GAAA,KAAA,gBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,aAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,UAAA;AACA,UAAA,IAAA,GAAA,KAAA,cAAA;AACA;;AACA,aAAA,EAAA;AACA,UAAA,GAAA,GAAA,QAAA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA;AACA;AAxCA;;AA0CA,aAAA;AAAA,QAAA,GAAA,EAAA,GAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA;AACA,KAhxBA;AAixBA,IAAA,IAjxBA,kBAixBA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,YAAA;;AACA,UAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAA,KAAA,GAAA,CAAA;AACA,QAAA,OAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OARA,MAQA;AACA,QAAA,IAAA,CAAA,QAAA;AACA,QAAA,IAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA,KAlyBA;AAmyBA,IAAA,QAnyBA,oBAmyBA,KAnyBA,EAmyBA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,EAAA;;AACA,UAAA,KAAA,CAAA,MAAA,GAAA,GAAA,EAAA;AACA,eAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,GAAA,IAAA,KAAA;AACA;;AACA,aAAA,KAAA;AACA,KAzyBA;AA0yBA,IAAA,cA1yBA,0BA0yBA,KA1yBA,EA0yBA,IA1yBA,EA0yBA;AACA,WAAA,QAAA,CAAA,IAAA,CAAA;AACA,QAAA,OAAA,EAAA,IAAA,CAAA,IAAA,GAAA;AADA,OAAA;AAGA,KA9yBA;AA+yBA,IAAA,mBA/yBA,iCA+yBA,CAEA,CAjzBA;AAkzBA,IAAA,iBAlzBA,6BAkzBA,IAlzBA,EAkzBA;AACA,WAAA,SAAA,CACA,IAAA,CAAA,IAAA,GAAA,gCADA;AAGA,KAtzBA;AAuzBA;AACA,IAAA,aAxzBA,yBAwzBA,IAxzBA,EAwzBA;AAAA;;AACA,UAAA,CAAA,IAAA,EAAA;AACA,aAAA,OAAA,CAAA,KAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,YAFA;AAGA,UAAA,QAAA,EAAA;AAHA,SAAA;AAKA;AACA;;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CATA,CASA;;AACA,UAAA,cAAA,GAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAVA,CAUA;;AACA,UAAA,UAAA,GAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA,QAAA,GAAA,CAAA,EAAA,cAAA,CAAA,CAXA,CAWA;;AACA,UAAA,SAAA,UAAA,IAAA,UAAA,UAAA,EAAA;AACA,aAAA,OAAA,CAAA,KAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,IAAA,CAAA,IAAA,GAAA,gCAFA;AAGA,UAAA,QAAA,EAAA;AAHA,SAAA;AAKA;AACA;;AACA,UAAA,KAAA,GAAA,EAAA;AACA,UAAA,KAAA,GAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA;AACA,QAAA,GAAA,EAAA,+BADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAA,EAAA,KAAA;AAHA,OAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA;AACA,UAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,YAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GADA;AAEA,YAAA,QAAA,EAAA,CAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA;AAMA,UAAA,IAAA,CAAA,IAAA,GAAA,KAAA;AACA;;AACA,QAAA,MAAA,CAAA,kBAAA;AACA,OAhBA,EAgBA,KAhBA,CAgBA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,KAAA;AACA,QAAA,IAAA,CAAA,IAAA,GAAA,KAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OApBA;AAqBA,WAAA,kBAAA;AACA,aAAA,KAAA;AACA,KAv2BA;AAy2BA;AACA,IAAA,YA12BA,0BA02BA;AAAA;;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,sCADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,YAAA,EAAA;AAHA,OAAA;AAKA,MAAA,KAAA,CAAA,IAAA,CAAA,GAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,QAAA,GAAA,KAAA;AACA,YAAA,OAAA,GAAA,GAAA;AACA,YAAA,IAAA,GAAA,IAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AACA,YAAA,QAAA,GAAA,eAAA;;AACA,YAAA,cAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA,EAAA;AACA;AACA,cAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA,QAAA;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,UAAA,KAAA,CAAA,IAAA,GAAA,GAAA,CAAA,eAAA,CAAA,IAAA,CAAA;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,UAAA,KAAA,CAAA,KAAA;AACA,UAAA,GAAA,CAAA,eAAA,CAAA,KAAA,CAAA,IAAA,EARA,CAQA;;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,SAVA,MAUA;AACA;AACA,UAAA,SAAA,CAAA,UAAA,CAAA,IAAA,EAAA,QAAA;AACA;AACA,OApBA;AAqBA;AAr4BA,GAnKA;AA0iCA,EAAA,OA1iCA,qBA0iCA;AACA,SAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,SAAA,SAAA,CAAA,OAAA,GAAA,KAAA,SAAA,CAAA,UAAA,CAAA,MAAA,CAAA,KAAA,SAAA,CAAA,WAAA,EAAA,MAAA,CAAA,KAAA,SAAA,CAAA,YAAA,CAAA;AACA,QAAA,IAAA,GAAA,IAAA;AACA,IAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,MAAA,eAAA,CAAA;AAAA,QAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA;AAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,IAAA,CAAA,WAAA;AACA,OAHA;AAIA,KATA;AAUA;AAxjCA,CAAA", "sourcesContent": ["<style lang=\"less\">\r\n    .mytable .ivu-table-cell{\r\n        padding-left: 5px;\r\n        padding-right: 5px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: normal;\r\n        word-break: break-all;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .accountEs .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .accountEs .header-bar-show {\r\n        max-height: 300px;\r\n        padding-top: 14px;\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .accountEs .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n\r\n\r\n    .mytable .myspan{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block\r\n    }\r\n    .mytable .errorStle{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block;\r\n        color:red;\r\n    }\r\n    .mytable .ivu-table-row{\r\n        max-height: 370px!important;\r\n    }\r\n</style>\r\n<template>\r\n    <div>\r\n        <div class=\"accountEs\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n                    <Row>\r\n                        <!-- @on-change='accountnoChange' -->\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.accountno\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{ item.name }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"关键字:\" prop=\"heatUseBody\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"accountObj.heatUseBody\" placeholder=\"用能主体关键字模糊查询\" :style=\"formItemWidth\" />\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.company\" @on-change=\"selectChange(accountObj.company)\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"accountObj.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly :style=\"formItemWidth\"/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <div  align=\"right\">\r\n                        <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n                        <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n                    </div>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <div>\r\n                <Row>\r\n                    <Col span=\"12\">\r\n                        <Page size=\"small\" :total=\"pageTotal\" :current=\"pageNum\" :page-size=\"pageSize\" show-elevator show-sizer show-total\r\n                              placement=\"top\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n                    </Col>\r\n                    <Col span=\"12\">\r\n                        <div align=\"right\" class=\"account\">\r\n                            <Button type=\"primary\" @click=\"addNewCoalAccount\">新增</Button>\r\n                            <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n                            <Button type=\"error\" @click=\"remove\">删除</Button>\r\n                            <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n                                <Button type='info' style=\"margin-left: 5px\">加入归集单\r\n                                    <Icon type='ios-arrow-down'></Icon>\r\n                                </Button>\r\n                                <DropdownMenu slot='list'>\r\n                                    <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n                                    <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n                                </DropdownMenu>\r\n                            </Dropdown>\r\n                            <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n                            <Button type=\"primary\" @click=\"loadTemplate\">导入模板下载</Button>\r\n                            <Upload style=\"float:right;\" :on-format-error=\"handleFormatError\"\r\n                                    :before-upload='onExcelUpload' :on-progress=\"handleProgress\"\r\n                                    :on-success=\"handleUploadSuccess\" :max-size=\"10240\" action=\"_blank\"\r\n                                    accept=\".csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\r\n                                    :format=\"['xls','xlsx']\">\r\n                                <Button icon='ios-cloud-upload'>导入</Button>\r\n                            </Upload>\r\n                        </div>\r\n                    </Col>\r\n                </Row>\r\n            </div>\r\n            <Table ref=\"accountEsTable\"\r\n                   border\r\n                   :columns=\"tbAccount.tailColumn\"\r\n                   :data=\"tbAccount.data\"\r\n                   class=\"mytable\">\r\n                <!--备注-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=500 v-model=\"editremark\" :ref=\"'remark'+index+10\" type=\"text\" @on-blur=\"setremark\"\r\n                               v-if=\"editIndex === index && columnsIndex === 10\"/>\r\n                        <Tooltip placement=\"left\" max-width=\"600\" v-else>\r\n                            <span :class=\"myStyle[index].remark\" style=\"width: 60px\" @click=\"selectCall(row,index,10,'remark')\">\r\n                                {{ ellipsis(row.remark) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                    <div v-else>\r\n                        <Tooltip placement=\"bottom\" max-width=\"200\">\r\n                            <span>{{ ellipsis(row.remark)}}</span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--用能主体-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"heatUseBody\" v-if=\"row.total == null\">\r\n                    <div>\r\n                        <Input :maxlength=100 v-model=\"editHeatUseBody\" :ref=\"'heatUseBody'+index+3\" type=\"text\" @on-blur=\"setHeatUseBody\"\r\n                               v-if=\"editIndex === index && columnsIndex === 3\"/>\r\n                            <!-- <span v-else :class=\"myStyle[index].heatUseBody\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\"  @click=\"selectCall(row,index,3,'heatUseBody')\">{{ ellipsis(row.heatUseBody) }}</span> -->\r\n\r\n                        <Tooltip placement=\"right\" max-width=\"200\" v-else>\r\n                            <span :class=\"myStyle[index].heatUseBody\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,3,'heatUseBody')\">\r\n                                {{ ellipsis(row.heatUseBody) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.heatUseBody }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--开始时间-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"startDate\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'startDate'+index+1\" type=\"text\" v-model=\"editStartDate\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 1\" />\r\n                    <span :class=\"myStyle[index].startDate\" @click=\"selectCall(row,index,1,'startDate')\" v-else>{{ row.startDate }}</span>\r\n                </template>\r\n                <!--结束时间-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"endDate\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'endDate'+index+2\" type=\"text\" v-model=\"editEndDate\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 2\" />\r\n                    <span :class=\"myStyle[index].endDate\" @click=\"selectCall(row,index,2,'endDate')\" v-else>{{ row.endDate }}</span>\r\n                </template>\r\n                <!--采暖面积-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"heatAreaSize\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'heatAreaSize'+index+4\" type=\"text\" v-model=\"editHeatAreaSize\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 4\" />\r\n                    <span :class=\"myStyle[index].heatAreaSize\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,4,'heatAreaSize')\" v-else>{{ row.heatAreaSize }}</span>\r\n                </template>\r\n                <!--热力-->\r\n                <!-- <template slot-scope=\"{ row, index }\" slot=\"heatAmount\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'heatAmount'+index+5\" type=\"text\" v-model=\"editHeatAmount\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 5\" />\r\n                    <span :class=\"myStyle[index].heatAmount\" @click=\"selectCall(row,index,5,'heatAmount')\" v-else>{{ row.heatAmount }}</span>\r\n                </template> -->\r\n                <!--单价-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"unitPrice\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'unitPrice'+index+6\" type=\"text\" v-model=\"editUnitPrice\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 6\" />\r\n                    <span :class=\"myStyle[index].unitPrice\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,6,'unitPrice')\" v-else>{{ row.unitPrice }}</span>\r\n                </template>\r\n                <!--票据类型-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"ticketImportType\" v-if=\"row.total == null\">\r\n                    <Select :ref=\"'ticketImportType'+index+7\" type=\"text\" v-model=\"editTicketType\" @on-change=\"setticketImportType\"\r\n                            v-if=\"editIndex === index && columnsIndex === 7\" transfer=\"true\">\r\n                        <Option value=\"专票\" label=\"专票\"></Option>\r\n                        <Option value=\"普票\" label=\"普票\"></Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].ticketImportType\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,7,'ticketImportType')\" v-else>{{ row.ticketImportType }}</span>\r\n                </template>\r\n                <!--专票税率-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"taxRateShow\" v-if=\"row.total == null\">\r\n                    <Select :ref=\"'taxRateShow'+index+8\" type=\"text\" v-model=\"editTaxRate\" @on-change=\"settaxrate\"\r\n                            v-if=\"editIndex === index && columnsIndex === 8\" transfer=\"true\">\r\n                        <Option value=\"1\">1</Option>\r\n                        <Option value=\"3\">3</Option>\r\n                        <Option value=\"6\">6</Option>\r\n                        <Option value=\"13\">13</Option>\r\n                        <Option value=\"16\">16</Option>\r\n                        <Option value=\"17\">17</Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].taxRateShow\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,8,'taxRateShow')\" v-else>{{ row.taxRateShow }}</span>\r\n                </template>\r\n                <!--其他费用-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"otherFee\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'otherFee'+index+9\" type=\"text\" v-model=\"editOtherMoney\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 9\" />\r\n                    <span v-else :class=\"myStyle[index].otherFee\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,9,'otherFee')\">{{ row.otherFee }}</span>\r\n                </template>\r\n            </Table>\r\n            <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n        </div>\r\n        <div>\r\n            <add-bill-per ref=\"addBillPer\"\r\n                          v-on:refreshList=\"refresh\"></add-bill-per >\r\n            <completed-pre-modal ref=\"completedPre\" v-on:refreshList=\"refresh\"></completed-pre-modal>\r\n            <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport {\r\n    _verify_StartDate1,\r\n    judgeNumber,\r\n    _verify_EndDate1,\r\n    _verify_PrevTotalReadings,\r\n    _verify_CurTotalReadings,\r\n    other_no_ammeteror_protocol,\r\n    self_no_ammeteror_protocol,\r\n    HFL_ammeteror,\r\n    judging_editability,\r\n    judging_editability1,\r\n    _verify_Money,\r\n    _calculateUsedReadings,\r\n    _calculateTotalReadings,\r\n    _calculateUnitPriceByUsedMoney,\r\n    _calculateAccountMoney,\r\n    _calculateQuotereadingsratio,\r\n    requiredFieldValidator,\r\n    countTaxamount,\r\n    calculateActualMoney,\r\n    judge_negate,\r\n    judge_recovery,\r\n    judge_yb,\r\n    unitpirceMin,\r\n    unitpirceMax\r\n} from '@/view/account/PowerAccountController';\r\n    import {\r\n        saveHeatAccount,\r\n        removeHeatAccount,\r\n        selectHeatIds\r\n    } from '@/api/coalHeatOilAccount';\r\n    import checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\n    import checkResult from \"@/view/account/check/checkResult\";\r\n    import alarmCheck from \"@/view/account/check/alarmCheck\";\r\n    import {getDates,testNumber,} from '@/view/account/powerAccountHelper';\r\n    import axios from '@/libs/api.request';\r\n    import SelectAmmeter from \"./selectAmmeter\";\r\n    import {_verify_FeeStartDate} from '@/view/account/PowerAccountEs';\r\n    import AddBillPer from \"./addHeatBillPreModal\";\r\n    import {reJoinBillpre} from '@/api/accountBillPer';\r\n    import {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\n    import CompletedPreModal from \"./completedPreModal\";\r\n    import indexData from '@/config/index'\r\n    import CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import {getUserdata,getUserByUserRole,getCountrysdata,getCountryByUserId} from '@/api/basedata/ammeter.js'\r\n    let dates=getDates();\r\n    export default {\r\n        name: 'addCoalAccount',\r\n        components: {alarmCheck, checkResult, checkResultAndResponse,CompletedPreModal, SelectAmmeter,AddBillPer,CountryModal},\r\n        data() {\r\n            let photo = (h, {row, index}) => {\r\n                let that = this\r\n                let str = ''\r\n                if (row.projectname != '小计' && row.projectname != '合计') {\r\n                    str = '上传附件'\r\n                }\r\n                return h(\"div\", [h(\"u\", {\r\n                    on: {\r\n                        click() {\r\n                            //打开弹出框\r\n                            if (row.projectname != '小计' && row.projectname != '合计') {\r\n                                that.uploadFile(row)\r\n                            }\r\n                        }\r\n                    }\r\n                }, str)]);\r\n            };\r\n            return {\r\n                submit:[],\r\n                submit2:[],\r\n                showCheckModel:false,\r\n                showJhModel:false,\r\n                showAlarmModel:false,\r\n                formItemWidth: widthstyle,\r\n                version:'',\r\n                dateList:dates,\r\n                filterColl: true,//搜索面板展开\r\n                editIndex: -1,//当前编辑行\r\n                columnsIndex:-1,//当前编辑列\r\n                myStyle:[],//样式\r\n                editStartDate:'',\r\n                editEndDate:'',\r\n                editHeatUseBody:'',\r\n                editHeatAreaSize:'',\r\n                editHeatAmount:'',\r\n                editUnitPrice:'',\r\n                editTicketType:'',\r\n                editTaxRate:'',\r\n                editOtherMoney:'',\r\n                spinShow:false,//遮罩\r\n                categorys:[],//描述类型\r\n                editremark:'',\r\n                accountStatus:[],\r\n                companies:[],\r\n                coalTypes: [],\r\n                coalUseTypes: [],\r\n                departments:[],\r\n                isAdmin:false,\r\n                company:null,//用户默认公司\r\n                country:null,//用户默认所属部门\r\n                countryName:null,//用户默认所属部门\r\n                accountObj:{\r\n                    accountno:dates[1].code,//期号,默认当前月\r\n                    company:\"\",//分公司\r\n                    country:\"\",//所属部门\r\n                    coalUseBody:null,//用能主体\r\n                    countryName: \"\",\r\n                },\r\n                tbAccount: {\r\n                    loading: true,\r\n                    columns: [],\r\n                    tailColumn: [\r\n                        {type: 'selection', width: 60, align: 'center',},\r\n                        {\r\n                            title: \"期号\",\r\n                            key: \"accountNo\",\r\n                            align: \"center\",\r\n                            width: 90,\r\n                        },\r\n                        {\r\n                            title: \"开始时间\",\r\n                            slot: \"startDate\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"结束时间\",\r\n                            slot: \"endDate\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"用能主体\",\r\n                            slot: \"heatUseBody\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"采暖面积(㎡)\",\r\n                            slot: \"heatAreaSize\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"热力(百万千焦)\",\r\n                            // slot: \"heatAmount\",\r\n                            key: \"heatAmount\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {\r\n                            title: \"单价(元/平方米)\",\r\n                            slot: \"unitPrice\",\r\n                            // key: \"unitPrice\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"票据类型\",\r\n                            slot: \"ticketImportType\",\r\n                            align: \"center\",\r\n                            width: 60,\r\n                        },\r\n                        {\r\n                            title: \"普票含税金额(元)\",\r\n                            key: \"ticketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票含税金额(元)\",\r\n                            key: \"taxTicketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票税率（%）\",\r\n                            slot: \"taxRateShow\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票税额\",\r\n                            key: \"taxAmount\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"其他(元)\",\r\n                            slot: \"otherFee\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"实缴费用(元)含税\",\r\n                            key: \"paidMoney\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        // {title: \"附件\", align: \"center\", render: photo, width: 100},\r\n                        {title: \"备注\", slot: \"remark\",align: \"center\", width: 150},\r\n                    ],\r\n                    data: [],\r\n                },\r\n                pageTotal: 0,\r\n                pageNum: 1,\r\n                pageSize: 10,//当前页\r\n            }\r\n        },\r\n        methods: {\r\n            selectChange(){\r\n                let that = this;\r\n                if (that.accountObj.company != undefined) {\r\n                    if(that.accountObj.company == \"-1\"){\r\n                        that.accountObj.country = -1;\r\n                        that.accountObj.countryName = null;\r\n                    }else{\r\n                        getCountryByUserId(that.accountObj.company).then(res => {\r\n                            if(res.data.departments.length != 0){\r\n                                that.accountObj.country = res.data.departments[0].id;\r\n                                that.accountObj.countryName = res.data.departments[0].name;\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            },\r\n            //选择所属部门开始\r\n            chooseResponseCenter() {\r\n                if(this.accountObj.company == null || this.accountObj.company == \"-1\" ){\r\n                    this.$Message.info(\"请先选择分公司\");return;\r\n                }\r\n                this.$refs.countryModal.choose(this.accountObj.company);//所属部门\r\n            },\r\n            getDataFromModal(data) {\r\n                this.accountObj.country = data.id;\r\n                this.accountObj.countryName = data.name;\r\n                //选择所属部门结束\r\n            },\r\n            getUserData(){\r\n                let that = this;\r\n                getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                    if(res.data.companies.length != 0){\r\n                        let companies = res.data.companies;\r\n                        if(res.data.companies[0].id == \"**********\"){\r\n                            companies = that.companies;\r\n                        }\r\n                        that.company = companies[0].id;\r\n                        that.accountObj.company = companies[0].id;\r\n                    }\r\n                    if(res.data.departments.length != 0){\r\n                        let departments = res.data.departments;\r\n                        if(res.data.companies[0].id == \"**********\" && that.departments.length != 0){\r\n                            departments = that.departments\r\n                        }\r\n                        that.country = departments[0].id;\r\n                        that.countryName = departments[0].name;\r\n                        that.accountObj.country = Number(departments[0].id);\r\n                        that.accountObj.countryName = departments[0].name;\r\n                    }\r\n\r\n                    that.pageNum = 1\r\n                    that.getAccountMessages();\r\n                });\r\n            },\r\n            searchList(){\r\n                if(this.accountObj.countryName == \"\"){\r\n                    this.accountObj.country = \"-1\";\r\n                }\r\n                this.pageNum = 1\r\n                this.getAccountMessages()\r\n            },\r\n            accountnoChange(){\r\n                this.searchList()\r\n            },\r\n            //点击保存\r\n            preserve() {\r\n                let dataL = this.$refs.accountEsTable.getSelection();\r\n                let b = false;\r\n                let array = [];\r\n                for (let i = 0; i < dataL.length; i ++) {\r\n                    b = true;\r\n                    array.push(dataL[i])\r\n                }\r\n                if(b){\r\n                    this.submitData(array);\r\n                }else {\r\n                    this.errorTips('没有可保存数据')\r\n                }\r\n            },\r\n            submitChange(indexList){\r\n                let data=[];\r\n                this.submit2.map((item,index)=>{\r\n                    indexList.map((item2)=>{\r\n                        if(index==item2){\r\n                            data.push(item)\r\n                        }\r\n                    })\r\n                });\r\n                this.submit=data\r\n            },\r\n\r\n            //提交数据\r\n            submitData(data){\r\n                let a = [];\r\n                let that=this;\r\n                if(data != null && data.length > 0){\r\n                    let number = 0;\r\n                    let submitData = [];\r\n                    let str = '';\r\n                    let accountno = this.accountObj.accountno;\r\n                    data.forEach(function (item) {\r\n                        // 校验数据\r\n                        if(item.id == null){\r\n                            item.accountno = accountno\r\n                        }\r\n                        a.push(item.id);\r\n                        submitData.push(item);\r\n                        number ++;\r\n                    });\r\n                    that.ids=a;\r\n                    if(str.length > 0){\r\n                        this.errorTips(str)\r\n                    }\r\n                    if(submitData.length > 0){\r\n                        saveHeatAccount(submitData).then((res) => {\r\n                            if (res.data.code == 0) {\r\n                                this.$Message.info({\r\n                                    content: '提示：成功保存 ' + res.data.num + ' 条数据',\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }\r\n                }\r\n            },\r\n            addNewCoalAccount() {\r\n                // let companyId = this.accountObj.company;\r\n                // let country = this.accountObj.country;\r\n                // if(companyId != null && country != null){\r\n                //     let obj = {\r\n                //         company:companyId,\r\n                //         country:country,\r\n                //         accountno:this.accountObj.accountno,\r\n                //         accountType:'1',\r\n                //         accountestype:1\r\n                //     }\r\n                // }else{\r\n                //     this.errorTips('请选择分公司和部门')\r\n                // }\r\n                const currentDate = new Date();\r\n                const currentYear = currentDate.getFullYear();\r\n                const currentMonth = currentDate.getMonth() + 1;\r\n                if (null == this.tbAccount.data) {\r\n                    this.tbAccount.data = [];\r\n                }\r\n                this.tbAccount.data.unshift({\r\n                    // accountNo: this.accountObj.accountno,\r\n                    accountNo: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined) ? currentYear+\"\"+currentMonth: this.accountObj.accountno,\r\n                    // accountNo:dates[1].code, new Date(year, month, 0);\r\n                    startDate: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined)\r\n                    ?\r\n                    currentYear + \".\" + currentMonth + \".\" + \"01\"\r\n                    :\r\n                    this.accountObj.accountno.slice(0,4) + \".\" + this.accountObj.accountno.slice(4) + \".\" + \"01\",\r\n                    endDate: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined)\r\n                    ?\r\n                    currentYear + \".\" + currentMonth + \".\" +\r\n                        new Date(currentYear, currentMonth, 0).getDate()\r\n                    :\r\n                    this.accountObj.accountno.slice(0,4) + \".\" + this.accountObj.accountno.slice(4) + \".\" +\r\n                        new Date(this.accountObj.accountno.slice(0,4), this.accountObj.accountno.slice(4), 0).getDate(),\r\n                    heatUseBody: \"\",\r\n                    heatAreaSize:\"0\",\r\n                    heatAmount: \"0\",\r\n                    unitPrice:\"0\",\r\n                    ticketImportType:\"\",\r\n                    ticketMoney:\"0\",\r\n                    taxTicketMoney:\"0\",\r\n                    taxRateShow:\"\",\r\n                    taxAmount:\"0\",\r\n                    otherFee:\"0\",\r\n                    paidMoney:\"0\",\r\n                    remark:\"\",\r\n                });\r\n                this.myStyle.push({\r\n                    startDate: 'myspan',\r\n                    endDate: 'myspan',\r\n                    // curtotalreadings: 'myspan',\r\n                    heatUseBody: 'myspan',\r\n                    heatAreaSize: 'myspan',\r\n                    heatAmount: 'myspan',\r\n\r\n                    ticketImportType: 'myspan',\r\n                    ticketMoney:\"myspan\",\r\n                    taxTicketMoney:\"myspan\",\r\n                    taxRateShow: 'myspan',\r\n                    taxAmount: 'myspan',\r\n                    otherFee: 'myspan',\r\n                    paidMoney: 'myspan',\r\n                    unitPrice: 'myspan',\r\n                    remark: 'myspan',\r\n\r\n                    });\r\n            },\r\n            //验证错误弹出提示框\r\n            errorTips(str){\r\n                this.$Notice.error({\r\n                    title: '提示',\r\n                    desc: str,\r\n                    duration: 10\r\n                });\r\n            },\r\n            handlePage(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageNum = value;\r\n                this.getAccountMessages();\r\n            },\r\n            handlePageSize(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageSize = value;\r\n                this.getAccountMessages();\r\n            },\r\n            //向后台请求数据\r\n            getAccountMessages() {\r\n                let postData = this.accountObj;\r\n                postData.pageNum = this.pageNum;\r\n                postData.pageSize = this.pageSize;\r\n                let req = {\r\n                    url : \"/business/heat/account/list\",\r\n                    method : \"get\",\r\n                    params : postData\r\n                };\r\n                this.tbAccount.loading = true\r\n                axios.request(req).then(res => {\r\n                    this.tbAccount.loading = false\r\n                    if (res.data) {\r\n                        let data = res.data.rows;\r\n                        data.forEach(function (item) {\r\n                            item.editType = 0;\r\n                        })\r\n                        // data.push(this.suntotal(data))//小计\r\n                        this.tbAccount.data = data\r\n                        this.pageTotal = res.data.total || 0\r\n                        this.setMyStyle(this.tbAccount.data.length);\r\n\r\n                        this.editIndex = -1;\r\n                        this.columnsIndex = -1;\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            },\r\n            //重置\r\n            onResetHandle(){\r\n                this.accountObj = {\r\n                    accountno:null,\r\n                    company:this.company,\r\n                    heatUseBody:null,\r\n                    country:Number(this.country),\r\n                };\r\n                this.getAccountMessages()\r\n            },\r\n            remove(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                if(data == null || data.length === 0){\r\n                    this.errorTips(\"请选择要删除的数据\")\r\n                    return;\r\n                }\r\n                this.$Modal.confirm({\r\n                    title: '提示',\r\n                    content: '<p>是否确认删除选中信息？</p>',\r\n                    onOk: () => {\r\n                        let b = true;\r\n                        let ids = '';\r\n                        let total = this.pageTotal\r\n                        for(let i=0;i<data.length;i++){\r\n                            let item = data[i];\r\n                            if(item.id != null && item.id.length > 0){\r\n                                if(item.pabriid){\r\n                                    b = false;\r\n                                }\r\n                                ids += item.id + ',';\r\n                            }\r\n                        }\r\n                        this.pageTotal = total;\r\n                        if(b){\r\n                            if(ids.length > 0){\r\n                                removeHeatAccount(ids).then((res) => {\r\n                                    if (res.data.code == 0) {\r\n                                        this.$Message.success('删除成功');\r\n                                        this.getAccountMessages();\r\n                                    }\r\n                                });\r\n                            }\r\n                        }else {\r\n                            this.errorTips('选中信息中有信息还没有跟归集单解除关联，请先解除关联')\r\n                        }\r\n                    },\r\n                    onCancel: () => {\r\n                    }\r\n                });\r\n            },\r\n            openAddBillPerModal(name) {\r\n                if (name === 'current') {\r\n                    this.selectedAccount()\r\n                } else if (name === 'all') {\r\n                    this.selectedAllAccount()\r\n                }\r\n            },\r\n            //加入归集单，全部有效台账\r\n            selectedAllAccount(){\r\n                let that = this\r\n                that.spinShow = true;\r\n                selectHeatIds(this.accountObj).then(res => {\r\n                    that.spinShow = false;\r\n                    if(res.data.length == 0){\r\n                        that.errorTips('无有效数据可加入归集单')\r\n                    }else {\r\n                        let ids = [];\r\n                        for(let i=0;i<res.data.rows.length;i++){\r\n                            let item = res.data.rows[i];\r\n                            ids.push(item.id)\r\n                        }\r\n                        that.$refs.addBillPer.initAmmeter(ids, 19,this.accountObj.country);\r\n                    }\r\n                });\r\n            },\r\n            selectedAccount(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = 1;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要加入归集单的台账')\r\n                } else {\r\n                    let ids = [];\r\n                    data.forEach(function (item) {\r\n                        if(item.status === 5){\r\n                            b = 3\r\n                        }\r\n                        if(item.status === 4){\r\n                            b=4;\r\n                        }\r\n                        ids.push(item.id)\r\n                    });\r\n                    if(b === 1){\r\n                        this.$refs.addBillPer.initAmmeter(ids,19,this.accountObj.country);\r\n                    }else if(b === 2) {\r\n                        this.errorTips('选中的台账中存在临时数据，请先保存再加入归集单！')\r\n                    }else if(b===3){\r\n                        this.errorTips('退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮')\r\n                    }else if(b===4){\r\n                        this.errorTips('选择的台账有已加入归集单的台账，不能加入其他归集单')\r\n                    }\r\n                }\r\n            },\r\n            openCompletedPreModal(){\r\n                this.$refs.completedPre.initAmmeter(this.accountObj.country,2);\r\n            },\r\n            againJoin(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = true;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要重新加入归集单的台账')\r\n                } else {\r\n                    let ids = '';\r\n                    data.forEach(function (item) {\r\n                        let status = item.status;\r\n                        if(status != 5){\r\n                            b = false;\r\n                        }\r\n                        ids+= item.id +','\r\n                    });\r\n                    if(b){\r\n                        reJoinBillpre(ids).then((res) =>{\r\n                            if(res.data.code==0){\r\n                                this.$Message.info({\r\n                                    content:'提示：操作成功' ,\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }else {\r\n                        this.errorTips('只有已退回的台账才能重新加入归集单')\r\n                    }\r\n                }\r\n            },\r\n            refresh(){\r\n                let obj = this\r\n                setTimeout(function () {\r\n                    obj.getAccountMessages()\r\n                },200);\r\n            },\r\n            setticketImportType() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTicketType;\r\n                data.ticketImportType = val;\r\n                console.log(data.ticketImportType, \"data.ticketImportType\")\r\n                if(data.ticketImportType == '专票') {\r\n                    data.ticketMoney = 0;\r\n                    data.taxTicketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = data.taxRateShow*data.taxTicketMoney*1/100;\r\n                    data.editType = 1;\r\n                }else if(data.ticketImportType == '普票') {\r\n                    data.ticketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    data.taxTicketMoney = 0;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = 0;\r\n                    //  data.taxRateShow = \"\";\r\n                    data.editType = 1;\r\n                }\r\n            },\r\n            settaxrate() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxRate;\r\n                data.taxRateShow = val;\r\n                data.taxAmount = val*data.taxTicketMoney*1/100;\r\n                data.editType = 1;\r\n            },\r\n            validate(){\r\n                if(this.columnsIndex != 5){\r\n                    let val = this.enterOperate(this.columnsIndex).data;\r\n                    if(val) {\r\n                        switch (this.columnsIndex) {\r\n                            case 1:\r\n                                this.validateStartdate();\r\n                                break;\r\n                            case 2:\r\n                                this.validateEnddate();\r\n                                break;\r\n                            case 3:\r\n                                this.validateFeeStartDate();\r\n                                break;\r\n                            case 4:\r\n                                this.validateHeatAreaSize();\r\n                                break;\r\n                            case 6:\r\n                                this.validateUnitPrice();\r\n                                break;\r\n                            case 7:\r\n                                this.validateTicketImportType();\r\n                                break;\r\n                            case 8:\r\n                                this.validateTaxRateShow();\r\n                                break;\r\n                            // case 3:\r\n                            //     this.validateFeeStartDate();\r\n                            //     break;\r\n                            case 9:\r\n                                this.validateOtherMoney();\r\n                                break;\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            //验证起始时间\r\n            validateStartdate() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editStartDate;\r\n                let isDian = val[4]=='.' && val[7]=='.' || val[4]=='.' && val[6]=='.' ;\r\n                if(!isDian) {\r\n                    this.errorTips(\"开始时间格式不正确！\");\r\n                    val = \"\";\r\n                }\r\n                // debugger\r\n                // if (val != data.old_startdate) {\r\n                //     // 验证起始时间方法\r\n                //     let result = _verify_StartDate1(data, val);\r\n                //     console.log(result, \"result\");\r\n                //     if (result) {//失败就弹出提示内容，并将数据恢复初始化\r\n                //         this.errorTips(result);\r\n                //         this.myStyle[this.editIndex].startDate = \"errorStle\";\r\n                //     } else {\r\n                //         this.myStyle[this.editIndex].startDate = \"myspan\";\r\n                //         this.startModal = true;\r\n                //     }\r\n                // } else if (val == data.old_startdate) {\r\n                //     data.startDate = val;\r\n                // }else {\r\n                if(data.startDate !=\"\" && data.endDate !=\"\") {\r\n                    let riqiLengh = data.endDate.split(\".\")[2]*1 - data.startDate.split(\".\")[2]*1;\r\n                    data.heatAmount = data.heatAreaSize*riqiLengh*60*0.7*3.6/1000000;\r\n                    console.log(data.heatAmount, \"data.heatAmount\")\r\n                }\r\n                    data.startDate = val;\r\n                    data.editType = 1;\r\n                // }\r\n\r\n            },\r\n            //验证截止时间\r\n            validateEnddate() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editEndDate;\r\n                let isDian = val[4]=='.' && val[7]=='.' || val[4]=='.' && val[6]=='.' ;\r\n                if(!isDian) {\r\n                    this.errorTips(\"结束时间格式不正确！\");\r\n                    val = \"\";\r\n                }\r\n                if(data.startDate !=\"\" && data.endDate !=\"\") {\r\n                    let riqiLengh = data.endDate.split(\".\")[2]*1 - data.startDate.split(\".\")[2]*1;\r\n                    data.heatAmount = data.heatAreaSize*riqiLengh*60*0.7*3.6/1000000;\r\n                    console.log(data.heatAmount, \"data.heatAmount\")\r\n                }\r\n                // if (val != data.old_enddate) {\r\n                //     // 验证截止日期方法\r\n                //     let result = _verify_EndDate1(data, val);\r\n                //     if (result) {\r\n                //         this.errorTips(result);\r\n                //         this.myStyle[this.editIndex].endDate = \"errorStle\";\r\n                //     } else {\r\n                //         this.myStyle[this.editIndex].endDate = \"myspan\";\r\n\r\n                //         this.updateenddate(data, val)\r\n\r\n                //     }\r\n                // } else if (val == data.old_enddate) {\r\n                    data.endDate = val;\r\n                    data.editType = 1;\r\n                // }\r\n            },\r\n            validateHeatAreaSize() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editHeatAreaSize;\r\n                data.heatAreaSize = val;\r\n                console.log(data.heatAreaSize, \"data.heatAreaSize\")\r\n                data.editType = 1;\r\n                console.log(data.startDate.split(\".\")[2]*1, \"data.startDate.split(\")\r\n                console.log(data.endDate.split(\".\")[2]*1, \"data.endDate.split(\")\r\n                if(data.startDate !=\"\" && data.endDate !=\"\") {\r\n                    let riqiLengh = data.endDate.split(\".\")[2]*1 - data.startDate.split(\".\")[2]*1 + 1;\r\n                    console.log(riqiLengh, \"riqiLengh\")\r\n\r\n                    data.heatAmount = (val*riqiLengh*24*60*0.7*3.6/1000000).toFixed(6);\r\n                }\r\n                console.log(data.ticketImportType, \"data.ticketImportType\")\r\n                // debugger\r\n                if(data.ticketImportType == '专票') {\r\n                    data.ticketMoney = 0;\r\n                    data.taxTicketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = data.taxRateShow*data.taxTicketMoney*1/100;\r\n                    data.editType = 1;\r\n                }else if(data.ticketImportType == '普票') {\r\n                    data.ticketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    data.taxTicketMoney = 0;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = 0;\r\n                    data.editType = 1;\r\n                }\r\n                // else {\r\n                //     this.errorTips(\"开始或者结束时间不能为空！\");\r\n                //     data.heatAmount = \"\";\r\n                // }\r\n            },\r\n            validateTicketImportType() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTicketType;\r\n                data.ticketImportType = val;\r\n                data.editType = 1;\r\n            },\r\n            validateTaxRateShow() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxRate;\r\n                data.taxRateShow = val;\r\n                data.editType = 1;\r\n            },\r\n            validateFeeStartDate(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editFeeStartDate;\r\n                let result = _verify_FeeStartDate(data,val);\r\n                if(result){//失败就弹出提示内容，并将数据恢复初始化\r\n                    this.errorTips(result)\r\n                }else{\r\n                    data.feeStartDate = val;\r\n                    data.editType = 1;\r\n                }\r\n            },\r\n            validateUnitPrice() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editUnitPrice;\r\n                data.unitPrice = val;\r\n                console.log(data.ticketImportType, \"data.ticketImportType\")\r\n                // debugger\r\n                if(data.ticketImportType == '专票') {\r\n                    data.ticketMoney = 0;\r\n                    data.taxTicketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = data.taxRateShow*data.taxTicketMoney*1/100;\r\n                    data.editType = 1;\r\n                }else if(data.ticketImportType == '普票') {\r\n                    data.ticketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    data.taxTicketMoney = 0;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = 0;\r\n                    data.editType = 1;\r\n                }\r\n                data.editType = 1;\r\n            },\r\n            validateOtherMoney(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOtherMoney;\r\n                if (!testNumber(val)) {\r\n                    this.errorTips('请输入数字！');\r\n                }\r\n                data.otherFee = val;\r\n                data.editType = 1;\r\n                let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                    data.paidMoney = paidMoney.toFixed(2);\r\n                // debugger\r\n            },\r\n            setremark(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editremark;\r\n                data.remark = val;\r\n                data.editType = 1;\r\n            },\r\n            setHeatUseBody(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editHeatUseBody;\r\n                data.heatUseBody = val;\r\n                data.editType = 1;\r\n            },\r\n            setMyStyle(length){\r\n                this.myStyle=[];\r\n                for(var i=0;i<length;i++){\r\n                    this.myStyle.push({\r\n                        startDate: 'myspan',\r\n                        endDate: 'myspan',\r\n                        coalUseBody:'myspan',\r\n                        feeStartDate:'myspan',\r\n                        coalAmount:'myspan',\r\n                        taxTicketMoney:'myspan',\r\n                        remark:'myspan',\r\n                    });\r\n                }\r\n            },\r\n            //span点击事件将span换成输入框并且获取焦点\r\n            selectCall(row,index,columns,str){\r\n                this.editStartDate = row.startDate;\r\n                this.editEndDate = row.endDate;\r\n                this.editHeatUseBody = row.heatUseBody;\r\n                this.editHeatAreaSize = row.heatAreaSize;\r\n                this.editHeatAmount = row.heatAmount;\r\n                this.editUnitPrice = row.unitPrice;\r\n                this.editTicketType = row.ticketImportType;\r\n                this.editTaxRate = row.taxRateShow;\r\n                this.editOtherMoney = row.otherFee;\r\n                this.editremark = row.remark;\r\n                this.editIndex = index;\r\n                this.columnsIndex = columns;\r\n                let a=this;\r\n                setTimeout(function () {\r\n                    a.$refs[str+index+columns].focus();\r\n                },200);\r\n\r\n            },\r\n            //跳转到下一格\r\n            nextCell(data){\r\n                let index = data.editIndex;\r\n                let columns = data.columnsIndex;\r\n                let row = '';\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                }else if(index > -1 && columns === 5){\r\n                    //当跳转的最后一行最后一格的时候\r\n                    if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n                        index = 0;\r\n                    }else{\r\n                        index ++;\r\n                    }\r\n                    columns = 1;\r\n                }else{\r\n                    columns += 1;\r\n                }\r\n                debugger\r\n                data.editIndex = index;\r\n                data.columnsIndex = columns;\r\n                row = data.tbAccount.data[index];\r\n                if(row){\r\n                    data.editStartDate = row.startDate;\r\n                    data.editEndDate = row.endDate;\r\n                    data.editHeatUseBody = row.heatUseBody;\r\n                    data.editHeatAreaSize = row.heatAreaSize;\r\n                    data.editHeatAmount = row.heatAmount;\r\n                    data.editUnitPrice = row.unitPrice;\r\n                    data.editTicketType = row.ticketImportType;\r\n                    data.editTaxRate = row.taxRateShow;\r\n                    data.editOtherMoney = row.otherFee;\r\n                    data.editremark = row.remark;\r\n                }\r\n                setTimeout(function () {\r\n                    data.$refs[data.enterOperate(columns).str+index+columns].focus();\r\n                },200);\r\n            },\r\n            //根据列号返回对应的列名\r\n            enterOperate(number){\r\n                let str = '';\r\n                let data = null;\r\n                switch (number) {\r\n                    case 1:\r\n                        str = 'startDate';\r\n                        data = this.editStartDate;\r\n                        break;\r\n                    case 2:\r\n                        str = 'endDate';\r\n                        data = this.editEndDate;\r\n                        break;\r\n                    case 3:\r\n                        str = 'heatUseBody'\r\n                        data = this.editHeatUseBody;\r\n                        break;\r\n                    case 4:\r\n                        str = 'heatAreaSize';\r\n                        data = this.editHeatAreaSize;\r\n                        break;\r\n                    case 5:\r\n                        str = 'heatAmount';\r\n                        data = this.editHeatAmount;\r\n                        break;\r\n                    case 6:\r\n                        str = 'unitPrice';\r\n                        data = this.editUnitPrice;\r\n                        break;\r\n                    case 7:\r\n                        str = 'editTicketType';\r\n                        data = this.ticketImportType;\r\n                        break;\r\n                    case 8:\r\n                        str = 'editTaxRate';\r\n                        data = this.taxRateShow;\r\n                        break;\r\n                    case 9:\r\n                        str = 'otherFee';\r\n                        data = this.editOtherMoney;\r\n                        break;\r\n                    case 10:\r\n                        str = 'remark';\r\n                        data = this.editremark;\r\n                        break;\r\n                }\r\n                return {str:str,data:data};\r\n            },\r\n            pred(){\r\n                var lett = this;\r\n                let index = lett.editIndex;\r\n                let columns = lett.columnsIndex;\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                    lett.editIndex = index;\r\n                    lett.columnsIndex = columns;\r\n                    setTimeout(function () {\r\n                        lett.$refs[lett.enterOperate(columns).str+index+columns].focus();\r\n                    },200);\r\n                }else{\r\n                    lett.validate()\r\n                    lett.setremark()\r\n                    lett.nextCell(lett)\r\n                }\r\n            },\r\n            ellipsis (value) {\r\n                if (!value) return ''\r\n                if (value.length > 100) {\r\n                    return value.slice(0,100) + '...'\r\n                }\r\n                return value\r\n            },\r\n            handleProgress(event, file) {\r\n                this.$Message.info({\r\n                    content: file.name + \" 正在上传。\",\r\n                });\r\n            },\r\n            handleUploadSuccess() {\r\n\r\n            },\r\n            handleFormatError(file) {\r\n                this.errorTips(\r\n                    file.name + \" 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件\"\r\n                );\r\n            },\r\n            // 导入\r\n            onExcelUpload(file) {\r\n                if (!file) {\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: '请选择要上传的文件！',\r\n                        duration: 10\r\n                    });\r\n                    return\r\n                }\r\n                let fileName = file.name.lastIndexOf(\".\");//取到文件名开始到最后一个点的长度\r\n                let fileNameLength = file.name.length;//取到文件名长度\r\n                let fileFormat = file.name.substring(fileName + 1, fileNameLength);//截\r\n                if('xls' != fileFormat && 'xlsx' != fileFormat){\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: file.name + ' 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件',\r\n                        duration: 10\r\n                    });\r\n                    return;\r\n                }\r\n                let param = {}\r\n                let excel = {file: file}\r\n                let that = this\r\n                that.spinShow = true\r\n                axios.request({\r\n                    url: '/business/heat/account/import',\r\n                    method: 'post',\r\n                    data: Object.assign({}, param, excel)\r\n                }).then((res) => {\r\n                    that.spinShow = false\r\n                    if (res.data.str) {\r\n                        that.$Message.info({\r\n                            content: res.data.str,\r\n                            duration: 0,\r\n                            closable: true\r\n                        });\r\n\r\n                        that.show = false;\r\n                    }\r\n                    this.getAccountMessages();\r\n                }).catch(err => {\r\n                    that.spinShow = false\r\n                    that.show = false;\r\n                    console.log(err);\r\n                });\r\n                this.getAccountMessages()\r\n                return false\r\n            },\r\n\r\n            // 导入模板下载\r\n            loadTemplate() {\r\n                let req = {\r\n                    url : \"/business/heat/account/template/load\",\r\n                    method : \"get\",\r\n                    responseType: 'blob',\r\n                };\r\n                axios.file(req)\r\n                    .then(res => {\r\n                        this.spinShow = false;\r\n                        const content = res;\r\n                        const blob = new Blob([content]);\r\n                        const fileName = \"热力台账导入模板.xlsx\";\r\n                        if (\"download\" in document.createElement(\"a\")) {\r\n                            // 非IE下载\r\n                            const elink = document.createElement(\"a\");\r\n                            elink.download = fileName;\r\n                            elink.style.display = \"none\";\r\n                            elink.href = URL.createObjectURL(blob);\r\n                            document.body.appendChild(elink);\r\n                            elink.click();\r\n                            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n                            document.body.removeChild(elink);\r\n                        } else {\r\n                            // IE10+下载\r\n                            navigator.msSaveBlob(blob, fileName);\r\n                        }\r\n                    });\r\n            }\r\n        },\r\n        mounted() {\r\n            this.version = indexData.version\r\n            this.tbAccount.columns = this.tbAccount.tailColumn.concat(this.tbAccount.photoColumn).concat(this.tbAccount.remarkColumn);\r\n            let that = this\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                    that.getUserData();\r\n                });\r\n            });\r\n        }\r\n    }\r\n</script>\r\n"], "sourceRoot": "src/view/account"}]}