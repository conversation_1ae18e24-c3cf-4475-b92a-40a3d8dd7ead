{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\statistics\\energymeter\\modal-list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\statistics\\energymeter\\modal-list.vue", "mtime": 1754285403055}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["modal-list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "modal-list.vue", "sourceRoot": "src/view/statistics/energymeter", "sourcesContent": ["<template>\r\n  <Modal v-model=\"showModal\" :title=\"title\" width=\"70%\">\r\n    <div class=\"charge-info common-wh\">\r\n      <div class=\"query-box\">\r\n        <Form ref=\"queryform\" :model=\"queryParams\" :label-width=\"100\">\r\n          <Row class=\"form-row\">\r\n            <div class=\"query-btns\">\r\n              <Button\r\n                type=\"success\"\r\n                class=\"queryBtn\"\r\n                icon=\"ios-search\"\r\n                @click=\"_onSearchHandle\"\r\n                >搜索\r\n              </Button>\r\n              <Button type=\"info\" class=\"queryBtn\" icon=\"ios-redo\" @click=\"_onResetHandle\"\r\n                >重置</Button\r\n              >\r\n            </div>\r\n          </Row>\r\n        </Form>\r\n      </div>\r\n\r\n      <cl-table\r\n        ref=\"clTable\"\r\n        :height=\"400\"\r\n        :query-params=\"queryParams\"\r\n        :columns=\"tableSet.columns\"\r\n        :loading=\"tableSet.loading\"\r\n        :total=\"tableSet.total\"\r\n        :pageSize=\"tableSet.pageSize\"\r\n        :data=\"tableList\"\r\n        :sum-columns=\"[]\"\r\n        @on-query=\"tableQuery\"\r\n        :searchable=\"false\"\r\n        :exportable=\"false\"\r\n      >\r\n      </cl-table>\r\n    </div>\r\n    <div slot=\"footer\">\r\n      <Button type=\"default\" class=\"cancelBtn\" @click=\"showModal = false\">取消</Button>\r\n    </div>\r\n  </Modal>\r\n</template>\r\n<script>\r\nimport { getUserByUserRole, getCountryByUserId } from \"@/api/basedata/ammeter\";\r\nimport { getAmmeterInfo } from \"@/api/statistics/index\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport { noEmpty } from \"@/libs/util\";\r\nimport axios from \"@/libs/api.request\";\r\n\r\nexport default {\r\n  props: [\"title\"],\r\n  data() {\r\n    return {\r\n      showModal: false,\r\n      btnloading: false, //确认提交\r\n      pageParams: {\r\n        type: \"\",\r\n      },\r\n      queryParams: {\r\n        //查询参数\r\n      },\r\n      queryedParams: {},\r\n      dicts: {\r\n        stationType: [],\r\n        company: [], //所属分公司\r\n        country: [], //所属部门\r\n        isAdmin: false,\r\n      },\r\n      listData: [],\r\n      tableSet: {\r\n        loading: false,\r\n        pageTotal: 0,\r\n        pageNum: 1,\r\n        pageSize: 10, //当前页\r\n        columns: [\r\n          {\r\n            title: \"部门名称\",\r\n            key: \"orgName\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"局站编码\",\r\n            key: \"stationcode\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"局站名称\",\r\n            key: \"stationname\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"电表编号\",\r\n            key: \"meterCode\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"项目名称\",\r\n            key: \"projectname\",\r\n            align: \"center\",\r\n          },\r\n        ],\r\n      },\r\n      tableList: [],\r\n      curMonth: {},\r\n      list_loading: true,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.dicts.state = blist(\"budget_audit_type\");\r\n    let arr2 = [];\r\n    for (let i = 0; i < 12; i++) {\r\n      arr2.push({\r\n        name: i + 1,\r\n        cons: null,\r\n        cnt: null,\r\n        consCnt: null,\r\n        noReach: false,\r\n      });\r\n    }\r\n    this.listData = arr2;\r\n  },\r\n  methods: {\r\n    //弹窗:打开\r\n    openModal(row) {\r\n      this.showModal = true;\r\n      this.pageParams = row;\r\n      this._onResetHandle(); //下拉选项\r\n    },\r\n    //表格-筛选-重置\r\n    _onResetHandle() {\r\n      this.$refs[\"queryform\"].resetFields();\r\n      this._onSearchHandle(); //搜索列表\r\n    },\r\n    //表格-筛选-搜索\r\n    _onSearchHandle() {\r\n      this.queryedParams = { ...this.queryParams };\r\n      this.$refs.clTable.query(this.queryedParams);\r\n    },\r\n    //查询\r\n    tableQuery(params) {\r\n      Object.assign(params, this.pageParams, {\r\n        pageNumber: params.pageNum,\r\n      });\r\n      delete params.pageNum;\r\n      this.tableSet.loading = true;\r\n      getAmmeterInfo(params).then((res) => {\r\n        this.tableSet.loading = false;\r\n        let data = res.data.rows;\r\n        this.tableSet.total = res.data.total;\r\n        this.tableList = data;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.charge-info {\r\n  font-weight: 400;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .list-box {\r\n    margin: 0 20px 10px 20px;\r\n    padding: 10px 10px 0 10px;\r\n    background: #f6f8fa;\r\n    .list-title {\r\n      font-size: 14px;\r\n      margin-bottom: 10px;\r\n      .title {\r\n        font-weight: bold;\r\n      }\r\n    }\r\n    .list-con {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      position: relative;\r\n      .top {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        .title {\r\n          font-size: 14px;\r\n          margin-bottom: 10px;\r\n        }\r\n      }\r\n      .list-item {\r\n        width: ~\"calc(25% - 10px)\";\r\n        height: 87px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        margin: 0 5px;\r\n        margin-bottom: 10px;\r\n        padding: 10px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        cursor: pointer;\r\n\r\n        .value {\r\n          text-align: center;\r\n          // margin: 6px 0;\r\n          font-size: 14px;\r\n          font-weight: bold;\r\n        }\r\n        .value-red {\r\n          color: red;\r\n        }\r\n        .rate {\r\n          display: flex;\r\n          .rate-1 {\r\n            width: 50%;\r\n            text-align: left;\r\n          }\r\n        }\r\n        .list-no {\r\n          text-align: center;\r\n          margin: auto;\r\n        }\r\n      }\r\n      .list-active {\r\n        background: #3581f442;\r\n      }\r\n    }\r\n  }\r\n  .list-title2 {\r\n    font-size: 14px;\r\n    margin: 0 20px;\r\n    font-weight: bold;\r\n  }\r\n  .query-btns {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n::v-deep .ivu-modal {\r\n  top: 50px !important;\r\n}\r\n::v-deep .ivu-modal-content .ivu-modal-body form {\r\n  padding: 10px 10px 0px 10px !important;\r\n}\r\n::v-deep .two-input .ivu-form-item-content {\r\n  display: flex;\r\n}\r\n</style>\r\n"]}]}