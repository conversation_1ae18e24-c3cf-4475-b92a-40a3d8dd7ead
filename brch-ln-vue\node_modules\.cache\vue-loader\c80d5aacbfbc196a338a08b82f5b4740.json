{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\energyview\\calculate.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\energyview\\calculate.vue", "mtime": 1754285403041}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBkb3dubG9hZFRlbXBsYXRlLA0KICByZXBvcnRMaXN0LA0KICByZUNvdW50RGF0YUxpc3QsDQogIHJlcG9ydFRvR3JvdXAsDQp9IGZyb20gIkAvYXBpL2NhcmJvbi9kaXNjaGFyZ2UvZW5lcmd5IjsNCmltcG9ydCB7DQogIGVuZXJneUNhbGN1bGF0ZUxpc3QsDQogIHNhdmVFbmVyZ3lDYWxjdWxhdGVMaXN0LA0KICBleHBvcnRFeGNlbCwNCn0gZnJvbSAiQC9hcGkvY2FyYm9uL2Rpc2NoYXJnZS9lbmVyZ3lDYWxjdWxhdGUiOw0KaW1wb3J0IHsgcHJpY2VEZXRhaWwsIGFkZFByaWNlLCBwdXRQcmljZSB9IGZyb20gIkAvYXBpL2NhcmJvbi9kaXNjaGFyZ2UvZW5lcmd5UHJpY2UiOw0KaW1wb3J0IHsNCiAgY2xlYXJDb21wYW55UmVtaW5kLA0KICBnZXRDb21wYW55UmVtaW5kLA0KICBnZXRSZW1pbmRDb3VudCwNCn0gZnJvbSAiQC9hcGkvY2FyYm9uL2Rpc2NoYXJnZS9ub3RpZmljYXRpb24iOw0KaW1wb3J0IHsgZG93bmxvYWRGaWxlIH0gZnJvbSAiQC9hcGkvY2FyYm9uL3J1b3lpIjsNCmltcG9ydCBpbXBvcnRFeGNlbCBmcm9tICJAL2FwaS9jYXJib24vZXhjZWwvaW1wb3J0IjsNCmltcG9ydCBkZWxlY3RzIGZyb20gIl9jL2RlbGVjdHMvaW5kZXgiOw0KaW1wb3J0IHsgZGF0ZUZvcm1hdCB9IGZyb20gIkAvYXBpL2NhcmJvbi9kYXRlIjsNCmltcG9ydCByZWplY3REaWFsb2cgZnJvbSAiQC9jb21wb25lbnRzL2NhcmJvbi9yZWplY3REaWFsb2ciOw0KaW1wb3J0IHsgZ2V0VXNlckJ5VXNlclJvbGUgfSBmcm9tICJAL2FwaS9iYXNlZGF0YS9hbW1ldGVyIjsNCmltcG9ydCBqaXR1YW5FbmVyZ3lSZXBvcnRMb2cgZnJvbSAiLi9qaXR1YW5FbmVyZ3lSZXBvcnRMb2ciOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsNCiAgICAvLyBlbXB0eVN0cmF0ZSwNCiAgICBqaXR1YW5FbmVyZ3lSZXBvcnRMb2csDQogICAgZGVsZWN0cywNCiAgICByZWplY3REaWFsb2csDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHRhYmxlTG9hZGluZzogZmFsc2UsDQogICAgICB0YWJsZURhdGFUaW1lOiB1bmRlZmluZWQsDQogICAgICB0YWJsZURhdGE6IFtdLA0KICAgICAgZW5lcmd5RGF0YTogW10sDQogICAgICBlZGl0RGlzYWJsZWQ6IHRydWUsDQogICAgICBlZGl0QnV0dG9uTGFiZWw6ICLnvJbovpEiLA0KICAgICAgY29uZmlybURpYWxvZzogew0KICAgICAgICB2aXNpYmxlOiBmYWxzZSwNCiAgICAgICAgY29udGVudDogIiIsDQogICAgICB9LA0KICAgICAgcmVqZWN0RGlhbG9nOiB7DQogICAgICAgIHRpdGxlOiAi55Sz6K+36YCA<PERSON><PERSON>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"}, {"version": 3, "sources": ["calculate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "calculate.vue", "sourceRoot": "src/view/carbon/discharge/energyview", "sourcesContent": ["<template>\r\n  <div class=\"page-class common-el\" id=\"modularForm\">\r\n    <el-form\r\n      class=\"form-head\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      :model=\"formData\"\r\n      label-width=\"80px\"\r\n    >\r\n      <el-row>\r\n        <el-col :span=\"24\">\r\n          <el-form-item label=\"上报年月：\">\r\n            <el-date-picker\r\n              v-model=\"formData.reportTime\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              placeholder=\"请选择日期\"\r\n              type=\"month\"\r\n              :clearable=\"false\"\r\n              :picker-options=\"pickerOptions\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"数据部门：\">\r\n            <avue-select\r\n              :disabled=\"this.companyOption == null || this.companyOption.length <= 1\"\r\n              :clearable=\"false\"\r\n              v-model=\"formData.companyId\"\r\n              placeholder=\"请选择\"\r\n              :dic=\"companyOption\"\r\n              @change=\"companyChange($event)\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button class=\"searchBtn\" @click=\"searchList\">搜索</el-button>\r\n            <el-button class=\"resetBtn\" @click=\"reset\">重置</el-button>\r\n            <el-button class=\"resetBtn\" @click=\"handleDownClick\">导出</el-button>\r\n            <el-button class=\"searchBtn\" @click=\"insertEvent\">{{\r\n              editButtonLabel\r\n            }}</el-button>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"24\" style=\"display: flex; justify-content: flex-end\">\r\n          <el-form-item>\r\n            <el-button class=\"resetBtn\" @click=\"ruleDialogVisible = true\"\r\n              >规则口径说明</el-button\r\n            >\r\n            <el-button class=\"resetBtn\" @click=\"handleReCountClick\"\r\n              >重新计算能耗数据</el-button\r\n            >\r\n            <el-button class=\"searchBtn\" @click=\"reportToGroup\">上报到集团</el-button>\r\n            <el-button class=\"searchBtn\" @click=\"reportToGroupLog\">上报历史</el-button>\r\n            <el-button class=\"searchBtn\" @click=\"handlePriceClick\">单价维护</el-button>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n    <el-row :gutter=\"10\" class=\"total-card-row\">\r\n      <el-col :span=\"6\" style=\"padding: 0\">\r\n        <div\r\n          class=\"total-card total-card-bg-green\"\r\n          @click=\"handleReportCompany()\"\r\n          style=\"cursor: pointer\"\r\n        >\r\n          <div class=\"number-type\">\r\n            {{ this.reportDialog.count }}\r\n            <span\r\n              style=\"font-size: smaller; font-weight: normal\"\r\n              v-if=\"reportDialog.applyReturnCount > 0\"\r\n              >(退回申请{{ reportDialog.applyReturnCount }})</span\r\n            >\r\n          </div>\r\n          <div class=\"text-type\">已上报部门</div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"6\" style=\"padding: 0; cursor: pointer\">\r\n        <div class=\"total-card total-card-bg-yellow\" @click=\"handleUnReportCompany()\">\r\n          <div class=\"number-type\">{{ this.unReportDialog.count }}</div>\r\n          <div class=\"text-type\">未上报部门</div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"6\" style=\"padding: 0; cursor: pointer\">\r\n        <div class=\"total-card total-card-bg-red\">\r\n          <div class=\"number-type\">{{ this.reportDialog.reportRate }}%</div>\r\n          <div class=\"text-type\">上报率</div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <div class=\"curd-header\">\r\n      <div class=\"reporting\">\r\n        <p>上报年月</p>\r\n        <el-date-picker\r\n          v-model=\"tableDataTime\"\r\n          type=\"month\"\r\n          placeholder=\"选择月\"\r\n          disabled\r\n        >\r\n        </el-date-picker>\r\n      </div>\r\n      <p class=\"companyName\">{{ titleCompanyName }}</p>\r\n      <!--        <div class=\"reportLabel\" :style=\"{background:reportLabel=='已上报'?'#00bf9a':'#FF7D41'}\">{{ reportLabel }}</div>-->\r\n    </div>\r\n    <avue-crud\r\n      ref=\"crud\"\r\n      :data=\"tableData\"\r\n      :table-loading=\"tableLoading\"\r\n      :option=\"tableOption\"\r\n    >\r\n      <template slot=\"groupDataForm\" slot-scope=\"{ row, index }\">\r\n        <el-input\r\n          :class=\"[backgroundList.groupList.indexOf(index) == -1 ? '' : 'el_input_class']\"\r\n          v-model=\"row.groupData\"\r\n          :disabled=\"row.groupInputType != '2' ? editDisabled : true\"\r\n          :type=\"judgeInputType(index, 'group')\"\r\n          @change=\"handleGroupChange(index)\"\r\n          @input=\"limitInputTwo($event, index, 'group')\"\r\n          @keydown.native=\"inputLimit\"\r\n          :placeholder=\"\r\n            formulaList.groupList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\r\n          \"\r\n          @mousewheel.native.prevent\r\n        ></el-input>\r\n      </template>\r\n      <template slot=\"stockDataForm\" slot-scope=\"{ row, index }\">\r\n        <el-input\r\n          :class=\"[backgroundList.stockList.indexOf(index) == -1 ? '' : 'el_input_class']\"\r\n          v-model=\"row.stockData\"\r\n          :disabled=\"row.stockInputType != '2' ? editDisabled : true\"\r\n          :type=\"judgeInputType(index, 'stock')\"\r\n          @change=\"handleStockChange(index)\"\r\n          @input=\"limitInputTwo($event, index, 'stock')\"\r\n          @keydown.native=\"inputLimit\"\r\n          :placeholder=\"\r\n            formulaList.stockList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\r\n          \"\r\n          @mousewheel.native.prevent\r\n        ></el-input>\r\n      </template>\r\n      <template slot=\"largeDataForm\" slot-scope=\"{ row, index }\">\r\n        <el-input\r\n          :class=\"[backgroundList.largeList.indexOf(index) == -1 ? '' : 'el_input_class']\"\r\n          v-model=\"row.largeData\"\r\n          :disabled=\"row.largeInputType != '2' ? editDisabled : true\"\r\n          :type=\"judgeInputType(index, 'large')\"\r\n          @change=\"handleLargeChange(index)\"\r\n          @input=\"limitInputTwo($event, index, 'large')\"\r\n          @keydown.native=\"inputLimit\"\r\n          :placeholder=\"\r\n            formulaList.largeList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\r\n          \"\r\n          @mousewheel.native.prevent\r\n        ></el-input>\r\n      </template>\r\n      <template slot=\"mediumDataForm\" slot-scope=\"{ row, index }\">\r\n        <el-input\r\n          :class=\"[\r\n            backgroundList.mediumList.indexOf(index) == -1 ? '' : 'el_input_class',\r\n          ]\"\r\n          v-model=\"row.mediumData\"\r\n          :disabled=\"row.mediumInputType != '2' ? editDisabled : true\"\r\n          :type=\"judgeInputType(index, 'medium')\"\r\n          @change=\"handleMediumChange(index)\"\r\n          @input=\"limitInputTwo($event, index, 'medium')\"\r\n          @keydown.native=\"inputLimit\"\r\n          :placeholder=\"\r\n            formulaList.mediumList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\r\n          \"\r\n          @mousewheel.native.prevent\r\n        ></el-input>\r\n      </template>\r\n      <template slot=\"mobileDataForm\" slot-scope=\"{ row, index }\">\r\n        <el-input\r\n          :class=\"[\r\n            backgroundList.mobileList.indexOf(index) == -1 ? '' : 'el_input_class',\r\n          ]\"\r\n          v-model=\"row.mobileData\"\r\n          :disabled=\"row.mobileInputType != '2' ? editDisabled : true\"\r\n          :type=\"judgeInputType(index, 'mobile')\"\r\n          @change=\"handleMobileChange(index)\"\r\n          @input=\"limitInputTwo($event, index, 'mobile')\"\r\n          @keydown.native=\"inputLimit\"\r\n          :placeholder=\"\r\n            formulaList.mobileList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\r\n          \"\r\n          @mousewheel.native.prevent\r\n        ></el-input>\r\n      </template>\r\n      <!-- 空状态 -->\r\n      <!--        <template slot=\"empty\">-->\r\n      <!--          <emptyStrate />-->\r\n      <!--        </template>-->\r\n    </avue-crud>\r\n    <!-- 弹窗, 已上报部门 -->\r\n    <el-dialog\r\n      :visible.sync=\"reportDialog.visible\"\r\n      title=\"已上报部门\"\r\n      width=\"65%\"\r\n      :append-to-body=\"true\"\r\n    >\r\n      <avue-crud\r\n        ref=\"report\"\r\n        :data=\"reportDialog.companyList\"\r\n        :option=\"reportDialog.tableOption\"\r\n      >\r\n        <!--        <template slot-scope=\"scope\" slot=\"menu\" fixed=\"right\">-->\r\n        <!--          <el-button type=\"text\" @click=\"handleEdit(scope.row)\"-->\r\n        <!--          >修改上报数据-->\r\n        <!--          </el-button>-->\r\n        <!--          <el-button type=\"text\" @click=\"handleRejectData(scope.row)\"-->\r\n        <!--          >退回数据-->\r\n        <!--          </el-button>-->\r\n        <!--          <el-button type=\"text\"-->\r\n        <!--                     v-if=\"scope.row.recordCount &&  scope.row.recordCount > 1\"-->\r\n        <!--                     @click=\"modifyReport(scope.row)\"-->\r\n        <!--          >修改记录-->\r\n        <!--          </el-button>-->\r\n        <!--          <el-button type=\"text\"-->\r\n        <!--                     v-if=\"scope.row.reportFlag == '4'\"-->\r\n        <!--                     @click=\"handleRejectReason(scope.row)\"-->\r\n        <!--          >申请理由-->\r\n        <!--          </el-button>-->\r\n        <!--        </template>-->\r\n      </avue-crud>\r\n      <div class=\"foot_btn\">\r\n        <el-button type=\"success\" @click=\"reportDialog.visible = false\">关闭 </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 弹窗, 未上报部门 -->\r\n    <el-dialog\r\n      :visible.sync=\"unReportDialog.visible\"\r\n      title=\"未上报部门\"\r\n      width=\"60%\"\r\n      :append-to-body=\"true\"\r\n    >\r\n      <!--      <div style=\"margin: 0vh 2rem 2vh;\">-->\r\n      <!--        <el-button type=\"success\" @click=\"handleRemind()\">提醒上报</el-button>-->\r\n      <!--      </div>-->\r\n      <avue-crud\r\n        ref=\"unReport\"\r\n        :data=\"unReportDialog.companyList\"\r\n        :option=\"unReportDialog.tableOption\"\r\n      >\r\n      </avue-crud>\r\n      <!-- <div class=\"foot_btn\">\r\n        <el-button type=\"success\" @click=\"unReportDialog.visible = false\"\r\n          >关闭</el-button\r\n        >\r\n      </div> -->\r\n    </el-dialog>\r\n    <el-dialog\r\n      width=\"28%\"\r\n      :visible.sync=\"confirmDialog.visible\"\r\n      title=\"上报确认\"\r\n      :append-to-body=\"true\"\r\n    >\r\n      <delects\r\n        :content=\"confirmDialog.content\"\r\n        @delectRow=\"saveReportData\"\r\n        @cancel=\"confirmDialog.visible = false\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      width=\"50%\"\r\n      :visible.sync=\"reportLogDialog.visible\"\r\n      title=\"上报记录\"\r\n      :append-to-body=\"true\"\r\n    >\r\n      <jituan-energy-report-log></jituan-energy-report-log>\r\n    </el-dialog>\r\n\r\n    <!-- 弹窗, 单价维护 -->\r\n    <el-dialog\r\n      :visible.sync=\"priceDialog.visible\"\r\n      title=\"单价维护\"\r\n      :append-to-body=\"true\"\r\n      class=\"common-el\"\r\n    >\r\n      <el-form\r\n        ref=\"formDataRef\"\r\n        :model=\"priceDialog.formData\"\r\n        :rules=\"priceDialog.formRules\"\r\n        label-width=\"130px\"\r\n        @keyup.enter=\"handleSaveEnergyPrice\"\r\n      >\r\n        <el-row :gutter=\"10\" style=\"width: 80%; margin: 0 auto\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"价格月份\" prop=\"reportTime\">\r\n              <el-date-picker\r\n                v-model=\"priceDialog.formData.reportTime\"\r\n                value-format=\"yyyy-MM-01\"\r\n                placeholder=\"请选择日期\"\r\n                type=\"month\"\r\n                disabled\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"24\" class=\"el-col-top\">\r\n            <el-form-item label=\"电价\" prop=\"electricityPrice\">\r\n              <el-input\r\n                type=\"number\"\r\n                :controls=\"false\"\r\n                v-model=\"priceDialog.formData.electricityPrice\"\r\n                placeholder=\"电价\"\r\n                class=\"number-class\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <i slot=\"suffix\">元/千瓦时</i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\" class=\"el-col-top\">\r\n            <el-form-item label=\"汽油价\" prop=\"gasolinePriceTwo\">\r\n              <el-input\r\n                type=\"number\"\r\n                :controls=\"false\"\r\n                v-model=\"priceDialog.formData.gasolinePriceTwo\"\r\n                placeholder=\"汽油价\"\r\n                class=\"number-class\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <i slot=\"suffix\">元/升</i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\" class=\"el-col-top\">\r\n            <el-form-item label=\"柴油价\" prop=\"dieselPrice\">\r\n              <el-input\r\n                type=\"number\"\r\n                :controls=\"false\"\r\n                v-model=\"priceDialog.formData.dieselPrice\"\r\n                placeholder=\"柴油价\"\r\n                class=\"number-class\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <i slot=\"suffix\">元/升</i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\" class=\"el-col-top\">\r\n            <el-form-item label=\"热能价\" prop=\"thermalPrice\">\r\n              <el-input\r\n                type=\"number\"\r\n                :controls=\"false\"\r\n                v-model=\"priceDialog.formData.thermalPrice\"\r\n                placeholder=\"热能价\"\r\n                class=\"number-class\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <i slot=\"suffix\">元/平方米</i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\" class=\"el-col-top\">\r\n            <el-form-item label=\"煤炭价\" prop=\"coalPrice\">\r\n              <el-input\r\n                type=\"number\"\r\n                :controls=\"false\"\r\n                v-model=\"priceDialog.formData.coalPrice\"\r\n                placeholder=\"煤炭价\"\r\n                class=\"number-class\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <i slot=\"suffix\">元/吨</i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button class=\"cancelBtn\" @click=\"priceDialog.visible = false\" type=\"success\"\r\n          >取消</el-button\r\n        >\r\n        <el-button class=\"okBtn\" type=\"info\" @click=\"handleSaveEnergyPrice\"\r\n          >确定</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 导出提示弹窗 -->\r\n    <el-dialog\r\n      :visible.sync=\"downDialog.visible\"\r\n      :title=\"downDialog.title\"\r\n      width=\"28%\"\r\n      :append-to-body=\"true\"\r\n      class=\"common-el\"\r\n    >\r\n      <div>\r\n        <el-radio v-model=\"downDialog.downOpts\" label=\"1\">导出选择部门数据</el-radio>\r\n      </div>\r\n      <div>\r\n        <el-radio v-model=\"downDialog.downOpts\" label=\"2\"\r\n          >导出所有部门数据</el-radio\r\n        >\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button class=\"cancelBtn\" @click=\"downDialog.visible = false\" type=\"success\"\r\n          >取消</el-button\r\n        >\r\n        <el-button class=\"okBtn\" type=\"info\" @click=\"downExcel()\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 规则口径说明弹窗 -->\r\n    <el-dialog\r\n      :visible.sync=\"ruleDialogVisible\"\r\n      title=\"规则口径说明\"\r\n      width=\"28%\"\r\n      :append-to-body=\"true\"\r\n      class=\"common-el\"\r\n    >\r\n      <div>\r\n        <p v-text=\"ruleContent\"></p>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button class=\"cancelBtn\" @click=\"ruleDialogVisible = false\" type=\"success\"\r\n          >关闭</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  downloadTemplate,\r\n  reportList,\r\n  reCountDataList,\r\n  reportToGroup,\r\n} from \"@/api/carbon/discharge/energy\";\r\nimport {\r\n  energyCalculateList,\r\n  saveEnergyCalculateList,\r\n  exportExcel,\r\n} from \"@/api/carbon/discharge/energyCalculate\";\r\nimport { priceDetail, addPrice, putPrice } from \"@/api/carbon/discharge/energyPrice\";\r\nimport {\r\n  clearCompanyRemind,\r\n  getCompanyRemind,\r\n  getRemindCount,\r\n} from \"@/api/carbon/discharge/notification\";\r\nimport { downloadFile } from \"@/api/carbon/ruoyi\";\r\nimport importExcel from \"@/api/carbon/excel/import\";\r\nimport delects from \"_c/delects/index\";\r\nimport { dateFormat } from \"@/api/carbon/date\";\r\nimport rejectDialog from \"@/components/carbon/rejectDialog\";\r\nimport { getUserByUserRole } from \"@/api/basedata/ammeter\";\r\nimport jituanEnergyReportLog from \"./jituanEnergyReportLog\";\r\n\r\nexport default {\r\n  components: {\r\n    // emptyStrate,\r\n    jituanEnergyReportLog,\r\n    delects,\r\n    rejectDialog,\r\n  },\r\n  data() {\r\n    return {\r\n      tableLoading: false,\r\n      tableDataTime: undefined,\r\n      tableData: [],\r\n      energyData: [],\r\n      editDisabled: true,\r\n      editButtonLabel: \"编辑\",\r\n      confirmDialog: {\r\n        visible: false,\r\n        content: \"\",\r\n      },\r\n      rejectDialog: {\r\n        title: \"申请退回理由\",\r\n        tips: \"请输入申请退回理由\",\r\n        visible: false,\r\n        id: undefined,\r\n        content: \"\",\r\n        mode: 1,\r\n      },\r\n      formulaList: {\r\n        groupList: [0, 4, 5, 7, 18, 23, 27],\r\n        stockList: [0, 4, 5, 7, 18, 23, 27],\r\n        largeList: [0, 4, 23, 27],\r\n        mediumList: [0, 4, 11, 12, 13, 14, 15, 16, 23, 27],\r\n        mobileList: [0, 4, 7, 8, 9, 10],\r\n      },\r\n      backgroundList: {\r\n        groupList: [],\r\n        stockList: [],\r\n        largeList: [],\r\n        mediumList: [],\r\n        mobileList: [],\r\n      },\r\n      tableOption: {\r\n        border: false,\r\n        index: false,\r\n        height: \"auto\",\r\n        calcHeight: 35,\r\n        stripe: true,\r\n        menuAlign: \"center\",\r\n        align: \"center\",\r\n        refreshBtn: false,\r\n        showClomnuBtn: false,\r\n        searchMenuSpan: 4,\r\n        searchSize: \"mini\",\r\n        card: true,\r\n        addBtn: false,\r\n        editBtn: false,\r\n        delBtn: false,\r\n        columnBtn: false,\r\n        searchBtn: false,\r\n        gridBtn: false,\r\n        emptyBtn: false,\r\n        menu: false,\r\n        dialogWidth: 500,\r\n        dialogMenuPosition: \"center\",\r\n        dialogCustomClass: \"singleRowDialog\",\r\n        labelWidth: 100,\r\n        column: [\r\n          {\r\n            label: \"指标\",\r\n            prop: \"indicatorName\",\r\n            overHidden: true,\r\n            width: 500,\r\n            align: \"left\",\r\n          },\r\n          {\r\n            label: \"集团\",\r\n            prop: \"groupData\",\r\n            formatter: (val, value, label) => {\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: true,\r\n          },\r\n          {\r\n            label: \"股份\",\r\n            prop: \"stockData\",\r\n            formatter: (val, value, label) => {\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: true,\r\n          },\r\n          {\r\n            label: \"数据中心\",\r\n            children: [\r\n              {\r\n                label: \"大型\",\r\n                prop: \"largeData\",\r\n                formatter: (val, value, label) => {\r\n                  return this.formatDisplayData(value);\r\n                },\r\n                overHidden: true,\r\n                cell: true,\r\n                slot: true,\r\n              },\r\n              {\r\n                label: \"中小型\",\r\n                prop: \"mediumData\",\r\n                formatter: (val, value, label) => {\r\n                  return this.formatDisplayData(value);\r\n                },\r\n                cell: true,\r\n                slot: true,\r\n                overHidden: true,\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            label: \"移动业务\",\r\n            prop: \"mobileData\",\r\n            formatter: (val, value, label) => {\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: true,\r\n          },\r\n        ],\r\n      },\r\n      hasReported: false,\r\n      reportLabel: \"未上报\",\r\n      companyOption: [],\r\n      companyDisabled: true,\r\n      titleCompanyName: undefined,\r\n      formData: {\r\n        reportTime: undefined,\r\n        companyId: undefined,\r\n        companyName: undefined,\r\n        countType: \"1\", //1-按规则计算\r\n      },\r\n      ruleDialogVisible: false,\r\n      ruleContent:\r\n        \"系统自动统计能耗报账对应账期内报账单状态为报账完成的数据，根据该账期内已完成的报账金额，结合各类型能源平均单价反算能耗值。\",\r\n      downDialog: {\r\n        visible: false,\r\n        title: \"导出提示\",\r\n        downOpts: \"1\",\r\n        fileName: \"\",\r\n        queryForm: {\r\n          reportTime: undefined,\r\n          companyId: undefined,\r\n          downType: \"1\",\r\n          companyIds: [],\r\n        },\r\n      },\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n          let year = new Date().getFullYear();\r\n          let month = new Date().getMonth(); // 上月\r\n          let days = new Date(year, month, 0).getDate(); // 上月总天数\r\n          return time.getTime() > Date.now() - 24 * 60 * 60 * 1000 * `${days}`;\r\n        },\r\n      },\r\n      userInfo: {\r\n        deptId: undefined,\r\n        companyName: undefined,\r\n        isAdmin: false,\r\n      },\r\n      companyRemindList: [],\r\n      reportLogDialog: {\r\n        visible: false,\r\n      },\r\n      reportDialog: {\r\n        visible: false,\r\n        companyList: [],\r\n        count: 0,\r\n        applyReturnCount: 0,\r\n        reportRate: 0.0,\r\n        tableOption: {\r\n          border: false,\r\n          height: \"50vh\",\r\n          index: true,\r\n          indexLabel: \"序号\",\r\n          indexWidth: 70,\r\n          stripe: true,\r\n          menuAlign: \"center\",\r\n          align: \"center\",\r\n          refreshBtn: false,\r\n          showClomnuBtn: false,\r\n          addBtn: false,\r\n          editBtn: false,\r\n          delBtn: false,\r\n          gridBtn: false,\r\n          columnBtn: false,\r\n          menu: false,\r\n          searchBtn: false,\r\n          emptyBtn: false,\r\n          column: [\r\n            {\r\n              label: \"部门名称\",\r\n              prop: \"companyName\",\r\n              overHidden: true,\r\n            },\r\n            {\r\n              label: \"上报时间\",\r\n              prop: \"createTime\",\r\n              overHidden: true,\r\n            },\r\n            {\r\n              label: \"提醒次数\",\r\n              prop: \"remindCount\",\r\n              width: 100,\r\n              overHidden: true,\r\n            },\r\n            {\r\n              label: \"申请退回\",\r\n              prop: \"reportFlag\",\r\n              formatter: (val, value, label) => {\r\n                return value == \"4\" ? \"是\" : \"否\";\r\n              },\r\n              width: 100,\r\n              overHidden: true,\r\n            },\r\n          ],\r\n        },\r\n      },\r\n      unReportDialog: {\r\n        visible: false,\r\n        companyList: [],\r\n        remindCompanyList: [],\r\n        count: 0,\r\n        tableOption: {\r\n          border: false,\r\n          index: true,\r\n          height: \"50vh\",\r\n          selection: false,\r\n          tip: false,\r\n          indexWidth: 70,\r\n          indexLabel: \"序号\",\r\n          stripe: true,\r\n          menuAlign: \"center\",\r\n          align: \"center\",\r\n          refreshBtn: false,\r\n          showClomnuBtn: false,\r\n          addBtn: false,\r\n          menu: false,\r\n          editBtn: false,\r\n          gridBtn: false,\r\n          delBtn: false,\r\n          columnBtn: false,\r\n          searchBtn: false,\r\n          emptyBtn: false,\r\n          column: [\r\n            {\r\n              label: \"部门名称\",\r\n              prop: \"companyName\",\r\n              overHidden: true,\r\n            },\r\n            {\r\n              label: \"提醒次数\",\r\n              prop: \"remindCount\",\r\n              overHidden: true,\r\n            },\r\n          ],\r\n        },\r\n      },\r\n      priceDialog: {\r\n        visible: false,\r\n        queryParams: {\r\n          reportTime: undefined,\r\n        },\r\n        formData: {\r\n          id: undefined,\r\n          reportTime: undefined,\r\n          electricityPrice: undefined,\r\n          gasolinePriceTwo: undefined,\r\n          gasolinePriceFive: undefined,\r\n          gasolinePriceEight: undefined,\r\n          dieselPrice: undefined,\r\n          thermalPrice: undefined,\r\n          coalPrice: undefined,\r\n        },\r\n        formRules: {\r\n          reportTime: [{ required: true, message: \"请输入价格月份\", trigger: \"change\" }],\r\n          electricityPrice: [{ required: true, message: \"请输入电价\", trigger: \"blur\" }],\r\n          gasolinePriceTwo: [\r\n            { required: true, message: \"请输入92号汽油价格\", trigger: \"blur\" },\r\n          ],\r\n          gasolinePriceFive: [\r\n            { required: true, message: \"请输入95号汽油价格\", trigger: \"blur\" },\r\n          ],\r\n          gasolinePriceEight: [\r\n            { required: true, message: \"请输入98号汽油价格\", trigger: \"blur\" },\r\n          ],\r\n          dieselPrice: [{ required: true, message: \"请输入柴油价格\", trigger: \"blur\" }],\r\n          thermalPrice: [{ required: true, message: \"请输入热能价格\", trigger: \"blur\" }],\r\n          coalPrice: [{ required: true, message: \"请输入煤炭价格\", trigger: \"blur\" }],\r\n        },\r\n      },\r\n    };\r\n  },\r\n  // computed: {\r\n  //   ...mapGetters([\"roleCodes\", \"userInfo\", \"permissions\"]),\r\n  // },\r\n  created() {\r\n    let lastMonth = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);\r\n    this.formData.reportTime = dateFormat(lastMonth, \"yyyy-MM-01 0:00:00\");\r\n    this.initUserInfo();\r\n  },\r\n  methods: {\r\n    handleGroupChange(index) {\r\n      if (this.tableData[index].groupInputType != \"2\") {\r\n        // if (parseFloat(this.tableData[index]['groupData'].toString()) < 0) {\r\n        //   this.tableData[index]['groupData'] = \"0\";\r\n        // }\r\n        if (\r\n          this.tableData[index][\"groupData\"] != undefined &&\r\n          this.tableData[index][\"groupData\"] != \"\"\r\n        ) {\r\n          let j = this.backgroundList.groupList.indexOf(index);\r\n          if (j > -1) {\r\n            this.backgroundList.groupList.splice(j, 1);\r\n          }\r\n        }\r\n        if (index == 6 || index == 11 || index == 17) {\r\n          this.tableData[5][\"groupData\"] = (\r\n            this.cellToNumber(this.tableData[6][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[7][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[11][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[17][\"groupData\"])\r\n          ).toString();\r\n          this.tableData[4][\"groupData\"] = (\r\n            this.cellToNumber(this.tableData[5][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[18][\"groupData\"])\r\n          ).toString();\r\n          if (index == 11) {\r\n            this.tableData[index][\"mediumData\"] = (\r\n              this.cellToNumber(this.tableData[index][\"groupData\"]) +\r\n              this.cellToNumber(this.tableData[index][\"stockData\"])\r\n            ).toString();\r\n            this.tableData[4][\"mediumData\"] = this.tableData[11][\"mediumData\"];\r\n            this.countData(\"mediumData\");\r\n          }\r\n        } else if (index == 8 || index == 9 || index == 10) {\r\n          this.tableData[7][\"groupData\"] = (\r\n            this.cellToNumber(this.tableData[8][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[9][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[10][\"groupData\"])\r\n          ).toString();\r\n          this.tableData[5][\"groupData\"] = (\r\n            this.cellToNumber(this.tableData[6][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[7][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[11][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[17][\"groupData\"])\r\n          ).toString();\r\n          this.tableData[4][\"groupData\"] = (\r\n            this.cellToNumber(this.tableData[5][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[18][\"groupData\"])\r\n          ).toString();\r\n        } else if (index > 10 && index < 17) {\r\n          this.tableData[index][\"mediumData\"] = (\r\n            this.cellToNumber(this.tableData[index][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[index][\"stockData\"])\r\n          ).toString();\r\n          this.countData(\"mediumData\");\r\n        } else if (index == 19 || index == 20) {\r\n          this.tableData[18][\"groupData\"] = (\r\n            this.cellToNumber(this.tableData[19][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[20][\"groupData\"])\r\n          ).toString();\r\n          this.tableData[4][\"groupData\"] = (\r\n            this.cellToNumber(this.tableData[5][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[18][\"groupData\"])\r\n          ).toString();\r\n        } else if (index == 23 || index == 24) {\r\n          this.tableData[22][\"groupData\"] = (\r\n            this.cellToNumber(this.tableData[23][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[24][\"groupData\"])\r\n          ).toString();\r\n        } else if (index == 27 || index == 28) {\r\n          this.tableData[26][\"groupData\"] = (\r\n            this.cellToNumber(this.tableData[27][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[28][\"groupData\"])\r\n          ).toString();\r\n        }\r\n        this.countData(\"groupData\");\r\n      }\r\n    },\r\n    handleStockChange(index) {\r\n      if (this.tableData[index].stockInputType != \"2\") {\r\n        // if (parseFloat(this.tableData[index]['stockData'].toString()) < 0) {\r\n        //   this.tableData[index]['stockData'] = \"0\";\r\n        // }\r\n        if (\r\n          this.tableData[index][\"stockData\"] != undefined &&\r\n          this.tableData[index][\"stockData\"] != \"\"\r\n        ) {\r\n          let j = this.backgroundList.stockList.indexOf(index);\r\n          if (j > -1) {\r\n            this.backgroundList.stockList.splice(j, 1);\r\n          }\r\n        }\r\n        if (index == 6 || index == 11 || index == 17) {\r\n          this.tableData[5][\"stockData\"] = (\r\n            this.cellToNumber(this.tableData[6][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[7][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[11][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[17][\"stockData\"])\r\n          ).toString();\r\n          this.tableData[4][\"stockData\"] = (\r\n            this.cellToNumber(this.tableData[5][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[18][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[21][\"stockData\"])\r\n          ).toString();\r\n          if (index == 11) {\r\n            this.tableData[index][\"mediumData\"] = (\r\n              this.cellToNumber(this.tableData[index][\"groupData\"]) +\r\n              this.cellToNumber(this.tableData[index][\"stockData\"])\r\n            ).toString();\r\n            this.tableData[4][\"mediumData\"] = this.tableData[11][\"mediumData\"];\r\n          }\r\n        } else if (index == 8 || index == 9 || index == 10) {\r\n          this.tableData[index][\"mobileData\"] = (\r\n            this.cellToNumber(this.tableData[index][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[index][\"stockData\"])\r\n          ).toString();\r\n          this.tableData[7][\"stockData\"] = (\r\n            this.cellToNumber(this.tableData[8][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[9][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[10][\"stockData\"])\r\n          ).toString();\r\n          this.tableData[4][\"mediumData\"] = this.tableData[7][\"stockData\"];\r\n          this.tableData[7][\"mobileData\"] = this.tableData[7][\"stockData\"];\r\n          this.tableData[4][\"mobileData\"] = this.tableData[7][\"stockData\"];\r\n          this.tableData[5][\"stockData\"] = (\r\n            this.cellToNumber(this.tableData[6][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[7][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[11][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[17][\"stockData\"])\r\n          ).toString();\r\n          this.tableData[4][\"stockData\"] = (\r\n            this.cellToNumber(this.tableData[5][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[18][\"stockData\"])\r\n          ).toString();\r\n          this.countData(\"mediumData\");\r\n          this.countData(\"mobileData\");\r\n        } else if (index > 10 && index < 17) {\r\n          this.tableData[index][\"mediumData\"] = (\r\n            this.cellToNumber(this.tableData[index][\"groupData\"]) +\r\n            this.cellToNumber(this.tableData[index][\"stockData\"])\r\n          ).toString();\r\n          this.countData(\"mediumData\");\r\n        } else if (index == 19 || index == 20) {\r\n          this.tableData[18][\"stockData\"] = (\r\n            this.cellToNumber(this.tableData[19][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[20][\"stockData\"])\r\n          ).toString();\r\n          this.tableData[4][\"stockData\"] = (\r\n            this.cellToNumber(this.tableData[5][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[18][\"stockData\"])\r\n          ).toString();\r\n        } else if (index == 23 || index == 24) {\r\n          this.tableData[22][\"stockData\"] = (\r\n            this.cellToNumber(this.tableData[23][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[24][\"stockData\"])\r\n          ).toString();\r\n        } else if (index == 27 || index == 28) {\r\n          this.tableData[26][\"stockData\"] = (\r\n            this.cellToNumber(this.tableData[27][\"stockData\"]) +\r\n            this.cellToNumber(this.tableData[28][\"stockData\"])\r\n          ).toString();\r\n        }\r\n        this.countData(\"stockData\");\r\n      }\r\n    },\r\n    handleLargeChange(index) {\r\n      if (this.tableData[index].largeInputType != \"2\") {\r\n        // if (parseFloat(this.tableData[index]['largeData'].toString()) < 0) {\r\n        //   this.tableData[index]['largeData'] = \"0\";\r\n        // }\r\n        if (\r\n          this.tableData[index][\"largeData\"] != undefined &&\r\n          this.tableData[index][\"largeData\"] != \"\"\r\n        ) {\r\n          let j = this.backgroundList.largeList.indexOf(index);\r\n          if (j > -1) {\r\n            this.backgroundList.largeList.splice(j, 1);\r\n          }\r\n        }\r\n        if (index == 11) {\r\n          this.tableData[4][\"largeData\"] = this.tableData[11][\"largeData\"];\r\n        } else if (index == 23 || index == 24) {\r\n          this.tableData[22][\"largeData\"] = (\r\n            this.cellToNumber(this.tableData[23][\"largeData\"]) +\r\n            this.cellToNumber(this.tableData[24][\"largeData\"])\r\n          ).toString();\r\n        } else if (index == 27 || index == 28) {\r\n          this.tableData[26][\"largeData\"] = (\r\n            this.cellToNumber(this.tableData[27][\"largeData\"]) +\r\n            this.cellToNumber(this.tableData[28][\"largeData\"])\r\n          ).toString();\r\n        }\r\n        this.countData(\"largeData\");\r\n      }\r\n    },\r\n    handleMediumChange(index) {\r\n      if (this.tableData[index].mediumInputType != \"2\") {\r\n        // if (parseFloat(this.tableData[index]['mediumData'].toString()) < 0) {\r\n        //   this.tableData[index]['mediumData'] = \"0\";\r\n        // }\r\n        if (\r\n          this.tableData[index][\"mediumData\"] != undefined &&\r\n          this.tableData[index][\"mediumData\"] != \"\"\r\n        ) {\r\n          let j = this.backgroundList.mediumList.indexOf(index);\r\n          if (j > -1) {\r\n            this.backgroundList.mediumList.splice(j, 1);\r\n          }\r\n        }\r\n        if (index == 23 || index == 24) {\r\n          this.tableData[22][\"mediumData\"] = (\r\n            this.cellToNumber(this.tableData[23][\"mediumData\"]) +\r\n            this.cellToNumber(this.tableData[24][\"mediumData\"])\r\n          ).toString();\r\n        } else if (index == 27 || index == 28) {\r\n          this.tableData[26][\"mediumData\"] = (\r\n            this.cellToNumber(this.tableData[27][\"mediumData\"]) +\r\n            this.cellToNumber(this.tableData[28][\"mediumData\"])\r\n          ).toString();\r\n        }\r\n        this.countData(\"mediumData\");\r\n      }\r\n    },\r\n    handleMobileChange(index) {\r\n      if (this.tableData[index].mobileInputType != \"2\") {\r\n        // if (parseFloat(this.tableData[index]['mobileData'].toString()) < 0) {\r\n        //   this.tableData[index]['mobileData'] = \"0\";\r\n        // }\r\n        if (\r\n          this.tableData[index][\"mobileData\"] != undefined &&\r\n          this.tableData[index][\"mobileData\"] != \"\"\r\n        ) {\r\n          let j = this.backgroundList.mobileList.indexOf(index);\r\n          if (j > -1) {\r\n            this.backgroundList.mobileList.splice(j, 1);\r\n          }\r\n        }\r\n        this.countData(\"mobileData\");\r\n      }\r\n    },\r\n    //限制只能输入两位小数\r\n    limitInputTwo(value, index, dataType) {\r\n      let inputValue = \"\" + value;\r\n      // inputValue = inputValue.replace(/[^\\d+.-]/g, \"\"); //清除\"数字\"、\".\"、\"+\"、\"-\"号以外的字符\r\n      // inputValue = inputValue.replace(/^\\./g, \"\"); //验证第一个字符是.字\r\n      //inputValue = inputValue.replace(/^[0]/g, \"\");//验证第一个字符是0字\r\n      // inputValue = inputValue.replace(/00/g, \"0\"); //验证第一个是多个0（只保留第一个0, 清除多余的0）\r\n      // inputValue = inputValue.replace(/\\.{2,}/g, \".\"); //只保留第一个\".\", 清除多余的\r\n      // inputValue = inputValue.replace(/\\-{2,}/g, \"-\"); //只保留第一个\"-\", 清除多余的\r\n      // inputValue = inputValue.replace(/\\+{2,}/g, \"+\"); //只保留第一个\"+\", 清除多余的\r\n      // inputValue = inputValue.replace(/\\+\\-/g, \"+\"); //只保留第一个\"+\", 清除多余的\"-\"\r\n      // inputValue = inputValue.replace(/\\-\\+/g, \"-\"); //只保留第一个\"-\", 清除多余的\"+\"\r\n      // inputValue = inputValue.replace(/[0-9]+\\+/g, \"\"); //数字后面不准许输入\"+\"\r\n      // inputValue = inputValue.replace(/[0-9]+\\-/g, \"\"); //数字后面不准许输入\"-\"\r\n      // inputValue = inputValue.replace(/\\.[0-9]*\\+/g, \".\"); //去除\".\"号后面的\"+\"\r\n      // inputValue = inputValue.replace(/\\.[0-9]*\\-/g, \".\"); //去除\".\"号后面的\"-\"\r\n      // inputValue = inputValue.replace(\".\", \"$#$\").replace(/\\./g, \"\").replace(\"$#$\", \".\");\r\n      inputValue = inputValue.replace(/^(\\-)*(\\d+)\\.(\\d\\d).*$/, \"$1$2.$3\"); //只能输入两个小数\r\n      this.tableData[index][dataType + \"Data\"] = inputValue;\r\n      // this.tableData[index][dataType + 'Data'] = ('' + value) // 第一步：转成字符串\r\n      //     .replace(/[^\\d^\\.^\\-]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉\r\n      //     .replace(/^0+(\\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字\r\n      //     .replace(/^\\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全\r\n      //     .match(/^-?\\d*(\\.?\\d{0,2})/g)[0] || '' // 第五步：最终匹配得到结果 以数字开头，只有一个小数点，而且小数点后面只能有0到2位小数\r\n    },\r\n    //数据禁止输入e\r\n    inputLimit(event) {\r\n      if (event.key === \"e\") {\r\n        event.returnValue = false;\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    countData(label) {\r\n      this.tableData[0][label] = (\r\n        Math.round(\r\n          (this.cellToNumber(this.tableData[1][label]) * this.tableData[1].coefficient +\r\n            this.cellToNumber(this.tableData[3][label]) * this.tableData[3].coefficient +\r\n            (this.cellToNumber(this.tableData[4][label]) *\r\n              this.tableData[4].coefficient) /\r\n              1000 +\r\n            this.cellToNumber(this.tableData[21][label]) *\r\n              this.tableData[21].coefficient +\r\n            (this.cellToNumber(this.tableData[22][label]) *\r\n              this.tableData[22].coefficient) /\r\n              1000 +\r\n            (this.cellToNumber(this.tableData[25][label]) *\r\n              this.tableData[25].coefficient) /\r\n              1000 +\r\n            (this.cellToNumber(this.tableData[26][label]) *\r\n              this.tableData[26].coefficient) /\r\n              1000 +\r\n            (this.cellToNumber(this.tableData[29][label]) *\r\n              this.tableData[29].coefficient) /\r\n              1000 +\r\n            this.cellToNumber(this.tableData[30][label]) *\r\n              this.tableData[30].coefficient +\r\n            (this.cellToNumber(this.tableData[31][label]) *\r\n              this.tableData[31].coefficient) /\r\n              1000 +\r\n            this.cellToNumber(this.tableData[32][label]) *\r\n              this.tableData[32].coefficient +\r\n            this.cellToNumber(this.tableData[33][label]) +\r\n            (this.cellToNumber(this.tableData[34][label]) *\r\n              this.tableData[34].coefficient) /\r\n              1000 -\r\n            this.cellToNumber(this.tableData[35][label])) *\r\n            1000000\r\n        ) / 1000000\r\n      ).toString();\r\n    },\r\n    //重置数据\r\n    reset() {\r\n      this.initParams();\r\n      let lastMonth = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);\r\n      this.formData.reportTime = dateFormat(lastMonth, \"yyyy-MM-01 0:00:00\");\r\n      this.getList();\r\n    },\r\n    cellToNumber(cell) {\r\n      let num = 0;\r\n      if (cell == undefined || cell == null) {\r\n        num = 0;\r\n      } else {\r\n        num = Math.round(parseFloat(cell) * 1000000) / 1000000;\r\n        if (isNaN(num) || num == null) {\r\n          num = 0;\r\n        }\r\n      }\r\n      return num;\r\n    },\r\n    handleImportExcel(file, fileList) {\r\n      this.ImportExcel(file, fileList);\r\n    },\r\n    ImportExcel(file, fileList) {\r\n      try {\r\n        let tableField = [\"上报指标\", \"集团\", \"股份\", \"大型\", \"中小型\", \"移动业务\"];\r\n        let m = this;\r\n        importExcel(file, tableField).then((res) => {\r\n          if (res.length > this.tableData.length) {\r\n            for (let i = 0; i < this.tableData.length; i++) {\r\n              if (this.tableData[i].indicatorName == res[i + 1][\"上报指标\"].trim()) {\r\n                if (this.checkExcelLineData(res[i + 1], i) == true) {\r\n                  this.tableData[i].groupData = res[i + 1][\"集团\"].trim();\r\n                  this.tableData[i].stockData = res[i + 1][\"股份\"].trim();\r\n                  this.tableData[i].largeData = res[i + 1][\"大型\"].trim();\r\n                  this.tableData[i].mediumData = res[i + 1][\"中小型\"].trim();\r\n                  this.tableData[i].mobileData = res[i + 1][\"移动业务\"].trim();\r\n                } else {\r\n                  m.alertError(\r\n                    \"第\" + Math.floor(i + 3) + \"行有非法数据数据，请检查文档！\"\r\n                  );\r\n                  return;\r\n                }\r\n              } else {\r\n                m.alertError(\r\n                  \"第\" + Math.floor(i + 3) + \"行上报指标名称错误，请参考填报模板！\"\r\n                );\r\n                return;\r\n              }\r\n            }\r\n          } else {\r\n            m.alertError(\"数据行数过少，请参考填报模板！\");\r\n            return;\r\n          }\r\n          this.editDisabled = false;\r\n          this.editButtonLabel = \"保存\";\r\n        });\r\n      } catch (exception) {\r\n        //抓住throw抛出的错误\r\n        this.alertError(\"数据格式错误\");\r\n      }\r\n    },\r\n    checkExcelLineData(data, line) {\r\n      let ret = true;\r\n      if (\r\n        data[\"集团\"] == undefined ||\r\n        data[\"集团\"] == null ||\r\n        data[\"股份\"] == undefined ||\r\n        data[\"股份\"] == null ||\r\n        data[\"大型\"] == undefined ||\r\n        data[\"大型\"] == null ||\r\n        data[\"中小型\"] == undefined ||\r\n        data[\"中小型\"] == null ||\r\n        data[\"移动业务\"] == undefined ||\r\n        data[\"移动业务\"] == null\r\n      ) {\r\n        ret = false;\r\n        return ret;\r\n      } else {\r\n        if (this.tableData[line].groupInputType != \"2\") {\r\n          let groupData = parseFloat(data[\"集团\"]);\r\n          if (isNaN(groupData) || groupData == null) {\r\n            // if (isNaN(groupData) || groupData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.tableData[line].stockInputType != \"2\") {\r\n          let stockData = parseFloat(data[\"股份\"]);\r\n          if (isNaN(stockData) || stockData == null) {\r\n            // if (isNaN(stockData) || stockData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.tableData[line].largeInputType != \"2\") {\r\n          let largeData = parseFloat(data[\"大型\"]);\r\n          if (isNaN(largeData) || largeData == null) {\r\n            // if (isNaN(largeData) || largeData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.tableData[line].mediumInputType != \"2\") {\r\n          let mediumData = parseFloat(data[\"中小型\"]);\r\n          if (isNaN(mediumData) || mediumData == null) {\r\n            // if (isNaN(mediumData) || mediumData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.tableData[line].mobileInputType != \"2\") {\r\n          let mobileData = parseFloat(data[\"移动业务\"]);\r\n          if (isNaN(mobileData) || mobileData == null) {\r\n            // if (isNaN(mobileData) || mobileData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n    checkOpts() {\r\n      this.downDialog.queryForm.reportTime = this.formData.reportTime;\r\n      this.downDialog.queryForm.downType = this.downDialog.downOpts;\r\n      let newDate = new Date(this.downDialog.queryForm.reportTime);\r\n      if (this.downDialog.downOpts == \"2\") {\r\n        // 所有上报的部门数据\r\n        this.downDialog.fileName = dateFormat(newDate, \"yyyy年MM月\") + \"能源数据汇总.xls\";\r\n        this.downDialog.queryForm.companyId = undefined;\r\n        this.downDialog.queryForm.companyIds = [];\r\n        this.companyOption.forEach((node) => {\r\n          // if (node.value != undefined) {\r\n            this.downDialog.queryForm.companyIds.push(node.value);\r\n          // }\r\n        });\r\n      } else {\r\n        this.downDialog.queryForm.companyId = this.formData.companyId;\r\n        this.downDialog.queryForm.companyIds = undefined;\r\n        this.downDialog.fileName =\r\n          dateFormat(newDate, \"yyyy年MM月\") + this.formData.companyName + \"能源数据.xls\";\r\n      }\r\n    },\r\n    downExcel() {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"导出中\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      this.checkOpts();\r\n      exportExcel(this.downDialog.queryForm).then((res) => {\r\n        downloadFile(res, this.downDialog.fileName);\r\n        loading.close();\r\n        this.downDialog.downOpts = \"1\";\r\n        this.downDialog.visible = false;\r\n      });\r\n    },\r\n    initParams() {\r\n      this.editDisabled = true;\r\n      this.editButtonLabel = \"编辑\";\r\n    },\r\n    //搜索数据\r\n    searchList() {\r\n      if (this.formData.reportTime == undefined) {\r\n        this.alertError(\"请选择上报年月\");\r\n        return;\r\n      }\r\n      this.editDisabled = true;\r\n      this.editButtonLabel = \"编辑\";\r\n      this.getList();\r\n    },\r\n    handleReCountClick() {\r\n      let that = this;\r\n      this.$confirm(\r\n        \"是否确认重新计算能耗数据，当前操作会清除已经计算的数据重新按规则计算！\",\r\n        \"提示\",\r\n        {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }\r\n      )\r\n        .then(function () {\r\n          //确定\r\n          that.countEnergyData();\r\n        })\r\n        .catch(() => {\r\n          //取消\r\n        });\r\n    },\r\n    //重新统计能耗数据\r\n    countEnergyData() {\r\n      if (this.formData.reportTime.length == 10) {\r\n        this.formData.reportTime = this.formData.reportTime + \" 0:00:00\";\r\n      }\r\n      this.tableLoading = true;\r\n      this.tableData = [];\r\n      this.energyData = [];\r\n\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"正在重新计算能耗数据，请稍候...\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      reCountDataList(this.formData).then((res) => {\r\n        loading.close();\r\n        this.tableLoading = false;\r\n        if (res.data) {\r\n          this.tableData = res.data;\r\n          this.checkIndicatorInput();\r\n          // this.initTableData();\r\n          for (let i = 0; i < this.tableData.length; i++) {\r\n            let item = this.tableData[i];\r\n            item.unit = item.unitDescription + \"(\" + item.unitName + \")\";\r\n            item.$cellEdit = true;\r\n            if (item.groupData != null && item.groupData != \"/\") {\r\n              this.handleGroupChange(i);\r\n            }\r\n            if (item.stockData != null && item.stockData != \"/\") {\r\n              this.handleStockChange(i);\r\n            }\r\n            if (item.groupInputType == \"2\" && item.groupData == null) {\r\n              item[\"groupData\"] = \"\\\\\";\r\n            }\r\n            if (item.stockInputType == \"2\" && item.stockData == null) {\r\n              item[\"stockData\"] = \"\\\\\";\r\n            }\r\n            if (item.largeInputType == \"2\" && item.largeData == null) {\r\n              item[\"largeData\"] = \"\\\\\";\r\n            }\r\n            if (item.mediumInputType == \"2\" && item.mediumData == null) {\r\n              item[\"mediumData\"] = \"\\\\\";\r\n            }\r\n            if (item.mobileInputType == \"2\" && item.mobileData == null) {\r\n              item[\"mobileData\"] = \"\\\\\";\r\n            }\r\n            item.groupData = item.groupData == null ? \"\\\\\" : item.groupData;\r\n            item.stockData = item.stockData == null ? \"\\\\\" : item.stockData;\r\n            item.largeData = item.largeData == null ? \"\\\\\" : item.largeData;\r\n            item.mediumData = item.mediumData == null ? \"\\\\\" : item.mediumData;\r\n            item.mobileData = item.mobileData == null ? \"\\\\\" : item.mobileData;\r\n            this.autoFillDataItem(item);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    // 查询列表\r\n    getList() {\r\n      if (this.formData.reportTime.length == 10) {\r\n        this.formData.reportTime = this.formData.reportTime + \" 0:00:00\";\r\n      }\r\n      this.tableDataTime = this.formData.reportTime;\r\n      this.reportDialog.reportRate = 0;\r\n      this.reportDialog.count = 0;\r\n      this.reportDialog.applyReturnCount = 0;\r\n      this.unReportDialog.count = 0;\r\n      this.reportDialog.companyList = [];\r\n      this.unReportDialog.companyList = [];\r\n      this.reportCompanyList = [];\r\n      this.getReportList();\r\n      this.getRemindCountList(this.formData.reportTime);\r\n      this.tableLoading = true;\r\n      this.tableData = [];\r\n      this.energyData = [];\r\n      energyCalculateList(this.formData).then((res) => {\r\n        if (res.data) {\r\n          this.titleCompanyName = this.formData.companyName;\r\n          this.tableLoading = false;\r\n          if (res.data) {\r\n            this.tableData = res.data;\r\n            // if (res.data[0].reportFlag == '1') {\r\n            //   this.hasReported = true;\r\n            //   this.reportLabel = \"已上报集团\";\r\n            // } else {\r\n            //   this.hasReported = false;\r\n            //   this.reportLabel = \"未上报集团\";\r\n            // }\r\n            this.checkIndicatorInput();\r\n            // this.initTableData();\r\n            for (let i = 0; i < this.tableData.length; i++) {\r\n              let item = this.tableData[i];\r\n              item.unit = item.unitDescription + \"(\" + item.unitName + \")\";\r\n              item.$cellEdit = true;\r\n              if (item.stockData != null && item.stockData != \"/\") {\r\n                this.handleStockChange(i);\r\n              }\r\n              if (item.groupData != null && item.groupData != \"/\") {\r\n                this.handleGroupChange(i);\r\n              }\r\n              if (item.groupInputType == \"2\" && item.groupData == null) {\r\n                item[\"groupData\"] = \"\\\\\";\r\n              }\r\n              if (item.stockInputType == \"2\" && item.stockData == null) {\r\n                item[\"stockData\"] = \"\\\\\";\r\n              }\r\n              if (item.largeInputType == \"2\" && item.largeData == null) {\r\n                item[\"largeData\"] = \"\\\\\";\r\n              }\r\n              if (item.mediumInputType == \"2\" && item.mediumData == null) {\r\n                item[\"mediumData\"] = \"\\\\\";\r\n              }\r\n              if (item.mobileInputType == \"2\" && item.mobileData == null) {\r\n                item[\"mobileData\"] = \"\\\\\";\r\n              }\r\n              item.groupData = item.groupData == null ? \"\\\\\" : item.groupData;\r\n              item.stockData = item.stockData == null ? \"\\\\\" : item.stockData;\r\n              item.largeData = item.largeData == null ? \"\\\\\" : item.largeData;\r\n              item.mediumData = item.mediumData == null ? \"\\\\\" : item.mediumData;\r\n              item.mobileData = item.mobileData == null ? \"\\\\\" : item.mobileData;\r\n              this.autoFillDataItem(item);\r\n              // this.tableData.forEach((item) => {\r\n              //   item.unit = item.unitDescription + \"(\" + item.unitName + \")\";\r\n              //   item.$cellEdit = true;\r\n              // });\r\n            }\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /****\r\n     * 判断输入框类型，0-number，1-text\r\n     * @param index\r\n     */\r\n    judgeInputType(index, dataType) {\r\n      if (this.formulaList[dataType + \"List\"].indexOf(index) > -1) {\r\n        return \"number\";\r\n      }\r\n      return this.tableData[index][dataType + \"InputType\"] == \"2\" ? \"text\" : \"number\";\r\n    },\r\n    //根据指标名称确定输入框状态\r\n    checkIndicatorInput() {\r\n      Object.keys(this.backgroundList).forEach((key) => (this.backgroundList[key] = []));\r\n      Object.keys(this.formulaList).forEach((key) => (this.formulaList[key] = []));\r\n      for (let i = 0; i < this.tableData.length; i++) {\r\n        if (\"1、能源消费总量(吨标煤)\" == this.tableData[i].indicatorName) {\r\n          Object.keys(this.formulaList).forEach((key) => this.formulaList[key].push(i));\r\n        } else if (\"1.3、耗电量（总）(千瓦时)\" == this.tableData[i].indicatorName) {\r\n          Object.keys(this.formulaList).forEach((key) => this.formulaList[key].push(i));\r\n        } else if (\"1.3.1、生产用房耗电量(千瓦时)\" == this.tableData[i].indicatorName) {\r\n          this.formulaList.groupList.push(i);\r\n          this.formulaList.stockList.push(i);\r\n        } else if (\r\n          \"1.3.1.2、其中：基站耗电量(千瓦时)\" == this.tableData[i].indicatorName\r\n        ) {\r\n          //7\r\n          Object.keys(this.formulaList).forEach((key) => {\r\n            if (key != \"largeList\" && key != \"mediumList\") {\r\n              this.formulaList[key].push(i);\r\n            }\r\n          });\r\n        } else if (\r\n          \"1.3.1.2.1、其中：铁塔公司基站耗电量（包括室内分布、室外站等） (千瓦时)\" ==\r\n            this.tableData[i].indicatorName ||\r\n          \"1.3.1.2.2、其中：第三方租赁基站耗电量（包括室内分布、室外站等） (千瓦时)\" ==\r\n            this.tableData[i].indicatorName ||\r\n          \"1.3.1.2.3、其中：自有产权基站耗电量（包括室内分布、室外站等） (千瓦时)\" ==\r\n            this.tableData[i].indicatorName\r\n        ) {\r\n          //8 - 10\r\n          this.formulaList.mobileList.push(i);\r\n        } else if (\r\n          \"1.3.1.3、其中：数据中心耗电量(千瓦时)\" == this.tableData[i].indicatorName ||\r\n          \"1.3.1.3.1、其中：数据中心IT设备总耗电量（千瓦时）\" ==\r\n            this.tableData[i].indicatorName ||\r\n          \"1.3.1.3.2、其中：对外IDC机房耗电量(千瓦时)\" ==\r\n            this.tableData[i].indicatorName ||\r\n          \"1.3.1.3.2.1、其中：对外IDC机房IT设备耗电量（千瓦时)\" ==\r\n            this.tableData[i].indicatorName ||\r\n          \"1.3.1.3.3、其中：自用业务平台和IT支撑用房耗电量(千瓦时)\" ==\r\n            this.tableData[i].indicatorName ||\r\n          \"1.3.1.3.3.1、其中：自用业务平台IT设备耗电量（千瓦时）\" ==\r\n            this.tableData[i].indicatorName\r\n        ) {\r\n          //11 - 16\r\n          this.formulaList.mediumList.push(i);\r\n        } else if (\"1.3.2、非生产用房耗电量(千瓦时)\" == this.tableData[i].indicatorName) {\r\n          //18\r\n          this.formulaList.groupList.push(i);\r\n          this.formulaList.stockList.push(i);\r\n        } else if (\"1.5、汽油消耗量(升)\" == this.tableData[i].indicatorName) {\r\n          //23\r\n          Object.keys(this.formulaList).forEach((key) => {\r\n            if (key != \"mobileList\") {\r\n              this.formulaList[key].push(i);\r\n            }\r\n          });\r\n        } else if (\"1.7、柴油消耗量(升)\" == this.tableData[i].indicatorName) {\r\n          //27\r\n          Object.keys(this.formulaList).forEach((key) => {\r\n            if (key != \"mobileList\") {\r\n              this.formulaList[key].push(i);\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    // 编辑按钮\r\n    insertEvent() {\r\n      if (this.editDisabled == true) {\r\n        this.editDisabled = false;\r\n        this.editButtonLabel = \"保存\";\r\n      } else {\r\n        this.saveData();\r\n      }\r\n    },\r\n    //校验保存数据\r\n    validSaveData() {\r\n      let ret = false;\r\n      for (let i = 0; i < this.tableData.length; i++) {\r\n        if (\r\n          this.tableData[i].groupData != undefined &&\r\n          this.tableData[i].groupData != null &&\r\n          this.tableData[i].groupData.length > 0 &&\r\n          this.tableData[i].stockData != undefined &&\r\n          this.tableData[i].stockData != null &&\r\n          this.tableData[i].stockData.length > 0 &&\r\n          this.tableData[i].largeData != undefined &&\r\n          this.tableData[i].largeData != null &&\r\n          this.tableData[i].largeData.length > 0 &&\r\n          this.tableData[i].mediumData != undefined &&\r\n          this.tableData[i].mediumData != null &&\r\n          this.tableData[i].mediumData.length > 0 &&\r\n          this.tableData[i].mobileData != undefined &&\r\n          this.tableData[i].mobileData != null &&\r\n          this.tableData[i].mobileData.length > 0\r\n        ) {\r\n          ret = true;\r\n          break;\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n    //保存数据\r\n    saveData() {\r\n      if (this.validReportData() == false) {\r\n        this.alertError(\"请输入数据\");\r\n        return;\r\n      }\r\n      this.editDisabled = true;\r\n      this.editButtonLabel = \"编辑\";\r\n      if (this.formData.reportTime.length == 10) {\r\n        this.formData.reportTime = this.formData.reportTime + \" 0:00:00\";\r\n      }\r\n      let saveDataList = [];\r\n      this.tableData.forEach((item) => {\r\n        if (\r\n          item.groupData != undefined &&\r\n          item.groupData != null &&\r\n          item.groupData.length > 0 &&\r\n          item.stockData != undefined &&\r\n          item.stockData != null &&\r\n          item.stockData.length > 0 &&\r\n          item.largeData != undefined &&\r\n          item.largeData != null &&\r\n          item.largeData.length > 0 &&\r\n          item.mediumData != undefined &&\r\n          item.mediumData != null &&\r\n          item.mediumData.length > 0 &&\r\n          item.mobileData != undefined &&\r\n          item.mobileData != null &&\r\n          item.mobileData.length > 0\r\n        ) {\r\n          let saveItem = {};\r\n          saveItem.energyIndicatorId = item.id;\r\n          saveItem.reportTime = this.formData.reportTime;\r\n          saveItem.companyId = this.formData.companyId;\r\n          saveItem.reportFlag = \"0\";\r\n          saveItem.groupData =\r\n            item.groupData == \"\\\\\" || item.groupData == \"/\" ? null : item.groupData;\r\n          saveItem.stockData =\r\n            item.stockData == \"\\\\\" || item.stockData == \"/\" ? null : item.stockData;\r\n          saveItem.largeData =\r\n            item.largeData == \"\\\\\" || item.largeData == \"/\" ? null : item.largeData;\r\n          saveItem.mediumData =\r\n            item.mediumData == \"\\\\\" || item.mediumData == \"/\" ? null : item.mediumData;\r\n          saveItem.mobileData =\r\n            item.mobileData == \"\\\\\" || item.mobileData == \"/\" ? null : item.mobileData;\r\n          saveDataList.push(saveItem);\r\n        }\r\n      });\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"正在保存数据，请稍侯...\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      saveEnergyCalculateList(saveDataList).then((res) => {\r\n        loading.close();\r\n        if (res.data.code == 0) {\r\n          this.alertSuccess(\"操作成功\");\r\n        }\r\n      });\r\n    },\r\n\r\n    autoFillDataItem(item) {\r\n      if (\r\n        item.groupData == undefined ||\r\n        item.groupData == null ||\r\n        item.groupData == \"/\" ||\r\n        item.groupData.length == 0\r\n      ) {\r\n        if (item.groupInputType == \"0\" || item.groupInputType == \"1\") {\r\n          item.groupData = \"0\";\r\n        }\r\n      }\r\n      if (\r\n        item.stockData == undefined ||\r\n        item.stockData == null ||\r\n        item.stockData == \"/\" ||\r\n        item.stockData.length == 0\r\n      ) {\r\n        if (item.stockInputType == \"0\" || item.stockInputType == \"1\") {\r\n          item.stockData = \"0\";\r\n        }\r\n      }\r\n      if (\r\n        item.largeData == undefined ||\r\n        item.largeData == null ||\r\n        item.largeData == \"/\" ||\r\n        item.largeData.length == 0\r\n      ) {\r\n        if (item.largeInputType == \"0\" || item.largeInputType == \"1\") {\r\n          item.largeData = \"0\";\r\n        }\r\n      }\r\n      if (\r\n        item.mediumData == undefined ||\r\n        item.mediumData == null ||\r\n        item.mediumData == \"/\" ||\r\n        item.mediumData.length == 0\r\n      ) {\r\n        if (item.mediumInputType == \"0\" || item.mediumInputType == \"1\") {\r\n          item.mediumData = \"0\";\r\n        }\r\n      }\r\n      if (\r\n        item.mobileData == undefined ||\r\n        item.mobileData == null ||\r\n        item.mobileData == \"/\" ||\r\n        item.mobileData.length == 0\r\n      ) {\r\n        if (item.mobileInputType == \"0\" || item.mobileInputType == \"1\") {\r\n          item.mobileData = \"0\";\r\n        }\r\n      }\r\n    },\r\n    autoFillData() {\r\n      for (let i = 0; i < this.tableData.length; i++) {\r\n        if (\r\n          this.tableData[i].groupData == undefined ||\r\n          this.tableData[i].groupData == null ||\r\n          this.tableData[i].groupData == \"/\" ||\r\n          this.tableData[i].groupData.length == 0\r\n        ) {\r\n          if (\r\n            this.tableData[i].groupInputType == \"0\" ||\r\n            this.tableData[i].groupInputType == \"1\"\r\n          ) {\r\n            this.tableData[i].groupData = \"0\";\r\n          }\r\n        }\r\n        if (\r\n          this.tableData[i].stockData == undefined ||\r\n          this.tableData[i].stockData == null ||\r\n          this.tableData[i].stockData == \"/\" ||\r\n          this.tableData[i].stockData.length == 0\r\n        ) {\r\n          if (\r\n            this.tableData[i].stockInputType == \"0\" ||\r\n            this.tableData[i].stockInputType == \"1\"\r\n          ) {\r\n            this.tableData[i].stockData = \"0\";\r\n          }\r\n        }\r\n        if (\r\n          this.tableData[i].largeData == undefined ||\r\n          this.tableData[i].largeData == null ||\r\n          this.tableData[i].largeData == \"/\" ||\r\n          this.tableData[i].largeData.length == 0\r\n        ) {\r\n          if (\r\n            this.tableData[i].largeInputType == \"0\" ||\r\n            this.tableData[i].largeInputType == \"1\"\r\n          ) {\r\n            this.tableData[i].largeData = \"0\";\r\n          }\r\n        }\r\n        if (\r\n          this.tableData[i].mediumData == undefined ||\r\n          this.tableData[i].mediumData == null ||\r\n          this.tableData[i].mediumData == \"/\" ||\r\n          this.tableData[i].mediumData.length == 0\r\n        ) {\r\n          if (\r\n            this.tableData[i].mediumInputType == \"0\" ||\r\n            this.tableData[i].mediumInputType == \"1\"\r\n          ) {\r\n            this.tableData[i].mediumData = \"0\";\r\n          }\r\n        }\r\n        if (\r\n          this.tableData[i].mobileData == undefined ||\r\n          this.tableData[i].mobileData == null ||\r\n          this.tableData[i].mobileData == \"/\" ||\r\n          this.tableData[i].mobileData.length == 0\r\n        ) {\r\n          if (\r\n            this.tableData[i].mobileInputType == \"0\" ||\r\n            this.tableData[i].mobileInputType == \"1\"\r\n          ) {\r\n            this.tableData[i].mobileData = \"0\";\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //校验上报数据\r\n    validReportData() {\r\n      let ret = true;\r\n      Object.keys(this.backgroundList).forEach((key) => (this.backgroundList[key] = []));\r\n      for (let i = 0; i < this.tableData.length; i++) {\r\n        if (\r\n          this.tableData[i].groupData == undefined ||\r\n          this.tableData[i].groupData == null ||\r\n          this.tableData[i].groupData == \"/\" ||\r\n          this.tableData[i].groupData.length == 0\r\n        ) {\r\n          if (this.tableData[i].groupInputType == \"0\") {\r\n            this.backgroundList.groupList.push(i);\r\n            ret = false;\r\n          } else if (this.tableData[i].groupInputType == \"1\") {\r\n            this.tableData[i].groupData = \"0\";\r\n          }\r\n        }\r\n        if (\r\n          this.tableData[i].stockData == undefined ||\r\n          this.tableData[i].stockData == null ||\r\n          this.tableData[i].stockData == \"/\" ||\r\n          this.tableData[i].stockData.length == 0\r\n        ) {\r\n          if (this.tableData[i].stockInputType == \"0\") {\r\n            this.backgroundList.stockList.push(i);\r\n            ret = false;\r\n          } else if (this.tableData[i].stockInputType == \"1\") {\r\n            this.tableData[i].stockData = \"0\";\r\n          }\r\n        }\r\n        if (\r\n          this.tableData[i].largeData == undefined ||\r\n          this.tableData[i].largeData == null ||\r\n          this.tableData[i].largeData == \"/\" ||\r\n          this.tableData[i].largeData.length == 0\r\n        ) {\r\n          if (this.tableData[i].largeInputType == \"0\") {\r\n            this.backgroundList.largeList.push(i);\r\n            ret = false;\r\n          } else if (this.tableData[i].largeInputType == \"1\") {\r\n            this.tableData[i].largeData = \"0\";\r\n          }\r\n        }\r\n        if (\r\n          this.tableData[i].mediumData == undefined ||\r\n          this.tableData[i].mediumData == null ||\r\n          this.tableData[i].mediumData == \"/\" ||\r\n          this.tableData[i].mediumData.length == 0\r\n        ) {\r\n          if (this.tableData[i].mediumInputType == \"0\") {\r\n            this.backgroundList.mediumList.push(i);\r\n            ret = false;\r\n          } else if (this.tableData[i].mediumInputType == \"1\") {\r\n            this.tableData[i].mediumData = \"0\";\r\n          }\r\n        }\r\n        if (\r\n          this.tableData[i].mobileData == undefined ||\r\n          this.tableData[i].mobileData == null ||\r\n          this.tableData[i].mobileData == \"/\" ||\r\n          this.tableData[i].mobileData.length == 0\r\n        ) {\r\n          if (this.tableData[i].mobileInputType == \"0\") {\r\n            this.backgroundList.mobileList.push(i);\r\n            ret = false;\r\n          } else if (this.tableData[i].mobileInputType == \"1\") {\r\n            this.tableData[i].mobileData = \"0\";\r\n          }\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n    // 上报数据\r\n    async reportData() {\r\n      if (this.validReportData() == false) {\r\n        this.alertError(\"请输入全部数据\");\r\n        return;\r\n      }\r\n      this.confirmDialog.content = \"是否上报\";\r\n      if (this.formData.companyName) {\r\n        this.confirmDialog.content += this.formData.companyName;\r\n      }\r\n      this.confirmDialog.content +=\r\n        this.formData.reportTime.slice(0, 4) +\r\n        \"年\" +\r\n        this.formData.reportTime.slice(5, 7) +\r\n        \"月填报数据?\";\r\n      this.confirmDialog.visible = true;\r\n    },\r\n    saveReportData() {\r\n      let saveList = [];\r\n      if (this.formData.reportTime.length == 10) {\r\n        this.formData.reportTime = this.formData.reportTime + \" 0:00:00\";\r\n      }\r\n      this.tableData.forEach((item) => {\r\n        let saveItem = {};\r\n        saveItem.reportTime = this.formData.reportTime;\r\n        saveItem.companyId = this.formData.companyId;\r\n        saveItem.reportFlag = \"2\";\r\n        saveItem.id = item.id;\r\n        saveItem.indicatorName = item.indicatorName;\r\n        saveItem.groupData =\r\n          item.groupData == \"\\\\\" || item.groupData == \"/\" ? null : item.groupData;\r\n        saveItem.stockData =\r\n          item.stockData == \"\\\\\" || item.stockData == \"/\" ? null : item.stockData;\r\n        saveItem.largeData =\r\n          item.largeData == \"\\\\\" || item.largeData == \"/\" ? null : item.largeData;\r\n        saveItem.mediumData =\r\n          item.mediumData == \"\\\\\" || item.mediumData == \"/\" ? null : item.mediumData;\r\n        saveItem.mobileData =\r\n          item.mobileData == \"\\\\\" || item.mobileData == \"/\" ? null : item.mobileData;\r\n        saveList.push(saveItem);\r\n      });\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"保存数据中，请稍候...\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      energySaveList(saveList).then((res) => {\r\n        loading.close();\r\n        if (res.data.code == 0) {\r\n          this.confirmDialog.visible = false;\r\n          this.alertSuccess(\"操作成功\");\r\n          this.initParams();\r\n          this.getList();\r\n        }\r\n      });\r\n    },\r\n    getEnergyDataById(id) {\r\n      let ret = undefined;\r\n      for (let i = 0; i < this.energyData.length; i++) {\r\n        if (id == this.energyData[i].energyIndicatorId) {\r\n          ret = this.energyData[i];\r\n          return ret;\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n    initTableData() {\r\n      for (let i = 0; i < this.tableData.length; i++) {\r\n        if (this.tableData[i].groupInputType == \"2\") {\r\n          this.tableData[i][\"groupData\"] = \"\\\\\";\r\n        }\r\n        if (this.tableData[i].stockInputType == \"2\") {\r\n          this.tableData[i][\"stockData\"] = \"\\\\\";\r\n        }\r\n        if (this.tableData[i].largeInputType == \"2\") {\r\n          this.tableData[i][\"largeData\"] = \"\\\\\";\r\n        }\r\n        if (this.tableData[i].mediumInputType == \"2\") {\r\n          this.tableData[i][\"mediumData\"] = \"\\\\\";\r\n        }\r\n        if (this.tableData[i].mobileInputType == \"2\") {\r\n          this.tableData[i][\"mobileData\"] = \"\\\\\";\r\n        }\r\n      }\r\n    },\r\n    formatDisplayData(value) {\r\n      let data = parseFloat(value);\r\n      if (isNaN(value)) {\r\n        return value;\r\n      } else {\r\n        return data.toFixed(2);\r\n      }\r\n    },\r\n    downTemplate() {\r\n      // const loading = this.$loading({\r\n      //   lock: true,\r\n      //   text: \"导出中\",\r\n      //   spinner: \"el-icon-loading\",\r\n      //   background: \"rgba(0, 0, 0, 0.7)\",\r\n      // });\r\n      downloadTemplate().then((res) => {\r\n        // downloadFile(res, \"能源数据填报模板.xlsx\");\r\n        const content = res;\r\n        const blob = new Blob([content]);\r\n        const fileName = \"能源数据填报模板\" + \".xlsx\";\r\n        if (\"download\" in document.createElement(\"a\")) {\r\n          // 非IE下载\r\n          const elink = document.createElement(\"a\");\r\n          elink.download = fileName;\r\n          elink.style.display = \"none\";\r\n          elink.href = URL.createObjectURL(blob);\r\n          document.body.appendChild(elink);\r\n          elink.click();\r\n          URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n          document.body.removeChild(elink);\r\n        } else {\r\n          // IE10+下载\r\n          navigator.msSaveBlob(blob, fileName);\r\n        }\r\n        // loading.close();\r\n      });\r\n    },\r\n    rejectDialogClose() {\r\n      this.rejectDialog.visible = false;\r\n    },\r\n    // 获取单价维护\r\n    getEnergyPrice() {\r\n      this.priceDialog.queryParams.reportTime = this.formData.reportTime;\r\n      Object.keys(this.priceDialog.formData).forEach((key) => {\r\n        this.priceDialog.formData[key] = undefined;\r\n      });\r\n      this.priceDialog.formData.reportTime = this.formData.reportTime;\r\n      priceDetail(this.priceDialog.queryParams).then((res) => {\r\n        if (res.data.id) {\r\n          Object.keys(this.priceDialog.formData).forEach((key) => {\r\n            this.priceDialog.formData[key] = res.data[key];\r\n          });\r\n        }\r\n      });\r\n    },\r\n    handleDownClick() {\r\n      this.downDialog.visible = true;\r\n    },\r\n    handlePriceClick() {\r\n      this.getEnergyPrice();\r\n      this.priceDialog.visible = true;\r\n    },\r\n    handleSaveEnergyPrice() {\r\n      if (this.priceDialog.formData.reportTime != this.formData.reportTime) {\r\n        this.priceDialog.formData.id = undefined;\r\n        this.priceDialog.formData.reportTime = this.formData.reportTime;\r\n      }\r\n      if (this.priceDialog.formData.id) {\r\n        //更新\r\n        putPrice(this.priceDialog.formData).then((res) => {\r\n          if (res.data) {\r\n            this.priceDialog.visible = false;\r\n            this.alertSuccess(\"保存成功。\");\r\n          }\r\n        });\r\n      } else {\r\n        //新增\r\n        addPrice(this.priceDialog.formData).then((res) => {\r\n          if (res.data) {\r\n            this.priceDialog.visible = false;\r\n            this.alertSuccess(\"保存成功。\");\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 上报到集团日志\r\n    reportToGroupLog() {\r\n      this.reportLogDialog.visible = true;\r\n    },\r\n    // 上报到集团\r\n    reportToGroup() {\r\n      let currentDate = new Date(); // 获取当前日期时间戳\r\n      let reportTime = new Date(this.formData.reportTime).getTime(); // 获取报告日期时间戳\r\n      let lastDays =\r\n        new Date(currentDate.getFullYear(), currentDate.getMonth(), 0).getDate() + 10;\r\n      let oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数\r\n      let diffDays = Math.abs((currentDate.getTime() - reportTime) / oneDay); // 两个日期时间戳差值除以一天的毫秒数得到相差天数\r\n      // if (diffDays > lastDays) {\r\n      //   this.alertError(\"请于每月1-10号上报数据\");\r\n      //   return;\r\n      // }\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"正在上报数据，请稍侯...\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      reportToGroup({ reportTime: this.formData.reportTime, countType: \"1\" }).then(\r\n        (res) => {\r\n          loading.close();\r\n          if (res.data.code == 0) {\r\n            this.alertSuccess(\"操作成功\");\r\n          }\r\n        }\r\n      );\r\n    },\r\n    companyChange(key) {\r\n      if (key && key.item) {\r\n        this.formData.companyName = key.item.label;\r\n      }\r\n    },\r\n    handleReportCompany() {\r\n      this.reportDialog.companyList.forEach((item) => {\r\n        item.remindCount = this.getCompanyRemindCount(item.companyId);\r\n      });\r\n      this.reportDialog.visible = true;\r\n    },\r\n    handleUnReportCompany() {\r\n      this.unReportDialog.companyList.forEach((item) => {\r\n        item.remindCount = this.getCompanyRemindCount(item.companyId);\r\n      });\r\n      this.unReportDialog.remindCompanyList = [];\r\n      this.unReportDialog.visible = true;\r\n    },\r\n    // 获取提醒列表\r\n    getRemindCountList(reportTime) {\r\n      getRemindCount(Object.assign({ reportTime: reportTime })).then((res) => {\r\n        if (res.data) {\r\n          this.companyRemindList = res.data;\r\n        }\r\n      });\r\n    },\r\n    getCompanyRemindCount(companyId) {\r\n      let ret = 0;\r\n      for (let i = 0; i < this.companyRemindList.length; i++) {\r\n        if (companyId == this.companyRemindList[i].companyId) {\r\n          ret = this.companyRemindList[i].remindCount;\r\n          return ret;\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n    // 查询上报情况列表\r\n    getReportList() {\r\n      reportList(this.formData).then((res) => {\r\n        if (res.data) {\r\n          for (let i = 0; i < res.data.length; i++) {\r\n            if (res.data[i].reportStatus.indexOf(\"已上报\") > -1) {\r\n              this.reportDialog.count += 1;\r\n              if (res.data[i].reportFlag == \"4\") {\r\n                this.reportDialog.applyReturnCount += 1;\r\n              }\r\n              this.reportDialog.companyList.push({\r\n                companyId: res.data[i].companyId,\r\n                companyName: res.data[i].companyName,\r\n                createTime: res.data[i].createTime,\r\n                reportTime: this.formData.reportTime,\r\n                reportFlag: res.data[i].reportFlag,\r\n                applyReason: res.data[i].applyReason,\r\n                returnReason: res.data[i].returnReason,\r\n                recordCount: res.data[i].recordCount,\r\n                remindCount: 0,\r\n              });\r\n            } else if (res.data[i].reportStatus.indexOf(\"未上报\") > -1) {\r\n              this.unReportDialog.count += 1;\r\n              this.unReportDialog.companyList.push({\r\n                companyId: res.data[i].companyId,\r\n                reportTime: this.formData.reportTime,\r\n                companyName: res.data[i].companyName,\r\n                remindCount: 0,\r\n              });\r\n            }\r\n          }\r\n          this.reportDialog.companyList.sort(function (a, b) {\r\n            return b.createTime > a.createTime ? -1 : 1;\r\n          });\r\n          this.reportDialog.reportRate = (\r\n            (this.reportDialog.count * 100) /\r\n            (this.reportDialog.count + this.unReportDialog.count)\r\n          ).toFixed(2);\r\n        }\r\n      });\r\n    },\r\n    initUserInfo() {\r\n      let that = this;\r\n      getUserByUserRole().then((res) => {\r\n        //根据权限获取分公司\r\n        if (res.data) {\r\n          that.userInfo.companyName = res.data.companies[0].name;\r\n          that.userInfo.deptId = res.data.companies[0].id;\r\n          that.userInfo.isAdmin = res.data.isCityAdmin || res.data.isSubAdmin;\r\n          res.data.companies.forEach((item) => {\r\n            this.companyOption.push({ value: item.id, label: item.name });\r\n          });\r\n          this.companyOption.unshift({\r\n            value: undefined,\r\n            label: \"全省汇总\"\r\n          });\r\n          if (this.companyOption && this.companyOption.length > 0) {\r\n            this.formData.companyId = this.companyOption[0].value;\r\n            this.getList();\r\n          }\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\np {\r\n  margin: 0;\r\n}\r\n/deep/.avue-crud__menu {\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n.curd-header {\r\n  width: 100%;\r\n  height: 50px;\r\n  margin-bottom: 15px;\r\n  display: flex;\r\n  background: #f6f8fa;\r\n  color: #303b50;\r\n  position: relative;\r\n  & > .reporting {\r\n    width: 690px;\r\n    display: flex;\r\n    margin-left: 10px;\r\n    align-items: center;\r\n    p {\r\n      font-size: 14px;\r\n      line-height: 50px;\r\n      letter-spacing: 1px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      font-weight: 400;\r\n    }\r\n    /deep/.el-date-editor.el-input,\r\n    .el-date-editor.el-input__inner {\r\n      width: 20rem !important;\r\n    }\r\n  }\r\n  & > .companyName {\r\n    font-size: 14px;\r\n    line-height: 50px;\r\n    letter-spacing: 1px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    font-weight: 400;\r\n    color: #00ecc0;\r\n  }\r\n  & > .reportLabel {\r\n    width: 610px;\r\n    height: 23px;\r\n    border-radius: 0px 0px 0px 8px;\r\n    position: absolute;\r\n    right: 0;\r\n    top: 0;\r\n    font-size: 12px;\r\n    text-align: center;\r\n    line-height: 23px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    font-weight: 400;\r\n    color: #1a3854;\r\n  }\r\n}\r\n\r\n.total-card-bg-green {\r\n  background: url(\"../../../../assets/carbon/wild_green_rectangle.png\");\r\n}\r\n\r\n.total-card-bg-yellow {\r\n  background: url(\"../../../../assets/carbon/summer_yellow_rectangle.png\");\r\n}\r\n\r\n.total-card-bg-red {\r\n  background: url(\"../../../../assets/carbon/maiden_red_rectangle.png\");\r\n}\r\n\r\n.total-card-bg-violet {\r\n  background: url(\"../../../../assets/carbon/violet_rectangle.png\");\r\n}\r\n\r\n.total-card-bg-blue {\r\n  background: url(\"../../../../assets/carbon/sky_blue_rectangle.png\");\r\n}\r\n.total-card {\r\n  background-size: 100% 100%;\r\n  margin-right: 1rem;\r\n\r\n  & > .number-type {\r\n    font-size: 1rem;\r\n    font-weight: bold;\r\n    font-family: HarmonyOS_Sans_Black;\r\n    color: #ffffff;\r\n    padding: 0.5rem 0 0.7rem 1.5rem;\r\n  }\r\n\r\n  & > .text-type {\r\n    font-size: 1rem;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    color: #ffffff;\r\n    letter-spacing: 0.1rem;\r\n    padding: 0 0 0.6rem 1rem;\r\n  }\r\n}\r\n\r\n.total-card-row {\r\n  margin: 0;\r\n  margin: 0 10px 10px 10px;\r\n  .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n.el_input_class {\r\n  /deep/.el-input__inner {\r\n    background: #c78f08;\r\n  }\r\n}\r\n.el-input.is-disabled /deep/ .el-input__inner {\r\n  background: #f2f2f2 !important;\r\n  color: #303b50 !important;\r\n}\r\n::v-deep input::-webkit-outer-spin-button,\r\n::v-deep input::-webkit-inner-spin-button {\r\n  -webkit-appearance: none !important;\r\n}\r\n::v-deep input[type=\"number\"] {\r\n  -moz-appearance: textfield !important;\r\n}\r\n.page-class {\r\n  background: #fff;\r\n  padding: 10px 20px 0 20px;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .form-head {\r\n    margin-bottom: 10px;\r\n  }\r\n  .avue-crud {\r\n    flex: 1;\r\n  }\r\n}\r\n</style>\r\n"]}]}