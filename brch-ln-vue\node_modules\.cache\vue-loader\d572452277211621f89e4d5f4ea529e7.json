{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\business\\mssAccountbill\\mssPreSupplierAccountBillStatement.vue?vue&type=style&index=0&id=f039c752&lang=less&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\business\\mssAccountbill\\mssPreSupplierAccountBillStatement.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5teXRhYmxlIC5pdnUtdGFibGUtY2VsbHsKICAgIHBhZGRpbmctbGVmdDogNXB4OwogICAgcGFkZGluZy1yaWdodDogNXB4OwogICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwogICAgd2hpdGUtc3BhY2U6IG5vcm1hbDsKICAgIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsKICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7Cn0KCi5hY2NvdW50RXMgLmZpbHRlci1kaXZpZGVyIHsKICAgIG1hcmdpbjogMHB4OwogICAgdGV4dC1hbGlnbjogY2VudGVyOwp9Ci5hY2NvdW50RXMgLmhlYWRlci1iYXItc2hvdyB7CiAgICBtYXgtaGVpZ2h0OiAzMDBweDsKICAgIHBhZGRpbmctdG9wOiAxNHB4OwogICAgb3ZlcmZsb3c6IGluaGVyaXQ7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZWFlYzsKfQouYWNjb3VudEVzIC5oZWFkZXItYmFyLWhpZGUgewogICAgbWF4LWhlaWdodDogMDsKICAgIHBhZGRpbmctdG9wOiAwOwogICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIGJvcmRlci1ib3R0b206IDA7Cn0KCgoubXl0YWJsZSAubXlzcGFuewogICAgd2lkdGg6IDEwMCU7CiAgICBoZWlnaHQ6IDIwcHg7CiAgICBkaXNwbGF5OmJsb2NrCn0KLm15dGFibGUgLmVycm9yU3RsZXsKICAgIHdpZHRoOiAxMDAlOwogICAgaGVpZ2h0OiAyMHB4OwogICAgZGlzcGxheTpibG9jazsKICAgIGNvbG9yOnJlZDsKfQo="}, {"version": 3, "sources": ["mssPreSupplierAccountBillStatement.vue"], "names": [], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "mssPreSupplierAccountBillStatement.vue", "sourceRoot": "src/view/business/mssAccountbill", "sourcesContent": ["<style lang=\"less\">\r\n    .mytable .ivu-table-cell{\r\n        padding-left: 5px;\r\n        padding-right: 5px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: normal;\r\n        word-break: break-all;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .accountEs .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .accountEs .header-bar-show {\r\n        max-height: 300px;\r\n        padding-top: 14px;\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .accountEs .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n\r\n\r\n    .mytable .myspan{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block\r\n    }\r\n    .mytable .errorStle{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block;\r\n        color:red;\r\n    }\r\n</style>\r\n<template>\r\n    <div>\r\n        <div class=\"accountEs\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"关键字:\" prop=\"keySupplierWord\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"accountObj.keySupplierWord\" placeholder=\"供应商名称关键字模糊查询\" :style=\"formItemWidth\" />\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"12\">\r\n                            <FormItem label=\"时间周期：\" prop=\"startDate\" class=\"form-line-height\">\r\n                                <DatePicker :value=\"accountObj.startDate\" type=\"month\" @on-change='accountObj.startDate = $event'\r\n                                            placeholder=\"---年---月\" format=\"yyyyMM\" :style=\"formItemWidth\"></DatePicker>\r\n                                <span class=\"range-connector\">-</span>\r\n                                <DatePicker :value=\"accountObj.endDate\" type=\"month\" @on-change='accountObj.endDate = $event'\r\n                                            placeholder=\"---年---月\" format=\"yyyyMM\" :style=\"formItemWidth\"></DatePicker>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <div align=\"right\">\r\n                        <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n                        <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n                        <Button type=\"default\" icon=\"ios-redo\" @click=\"exportExcel()\">导出</Button>\r\n                    </div>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <div>\r\n                <Row>\r\n                    <Col span=\"12\">\r\n                        <Page size=\"small\" :total=\"pageTotal\" :current=\"pageNum\" :page-size=\"pageSize\" show-elevator show-sizer show-total\r\n                              placement=\"top\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n                    </Col>\r\n                </Row>\r\n            </div>\r\n            <Table ref=\"accountEsTable\"\r\n                   border\r\n                   :columns=\"tbAccount.tailColumn\"\r\n                   :data=\"tbAccount.data\"\r\n                   class=\"mytable\">\r\n            </Table>\r\n            <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    import checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\n    import checkResult from \"@/view/account/check/checkResult\";\r\n    import alarmCheck from \"@/view/account/check/alarmCheck\";\r\n    import {getNewDate,} from '@/view/account/powerAccountHelper';\r\n    import axios from '@/libs/api.request';\r\n    import {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\n    import indexData from '@/config/index'\r\n    import CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import {getUserByUserRole,getCountrysdata,getCountryByUserId} from '@/api/basedata/ammeter.js'\r\n    let dates=getNewDate();\r\n    export default {\r\n        name: 'mssPreSupplierAccountBillStatement',\r\n        components: {alarmCheck, checkResult, checkResultAndResponse,CountryModal},\r\n        data() {\r\n            return {\r\n                showCheckModel:false,\r\n                showJhModel:false,\r\n                showAlarmModel:false,\r\n                formItemWidth: widthstyle,\r\n                version:'',\r\n                filterColl: true,//搜索面板展开\r\n                editIndex: -1,//当前编辑行\r\n                columnsIndex:-1,//当前编辑列\r\n                myStyle:[],//样式\r\n                spinShow:false,//遮罩\r\n                isAdmin:false,\r\n                accountObj:{\r\n                    exportType:2,\r\n                    keySupplierWord:null,\r\n                    startDate:dates[0].code,//期号,默认当前月\r\n                    endDate:dates[1].code,//期号,默认当前月\r\n                },\r\n                tbAccount: {\r\n                    loading: true,\r\n                    columns: [],\r\n                    data: [],\r\n                    tailColumn: [\r\n                        {\r\n                            title: \"序号\",\r\n                            key: \"accountNo\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"供应商名称\",\r\n                            key: \"supplierName\",\r\n                            align: \"center\",\r\n                            width: 240,\r\n                        },\r\n                        {\r\n                            title: \"所属公司\",\r\n                            key: \"company\",\r\n                            align: \"center\",\r\n                            width: 240,\r\n                        },\r\n                        {\r\n                            title: \"预付金额（元）\",\r\n                            key: \"preMoney\",\r\n                            align: \"center\",\r\n                            width: 240,\r\n                        },\r\n                        {\r\n                            title: \"已挑对金额（元）\",\r\n                            key: \"checkMoney\",\r\n                            align: \"center\",\r\n                            width: 240,\r\n                        },\r\n                        {\r\n                            title: \"差额（元）\",\r\n                            key: \"balanceMoney\",\r\n                            align: \"center\",\r\n                            width: 240,\r\n                        },\r\n                    ],\r\n                },\r\n                pageTotal: 0,\r\n                pageNum: 1,\r\n                pageSize: 10,//当前页\r\n            }\r\n        },\r\n        methods: {\r\n            searchList(){\r\n                if(this.accountObj.countryName == \"\"){\r\n                    this.accountObj.country = \"-1\";\r\n                }\r\n                this.pageNum = 1\r\n                this.getAccountMessages()\r\n            },\r\n            accountnoChange(){\r\n                // this.searchList()\r\n            },\r\n            startChange(year) {\r\n                this.accountObj.startDate = year\r\n            },\r\n            endChange(year) {\r\n                this.accountObj.endDate = year\r\n            },\r\n            //验证错误弹出提示框\r\n            errorTips(str){\r\n                this.$Notice.error({\r\n                    title: '提示',\r\n                    desc: str,\r\n                    duration: 10\r\n                });\r\n            },\r\n            _onSearchHandle(){\r\n                this.isDisable=true\r\n                setTimeout(()=>{\r\n                    this.isDisable=false   //点击一次时隔两秒后才能再次点击\r\n                },12000);\r\n                this.getAccountMessages();\r\n            },\r\n            handlePage(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                this.pageNum = value;\r\n                this.getAccountMessages();\r\n            },\r\n            handlePageSize(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                this.pageSize = value;\r\n                this.getAccountMessages();\r\n            },\r\n            //向后台请求数据\r\n            getAccountMessages() {\r\n                let postData = this.accountObj;\r\n                postData.pageNum = this.pageNum;\r\n                postData.pageSize = this.pageSize;\r\n                let req = {\r\n                    url : \"/mssaccount/mssAccountbill/list/PreAccountBill\",\r\n                    method : \"get\",\r\n                    params : postData\r\n                };\r\n                this.tbAccount.loading = true\r\n                axios.request(req).then(res => {\r\n                    this.tbAccount.loading = false\r\n                    if (res.data) {\r\n                        let data = res.data.rows;\r\n                        data.forEach(function (item) {\r\n                            item.editType = 0;\r\n                        })\r\n                        this.tbAccount.data = data;\r\n                        this.pageTotal = res.data.total || 0;\r\n                        this.setMyStyle(this.tbAccount.data.length);\r\n\r\n                        this.editIndex = -1;\r\n                        this.columnsIndex = -1;\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            },\r\n            //重置\r\n            onResetHandle(){\r\n                this.accountObj = {\r\n                    startDate:null,\r\n                    endDate:null,\r\n                    exportType:2,\r\n                    company:this.company,\r\n                    heatUseBody:null,\r\n                    country:Number(this.country),\r\n                };\r\n                this.getAccountMessages()\r\n            },\r\n            refresh(){\r\n                let obj = this;\r\n                setTimeout(function () {\r\n                    obj.getAccountMessages()\r\n                },200);\r\n            },\r\n            ellipsis (value) {\r\n                if (!value) return ''\r\n                if (value.length > 3) {\r\n                    return value.slice(0,3) + '...'\r\n                }\r\n                return value\r\n            },\r\n            exportExcel() {\r\n                let params = this.accountObj;\r\n                let req = {\r\n                    url: \"/mssaccount/mssAccountbill/export/accountBill\",\r\n                    method: \"get\",\r\n                    params: params\r\n                };\r\n                this.spinShow = true\r\n                axios.file(req).then(res => {\r\n                    this.spinShow = false\r\n                    const content = res\r\n                    const blob = new Blob([content])\r\n                    const fileName = '预付管理-预付报表'+'.xlsx';\r\n                    if ('download' in document.createElement('a')) { // 非IE下载\r\n                        const elink = document.createElement('a')\r\n                        elink.download = fileName\r\n                        elink.style.display = 'none'\r\n                        elink.href = URL.createObjectURL(blob)\r\n                        document.body.appendChild(elink)\r\n                        elink.click()\r\n                        URL.revokeObjectURL(elink.href) // 释放URL 对象\r\n                        document.body.removeChild(elink)\r\n                    } else { // IE10+下载\r\n                        navigator.msSaveBlob(blob, fileName)\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            }\r\n        },\r\n        mounted() {\r\n            this.version = indexData.version\r\n            this.tbAccount.columns = this.tbAccount.tailColumn;\r\n            let that = this\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                });\r\n            });\r\n            this._onSearchHandle();\r\n        }\r\n    }\r\n</script>\r\n"]}]}