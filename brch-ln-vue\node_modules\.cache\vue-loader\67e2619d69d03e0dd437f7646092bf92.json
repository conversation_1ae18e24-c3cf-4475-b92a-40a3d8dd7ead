{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\sc\\addPylonAccountSC.vue?vue&type=template&id=18a88480&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\sc\\addPylonAccountSC.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}