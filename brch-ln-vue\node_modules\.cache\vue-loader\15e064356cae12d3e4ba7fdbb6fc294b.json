{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\energyview\\calculate.vue?vue&type=template&id=658bb542&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\energyview\\calculate.vue", "mtime": 1754285403041}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}