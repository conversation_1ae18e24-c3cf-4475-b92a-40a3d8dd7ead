{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\quota\\listQuota.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\quota\\listQuota.vue", "mtime": 1754285403021}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": 1741317456427}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXM2Lm51bWJlci5jb25zdHJ1Y3RvciI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzNi5vYmplY3QuYXNzaWduIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXM2LmZ1bmN0aW9uLm5hbWUiOwppbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tICJFOlxcY2wtcHJvamVjdFxcbG4tbmVuZ2hhb1xcYnJjaC1sbi12dWVcXG5vZGVfbW9kdWxlc1xcQGJhYmVsXFxydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZCI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzNy5zeW1ib2wuYXN5bmMtaXRlcmF0b3IiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lczYuc3ltYm9sIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS5pdGVyYWJsZSI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCmltcG9ydCB7IGdldFVzZXJCeVVzZXJSb2xlLCBnZXRDb3VudHJ5QnlVc2VySWQsIGdldFVzZXJkYXRhLCBnZXRDb3VudHJ5c2RhdGEgfSBmcm9tICdAL2FwaS9iYXNlZGF0YS9hbW1ldGVyLmpzJzsKaW1wb3J0IHsgaXNJblRvZG9MaXN0IH0gZnJvbSAiQC9hcGkvYWxlcnRjb250cm9sL2FsZXJ0Y29udHJvbCI7CmltcG9ydCB7IHJlbW92ZVF1b3RhIGFzIF9yZW1vdmVRdW90YSwgbGlzdFF1b3RhLCBjaGVja1N0YXJ0RmxvdyB9IGZyb20gJ0AvYXBpL2Jhc2VkYXRhL3F1b3RhL3F1b3RhLmpzJzsKaW1wb3J0IHsgYmxpc3QsIGJ0ZXh0IH0gZnJvbSAiQC9saWJzL3Rvb2xzIjsKaW1wb3J0IHZpZXdRdW90YVBhZ2UgZnJvbSAnLi92aWV3UXVvdGEudnVlJzsKaW1wb3J0IGNvdW50cnlNb2RhbCBmcm9tICJAL3ZpZXcvYmFzZWRhdGEvYW1tZXRlci9jb3VudHJ5TW9kYWwiOwppbXBvcnQgUHJvY2Vzc0luZm8gZnJvbSAnQC92aWV3L2Jhc2ljL3N5c3RlbS93b3JrZmxvdy9wcm9jZXNzLWluZm8nOwppbXBvcnQgV29ya0Zsb3dJbmZvQ29tcG9uZXQgZnJvbSAnQC92aWV3L2Jhc2ljL3N5c3RlbS93b3JrZmxvdy93b3JrRmxvd0luZm9Db21wb25ldCc7CmltcG9ydCBheGlvcyBmcm9tICdAL2xpYnMvYXBpLnJlcXVlc3QnOwppbXBvcnQgZXhjZWwgZnJvbSAnQC9saWJzL2V4Y2VsJzsKaW1wb3J0IHsgbWFwTXV0YXRpb25zIH0gZnJvbSAidnVleCI7CmltcG9ydCByb3V0ZXJzIGZyb20gJ0Avcm91dGVyL3JvdXRlcnMnOwppbXBvcnQgeyBnZXRIb21lUm91dGUgfSBmcm9tICdAL2xpYnMvdXRpbCc7CmltcG9ydCBpbmRleERhdGEgZnJvbSAnQC9jb25maWcvaW5kZXgnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ3F1b3RhJywKICBjb21wb25lbnRzOiB7CiAgICBQcm9jZXNzSW5mbzogUHJvY2Vzc0luZm8sCiAgICBXb3JrRmxvd0luZm9Db21wb25ldDogV29ya0Zsb3dJbmZvQ29tcG9uZXQsCiAgICB2aWV3UXVvdGFQYWdlOiB2aWV3UXVvdGFQYWdlLAogICAgY291bnRyeU1vZGFsOiBjb3VudHJ5TW9kYWwKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwoKICAgIC8v5p+l55yL6K+m5oOFCiAgICB2YXIgcmVudGVyVmlld1F1b3RhID0gZnVuY3Rpb24gcmVudGVyVmlld1F1b3RhKGgsIHBhcmFtcykgewogICAgICB2YXIgY29sdW1uID0gcGFyYW1zLmNvbHVtbi5rZXk7CiAgICAgIHJldHVybiBoKCJkaXYiLCBbaCgidSIsIHsKICAgICAgICBvbjogewogICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICAgICAgICBfdGhpcy52aWV3UXVvdGEocGFyYW1zLnJvdyk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCBwYXJhbXMucm93W2NvbHVtbl0pXSk7CiAgICB9OwoKICAgIHZhciByZW5kZXJXID0gZnVuY3Rpb24gcmVuZGVyVyhoLCBwYXJhbXMpIHsKICAgICAgdmFyIHRoYXQgPSBfdGhpczsKICAgICAgdmFyIHRleHQsCiAgICAgICAgICB0eXBlID0gIiI7CgogICAgICBpZiAocGFyYW1zLnJvdy5iaWxsU3RhdHVzICE9IDAgJiYgcGFyYW1zLnJvdy5iaWxsU3RhdHVzICE9IDMgJiYgcGFyYW1zLnJvdy5wcm9jZXNzaW5zdElkICE9IG51bGwpIHsKICAgICAgICB0ZXh0ID0gIuafpeeciyI7CiAgICAgICAgdHlwZSA9ICJzdWNjZXNzIjsKICAgICAgfSBlbHNlIGlmIChwYXJhbXMucm93LmJpbGxTdGF0dXMgPT0gMCB8fCBwYXJhbXMucm93LmJpbGxTdGF0dXMgPT0gMykgewogICAgICAgIHRleHQgPSAi5o+Q5LqkIjsKICAgICAgICB0eXBlID0gInByaW1hcnkiOwogICAgICB9CgogICAgICBpZiAodHlwZSA9PSAiIikgewogICAgICAgIHJldHVybiBoKCJkaXYiLCB7fSwgdGV4dCk7CiAgICAgIH0KCiAgICAgIHJldHVybiBoKCJCdXR0b24iLCB7CiAgICAgICAgcHJvcHM6IHsKICAgICAgICAgIHR5cGU6IHR5cGUsCiAgICAgICAgICBzaXplOiAic21hbGwiCiAgICAgICAgfSwKICAgICAgICBvbjogewogICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICAgICAgICBpZiAocGFyYW1zLnJvdy5iaWxsU3RhdHVzICE9IDAgJiYgcGFyYW1zLnJvdy5iaWxsU3RhdHVzICE9IDMgJiYgcGFyYW1zLnJvdy5wcm9jZXNzaW5zdElkICE9IG51bGwpIHsKICAgICAgICAgICAgICB0aGF0LnNob3dGbG93KHBhcmFtcy5yb3csIHBhcmFtcy5yb3cucHJvY2Vzc2luc3RJZCk7CiAgICAgICAgICAgIH0gZWxzZSBpZiAocGFyYW1zLnJvdy5iaWxsU3RhdHVzID09IDAgfHwgcGFyYW1zLnJvdy5iaWxsU3RhdHVzID09IDMpIHsKICAgICAgICAgICAgICB0aGF0LmxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgICAgIHRoYXQuc3RhcnRGbG93KHBhcmFtcy5yb3cpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LCB0ZXh0KTsKICAgIH07IC8v5Y2V5o2u54q25oCBCgoKICAgIHZhciByZW5kZXJCaWxsU3RhdHVzID0gZnVuY3Rpb24gcmVuZGVyQmlsbFN0YXR1cyhoLCBwYXJhbXMpIHsKICAgICAgdmFyIHZhbHVlID0gIiI7CiAgICAgIHZhciBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uID0gdHJ1ZTsKICAgICAgdmFyIF9kaWRJdGVyYXRvckVycm9yID0gZmFsc2U7CiAgICAgIHZhciBfaXRlcmF0b3JFcnJvciA9IHVuZGVmaW5lZDsKCiAgICAgIHRyeSB7CiAgICAgICAgZm9yICh2YXIgX2l0ZXJhdG9yID0gX3RoaXMuYmlsbFN0YXR1c1tTeW1ib2wuaXRlcmF0b3JdKCksIF9zdGVwOyAhKF9pdGVyYXRvck5vcm1hbENvbXBsZXRpb24gPSAoX3N0ZXAgPSBfaXRlcmF0b3IubmV4dCgpKS5kb25lKTsgX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbiA9IHRydWUpIHsKICAgICAgICAgIHZhciBpdGVtID0gX3N0ZXAudmFsdWU7CgogICAgICAgICAgaWYgKGl0ZW0udHlwZUNvZGUgPT0gcGFyYW1zLnJvdy5iaWxsU3RhdHVzKSB7CiAgICAgICAgICAgIHZhbHVlID0gaXRlbS50eXBlTmFtZTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfZGlkSXRlcmF0b3JFcnJvciA9IHRydWU7CiAgICAgICAgX2l0ZXJhdG9yRXJyb3IgPSBlcnI7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIGlmICghX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbiAmJiBfaXRlcmF0b3IucmV0dXJuICE9IG51bGwpIHsKICAgICAgICAgICAgX2l0ZXJhdG9yLnJldHVybigpOwogICAgICAgICAgfQogICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICBpZiAoX2RpZEl0ZXJhdG9yRXJyb3IpIHsKICAgICAgICAgICAgdGhyb3cgX2l0ZXJhdG9yRXJyb3I7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICByZXR1cm4gaCgiZGl2IiwgdmFsdWUpOwogICAgfTsKCiAgICByZXR1cm4gewogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgLy/mj5DkuqTmjInpkq7ph43lpI3mj5DkuqQKICAgICAgZmlsdGVyQ29sbDogdHJ1ZSwKICAgICAgLy/mkJzntKLpnaLmnb8KICAgICAgaXNBZG1pbjogZmFsc2UsCiAgICAgIGlzUHJvQWRtaW46IGZhbHNlLAogICAgICBjb21wYW55OiBudWxsLAogICAgICAvL+eUqOaIt+m7mOiupOWFrOWPuAogICAgICBjb3VudHJ5OiBudWxsLAogICAgICAvL+eUqOaIt+m7mOiupOaJgOWxnumDqOmXqAogICAgICBjb3VudHJ5TmFtZTogbnVsbCwKICAgICAgLy/nlKjmiLfpu5jorqTmiYDlsZ7pg6jpl6gKICAgICAgdmVyc2lvbjogJycsCiAgICAgIGJpbGxTdGF0dXM6IFtdLAogICAgICAvL+WNleaNrueKtuaAgQogICAgICBkZW1vTGlzdDogW10sCiAgICAgIGNvbXBhbmllczogW10sCiAgICAgIGRlcGFydG1lbnRzOiBbXSwKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBjb3VudHJ5OiBudWxsLAogICAgICAgIGNvbXBhbnk6IG51bGwsCiAgICAgICAgY291bnRyeU5hbWU6IG51bGwKICAgICAgfSwKICAgICAgbXVsdGlwbGVTZWxlY3Rpb25Sb3c6IFtdLAogICAgICB3b3JrRmxvd1BhcmFtczoge30sCiAgICAgIGhpc1BhcmFtczoge30sCiAgICAgIHNob3dXb3JrRmxvdzogZmFsc2UsCiAgICAgIGV4cG9ydENvbHVtbnM6IFt7CiAgICAgICAgdGl0bGU6ICfnlLXooagv5Y2P6K6u57yW5Y+3JywKICAgICAgICBrZXk6ICdhbW1ldGVyQ29kZScKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn6aG555uu5ZCN56ewJywKICAgICAgICBrZXk6ICdwcm9qZWN0TmFtZScKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5Y2V5o2u54q25oCBJywKICAgICAgICBrZXk6ICdiaWxsU3RhdHVzU3RyJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICcx5pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICBrZXk6ICdqYW5RdW90YVZhbHVlJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICcy5pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICBrZXk6ICdmZWJRdW90YVZhbHVlJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICcz5pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICBrZXk6ICdtYXJRdW90YVZhbHVlJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICc05pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICBrZXk6ICdhcHJRdW90YVZhbHVlJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICc15pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICBrZXk6ICdtYXlRdW90YVZhbHVlJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICc25pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICBrZXk6ICdqdW5RdW90YVZhbHVlJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICc35pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICBrZXk6ICdqdWxRdW90YVZhbHVlJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICc45pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICBrZXk6ICdhdWdRdW90YVZhbHVlJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICc55pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICBrZXk6ICdzZXBRdW90YVZhbHVlJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICcxMOaciOWumumineWAvO+8iOW6pu+8iScsCiAgICAgICAga2V5OiAnb2N0UXVvdGFWYWx1ZScKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAnMTHmnIjlrprpop3lgLzvvIjluqbvvIknLAogICAgICAgIGtleTogJ25vdlF1b3RhVmFsdWUnCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJzEy5pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICBrZXk6ICdkZWNRdW90YVZhbHVlJwogICAgICB9XSwKICAgICAgZXhwb3J0OiB7CiAgICAgICAgcnVuOiBmYWxzZSwKICAgICAgICAvL+aYr+WQpuato+WcqOaJp+ihjOWvvOWHugogICAgICAgIGRhdGE6ICIiLAogICAgICAgIC8v5a+85Ye65pWw5o2uCiAgICAgICAgdG90YWxQYWdlOiAwLAogICAgICAgIC8v5LiA5YWx5aSa5bCR6aG1CiAgICAgICAgY3VycmVudFBhZ2U6IDAsCiAgICAgICAgLy/lvZPliY3lpJrlsJHpobUKICAgICAgICBwZXJjZW50OiAwLAogICAgICAgIHNpemU6IDIwMDAwMAogICAgICB9LAogICAgICBxdW90YTogewogICAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAgIGNvbHVtbnM6IFt7CiAgICAgICAgICB0aXRsZTogJ+eUteihqC/ljY/orq7nvJblj7cnLAogICAgICAgICAga2V5OiAnYW1tZXRlckNvZGUnLAogICAgICAgICAgY2xhc3NOYW1lOiAidGQtaWQiLAogICAgICAgICAgcmVuZGVyOiByZW50ZXJWaWV3UXVvdGEsCiAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICBtaW5XaWR0aDogMTMwLAogICAgICAgICAgbWF4V2lkdGg6IDIwMAogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAn6aG555uu5ZCN56ewJywKICAgICAgICAgIGtleTogJ3Byb2plY3ROYW1lJywKICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgIG1pbldpZHRoOiAxMzAsCiAgICAgICAgICBtYXhXaWR0aDogMjAwCiAgICAgICAgfSwgewogICAgICAgICAgdGl0bGU6ICfljZXmja7nirbmgIEnLAogICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgcmVuZGVyOiByZW5kZXJCaWxsU3RhdHVzLAogICAgICAgICAgbWluV2lkdGg6IDgwLAogICAgICAgICAgbWF4V2lkdGg6IDIwMAogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAnMeaciOWumumineWAvO+8iOW6pu+8iScsCiAgICAgICAgICBrZXk6ICdqYW5RdW90YVZhbHVlJywKICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgIG1pbldpZHRoOiA3MCwKICAgICAgICAgIG1heFdpZHRoOiAyMDAKICAgICAgICB9LCB7CiAgICAgICAgICB0aXRsZTogJzLmnIjlrprpop3lgLzvvIjluqbvvIknLAogICAgICAgICAga2V5OiAnZmViUXVvdGFWYWx1ZScsCiAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICBtaW5XaWR0aDogNzAsCiAgICAgICAgICBtYXhXaWR0aDogMjAwCiAgICAgICAgfSwgewogICAgICAgICAgdGl0bGU6ICcz5pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICAgIGtleTogJ21hclF1b3RhVmFsdWUnLAogICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgbWluV2lkdGg6IDcwLAogICAgICAgICAgbWF4V2lkdGg6IDIwMAogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAnNOaciOWumumineWAvO+8iOW6pu+8iScsCiAgICAgICAgICBrZXk6ICdhcHJRdW90YVZhbHVlJywKICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgIG1pbldpZHRoOiA3MCwKICAgICAgICAgIG1heFdpZHRoOiAyMDAKICAgICAgICB9LCB7CiAgICAgICAgICB0aXRsZTogJzXmnIjlrprpop3lgLzvvIjluqbvvIknLAogICAgICAgICAga2V5OiAnbWF5UXVvdGFWYWx1ZScsCiAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICBtaW5XaWR0aDogNzAsCiAgICAgICAgICBtYXhXaWR0aDogMjAwCiAgICAgICAgfSwgewogICAgICAgICAgdGl0bGU6ICc25pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICAgIGtleTogJ2p1blF1b3RhVmFsdWUnLAogICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgbWluV2lkdGg6IDcwLAogICAgICAgICAgbWF4V2lkdGg6IDIwMAogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAnN+aciOWumumineWAvO+8iOW6pu+8iScsCiAgICAgICAgICBrZXk6ICdqdWxRdW90YVZhbHVlJywKICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgIG1pbldpZHRoOiA3MCwKICAgICAgICAgIG1heFdpZHRoOiAyMDAKICAgICAgICB9LCB7CiAgICAgICAgICB0aXRsZTogJzjmnIjlrprpop3lgLzvvIjluqbvvIknLAogICAgICAgICAga2V5OiAnYXVnUXVvdGFWYWx1ZScsCiAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICBtaW5XaWR0aDogNzAsCiAgICAgICAgICBtYXhXaWR0aDogMjAwCiAgICAgICAgfSwgewogICAgICAgICAgdGl0bGU6ICc55pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICAgIGtleTogJ3NlcFF1b3RhVmFsdWUnLAogICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgbWluV2lkdGg6IDcwLAogICAgICAgICAgbWF4V2lkdGg6IDIwMAogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAnMTDmnIjlrprpop3lgLzvvIjluqbvvIknLAogICAgICAgICAga2V5OiAnb2N0UXVvdGFWYWx1ZScsCiAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICBtaW5XaWR0aDogNzAsCiAgICAgICAgICBtYXhXaWR0aDogMjAwCiAgICAgICAgfSwgewogICAgICAgICAgdGl0bGU6ICcxMeaciOWumumineWAvO+8iOW6pu+8iScsCiAgICAgICAgICBrZXk6ICdub3ZRdW90YVZhbHVlJywKICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgIG1pbldpZHRoOiA3MCwKICAgICAgICAgIG1heFdpZHRoOiAyMDAKICAgICAgICB9LCB7CiAgICAgICAgICB0aXRsZTogJzEy5pyI5a6a6aKd5YC877yI5bqm77yJJywKICAgICAgICAgIGtleTogJ2RlY1F1b3RhVmFsdWUnLAogICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgbWluV2lkdGg6IDcwLAogICAgICAgICAgbWF4V2lkdGg6IDIwMAogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAi5rWB56iLIiwKICAgICAgICAgIGZpeGVkOiAncmlnaHQnLAogICAgICAgICAga2V5OiAiYWN0aW9uIiwKICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgIG1pbldpZHRoOiA3MCwKICAgICAgICAgIG1heFdpZHRoOiAyMDAsCiAgICAgICAgICByZW5kZXI6IHJlbmRlclcKICAgICAgICB9XSwKICAgICAgICBkYXRhOiBbXSwKICAgICAgICB0b3RhbDogMCwKICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgfQogICAgfTsKICB9LAogIG1ldGhvZHM6IF9vYmplY3RTcHJlYWQoe30sIG1hcE11dGF0aW9ucyhbImNsb3NlVGFnIiwgImNsb3NlVGFnQnlOYW1lIl0pLCB7CiAgICBvbk1vZGFsT0s6IGZ1bmN0aW9uIG9uTW9kYWxPSygpIHsKICAgICAgdGhpcy4kTWVzc2FnZS5lcnJvcign56Gu5a6aJyk7CiAgICB9LAogICAgb25Nb2RhbENhbmNlbDogZnVuY3Rpb24gb25Nb2RhbENhbmNlbCgpIHsKICAgICAgdGhpcy4kTWVzc2FnZS5lcnJvcign5Y+W5raIJyk7CiAgICB9LAogICAgc2VsZWN0Q2hhbmdlOiBmdW5jdGlvbiBzZWxlY3RDaGFuZ2UoKSB7CiAgICAgIHZhciB0aGF0ID0gdGhpczsKCiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLmNvbXBhbnkgIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgaWYgKHRoaXMucXVlcnlQYXJhbXMuY29tcGFueSA9PSAiLTEiKSB7CiAgICAgICAgICB0aGF0LnF1ZXJ5UGFyYW1zLmNvdW50cnkgPSAtMTsKICAgICAgICAgIHRoYXQucXVlcnlQYXJhbXMuY291bnRyeU5hbWUgPSBudWxsOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBnZXRDb3VudHJ5QnlVc2VySWQodGhhdC5xdWVyeVBhcmFtcy5jb21wYW55KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgaWYgKHJlcy5kYXRhLmRlcGFydG1lbnRzLmxlbmd0aCAhPSAwKSB7CiAgICAgICAgICAgICAgdGhhdC5xdWVyeVBhcmFtcy5jb3VudHJ5ID0gcmVzLmRhdGEuZGVwYXJ0bWVudHNbMF0uaWQ7CiAgICAgICAgICAgICAgdGhhdC5xdWVyeVBhcmFtcy5jb3VudHJ5TmFtZSA9IHJlcy5kYXRhLmRlcGFydG1lbnRzWzBdLm5hbWU7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvKuWIoOmZpCovCiAgICByZW1vdmVRdW90YTogZnVuY3Rpb24gcmVtb3ZlUXVvdGEoaWQpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CgogICAgICB2YXIgbXVsdGlwbGVTZWxlY3Rpb24gPSBbXTsKCiAgICAgIGlmICh0aGlzLm11bHRpcGxlU2VsZWN0aW9uUm93Lmxlbmd0aCA+IDApIHsKICAgICAgICB2YXIgX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjIgPSB0cnVlOwogICAgICAgIHZhciBfZGlkSXRlcmF0b3JFcnJvcjIgPSBmYWxzZTsKICAgICAgICB2YXIgX2l0ZXJhdG9yRXJyb3IyID0gdW5kZWZpbmVkOwoKICAgICAgICB0cnkgewogICAgICAgICAgZm9yICh2YXIgX2l0ZXJhdG9yMiA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb25Sb3dbU3ltYm9sLml0ZXJhdG9yXSgpLCBfc3RlcDI7ICEoX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjIgPSAoX3N0ZXAyID0gX2l0ZXJhdG9yMi5uZXh0KCkpLmRvbmUpOyBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uMiA9IHRydWUpIHsKICAgICAgICAgICAgdmFyIGl0ZW0gPSBfc3RlcDIudmFsdWU7CgogICAgICAgICAgICBpZiAoaXRlbS5iaWxsU3RhdHVzICE9IDApIHsKICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIuaJgOmAieaVsOaNruWMheWQq+mdnuiNieeov+aVsOaNru+8jOS4jeiDveWIoOmZpO+8gSIpOwogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgbXVsdGlwbGVTZWxlY3Rpb24ucHVzaChpdGVtLmlkKTsKICAgICAgICAgIH0KICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgIF9kaWRJdGVyYXRvckVycm9yMiA9IHRydWU7CiAgICAgICAgICBfaXRlcmF0b3JFcnJvcjIgPSBlcnI7CiAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIGlmICghX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjIgJiYgX2l0ZXJhdG9yMi5yZXR1cm4gIT0gbnVsbCkgewogICAgICAgICAgICAgIF9pdGVyYXRvcjIucmV0dXJuKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICAgIGlmIChfZGlkSXRlcmF0b3JFcnJvcjIpIHsKICAgICAgICAgICAgICB0aHJvdyBfaXRlcmF0b3JFcnJvcjI7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIGlkID0gbXVsdGlwbGVTZWxlY3Rpb24uam9pbignLCcpOwogICAgICAgIHRoaXMuJE1vZGFsLmNvbmZpcm0oewogICAgICAgICAgdGl0bGU6ICfmuKnppqjmj5DnpLonLAogICAgICAgICAgY29udGVudDogJzxwPuehruiupOWIoOmZpOWQlz88L3A+JywKICAgICAgICAgIG9uT2s6IGZ1bmN0aW9uIG9uT2soKSB7CiAgICAgICAgICAgIF90aGlzMi5xdW90YS5sb2FkaW5nID0gdHJ1ZTsKCiAgICAgICAgICAgIF9yZW1vdmVRdW90YSh7CiAgICAgICAgICAgICAgaWRzOiBpZAogICAgICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICBfdGhpczIucXVvdGEubG9hZGluZyA9IGZhbHNlOwoKICAgICAgICAgICAgICBfdGhpczIuJE1lc3NhZ2Uuc3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CgogICAgICAgICAgICAgIF90aGlzMi5fb25TZWFyY2hIYW5kbGUoKTsKICAgICAgICAgICAgfSk7CgogICAgICAgICAgICBfdGhpczIubXVsdGlwbGVTZWxlY3Rpb25Sb3cgPSBbXTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIuivt+iHs+WwkemAieaLqeS4gOihjCIpOwogICAgICB9CiAgICB9LAoKICAgIC8q57yW6L6RKi8KICAgIGVkaXRRdW90YTogZnVuY3Rpb24gZWRpdFF1b3RhKCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKCiAgICAgIGlmICh0aGlzLm11bHRpcGxlU2VsZWN0aW9uUm93Lmxlbmd0aCA9PSAxKSB7CiAgICAgICAgdmFyIHJvdyA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb25Sb3dbMF07CiAgICAgICAgaXNJblRvZG9MaXN0KHJvdy5pZCwgMykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAvL+WtmOWcqOS6juS7o+WKnuS4reaXtu+8jOaKpeWHuuaPkOekugogICAgICAgICAgdmFyIG93bmVybmFtZSA9ICIiOwoKICAgICAgICAgIGlmIChyZXMuZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgcmVzLmRhdGEubGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgICBvd25lcm5hbWUgKz0gcmVzLmRhdGFbaV0ub3duZXJuYW1lICsgJyAnOwogICAgICAgICAgICB9CgogICAgICAgICAgICBfdGhpczMuJE1vZGFsLndhcm5pbmcoewogICAgICAgICAgICAgIHRpdGxlOiAi5rip6aao5o+Q56S6IiwKICAgICAgICAgICAgICBjb250ZW50OiAi6K+l5pWw5o2u5a2Y5Zyo5LqOIiArIG93bmVybmFtZSArICLnmoTmtYHnqIvku6Plip7kuK3vvIzlpITnkIblkI7miY3lj6/kv67mlLnmlbDmja4iCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXMzLmNsb3NlVGFnQnlOYW1lKHsKICAgICAgICAgICAgICByb3V0ZTogZ2V0SG9tZVJvdXRlKHJvdXRlcnMsICJlZGl0UXVvdGEiKQogICAgICAgICAgICB9KTsKCiAgICAgICAgICAgIF90aGlzMy4kcm91dGVyLnB1c2goewogICAgICAgICAgICAgIG5hbWU6ICJlZGl0UXVvdGEiLAogICAgICAgICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICAgICAgICBpZDogcm93LmlkCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICByZXBsYWNlOiB0cnVlCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnIpIHsKICAgICAgICAgIGNvbnNvbGUubG9nKGVycik7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLor7fpgInmi6nlhbbkuK3kuIDooYwiKTsKICAgICAgfQogICAgfSwKCiAgICAvKua3u+WKoCovCiAgICBhZGRRdW90YTogZnVuY3Rpb24gYWRkUXVvdGEoKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBuYW1lOiAiYWRkUXVvdGEiLAogICAgICAgIHF1ZXJ5OiB7fSwKICAgICAgICByZXBsYWNlOiB0cnVlCiAgICAgIH0pOwogICAgfSwKCiAgICAvKuafpeeciyovCiAgICB2aWV3UXVvdGE6IGZ1bmN0aW9uIHZpZXdRdW90YShyb3cpIHsKICAgICAgdGhpcy4kcmVmcy52aWV3UXVvdGFQYWdlLmluaXRRdW90YShyb3cuaWQpOwogICAgfSwKICAgIF9vblJlc2V0SGFuZGxlOiBmdW5jdGlvbiBfb25SZXNldEhhbmRsZSgpIHsKICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvblJvdyA9IFtdOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvbXBhbnkgPSB0aGlzLmNvbXBhbnk7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY291bnRyeSA9IHRoaXMuY291bnRyeTsKICAgICAgdGhpcy4kcmVmcy5xdW90YVRhYmxlLnF1ZXJ5KHRoaXMucXVlcnlQYXJhbXMpOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvdW50cnlOYW1lID0gdGhpcy5jb3VudHJ5TmFtZTsKICAgIH0sCiAgICBfb25TZWFyY2hIYW5kbGU6IGZ1bmN0aW9uIF9vblNlYXJjaEhhbmRsZSgpIHsKICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvblJvdyA9IFtdOwoKICAgICAgaWYgKHRoaXMucXVlcnlQYXJhbXMuY291bnRyeU5hbWUgPT0gIiIpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvdW50cnkgPSAiLTEiOwogICAgICB9CgogICAgICB0aGlzLiRyZWZzLnF1b3RhVGFibGUucXVlcnkodGhpcy5xdWVyeVBhcmFtcyk7CiAgICB9LAogICAgc2V0RGlzYWJsZWQ6IGZ1bmN0aW9uIHNldERpc2FibGVkKCkgewogICAgICB2YXIgX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjMgPSB0cnVlOwogICAgICB2YXIgX2RpZEl0ZXJhdG9yRXJyb3IzID0gZmFsc2U7CiAgICAgIHZhciBfaXRlcmF0b3JFcnJvcjMgPSB1bmRlZmluZWQ7CgogICAgICB0cnkgewogICAgICAgIGZvciAodmFyIF9pdGVyYXRvcjMgPSB0aGlzLiRyZWZzLnF1b3RhVGFibGUuaW5zaWRlRGF0YVtTeW1ib2wuaXRlcmF0b3JdKCksIF9zdGVwMzsgIShfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uMyA9IChfc3RlcDMgPSBfaXRlcmF0b3IzLm5leHQoKSkuZG9uZSk7IF9pdGVyYXRvck5vcm1hbENvbXBsZXRpb24zID0gdHJ1ZSkgewogICAgICAgICAgdmFyIGl0ZW0gPSBfc3RlcDMudmFsdWU7CgogICAgICAgICAgaWYgKGl0ZW0uYmlsbFN0YXR1cyAhPSAwKSB7CiAgICAgICAgICAgIGl0ZW0uX2Rpc2FibGVkID0gdHJ1ZTsgLy/npoHmraLpgInmi6kKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgIF9kaWRJdGVyYXRvckVycm9yMyA9IHRydWU7CiAgICAgICAgX2l0ZXJhdG9yRXJyb3IzID0gZXJyOwogICAgICB9IGZpbmFsbHkgewogICAgICAgIHRyeSB7CiAgICAgICAgICBpZiAoIV9pdGVyYXRvck5vcm1hbENvbXBsZXRpb24zICYmIF9pdGVyYXRvcjMucmV0dXJuICE9IG51bGwpIHsKICAgICAgICAgICAgX2l0ZXJhdG9yMy5yZXR1cm4oKTsKICAgICAgICAgIH0KICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgaWYgKF9kaWRJdGVyYXRvckVycm9yMykgewogICAgICAgICAgICB0aHJvdyBfaXRlcmF0b3JFcnJvcjM7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgaGFuZGxlU2VsZWN0Um93OiBmdW5jdGlvbiBoYW5kbGVTZWxlY3RSb3codmFsKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwoKICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvblJvdyA9IFtdOwogICAgICB2YWwuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIF90aGlzNC5tdWx0aXBsZVNlbGVjdGlvblJvdy5wdXNoKGl0ZW0pOwogICAgICB9KTsKICAgIH0sCiAgICBzdGFydEZsb3dTdWJtaXQ6IGZ1bmN0aW9uIHN0YXJ0Rmxvd1N1Ym1pdChyb3cpIHsKICAgICAgdmFyIGJ1c2lBbGlhcyA9ICJBRERfUVVPVEEiOwogICAgICB2YXIgYnVzaVRpdGxlID0gIuaWsOWinuWumuminSgiICsgcm93LnByb2plY3ROYW1lICsgIinlrqHmibkiOwoKICAgICAgaWYgKHJvdy5iaWxsU3RhdHVzID09PSAzKSB7CiAgICAgICAgYnVzaUFsaWFzID0gIk1PRElGWV9RVU9UQSI7CiAgICAgICAgYnVzaVRpdGxlID0gIuS/ruaUueWumuminSgiICsgcm93LnByb2plY3ROYW1lICsgIinlrqHmibkiOwogICAgICB9CgogICAgICB0aGlzLndvcmtGbG93UGFyYW1zID0gewogICAgICAgIGJ1c2lJZDogcm93LmlkLAogICAgICAgIGJ1c2lBbGlhczogYnVzaUFsaWFzLAogICAgICAgIGJ1c2lUaXRsZTogYnVzaVRpdGxlCiAgICAgIH07CiAgICAgIHZhciB0aGF0ID0gdGhpczsKICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7CiAgICAgICAgdGl0bGU6ICflrprpop3mj5DkuqTmtYHnqIsnLAogICAgICAgIGNvbnRlbnQ6ICc8cD7mmK/lkKbmj5DkuqTlrprpop0gKCcgKyByb3cucHJvamVjdE5hbWUgKyAnKSDliLDmtYHnqIs8L3A+JywKICAgICAgICBvbk9rOiBmdW5jdGlvbiBvbk9rKCkgewogICAgICAgICAgdGhhdC5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICB0aGF0LiRyZWZzLmNsd2ZidG4ub25DbGljaygpOwogICAgICAgICAgfSwgMzAwKTsKICAgICAgICB9LAogICAgICAgIG9uQ2FuY2VsOiBmdW5jdGlvbiBvbkNhbmNlbCgpIHsKICAgICAgICAgIHRoYXQubG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgc3RhcnRGbG93OiBmdW5jdGlvbiBzdGFydEZsb3cocm93KSB7CiAgICAgIHZhciB0aGF0ID0gdGhpczsKICAgICAgaXNJblRvZG9MaXN0KHJvdy5pZCwgMykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgLy/lrZjlnKjkuo7ku6Plip7kuK3ml7bvvIzmiqXlh7rmj5DnpLoKICAgICAgICB2YXIgb3duZXJuYW1lID0gIiI7CgogICAgICAgIGlmIChyZXMuZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHJlcy5kYXRhLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICAgIG93bmVybmFtZSArPSByZXMuZGF0YVtpXS5vd25lcm5hbWUgKyAnICc7CiAgICAgICAgICB9CgogICAgICAgICAgdGhhdC4kTW9kYWwud2FybmluZyh7CiAgICAgICAgICAgIHRpdGxlOiAi5rip6aao5o+Q56S6IiwKICAgICAgICAgICAgY29udGVudDogIuivpeaVsOaNruWtmOWcqOS6jiIgKyBvd25lcm5hbWUgKyAi55qE5rWB56iL5Luj5Yqe5Lit77yM5aSE55CG5ZCO5omN5Y+v57un57ut5o+Q5Lqk5rWB56iLIgogICAgICAgICAgfSk7CiAgICAgICAgICB0aGF0LmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9IGVsc2UgaWYgKHJvdy5iaWxsU3RhdHVzID09IDMgfHwgcm93LmJpbGxTdGF0dXMgPT0gNCkgewogICAgICAgICAgY2hlY2tTdGFydEZsb3coewogICAgICAgICAgICBpZDogcm93LmlkCiAgICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMxKSB7CiAgICAgICAgICAgIC8q5o+Q5Lqk5rWB56iL6aqM6K+B55So5oi35piv5ZCm5pyJ5pWw5o2u6ZyA6KaB5o+Q5LqkKi8KICAgICAgICAgICAgdGhhdC5sb2FkaW5nID0gZmFsc2U7CgogICAgICAgICAgICBpZiAocmVzMS5kYXRhLmlkID09IG51bGwgfHwgcmVzMS5kYXRhLmlkID09IHVuZGVmaW5lZCkgewogICAgICAgICAgICAgIHRoYXQuJE1vZGFsLndhcm5pbmcoewogICAgICAgICAgICAgICAgdGl0bGU6ICLmuKnppqjmj5DnpLoiLAogICAgICAgICAgICAgICAgY29udGVudDogIuaCqOayoeacieWPr+aPkOS6pOeahOaVsOaNriIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGF0LnN0YXJ0Rmxvd1N1Ym1pdChyb3cpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhhdC5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICB0aGF0LnN0YXJ0Rmxvd1N1Ym1pdChyb3cpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgc2hvd0Zsb3c6IGZ1bmN0aW9uIHNob3dGbG93KHJvdywgcHJvY0luc3RJZCkgewogICAgICB0aGlzLnNob3dXb3JrRmxvdyA9IHRydWU7CiAgICAgIHRoaXMuaGlzUGFyYW1zID0gewogICAgICAgIGJ1c2lJZDogcm93LmlkLAogICAgICAgIGJ1c2lUeXBlOiByb3cuYnVzaUFsaWFzLAogICAgICAgIHByb2NJbnN0SWQ6IHByb2NJbnN0SWQKICAgICAgfTsKICAgIH0sCiAgICBkb1dvcmtGbG93OiBmdW5jdGlvbiBkb1dvcmtGbG93KGRhdGEpIHsKICAgICAgLy/mtYHnqIvlm57osIMKICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIHRoaXMuJHJlZnMucXVvdGFUYWJsZS5xdWVyeSgpOwogICAgfSwKICAgIHF1ZXJ5OiBmdW5jdGlvbiBxdWVyeShwYXJhbXMpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CgogICAgICB0aGlzLnF1b3RhLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0UXVvdGEocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczUucXVvdGEubG9hZGluZyA9IGZhbHNlOwogICAgICAgIF90aGlzNS5xdW90YS50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOwogICAgICAgIF90aGlzNS5xdW90YS5kYXRhID0gT2JqZWN0LmFzc2lnbihbXSwgcmVzLmRhdGEucm93cyk7CiAgICAgIH0pOwogICAgfSwKICAgIGJlZm9yZUxvYWREYXRhOiBmdW5jdGlvbiBiZWZvcmVMb2FkRGF0YShkYXRhKSB7CiAgICAgIHZhciBjb2xzID0gW10sCiAgICAgICAgICBrZXlzID0gW107CgogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRoaXMuZXhwb3J0Q29sdW1ucy5sZW5ndGg7IGkrKykgewogICAgICAgIGNvbHMucHVzaCh0aGlzLmV4cG9ydENvbHVtbnNbaV0udGl0bGUpOwogICAgICAgIGtleXMucHVzaCh0aGlzLmV4cG9ydENvbHVtbnNbaV0ua2V5KTsKICAgICAgfQoKICAgICAgdmFyIHBhcmFtcyA9IHsKICAgICAgICB0aXRsZTogY29scywKICAgICAgICBrZXk6IGtleXMsCiAgICAgICAgZGF0YTogZGF0YSwKICAgICAgICBhdXRvV2lkdGg6IHRydWUsCiAgICAgICAgZmlsZW5hbWU6ICflrprpop3mlbDmja7lr7zlh7onCiAgICAgIH07CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZVNpemUgPSB0aGlzLnF1b3RhLnBhZ2VTaXplOwogICAgICBleGNlbC5leHBvcnRfYXJyYXlfdG9fZXhjZWwocGFyYW1zKTsKICAgICAgdGhpcy4kU3Bpbi5oaWRlKCk7CiAgICAgIHJldHVybjsKICAgIH0sCiAgICBleHBvcnRMb2FkaW5nOiBmdW5jdGlvbiBleHBvcnRMb2FkaW5nKCkgewogICAgICB0aGlzLiRTcGluLnNob3coewogICAgICAgIHJlbmRlcjogZnVuY3Rpb24gcmVuZGVyKGgpIHsKICAgICAgICAgIHJldHVybiBoKCdkaXYnLCBbaCgnUHJvZ3Jlc3MnLCB7CiAgICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgICAgd2lkdGg6ICc4MDBweCcKICAgICAgICAgICAgfQogICAgICAgICAgfSksIGgoJ2RpdicsICflr7zlh7rkuK3vvIzor7fli7/liLfmlrDpobXpnaIuLi4uLi4nKV0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgZXhwb3J0Q3N2OiBmdW5jdGlvbiBleHBvcnRDc3YobmFtZSkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKCiAgICAgIHRoaXMuZXhwb3J0TG9hZGluZygpOwogICAgICB0aGlzLmV4cG9ydC5ydW4gPSB0cnVlOwogICAgICB2YXIgcGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsKCiAgICAgIGlmIChuYW1lID09PSAnY3VycmVudCcpIHsKICAgICAgICB0aGlzLmJlZm9yZUxvYWREYXRhKHRoaXMuc2V0VmFsdWVCeUZvckVhY2godGhpcy5xdW90YS5kYXRhKSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9IGVsc2UgaWYgKG5hbWUgPT09ICdhbGwnKSB7CiAgICAgICAgcGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICAgIHBhcmFtcy5wYWdlU2l6ZSA9IHRoaXMuZXhwb3J0LnNpemU7CiAgICAgIH0KCiAgICAgIHZhciByZXEgPSB7CiAgICAgICAgdXJsOiAiL2J1c2luZXNzL3F1b3RhL2xpc3QiLAogICAgICAgIG1ldGhvZDogImdldCIsCiAgICAgICAgcGFyYW1zOiBwYXJhbXMKICAgICAgfTsKICAgICAgYXhpb3MucmVxdWVzdChyZXEpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuZGF0YSkgewogICAgICAgICAgdmFyIGFycmF5ID0gcmVzLmRhdGEucm93czsKCiAgICAgICAgICBfdGhpczYuYmVmb3JlTG9hZERhdGEoX3RoaXM2LnNldFZhbHVlQnlGb3JFYWNoKGFycmF5KSk7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyKSB7CiAgICAgICAgY29uc29sZS5sb2coZXJyKTsKICAgICAgfSk7CiAgICB9LAogICAgc2V0VmFsdWVCeUZvckVhY2g6IGZ1bmN0aW9uIHNldFZhbHVlQnlGb3JFYWNoKGFycmF5KSB7CiAgICAgIGFycmF5LmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICBpdGVtLmJpbGxTdGF0dXNTdHIgPSBidGV4dCgiYmFzaWNCaWxsU3RhdHVzIiwgaXRlbS5iaWxsU3RhdHVzLCAndHlwZUNvZGUnLCAndHlwZU5hbWUnKTsKICAgICAgfSk7CiAgICAgIHJldHVybiBhcnJheTsKICAgIH0sCiAgICAvL+mAieaLqeaJgOWxnumDqOmXqOW8gOWniwogICAgY2hvb3NlUmVzcG9uc2VDZW50ZXI6IGZ1bmN0aW9uIGNob29zZVJlc3BvbnNlQ2VudGVyKCkgewogICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5jb21wYW55ID09IG51bGwgfHwgdGhpcy5xdWVyeVBhcmFtcy5jb21wYW55ID09ICItMSIpIHsKICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIuivt+WFiOmAieaLqeWIhuWFrOWPuCIpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgdGhpcy4kcmVmcy5jb3VudHJ5TW9kYWwuY2hvb3NlKHRoaXMucXVlcnlQYXJhbXMuY29tcGFueSk7IC8v5omA5bGe6YOo6ZeoCiAgICB9LAogICAgZ2V0RGF0YUZyb21Nb2RhbDogZnVuY3Rpb24gZ2V0RGF0YUZyb21Nb2RhbChkYXRhKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY291bnRyeSA9IGRhdGEuaWQ7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY291bnRyeU5hbWUgPSBkYXRhLm5hbWU7IC8v6YCJ5oup5omA5bGe6YOo6Zeo57uT5p2fCiAgICB9LAogICAgZ2V0VXNlckRhdGE6IGZ1bmN0aW9uIGdldFVzZXJEYXRhKCkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKCiAgICAgIHZhciB0aGF0ID0gdGhpczsKICAgICAgZ2V0VXNlcmRhdGEoKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAvL+W9k+WJjeeZu+W9leeUqOaIt+aJgOWcqOWFrOWPuOWSjOaJgOWxnumDqOmXqAogICAgICAgIHZhciBjb21wYW5pZXMgPSB0aGF0LmNvbXBhbmllczsKCiAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllcyAhPSBudWxsICYmIHJlcy5kYXRhLmNvbXBhbmllcy5sZW5ndGggIT0gMCkgewogICAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllc1swXS5pZCAhPSAiMjYwMDAwMDAwMCIpIHsKICAgICAgICAgICAgY29tcGFuaWVzID0gcmVzLmRhdGEuY29tcGFuaWVzOwogICAgICAgICAgICA7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGF0LmlzUHJvQWRtaW4gPSB0cnVlOwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgdGhhdC5jb21wYW55ID0gY29tcGFuaWVzWzBdLmlkOwogICAgICAgIHRoYXQucXVlcnlQYXJhbXMuY29tcGFueSA9IGNvbXBhbmllc1swXS5pZDsKICAgICAgICB2YXIgZGVwYXJ0bWVudHMgPSB0aGF0LmRlcGFydG1lbnRzOwoKICAgICAgICBpZiAocmVzLmRhdGEuZGVwYXJ0bWVudHMgIT0gbnVsbCAmJiByZXMuZGF0YS5kZXBhcnRtZW50cy5sZW5ndGggIT0gMCkgewogICAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllc1swXS5pZCAhPSAiMjYwMDAwMDAwMCIpIHsKICAgICAgICAgICAgZGVwYXJ0bWVudHMgPSByZXMuZGF0YS5kZXBhcnRtZW50czsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIHRoYXQuY291bnRyeSA9IGRlcGFydG1lbnRzWzBdLmlkOwogICAgICAgIHRoYXQuY291bnRyeU5hbWUgPSBkZXBhcnRtZW50c1swXS5uYW1lOwogICAgICAgIHRoYXQucXVlcnlQYXJhbXMuY291bnRyeSA9IE51bWJlcihkZXBhcnRtZW50c1swXS5pZCk7CiAgICAgICAgdGhhdC5xdWVyeVBhcmFtcy5jb3VudHJ5TmFtZSA9IGRlcGFydG1lbnRzWzBdLm5hbWU7CgogICAgICAgIF90aGlzNy5xdWVyeSh7CiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdHlwZTogMCwKICAgICAgICAgIHBhZ2VTaXplOiBfdGhpczcucXVvdGEucGFnZVNpemUsCiAgICAgICAgICBjb21wYW55OiBfdGhpczcuY29tcGFueSwKICAgICAgICAgIGNvdW50cnk6IF90aGlzNy5jb3VudHJ5CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIGluaXQ6IGZ1bmN0aW9uIGluaXQoKSB7CiAgICAgIHRoaXMudmVyc2lvbiA9IGluZGV4RGF0YS52ZXJzaW9uOwogICAgICB0aGlzLmJpbGxTdGF0dXMgPSBibGlzdCgiYmFzaWNCaWxsU3RhdHVzIik7IC8v5Y2V5o2u54q25oCBCgogICAgICB2YXIgdGhhdCA9IHRoaXM7CiAgICAgIGdldFVzZXJCeVVzZXJSb2xlKCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgLy/moLnmja7mnYPpmZDojrflj5bliIblhazlj7gKICAgICAgICB0aGF0LmNvbXBhbmllcyA9IHJlcy5kYXRhLmNvbXBhbmllczsKCiAgICAgICAgaWYgKHJlcy5kYXRhLmlzQ2l0eUFkbWluID09IHRydWUgfHwgcmVzLmRhdGEuaXNQcm9BZG1pbiA9PSB0cnVlIHx8IHJlcy5kYXRhLmlzU3ViQWRtaW4gPT0gdHJ1ZSkgewogICAgICAgICAgdGhhdC5pc0FkbWluID0gdHJ1ZTsKICAgICAgICB9CgogICAgICAgIGdldENvdW50cnlzZGF0YSh7CiAgICAgICAgICBvcmdDb2RlOiByZXMuZGF0YS5jb21wYW5pZXNbMF0uaWQKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgIC8v5qC55o2u5p2D6ZmQ6I635Y+W5omA5bGe6YOo6ZeoCiAgICAgICAgICB0aGF0LmRlcGFydG1lbnRzID0gcmVzLmRhdGE7CiAgICAgICAgICB0aGF0LmdldFVzZXJEYXRhKCk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfQogIH0pLAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB0aGlzLmluaXQoKTsKICB9Cn07"}, {"version": 3, "sources": ["listQuota.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmHA,SAAA,iBAAA,EAAA,kBAAA,EAAA,WAAA,EAAA,eAAA,QAAA,2BAAA;AACA,SAAA,YAAA,QAAA,iCAAA;AACA,SAAA,WAAA,IAAA,YAAA,EAAA,SAAA,EAAA,cAAA,QAAA,+BAAA;AACA,SAAA,KAAA,EAAA,KAAA,QAAA,cAAA;AACA,OAAA,aAAA,MAAA,iBAAA;AACA,OAAA,YAAA,MAAA,sCAAA;AACA,OAAA,WAAA,MAAA,2CAAA;AACA,OAAA,oBAAA,MAAA,mDAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AACA,OAAA,KAAA,MAAA,cAAA;AACA,SAAA,YAAA,QAAA,MAAA;AACA,OAAA,OAAA,MAAA,kBAAA;AACA,SAAA,YAAA,QAAA,aAAA;AACA,OAAA,SAAA,MAAA,gBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,WAAA,EAAA,WADA;AAEA,IAAA,oBAAA,EAAA,oBAFA;AAGA,IAAA,aAAA,EAAA,aAHA;AAIA,IAAA,YAAA,EAAA;AAJA,GAFA;AASA,EAAA,IATA,kBASA;AAAA;;AACA;AACA,QAAA,eAAA,GAAA,SAAA,eAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,MAAA,GAAA,MAAA,CAAA,MAAA,CAAA,GAAA;AACA,aAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,GAAA,EAAA;AACA,QAAA,EAAA,EAAA;AACA,UAAA,KAAA,EAAA,iBAAA;AACA,YAAA,KAAA,CAAA,SAAA,CAAA,MAAA,CAAA,GAAA;AACA;AAHA;AADA,OAAA,EAMA,MAAA,CAAA,GAAA,CAAA,MAAA,CANA,CAAA,CAAA,CAAA;AAOA,KATA;;AAUA,QAAA,OAAA,GAAA,SAAA,OAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA;AACA,UAAA,IAAA;AAAA,UAAA,IAAA,GAAA,EAAA;;AACA,UAAA,MAAA,CAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,MAAA,CAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,MAAA,CAAA,GAAA,CAAA,aAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,GAAA,IAAA;AACA,QAAA,IAAA,GAAA,SAAA;AACA,OAHA,MAGA,IAAA,MAAA,CAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,MAAA,CAAA,GAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,QAAA,IAAA,GAAA,IAAA;AACA,QAAA,IAAA,GAAA,SAAA;AACA;;AACA,UAAA,IAAA,IAAA,EAAA,EAAA;AACA,eAAA,CAAA,CAAA,KAAA,EAAA,EAAA,EAAA,IAAA,CAAA;AACA;;AACA,aAAA,CAAA,CAAA,QAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,IAAA,EAAA,IADA;AACA,UAAA,IAAA,EAAA;AADA,SADA;AAGA,QAAA,EAAA,EAAA;AACA,UAAA,KADA,mBACA;AACA,gBAAA,MAAA,CAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,MAAA,CAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,MAAA,CAAA,GAAA,CAAA,aAAA,IAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,EAAA,MAAA,CAAA,GAAA,CAAA,aAAA;AACA,aAFA,MAEA,IAAA,MAAA,CAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,MAAA,CAAA,GAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA,cAAA,IAAA,CAAA,SAAA,CAAA,MAAA,CAAA,GAAA;AACA;AACA;AARA;AAHA,OAAA,EAaA,IAbA,CAAA;AAcA,KA3BA,CAZA,CAwCA;;;AACA,QAAA,gBAAA,GAAA,SAAA,gBAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,KAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,6BAAA,KAAA,CAAA,UAAA,8HAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,UAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,KATA;;AAUA,WAAA;AACA,MAAA,OAAA,EAAA,KADA;AACA;AACA,MAAA,UAAA,EAAA,IAFA;AAEA;AACA,MAAA,OAAA,EAAA,KAHA;AAIA,MAAA,UAAA,EAAA,KAJA;AAKA,MAAA,OAAA,EAAA,IALA;AAKA;AACA,MAAA,OAAA,EAAA,IANA;AAMA;AACA,MAAA,WAAA,EAAA,IAPA;AAOA;AACA,MAAA,OAAA,EAAA,EARA;AASA,MAAA,UAAA,EAAA,EATA;AASA;AACA,MAAA,QAAA,EAAA,EAVA;AAWA,MAAA,SAAA,EAAA,EAXA;AAYA,MAAA,WAAA,EAAA,EAZA;AAaA,MAAA,WAAA,EAAA;AAAA,QAAA,OAAA,EAAA,IAAA;AAAA,QAAA,OAAA,EAAA,IAAA;AAAA,QAAA,WAAA,EAAA;AAAA,OAbA;AAcA,MAAA,oBAAA,EAAA,EAdA;AAgBA,MAAA,cAAA,EAAA,EAhBA;AAiBA,MAAA,SAAA,EAAA,EAjBA;AAkBA,MAAA,YAAA,EAAA,KAlBA;AAmBA,MAAA,aAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAAA,EACA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,UAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,UAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAJA,EAKA;AAAA,QAAA,KAAA,EAAA,UAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OALA,EAMA;AAAA,QAAA,KAAA,EAAA,UAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OANA,EAOA;AAAA,QAAA,KAAA,EAAA,UAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAPA,EAQA;AAAA,QAAA,KAAA,EAAA,UAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OARA,EASA;AAAA,QAAA,KAAA,EAAA,UAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OATA,EAUA;AAAA,QAAA,KAAA,EAAA,UAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAVA,EAWA;AAAA,QAAA,KAAA,EAAA,UAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAXA,EAYA;AAAA,QAAA,KAAA,EAAA,WAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAZA,EAaA;AAAA,QAAA,KAAA,EAAA,WAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAbA,EAcA;AAAA,QAAA,KAAA,EAAA,WAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAdA,CAnBA;AAkCA,MAAA,MAAA,EAAA;AACA,QAAA,GAAA,EAAA,KADA;AACA;AACA,QAAA,IAAA,EAAA,EAFA;AAEA;AACA,QAAA,SAAA,EAAA,CAHA;AAGA;AACA,QAAA,WAAA,EAAA,CAJA;AAIA;AACA,QAAA,OAAA,EAAA,CALA;AAMA,QAAA,IAAA,EAAA;AANA,OAlCA;AA0CA,MAAA,KAAA,EAAA;AACA,QAAA,OAAA,EAAA,KADA;AAEA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,SAAA,EAAA,OAHA;AAIA,UAAA,MAAA,EAAA,eAJA;AAKA,UAAA,KAAA,EAAA,QALA;AAMA,UAAA,QAAA,EAAA,GANA;AAOA,UAAA,QAAA,EAAA;AAPA,SADA,EAUA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,GAJA;AAKA,UAAA,QAAA,EAAA;AALA,SAVA,EAiBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,MAAA,EAAA,gBAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SAjBA,EAwBA;AACA,UAAA,KAAA,EAAA,UADA;AAEA,UAAA,GAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SAxBA,EA+BA;AACA,UAAA,KAAA,EAAA,UADA;AAEA,UAAA,GAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SA/BA,EAsCA;AACA,UAAA,KAAA,EAAA,UADA;AAEA,UAAA,GAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SAtCA,EA6CA;AACA,UAAA,KAAA,EAAA,UADA;AAEA,UAAA,GAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SA7CA,EAoDA;AACA,UAAA,KAAA,EAAA,UADA;AAEA,UAAA,GAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SApDA,EA2DA;AACA,UAAA,KAAA,EAAA,UADA;AAEA,UAAA,GAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SA3DA,EAkEA;AACA,UAAA,KAAA,EAAA,UADA;AAEA,UAAA,GAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SAlEA,EAyEA;AACA,UAAA,KAAA,EAAA,UADA;AAEA,UAAA,GAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SAzEA,EAgFA;AACA,UAAA,KAAA,EAAA,UADA;AAEA,UAAA,GAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SAhFA,EAuFA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,GAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SAvFA,EA8FA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,GAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SA9FA,EAqGA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,GAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SArGA,EA4GA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,KAAA,EAAA,OAFA;AAGA,UAAA,GAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,QAAA,EAAA,EALA;AAMA,UAAA,QAAA,EAAA,GANA;AAOA,UAAA,MAAA,EAAA;AAPA,SA5GA,CAFA;AAwHA,QAAA,IAAA,EAAA,EAxHA;AAyHA,QAAA,KAAA,EAAA,CAzHA;AA0HA,QAAA,QAAA,EAAA;AA1HA;AA1CA,KAAA;AAuKA,GAnOA;AAoOA,EAAA,OAAA,oBACA,YAAA,CAAA,CAAA,UAAA,EAAA,gBAAA,CAAA,CADA;AAEA,IAAA,SAFA,uBAEA;AACA,WAAA,QAAA,CAAA,KAAA,CAAA,IAAA;AACA,KAJA;AAKA,IAAA,aALA,2BAKA;AACA,WAAA,QAAA,CAAA,KAAA,CAAA,IAAA;AACA,KAPA;AAQA,IAAA,YARA,0BAQA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,KAAA,WAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,YAAA,KAAA,WAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,WAAA,CAAA,OAAA,GAAA,CAAA,CAAA;AACA,UAAA,IAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,SAHA,MAGA;AACA,UAAA,kBAAA,CAAA,IAAA,CAAA,WAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,WAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EAAA;AACA,cAAA,IAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA;AACA;AACA,WALA;AAMA;AACA;AACA,KAvBA;;AAyBA;AACA,IAAA,WA1BA,uBA0BA,EA1BA,EA0BA;AAAA;;AACA,UAAA,iBAAA,GAAA,EAAA;;AACA,UAAA,KAAA,oBAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AAAA;AAAA;AAAA;;AAAA;AACA,gCAAA,KAAA,oBAAA,mIAAA;AAAA,gBAAA,IAAA;;AACA,gBAAA,IAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,mBAAA,QAAA,CAAA,IAAA,CAAA,mBAAA;AACA;AACA;;AACA,YAAA,iBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,QAAA,EAAA,GAAA,iBAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA,eAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,OAAA,GAAA,IAAA;;AACA,YAAA,YAAA,CAAA;AAAA,cAAA,GAAA,EAAA;AAAA,aAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,OAAA,GAAA,KAAA;;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,cAAA,MAAA,CAAA,eAAA;AACA,aAJA;;AAKA,YAAA,MAAA,CAAA,oBAAA,GAAA,EAAA;AACA;AAXA,SAAA;AAaA,OAtBA,MAsBA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AACA;AAEA,KAtDA;;AAwDA;AACA,IAAA,SAzDA,uBAyDA;AAAA;;AACA,UAAA,KAAA,oBAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,GAAA,GAAA,KAAA,oBAAA,CAAA,CAAA,CAAA;AACA,QAAA,YAAA,CAAA,GAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,cAAA,SAAA,GAAA,EAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,iBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,cAAA,SAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,SAAA,GAAA,GAAA;AACA;;AACA,YAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AAAA,cAAA,KAAA,EAAA,MAAA;AAAA,cAAA,OAAA,EAAA,WAAA,SAAA,GAAA;AAAA,aAAA;AACA,WALA,MAKA;AACA,YAAA,MAAA,CAAA,cAAA,CAAA;AACA,cAAA,KAAA,EAAA,YAAA,CAAA,OAAA,EAAA,WAAA;AADA,aAAA;;AAGA,YAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,cAAA,IAAA,EAAA,WADA;AAEA,cAAA,KAAA,EAAA;AAAA,gBAAA,EAAA,EAAA,GAAA,CAAA;AAAA,eAFA;AAGA,cAAA,OAAA,EAAA;AAHA,aAAA;AAKA;AACA,SAlBA,EAkBA,KAlBA,CAkBA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,SApBA;AAqBA,OAvBA,MAuBA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AACA;AACA,KApFA;;AAsFA;AACA,IAAA,QAvFA,sBAuFA;AAEA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,UADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA;AAHA,OAAA;AAKA,KA9FA;;AA+FA;AACA,IAAA,SAhGA,qBAgGA,GAhGA,EAgGA;AACA,WAAA,KAAA,CAAA,aAAA,CAAA,SAAA,CAAA,GAAA,CAAA,EAAA;AACA,KAlGA;AAmGA,IAAA,cAnGA,4BAmGA;AACA,WAAA,oBAAA,GAAA,EAAA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,KAAA,CAAA,KAAA,WAAA;AACA,WAAA,WAAA,CAAA,WAAA,GAAA,KAAA,WAAA;AACA,KAzGA;AA0GA,IAAA,eA1GA,6BA0GA;AACA,WAAA,oBAAA,GAAA,EAAA;;AACA,UAAA,KAAA,WAAA,CAAA,WAAA,IAAA,EAAA,EAAA;AACA,aAAA,WAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,KAAA,CAAA,KAAA,WAAA;AACA,KAhHA;AAiHA,IAAA,WAjHA,yBAiHA;AAAA;AAAA;AAAA;;AAAA;AACA,8BAAA,KAAA,KAAA,CAAA,UAAA,CAAA,UAAA,mIAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CADA,CACA;AACA;AACA;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KAvHA;AAwHA,IAAA,eAxHA,2BAwHA,GAxHA,EAwHA;AAAA;;AACA,WAAA,oBAAA,GAAA,EAAA;AACA,MAAA,GAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,MAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA7HA;AA8HA,IAAA,eA9HA,2BA8HA,GA9HA,EA8HA;AACA,UAAA,SAAA,GAAA,WAAA;AACA,UAAA,SAAA,GAAA,UAAA,GAAA,CAAA,WAAA,GAAA,KAAA;;AACA,UAAA,GAAA,CAAA,UAAA,KAAA,CAAA,EAAA;AACA,QAAA,SAAA,GAAA,cAAA;AACA,QAAA,SAAA,GAAA,UAAA,GAAA,CAAA,WAAA,GAAA,KAAA;AACA;;AACA,WAAA,cAAA,GAAA;AACA,QAAA,MAAA,EAAA,GAAA,CAAA,EADA;AAEA,QAAA,SAAA,EAAA,SAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OAAA;AAKA,UAAA,IAAA,GAAA,IAAA;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,QADA;AAEA,QAAA,OAAA,EAAA,gBAAA,GAAA,CAAA,WAAA,GAAA,WAFA;AAGA,QAAA,IAAA,EAAA,gBAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA,UAAA,UAAA,CAAA,YAAA;AACA,YAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,OAAA;AACA,WAFA,EAEA,GAFA,CAAA;AAGA,SARA;AAQA,QAAA,QAAA,EAAA,oBAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA;AAVA,OAAA;AAYA,KAvJA;AAwJA,IAAA,SAxJA,qBAwJA,GAxJA,EAwJA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,YAAA,CAAA,GAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,YAAA,SAAA,GAAA,EAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,SAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,SAAA,GAAA,GAAA;AACA;;AACA,UAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA,WAAA,SAAA,GAAA;AAAA,WAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA,SANA,MAMA,IAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,GAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,UAAA,cAAA,CAAA;AAAA,YAAA,EAAA,EAAA,GAAA,CAAA;AAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,KAAA;;AACA,gBAAA,IAAA,CAAA,IAAA,CAAA,EAAA,IAAA,IAAA,IAAA,IAAA,CAAA,IAAA,CAAA,EAAA,IAAA,SAAA,EAAA;AACA,cAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AAAA,gBAAA,KAAA,EAAA,MAAA;AAAA,gBAAA,OAAA,EAAA;AAAA,eAAA;AACA,aAFA,MAEA;AACA,cAAA,IAAA,CAAA,eAAA,CAAA,GAAA;AACA;AACA,WARA;AASA,SAVA,MAUA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA,UAAA,IAAA,CAAA,eAAA,CAAA,GAAA;AACA;AACA,OAvBA;AAwBA,KAlLA;AAmLA,IAAA,QAnLA,oBAmLA,GAnLA,EAmLA,UAnLA,EAmLA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,WAAA,SAAA,GAAA;AACA,QAAA,MAAA,EAAA,GAAA,CAAA,EADA;AAEA,QAAA,QAAA,EAAA,GAAA,CAAA,SAFA;AAGA,QAAA,UAAA,EAAA;AAHA,OAAA;AAKA,KA1LA;AA2LA,IAAA,UA3LA,sBA2LA,IA3LA,EA2LA;AAAA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,KAAA;AACA,KA9LA;AA+LA,IAAA,KA/LA,iBA+LA,MA/LA,EA+LA;AAAA;;AACA,WAAA,KAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,SAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,IAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,OAJA;AAKA,KAtMA;AAuMA,IAAA,cAvMA,0BAuMA,IAvMA,EAuMA;AACA,UAAA,IAAA,GAAA,EAAA;AAAA,UAAA,IAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,aAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,IAAA,CAAA,IAAA,CAAA,KAAA,aAAA,CAAA,CAAA,EAAA,KAAA;AACA,QAAA,IAAA,CAAA,IAAA,CAAA,KAAA,aAAA,CAAA,CAAA,EAAA,GAAA;AACA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,GAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,SAAA,EAAA,IAJA;AAKA,QAAA,QAAA,EAAA;AALA,OAAA;AAOA,WAAA,WAAA,CAAA,QAAA,GAAA,KAAA,KAAA,CAAA,QAAA;AACA,MAAA,KAAA,CAAA,qBAAA,CAAA,MAAA;AACA,WAAA,KAAA,CAAA,IAAA;AACA;AACA,KAxNA;AAyNA,IAAA,aAzNA,2BAyNA;AACA,WAAA,KAAA,CAAA,IAAA,CAAA;AACA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA;AACA,iBAAA,CAAA,CAAA,KAAA,EAAA,CACA,CAAA,CAAA,UAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,KAAA,EAAA;AADA;AADA,WAAA,CADA,EAMA,CAAA,CAAA,KAAA,EAAA,kBAAA,CANA,CAAA,CAAA;AAQA;AAVA,OAAA;AAYA,KAtOA;AAuOA,IAAA,SAvOA,qBAuOA,IAvOA,EAuOA;AAAA;;AACA,WAAA,aAAA;AACA,WAAA,MAAA,CAAA,GAAA,GAAA,IAAA;AACA,UAAA,MAAA,GAAA,KAAA,WAAA;;AACA,UAAA,IAAA,KAAA,SAAA,EAAA;AACA,aAAA,cAAA,CAAA,KAAA,iBAAA,CAAA,KAAA,KAAA,CAAA,IAAA,CAAA;AACA;AACA,OAHA,MAGA,IAAA,IAAA,KAAA,KAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,KAAA,MAAA,CAAA,IAAA;AACA;;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,sBADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,MAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,EAAA;AACA,cAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,UAAA,MAAA,CAAA,cAAA,CAAA,MAAA,CAAA,iBAAA,CAAA,KAAA,CAAA;AACA;AACA,OALA,EAKA,KALA,CAKA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OAPA;AAQA,KA/PA;AAgQA,IAAA,iBAhQA,6BAgQA,KAhQA,EAgQA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,KAAA,CAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,OAFA;AAGA,aAAA,KAAA;AACA,KArQA;AAsQA;AACA,IAAA,oBAvQA,kCAuQA;AACA,UAAA,KAAA,WAAA,CAAA,OAAA,IAAA,IAAA,IAAA,KAAA,WAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AAAA;AACA;;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,KAAA,WAAA,CAAA,OAAA,EAJA,CAIA;AACA,KA5QA;AA6QA,IAAA,gBA7QA,4BA6QA,IA7QA,EA6QA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,WAAA,CAAA,WAAA,GAAA,IAAA,CAAA,IAAA,CAFA,CAGA;AACA,KAjRA;AAkRA,IAAA,WAlRA,yBAkRA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,WAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,SAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,SAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,EAAA;AACA,YAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;AAAA;AACA,WAFA,MAEA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,IAAA;AACA;AACA;;AACA,QAAA,IAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AAEA,YAAA,WAAA,GAAA,IAAA,CAAA,WAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,EAAA;AACA,YAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA;AACA;AACA;;AACA,QAAA,IAAA,CAAA,OAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,QAAA,IAAA,CAAA,WAAA,CAAA,OAAA,GAAA,MAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,KAAA,CAAA;AAAA,UAAA,OAAA,EAAA,CAAA;AAAA,UAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,MAAA,CAAA,KAAA,CAAA,QAAA;AAAA,UAAA,OAAA,EAAA,MAAA,CAAA,OAAA;AAAA,UAAA,OAAA,EAAA,MAAA,CAAA;AAAA,SAAA;AACA,OAvBA;AAwBA,KA5SA;AA6SA,IAAA,IA7SA,kBA6SA;AACA,WAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,WAAA,UAAA,GAAA,KAAA,CAAA,iBAAA,CAAA,CAFA,CAEA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,QAAA,eAAA,CAAA;AAAA,UAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA;AAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,UAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,WAAA;AACA,SAHA;AAIA,OATA;AAWA;AA5TA,IApOA;AAmiBA,EAAA,OAniBA,qBAmiBA;AACA,SAAA,IAAA;AACA;AAriBA,CAAA", "sourcesContent": ["import {addQuota,viewQuota} from \"../../../api/basedata/quota/quota\";\r\n<template>\r\n    <div>\r\n        <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n<!--        <add-quota-page ref=\"addQuotaPage\" ></add-quota-page>-->\r\n        <view-quota-page ref=\"viewQuotaPage\" ></view-quota-page>\r\n        <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n        <div class=\"noaccount\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"formInline\" :model=\"queryParams\">\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"电表/协议编号：\" :label-width=\"140\" prop=\"ammeterCode\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.ammeterCode\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"项目名称：\" prop=\"projectName\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.projectName\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"单据状态：\" prop=\"billStatus\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-select v-model=\"queryParams.billStatus\"\r\n                                           category=\"basicBillStatus\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n<!--                        <Col span=\"5\" v-if=\"isProAdmin\">-->\r\n<!--                            <FormItem label=\"所属分公司：\" prop=\"company\" :label-width=\"100\" class=\"form-line-height\">-->\r\n<!--                                <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\">-->\r\n<!--                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>-->\r\n<!--                                </Select>-->\r\n<!--                            </FormItem>-->\r\n<!--                        </Col>-->\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n<!--                            <FormItem label=\"所属部门\" prop=\"country\">-->\r\n<!--                                <Select v-model=\"queryParams.country\">-->\r\n<!--                                    <Option value=\"-1\">全部</Option>-->\r\n<!--                                    <Option v-for=\"item in departments\" :value=\"item.id\" >{{item.name}}</Option>-->\r\n<!--                                </Select>-->\r\n<!--                            </FormItem>-->\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" :label-width=\"140\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"queryParams.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\" :label-width=\"140\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.country\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <div class=\"form-line-height\">\r\n                                <Button style=\"margin-left: 5px;width:69px;\" type=\"success\"  icon=\"ios-search\" @click=\"_onSearchHandle()\" >搜索 </Button>\r\n                                <Button style=\"margin-left: 5px;width:69px;\" type=\"info\" icon=\"ios-redo\" @click=\"_onResetHandle\" >重置</Button>\r\n                            </div>\r\n                        </Col>\r\n                    </Row>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <cl-table ref=\"quotaTable\"\r\n                  :searchLayout=\"quota.filter\"\r\n                  :query-params=\"queryParams\"\r\n                  :columns=\"quota.columns\"\r\n                  :data=\"quota.data\"\r\n                  select-enabled\r\n                  select-multiple\r\n                  @on-selection-change=\"handleSelectRow\"\r\n                  :loading=\"quota.loading\"\r\n                  :total=\"quota.total\"\r\n                  :pageSize=\"quota.pageSize\"\r\n                  :searchable=\"false\"\r\n                  :exportable=\"false\"\r\n                  @on-query=\"query\">\r\n            <div slot=\"buttons\">\r\n                <Button type=\"primary\" @click=\"addQuota\">添加</Button>\r\n                <Button type=\"success\" v-if=\"'sc' == version\" @click=\"editQuota\">修改</Button>\r\n                <Button type=\"error\"   v-if=\"'sc' == version\" @click=\"removeQuota\">删除</Button>\r\n                <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n                    <Button type='default' style=\"margin-left: 5px\" >导出\r\n                        <Icon type='ios-arrow-down'></Icon>\r\n                    </Button>\r\n                    <DropdownMenu slot='list'>\r\n                        <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                        <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n                    </DropdownMenu>\r\n                </Dropdown>\r\n            </div>\r\n        </cl-table>\r\n        <cl-wf-btn ref=\"clwfbtn\" :isStart=\"true\" :params=\"workFlowParams\" @on-ok=\"doWorkFlow\" v-show=\"false\"></cl-wf-btn>\r\n        <!-- 查看流程 -->\r\n        <Modal v-model=\"showWorkFlow\" title=\"定额流程及审批意见跟踪表\" :width=\"800\">\r\n            <WorkFlowInfoComponet :wfHisParams=\"hisParams\" v-if=\"showWorkFlow\"></WorkFlowInfoComponet>\r\n        </Modal>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import {getUserByUserRole,getCountryByUserId,getUserdata,getCountrysdata} from '@/api/basedata/ammeter.js'\r\n    import {isInTodoList}from\"@/api/alertcontrol/alertcontrol\";\r\n    import {removeQuota,listQuota,checkStartFlow} from '@/api/basedata/quota/quota.js';\r\n    import {blist,btext} from \"@/libs/tools\";\r\n    import viewQuotaPage from './viewQuota.vue';\r\n    import countryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import ProcessInfo from '@/view/basic/system/workflow/process-info';\r\n    import WorkFlowInfoComponet from '@/view/basic/system/workflow/workFlowInfoComponet'\r\n    import axios from '@/libs/api.request';\r\n    import excel from '@/libs/excel'\r\n    import {mapMutations} from \"vuex\";\r\n    import routers from '@/router/routers';\r\n    import {getHomeRoute} from '@/libs/util';\r\n    import indexData from '@/config/index'\r\n\r\nexport default {\r\n    name: 'quota',\r\n    components: {\r\n        ProcessInfo,\r\n        WorkFlowInfoComponet,\r\n        viewQuotaPage,\r\n        countryModal\r\n    },\r\n\r\n    data() {\r\n        //查看详情\r\n        let renterViewQuota = (h, params) => {\r\n            let column = params.column.key;\r\n            return h(\"div\", [h(\"u\", {\r\n                on: {\r\n                    click: () => {\r\n                        this.viewQuota(params.row);\r\n                    }\r\n                }\r\n            }, params.row[column])]);\r\n        };\r\n        let renderW = (h, params) => {\r\n            let that = this;\r\n            let text, type = \"\";\r\n            if (params.row.billStatus != 0 && params.row.billStatus != 3 && params.row.processinstId != null) {\r\n                text = \"查看\";\r\n                type = \"success\";\r\n            } else if (params.row.billStatus == 0 || params.row.billStatus == 3) {\r\n                text = \"提交\";\r\n                type = \"primary\";\r\n            }\r\n            if(type == \"\"){\r\n                return h(\"div\", {}, text);\r\n            }\r\n            return h(\"Button\", {\r\n                props: {\r\n                    type: type, size: \"small\"\r\n                }, on: {\r\n                    click() {\r\n                        if (params.row.billStatus!=0 && params.row.billStatus != 3 && params.row.processinstId != null) {\r\n                            that.showFlow(params.row, params.row.processinstId);\r\n                        } else if (params.row.billStatus == 0 || params.row.billStatus == 3) {\r\n                            that.loading = true;\r\n                            that.startFlow(params.row);\r\n                        }\r\n                    }\r\n                }\r\n            }, text);\r\n        };\r\n        //单据状态\r\n        let renderBillStatus = (h, params) => {\r\n            let value = \"\";\r\n            for (let item of this.billStatus) {\r\n                if (item.typeCode == params.row.billStatus) {\r\n                    value = item.typeName;\r\n                    break;\r\n                }\r\n            }\r\n            return h(\"div\", value);\r\n        };\r\n        return {\r\n            loading:false,//提交按钮重复提交\r\n            filterColl: true,//搜索面板\r\n            isAdmin:false,\r\n            isProAdmin:false,\r\n            company:null,//用户默认公司\r\n            country:null,//用户默认所属部门\r\n            countryName:null,//用户默认所属部门\r\n            version:'',\r\n            billStatus:[],//单据状态\r\n            demoList: [],\r\n            companies:[],\r\n            departments:[],\r\n            queryParams:{country:null,company:null,countryName:null},\r\n            multipleSelectionRow: [],\r\n\r\n            workFlowParams: {},\r\n            hisParams: {},\r\n            showWorkFlow: false,\r\n            exportColumns:[{title: '电表/协议编号',key: 'ammeterCode'},\r\n                {title: '项目名称',key: 'projectName'},\r\n                {title: '单据状态',key:'billStatusStr'},\r\n                {title: '1月定额值（度）',key: 'janQuotaValue'},\r\n                {title: '2月定额值（度）',key: 'febQuotaValue'},\r\n                {title: '3月定额值（度）',key: 'marQuotaValue'},\r\n                {title: '4月定额值（度）',key: 'aprQuotaValue'},\r\n                {title: '5月定额值（度）',key: 'mayQuotaValue'},\r\n                {title: '6月定额值（度）',key: 'junQuotaValue'},\r\n                {title: '7月定额值（度）',key: 'julQuotaValue'},\r\n                {title: '8月定额值（度）',key: 'augQuotaValue'},\r\n                {title: '9月定额值（度）',key: 'sepQuotaValue'},\r\n                {title: '10月定额值（度）',key: 'octQuotaValue'},\r\n                {title: '11月定额值（度）',key: 'novQuotaValue'},\r\n                {title: '12月定额值（度）',key: 'decQuotaValue'}],\r\n            export: {\r\n                run: false,//是否正在执行导出\r\n                data: \"\",//导出数据\r\n                totalPage: 0,//一共多少页\r\n                currentPage: 0,//当前多少页\r\n                percent: 0,\r\n                size: 200000\r\n            },\r\n            quota: {\r\n                loading: false,\r\n                columns: [\r\n                    {\r\n                        title: '电表/协议编号',\r\n                        key: 'ammeterCode',\r\n                        className: \"td-id\",\r\n                        render: renterViewQuota,\r\n                        align: 'center',\r\n                        minWidth:130,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '项目名称',\r\n                        key: 'projectName',\r\n                        align: 'center',\r\n                        minWidth:130,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '单据状态',\r\n                        align: 'center',\r\n                        render: renderBillStatus,\r\n                        minWidth:80,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '1月定额值（度）',\r\n                        key: 'janQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '2月定额值（度）',\r\n                        key: 'febQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '3月定额值（度）',\r\n                        key: 'marQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '4月定额值（度）',\r\n                        key: 'aprQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '5月定额值（度）',\r\n                        key: 'mayQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '6月定额值（度）',\r\n                        key: 'junQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '7月定额值（度）',\r\n                        key: 'julQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '8月定额值（度）',\r\n                        key: 'augQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '9月定额值（度）',\r\n                        key: 'sepQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '10月定额值（度）',\r\n                        key: 'octQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '11月定额值（度）',\r\n                        key: 'novQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '12月定额值（度）',\r\n                        key: 'decQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: \"流程\",\r\n                        fixed: 'right',\r\n                        key: \"action\",\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200,\r\n                        render: renderW\r\n                    }\r\n                ],\r\n                data: [],\r\n                total: 0,\r\n                pageSize: 10\r\n            }\r\n        }\r\n    },\r\n    methods: {\r\n        ...mapMutations([\"closeTag\", \"closeTagByName\"]),\r\n        onModalOK() {\r\n            this.$Message.error('确定')\r\n        },\r\n        onModalCancel() {\r\n            this.$Message.error('取消')\r\n        },\r\n        selectChange(){\r\n            let that = this;\r\n            if (this.queryParams.company != undefined) {\r\n                if(this.queryParams.company == \"-1\"){\r\n                    that.queryParams.country = -1;\r\n                    that.queryParams.countryName = null;\r\n                }else{\r\n                    getCountryByUserId(that.queryParams.company).then(res => {\r\n                        if(res.data.departments.length != 0){\r\n                            that.queryParams.country = res.data.departments[0].id;\r\n                            that.queryParams.countryName = res.data.departments[0].name;\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n\r\n        /*删除*/\r\n        removeQuota(id){\r\n            let multipleSelection = [];\r\n            if (this.multipleSelectionRow.length > 0) {\r\n                for(let item of this.multipleSelectionRow){\r\n                    if(item.billStatus != 0){\r\n                        this.$Message.info(\"所选数据包含非草稿数据，不能删除！\");\r\n                        return ;\r\n                    }\r\n                    multipleSelection.push(item.id);\r\n                }\r\n                id = multipleSelection.join(',');\r\n                this.$Modal.confirm({\r\n                    title: '温馨提示',\r\n                    content: '<p>确认删除吗?</p>',\r\n                    onOk: () => {\r\n                        this.quota.loading = true;\r\n                        removeQuota({ids: id}).then(res => {\r\n                            this.quota.loading = false;\r\n                            this.$Message.success(\"删除成功\");\r\n                            this._onSearchHandle();\r\n                        });\r\n                        this.multipleSelectionRow = [];\r\n                    },\r\n                });\r\n            } else {\r\n                this.$Message.info(\"请至少选择一行\");\r\n            }\r\n\r\n        },\r\n\r\n        /*编辑*/\r\n        editQuota() {\r\n            if (this.multipleSelectionRow.length == 1) {\r\n                let row = this.multipleSelectionRow[0];\r\n                isInTodoList(row.id,3).then(res=>{\r\n                    //存在于代办中时，报出提示\r\n                    let ownername=\"\";\r\n                    if(res.data.length>0) {\r\n                        for (let i = 0; i < res.data.length; i++) {\r\n                            ownername += res.data[i].ownername + ' ';\r\n                        }\r\n                        this.$Modal.warning({title:\"温馨提示\",content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可修改数据\"});\r\n                    }else{\r\n                        this.closeTagByName({\r\n                            route: getHomeRoute(routers, \"editQuota\"),\r\n                        });\r\n                        this.$router.push({\r\n                            name: \"editQuota\",\r\n                            query:{id:row.id},\r\n                            replace:true\r\n                        })\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            } else {\r\n                this.$Message.info(\"请选择其中一行\");\r\n            }\r\n        },\r\n\r\n        /*添加*/\r\n        addQuota() {\r\n\r\n            this.$router.push({\r\n                name: \"addQuota\",\r\n                query:{},\r\n                replace:true\r\n            })\r\n        },\r\n        /*查看*/\r\n        viewQuota(row) {\r\n            this.$refs.viewQuotaPage.initQuota(row.id);\r\n        },\r\n        _onResetHandle(){\r\n            this.multipleSelectionRow=[];\r\n            this.queryParams.company= this.company;\r\n            this.queryParams.country= this.country;\r\n            this.$refs.quotaTable.query(this.queryParams);\r\n            this.queryParams.countryName = this.countryName;\r\n        },\r\n        _onSearchHandle(){\r\n            this.multipleSelectionRow=[];\r\n            if(this.queryParams.countryName == \"\"){\r\n                this.queryParams.country = \"-1\";\r\n            }\r\n            this.$refs.quotaTable.query(this.queryParams);\r\n        },\r\n        setDisabled(){\r\n            for(let item of this.$refs.quotaTable.insideData){\r\n                if(item.billStatus != 0){\r\n                    item._disabled = true;//禁止选择\r\n                }\r\n            }\r\n        },\r\n        handleSelectRow(val){\r\n            this.multipleSelectionRow = [];\r\n            val.forEach(item => {\r\n                this.multipleSelectionRow.push(item);\r\n            });\r\n        },\r\n        startFlowSubmit(row){\r\n            let busiAlias = \"ADD_QUOTA\";\r\n            let busiTitle = \"新增定额(\" + row.projectName + \")审批\";\r\n            if (row.billStatus === 3) {\r\n                busiAlias = \"MODIFY_QUOTA\";\r\n                busiTitle = \"修改定额(\" + row.projectName + \")审批\";\r\n            }\r\n            this.workFlowParams = {\r\n                busiId: row.id,\r\n                busiAlias: busiAlias,\r\n                busiTitle: busiTitle\r\n            };\r\n            let that = this;\r\n            this.$Modal.confirm({\r\n                title: '定额提交流程',\r\n                content: '<p>是否提交定额 (' + row.projectName + ') 到流程</p>',\r\n                onOk: () => {\r\n                    that.loading = true;\r\n                    setTimeout(function () {\r\n                        that.$refs.clwfbtn.onClick();\r\n                    }, 300);\r\n                },onCancel: () => {\r\n                    that.loading = false;\r\n                }\r\n            });\r\n        },\r\n        startFlow(row) {\r\n            let that = this;\r\n            isInTodoList(row.id,3).then(res => {\r\n                //存在于代办中时，报出提示\r\n                let ownername = \"\";\r\n                if (res.data.length > 0) {\r\n                    for (let i = 0; i < res.data.length; i++) {\r\n                        ownername += res.data[i].ownername + ' ';\r\n                    }\r\n                    that.$Modal.warning({title:\"温馨提示\",content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可继续提交流程\"});\r\n                    that.loading = false;\r\n                }else if(row.billStatus == 3 || row.billStatus == 4){\r\n                    checkStartFlow({id:row.id}).then(res1 => {\r\n                        /*提交流程验证用户是否有数据需要提交*/\r\n                        that.loading = false;\r\n                        if (res1.data.id == null || res1.data.id == undefined) {\r\n                            that.$Modal.warning({title:\"温馨提示\",content: \"您没有可提交的数据\"});\r\n                        }else{\r\n                            that.startFlowSubmit(row);\r\n                        }\r\n                    });\r\n                }else{\r\n                    that.loading = false;\r\n                    that.startFlowSubmit(row);\r\n                }\r\n            });\r\n        },\r\n        showFlow(row, procInstId) {\r\n            this.showWorkFlow = true;\r\n            this.hisParams = {\r\n                busiId: row.id,\r\n                busiType: row.busiAlias,\r\n                procInstId: procInstId\r\n            }\r\n        },\r\n        doWorkFlow(data) { //流程回调\r\n            this.loading = false;\r\n            this.$refs.quotaTable.query();\r\n        },\r\n        query(params) {\r\n            this.quota.loading = true;\r\n            listQuota(params).then(res => {\r\n                this.quota.loading = false;\r\n                this.quota.total = res.data.total\r\n                this.quota.data = Object.assign([], res.data.rows)\r\n            });\r\n        },\r\n        beforeLoadData(data) {\r\n            let cols=[],keys=[]\r\n            for (let i = 0; i < this.exportColumns.length; i++) {\r\n                cols.push(this.exportColumns[i].title)\r\n                keys.push(this.exportColumns[i].key)\r\n            }\r\n            const params = {\r\n                title: cols,\r\n                key: keys,\r\n                data: data,\r\n                autoWidth: true,\r\n                filename: '定额数据导出'\r\n            };\r\n            this.queryParams.pageSize = this.quota.pageSize;\r\n            excel.export_array_to_excel(params);\r\n            this.$Spin.hide();\r\n            return\r\n        },\r\n        exportLoading(){\r\n            this.$Spin.show({\r\n                render: (h) => {\r\n                    return h('div', [\r\n                        h('Progress', {\r\n                            style: {\r\n                                width: '800px'\r\n                            },\r\n                        }),\r\n                        h('div', '导出中，请勿刷新页面......')\r\n                    ])\r\n                }\r\n            });\r\n        },\r\n        exportCsv(name) {\r\n            this.exportLoading();\r\n            this.export.run = true;\r\n            let params = this.queryParams;\r\n            if (name === 'current') {\r\n                this.beforeLoadData(this.setValueByForEach(this.quota.data));\r\n                return;\r\n            } else if (name === 'all') {\r\n                params.pageNum = 1;\r\n                params.pageSize = this.export.size\r\n            }\r\n            let req = {\r\n                url : \"/business/quota/list\",\r\n                method : \"get\",\r\n                params : params\r\n            };\r\n            axios.request(req).then(res => {\r\n                if (res.data) {\r\n                    let array = res.data.rows;\r\n                    this.beforeLoadData(this.setValueByForEach(array))\r\n                }\r\n            }).catch(err => {\r\n                console.log(err);\r\n            });\r\n        },\r\n        setValueByForEach(array){\r\n            array.forEach(function (item) {\r\n                item.billStatusStr = btext(\"basicBillStatus\", item.billStatus,'typeCode','typeName');\r\n            });\r\n            return array;\r\n        },\r\n        //选择所属部门开始\r\n        chooseResponseCenter() {\r\n            if(this.queryParams.company == null || this.queryParams.company == \"-1\"){\r\n                this.$Message.info(\"请先选择分公司\");return;\r\n            }\r\n            this.$refs.countryModal.choose(this.queryParams.company);//所属部门\r\n        },\r\n        getDataFromModal(data) {\r\n            this.queryParams.country = data.id;\r\n            this.queryParams.countryName = data.name;\r\n            //选择所属部门结束\r\n        },\r\n        getUserData(){\r\n            let that = this;\r\n            getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                let companies = that.companies;\r\n                if(res.data.companies != null && res.data.companies.length != 0){\r\n                    if(res.data.companies[0].id != \"2600000000\"){\r\n                        companies = res.data.companies;;\r\n                    }else{\r\n                        that.isProAdmin = true;\r\n                    }\r\n                }\r\n                that.company = companies[0].id;\r\n                that.queryParams.company = companies[0].id;\r\n\r\n                let departments = that.departments;\r\n                if(res.data.departments != null && res.data.departments.length != 0){\r\n                    if(res.data.companies[0].id != \"2600000000\"){\r\n                        departments = res.data.departments;\r\n                    }\r\n                }\r\n                that.country = departments[0].id;\r\n                that.countryName = departments[0].name;\r\n                that.queryParams.country = Number(departments[0].id);\r\n                that.queryParams.countryName = departments[0].name;\r\n                this.query({pageNum: 1,type:0,pageSize: this.quota.pageSize,company:this.company,country:this.country});\r\n            });\r\n        },\r\n        init() {\r\n            this.version = indexData.version\r\n            this.billStatus = blist(\"basicBillStatus\");//单据状态\r\n            let that = this;\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                    that.getUserData();\r\n                });\r\n            });\r\n\r\n        },\r\n    },\r\n\r\n    mounted() {\r\n        this.init();\r\n    },\r\n    // watch:{\r\n    //     '$route':\"init\"\r\n    // },\r\n\r\n}\r\n</script>\r\n\r\n\r\n<style lang=\"less\">\r\n    td.td-id {\r\n        font-weight: bold;\r\n        color: green;\r\n        cursor: pointer;\r\n    }\r\n    .noaccount .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .noaccount .header-bar-show {\r\n        max-height: 300px;\r\n        /*padding-top: 14px;*/\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .noaccount .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n    .form-line-height{\r\n        margin-bottom:10px;\r\n    }\r\n</style>"], "sourceRoot": "src/view/basedata/quota"}]}