{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\editAmmeter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\editAmmeter.vue", "mtime": 1754285403018}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0RWxlY3RyaWNUeXBlLA0KICBjaGVja0FtbWV0ZXJFeGlzdCwNCiAgZ2V0Q291bnRyeXNkYXRhLA0KICBlZGl0QW1tZXRlciwNCiAgZWRpdEFtbWV0ZXJSZWNvcmQsDQogIHVwZGF0ZUFtbWV0ZXIsDQogIGNoZWNrUHJvamVjdE5hbWVFeGlzdCwNCiAgY2hlY2tBbW1ldGVyQnlTdGF0aW9uLA0KICBnZXRDbGFzc2lmaWNhdGlvbiwNCiAgZ2V0Q2xhc3NpZmljYXRpb25JZCwNCiAgZ2V0VXNlcmRhdGEsDQogIGNoZWNrQ2xhc3NpZmljYXRpb25MZXZlbCwNCiAgbGlzdEVsZWN0cmljVHlwZVJhdGlvLA0KICBjaGVja0Fjb3VudEJ5VXBkYXRlLA0KICBnZXRVc2VyQnlVc2VyUm9sZSwNCiAgZ2V0Q291bnRyeUJ5VXNlcklkLA0KICByZW1vdmVBdHRhY2gsDQogIGF0dGNoTGlzdCwNCiAgY2hlY2tTdGF0aW9uLA0KICBnZXRCYW5rQ2FyZCwNCn0gZnJvbSAiQC9hcGkvYmFzZWRhdGEvYW1tZXRlci5qcyI7DQppbXBvcnQgeyBpc0luVG9kb0xpc3QsIGdldHN0YXRpb25vbGQgfSBmcm9tICJAL2FwaS9hbGVydGNvbnRyb2wvYWxlcnRjb250cm9sIjsNCmltcG9ydCB7IGJsaXN0LCBidGV4dCB9IGZyb20gIkAvbGlicy90b29scyI7DQppbXBvcnQgU2VsZWN0RWxlY3RyaWNUeXBlIGZyb20gIi4vc2VsZWN0RWxlY3RyaWNUeXBlIjsNCmltcG9ydCBjb3VudHJ5TW9kYWwgZnJvbSAiLi9jb3VudHJ5TW9kYWwiOw0KaW1wb3J0IHN0YXRpb25Nb2RhbCBmcm9tICIuL3N0YXRpb25Nb2RhbCI7DQppbXBvcnQgeyBpc0VtcHR5IH0gZnJvbSAiQC9saWJzL3ZhbGlkYXRlIjsNCi8vIGltcG9ydCBzdGF0aW9uTE5Nb2RhbCBmcm9tICIuL3N0YXRpb25Nb2RhbExOIjsNCmltcG9ydCB7IG1hcE11dGF0aW9ucyB9IGZyb20gInZ1ZXgiOw0KaW1wb3J0IHJvdXRlcnMgZnJvbSAiQC9yb3V0ZXIvcm91dGVycyI7DQppbXBvcnQgeyBnZXRIb21lUm91dGUgfSBmcm9tICJAL2xpYnMvdXRpbCI7DQppbXBvcnQgV29ya0Zsb3dJbmZvQ29tcG9uZXQgZnJvbSAiQC92aWV3L2Jhc2ljL3N5c3RlbS93b3JrZmxvdy93b3JrRmxvd0luZm9Db21wb25ldCI7DQppbXBvcnQgQW1tZXRlclByb3RvY29sTGlzdCBmcm9tICJAL3ZpZXcvYmFzZWRhdGEvcXVvdGEvbGlzdEFtbWV0ZXJQcm90b2NvbCI7DQppbXBvcnQgY3VzdG9tZXJMaXN0IGZyb20gIi4vY3VzdG9tZXJNb2RhbCI7DQppbXBvcnQgQ2hvb3NlTW9kYWwgZnJvbSAiQC92aWV3L2J1c2luZXNzL2dhc0J1c2luZXNzL2Nob29zZU1vZGFsIjsNCmltcG9ydCBDaG9vc2VBbW1ldGVyTW9kZWwgZnJvbSAiQC92aWV3L2Jhc2VkYXRhL2FtbWV0ZXIvY2hvb3NlQW1tZXRlck1vZGVsIjsNCmltcG9ydCBhdHRhY2hGaWxlIGZyb20gIi4vLi4vcHJvdG9jb2wvYXR0YWNoRmlsZSI7DQppbXBvcnQgYXhpb3MgZnJvbSAiQC9saWJzL2FwaS5yZXF1ZXN0IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAidXBkYXRlQW1tZXRlciIsDQogIGNvbXBvbmVudHM6IHsNCiAgICBhdHRhY2hGaWxlLA0KICAgIHN0YXRpb25Nb2RhbCwNCiAgICBjdXN0b21lckxpc3QsDQogICAgY291bnRyeU1vZGFsLA0KICAgIFNlbGVjdEVsZWN0cmljVHlwZSwNCiAgICBXb3JrRmxvd0luZm9Db21wb25ldCwNCiAgICBBbW1ldGVyUHJvdG9jb2xMaXN0LA0KICAgIENob29zZUFtbWV0ZXJNb2RlbCwNCiAgICBDaG9vc2VNb2RhbCwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICAvL+S4jeiDvei+k+WFpeaxieWtlw0KICAgIGNvbnN0IGNoZWNrRGF0YSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGlmICh2YWx1ZSkgew0KICAgICAgICBpZiAoL1tcdTRFMDAtXHU5RkE1XS9nLnRlc3QodmFsdWUpKSB7DQogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLnvJbnoIHkuI3og73ovpPlhaXmsYnlrZchIikpOw0KICAgICAgICB9IGVsc2UgaWYgKGVzY2FwZSh2YWx1ZSkuaW5kZXhPZigiJXUiKSA+PSAwKSB7DQogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLnvJbnoIHkuI3og73ovpPlhaXkuK3mloflrZfnrKYhIikpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNhbGxiYWNrKCk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGNhbGxiYWNrKCk7DQogICAgfTsNCiAgICBjb25zdCB2YWxpZGF0b3JOdW1iZXIgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBpZiAodmFsdWUubGVuZ3RoIDw9IDApIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLkuI3og73kuLrnqboiKSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjYWxsYmFjaygpOw0KICAgICAgfQ0KICAgIH07DQogICAgY29uc3QgdmFsaWRhdG9yTnVtYmVyWmVybyA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGlmICh2YWx1ZSAhPSBudWxsICYmIHZhbHVlID09IDApIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLlj6rog73ovpPlhaXlpKfkuo4w55qE5pWwIikpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgIH0NCiAgICB9Ow0KICAgIGNvbnN0IHZhbGlkYXRvck51bWJlclplcm8xID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgaWYgKHZhbHVlICE9IG51bGwgJiYgdmFsdWUgPCAwKSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5Y+q6IO96L6T5YWl5aSn5LqO562J5LqOMOeahOaVsCIpKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICB9DQogICAgfTsNCiAgICBjb25zdCB2YWxpZGF0ZUNsYXNzaWZpY2F0aW9ucyA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGlmICh2YWx1ZSA9PSB1bmRlZmluZWQgfHwgdmFsdWUgPT0gbnVsbCkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuS4jeiDveS4uuepuiIpKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmICh2YWx1ZS5sZW5ndGggPD0gMCkgew0KICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5LiN6IO95Li656m6IikpOw0KICAgICAgICB9DQogICAgICB9DQogICAgICBjYWxsYmFjaygpOw0KICAgIH07DQogICAgY29uc3QgdmFsaWRhdGVsdW1wc3RhcnRkYXRlID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLmFtbWV0ZXI7DQogICAgICBsZXQgc3RhcnQgPSBkYXRhLmx1bXBzdGFydGRhdGU7DQogICAgICBsZXQgZW5kID0gZGF0YS5sdW1wZW5kZGF0ZTsNCiAgICAgIGlmIChzdGFydCA9PSBudWxsKSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5LiN6IO95Li656m6IikpOw0KICAgICAgfQ0KICAgICAgaWYgKHN0YXJ0ICE9IG51bGwgJiYgZW5kICE9IG51bGwpIHsNCiAgICAgICAgaWYgKGVuZCA8PSBzdGFydCkgew0KICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5YyF5bmy6LW35aeL5pel5pyf5LiN6IO95aSn5LqO562J5LqO5oiq5q2i5pel5pyfIikpOw0KICAgICAgICB9DQogICAgICB9DQogICAgICBjYWxsYmFjaygpOw0KICAgIH07DQogICAgY29uc3QgdmFsaWRhdGVsdW1wZW5kZGF0ZSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy5hbW1ldGVyOw0KICAgICAgbGV0IHN0YXJ0ID0gZGF0YS5sdW1wc3RhcnRkYXRlOw0KICAgICAgbGV0IGVuZCA9IGRhdGEubHVtcGVuZGRhdGU7DQogICAgICBpZiAoZW5kID09IG51bGwpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLkuI3og73kuLrnqboiKSk7DQogICAgICB9DQogICAgICBpZiAoc3RhcnQgIT0gbnVsbCAmJiBlbmQgIT0gbnVsbCkgew0KICAgICAgICBpZiAoZW5kIDw9IHN0YXJ0KSB7DQogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLljIXlubLmiKrmraLml6XmnJ/kuI3og73lsI/kuo7nrYnkuo7otbflp4vml6XmnJ8iKSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGNhbGxiYWNrKCk7DQogICAgfTsNCiAgICAvL+abtOaUueagh+mimOWQjeensOWPiuagt+W8jw0KICAgIGxldCByZW5kZXJIZWFkZXIgPSAoaCwgcGFyYW1zKSA9PiB7DQogICAgICBsZXQgdCA9IGgoDQogICAgICAgICJzcGFuIiwNCiAgICAgICAgew0KICAgICAgICAgIHN0eWxlOiB7DQogICAgICAgICAgICBmb250V2VpZ2h0OiAibm9ybWFsIiwNCiAgICAgICAgICAgIGNvbG9yOiAiI2VkNDAxNCIsDQogICAgICAgICAgICBmb250U2l6ZTogIjEycHgiLA0KICAgICAgICAgICAgZm9udEZhbWlseTogIlNpbVN1biIsDQogICAgICAgICAgICBtYXJnaW5SaWdodDogIjRweCIsDQogICAgICAgICAgICBsaW5lSGVpZ2h0OiAxLA0KICAgICAgICAgICAgZGlzcGxheTogImlubGluZS1ibG9jayIsDQogICAgICAgICAgfSwNCiAgICAgICAgfSwNCiAgICAgICAgIioiDQogICAgICApOw0KICAgICAgcmV0dXJuIGgoImRpdiIsIFt0LCBoKCJzcGFuIiwge30sICLmiYDljaDmr5TkvosoJSkiKV0pOw0KICAgIH07DQogICAgcmV0dXJuIHsNCiAgICAgIHByb3BlcnR5cmlnaHQ6IG51bGwsIC8v5bGA56uZ5Lqn5p2DDQogICAgICBpc1JlcXVpcmVGbGFnOiBmYWxzZSwgLy/lsYDnq5nmmK/lkKblv4XloasNCiAgICAgIG1vZGFsMTogZmFsc2UsDQogICAgICBjaGVja1N0YXRpb25UeXBlOiBudWxsLA0KICAgICAgaXNjaGVja1N0YXRpb246IGZhbHNlLCAvL+aYr+WQpumcgOimgemqjOivgeWxgOermeWPquiDveWFs+iBlDXkuKoNCiAgICAgIGlzb2xkY2hlY2tTdGF0aW9uOiBudWxsLCAvL+WIpOaWreeUqOaIt+WFs+iBlOWxgOermeayoeaciSzpu5jorqTmsqHmnIkNCiAgICAgIGlzQ0RDb21wYW55OiBmYWxzZSwgLy/mmK/lkKbmmK/miJDpg73liIblhazlj7gNCiAgICAgIGlzTW9iaWxlQmFzZTogZmFsc2UsDQogICAgICBjb25maWdWZXJzaW9uOiBudWxsLCAvL+eJiOacrA0KICAgICAgcHJvcGVydHlMaXN0OiBbXSwNCiAgICAgIHByb3BlcnR5UmVhZG9ubHk6IHRydWUsDQoNCiAgICAgIHdvcmtGbG93UGFyYW1zOiB7fSwNCiAgICAgIGhpc1BhcmFtczoge30sDQogICAgICBpc1Nob3dGbG93OiBmYWxzZSwNCiAgICAgIHNob3dXb3JrRmxvdzogZmFsc2UsDQogICAgICBmbG93TmFtZTogbnVsbCwNCg0KICAgICAgaXNFcnJvcjogZmFsc2UsIC8v55So55S157G75Z6L5q+U5L6L6aqM6K+BDQogICAgICBpc0Vycm9yMTogZmFsc2UsIC8v55So55S157G75Z6L5q+U5L6L6aqM6K+BDQoNCiAgICAgIGlzbW9kYWwxOiBudWxsLCAvL+aYr+WQpuW3sue7j+aPkOekuui/h+aYr+WQpuaWsOWei+WupOWIhg0KDQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIGlzTG9hZGluZzogbnVsbCwNCg0KICAgICAgc2hvd01vZGVsOiBmYWxzZSwNCiAgICAgIGlzQ2xhc3NpZmljYXRpb246IGZhbHNlLA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgaXNFZGl0QnlDb3VudHJ5OiBmYWxzZSwNCiAgICAgIGlzQ2l0eUFkbWluOiBmYWxzZSwNCiAgICAgIGlzQWRtaW46IGZhbHNlLA0KICAgICAgY2hvb3NlSW5kZXg6IG51bGwsDQogICAgICBlbGVjdHJvUm93TnVtOiBudWxsLCAvL+WFs+iBlOeUqOeUteexu+Wei+eahOW9k+WJjeihjA0KICAgICAgZWxlY3RyaWNUeXBlTW9kZWw6IGZhbHNlLA0KICAgICAgY29tcGFuaWVzOiBbXSwNCiAgICAgIGRlcGFydG1lbnRzOiBbXSwNCiAgICAgIGNsYXNzaWZpY2F0aW9uRGF0YTogW10sIC8v55So55S157G75Z6LDQoNCiAgICAgIG9sZERhdGE6IFtdLA0KICAgICAgb2xkQ2F0ZWdvcnk6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkUGFja2FnZXR5cGU6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkUGF5cGVyaW9kOiAiIiwgLy/ljp/lp4vmlbDmja4NCiAgICAgIG9sZFBheXR5cGU6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkRWxlY3Ryb25hdHVyZTogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRFbGVjdHJvdmFsZW5jZW5hdHVyZTogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRFbGVjdHJvdHlwZTogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRTdGF0dXM6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkUHJvcGVydHk6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkQW1tZXRlcnR5cGU6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkU3RhdGlvbnN0YXR1czogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRTdGF0aW9udHlwZTogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRBbW1ldGVydXNlOiAiIiwgLy/ljp/lp4vmlbDmja4NCiAgICAgIG9sZERpcmVjdHN1cHBseWZsYWc6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgcnVsZVZhbGlkYXRlOiB7DQogICAgICAgIGlzZW50aXR5YW1tZXRlcjogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlLGJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIHByb2plY3RuYW1lOiBbDQogICAgICAgICAgLy/pobnnm67lkI3np7ANCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvdW50cnlOYW1lOiBbDQogICAgICAgICAgLy/miYDlsZ7pg6jpl6gNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvdW50cnk6IFsNCiAgICAgICAgICAvL+aJgOWxnumDqOmXqA0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHR5cGU6ICJudW1iZXIiLCB2YWxpZGF0b3I6IHZhbGlkYXRvck51bWJlciwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvbXBhbnk6IFt7IHJlcXVpcmVkOiB0cnVlLCB2YWxpZGF0b3I6IHZhbGlkYXRvck51bWJlciwgdHJpZ2dlcjogImJsdXIiIH1dLA0KICAgICAgICBwYXl0eXBlOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICB0eXBlOiAibnVtYmVyIiwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSxibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBwYXlwZXJpb2Q6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIHR5cGU6ICJudW1iZXIiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlLGJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGFtbWV0ZXJ1c2U6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIHR5cGU6ICJudW1iZXIiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlLGJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGFtbWV0ZXJ0eXBlOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICB0eXBlOiAibnVtYmVyIiwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSxibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBwcm9wZXJ0eTogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgdHlwZTogIm51bWJlciIsDQogICAgICAgICAgICBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UsYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgZWxlY3Ryb3ZhbGVuY2VuYXR1cmU6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIHR5cGU6ICJudW1iZXIiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlLGJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGNsYXNzaWZpY2F0aW9uczogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogdmFsaWRhdGVDbGFzc2lmaWNhdGlvbnMsIHRyaWdnZXI6ICJjaGFuZ2UsYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgbWFnbmlmaWNhdGlvbjogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHR5cGU6ICJudW1iZXIiLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgcGF0dGVybjogL14oKFsxLTldXGR7MCwxNH0pfDApKFwuXGR7MCwyfSk/JC8sDQogICAgICAgICAgICBtZXNzYWdlOiAi5Y+q6IO95L+d55WZ5Lik5L2N5bCP5pWwIiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBkaXJlY3RzdXBwbHlmbGFnOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICB0eXBlOiAibnVtYmVyIiwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSxibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBwcmljZTogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgdHlwZTogIm51bWJlciIsDQogICAgICAgICAgICBwYXR0ZXJuOiAvXigoWzEtOV1cZHswLDE0fSl8MCkoXC5cZHswLDJ9KT8kLywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIHBhY2thZ2V0eXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHlwZTogIm51bWJlciIsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29udHJhY3RPdGhQYXJ0OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9XSwNCiAgICAgICAgc3RhdGlvbk5hbWU6IFtdLA0KICAgICAgICBzdGF0dXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIHR5cGU6ICJudW1iZXIiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlLGJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIHRlbGVwaG9uZTogW3sgcGF0dGVybjogL14xXGR7MTB9JC8sIG1lc3NhZ2U6ICLmoLzlvI/kuI3mraPnoa4iLCB0cmlnZ2VyOiAiYmx1ciIgfV0sDQogICAgICAgIHBlcmNlbnQ6IFsNCiAgICAgICAgICB7IHR5cGU6ICJudW1iZXIiLCB2YWxpZGF0b3I6IHZhbGlkYXRvck51bWJlclplcm8sIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHBhdHRlcm46IC9eKChbMC05XVxkezAsMTJ9KSkoXC5cZHswLDR9KT8kLywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlj6rog73kv53nlZnlm5vkvY3lsI/mlbAiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGx1bXBzdGFydGRhdGU6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIHR5cGU6ICJkYXRlIiwNCiAgICAgICAgICAgIHZhbGlkYXRvcjogdmFsaWRhdGVsdW1wc3RhcnRkYXRlLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGx1bXBlbmRkYXRlOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsDQogICAgICAgICAgICB2YWxpZGF0b3I6IHZhbGlkYXRlbHVtcGVuZGRhdGUsDQogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgZmVlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHlwZTogIm51bWJlciIsIHZhbGlkYXRvcjogdmFsaWRhdG9yTnVtYmVyLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBwYXR0ZXJuOiAvXigoWzEtOV1cZHswLDE0fSl8MCkoXC5cZHswLDJ9KT8kLywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlj6rog73kv53nlZnkuKTkvY3lsI/mlbAiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIHN1cHBseWJ1cmVhdWFtbWV0ZXJjb2RlOiBbXSwNCiAgICAgICAgdHJhbnNkaXN0cmljb21wYW55OiBbXSwNCiAgICAgICAgdm9sdGFnZUNsYXNzOiBbXSwNCiAgICAgICAgY3VzdG9tZXJOYW1lOiBbXSwNCiAgICAgICAgdXNlcnVuaXQ6IFtdLA0KICAgICAgICBhbW1ldGVybmFtZTogW10sDQogICAgICB9LA0KICAgICAgZWxlY3Rybzogew0KICAgICAgICBjb2x1bW5zOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLluo/lj7ciLA0KICAgICAgICAgICAgdHlwZTogImluZGV4IiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi55So55S157G75Z6LIiwNCiAgICAgICAgICAgIGtleTogInR5cGVOYW1lIiwNCiAgICAgICAgICB9LA0KDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmiYDljaDmr5TkvosoJSkiLA0KICAgICAgICAgICAga2V5OiAicmF0aW8iLA0KICAgICAgICAgICAgcmVuZGVySGVhZGVyOiByZW5kZXJIZWFkZXIsDQogICAgICAgICAgICByZW5kZXI6IChoLCBwYXJhbXMpID0+IHsNCiAgICAgICAgICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgICAgICAgICBsZXQgcmF0aW8gPSBwYXJhbXMucm93LnJhdGlvOw0KICAgICAgICAgICAgICBsZXQgaXNFcnJvcjEgPSBwYXJhbXMucm93LmlkRXJyb3IxOw0KICAgICAgICAgICAgICBsZXQgZXJyb3IgPSBoKA0KICAgICAgICAgICAgICAgICJsYWJlbCIsDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgc3R5bGU6IHsNCiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICIjZWQ0MDE0IiwNCiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICIxMnB4IiwNCiAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogIlNpbVN1biIsDQogICAgICAgICAgICAgICAgICAgIHBhZGRpbmdUb3A6ICI2cHgiLA0KICAgICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAxLA0KICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAiYm9sZCIsDQogICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IG51bGwgIT0gcmF0aW8gPyAibm9uZSIgOiAiaW5saW5lLWJsb2NrIiwNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAi5LiN6IO95Li656m6Ig0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICBsZXQgZXJyb3IxID0gaCgNCiAgICAgICAgICAgICAgICAibGFiZWwiLA0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgIHN0eWxlOiB7DQogICAgICAgICAgICAgICAgICAgIGNvbG9yOiAiI2VkNDAxNCIsDQogICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAiMTJweCIsDQogICAgICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6ICJTaW1TdW4iLA0KICAgICAgICAgICAgICAgICAgICBwYWRkaW5nVG9wOiAiNnB4IiwNCiAgICAgICAgICAgICAgICAgICAgbGluZUhlaWdodDogMSwNCiAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogImJvbGQiLA0KICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBpc0Vycm9yMSA9PSB0cnVlID8gImlubGluZS1ibG9jayIgOiAibm9uZSIsDQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgIui+k+WFpeavlOS+i+S4jeWQiOagvOimgeaxgiINCiAgICAgICAgICAgICAgKTsNCiAgICAgICAgICAgICAgbGV0IHJlc3VsdCA9IGgoIklucHV0TnVtYmVyIiwgew0KICAgICAgICAgICAgICAgIHN0eWxlOiB7DQogICAgICAgICAgICAgICAgICBib3JkZXI6IG51bGwgPT0gcmF0aW8gfHwgaXNFcnJvcjEgPT0gdHJ1ZSA/ICIxcHggc29saWQgI2VkNDAxNCIgOiAiIiwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIHByb3BzOiB7DQogICAgICAgICAgICAgICAgICB2YWx1ZTogcmF0aW8sDQogICAgICAgICAgICAgICAgICBtYXg6IDEwMCwNCiAgICAgICAgICAgICAgICAgIG1pbjogMC4xLA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgb246IHsNCiAgICAgICAgICAgICAgICAgICJvbi1jaGFuZ2UiOiAodikgPT4gew0KICAgICAgICAgICAgICAgICAgICBpZiAodiA9PSB1bmRlZmluZWQgfHwgdiA9PSBudWxsKSB7DQogICAgICAgICAgICAgICAgICAgICAgdGhhdC5pc0Vycm9yID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICB0aGF0LmlzRXJyb3IgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAvL+e7mWRhdGHph43mlrDotYvlgLwNCiAgICAgICAgICAgICAgICAgICAgLy8gbGV0IHJlZyA9IC9eKD86WzEtOV0/XGR8MTAwKSQvOw0KICAgICAgICAgICAgICAgICAgICBsZXQgcmVnID0gL14tPygoWzEtOV1bMC05XSopfCgoWzBdXC5cZHsxLDJ9fFsxLTldWzAtOV0qXC5cZHsxLDJ9KSkpJC87DQogICAgICAgICAgICAgICAgICAgIGlmICh2ICE9IHVuZGVmaW5lZCAmJiB2ICE9IG51bGwgJiYgIXJlZy50ZXN0KHYpKSB7DQogICAgICAgICAgICAgICAgICAgICAgcGFyYW1zLnJvdy5pZEVycm9yMSA9IHRydWU7DQogICAgICAgICAgICAgICAgICAgICAgdGhhdC5pc0Vycm9yMSA9IHRydWU7DQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgcGFyYW1zLnJvdy5pZEVycm9yMSA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgIHRoYXQuaXNFcnJvcjEgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICBwYXJhbXMucm93LnJhdGlvID0gdjsNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5lbGVjdHJvLmRhdGFbcGFyYW1zLnJvdy5faW5kZXhdID0gcGFyYW1zLnJvdzsNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIHJldHVybiBoKCJkaXYiLCBbcmVzdWx0LCBlcnJvciwgZXJyb3IxXSk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLlhbPogZTlsYDnq5kiLA0KICAgICAgICAgICAga2V5OiAic3RhdGlvbk5hbWUiLA0KICAgICAgICAgICAgcmVuZGVyOiAoaCwgcGFyYW1zKSA9PiB7DQogICAgICAgICAgICAgIGxldCBzdGF0aW9uTmFtZSA9IHBhcmFtcy5yb3cuc3RhdGlvbk5hbWU7DQogICAgICAgICAgICAgIGxldCBkaXNhYmxlZCA9IHBhcmFtcy5yb3cuX2Rpc2FibGVkOw0KICAgICAgICAgICAgICBpZiAoZGlzYWJsZWQgIT0gdW5kZWZpbmVkICYmIGRpc2FibGVkID09IHRydWUpIHsNCiAgICAgICAgICAgICAgICByZXR1cm4gaCgiSW5wdXQiLCB7DQogICAgICAgICAgICAgICAgICBwcm9wczogew0KICAgICAgICAgICAgICAgICAgICB2YWx1ZTogc3RhdGlvbk5hbWUsDQogICAgICAgICAgICAgICAgICAgIHJlYWRvbmx5OiB0cnVlLA0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICByZXR1cm4gaCgiSW5wdXQiLCB7DQogICAgICAgICAgICAgICAgICBwcm9wczogew0KICAgICAgICAgICAgICAgICAgICB2YWx1ZTogc3RhdGlvbk5hbWUsDQogICAgICAgICAgICAgICAgICAgIGljb246ICJpb3MtYXJjaGl2ZSIsDQogICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAi54K55Ye75Zu+5qCH6YCJ5oupIiwNCiAgICAgICAgICAgICAgICAgICAgcmVhZG9ubHk6IHRydWUsDQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgb246IHsNCiAgICAgICAgICAgICAgICAgICAgIm9uLWNsaWNrIjogKHYpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICB0aGlzLmNob29zZVJlc3BvbnNlQ2VudGVyKDIsIHBhcmFtcywgcGFyYW1zLnJvdy5faW5kZXgpOw0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBkYXRhOiBbXSwNCiAgICAgIH0sDQogICAgICByZW1vdmVJZHM6IFtdLA0KICAgICAgZmlsZXM6IFtdLA0KICAgICAgbXVsdGlGaWxlczogbnVsbCwNCiAgICAgIGZpbGVQYXJhbTogew0KICAgICAgICBidXNpSWQ6ICIiLA0KICAgICAgICBidXNpQWxpYXM6ICLpmYTku7Yo5Y2P6K6u566h55CGKSIsDQogICAgICAgIGNhdGVnb3J5Q29kZTogImZpbGUiLA0KICAgICAgICBhcmVhQ29kZTogImxuIiwNCiAgICAgIH0sDQogICAgICByZWNlaXB0YWNjb3VudG5hbWVMaXN0OiBbXSwgLy/pk7booYzljaHliJfooagNCiAgICAgIGF0dGFjaERhdGE6IFtdLA0KICAgICAgYW1tZXRlcjogew0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgY291bnRyeTogbnVsbCwNCiAgICAgICAgY29tcGFueTogbnVsbCwNCiAgICAgICAgY291bnRyeU5hbWU6ICIiLA0KICAgICAgICBlbGVjdHJpY1R5cGVzOiBbXSwNCiAgICAgICAgZWxlY3RybzogW10sDQogICAgICAgIGNsYXNzaWZpY2F0aW9uczogW10sIC8v55So55S157G75Z6LDQogICAgICAgIGlzemd6OiAwLA0KICAgICAgICBkaXJlY3RGbGFnOiAwLA0KICAgICAgICBvZmZpY2VGbGFnOiAwLA0KICAgICAgICB0cmFuc2Rpc3RyaWNvbXBhbnk6IDEsDQogICAgICAgIHZvbHRhZ2VDbGFzczogIiIsDQogICAgICAgIHN0YXRpb25jb2RlNWdyOiBudWxsLA0KICAgICAgICBzdGF0aW9ubmFtZTVncjogbnVsbCwNCiAgICAgIH0sDQogICAgICBpc3pnek9ubHk6IGZhbHNlLA0KICAgICAgaXN6Z3ptZTogZmFsc2UsDQogICAgICBpc3pnem1lbmFtZTogbnVsbCwNCiAgICAgIGRpc2FibGVkaXN6Z3o6IGZhbHNlLA0KICAgICAgZWxlY3RyaWNUeXBlOiB7DQogICAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICBmaWx0ZXI6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBmb3JtSXRlbVR5cGU6ICJpbnB1dCIsDQogICAgICAgICAgICBwcm9wOiAibmFtZSIsDQogICAgICAgICAgICBsYWJlbDogIueUqOeUteexu+WeiyIsDQogICAgICAgICAgICB3aWR0aDogMTAwLA0KICAgICAgICAgICAgc2l6ZTogInNtYWxsIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBjb2x1bW5zOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLluo/lj7ciLA0KICAgICAgICAgICAgdHlwZTogImluZGV4IiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA3MCwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAiaWQiLA0KICAgICAgICAgICAga2V5OiAiaWQiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDgwLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLnlKjnlLXnsbvlnosiLA0KICAgICAgICAgICAga2V5OiAidHlwZU5hbWUiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDgwLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGRhdGE6IFtdLA0KICAgICAgICB0b3RhbDogMCwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgfSwNCiAgICB9Ow0KICB9LA0KDQogIG1ldGhvZHM6IHsNCiAgICAuLi5tYXBNdXRhdGlvbnMoWyJjbG9zZVRhZyIsICJjbG9zZVRhZ0J5TmFtZSJdKSwNCg0KICAgIGdldE5vd1RpbWUoKSB7DQogICAgICB2YXIgZGF0ZSA9IG5ldyBEYXRlKCk7DQogICAgICAvL+W5tCBnZXRGdWxsWWVhcigp77ya5Zub5L2N5pWw5a2X6L+U5Zue5bm05Lu9DQogICAgICB2YXIgeWVhciA9IGRhdGUuZ2V0RnVsbFllYXIoKTsgLy9nZXRGdWxsWWVhcigp5Luj5pu/Z2V0WWVhcigpDQogICAgICAvL+aciCBnZXRNb250aCgp77yaMCB+IDExDQogICAgICB2YXIgbW9udGggPSBkYXRlLmdldE1vbnRoKCkgKyAxOw0KICAgICAgLy/ml6UgZ2V0RGF0ZSgp77yaKDEgfiAzMSkNCiAgICAgIHZhciBkYXkgPSBkYXRlLmdldERhdGUoKTsNCiAgICAgIC8v5pe2IGdldEhvdXJzKCnvvJooMCB+IDIzKQ0KICAgICAgdmFyIGhvdXIgPSBkYXRlLmdldEhvdXJzKCk7DQogICAgICAvL+WIhiBnZXRNaW51dGVzKCnvvJogKDAgfiA1OSkNCiAgICAgIHZhciBtaW51dGUgPSBkYXRlLmdldE1pbnV0ZXMoKTsNCiAgICAgIC8v56eSIGdldFNlY29uZHMoKe+8migwIH4gNTkpDQogICAgICB2YXIgc2Vjb25kID0gZGF0ZS5nZXRTZWNvbmRzKCk7DQoNCiAgICAgIHZhciB0aW1lID0NCiAgICAgICAgeWVhciArDQogICAgICAgICItIiArDQogICAgICAgIHRoaXMuYWRkWmVybyhtb250aCkgKw0KICAgICAgICAiLSIgKw0KICAgICAgICB0aGlzLmFkZFplcm8oZGF5KSArDQogICAgICAgICIgIiArDQogICAgICAgIHRoaXMuYWRkWmVybyhob3VyKSArDQogICAgICAgICI6IiArDQogICAgICAgIHRoaXMuYWRkWmVybyhtaW51dGUpICsNCiAgICAgICAgIjoiICsNCiAgICAgICAgdGhpcy5hZGRaZXJvKHNlY29uZCk7DQogICAgICByZXR1cm4gdGltZTsNCiAgICB9LA0KDQogICAgLy/lsI/kuo4xMOeahOaLvOaOpeS4ijDlrZfnrKbkuLINCiAgICBhZGRaZXJvKHMpIHsNCiAgICAgIHJldHVybiBzIDwgMTAgPyAiMCIgKyBzIDogczsNCiAgICB9LA0KICAgIGdldERhdGFGcm9tTW9kYWxPYmplY3QoZGF0YSwgZmxhZykgew0KICAgICAgdGhpcy5oYW5kbGVDaG9vc2VTdXAoZGF0YSk7IC8vIOS8oCB0cnVlIOiuvue9riDlm57osIPlgLwNCiAgICB9LA0KICAgIG9uQ2hhbmdlKHYpIHsNCiAgICAgIHRoaXMucmVjZWlwdGFjY291bnRuYW1lTGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGlmIChpdGVtLmtvaW5oID09PSB2KSB7DQogICAgICAgICAgY29uc29sZS5sb2codik7DQogICAgICAgICAgdGhpcy5hbW1ldGVyLnJlY2VpcHRhY2NvdW50YmFuayA9IGl0ZW0uYmFua2E7DQogICAgICAgICAgdGhpcy5hbW1ldGVyLnJlY2VpcHRhY2NvdW50cyA9IGl0ZW0uYmFua247DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy/pgInmi6nlr7nmlrnljZXkvY0NCiAgICBoYW5kbGVDaG9vc2VTdXAoZGF0YSkgew0KICAgICAgaWYgKCFkYXRhKSB7DQogICAgICAgIHRoaXMuJHJlZnMuY2hvb3NlTW9kYWxTdXAyLmNob29zZSgxKTsgLy/miZPlvIDmqKHmgIHmoYYNCiAgICAgIH0gZWxzZSB7DQogICAgICAgICh0aGlzLmFtbWV0ZXIudXNlcnVuaXQgPSBkYXRhLm5hbWUpLA0KICAgICAgICAgIGdldEJhbmtDYXJkKHsgbGlmbnI6IGRhdGEuaWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMuZGF0YSk7DQogICAgICAgICAgICB0aGlzLnJlY2VpcHRhY2NvdW50bmFtZUxpc3QgPSByZXMuZGF0YS5yb3dzOw0KICAgICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8qKioqKuagoemqjOW9k+WJjeWxgOermeaYr+aYr+WQpui/h+acnw0KICAgIG9uTW9kYWxPSzEodHlwZSkgew0KICAgICAgbGV0IG5vd1RpbWUgPSB0aGlzLmdldE5vd1RpbWUoKTsNCiAgICAgIGxldCBwYXJhbXMgPSB7DQogICAgICAgIHN0YXRpb25hZGRyX2NvZGU6IHRoaXMuYW1tZXRlci5zdGF0aW9uYWRkcmVzc2NvZGUsDQogICAgICAgIHNlcnZlZW5kZGF0ZTogbm93VGltZSwNCiAgICAgIH07DQogICAgICAvL+agoemqjOWxgOermei/h+acn+aXtumXtA0KICAgICAgY2hlY2tTdGF0aW9uKHBhcmFtcykudGhlbigocmVzKSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKHJlcy5kYXRhKTsNCiAgICAgICAgdGhpcy5hbW1ldGVyLm1hcCA9IHJlcy5kYXRhOw0KICAgICAgICB0aGlzLm9uTW9kYWxPSyh0eXBlKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBPSyh0eXBlKSB7DQogICAgICB0aGlzLmNoZWNrU3RhdGlvblR5cGUgPSB0eXBlOw0KICAgICAgaWYgKHR5cGUgPT0gMSkgew0KICAgICAgICB0aGlzLmlzTG9hZGluZyA9IDE7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmlzTG9hZGluZyA9IDA7DQogICAgICB9DQogICAgICBpZiAodGhpcy5sb2FkaW5nID09IHRydWUpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIHRoaXMuYW1tZXRlci5lbGVjdHJpY1R5cGVzID0gdGhpcy5lbGVjdHJvLmRhdGE7DQogICAgICB0aGlzLiRyZWZzLmFtbWV0ZXIudmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMuJHJlZnMuYW1tZXRlcjEudmFsaWRhdGUoKHZhbGlkMSkgPT4gew0KICAgICAgICAgICAgaWYgKHZhbGlkMSkgew0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLmFtbWV0ZXIyLnZhbGlkYXRlKCh2YWxpZDIpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAodmFsaWQyKSB7DQogICAgICAgICAgICAgICAgICBpZiAodGhpcy5hbW1ldGVyLnN0YXR1cyA9PSAwKSB7DQogICAgICAgICAgICAgICAgICAgIC8v5YGc55So55S16KGo5Y2P6K6u5LiN6aqM6K+BIOWxgOermeOAgeeUqOeUteexu+Weiw0KICAgICAgICAgICAgICAgICAgICB0aGlzLnNhdmVEYXRhKHR5cGUpOw0KICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5jaGVja0RhdGEodHlwZSk7DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuZXJyb3IoIuS4muS4u+S/oeaBr+mqjOivgeayoemAmui/hyIpOw0KICAgICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuZXJyb3IoIuWFs+iBlOWxgOermeS/oeaBr+mqjOivgeayoemAmui/hyIpOw0KICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRNZXNzYWdlLmVycm9yKCLln7rmnKzkv6Hmga/pqozor4HmsqHpgJrov4ciKTsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIG9uTW9kYWxPSyh0eXBlKSB7DQogICAgICBsZXQgYXR0YWNoRGF0YSA9IFtdOw0KICAgICAgY29uc29sZS5sb2codGhpcy5hbW1ldGVyKTsNCiAgICAgIGlmICh0aGlzLmFtbWV0ZXIubWFwLmlmdGltZW91dCA9PSAiMyIpIHsNCiAgICAgICAgYXR0Y2hMaXN0KHsgYnVzaUlkOiB0aGlzLmZpbGVQYXJhbS5idXNpSWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgY29uc29sZS5sb2cocmVzKTsNCiAgICAgICAgICBhdHRhY2hEYXRhID0gT2JqZWN0LmFzc2lnbihbXSwgcmVzLmRhdGEucm93cyk7DQogICAgICAgICAgY29uc29sZS5sb2coYXR0YWNoRGF0YSwgImF0dGFjaERhdGEiKTsNCiAgICAgICAgICBpZiAoYXR0YWNoRGF0YS5sZW5ndGggPCAxKSB7DQogICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmVycm9yKCLlvZPliY3pgInmi6nlsYDnq5nlt7Lov4fmnJ8s5b+F6aG75LiK5Lyg6ZmE5Lu2Iik7DQogICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5PSyh0eXBlKTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLk9LKHR5cGUpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy/pqozor4HmlbDmja4NCiAgICBjaGVja0RhdGEodHlwZSkgew0KICAgICAgbGV0IHR5cGVzID0gdGhpcy5hbW1ldGVyLmNsYXNzaWZpY2F0aW9uczsNCiAgICAgIHRoaXMuYW1tZXRlci5lbGVjdHJvdHlwZSA9IHR5cGVzW3R5cGVzLmxlbmd0aCAtIDFdOw0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgaWYgKA0KICAgICAgICB0aGlzLmFtbWV0ZXIuc3RhdHVzID09PSAxICYmDQogICAgICAgICh0aGlzLmNvbmZpZ1ZlcnNpb24gPT0gInNjIiB8fCB0aGlzLmNvbmZpZ1ZlcnNpb24gPT0gIlNDIikNCiAgICAgICkgew0KICAgICAgICAvL+WcqOeUqOeKtuaAgeS4i+mqjOivgeWxgOermeWcsOWdgOS4jeiDveS4uuepug0KICAgICAgICBpZiAoDQogICAgICAgICAgdGhpcy5hbW1ldGVyLnN0YXRpb25hZGRyZXNzID09IG51bGwgfHwNCiAgICAgICAgICB0aGlzLmFtbWV0ZXIuc3RhdGlvbmFkZHJlc3MgPT0gdW5kZWZpbmVkDQogICAgICAgICkgew0KICAgICAgICAgIHRoaXMuJE1vZGFsLndhcm5pbmcoew0KICAgICAgICAgICAgdGl0bGU6ICLmuKnppqjmj5DnpLoiLA0KICAgICAgICAgICAgY29udGVudDogIuWxgOermeWcsOWdgOS4jeiDveS4uuepuu+8jOivt+WcqOWxgOermeeuoeeQhue7tOaKpOivpeWxgOermeS/oeaBr++8gSIsDQogICAgICAgICAgfSk7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAodGhpcy5jaGVja1N0YXRpb25FbGVjKCkpIHsNCiAgICAgICAgLy/pqozor4HnlKjnlLXnsbvlnovlkozlsYDnq5nnsbvlnovmmK/lkKbljLnphY0NCiAgICAgICAgaWYgKHRoaXMuY2hlY2tFbGVjdHJpY1R5cGVJdGVtKCkpIHsNCiAgICAgICAgICBpZiAodGhpcy5jb25maWdWZXJzaW9uICE9ICJsbiIgJiYgdGhpcy5jb25maWdWZXJzaW9uICE9ICJMTiIpIHsNCiAgICAgICAgICAgIGlmICh0aGlzLmFtbWV0ZXIuaXNjaGFuZ2VhbW1ldGVyID09IDEgJiYgdGhpcy5hbW1ldGVyLmJpbGxTdGF0dXMgPCAyKSB7DQogICAgICAgICAgICAgIHRoYXQuY2hlY2tlZERhdGUodHlwZSk7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBjaGVja0FtbWV0ZXJFeGlzdCh0aGlzLmFtbWV0ZXIuaWQsIHRoaXMuYW1tZXRlci5hbW1ldGVybmFtZSwgMCkudGhlbigNCiAgICAgICAgICAgICAgICAocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgICAvL+mqjOivgeeUteihqOaYr+WQpuWtmOWcqA0KICAgICAgICAgICAgICAgICAgbGV0IGNvZGUgPSByZXMuZGF0YS5jb2RlOw0KICAgICAgICAgICAgICAgICAgaWYgKGNvZGUgPT0gMCkgew0KICAgICAgICAgICAgICAgICAgICB0aGF0LmNoZWNrZWREYXRlKHR5cGUpOw0KICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGF0LmNoZWNrZWREYXRlKHR5cGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgY2hlY2tTdGF0aW9uRWxlYygpIHsNCiAgICAgIGxldCBlbGVjdHJvdHlwZSA9IHRoaXMuYW1tZXRlci5lbGVjdHJvdHlwZTsNCiAgICAgIGxldCBzdGF0aW9udHlwZSA9IHRoaXMuYW1tZXRlci5zdGF0aW9udHlwZTsNCiAgICAgIGlmIChlbGVjdHJvdHlwZSA9PT0gMTExIHx8IGVsZWN0cm90eXBlID09PSAxMTIgfHwgZWxlY3Ryb3R5cGUgPT09IDExMykgew0KICAgICAgICBpZiAoc3RhdGlvbnR5cGUgIT09IDEwMDAxKSB7DQogICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7DQogICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsDQogICAgICAgICAgICBjb250ZW50OiAi55So55S157G75Z6L5ZKM5bGA56uZ57G75Z6L5LiN5Yy56YWN77yM6K+356Gu6K6kIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSBpZiAoZWxlY3Ryb3R5cGUgPT09IDEyMSB8fCBlbGVjdHJvdHlwZSA9PT0gMTEyKSB7DQogICAgICAgIGlmIChzdGF0aW9udHlwZSAhPT0gMTAwMDMgJiYgc3RhdGlvbnR5cGUgIT09IDEwMDA0KSB7DQogICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7DQogICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsDQogICAgICAgICAgICBjb250ZW50OiAi55So55S157G75Z6L5ZKM5bGA56uZ57G75Z6L5LiN5Yy56YWN77yM6K+356Gu6K6kIiArIHN0YXRpb250eXBlLA0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIGlmIChlbGVjdHJvdHlwZSA9PT0gMTMxIHx8IGVsZWN0cm90eXBlID09PSAxMzIgfHwgZWxlY3Ryb3R5cGUgPT09IDEzMykgew0KICAgICAgICBpZiAoc3RhdGlvbnR5cGUgIT09IDEwMDA1KSB7DQogICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7DQogICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsDQogICAgICAgICAgICBjb250ZW50OiAi55So55S157G75Z6L5ZKM5bGA56uZ57G75Z6L5LiN5Yy56YWN77yM6K+356Gu6K6kIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSBpZiAoDQogICAgICAgIGVsZWN0cm90eXBlID09PSAxNDExIHx8DQogICAgICAgIGVsZWN0cm90eXBlID09PSAxNDEyIHx8DQogICAgICAgIGVsZWN0cm90eXBlID09PSAxNDIxIHx8DQogICAgICAgIGVsZWN0cm90eXBlID09PSAxNDIyIHx8DQogICAgICAgIGVsZWN0cm90eXBlID09PSAxNDMxIHx8DQogICAgICAgIGVsZWN0cm90eXBlID09PSAxNDMyDQogICAgICApIHsNCiAgICAgICAgaWYgKHN0YXRpb250eXBlICE9PSAxMDAwMikgew0KICAgICAgICAgIHRoaXMuJE1vZGFsLndhcm5pbmcoew0KICAgICAgICAgICAgdGl0bGU6ICLmuKnppqjmj5DnpLoiLA0KICAgICAgICAgICAgY29udGVudDogIueUqOeUteexu+Wei+WSjOWxgOermeexu+Wei+S4jeWMuemFje+8jOivt+ehruiupCIsDQogICAgICAgICAgfSk7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAodGhpcy5jb25maWdWZXJzaW9uID09ICJzYyIgJiYgdGhpcy5hbW1ldGVyLnN0YXRpb25hZGRyZXNzY29kZSkgew0KICAgICAgICAvL+KAnDUx4oCd5byA5aS06ZOB5aGU56uZ5Z2A57yW56CB5o6n5Yi2DQogICAgICAgIGlmICgNCiAgICAgICAgICBbMTQxMSwgMTQxMl0uaW5jbHVkZXMoZWxlY3Ryb3R5cGUpICYmDQogICAgICAgICAgIXRoaXMuYW1tZXRlci5zdGF0aW9uYWRkcmVzc2NvZGUuc3RhcnRzV2l0aCgiNTEiKQ0KICAgICAgICApIHsNCiAgICAgICAgICB0aGlzLiRNb2RhbC53YXJuaW5nKHsNCiAgICAgICAgICAgIHRpdGxlOiAi5rip6aao5o+Q56S6IiwNCiAgICAgICAgICAgIGNvbnRlbnQ6ICLnlKjnlLXnsbvlnovlkozlsYDnq5nnq5nlnYDnvJbnoIHkuI3ljLnphY0oNTHlvIDlpLTkuLrpk4HloZTnq5nlnYDnvJbnoIEp77yM6K+356Gu6K6kIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiB0cnVlOw0KICAgIH0sDQogICAgb2tNb2RlbCgpIHsNCiAgICAgIC8v5LiN6aqM6K+B5Liq5pWwDQogICAgICB0aGlzLmlzb2xkY2hlY2tTdGF0aW9uID0gbnVsbDsNCiAgICAgIHRoaXMuc2F2ZURhdGEodGhpcy5jaGVja1N0YXRpb25UeXBlKTsgLy/kv53lrZjmlbDmja4NCiAgICB9LA0KICAgIGNhbmNlbE1vZGVsKCkgew0KICAgICAgdGhpcy5pc29sZGNoZWNrU3RhdGlvbiA9IG51bGw7DQogICAgICB0aGlzLiRNb2RhbC53YXJuaW5nKHsgdGl0bGU6ICLmuKnppqjmj5DnpLoiLCBjb250ZW50OiB0aGlzLmVycm9yTWVzc2FnZSB9KTsNCiAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgIH0sDQogICAgY2hlY2tlZERhdGUodHlwZSkgew0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgY2hlY2tQcm9qZWN0TmFtZUV4aXN0KHRoYXQuYW1tZXRlci5pZCwgdGhhdC5hbW1ldGVyLnByb2plY3RuYW1lLCAwKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgLy/pqozor4Hpobnnm67lkI3np7DmmK/lkKblrZjlnKgNCiAgICAgICAgbGV0IGNvZGUgPSByZXMuZGF0YS5jb2RlOw0KICAgICAgICBpZiAoY29kZSA9PSAwKSB7DQogICAgICAgICAgaWYgKA0KICAgICAgICAgICAgdGhhdC5hbW1ldGVyLnN0YXRpb25jb2RlICE9IHVuZGVmaW5lZCAmJg0KICAgICAgICAgICAgdGhhdC5hbW1ldGVyLnN0YXRpb25jb2RlICE9IG51bGwgJiYNCiAgICAgICAgICAgICh0aGF0LmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPT0gMTQxMSB8fCB0aGF0LmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPT0gMTQxMikNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIC8v5Yik5pat5piv5ZCm6ZOB5aGUDQogICAgICAgICAgICBpZiAodGhhdC5wcm9wZXJ0eXJpZ2h0ID09IG51bGwpIHsNCiAgICAgICAgICAgICAgLy/liKTmlq3mmK/lkKbpk4HloZQNCiAgICAgICAgICAgICAgZ2V0c3RhdGlvbm9sZCh0aGF0LmFtbWV0ZXIuc3RhdGlvbmNvZGUpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICAgIC8v6aqM6K+B6aG555uu5ZCN56ew5piv5ZCm5a2Y5ZyoDQogICAgICAgICAgICAgICAgdGhhdC5wcm9wZXJ0eXJpZ2h0ID0gcmVzLmRhdGEucHJvcGVydHlyaWdodDsNCiAgICAgICAgICAgICAgICBpZiAodGhhdC5wcm9wZXJ0eXJpZ2h0ICE9IDMpIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMuJE1vZGFsLndhcm5pbmcoew0KICAgICAgICAgICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsDQogICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICLnlKjnlLXnsbvlnovlkozlsYDnq5nnsbvlnovmiJbkuqfmnYPkuI3ljLnphY3vvIzor7fnoa7orqQiLA0KICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgdGhhdC5pc0NoZWNrU3RhdGlvbih0eXBlKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgaWYgKHRoYXQucHJvcGVydHlyaWdodCAhPSAzKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7DQogICAgICAgICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsDQogICAgICAgICAgICAgICAgICBjb250ZW50OiAi55So55S157G75Z6L5ZKM5bGA56uZ57G75Z6L5oiW5Lqn5p2D5LiN5Yy56YWN77yM6K+356Gu6K6kIiwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICB0aGF0LmlzQ2hlY2tTdGF0aW9uKHR5cGUpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoYXQuaXNDaGVja1N0YXRpb24odHlwZSk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoYXQubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGlzQ2hlY2tTdGF0aW9uKHR5cGUpIHsNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIGNoZWNrQW1tZXRlckJ5U3RhdGlvbih7DQogICAgICAgIGlkOiB0aGF0LmFtbWV0ZXIuaWQsDQogICAgICAgIHR5cGU6IDAsDQogICAgICAgIGVsZWN0cm90eXBlOiB0aGF0LmFtbWV0ZXIuZWxlY3Ryb3R5cGUsDQogICAgICAgIHN0YXRpb25jb2RlOiB0aGF0LmFtbWV0ZXIuc3RhdGlvbmNvZGUsDQogICAgICAgIGFtbWV0ZXJ1c2U6IHRoYXQuYW1tZXRlci5hbW1ldGVydXNlLA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGxldCBjb2RlID0gcmVzLmRhdGEuY29kZTsNCiAgICAgICAgaWYgKGNvZGUgPT0gImVycm9yIikgew0KICAgICAgICAgIHRoaXMuZXJyb3JNZXNzYWdlID0gcmVzLmRhdGEubXNnOw0KICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICh0aGF0Lmlzb2xkY2hlY2tTdGF0aW9uID09IG51bGwgfHwgdGhhdC5pc29sZGNoZWNrU3RhdGlvbiA9PSBmYWxzZSkgJiYNCiAgICAgICAgICAgIHRoYXQuYW1tZXRlci5zdGF0aW9udHlwZSA9PSAxMDAwMiAmJg0KICAgICAgICAgICAgcmVzLmRhdGEuZmxhZzUNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIC8v57yW6L6R5pWw5o2u5pe25Yik5pat5piv5ZCm6YCJ5oup5YWz6IGU5bGA56uZ77yM5rKh5pyJ5YWz6IGU5by55Ye65piv5ZCm5a6k5YiGDQogICAgICAgICAgICBpZiAodGhhdC5pc21vZGFsMSA9PSBudWxsKSB7DQogICAgICAgICAgICAgIHRoYXQubW9kYWwxID0gdHJ1ZTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoYXQub2tNb2RlbCgpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGF0LiRNb2RhbC53YXJuaW5nKHsgdGl0bGU6ICLmuKnppqjmj5DnpLoiLCBjb250ZW50OiByZXMuZGF0YS5tc2cgfSk7DQogICAgICAgICAgICB0aGF0LmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhhdC5zYXZlRGF0YSh0eXBlKTsgLy/kv53lrZjmlbDmja4NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBzYXZlRGF0YSh0eXBlKSB7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICBpc0luVG9kb0xpc3QodGhpcy5hbW1ldGVyLmlkLCAxKQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgLy/lrZjlnKjkuo7ku6Plip7kuK3ml7bvvIzmiqXlh7rmj5DnpLoNCiAgICAgICAgICBsZXQgb3duZXJuYW1lID0gIiI7DQogICAgICAgICAgaWYgKHJlcy5kYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcmVzLmRhdGEubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICAgICAgb3duZXJuYW1lICs9IHJlcy5kYXRhW2ldLm93bmVybmFtZSArICIgIjsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHRoaXMuJE1vZGFsLndhcm5pbmcoew0KICAgICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsDQogICAgICAgICAgICAgIGNvbnRlbnQ6ICLor6XmlbDmja7lrZjlnKjkuo4iICsgb3duZXJuYW1lICsgIueahOa1geeoi+S7o+WKnuS4re+8jOWkhOeQhuWQjuaJjeWPr+S/ruaUueaVsOaNriIsDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBjaGVja0Fjb3VudEJ5VXBkYXRlKHsgaWQ6IHRoYXQuYW1tZXRlci5pZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgLy/kv67mlLnmlbDmja7liY3pqozor4Hlj7DotKYNCiAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhID09IC0xKSB7DQogICAgICAgICAgICAgICAgdGhhdC4kTW9kYWwud2FybmluZyh7DQogICAgICAgICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsDQogICAgICAgICAgICAgICAgICBjb250ZW50OiAi6K+l5pWw5o2u5bey5aGr5YaZ5Y+w6LSm5oiW5q2j5Zyo5oql6LSm5Lit77yM5aSE55CG5ZCO5omN5Y+v5L+u5pS55pWw5o2uIiwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB0aGF0LmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICB0aGF0LmNsZWFyRGF0YUJ5Q29uZGl0aW9uKCk7DQogICAgICAgICAgICAgICAgdXBkYXRlQW1tZXRlcih0aGF0LmFtbWV0ZXIpDQogICAgICAgICAgICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5jb2RlICE9IDAgJiYgcmVzLmRhdGEubXNnKSB7DQogICAgICAgICAgICAgICAgICAgICAgdGhhdC4kTm90aWNlLmVycm9yKHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5o+Q56S6IiwNCiAgICAgICAgICAgICAgICAgICAgICAgIGRlc2M6IHJlcy5kYXRhLm1zZywNCiAgICAgICAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxMCwNCiAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgICB0aGF0LmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGUgPT0gMSkgew0KICAgICAgICAgICAgICAgICAgICAgIHRoYXQuc3RhcnRGbG93KHRoYXQuYW1tZXRlcik7DQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgLy8gdGhpcy5jbG9zZVRhZ0J5TmFtZSh7Ly8g5YWz6Zet5bey57uP5omT5byA55qEIO+8jOmBv+WFjeWGsueqgQ0KICAgICAgICAgICAgICAgICAgICAgIC8vICAgICByb3V0ZTogZ2V0SG9tZVJvdXRlKHJvdXRlcnMsICJhbW1ldGVyIiksDQogICAgICAgICAgICAgICAgICAgICAgLy8gfSk7DQogICAgICAgICAgICAgICAgICAgICAgLy8gLy/ot7Povazoh7Pkv67mlLnpobXpnaIg5bm25YWz6Zet5b2T5YmN6aG1DQogICAgICAgICAgICAgICAgICAgICAgLy8gdGhpcy5jbG9zZVRhZyh7DQogICAgICAgICAgICAgICAgICAgICAgLy8gICAgIHJvdXRlOiB0aGlzLiRyb3V0ZSwgbmV4dDogew0KICAgICAgICAgICAgICAgICAgICAgIC8vICAgICAgICAgbmFtZTogImFtbWV0ZXIiLCBxdWVyeToge30NCiAgICAgICAgICAgICAgICAgICAgICAvLyAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIC8vIH0pOw0KICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLiRyb3V0ZSIsIHRoaXMuJHJvdXRlKTsNCiAgICAgICAgICAgICAgICAgICAgICB0aGlzLmNsb3NlVGFnKHsgcm91dGU6IHRoaXMuJHJvdXRlIH0pOw0KICAgICAgICAgICAgICAgICAgICAgIHRoYXQud2FybigpOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgLmNhdGNoKChlcnIpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGVycik7DQogICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKGVycikgPT4gew0KICAgICAgICAgIGNvbnNvbGUubG9nKGVycik7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgY2hhbmdlZGlyZWN0c3VwcGx5KHZhbHVlKSB7DQogICAgICBjb25zb2xlLmxvZyh2YWx1ZSk7DQogICAgICBpZiAodGhpcy5hbW1ldGVyLmRpcmVjdHN1cHBseWZsYWcgPT0gMSkgew0KICAgICAgICAvL3RoaXMuYW1tZXRlci5wcm9wZXJ0eSA9IG51bGw7DQogICAgICAgIHRoaXMucnVsZVZhbGlkYXRlLnN1cHBseWJ1cmVhdWFtbWV0ZXJjb2RlID0gWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXTsNCiAgICAgICAgdGhpcy5ydWxlVmFsaWRhdGUucHJpY2UgPSBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHlwZTogIm51bWJlciIsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vdGhpcy5hbW1ldGVyLnByb3BlcnR5ID0gbnVsbDsNCiAgICAgICAgdGhpcy5ydWxlVmFsaWRhdGUuc3VwcGx5YnVyZWF1YW1tZXRlcmNvZGUgPSBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdOw0KICAgICAgICB0aGlzLnJ1bGVWYWxpZGF0ZS5wcmljZSA9IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0eXBlOiAibnVtYmVyIiwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy/moLnmja7mnaHku7bliKTmlq3mlbDmja7mmK/lkKbor6XmuIXpmaQNCiAgICBjbGVhckRhdGFCeUNvbmRpdGlvbigpIHsNCiAgICAgIGlmICh0aGlzLmFtbWV0ZXIucHJvcGVydHkgIT09IDIgJiYgdGhpcy5hbW1ldGVyLnByb3BlcnR5ICE9PSA0KSB7DQogICAgICAgIC8v56uZ5Z2A5Lqn5p2D5b2S5bGe5Li66ZOB5aGUIOa4hemZpOWIhuWJsuavlOS+i2NoZWNrQW1tZXRlckJ5U3RhdGlvbu+8jOaYr+WQpumTgeWhlOaMiVJSVeWMheW5sg0KICAgICAgICB0aGlzLmFtbWV0ZXIucGVyY2VudCA9IG51bGw7DQogICAgICB9DQogICAgICBpZiAodGhpcy5hbW1ldGVyLmFtbWV0ZXJ1c2UgIT09IDMpIHsNCiAgICAgICAgLy/nlLXooajnlKjpgJTkuI3mmK/lm57mlLbnlLXotLnvvIzmuIXpmaTniLbnlLXooajkv6Hmga8NCiAgICAgICAgdGhpcy5hbW1ldGVyLnBhcmVudElkID0gbnVsbDsNCiAgICAgICAgdGhpcy5hbW1ldGVyLmN1c3RvbWVySWQgPSBudWxsOw0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuYW1tZXRlci5kaXJlY3RzdXBwbHlmbGFnICE9IDEpIHsNCiAgICAgICAgLy/lj6rmnInlr7nlpJbnu5PnrpfnsbvlnovkuLrnm7TkvpvnlLXmiY3loavlhpnor6XlrZfmrrXvvIzovazkvpvnlLXkuI3pnIDloavlhpkNCiAgICAgICAgdGhpcy5hbW1ldGVyLmVsZWN0cm92YWxlbmNlbmF0dXJlID0gbnVsbDsNCiAgICAgIH0NCiAgICAgIGlmICghdGhpcy5pc0NEQ29tcGFueSkgew0KICAgICAgICAvL+aIkOmDveWIhuWFrOWPuOaYvuekuuWQiOWQjOWvueaWueetie+8jOS4jeaYr++8jOWwsea4hemZpOaVsOaNrg0KICAgICAgICB0aGlzLmFtbWV0ZXIuY29udHJhY3RPdGhQYXJ0ID0gbnVsbDsNCiAgICAgICAgdGhpcy5hbW1ldGVyLm5tQ2NvZGUgPSBudWxsOw0KICAgICAgICB0aGlzLmFtbWV0ZXIubm1MMjEwMCA9IG51bGw7DQogICAgICAgIHRoaXMuYW1tZXRlci5ubUwxODAwID0gbnVsbDsNCiAgICAgICAgdGhpcy5hbW1ldGVyLm5tQ2w4MDBtID0gbnVsbDsNCiAgICAgIH0NCiAgICB9LA0KICAgIHdhcm4oKSB7DQogICAgICB0aGlzLiRNb2RhbC53YXJuaW5nKHsNCiAgICAgICAgdGl0bGU6ICLmuKnppqjmj5DnpLoiLA0KICAgICAgICBjb250ZW50OiAi5L+d5a2Y5ZCO55qE5pWw5o2u6KaB5o+Q5Lqk5a6h5om55omN6IO955Sf5pWI77yBIiwNCiAgICAgIH0pOw0KICAgIH0sDQogICAgcmVmcmVzaERhdGEoKSB7DQogICAgICB0aGlzLmluaXREYXRhKCk7DQogICAgfSwNCiAgICBpbml0RGF0YSgpIHsNCiAgICAgIHRoaXMuY291bnRyeU5hbWUgPSAiIjsNCiAgICAgIHRoaXMuZWxlY3Ryby5kYXRhID0gW107DQogICAgICB0aGlzLnJlbW92ZUlkcyA9IFtdOw0KICAgICAgdGhpcy5tdWx0aUZpbGVzID0gW107DQogICAgICB0aGlzLmZpbGVzID0gW107DQogICAgICB0aGlzLm9sZERhdGEgPSBbXTsNCiAgICAgIHRoaXMuaXNDaXR5QWRtaW4gPSBmYWxzZTsNCiAgICAgIHRoaXMuaXNBZG1pbiA9IGZhbHNlOw0KICAgICAgdGhpcy5pc0VkaXRCeUNvdW50cnkgPSBmYWxzZTsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy4kcmVmcy5hbW1ldGVyLnJlc2V0RmllbGRzKCk7IC8vIHRoaXMuJHJlZnMuYWRkdXNlcmZvcm0ucmVzZXRGaWVsZHMoKTsNCiAgICAgICAgdGhpcy4kcmVmcy5hbW1ldGVyMS5yZXNldEZpZWxkcygpOyAvLyB0aGlzLiRyZWZzLmFkZHVzZXJmb3JtLnJlc2V0RmllbGRzKCk7DQogICAgICAgIHRoaXMuJHJlZnMuYW1tZXRlcjIucmVzZXRGaWVsZHMoKTsgLy8gdGhpcy4kcmVmcy5hZGR1c2VyZm9ybS5yZXNldEZpZWxkcygpOw0KICAgICAgfSk7DQogICAgICB0aGlzLnNob3dNb2RlbCA9IGZhbHNlOw0KICAgICAgdGhpcy5lbGVjdHJpY1R5cGVNb2RlbCA9IGZhbHNlOw0KICAgIH0sDQogICAgb25Nb2RhbENhbmNlbCgpIHsNCiAgICAgIHRoaXMuaW5pdERhdGEoKTsNCiAgICB9LA0KICAgIC8q5Yid5aeL5YyWKi8NCiAgICBpbml0QW1tZXRlcihpZCkgew0KICAgICAgY29uc29sZS5sb2coaWQsICJpbml0QW1tZXRlcihpZCkiKTsNCiAgICAgIHRoaXMuaW5pdERhdGEoKTsNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIGlmIChpZCAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnnlLXooagiOw0KICAgICAgICB0aGlzLmlzRWRpdEJ5Q291bnRyeSA9IHRydWU7DQogICAgICAgIC8v6I635Y+W5LiK5LiA5qyh5L+u5pS55Y6G5Y+yDQogICAgICAgIGVkaXRBbW1ldGVyUmVjb3JkKHsgaWQ6IGlkIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGNvbnNvbGUubG9nKCLojrflj5bmlbDmja4tLS0iLCByZXMpOw0KICAgICAgICAgIGlmIChyZXMuZGF0YS5pZCAhPSB1bmRlZmluZWQgJiYgcmVzLmRhdGEuaWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgaWYgKG51bGwgIT0gcmVzLmRhdGEubWF4ZGVncmVlKSB7DQogICAgICAgICAgICAgIHJlcy5kYXRhLm1heGRlZ3JlZSA9IHBhcnNlRmxvYXQocmVzLmRhdGEubWF4ZGVncmVlKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHRoaXMuc2V0QW1tZXRlcihPYmplY3QuYXNzaWduKHt9LCByZXMuZGF0YSkpOw0KICAgICAgICAgICAgdGhpcy5saXN0RWxlY3RyaWNUeXBlUmF0aW8oaWQsIHJlcy5kYXRhLmlkLCByZXMuZGF0YS5zdGF0aW9uY29kZSk7DQogICAgICAgICAgICB0aGlzLmFtbWV0ZXIuaWQgPSBpZDsNCiAgICAgICAgICAgIHRoYXQuZmlsZVBhcmFtLmJ1c2lJZCA9IGlkOw0KICAgICAgICAgICAgZ2V0Q2xhc3NpZmljYXRpb25JZCh0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICB0aGlzLmFtbWV0ZXIuY2xhc3NpZmljYXRpb25zID0gcmVzLmRhdGE7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIGF0dGNoTGlzdCh7IGJ1c2lJZDogdGhhdC5maWxlUGFyYW0uYnVzaUlkIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICB0aGF0LmF0dGFjaERhdGEgPSBPYmplY3QuYXNzaWduKFtdLCByZXMuZGF0YS5yb3dzKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBlZGl0QW1tZXRlcihpZCkudGhlbigocmVzMSkgPT4gew0KICAgICAgICAgICAgICBpZiAobnVsbCAhPSByZXMxLmRhdGEubWF4ZGVncmVlKSB7DQogICAgICAgICAgICAgICAgcmVzMS5kYXRhLm1heGRlZ3JlZSA9IHBhcnNlRmxvYXQocmVzMS5kYXRhLm1heGRlZ3JlZSk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgdGhpcy5zZXRBbW1ldGVyKHJlczEuZGF0YSk7DQogICAgICAgICAgICAgIHRoYXQuZmlsZVBhcmFtLmJ1c2lJZCA9IHRoYXQuYW1tZXRlci5pZDsNCiAgICAgICAgICAgICAgdGhpcy5saXN0RWxlY3RyaWNUeXBlUmF0aW8oaWQsIG51bGwsIHJlczEuZGF0YS5zdGF0aW9uY29kZSk7DQogICAgICAgICAgICAgIGdldENsYXNzaWZpY2F0aW9uSWQodGhpcy5hbW1ldGVyLmVsZWN0cm90eXBlKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLmFtbWV0ZXIuY2xhc3NpZmljYXRpb25zID0gcmVzLmRhdGE7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICBhdHRjaExpc3QoeyBidXNpSWQ6IHRoYXQuZmlsZVBhcmFtLmJ1c2lJZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgICB0aGF0LmF0dGFjaERhdGEgPSBPYmplY3QuYXNzaWduKFtdLCByZXMuZGF0YS5yb3dzKTsNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICAgICAgfSk7DQogICAgICAgIHRoaXMuZ2V0VXNlcigpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDnlLXooagiOw0KICAgICAgICBlZGl0QW1tZXRlcigiIiwgMCkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgdGhpcy5zZXRBbW1ldGVyKE9iamVjdC5hc3NpZ24oe30sIHJlcy5kYXRhKSk7DQogICAgICAgICAgdGhpcy5nZXRVc2VyKCk7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgZ2V0Q2xhc3NpZmljYXRpb24oKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgLy/nlKjnlLXnsbvlnosNCiAgICAgICAgdGhpcy5jbGFzc2lmaWNhdGlvbkRhdGEgPSByZXMuZGF0YTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgbGlzdEVsZWN0cmljVHlwZVJhdGlvKGlkLCByZWNvcmRJZCwgc3RhdGlvbmNvZGUpIHsNCiAgICAgIGxpc3RFbGVjdHJpY1R5cGVSYXRpbyh7IGFtbWV0ZXJJZDogaWQsIGFtbWV0ZXJSZWNvcmRJZDogcmVjb3JkSWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHJlcy5kYXRhLnJvd3MuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIGlmIChpdGVtLnN0YXRpb25JZCA9PSBudWxsIHx8IGl0ZW0uc3RhdGlvbklkID09IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgaXRlbS5zdGF0aW9uSWQgPSBudWxsOw0KICAgICAgICAgICAgaXRlbS5zdGF0aW9uTmFtZSA9IG51bGw7DQogICAgICAgICAgfSBlbHNlIGlmIChpdGVtLnN0YXRpb25JZCA9PSBzdGF0aW9uY29kZSkgew0KICAgICAgICAgICAgaXRlbS5fZGlzYWJsZWQgPSB0cnVlOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICAgIHRoaXMuZWxlY3Ryby5kYXRhID0gT2JqZWN0LmFzc2lnbihbXSwgcmVzLmRhdGEucm93cyk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGNoYW5nZVN0YXR1cygpIHsNCiAgICAgIGlmICh0aGlzLmFtbWV0ZXIuc3RhdHVzID09IDEpIHsNCiAgICAgICAgdGhpcy5pc1JlcXVpcmVGbGFnID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5ydWxlVmFsaWRhdGUuc3RhdGlvbk5hbWUgPSBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5pc1JlcXVpcmVGbGFnID0gZmFsc2U7DQogICAgICAgIHRoaXMucnVsZVZhbGlkYXRlLnN0YXRpb25OYW1lID0gWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IGZhbHNlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF07DQogICAgICB9DQogICAgfSwNCiAgICBzZWxlY3RDaGFuZ2UoKSB7DQogICAgICBpZiAodGhpcy5hbW1ldGVyLmNvbXBhbnkgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgIGlmICh0aGlzLmFtbWV0ZXIuY29tcGFueSA9PSAiMTAwMDA4NSIpIHsNCiAgICAgICAgICB0aGlzLmlzQ0RDb21wYW55ID0gdHJ1ZTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmlzQ0RDb21wYW55ID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgICAgZ2V0Q291bnRyeUJ5VXNlcklkKHRoaXMuYW1tZXRlci5jb21wYW55KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLmRlcGFydG1lbnRzID0gcmVzLmRhdGEuZGVwYXJ0bWVudHM7DQogICAgICAgICAgdGhpcy5hbW1ldGVyLmNvdW50cnkgPSByZXMuZGF0YS5kZXBhcnRtZW50c1swXS5pZDsNCiAgICAgICAgICB0aGlzLmFtbWV0ZXIuY291bnRyeU5hbWUgPSB0aGlzLmRlcGFydG1lbnRzWzBdLm5hbWU7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0VXNlcigpIHsNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIGdldFVzZXJCeVVzZXJSb2xlKCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIC8v5b2T5YmN55m75b2V55So5oi35omA5Zyo5YWs5Y+4DQogICAgICAgIHRoYXQuY29tcGFuaWVzID0gcmVzLmRhdGEuY29tcGFuaWVzOw0KICAgICAgICB0aGF0LmlzQ2l0eUFkbWluID0gcmVzLmRhdGEuaXNFZGl0QWRtaW47DQogICAgICAgIGlmICgNCiAgICAgICAgICByZXMuZGF0YS5pc0NpdHlBZG1pbiA9PSB0cnVlIHx8DQogICAgICAgICAgcmVzLmRhdGEuaXNQcm9BZG1pbiA9PSB0cnVlIHx8DQogICAgICAgICAgcmVzLmRhdGEuaXNTdWJBZG1pbiA9PSB0cnVlDQogICAgICAgICkgew0KICAgICAgICAgIHRoYXQuaXNBZG1pbiA9IHRydWU7DQogICAgICAgIH0NCiAgICAgICAgZ2V0Q291bnRyeXNkYXRhKHsgb3JnQ29kZTogcmVzLmRhdGEuY29tcGFuaWVzWzBdLmlkIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIC8v5qC55o2u5p2D6ZmQ6I635Y+W5omA5bGe6YOo6ZeoDQogICAgICAgICAgdGhhdC5kZXBhcnRtZW50cyA9IHJlcy5kYXRhOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgc2V0T2xkRGF0YShkYXRhKSB7DQogICAgICB0aGlzLm9sZENhdGVnb3J5ID0gYnRleHQoImFtbWV0ZXJDYXRlZ29yeSIsIGRhdGEuY2F0ZWdvcnksICJ0eXBlQ29kZSIsICJ0eXBlTmFtZSIpOw0KICAgICAgdGhpcy5vbGRQYWNrYWdldHlwZSA9IGJ0ZXh0KA0KICAgICAgICAicGFja2FnZVR5cGUiLA0KICAgICAgICBkYXRhLnBhY2thZ2V0eXBlLA0KICAgICAgICAidHlwZUNvZGUiLA0KICAgICAgICAidHlwZU5hbWUiDQogICAgICApOw0KICAgICAgdGhpcy5vbGRQYXlwZXJpb2QgPSBidGV4dCgicGF5UGVyaW9kIiwgZGF0YS5wYXlwZXJpb2QsICJ0eXBlQ29kZSIsICJ0eXBlTmFtZSIpOw0KICAgICAgdGhpcy5vbGRQYXl0eXBlID0gYnRleHQoInBheVR5cGUiLCBkYXRhLnBheXR5cGUsICJ0eXBlQ29kZSIsICJ0eXBlTmFtZSIpOw0KICAgICAgdGhpcy5vbGRFbGVjdHJvbmF0dXJlID0gYnRleHQoDQogICAgICAgICJlbGVjdHJvTmF0dXJlIiwNCiAgICAgICAgZGF0YS5lbGVjdHJvbmF0dXJlLA0KICAgICAgICAidHlwZUNvZGUiLA0KICAgICAgICAidHlwZU5hbWUiDQogICAgICApOw0KICAgICAgdGhpcy5vbGRFbGVjdHJvdmFsZW5jZW5hdHVyZSA9IGJ0ZXh0KA0KICAgICAgICAiZWxlY3Ryb3ZhbGVuY2VOYXR1cmUiLA0KICAgICAgICBkYXRhLmVsZWN0cm92YWxlbmNlbmF0dXJlLA0KICAgICAgICAidHlwZUNvZGUiLA0KICAgICAgICAidHlwZU5hbWUiDQogICAgICApOw0KICAgICAgdGhpcy5vbGRFbGVjdHJvdHlwZSA9IGJ0ZXh0KA0KICAgICAgICAiZWxlY3Ryb1R5cGUiLA0KICAgICAgICBkYXRhLmVsZWN0cm90eXBlLA0KICAgICAgICAidHlwZUNvZGUiLA0KICAgICAgICAidHlwZU5hbWUiDQogICAgICApOw0KICAgICAgdGhpcy5vbGRTdGF0dXMgPSBidGV4dCgic3RhdHVzIiwgZGF0YS5zdGF0dXMsICJ0eXBlQ29kZSIsICJ0eXBlTmFtZSIpOw0KICAgICAgdGhpcy5vbGRQcm9wZXJ0eSA9IGJ0ZXh0KCJwcm9wZXJ0eSIsIGRhdGEucHJvcGVydHksICJ0eXBlQ29kZSIsICJ0eXBlTmFtZSIpOw0KICAgICAgdGhpcy5vbGRBbW1ldGVydHlwZSA9IGJ0ZXh0KA0KICAgICAgICAiYW1tZXRlclR5cGUiLA0KICAgICAgICBkYXRhLmFtbWV0ZXJ0eXBlLA0KICAgICAgICAidHlwZUNvZGUiLA0KICAgICAgICAidHlwZU5hbWUiDQogICAgICApOw0KICAgICAgdGhpcy5vbGRTdGF0aW9uc3RhdHVzID0gYnRleHQoDQogICAgICAgICJzdGF0aW9uU3RhdHVzIiwNCiAgICAgICAgZGF0YS5zdGF0aW9uc3RhdHVzLA0KICAgICAgICAidHlwZUNvZGUiLA0KICAgICAgICAidHlwZU5hbWUiDQogICAgICApOw0KICAgICAgdGhpcy5vbGRTdGF0aW9udHlwZSA9IGJ0ZXh0KA0KICAgICAgICAiQlVSX1NUQU5EX1RZUEUiLA0KICAgICAgICBkYXRhLnN0YXRpb250eXBlLA0KICAgICAgICAidHlwZUNvZGUiLA0KICAgICAgICAidHlwZU5hbWUiDQogICAgICApOw0KICAgICAgdGhpcy5vbGREaXJlY3RzdXBwbHlmbGFnID0gYnRleHQoDQogICAgICAgICJkaXJlY3RTdXBwbHlGbGFnIiwNCiAgICAgICAgZGF0YS5kaXJlY3RzdXBwbHlmbGFnLA0KICAgICAgICAidHlwZUNvZGUiLA0KICAgICAgICAidHlwZU5hbWUiDQogICAgICApOw0KICAgICAgdGhpcy5vbGRBbW1ldGVydXNlID0gYnRleHQoImFtbWV0ZXJVc2UiLCBkYXRhLmFtbWV0ZXJ1c2UsICJ0eXBlQ29kZSIsICJ0eXBlTmFtZSIpOw0KICAgICAgdGhpcy5vbGR2b2x0YWdlQ2xhc3MgPSBidGV4dCh7DQogICAgICAgIGNhdGVnb3J5OiAidm9sdGFnZUNsYXNzIiwNCiAgICAgICAgdjogZGF0YS52b2x0YWdlQ2xhc3MsDQogICAgICAgIHZhbHVlRmllbGQ6ICJ0eXBlQ29kZSIsDQogICAgICAgIGxhYmVsRmllbGQ6ICJ0eXBlTmFtZSIsDQogICAgICB9KTsNCiAgICB9LA0KDQogICAgc2V0QW1tZXRlcihmb3JtKSB7DQogICAgICBpZiAoZm9ybS5zdGF0dXMgPT0gbnVsbCB8fCBmb3JtLnN0YXR1cyA9PT0gMSkgew0KICAgICAgICBmb3JtLnN0YXR1cyA9IDE7DQogICAgICAgIHRoaXMuaXNSZXF1aXJlRmxhZyA9IHRydWU7DQogICAgICAgIHRoaXMucnVsZVZhbGlkYXRlLnN0YXRpb25OYW1lID0gWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuaXNSZXF1aXJlRmxhZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLnJ1bGVWYWxpZGF0ZS5zdGF0aW9uTmFtZSA9IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiBmYWxzZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdOw0KICAgICAgfQ0KICAgICAgaWYgKGZvcm0uZWxlY3Ryb3ZhbGVuY2VuYXR1cmUgIT0gMSAmJiBmb3JtLmVsZWN0cm92YWxlbmNlbmF0dXJlICE9IDIpIHsNCiAgICAgICAgZm9ybS5lbGVjdHJvdmFsZW5jZW5hdHVyZSA9IG51bGw7DQogICAgICB9DQogICAgICBmb3JtLmlzc21hcnRhbW1ldGVyID0gZm9ybS5pc3NtYXJ0YW1tZXRlciA9PSBudWxsID8gIjAiIDogZm9ybS5pc3NtYXJ0YW1tZXRlciArICIiOw0KICAgICAgZm9ybS5pc2VudGl0eWFtbWV0ZXIgPQ0KICAgICAgICBmb3JtLmlzZW50aXR5YW1tZXRlciA9PSBudWxsID8gbnVsbCA6IGZvcm0uaXNlbnRpdHlhbW1ldGVyICsgIiI7DQogICAgICBmb3JtLmlzYWlyY29uZGl0aW9uaW5nID0NCiAgICAgICAgZm9ybS5pc2FpcmNvbmRpdGlvbmluZyA9PSBudWxsID8gIjAiIDogZm9ybS5pc2FpcmNvbmRpdGlvbmluZyArICIiOw0KICAgICAgZm9ybS5pc2NoYW5nZWFtbWV0ZXIgPQ0KICAgICAgICBmb3JtLmlzY2hhbmdlYW1tZXRlciA9PSBudWxsID8gbnVsbCA6IGZvcm0uaXNjaGFuZ2VhbW1ldGVyICsgIiI7DQogICAgICBmb3JtLm9sZEJpbGxQb3dlciA9IGZvcm0ub2xkQmlsbFBvd2VyID09IG51bGwgPyAiIiA6IGZvcm0ub2xkQmlsbFBvd2VyICsgIiI7DQogICAgICBmb3JtLmlzbHVtcHN1bSA9IGZvcm0uaXNsdW1wc3VtID09IG51bGwgPyAiMCIgOiBmb3JtLmlzbHVtcHN1bSArICIiOw0KICAgICAgZm9ybS5pc3pneiA9IGZvcm0uaXN6Z3ogPT0gbnVsbCA/ICIwIiA6IGZvcm0uaXN6Z3ogKyAiIjsNCiAgICAgIGZvcm0uZGlyZWN0RmxhZyA9IGZvcm0uZGlyZWN0RmxhZyA9PSBudWxsID8gIjAiIDogZm9ybS5kaXJlY3RGbGFnICsgIiI7DQogICAgICBmb3JtLm9mZmljZUZsYWcgPSBmb3JtLm9mZmljZUZsYWcgPT0gbnVsbCA/ICIwIiA6IGZvcm0ub2ZmaWNlRmxhZyArICIiOw0KICAgICAgaWYgKGZvcm0uaXN6Z3ogPT0gIjEiKSB0aGlzLmRpc2FibGVkaXN6Z3ogPSB0cnVlOw0KICAgICAgdGhpcy5hbW1ldGVyID0gZm9ybTsNCiAgICAgIGxldCBlbGVjdHJvdHlwZSA9IHRoaXMuYW1tZXRlci5lbGVjdHJvdHlwZTsNCiAgICAgIHRoaXMuaXNNb2JpbGVCYXNlID0gZWxlY3Ryb3R5cGUgPiAxNDAwID8gdHJ1ZSA6IGZhbHNlOw0KICAgICAgaWYgKA0KICAgICAgICBlbGVjdHJvdHlwZSA9PT0gMTExIHx8DQogICAgICAgIGVsZWN0cm90eXBlID09PSAxMTIgfHwNCiAgICAgICAgZWxlY3Ryb3R5cGUgPT09IDExMyB8fA0KICAgICAgICBlbGVjdHJvdHlwZSA9PT0gMg0KICAgICAgKSB7DQogICAgICAgIHRoaXMuaXNDbGFzc2lmaWNhdGlvbiA9IHRydWU7DQogICAgICB9DQogICAgICBpZiAoDQogICAgICAgIChlbGVjdHJvdHlwZSAhPSBudWxsICYmIGVsZWN0cm90eXBlICE9PSAxNDExICYmIGVsZWN0cm90eXBlICE9PSAxNDEyKSB8fA0KICAgICAgICB0aGlzLmFtbWV0ZXIucHJvcGVydHkgIT09IDINCiAgICAgICkgew0KICAgICAgICB0aGlzLnByb3BlcnR5UmVhZG9ubHkgPSBmYWxzZTsNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmFtbWV0ZXIubWFnbmlmaWNhdGlvbiA9PSBudWxsKSB7DQogICAgICAgIHRoaXMuYW1tZXRlci5tYWduaWZpY2F0aW9uID0gMTsNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmFtbWV0ZXIuY29tcGFueSAhPSBudWxsKSB7DQogICAgICAgIHRoaXMuYW1tZXRlci5jb21wYW55ID0gdGhpcy5hbW1ldGVyLmNvbXBhbnkgKyAiIjsNCiAgICAgICAgaWYgKHRoaXMuYW1tZXRlci5jb21wYW55ID09ICIxMDAwMDg1Iikgew0KICAgICAgICAgIHRoaXMuaXNDRENvbXBhbnkgPSB0cnVlOw0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAodGhpcy5hbW1ldGVyLnByb2Nlc3NpbnN0SWQgIT0gbnVsbCkgew0KICAgICAgICB0aGlzLmlzU2hvd0Zsb3cgPSB0cnVlOw0KICAgICAgfQ0KICAgICAgdGhpcy5mbG93TmFtZSA9IHRoaXMuYW1tZXRlci5wcm9qZWN0bmFtZTsgLy/nlKjkuo7mj5DkuqTmtYHnqIvkvb/nlKjljp/pobnnm67lkI3np7ANCiAgICAgIHRoaXMuc2hvd01vZGVsID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgLy/kv67mlLnnlLXooajjgIHljY/orq7nmoTnlKjnlLXnsbvlnovml7bvvIzlpoLnlKjnlLXnsbvlnovkuI3lho3kuI7ljp/lhYjpgInmi6nnmoTlsYDnq5nnmoTlsYDnq5nnsbvlnovljLnphY3ml7bvvIzns7vnu5/oh6rliqjmuIXnqbrljp/lhbPogZTlsYDnq5nvvIzpnIDnlKjmiLfph43mlrDlho3lhbPogZTlsYDnq5njgIINCiAgICBjaGFuZ2VDbGFzc2lmaWNhdGlvbnModmFsdWUpIHsNCiAgICAgIHRoaXMuaXNDbGFzc2lmaWNhdGlvbiA9IGZhbHNlOw0KICAgICAgdGhpcy5jbGVhclN0YXRpb24oKTsNCiAgICAgIGlmICh2YWx1ZS5sZW5ndGggPT0gMCkgew0KICAgICAgICAvLyB0aGlzLmNsZWFyU3RhdGlvbigpOw0KICAgICAgICB0aGlzLmFtbWV0ZXIucHJvcGVydHkgPSBudWxsOw0KICAgICAgICB0aGlzLnByb3BlcnR5UmVhZG9ubHkgPSB0cnVlOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5wcm9wZXJ0eVJlYWRvbmx5ID0gZmFsc2U7DQogICAgICAgIHRoaXMuYW1tZXRlci5lbGVjdHJvdHlwZSA9IHZhbHVlW3ZhbHVlLmxlbmd0aCAtIDFdOw0KICAgICAgICBsZXQgZWxlY3Ryb3R5cGUgPSB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGU7DQogICAgICAgIHRoaXMuaXNNb2JpbGVCYXNlID0gZWxlY3Ryb3R5cGUgPiAxNDAwID8gdHJ1ZSA6IGZhbHNlOw0KICAgICAgICBpZiAoZWxlY3Ryb3R5cGUgPT09IDE0MTEgfHwgZWxlY3Ryb3R5cGUgPT09IDE0MTIpIHsNCiAgICAgICAgICAvL+aOp+WItuS6p+adg+W9kuWxng0KICAgICAgICAgIHRoaXMuYW1tZXRlci5wcm9wZXJ0eSA9IDI7DQogICAgICAgICAgdGhpcy5wcm9wZXJ0eVJlYWRvbmx5ID0gdHJ1ZTsNCiAgICAgICAgICB0aGlzLnJ1bGVWYWxpZGF0ZS5zdXBwbHlidXJlYXVhbW1ldGVyY29kZSA9IFsNCiAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICBdOw0KICAgICAgICB9IGVsc2UgaWYgKGVsZWN0cm90eXBlID09PSAxNDIxIHx8IGVsZWN0cm90eXBlID09PSAxNDIyKSB7DQogICAgICAgICAgdGhpcy5wcm9wZXJ0eVJlYWRvbmx5ID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5hbW1ldGVyLnByb3BlcnR5ID0gNDsNCiAgICAgICAgICB0aGlzLnJ1bGVWYWxpZGF0ZS5zdXBwbHlidXJlYXVhbW1ldGVyY29kZSA9IFsNCiAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICBdOw0KICAgICAgICB9IGVsc2UgaWYgKGVsZWN0cm90eXBlID09PSAxNDMxIHx8IGVsZWN0cm90eXBlID09PSAxNDMyKSB7DQogICAgICAgICAgdGhpcy5wcm9wZXJ0eVJlYWRvbmx5ID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5hbW1ldGVyLnByb3BlcnR5ID0gMTsNCiAgICAgICAgICB0aGlzLnJ1bGVWYWxpZGF0ZS5zdXBwbHlidXJlYXVhbW1ldGVyY29kZSA9IFsNCiAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICBdOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMucHJvcGVydHlSZWFkb25seSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuYW1tZXRlci5wcm9wZXJ0eSA9IG51bGw7DQogICAgICAgICAgdGhpcy5ydWxlVmFsaWRhdGUuc3VwcGx5YnVyZWF1YW1tZXRlcmNvZGUgPSBbDQogICAgICAgICAgICB7IHJlcXVpcmVkOiBmYWxzZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIF07DQogICAgICAgIH0NCiAgICAgICAgLy8gY2hlY2tDbGFzc2lmaWNhdGlvbkxldmVsKHRoaXMuYW1tZXRlci5lbGVjdHJvdHlwZSkudGhlbihyZXMgPT4gew0KICAgICAgICAvLyAgICAgbGV0IGNvZGUgPSByZXMuZGF0YS5tc2c7DQogICAgICAgIC8vICAgICBpZiAoY29kZSAhPT0gJzEnKSB7DQogICAgICAgIGxldCBzdGF0aW9udHlwZSA9IHRoaXMuYW1tZXRlci5zdGF0aW9udHlwZTsNCiAgICAgICAgaWYgKGVsZWN0cm90eXBlID09PSAxMTEgfHwgZWxlY3Ryb3R5cGUgPT09IDExMiB8fCBlbGVjdHJvdHlwZSA9PT0gMTEzKSB7DQogICAgICAgICAgdGhpcy5pc0NsYXNzaWZpY2F0aW9uID0gdHJ1ZTsNCiAgICAgICAgICBpZiAoc3RhdGlvbnR5cGUgIT09IDEwMDAxKSB7DQogICAgICAgICAgICB0aGlzLmNsZWFyU3RhdGlvbigpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIGlmIChlbGVjdHJvdHlwZSA9PT0gMTIxIHx8IGVsZWN0cm90eXBlID09PSAxMTIpIHsNCiAgICAgICAgICBpZiAoc3RhdGlvbnR5cGUgIT09IDEwMDAzICYmIHN0YXRpb250eXBlICE9PSAxMDAwNCkgew0KICAgICAgICAgICAgdGhpcy5jbGVhclN0YXRpb24oKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSBpZiAoZWxlY3Ryb3R5cGUgPT09IDEzMSB8fCBlbGVjdHJvdHlwZSA9PT0gMTMyIHx8IGVsZWN0cm90eXBlID09PSAxMzMpIHsNCiAgICAgICAgICBpZiAoc3RhdGlvbnR5cGUgIT09IDEwMDA1KSB7DQogICAgICAgICAgICB0aGlzLmNsZWFyU3RhdGlvbigpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIGlmIChlbGVjdHJvdHlwZSA9PT0gMTQxMSB8fCBlbGVjdHJvdHlwZSA9PT0gMTQxMikgew0KICAgICAgICAgIGlmICgNCiAgICAgICAgICAgIHN0YXRpb250eXBlICE9PSAxMDAwMiB8fA0KICAgICAgICAgICAgKHN0YXRpb250eXBlID09IDEwMDAyICYmIHRoaXMucHJvcGVydHlyaWdodCAhPT0gMykNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIHRoaXMuY2xlYXJTdGF0aW9uKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2UgaWYgKA0KICAgICAgICAgIGVsZWN0cm90eXBlID09PSAxNDIxIHx8DQogICAgICAgICAgZWxlY3Ryb3R5cGUgPT09IDE0MjIgfHwNCiAgICAgICAgICBlbGVjdHJvdHlwZSA9PT0gMTQzMSB8fA0KICAgICAgICAgIGVsZWN0cm90eXBlID09PSAxNDMyDQogICAgICAgICkgew0KICAgICAgICAgIGlmIChzdGF0aW9udHlwZSAhPT0gMTAwMDIpIHsNCiAgICAgICAgICAgIHRoaXMuY2xlYXJTdGF0aW9uKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2UgaWYgKGVsZWN0cm90eXBlID09PSAyKSB7DQogICAgICAgICAgdGhpcy5pc0NsYXNzaWZpY2F0aW9uID0gdHJ1ZTsNCiAgICAgICAgICAvLyAgICAgaWYoc3RhdGlvbnR5cGUgIT09IDIwMDAxKXsgdGhpcy5jbGVhclN0YXRpb24oKTt9DQogICAgICAgICAgLy8gfWVsc2UgaWYoZWxlY3Ryb3R5cGUgPT09IDMxIHx8IGVsZWN0cm90eXBlID09PSAzMiB8fCBlbGVjdHJvdHlwZSA9PT0gMzMpew0KICAgICAgICAgIC8vICAgICBpZihzdGF0aW9udHlwZSAhPT0gMjAwMDIgfHwgc3RhdGlvbnR5cGUgIT09IC0yKXsgdGhpcy5jbGVhclN0YXRpb24oKTt9DQogICAgICAgICAgLy8gfWVsc2UgaWYoZWxlY3Ryb3R5cGUgPT09IDQpew0KICAgICAgICAgIC8vICAgICBpZihzdGF0aW9udHlwZSAhPT0gLTEgfHwgc3RhdGlvbnR5cGUgIT09IC0yKXsgdGhpcy5jbGVhclN0YXRpb24oKTt9DQogICAgICAgIH0NCiAgICAgICAgLy8gICAgIH0NCiAgICAgICAgLy8gfSk7DQogICAgICAgIGlmICh0aGlzLmNvbmZpZ1ZlcnNpb24gPT0gInNjIiAmJiB0aGlzLmFtbWV0ZXIuc3RhdGlvbmFkZHJlc3Njb2RlKSB7DQogICAgICAgICAgLy/igJw1MeKAneW8gOWktOmTgeWhlOermeWdgOe8lueggeaOp+WItg0KICAgICAgICAgIGlmICgNCiAgICAgICAgICAgIFsxNDExLCAxNDEyXS5pbmNsdWRlcyhlbGVjdHJvdHlwZSkgJiYNCiAgICAgICAgICAgICF0aGlzLmFtbWV0ZXIuc3RhdGlvbmFkZHJlc3Njb2RlLnN0YXJ0c1dpdGgoIjUxIikNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIHRoaXMuY2xlYXJTdGF0aW9uKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBjbGVhclN0YXRpb24oKSB7DQogICAgICAvL+a4hemZpOWxgOermeS/oeaBrw0KICAgICAgdGhpcy5hbW1ldGVyLnN0YXRpb25OYW1lID0gbnVsbDsNCiAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uY29kZSA9IG51bGw7DQogICAgICB0aGlzLmFtbWV0ZXIuc3RhdGlvbnN0YXR1cyA9IG51bGw7DQogICAgICB0aGlzLmFtbWV0ZXIuc3RhdGlvbnR5cGUgPSBudWxsOw0KICAgICAgdGhpcy5hbW1ldGVyLnN0YXRpb25hZGRyZXNzID0gbnVsbDsNCiAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uYWRkcmVzc2NvZGUgPSBudWxsOw0KICAgIH0sDQogICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jlvIDlp4sNCiAgICBjaG9vc2VSZXNwb25zZUNlbnRlcihpbmRleCwgcGFyYW1zLCBlbGVjdHJvUm93TnVtKSB7DQogICAgICB0aGlzLmNob29zZUluZGV4ID0gaW5kZXg7DQogICAgICB0aGlzLmVsZWN0cm9Sb3dOdW0gPSBlbGVjdHJvUm93TnVtOw0KICAgICAgaWYgKGluZGV4ID09IDEgfHwgaW5kZXggPT0gMikgew0KICAgICAgICBsZXQgdHlwZXMgPSB0aGlzLmFtbWV0ZXIuY2xhc3NpZmljYXRpb25zOw0KICAgICAgICBpZiAodHlwZXMubGVuZ3RoID09IDApIHsNCiAgICAgICAgICB0aGlzLiRNb2RhbC53YXJuaW5nKHsgdGl0bGU6ICLmuKnppqjmj5DnpLoiLCBjb250ZW50OiAi6K+35YWI6YCJ5oup55So55S157G75Z6L77yBIiB9KTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0gZWxzZSBpZiAodGhpcy5hbW1ldGVyLmFtbWV0ZXJ1c2UgPT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMuJE1vZGFsLndhcm5pbmcoeyB0aXRsZTogIua4qemmqOaPkOekuiIsIGNvbnRlbnQ6ICLor7flhYjpgInmi6nnlLXooajnlKjpgJTvvIEiIH0pOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBpZiAodGhpcy5hbW1ldGVyLmNvbXBhbnkgPT0gbnVsbCkgew0KICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLor7flhYjpgInmi6nliIblhazlj7giKTsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5hbW1ldGVyLmVsZWN0cm90eXBlID0gdHlwZXNbdHlwZXMubGVuZ3RoIC0gMV07DQogICAgICAgICAgLy8gaWYodGhpcy5jb25maWdWZXJzaW9uPT0nbG4nIHx8IHRoaXMuY29uZmlnVmVyc2lvbiA9PSdMTicpew0KICAgICAgICAgIC8vICAgICB0aGlzLiRyZWZzLnN0YXRpb25Nb2RhbExOLmluaXREYXRhTGlzdCh0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUsMCx0aGlzLmFtbWV0ZXIuYW1tZXRlcnVzZSxwYXJhbXMpOy8v5bGA56uZDQogICAgICAgICAgLy8gfWVsc2V7DQogICAgICAgICAgdGhpcy4kcmVmcy5zdGF0aW9uTW9kYWwuYW1tZXRlcmlkID0gdGhpcy5hbW1ldGVyLmlkOw0KICAgICAgICAgIHRoaXMuJHJlZnMuc3RhdGlvbk1vZGFsLmluaXREYXRhTGlzdCgNCiAgICAgICAgICAgIHRoaXMuYW1tZXRlci5lbGVjdHJvdHlwZSwNCiAgICAgICAgICAgIDAsDQogICAgICAgICAgICB0aGlzLmFtbWV0ZXIuYW1tZXRlcnVzZSwNCiAgICAgICAgICAgIHRoaXMuYW1tZXRlci5jb21wYW55LA0KICAgICAgICAgICAgcGFyYW1zDQogICAgICAgICAgKTsgLy/lsYDnq5kNCiAgICAgICAgICAvLyB9DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmICh0aGlzLmFtbWV0ZXIuY29tcGFueSA9PSBudWxsKSB7DQogICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLor7flhYjpgInmi6nliIblhazlj7giKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgdGhpcy4kcmVmcy5jb3VudHJ5TW9kYWwuY2hvb3NlKHRoaXMuYW1tZXRlci5jb21wYW55KTsgLy/miYDlsZ7pg6jpl6gNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldERhdGFGcm9tTW9kYWwoZGF0YSwgZmxhZykgew0KICAgICAgdGhpcy5hbW1ldGVyLmNvdW50cnkgPSBkYXRhLmlkOw0KICAgICAgdGhpcy5hbW1ldGVyLmNvdW50cnlOYW1lID0gZGF0YS5uYW1lOw0KICAgICAgLy90aGlzLmNob29zZVJlc3BvbnNlQ2VudGVyKDQsIGRhdGEpOw0KICAgICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jnu5PmnZ8NCiAgICB9LA0KICAgIC8v6I635Y+W5bGA56uZ5pWw5o2uDQogICAgZ2V0RGF0YUZyb21TdGF0aW9uTW9kYWwoZGF0YSwgZmxhZywgaXNtb2RhbDEpIHsNCiAgICAgIHRoaXMuaXNjaGVja1N0YXRpb24gPSBmbGFnOw0KICAgICAgdGhpcy5pc29sZGNoZWNrU3RhdGlvbiA9IGZsYWc7DQogICAgICB0aGlzLmlzbW9kYWwxID0gaXNtb2RhbDE7DQogICAgICBpZiAodGhpcy5jaG9vc2VJbmRleCA9PSAyKSB7DQogICAgICAgIHRoaXMuZWxlY3Ryby5kYXRhW3RoaXMuZWxlY3Ryb1Jvd051bV0uc3RhdGlvbklkID0gZGF0YS5pZDsNCiAgICAgICAgdGhpcy5lbGVjdHJvLmRhdGFbdGhpcy5lbGVjdHJvUm93TnVtXS5zdGF0aW9uTmFtZSA9IGRhdGEuc3RhdGlvbm5hbWU7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnByb3BlcnR5cmlnaHQgPSBkYXRhLnByb3BlcnR5cmlnaHQ7DQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uTmFtZSA9IGRhdGEuc3RhdGlvbm5hbWU7DQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uY29kZSA9IGRhdGEuaWQ7DQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uc3RhdHVzID0gTnVtYmVyKA0KICAgICAgICAgIGRhdGEuc3RhdHVzID09IHVuZGVmaW5lZCA/IGRhdGEuU1RBVFVTIDogZGF0YS5zdGF0dXMNCiAgICAgICAgKTsNCiAgICAgICAgdGhpcy5hbW1ldGVyLnN0YXRpb250eXBlID0gTnVtYmVyKGRhdGEuc3RhdGlvbnR5cGUpOw0KICAgICAgICB0aGlzLmFtbWV0ZXIuc3RhdGlvbmFkZHJlc3MgPSBkYXRhLmFkZHJlc3M7DQogICAgICAgIC8vIGlmIChkYXRhLnN0YXRpb250eXBlID09IDEwMDAyICYmIGRhdGEucHJvcGVydHlyaWdodCA9PSAzKSB7Ly/lj6rmnInlvZPlsYDnq5nnsbvlnovkuLrigJjnlJ/kuqfnlKjmiL8t56e75Yqo5Z+656uZ4oCZ5LiU5Lqn5p2D5Li64oCY56ef55So4oCZ5pe277yM5a2Y5pS+56uZ5Z2A57yW56CBDQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uYWRkcmVzc2NvZGUgPSBkYXRhLnJlc3N0YXRpb25jb2RlOw0KICAgICAgICB0aGlzLmFtbWV0ZXIucmVzc3RhdGlvbmNvZGUgPSBkYXRhLnJlc3N0YXRpb25jb2RlOw0KICAgICAgICAvLyB9DQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9ubmFtZTVnciA9IGRhdGEuc3RhdGlvbm5hbWU1Z3I7DQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uY29kZTVnciA9IGRhdGEuc3RhdGlvbmNvZGVpbnRpZDsNCiAgICAgICAgLy/pu5jorqTnlJ/miJDkuIDmnaHlhbPogZTnlKjnlLXnsbvlnosNCiAgICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgICBsaXN0RWxlY3RyaWNUeXBlKHsgaWQ6IGRhdGEuc3RhdGlvbnR5cGUgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgbGV0IHJlc3VsdCA9IHRoYXQuZWxlY3Ryby5kYXRhOw0KICAgICAgICAgIGxldCBlbGVjdHJvRGF0YSA9IE9iamVjdC5hc3NpZ24oW10sIHJlcy5kYXRhLnJvd3MpOw0KICAgICAgICAgIGxldCBjb3VudCA9IDA7DQogICAgICAgICAgaWYgKHJlc3VsdC5sZW5ndGggPT0gMCkgew0KICAgICAgICAgICAgY291bnQrKzsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcmVzdWx0LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgZWxlY3Ryb0RhdGEuZm9yRWFjaCgoaXRlbTEpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAoaXRlbS5pZCA9PT0gaXRlbTEuaWQpIHsNCiAgICAgICAgICAgICAgICAgIGVsZWN0cm9EYXRhWzBdLnN0YXRpb25JZCA9IGRhdGEuaWQ7DQogICAgICAgICAgICAgICAgICBlbGVjdHJvRGF0YVswXS5zdGF0aW9uTmFtZSA9IGRhdGEuc3RhdGlvbm5hbWU7DQogICAgICAgICAgICAgICAgICBlbGVjdHJvRGF0YVswXS5fZGlzYWJsZWQgPSB0cnVlOw0KICAgICAgICAgICAgICAgICAgbGV0IGluZGV4ID0gcmVzdWx0LmluZGV4T2YoaXRlbSk7DQogICAgICAgICAgICAgICAgICByZXN1bHQuc3BsaWNlKGluZGV4LCAxKTsNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgY291bnQrKzsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChjb3VudCA+IDApIHsNCiAgICAgICAgICAgIHRoYXQuZWxlY3Ryby5kYXRhID0gT2JqZWN0LmFzc2lnbihbXSwgcmVzLmRhdGEucm93cyk7DQogICAgICAgICAgICB0aGF0LmVsZWN0cm8uZGF0YVswXS5zdGF0aW9uSWQgPSBkYXRhLmlkOw0KICAgICAgICAgICAgdGhhdC5lbGVjdHJvLmRhdGFbMF0uc3RhdGlvbk5hbWUgPSBkYXRhLnN0YXRpb25uYW1lOw0KICAgICAgICAgICAgdGhhdC5lbGVjdHJvLmRhdGFbMF0uX2Rpc2FibGVkID0gdHJ1ZTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcmVzdWx0LnVuc2hpZnQoZWxlY3Ryb0RhdGFbMF0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8q5re75Yqg55S16KGo5YWz6IGU55So55S157G75Z6L5q+U546HKi8NCiAgICBhZGRFbGVjdHJpY1R5cGUoKSB7DQogICAgICB0aGlzLiRyZWZzLnNlbGVjdEVsZWN0cmljVHlwZS5pbml0RWxlY3RyaWNUeXBlKCk7DQogICAgfSwNCg0KICAgIHNldEF0dGFjaERhdGEoZGF0YSkgew0KICAgICAgdGhpcy5tdWx0aUZpbGVzID0gZGF0YS5kYXRhOw0KICAgICAgdGhpcy5yZW1vdmVJZHMgPSBkYXRhLmlkczsNCiAgICAgIHRoaXMubXVsdGlGaWxlcy5idXNpSWQgPSB0aGlzLmFtbWV0ZXIuaWQ7DQogICAgICBpZiAodGhpcy5yZW1vdmVJZHMubGVuZ3RoICE9IDAgJiYgZGF0YS50eXBlID09ICJyZW1vdmUiKSB7DQogICAgICAgIHRoaXMucmVtb3ZlQXR0YWNoKCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnVwbG9hZCgpOw0KICAgICAgfQ0KICAgIH0sDQogICAgcmVtb3ZlQXR0YWNoKCkgew0KICAgICAgcmVtb3ZlQXR0YWNoKHsgaWRzOiB0aGlzLnJlbW92ZUlkcy5qb2luKCkgfSkudGhlbigoKSA9PiB7fSk7DQogICAgfSwNCiAgICB1cGxvYWQoKSB7DQogICAgICBpZiAodGhpcy5hdHRhY2hEYXRhLmxlbmd0aCAhPSAwICYmIHRoaXMubXVsdGlGaWxlcy5sZW5ndGggIT0gMCkgew0KICAgICAgICAvLyB0aGlzLiRNZXNzYWdlLmluZm8oIuaPkOekujrkuIrkvKDmlofku7bov4flpKflj6/og73lr7zoh7TkuIrkvKDlpLHotKXvvIEiKTsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgYXhpb3MNCiAgICAgICAgICAucmVxdWVzdCh7DQogICAgICAgICAgICB1cmw6ICIvY29tbW9uL2F0dGFjaG1lbnRzL3VwbG9hZE11bHRpRmlsZSIsDQogICAgICAgICAgICBtZXRob2Q6ICJwb3N0IiwNCiAgICAgICAgICAgIGRhdGE6IHRoaXMubXVsdGlGaWxlcywNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuZGF0YS5jb2RlICE9IDApIHsNCiAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICAgICAgICBhdHRjaExpc3QoeyBidXNpSWQ6IHRoYXQuZmlsZVBhcmFtLmJ1c2lJZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgdGhhdC5hdHRhY2hEYXRhID0gT2JqZWN0LmFzc2lnbihbXSwgcmVzLmRhdGEucm93cyk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyrnp7vpmaTpgInkuK3nmoTnlKjnlLXnsbvlnovmr5TnjocqLw0KICAgIHJlbW92ZUVsZWN0cmljVHlwZSgpIHsNCiAgICAgIGxldCByb3dzID0gdGhpcy4kcmVmcy5hbW1ldGVyVGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICBsZXQgZGF0YXMgPSB0aGlzLmVsZWN0cm8uZGF0YTsNCiAgICAgIHJvd3MuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICBpZiAoaXRlbS5faW5kZXggIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgZGF0YXMuc3BsaWNlKGl0ZW0uX2luZGV4LCAxKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBkYXRhcy5mb3JFYWNoKChkYXRhKSA9PiB7DQogICAgICAgICAgICBpZiAoZGF0YS5pZCA9PT0gaXRlbS5pZCkgew0KICAgICAgICAgICAgICBsZXQgaW5kZXggPSBkYXRhcy5pbmRleE9mKGRhdGEpOw0KICAgICAgICAgICAgICBkYXRhcy5zcGxpY2UoaW5kZXgsIDEpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIHRoaXMuZWxlY3Ryby5kYXRhID0gZGF0YXM7DQogICAgfSwNCg0KICAgIC8qIOiuvue9rueUqOeUteexu+Wei+WIl+ihqCovDQogICAgc2V0RWxlY3RyaWNEYXRhOiBmdW5jdGlvbiAoZGF0YSkgew0KICAgICAgbGV0IG9yaWdpbiA9IHRoaXMuZWxlY3Ryby5kYXRhOw0KICAgICAgaWYgKG9yaWdpbi5sZW5ndGggPCAxKSB7DQogICAgICAgIHRoaXMuZWxlY3Ryby5kYXRhID0gZGF0YTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldCB0ZW0gPSBkYXRhOw0KICAgICAgICBmb3IgKGxldCBqID0gMDsgaiA8IG9yaWdpbi5sZW5ndGg7IGorKykgew0KICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGF0YS5sZW5ndGg7IGkrKykgew0KICAgICAgICAgICAgbGV0IHR5cGVJZCA9DQogICAgICAgICAgICAgIG9yaWdpbltqXS5lbGVjdHJvVHlwZUlkICE9IHVuZGVmaW5lZA0KICAgICAgICAgICAgICAgID8gb3JpZ2luW2pdLmVsZWN0cm9UeXBlSWQNCiAgICAgICAgICAgICAgICA6IG9yaWdpbltqXS5pZDsNCiAgICAgICAgICAgIGlmIChkYXRhW2ldLmlkID09PSB0eXBlSWQpIHsNCiAgICAgICAgICAgICAgdGVtLnNwbGljZSh0ZW0uaW5kZXhPZihkYXRhW2ldKSwgMSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuZWxlY3Ryby5kYXRhID0gdGhpcy5lbGVjdHJvLmRhdGEuY29uY2F0KHRlbSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8v55So55S157G75Z6L5q+U5L6L5qCh6aqMDQogICAgY2hlY2tFbGVjdHJpY1R5cGVJdGVtKCkgew0KICAgICAgbGV0IGl0ZW1zID0gdGhpcy5lbGVjdHJvLmRhdGE7DQogICAgICAvL+W9k+KAnOeUqOeUteexu+Wei+KAnemAieaLqeKAnDExMSBB57G75py65qW877yI5py65oi/77yJ77yMMTEyIELnsbvmnLrmpbzvvIjmnLrmiL/vvInvvIwxMTMgQ+exu+acuualvO+8iOacuuaIv++8iSDigJ3miJbigJwyIOeuoeeQhuWKnuWFrOeUqOeUteKAneaXtu+8jOaJjemcgOWhq+eUqOeUteexu+Wei+WIhuavlOS4lOW/heWhq++8jOeUqOeUteexu+Wei+avlOS+i+S5i+WSjOW/hemhu+etieS6jjEwMCUNCiAgICAgIGlmICgNCiAgICAgICAgdGhpcy5hbW1ldGVyLmVsZWN0cm90eXBlID09PSAxMTEgfHwNCiAgICAgICAgdGhpcy5hbW1ldGVyLmVsZWN0cm90eXBlID09PSAxMTIgfHwNCiAgICAgICAgdGhpcy5hbW1ldGVyLmVsZWN0cm90eXBlID09PSAxMTMgfHwNCiAgICAgICAgdGhpcy5hbW1ldGVyLmVsZWN0cm90eXBlID09PSAyDQogICAgICApIHsNCiAgICAgICAgbGV0IHN1bVJhdGlvID0gaXRlbXMucmVkdWNlKCh0b3RhbCwgaXRlbSkgPT4gew0KICAgICAgICAgIHJldHVybiB0b3RhbCArIGl0ZW0ucmF0aW87DQogICAgICAgIH0sIDApOw0KICAgICAgICBpZiAoc3VtUmF0aW8gIT09IDEwMCkgew0KICAgICAgICAgIHRoaXMuJE1vZGFsLndhcm5pbmcoew0KICAgICAgICAgICAgdGl0bGU6ICLmuKnppqjmj5DnpLoiLA0KICAgICAgICAgICAgY29udGVudDogIueUqOeUteexu+Wei+aJgOWNoOavlOS+i+WSjOW/hemhu+S4ujEwMCXvvIzlvZPliY3lgLzkuLoiICsgc3VtUmF0aW8gKyAiJSIsDQogICAgICAgICAgfSk7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gdHJ1ZTsNCiAgICB9LA0KICAgIHNob3dGbG93KCkgew0KICAgICAgdGhpcy5zaG93V29ya0Zsb3cgPSB0cnVlOw0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgdGhpcy5oaXNQYXJhbXMgPSB7DQogICAgICAgIGJ1c2lJZDogdGhhdC5hbW1ldGVyLmlkLA0KICAgICAgICBidXNpVHlwZTogdGhhdC5hbW1ldGVyLmJ1c2lBbGlhcywNCiAgICAgICAgcHJvY0luc3RJZDogdGhhdC5hbW1ldGVyLnByb2Nlc3NpbnN0SWQsDQogICAgICB9Ow0KICAgIH0sDQogICAgc3RhcnRGbG93KGRhdGEpIHsNCiAgICAgIGxldCBidXNpQWxpYXMgPSAiTU9ESUZZX0FNTSI7DQogICAgICBsZXQgYnVzaVRpdGxlID0gIuS/ruaUueeUteihqCgiICsgdGhpcy5mbG93TmFtZSArICIp5a6h5om5IjsNCiAgICAgIGlmIChkYXRhLmlzY2hhbmdlYW1tZXRlciA9PSAxICYmIGRhdGEuYmlsbFN0YXR1cyA8IDIpIHsNCiAgICAgICAgYnVzaUFsaWFzID0gIkFNTV9TV0lUQ0hfQU1NIjsNCiAgICAgICAgYnVzaVRpdGxlID0gIueUteihqOaNouihqCgiICsgdGhpcy5mbG93TmFtZSArICIp5a6h5om5IjsNCiAgICAgIH0NCiAgICAgIHRoaXMud29ya0Zsb3dQYXJhbXMgPSB7DQogICAgICAgIGJ1c2lJZDogZGF0YS5pZCwNCiAgICAgICAgYnVzaUFsaWFzOiBidXNpQWxpYXMsDQogICAgICAgIGJ1c2lUaXRsZTogYnVzaVRpdGxlLA0KICAgICAgfTsNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgew0KICAgICAgICB0aGF0LiRyZWZzLmNsd2ZidG4ub25DbGljaygpOw0KICAgICAgfSwgMjAwKTsNCiAgICB9LA0KICAgIGRvV29ya0Zsb3coZGF0YSkgew0KICAgICAgLy/mtYHnqIvlm57osIMNCiAgICAgIC8vIHRoaXMuY2xvc2VUYWdCeU5hbWUoey8vIOWFs+mXreW3sue7j+aJk+W8gOeahCDvvIzpgb/lhY3lhrLnqoENCiAgICAgIC8vICAgICByb3V0ZTogZ2V0SG9tZVJvdXRlKHJvdXRlcnMsICJhbW1ldGVyIiksDQogICAgICAvLyB9KTsNCiAgICAgIC8vIC8v6Lez6L2s6Iez5L+u5pS56aG16Z2iIOW5tuWFs+mXreW9k+WJjemhtQ0KICAgICAgLy8gdGhpcy5jbG9zZVRhZyh7DQogICAgICAvLyAgICAgcm91dGU6IHRoaXMuJHJvdXRlLCBuZXh0OiB7DQogICAgICAvLyAgICAgICAgIG5hbWU6ICJhbW1ldGVyIiwgcXVlcnk6IHt9DQogICAgICAvLyAgICAgfQ0KICAgICAgLy8gfSk7DQogICAgICB0aGlzLmNsb3NlVGFnKHsgcm91dGU6IHRoaXMuJHJvdXRlIH0pOw0KICAgICAgaWYgKGRhdGEgPT0gMCkgew0KICAgICAgICB0aGlzLndhcm4oKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8q6YCJ5oup55S16KGoL+WNj+iuriovDQogICAgYWRkQW1tZXRlclByb3RvY29sKCkgew0KICAgICAgdGhpcy4kcmVmcy5zZWxlY3RBbW1ldGVyUHJvdG9jb2wuaW5pdERhdGFMaXN0KDEsIHRoaXMuYW1tZXRlci5pZCk7DQogICAgfSwNCiAgICAvKiDpgInmi6nnlLXooajmiLflj7cv5Y2P6K6u57yW5Y+3Ki8NCiAgICBzZXRBbW1ldGVyUHJvcm9jb2xEYXRhOiBmdW5jdGlvbiAoZGF0YSkgew0KICAgICAgdGhpcy5hbW1ldGVyLnBhcmVudElkID0gZGF0YS5pZDsNCiAgICAgIGlmIChkYXRhLnByb3RvY29sbmFtZSAhPSBudWxsICYmIGRhdGEucHJvdG9jb2xuYW1lLmxlbmd0aCAhPSAwKSB7DQogICAgICAgIHRoaXMuYW1tZXRlci5wYXJlbnRDb2RlID0gZGF0YS5wcm90b2NvbG5hbWU7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmFtbWV0ZXIucGFyZW50Q29kZSA9IGRhdGEuYW1tZXRlcm5hbWU7DQogICAgICB9DQogICAgfSwNCiAgICAvKumAieaLqeWuouaItyovDQogICAgYWRkQ3VzdG9tZXIoKSB7DQogICAgICB0aGlzLiRyZWZzLmN1c3RvbWVyTGlzdC5jaG9vc2UoMik7IC8v5omT5byA5qih5oCB5qGGDQogICAgfSwNCiAgICBnZXREYXRhRnJvbUN1c3RvbWVyTW9kYWw6IGZ1bmN0aW9uIChkYXRhKSB7DQogICAgICB0aGlzLmFtbWV0ZXIuY3VzdG9tZXJJZCA9IGRhdGEuaWQ7DQogICAgICB0aGlzLmFtbWV0ZXIuY3VzdG9tZXJOYW1lID0gZGF0YS5uYW1lOw0KICAgIH0sDQogICAgLy/pgInmi6nljIXlubLnmoTml7blgJnkv67mlLnpu5jorqTljIXlubLnsbvlnosNCiAgICB1cGRhdGVwYWNrYWdldHlwZSgpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy5hbW1ldGVyOw0KICAgICAgZGF0YS5wYWNrYWdldHlwZSA9IG51bGw7DQogICAgfSwNCiAgICBpc3pnemNoYW5nZSgpIHsNCiAgICAgIGlmICh0aGlzLmFtbWV0ZXIuaXN6Z3ogPT0gIjEiKSB7DQogICAgICAgIHRoaXMuYW1tZXRlci5kaXJlY3RzdXBwbHlmbGFnID0gMTsNCiAgICAgICAgdGhpcy5pc3pnek9ubHkgPSB0cnVlOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5pc3pnek9ubHkgPSBmYWxzZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGNob29zZW9sZGFtbWV0ZXJuYW1lKCkgew0KICAgICAgaWYgKHRoaXMuZGlzYWJsZWRpc3pneikgcmV0dXJuOw0KICAgICAgdGhpcy4kcmVmcy5jaG9vc2VBbW1ldGVyTW9kZWwubW9kYWwucXVlcnlwYXJhbXMuc3RhdHVzID0gMDsNCiAgICAgIHRoaXMuJHJlZnMuY2hvb3NlQW1tZXRlck1vZGVsLm1vZGFsLnF1ZXJ5cGFyYW1zLmFtbWV0ZXJ1c2UgPSAxOw0KICAgICAgdGhpcy4kcmVmcy5jaG9vc2VBbW1ldGVyTW9kZWwubW9kYWwucXVlcnlwYXJhbXMudHlwZSA9IDM7DQogICAgICB0aGlzLiRyZWZzLmNob29zZUFtbWV0ZXJNb2RlbC5tb2RhbC5xdWVyeXBhcmFtcy5jb21wYW55ID0gdGhpcy5hbW1ldGVyLmNvbXBhbnk7DQogICAgICB0aGlzLiRyZWZzLmNob29zZUFtbWV0ZXJNb2RlbC5tb2RhbC5xdWVyeXBhcmFtcy5jb3VudHJ5ID0gdGhpcy5hbW1ldGVyLmNvdW50cnk7DQogICAgICB0aGlzLiRyZWZzLmNob29zZUFtbWV0ZXJNb2RlbC5tb2RhbC5xdWVyeXBhcmFtcy5kaXJlY3RzdXBwbHlmbGFnID0gMjsNCiAgICAgIHRoaXMuJHJlZnMuY2hvb3NlQW1tZXRlck1vZGVsLm1vZGFsLnNob3cgPSB0cnVlOw0KICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLlj4zlh7vpgInmi6nvvIHvvIEiKTsNCiAgICB9LA0KICAgIGdldEFtbWV0ZXJNb2RlbE1vZGFsKGRhdGEpIHsNCiAgICAgIHRoaXMuYW1tZXRlci5vbGRhbW1ldGVybmFtZSA9IGRhdGEubmFtZSArICIsIiArIGRhdGEuaWQ7DQogICAgICB0aGlzLmlzemd6bWVuYW1lID0gZGF0YS5uYW1lICsgIiwiICsgZGF0YS5pZDsNCiAgICB9LA0KICAgIGlzemd6bWVjaGFuZ2UoKSB7DQogICAgICBpZiAoIXRoaXMuaXN6Z3ptZW5hbWUpIHRoaXMuaXN6Z3ptZW5hbWUgPSB0aGlzLmFtbWV0ZXIub2xkYW1tZXRlcm5hbWU7DQogICAgICBpZiAodGhpcy5pc3pnem1lKSB7DQogICAgICAgIHRoaXMuYW1tZXRlci5vbGRhbW1ldGVybmFtZSA9IHRoaXMuYW1tZXRlci5hbW1ldGVybmFtZSArICIsIiArIHRoaXMuYW1tZXRlci5pZDsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmICh0aGlzLmlzemd6bWVuYW1lID09IHRoaXMuYW1tZXRlci5hbW1ldGVybmFtZSkgew0KICAgICAgICAgIHRoaXMuYW1tZXRlci5vbGRhbW1ldGVybmFtZSA9IG51bGw7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5hbW1ldGVyLm9sZGFtbWV0ZXJuYW1lID0gdGhpcy5pc3pnem1lbmFtZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgcHJvamVjdE5hbWVDaGFuZ2UodmFsKSB7DQogICAgICAvLyB2YXIgcGF0dD0vXihbXlx1MDAwMC1cdTAwZmZdK+i3rykoW15cdTAwMDAtXHUwMGZmXSopKFswLTldKuWPtykoW15cdTAwMDAtXHUwMGZmXSvmpbznlLXooagpJC87DQogICAgICBpZiAoDQogICAgICAgICEvXi4qKFteXHUwMDAwLVx1MDBmZl0r6LevKS4qJC8udGVzdCh2YWwpICYmDQogICAgICAgICEvXi4qKFteXHUwMDAwLVx1MDBmZl0qKShbMC05XSrlj7cpLiokLy50ZXN0KHZhbCkgJiYNCiAgICAgICAgIS9eLiooW15cdTAwMDAtXHUwMGZmXSvmpbznlLXooagpLiokLy50ZXN0KHZhbCkNCiAgICAgICkgew0KICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIua4qemmqOaPkOekuu+8mumbhuWbouimgeaxguagvOW8j+S4uigqKui3ryoq5Y+3KirmpbznlLXooagpIik7DQogICAgICB9DQogICAgfSwNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAvL+ebtOaOpeS7juWJjeWPsOWPlg0KICAgIHRoaXMuY2F0ZWdvcnlzID0gew0KICAgICAgZGlyZWN0c3VwcGx5ZmxhZzogYmxpc3QoImRpcmVjdFN1cHBseUZsYWciKSwNCiAgICB9Ow0KICAgIHRoaXMucHJvcGVydHlMaXN0ID0gYmxpc3QoInByb3BlcnR5Iik7DQogICAgdGhpcy5pbml0QW1tZXRlcih0aGlzLiRyb3V0ZS5xdWVyeS5pZCk7DQoNCiAgICB0aGlzLmNvbmZpZ1ZlcnNpb24gPSB0aGlzLiRjb25maWcudmVyc2lvbjsNCiAgICBpZiAodGhpcy5jb25maWdWZXJzaW9uICE9ICJsbiIgJiYgdGhpcy5jb25maWdWZXJzaW9uICE9ICJMTiIpIHsNCiAgICAgIHRoaXMucnVsZVZhbGlkYXRlLmFtbWV0ZXJuYW1lLnB1c2goew0KICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgIH0pOw0KICAgICAgdGhpcy5ydWxlVmFsaWRhdGUuY3VzdG9tZXJOYW1lID0gew0KICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgIH07DQogICAgICB0aGlzLnJ1bGVWYWxpZGF0ZS51c2VydW5pdC5wdXNoKHsNCiAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLA0KICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICB9KTsNCiAgICB9DQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["editAmmeter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6tDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "editAmmeter.vue", "sourceRoot": "src/view/basedata/ammeter", "sourcesContent": ["<template>\r\n  <div class=\"testaa\">\r\n    <!--重点就是下面的代码了-->\r\n    <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n    <div solt=\"header\">\r\n      <Row>\r\n        <Col span=\"24\" style=\"text-align: right; right: 10px\">\r\n          <Button\r\n            type=\"success\"\r\n            :loading=\"isLoading == 0 ? loading : false\"\r\n            @click=\"onModalOK1(0)\"\r\n            >保存</Button\r\n          >\r\n          <Button\r\n            type=\"primary\"\r\n            :loading=\"isLoading == 1 ? loading : false\"\r\n            @click=\"onModalOK1(1)\"\r\n            >提交</Button\r\n          >\r\n          <Button v-if=\"isShowFlow\" type=\"success\" @click=\"showFlow\">流程图</Button>\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n    <Modal\r\n      v-model=\"modal1\"\r\n      title=\"温馨提示\"\r\n      okText=\"是\"\r\n      cancelText=\"否\"\r\n      @on-ok=\"okModel\"\r\n      @on-cancel=\"cancelModel\"\r\n    >\r\n      <p style=\"margin: 25px 0 25px 40px\">是否新型室分?</p>\r\n    </Modal>\r\n    <cl-wf-btn\r\n      ref=\"clwfbtn\"\r\n      :isStart=\"true\"\r\n      :params=\"workFlowParams\"\r\n      @on-ok=\"doWorkFlow\"\r\n      v-show=\"false\"\r\n    ></cl-wf-btn>\r\n    <!-- 查看流程 -->\r\n    <Modal v-model=\"showWorkFlow\" title=\"电表流程及审批意见跟踪表\" :width=\"800\">\r\n      <WorkFlowInfoComponet\r\n        :wfHisParams=\"hisParams\"\r\n        v-if=\"showWorkFlow\"\r\n      ></WorkFlowInfoComponet>\r\n    </Modal>\r\n    <select-electric-type\r\n      ref=\"selectElectricType\"\r\n      v-on:listenToSetElectricType=\"setElectricData\"\r\n    ></select-electric-type>\r\n    <country-modal\r\n      ref=\"countryModal\"\r\n      v-on:getDataFromModal=\"getDataFromModal\"\r\n    ></country-modal>\r\n    <station-modal\r\n      ref=\"stationModal\"\r\n      v-on:getDataFromStationModal=\"getDataFromStationModal\"\r\n    ></station-modal>\r\n    <ammeter-protocol-list\r\n      ref=\"selectAmmeterProtocol\"\r\n      v-on:listenToSetAmmeterProrocol=\"setAmmeterProrocolData\"\r\n    ></ammeter-protocol-list>\r\n    <customer-list\r\n      ref=\"customerList\"\r\n      v-on:getDataFromCustomerModal=\"getDataFromCustomerModal\"\r\n    ></customer-list>\r\n    <!--        <Modal v-model=\"showModel\" width=\"80%\" :title=\"title\">-->\r\n    <Card class=\"menu-card\">\r\n      <Collapse :value=\"['Panel1', 'Panel2', 'Panel3', 'Panel4', 'Panel5']\">\r\n        <Panel name=\"Panel1\"\r\n          >基本信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"项目名称：\" prop=\"projectname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.projectname\"\r\n                        placeholder=\"**路**号**楼电表\"\r\n                        @on-blur=\"projectNameChange\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.projectname != null &&\r\n                          oldData.projectname != ammeter.projectname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.projectname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag === 1\">\r\n                    <FormItem\r\n                      label=\"供电局电表编号(下户户号)：\"\r\n                      prop=\"supplybureauammetercode\"\r\n                    >\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauammetercode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauammetercode != null &&\r\n                          oldData.supplybureauammetercode !=\r\n                            ammeter.supplybureauammetercode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauammetercode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag === 2\">\r\n                    <FormItem\r\n                      label=\"供电局电表编号(电表编号)：\"\r\n                      prop=\"supplybureauammetercode\"\r\n                    >\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauammetercode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauammetercode != null &&\r\n                          oldData.supplybureauammetercode !=\r\n                            ammeter.supplybureauammetercode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauammetercode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否智能电表：\" prop=\"issmartammeter\">\r\n                      <RadioGroup v-model=\"ammeter.issmartammeter\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.issmartammeter != null &&\r\n                          oldData.issmartammeter != ammeter.issmartammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.issmartammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否实体电表：\" prop=\"isentityammeter\">\r\n                      <RadioGroup v-model=\"ammeter.isentityammeter\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.isentityammeter != null &&\r\n                          oldData.isentityammeter != ammeter.isentityammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.isentityammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\" v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n                    <FormItem label=\"电表编号：\" prop=\"ammetername\">\r\n                      <cl-input\r\n                        readonly\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.ammetername\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetername != null &&\r\n                          oldData.ammetername != ammeter.ammetername\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetername }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"configVersion != 'ln' && configVersion != 'LN'\">\r\n                    <FormItem label=\"电表编号：\" prop=\"ammetername\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.ammetername\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetername != null &&\r\n                          oldData.ammetername != ammeter.ammetername\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetername }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"电表用途：\" prop=\"ammeteruse\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.ammeteruse\"\r\n                        category=\"ammeterUse\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammeteruse != null &&\r\n                          oldData.ammeteruse != ammeter.ammeteruse\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldAmmeteruse }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"关联实际报账电表：\"\r\n                      prop=\"parentCode\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.parentCode\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addAmmeterProtocol\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.parentCode != null &&\r\n                          oldData.parentCode != ammeter.parentCode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.parentCode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"供电局名称：\" prop=\"supplybureauname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauname != null &&\r\n                          oldData.supplybureauname != ammeter.supplybureauname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"电表类型：\" prop=\"ammetertype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.ammetertype\"\r\n                        category=\"ammeterType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetertype != null &&\r\n                          oldData.ammetertype != ammeter.ammetertype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldAmmetertype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"包干类型：\" prop=\"packagetype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.packagetype\"\r\n                        :disabled=\"ammeter.islumpsum == 1 ? false : true\"\r\n                        category=\"packageType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      >\r\n                      </cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.packagetype != null &&\r\n                          oldData.packagetype != ammeter.packagetype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPackagetype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"所属分公司：\" prop=\"company\">\r\n                      <Select\r\n                        v-model=\"ammeter.company\"\r\n                        @on-change=\"selectChange(ammeter.company)\"\r\n                      >\r\n                        <Option\r\n                          v-for=\"item in companies\"\r\n                          :value=\"item.id\"\r\n                          :key=\"item.id\"\r\n                          >{{ item.name }}</Option\r\n                        >\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.company != null && oldData.company != ammeter.company\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.companyName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <!--                                            <FormItem label=\"所属部门\" prop=\"countryName\">-->\r\n                    <!--                                                <Input icon=\"ios-archive\" v-model=\"ammeter.countryName\"-->\r\n                    <!--                                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly/>-->\r\n                    <!--                                            </FormItem>-->\r\n                    <FormItem\r\n                      label=\"所属部门：\"\r\n                      prop=\"countryName\"\r\n                      v-if=\"isAdmin == true\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-if=\"isCityAdmin == true || isEditByCountry == false\"\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseResponseCenter()\"\r\n                        readonly\r\n                      />\r\n                      <Input\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-else-if=\"isEditByCountry == true && isCityAdmin == false\"\r\n                        readonly\r\n                      />\r\n                      <!--                                                <label v-if=\"oldData.countryName != null &&oldData.countryName != ammeter.countryName\" style=\"color: red;\">历史数据：{{oldData.countryName}}</label>-->\r\n                    </FormItem>\r\n                    <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\">\r\n                      <Select v-model=\"ammeter.country\" v-if=\"isEditByCountry == false\">\r\n                        <Option\r\n                          v-for=\"item in departments\"\r\n                          :value=\"item.id\"\r\n                          :key=\"item.id\"\r\n                          >{{ item.name }}</Option\r\n                        >\r\n                      </Select>\r\n                      <Input\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-else-if=\"isEditByCountry == true\"\r\n                        readonly\r\n                      />\r\n                      <!--                                                <label v-if=\"oldData.countryName != null &&oldData.countryName != ammeter.countryName\" style=\"color: red;\">历史数据：{{oldData.countryName}}</label>-->\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"所属分局或支局：\" prop=\"substation\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.substation\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.substation != null &&\r\n                          oldData.substation != ammeter.substation\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.substation }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"详细地址：\" prop=\"address\">\r\n                      <cl-input :maxlength=\"100\" v-model=\"ammeter.address\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.address != null && oldData.address != ammeter.address\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.address }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费名称：\" prop=\"payname\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.payname\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.payname != null && oldData.payname != ammeter.payname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.payname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费类型：\" prop=\"payperiod\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.payperiod\"\r\n                        category=\"payPeriod\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.payperiod != null &&\r\n                          oldData.payperiod != ammeter.payperiod\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPayperiod }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费经办人：\" prop=\"paymanager\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.paymanager\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.paymanager != null &&\r\n                          oldData.paymanager != ammeter.paymanager\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.paymanager }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"付费方式：\" prop=\"paytype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.paytype\"\r\n                        category=\"payType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.paytype != null && oldData.paytype != ammeter.paytype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPaytype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"管理负责人：\" prop=\"ammetermanager\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.ammetermanager\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetermanager != null &&\r\n                          oldData.ammetermanager != ammeter.ammetermanager\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetermanager }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"单价(元)：\" prop=\"price\">\r\n                      <InputNumber\r\n                        :min=\"0\"\r\n                        :maxlength=\"20\"\r\n                        v-model=\"ammeter.price\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.price != null && oldData.price != ammeter.price\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.price }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"用电类型：\" prop=\"classifications\">\r\n                      <Cascader\r\n                        :data=\"classificationData\"\r\n                        :change-on-select=\"true\"\r\n                        v-model=\"ammeter.classifications\"\r\n                        @on-change=\"changeClassifications\"\r\n                      ></Cascader>\r\n                      <label\r\n                        v-if=\"\r\n                          ammeter.classifications !== undefined &&\r\n                          ammeter.classifications.length > 0\r\n                        \"\r\n                      >\r\n                        <label v-for=\"(item, i) in ammeter.classifications\" :key=\"i\">\r\n                          <label\r\n                            v-if=\"\r\n                              i === ammeter.classifications.length - 1 &&\r\n                              oldData.electrotype != null &&\r\n                              oldData.electrotype != item\r\n                            \"\r\n                            style=\"color: red\"\r\n                            >历史数据：{{ oldData.electrotypename }}</label\r\n                          >\r\n                        </label>\r\n                      </label>\r\n                      <label\r\n                        v-else-if=\"\r\n                          ammeter.classifications !== undefined &&\r\n                          ammeter.classifications.length <= 0\r\n                        \"\r\n                      >\r\n                        <label v-if=\"oldData.electrotype != null\" style=\"color: red\"\r\n                          >历史数据：{{ oldData.electrotypename }}</label\r\n                        >\r\n                      </label>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"倍率：\" prop=\"magnification\">\r\n                      <InputNumber\r\n                        :min=\"1\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.magnification\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.magnification != null &&\r\n                          oldData.magnification != ammeter.magnification\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.magnification }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"对外结算类型：\" prop=\"directsupplyflag\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.directsupplyflag\"\r\n                        category=\"directSupplyFlag\"\r\n                        :disabled=\"iszgzOnly\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                        @on-change=\"changedirectsupply\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.directsupplyflag != null &&\r\n                          oldData.directsupplyflag != ammeter.directsupplyflag\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldDirectsupplyflag }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem label=\"电价性质：\" prop=\"electrovalencenature\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.electrovalencenature\"\r\n                        category=\"electrovalenceNature\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.electrovalencenature != null &&\r\n                          oldData.electrovalencenature != ammeter.electrovalencenature\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldElectrovalencenature }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"翻表读数(度)：\" prop=\"maxdegree\">\r\n                      <InputNumber\r\n                        :maxlength=\"20\"\r\n                        v-model=\"ammeter.maxdegree\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.maxdegree != null &&\r\n                          oldData.maxdegree != ammeter.maxdegree\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.maxdegree }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"状态：\" prop=\"status\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.status\"\r\n                        @on-change=\"changeStatus\"\r\n                        category=\"status\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"oldData.status != null && oldData.status != ammeter.status\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStatus }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"占局(站)定额电量比例(%)：\" prop=\"quotapowerratio\">\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :min=\"1\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.quotapowerratio\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.quotapowerratio != null &&\r\n                          oldData.quotapowerratio != ammeter.quotapowerratio\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.quotapowerratio }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"合同对方：\" prop=\"contractOthPart\">\r\n                      <cl-input\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.contractOthPart\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.contractOthPart != null &&\r\n                          oldData.contractOthPart != ammeter.contractOthPart\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.contractOthPart }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-else-if=\"!isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\">\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}</Option\r\n                        >\r\n                      </Select>\r\n                      <!--                                            <cl-select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\"-->\r\n                      <!--                                                       category=\"property\"-->\r\n                      <!--                                                       labelField=\"typeName\" valueField=\"typeCode\"></cl-select>-->\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"ammeter.ischangeammeter == 1 && ammeter.billStatus < 2\">\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\">\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}</Option\r\n                        >\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter == 1\">\r\n                    <FormItem label=\"是否换表：\" prop=\"ischangeammeter\">\r\n                      <RadioGroup v-model=\"ammeter.ischangeammeter\">\r\n                        <Radio label=\"0\" disabled>\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\" disabled>\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ischangeammeter != null &&\r\n                          oldData.ischangeammeter != ammeter.ischangeammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.ischangeammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter == 1\">\r\n                    <FormItem label=\"原电表/协议编号：\" prop=\"oldAmmeterName\">\r\n                      <cl-input v-model=\"ammeter.oldAmmeterName\" readonly></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.oldAmmeterName != null &&\r\n                          oldData.oldAmmeterName != ammeter.oldAmmeterName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.oldAmmeterName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter == 1\">\r\n                    <FormItem label=\"原电表还需报账电量(度)：\" prop=\"oldBillPower\">\r\n                      <cl-input\r\n                        :maxlength=\"20\"\r\n                        v-model=\"ammeter.oldBillPower\"\r\n                        :placeholder=\"\r\n                          [2, 4].includes(ammeter.property) ? '需填写【分摊后电量】' : ''\r\n                        \"\r\n                      ></cl-input>\r\n                      <label style=\"color: red; margin-left: -100px; margin-top: 5px\"\r\n                        >注意：该电量将计入新表的总电量，没有请填0</label\r\n                      >\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.oldBillPower != null &&\r\n                          oldData.oldBillPower != ammeter.oldBillPower\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.oldBillPower }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter != 1\">\r\n                    <FormItem\r\n                      label=\"分割比例(%)：\"\r\n                      prop=\"percent\"\r\n                      v-if=\"ammeter.property === 2 || ammeter.property === 4\"\r\n                    >\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.percent\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.percent != null && oldData.percent != ammeter.percent\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.percent }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter != 1\">\r\n                    <FormItem\r\n                      label=\"收款客户名称：\"\r\n                      prop=\"customerName\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.customerName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addCustomer\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.customerName != null &&\r\n                          oldData.customerName != ammeter.customerName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.customerName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"ammeter.ischangeammeter == 1 && ammeter.billStatus < 2\">\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter == 1\">\r\n                    <FormItem\r\n                      label=\"分割比例(%)：\"\r\n                      prop=\"percent\"\r\n                      v-if=\"ammeter.property === 2 || ammeter.property === 4\"\r\n                    >\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.percent\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.percent != null && oldData.percent != ammeter.percent\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.percent }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter == 1\">\r\n                    <FormItem\r\n                      label=\"收款客户名称：\"\r\n                      prop=\"customerName\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.customerName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addCustomer\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.customerName != null &&\r\n                          oldData.customerName != ammeter.customerName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.customerName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <!--                                    <Col span=\"6\">-->\r\n                  <!--                                        <FormItem label=\"生产占比(%)：\" prop=\"generationof\">-->\r\n                  <!--                                            <InputNumber :min=\"1\" :maxlength=15 v-model=\"ammeter.generationof\"></InputNumber>-->\r\n                  <!--                                            <label v-if=\"oldData.generationof != null &&oldData.generationof != ammeter.generationof\" style=\"color: red;\">历史数据：{{oldData.generationof}}</label>-->\r\n                  <!--                                        </FormItem>-->\r\n                  <!--                                    </Col>-->\r\n                </Row>\r\n                <Row\r\n                  v-if=\"\r\n                    ammeter.ischangeammeter != 1 ||\r\n                    (ammeter.ischangeammeter == 1 && ammeter.billStatus > 1)\r\n                  \"\r\n                >\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\">\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}</Option\r\n                        >\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"分割比例(%)：\"\r\n                      prop=\"percent\"\r\n                      v-if=\"ammeter.property === 2 || ammeter.property === 4\"\r\n                    >\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.percent\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.percent != null && oldData.percent != ammeter.percent\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.percent }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"收款客户名称：\"\r\n                      prop=\"customerName\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.customerName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addCustomer\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.customerName != null &&\r\n                          oldData.customerName != ammeter.customerName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.customerName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <!--                                    <Col span=\"6\">-->\r\n                  <!--                                        <FormItem label=\"生产占比(%)：\" prop=\"generationof\">-->\r\n                  <!--                                            <InputNumber :min=\"1\" :maxlength=15 v-model=\"ammeter.generationof\"></InputNumber>-->\r\n                  <!--                                            <label v-if=\"oldData.generationof != null &&oldData.generationof != ammeter.generationof\" style=\"color: red;\">历史数据：{{oldData.generationof}}</label>-->\r\n                  <!--                                        </FormItem>-->\r\n                  <!--                                    </Col>-->\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"是否铁塔包干：\"\r\n                      prop=\"islumpsum\"\r\n                      v-if=\"ammeter.property == 2\"\r\n                      class=\"form-line-height\"\r\n                    >\r\n                      <RadioGroup\r\n                        v-model=\"ammeter.islumpsum\"\r\n                        @on-change=\"updatepackagetype\"\r\n                      >\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.islumpsum != null &&\r\n                          oldData.islumpsum != ammeter.islumpsum\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >原是否铁塔按RRU包干：{{\r\n                          oldData.islumpsum == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"月包干电量(度)：\" prop=\"ybgPower\">\r\n                      <cl-input :maxlength=\"20\" v-model=\"ammeter.ybgPower\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ybgPower != null && oldData.ybgPower != ammeter.ybgPower\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ybgPower }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干起始日期：\" prop=\"lumpstartdate\">\r\n                      <cl-date-picker\r\n                        type=\"date\"\r\n                        placeholder=\"包干起始日期\"\r\n                        v-model=\"ammeter.lumpstartdate\"\r\n                        style=\"width: 160px\"\r\n                      ></cl-date-picker>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.lumpstartdate != null &&\r\n                          oldData.lumpstartdate != ammeter.lumpstartdate\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.lumpstartdate }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干截止日期：\" prop=\"lumpenddate\">\r\n                      <cl-date-picker\r\n                        type=\"date\"\r\n                        placeholder=\"包干截止日期\"\r\n                        v-model=\"ammeter.lumpenddate\"\r\n                        style=\"width: 160px\"\r\n                      ></cl-date-picker>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.lumpenddate != null &&\r\n                          oldData.lumpenddate != ammeter.lumpenddate\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.lumpenddate }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"ammeter.packagetype != null && ammeter.islumpsum == 1\"\r\n                  >\r\n                    <FormItem label=\"月包干电费：\" prop=\"fee\">\r\n                      <InputNumber\r\n                        :min=\"0\"\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.fee\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.fee != null && oldData.fee != ammeter.fee\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.fee }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc' || configVersion == 'SC'\">\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"输配电公司：\"\r\n                      prop=\"transdistricompany\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: true,\r\n                          type: 'number',\r\n                          message: '请选择',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <cl-select\r\n                        v-model=\"ammeter.transdistricompany\"\r\n                        category=\"transdistricompany\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.transdistricompany != null &&\r\n                          oldData.transdistricompany != ammeter.transdistricompany\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldtransdistricompany }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.directsupplyflag == 1 && ammeter.transdistricompany == 1\r\n                    \"\r\n                  >\r\n                    <FormItem\r\n                      label=\"电压等级：\"\r\n                      prop=\"voltageClass\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: true,\r\n                          type: 'number',\r\n                          message: '请选择',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <cl-select\r\n                        v-model=\"ammeter.voltageClass\"\r\n                        category=\"voltageClass\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.voltageClass != null &&\r\n                          oldData.voltageClass != ammeter.voltageClass\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldvoltageClass }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc'\">\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"是否转改直：\"\r\n                      prop=\"iszgz\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.iszgz\" @on-change=\"iszgzchange\">\r\n                        <Radio label=\"0\" :disabled=\"disablediszgz\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"原转供电表编号：\"\r\n                      prop=\"oldammetername\"\r\n                      v-if=\"ammeter.iszgz == '1'\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: ammeter.iszgz == '1',\r\n                          message: '不能为空',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        :value=\"\r\n                          ammeter.oldammetername\r\n                            ? ammeter.oldammetername.split(',')[0]\r\n                            : null\r\n                        \"\r\n                        readonly\r\n                        :disabled=\"disablediszgz\"\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseoldammetername\"\r\n                      />\r\n                    </FormItem>\r\n                    <ChooseAmmeterModel\r\n                      ref=\"chooseAmmeterModel\"\r\n                      v-if=\"ammeter.iszgz == '1'\"\r\n                      v-on:getAmmeterModelModal=\"getAmmeterModelModal\"\r\n                    />\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"原转供电表为当前电表：\" v-if=\"ammeter.iszgz == '1'\">\r\n                      <i-switch\r\n                        v-model=\"iszgzme\"\r\n                        @on-change=\"iszgzmechange\"\r\n                        :disabled=\"disablediszgz\"\r\n                      />\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"是否直购电：\"\r\n                      prop=\"directFlag\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.directFlag\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"是否含办公：\"\r\n                      prop=\"officeFlag\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.officeFlag\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel2\"\r\n          >关联局站信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter1\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"局(站)名称：\"\r\n                      prop=\"stationName\"\r\n                      :class=\"{ requireStar: isRequireFlag }\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.stationName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseResponseCenter(1)\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationName != null &&\r\n                          oldData.stationName != ammeter.stationName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)编码：\" prop=\"stationcode\">\r\n                      <cl-input readonly v-model=\"ammeter.stationcode\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationcode != null &&\r\n                          oldData.stationcode != ammeter.stationcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)状态：\" prop=\"stationstatus\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.stationstatus\"\r\n                        filterable\r\n                        category=\"stationStatus\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationstatus != null &&\r\n                          oldData.stationstatus != ammeter.stationstatus\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStationstatus }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)类型：\" prop=\"stationtype\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.stationtype\"\r\n                        filterable\r\n                        category=\"BUR_STAND_TYPE\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationtype != null &&\r\n                          oldData.stationtype != ammeter.stationtype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStationtype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)地址：\" prop=\"stationaddress\">\r\n                      <cl-input readonly v-model=\"ammeter.stationaddress\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationaddress != null &&\r\n                          oldData.stationaddress != ammeter.stationaddress\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationaddress }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"资源局站id\" prop=\"resstationcode\">\r\n                      <cl-input\r\n                        readonly\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.resstationcode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.resstationcode != null &&\r\n                          oldData.resstationcode != ammeter.resstationcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.resstationcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否有空调：\" prop=\"isairconditioning\">\r\n                      <RadioGroup v-model=\"ammeter.isairconditioning\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.isairconditioning != null &&\r\n                          oldData.isairconditioning != ammeter.isairconditioning\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.isairconditioning == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"核定电量：\" prop=\"vouchelectricity\">\r\n                      <InputNumber\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.vouchelectricity\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.vouchelectricity != null &&\r\n                          oldData.vouchelectricity != ammeter.vouchelectricity\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.vouchelectricity }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"isCDCompany\">\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管C网编号：\" prop=\"nmCcode\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmCcode\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmCcode != null && oldData.nmCcode != ammeter.nmCcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmCcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号L2.1G：\" prop=\"nmL2100\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmL2100\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmL2100 != null && oldData.nmL2100 != ammeter.nmL2100\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmL2100 }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号L1.8G：\" prop=\"nmL1800\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmL1800\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmL1800 != null && oldData.nmL1800 != ammeter.nmL1800\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmL1800 }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号C+L800M：\" prop=\"nmCl800m\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmCl800m\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmCl800m != null && oldData.nmCl800m != ammeter.nmCl800m\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmCl800m }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc' && isMobileBase\">\r\n                  <Col span=\"12\">\r\n                    <FormItem label=\"5GR站址编码：\" prop=\"stationcode5gr\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.stationcode5gr\"\r\n                        readonly\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationcode5gr != null &&\r\n                          oldData.stationcode5gr != ammeter.stationcode5gr\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationcode5gr }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"12\">\r\n                    <FormItem label=\"5GR站址名称：\" prop=\"stationname5gr\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.stationname5gr\"\r\n                        readonly\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationname5gr != null &&\r\n                          oldData.stationname5gr != ammeter.stationname5gr\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationname5gr }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel3\"\r\n          >关联用电类型比例\r\n          <!--                        <span style=\"font-size: 10px;color:red\">（用电类型为：A类机楼、B类机楼、C类机楼、管理办公用电时必须关联用电类型比例）</span>-->\r\n          <!-- <div slot=\"content\" v-if=\"isClassification && ammeter.stationcode!= null\">-->\r\n          <div slot=\"content\" v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n            <cl-table\r\n              ref=\"ammeterTable\"\r\n              strip\r\n              :columns=\"electro.columns\"\r\n              :data=\"electro.data\"\r\n              :searchable=\"false\"\r\n              :showPage=\"false\"\r\n              selectEnabled\r\n              selectMultiple\r\n            >\r\n              <div slot=\"buttons\">\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"md-add\"\r\n                  type=\"primary\"\r\n                  @click=\"addElectricType\"\r\n                  >新增\r\n                </Button>\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"ios-trash-outline\"\r\n                  type=\"warning\"\r\n                  @click=\"removeElectricType\"\r\n                  >删除\r\n                </Button>\r\n              </div>\r\n            </cl-table>\r\n          </div>\r\n          <div slot=\"content\" v-else-if=\"isClassification && ammeter.stationcode != null\">\r\n            <cl-table\r\n              ref=\"ammeterTable\"\r\n              strip\r\n              :columns=\"electro.columns\"\r\n              :data=\"electro.data\"\r\n              :searchable=\"false\"\r\n              :showPage=\"false\"\r\n              selectEnabled\r\n              selectMultiple\r\n            >\r\n              <div slot=\"buttons\">\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"md-add\"\r\n                  type=\"primary\"\r\n                  @click=\"addElectricType\"\r\n                  >新增\r\n                </Button>\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"ios-trash-outline\"\r\n                  type=\"warning\"\r\n                  @click=\"removeElectricType\"\r\n                  >删除\r\n                </Button>\r\n              </div>\r\n            </cl-table>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel4\"\r\n          >业主信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter2\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"联系人：\" prop=\"contractname\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.contractname\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.contractname != null &&\r\n                          oldData.contractname != ammeter.contractname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.contractname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"具体位置：\" prop=\"location\">\r\n                      <cl-input :maxlength=\"100\" v-model=\"ammeter.location\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.location != null && oldData.location != ammeter.location\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.location }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"办公电话：\" prop=\"officephone\">\r\n                      <cl-input :maxlength=\"15\" v-model=\"ammeter.officephone\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.officephone != null &&\r\n                          oldData.officephone != ammeter.officephone\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.officephone }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"移动电话：\" prop=\"telephone\">\r\n                      <cl-input :maxlength=\"15\" v-model=\"ammeter.telephone\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.telephone != null &&\r\n                          oldData.telephone != ammeter.telephone\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.telephone }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"对方单位：\" prop=\"userunit\">\r\n                      <cl-input\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.userunit\"\r\n                      ></cl-input>\r\n                      <ChooseModal\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        ref=\"chooseModalSup2\"\r\n                        v-on:getDataFromModal=\"getDataFromModalObject\"\r\n                      />\r\n                      <Input\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        :maxlength=\"50\"\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.userunit\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"handleChooseSup()\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.userunit != null && oldData.userunit != ammeter.userunit\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.userunit }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款账户：\" prop=\"receiptaccountname\">\r\n                      <Select\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        v-model=\"ammeter.receiptaccountname\"\r\n                        style=\"width: 100%\"\r\n                        @on-change=\"onChange\"\r\n                      >\r\n                        <Option\r\n                          v-for=\"item in receiptaccountnameList\"\r\n                          :value=\"item.koinh\"\r\n                          :label=\"item.koinh\"\r\n                          :key=\"item.iid\"\r\n                        ></Option>\r\n                      </Select>\r\n                      <cl-input\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.receiptaccountname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccountname != null &&\r\n                          oldData.receiptaccountname != ammeter.receiptaccountname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccountname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款开户支行：\" prop=\"receiptaccountbank\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        readonly\r\n                        v-model=\"ammeter.receiptaccountbank\"\r\n                      ></cl-input>\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        v-model=\"ammeter.receiptaccountbank\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccountbank != null &&\r\n                          oldData.receiptaccountbank != ammeter.receiptaccountbank\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccountbank }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款账号：\" prop=\"receiptaccounts\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        readonly\r\n                        v-model=\"ammeter.receiptaccounts\"\r\n                      ></cl-input>\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        v-model=\"ammeter.receiptaccounts\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccounts != null &&\r\n                          oldData.receiptaccounts != ammeter.receiptaccounts\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccounts }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"24\">\r\n                    <FormItem label=\"说明：\" prop=\"memo\">\r\n                      <cl-input\r\n                        type=\"textarea\"\r\n                        :rows=\"3\"\r\n                        v-model=\"ammeter.memo\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"oldData.memo != null && oldData.memo != ammeter.memo\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.memo }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel5\"\r\n          >附件信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <attach-file\r\n                :param=\"fileParam\"\r\n                :attachData=\"attachData\"\r\n                v-on:setAttachData=\"setAttachData\"\r\n              />\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n      </Collapse>\r\n    </Card>\r\n    <!--            <div slot=\"footer\">-->\r\n    <!--                <Button type=\"text\" size=\"large\" @click=\"onModalCancel\">取消</Button>-->\r\n    <!--                <Button type=\"primary\" size=\"large\" @click=\"onModalOK\">保存</Button>-->\r\n    <!--            </div>-->\r\n    <!--        </Modal>-->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listElectricType,\r\n  checkAmmeterExist,\r\n  getCountrysdata,\r\n  editAmmeter,\r\n  editAmmeterRecord,\r\n  updateAmmeter,\r\n  checkProjectNameExist,\r\n  checkAmmeterByStation,\r\n  getClassification,\r\n  getClassificationId,\r\n  getUserdata,\r\n  checkClassificationLevel,\r\n  listElectricTypeRatio,\r\n  checkAcountByUpdate,\r\n  getUserByUserRole,\r\n  getCountryByUserId,\r\n  removeAttach,\r\n  attchList,\r\n  checkStation,\r\n  getBankCard,\r\n} from \"@/api/basedata/ammeter.js\";\r\nimport { isInTodoList, getstationold } from \"@/api/alertcontrol/alertcontrol\";\r\nimport { blist, btext } from \"@/libs/tools\";\r\nimport SelectElectricType from \"./selectElectricType\";\r\nimport countryModal from \"./countryModal\";\r\nimport stationModal from \"./stationModal\";\r\nimport { isEmpty } from \"@/libs/validate\";\r\n// import stationLNModal from \"./stationModalLN\";\r\nimport { mapMutations } from \"vuex\";\r\nimport routers from \"@/router/routers\";\r\nimport { getHomeRoute } from \"@/libs/util\";\r\nimport WorkFlowInfoComponet from \"@/view/basic/system/workflow/workFlowInfoComponet\";\r\nimport AmmeterProtocolList from \"@/view/basedata/quota/listAmmeterProtocol\";\r\nimport customerList from \"./customerModal\";\r\nimport ChooseModal from \"@/view/business/gasBusiness/chooseModal\";\r\nimport ChooseAmmeterModel from \"@/view/basedata/ammeter/chooseAmmeterModel\";\r\nimport attachFile from \"./../protocol/attachFile\";\r\nimport axios from \"@/libs/api.request\";\r\n\r\nexport default {\r\n  name: \"updateAmmeter\",\r\n  components: {\r\n    attachFile,\r\n    stationModal,\r\n    customerList,\r\n    countryModal,\r\n    SelectElectricType,\r\n    WorkFlowInfoComponet,\r\n    AmmeterProtocolList,\r\n    ChooseAmmeterModel,\r\n    ChooseModal,\r\n  },\r\n  data() {\r\n    //不能输入汉字\r\n    const checkData = (rule, value, callback) => {\r\n      if (value) {\r\n        if (/[\\u4E00-\\u9FA5]/g.test(value)) {\r\n          callback(new Error(\"编码不能输入汉字!\"));\r\n        } else if (escape(value).indexOf(\"%u\") >= 0) {\r\n          callback(new Error(\"编码不能输入中文字符!\"));\r\n        } else {\r\n          callback();\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatorNumber = (rule, value, callback) => {\r\n      if (value.length <= 0) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatorNumberZero = (rule, value, callback) => {\r\n      if (value != null && value == 0) {\r\n        callback(new Error(\"只能输入大于0的数\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatorNumberZero1 = (rule, value, callback) => {\r\n      if (value != null && value < 0) {\r\n        callback(new Error(\"只能输入大于等于0的数\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validateClassifications = (rule, value, callback) => {\r\n      if (value == undefined || value == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        if (value.length <= 0) {\r\n          callback(new Error(\"不能为空\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatelumpstartdate = (rule, value, callback) => {\r\n      let data = this.ammeter;\r\n      let start = data.lumpstartdate;\r\n      let end = data.lumpenddate;\r\n      if (start == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      }\r\n      if (start != null && end != null) {\r\n        if (end <= start) {\r\n          callback(new Error(\"包干起始日期不能大于等于截止日期\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatelumpenddate = (rule, value, callback) => {\r\n      let data = this.ammeter;\r\n      let start = data.lumpstartdate;\r\n      let end = data.lumpenddate;\r\n      if (end == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      }\r\n      if (start != null && end != null) {\r\n        if (end <= start) {\r\n          callback(new Error(\"包干截止日期不能小于等于起始日期\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    //更改标题名称及样式\r\n    let renderHeader = (h, params) => {\r\n      let t = h(\r\n        \"span\",\r\n        {\r\n          style: {\r\n            fontWeight: \"normal\",\r\n            color: \"#ed4014\",\r\n            fontSize: \"12px\",\r\n            fontFamily: \"SimSun\",\r\n            marginRight: \"4px\",\r\n            lineHeight: 1,\r\n            display: \"inline-block\",\r\n          },\r\n        },\r\n        \"*\"\r\n      );\r\n      return h(\"div\", [t, h(\"span\", {}, \"所占比例(%)\")]);\r\n    };\r\n    return {\r\n      propertyright: null, //局站产权\r\n      isRequireFlag: false, //局站是否必填\r\n      modal1: false,\r\n      checkStationType: null,\r\n      ischeckStation: false, //是否需要验证局站只能关联5个\r\n      isoldcheckStation: null, //判断用户关联局站没有,默认没有\r\n      isCDCompany: false, //是否是成都分公司\r\n      isMobileBase: false,\r\n      configVersion: null, //版本\r\n      propertyList: [],\r\n      propertyReadonly: true,\r\n\r\n      workFlowParams: {},\r\n      hisParams: {},\r\n      isShowFlow: false,\r\n      showWorkFlow: false,\r\n      flowName: null,\r\n\r\n      isError: false, //用电类型比例验证\r\n      isError1: false, //用电类型比例验证\r\n\r\n      ismodal1: null, //是否已经提示过是否新型室分\r\n\r\n      loading: false,\r\n      isLoading: null,\r\n\r\n      showModel: false,\r\n      isClassification: false,\r\n      title: \"\",\r\n      isEditByCountry: false,\r\n      isCityAdmin: false,\r\n      isAdmin: false,\r\n      chooseIndex: null,\r\n      electroRowNum: null, //关联用电类型的当前行\r\n      electricTypeModel: false,\r\n      companies: [],\r\n      departments: [],\r\n      classificationData: [], //用电类型\r\n\r\n      oldData: [],\r\n      oldCategory: \"\", //原始数据\r\n      oldPackagetype: \"\", //原始数据\r\n      oldPayperiod: \"\", //原始数据\r\n      oldPaytype: \"\", //原始数据\r\n      oldElectronature: \"\", //原始数据\r\n      oldElectrovalencenature: \"\", //原始数据\r\n      oldElectrotype: \"\", //原始数据\r\n      oldStatus: \"\", //原始数据\r\n      oldProperty: \"\", //原始数据\r\n      oldAmmetertype: \"\", //原始数据\r\n      oldStationstatus: \"\", //原始数据\r\n      oldStationtype: \"\", //原始数据\r\n      oldAmmeteruse: \"\", //原始数据\r\n      oldDirectsupplyflag: \"\", //原始数据\r\n      ruleValidate: {\r\n        isentityammeter: [\r\n          { required: true, message: \"不能为空\", trigger: \"change,blur\" },\r\n        ],\r\n        projectname: [\r\n          //项目名称\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        countryName: [\r\n          //所属部门\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        country: [\r\n          //所属部门\r\n          { required: true, type: \"number\", validator: validatorNumber, trigger: \"blur\" },\r\n        ],\r\n        company: [{ required: true, validator: validatorNumber, trigger: \"blur\" }],\r\n        paytype: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        payperiod: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        ammeteruse: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        ammetertype: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        property: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        electrovalencenature: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        classifications: [\r\n          { required: true, validator: validateClassifications, trigger: \"change,blur\" },\r\n        ],\r\n        magnification: [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        directsupplyflag: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        packagetype: [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contractOthPart: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\r\n        stationName: [],\r\n        status: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        telephone: [{ pattern: /^1\\d{10}$/, message: \"格式不正确\", trigger: \"blur\" }],\r\n        percent: [\r\n          { type: \"number\", validator: validatorNumberZero, trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([0-9]\\d{0,12}))(\\.\\d{0,4})?$/,\r\n            message: \"只能保留四位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lumpstartdate: [\r\n          {\r\n            required: true,\r\n            type: \"date\",\r\n            validator: validatelumpstartdate,\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lumpenddate: [\r\n          {\r\n            required: true,\r\n            type: \"date\",\r\n            validator: validatelumpenddate,\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        fee: [\r\n          { required: true, type: \"number\", validator: validatorNumber, trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        supplybureauammetercode: [],\r\n        transdistricompany: [],\r\n        voltageClass: [],\r\n        customerName: [],\r\n        userunit: [],\r\n        ammetername: [],\r\n      },\r\n      electro: {\r\n        columns: [\r\n          {\r\n            title: \"序号\",\r\n            type: \"index\",\r\n          },\r\n          {\r\n            title: \"用电类型\",\r\n            key: \"typeName\",\r\n          },\r\n\r\n          {\r\n            title: \"所占比例(%)\",\r\n            key: \"ratio\",\r\n            renderHeader: renderHeader,\r\n            render: (h, params) => {\r\n              let that = this;\r\n              let ratio = params.row.ratio;\r\n              let isError1 = params.row.idError1;\r\n              let error = h(\r\n                \"label\",\r\n                {\r\n                  style: {\r\n                    color: \"#ed4014\",\r\n                    fontSize: \"12px\",\r\n                    fontFamily: \"SimSun\",\r\n                    paddingTop: \"6px\",\r\n                    lineHeight: 1,\r\n                    fontWeight: \"bold\",\r\n                    display: null != ratio ? \"none\" : \"inline-block\",\r\n                  },\r\n                },\r\n                \"不能为空\"\r\n              );\r\n              let error1 = h(\r\n                \"label\",\r\n                {\r\n                  style: {\r\n                    color: \"#ed4014\",\r\n                    fontSize: \"12px\",\r\n                    fontFamily: \"SimSun\",\r\n                    paddingTop: \"6px\",\r\n                    lineHeight: 1,\r\n                    fontWeight: \"bold\",\r\n                    display: isError1 == true ? \"inline-block\" : \"none\",\r\n                  },\r\n                },\r\n                \"输入比例不合格要求\"\r\n              );\r\n              let result = h(\"InputNumber\", {\r\n                style: {\r\n                  border: null == ratio || isError1 == true ? \"1px solid #ed4014\" : \"\",\r\n                },\r\n                props: {\r\n                  value: ratio,\r\n                  max: 100,\r\n                  min: 0.1,\r\n                },\r\n                on: {\r\n                  \"on-change\": (v) => {\r\n                    if (v == undefined || v == null) {\r\n                      that.isError = true;\r\n                    } else {\r\n                      that.isError = false;\r\n                    }\r\n                    //给data重新赋值\r\n                    // let reg = /^(?:[1-9]?\\d|100)$/;\r\n                    let reg = /^-?(([1-9][0-9]*)|(([0]\\.\\d{1,2}|[1-9][0-9]*\\.\\d{1,2})))$/;\r\n                    if (v != undefined && v != null && !reg.test(v)) {\r\n                      params.row.idError1 = true;\r\n                      that.isError1 = true;\r\n                    } else {\r\n                      params.row.idError1 = false;\r\n                      that.isError1 = false;\r\n                    }\r\n                    params.row.ratio = v;\r\n                    that.electro.data[params.row._index] = params.row;\r\n                  },\r\n                },\r\n              });\r\n              return h(\"div\", [result, error, error1]);\r\n            },\r\n          },\r\n          {\r\n            title: \"关联局站\",\r\n            key: \"stationName\",\r\n            render: (h, params) => {\r\n              let stationName = params.row.stationName;\r\n              let disabled = params.row._disabled;\r\n              if (disabled != undefined && disabled == true) {\r\n                return h(\"Input\", {\r\n                  props: {\r\n                    value: stationName,\r\n                    readonly: true,\r\n                  },\r\n                });\r\n              } else {\r\n                return h(\"Input\", {\r\n                  props: {\r\n                    value: stationName,\r\n                    icon: \"ios-archive\",\r\n                    placeholder: \"点击图标选择\",\r\n                    readonly: true,\r\n                  },\r\n                  on: {\r\n                    \"on-click\": (v) => {\r\n                      this.chooseResponseCenter(2, params, params.row._index);\r\n                    },\r\n                  },\r\n                });\r\n              }\r\n            },\r\n          },\r\n        ],\r\n        data: [],\r\n      },\r\n      removeIds: [],\r\n      files: [],\r\n      multiFiles: null,\r\n      fileParam: {\r\n        busiId: \"\",\r\n        busiAlias: \"附件(协议管理)\",\r\n        categoryCode: \"file\",\r\n        areaCode: \"ln\",\r\n      },\r\n      receiptaccountnameList: [], //银行卡列表\r\n      attachData: [],\r\n      ammeter: {\r\n        id: null,\r\n        country: null,\r\n        company: null,\r\n        countryName: \"\",\r\n        electricTypes: [],\r\n        electro: [],\r\n        classifications: [], //用电类型\r\n        iszgz: 0,\r\n        directFlag: 0,\r\n        officeFlag: 0,\r\n        transdistricompany: 1,\r\n        voltageClass: \"\",\r\n        stationcode5gr: null,\r\n        stationname5gr: null,\r\n      },\r\n      iszgzOnly: false,\r\n      iszgzme: false,\r\n      iszgzmename: null,\r\n      disablediszgz: false,\r\n      electricType: {\r\n        loading: false,\r\n        filter: [\r\n          {\r\n            formItemType: \"input\",\r\n            prop: \"name\",\r\n            label: \"用电类型\",\r\n            width: 100,\r\n            size: \"small\",\r\n          },\r\n        ],\r\n        columns: [\r\n          {\r\n            title: \"序号\",\r\n            type: \"index\",\r\n            align: \"center\",\r\n            width: 70,\r\n          },\r\n          {\r\n            title: \"id\",\r\n            key: \"id\",\r\n            align: \"center\",\r\n            width: 80,\r\n          },\r\n          {\r\n            title: \"用电类型\",\r\n            key: \"typeName\",\r\n            align: \"center\",\r\n            width: 80,\r\n          },\r\n        ],\r\n        data: [],\r\n        total: 0,\r\n        pageSize: 10,\r\n      },\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    ...mapMutations([\"closeTag\", \"closeTagByName\"]),\r\n\r\n    getNowTime() {\r\n      var date = new Date();\r\n      //年 getFullYear()：四位数字返回年份\r\n      var year = date.getFullYear(); //getFullYear()代替getYear()\r\n      //月 getMonth()：0 ~ 11\r\n      var month = date.getMonth() + 1;\r\n      //日 getDate()：(1 ~ 31)\r\n      var day = date.getDate();\r\n      //时 getHours()：(0 ~ 23)\r\n      var hour = date.getHours();\r\n      //分 getMinutes()： (0 ~ 59)\r\n      var minute = date.getMinutes();\r\n      //秒 getSeconds()：(0 ~ 59)\r\n      var second = date.getSeconds();\r\n\r\n      var time =\r\n        year +\r\n        \"-\" +\r\n        this.addZero(month) +\r\n        \"-\" +\r\n        this.addZero(day) +\r\n        \" \" +\r\n        this.addZero(hour) +\r\n        \":\" +\r\n        this.addZero(minute) +\r\n        \":\" +\r\n        this.addZero(second);\r\n      return time;\r\n    },\r\n\r\n    //小于10的拼接上0字符串\r\n    addZero(s) {\r\n      return s < 10 ? \"0\" + s : s;\r\n    },\r\n    getDataFromModalObject(data, flag) {\r\n      this.handleChooseSup(data); // 传 true 设置 回调值\r\n    },\r\n    onChange(v) {\r\n      this.receiptaccountnameList.forEach((item) => {\r\n        if (item.koinh === v) {\r\n          console.log(v);\r\n          this.ammeter.receiptaccountbank = item.banka;\r\n          this.ammeter.receiptaccounts = item.bankn;\r\n        }\r\n      });\r\n    },\r\n    //选择对方单位\r\n    handleChooseSup(data) {\r\n      if (!data) {\r\n        this.$refs.chooseModalSup2.choose(1); //打开模态框\r\n      } else {\r\n        (this.ammeter.userunit = data.name),\r\n          getBankCard({ lifnr: data.id }).then((res) => {\r\n            console.log(res.data);\r\n            this.receiptaccountnameList = res.data.rows;\r\n          });\r\n      }\r\n    },\r\n    //*****校验当前局站是是否过期\r\n    onModalOK1(type) {\r\n      let nowTime = this.getNowTime();\r\n      let params = {\r\n        stationaddr_code: this.ammeter.stationaddresscode,\r\n        serveenddate: nowTime,\r\n      };\r\n      //校验局站过期时间\r\n      checkStation(params).then((res) => {\r\n        console.log(res.data);\r\n        this.ammeter.map = res.data;\r\n        this.onModalOK(type);\r\n      });\r\n    },\r\n\r\n    OK(type) {\r\n      this.checkStationType = type;\r\n      if (type == 1) {\r\n        this.isLoading = 1;\r\n      } else {\r\n        this.isLoading = 0;\r\n      }\r\n      if (this.loading == true) {\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      this.ammeter.electricTypes = this.electro.data;\r\n      this.$refs.ammeter.validate((valid) => {\r\n        if (valid) {\r\n          this.$refs.ammeter1.validate((valid1) => {\r\n            if (valid1) {\r\n              this.$refs.ammeter2.validate((valid2) => {\r\n                if (valid2) {\r\n                  if (this.ammeter.status == 0) {\r\n                    //停用电表协议不验证 局站、用电类型\r\n                    this.saveData(type);\r\n                  } else {\r\n                    this.checkData(type);\r\n                  }\r\n                } else {\r\n                  this.$Message.error(\"业主信息验证没通过\");\r\n                  this.loading = false;\r\n                }\r\n              });\r\n            } else {\r\n              this.$Message.error(\"关联局站信息验证没通过\");\r\n              this.loading = false;\r\n            }\r\n          });\r\n        } else {\r\n          this.$Message.error(\"基本信息验证没通过\");\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n\r\n    onModalOK(type) {\r\n      let attachData = [];\r\n      console.log(this.ammeter);\r\n      if (this.ammeter.map.iftimeout == \"3\") {\r\n        attchList({ busiId: this.fileParam.busiId }).then((res) => {\r\n          this.loading = false;\r\n          console.log(res);\r\n          attachData = Object.assign([], res.data.rows);\r\n          console.log(attachData, \"attachData\");\r\n          if (attachData.length < 1) {\r\n            this.$Message.error(\"当前选择局站已过期,必须上传附件\");\r\n            this.loading = false;\r\n            return;\r\n          }\r\n          this.OK(type);\r\n        });\r\n      } else {\r\n        this.OK(type);\r\n      }\r\n    },\r\n    //验证数据\r\n    checkData(type) {\r\n      let types = this.ammeter.classifications;\r\n      this.ammeter.electrotype = types[types.length - 1];\r\n      let that = this;\r\n      if (\r\n        this.ammeter.status === 1 &&\r\n        (this.configVersion == \"sc\" || this.configVersion == \"SC\")\r\n      ) {\r\n        //在用状态下验证局站地址不能为空\r\n        if (\r\n          this.ammeter.stationaddress == null ||\r\n          this.ammeter.stationaddress == undefined\r\n        ) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"局站地址不能为空，请在局站管理维护该局站信息！\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      if (this.checkStationElec()) {\r\n        //验证用电类型和局站类型是否匹配\r\n        if (this.checkElectricTypeItem()) {\r\n          if (this.configVersion != \"ln\" && this.configVersion != \"LN\") {\r\n            if (this.ammeter.ischangeammeter == 1 && this.ammeter.billStatus < 2) {\r\n              that.checkedDate(type);\r\n            } else {\r\n              checkAmmeterExist(this.ammeter.id, this.ammeter.ammetername, 0).then(\r\n                (res) => {\r\n                  //验证电表是否存在\r\n                  let code = res.data.code;\r\n                  if (code == 0) {\r\n                    that.checkedDate(type);\r\n                  } else {\r\n                    that.loading = false;\r\n                  }\r\n                }\r\n              );\r\n            }\r\n          } else {\r\n            that.checkedDate(type);\r\n          }\r\n        }\r\n      }\r\n    },\r\n    checkStationElec() {\r\n      let electrotype = this.ammeter.electrotype;\r\n      let stationtype = this.ammeter.stationtype;\r\n      if (electrotype === 111 || electrotype === 112 || electrotype === 113) {\r\n        if (stationtype !== 10001) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      } else if (electrotype === 121 || electrotype === 112) {\r\n        if (stationtype !== 10003 && stationtype !== 10004) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\" + stationtype,\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      } else if (electrotype === 131 || electrotype === 132 || electrotype === 133) {\r\n        if (stationtype !== 10005) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      } else if (\r\n        electrotype === 1411 ||\r\n        electrotype === 1412 ||\r\n        electrotype === 1421 ||\r\n        electrotype === 1422 ||\r\n        electrotype === 1431 ||\r\n        electrotype === 1432\r\n      ) {\r\n        if (stationtype !== 10002) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      if (this.configVersion == \"sc\" && this.ammeter.stationaddresscode) {\r\n        //“51”开头铁塔站址编码控制\r\n        if (\r\n          [1411, 1412].includes(electrotype) &&\r\n          !this.ammeter.stationaddresscode.startsWith(\"51\")\r\n        ) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站站址编码不匹配(51开头为铁塔站址编码)，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    okModel() {\r\n      //不验证个数\r\n      this.isoldcheckStation = null;\r\n      this.saveData(this.checkStationType); //保存数据\r\n    },\r\n    cancelModel() {\r\n      this.isoldcheckStation = null;\r\n      this.$Modal.warning({ title: \"温馨提示\", content: this.errorMessage });\r\n      this.loading = false;\r\n    },\r\n    checkedDate(type) {\r\n      let that = this;\r\n      checkProjectNameExist(that.ammeter.id, that.ammeter.projectname, 0).then((res) => {\r\n        //验证项目名称是否存在\r\n        let code = res.data.code;\r\n        if (code == 0) {\r\n          if (\r\n            that.ammeter.stationcode != undefined &&\r\n            that.ammeter.stationcode != null &&\r\n            (that.ammeter.electrotype == 1411 || that.ammeter.electrotype == 1412)\r\n          ) {\r\n            //判断是否铁塔\r\n            if (that.propertyright == null) {\r\n              //判断是否铁塔\r\n              getstationold(that.ammeter.stationcode).then((res) => {\r\n                //验证项目名称是否存在\r\n                that.propertyright = res.data.propertyright;\r\n                if (that.propertyright != 3) {\r\n                  this.$Modal.warning({\r\n                    title: \"温馨提示\",\r\n                    content: \"用电类型和局站类型或产权不匹配，请确认\",\r\n                  });\r\n                  this.loading = false;\r\n                } else {\r\n                  that.isCheckStation(type);\r\n                }\r\n              });\r\n            } else {\r\n              if (that.propertyright != 3) {\r\n                this.$Modal.warning({\r\n                  title: \"温馨提示\",\r\n                  content: \"用电类型和局站类型或产权不匹配，请确认\",\r\n                });\r\n                this.loading = false;\r\n              } else {\r\n                that.isCheckStation(type);\r\n              }\r\n            }\r\n          } else {\r\n            that.isCheckStation(type);\r\n          }\r\n        } else {\r\n          that.loading = false;\r\n        }\r\n      });\r\n    },\r\n    isCheckStation(type) {\r\n      let that = this;\r\n      checkAmmeterByStation({\r\n        id: that.ammeter.id,\r\n        type: 0,\r\n        electrotype: that.ammeter.electrotype,\r\n        stationcode: that.ammeter.stationcode,\r\n        ammeteruse: that.ammeter.ammeteruse,\r\n      }).then((res) => {\r\n        let code = res.data.code;\r\n        if (code == \"error\") {\r\n          this.errorMessage = res.data.msg;\r\n          if (\r\n            (that.isoldcheckStation == null || that.isoldcheckStation == false) &&\r\n            that.ammeter.stationtype == 10002 &&\r\n            res.data.flag5\r\n          ) {\r\n            //编辑数据时判断是否选择关联局站，没有关联弹出是否室分\r\n            if (that.ismodal1 == null) {\r\n              that.modal1 = true;\r\n            } else {\r\n              that.okModel();\r\n            }\r\n          } else {\r\n            that.$Modal.warning({ title: \"温馨提示\", content: res.data.msg });\r\n            that.loading = false;\r\n          }\r\n        } else {\r\n          that.saveData(type); //保存数据\r\n        }\r\n      });\r\n    },\r\n    saveData(type) {\r\n      let that = this;\r\n      isInTodoList(this.ammeter.id, 1)\r\n        .then((res) => {\r\n          //存在于代办中时，报出提示\r\n          let ownername = \"\";\r\n          if (res.data.length > 0) {\r\n            for (let i = 0; i < res.data.length; i++) {\r\n              ownername += res.data[i].ownername + \" \";\r\n            }\r\n            this.$Modal.warning({\r\n              title: \"温馨提示\",\r\n              content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可修改数据\",\r\n            });\r\n            this.loading = false;\r\n          } else {\r\n            checkAcountByUpdate({ id: that.ammeter.id }).then((res) => {\r\n              //修改数据前验证台账\r\n              if (res.data == -1) {\r\n                that.$Modal.warning({\r\n                  title: \"温馨提示\",\r\n                  content: \"该数据已填写台账或正在报账中，处理后才可修改数据\",\r\n                });\r\n                that.loading = false;\r\n              } else {\r\n                that.clearDataByCondition();\r\n                updateAmmeter(that.ammeter)\r\n                  .then((res) => {\r\n                    if (res.data.code != 0 && res.data.msg) {\r\n                      that.$Notice.error({\r\n                        title: \"提示\",\r\n                        desc: res.data.msg,\r\n                        duration: 10,\r\n                      });\r\n                      that.loading = false;\r\n                      return;\r\n                    }\r\n                    if (type == 1) {\r\n                      that.startFlow(that.ammeter);\r\n                    } else {\r\n                      // this.closeTagByName({// 关闭已经打开的 ，避免冲突\r\n                      //     route: getHomeRoute(routers, \"ammeter\"),\r\n                      // });\r\n                      // //跳转至修改页面 并关闭当前页\r\n                      // this.closeTag({\r\n                      //     route: this.$route, next: {\r\n                      //         name: \"ammeter\", query: {}\r\n                      //     }\r\n                      // });\r\n                      console.log(\"this.$route\", this.$route);\r\n                      this.closeTag({ route: this.$route });\r\n                      that.warn();\r\n                    }\r\n                  })\r\n                  .catch((err) => {\r\n                    that.loading = false;\r\n                    console.log(err);\r\n                  });\r\n              }\r\n            });\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n          this.loading = false;\r\n        });\r\n    },\r\n    changedirectsupply(value) {\r\n      console.log(value);\r\n      if (this.ammeter.directsupplyflag == 1) {\r\n        //this.ammeter.property = null;\r\n        this.ruleValidate.supplybureauammetercode = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n        this.ruleValidate.price = [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      } else {\r\n        //this.ammeter.property = null;\r\n        this.ruleValidate.supplybureauammetercode = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n        this.ruleValidate.price = [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n    },\r\n    //根据条件判断数据是否该清除\r\n    clearDataByCondition() {\r\n      if (this.ammeter.property !== 2 && this.ammeter.property !== 4) {\r\n        //站址产权归属为铁塔 清除分割比例checkAmmeterByStation，是否铁塔按RRU包干\r\n        this.ammeter.percent = null;\r\n      }\r\n      if (this.ammeter.ammeteruse !== 3) {\r\n        //电表用途不是回收电费，清除父电表信息\r\n        this.ammeter.parentId = null;\r\n        this.ammeter.customerId = null;\r\n      }\r\n      if (this.ammeter.directsupplyflag != 1) {\r\n        //只有对外结算类型为直供电才填写该字段，转供电不需填写\r\n        this.ammeter.electrovalencenature = null;\r\n      }\r\n      if (!this.isCDCompany) {\r\n        //成都分公司显示合同对方等，不是，就清除数据\r\n        this.ammeter.contractOthPart = null;\r\n        this.ammeter.nmCcode = null;\r\n        this.ammeter.nmL2100 = null;\r\n        this.ammeter.nmL1800 = null;\r\n        this.ammeter.nmCl800m = null;\r\n      }\r\n    },\r\n    warn() {\r\n      this.$Modal.warning({\r\n        title: \"温馨提示\",\r\n        content: \"保存后的数据要提交审批才能生效！\",\r\n      });\r\n    },\r\n    refreshData() {\r\n      this.initData();\r\n    },\r\n    initData() {\r\n      this.countryName = \"\";\r\n      this.electro.data = [];\r\n      this.removeIds = [];\r\n      this.multiFiles = [];\r\n      this.files = [];\r\n      this.oldData = [];\r\n      this.isCityAdmin = false;\r\n      this.isAdmin = false;\r\n      this.isEditByCountry = false;\r\n      this.$nextTick(() => {\r\n        this.$refs.ammeter.resetFields(); // this.$refs.adduserform.resetFields();\r\n        this.$refs.ammeter1.resetFields(); // this.$refs.adduserform.resetFields();\r\n        this.$refs.ammeter2.resetFields(); // this.$refs.adduserform.resetFields();\r\n      });\r\n      this.showModel = false;\r\n      this.electricTypeModel = false;\r\n    },\r\n    onModalCancel() {\r\n      this.initData();\r\n    },\r\n    /*初始化*/\r\n    initAmmeter(id) {\r\n      console.log(id, \"initAmmeter(id)\");\r\n      this.initData();\r\n      let that = this;\r\n      if (id != undefined) {\r\n        this.title = \"修改电表\";\r\n        this.isEditByCountry = true;\r\n        //获取上一次修改历史\r\n        editAmmeterRecord({ id: id }).then((res) => {\r\n          console.log(\"获取数据---\", res);\r\n          if (res.data.id != undefined && res.data.id != null) {\r\n            if (null != res.data.maxdegree) {\r\n              res.data.maxdegree = parseFloat(res.data.maxdegree);\r\n            }\r\n            this.setAmmeter(Object.assign({}, res.data));\r\n            this.listElectricTypeRatio(id, res.data.id, res.data.stationcode);\r\n            this.ammeter.id = id;\r\n            that.fileParam.busiId = id;\r\n            getClassificationId(this.ammeter.electrotype).then((res) => {\r\n              this.ammeter.classifications = res.data;\r\n            });\r\n            attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n              that.attachData = Object.assign([], res.data.rows);\r\n            });\r\n          } else {\r\n            editAmmeter(id).then((res1) => {\r\n              if (null != res1.data.maxdegree) {\r\n                res1.data.maxdegree = parseFloat(res1.data.maxdegree);\r\n              }\r\n              this.setAmmeter(res1.data);\r\n              that.fileParam.busiId = that.ammeter.id;\r\n              this.listElectricTypeRatio(id, null, res1.data.stationcode);\r\n              getClassificationId(this.ammeter.electrotype).then((res) => {\r\n                this.ammeter.classifications = res.data;\r\n              });\r\n              attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n                that.attachData = Object.assign([], res.data.rows);\r\n              });\r\n            });\r\n          }\r\n          this.$forceUpdate();\r\n        });\r\n        this.getUser();\r\n      } else {\r\n        this.title = \"添加电表\";\r\n        editAmmeter(\"\", 0).then((res) => {\r\n          this.setAmmeter(Object.assign({}, res.data));\r\n          this.getUser();\r\n        });\r\n      }\r\n      getClassification().then((res) => {\r\n        //用电类型\r\n        this.classificationData = res.data;\r\n      });\r\n    },\r\n    listElectricTypeRatio(id, recordId, stationcode) {\r\n      listElectricTypeRatio({ ammeterId: id, ammeterRecordId: recordId }).then((res) => {\r\n        res.data.rows.forEach((item) => {\r\n          if (item.stationId == null || item.stationId == undefined) {\r\n            item.stationId = null;\r\n            item.stationName = null;\r\n          } else if (item.stationId == stationcode) {\r\n            item._disabled = true;\r\n          }\r\n        });\r\n        this.electro.data = Object.assign([], res.data.rows);\r\n      });\r\n    },\r\n    changeStatus() {\r\n      if (this.ammeter.status == 1) {\r\n        this.isRequireFlag = true;\r\n        this.ruleValidate.stationName = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      } else {\r\n        this.isRequireFlag = false;\r\n        this.ruleValidate.stationName = [\r\n          { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n    },\r\n    selectChange() {\r\n      if (this.ammeter.company != undefined) {\r\n        if (this.ammeter.company == \"1000085\") {\r\n          this.isCDCompany = true;\r\n        } else {\r\n          this.isCDCompany = false;\r\n        }\r\n        getCountryByUserId(this.ammeter.company).then((res) => {\r\n          this.departments = res.data.departments;\r\n          this.ammeter.country = res.data.departments[0].id;\r\n          this.ammeter.countryName = this.departments[0].name;\r\n        });\r\n      }\r\n    },\r\n    getUser() {\r\n      let that = this;\r\n      getUserByUserRole().then((res) => {\r\n        //当前登录用户所在公司\r\n        that.companies = res.data.companies;\r\n        that.isCityAdmin = res.data.isEditAdmin;\r\n        if (\r\n          res.data.isCityAdmin == true ||\r\n          res.data.isProAdmin == true ||\r\n          res.data.isSubAdmin == true\r\n        ) {\r\n          that.isAdmin = true;\r\n        }\r\n        getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {\r\n          //根据权限获取所属部门\r\n          that.departments = res.data;\r\n        });\r\n      });\r\n    },\r\n    setOldData(data) {\r\n      this.oldCategory = btext(\"ammeterCategory\", data.category, \"typeCode\", \"typeName\");\r\n      this.oldPackagetype = btext(\r\n        \"packageType\",\r\n        data.packagetype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldPayperiod = btext(\"payPeriod\", data.payperiod, \"typeCode\", \"typeName\");\r\n      this.oldPaytype = btext(\"payType\", data.paytype, \"typeCode\", \"typeName\");\r\n      this.oldElectronature = btext(\r\n        \"electroNature\",\r\n        data.electronature,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldElectrovalencenature = btext(\r\n        \"electrovalenceNature\",\r\n        data.electrovalencenature,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldElectrotype = btext(\r\n        \"electroType\",\r\n        data.electrotype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldStatus = btext(\"status\", data.status, \"typeCode\", \"typeName\");\r\n      this.oldProperty = btext(\"property\", data.property, \"typeCode\", \"typeName\");\r\n      this.oldAmmetertype = btext(\r\n        \"ammeterType\",\r\n        data.ammetertype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldStationstatus = btext(\r\n        \"stationStatus\",\r\n        data.stationstatus,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldStationtype = btext(\r\n        \"BUR_STAND_TYPE\",\r\n        data.stationtype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldDirectsupplyflag = btext(\r\n        \"directSupplyFlag\",\r\n        data.directsupplyflag,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldAmmeteruse = btext(\"ammeterUse\", data.ammeteruse, \"typeCode\", \"typeName\");\r\n      this.oldvoltageClass = btext({\r\n        category: \"voltageClass\",\r\n        v: data.voltageClass,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n    },\r\n\r\n    setAmmeter(form) {\r\n      if (form.status == null || form.status === 1) {\r\n        form.status = 1;\r\n        this.isRequireFlag = true;\r\n        this.ruleValidate.stationName = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      } else {\r\n        this.isRequireFlag = false;\r\n        this.ruleValidate.stationName = [\r\n          { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n      if (form.electrovalencenature != 1 && form.electrovalencenature != 2) {\r\n        form.electrovalencenature = null;\r\n      }\r\n      form.issmartammeter = form.issmartammeter == null ? \"0\" : form.issmartammeter + \"\";\r\n      form.isentityammeter =\r\n        form.isentityammeter == null ? null : form.isentityammeter + \"\";\r\n      form.isairconditioning =\r\n        form.isairconditioning == null ? \"0\" : form.isairconditioning + \"\";\r\n      form.ischangeammeter =\r\n        form.ischangeammeter == null ? null : form.ischangeammeter + \"\";\r\n      form.oldBillPower = form.oldBillPower == null ? \"\" : form.oldBillPower + \"\";\r\n      form.islumpsum = form.islumpsum == null ? \"0\" : form.islumpsum + \"\";\r\n      form.iszgz = form.iszgz == null ? \"0\" : form.iszgz + \"\";\r\n      form.directFlag = form.directFlag == null ? \"0\" : form.directFlag + \"\";\r\n      form.officeFlag = form.officeFlag == null ? \"0\" : form.officeFlag + \"\";\r\n      if (form.iszgz == \"1\") this.disablediszgz = true;\r\n      this.ammeter = form;\r\n      let electrotype = this.ammeter.electrotype;\r\n      this.isMobileBase = electrotype > 1400 ? true : false;\r\n      if (\r\n        electrotype === 111 ||\r\n        electrotype === 112 ||\r\n        electrotype === 113 ||\r\n        electrotype === 2\r\n      ) {\r\n        this.isClassification = true;\r\n      }\r\n      if (\r\n        (electrotype != null && electrotype !== 1411 && electrotype !== 1412) ||\r\n        this.ammeter.property !== 2\r\n      ) {\r\n        this.propertyReadonly = false;\r\n      }\r\n      if (this.ammeter.magnification == null) {\r\n        this.ammeter.magnification = 1;\r\n      }\r\n      if (this.ammeter.company != null) {\r\n        this.ammeter.company = this.ammeter.company + \"\";\r\n        if (this.ammeter.company == \"1000085\") {\r\n          this.isCDCompany = true;\r\n        }\r\n      }\r\n      if (this.ammeter.processinstId != null) {\r\n        this.isShowFlow = true;\r\n      }\r\n      this.flowName = this.ammeter.projectname; //用于提交流程使用原项目名称\r\n      this.showModel = true;\r\n    },\r\n\r\n    //修改电表、协议的用电类型时，如用电类型不再与原先选择的局站的局站类型匹配时，系统自动清空原关联局站，需用户重新再关联局站。\r\n    changeClassifications(value) {\r\n      this.isClassification = false;\r\n      this.clearStation();\r\n      if (value.length == 0) {\r\n        // this.clearStation();\r\n        this.ammeter.property = null;\r\n        this.propertyReadonly = true;\r\n      } else {\r\n        this.propertyReadonly = false;\r\n        this.ammeter.electrotype = value[value.length - 1];\r\n        let electrotype = this.ammeter.electrotype;\r\n        this.isMobileBase = electrotype > 1400 ? true : false;\r\n        if (electrotype === 1411 || electrotype === 1412) {\r\n          //控制产权归属\r\n          this.ammeter.property = 2;\r\n          this.propertyReadonly = true;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else if (electrotype === 1421 || electrotype === 1422) {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = 4;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else if (electrotype === 1431 || electrotype === 1432) {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = 1;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = null;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        }\r\n        // checkClassificationLevel(this.ammeter.electrotype).then(res => {\r\n        //     let code = res.data.msg;\r\n        //     if (code !== '1') {\r\n        let stationtype = this.ammeter.stationtype;\r\n        if (electrotype === 111 || electrotype === 112 || electrotype === 113) {\r\n          this.isClassification = true;\r\n          if (stationtype !== 10001) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 121 || electrotype === 112) {\r\n          if (stationtype !== 10003 && stationtype !== 10004) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 131 || electrotype === 132 || electrotype === 133) {\r\n          if (stationtype !== 10005) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 1411 || electrotype === 1412) {\r\n          if (\r\n            stationtype !== 10002 ||\r\n            (stationtype == 10002 && this.propertyright !== 3)\r\n          ) {\r\n            this.clearStation();\r\n          }\r\n        } else if (\r\n          electrotype === 1421 ||\r\n          electrotype === 1422 ||\r\n          electrotype === 1431 ||\r\n          electrotype === 1432\r\n        ) {\r\n          if (stationtype !== 10002) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 2) {\r\n          this.isClassification = true;\r\n          //     if(stationtype !== 20001){ this.clearStation();}\r\n          // }else if(electrotype === 31 || electrotype === 32 || electrotype === 33){\r\n          //     if(stationtype !== 20002 || stationtype !== -2){ this.clearStation();}\r\n          // }else if(electrotype === 4){\r\n          //     if(stationtype !== -1 || stationtype !== -2){ this.clearStation();}\r\n        }\r\n        //     }\r\n        // });\r\n        if (this.configVersion == \"sc\" && this.ammeter.stationaddresscode) {\r\n          //“51”开头铁塔站址编码控制\r\n          if (\r\n            [1411, 1412].includes(electrotype) &&\r\n            !this.ammeter.stationaddresscode.startsWith(\"51\")\r\n          ) {\r\n            this.clearStation();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    clearStation() {\r\n      //清除局站信息\r\n      this.ammeter.stationName = null;\r\n      this.ammeter.stationcode = null;\r\n      this.ammeter.stationstatus = null;\r\n      this.ammeter.stationtype = null;\r\n      this.ammeter.stationaddress = null;\r\n      this.ammeter.stationaddresscode = null;\r\n    },\r\n    //选择所属部门开始\r\n    chooseResponseCenter(index, params, electroRowNum) {\r\n      this.chooseIndex = index;\r\n      this.electroRowNum = electroRowNum;\r\n      if (index == 1 || index == 2) {\r\n        let types = this.ammeter.classifications;\r\n        if (types.length == 0) {\r\n          this.$Modal.warning({ title: \"温馨提示\", content: \"请先选择用电类型！\" });\r\n          return;\r\n        } else if (this.ammeter.ammeteruse == null) {\r\n          this.$Modal.warning({ title: \"温馨提示\", content: \"请先选择电表用途！\" });\r\n          return;\r\n        } else {\r\n          if (this.ammeter.company == null) {\r\n            this.$Message.info(\"请先选择分公司\");\r\n            return;\r\n          }\r\n          this.ammeter.electrotype = types[types.length - 1];\r\n          // if(this.configVersion=='ln' || this.configVersion =='LN'){\r\n          //     this.$refs.stationModalLN.initDataList(this.ammeter.electrotype,0,this.ammeter.ammeteruse,params);//局站\r\n          // }else{\r\n          this.$refs.stationModal.ammeterid = this.ammeter.id;\r\n          this.$refs.stationModal.initDataList(\r\n            this.ammeter.electrotype,\r\n            0,\r\n            this.ammeter.ammeteruse,\r\n            this.ammeter.company,\r\n            params\r\n          ); //局站\r\n          // }\r\n        }\r\n      } else {\r\n        if (this.ammeter.company == null) {\r\n          this.$Message.info(\"请先选择分公司\");\r\n          return;\r\n        }\r\n        this.$refs.countryModal.choose(this.ammeter.company); //所属部门\r\n      }\r\n    },\r\n    getDataFromModal(data, flag) {\r\n      this.ammeter.country = data.id;\r\n      this.ammeter.countryName = data.name;\r\n      //this.chooseResponseCenter(4, data);\r\n      //选择所属部门结束\r\n    },\r\n    //获取局站数据\r\n    getDataFromStationModal(data, flag, ismodal1) {\r\n      this.ischeckStation = flag;\r\n      this.isoldcheckStation = flag;\r\n      this.ismodal1 = ismodal1;\r\n      if (this.chooseIndex == 2) {\r\n        this.electro.data[this.electroRowNum].stationId = data.id;\r\n        this.electro.data[this.electroRowNum].stationName = data.stationname;\r\n      } else {\r\n        this.propertyright = data.propertyright;\r\n        this.ammeter.stationName = data.stationname;\r\n        this.ammeter.stationcode = data.id;\r\n        this.ammeter.stationstatus = Number(\r\n          data.status == undefined ? data.STATUS : data.status\r\n        );\r\n        this.ammeter.stationtype = Number(data.stationtype);\r\n        this.ammeter.stationaddress = data.address;\r\n        // if (data.stationtype == 10002 && data.propertyright == 3) {//只有当局站类型为‘生产用房-移动基站’且产权为‘租用’时，存放站址编码\r\n        this.ammeter.stationaddresscode = data.resstationcode;\r\n        this.ammeter.resstationcode = data.resstationcode;\r\n        // }\r\n        this.ammeter.stationname5gr = data.stationname5gr;\r\n        this.ammeter.stationcode5gr = data.stationcodeintid;\r\n        //默认生成一条关联用电类型\r\n        let that = this;\r\n        listElectricType({ id: data.stationtype }).then((res) => {\r\n          let result = that.electro.data;\r\n          let electroData = Object.assign([], res.data.rows);\r\n          let count = 0;\r\n          if (result.length == 0) {\r\n            count++;\r\n          } else {\r\n            result.forEach((item) => {\r\n              electroData.forEach((item1) => {\r\n                if (item.id === item1.id) {\r\n                  electroData[0].stationId = data.id;\r\n                  electroData[0].stationName = data.stationname;\r\n                  electroData[0]._disabled = true;\r\n                  let index = result.indexOf(item);\r\n                  result.splice(index, 1);\r\n                } else {\r\n                  count++;\r\n                }\r\n              });\r\n            });\r\n          }\r\n          if (count > 0) {\r\n            that.electro.data = Object.assign([], res.data.rows);\r\n            that.electro.data[0].stationId = data.id;\r\n            that.electro.data[0].stationName = data.stationname;\r\n            that.electro.data[0]._disabled = true;\r\n          } else {\r\n            result.unshift(electroData[0]);\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    /*添加电表关联用电类型比率*/\r\n    addElectricType() {\r\n      this.$refs.selectElectricType.initElectricType();\r\n    },\r\n\r\n    setAttachData(data) {\r\n      this.multiFiles = data.data;\r\n      this.removeIds = data.ids;\r\n      this.multiFiles.busiId = this.ammeter.id;\r\n      if (this.removeIds.length != 0 && data.type == \"remove\") {\r\n        this.removeAttach();\r\n      } else {\r\n        this.upload();\r\n      }\r\n    },\r\n    removeAttach() {\r\n      removeAttach({ ids: this.removeIds.join() }).then(() => {});\r\n    },\r\n    upload() {\r\n      if (this.attachData.length != 0 && this.multiFiles.length != 0) {\r\n        // this.$Message.info(\"提示:上传文件过大可能导致上传失败！\");\r\n        this.loading = true;\r\n        axios\r\n          .request({\r\n            url: \"/common/attachments/uploadMultiFile\",\r\n            method: \"post\",\r\n            data: this.multiFiles,\r\n          })\r\n          .then((res) => {\r\n            if (res.data.code != 0) {\r\n              this.loading = false;\r\n            }\r\n            let that = this;\r\n            attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n              that.attachData = Object.assign([], res.data.rows);\r\n            });\r\n          });\r\n      }\r\n    },\r\n\r\n    /*移除选中的用电类型比率*/\r\n    removeElectricType() {\r\n      let rows = this.$refs.ammeterTable.getSelection();\r\n      let datas = this.electro.data;\r\n      rows.forEach((item) => {\r\n        if (item._index != undefined) {\r\n          datas.splice(item._index, 1);\r\n        } else {\r\n          datas.forEach((data) => {\r\n            if (data.id === item.id) {\r\n              let index = datas.indexOf(data);\r\n              datas.splice(index, 1);\r\n            }\r\n          });\r\n        }\r\n      });\r\n      this.electro.data = datas;\r\n    },\r\n\r\n    /* 设置用电类型列表*/\r\n    setElectricData: function (data) {\r\n      let origin = this.electro.data;\r\n      if (origin.length < 1) {\r\n        this.electro.data = data;\r\n      } else {\r\n        let tem = data;\r\n        for (let j = 0; j < origin.length; j++) {\r\n          for (let i = 0; i < data.length; i++) {\r\n            let typeId =\r\n              origin[j].electroTypeId != undefined\r\n                ? origin[j].electroTypeId\r\n                : origin[j].id;\r\n            if (data[i].id === typeId) {\r\n              tem.splice(tem.indexOf(data[i]), 1);\r\n            }\r\n          }\r\n        }\r\n        this.electro.data = this.electro.data.concat(tem);\r\n      }\r\n    },\r\n\r\n    //用电类型比例校验\r\n    checkElectricTypeItem() {\r\n      let items = this.electro.data;\r\n      //当“用电类型”选择“111 A类机楼（机房），112 B类机楼（机房），113 C类机楼（机房） ”或“2 管理办公用电”时，才需填用电类型分比且必填，用电类型比例之和必须等于100%\r\n      if (\r\n        this.ammeter.electrotype === 111 ||\r\n        this.ammeter.electrotype === 112 ||\r\n        this.ammeter.electrotype === 113 ||\r\n        this.ammeter.electrotype === 2\r\n      ) {\r\n        let sumRatio = items.reduce((total, item) => {\r\n          return total + item.ratio;\r\n        }, 0);\r\n        if (sumRatio !== 100) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型所占比例和必须为100%，当前值为\" + sumRatio + \"%\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    showFlow() {\r\n      this.showWorkFlow = true;\r\n      let that = this;\r\n      this.hisParams = {\r\n        busiId: that.ammeter.id,\r\n        busiType: that.ammeter.busiAlias,\r\n        procInstId: that.ammeter.processinstId,\r\n      };\r\n    },\r\n    startFlow(data) {\r\n      let busiAlias = \"MODIFY_AMM\";\r\n      let busiTitle = \"修改电表(\" + this.flowName + \")审批\";\r\n      if (data.ischangeammeter == 1 && data.billStatus < 2) {\r\n        busiAlias = \"AMM_SWITCH_AMM\";\r\n        busiTitle = \"电表换表(\" + this.flowName + \")审批\";\r\n      }\r\n      this.workFlowParams = {\r\n        busiId: data.id,\r\n        busiAlias: busiAlias,\r\n        busiTitle: busiTitle,\r\n      };\r\n      let that = this;\r\n      setTimeout(function () {\r\n        that.$refs.clwfbtn.onClick();\r\n      }, 200);\r\n    },\r\n    doWorkFlow(data) {\r\n      //流程回调\r\n      // this.closeTagByName({// 关闭已经打开的 ，避免冲突\r\n      //     route: getHomeRoute(routers, \"ammeter\"),\r\n      // });\r\n      // //跳转至修改页面 并关闭当前页\r\n      // this.closeTag({\r\n      //     route: this.$route, next: {\r\n      //         name: \"ammeter\", query: {}\r\n      //     }\r\n      // });\r\n      this.closeTag({ route: this.$route });\r\n      if (data == 0) {\r\n        this.warn();\r\n      }\r\n    },\r\n    /*选择电表/协议*/\r\n    addAmmeterProtocol() {\r\n      this.$refs.selectAmmeterProtocol.initDataList(1, this.ammeter.id);\r\n    },\r\n    /* 选择电表户号/协议编号*/\r\n    setAmmeterProrocolData: function (data) {\r\n      this.ammeter.parentId = data.id;\r\n      if (data.protocolname != null && data.protocolname.length != 0) {\r\n        this.ammeter.parentCode = data.protocolname;\r\n      } else {\r\n        this.ammeter.parentCode = data.ammetername;\r\n      }\r\n    },\r\n    /*选择客户*/\r\n    addCustomer() {\r\n      this.$refs.customerList.choose(2); //打开模态框\r\n    },\r\n    getDataFromCustomerModal: function (data) {\r\n      this.ammeter.customerId = data.id;\r\n      this.ammeter.customerName = data.name;\r\n    },\r\n    //选择包干的时候修改默认包干类型\r\n    updatepackagetype() {\r\n      let data = this.ammeter;\r\n      data.packagetype = null;\r\n    },\r\n    iszgzchange() {\r\n      if (this.ammeter.iszgz == \"1\") {\r\n        this.ammeter.directsupplyflag = 1;\r\n        this.iszgzOnly = true;\r\n      } else {\r\n        this.iszgzOnly = false;\r\n      }\r\n    },\r\n    chooseoldammetername() {\r\n      if (this.disablediszgz) return;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.status = 0;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.ammeteruse = 1;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.type = 3;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.company = this.ammeter.company;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.country = this.ammeter.country;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.directsupplyflag = 2;\r\n      this.$refs.chooseAmmeterModel.modal.show = true;\r\n      this.$Message.info(\"双击选择！！\");\r\n    },\r\n    getAmmeterModelModal(data) {\r\n      this.ammeter.oldammetername = data.name + \",\" + data.id;\r\n      this.iszgzmename = data.name + \",\" + data.id;\r\n    },\r\n    iszgzmechange() {\r\n      if (!this.iszgzmename) this.iszgzmename = this.ammeter.oldammetername;\r\n      if (this.iszgzme) {\r\n        this.ammeter.oldammetername = this.ammeter.ammetername + \",\" + this.ammeter.id;\r\n      } else {\r\n        if (this.iszgzmename == this.ammeter.ammetername) {\r\n          this.ammeter.oldammetername = null;\r\n        } else {\r\n          this.ammeter.oldammetername = this.iszgzmename;\r\n        }\r\n      }\r\n    },\r\n    projectNameChange(val) {\r\n      // var patt=/^([^\\u0000-\\u00ff]+路)([^\\u0000-\\u00ff]*)([0-9]*号)([^\\u0000-\\u00ff]+楼电表)$/;\r\n      if (\r\n        !/^.*([^\\u0000-\\u00ff]+路).*$/.test(val) &&\r\n        !/^.*([^\\u0000-\\u00ff]*)([0-9]*号).*$/.test(val) &&\r\n        !/^.*([^\\u0000-\\u00ff]+楼电表).*$/.test(val)\r\n      ) {\r\n        this.$Message.info(\"温馨提示：集团要求格式为(**路**号**楼电表)\");\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    //直接从前台取\r\n    this.categorys = {\r\n      directsupplyflag: blist(\"directSupplyFlag\"),\r\n    };\r\n    this.propertyList = blist(\"property\");\r\n    this.initAmmeter(this.$route.query.id);\r\n\r\n    this.configVersion = this.$config.version;\r\n    if (this.configVersion != \"ln\" && this.configVersion != \"LN\") {\r\n      this.ruleValidate.ammetername.push({\r\n        required: true,\r\n        message: \"不能为空\",\r\n        trigger: \"blur\",\r\n      });\r\n      this.ruleValidate.customerName = {\r\n        required: true,\r\n        message: \"不能为空\",\r\n        trigger: \"blur\",\r\n      };\r\n      this.ruleValidate.userunit.push({\r\n        required: true,\r\n        message: \"不能为空\",\r\n        trigger: \"blur\",\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.margin-right-width {\r\n  margin-right: 10px;\r\n}\r\n.testaa .ivu-row {\r\n  margin-left: 5px;\r\n  margin-right: 5px;\r\n}\r\n.testaa .requireStar .ivu-form-item-label:before {\r\n  content: \"*\";\r\n  display: inline-block;\r\n  margin-right: 4px;\r\n  line-height: 1;\r\n  font-family: SimSun;\r\n  font-size: 12px;\r\n  color: #ed4014;\r\n}\r\n</style>\r\n"]}]}