{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addAdvanceAccount.vue?vue&type=style&index=0&id=c6a9776e&lang=less&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addAdvanceAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5wYWdlLWNsYXNzIHsNCiAgaGVpZ2h0OiAxMDAlOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICAuY2wtdGFibGUgew0KICAgIGZsZXg6IDE7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgfQ0KICAuYnV0dG9uLWJhciB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOw0KICB9DQp9DQoueWpqaCA+IC5pdnUtbW9kYWwtd3JhcCA+IC5pdnUtbW9kYWwgew0KICB0b3A6IDIwcHggIWltcG9ydGFudDsNCn0NCi5teXRhYmxlIC5pdnUtdGFibGUtY2VsbCB7DQogIHBhZGRpbmctbGVmdDogNXB4Ow0KICBwYWRkaW5nLXJpZ2h0OiA1cHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICB3aGl0ZS1zcGFjZTogbm9ybWFsOw0KICB3b3JkLWJyZWFrOiBicmVhay1hbGw7DQogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7DQp9DQoNCi5hY2NvdW50RXMgLmZpbHRlci1kaXZpZGVyIHsNCiAgbWFyZ2luOiAwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCi5hY2NvdW50RXMgLmhlYWRlci1iYXItc2hvdyB7DQogIG1heC1oZWlnaHQ6IDMwMHB4Ow0KICBwYWRkaW5nLXRvcDogMTRweDsNCiAgb3ZlcmZsb3c6IGluaGVyaXQ7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZThlYWVjOw0KfQ0KLmFjY291bnRFcyAuaGVhZGVyLWJhci1oaWRlIHsNCiAgbWF4LWhlaWdodDogMDsNCiAgcGFkZGluZy10b3A6IDA7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGJvcmRlci1ib3R0b206IDA7DQp9DQoNCi5teXRhYmxlIC5teXNwYW4gew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAyMHB4Ow0KICBkaXNwbGF5OiBibG9jazsNCn0NCi5teXRhYmxlIC5lcnJvclN0bGUgew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAyMHB4Ow0KICBkaXNwbGF5OiBibG9jazsNCiAgY29sb3I6IHJlZDsNCn0NCg=="}, {"version": 3, "sources": ["addAdvanceAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy1DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addAdvanceAccount.vue", "sourceRoot": "src/view/account/homePageAccount", "sourcesContent": ["<!--自有预付电费台账-->\r\n\r\n<template>\r\n  <div class=\"page-class page-card\">\r\n    <div class=\"query-box\">\r\n      <Row :class=\"filterColl ? 'header-bar-show' : 'header-bar-hide'\">\r\n        <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.accountno\"\r\n                  :style=\"formItemWidth\"\r\n                  @on-change=\"accountnoChange\"\r\n                >\r\n                  <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"项目名称:\" prop=\"projectName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.projectName\"\r\n                  placeholder=\"请输入项目名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"电表/协议编码:\"\r\n                prop=\"ammeterName\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.ammeterName\"\r\n                  placeholder=\"请输入电表户号/协议编码\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"供电局电表编号:\"\r\n                prop=\"supplybureauammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.supplybureauammetercode\"\r\n                  placeholder=\"请输入供电局电表编号\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"局站名称:\" prop=\"stationName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.stationName\"\r\n                  placeholder=\"请输入局站名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.company\"\r\n                  @on-change=\"selectChange(accountObj.company)\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                  <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"所属部门：\"\r\n                prop=\"countryName\"\r\n                v-if=\"isAdmin == true\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Input\r\n                  :clearable=\"true\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"accountObj.countryName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n              <FormItem\r\n                label=\"所属部门：\"\r\n                prop=\"country\"\r\n                v-if=\"isAdmin == false\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option value=\"-1\">全部</Option>\r\n                  <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\"></Col>\r\n          </Row>\r\n          <div align=\"right\">\r\n            <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n            <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n            <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n              <Button type=\"default\" style=\"margin-left: 5px\"\r\n                >导出\r\n                <Icon type=\"ios-arrow-down\"></Icon>\r\n              </Button>\r\n              <DropdownMenu slot=\"list\">\r\n                <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n          </div>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon\r\n          :type=\"filterColl ? 'md-arrow-dropup' : 'md-arrow-dropdown'\"\r\n          size=\"20\"\r\n          @click=\"filterColl = !filterColl\"\r\n          :color=\"filterColl ? '#000' : '#1ab394'\"\r\n        ></icon>\r\n      </div>\r\n    </div>\r\n    <div class=\"cl-table\">\r\n      <Row class=\"button-bar\">\r\n        <div class=\"table-button\">\r\n          <Button type=\"primary\" @click=\"addElectricType\">新增</Button>\r\n          <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n          <Button type=\"error\" @click=\"remove\">删除</Button>\r\n          <Button type=\"primary\" @click=\"openCompletedPreModal\">复制归集单台账</Button>\r\n          <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n            <Button type=\"info\" style=\"margin-left: 5px\"\r\n              >加入归集单\r\n              <Icon type=\"ios-arrow-down\"></Icon>\r\n            </Button>\r\n            <DropdownMenu slot=\"list\">\r\n              <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n              <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n          <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n        </div>\r\n      </Row>\r\n      <Table\r\n        ref=\"accountEsTable\"\r\n        border\r\n        :columns=\"tbAccount.columns\"\r\n        :data=\"tbAccount.data\"\r\n        class=\"mytable\"\r\n        :loading=\"tbAccount.loading\"\r\n        :height=\"tableHeight\"\r\n      >\r\n        <!--项目名称-->\r\n        <template slot-scope=\"{ row }\" slot=\"projectName\">\r\n          <div v-if=\"row.status === 5\">\r\n            <span>{{ row.projectName }}</span\r\n            ><span style=\"color: red\">[退回]</span>\r\n          </div>\r\n          <span v-else>{{ row.projectName }}</span>\r\n        </template>\r\n        <!--起始时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"startdate\" v-if=\"row.total == null\">\r\n          <Input\r\n            :ref=\"'startdate' + index + 1\"\r\n            type=\"text\"\r\n            @on-blur=\"validate\"\r\n            v-model=\"editStartDate\"\r\n            v-if=\"editIndex === index && columnsIndex === 1\"\r\n          />\r\n          <span\r\n            :class=\"myStyle[index].startdate\"\r\n            @click=\"selectCall(row, index, 1, 'startdate')\"\r\n            v-else\r\n            >{{ row.startdate }}</span\r\n          >\r\n        </template>\r\n        <!--截止时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"enddate\" v-if=\"row.total == null\">\r\n          <Input\r\n            :ref=\"'enddate' + index + 2\"\r\n            type=\"text\"\r\n            v-model=\"editEndDate\"\r\n            @on-blur=\"validate\"\r\n            v-if=\"editIndex === index && columnsIndex === 2\"\r\n          />\r\n          <span\r\n            :class=\"myStyle[index].enddate\"\r\n            @click=\"selectCall(row, index, 2, 'enddate')\"\r\n            v-else\r\n            >{{ row.enddate }}</span\r\n          >\r\n        </template>\r\n        <!--止度-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"curtotalreadings\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'curtotalreadings' + index + 3\"\r\n              type=\"text\"\r\n              v-model=\"editcurtotalreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 3\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].curtotalreadings\"\r\n              @click=\"selectCall(row, index, 3, 'curtotalreadings')\"\r\n              v-else\r\n              >{{ row.curtotalreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.curtotalreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--预估电费-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"accountmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'accountmoney' + index + 4\"\r\n              type=\"text\"\r\n              v-model=\"editaccountmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 4\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].accountmoney\"\r\n              @click=\"selectCall(row, index, 4, 'accountmoney')\"\r\n              v-else\r\n              >{{ row.accountmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.accountmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--备注-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              v-model=\"editremark\"\r\n              :ref=\"'remark' + index + 5\"\r\n              type=\"text\"\r\n              @on-blur=\"setremark\"\r\n              v-if=\"editIndex === index && columnsIndex === 5\"\r\n            />\r\n            <Tooltip placement=\"bottom\" max-width=\"200\" v-else>\r\n              <span\r\n                :class=\"myStyle[index].remark\"\r\n                style=\"width: 60px\"\r\n                @click=\"selectCall(row, index, 5, 'remark')\"\r\n                >{{ ellipsis(row.remark) }}</span\r\n              >\r\n              <div slot=\"content\">\r\n                {{ row.remark }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n          <div v-else>\r\n            <Tooltip placement=\"bottom\" max-width=\"200\">\r\n              <span>{{ ellipsis(row.remark) }}</span>\r\n              <div slot=\"content\">\r\n                {{ row.remark }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n        </template>\r\n      </Table>\r\n      <div class=\"table-page\">\r\n        <Page\r\n          size=\"small\"\r\n          :total=\"pageTotal\"\r\n          :current=\"pageNum\"\r\n          :page-size=\"pageSize\"\r\n          show-elevator\r\n          show-sizer\r\n          show-total\r\n          placement=\"top\"\r\n          @on-change=\"handlePage\"\r\n          @on-page-size-change=\"handlePageSize\"\r\n        ></Page>\r\n      </div>\r\n      <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n    </div>\r\n    <div>\r\n      <select-ammeter\r\n        ref=\"selectAmmeter\"\r\n        v-on:listenToSelectAmmeter=\"setAmmeterData\"\r\n      ></select-ammeter>\r\n      <add-bill-per\r\n        ref=\"addBillPer\"\r\n        v-on:refreshList=\"refresh\"\r\n        @buttonload2=\"buttonload2\"\r\n        @isButtonload=\"isButtonload\"\r\n      ></add-bill-per>\r\n      <completed-pre-modal\r\n        ref=\"completedPre\"\r\n        v-on:refreshList=\"refresh\"\r\n      ></completed-pre-modal>\r\n      <country-modal\r\n        ref=\"countryModal\"\r\n        v-on:getDataFromModal=\"getDataFromModal\"\r\n      ></country-modal>\r\n    </div>\r\n    <!--    稽核modal-->\r\n    <Modal\r\n      v-model=\"showCheckModel\"\r\n      width=\"80%\"\r\n      title=\"稽核结果及反馈\"\r\n      :mask-closable=\"false\"\r\n    >\r\n      <check-result-and-response ref=\"chekResultAndResponse\"></check-result-and-response>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"showJhModel\"\r\n      width=\"80%\"\r\n      title=\"稽核结果\"\r\n      @on-cancel=\"alarmClose\"\r\n      :mask-closable=\"false\"\r\n    >\r\n      <check-result ref=\"checkResult\"></check-result>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button type=\"primary\" @click=\"nextCheck\">已查阅</Button>\r\n        <Button type=\"text\" @click=\"checkCancel\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"showAlarmModel\"\r\n      width=\"80%\"\r\n      title=\"台账预警稽核\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n      class=\"yjjh\"\r\n    >\r\n      <alarm-check\r\n        ref=\"showAlarmModel\"\r\n        @save=\"save\"\r\n        @submitChange=\"submitChange\"\r\n        @close=\"alarmClose\"\r\n      ></alarm-check>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button size=\"large\" @click=\"alarmClose\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { mapState } from \"vuex\";\r\nimport { getAuditResult, getAuditResultNew, getAuditResultNew_QXM } from \"@/api/account\";\r\nimport {\r\n  temporaryStorage2,\r\n  addPredPowerAccount,\r\n  addAccountEs,\r\n  removeAccountEs,\r\n  getUser,\r\n  getDepartments,\r\n  accountEsTotal,\r\n  selectIdsByEsParams,\r\n} from \"@/api/account\";\r\nimport { getResCenter, getcompany } from \"@/api/alertcontrol/alertcontrol\";\r\nimport { againJoin } from \"@/api/accountBillPer\";\r\nimport {\r\n  getClassification,\r\n  getUserdata,\r\n  getUserByUserRole,\r\n  getCountrysdata,\r\n  getCountryByUserId,\r\n} from \"@/api/basedata/ammeter.js\";\r\nimport checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\nimport checkResult from \"@/view/account/check/checkResult\";\r\nimport alarmCheck from \"@/view/account/check/alarmCheck\";\r\nimport {\r\n  getDates2,\r\n  getDates,\r\n  testNumber,\r\n  getFirstDateByAccountno_yyyymmdd,\r\n  getLastDateByAccountno_yyyymmdd,\r\n  cutDate_yyyymmdd,\r\n  stringToDate,\r\n  getCurrentDate,\r\n} from \"@/view/account/powerAccountHelper\";\r\nimport {\r\n  _verify_StartDate,\r\n  _verify_EndDate,\r\n  verification,\r\n  unitpirceMin,\r\n  unitpirceMax,\r\n  unitpirceMax1,\r\n} from \"@/view/account/PowerAccountEs\";\r\nimport { judge_negate, judge_recovery } from \"@/view/account/PowerAccountController\";\r\nimport SelectAmmeter from \"@/view/account/selectAmmeter\";\r\nimport AddBillPer from \"@/view/account/addBillPreModal\";\r\nimport CompletedPreModal from \"@/view/account/completedPreModal\";\r\nimport { widthstyle } from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nimport CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\nimport excel from \"@/libs/excel\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport axios from \"@/libs/api.request\";\r\nimport indexData from \"@/config/index\";\r\nimport permissionMixin from \"@/mixins/permission\";\r\nimport pageFun from \"@/mixins/pageFun\";\r\n\r\nlet dates = getDates();\r\nexport default {\r\n  mixins: [permissionMixin, pageFun],\r\n  name: \"addPredPowerAccount\",\r\n  components: {\r\n    alarmCheck,\r\n    checkResult,\r\n    checkResultAndResponse,\r\n    CompletedPreModal,\r\n    SelectAmmeter,\r\n    AddBillPer,\r\n    CountryModal,\r\n  },\r\n  data() {\r\n    let renderStatus = (h, { row, index }) => {\r\n      var status = \"\";\r\n      let data = this.tbAccount.data[index];\r\n      for (let item of this.accountStatus) {\r\n        if (item.typeCode == row.status) {\r\n          data.statusName = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", data.statusName);\r\n    };\r\n\r\n    let renderCategory = (h, params) => {\r\n      var categoryname = \"\";\r\n      for (let item of this.categorys) {\r\n        if (item.typeCode == params.row.category) {\r\n          categoryname = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", categoryname);\r\n    };\r\n\r\n    return {\r\n      isT: true,\r\n      number2: 0,\r\n      name: \"\",\r\n      dataL: [],\r\n      isQuery: true,\r\n      number: 0,\r\n      ctgKeyList: [],\r\n      submit: [],\r\n      submit2: [],\r\n      ammeterids: [],\r\n      showCheckModel: false,\r\n      showAlarmModel: false,\r\n      showJhModel: false,\r\n      formItemWidth: widthstyle,\r\n      version: \"\",\r\n      dateList: dates,\r\n      filterColl: true, //搜索面板展开\r\n      editIndex: -1, //当前编辑行\r\n      columnsIndex: -1, //当前编辑列\r\n      editStartDate: \"\",\r\n      myStyle: [], //样式\r\n      editEndDate: \"\",\r\n      editcurusedreadings: \"\",\r\n      editcurtotalreadings: \"\",\r\n      edittransformerullage: \"\",\r\n      spinShow: false, //遮罩\r\n      categorys: [], //描述类型\r\n      editaccountmoney: \"\",\r\n      editremark: \"\",\r\n      accountStatus: [],\r\n      companies: [],\r\n      departments: [],\r\n      isAdmin: false,\r\n      company: null, //用户默认公司\r\n      country: null, //用户默认所属部门\r\n      countryName: null, //用户默认所属部门\r\n      export: {\r\n        run: false, //是否正在执行导出\r\n        data: \"\", //导出数据\r\n        totalPage: 0, //一共多少页\r\n        currentPage: 0, //当前多少页\r\n        percent: 0,\r\n        size: ********,\r\n      },\r\n      accountObj: {\r\n        supplybureauammetercode: \"\",\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        company: \"\", //分公司\r\n        projectName: \"\", //项目名称\r\n        country: \"\", //所属部门\r\n        ammeterName: \"\", //电表户号/协议编码\r\n        stationName: \"\",\r\n        accountType: \"1\", //台账类型\r\n        accountestype: 3, //台账类型\r\n        countryName: \"\",\r\n      },\r\n      tbAccount: {\r\n        loading: false,\r\n        columns: [\r\n          { type: \"selection\", width: 60, align: \"center\" },\r\n          {\r\n            title: \"稽核结果及反馈\",\r\n            key: \"action\",\r\n            align: \"center\",\r\n            render: (h, params) => {\r\n              var that = this;\r\n              return h(\r\n                \"Button\",\r\n                {\r\n                  props: {\r\n                    type: \"primary\",\r\n                    size: \"small\",\r\n                  },\r\n                  style: {\r\n                    \"font-size\": \"10px\",\r\n                  },\r\n                  on: {\r\n                    click() {\r\n                      that.$refs.chekResultAndResponse.pcid = params.row.pcid;\r\n                      that.showCheckModel = true;\r\n                    },\r\n                  },\r\n                },\r\n                \"稽核结果及反馈\"\r\n              );\r\n            },\r\n            minWidth: 120,\r\n            maxWidth: 150,\r\n          },\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n          { title: \"电表户号/协议编码\", key: \"ammeterName\", align: \"center\" },\r\n          {\r\n            title: \"供电局编码\",\r\n            key: \"supplybureauammetercode\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"所属分公司\", key: \"companyName\", align: \"center\" },\r\n          { title: \"所属部门\", key: \"countryName\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 160 },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            key: \"startdate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            key: \"enddate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          { title: \"本期起度\", key: \"prevtotalreadings\", align: \"center\" },\r\n          {\r\n            title: \"本期止度\",\r\n            slot: \"curtotalreadings\",\r\n            key: \"curtotalreadings\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"用电量(度)\", key: \"curusedreadings\", align: \"center\" },\r\n          { title: \"电价(元)\", key: \"unitpirce\", align: \"center\" },\r\n          { title: \"电费\", slot: \"accountmoney\", key: \"accountmoney\", align: \"center\" },\r\n          { title: \"备注\", slot: \"remark\", key: \"remark\", align: \"center\" },\r\n          { title: \"用电类型\", key: \"electrotypename\", align: \"center\", width: 94 },\r\n          {\r\n            title: \"类型描述\",\r\n            key: \"categoryname\",\r\n            align: \"center\",\r\n            render: renderCategory,\r\n          },\r\n        ],\r\n        data: [],\r\n        total: 0,\r\n        exportColumns: [\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n          { title: \"电表户号/协议编码\", key: \"ammeterName\", align: \"center\" },\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n          { title: \"所属分公司\", key: \"companyName\", align: \"center\" },\r\n          { title: \"所属部门\", key: \"countryName\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            key: \"startdate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            key: \"enddate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          { title: \"本期起度\", key: \"prevtotalreadings\", align: \"center\" },\r\n          {\r\n            title: \"本期止度\",\r\n            slot: \"curtotalreadings\",\r\n            key: \"curtotalreadings\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"用电量(度)\", key: \"curusedreadings\", align: \"center\" },\r\n          { title: \"电价(元)\", key: \"unitpirce\", align: \"center\" },\r\n          { title: \"电费\", slot: \"accountmoney\", key: \"accountmoney\", align: \"center\" },\r\n          { title: \"备注\", slot: \"remark\", key: \"remark\", align: \"center\" },\r\n          { title: \"用电类型\", key: \"electrotypename\", align: \"center\", width: 94 },\r\n          {\r\n            title: \"类型描述\",\r\n            key: \"categoryname\",\r\n            align: \"center\",\r\n            render: renderCategory,\r\n          },\r\n        ],\r\n      },\r\n      pageTotal: 0,\r\n      pageNum: 1,\r\n      pageSize: 10, //当前页\r\n    };\r\n  },\r\n  mounted() {\r\n    this.handleHeight(); //table高度自定义\r\n\r\n    this.version = indexData.version;\r\n\r\n    this.accountStatus = blist(\"accountStatus\");\r\n    this.categorys = blist(\"ammeterCategory\");\r\n    let that = this;\r\n    getUserByUserRole().then((res) => {\r\n      //根据权限获取分公司\r\n      that.companies = res.data.companies;\r\n      if (\r\n        res.data.isCityAdmin == true ||\r\n        res.data.isProAdmin == true ||\r\n        res.data.isSubAdmin == true\r\n      ) {\r\n        that.isAdmin = true;\r\n      }\r\n      getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {\r\n        //根据权限获取所属部门\r\n        that.departments = res.data;\r\n        that.getUserData();\r\n      });\r\n    });\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      loginId: (state) => state.user.loginId,\r\n    }),\r\n  },\r\n  methods: {\r\n    buttonload2(v) {\r\n      this.$refs.showAlarmModel.buttonload2 = v;\r\n    },\r\n    isButtonload(v) {\r\n      this.$refs.showAlarmModel.buttonload = v;\r\n    },\r\n    isShows(t) {\r\n      this.$refs.showAlarmModel.show = t;\r\n      if (this.$refs.showAlarmModel.show == false) {\r\n        this.number2++;\r\n        this.isT = true;\r\n      } else if (this.$refs.showAlarmModel.show == true) {\r\n        this.number2 = 0;\r\n        this.isT = false;\r\n      }\r\n      if (this.isT && this.number2 < 10) {\r\n        this.isShows(t);\r\n      }\r\n    },\r\n    nextCheck() {\r\n      this.showAlarmModel = true;\r\n      this.isShows(true);\r\n      this.$refs.showAlarmModel.tableData = [];\r\n      this.$refs.showAlarmModel.resultList = [];\r\n      this.$refs.showAlarmModel.tableData1 = [];\r\n      this.$refs.showAlarmModel.resultList1 = [];\r\n      this.$refs.showAlarmModel.tableData2 = [];\r\n      this.$refs.showAlarmModel.resultList2 = [];\r\n      this.$refs.showAlarmModel.tableData3 = [];\r\n      this.$refs.showAlarmModel.resultList3 = [];\r\n      this.$refs.showAlarmModel.tableData4 = [];\r\n      this.$refs.showAlarmModel.resultList4 = [];\r\n      this.$refs.showAlarmModel.tableData5 = [];\r\n      this.$refs.showAlarmModel.resultList5 = [];\r\n      this.showJhModel = false;\r\n      this.$refs.showAlarmModel.activeButton = 6;\r\n    },\r\n    alarmClose() {\r\n      this.showAlarmModel = false;\r\n      this.$refs.showAlarmModel.show = false;\r\n    },\r\n    checkCancel() {\r\n      this.showJhModel = false;\r\n    },\r\n    alarmCheck() {},\r\n    selectChange() {\r\n      let that = this;\r\n      if (that.accountObj.company != undefined) {\r\n        if (that.accountObj.company == \"-1\") {\r\n          that.accountObj.country = -1;\r\n          that.accountObj.countryName = null;\r\n        } else {\r\n          getCountryByUserId(that.accountObj.company).then((res) => {\r\n            if (res.data.departments.length != 0) {\r\n              that.accountObj.country = res.data.departments[0].id;\r\n              that.accountObj.countryName = res.data.departments[0].name;\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //选择所属部门开始\r\n    chooseResponseCenter() {\r\n      if (this.accountObj.company == null || this.accountObj.company == \"-1\") {\r\n        this.$Message.info(\"请先选择分公司\");\r\n        return;\r\n      }\r\n      this.$refs.countryModal.choose(this.accountObj.company); //所属部门\r\n    },\r\n    getDataFromModal(data) {\r\n      this.accountObj.country = data.id;\r\n      this.accountObj.countryName = data.name;\r\n      //选择所属部门结束\r\n    },\r\n    getUserData() {\r\n      let that = this;\r\n      getUserdata().then((res) => {\r\n        //当前登录用户所在公司和所属部门\r\n        if (res.data.companies.length != 0) {\r\n          let companies = res.data.companies;\r\n          if (res.data.companies[0].id == \"**********\") {\r\n            companies = that.companies;\r\n          }\r\n          that.company = companies[0].id;\r\n          that.accountObj.company = companies[0].id;\r\n        }\r\n        if (res.data.departments.length != 0) {\r\n          let departments = res.data.departments;\r\n          if (res.data.companies[0].id == \"**********\" && that.departments.length != 0) {\r\n            departments = that.departments;\r\n          }\r\n          that.country = departments[0].id;\r\n          that.countryName = departments[0].name;\r\n          that.accountObj.country = Number(departments[0].id);\r\n          that.accountObj.countryName = departments[0].name;\r\n        }\r\n\r\n        that.pageNum = 1;\r\n        that.getAccountMessages();\r\n      });\r\n    },\r\n    searchList() {\r\n      if (this.accountObj.countryName == \"\") {\r\n        this.accountObj.country = \"-1\";\r\n      }\r\n      this.pageNum = 1;\r\n      this.getAccountMessages();\r\n    },\r\n    accountnoChange() {\r\n      this.searchList();\r\n    },\r\n    setAmmeterData: function (data) {\r\n      let arrayData = [];\r\n      let ctgKeyList = [];\r\n      let no = this.accountObj.accountno;\r\n\r\n      if (data != null && data.length > 0) {\r\n        data.forEach(function (item) {\r\n          let obj = {};\r\n          obj.pcid = item.pcid;\r\n          obj.ammeterName = item.ammetername;\r\n          obj.projectName = item.projectname;\r\n          obj.substation = item.substation;\r\n          obj.categoryname = item.categoryname;\r\n          obj.category = item.category;\r\n          obj.ammeterid = item.ammeterid;\r\n          obj.company = item.company;\r\n          obj.companyName = item.companyName;\r\n          obj.country = item.country;\r\n          obj.ammeteruse = item.ammeteruse;\r\n          obj.countryName = item.countryName;\r\n          obj.startdate = null;\r\n          obj.enddate = null;\r\n          obj.prevtotalreadings = 0;\r\n          obj.curtotalreadings = 0;\r\n          obj.curusedreadings = 0;\r\n          obj.transformerullage = 0;\r\n          obj.unitpirce = 0;\r\n          obj.accountmoney = 0;\r\n          obj.remark = null;\r\n          obj.electrotype = item.electrotype;\r\n          obj.stationcode5gr = item.stationcode5gr;\r\n          obj.stationname5gr = item.stationname5gr;\r\n          obj.electrotypename = item.electrotypename;\r\n          obj.stationName = item.stationName;\r\n          obj.startdate = getFirstDateByAccountno_yyyymmdd(no);\r\n          obj.enddate = getLastDateByAccountno_yyyymmdd(no);\r\n          obj.accountestype = 3;\r\n          obj.supplybureauammetercode = item.supplybureauammetercode;\r\n          arrayData.push(obj);\r\n          ctgKeyList.push({ ctgKey: item.ctgKey, ammetername: item.ammetername });\r\n        });\r\n        this.ctgKeyList = ctgKeyList;\r\n      }\r\n      if (arrayData.length > 0) {\r\n        arrayData.forEach(function (item) {\r\n          let req = {\r\n            url: \"/business/accountEs/selectPrevtotalreadings\",\r\n            method: \"post\",\r\n            params: { ammeterid: item.ammeterid },\r\n          };\r\n          axios.request(req).then((res) => {\r\n            if (res.data) {\r\n              item.prevtotalreadings = res.data;\r\n            }\r\n          });\r\n        });\r\n      }\r\n\r\n      let origin = this.tbAccount.data;\r\n      if (origin.length < 1) {\r\n        this.tbAccount.data = arrayData;\r\n      } else {\r\n        let tem = arrayData;\r\n        let total = this.pageTotal;\r\n        this.pageTotal = total + tem.length;\r\n        this.tbAccount.data = tem.concat(this.tbAccount.data);\r\n      }\r\n\r\n      this.setMyStyle(this.tbAccount.data.length);\r\n    },\r\n    //暂存\r\n    temporaryStorage() {\r\n      let array = [];\r\n      this.tbAccount.data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          array.push(item);\r\n        }\r\n      });\r\n      // ----------------------\r\n      let data = array;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let str = \"\";\r\n        let accountno = this.accountObj.accountno;\r\n        let index = 0;\r\n        data.forEach(function (item) {\r\n          if (item.projectName != \"小计\" && item.projectName != \"合计\") {\r\n            if (item.pcid == null) {\r\n              item.accountno = accountno;\r\n            }\r\n            Object.assign(item, { totalusedreadings: item.curusedreadings }); // 确保预付总电量 等于用电量\r\n            submitData.push(item);\r\n            number++;\r\n          }\r\n        });\r\n        if (submitData.length > 0) {\r\n          temporaryStorage2(submitData).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：成功暂存 \" + submitData.length + \" 条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //点击保存\r\n    preserve() {\r\n      let dataL = this.$refs.accountEsTable.getSelection();\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      let version = indexData.version;\r\n      for (let i = 0; i < dataL.length; i++) {\r\n        if (dataL[i].editType == 1) {\r\n          if (\r\n            \"sc\" == version &&\r\n            dataL[i].electrotype &&\r\n            dataL[i].electrotype > 1400 &&\r\n            (dataL[i].stationcode5gr == null ||\r\n              dataL[i].stationcode5gr == undefined ||\r\n              dataL[i].stationcode5gr == \"\")\r\n          ) {\r\n            this.errorTips(\r\n              \"电表/协议编号【\" +\r\n                dataL[i].ammeterName +\r\n                \"】，项目名称【\" +\r\n                dataL[i].projectName +\r\n                \"】关联局站的5GR站址为空，请完善局站信息，或者5GR有效性清单失效，请联系无线管理员。\"\r\n            );\r\n          }\r\n          b = true;\r\n          array.push(dataL[i]);\r\n        }\r\n      }\r\n      if (b) {\r\n        this.submitData(array);\r\n      } else {\r\n        this.errorTips(\"没有可保存数据\");\r\n      }\r\n    },\r\n    //四川能耗稽核流程\r\n    preserveSc() {\r\n      this.$refs.checkResult.ammeterids = this.ammeterids;\r\n      this.showJhModel = true;\r\n    },\r\n    submitChange(indexList) {\r\n      let data = [];\r\n      this.submit2.map((item, index) => {\r\n        indexList.map((item2) => {\r\n          if (index == item2) {\r\n            data.push(item);\r\n          }\r\n        });\r\n      });\r\n      this.submit = data;\r\n    },\r\n    submitChange1(data) {\r\n      this.submit = data;\r\n    },\r\n    getAuditResultNew(data) {\r\n      let arr = [];\r\n      data.forEach((item) => {\r\n        arr.push(item.pcid);\r\n      });\r\n      let param = {\r\n        pcids: arr,\r\n      };\r\n      getAuditResultNew_QXM(param).then((res2) => {\r\n        this.auditResultList = res2.data;\r\n        if (this.auditResultList && this.auditResultList.length == 0) {\r\n          this.number++;\r\n          this.isQuery = true;\r\n        } else if (data.length != this.auditResultList.length) {\r\n          this.number++;\r\n          this.isQuery = true;\r\n        } else if (data.length == this.auditResultList.length) {\r\n          this.isQuery = false;\r\n          this.number = 0;\r\n        } else {\r\n          this.isQuery = false;\r\n          this.number = 0;\r\n        }\r\n        if (this.isQuery && this.number < 3) {\r\n          if (data.length > 300) {\r\n            setTimeout(() => this.getAuditResultNew(data), 5000);\r\n          } else if (data.length > 100 && data.length < 300) {\r\n            setTimeout(() => this.getAuditResultNew(data), 3000);\r\n          } else {\r\n            setTimeout(() => this.getAuditResultNew(data), 2000);\r\n          }\r\n        } else {\r\n          this.auditResultList.forEach((item) => {\r\n            this.$refs.showAlarmModel.resultList.push(item.msg);\r\n            this.$refs.showAlarmModel.tableData.push(item.powerAuditEntity);\r\n            if (item.staute == \"失败\") {\r\n              // if(item.powerAuditEntity.mutiJtlteCodes=='是'\r\n              // || item.powerAuditEntity.electricityPrices=='否'\r\n              // || item.powerAuditEntity.addressConsistence=='否'\r\n              // || item.powerAuditEntity.reimbursementCycle=='否' || item.powerAuditEntity.electricityContinuity=='否' ||\r\n              // item.powerAuditEntity.shareAccuracy=='否' ||\r\n              // item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n              // item.powerAuditEntity.paymentConsistence=='否'){\r\n              if (item.powerAuditEntity.mutiJtlteCodes == \"是\") {\r\n                this.$refs.showAlarmModel.tableData4.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList4.push(item.msg);\r\n              }\r\n              if (item.powerAuditEntity.electricityPrices == \"否\") {\r\n                this.$refs.showAlarmModel.tableData5.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList5.push(item.msg);\r\n              }\r\n              if (\r\n                item.powerAuditEntity.addressConsistence == \"否\" ||\r\n                item.powerAuditEntity.reimbursementCycle == \"否\" ||\r\n                item.powerAuditEntity.electricityContinuity == \"否\" ||\r\n                item.powerAuditEntity.shareAccuracy == \"否\" ||\r\n                //   item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n                item.powerAuditEntity.paymentConsistence == \"否\" ||\r\n                item.powerAuditEntity.fluctuateContinuity == \"否\"\r\n              ) {\r\n                this.$refs.showAlarmModel.tableData2.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList2.push(item.msg);\r\n              }\r\n            } else {\r\n              if (\r\n                // item.powerAuditEntity.electricityRationality == \"是\" && //电量合理性(省内大数据)\r\n                // item.powerAuditEntity.exclusiveAccuracy == \"是\" && //局站独享共享设置\r\n                item.powerAuditEntity.periodicAnomaly == \"是\" //台账周期合理性\r\n              ) {\r\n                this.$refs.showAlarmModel.tableData1.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList1.push(item.msg);\r\n              } else {\r\n                this.$refs.showAlarmModel.tableData3.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList3.push(item.msg);\r\n              }\r\n            }\r\n            if (this.auditResultList.length > 0) {\r\n              this.auditResultList[this.auditResultList.length - 1].progress = 1;\r\n            }\r\n            this.$refs.showAlarmModel.processData = Number(item.progress) * 100;\r\n            this.$refs.showAlarmModel.scrollList();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    save(value) {\r\n      var that = this;\r\n      //进度条页面链接websoket后调用，传jh字段不走保存流程，走稽核流程\r\n      if (value == 1) {\r\n        that.submit[0].jh = \"1\";\r\n        that.submit[0].ymmc = \"自有预付电费台账\";\r\n        that.submit.forEach((item1) => {\r\n          this.ctgKeyList.forEach((item2) => {\r\n            if (item1.ammeterName == item2.ammetername) {\r\n              item1.ctgKey = item2.ctgKey;\r\n            }\r\n          });\r\n        });\r\n        this.getAuditResultNew(that.submit);\r\n      } else {\r\n        if (that.submit[0].hasOwnProperty(\"jh\")) {\r\n          delete that.submit[0].jh;\r\n        }\r\n        if (this.name == \"current\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAccount();\r\n          }\r\n        } else if (this.name == \"all\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAllAccount();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //加入归集单，全部非强控\r\n    selectedFQK() {\r\n      let that = this;\r\n      that.$refs.addBillPer.initAmmeter(\r\n        that.$refs.showAlarmModel.selectIds3,\r\n        13,\r\n        this.accountObj.country\r\n      );\r\n    },\r\n    //提交数据\r\n    submitData(data) {\r\n      let a = [];\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let str = \"\";\r\n        let accountno = this.accountObj.accountno;\r\n        let index = 0;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          if (item.projectName != \"小计\" && item.projectName != \"合计\") {\r\n            let obj = verification(item);\r\n            if (obj.result) {\r\n              if (item.pcid == null) {\r\n                item.accountno = accountno;\r\n              }\r\n              Object.assign(item, { totalusedreadings: item.curusedreadings }); // 确保预付总电量 等于用电量\r\n\r\n              a.push(item.ammeterid);\r\n              submitData.push(item);\r\n              number++;\r\n            } else {\r\n              str +=\r\n                \"电表/协议编号为【\" +\r\n                item.ammeterName +\r\n                \"】的台账验证没有通过：【\" +\r\n                obj.str +\r\n                \"】；\";\r\n            }\r\n          }\r\n        });\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          this.errorTips(str);\r\n        }\r\n        if (submitData.length > 0) {\r\n          this.submit = submitData;\r\n          this.submit2 = submitData;\r\n          //   this.preserveSc()\r\n          addAccountEs(submitData).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：成功保存 \" + submitData.length + \" 条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    addElectricType() {\r\n      let companyId = this.accountObj.company;\r\n      let country = this.accountObj.country;\r\n      if (companyId != null && country != null) {\r\n        let obj = {\r\n          company: companyId,\r\n          country: country,\r\n          accountno: this.accountObj.accountno,\r\n          accountType: \"1\",\r\n          accountestype: 3,\r\n        };\r\n        this.$refs.selectAmmeter.initAmmeter(obj);\r\n      } else {\r\n        this.errorTips(\"请选择分公司和部门\");\r\n      }\r\n    },\r\n    //验证错误弹出提示框\r\n    errorTips(str) {\r\n      this.$Notice.error({\r\n        title: \"提示\",\r\n        desc: str,\r\n        duration: 10,\r\n      });\r\n    },\r\n    handlePage(value) {\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n\r\n      this.pageNum = value;\r\n      this.getAccountMessages();\r\n    },\r\n    handlePageSize(value) {\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n\r\n      this.pageSize = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //向后台请求数据\r\n    getAccountMessages() {\r\n      let params = this.accountObj;\r\n      params.pageNum = this.pageNum;\r\n      params.pageSize = this.pageSize;\r\n      let req = {\r\n        url: \"/business/accountEs/selectAccountEsList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      this.tbAccount.loading = true;\r\n      axios\r\n        .request(req)\r\n        .then((res) => {\r\n          this.tbAccount.loading = false;\r\n          if (res.data) {\r\n            let data = res.data.rows;\r\n            data.forEach(function (item) {\r\n              item.editType = 0;\r\n            });\r\n            data.push(this.suntotal(data)); //小计\r\n            accountEsTotal(this.accountObj).then((res) => {\r\n              //合计\r\n              let alltotal = res.data;\r\n              alltotal.total = \"合计\";\r\n              alltotal.projectName = \"合计\";\r\n              alltotal._disabled = true;\r\n              data.push(alltotal);\r\n            });\r\n            this.tbAccount.data = data;\r\n            this.pageTotal = res.data.total || 0;\r\n            this.setMyStyle(this.tbAccount.data.length);\r\n\r\n            this.editIndex = -1;\r\n            this.columnsIndex = -1;\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //小计\r\n    suntotal(array) {\r\n      let curusedreadings = 0;\r\n      let transformerullage = 0;\r\n      let accountmoney = 0;\r\n      array.forEach(function (item) {\r\n        if (item.effective === 1) {\r\n          curusedreadings += item.curusedreadings;\r\n          transformerullage += item.transformerullage;\r\n          accountmoney += item.accountmoney;\r\n        }\r\n      });\r\n      return {\r\n        curusedreadings: curusedreadings,\r\n        transformerullage: transformerullage,\r\n        accountmoney: accountmoney,\r\n        total: \"小计\",\r\n        projectName: \"小计\",\r\n        _disabled: true,\r\n      };\r\n    },\r\n    //重置\r\n    onResetHandle() {\r\n      this.accountObj = {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        company: this.company,\r\n        projectName: \"\", //项目名称\r\n        country: Number(this.country), //所属部门\r\n        ammeterName: \"\", //电表户号/协议编码\r\n        stationName: \"\",\r\n        accountType: \"1\", //台账类型\r\n        accountestype: 3, //台账类型\r\n        supplybureauammetercode: \"\",\r\n        countryName: this.countryName,\r\n      };\r\n      this.getAccountMessages();\r\n    },\r\n    //计算单价\r\n    unitPrice(row) {\r\n      let accountmoney = row.accountmoney;\r\n      let curusedreadings = row.curusedreadings;\r\n      if (accountmoney != null && curusedreadings != null) {\r\n        let total = null;\r\n        if (curusedreadings == 0) {\r\n          total = 0;\r\n        } else {\r\n          total = accountmoney / curusedreadings;\r\n        }\r\n\r\n        row.unitpirce = total.toFixed(2);\r\n      }\r\n    },\r\n    //验证单价\r\n    validateUnitPrice(data) {\r\n      let category = data.category; //电表描述类型\r\n      let ammeteruse = data.ammeteruse; //电表用途\r\n      let unitpirce = data.unitpirce; //台账单价\r\n      if (!judge_negate(category) && !judge_recovery(ammeteruse)) {\r\n        // if (unitpirce) {\r\n        //   if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n        //     this.errorTips(\r\n        //       \"集团要求单价范围在0.3~2元，此台账单价: \" +\r\n        //         unitpirce +\r\n        //         \" 已超过范围，请确认！\"\r\n        //     );\r\n        //   }\r\n        // }\r\n        if (unitpirce) {\r\n          if (unitpirce != null && unitpirce < unitpirceMax1) {\r\n            // if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n            this.errorTips(\r\n              \"单价范围必须大于0.1元，此台账单价: \" + unitpirce + \"不在范围内，请确认！\"\r\n            );\r\n          }\r\n        }\r\n      }\r\n    },\r\n    remove() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      if (data == null || data.length === 0) {\r\n        this.errorTips(\"请选择要删除的数据\");\r\n        return;\r\n      }\r\n      this.$Modal.confirm({\r\n        title: \"提示\",\r\n        content: \"<p>是否确认删除选中信息？</p>\",\r\n        onOk: () => {\r\n          let b = true;\r\n          let ids = \"\";\r\n          let array = this.tbAccount.data;\r\n          let total = this.pageTotal;\r\n          for (let i = 0; i < data.length; i++) {\r\n            let item = data[i];\r\n            if (item.pcid != null && item.pcid.length > 0) {\r\n              if (item.pabriid) {\r\n                b = false;\r\n              }\r\n              ids += item.pcid + \",\";\r\n            } else {\r\n              for (let j = array.length - 1; j >= 0; j--) {\r\n                let jj = array[j];\r\n                if (jj.ammeterid === item.ammeterid) {\r\n                  array.splice(j, 1);\r\n                  total = total - 1;\r\n                }\r\n              }\r\n            }\r\n          }\r\n          this.pageTotal = total;\r\n          if (b) {\r\n            if (ids.length > 0) {\r\n              removeAccountEs(ids).then((res) => {\r\n                if (res.data.code == 0) {\r\n                  this.$Message.success(\"删除成功\");\r\n                  this.getAccountMessages();\r\n                }\r\n              });\r\n            }\r\n          } else {\r\n            this.errorTips(\"选中信息中有信息还没有跟归集单解除关联，请先解除关联\");\r\n          }\r\n        },\r\n        onCancel: () => {},\r\n      });\r\n    },\r\n    //加入归集单\r\n    addPreserveGJ() {\r\n      let dataL = this.$refs.accountEsTable.getSelection();\r\n      this.dataL = this.$refs.accountEsTable.getSelection();\r\n      if (dataL == null || dataL.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        this.addSubmitDataGJ(dataL);\r\n      }\r\n    },\r\n    //加入归集单\r\n    addPreserveGJAll() {\r\n      let params = this.accountObj;\r\n      params.pageNum = 1;\r\n      params.pageSize = 20000;\r\n      let req = {\r\n        url: \"/business/accountEs/selectAccountEsList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      let array1 = [];\r\n      this.tbAccount.loading = true;\r\n      axios.request(req).then((res) => {\r\n        this.tbAccount.loading = false;\r\n        let dataL = res.data.rows;\r\n        this.dataL = res.data.rows;\r\n        this.addSubmitDataGJ(dataL);\r\n      });\r\n    },\r\n    //提交归集单数据\r\n    addSubmitDataGJ(data) {\r\n      let a = [];\r\n      let b = 1;\r\n      let str = \"\";\r\n      let str1 = \"\";\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let obj = verification(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n        });\r\n        that.ammeterids = a;\r\n        if (b === 1) {\r\n          if (submitData.length > 0) {\r\n            this.submit = submitData;\r\n            this.submit2 = submitData;\r\n            this.preserveSc();\r\n          }\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n        if (str.length > 0) {\r\n          that.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n      }\r\n    },\r\n    openAddBillPerModal(name) {\r\n      this.name = name;\r\n      if (name === \"current\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJ();\r\n        // } else {\r\n        this.selectedAccount();\r\n        // }\r\n      } else if (name === \"all\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJAll();\r\n        // } else {\r\n        this.selectedAllAccount();\r\n        // }\r\n      }\r\n    },\r\n    //加入归集单，全部有效台账\r\n    selectedAllAccount() {\r\n      let that = this;\r\n      that.spinShow = true;\r\n      selectIdsByEsParams(this.accountObj).then((res) => {\r\n        that.spinShow = false;\r\n        if (res.data.length == 0) {\r\n          that.errorTips(\"无有效数据可加入归集单\");\r\n        } else {\r\n          that.$refs.addBillPer.initAmmeter(\r\n            res.data,\r\n            // this.$refs.showAlarmModel.selectIds1,\r\n            13,\r\n            this.accountObj.country\r\n          );\r\n        }\r\n      });\r\n    },\r\n    selectedAccount() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      let b = 1;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        let ids = [];\r\n        data.forEach(function (item) {\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n          ids.push(item.pcid);\r\n        });\r\n        if (b === 1) {\r\n          this.$refs.addBillPer.initAmmeter(ids, 13, this.accountObj.country);\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n      }\r\n    },\r\n    openCompletedPreModal() {\r\n      this.$refs.completedPre.initAmmeter(this.accountObj.country, 13);\r\n    },\r\n    againJoin() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      let b = true;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要重新加入归集单的台账\");\r\n      } else {\r\n        let ids = \"\";\r\n        data.forEach(function (item) {\r\n          let status = item.status;\r\n          if (status != 5) {\r\n            b = false;\r\n          }\r\n          ids += item.pcid + \",\";\r\n        });\r\n        if (b) {\r\n          againJoin(ids).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：操作成功\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        } else {\r\n          this.errorTips(\"只有已退回的台账才能重新加入归集单\");\r\n        }\r\n      }\r\n    },\r\n    refresh() {\r\n      if (this.$refs.showAlarmModel.fromGuijidan != 1) {\r\n        // window.history.go(0);\r\n        this.showAlarmModel = false;\r\n        let obj = this;\r\n        setTimeout(function () {\r\n          obj.getAccountMessages();\r\n        }, 200);\r\n      } else {\r\n        this.showAlarmModel = true;\r\n      }\r\n    },\r\n    beforeLoadData(data, str) {\r\n      var cols = [],\r\n        keys = [];\r\n      for (var i = 0; i < this.tbAccount.exportColumns.length; i++) {\r\n        cols.push(this.tbAccount.exportColumns[i].title);\r\n        keys.push(this.tbAccount.exportColumns[i].key);\r\n      }\r\n      const params = {\r\n        title: cols,\r\n        key: keys,\r\n        data: data,\r\n        autoWidth: true,\r\n        filename: str,\r\n      };\r\n      excel.export_array_to_excel(params);\r\n      return;\r\n    },\r\n    exportCsv(name) {\r\n      this.export.run = true;\r\n      if (name === \"current\") {\r\n        this.beforeLoadData(this.tbAccount.data, \"预付台账导出数据\");\r\n      } else if (name === \"all\") {\r\n        let params = this.accountObj;\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n        let req = {\r\n          url: \"/business/accountEs/selectAccountEsList\",\r\n          method: \"get\",\r\n          params: params,\r\n        };\r\n        this.tbAccount.loading = true;\r\n        axios\r\n          .request(req)\r\n          .then((res) => {\r\n            this.tbAccount.loading = false;\r\n            if (res.data) {\r\n              let array = res.data.rows;\r\n              accountEsTotal(this.accountObj).then((res) => {\r\n                //合计\r\n                let alltotal = res.data;\r\n                alltotal.total = \"合计\";\r\n                alltotal._disabled = true;\r\n                array.push(alltotal);\r\n                this.beforeLoadData(array, \"预付台账导出数据\");\r\n              });\r\n            }\r\n          })\r\n          .catch((err) => {\r\n            console.log(err);\r\n          });\r\n      }\r\n    },\r\n    validate() {\r\n      if (this.columnsIndex != 5) {\r\n        let val = this.enterOperate(this.columnsIndex).data;\r\n        if (val) {\r\n          if (testNumber(val)) {\r\n            switch (this.columnsIndex) {\r\n              case 1:\r\n                this.validateStartdate();\r\n                break;\r\n              case 2:\r\n                this.validateEnddate();\r\n                break;\r\n              case 3:\r\n                this.validatecurtotalreadings();\r\n                break;\r\n              case 4:\r\n                this.validateaccountmoney();\r\n                break;\r\n            }\r\n          } else {\r\n            this.errorTips(\"请输入数字！\");\r\n          }\r\n        }\r\n      }\r\n    },\r\n    validateStartdate() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editStartDate;\r\n      let result = _verify_StartDate(data, val);\r\n      if (result) {\r\n        //失败就弹出提示内容\r\n        this.errorTips(result);\r\n        this.myStyle[this.editIndex].startdate = \"errorStle\";\r\n      } else {\r\n        this.myStyle[this.editIndex].startdate = \"myspan\";\r\n        data.startdate = val;\r\n        data.editType = 1;\r\n      }\r\n    },\r\n    validateEnddate() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editEndDate;\r\n\r\n      let result = _verify_EndDate(data, val, \"大于当前\");\r\n      if (result) {\r\n        //失败就弹出提示内容，并将数据恢复初始化\r\n        this.errorTips(result);\r\n      } else {\r\n        data.enddate = val;\r\n        data.editType = 1;\r\n      }\r\n    },\r\n    validatecurtotalreadings() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editcurtotalreadings;\r\n      val = Math.abs(val);\r\n      let prevtotalreadings = Math.abs(data.prevtotalreadings);\r\n      if (prevtotalreadings > val) {\r\n        this.errorTips(\"止度不能小于起度！\");\r\n        return;\r\n      }\r\n\r\n      data.curtotalreadings = val;\r\n      data.totalusedreadings = val;\r\n      data.curusedreadings = val - prevtotalreadings;\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      if (indexData.version == \"sc\") {\r\n        //验证上期台账是否完成报账\r\n        axios\r\n          .request({\r\n            url: \"/business/accountSC/valOldAcount\",\r\n            method: \"post\",\r\n            params: { pcid: data.pcid, ammeterid: data.ammeterid },\r\n          })\r\n          .then((res) => {\r\n            let msg = \"\";\r\n            if (res.data.msg) msg = res.data.msg;\r\n            // if(data.startdate.endsWith(\"0101\"))\r\n            //     msg +=\"【该起始日期是默认值，请注意修改】\";\r\n            if (msg != \"\")\r\n              this.$Notice.warning({\r\n                title: \"注意\",\r\n                desc: \"电表/协议【\" + data.ammetercode + \"】\" + msg,\r\n                duration: 10,\r\n              });\r\n          });\r\n      }\r\n    },\r\n    validatetransformerullage() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.edittransformerullage;\r\n      data.transformerullage = val;\r\n      data.editType = 1;\r\n    },\r\n    validateaccountmoney() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editaccountmoney;\r\n      data.accountmoney = Math.abs(val);\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      this.validateUnitPrice(data);\r\n    },\r\n    setremark() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editremark;\r\n      data.remark = val;\r\n      data.editType = 1;\r\n    },\r\n    setMyStyle(length) {\r\n      this.myStyle = [];\r\n      for (var i = 0; i < length; i++) {\r\n        this.myStyle.push({\r\n          startdate: \"myspan\",\r\n          enddate: \"myspan\",\r\n          curtotalreadings: \"myspan\",\r\n          accountmoney: \"myspan\",\r\n          remark: \"myspan\",\r\n        });\r\n      }\r\n    },\r\n    //span点击事件将span换成输入框并且获取焦点\r\n    selectCall(row, index, columns, str) {\r\n      this.editStartDate = row.startdate;\r\n      this.editEndDate = row.enddate;\r\n      this.editcurtotalreadings =\r\n        row.curtotalreadings == null || row.curtotalreadings === 0\r\n          ? null\r\n          : row.curtotalreadings;\r\n      this.editaccountmoney =\r\n        row.accountmoney == null || row.accountmoney === 0 ? null : row.accountmoney;\r\n      this.editremark = row.remark;\r\n\r\n      this.editIndex = index;\r\n      this.columnsIndex = columns;\r\n\r\n      let a = this;\r\n      setTimeout(function () {\r\n        if (columns != 8) {\r\n          a.$refs[str + index + columns].focus();\r\n        }\r\n      }, 200);\r\n    },\r\n    //跳转到下一格\r\n    nextCell(data) {\r\n      let index = data.editIndex;\r\n      let columns = data.columnsIndex;\r\n      let row = \"\";\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n      } else if (index > -1 && columns === 5) {\r\n        //当跳转的最后一行最后一格的时候\r\n        if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n          index = 0;\r\n        } else {\r\n          index++;\r\n        }\r\n        columns = 1;\r\n      } else {\r\n        columns += 1;\r\n      }\r\n      data.editIndex = index;\r\n      data.columnsIndex = columns;\r\n      row = data.tbAccount.data[index];\r\n      if (row) {\r\n        data.editStartDate = row.startdate;\r\n        data.editEndDate = row.enddate;\r\n        data.editcurtotalreadings =\r\n          row.curtotalreadings == null || row.curtotalreadings === 0\r\n            ? null\r\n            : row.curtotalreadings;\r\n        data.editaccountmoney =\r\n          row.accountmoney == null || row.accountmoney === 0 ? null : row.accountmoney;\r\n        data.editremark = row.remark;\r\n      }\r\n\r\n      setTimeout(function () {\r\n        data.$refs[data.enterOperate(columns).str + index + columns].focus();\r\n      }, 200);\r\n    },\r\n    //根据列号返回对应的列名\r\n    enterOperate(number) {\r\n      let str = \"\";\r\n      let data = null;\r\n      switch (number) {\r\n        case 1:\r\n          str = \"startdate\";\r\n          data = this.editStartDate;\r\n          break;\r\n        case 2:\r\n          str = \"enddate\";\r\n          data = this.editEndDate;\r\n          break;\r\n        case 3:\r\n          str = \"curtotalreadings\";\r\n          data = this.editcurtotalreadings;\r\n          break;\r\n        case 4:\r\n          str = \"accountmoney\";\r\n          data = this.editaccountmoney;\r\n          break;\r\n        case 5:\r\n          str = \"remark\";\r\n          data = this.editremark;\r\n          break;\r\n      }\r\n      return { str: str, data: data };\r\n    },\r\n    pred() {\r\n      var lett = this;\r\n      let index = lett.editIndex;\r\n      let columns = lett.columnsIndex;\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n        lett.editIndex = index;\r\n        lett.columnsIndex = columns;\r\n        lett.editStartDate = lett.tbAccount.data[index].startdate;\r\n        setTimeout(function () {\r\n          lett.$refs[lett.enterOperate(columns).str + index + columns].focus();\r\n        }, 200);\r\n      } else {\r\n        lett.validate();\r\n        lett.setremark();\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    ellipsis(value) {\r\n      if (!value) return \"\";\r\n      if (value.length > 3) {\r\n        return value.slice(0, 3) + \"...\";\r\n      }\r\n      return value;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.page-class {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  .cl-table {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n  .button-bar {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n.yjjh > .ivu-modal-wrap > .ivu-modal {\r\n  top: 20px !important;\r\n}\r\n.mytable .ivu-table-cell {\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: normal;\r\n  word-break: break-all;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.accountEs .filter-divider {\r\n  margin: 0px;\r\n  text-align: center;\r\n}\r\n.accountEs .header-bar-show {\r\n  max-height: 300px;\r\n  padding-top: 14px;\r\n  overflow: inherit;\r\n  border-bottom: 1px solid #e8eaec;\r\n}\r\n.accountEs .header-bar-hide {\r\n  max-height: 0;\r\n  padding-top: 0;\r\n  overflow: hidden;\r\n  border-bottom: 0;\r\n}\r\n\r\n.mytable .myspan {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n}\r\n.mytable .errorStle {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n  color: red;\r\n}\r\n</style>\r\n"]}]}