{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\station\\viewStation.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\station\\viewStation.vue", "mtime": 1754285403021}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBjb3VudHJ5TW9kYWwgZnJvbSAiLi9jb3VudHJ5TW9kYWwiOwppbXBvcnQge2dldHVzZXJ9IGZyb20gIkAvYXBpL2FsZXJ0Y29udHJvbC9hbGVydGNvbnRyb2wiOwppbXBvcnQge2FkZHN0YXRpb24sIGdldEFtbWV0ZXJMaXN0QnlTdGF0aW9ufSBmcm9tICJAL2FwaS9hbGVydGNvbnRyb2wvYWxlcnRjb250cm9sIjsKaW1wb3J0IHtpc1N0YXRpb25uYW1lRXhpc3R9IGZyb20gIkAvYXBpL2FsZXJ0Y29udHJvbC9hbGVydGNvbnRyb2wiOwppbXBvcnQge2JsaXN0LCBidGV4dH0gZnJvbSAiQC9saWJzL3Rvb2xzIjsKaW1wb3J0IHtnZXRzdGF0aW9ub2xkfSBmcm9tICJAL2FwaS9hbGVydGNvbnRyb2wvYWxlcnRjb250cm9sIjsKCmV4cG9ydCBkZWZhdWx0IHsKICAgIG5hbWU6ICJ2aWV3U3RhdGlvbiIsCiAgICBjb21wb25lbnRzOiB7Y291bnRyeU1vZGFsfSwKICAgIGRhdGEoKSB7CiAgICAgICAgLy/nirbmgIEKICAgICAgICBsZXQgcmVuZGVyU3RhdHVzID0gKGgsIHBhcmFtcykgPT4gewogICAgICAgICAgICBsZXQgdmFsdWUgPSAiIjsKICAgICAgICAgICAgZm9yIChsZXQgaXRlbSBvZiB0aGlzLnN0YXR1cykgewogICAgICAgICAgICAgICAgaWYgKGl0ZW0udHlwZUNvZGUgPT0gcGFyYW1zLnJvdy5zdGF0dXMpIHsKICAgICAgICAgICAgICAgICAgICB2YWx1ZSA9IGl0ZW0udHlwZU5hbWU7CiAgICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIGgoImRpdiIsIHZhbHVlKTsKICAgICAgICB9OwogICAgICAgIC8v55S16KGo57G75Z6LCiAgICAgICAgbGV0IHJlbmRlckFtbWV0ZXJUeXBlID0gKGgsIHBhcmFtcykgPT4gewogICAgICAgICAgICBsZXQgdmFsdWUgPSAiIjsKICAgICAgICAgICAgZm9yIChsZXQgaXRlbSBvZiB0aGlzLmFtbWV0ZXJUeXBlKSB7CiAgICAgICAgICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSBwYXJhbXMucm93LmFtbWV0ZXJ0eXBlKSB7CiAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSBpdGVtLnR5cGVOYW1lOwogICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiBoKCJkaXYiLCB2YWx1ZSk7CiAgICAgICAgfTsKICAgICAgICAvL+eUteihqOeUqOmAlAogICAgICAgIGxldCByZW5kZXJhbW1ldGVyVXNlID0gKGgsIHBhcmFtcykgPT4gewogICAgICAgICAgICBsZXQgdmFsdWUgPSAiIjsKICAgICAgICAgICAgZm9yIChsZXQgaXRlbSBvZiB0aGlzLmFtbWV0ZXJVc2UpIHsKICAgICAgICAgICAgICAgIGlmIChpdGVtLnR5cGVDb2RlID09IHBhcmFtcy5yb3cuYW1tZXRlcnVzZSkgewogICAgICAgICAgICAgICAgICAgIHZhbHVlID0gaXRlbS50eXBlTmFtZTsKICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gaCgiZGl2IiwgdmFsdWUpOwogICAgICAgIH07CiAgICAgICAgY29uc3QgdmFsaWRhbmFtZSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgICAgICAgaWYgKHZhbHVlID09IG51bGwgfHwgdmFsdWUgPT0gJycpIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5LiN6IO95Li656m6JykpCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB2YXIgdGVtcCA9IHt9OwogICAgICAgICAgICAgICAgdGVtcC5zdGF0aW9ubmFtZSA9IHZhbHVlOwoKICAgICAgICAgICAgICAgIGlzU3RhdGlvbm5hbWVFeGlzdCh0ZW1wKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CgogICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YSA9PSB0cnVlKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5bGA56uZ5ZCN56ew5bey5a2Y5ZyoJykpCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgICAgY2FsbGJhY2soKQogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgfTsKICAgICAgICBjb25zdCB2YWxpZGF0b3J0eXBlID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gewogICAgICAgICAgICBpZiAodmFsdWUgPT0gbnVsbCB8fCB2YWx1ZSA9PSAnJykgewogICAgICAgICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfkuI3og73kuLrnqbonKSkKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKCkKICAgICAgICAgICAgfQogICAgICAgIH07CiAgICAgICAgY29uc3QgdmFsaWRhbW90aGVybmFtZSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgICAgICAgaWYgKCh2YWx1ZSA9PSBudWxsIHx8IHZhbHVlID09ICcnKSAmJiB0aGlzLnN0YXRpb24uaXNzdWIgPT0gMSkgewogICAgICAgICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfkuI3og73kuLrnqbonKSkKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKCkKICAgICAgICAgICAgfQogICAgICAgIH07CiAgICAgICAgY29uc3QgdmFsaXNiaWdmYWN0b3JpZXMgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CgogICAgICAgICAgICBpZiAoKHZhbHVlID09IG51bGwgfHwgdmFsdWUgPT0gJycpICYmIHRoaXMuc3RhdGlvbi5pc2JpZ2ZhY3RvcmllcyA9PSAxKSB7CiAgICAgICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+S4jeiDveS4uuepuicpKQogICAgICAgICAgICB9IGVsc2UgaWYgKHZhbHVlID09IDApIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5LiN6IO95Li6MCcpKQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgY2FsbGJhY2soKQogICAgICAgICAgICB9CiAgICAgICAgfTsKICAgICAgICBjb25zdCB2YWxpc3RhdGlvbmxldmVsID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gewoKICAgICAgICAgICAgaWYgKCh2YWx1ZSA9PSBudWxsIHx8IHZhbHVlID09ICcnKSkgewogICAgICAgICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfkuI3og73kuLrnqbonKSkKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKCkKICAgICAgICAgICAgfQogICAgICAgIH07CgogICAgICAgIGNvbnN0IHZhbGlkYW5vcm1hbGJ1c3ByaWNlID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gewoKICAgICAgICAgICAgaWYgKCh2YWx1ZSA9PSBudWxsIHx8IHZhbHVlID09ICcnKSAmJiB0aGlzLnN0YXRpb24uaXNub3JtYWxidXNzdXNlID09IDEpIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5LiN6IO95Li656m6JykpCiAgICAgICAgICAgIH0gZWxzZSBpZiAodmFsdWUgPT0gMCkgewogICAgICAgICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCfkuI3og73kuLowJykpCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBjYWxsYmFjaygpCiAgICAgICAgICAgIH0KICAgICAgICB9OwogICAgICAgIGNvbnN0IHZhbGlkYXJlc3N0YXRpb25uYW1lID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gewoKICAgICAgICAgICAgaWYgKCh2YWx1ZSA9PSBudWxsIHx8IHZhbHVlID09ICcnKSAmJiAodGhpcy5zdGF0aW9uLnN0YXRpb250eXBlID09ICcxMDAwMScgfHwgdGhpcy5zdGF0aW9uLnN0YXRpb250eXBlID09ICcxMDAwMicgfHwgdGhpcy5zdGF0aW9uLnN0YXRpb250eXBlID09ICcxMDAwMycgfHwgdGhpcy5zdGF0aW9uLnN0YXRpb250eXBlID09ICcxMDAwNCcgfHwgdGhpcy5zdGF0aW9uLnN0YXRpb250eXBlID09ICcxMDAwNScpKSB7CiAgICAgICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+S4jeiDveS4uuepuicpKQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgY2FsbGJhY2soKQogICAgICAgICAgICB9CiAgICAgICAgfTsKICAgICAgICByZXR1cm4gewogICAgICAgICAgICBzdGF0dXM6IFtdLC8v54q25oCBCiAgICAgICAgICAgIGFtbWV0ZXJUeXBlOiBbXSwvL+eUteihqOexu+WeiwogICAgICAgICAgICBhbW1ldGVyVXNlOiBbXSwvL+eUteihqOeUqOmAlAogICAgICAgICAgICBzdGF0aW9uTGV2ZWw6IHt9LAogICAgICAgICAgICBzdGF0aW9ubGV2ZWxjYXRlZ29yeTogJycsLy/lsYDnq5nnrYnnuqfnoIHooajlgLwKICAgICAgICAgICAgZGF0YTE6ICcnLAogICAgICAgICAgICBkYXRhMjogJycsCiAgICAgICAgICAgIHVzZXJzOiB0aGlzLnVzZXJzMSwKICAgICAgICAgICAgc2hvd01vZGVsOiBmYWxzZSwKICAgICAgICAgICAgY29tcGFuaWVzOiBbXSwKICAgICAgICAgICAgY291bnRyeWxpc3Q6IFtdLAogICAgICAgICAgICBjb21wYW55OiAnJywvL+WIneWni+WAvAogICAgICAgICAgICBjb3VudHJ5OiAnJywvL+WIneWni+WAvAogICAgICAgICAgICBydWxlVmFsaWRhdGU6IHsKICAgICAgICAgICAgICAgIHN0YXRpb25uYW1lOiBbCiAgICAgICAgICAgICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCB2YWxpZGF0b3I6IHZhbGlkYW5hbWUsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBzdGF0aW9udHlwZTogWwogICAgICAgICAgICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB2YWxpZGF0b3J0eXBlLCB0cmlnZ2VyOiAnYmx1cid9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgc3RhdGlvbmNvZGU6IFsKICAgICAgICAgICAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cid9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgdXNld2F5OiBbCiAgICAgICAgICAgICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCB2YWxpZGF0b3I6IHZhbGlkYXRvcnR5cGUsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBwcm9wZXJ0eXJpZ2h0OiBbCiAgICAgICAgICAgICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCB2YWxpZGF0b3I6IHZhbGlkYXRvcnR5cGUsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBjb21wYW55OiBbCiAgICAgICAgICAgICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCB2YWxpZGF0b3I6IHZhbGlkYXRvcnR5cGUsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBjb3VudHJ5OiBbCiAgICAgICAgICAgICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCB2YWxpZGF0b3I6IHZhbGlkYXRvcnR5cGUsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBzdGFmZm51bWJlcjogWwogICAgICAgICAgICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB2YWxpZGF0b3J0eXBlLCB0cmlnZ2VyOiAnYmx1cid9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgYXJlYTogWwogICAgICAgICAgICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB2YWxpZGF0b3J0eXBlLCB0cmlnZ2VyOiAnYmx1cid9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgaXNiaWdmYWN0b3JpZXM6IFt7cmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogdmFsaWRhdG9ydHlwZSwgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgIG1vdGhlcnN0YXRpb25uYW1lOiBbCiAgICAgICAgICAgICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCB2YWxpZGF0b3I6IHZhbGlkYW1vdGhlcm5hbWUsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBjb3N0Y2VudGVybmFtZTogWwogICAgICAgICAgICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB2YWxpZGF0b3J0eXBlLCB0cmlnZ2VyOiAnYmx1cid9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgbm9ybWFsYnVzcHJpY2U6IFsKICAgICAgICAgICAgICAgICAgICB7cmVxdWlyZWQ6IGZhbHNlLCB2YWxpZGF0b3I6IHZhbGlkYW5vcm1hbGJ1c3ByaWNlLCB0cmlnZ2VyOiAnYmx1cid9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgcmVzc3RhdGlvbm5hbWU6IFsKICAgICAgICAgICAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogdmFsaWRhcmVzc3RhdGlvbm5hbWUsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBiaWdmYWN0b3J5dmFsbGV5cHJpY2U6IFsKICAgICAgICAgICAgICAgICAgICB7cmVxdWlyZWQ6IGZhbHNlLCB2YWxpZGF0b3I6IHZhbGlzYmlnZmFjdG9yaWVzLCB0cmlnZ2VyOiAnYmx1cid9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgYmlnZmFjdG9yeXBsYWlucHJpY2U6IFsKICAgICAgICAgICAgICAgICAgICB7cmVxdWlyZWQ6IGZhbHNlLCB2YWxpZGF0b3I6IHZhbGlzYmlnZmFjdG9yaWVzLCB0cmlnZ2VyOiAnYmx1cid9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgYmlnZmFjdG9yeXBlYWtwcmljZTogWwogICAgICAgICAgICAgICAgICAgIHtyZXF1aXJlZDogZmFsc2UsIHZhbGlkYXRvcjogdmFsaXNiaWdmYWN0b3JpZXMsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBzdGF0aW9ubGV2ZWw6IFsKICAgICAgICAgICAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogdmFsaXN0YXRpb25sZXZlbCwgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgICAgICAgICAgXQogICAgICAgICAgICB9LAogICAgICAgICAgICBzdGF0aW9uOiB7CiAgICAgICAgICAgICAgICBpc2JpZ2ZhY3RvcmllczogJzEnLAogICAgICAgICAgICAgICAgbm9ybWFsYnVzcHJpY2VmbGFnOiBmYWxzZSwKICAgICAgICAgICAgICAgIGlzc3ViOiBudWxsLAogICAgICAgICAgICAgICAgbW90aGVyc3RhdGlvbjogbnVsbCwKICAgICAgICAgICAgICAgIG1vdGhlcnN0YXRpb25uYW1lOiBudWxsLAogICAgICAgICAgICAgICAgYmlnZmFjdG9yeXBlYWtwcmljZTogbnVsbCwKICAgICAgICAgICAgICAgIG5vcm1hbGJ1c3ByaWNlOiBudWxsLAogICAgICAgICAgICAgICAgYmlnZmFjdG9yeXZhbGxleXByaWNlOiBudWxsLAogICAgICAgICAgICAgICAgLy8gYmlnZmFjdG9yeXBlYWtwcmljZTogbnVsbCwKICAgICAgICAgICAgICAgIGJpZ2ZhY3RvcnlwbGFpbnByaWNlOiBudWxsLAogICAgICAgICAgICAgICAgdHJhbnNmb3JtZXJjYXBhY2l0eTogW10KICAgICAgICAgICAgfSwKICAgICAgICAgICAgYW1tZXRlcjogewogICAgICAgICAgICAgICAgY29sdW1uczogWwogICAgICAgICAgICAgICAgICAgIC8qe3RpdGxlOiAn55S16KGo57yW5Y+3Jywga2V5OiAnYW1tZXRlcm5vJywgYWxpZ246ICdjZW50ZXInfSwqLwogICAgICAgICAgICAgICAgICAgIHt0aXRsZTogJ+mhueebruWQjeensCcsIGtleTogJ3Byb2plY3RuYW1lJywgYWxpZ246ICdjZW50ZXInfSwKICAgICAgICAgICAgICAgICAgICB7dGl0bGU6ICfnlLXooajmiLflj7cv5Y2P6K6u57yW5Y+3Jywga2V5OiAnYW1tZXRlcm5hbWUnLCBhbGlnbjogJ2NlbnRlcid9LAogICAgICAgICAgICAgICAgICAgIHt0aXRsZTogJ+aJgOWxnuWIhuWFrOWPuCcsIGtleTogJ2NvbXBhbnlOYW1lJywgYWxpZ246ICdjZW50ZXInLH0sCiAgICAgICAgICAgICAgICAgICAge3RpdGxlOiAn6LSj5Lu75Lit5b+DJywga2V5OiAnY291bnRyeU5hbWUnLCBhbGlnbjogJ2NlbnRlcicsfSwKICAgICAgICAgICAgICAgICAgICB7dGl0bGU6ICfnlKjnlLXnsbvlnosnLCBrZXk6ICdlbGVjdHJvdHlwZW5hbWUnLCBhbGlnbjogJ2NlbnRlcid9LAogICAgICAgICAgICAgICAgICAgIHt0aXRsZTogJ+eUteihqC/ljY/orq7nsbvlnosnLCBrZXk6ICdhbW1ldGVydHlwZW5hbWUnLCBhbGlnbjogJ2NlbnRlcid9LAogICAgICAgICAgICAgICAgICAgIHt0aXRsZTogJ+eUteihqOeUqOmAlCcsIGtleTogJ2FtbWV0ZXJ1c2UnLCByZW5kZXI6IHJlbmRlcmFtbWV0ZXJVc2UsIGFsaWduOiAnY2VudGVyJ30sCiAgICAgICAgICAgICAgICAgICAge3RpdGxlOiAn54q25oCBJywga2V5OiAnc3RhdHVzJywgcmVuZGVyOiByZW5kZXJTdGF0dXMsIGFsaWduOiAnY2VudGVyJ30sCiAgICAgICAgICAgICAgICAgICAge3RpdGxlOiAn5Yib5bu65pe26Ze0Jywga2V5OiAnY3JlYXRlVGltZScsIGFsaWduOiAnY2VudGVyJ30KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBkYXRhOiBbXQogICAgICAgICAgICB9CiAgICAgICAgfQogICAgfSwKICAgIHByb3BzOiB7CiAgICAgICAgLypzaG93TW9kZWw6ZmFsc2UsKi8KICAgICAgICAvLyB1c2VyczE6IHt0eXBlOiBbQXJyYXksIE9iamVjdF0sIHJlcXVpcmVkOiB0cnVlfSwKICAgICAgICAvLyB1c2VyczondXNlcnMnCiAgICB9LAogICAgLyp3YXRjaDogewogICAgICAgIGRhdGEyOiB7CiAgICAgICAgICAgIGltbWVkaWF0ZTogdHJ1ZSwKICAgICAgICAgICAgaGFuZGxlcih2YWwpIHsKICAgICAgICAgICAgICAgIHRoaXMuc3RhdGlvbi5tb3RoZXJzdGF0aW9ubmFtZSA9IHZhbDsKICAgICAgICAgICAgfQogICAgICAgIH0KICAgIH0sKi8KICAgIG1ldGhvZHM6IHsKICAgICAgICAvKnN0YXRpb250eXBlQ2hhbmdlKHYsIHBhcmFtKXsKICAgICAgICAgICAgaWYodj09JzEwMDAxJ3x8dj09JzEwMDAxJ3x8dj09JzEwMDAxJ3x8dj09JzEwMDAxJ3x8dj09JzEwMDAxJyl7CiAgICAgICAgICAgICAgICB0aGlzLnN0YXRpb25sZXZlbGNhdGVnb3J5PSdzdGF0aW9uTGV2ZWwxJzsKICAgICAgICAgICAgfWVsc2UgaWYodj09JzIwMDAxJ3x8dj09JzIwMDAyJ3x8dj09Jy0xJ3x8dj09Jy0yJyl7CiAgICAgICAgICAgICAgICB0aGlzLnN0YXRpb25sZXZlbGNhdGVnb3J5PSdzdGF0aW9uTGV2ZWwyJzsKICAgICAgICAgICAgfQogICAgICAgIH0sKi8KICAgICAgICBtb3RoZXJTdGF0aW9uKCkgewogICAgICAgICAgICB0aGlzLiRyZWZzLmNvdW50cnlNb2RhbC5jaG9vc2UoNik7Ly/otKPku7vkuK3lv4MKICAgICAgICB9LAogICAgICAgIGNvc3RDZW50ZXIoKSB7CiAgICAgICAgICAgIHRoaXMuJHJlZnMuY291bnRyeU1vZGFsLmNob29zZSg3KTsvL+aIkOacrOS4reW/gwogICAgICAgIH0sCiAgICAgICAgcmVzU3RhdGlvbigpIHsKICAgICAgICAgICAgdGhpcy4kcmVmcy5jb3VudHJ5TW9kYWwuY2hvb3NlKDgpOy8v5oiQ5pys5Lit5b+DCiAgICAgICAgfSwKICAgICAgICBnZXREYXRhRnJvbU1vZGFsKGRhdGEsIGZsYWcpIHsKCiAgICAgICAgICAgIGlmIChmbGFnID09IDYpIHsKICAgICAgICAgICAgICAgIHRoaXMuZGF0YTEgPSBkYXRhLmlkOwogICAgICAgICAgICAgICAgdGhpcy5kYXRhMiA9IGRhdGEubmFtZQogICAgICAgICAgICAgICAgdGhpcy5zdGF0aW9uLm1vdGhlcnN0YXRpb24gPSB0aGlzLmRhdGExOwogICAgICAgICAgICAgICAgdGhpcy5zdGF0aW9uLm1vdGhlcnN0YXRpb25uYW1lID0gdGhpcy5kYXRhMjsKICAgICAgICAgICAgfSBlbHNlIGlmIChmbGFnID09IDcpIHsKICAgICAgICAgICAgICAgIHRoaXMuZGF0YTEgPSBkYXRhLmlkOwogICAgICAgICAgICAgICAgdGhpcy5kYXRhMiA9IGRhdGEubmFtZQogICAgICAgICAgICAgICAgdGhpcy5zdGF0aW9uLmNvc3RjZW50ZXIgPSB0aGlzLmRhdGExOwogICAgICAgICAgICAgICAgdGhpcy5zdGF0aW9uLmNvc3RjZW50ZXJuYW1lID0gdGhpcy5kYXRhMjsKICAgICAgICAgICAgfSBlbHNlIGlmIChmbGFnID09IDgpIHsKICAgICAgICAgICAgICAgIHRoaXMuZGF0YTEgPSBkYXRhLmlkOwogICAgICAgICAgICAgICAgdGhpcy5kYXRhMiA9IGRhdGEubmFtZQogICAgICAgICAgICAgICAgdGhpcy5zdGF0aW9uLnJlc3N0YXRpb25jb2RlID0gdGhpcy5kYXRhMTsKICAgICAgICAgICAgICAgIHRoaXMuc3RhdGlvbi5yZXNzdGF0aW9ubmFtZSA9IHRoaXMuZGF0YTI7CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIC8qdGhpcy5zdGF0aW9uLm1vdGhlcnN0YXRpb24gPSBkYXRhLmlkOwogICAgICAgICAgICB0aGlzLnN0YXRpb24ubW90aGVyc3RhdGlvbm5hbWUgPSBkYXRhLm5hbWU7Ki8KICAgICAgICB9LAogICAgICAgIGluaXREYXRhKCkgewogICAgICAgICAgICB0aGlzLiRyZWZzLnN0YXRpb25Gb3JtLnJlc2V0RmllbGRzKCk7CiAgICAgICAgICAgIHRoaXMuc2hvd01vZGVsID0gZmFsc2U7CiAgICAgICAgfSwKICAgICAgICBvbk1vZGFsQ2FuY2VsKCkgewogICAgICAgICAgICB0aGlzLmluaXREYXRhKCk7CiAgICAgICAgICAgIC8vdGhpcy5zdGF0aW9uPXt9OwogICAgICAgIH0sCiAgICAgICAgb25Nb2RhbE9LKHN0YXRpb25Gb3JtKSB7CiAgICAgICAgICAgIHRoaXMuJHJlZnNbJ3N0YXRpb25Gb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgICAgICAgICAgICAvL+ivt+axguWQjuWPsOS/neWtmOaVsOaNrgoKICAgICAgICAgICAgICAgICAgICB2YXIgdGVtcCA9IHRoaXMuc3RhdGlvbjsKICAgICAgICAgICAgICAgICAgICB0aGlzLnN0YXRpb24udXNld2F5ID0gdGVtcC51c2V3YXk7CiAgICAgICAgICAgICAgICAgICAgYWRkc3RhdGlvbih0ZW1wKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwoKICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLmZsYWcgPT0gJzEnKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm1vZGFsMSA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5zdWNjZXNzKHJlcy5kYXRhLm1zZyk7CiAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm1vZGFsMSA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5lcnJvcihyZXMuZGF0YS5tc2cpOwogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5zaG93TW9kZWwgPSBmYWxzZTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKICAgICAgICBpbml0U3RhdGlvbihpZCkgewogICAgICAgICAgICAvL+WIneWni+WMlgogICAgICAgICAgICAvL+iOt+WPluecn+WunuaVsOaNri8KICAgICAgICAgICAgZ2V0c3RhdGlvbm9sZChpZCkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgY29uc29sZS5sb2cocmVzLmRhdGEuc2hlcmVkZXBhcnRuYW1lID09IG51bGwsICJyZXMuZGF0YS5zaGVyZWRlcGFydG5hbWU9PW51bGwiKTsKICAgICAgICAgICAgICAgIC8vIGRlYnVnZ2VyCiAgICAgICAgICAgICAgICBsZXQgc2hlcmVkZXBhcnRuYW1lMTsKICAgICAgICAgICAgICAgIGlmKHJlcy5kYXRhLnNoZXJlZGVwYXJ0bmFtZSA9PSBudWxsIHx8IHJlcy5kYXRhLnNoZXJlZGVwYXJ0bmFtZSA9PSB1bmRlZmluZWQgfHwgcmVzLmRhdGEuc2hlcmVkZXBhcnRuYW1lID09ICIiKSAgewogICAgICAgICAgICAgICAgICAgIHNoZXJlZGVwYXJ0bmFtZTEgPSBbXTsKICAgICAgICAgICAgICAgIH0gZWxzZXsKICAgICAgICAgICAgICAgICAgICBzaGVyZWRlcGFydG5hbWUxID0gcmVzLmRhdGEuc2hlcmVkZXBhcnRuYW1lOwogICAgICAgICAgICAgICAgICAgIGxldCBzaGFyZU5hbWUgPSBbXTsKICAgICAgICAgICAgICAgICAgICBzaGVyZWRlcGFydG5hbWUxLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIGlmKGl0ZW0gPT0gIjEiKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2hhcmVOYW1lLnB1c2goIuenu+WKqCIpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgfWVsc2UgaWYoaXRlbSA9PSAiMiIpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaGFyZU5hbWUucHVzaCgi55S15L+hIik7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9ZWxzZSBpZiAoaXRlbSA9PSAiMyIpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaGFyZU5hbWUucHVzaCgi6IGU6YCaIik7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9ZWxzZSBpZiAoaXRlbSA9PSAiNCIpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaGFyZU5hbWUucHVzaCgi6IO95rqQIik7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9ZWxzZSBpZiAoaXRlbSA9PSAiNSIpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaGFyZU5hbWUucHVzaCgi5ouT5bGVIik7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9ZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2hhcmVOYW1lLnB1c2goIiIpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgcmVzLmRhdGEuc2hhcmVOYW1lID0gc2hhcmVOYW1lLmpvaW4oIiwiKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIHRoaXMuc3RhdGlvbiA9IHJlcy5kYXRhOwogICAgICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMuc3RhdGlvbiwgInRoaXMuc3RhdGlvbiIpCiAgICAgICAgICAgICAgICAvLyBPYmplY3Qua2V5cyh0aGlzLnN0YXRpb24pLmZvckVhY2goa2V5PT57CiAgICAgICAgICAgICAgICAvLyAgICAgdGhpcy5zdGF0aW9uW2tleV0gPSB0eXBlb2YgdGhpcy5zdGF0aW9uW2tleV09PSdudW1iZXInP3RoaXMuc3RhdGlvbltrZXldKycnOiB0aGlzLnN0YXRpb25ba2V5XQogICAgICAgICAgICAgICAgLy8gfSk7CiAgICAgICAgICAgICAgICAvLyB0aGlzLiRmb3JjZVVwZGF0ZSgpCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICAvL+iOt+WPluWFs+iBlOeUteihqOS/oeaBrwogICAgICAgICAgICBnZXRBbW1ldGVyTGlzdEJ5U3RhdGlvbihpZCkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgdGhpcy5hbW1ldGVyLmRhdGEgPSByZXMuZGF0YS5yb3dzOwogICAgICAgICAgICB9KQogICAgICAgICAgICB0aGlzLnNob3dNb2RlbCA9IHRydWU7CiAgICAgICAgfQogICAgfSwgbW91bnRlZCgpIHsKCiAgICAgICAgdGhpcy5zdGF0aW9uTGV2ZWwgPSB7CiAgICAgICAgICAgIHN0YXRpb25MZXZlbEE6IGJsaXN0KCJzdGF0aW9uTGV2ZWxBIiksCiAgICAgICAgICAgIHN0YXRpb25MZXZlbEI6IGJsaXN0KCJzdGF0aW9uTGV2ZWxCIiksCiAgICAgICAgfTsKICAgICAgICB0aGlzLnN0YXR1cyA9IGJsaXN0KCJzdGF0dXMiKTsvL+eKtuaAgQogICAgICAgIHRoaXMuYW1tZXRlclR5cGUgPSBibGlzdCgiYW1tZXRlclR5cGUiKS8v55S16KGo57G75Z6LCiAgICAgICAgdGhpcy5hbW1ldGVyVXNlID0gYmxpc3QoImFtbWV0ZXJVc2UiKS8v55S16KGo55So6YCUCgogICAgfQp9Cg=="}, {"version": 3, "sources": ["viewStation.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkaA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "viewStation.vue", "sourceRoot": "src/view/basedata/station", "sourcesContent": ["<template>\r\n    <div>\r\n        <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n        <Modal v-model=\"showModel\" width=\"80%\" title=\"查看局站\">\r\n            <!--<div slot='content'>-->\r\n            <card name=\"Panel1\">\r\n                <Collapse :value=\"['Panel1','Panel2']\">\r\n                    <Panel name=\"Panel1\">局站信息\r\n                        <div slot='content'>\r\n                            <Row class=\"\">\r\n                                <Form :model=\"station\" ref=\"stationForm\" :label-width=\"80\">\r\n                                    <Row>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"局站类型:\" prop=\"stationtype\">\r\n                                                <cl-select disabled size=\"small\" v-model=\"station.stationtype\"\r\n                                                           category=\"BUR_STAND_TYPE\" labelField=\"typeName\"\r\n                                                           valueField=\"typeCode\">\r\n                                                </cl-select>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"局站名称:\" prop=\"stationname\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.stationname\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"局站编码:\" prop=\"stationcode\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.stationcode\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"产权:\" prop=\"propertyright\">\r\n                                                <cl-select disabled size=\"small\" v-model=\"station.propertyright\"\r\n                                                           category=\"propertyRight\" labelField=\"typeName\"\r\n                                                           valueField=\"typeCode\">\r\n                                                </cl-select>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                    </Row>\r\n                                    <Row>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"用途:\" prop=\"useway\">\r\n                                                <cl-select disabled size=\"small\" v-model=\"station.useway\"\r\n                                                           category=\"useType\" labelField=\"typeName\"\r\n                                                           valueField=\"typeCode\">\r\n                                                </cl-select>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <!--<Col span=\"6\">\r\n                                            <FormItem label=\"所属成本中心:\" prop=\"costcentername\">\r\n                                                <cl-input readonly readonly icon=\"ios-archive\" v-model=\"station.costcentername\"\r\n                                                           placeholder=\"点击图标选择\" @onClick=\"costCenter()\" />\r\n                                            </FormItem>\r\n                                        </Col>-->\r\n                                        <!--\"-->\r\n                                        <Col span=\"6\">\r\n                                            <FormItem\r\n                                                    v-if=\"this.station.stationtype=='10002'&&this.station.propertyright=='3'\"\r\n                                                    label=\"站址名称:\">\r\n                                                <cl-input readonly size=\"small\" v-model=\"station.resstationname\"/>\r\n                                            </FormItem>\r\n                                            <FormItem\r\n                                                    v-else-if=\"this.station.stationtype=='20001'||this.station.stationtype=='20002'||this.station.stationtype=='-2'\"\r\n                                                    label=\"房屋名称:\">\r\n                                                <cl-input readonly size=\"small\" v-model=\"station.resstationname\"/>\r\n                                            </FormItem>\r\n                                            <FormItem v-else label=\"对应资源系统局站名称:\">\r\n                                                <cl-input readonly icon=\"ios-archive\" size=\"small\"\r\n                                                          v-model=\"station.resstationname\"\r\n                                                          placeholder=\"点击图标选择\" />\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem\r\n                                                    v-if=\"this.station.stationtype=='10002'&&this.station.propertyright=='3'\"\r\n                                                    label=\"站址编码:\">\r\n                                                <cl-input readonly size=\"small\" v-model=\"station.resstationcode\"/>\r\n                                            </FormItem>\r\n                                            <FormItem\r\n                                                    v-else-if=\"this.station.stationtype=='20001'||this.station.stationtype=='20002'||this.station.stationtype=='-2'\"\r\n                                                    label=\"房屋编码:\">\r\n                                                <cl-input readonly size=\"small\" v-model=\"station.resstationcode\"/>\r\n                                            </FormItem>\r\n                                            <FormItem v-else label=\"对应资源系统局站id:\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.resstationcode\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"局站等级:\" prop=\"stationlevel\">\r\n                                                <Select disabled\r\n                                                        v-if=\"(this.station.stationtype=='10001'||this.station.stationtype=='10002'||this.station.stationtype=='10003'||this.station.stationtype=='10004'||this.station.stationtype=='10005')\"\r\n                                                        size=\"small\" v-model=\"station.stationlevel\">\r\n                                                    <Option v-for=\"item in stationLevel.stationLevelA\"\r\n                                                            :value=\"item.typeCode\" :key=\"item.typeCode\">\r\n                                                        {{item.typeName}}\r\n                                                    </Option>\r\n                                                </Select>\r\n                                                <Select disabled\r\n                                                        v-if=\"(this.station.stationtype=='20001'||this.station.stationtype=='20002'||this.station.stationtype=='-1'||this.station.stationtype=='-2')\"\r\n                                                        size=\"small\" v-model=\"station.stationlevel\">\r\n                                                    <Option v-for=\"item in stationLevel.stationLevelB\"\r\n                                                            :value=\"item.typeCode\" :key=\"item.typeCode\">\r\n                                                        {{item.typeName}}\r\n                                                    </Option>\r\n                                                </Select>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                    </Row>\r\n                                    <Row>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"所属分公司:\" prop=\"company\">\r\n                                                <!--<Select disabled v-model=\"station.company\">\r\n                                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                                </Select>-->\r\n                                                <cl-input readonly size=\"small\" :maxlength=50\r\n                                                          v-model=\"station.companyname\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"所属部门:\" prop=\"country\">\r\n                                                <!--<Select disabled v-model=\"station.country\">\r\n                                                    <Option v-for=\"item1 in countrylist\" :value=\"item1.id\" :key=\"item1.id\">{{item1.name}}</Option>\r\n                                                </Select>-->\r\n                                                <cl-input readonly size=\"small\" :maxlength=50\r\n                                                          v-model=\"station.countryname\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"是否附属局站:\" prop=\"issub\">\r\n                                                <RadioGroup v-model=\"station.issub\">\r\n                                                    <Radio disabled label=\"0\">\r\n                                                        <span>否</span>\r\n                                                    </Radio>\r\n                                                    <Radio disabled label=\"1\">\r\n                                                        <span>是</span>\r\n                                                    </Radio>\r\n                                                </RadioGroup>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <!-- -->\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"对应母站房:\" prop=\"motherstationname\">\r\n                                                <cl-input disabled v-if=\"this.station.issub==='1'\" size=\"small\" readonly\r\n                                                          icon=\"ios-archive\" v-model=\"station.motherstationname\"\r\n                                                          placeholder=\"点击图标选择\"/>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                    </Row>\r\n                                    <Row>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"使用权:\" prop=\"useright\">\r\n                                                <cl-select disabled size=\"small\" v-model=\"station.useright\"\r\n                                                           category=\"useRight\" labelField=\"typeName\"\r\n                                                           valueField=\"typeCode\">\r\n                                                </cl-select>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"建筑结构:\" prop=\"buildingstructure\">\r\n                                                <cl-select disabled size=\"small\" v-model=\"station.buildingstructure\"\r\n                                                           category=\"buildingStructure\" labelField=\"typeName\"\r\n                                                           valueField=\"typeCode\">\r\n                                                </cl-select>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"责任人:\" prop=\"responsiblemanname\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=50\r\n                                                          v-model=\"station.responsiblemanname\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"面积:\" prop=\"area\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.area\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                    </Row>\r\n                                    <Row>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"状态:\" prop=\"status\">\r\n                                                <RadioGroup v-model=\"station.status\">\r\n                                                    <Radio disabled label=\"0\">\r\n                                                        <span>闲置</span>\r\n                                                    </Radio>\r\n                                                    <Radio disabled label=\"2\">\r\n                                                        <span>在用</span>\r\n                                                    </Radio>\r\n                                                    <Radio disabled label=\"1\">\r\n                                                        <span>停用</span>\r\n                                                    </Radio>\r\n                                                </RadioGroup>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"员工人数:\" prop=\"staffnumber\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.staffnumber\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"地址:\" prop=\"address\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.address\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"管理部门:\" prop=\"managedepartment\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.managedepartment\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                    </Row>\r\n                                    <Row>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"使用单位:\" prop=\"usedepartment\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.usedepartment\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"房屋价值:\" prop=\"houseprice\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.houseprice\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"是否共享:\" prop=\"isshare\">\r\n                                                <RadioGroup v-model=\"station.isshare\">\r\n                                                    <Radio disabled label=\"1\">\r\n                                                        <span>是</span>\r\n                                                    </Radio>\r\n                                                    <Radio disabled label=\"0\">\r\n                                                        <span>否</span>\r\n                                                    </Radio>\r\n                                                </RadioGroup>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"房产证号:\" prop=\"certificateno\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.certificateno\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n\r\n                                    </Row>\r\n                                    <Row>\r\n\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"电流:\" prop=\"electricity\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.electricity\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"环境:\" prop=\"environment\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.environment\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"共享单位名称:\" prop=\"sheredepartname\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=20\r\n                                                          v-model=\"station.shareName\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"是否达到申请大工业用电标准:\" prop=\"issatisfybigfactories\">\r\n                                                <RadioGroup v-model=\"station.issatisfybigfactories\">\r\n                                                    <Radio disabled label=\"1\">\r\n                                                        <span>是</span>\r\n                                                    </Radio>\r\n                                                    <Radio disabled label=\"0\">\r\n                                                        <span>否</span>\r\n                                                    </Radio>\r\n                                                </RadioGroup>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                    </Row>\r\n                                    <Row>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"是否申请大工业用电:\" prop=\"isaskbigfactories\">\r\n                                                <RadioGroup v-model=\"station.isaskbigfactories\">\r\n                                                    <Radio disabled label=\"1\">\r\n                                                        <span>是</span>\r\n                                                    </Radio>\r\n                                                    <Radio disabled label=\"0\">\r\n                                                        <span>否</span>\r\n                                                    </Radio>\r\n                                                </RadioGroup>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"是否有空调:\" prop=\"isaircondition\">\r\n                                                <RadioGroup v-model=\"station.isaircondition\">\r\n                                                    <Radio disabled label=\"1\">\r\n                                                        <span>是</span>\r\n                                                    </Radio>\r\n                                                    <Radio disabled label=\"0\">\r\n                                                        <span>否</span>\r\n                                                    </Radio>\r\n                                                </RadioGroup>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"是否电力局直接交易:\" prop=\"istradeelectric\">\r\n                                                <RadioGroup v-model=\"station.istradeelectric\">\r\n                                                    <Radio disabled label=\"1\">\r\n                                                        <span>是</span>\r\n                                                    </Radio>\r\n                                                    <Radio disabled label=\"0\">\r\n                                                        <span>否</span>\r\n                                                    </Radio>\r\n                                                </RadioGroup>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"直售电电价:\" prop=\"directsaleprice\">\r\n                                                <InputNumber readonly size=\"small\" :maxlength=20\r\n                                                             v-model=\"station.directsaleprice\"></InputNumber>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                    </Row>\r\n                                    <Row>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"是否大工业用电:\" prop=\"isbigfactories\">\r\n                                                <RadioGroup v-model=\"station.isbigfactories\">\r\n                                                    <Radio disabled label=\"1\">\r\n                                                        <span>是</span>\r\n                                                    </Radio>\r\n                                                    <Radio disabled label=\"0\">\r\n                                                        <span>否</span>\r\n                                                    </Radio>\r\n                                                </RadioGroup>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"大工业低谷电价:\" prop=\"bigfactoryvalleyprice\">\r\n                                                <InputNumber readonly size=\"small\" :maxlength=15\r\n                                                             v-model=\"station.bigfactoryvalleyprice\"></InputNumber>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"大工业平段电价:\" prop=\"bigfactoryplainprice\">\r\n                                                <InputNumber readonly size=\"small\" :maxlength=15\r\n                                                             v-model=\"station.bigfactoryplainprice\"></InputNumber>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"大工业高峰电价:\" prop=\"bigfactorypeakprice\">\r\n                                                <InputNumber readonly size=\"small\" :maxlength=15\r\n                                                             v-model=\"station.bigfactorypeakprice\"></InputNumber>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                    </Row>\r\n                                    <Row>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"是否一般工商用电:\" prop=\"isnormalbussuse\">\r\n                                                <RadioGroup v-model=\"station.isnormalbussuse\">\r\n                                                    <Radio disabled label=\"1\">\r\n                                                        <span>是</span>\r\n                                                    </Radio>\r\n                                                    <Radio disabled label=\"0\">\r\n                                                        <span>否</span>\r\n                                                    </Radio>\r\n                                                </RadioGroup>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"一般工商及其电价:\" prop=\"normalbusprice\">\r\n                                                <InputNumber readonly size=\"small\" :maxlength=15\r\n                                                             v-model=\"station.normalbusprice\"></InputNumber>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"变压器容量:\" prop=\"transformercapacity\">\r\n                                                <InputNumber readonly size=\"small\" :maxlength=15\r\n                                                             v-model=\"station.transformercapacity\"></InputNumber>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"变压器编号:\" prop=\"transformerno\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.transformerno\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                    </Row>\r\n                                    <Row>\r\n                                        <Col span=\"6\">\r\n                                            <FormItem label=\"功率因素:\" prop=\"powerfactor\">\r\n                                                <cl-input readonly size=\"small\" :maxlength=30\r\n                                                          v-model=\"station.powerfactor\"></cl-input>\r\n                                            </FormItem>\r\n                                        </Col>\r\n                                    </Row>\r\n                                </Form>\r\n                            </Row>\r\n                        </div>\r\n                    </Panel>\r\n                    <Panel name=\"Panel2\">关联电表信息\r\n                        <div slot=\"content\">\r\n                            <Table border :columns=\"ammeter.columns\" :data=\"ammeter.data\"></Table>\r\n                        </div>\r\n                    </Panel>\r\n                </Collapse>\r\n            </card>\r\n            <!--</div>-->\r\n            <div slot=\"footer\">\r\n                <Button type=\"text\" size=\"large\" @click=\"onModalCancel\">取消</Button>\r\n                <Button type=\"primary\" size=\"large\" @click=\"onModalCancel\">确认</Button>\r\n            </div>\r\n        </Modal>\r\n    </div>\r\n</template>\r\n<script>\r\n    import countryModal from \"./countryModal\";\r\n    import {getuser} from \"@/api/alertcontrol/alertcontrol\";\r\n    import {addstation, getAmmeterListByStation} from \"@/api/alertcontrol/alertcontrol\";\r\n    import {isStationnameExist} from \"@/api/alertcontrol/alertcontrol\";\r\n    import {blist, btext} from \"@/libs/tools\";\r\n    import {getstationold} from \"@/api/alertcontrol/alertcontrol\";\r\n\r\n    export default {\r\n        name: \"viewStation\",\r\n        components: {countryModal},\r\n        data() {\r\n            //状态\r\n            let renderStatus = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.status) {\r\n                    if (item.typeCode == params.row.status) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //电表类型\r\n            let renderAmmeterType = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.ammeterType) {\r\n                    if (item.typeCode == params.row.ammetertype) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //电表用途\r\n            let renderammeterUse = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.ammeterUse) {\r\n                    if (item.typeCode == params.row.ammeteruse) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            const validaname = (rule, value, callback) => {\r\n                if (value == null || value == '') {\r\n                    callback(new Error('不能为空'))\r\n                } else {\r\n                    var temp = {};\r\n                    temp.stationname = value;\r\n\r\n                    isStationnameExist(temp).then(res => {\r\n                        this.loading = false;\r\n\r\n                        if (res.data == true) {\r\n                            callback(new Error('局站名称已存在'))\r\n                        } else {\r\n                            callback()\r\n                        }\r\n                    });\r\n                }\r\n            };\r\n            const validatortype = (rule, value, callback) => {\r\n                if (value == null || value == '') {\r\n                    callback(new Error('不能为空'))\r\n                } else {\r\n                    callback()\r\n                }\r\n            };\r\n            const validamothername = (rule, value, callback) => {\r\n                if ((value == null || value == '') && this.station.issub == 1) {\r\n                    callback(new Error('不能为空'))\r\n                } else {\r\n                    callback()\r\n                }\r\n            };\r\n            const valisbigfactories = (rule, value, callback) => {\r\n\r\n                if ((value == null || value == '') && this.station.isbigfactories == 1) {\r\n                    callback(new Error('不能为空'))\r\n                } else if (value == 0) {\r\n                    callback(new Error('不能为0'))\r\n                } else {\r\n                    callback()\r\n                }\r\n            };\r\n            const valistationlevel = (rule, value, callback) => {\r\n\r\n                if ((value == null || value == '')) {\r\n                    callback(new Error('不能为空'))\r\n                } else {\r\n                    callback()\r\n                }\r\n            };\r\n\r\n            const validanormalbusprice = (rule, value, callback) => {\r\n\r\n                if ((value == null || value == '') && this.station.isnormalbussuse == 1) {\r\n                    callback(new Error('不能为空'))\r\n                } else if (value == 0) {\r\n                    callback(new Error('不能为0'))\r\n                } else {\r\n                    callback()\r\n                }\r\n            };\r\n            const validaresstationname = (rule, value, callback) => {\r\n\r\n                if ((value == null || value == '') && (this.station.stationtype == '10001' || this.station.stationtype == '10002' || this.station.stationtype == '10003' || this.station.stationtype == '10004' || this.station.stationtype == '10005')) {\r\n                    callback(new Error('不能为空'))\r\n                } else {\r\n                    callback()\r\n                }\r\n            };\r\n            return {\r\n                status: [],//状态\r\n                ammeterType: [],//电表类型\r\n                ammeterUse: [],//电表用途\r\n                stationLevel: {},\r\n                stationlevelcategory: '',//局站等级码表值\r\n                data1: '',\r\n                data2: '',\r\n                users: this.users1,\r\n                showModel: false,\r\n                companies: [],\r\n                countrylist: [],\r\n                company: '',//初始值\r\n                country: '',//初始值\r\n                ruleValidate: {\r\n                    stationname: [\r\n                        {required: true, validator: validaname, trigger: 'blur'}\r\n                    ],\r\n                    stationtype: [\r\n                        {required: true, validator: validatortype, trigger: 'blur'}\r\n                    ],\r\n                    stationcode: [\r\n                        {required: true, message: '不能为空', trigger: 'blur'}\r\n                    ],\r\n                    useway: [\r\n                        {required: true, validator: validatortype, trigger: 'blur'}\r\n                    ],\r\n                    propertyright: [\r\n                        {required: true, validator: validatortype, trigger: 'blur'}\r\n                    ],\r\n                    company: [\r\n                        {required: true, validator: validatortype, trigger: 'blur'}\r\n                    ],\r\n                    country: [\r\n                        {required: true, validator: validatortype, trigger: 'blur'}\r\n                    ],\r\n                    staffnumber: [\r\n                        {required: true, validator: validatortype, trigger: 'blur'}\r\n                    ],\r\n                    area: [\r\n                        {required: true, validator: validatortype, trigger: 'blur'}\r\n                    ],\r\n                    isbigfactories: [{required: true, validator: validatortype, trigger: 'blur'}\r\n                    ],\r\n                    motherstationname: [\r\n                        {required: true, validator: validamothername, trigger: 'blur'}\r\n                    ],\r\n                    costcentername: [\r\n                        {required: true, validator: validatortype, trigger: 'blur'}\r\n                    ],\r\n                    normalbusprice: [\r\n                        {required: false, validator: validanormalbusprice, trigger: 'blur'}\r\n                    ],\r\n                    resstationname: [\r\n                        {required: true, validator: validaresstationname, trigger: 'blur'}\r\n                    ],\r\n                    bigfactoryvalleyprice: [\r\n                        {required: false, validator: valisbigfactories, trigger: 'blur'}\r\n                    ],\r\n                    bigfactoryplainprice: [\r\n                        {required: false, validator: valisbigfactories, trigger: 'blur'}\r\n                    ],\r\n                    bigfactorypeakprice: [\r\n                        {required: false, validator: valisbigfactories, trigger: 'blur'}\r\n                    ],\r\n                    stationlevel: [\r\n                        {required: true, validator: valistationlevel, trigger: 'blur'}\r\n                    ]\r\n                },\r\n                station: {\r\n                    isbigfactories: '1',\r\n                    normalbuspriceflag: false,\r\n                    issub: null,\r\n                    motherstation: null,\r\n                    motherstationname: null,\r\n                    bigfactorypeakprice: null,\r\n                    normalbusprice: null,\r\n                    bigfactoryvalleyprice: null,\r\n                    // bigfactorypeakprice: null,\r\n                    bigfactoryplainprice: null,\r\n                    transformercapacity: []\r\n                },\r\n                ammeter: {\r\n                    columns: [\r\n                        /*{title: '电表编号', key: 'ammeterno', align: 'center'},*/\r\n                        {title: '项目名称', key: 'projectname', align: 'center'},\r\n                        {title: '电表户号/协议编号', key: 'ammetername', align: 'center'},\r\n                        {title: '所属分公司', key: 'companyName', align: 'center',},\r\n                        {title: '责任中心', key: 'countryName', align: 'center',},\r\n                        {title: '用电类型', key: 'electrotypename', align: 'center'},\r\n                        {title: '电表/协议类型', key: 'ammetertypename', align: 'center'},\r\n                        {title: '电表用途', key: 'ammeteruse', render: renderammeterUse, align: 'center'},\r\n                        {title: '状态', key: 'status', render: renderStatus, align: 'center'},\r\n                        {title: '创建时间', key: 'createTime', align: 'center'}\r\n                    ],\r\n                    data: []\r\n                }\r\n            }\r\n        },\r\n        props: {\r\n            /*showModel:false,*/\r\n            // users1: {type: [Array, Object], required: true},\r\n            // users:'users'\r\n        },\r\n        /*watch: {\r\n            data2: {\r\n                immediate: true,\r\n                handler(val) {\r\n                    this.station.motherstationname = val;\r\n                }\r\n            }\r\n        },*/\r\n        methods: {\r\n            /*stationtypeChange(v, param){\r\n                if(v=='10001'||v=='10001'||v=='10001'||v=='10001'||v=='10001'){\r\n                    this.stationlevelcategory='stationLevel1';\r\n                }else if(v=='20001'||v=='20002'||v=='-1'||v=='-2'){\r\n                    this.stationlevelcategory='stationLevel2';\r\n                }\r\n            },*/\r\n            motherStation() {\r\n                this.$refs.countryModal.choose(6);//责任中心\r\n            },\r\n            costCenter() {\r\n                this.$refs.countryModal.choose(7);//成本中心\r\n            },\r\n            resStation() {\r\n                this.$refs.countryModal.choose(8);//成本中心\r\n            },\r\n            getDataFromModal(data, flag) {\r\n\r\n                if (flag == 6) {\r\n                    this.data1 = data.id;\r\n                    this.data2 = data.name\r\n                    this.station.motherstation = this.data1;\r\n                    this.station.motherstationname = this.data2;\r\n                } else if (flag == 7) {\r\n                    this.data1 = data.id;\r\n                    this.data2 = data.name\r\n                    this.station.costcenter = this.data1;\r\n                    this.station.costcentername = this.data2;\r\n                } else if (flag == 8) {\r\n                    this.data1 = data.id;\r\n                    this.data2 = data.name\r\n                    this.station.resstationcode = this.data1;\r\n                    this.station.resstationname = this.data2;\r\n                }\r\n\r\n                /*this.station.motherstation = data.id;\r\n                this.station.motherstationname = data.name;*/\r\n            },\r\n            initData() {\r\n                this.$refs.stationForm.resetFields();\r\n                this.showModel = false;\r\n            },\r\n            onModalCancel() {\r\n                this.initData();\r\n                //this.station={};\r\n            },\r\n            onModalOK(stationForm) {\r\n                this.$refs['stationForm'].validate((valid) => {\r\n                    if (valid) {\r\n                        //请求后台保存数据\r\n\r\n                        var temp = this.station;\r\n                        this.station.useway = temp.useway;\r\n                        addstation(temp).then(res => {\r\n                            this.loading = false;\r\n\r\n                            if (res.data.flag == '1') {\r\n                                this.modal1 = false;\r\n                                this.$Message.success(res.data.msg);\r\n                            } else {\r\n                                this.modal1 = false;\r\n                                this.$Message.error(res.data.msg);\r\n                            }\r\n                        });\r\n                        this.showModel = false;\r\n                    }\r\n                });\r\n            },\r\n            initStation(id) {\r\n                //初始化\r\n                //获取真实数据/\r\n                getstationold(id).then(res => {\r\n                    console.log(res.data.sheredepartname == null, \"res.data.sheredepartname==null\");\r\n                    // debugger\r\n                    let sheredepartname1;\r\n                    if(res.data.sheredepartname == null || res.data.sheredepartname == undefined || res.data.sheredepartname == \"\")  {\r\n                        sheredepartname1 = [];\r\n                    } else{\r\n                        sheredepartname1 = res.data.sheredepartname;\r\n                        let shareName = [];\r\n                        sheredepartname1.forEach(item => {\r\n                            if(item == \"1\") {\r\n                                    shareName.push(\"移动\");\r\n                                }else if(item == \"2\") {\r\n                                    shareName.push(\"电信\");\r\n                                }else if (item == \"3\") {\r\n                                    shareName.push(\"联通\");\r\n                                }else if (item == \"4\") {\r\n                                    shareName.push(\"能源\");\r\n                                }else if (item == \"5\") {\r\n                                    shareName.push(\"拓展\");\r\n                                }else {\r\n                                    shareName.push(\"\");\r\n                                }\r\n                        })\r\n                        res.data.shareName = shareName.join(\",\");\r\n                    }\r\n                    this.station = res.data;\r\n                  console.log(this.station, \"this.station\")\r\n                    // Object.keys(this.station).forEach(key=>{\r\n                    //     this.station[key] = typeof this.station[key]=='number'?this.station[key]+'': this.station[key]\r\n                    // });\r\n                    // this.$forceUpdate()\r\n                });\r\n                //获取关联电表信息\r\n                getAmmeterListByStation(id).then(res => {\r\n                    this.ammeter.data = res.data.rows;\r\n                })\r\n                this.showModel = true;\r\n            }\r\n        }, mounted() {\r\n\r\n            this.stationLevel = {\r\n                stationLevelA: blist(\"stationLevelA\"),\r\n                stationLevelB: blist(\"stationLevelB\"),\r\n            };\r\n            this.status = blist(\"status\");//状态\r\n            this.ammeterType = blist(\"ammeterType\")//电表类型\r\n            this.ammeterUse = blist(\"ammeterUse\")//电表用途\r\n\r\n        }\r\n    }\r\n</script>\r\n"]}]}