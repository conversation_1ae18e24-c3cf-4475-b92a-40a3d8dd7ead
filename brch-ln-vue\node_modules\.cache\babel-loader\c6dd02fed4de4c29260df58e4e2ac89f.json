{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\sc\\addPylonAccountSC.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\sc\\addPylonAccountSC.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["addPylonAccountSC.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsxBA,SAAA,QAAA,QAAA,MAAA;AACA,SAAA,cAAA,EAAA,iBAAA,EAAA,qBAAA,QAAA,eAAA;AACA,SACA,mBADA,EAEA,cAFA,EAGA,OAHA,EAIA,SAJA,EAKA,YALA,EAMA,OANA,EAOA,cAPA,EAQA,iBARA,EASA,YATA,EAUA,oBAVA,QAWA,2BAXA;AAYA,SAAA,SAAA,EAAA,iBAAA,EAAA,WAAA,QAAA,2BAAA;AACA,SACA,QADA,EAEA,gBAFA,EAGA,UAHA,EAIA,WAJA,EAKA,YALA,EAMA,cANA,QAOA,mCAPA;AAQA,OAAA,KAAA,MAAA,oBAAA;AACA,SACA,iBADA,EAEA,WAFA,EAGA,eAHA,EAIA,yBAJA,EAKA,wBALA,EAMA,2BANA,EAOA,0BAPA,EAQA,aARA,EASA,mBATA,EAUA,oBAVA,EAWA,aAXA,EAYA,sBAZA,EAaA,+BAbA,EAcA,uBAdA,EAeA,8BAfA,EAgBA,sBAhBA,EAiBA,4BAjBA,EAkBA,sBAlBA,EAmBA,cAnBA,EAoBA,oBApBA,EAqBA,YArBA,EAsBA,cAtBA,EAuBA,QAvBA,EAwBA,YAxBA,EAyBA,YAzBA,EA0BA,aA1BA,EA2BA,gBA3BA,EA4BA,iBA5BA,EA6BA,gBA7BA,EA8BA,iBA9BA,EA+BA,oBA/BA,QAgCA,uCAhCA;AAiCA,OAAA,UAAA,MAAA,gCAAA;AACA,SAAA,SAAA,IAAA,UAAA,QAAA,sBAAA;AACA,SAAA,UAAA,QAAA,mDAAA;AACA,OAAA,KAAA,MAAA,cAAA;AACA,OAAA,gBAAA,MAAA,iCAAA;AACA,OAAA,SAAA,MAAA,gBAAA;AACA,SAAA,iBAAA,EAAA,SAAA,QAAA,eAAA;AACA,SAAA,KAAA,QAAA,cAAA;AACA,OAAA,eAAA,MAAA,gCAAA;AACA,OAAA,UAAA,MAAA,iCAAA;AACA,OAAA,WAAA,MAAA,kCAAA;AACA,OAAA,sBAAA,MAAA,6CAAA;AACA,SAAA,iBAAA,QAAA,gBAAA;AACA,OAAA,eAAA,MAAA,qBAAA;AACA,OAAA,OAAA,MAAA,kBAAA;AAEA,IAAA,KAAA,GAAA,QAAA,EAAA;AAEA,eAAA;AACA,EAAA,MAAA,EAAA,CAAA,eAAA,EAAA,OAAA,CADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,UAAA,EAAA,UADA;AAEA,IAAA,WAAA,EAAA,WAFA;AAGA,IAAA,sBAAA,EAAA,sBAHA;AAIA,IAAA,gBAAA,EAAA,gBAJA;AAKA,IAAA,UAAA,EAAA,UALA;AAMA,IAAA,eAAA,EAAA;AANA,GAFA;AAUA,EAAA,IAVA,kBAUA;AAAA;;AACA,QAAA,cAAA,GAAA,SAAA,cAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,YAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,6BAAA,KAAA,CAAA,SAAA,8HAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,QAAA,EAAA;AACA,YAAA,YAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,YAAA,CAAA;AACA,KATA;;AAUA,QAAA,SAAA,GAAA,SAAA,SAAA,CAAA,CAAA,QAAA;AAAA,UAAA,GAAA,QAAA,GAAA;AAAA,UAAA,KAAA,QAAA,KAAA;AAAA,UACA,GADA,GACA,GADA,CACA,GADA;AAAA,UACA,WADA,GACA,GADA,CACA,WADA;AAEA,UAAA,KAAA,GAAA,OAAA;AACA,UAAA,GAAA,GAAA,IAAA;;AACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,SAAA,IAAA,GAAA,EAAA;AACA,UAAA,KAAA,GAAA,KAAA;AACA,UAAA,GAAA,GAAA,qBAAA;AACA;AACA;;AAEA,aAAA,CAAA,CACA,SADA,EAEA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,KAAA,EAAA;AADA,SADA;AAIA,QAAA,KAAA,EAAA;AACA,UAAA,SAAA,EAAA;AADA;AAJA,OAFA,EAUA,CACA,WADA,EAEA,CAAA,CACA,MADA,EAEA;AACA,QAAA,IAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA;AACA,UAAA,UAAA,EAAA,QADA;AAEA,UAAA,SAAA,EAAA;AAFA;AAFA,OAFA,EASA,GATA,CAFA,CAVA,CAAA;AAyBA,KApCA;;AAqCA,QAAA,sBAAA,GAAA,SAAA,sBAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,gBAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,8BAAA,KAAA,CAAA,iBAAA,mIAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,gBAAA,EAAA;AACA,YAAA,gBAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,gBAAA,CAAA;AACA,KATA;;AAUA,QAAA,KAAA,GAAA,SAAA,KAAA,CAAA,CAAA,SAAA;AAAA,UAAA,GAAA,SAAA,GAAA;AAAA,UAAA,KAAA,SAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA;AACA,UAAA,GAAA,GAAA,EAAA;;AACA,UAAA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,IAAA,IAAA,EAAA;AACA,QAAA,GAAA,GAAA,IAAA;AACA;;AACA,aAAA,CAAA,CAAA,KAAA,EAAA,CACA,CAAA,CACA,GADA,EAEA;AACA,QAAA,EAAA,EAAA;AACA,UAAA,KADA,mBACA;AACA;AACA,gBAAA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,IAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,GAAA;AACA;AACA;AANA;AADA,OAFA,EAYA,GAZA,CADA,CAAA,CAAA;AAgBA,KAtBA;;AAuBA,WAAA;AACA,MAAA,aAAA,EAAA,CADA;AAEA,MAAA,GAAA,EAAA,IAFA;AAGA,MAAA,OAAA,EAAA,CAHA;AAIA,MAAA,MAAA,EAAA,KAJA;AAKA,MAAA,IAAA,EAAA,EALA;AAMA,MAAA,OAAA,EAAA,IANA;AAOA,MAAA,MAAA,EAAA,CAPA;AAQA,MAAA,eAAA,EAAA,EARA;AASA;AAEA;AACA,MAAA,OAAA,EAAA,EAZA;AAaA,MAAA,MAAA,EAAA,EAbA;AAcA,MAAA,OAAA,EAAA,EAdA;AAeA,MAAA,UAAA,EAAA,EAfA;AAgBA,MAAA,cAAA,EAAA,KAhBA;AAiBA,MAAA,cAAA,EAAA,KAjBA;AAkBA,MAAA,WAAA,EAAA,KAlBA;AAmBA,MAAA,OAAA,EAAA,SAAA,CAAA,OAnBA;AAoBA,MAAA,SAAA,EAAA,IApBA;AAqBA,MAAA,WAAA,EAAA,CAAA,EAAA,CArBA;AAsBA,MAAA,aAAA,EAAA,UAtBA;AAuBA,MAAA,SAAA,EAAA,CAvBA;AAwBA,MAAA,QAAA,EAAA,KAxBA;AAyBA,MAAA,SAAA,EAAA,EAzBA;AAyBA;AACA,MAAA,QAAA,EAAA,KA1BA;AA0BA;AACA,MAAA,UAAA,EAAA,IA3BA;AA2BA;AACA,MAAA,UAAA,EAAA,KA5BA;AA4BA;AACA,MAAA,OAAA,EAAA,KA7BA;AA6BA;AACA,MAAA,OAAA,EAAA,KA9BA;AA8BA;AACA,MAAA,SAAA,EAAA,KA/BA;AA+BA;AACA,MAAA,QAAA,EAAA,KAhCA;AAgCA;AACA,MAAA,SAAA,EAAA,CAAA,CAjCA;AAiCA;AACA,MAAA,YAAA,EAAA,CAAA,CAlCA;AAkCA;AACA,MAAA,kBAAA,EAAA,EAnCA;AAmCA;AACA,MAAA,eAAA,EAAA,EApCA;AAoCA;AACA,MAAA,iBAAA,EAAA,EArCA;AAsCA,MAAA,aAAA,EAAA,EAtCA;AAuCA,MAAA,WAAA,EAAA,EAvCA;AAwCA,MAAA,qBAAA,EAAA,EAxCA;AAyCA,MAAA,oBAAA,EAAA,EAzCA;AA0CA,MAAA,qBAAA,EAAA,EA1CA;AA2CA,MAAA,kBAAA,EAAA,EA3CA;AA4CA,MAAA,eAAA,EAAA,EA5CA;AA6CA,MAAA,mBAAA,EAAA,EA7CA;AA8CA,MAAA,eAAA,EAAA,EA9CA;AA+CA,MAAA,WAAA,EAAA,EA/CA;AAgDA,MAAA,WAAA,EAAA,EAhDA;AAiDA,MAAA,UAAA,EAAA,EAjDA;AAkDA,MAAA,WAAA,EAAA,EAlDA;AAmDA,MAAA,oBAAA,EAAA,CAnDA;AAoDA,MAAA,oBAAA,EAAA,CApDA;AAqDA,MAAA,mBAAA,EAAA,CArDA;AAsDA,MAAA,mBAAA,EAAA,CAtDA;AAuDA,MAAA,mBAAA,EAAA,CAvDA;AAwDA,MAAA,kBAAA,EAAA,CAxDA;AAyDA,MAAA,gBAAA,EAAA,CAzDA;AA0DA,MAAA,gBAAA,EAAA,CA1DA;AA2DA,MAAA,eAAA,EAAA,CA3DA;AA4DA,MAAA,OAAA,EAAA,EA5DA;AA6DA,MAAA,aAAA,EAAA,EA7DA;AA8DA,MAAA,WAAA,EAAA,EA9DA;AA+DA,MAAA,eAAA,EAAA,EA/DA;AAgEA,MAAA,iBAAA,EAAA,EAhEA;AAiEA,MAAA,SAAA,EAAA,EAjEA;AAkEA,MAAA,UAAA,EAAA,EAlEA;AAkEA;AACA,MAAA,eAAA,EAAA,EAnEA;AAmEA;AACA,MAAA,aAAA,EAAA,IApEA;AAqEA,MAAA,OAAA,EAAA,EArEA;AAqEA;AACA,MAAA,SAAA,EAAA,CAtEA;AAuEA,MAAA,OAAA,EAAA,CAvEA;AAwEA,MAAA,QAAA,EAAA,EAxEA;AAwEA;AACA,MAAA,UAAA,EAAA,EAzEA;AA0EA,MAAA,UAAA,EAAA,KA1EA;AA2EA,MAAA,WAAA,EAAA,IA3EA;AA4EA,MAAA,YAAA,EAAA,EA5EA;AA6EA,MAAA,MAAA,EAAA;AACA,QAAA,GAAA,EAAA,KADA;AACA;AACA,QAAA,IAAA,EAAA,EAFA;AAEA;AACA,QAAA,SAAA,EAAA,CAHA;AAGA;AACA,QAAA,WAAA,EAAA,CAJA;AAIA;AACA,QAAA,OAAA,EAAA,CALA;AAMA,QAAA,IAAA,EAAA;AANA,OA7EA;AAqFA,MAAA,UAAA,EAAA;AACA,QAAA,uBAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IAFA;AAEA;AACA,QAAA,WAAA,EAAA,EAHA;AAGA;AACA,QAAA,WAAA,EAAA,EAJA;AAIA;AACA,QAAA,iBAAA,EAAA,IALA;AAKA;AACA,QAAA,WAAA,EAAA,EANA;AAMA;AACA,QAAA,MAAA,EAAA,EAPA;AAOA;AACA,QAAA,OAAA,EAAA,EARA;AASA,QAAA,OAAA,EAAA,EATA;AAUA,QAAA,WAAA,EAAA,EAVA;AAUA;AACA,QAAA,WAAA,EAAA,GAXA;AAWA;AACA,QAAA,MAAA,EAAA,EAZA;AAaA,QAAA,OAAA,EAAA,EAbA;AAcA,QAAA,gBAAA,EAAA;AAdA,OArFA;AAqGA,MAAA,QAAA,EAAA,EArGA;AAqGA;AACA,MAAA,QAAA,EAAA,CAtGA;AAuGA,MAAA,WAAA,EAAA,CAvGA;AAwGA,MAAA,SAAA,EAAA;AACA,QAAA,OAAA,EAAA,KADA;AAEA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,EAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,GAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,gBAAA,IAAA,GAAA,KAAA;AACA,mBAAA,CAAA,CACA,QADA,EAEA;AACA,cAAA,KAAA,EAAA;AACA,gBAAA,IAAA,EAAA,SADA;AAEA,gBAAA,IAAA,EAAA;AAFA,eADA;AAKA,cAAA,KAAA,EAAA;AACA,6BAAA;AADA,eALA;AAQA,cAAA,EAAA,EAAA;AACA,gBAAA,KADA,mBACA;AACA,kBAAA,IAAA,CAAA,KAAA,CAAA,qBAAA,CAAA,IAAA,GAAA,MAAA,CAAA,GAAA,CAAA,IAAA;AACA,kBAAA,IAAA,CAAA,cAAA,GAAA,IAAA;AACA;AAJA;AARA,aAFA,EAiBA,SAjBA,CAAA;AAmBA,WA1BA;AA2BA,UAAA,QAAA,EAAA,GA3BA;AA4BA,UAAA,QAAA,EAAA;AA5BA,SAFA,EAgCA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAhCA,EAiCA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA,MALA;AAMA,UAAA,MAAA,EAAA;AANA,SAjCA,EAyCA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAzCA,EAgDA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA,yBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAhDA,EAuDA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAvDA,EAwDA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA,iBAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAxDA,EAyDA;AACA,UAAA,KAAA,EAAA,aADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAzDA,EA+DA;AACA,UAAA,KAAA,EAAA,aADA;AAEA,UAAA,GAAA,EAAA,gBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA/DA,EAqEA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,GAAA,EAAA,mBAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SArEA,EAsEA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAtEA,EAuEA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,cAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAvEA,EA8EA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA,eAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA9EA,EA+EA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA,eAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA/EA,EAgFA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,GAAA,EAAA,oBAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAhFA,EAiFA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,MAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAjFA,EAwFA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,SAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAxFA,EA+FA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,mBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SA/FA,EAsGA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,kBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAtGA,EA6GA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,mBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SA7GA,EAoHA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,IAAA,EAAA,iBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SApHA,EA2HA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SA3HA,EAkIA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,IAAA,EAAA,qBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAlIA,EAyIA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,IAAA,EAAA,kBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAzIA,EAgJA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,IAAA,EAAA,iBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAhJA,EAuJA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,SAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAvJA,EA8JA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA9JA,EA+JA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SA/JA,EAsKA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,GAAA,EAAA,cAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAtKA,EA6KA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA7KA,EA8KA;AAAA,UAAA,KAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,GAAA,EAAA,SAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA9KA,EA+KA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,GAAA,EAAA,0BAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA/KA,EAqLA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,GAAA,EAAA,0BAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SArLA,EA2LA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,GAAA,EAAA,0BAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA3LA,EAiMA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,GAAA,EAAA,0BAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAjMA,EAuMA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,GAAA,EAAA,kBAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAvMA,CAFA;AAiNA,QAAA,UAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA,MALA;AAMA,UAAA,MAAA,EAAA;AANA,SADA,CAjNA;AA2NA,QAAA,IAAA,EAAA,EA3NA;AA4NA,QAAA,aAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,YAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA,eAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAXA,EAYA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAZA,EAaA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAbA,EAcA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAdA,EAeA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAfA,EAgBA;AAAA,UAAA,KAAA,EAAA,WAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAhBA,EAiBA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAjBA,EAkBA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAlBA,EAmBA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAnBA,EAoBA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SApBA,EAqBA;AAAA,UAAA,KAAA,EAAA,WAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SArBA,EAsBA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAtBA,EAuBA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAvBA;AA5NA;AAxGA,KAAA;AA+VA,GA1bA;AA2bA,EAAA,OA3bA,qBA2bA;AAAA;;AACA,SAAA,YAAA,GADA,CACA;;AAEA,SAAA,UAAA,CAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,SAAA,SAAA,GAAA,KAAA,CAAA,iBAAA,CAAA;AACA,SAAA,iBAAA,GAAA,KAAA,CAAA,kBAAA,CAAA;AAEA,IAAA,OAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,SAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;AACA,QAAA,MAAA,CAAA,eAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,CAFA,CAGA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,OAAA,GAAA,MAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EAAA;AACA,QAAA,cAAA,CAAA,MAAA,CAAA,UAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA;AACA,UAAA,MAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,EAAA;;AAEA,UAAA,MAAA,CAAA,kBAAA;AACA,SANA;AAOA,OAbA,CAcA;;;AACA,MAAA,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,MAAA,CAAA,SAAA,CAAA,OAAA,CAAA,MAAA,CAAA,MAAA,CAAA,SAAA,CAAA,UAAA,CAAA;AACA,KAhBA;AAkBA,IAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,MAAA,MAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,IAAA;AACA,KAHA;AAIA,GAxdA;AAydA,EAAA,QAAA,oBACA,QAAA,CAAA;AACA,IAAA,OAAA,EAAA,iBAAA,KAAA;AAAA,aAAA,KAAA,CAAA,IAAA,CAAA,OAAA;AAAA;AADA,GAAA,CADA,CAzdA;AA8dA,EAAA,OAAA,EAAA;AACA,IAAA,WADA,uBACA,CADA,EACA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,CAAA;AACA,KAHA;AAIA,IAAA,YAJA,wBAIA,CAJA,EAIA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,CAAA;AACA,KANA;AAOA,IAAA,YAPA,wBAOA,SAPA,EAOA;AACA,UAAA,IAAA,GAAA,EAAA;AACA,WAAA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA;AACA,QAAA,SAAA,CAAA,GAAA,CAAA,UAAA,KAAA,EAAA;AACA,cAAA,KAAA,IAAA,KAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,SAJA;AAKA,OANA;AAOA,WAAA,MAAA,GAAA,IAAA;AACA,KAjBA;AAkBA,IAAA,aAlBA,yBAkBA,IAlBA,EAkBA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KApBA;AAqBA,IAAA,iBArBA,6BAqBA,IArBA,EAqBA;AAAA;;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA,OAFA;AAGA,UAAA,KAAA,GAAA;AACA,QAAA,KAAA,EAAA;AADA,OAAA;AAGA,MAAA,qBAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,MAAA,CAAA,eAAA,GAAA,IAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,eAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,SAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AAEA,cAAA,IAAA,CAAA,MAAA,IAAA,IAAA,EAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAA,IAAA,CAAA,gBAAA,CAAA,cAAA,IAAA,GAAA,EAAA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA,EADA,CACA;;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,gBAAA,IAAA,CAAA,gBAAA,CAAA,iBAAA,IAAA,GAAA,EAAA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA,EADA,CACA;;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,gBACA,IAAA,CAAA,gBAAA,CAAA,kBAAA,IAAA,GAAA,IACA,IAAA,CAAA,gBAAA,CAAA,kBAAA,IAAA,GADA,IAEA,IAAA,CAAA,gBAAA,CAAA,qBAAA,IAAA,GAFA,IAGA,IAAA,CAAA,gBAAA,CAAA,aAAA,IAAA,GAHA,IAIA;AACA,YAAA,IAAA,CAAA,gBAAA,CAAA,kBAAA,IAAA,GALA,IAMA,IAAA,CAAA,gBAAA,CAAA,mBAAA,IAAA,GAPA,EAQA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA,EADA,CACA;;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA,aA5BA,CA6BA;;AACA,WA9BA,MA8BA;AACA,iBACA;AACA;AACA,YAAA,IAAA,CAAA,gBAAA,CAAA,eAAA,IAAA,GAHA,CAGA;AAHA,cAIA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA,eAPA,MAOA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;AACA;;AACA,UAAA,OAAA,CAAA,GAAA,CACA,MAAA,CAAA,eAAA,CAAA,MAAA,GAAA,CAAA,GAAA,IAAA,CAAA,MAAA,GAAA,CADA,EAEA,6CAFA;;AAIA,cAAA,MAAA,CAAA,eAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,eAAA,CAAA,MAAA,CAAA,eAAA,CAAA,MAAA,GAAA,CAAA,EAAA,QAAA,GACA,MAAA,CAAA,eAAA,CAAA,MAAA,GAAA,CAAA,GAAA,IAAA,CAAA,MAAA,GAAA,CADA;AAEA;;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,GAAA,GAAA;;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA;AACA,SAzDA;AA0DA,OA5DA;AA6DA,KA1FA;AA2FA,IAAA,IA3FA,gBA2FA,KA3FA,EA2FA;AACA,UAAA,IAAA,GAAA,IAAA,CADA,CAEA;;AACA,UAAA,KAAA,IAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,IAAA,GAAA,QAAA;AACA,aAAA,OAAA,GAAA,IAAA,CAAA,MAAA;AACA,aAAA,iBAAA,CAAA,IAAA,CAAA,MAAA;AACA,OALA,MAKA;AACA,YAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,cAAA,CAAA,IAAA,CAAA,EAAA;AACA,iBAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EAAA;AACA;;AACA,YAAA,KAAA,IAAA,IAAA,SAAA,EAAA;AACA,cAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,IAAA,CAAA,EAAA;AACA,iBAAA,WAAA;AACA,WAFA,MAEA;AACA,iBAAA,eAAA;AACA;AACA,SANA,MAMA,IAAA,KAAA,IAAA,IAAA,KAAA,EAAA;AACA,cAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,IAAA,CAAA,EAAA;AACA,iBAAA,WAAA;AACA,WAFA,MAEA;AACA,iBAAA,kBAAA;AACA;AACA;AACA;AACA,KArHA;AAsHA,IAAA,OAtHA,mBAsHA,CAtHA,EAsHA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,IAAA,GAAA,CAAA;;AACA,UAAA,KAAA,KAAA,CAAA,cAAA,CAAA,IAAA,IAAA,KAAA,EAAA;AACA,aAAA,OAAA;AACA,aAAA,GAAA,GAAA,IAAA;AACA,OAHA,MAGA,IAAA,KAAA,KAAA,CAAA,cAAA,CAAA,IAAA,IAAA,IAAA,EAAA;AACA,aAAA,OAAA,GAAA,CAAA;AACA,aAAA,GAAA,GAAA,KAAA;AACA;;AACA,UAAA,KAAA,GAAA,IAAA,KAAA,OAAA,GAAA,EAAA,EAAA;AACA,aAAA,OAAA,CAAA,CAAA;AACA;AACA,KAlIA;AAmIA,IAAA,SAnIA,uBAmIA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,IAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,SAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,YAAA,GAAA,CAAA;AACA,KApJA;AAqJA,IAAA,UArJA,wBAqJA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,IAAA,GAAA,KAAA;AACA,KAxJA;AAyJA,IAAA,WAzJA,yBAyJA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,KA3JA;AA4JA,IAAA,UA5JA,wBA4JA,CAAA,CA5JA;AA6JA,IAAA,YA7JA,0BA6JA;AAAA;;AACA,MAAA,cAAA,CAAA,KAAA,UAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,MAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,EAAA;AACA,OAJA;AAKA,KAnKA;AAoKA;AACA,IAAA,QAAA,EAAA,kBAAA,SAAA,EAAA,SAAA,EAAA,OAAA,EAAA,QAAA,EAAA;AACA,UAAA,SAAA,IAAA,SAAA,IAAA,OAAA,EAAA;AACA,QAAA,cAAA,CAAA,SAAA,EAAA,SAAA,EAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,QAAA,EAAA,QAAA,CAAA,GAAA,CAAA,CAAA,KACA,QAAA;AACA,SAHA;AAIA;AACA,KA5KA;AA6KA;AACA,IAAA,MAAA,EAAA,gBAAA,IAAA,EAAA,QAAA,EAAA;AACA,MAAA,YAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,QAAA,EAAA,QAAA,CAAA,GAAA,CAAA,CAAA,KACA,QAAA;AACA,OAHA;AAIA,KAnLA;AAoLA;AACA,IAAA,UArLA,sBAqLA,KArLA,EAqLA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA,KAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAAA;AANA,SAAA;AAQA;;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KA3MA;AA4MA;AACA,IAAA,cA7MA,0BA6MA,KA7MA,EA6MA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA,KAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAAA;AANA,SAAA;AAQA;;AACA,WAAA,QAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KAnOA;AAoOA;AACA,IAAA,kBArOA,gCAqOA;AAAA;;AACA,WAAA,eAAA;AACA,UAAA,MAAA,GAAA,KAAA,UAAA;AACA,MAAA,MAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA,MAAA,MAAA,CAAA,QAAA,GAAA,KAAA,QAAA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,mCADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,UAAA,KAAA,GAAA,EAAA;AACA,UAAA,MAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,KAAA,CACA,OADA,CACA,GADA,EAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,EAAA;AACA,UAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,EAFA,CAEA;;AACA,UAAA,YAAA,CAAA,MAAA,CAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,gBAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,YAAA,QAAA,CAAA,KAAA,GAAA,IAAA;AACA,YAAA,QAAA,CAAA,WAAA,GAAA,IAAA;AACA,YAAA,QAAA,CAAA,SAAA,GAAA,IAAA;AACA,YAAA,KAAA,CAAA,IAAA,CAAA,QAAA;AACA,WAPA;AAQA,UAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,IAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,CAAA,CAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,CAAA,CAAA;AACA;AACA,OAtBA,EAuBA,KAvBA,CAuBA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OAzBA;AA0BA,KA5QA;AA6QA;AACA,IAAA,QA9QA,oBA8QA,KA9QA,EA8QA;AACA,UAAA,eAAA,GAAA,CAAA;AACA,UAAA,iBAAA,GAAA,CAAA;AACA,UAAA,WAAA,GAAA,CAAA;AACA,UAAA,cAAA,GAAA,CAAA;AACA,UAAA,SAAA,GAAA,CAAA;AACA,UAAA,WAAA,GAAA,CAAA;AACA,UAAA,YAAA,GAAA,CAAA;AACA,UAAA,mBAAA,GAAA,CAAA;AACA,UAAA,gBAAA,GAAA,CAAA;AACA,UAAA,eAAA,GAAA,CAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,KAAA,CAAA,EAAA;AACA,UAAA,eAAA,IAAA,IAAA,CAAA,eAAA;AACA,UAAA,iBAAA,IAAA,IAAA,CAAA,iBAAA;AACA,UAAA,WAAA,IAAA,IAAA,CAAA,WAAA;AACA,UAAA,cAAA,IAAA,IAAA,CAAA,cAAA;AACA,UAAA,SAAA,IAAA,IAAA,CAAA,SAAA;AACA,UAAA,mBAAA,IAAA,IAAA,CAAA,mBAAA;AACA,UAAA,gBAAA,IAAA,IAAA,CAAA,gBAAA;AACA,UAAA,WAAA,IAAA,IAAA,CAAA,WAAA;AACA,UAAA,YAAA,IAAA,IAAA,CAAA,YAAA;AACA,UAAA,eAAA,IAAA,IAAA,CAAA,eAAA;AACA;AACA,OAbA;AAcA,aAAA;AACA,QAAA,eAAA,EAAA,eADA;AAEA,QAAA,iBAAA,EAAA,iBAFA;AAGA,QAAA,WAAA,EAAA,WAAA,CAAA,OAAA,CAAA,CAAA,CAHA;AAIA,QAAA,cAAA,EAAA,cAAA,CAAA,OAAA,CAAA,CAAA,CAJA;AAKA,QAAA,SAAA,EAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CALA;AAMA,QAAA,mBAAA,EAAA,mBAAA,CAAA,OAAA,CAAA,CAAA,CANA;AAOA,QAAA,gBAAA,EAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAPA;AAQA,QAAA,WAAA,EAAA,WAAA,CAAA,OAAA,CAAA,CAAA,CARA;AASA,QAAA,YAAA,EAAA,YAAA,CAAA,OAAA,CAAA,CAAA,CATA;AAUA,QAAA,eAAA,EAAA,eAAA,CAAA,OAAA,CAAA,CAAA,CAVA;AAWA,QAAA,KAAA,EAAA,IAXA;AAYA,QAAA,WAAA,EAAA,IAZA;AAaA,QAAA,SAAA,EAAA;AAbA,OAAA;AAeA,KAtTA;AAuTA,IAAA,UAvTA,wBAuTA;AACA,WAAA,OAAA,GAAA,CAAA;AACA,WAAA,kBAAA;AACA,KA1TA;AA2TA,IAAA,aA3TA,2BA2TA;AACA,WAAA,UAAA,GAAA;AACA,QAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IADA;AACA;AACA,QAAA,UAAA,EAAA,EAFA;AAEA;AACA,QAAA,WAAA,EAAA,EAHA;AAGA;AACA,QAAA,iBAAA,EAAA,IAJA;AAIA;AACA,QAAA,WAAA,EAAA,EALA;AAKA;AACA,QAAA,QAAA,EAAA,EANA;AAMA;AACA,QAAA,OAAA,EAAA,KAAA,WAAA,CAAA,CAAA,EAAA,EAPA;AAQA,QAAA,MAAA,EAAA,EARA;AASA,QAAA,WAAA,EAAA,GATA;AASA;AACA,QAAA,uBAAA,EAAA;AAVA,OAAA;AAYA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,eAAA,GAAA,EAAA;AACA,WAAA,YAAA;AACA,WAAA,kBAAA;AACA,KA5UA;AA6UA;AACA,IAAA,WA9UA,uBA8UA,IA9UA,EA8UA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAA,iBAAA;AAEA,QAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,aAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAA,OAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,gBAAA;AACA,QAAA,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAA,iBAAA;AACA,QAAA,IAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,mBAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,IAAA,CAAA,gBAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,IAAA,CAAA,WAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,gBAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,gBAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAA,eAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAA,eAAA;AAEA,QAAA,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAA,eAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAA,eAAA;AACA,QAAA,IAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,cAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,gBAAA;AAEA,QAAA,IAAA,CAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,KAAA,GAAA,oBAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,CAAA,IAAA,GAAA,mBAAA,CAAA,IAAA,CAAA;AACA,YAAA,CAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,MAAA,GAAA,EAAA;AACA,YAAA,CAAA,IAAA,CAAA,EAAA,EAAA,IAAA,CAAA,EAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,iBAAA,GAAA,WAAA,CAAA,IAAA,CAAA,iBAAA,CAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,WAAA,CAAA,IAAA,CAAA,mBAAA,CAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,WAAA,CAAA,IAAA,CAAA,gBAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,WAAA,CAAA,IAAA,CAAA,cAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,IAAA,CAAA,WAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,IAAA,CAAA,WAAA,CAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,WAAA,CAAA,IAAA,CAAA,eAAA,CAAA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,WAAA,CAAA,IAAA,CAAA,YAAA,CAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,WAAA,CAAA,IAAA,CAAA,eAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,WAAA,CAAA,IAAA,CAAA,SAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,WAAA,CAAA,IAAA,CAAA,SAAA,CAAA;;AACA,YAAA,CAAA,IAAA,CAAA,OAAA,IAAA,IAAA,IAAA,IAAA,CAAA,OAAA,IAAA,CAAA,KAAA,IAAA,CAAA,KAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;AACA,OAzCA;AA0CA,KAzXA;AA0XA;AACA,IAAA,YA3XA,wBA2XA,GA3XA,EA2XA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA,EAAA,KAAA;AACA,MAAA,GAAA,CAAA,eAAA,GAAA,sBAAA,CAAA,GAAA,CAAA;AACA,MAAA,GAAA,CAAA,iBAAA,GAAA,uBAAA,CAAA,GAAA,CAAA,CAHA,CAIA;;AACA,UAAA,GAAA,CAAA,cAAA,EAAA;AACA,QAAA,GAAA,CAAA,YAAA,GAAA,iBAAA,CAAA,GAAA,CAAA;AACA,QAAA,GAAA,CAAA,SAAA,GAAA,iBAAA,CAAA,GAAA,CAAA,CAFA,CAGA;AACA;AACA;AACA;;AACA,UAAA,GAAA,CAAA,WAAA,EAAA;AACA,QAAA,GAAA,CAAA,YAAA,GAAA,iBAAA,CAAA,GAAA,CAAA;AACA,QAAA,GAAA,CAAA,SAAA,GAAA,iBAAA,CAAA,GAAA,CAAA,CAFA,CAGA;AACA;AACA;;AACA,MAAA,GAAA,CAAA,kBAAA,GAAA,4BAAA,CAAA,GAAA,CAAA;;AACA,UAAA,GAAA,CAAA,eAAA,IAAA,CAAA,IAAA,GAAA,CAAA,KAAA,IAAA,CAAA,EAAA;AACA,YAAA,GAAA,CAAA,YAAA,GAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,iBAAA,GACA,UAAA,CAAA,GAAA,CAAA,iBAAA,CAAA,GAAA,IAAA,CAAA,GAAA,CAAA,GAAA,CAAA,YAAA,CADA;AAEA,UAAA,GAAA,CAAA,SAAA,GAAA,oBAAA,CAAA,GAAA,CAAA;AACA;;AAEA,YAAA,MAAA,GAAA,GAAA,CAAA,MAAA;;AACA,YAAA,MAAA,CAAA,OAAA,CAAA,IAAA,KAAA,CAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,IAAA,gBAAA,GAAA,CAAA,YAAA,GAAA,IAAA;AACA;AACA;AACA,KA1ZA;AA2ZA;AACA,IAAA,QA5ZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6ZA,gBAAA,KA7ZA,GA6ZA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EA7ZA;AA8ZA,qBAAA,KAAA,CAAA,cAAA,CAAA,MAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;AACA,gBAAA,CA/ZA,GA+ZA,KA/ZA;AAgaA,gBAAA,IAhaA,GAgaA,KAAA,UAhaA;AAiaA,gBAAA,KAjaA,GAiaA,EAjaA;AAkaA,gBAAA,IAlaA,GAkaA,IAlaA;AAmaA,gBAAA,OAnaA,GAmaA,SAAA,CAAA,OAnaA;AAoaA,gBAAA,CApaA,GAoaA,CApaA;;AAAA;AAAA,sBAoaA,CAAA,GAAA,KAAA,CAAA,MApaA;AAAA;AAAA;AAAA;;AAAA,sBAqaA,KAAA,CAAA,CAAA,CAAA,CAAA,QAAA,IAAA,CAraA;AAAA;AAAA;AAAA;;AAsaA,oBAAA,QAAA,OAAA,IAAA,KAAA,CAAA,CAAA,CAAA,CAAA,WAAA,IAAA,KAAA,CAAA,CAAA,CAAA,CAAA,WAAA,GAAA,IAAA,EAAA;AACA,sBACA,KAAA,CAAA,CAAA,CAAA,CAAA,cAAA,IAAA,IAAA,IACA,KAAA,CAAA,CAAA,CAAA,CAAA,cAAA,IAAA,SADA,IAEA,KAAA,CAAA,CAAA,CAAA,CAAA,cAAA,IAAA,EAHA,EAIA;AACA,yBAAA,SAAA,CACA,aACA,KAAA,CAAA,CAAA,CAAA,CAAA,WADA,GAEA,SAFA,GAGA,KAAA,CAAA,CAAA,CAAA,CAAA,WAHA,GAIA,8CALA;AAOA;AACA,iBApbA,CAqbA;;;AArbA;AAAA,uBAsbA,KAAA,aAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAtbA;;AAAA;AAsbA,gBAAA,MAtbA;;AAAA,qBAubA,MAvbA;AAAA;AAAA;AAAA;;AAwbA,qBAAA,SAAA,CAAA,MAAA;AAxbA;;AAAA;AA2bA,gBAAA,SA3bA,GA2bA,QAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CA3bA,EA2bA;;AACA,oBAAA,SAAA,IAAA,IAAA,IAAA,SAAA,GAAA,CAAA,EAAA;AACA,sBAAA,KAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,GAAA,SAAA,EAAA;AACA,oBAAA,IAAA,CAAA,SAAA,CAAA,iBAAA,SAAA;AACA,mBAFA,MAEA;AACA,oBAAA,CAAA,GAAA,IAAA;AACA,oBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA;AACA,iBAPA,MAOA;AACA,kBAAA,CAAA,GAAA,IAAA;AACA,kBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA;;AAtcA;AAoaA,gBAAA,CAAA,EApaA;AAAA;AAAA;;AAAA;AAycA,oBAAA,CAAA,EAAA;AACA,uBAAA,UAAA,CAAA,KAAA;AACA,iBAFA,MAEA;AACA,uBAAA,SAAA,CAAA,SAAA;AACA;;AA7cA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA+cA,IAAA,UA/cA,wBA+cA;AACA,WAAA,KAAA,CAAA,WAAA,CAAA,UAAA,GAAA,KAAA,UAAA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,KAldA;AAmdA;AACA,IAAA,UApdA,sBAodA,IApdA,EAodA;AAAA;;AACA,UAAA,CAAA,GAAA,EAAA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,EAAA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,GAAA,CAAA;AACA,YAAA,UAAA,GAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,UAAA,CAAA,SAAA;AACA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,GAAA,GAAA,sBAAA,CAAA,IAAA,CAAA;;AACA,cAAA,GAAA,CAAA,MAAA,EAAA;AACA,gBAAA,QAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,SAAA,CAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,QAAA,CAAA,IAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,QAAA,CAAA,EAAA;AACA,YAAA,QAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,QAAA,CAAA,IAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA,EAAA;AACA,YAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,SAAA;AACA,YAAA,UAAA,CAAA,IAAA,CAAA,IAAA;AACA,YAAA,MAAA;AACA,WAVA,MAUA;AACA,YAAA,GAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,cAFA,GAGA,GAAA,CAAA,GAHA,GAIA,IALA;AAMA;;AAEA,cAAA,IAAA,CAAA,gBAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,SAFA,GAGA,IAAA,CAAA,aAHA,GAIA,SAJA,GAKA,IAAA,CAAA,YALA,GAMA,gBAPA;AAQA;;AAEA,cAAA,IAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,WAFA,GAGA,IAAA,CAAA,OAHA,GAIA,WAJA,GAKA,IAAA,CAAA,UALA,GAMA,eAPA;AAQA;AACA,SA1CA;AA2CA,QAAA,IAAA,CAAA,UAAA,GAAA,CAAA;;AACA,YAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,GAAA;AACA;;AACA,YAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,IAAA,EAAA,IAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA;AAKA;;AACA,YAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,MAAA,GAAA,UAAA;AACA,eAAA,OAAA,GAAA,UAAA;AACA,UAAA,OAAA,CAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,SAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,KADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;AAKA;;AAEA,gBAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,YAAA,MAAA,CAAA,OAAA,GAAA,CAAA;;AACA,YAAA,MAAA,CAAA,kBAAA;AACA,WAdA;AAeA;AACA;AACA,KAxiBA;AAyiBA;AACA,IAAA,UA1iBA,sBA0iBA,GA1iBA,EA0iBA;AACA,UAAA,GAAA,GAAA,GAAA,CAAA,oBAAA;;AACA,UAAA,GAAA,CAAA,MAAA,EAAA;AACA;AACA,OAFA,MAEA,IACA,GAAA,CAAA,eAAA,IAAA,IAAA,IACA,GAAA,CAAA,eAAA,GAAA,CADA,IAEA,KAAA,oBAAA,GAAA,GAAA,CAAA,eAHA,EAIA;AACA,aAAA,SAAA,CAAA,iBAAA,GAAA,CAAA,eAAA;AACA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,oBAAA,GAAA,GAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OAVA,MAUA;AACA,QAAA,GAAA,CAAA,gBAAA,GAAA,KAAA,oBAAA;AACA,QAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,GAAA;;AACA,YAAA,GAAA,CAAA,oBAAA,IAAA,GAAA,CAAA,gBAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,IACA,aACA,GAAA,CAAA,oBADA,GAEA,KAFA,GAGA,GAAA,CAAA,gBAHA,GAIA,IALA;AAMA,SAXA,CAYA;;;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,MAAA;;AACA,YAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,GAAA,MAAA,GAAA,CAAA;AACA;AACA;AACA,KA1kBA;AA2kBA;AACA,IAAA,UA5kBA,sBA4kBA,GA5kBA,EA4kBA;AACA,UAAA,GAAA,GAAA,GAAA,CAAA,oBAAA;;AACA,UAAA,GAAA,CAAA,MAAA,EAAA;AACA;AACA,OAFA,MAEA,IACA,GAAA,CAAA,eAAA,IAAA,IAAA,IACA,GAAA,CAAA,eAAA,GAAA,CADA,IAEA,KAAA,oBAAA,GAAA,GAAA,CAAA,eAHA,EAIA;AACA,aAAA,SAAA,CAAA,iBAAA,GAAA,CAAA,eAAA;AAEA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,oBAAA,GAAA,GAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OAXA,MAWA;AACA,QAAA,GAAA,CAAA,gBAAA,GAAA,KAAA,oBAAA;AACA,QAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,GAAA;;AACA,YAAA,GAAA,CAAA,oBAAA,IAAA,GAAA,CAAA,gBAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,IACA,aACA,GAAA,CAAA,oBADA,GAEA,KAFA,GAGA,GAAA,CAAA,gBAHA,GAIA,IALA;AAMA,SAXA,CAYA;;;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,MAAA;;AACA,YAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,GAAA,MAAA,GAAA,CAAA;AACA;AACA;AACA,KA7mBA;AA8mBA;AACA,IAAA,UA/mBA,sBA+mBA,GA/mBA,EA+mBA;AACA,UAAA,GAAA,GAAA,GAAA,CAAA,mBAAA;;AACA,UAAA,GAAA,CAAA,MAAA,EAAA;AACA;AACA,OAFA,MAEA,IACA,GAAA,CAAA,cAAA,IAAA,IAAA,IACA,GAAA,CAAA,cAAA,GAAA,CADA,IAEA,KAAA,mBAAA,GAAA,GAAA,CAAA,cAHA,EAIA;AACA,aAAA,SAAA,CAAA,iBAAA,GAAA,CAAA,cAAA;AAEA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,mBAAA,GAAA,GAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OAXA,MAWA;AACA,QAAA,GAAA,CAAA,eAAA,GAAA,KAAA,mBAAA;AACA,QAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,GAAA;;AACA,YAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,CAAA,eAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,IACA,aACA,GAAA,CAAA,mBADA,GAEA,KAFA,GAGA,GAAA,CAAA,eAHA,GAIA,IALA;AAMA,SAXA,CAYA;;;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,MAAA;;AACA,YAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,GAAA,MAAA,GAAA,CAAA;AACA;AACA;AACA,KAhpBA;AAipBA,IAAA,kBAjpBA,8BAipBA,GAjpBA,EAipBA;AACA,UAAA,GAAA,CAAA,MAAA,EAAA;AACA;AACA,OAFA,MAEA,IAAA,KAAA,mBAAA,GAAA,GAAA,CAAA,gBAAA,EAAA;AACA,aAAA,SAAA,CAAA,iBAAA,GAAA,CAAA,gBAAA;AAEA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,mBAAA,GAAA,GAAA,CAAA,eAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OAPA,MAOA;AACA,QAAA,GAAA,CAAA,eAAA,GAAA,KAAA,mBAAA;AACA,QAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,GAAA;AACA;AACA,KAhqBA;AAiqBA,IAAA,kBAjqBA,8BAiqBA,GAjqBA,EAiqBA;AACA,UAAA,GAAA,CAAA,MAAA,EAAA;AACA;AACA,OAFA,MAEA,IAAA,KAAA,mBAAA,GAAA,GAAA,CAAA,gBAAA,EAAA;AACA,aAAA,SAAA,CAAA,iBAAA,GAAA,CAAA,gBAAA;AAEA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,mBAAA,GAAA,GAAA,CAAA,eAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OAPA,MAOA;AACA,QAAA,GAAA,CAAA,eAAA,GAAA,KAAA,mBAAA;AACA,QAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,GAAA;AACA;AACA,KAhrBA;AAirBA,IAAA,iBAjrBA,6BAirBA,GAjrBA,EAirBA;AACA,UAAA,IAAA,GAAA,GAAA,CAAA,MAAA;;AACA,UAAA,GAAA,CAAA,MAAA,EAAA;AACA;AACA,OAFA,MAEA,IAAA,KAAA,kBAAA,GAAA,GAAA,CAAA,eAAA,EAAA;AACA,aAAA,SAAA,CAAA,iBAAA,GAAA,CAAA,eAAA;AACA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,cAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OANA,MAMA;AACA,QAAA,GAAA,CAAA,cAAA,GAAA,KAAA,kBAAA;AACA,QAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,GAAA;AACA;AACA,KAhsBA;AAisBA,IAAA,MAjsBA,kBAisBA,GAjsBA,EAisBA;AACA,UAAA,IAAA,GAAA;AACA,QAAA,gBAAA,EAAA,GAAA,CAAA,gBADA;AAEA,QAAA,gBAAA,EAAA,GAAA,CAAA,gBAFA;AAGA,QAAA,eAAA,EAAA,GAAA,CAAA,eAHA;AAIA,QAAA,eAAA,EAAA,GAAA,CAAA,eAJA;AAKA,QAAA,eAAA,EAAA,GAAA,CAAA,eALA;AAMA,QAAA,cAAA,EAAA,GAAA,CAAA,cANA;AAOA,QAAA,YAAA,EAAA,UAAA,CAAA,KAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAPA;AAQA,QAAA,YAAA,EAAA,UAAA,CAAA,KAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CARA;AASA,QAAA,WAAA,EAAA,UAAA,CAAA,KAAA,eAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CATA;AAUA,QAAA,aAAA,EAAA,GAAA,CAAA;AAVA,OAAA;;AAYA,UAAA,MAAA,GAAA,+BAAA,CAAA,IAAA,CAAA;;AACA,UAAA,MAAA,GAAA,CAAA,EAAA;AACA;AACA,aAAA,SAAA,CACA,cACA,MADA,GAEA,gBAFA,GAGA,UAHA,GAIA,UAAA,CAAA,KAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAJA,GAKA,WALA,GAMA,UAAA,CAAA,KAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CANA,GAOA,WAPA,GAQA,UAAA,CAAA,KAAA,eAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CARA,GASA,OATA,GAUA,GAAA,CAAA,eAXA;AAaA;AACA;;AACA,MAAA,GAAA,CAAA,YAAA,GAAA,UAAA,CAAA,KAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,GAAA,CAAA,YAAA,GAAA,UAAA,CAAA,KAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,GAAA,CAAA,WAAA,GAAA,UAAA,CAAA,KAAA,eAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,WAAA,YAAA,CAAA,GAAA;AACA,KAruBA;AAsuBA;AACA,IAAA,SAvuBA,uBAuuBA;AAAA;;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,iBAFA;AAGA,QAAA,IAAA,EAAA,gBAAA;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,cAAA,MAAA,GAAA,MAAA,CAAA,UAAA;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,IAAA;AACA,iBAAA,MAAA,CAAA,QAAA;AACA,iBAAA,MAAA,CAAA,OAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA,QAAA;AACA;AACA,UAAA,SAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,QAAA;;AACA,cAAA,MAAA,CAAA,UAAA;AACA,aAHA,MAGA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA;AACA,WARA;AASA,SApBA;AAqBA,QAAA,QAAA,EAAA,oBAAA,CAAA;AArBA,OAAA;AAuBA,KA/vBA;AAgwBA;AACA,IAAA,MAjwBA,oBAiwBA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,WAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,GAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,MAFA,GAGA,IAAA,CAAA,SAHA,GAIA,oBALA;AAMA;;AACA,QAAA,GAAA,IAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA,OAVA;;AAWA,UAAA,GAAA,CAAA,MAAA,GAAA,CAAA,IAAA,GAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,oBAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,SAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,EAAA;AACA,gBAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,kBAAA,OAAA,EAAA,SAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,KADA;AAEA,kBAAA,QAAA,EAAA,EAFA;AAGA,kBAAA,QAAA,EAAA;AAHA,iBAAA;AAKA;;AAEA,kBAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,gBAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,cAAA,IAAA,CAAA,UAAA;AACA,aAbA;AAcA,WAlBA;AAmBA,UAAA,QAAA,EAAA,oBAAA,CAAA;AAnBA,SAAA;AAqBA,OAtBA,MAsBA;AACA,QAAA,IAAA,CAAA,SAAA,CAAA,GAAA;AACA;AACA,KA/yBA;AAgzBA,IAAA,eAhzBA;AAAA;AAAA;AAAA,gDAgzBA,WAhzBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAizBA,qBAAA,aAAA,GAAA,CAAA;AAjzBA;AAAA,uBAkzBA,iBAAA,CAAA;AAAA,kBAAA,WAAA,EAAA;AAAA,iBAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,EAAA;AACA,oBAAA,OAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA;AACA;AACA,iBAJA,CAlzBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAwzBA;AACA,IAAA,UAzzBA,sBAyzBA,GAzzBA,EAyzBA,KAzzBA,EAyzBA,OAzzBA,EAyzBA,GAzzBA,EAyzBA;AACA,UAAA,GAAA,CAAA,cAAA,IAAA,IAAA,EAAA;AACA,aAAA,aAAA,GAAA,GAAA,CAAA,SAAA;AACA,aAAA,WAAA,GAAA,GAAA,CAAA,OAAA;AACA,aAAA,qBAAA,GACA,GAAA,CAAA,iBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,iBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,iBAHA;AAIA,aAAA,oBAAA,GACA,GAAA,CAAA,gBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,gBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,gBAHA;AAIA,aAAA,qBAAA,GACA,GAAA,CAAA,iBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,iBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,iBAHA;AAIA,aAAA,kBAAA,GACA,GAAA,CAAA,mBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,mBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,mBAHA;AAIA,aAAA,eAAA,GACA,GAAA,CAAA,gBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,gBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,gBAHA;AAIA,aAAA,eAAA,GACA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,KAAA,CAAA,GAAA,IAAA,GAAA,GAAA,CAAA,WADA;AAEA,aAAA,WAAA,GACA,GAAA,CAAA,OAAA,IAAA,IAAA,IAAA,GAAA,CAAA,OAAA,KAAA,CAAA,GAAA,IAAA,GAAA,QAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,EADA;AAEA,aAAA,mBAAA,GACA,GAAA,CAAA,eAAA,IAAA,IAAA,IAAA,GAAA,CAAA,eAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,eAHA;AAIA,aAAA,UAAA,GAAA,GAAA,CAAA,EAAA;AACA,aAAA,SAAA,GAAA,KAAA;AACA,aAAA,YAAA,GAAA,OAAA;AAEA,aAAA,WAAA,GAAA,GAAA,CAAA,OAAA;AACA,YAAA,CAAA,GAAA,IAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,cAAA,OAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,CAAA,KAAA,CAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA;AACA,SAJA,EAIA,GAJA,CAAA;AAKA,OA1CA,MA0CA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA;AACA,UAAA,OAAA,EAAA,kBADA;AAEA,UAAA,QAAA,EAAA,CAFA;AAGA,UAAA,QAAA,EAAA;AAHA,SAAA;AAKA;AACA,KA32BA;AA42BA;AACA,IAAA,YA72BA,wBA62BA,MA72BA,EA62BA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,cAAA,MAAA;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,SAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,WAAA;AACA,UAAA,IAAA,GAAA,KAAA,aAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,SAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,mBAAA;AACA,UAAA,IAAA,GAAA,KAAA,qBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,kBAAA;AACA,UAAA,IAAA,GAAA,KAAA,oBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,mBAAA;AACA,UAAA,IAAA,GAAA,KAAA,qBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,qBAAA;AACA,UAAA,IAAA,GAAA,KAAA,kBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,kBAAA;AACA,UAAA,IAAA,GAAA,KAAA,eAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,iBAAA;AACA,UAAA,IAAA,GAAA,KAAA,mBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,SAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA;AACA;;AACA,aAAA,EAAA;AACA,UAAA,GAAA,GAAA,aAAA;AACA,UAAA,IAAA,GAAA,KAAA,eAAA;AACA;;AACA,aAAA,EAAA;AACA,UAAA,GAAA,GAAA,QAAA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA;AACA;AAhDA;;AAkDA,aAAA;AAAA,QAAA,GAAA,EAAA,GAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA;AACA,KAn6BA;AAo6BA;AACA,IAAA,QAr6BA,sBAq6BA;AACA,UAAA,KAAA,YAAA,KAAA,EAAA,EAAA;AACA,aAAA,cAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,YAAA,CAAA,KAAA,YAAA,EAAA,IAAA;;AACA,UAAA,GAAA,EAAA;AACA,YAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,kBAAA,KAAA,YAAA;AACA;AACA;AACA,iBAAA,CAAA;AACA,mBAAA,iBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,eAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,yBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,wBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,yBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,sBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,mBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,uBAAA;AACA;;AACA,iBAAA,EAAA;AACA,mBAAA,mBAAA;AACA;AA7BA;AA+BA,SAhCA,MAgCA;AACA,eAAA,SAAA,CAAA,QAAA;AACA;AACA;AACA,KAh9BA;AAi9BA;AACA,IAAA,SAl9BA,qBAk9BA,GAl9BA,EAk9BA;AACA,WAAA,OAAA,CAAA,KAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,GAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA;AAKA,KAx9BA;AAy9BA;AACA,IAAA,QA19BA,oBA09BA,IA19BA,EA09BA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,YAAA;AACA,UAAA,GAAA,GAAA,EAAA;;AACA,UAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAA,KAAA,GAAA,CAAA;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OAHA,MAGA,IAAA,KAAA,GAAA,CAAA,CAAA,IAAA,OAAA,KAAA,EAAA,EAAA;AACA;AACA,YAAA,KAAA,IAAA,IAAA,CAAA,QAAA,GAAA,CAAA,IAAA,KAAA,IAAA,IAAA,CAAA,SAAA,GAAA,CAAA,EAAA;AACA,UAAA,KAAA,GAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,KAAA;AACA;;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OARA,MAQA;AACA,QAAA,GAAA,GAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CADA,CAEA;;AACA,YAAA,GAAA,KAAA,GAAA,CAAA,KAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,OAAA,IAAA,CAAA,IAAA,OAAA,IAAA,CAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,EAAA;AACA,YAAA,OAAA,IAAA,CAAA;AACA,WAFA,MAEA;AACA,YAAA,OAAA,IAAA,CAAA;AACA;AACA,SANA,MAMA;AACA,UAAA,OAAA,IAAA,CAAA;AACA,SAXA,CAYA;;;AACA,YAAA,GAAA,CAAA,MAAA,EAAA;AACA,cAAA,OAAA,GAAA,CAAA,EAAA;AACA,YAAA,OAAA,GAAA,CAAA;AACA;AACA;AACA;;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,MAAA,GAAA,GAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA;;AACA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,GAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,OAAA;AACA,QAAA,IAAA,CAAA,qBAAA,GACA,GAAA,CAAA,iBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,iBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,iBAHA;AAIA,QAAA,IAAA,CAAA,oBAAA,GACA,GAAA,CAAA,gBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,gBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,gBAHA;AAIA,QAAA,IAAA,CAAA,qBAAA,GACA,GAAA,CAAA,iBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,iBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,iBAHA;AAIA,QAAA,IAAA,CAAA,kBAAA,GACA,GAAA,CAAA,mBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,mBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,mBAHA;AAIA,QAAA,IAAA,CAAA,eAAA,GACA,GAAA,CAAA,gBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,gBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,gBAHA;AAIA,QAAA,IAAA,CAAA,eAAA,GACA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,KAAA,CAAA,GAAA,IAAA,GAAA,GAAA,CAAA,WADA;AAEA,QAAA,IAAA,CAAA,WAAA,GACA,GAAA,CAAA,OAAA,IAAA,IAAA,IAAA,GAAA,CAAA,OAAA,KAAA,CAAA,GAAA,IAAA,GAAA,QAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,EADA;AAEA,QAAA,IAAA,CAAA,mBAAA,GACA,GAAA,CAAA,eAAA,IAAA,IAAA,IAAA,GAAA,CAAA,eAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,eAHA;AAIA,QAAA,IAAA,CAAA,UAAA,GAAA,GAAA,CAAA,EAAA;AACA;;AAEA,MAAA,UAAA,CAAA,YAAA;AACA,YAAA,OAAA,IAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA;AACA,OAJA,EAIA,GAJA,CAAA;AAKA,KAtiCA;AAuiCA,IAAA,kBAviCA,gCAuiCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,WAAA;AACA,MAAA,IAAA,CAAA,OAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KA5iCA;AA6iCA;AACA,IAAA,iBA9iCA,+BA8iCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,aAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,aAAA,EAAA;AACA;AACA,YAAA,MAAA,GAAA,iBAAA,CAAA,IAAA,EAAA,GAAA,EAAA,KAAA,CAAA;;AACA,YAAA,MAAA,EAAA;AACA;AACA,eAAA,SAAA,CAAA,MAAA;AACA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,SAAA,GAAA,WAAA;AACA,SAJA,MAIA;AACA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,SAAA,GAAA,QAAA;AACA,eAAA,UAAA,GAAA,IAAA;AACA;AACA,OAXA,MAWA,IAAA,GAAA,IAAA,IAAA,CAAA,aAAA,EAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,GAAA;AACA;AACA,KA/jCA;AAgkCA;AACA,IAAA,eAjkCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkkCA,gBAAA,IAlkCA,GAkkCA,KAAA,UAAA,CAAA,KAAA,SAAA,CAlkCA;AAmkCA,gBAAA,GAnkCA,GAmkCA,KAAA,WAnkCA;;AAAA,sBAokCA,GAAA,IAAA,IAAA,CAAA,WApkCA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAskCA,KAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAtkCA;;AAAA;AAskCA,gBAAA,MAtkCA;;AAukCA,oBAAA,MAAA,EAAA;AACA,uBAAA,SAAA,CAAA,MAAA;AACA,uBAAA,OAAA,CAAA,KAAA,SAAA,EAAA,OAAA,GAAA,WAAA;AACA,iBAHA,MAGA;AACA,uBAAA,OAAA,CAAA,KAAA,SAAA,EAAA,OAAA,GAAA,QAAA;AAEA,uBAAA,aAAA,CAAA,IAAA,EAAA,GAAA;AACA;;AA9kCA;AAAA;;AAAA;AA+kCA,oBAAA,GAAA,IAAA,IAAA,CAAA,WAAA,EAAA;AACA,kBAAA,IAAA,CAAA,OAAA,GAAA,GAAA;AACA;;AAjlCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAmlCA;AACA,IAAA,aAplCA;AAAA;AAAA;AAAA,gDAolCA,IAplCA,EAolCA,GAplCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqlCA;AACA,gBAAA,KAtlCA,GAslCA,EAtlCA;AAulCA,gBAAA,MAvlCA,GAulCA,YAAA,CAAA,GAAA,CAvlCA,EAulCA;;AACA,gBAAA,OAxlCA,GAwlCA,YAAA,CAAA,cAAA,EAAA,CAxlCA,EAwlCA;;AAxlCA,sBAylCA,IAAA,CAAA,gBAAA,IAAA,CAAA,IAAA,MAAA,GAAA,OAzlCA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBA0lCA,KAAA,CACA,OADA,CACA;AACA,kBAAA,GAAA,EAAA,0BADA;AAEA,kBAAA,MAAA,EAAA,MAFA;AAGA,kBAAA,IAAA,EAAA;AACA,oBAAA,QAAA,EAAA,IADA;AAEA,oBAAA,SAAA,EAAA,QAFA;AAGA,oBAAA,MAAA,EAAA,IAAA,CAAA,IAAA,GAAA,EAHA;AAIA,oBAAA,YAAA,EAAA,MAJA;AAKA,oBAAA,OAAA,EAAA,CALA;AAMA,oBAAA,QAAA,EAAA;AANA;AAHA,iBADA,EAaA,IAbA,CAaA,UAAA,GAAA,EAAA;AACA,yBAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,iBAfA,CA1lCA;;AAAA;AA0lCA,gBAAA,KA1lCA;;AAAA,sBA0mCA,KAAA,CAAA,MAAA,IAAA,CA1mCA;AAAA;AAAA;AAAA;;AAAA,kDA2mCA,2BA3mCA;;AAAA;AA6mCA,gBAAA,KAAA,GAAA,MAAA,CA7mCA,CA6mCA;;AA7mCA;AAgnCA;AACA,gBAAA,MAjnCA,GAinCA,eAAA,CAAA,IAAA,EAAA,GAAA,EAAA,KAAA,CAjnCA;AAAA,kDAknCA,MAlnCA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAonCA,IAAA,aApnCA,yBAonCA,IApnCA,EAonCA,GApnCA,EAonCA;AAAA;;AACA,MAAA,IAAA,CAAA,OAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CAFA,CAGA;;AACA,WAAA,QAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,OAAA,EAAA,UAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,IAAA,KAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,CAAA;AACA;;AACA,QAAA,OAAA,CAAA,YAAA,CAAA,IAAA;AACA,OAPA;AAQA,WAAA,QAAA,GAAA,WAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,OAAA,CAAA;AACA,KAjoCA;AAkoCA;AACA,IAAA,yBAnoCA,uCAmoCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,qBAAA;AACA,MAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,qBAAA,EAAA;AACA;AACA,YAAA,MAAA,GAAA,yBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;;AACA,YAAA,MAAA,CAAA,MAAA,EAAA;AACA,eAAA,SAAA,CAAA,MAAA,CAAA,MAAA;AACA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,iBAAA,GAAA,WAAA;AACA,SAHA,MAGA;AACA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,iBAAA,GAAA,QAAA;AACA,eAAA,WAAA,GAAA,MAAA,CAAA,CAAA;AACA,eAAA,OAAA,GAAA,IAAA;AACA;AACA,OAXA,MAWA,IAAA,GAAA,IAAA,IAAA,CAAA,qBAAA,EAAA;AACA,QAAA,IAAA,CAAA,iBAAA,GAAA,GAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;AACA,KAtpCA;AAupCA;AACA,IAAA,wBAxpCA,sCAwpCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,oBAAA;;AAEA,UAAA,GAAA,IAAA,IAAA,CAAA,oBAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;;AACA,YAAA,MAAA,GAAA,wBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;;AACA,YAAA,MAAA,CAAA,MAAA,EAAA;AACA,eAAA,SAAA,CAAA,MAAA,CAAA,MAAA;AACA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,gBAAA,GAAA,WAAA;AACA,SAHA,MAGA;AACA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,gBAAA,GAAA,QAAA;AAEA,eAAA,sBAAA,CAAA,IAAA,EAAA,GAAA,EAAA,MAAA;AACA;AACA,OAXA,MAWA,IAAA,GAAA,IAAA,IAAA,CAAA,oBAAA,EAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,GAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;AACA,KA3qCA;AA4qCA,IAAA,sBA5qCA,kCA4qCA,IA5qCA,EA4qCA,GA5qCA,EA4qCA,MA5qCA,EA4qCA;AAAA;;AACA,MAAA,IAAA,CAAA,gBAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,UAAA,CAAA,GAAA,MAAA,CAAA,CAAA;;AACA,UAAA,CAAA,KAAA,IAAA,EAAA;AACA,aAAA,OAAA,GAAA,IAAA;AACA,OAFA,MAEA,IAAA,CAAA,KAAA,KAAA,EAAA;AACA,aAAA,SAAA,GAAA,IAAA;AACA,OAFA,MAEA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;;AACA,UAAA,SAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA;AACA,QAAA,KAAA,CACA,OADA,CACA;AACA,UAAA,GAAA,EAAA,kCADA;AAEA,UAAA,MAAA,EAAA,MAFA;AAGA,UAAA,MAAA,EAAA;AAAA,YAAA,IAAA,EAAA,IAAA,CAAA,IAAA;AAAA,YAAA,SAAA,EAAA,IAAA,CAAA;AAAA;AAHA,SADA,EAMA,IANA,CAMA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,GAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA;AACA,cAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,MAAA,CAAA,EACA,GAAA,IAAA,mBAAA;AACA,cAAA,GAAA,IAAA,EAAA,EACA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,IAAA,EAAA,WAAA,IAAA,CAAA,WAAA,GAAA,GAAA,GAAA,GAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA;;AAKA,cAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA,MAAA,CAAA,IAAA,EAAA;AACA,cAAA,YAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,SADA;AAEA,cAAA,kBAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AAFA,aAAA;AAIA;AACA,SAvBA;AAwBA;AACA,KAltCA;AAmtCA;AACA,IAAA,yBAptCA,uCAotCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,qBAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,qBAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,iBAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA,OALA,MAKA,IAAA,GAAA,IAAA,IAAA,CAAA,qBAAA,EAAA;AACA,QAAA,IAAA,CAAA,iBAAA,GAAA,GAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;;AACA,UACA,GAAA,IAAA,IAAA,CAAA,qBAAA,IACA,SAAA,CAAA,OAAA,IAAA,IADA,IAEA,IAAA,CAAA,eAAA,GAAA,CAFA,IAGA,UAAA,CAAA,GAAA,GAAA,IAAA,CAAA,eAAA,CAAA,GAAA,KAHA,KAIA,CAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,QAAA,CAAA,IAAA,CAAA,WAAA,KACA,IAAA,CAAA,UAAA,IAAA,CADA,IAEA,CAAA,CAAA,EAAA,CAAA,EAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CANA,CADA,EAQA;AACA,YAAA,IAAA,GAAA,KAAA;AACA,YAAA,IAAA,IAAA,GAAA,OAAA,KAAA,IAAA,IAAA,CAAA,WAAA,EAAA,OAAA,EAAA,EAAA,IAAA,GAAA,IAAA;AACA,YACA,IAAA,CAAA,SAAA,IAAA,OAAA,IACA,IAAA,IAAA,GAAA,OAAA,KAAA,IAAA,IAAA,CAAA,WAAA,EAAA,OAAA,EAFA,EAIA,IAAA,GAAA,IAAA;;AACA,YAAA,IAAA,EAAA;AACA,eAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,IAAA,EACA,WACA,IAAA,CAAA,WADA,GAEA,GAFA,GAGA,uBANA;AAOA,YAAA,QAAA,EAAA;AAPA,WAAA;AASA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,iBAAA,GAAA,WAAA;AACA,SAXA,MAWA;AACA,eAAA,OAAA,CAAA,KAAA,SAAA,EAAA,iBAAA,GAAA,QAAA;AACA;AACA,OA9BA,MA8BA;AACA,aAAA,OAAA,CAAA,KAAA,SAAA,EAAA,iBAAA,GAAA,QAAA;AACA;AACA,KAjwCA;AAkwCA;AACA,IAAA,sBAnwCA,oCAmwCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,kBAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,kBAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,oBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,cAAA,CAAA,IAAA,CAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA,OAPA,MAOA,IAAA,GAAA,IAAA,IAAA,CAAA,kBAAA,EAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,oBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,cAAA,CAAA,IAAA,CAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;;AACA,WAAA,iBAAA,CAAA,IAAA;AACA,WAAA,gBAAA,CAAA,IAAA;AACA,KArxCA;AAsxCA;AACA,IAAA,mBAvxCA,iCAuxCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,eAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,eAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,gBAAA,CAAA,IAAA,CAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA,OANA,MAMA,IAAA,GAAA,IAAA,IAAA,CAAA,eAAA,EAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,gBAAA,CAAA,IAAA,CAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;;AACA,WAAA,iBAAA,CAAA,IAAA;AACA,WAAA,gBAAA,CAAA,IAAA;AACA,KAvyCA;AAwyCA;AACA,IAAA,uBAzyCA,qCAyyCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,mBAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,mBAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,gBAAA,CAAA,IAAA,CAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA,OANA,MAMA,IAAA,GAAA,IAAA,IAAA,CAAA,mBAAA,EAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,gBAAA,CAAA,IAAA,CAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;AACA,KAvzCA;AAwzCA;AACA,IAAA,mBAzzCA,iCAyzCA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,eAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,eAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA,OALA,MAKA,IAAA,GAAA,IAAA,IAAA,CAAA,eAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;;AACA,WAAA,iBAAA,CAAA,IAAA;AACA,KAt0CA;AAu0CA;AACA,IAAA,cAx0CA,4BAw0CA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,UAAA;AACA,MAAA,IAAA,CAAA,EAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KA70CA;AA80CA,IAAA,gBA90CA,4BA80CA,IA90CA,EA80CA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;;AACA,UAAA,QAAA,OAAA,EAAA;AACA,YAAA,YAAA,GAAA,IAAA,CAAA,YAAA;AACA,YAAA,MAAA,GAAA,WAAA,CAAA,IAAA,CAAA,kBAAA,CAAA;AACA,YAAA,KAAA,WAAA,IAAA,CAAA,EACA,KAAA,WAAA,GAAA,WAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,OAAA,CAAA;;AACA,YAAA,MAAA,IAAA,CAAA,IAAA,CAAA,YAAA,GAAA,KAAA,WAAA,EAAA,OAAA,CAAA,CAAA,IAAA,MAAA,GAAA,CAAA,EAAA;AACA,cAAA,CAAA,CAAA,YAAA,GAAA,KAAA,WAAA,EAAA,OAAA,CAAA,CAAA,IAAA,MAAA,IAAA,MAAA,GAAA,GAAA,EACA,KAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,IAAA,EACA,WACA,IAAA,CAAA,WADA,GAEA,GAFA,GAGA,2BANA;AAOA,YAAA,QAAA,EAAA;AAPA,WAAA;AASA;AACA;AACA,KAl2CA;AAm2CA;AACA,IAAA,iBAp2CA,6BAo2CA,IAp2CA,EAo2CA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,QAAA,CAFA,CAEA;;AACA,UAAA,gBAAA,GAAA,IAAA,CAAA,gBAAA,CAHA,CAGA;;AACA,UAAA,UAAA,GAAA,IAAA,CAAA,UAAA,CAJA,CAIA;;AACA,UAAA,SAAA,GAAA,IAAA,CAAA,SAAA,CALA,CAKA;;AACA,UAAA,CAAA,YAAA,CAAA,QAAA,CAAA,IAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,QAAA,CAAA,QAAA,CAAA,EAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAA,SAAA,EAAA;AACA,cAAA,SAAA,IAAA,IAAA,IAAA,SAAA,GAAA,aAAA,EAAA;AACA;AACA,iBAAA,SAAA,CACA,yBAAA,SAAA,GAAA,YADA;AAGA;AACA;AACA;AACA,KAx6CA;AAy6CA,IAAA,SAz6CA,qBAy6CA,KAz6CA,EAy6CA;AACA,WAAA,UAAA,GAAA,IAAA,CADA,CACA;;AACA,UAAA,GAAA,GAAA,KAAA,UAAA,CAAA,KAAA,CAAA;;AACA,UAAA,GAAA,CAAA,SAAA,IAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IAAA,EAAA;AACA,YAAA,GAAA,GAAA;AACA,UAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IADA;AAEA,UAAA,SAAA,EAAA,GAAA,CAAA;AAFA,SAAA;AAIA,QAAA,iBAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,GAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,SAFA;AAGA;;AACA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,MAAA,EAAA;AACA,eAAA,QAAA,GAAA,IAAA;AACA,SAFA,MAEA;AACA,eAAA,QAAA,GAAA,KAAA;AACA;;AAEA,aAAA,UAAA,GAAA,GAAA,CAPA,CAOA;;AACA,aAAA,oBAAA,GACA,GAAA,CAAA,gBAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,gBADA;AAEA,aAAA,oBAAA,GACA,GAAA,CAAA,gBAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,gBADA;AAEA,aAAA,mBAAA,GAAA,GAAA,CAAA,eAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,eAAA;AACA,aAAA,mBAAA,GAAA,GAAA,CAAA,eAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,eAAA;AACA,aAAA,mBAAA,GAAA,GAAA,CAAA,eAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,eAAA;AACA,aAAA,kBAAA,GAAA,GAAA,CAAA,cAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,cAAA;;AACA,YAAA,KAAA,OAAA,IAAA,IAAA,EAAA;AACA,eAAA,gBAAA,GAAA,GAAA,CAAA,YAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,YAAA;AACA,eAAA,gBAAA,GAAA,GAAA,CAAA,YAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,YAAA;AACA,eAAA,eAAA,GAAA,GAAA,CAAA,WAAA,KAAA,IAAA,GAAA,CAAA,GAAA,GAAA,CAAA,WAAA;AACA;AACA;AACA,KA38CA;AA68CA,IAAA,SA78CA;AAAA;AAAA;AAAA,gDA68CA,IA78CA;AAAA;AAAA;AAAA;AAAA;AAAA;AA88CA,gBAAA,UA98CA,GA88CA,EA98CA;AA+8CA,gBAAA,IA/8CA,GA+8CA,EA/8CA;;AAg9CA,oBAAA,IAAA,KAAA,SAAA,EAAA;AACA,kBAAA,IAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;AACA,iBAFA,MAEA;AACA,kBAAA,IAAA,GAAA,KAAA,eAAA;AACA;;AACA,qBAAA,aAAA,GAAA,IAAA;AACA,gBAAA,CAt9CA,GAs9CA,CAt9CA;;AAAA;AAAA,sBAs9CA,CAAA,GAAA,IAAA,CAAA,MAt9CA;AAAA;AAAA;AAAA;;AAu9CA,gBAAA,EAv9CA,GAu9CA,IAAA,CAAA,CAAA,CAv9CA;;AAAA,sBAy9CA,EAAA,CAAA,GAAA,CAAA,SAAA,IAAA,GAz9CA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBA09CA,KAAA,WAAA,CAAA,EAAA,CA19CA;;AAAA;AAs9CA,gBAAA,CAAA,EAt9CA;AAAA;AAAA;;AAAA;AA69CA,oBAAA,KAAA,aAAA,EAAA;AACA,uBAAA,mBAAA,CAAA,IAAA;AACA;;AA/9CA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAi+CA,IAAA,WAj+CA;AAAA;AAAA;AAAA,gDAi+CA,EAj+CA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAk+CA,gBAAA,UAl+CA,GAk+CA,EAl+CA;AAAA;AAAA,uBAm+CA,SAAA,CAAA;AAAA,kBAAA,MAAA,EAAA,EAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;;AACA,sBAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,oBAAA,OAAA,CAAA,aAAA,GAAA,KAAA;;AACA,oBAAA,OAAA,CAAA,SAAA,CAAA,8BAAA;AACA;AACA,iBANA,CAn+CA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA2+CA,IAAA,mBA3+CA,+BA2+CA,IA3+CA,EA2+CA;AACA,WAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,KAAA,SAAA,EAAA;AACA;AACA;AACA;AACA;AACA,aAAA,eAAA,GALA,CAMA;AACA,OAPA,MAOA,IAAA,IAAA,KAAA,KAAA,EAAA;AACA;AACA;AACA;AACA;AACA,aAAA,kBAAA,GALA,CAMA;AACA;AACA,KA5/CA;AA6/CA;AACA,IAAA,aA9/CA,2BA8/CA;AACA,UAAA,KAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;;AACA,UAAA,KAAA,IAAA,IAAA,IAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,cAAA;AACA,OAFA,MAEA;AACA,aAAA,eAAA,CAAA,KAAA;AACA;AACA,KArgDA;AAsgDA;AACA,IAAA,gBAvgDA,8BAugDA;AAAA;;AACA,UAAA,MAAA,GAAA,KAAA,UAAA;AACA,MAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AACA,MAAA,MAAA,CAAA,QAAA,GAAA,KAAA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,mCADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,UAAA,KAAA,GAAA,EAAA;AACA,UAAA,MAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;AACA,YAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,QAAA,OAAA,CAAA,eAAA,CAAA,KAAA;AACA,OAJA;AAKA,KAxhDA;AAyhDA;AACA,IAAA,eA1hDA,2BA0hDA,KA1hDA,EA0hDA;AACA,UAAA,CAAA,GAAA,EAAA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,EAAA;AACA,UAAA,CAAA,GAAA,CAAA;AACA,UAAA,IAAA,GAAA,KAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,SAAA,IAAA,CAAA;AAAA,OAAA,CAAA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,GAAA,CAAA;AACA,YAAA,UAAA,GAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,UAAA,CAAA,SAAA;AACA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,GAAA,GAAA,sBAAA,CAAA,IAAA,CAAA;;AACA,cAAA,GAAA,CAAA,MAAA,EAAA;AACA,gBAAA,QAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,SAAA,CAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,QAAA,CAAA,IAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,QAAA,CAAA,EAAA;AACA,YAAA,QAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,QAAA,CAAA,IAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA,EAAA;AACA,YAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,SAAA;AACA,YAAA,UAAA,CAAA,IAAA,CAAA,IAAA;AACA,YAAA,MAAA;AACA,WAVA,MAUA;AACA,YAAA,GAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,cAFA,GAGA,GAAA,CAAA,GAHA,GAIA,IALA;AAMA;;AAEA,cAAA,IAAA,CAAA,gBAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,SAFA,GAGA,IAAA,CAAA,aAHA,GAIA,SAJA,GAKA,IAAA,CAAA,YALA,GAMA,gBAPA;AAQA;;AAEA,cAAA,IAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,WAFA,GAGA,IAAA,CAAA,OAHA,GAIA,WAJA,GAKA,IAAA,CAAA,UALA,GAMA,eAPA;AAQA;;AAEA,cAAA,IAAA,CAAA,GAAA,IAAA,QAAA,OAAA,EAAA;AACA,gBAAA,IAAA,CAAA,GAAA,CAAA,SAAA,IAAA,GAAA,EAAA;AACA,kBAAA,IAAA,CAAA,GAAA,CAAA,IAAA,EAAA;AACA,gBAAA,CAAA,GAAA,CAAA;AACA,gBAAA,GAAA,IAAA,IAAA,CAAA,WAAA,GAAA,GAAA;AACA;AACA;AACA;;AACA,cAAA,IAAA,CAAA,SAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cACA,QAAA,OAAA,IACA,IAAA,CAAA,SAAA,GAAA,CADA,KAEA,IAAA,CAAA,YAAA,IAAA,IAAA,IAAA,IAAA,CAAA,YAAA,GAAA,CAFA,KAGA,IAAA,CAAA,SAJA,EAKA;AACA,YAAA,CAAA,GAAA,CAAA;AACA,YAAA,GAAA,IAAA,IAAA,CAAA,WAAA,GAAA,GAAA;AACA;AACA,SAlEA;;AAmEA,YAAA,CAAA,KAAA,CAAA,EAAA;AACA,cAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,iBAAA,MAAA,GAAA,UAAA;AACA,iBAAA,OAAA,GAAA,UAAA;AACA,iBAAA,UAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,iBAAA,OAAA,CAAA,OAAA,CAAA;AACA,cAAA,KAAA,EAAA,IADA;AAEA,cAAA,IAAA,EAAA,IAFA;AAGA,cAAA,QAAA,EAAA;AAHA,aAAA;AAKA;AACA,SAbA,MAaA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,0BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CACA,cACA,GADA,GAEA,oCAHA;AAKA,SANA,MAMA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CACA,cACA,GADA,GAEA,uCAHA;AAKA,SANA,MAMA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CACA,cACA,GADA,GAEA,8BAHA;AAKA;;AAEA,aAAA,UAAA,GAAA,CAAA;;AACA,YAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,GAAA;AACA;;AACA,YAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,IAAA,EAAA,IAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA;AAKA;AACA;AACA,KAxpDA;AAypDA;AACA,IAAA,WA1pDA,yBA0pDA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,eAAA;AACA,MAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CACA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UADA,EAEA,CAFA,EAGA,KAAA,UAAA,CAAA,OAHA;AAKA,KAlqDA;AAmqDA;AACA,IAAA,kBApqDA,gCAoqDA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,eAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,MAAA,iBAAA,CAAA,KAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA;AAKA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,CAAA,aAAA;AACA,WAFA,MAEA;AACA,YAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,EAAA,OAAA,CAAA,UAAA,CAAA,OAAA,EADA,CAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAXA,MAWA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,aAAA;AACA;AACA,OAvBA;AAwBA,KAhsDA;AAisDA;AACA,IAAA,eAlsDA,6BAksDA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,CAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,cAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,SAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA,SAXA;;AAYA,YAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,EAAA,CAAA,EAAA,KAAA,UAAA,CAAA,OAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,0BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,+BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,2BAAA;AACA;AACA;AACA,KA/tDA;AAguDA,IAAA,UAhuDA,sBAguDA,MAhuDA,EAguDA;AACA,WAAA,OAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA;AACA,UAAA,SAAA,EAAA,QADA;AAEA,UAAA,OAAA,EAAA,QAFA;AAGA,UAAA,iBAAA,EAAA,QAHA;AAIA,UAAA,gBAAA,EAAA,QAJA;AAKA,UAAA,iBAAA,EAAA,QALA;AAMA,UAAA,mBAAA,EAAA,QANA;AAOA,UAAA,gBAAA,EAAA,QAPA;AAQA,UAAA,WAAA,EAAA,QARA;AASA,UAAA,OAAA,EAAA,QATA;AAUA,UAAA,eAAA,EAAA,QAVA;AAWA,UAAA,MAAA,EAAA,QAXA;AAYA,UAAA,OAAA,EAAA;AAZA,SAAA;AAcA;AACA,KAlvDA;AAmvDA,IAAA,OAnvDA,qBAmvDA;AACA,UAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,IAAA,CAAA,EAAA;AACA,YAAA,GAAA,GAAA,IAAA;AACA,QAAA,GAAA,CAAA,cAAA,GAAA,KAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,GAAA,CAAA,kBAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OANA,MAMA;AACA,aAAA,cAAA,GAAA,IAAA;AACA;AACA,KA7vDA;AA8vDA,IAAA,SA9vDA,uBA8vDA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,EAAA,GAAA,IAAA,CAAA,CAAA,CAAA;;AACA,YAAA,EAAA,CAAA,GAAA,CAAA,SAAA,IAAA,GAAA,EAAA;AACA,eAAA,WAAA,CAAA,EAAA;AACA;AACA;;AACA,UAAA,KAAA,aAAA,EAAA;AACA,YAAA,CAAA,GAAA,IAAA;AACA,YAAA,IAAA,GAAA,IAAA;;AACA,YAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,gBAAA;AACA,SAFA,MAEA;AACA,cAAA,YAAA,GAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,MAAA,GAAA,IAAA,CAAA,MAAA;;AACA,gBAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,CAAA,GAAA,KAAA;AACA,aAFA,MAEA;AACA,cAAA,YAAA,IAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA;AACA,WAPA;;AAQA,cAAA,CAAA,EAAA;AACA,YAAA,UAAA,CAAA,YAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,gBAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,kBAAA,OAAA,EAAA,SADA;AAEA,kBAAA,QAAA,EAAA,EAFA;AAGA,kBAAA,QAAA,EAAA;AAHA,iBAAA;AAKA,gBAAA,IAAA,CAAA,kBAAA;AACA;AACA,aATA;AAUA,WAXA,MAWA;AACA,YAAA,IAAA,CAAA,SAAA,CAAA,mBAAA;AACA;AACA;AACA;AACA,KAryDA;AAsyDA,IAAA,cAtyDA,0BAsyDA,IAtyDA,EAsyDA,GAtyDA,EAsyDA;AACA,UAAA,IAAA,GAAA,EAAA;AAAA,UACA,IAAA,GAAA,EADA;;AAEA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,SAAA,CAAA,aAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,IAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA,aAAA,CAAA,CAAA,EAAA,KAAA;AACA,QAAA,IAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA,aAAA,CAAA,CAAA,EAAA,GAAA;AACA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,GAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,SAAA,EAAA,IAJA;AAKA,QAAA,QAAA,EAAA;AALA,OAAA;AAOA,MAAA,KAAA,CAAA,qBAAA,CAAA,MAAA;AACA;AACA,KAtzDA;AAuzDA,IAAA,SAvzDA,qBAuzDA,IAvzDA,EAuzDA;AAAA;;AACA,WAAA,eAAA;AACA,UAAA,MAAA,GAAA,KAAA,UAAA;;AACA,UAAA,IAAA,IAAA,SAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,KAAA,QAAA;AACA,OAHA,MAGA,IAAA,IAAA,IAAA,KAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,KAAA,MAAA,CAAA,IAAA;AACA;;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,8BADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,WAAA,QAAA,GAAA,IAAA;AACA,MAAA,KAAA,CACA,IADA,CACA,GADA,EAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,QAAA,GAAA,KAAA;AACA,YAAA,OAAA,GAAA,GAAA;AACA,YAAA,IAAA,GAAA,IAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AACA,YAAA,QAAA,GAAA,aAAA,OAAA;;AACA,YAAA,cAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA,EAAA;AACA;AACA,cAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA,QAAA;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,UAAA,KAAA,CAAA,IAAA,GAAA,GAAA,CAAA,eAAA,CAAA,IAAA,CAAA;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,UAAA,KAAA,CAAA,KAAA;AACA,UAAA,GAAA,CAAA,eAAA,CAAA,KAAA,CAAA,IAAA,EARA,CAQA;;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,SAVA,MAUA;AACA;AACA,UAAA,SAAA,CAAA,UAAA,CAAA,IAAA,EAAA,QAAA;AACA;AACA,OArBA,EAsBA,KAtBA,CAsBA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OAxBA;AAyBA,KAh2DA;AAi2DA;AACA,IAAA,UAl2DA,wBAk2DA;AACA,UAAA,GAAA,GAAA,KAAA,WAAA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,MAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAHA,CAIA;;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,cAAA,CAAA,IAAA,CAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,WAAA,YAAA,CAAA,IAAA;AACA,KA12DA;AA22DA,IAAA,YA32DA,0BA22DA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,aAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAHA,CAGA;;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CAJA,CAKA;;AACA,WAAA,QAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,OAAA,EAAA,UAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,IAAA,KAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,CAAA;AACA;;AACA,QAAA,OAAA,CAAA,YAAA,CAAA,IAAA;AACA,OAPA;AAQA,UAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAdA,CAeA;;AACA,UAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,MAAA,GAAA,MAAA,GAAA,CAAA;AACA;;AACA,MAAA,IAAA,CAAA,MAAA,IAAA,aAAA,IAAA,CAAA,aAAA,GAAA,KAAA,GAAA,GAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,IAAA;AACA,KAj4DA;AAk4DA,IAAA,MAl4DA,oBAk4DA;AACA,WAAA,QAAA,CAAA,IAAA;AACA,KAp4DA;AAq4DA,IAAA,aAr4DA,yBAq4DA,IAr4DA,EAq4DA,KAr4DA,EAq4DA;AACA,UAAA,IAAA,GAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA;AACA,UAAA,GAAA,GAAA,IAAA,CAAA,aAAA;;AACA,UAAA,GAAA,EAAA;AACA,YAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,cAAA,GAAA,IAAA,IAAA,CAAA,aAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,GAAA;AAEA,YAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA,WAJA,MAIA;AACA,YAAA,IAAA,CAAA,QAAA;AACA;AACA,SARA,MAQA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,QAAA;AACA,UAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA,OAbA,MAaA;AACA,QAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA,KAx5DA;AAy5DA,IAAA,SAz5DA,uBAy5DA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,qBAAA;AAEA,MAAA,IAAA,CAAA,iBAAA,GAAA,GAAA;AACA,UAAA,CAAA,GAAA,KAAA,WAAA;;AACA,UAAA,CAAA,KAAA,IAAA,EAAA;AACA,aAAA,OAAA,GAAA,IAAA;AACA,OAFA,MAEA,IAAA,CAAA,KAAA,KAAA,EAAA;AACA,aAAA,SAAA,GAAA,IAAA;AACA,OAFA,MAEA;AACA,aAAA,YAAA,CAAA,IAAA;AACA;;AAEA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAfA,CAgBA;;AACA,UAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,IAAA,MAAA,IAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,MAAA,GAAA,MAAA,GAAA,CAAA;AACA;;AACA,MAAA,IAAA,CAAA,MAAA,IAAA,WAAA,IAAA,CAAA,qBAAA,GAAA,KAAA,GAAA,GAAA,GAAA,IAAA;AAEA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,IAAA;AACA,KAj7DA;AAk7DA,IAAA,SAl7DA,uBAk7DA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,MAAA,IAAA,CAAA,WAAA,GAAA,IAAA;AACA,WAAA,OAAA,GAAA,KAAA;AAEA,WAAA,YAAA,CAAA,IAAA;AACA,KAx7DA;AAy7DA,IAAA,WAz7DA,yBAy7DA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,CAAA;AACA,MAAA,IAAA,CAAA,WAAA,GAAA,KAAA;AACA,WAAA,SAAA,GAAA,KAAA;AAEA,WAAA,YAAA,CAAA,IAAA;AACA,KA/7DA;AAg8DA,IAAA,qBAh8DA,iCAg8DA,IAh8DA,EAg8DA,KAh8DA,EAg8DA;AACA,UAAA,IAAA,GAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA;AACA,UAAA,GAAA,GAAA,IAAA,CAAA,qBAAA;;AACA,UAAA,GAAA,EAAA;AACA,YAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,cAAA,GAAA,IAAA,IAAA,CAAA,qBAAA,EAAA;AACA,YAAA,IAAA,CAAA,iBAAA,GAAA,GAAA;AACA,YAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA,WAHA,MAGA;AACA,YAAA,IAAA,CAAA,QAAA;AACA;AACA,SAPA,MAOA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,QAAA;AACA,UAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA,OAZA,MAYA;AACA,QAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA,KAl9DA;AAm9DA,IAAA,QAn9DA,sBAm9DA;AACA,UAAA,KAAA,YAAA,KAAA,CAAA,EAAA;AACA,YAAA,IAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,EAAA,qBAAA;AACA,aAAA,qBAAA,GAAA,IAAA;AACA,aAAA,UAAA,CAAA,KAAA,SAAA,EAAA,iBAAA,GAAA,IAAA;AAEA,aAAA,KAAA,CAAA,qBAAA,KAAA,SAAA,GAAA,KAAA,YAAA,EAAA,KAAA;AACA,OANA,MAMA,IAAA,KAAA,YAAA,KAAA,CAAA,EAAA;AACA,YAAA,KAAA,GAAA,KAAA,UAAA,CAAA,KAAA,SAAA,EAAA,oBAAA;AACA,aAAA,oBAAA,GAAA,KAAA;AACA,aAAA,UAAA,CAAA,KAAA,SAAA,EAAA,gBAAA,GAAA,KAAA;AAEA,aAAA,KAAA,CAAA,sBAAA,KAAA,SAAA,GAAA,KAAA,YAAA,EAAA,KAAA;AACA;AACA,KAj+DA;AAk+DA,IAAA,eAl+DA,6BAk+DA;AACA,WAAA,UAAA;AACA,KAp+DA;AAq+DA,IAAA,eAr+DA,6BAq+DA;AACA,UAAA,KAAA,GAAA,KAAA,eAAA;AACA,WAAA,UAAA,CAAA,WAAA,GAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,KAx+DA;AAy+DA,IAAA,IAz+DA,kBAy+DA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,CAAA,UAAA,EAAA;AACA,QAAA,IAAA,CAAA,YAAA;AACA,OAFA,MAEA,IAAA,IAAA,CAAA,OAAA,EAAA;AACA,QAAA,IAAA,CAAA,SAAA;AACA,OAFA,MAEA,IAAA,IAAA,CAAA,OAAA,EAAA;AACA,QAAA,IAAA,CAAA,SAAA;AACA,OAFA,MAEA,IAAA,IAAA,CAAA,SAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA;AACA,OAFA,MAEA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,YAAA,OAAA,GAAA,IAAA,CAAA,YAAA;;AACA,YAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,UAAA,KAAA,GAAA,CAAA;AACA,UAAA,OAAA,GAAA,CAAA;AACA,UAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,UAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAA,UAAA,CAAA,KAAA,EAAA,SAAA;AACA,UAAA,UAAA,CAAA,YAAA;AACA,YAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,WAFA,EAEA,GAFA,CAAA;AAGA,SATA,MASA,IAAA,OAAA,KAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,CAAA,IAAA,EAAA,KAAA;AACA,SAFA,MAEA,IAAA,OAAA,KAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,qBAAA,CAAA,IAAA,EAAA,KAAA;AACA,SAFA,MAEA;AACA,UAAA,IAAA,CAAA,QAAA;AACA,UAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA;AACA,KAxgEA;AAygEA,IAAA,iBAzgEA,6BAygEA,IAzgEA,EAygEA;AACA,WAAA,SAAA,CAAA,IAAA,CAAA,IAAA,GAAA,gCAAA;AACA,KA3gEA;AA4gEA,IAAA,mBA5gEA,iCA4gEA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA;AACA,KA9gEA;AA+gEA,IAAA,cA/gEA,0BA+gEA,KA/gEA,EA+gEA,IA/gEA,EA+gEA;AACA,WAAA,QAAA,CAAA,IAAA,CAAA;AACA,QAAA,OAAA,EAAA,IAAA,CAAA,IAAA,GAAA;AADA,OAAA;AAGA,KAnhEA;AAohEA,IAAA,aAphEA,yBAohEA,IAphEA,EAohEA;AACA,UAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,WAAA;AACA;AACA;;AACA,UAAA,CAAA,IAAA,EAAA;AACA,aAAA,SAAA,CAAA,YAAA;AACA;AACA;;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CATA,CASA;;AACA,UAAA,cAAA,GAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAVA,CAUA;;AACA,UAAA,UAAA,GAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA,QAAA,GAAA,CAAA,EAAA,cAAA,CAAA,CAXA,CAWA;;AACA,UAAA,SAAA,UAAA,IAAA,UAAA,UAAA,EAAA;AACA;AACA;;AACA,UAAA,KAAA,GAAA;AAAA,QAAA,OAAA,EAAA,SAAA,CAAA;AAAA,OAAA;AACA,UAAA,KAAA,GAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,MAAA,KAAA,CACA,OADA,CACA;AACA,QAAA,GAAA,EAAA,iCADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAA,EAAA,KAAA;AAHA,OADA,EAMA,IANA,CAMA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,YAAA,OAAA,EAAA,SAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA;AADA,WAAA;AAGA,SAJA,MAIA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,oBAAA;AACA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,MAAA,CAAA,GAAA,GAAA,IAAA;AACA,UAAA,IAAA,CAAA,cAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,EAAA,QAAA,EAFA,CAGA;;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,UAAA,IAAA,CAAA,kBAAA;AACA;AACA,OAtBA;AAuBA,aAAA,KAAA;AACA,KA/jEA;AAgkEA,IAAA,gBAhkEA,4BAgkEA,IAhkEA,EAgkEA,IAhkEA,EAgkEA;AACA,WAAA,oBAAA,CAAA,IAAA;AACA,KAlkEA;AAmkEA,IAAA,MAnkEA,oBAmkEA;AACA,WAAA,kBAAA;AACA,KArkEA;AAskEA,IAAA,oBAtkEA,gCAskEA,IAtkEA,EAskEA;AACA,UAAA,CAAA,IAAA,EAAA;AACA,YAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAAA;AACA,eAAA,SAAA,CAAA,UAAA;AACA;;AACA,YAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAAA;AACA,eAAA,SAAA,CAAA,SAAA;AACA;;AACA,aAAA,KAAA,CAAA,WAAA,CAAA,KAAA,CAAA,MAAA,GAAA;AACA,UAAA,MAAA,EAAA,KAAA,UAAA,CAAA,OADA;AAEA,UAAA,MAAA,EAAA,KAAA,UAAA,CAAA;AAFA,SAAA,CAPA,CAUA;;AACA,aAAA,KAAA,CAAA,WAAA,CAAA,MAAA,GAXA,CAWA;AACA,OAZA,MAYA;AACA,aAAA,QAAA,GAAA,IAAA,CAAA,IAAA;AACA,aAAA,UAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA;AACA;AACA,KAvlEA;AAwlEA,IAAA,QAxlEA,oBAwlEA,GAxlEA,EAwlEA;AACA,UAAA,KAAA,GAAA,GAAA,CAAA,MAAA,GAAA,GAAA,CAAA,EAAA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,EAAA;;AACA,UAAA,KAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,IAAA,KAAA;AACA;;AACA,aAAA,KAAA;AACA,KA/lEA;AAgmEA,IAAA,UAhmEA,sBAgmEA,GAhmEA,EAgmEA;AACA,WAAA,KAAA,CAAA,eAAA,CAAA,MAAA,CAAA,GAAA,CAAA,IAAA,GAAA,EAAA;AACA;AAlmEA;AA9dA,CAAA", "sourcesContent": ["<!--四川铁塔电费台账-->\r\n<template>\r\n  <div class=\"page-class page-card\">\r\n    <div class=\"query-box\">\r\n      <Row :class=\"filterColl ? 'header-bar-show' : 'header-bar-hide'\">\r\n        <Form ref=\"accountForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.accountno\"\r\n                  @on-change=\"accountnoChange\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"上期止度:\"\r\n                prop=\"prevtotalreadings\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <InputNumber\r\n                  v-model=\"accountObj.prevtotalreadings\"\r\n                  placeholder=\"请输入上期止度\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"局站名称:\" prop=\"stationName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.stationName\"\r\n                  placeholder=\"请输入局站名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"项目名称:\" prop=\"projectname\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.projectname\"\r\n                  placeholder=\"请输入项目名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"电表/协议编码:\"\r\n                prop=\"ammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.ammetercode\"\r\n                  placeholder=\"请输入电表户号/协议编码\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"供电局电表编号:\"\r\n                prop=\"supplybureauammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.supplybureauammetercode\"\r\n                  placeholder=\"请输入供电局电表编号\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"用电类型:\" prop=\"classifications\" class=\"form-line-height\">\r\n                <Cascader\r\n                  :data=\"classificationData\"\r\n                  v-model=\"classifications\"\r\n                  :style=\"formItemWidth\"\r\n                ></Cascader>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"是否退回：\" prop=\"status\" class=\"form-line-height\">\r\n                <Select v-model=\"accountObj.status\" :style=\"formItemWidth\">\r\n                  <Option value=\"\">请选择</Option>\r\n                  <Option value=\"5\">是</Option>\r\n                  <Option value=\"1\">否</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\" v-if=\"companyListSize > 1\">\r\n              <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.company\"\r\n                  @on-change=\"selectChange(accountObj.company)\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option v-for=\"item in CompanyList\" :value=\"item.id\" :key=\"item.id\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize == 1 && resCenterListSize > 1\">\r\n              <FormItem label=\"所属部门：\" prop=\"country\" class=\"form-line-height\">\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option v-for=\"item in resCenterList\" :value=\"item.id\" :key=\"item.id\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize == 1 && resCenterListSize == 1\">\r\n              <FormItem label=\"台账填写人：\" prop=\"country\" class=\"form-line-height\">\r\n                <Input\r\n                  :style=\"formItemWidth\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"userName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  clearable\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize > 1\">\r\n              <FormItem label=\"所属部门：\" prop=\"country\" class=\"form-line-height\">\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option v-for=\"item in resCenterList\" :value=\"item.id\" :key=\"item.id\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize > 1 || resCenterListSize > 1\">\r\n              <FormItem label=\"台账填写人：\" prop=\"country\" class=\"form-line-height\">\r\n                <Input\r\n                  :style=\"formItemWidth\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"userName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  clearable\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"对外结算类型：\"\r\n                prop=\"directsupplyflag\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Select v-model=\"accountObj.directsupplyflag\" :style=\"formItemWidth\">\r\n                  <Option\r\n                    v-for=\"item in directsupplyflags\"\r\n                    :value=\"item.typeCode\"\r\n                    :key=\"item.typeCode\"\r\n                    >{{ item.typeName }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <div align=\"right\">\r\n            <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n            <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n            <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n              <Button type=\"default\" style=\"margin-left: 5px\"\r\n                >导出\r\n                <Icon type=\"ios-arrow-down\"></Icon>\r\n              </Button>\r\n              <DropdownMenu slot=\"list\">\r\n                <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n          </div>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon\r\n          :type=\"filterColl ? 'md-arrow-dropup' : 'md-arrow-dropdown'\"\r\n          size=\"20\"\r\n          @click=\"filterColl = !filterColl\"\r\n          :color=\"filterColl ? '#000' : '#1ab394'\"\r\n        ></icon>\r\n      </div>\r\n      <div>\r\n        <Modal\r\n          v-model=\"meterModal\"\r\n          title=\"峰平谷信息\"\r\n          width=\"50%\"\r\n          @on-ok=\"setFPG(currentRow)\"\r\n        >\r\n          <Form ref=\"meterForm\" :model=\"currentRow\" :label-width=\"80\" inline>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">本期起度</Col>\r\n              <Col span=\"8\" align=\"center\">本期止度</Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">加减</Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"峰:\" prop=\"prevhighreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editprevhighreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"addFremark(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"\" prop=\"curhighreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editcurhighreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"setcurhighreadings(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">\r\n                <FormItem label=\"\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"edithighreadings\"\r\n                    :readonly=\"readonly\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"平:\" prop=\"prevflatreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editprevflatreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"addPremark(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"\" prop=\"curflatreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editcurflatreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"setcurflatreadings(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">\r\n                <FormItem label=\"\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editflatreadings\"\r\n                    :readonly=\"readonly\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"谷:\" prop=\"prevlowreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editprevlowreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"addGremark(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"\" prop=\"curlowreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editcurlowreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"setcurlowreadings(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">\r\n                <FormItem label=\"\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editlowreadings\"\r\n                    :readonly=\"readonly\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n            </Row>\r\n          </Form>\r\n        </Modal>\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"startModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"startModalOk\"\r\n          @on-cancel=\"cancel\"\r\n          ><p>\r\n            是否确定更改本期起始日期？保存后从当前修改的起始日期前无法填入台帐！(可删除保存解除限制)\r\n          </p></Modal\r\n        >\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"qdModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"qdModalOk\"\r\n          @on-cancel=\"cancel\"\r\n          ><p>是否确定更改本期起度?</p></Modal\r\n        >\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"fbModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"fbModalOk\"\r\n          @on-cancel=\"qdcancel\"\r\n          ><p>是否翻表?</p></Modal\r\n        >\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"qxfbModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"qxfbModalOk\"\r\n          @on-cancel=\"qdcancel\"\r\n          ><p>是否取消翻表?</p></Modal\r\n        >\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"cl-table\">\r\n      <Row class=\"button-bar\">\r\n        <div class=\"table-button\">\r\n          <Button type=\"success\" @click=\"preserve()\">保存</Button>\r\n          <Button type=\"error\" @click=\"deleteAll()\">一键删除</Button>\r\n          <Button type=\"error\" @click=\"remove()\">删除</Button>\r\n          <Upload\r\n            style=\"float: right\"\r\n            :on-format-error=\"handleFormatError\"\r\n            :before-upload=\"onExcelUpload\"\r\n            :on-progress=\"handleProgress\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :max-size=\"10240\"\r\n            action=\"_blank\"\r\n            accept=\".csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\r\n            :format=\"['xls', 'xlsx']\"\r\n          >\r\n            <Button icon=\"ios-cloud-upload\">导入Excel</Button>\r\n          </Upload>\r\n          <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n            <Button type=\"info\" style=\"margin-left: 5px\"\r\n              >加入归集单\r\n              <Icon type=\"ios-arrow-down\"></Icon>\r\n            </Button>\r\n            <DropdownMenu slot=\"list\">\r\n              <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n              <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n          <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n        </div>\r\n      </Row>\r\n      <Table\r\n        ref=\"accountTable\"\r\n        border\r\n        :columns=\"accountTb.columns\"\r\n        :data=\"insideData\"\r\n        class=\"mytable\"\r\n        :loading=\"accountTb.loading\"\r\n        :height=\"tableHeight\"\r\n      >\r\n        <div></div>\r\n        <!--其他-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"address\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'address' + index + 0\"\r\n              type=\"text\"\r\n              v-model=\"editaddress\"\r\n              @on-blur=\"validateStartdate0\"\r\n              v-if=\"editIndex === index && columnsIndex === 0\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].address\"\r\n              @click=\"selectCall(row, index, 0, 'address')\"\r\n              v-else\r\n              >{{ row.address }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.address }}</span>\r\n          </div>\r\n        </template>\r\n        <!--项目名称-->\r\n        <template slot-scope=\"{ row }\" slot=\"projectname\">\r\n          <div v-if=\"row.status === 5\">\r\n            <span>{{ row.projectname }}</span\r\n            ><span style=\"color: red\">[退回]</span>\r\n          </div>\r\n          <span v-else>{{ row.projectname }}</span>\r\n        </template>\r\n        <!--起始时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"startdate\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'startdate' + index + 1\"\r\n              class=\"myinput\"\r\n              type=\"text\"\r\n              @on-blur=\"validate\"\r\n              v-model=\"editStartDate\"\r\n              v-if=\"editIndex === index && columnsIndex === 1\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].startdate\"\r\n              @click=\"selectCall(row, index, 1, 'startdate')\"\r\n              v-else\r\n              >{{ row.startdate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.startdate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--截止时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"enddate\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'enddate' + index + 2\"\r\n              type=\"text\"\r\n              v-model=\"editEndDate\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 2\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].enddate\"\r\n              @click=\"selectCall(row, index, 2, 'enddate')\"\r\n              v-else\r\n              >{{ row.enddate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.enddate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--起度-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"prevtotalreadings\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'prevtotalreadings' + index + 3\"\r\n              type=\"text\"\r\n              v-model=\"editPrevtotalreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 3 && !row.isFPG && !row.isWB\"\r\n            />\r\n            <span @click=\"openModal(index)\" v-else-if=\"row.isFPG\">峰平谷</span>\r\n            <span\r\n              :class=\"myStyle[index].prevtotalreadings\"\r\n              @click=\"selectCall(row, index, 3, 'prevtotalreadings')\"\r\n              v-else\r\n              >{{ row.prevtotalreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span @click=\"openModal(index)\" v-if=\"row.isFPG\">峰平谷</span>\r\n            <span v-else>{{ row.prevtotalreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--止度-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"curtotalreadings\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'curtotalreadings' + index + 4\"\r\n              type=\"text\"\r\n              v-model=\"editcurtotalreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 4 && !row.isFPG && !row.isWB\"\r\n            />\r\n            <span @click=\"openModal(index)\" v-else-if=\"row.isFPG\">峰平谷</span>\r\n            <span\r\n              :class=\"myStyle[index].curtotalreadings\"\r\n              @click=\"selectCall(row, index, 4, 'curtotalreadings')\"\r\n              v-else\r\n              >{{ row.curtotalreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span @click=\"openModal(index)\" v-if=\"row.isFPG\">峰平谷</span>\r\n            <span v-else>{{ row.curtotalreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--电损-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"transformerullage\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'transformerullage' + index + 5\"\r\n              type=\"text\"\r\n              v-model=\"edittransformerullage\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 5 && !row.isWB\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].transformerullage\"\r\n              @click=\"selectCall(row, index, 5, 'transformerullage')\"\r\n              v-else\r\n              >{{ row.transformerullage }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.transformerullage }}</span>\r\n          </div>\r\n        </template>\r\n        <template slot-scope=\"{ row }\" slot=\"curusedreadings\">\r\n          <Tooltip\r\n            v-if=\"row.total == null\"\r\n            placement=\"top\"\r\n            max-width=\"200\"\r\n            :content=\"'上期电量:' + row.curusedreadingsold\"\r\n            :disabled=\"\r\n              row.ammeteruse == '1' &&\r\n              row.curusedreadingsold &&\r\n              (row.curusedreadings - row.curusedreadingsold) / row.curusedreadings > 0.2\r\n                ? false\r\n                : true\r\n            \"\r\n          >\r\n            <span\r\n              :style=\"\r\n                row.ammeteruse == '1' &&\r\n                row.curusedreadingsold &&\r\n                (row.curusedreadings - row.curusedreadingsold) / row.curusedreadings > 0.2\r\n                  ? { color: 'orange', 'font-size': '14px' }\r\n                  : {}\r\n              \"\r\n              >{{ row.curusedreadings }}</span\r\n            >\r\n          </Tooltip>\r\n          <span v-else>{{ row.curusedreadings }}</span>\r\n        </template>\r\n        <template slot-scope=\"{ row }\" slot=\"unitpirce\">\r\n          <Tooltip\r\n            v-if=\"row.total == null\"\r\n            placement=\"top\"\r\n            max-width=\"200\"\r\n            :content=\"'上期单价:' + row.unitpirceold\"\r\n            :disabled=\"\r\n              row.ammeteruse == '1' &&\r\n              row.unitpirceold &&\r\n              (row.unitpirce - row.unitpirceold) / row.unitpirce > 0.2\r\n                ? false\r\n                : true\r\n            \"\r\n          >\r\n            <span\r\n              :style=\"\r\n                row.ammeteruse == '1' &&\r\n                row.unitpirceold &&\r\n                (row.unitpirce - row.unitpirceold) / row.unitpirce > 0.2\r\n                  ? { color: 'orange', 'font-size': '14px' }\r\n                  : {}\r\n              \"\r\n              >{{ row.unitpirce }}</span\r\n            >\r\n          </Tooltip>\r\n          <span v-else>{{ row.unitpirce }}</span>\r\n        </template>\r\n        <!--专票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputtaxticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputtaxticketmoney' + index + 6\"\r\n              type=\"text\"\r\n              v-model=\"edittaxticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 6\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputtaxticketmoney\"\r\n              @click=\"selectCall(row, index, 6, 'inputtaxticketmoney')\"\r\n              v-else\r\n              >{{ row.inputtaxticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputtaxticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--普票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputticketmoney' + index + 7\"\r\n              type=\"text\"\r\n              v-model=\"editticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 7\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputticketmoney\"\r\n              @click=\"selectCall(row, index, 7, 'inputticketmoney')\"\r\n              v-else\r\n              >{{ row.inputticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--普票税额-->\r\n        <template slot-scope=\"{ row }\" slot=\"tickettaxamount\">\r\n          <!-- <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'tickettaxamount' + index + 8\"\r\n              type=\"text\"\r\n              v-model=\"edittickettaxamount\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 8\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].tickettaxamount\"\r\n              @click=\"selectCall(row, index, 8, 'tickettaxamount')\"\r\n              v-else\r\n              >{{ row.tickettaxamount }}</span\r\n            >\r\n          </div>\r\n          <div v-else> -->\r\n          <span>{{ row.tickettaxamount }}</span>\r\n          <!-- </div> -->\r\n        </template>\r\n        <!--税率-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"taxrate\">\r\n          <div v-if=\"row.total == null\">\r\n            <Select\r\n              :ref=\"'taxrate' + index + 9\"\r\n              type=\"text\"\r\n              v-model=\"edittaxrate\"\r\n              @on-change=\"settaxrate\"\r\n              v-if=\"editIndex === index && columnsIndex === 9\"\r\n              transfer=\"true\"\r\n            >\r\n              <Option selected value=\"13\">13</Option>\r\n              <Option value=\"1\">1</Option>\r\n              <Option value=\"3\">3</Option>\r\n              <Option value=\"6\">6</Option>\r\n              <Option value=\"16\">16</Option>\r\n              <Option value=\"17\">17</Option>\r\n            </Select>\r\n            <span\r\n              :class=\"myStyle[index].taxrate\"\r\n              @click=\"selectCall(row, index, 9, 'taxrate')\"\r\n              v-else\r\n              >{{ row.taxrate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.taxrate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--其他-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"ullagemoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'ullagemoney' + index + 10\"\r\n              type=\"text\"\r\n              v-model=\"editullagemoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 10\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].ullagemoney\"\r\n              @click=\"selectCall(row, index, 10, 'ullagemoney')\"\r\n              v-else\r\n              >{{ row.ullagemoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.ullagemoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--备注-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              v-model=\"editremark\"\r\n              :ref=\"'remark' + index + 11\"\r\n              type=\"text\"\r\n              @on-blur=\"validateRemark\"\r\n              v-if=\"editIndex === index && columnsIndex === 11\"\r\n            />\r\n            <Tooltip placement=\"bottom\" max-width=\"200\" v-else>\r\n              <span\r\n                :class=\"myStyle[index].remark\"\r\n                style=\"width: 60px\"\r\n                @click=\"selectCall(row, index, 11, 'remark')\"\r\n                >{{ ellipsis(row) }}</span\r\n              >\r\n              <div slot=\"content\">\r\n                {{ row.remark + row.bz }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n          <div v-else>\r\n            <Tooltip placement=\"bottom\" max-width=\"200\">\r\n              <span>{{ ellipsis(row) }}</span>\r\n              <div slot=\"content\">\r\n                {{ row.remark + row.bz }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n        </template>\r\n      </Table>\r\n      <div class=\"table-page\">\r\n        <Page\r\n          size=\"small\"\r\n          :total=\"pageTotal\"\r\n          :current=\"pageNum\"\r\n          :page-size=\"pageSize\"\r\n          show-elevator\r\n          show-sizer\r\n          show-total\r\n          placement=\"top\"\r\n          @on-change=\"handlePage\"\r\n          @on-page-size-change=\"handlePageSize\"\r\n        ></Page>\r\n      </div>\r\n      <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n    </div>\r\n    <add-bill-per\r\n      ref=\"addBillPer\"\r\n      v-on:refreshList=\"refresh\"\r\n      @buttonload2=\"buttonload2\"\r\n      @isButtonload=\"isButtonload\"\r\n    ></add-bill-per>\r\n    <query-people-modal\r\n      ref=\"queryPeople\"\r\n      v-on:getDataFromModal=\"getDataFromModal\"\r\n    ></query-people-modal>\r\n\r\n    <upload-file-modal ref=\"uploadFileModal\" v-on:onchange=\"change\"></upload-file-modal>\r\n    <!--    稽核modal-->\r\n    <Modal\r\n      v-model=\"showCheckModel\"\r\n      width=\"80%\"\r\n      title=\"稽核结果及反馈\"\r\n      :mask-closable=\"false\"\r\n    >\r\n      <check-result-and-response ref=\"chekResultAndResponse\"></check-result-and-response>\r\n    </Modal>\r\n    <Modal v-model=\"showJhModel\" width=\"80%\" title=\"稽核结果\" :mask-closable=\"false\">\r\n      <check-result ref=\"checkResult\"></check-result>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button type=\"primary\" @click=\"nextCheck\">已查阅</Button>\r\n        <Button type=\"text\" @click=\"checkCancel\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <!-- @on-cancel=\"alarmClose\" -->\r\n    <Modal\r\n      v-model=\"showAlarmModel\"\r\n      width=\"80%\"\r\n      title=\"台账预警稽核\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n      class=\"yjjh\"\r\n    >\r\n      <alarm-check\r\n        ref=\"showAlarmModel\"\r\n        @save=\"save\"\r\n        @submitChange=\"submitChange\"\r\n        @close=\"alarmClose\"\r\n        :auditResultData=\"auditResultList\"\r\n        :isShow=\"isShow\"\r\n      ></alarm-check>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button size=\"large\" @click=\"alarmClose\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { mapState } from \"vuex\";\r\nimport { getAuditResult, getAuditResultNew, getAuditResultNew_QXM } from \"@/api/account\";\r\nimport {\r\n  addSelfPowerAccount,\r\n  getElectrQuota,\r\n  editOwn,\r\n  removeOwn,\r\n  selectByPcid,\r\n  getUser,\r\n  getDepartments,\r\n  selectByAmmeterId,\r\n  accountTotal,\r\n  selectCompletedMoney,\r\n} from \"@/api/accountSC/accountSC\";\r\nimport { attchList, getClassification, getUserdata } from \"@/api/basedata/ammeter.js\";\r\nimport {\r\n  getDates,\r\n  cutDate_yyyymmdd,\r\n  testNumber,\r\n  GetDateDiff,\r\n  stringToDate,\r\n  getCurrentDate,\r\n} from \"@/view/account/powerAccountHelper\";\r\nimport axios from \"@/libs/api.request\";\r\nimport {\r\n  _verify_StartDate,\r\n  judgeNumber,\r\n  _verify_EndDate,\r\n  _verify_PrevTotalReadings,\r\n  _verify_CurTotalReadings,\r\n  other_no_ammeteror_protocol,\r\n  self_no_ammeteror_protocol,\r\n  HFL_ammeteror,\r\n  judging_editability,\r\n  judging_editability1,\r\n  _verify_Money,\r\n  _calculateUsedReadings,\r\n  _calculateUsedReadingsForType_2,\r\n  _calculateTotalReadings,\r\n  _calculateUnitPriceByUsedMoney,\r\n  _calculateAccountMoney,\r\n  _calculateQuotereadingsratio,\r\n  requiredFieldValidator,\r\n  countTaxamount,\r\n  calculateActualMoney,\r\n  judge_negate,\r\n  judge_recovery,\r\n  judge_yb,\r\n  unitpirceMin,\r\n  unitpirceMax,\r\n  unitpirceMax1,\r\n  countTaxAmounttt,\r\n  countAccountMoney,\r\n  countTicketmoney,\r\n  computeUnitPrices,\r\n  computeUnitPrices_hb,\r\n} from \"@/view/account/PowerAccountController\";\r\nimport AddBillPer from \"@/view/account/addBillPreModal\";\r\nimport { againJoin } from \"@/api/accountBillPer\";\r\nimport { widthstyle } from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nimport excel from \"@/libs/excel\";\r\nimport QueryPeopleModal from \"@/view/account/queryPeopleModal\";\r\nimport indexData from \"@/config/index\";\r\nimport { selectIdsByParams, removeAll } from \"@/api/account\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport UploadFileModal from \"@/view/account/uploadFileModal\";\r\nimport alarmCheck from \"@/view/account/check/alarmCheck\";\r\nimport checkResult from \"@/view/account/check/checkResult\";\r\nimport checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\nimport { validContractList } from \"@/api/contract\";\r\nimport permissionMixin from \"@/mixins/permission\";\r\nimport pageFun from \"@/mixins/pageFun\";\r\n\r\nlet dates = getDates();\r\n\r\nexport default {\r\n  mixins: [permissionMixin, pageFun],\r\n  components: {\r\n    alarmCheck,\r\n    checkResult,\r\n    checkResultAndResponse,\r\n    QueryPeopleModal,\r\n    AddBillPer,\r\n    UploadFileModal,\r\n  },\r\n  data() {\r\n    let renderCategory = (h, params) => {\r\n      var categoryname = \"\";\r\n      for (let item of this.categorys) {\r\n        if (item.typeCode == params.row.category) {\r\n          categoryname = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", categoryname);\r\n    };\r\n    let iftimeout = (h, { row, index }) => {\r\n      const { map, projectname } = row;\r\n      let color = \"green\";\r\n      let str = \"正常\";\r\n      if (map) {\r\n        if (map.iftimeout == \"3\") {\r\n          color = \"red\";\r\n          str = \"当前台账对应局站已到期,请上传附件说明\";\r\n        }\r\n      }\r\n\r\n      return h(\r\n        \"Tooltip\",\r\n        {\r\n          style: {\r\n            color: color,\r\n          },\r\n          props: {\r\n            placement: \"bottom-start\",\r\n          },\r\n        },\r\n        [\r\n          projectname,\r\n          h(\r\n            \"span\",\r\n            {\r\n              slot: \"content\",\r\n              style: {\r\n                whiteSpace: \"normal\",\r\n                wordBreak: \"break-all\",\r\n              },\r\n            },\r\n            str\r\n          ),\r\n        ]\r\n      );\r\n    };\r\n    let renderDirectsupplyflag = (h, params) => {\r\n      var directsupplyflag = \"\";\r\n      for (let item of this.directsupplyflags) {\r\n        if (item.typeCode == params.row.directsupplyflag) {\r\n          directsupplyflag = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", directsupplyflag);\r\n    };\r\n    let photo = (h, { row, index }) => {\r\n      let that = this;\r\n      let str = \"\";\r\n      if (row.projectname != \"小计\" && row.projectname != \"合计\") {\r\n        str = \"上传\";\r\n      }\r\n      return h(\"div\", [\r\n        h(\r\n          \"u\",\r\n          {\r\n            on: {\r\n              click() {\r\n                //打开弹出框\r\n                if (row.projectname != \"小计\" && row.projectname != \"合计\") {\r\n                  that.uploadFile(row);\r\n                }\r\n              },\r\n            },\r\n          },\r\n          str\r\n        ),\r\n      ]);\r\n    };\r\n    return {\r\n      contractCount: 0,\r\n      isT: true,\r\n      number2: 0,\r\n      isShow: false,\r\n      name: \"\",\r\n      isQuery: true,\r\n      number: 0,\r\n      auditResultList: [],\r\n      // auditResultList: {\r\n\r\n      // },\r\n      submitL: [],\r\n      submit: [],\r\n      submit2: [],\r\n      ammeterids: [],\r\n      showCheckModel: false,\r\n      showAlarmModel: false,\r\n      showJhModel: false,\r\n      version: indexData.version,\r\n      valiprice: true,\r\n      formatArray: [\"\"],\r\n      formItemWidth: widthstyle,\r\n      tableName: 1,\r\n      dateList: dates,\r\n      categorys: [], //描述类型\r\n      spinShow: false, //导入数据遮罩\r\n      filterColl: true, //搜索面板展开\r\n      startModal: false, //起始时间修改提示\r\n      qdModal: false, //起度修改提示\r\n      fbModal: false, //翻表提示\r\n      qxfbModal: false, //取消翻表提示\r\n      readonly: false, //封平谷只读\r\n      editIndex: -1, //当前编辑行\r\n      columnsIndex: -1, //当前编辑列\r\n      classificationData: [], //用电类型树\r\n      classifications: [], //选择的用电类型树\r\n      directsupplyflags: [],\r\n      editStartDate: \"\",\r\n      editEndDate: \"\",\r\n      editPrevtotalreadings: \"\",\r\n      editcurtotalreadings: \"\",\r\n      edittransformerullage: \"\",\r\n      edittaxticketmoney: \"\",\r\n      editticketmoney: \"\",\r\n      edittickettaxamount: \"\",\r\n      editullagemoney: \"\",\r\n      editpercent: \"\",\r\n      edittaxrate: \"\",\r\n      editremark: \"\",\r\n      editaddress: \"\",\r\n      editprevhighreadings: 0,\r\n      editprevflatreadings: 0,\r\n      editprevlowreadings: 0,\r\n      editcurhighreadings: 0,\r\n      editcurflatreadings: 0,\r\n      editcurlowreadings: 0,\r\n      edithighreadings: 0,\r\n      editflatreadings: 0,\r\n      editlowreadings: 0,\r\n      pabriid: \"\",\r\n      resCenterList: [],\r\n      CompanyList: [],\r\n      companyListSize: \"\",\r\n      resCenterListSize: \"\",\r\n      iftimeout: \"\",\r\n      insideData: [], //数据\r\n      insideDataFirst: [], //数据\r\n      iftimeoutShow: true,\r\n      myStyle: [], //样式\r\n      pageTotal: 0,\r\n      pageNum: 1,\r\n      pageSize: 10, //当前页\r\n      currentRow: {},\r\n      meterModal: false,\r\n      ifMaxdegree: null,\r\n      againJoinIds: \"\",\r\n      export: {\r\n        run: false, //是否正在执行导出\r\n        data: \"\", //导出数据\r\n        totalPage: 0, //一共多少页\r\n        currentPage: 0, //当前多少页\r\n        percent: 0,\r\n        size: ********,\r\n      },\r\n      accountObj: {\r\n        supplybureauammetercode: \"\",\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        stationName: \"\", //局站名称\r\n        projectname: \"\", //项目名称\r\n        prevtotalreadings: null, //上期止度\r\n        ammetercode: \"\", //电表户号/协议编码\r\n        status: \"\", //是否退回\r\n        company: \"\",\r\n        country: \"\",\r\n        electrotype: \"\", //用电类型\r\n        accountType: \"2\", //台账类型\r\n        userId: \"\",\r\n        version: \"\",\r\n        directsupplyflag: \"\",\r\n      },\r\n      userName: \"\", //查询选择人员名称\r\n      avemoney: 0,\r\n      nowdatediff: 0,\r\n      accountTb: {\r\n        loading: false,\r\n        columns: [\r\n          { type: \"selection\", width: 30, align: \"center\", fixed: \"left\" },\r\n          {\r\n            title: \"稽核结果及反馈\",\r\n            key: \"action\",\r\n            fixed: \"left\",\r\n            align: \"center\",\r\n            render: (h, params) => {\r\n              var that = this;\r\n              return h(\r\n                \"Button\",\r\n                {\r\n                  props: {\r\n                    type: \"primary\",\r\n                    size: \"small\",\r\n                  },\r\n                  style: {\r\n                    \"font-size\": \"10px\",\r\n                  },\r\n                  on: {\r\n                    click() {\r\n                      that.$refs.chekResultAndResponse.pcid = params.row.pcid;\r\n                      that.showCheckModel = true;\r\n                    },\r\n                  },\r\n                },\r\n                \"稽核结果及反馈\"\r\n              );\r\n            },\r\n            minWidth: 100,\r\n            maxWidth: 150,\r\n          },\r\n          { title: \"地址\", slot: \"address\", align: \"center\", width: 60, fixed: \"left\" },\r\n          {\r\n            title: \"项目名称\",\r\n            slot: \"projectname\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n            render: iftimeout,\r\n          },\r\n          {\r\n            title: \"电表户号/协议编码\",\r\n            key: \"ammetercode\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"供电局编码\",\r\n            key: \"supplybureauammetercode\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          { title: \"期号\", key: \"accountno\", align: \"center\", width: 40, fixed: \"left\" },\r\n          { title: \"用电类型\", key: \"electrotypename\", align: \"center\", width: 94 },\r\n          {\r\n            title: \"实际普票含税金额(元)\",\r\n            key: \"ticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n          },\r\n          {\r\n            title: \"实际专票含税金额(元)\",\r\n            key: \"taxticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n          },\r\n          { title: \"总电量(度)\", key: \"totalusedreadings\", align: \"center\", width: 60 },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"类型描述\",\r\n            key: \"categoryname\",\r\n            align: \"center\",\r\n            width: 94,\r\n            render: renderCategory,\r\n          },\r\n          { title: \"倍率\", key: \"magnification\", align: \"center\", width: 60 },\r\n          { title: \"定额\", key: \"quotareadings\", align: \"center\", width: 60 },\r\n          { title: \"浮动比（%）\", key: \"quotereadingsratio\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            align: \"center\",\r\n            fixed: \"left\",\r\n            width: 82,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            align: \"center\",\r\n            width: 82,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"本期起度\",\r\n            slot: \"prevtotalreadings\",\r\n            align: \"center\",\r\n            width: 70,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"本期止度\",\r\n            slot: \"curtotalreadings\",\r\n            align: \"center\",\r\n            width: 70,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"电损(度)\",\r\n            slot: \"transformerullage\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"用电量(度)\",\r\n            slot: \"curusedreadings\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"电价(元)\",\r\n            slot: \"unitpirce\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"专票含税金额(元)\",\r\n            slot: \"inputtaxticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"普票金额(元)\",\r\n            slot: \"inputticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"普票税额(元)\",\r\n            slot: \"tickettaxamount\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"税率（%）\",\r\n            slot: \"taxrate\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          { title: \"税额\", key: \"taxamount\", align: \"center\", width: 60, fixed: \"left\" },\r\n          {\r\n            title: \"其他(元)\",\r\n            slot: \"ullagemoney\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"实缴费用(元)含税\",\r\n            key: \"accountmoney\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          { title: \"备注\", slot: \"remark\", align: \"center\", width: 80, fixed: \"left\" },\r\n          { title: \"电信分割比例(%)\", align: \"center\", key: \"percent\", width: 80 },\r\n          {\r\n            title: \"移动分割比例(%)\",\r\n            align: \"center\",\r\n            key: \"mobileApportionmentratio\",\r\n            width: 80,\r\n          },\r\n          {\r\n            title: \"联通分割比例(%)\",\r\n            align: \"center\",\r\n            key: \"unicomApportionmentratio\",\r\n            width: 80,\r\n          },\r\n          {\r\n            title: \"扩展分割比例(%)\",\r\n            align: \"center\",\r\n            key: \"expandApportionmentratio\",\r\n            width: 80,\r\n          },\r\n          {\r\n            title: \"能源分割比例(%)\",\r\n            align: \"center\",\r\n            key: \"energyApportionmentratio\",\r\n            width: 80,\r\n          },\r\n          {\r\n            title: \"对外结算类型\",\r\n            align: \"center\",\r\n            key: \"directsupplyflag\",\r\n            width: 80,\r\n            render: renderDirectsupplyflag,\r\n          },\r\n        ],\r\n        fileColumn: [\r\n          {\r\n            title: \"附件\",\r\n            slot: \"file\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n            render: photo,\r\n          },\r\n        ],\r\n        data: [],\r\n        exportcolumns: [\r\n          { title: \"错误信息\", key: \"error\" },\r\n          { title: \"注意信息\", key: \"careful\" },\r\n          { title: \"基站名称（项目名称）\", key: \"projectname\" },\r\n          { title: \"站址编码\", key: \"stationaddresscode\" },\r\n          { title: \"供电局编码\", key: \"supplybureauammetercode\" },\r\n          { title: \"倍率\", key: \"magnification\", align: \"center\" },\r\n          { title: \"期号\", key: \"accountno\" },\r\n          { title: \"电表/协议id\", key: \"ammeterid\" },\r\n          { title: \"起始日期(必填)\", key: \"startdate\" },\r\n          { title: \"截止日期(必填)\", key: \"enddate\" },\r\n          { title: \"本期起度\", key: \"prevtotalreadings\" },\r\n          { title: \"本期止度\", key: \"curtotalreadings\" },\r\n          { title: \"用电量(度)\", key: \"curusedreadings\" },\r\n          { title: \"电损(度)\", key: \"transformerullage\" },\r\n          { title: \"电价(元)\", key: \"unitpirce\" },\r\n          { title: \"专票含税金额(元)\", key: \"inputtaxticketmoney\" },\r\n          { title: \"普票金额(元)\", key: \"inputticketmoney\" },\r\n          { title: \"普票税额(元)\", key: \"tickettaxamount\" },\r\n          { title: \"分割比例(%)\", key: \"percent\" },\r\n          { title: \"总电量(度)\", key: \"totalusedreadings\" },\r\n          { title: \"实缴费用(元)含税\", key: \"accountmoney\" },\r\n          { title: \"其他(元)\", key: \"ullagemoney\" },\r\n          { title: \"备注\", key: \"remark\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  mounted() {\r\n    this.handleHeight(); //table高度自定义\r\n\r\n    this.accountObj.version = indexData.version;\r\n    this.categorys = blist(\"ammeterCategory\");\r\n    this.directsupplyflags = blist(\"directSupplyFlag\");\r\n\r\n    getUser().then((res) => {\r\n      if (res.data.companies != null && res.data.companies.length > 0) {\r\n        this.CompanyList = res.data.companies;\r\n        this.companyListSize = res.data.companies.length;\r\n        //初始化默认展示登陆用户的第一个分公司和分公司下的部门\r\n        this.accountObj.company = this.CompanyList[0].id;\r\n        getDepartments(this.accountObj.company).then((res) => {\r\n          this.resCenterList = res.data;\r\n          this.resCenterListSize = res.data.length;\r\n          this.accountObj.country = res.data[0].id;\r\n\r\n          this.getAccountMessages();\r\n        });\r\n      }\r\n      //开放全省\r\n      this.accountTb.columns = this.accountTb.columns.concat(this.accountTb.fileColumn);\r\n    });\r\n\r\n    getClassification().then((res) => {\r\n      //用电类型\r\n      this.classificationData = res.data;\r\n    });\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      loginId: (state) => state.user.loginId,\r\n    }),\r\n  },\r\n  methods: {\r\n    buttonload2(v) {\r\n      this.$refs.showAlarmModel.buttonload2 = v;\r\n    },\r\n    isButtonload(v) {\r\n      this.$refs.showAlarmModel.buttonload = v;\r\n    },\r\n    submitChange(indexList) {\r\n      let data = [];\r\n      this.submit2.map((item, index) => {\r\n        indexList.map((item2) => {\r\n          if (index == item2) {\r\n            data.push(item);\r\n          }\r\n        });\r\n      });\r\n      this.submit = data;\r\n    },\r\n    submitChange1(data) {\r\n      this.submit = data;\r\n    },\r\n    getAuditResultNew(data) {\r\n      let arr = [];\r\n      data.forEach((item) => {\r\n        arr.push(item.pcid);\r\n      });\r\n      let param = {\r\n        pcids: arr,\r\n      };\r\n      getAuditResultNew_QXM(param).then((res2) => {\r\n        this.auditResultList = res2.data;\r\n        this.auditResultList.forEach((item) => {\r\n          this.$refs.showAlarmModel.resultList.push(item.msg);\r\n          this.$refs.showAlarmModel.tableData.push(item.powerAuditEntity);\r\n\r\n          if (item.staute == \"失败\") {\r\n            // if(item.powerAuditEntity.mutiJtlteCodes=='是'\r\n            // || item.powerAuditEntity.electricityPrices=='否'\r\n            // || item.powerAuditEntity.addressConsistence=='否'\r\n            // || item.powerAuditEntity.reimbursementCycle=='否'\r\n            // || item.powerAuditEntity.electricityContinuity=='否'\r\n            // || item.powerAuditEntity.shareAccuracy=='否' ||\r\n            // item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n            // item.powerAuditEntity.paymentConsistence=='否'){\r\n            if (item.powerAuditEntity.mutiJtlteCodes == \"是\") {\r\n              this.$refs.showAlarmModel.tableData4.push(item.powerAuditEntity); //一表多站\r\n              this.$refs.showAlarmModel.resultList4.push(item.msg);\r\n            }\r\n            if (item.powerAuditEntity.electricityPrices == \"否\") {\r\n              this.$refs.showAlarmModel.tableData5.push(item.powerAuditEntity); //单价\r\n              this.$refs.showAlarmModel.resultList5.push(item.msg);\r\n            }\r\n            if (\r\n              item.powerAuditEntity.addressConsistence == \"否\" ||\r\n              item.powerAuditEntity.reimbursementCycle == \"否\" ||\r\n              item.powerAuditEntity.electricityContinuity == \"否\" ||\r\n              item.powerAuditEntity.shareAccuracy == \"否\" ||\r\n              // item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n              item.powerAuditEntity.paymentConsistence == \"否\" ||\r\n              item.powerAuditEntity.fluctuateContinuity == \"否\"\r\n            ) {\r\n              this.$refs.showAlarmModel.tableData2.push(item.powerAuditEntity); //其他\r\n              this.$refs.showAlarmModel.resultList2.push(item.msg);\r\n            }\r\n            // }\r\n          } else {\r\n            if (\r\n              // item.powerAuditEntity.electricityRationality == \"是\" && //电量合理性(省内大数据)\r\n              // item.powerAuditEntity.exclusiveAccuracy == \"是\" && //局站独享共享设置\r\n              item.powerAuditEntity.periodicAnomaly == \"是\" //台账周期合理性\r\n            ) {\r\n              this.$refs.showAlarmModel.tableData1.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList1.push(item.msg);\r\n            } else {\r\n              this.$refs.showAlarmModel.tableData3.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList3.push(item.msg);\r\n            }\r\n          }\r\n          console.log(\r\n            ((this.auditResultList.length * 1) / data.length) * 1,\r\n            \"this.auditResultList.length*1/data.length*1\"\r\n          );\r\n          if (this.auditResultList.length > 0) {\r\n            this.auditResultList[this.auditResultList.length - 1].progress =\r\n              ((this.auditResultList.length * 1) / data.length) * 1;\r\n          }\r\n          this.$refs.showAlarmModel.processData = Number(item.progress) * 100;\r\n          this.$refs.showAlarmModel.scrollList();\r\n        });\r\n      });\r\n    },\r\n    save(value) {\r\n      var that = this;\r\n      //进度条页面链接websoket后调用，传jh字段不走保存流程，走稽核流程\r\n      if (value == 1) {\r\n        that.submit[0].jh = \"1\";\r\n        that.submit[0].ymmc = \"铁塔电费台账\";\r\n        this.submitL = that.submit;\r\n        this.getAuditResultNew(that.submit);\r\n      } else {\r\n        if (that.submit[0].hasOwnProperty(\"jh\")) {\r\n          delete that.submit[0].jh;\r\n        }\r\n        if (this.name == \"current\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAccount();\r\n          }\r\n        } else if (this.name == \"all\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAllAccount();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    isShows(t) {\r\n      this.$refs.showAlarmModel.show = t;\r\n      if (this.$refs.showAlarmModel.show == false) {\r\n        this.number2++;\r\n        this.isT = true;\r\n      } else if (this.$refs.showAlarmModel.show == true) {\r\n        this.number2 = 0;\r\n        this.isT = false;\r\n      }\r\n      if (this.isT && this.number2 < 10) {\r\n        this.isShows(t);\r\n      }\r\n    },\r\n    nextCheck() {\r\n      this.showAlarmModel = true;\r\n      this.isShows(true);\r\n      this.$refs.showAlarmModel.tableData = [];\r\n      this.$refs.showAlarmModel.resultList = [];\r\n      this.$refs.showAlarmModel.tableData1 = [];\r\n      this.$refs.showAlarmModel.resultList1 = [];\r\n      this.$refs.showAlarmModel.tableData2 = [];\r\n      this.$refs.showAlarmModel.resultList2 = [];\r\n      this.$refs.showAlarmModel.tableData3 = [];\r\n      this.$refs.showAlarmModel.resultList3 = [];\r\n      this.$refs.showAlarmModel.tableData4 = [];\r\n      this.$refs.showAlarmModel.resultList4 = [];\r\n      this.$refs.showAlarmModel.tableData5 = [];\r\n      this.$refs.showAlarmModel.resultList5 = [];\r\n      this.showJhModel = false;\r\n      this.$refs.showAlarmModel.activeButton = 6;\r\n    },\r\n    alarmClose() {\r\n      this.showAlarmModel = false;\r\n      this.$refs.showAlarmModel.show = false;\r\n    },\r\n    checkCancel() {\r\n      this.showJhModel = false;\r\n    },\r\n    alarmCheck() {},\r\n    selectChange() {\r\n      getDepartments(this.accountObj.company).then((res) => {\r\n        this.resCenterList = res.data;\r\n        this.resCenterListSize = res.data.length;\r\n        this.accountObj.country = res.data[0].id;\r\n      });\r\n    },\r\n    // 计算定额\r\n    getQuota: (ammeterid, startdate, enddate, callback) => {\r\n      if (ammeterid && startdate && enddate) {\r\n        getElectrQuota(ammeterid, startdate, enddate).then((res) => {\r\n          if (callback) callback(res);\r\n          else callback();\r\n        });\r\n      }\r\n    },\r\n    //删除时检查退回台账是否解除与归集单关联\r\n    getTem: (pcid, callback) => {\r\n      selectByPcid(pcid).then((res) => {\r\n        if (callback) callback(res);\r\n        else callback();\r\n      });\r\n    },\r\n    //翻页时先确认数据是否保存\r\n    handlePage(value) {\r\n      let b = false;\r\n      let data = this.insideData;\r\n      let array = [];\r\n      data.forEach(function (item, index, input) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n      this.pageNum = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //改变表格可显示数据数量时先确认数据是否保存\r\n    handlePageSize(value) {\r\n      let b = false;\r\n      let data = this.insideData;\r\n      let array = [];\r\n      data.forEach(function (item, index, input) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n      this.pageSize = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //向后台请求数据\r\n    getAccountMessages() {\r\n      this.setElectroyType();\r\n      let params = this.accountObj;\r\n      params.pageNum = this.pageNum;\r\n      params.pageSize = this.pageSize;\r\n      let req = {\r\n        url: \"/business/account/selfAccountList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      let array1 = [];\r\n      this.accountTb.loading = true;\r\n      axios\r\n        .request(req)\r\n        .then((res) => {\r\n          this.accountTb.loading = false;\r\n          if (res.data) {\r\n            array = res.data.rows;\r\n            array.push(this.suntotal(array)); //小计\r\n            accountTotal(this.accountObj).then((res) => {\r\n              //合计\r\n              let alltotal = res.data;\r\n              alltotal.total = \"合计\";\r\n              alltotal.projectname = \"合计\";\r\n              alltotal._disabled = true;\r\n              array.push(alltotal);\r\n            });\r\n            this.insideData = array;\r\n            this.pageTotal = res.data.total || 0;\r\n            this.setNewField(res.data.rows);\r\n            this.setMyStyle(res.data.rows.length);\r\n            this.editIndex = -1;\r\n            this.columnsIndex = -1;\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //小计\r\n    suntotal(array) {\r\n      let curusedreadings = 0;\r\n      let transformerullage = 0;\r\n      let ticketmoney = 0;\r\n      let taxticketmoney = 0;\r\n      let taxamount = 0;\r\n      let ullagemoney = 0;\r\n      let accountmoney = 0;\r\n      let inputtaxticketmoney = 0;\r\n      let inputticketmoney = 0;\r\n      let tickettaxamount = 0;\r\n      array.forEach(function (item) {\r\n        if (item.effective === 1) {\r\n          curusedreadings += item.curusedreadings;\r\n          transformerullage += item.transformerullage;\r\n          ticketmoney += item.ticketmoney;\r\n          taxticketmoney += item.taxticketmoney;\r\n          taxamount += item.taxamount;\r\n          inputtaxticketmoney += item.inputtaxticketmoney;\r\n          inputticketmoney += item.inputticketmoney;\r\n          ullagemoney += item.ullagemoney;\r\n          accountmoney += item.accountmoney;\r\n          tickettaxamount += item.tickettaxamount;\r\n        }\r\n      });\r\n      return {\r\n        curusedreadings: curusedreadings,\r\n        transformerullage: transformerullage,\r\n        ticketmoney: ticketmoney.toFixed(2),\r\n        taxticketmoney: taxticketmoney.toFixed(2),\r\n        taxamount: taxamount.toFixed(2),\r\n        inputtaxticketmoney: inputtaxticketmoney.toFixed(2),\r\n        inputticketmoney: inputticketmoney.toFixed(2),\r\n        ullagemoney: ullagemoney.toFixed(2),\r\n        accountmoney: accountmoney.toFixed(2),\r\n        tickettaxamount: tickettaxamount.toFixed(2),\r\n        total: \"小计\",\r\n        projectname: \"小计\",\r\n        _disabled: true,\r\n      };\r\n    },\r\n    searchList() {\r\n      this.pageNum = 1;\r\n      this.getAccountMessages();\r\n    },\r\n    onResetHandle() {\r\n      this.accountObj = {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        substation: \"\", //支局\r\n        projectname: \"\", //项目名称\r\n        prevtotalreadings: null, //上期止度\r\n        ammetercode: \"\", //电表户号/协议编码\r\n        isreturn: \"\", //是否退回\r\n        company: this.CompanyList[0].id,\r\n        userId: \"\",\r\n        accountType: \"2\", //台账类型\r\n        supplybureauammetercode: \"\",\r\n      };\r\n      this.userName = \"\";\r\n      this.classifications = [];\r\n      this.selectChange();\r\n      this.getAccountMessages();\r\n    },\r\n    //保存可编辑表格的初始化数据\r\n    setNewField(data) {\r\n      data.forEach(function (item) {\r\n        item.old_startdate = item.startdate;\r\n        item.old_prevtotalreadings = item.prevtotalreadings;\r\n\r\n        item.multtimes = item.magnification;\r\n        item.old_enddate = item.enddate;\r\n        item.old_curtotalreadings = item.curtotalreadings;\r\n        item.old_transformerullage = item.transformerullage;\r\n        item.old_taxticketmoney = item.inputtaxticketmoney;\r\n        item.old_ticketmoney = item.inputticketmoney;\r\n        item.old_ullagemoney = item.ullagemoney;\r\n        item.old_prevhighreadings = item.prevhighreadings;\r\n        item.old_prevflatreadings = item.prevflatreadings;\r\n        item.old_prevlowreadings = item.prevlowreadings;\r\n        item.old_tickettaxamount = item.tickettaxamount;\r\n\r\n        item.old_curhighreadings = item.curhighreadings;\r\n        item.old_curflatreadings = item.curflatreadings;\r\n        item.old_curlowreadings = item.curlowreadings;\r\n        item.old_curtotalreadings = item.curtotalreadings;\r\n\r\n        item.version = indexData.version;\r\n        item.editType = 0;\r\n        item.isFPG = judging_editability1(item);\r\n        item.isWB = judging_editability(item);\r\n        if (!item.remark) item.remark = \"\";\r\n        if (!item.bz) item.bz = \"\";\r\n        item.transformerullage = judgeNumber(item.transformerullage);\r\n        item.inputtaxticketmoney = judgeNumber(item.inputtaxticketmoney);\r\n        item.inputticketmoney = judgeNumber(item.inputticketmoney);\r\n        item.taxticketmoney = judgeNumber(item.taxticketmoney);\r\n        item.ticketmoney = judgeNumber(item.ticketmoney);\r\n        item.ullagemoney = judgeNumber(item.ullagemoney);\r\n        item.curusedreadings = judgeNumber(item.curusedreadings);\r\n        item.accountmoney = judgeNumber(item.accountmoney);\r\n        item.tickettaxamount = judgeNumber(item.tickettaxamount);\r\n        item.unitpirce = judgeNumber(item.unitpirce);\r\n        item.taxamount = judgeNumber(item.taxamount);\r\n        if ((item.taxrate == null || item.taxrate == 0) && item.total == null) {\r\n          item.taxrate = \"13\";\r\n        }\r\n      });\r\n    },\r\n    //计算 用电量,总电量,单价,总费用,浮动比.\r\n    calculateAll(row) {\r\n      console.log(row, \"row\");\r\n      row.curusedreadings = _calculateUsedReadings(row);\r\n      row.totalusedreadings = _calculateTotalReadings(row);\r\n      // if (row.ticketmoney || row.taxticketmoney) {\r\n      if (row.taxticketmoney) {\r\n        row.accountmoney = countAccountMoney(row);\r\n        row.unitpirce = computeUnitPrices(row);\r\n        // row.taxamount = countTaxAmounttt(row);\r\n        // row.accountmoney = _calculateAccountMoney(row);\r\n        // row.unitpirce = _calculateUnitPriceByUsedMoney(row);\r\n      }\r\n      if (row.ticketmoney) {\r\n        row.accountmoney = countAccountMoney(row);\r\n        row.unitpirce = computeUnitPrices(row);\r\n        // row.accountmoney = _calculateAccountMoney(row);\r\n        // row.unitpirce = _calculateUnitPriceByUsedMoney(row);\r\n      }\r\n      row.quotereadingsratio = _calculateQuotereadingsratio(row);\r\n      if (row.ischangeammeter == 1 && row.isnew == 1) {\r\n        if (row.oldbillpower > 0) {\r\n          row.totalusedreadings =\r\n            parseFloat(row.totalusedreadings) + Math.abs(row.oldbillpower);\r\n          row.unitpirce = computeUnitPrices_hb(row);\r\n        }\r\n\r\n        let remark = row.remark;\r\n        if (remark.indexOf(\"换表\") == -1) {\r\n          row.remark += \"换表，结清原电表读数【\" + row.oldbillpower + \"】；\";\r\n        }\r\n      }\r\n    },\r\n    //点击保存\r\n    async preserve() {\r\n      let dataL = this.$refs.accountTable.getSelection();\r\n      this.$refs.showAlarmModel.listJH = this.$refs.accountTable.getSelection();\r\n      let b = false;\r\n      let data = this.insideData;\r\n      let array = [];\r\n      let that = this;\r\n      let version = indexData.version;\r\n      for (let i = 0; i < dataL.length; i++) {\r\n        if (dataL[i].editType == 1) {\r\n          if (\"sc\" == version && dataL[i].electrotype && dataL[i].electrotype > 1400) {\r\n            if (\r\n              dataL[i].stationcode5gr == null ||\r\n              dataL[i].stationcode5gr == undefined ||\r\n              dataL[i].stationcode5gr == \"\"\r\n            ) {\r\n              this.errorTips(\r\n                \"电表/协议编号【\" +\r\n                  dataL[i].ammetercode +\r\n                  \"】，项目名称【\" +\r\n                  dataL[i].projectname +\r\n                  \"】关联局站的5GR站址为空，请完善局站信息，或者5GR有效性清单失效，请联系无线管理员。\"\r\n              );\r\n            }\r\n          }\r\n          //截止日期校验\r\n          let result = await this.handleEndDate(dataL[i], dataL[i].enddate);\r\n          if (result) {\r\n            this.errorTips(result);\r\n            return;\r\n          }\r\n          let maxdegree = parseInt(dataL[i].maxdegree); //翻表度数,即电表的最大度数\r\n          if (maxdegree != null && maxdegree > 0) {\r\n            if (dataL[i].curtotalreadings > maxdegree) {\r\n              that.errorTips(\"本期止度不能大于翻表值：\" + maxdegree);\r\n            } else {\r\n              b = true;\r\n              array.push(dataL[i]);\r\n            }\r\n          } else {\r\n            b = true;\r\n            array.push(dataL[i]);\r\n          }\r\n        }\r\n      }\r\n      if (b) {\r\n        this.submitData(array);\r\n      } else {\r\n        this.errorTips(\"没有可保存数据\");\r\n      }\r\n    },\r\n    preserveSc() {\r\n      this.$refs.checkResult.ammeterids = this.ammeterids;\r\n      this.showJhModel = true;\r\n    },\r\n    //提交数据\r\n    submitData(data) {\r\n      let a = [];\r\n      let str = \"\";\r\n      let str1 = \"\";\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let obj = requiredFieldValidator(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n        });\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          that.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n        if (submitData.length > 0) {\r\n          this.submit = submitData;\r\n          this.submit2 = submitData;\r\n          editOwn(submitData).then((res) => {\r\n            if (res.data.num > 0) {\r\n              this.$Message.info({\r\n                content: \"成功保存\" + res.data.num + \"条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n            }\r\n\r\n            if (res.data.str.length > 0) {\r\n              this.errorTips(res.data.str);\r\n            }\r\n            this.pageNum = 1;\r\n            this.getAccountMessages();\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //添加修改峰平谷值的备注\r\n    addFremark(row) {\r\n      let old = row.old_prevhighreadings;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (\r\n        row.curhighreadings != null &&\r\n        row.curhighreadings > 0 &&\r\n        this.editprevhighreadings > row.curhighreadings\r\n      ) {\r\n        this.errorTips(\"起始峰值不能大于截止峰值\" + row.curhighreadings);\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editprevhighreadings = old;\r\n        }, 200);\r\n      } else {\r\n        row.prevhighreadings = this.editprevhighreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n        if (row.old_prevhighreadings != row.prevhighreadings) {\r\n          row.remark +=\r\n            \"本期起始峰值 从\" +\r\n            row.old_prevhighreadings +\r\n            \"修改为\" +\r\n            row.prevhighreadings +\r\n            \"; \";\r\n        }\r\n        //修改opflag修改起日期 原来数字加\r\n        let opflag = row.opflag;\r\n        if (opflag != 3 && opflag != 5 && opflag != 7 && opflag != 9) {\r\n          row.opflag = opflag + 3;\r\n        }\r\n      }\r\n    },\r\n    //起始平值变换记录备注\r\n    addPremark(row) {\r\n      let old = row.old_prevflatreadings;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (\r\n        row.curflatreadings != null &&\r\n        row.curflatreadings > 0 &&\r\n        this.editprevflatreadings > row.curflatreadings\r\n      ) {\r\n        this.errorTips(\"起始平值不能大于截止平值\" + row.curflatreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editprevflatreadings = old;\r\n        }, 200);\r\n      } else {\r\n        row.prevflatreadings = this.editprevflatreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n        if (row.old_prevflatreadings != row.prevflatreadings) {\r\n          row.remark +=\r\n            \"本期起始平值 从\" +\r\n            row.old_prevflatreadings +\r\n            \"修改为\" +\r\n            row.prevflatreadings +\r\n            \"; \";\r\n        }\r\n        //修改opflag修改起日期 原来数字加\r\n        let opflag = row.opflag;\r\n        if (opflag != 3 && opflag != 5 && opflag != 7 && opflag != 9) {\r\n          row.opflag = opflag + 3;\r\n        }\r\n      }\r\n    },\r\n    //起始谷值变换记录备注\r\n    addGremark(row) {\r\n      let old = row.old_prevlowreadings;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (\r\n        row.curlowreadings != null &&\r\n        row.curlowreadings > 0 &&\r\n        this.editprevlowreadings > row.curlowreadings\r\n      ) {\r\n        this.errorTips(\"起始谷值不能大于截止谷值\" + row.curlowreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editprevlowreadings = old;\r\n        }, 200);\r\n      } else {\r\n        row.prevlowreadings = this.editprevlowreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n        if (row.old_prevlowreadings != row.prevlowreadings) {\r\n          row.remark +=\r\n            \"本期起始谷值 从\" +\r\n            row.old_prevlowreadings +\r\n            \"修改为\" +\r\n            row.prevlowreadings +\r\n            \"; \";\r\n        }\r\n        //修改opflag修改起日期 原来数字加\r\n        let opflag = row.opflag;\r\n        if (opflag != 3 && opflag != 5 && opflag != 7 && opflag != 9) {\r\n          row.opflag = opflag + 3;\r\n        }\r\n      }\r\n    },\r\n    setcurhighreadings(row) {\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (this.editcurhighreadings < row.prevhighreadings) {\r\n        this.errorTips(\"截止峰值不能小于起始峰值\" + row.prevhighreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editcurhighreadings = row.curhighreadings;\r\n        }, 200);\r\n      } else {\r\n        row.curhighreadings = this.editcurhighreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n      }\r\n    },\r\n    setcurflatreadings(row) {\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (this.editcurflatreadings < row.prevflatreadings) {\r\n        this.errorTips(\"截止平值不能小于起始平值\" + row.prevflatreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editcurflatreadings = row.curflatreadings;\r\n        }, 200);\r\n      } else {\r\n        row.curflatreadings = this.editcurflatreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n      }\r\n    },\r\n    setcurlowreadings(row) {\r\n      let next = row.ifNext;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (this.editcurlowreadings < row.prevlowreadings) {\r\n        this.errorTips(\"截止谷值不能小于起始谷值\" + row.prevlowreadings);\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editcurlowreadings = row.curlowreadings;\r\n        }, 200);\r\n      } else {\r\n        row.curlowreadings = this.editcurlowreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n      }\r\n    },\r\n    setFPG(row) {\r\n      let item = {\r\n        prevhighreadings: row.prevhighreadings,\r\n        prevflatreadings: row.prevflatreadings,\r\n        prevlowreadings: row.prevlowreadings,\r\n        curhighreadings: row.curhighreadings,\r\n        curflatreadings: row.curflatreadings,\r\n        curlowreadings: row.curlowreadings,\r\n        highreadings: parseFloat(this.edithighreadings.toFixed(2)),\r\n        flatreadings: parseFloat(this.editflatreadings.toFixed(2)),\r\n        lowreadings: parseFloat(this.editlowreadings.toFixed(2)),\r\n        magnification: row.magnification,\r\n      };\r\n      let amount = _calculateUsedReadingsForType_2(item);\r\n      if (amount < 0) {\r\n        //计算用电量\r\n        this.errorTips(\r\n          \"计算用电量小于0(\" +\r\n            amount +\r\n            \"),请确认峰平谷加减电量值！\" +\r\n            \"加减电量(峰值)\" +\r\n            parseFloat(this.edithighreadings.toFixed(2)) +\r\n            \",加减电量(平值)\" +\r\n            parseFloat(this.editflatreadings.toFixed(2)) +\r\n            \",加减电量(谷值)\" +\r\n            parseFloat(this.editlowreadings.toFixed(2)) +\r\n            \"当前用电量\" +\r\n            row.curusedreadings\r\n        );\r\n        return;\r\n      }\r\n      row.highreadings = parseFloat(this.edithighreadings.toFixed(2));\r\n      row.flatreadings = parseFloat(this.editflatreadings.toFixed(2));\r\n      row.lowreadings = parseFloat(this.editlowreadings.toFixed(2));\r\n      row.editType = 1;\r\n      this.calculateAll(row);\r\n    },\r\n    //一键删除数据\r\n    deleteAll() {\r\n      this.$Modal.confirm({\r\n        title: \"提示\",\r\n        content: \"<p>确定一键删除吗？</p>\",\r\n        onOk: () => {\r\n          this.accountTb.loading = true;\r\n          let params = this.accountObj;\r\n          params.removeAllFlag = true;\r\n          delete params.pageSize;\r\n          delete params.pageNum;\r\n          console.log(params, \"params\");\r\n          debugger;\r\n          removeAll(params).then((res) => {\r\n            this.accountTb.loading = false;\r\n            if (res.data.num > 0) {\r\n              this.$Message.success(\"一键删除成功\");\r\n              this.searchList();\r\n            } else {\r\n              this.$Message.error(\"一键删除失败\");\r\n            }\r\n          });\r\n        },\r\n        onCancel: () => {},\r\n      });\r\n    },\r\n    //删除行数据\r\n    remove() {\r\n      let version = indexData.version;\r\n      let data = this.$refs.accountTable.getSelection();\r\n      if (data == null || data.length === 0) {\r\n        this.errorTips(\"请选择要删除的台账\");\r\n        return;\r\n      }\r\n      let ids = \"\";\r\n      let that = this;\r\n      let str = \"\";\r\n      data.forEach(function (item) {\r\n        if (item.ifNext) {\r\n          str +=\r\n            \"电表/协议编号为【\" +\r\n            item.ammetercode +\r\n            \"】当期【\" +\r\n            item.accountno +\r\n            \"期】台账之后已有正式数据，不能删除！\";\r\n        }\r\n        ids += item.pcid + \",\";\r\n      });\r\n      if (ids.length > 0 && str.length === 0) {\r\n        that.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>是否确认删除选中信息？</p>\",\r\n          onOk: () => {\r\n            removeOwn(ids).then((res) => {\r\n              if (res.data.num > 0) {\r\n                that.$Message.info({\r\n                  content: \"成功删除\" + res.data.num + \"条数据\",\r\n                  duration: 10,\r\n                  closable: true,\r\n                });\r\n              }\r\n\r\n              if (res.data.str.length > 0) {\r\n                that.errorTips(res.data.str);\r\n              }\r\n              that.searchList();\r\n            });\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      } else {\r\n        that.errorTips(str);\r\n      }\r\n    },\r\n    async getContractInfo(stationCode) {\r\n      this.contractCount = 0;\r\n      await validContractList({ stationCode: stationCode }).then((res) => {\r\n        if (res.data) {\r\n          this.contractCount = res.data.length;\r\n        }\r\n      });\r\n    },\r\n    //span点击事件将span换成输入框并且获取焦点\r\n    selectCall(row, index, columns, str) {\r\n      if (row.toweraccountid == null) {\r\n        this.editStartDate = row.startdate;\r\n        this.editEndDate = row.enddate;\r\n        this.editPrevtotalreadings =\r\n          row.prevtotalreadings == null || row.prevtotalreadings === 0\r\n            ? null\r\n            : row.prevtotalreadings;\r\n        this.editcurtotalreadings =\r\n          row.curtotalreadings == null || row.curtotalreadings === 0\r\n            ? null\r\n            : row.curtotalreadings;\r\n        this.edittransformerullage =\r\n          row.transformerullage == null || row.transformerullage === 0\r\n            ? null\r\n            : row.transformerullage;\r\n        this.edittaxticketmoney =\r\n          row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n            ? null\r\n            : row.inputtaxticketmoney;\r\n        this.editticketmoney =\r\n          row.inputticketmoney == null || row.inputticketmoney === 0\r\n            ? null\r\n            : row.inputticketmoney;\r\n        this.editullagemoney =\r\n          row.ullagemoney == null || row.ullagemoney === 0 ? null : row.ullagemoney;\r\n        this.edittaxrate =\r\n          row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n        this.edittickettaxamount =\r\n          row.tickettaxamount == null || row.tickettaxamount === 0\r\n            ? null\r\n            : row.tickettaxamount;\r\n        this.editremark = row.bz;\r\n        this.editIndex = index;\r\n        this.columnsIndex = columns;\r\n\r\n        this.editaddress = row.address;\r\n        let a = this;\r\n        setTimeout(function () {\r\n          if (columns != 9) {\r\n            a.$refs[str + index + columns].focus();\r\n          }\r\n        }, 200);\r\n      } else {\r\n        this.$Message.info({\r\n          content: \"提示：铁塔导入不可修改，只能删除\",\r\n          duration: 5,\r\n          closable: true,\r\n        });\r\n      }\r\n    },\r\n    //根据列号返回对应的列名\r\n    enterOperate(number) {\r\n      let str = \"\";\r\n      let data = null;\r\n      switch (number) {\r\n        case 0:\r\n          str = \"address\";\r\n          data = this.editaddress;\r\n          break;\r\n        case 1:\r\n          str = \"startdate\";\r\n          data = this.editStartDate;\r\n          break;\r\n        case 2:\r\n          str = \"enddate\";\r\n          data = this.editEndDate;\r\n          break;\r\n        case 3:\r\n          str = \"prevtotalreadings\";\r\n          data = this.editPrevtotalreadings;\r\n          break;\r\n        case 4:\r\n          str = \"curtotalreadings\";\r\n          data = this.editcurtotalreadings;\r\n          break;\r\n        case 5:\r\n          str = \"transformerullage\";\r\n          data = this.edittransformerullage;\r\n          break;\r\n        case 6:\r\n          str = \"inputtaxticketmoney\";\r\n          data = this.edittaxticketmoney;\r\n          break;\r\n        case 7:\r\n          str = \"inputticketmoney\";\r\n          data = this.editticketmoney;\r\n          break;\r\n        case 8:\r\n          str = \"tickettaxamount\";\r\n          data = this.edittickettaxamount;\r\n          break;\r\n        case 9:\r\n          str = \"taxrate\";\r\n          data = this.edittaxrate;\r\n          break;\r\n        case 10:\r\n          str = \"ullagemoney\";\r\n          data = this.editullagemoney;\r\n          break;\r\n        case 11:\r\n          str = \"remark\";\r\n          data = this.editremark;\r\n          break;\r\n      }\r\n      return { str: str, data: data };\r\n    },\r\n    //输入数据验证\r\n    validate() {\r\n      if (this.columnsIndex === 11) {\r\n        this.validateRemark();\r\n        return;\r\n      }\r\n      let val = this.enterOperate(this.columnsIndex).data;\r\n      if (val) {\r\n        if (testNumber(val)) {\r\n          switch (this.columnsIndex) {\r\n            // case 0:\r\n            //     this.validateStartdate0();\r\n            case 1:\r\n              this.validateStartdate();\r\n              break;\r\n            case 2:\r\n              this.validateEnddate();\r\n              break;\r\n            case 3:\r\n              this.validatePrevtotalreadings();\r\n              break;\r\n            case 4:\r\n              this.validateCurtotalreadings();\r\n              break;\r\n            case 5:\r\n              this.validateTransformerullage();\r\n              break;\r\n            case 6:\r\n              this.validateTaxticketmoney();\r\n              break;\r\n            case 7:\r\n              this.validateTicketmoney();\r\n              break;\r\n            case 8:\r\n              this.validateTickettaxamount();\r\n              break;\r\n            case 10:\r\n              this.validateUllagemoney();\r\n              break;\r\n          }\r\n        } else {\r\n          this.errorTips(\"请输入数字！\");\r\n        }\r\n      }\r\n    },\r\n    //验证错误弹出提示框并跳转到下一格\r\n    errorTips(str) {\r\n      this.$Notice.error({\r\n        title: \"提示\",\r\n        desc: str,\r\n        duration: 10,\r\n      });\r\n    },\r\n    //跳转到下一格\r\n    nextCell(data) {\r\n      let index = data.editIndex;\r\n      let columns = data.columnsIndex;\r\n      let row = \"\";\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n      } else if (index > -1 && columns === 11) {\r\n        //当跳转的最后一行最后一格的时候\r\n        if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n          index = 0;\r\n        } else {\r\n          index++;\r\n        }\r\n        columns = 1;\r\n      } else {\r\n        row = data.insideData[index];\r\n        //无表或峰平谷表的时候\r\n        if (row && (row.isFPG || row.isWB) && columns >= 2 && columns <= 4) {\r\n          if (row.isWB) {\r\n            columns += 4;\r\n          } else {\r\n            columns += 3;\r\n          }\r\n        } else {\r\n          columns += 1;\r\n        }\r\n        //有下期的台账不能改\r\n        if (row.ifNext) {\r\n          if (columns < 5) {\r\n            columns = 5;\r\n          }\r\n        }\r\n      }\r\n      data.editIndex = index;\r\n      data.columnsIndex = columns;\r\n      row = data.insideData[index];\r\n      if (row) {\r\n        data.editStartDate = row.startdate;\r\n        data.editEndDate = row.enddate;\r\n        data.editPrevtotalreadings =\r\n          row.prevtotalreadings == null || row.prevtotalreadings === 0\r\n            ? null\r\n            : row.prevtotalreadings;\r\n        data.editcurtotalreadings =\r\n          row.curtotalreadings == null || row.curtotalreadings === 0\r\n            ? null\r\n            : row.curtotalreadings;\r\n        data.edittransformerullage =\r\n          row.transformerullage == null || row.transformerullage === 0\r\n            ? null\r\n            : row.transformerullage;\r\n        data.edittaxticketmoney =\r\n          row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n            ? null\r\n            : row.inputtaxticketmoney;\r\n        data.editticketmoney =\r\n          row.inputticketmoney == null || row.inputticketmoney === 0\r\n            ? null\r\n            : row.inputticketmoney;\r\n        data.editullagemoney =\r\n          row.ullagemoney == null || row.ullagemoney === 0 ? null : row.ullagemoney;\r\n        data.edittaxrate =\r\n          row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n        data.edittickettaxamount =\r\n          row.tickettaxamount == null || row.tickettaxamount === 0\r\n            ? null\r\n            : row.tickettaxamount;\r\n        data.editremark = row.bz;\r\n      }\r\n\r\n      setTimeout(function () {\r\n        if (columns != 9) {\r\n          data.$refs[data.enterOperate(columns).str + index + columns].focus();\r\n        }\r\n      }, 200);\r\n    },\r\n    validateStartdate0() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editaddress;\r\n      data.address = val;\r\n      data.editType = 1;\r\n    },\r\n    //验证起始时间\r\n    validateStartdate() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editStartDate;\r\n      if (val != data.old_startdate) {\r\n        // 验证起始时间方法\r\n        let result = _verify_StartDate(data, val, \"可大于\");\r\n        if (result) {\r\n          //失败就弹出提示内容，并将数据恢复初始化\r\n          this.errorTips(result);\r\n          this.myStyle[this.editIndex].startdate = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].startdate = \"myspan\";\r\n          this.startModal = true;\r\n        }\r\n      } else if (val == data.old_startdate) {\r\n        data.startdate = val;\r\n      }\r\n    },\r\n    //验证截止时间\r\n    async validateEnddate() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editEndDate;\r\n      if (val != data.old_enddate) {\r\n        // 验证截止日期方法\r\n        let result = await this.handleEndDate(data, val);\r\n        if (result) {\r\n          this.errorTips(result);\r\n          this.myStyle[this.editIndex].enddate = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].enddate = \"myspan\";\r\n\r\n          this.updateenddate(data, val);\r\n        }\r\n      } else if (val == data.old_enddate) {\r\n        data.enddate = val;\r\n      }\r\n    },\r\n    //截止日期处理\r\n    async handleEndDate(data, val) {\r\n      //直供电有上传日期才可以修改到月底 directsupplyflag 1直供2转供\r\n      let fType = \"\";\r\n      let curval = stringToDate(val); //输入值\r\n      let nowdate = stringToDate(getCurrentDate()); //当天\r\n      if (data.directsupplyflag == 1 && curval > nowdate) {\r\n        let files = await axios\r\n          .request({\r\n            url: \"/common/attachments/list\",\r\n            method: \"post\",\r\n            data: {\r\n              areaCode: \"sc\",\r\n              busiAlias: \"附件(台账)\",\r\n              busiId: data.pcid + \"\",\r\n              categoryCode: \"file\",\r\n              pageNum: 1,\r\n              pageSize: 20,\r\n            },\r\n          })\r\n          .then((res) => {\r\n            return res.data.rows;\r\n          });\r\n        if (files.length == 0) {\r\n          return \"截止日期需小于等于当前时间，超过当前时间需上传附件\";\r\n        } else {\r\n          fType = \"限制期号\"; //截止日期不限制期号的最后一天（月底）\r\n        }\r\n      }\r\n      // 验证截止日期方法\r\n      let result = _verify_EndDate(data, val, fType);\r\n      return result;\r\n    },\r\n    updateenddate(data, val) {\r\n      data.enddate = val;\r\n      data.editType = 1;\r\n      //计算定额\r\n      this.getQuota(data.ammeterid, data.startdate, data.enddate, (result) => {\r\n        if (result.data.code === 0) {\r\n          data.quotareadings = Math.round(result.data.msg);\r\n        } else {\r\n          data.quotareadings = 0;\r\n        }\r\n        this.calculateAll(data);\r\n      });\r\n      this.avemoney = GetDateDiff(data.startdate, data.enddate);\r\n    },\r\n    //验证起度\r\n    validatePrevtotalreadings() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editPrevtotalreadings;\r\n      val = parseFloat(val);\r\n      if (val != data.old_prevtotalreadings) {\r\n        //验证\r\n        let result = _verify_PrevTotalReadings(data, val);\r\n        if (result.string) {\r\n          this.errorTips(result.string);\r\n          this.myStyle[this.editIndex].prevtotalreadings = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].prevtotalreadings = \"myspan\";\r\n          this.ifMaxdegree = result.b;\r\n          this.qdModal = true;\r\n        }\r\n      } else if (val == data.old_prevtotalreadings) {\r\n        data.prevtotalreadings = val;\r\n        this.calculateAll(data);\r\n      }\r\n    },\r\n    //验证止度\r\n    validateCurtotalreadings() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editcurtotalreadings;\r\n\r\n      if (val != data.old_curtotalreadings) {\r\n        val = parseFloat(val);\r\n        let result = _verify_CurTotalReadings(data, val);\r\n        if (result.string) {\r\n          this.errorTips(result.string);\r\n          this.myStyle[this.editIndex].curtotalreadings = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].curtotalreadings = \"myspan\";\r\n\r\n          this.updateCurtotalreadings(data, val, result);\r\n        }\r\n      } else if (val == data.old_curtotalreadings) {\r\n        data.curtotalreadings = val;\r\n        this.calculateAll(data);\r\n      }\r\n    },\r\n    updateCurtotalreadings(data, val, result) {\r\n      data.curtotalreadings = val;\r\n      data.editType = 1;\r\n      let b = result.b;\r\n      if (b === true) {\r\n        this.fbModal = true;\r\n      } else if (b === false) {\r\n        this.qxfbModal = true;\r\n      } else {\r\n        this.calculateAll(data);\r\n      }\r\n      if (indexData.version == \"sc\") {\r\n        //验证上期台账是否完成报账\r\n        axios\r\n          .request({\r\n            url: \"/business/accountSC/valOldAcount\",\r\n            method: \"post\",\r\n            params: { pcid: data.pcid, ammeterid: data.ammeterid },\r\n          })\r\n          .then((res) => {\r\n            let msg = \"\";\r\n            if (res.data.msg) msg = res.data.msg;\r\n            if (data.startdate.endsWith(\"0101\"))\r\n              msg += \"【该起始日期是默认值，请注意修改】\";\r\n            if (msg != \"\")\r\n              this.$Notice.warning({\r\n                title: \"注意\",\r\n                desc: \"电表/协议【\" + data.ammetercode + \"】\" + msg,\r\n                duration: 10,\r\n              });\r\n            if (res.data.acc) {\r\n              Object.assign(data, {\r\n                unitpirceold: res.data.acc.unitpirce,\r\n                curusedreadingsold: res.data.acc.curusedreadings,\r\n              });\r\n            }\r\n          });\r\n      }\r\n    },\r\n    //验证电损\r\n    validateTransformerullage() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.edittransformerullage;\r\n      if (val != data.old_transformerullage) {\r\n        val = parseFloat(val);\r\n        data.transformerullage = val;\r\n        data.editType = 1;\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_transformerullage) {\r\n        data.transformerullage = val;\r\n        this.calculateAll(data);\r\n      }\r\n      if (\r\n        val != data.old_transformerullage &&\r\n        indexData.version == \"sc\" &&\r\n        data.curusedreadings > 0 &&\r\n        parseFloat(val / data.curusedreadings) > 0.105 &&\r\n        (([1411, 1412, 1421, 1422, 1431, 1432].includes(data.electrotype) &&\r\n          data.ammeteruse == 1) ||\r\n          [1, 3].includes(data.ammeteruse))\r\n      ) {\r\n        let flag = false;\r\n        if (new Date().getTime() < new Date(\"2020/8/10\").getTime()) flag = true;\r\n        if (\r\n          data.companyId == 1000085 &&\r\n          new Date().getTime() < new Date(\"2020/8/30\").getTime()\r\n        )\r\n          flag = true;\r\n        if (flag) {\r\n          this.$Notice.warning({\r\n            title: \"温馨提示\",\r\n            desc:\r\n              \"电表/协议【\" +\r\n              data.ammetercode +\r\n              \"】\" +\r\n              \"电损与实际电量比值已经超过10%，请注意！\",\r\n            duration: 10,\r\n          });\r\n          this.myStyle[this.editIndex].transformerullage = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].transformerullage = \"myspan\";\r\n        }\r\n      } else {\r\n        this.myStyle[this.editIndex].transformerullage = \"myspan\";\r\n      }\r\n    },\r\n    //验证专票\r\n    validateTaxticketmoney() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.edittaxticketmoney;\r\n      if (val != data.old_taxticketmoney) {\r\n        val = parseFloat(val);\r\n        data.inputtaxticketmoney = _verify_Money(data, val);\r\n        data.editType = 1;\r\n        data.taxticketmoney = calculateActualMoney(data, val);\r\n        data.taxamount = countTaxamount(data);\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_taxticketmoney) {\r\n        data.inputtaxticketmoney = val;\r\n        data.taxticketmoney = calculateActualMoney(data, val);\r\n        data.taxamount = countTaxamount(data);\r\n        this.calculateAll(data);\r\n      }\r\n      this.validateUnitPrice(data);\r\n      this.validateavemoney(data);\r\n    },\r\n    //验证普票\r\n    validateTicketmoney() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editticketmoney;\r\n      if (val != data.old_ticketmoney) {\r\n        val = parseFloat(val);\r\n        data.inputticketmoney = _verify_Money(data, val);\r\n        data.editType = 1;\r\n        data.ticketmoney = countTicketmoney(data);\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_ticketmoney) {\r\n        data.inputticketmoney = val;\r\n        data.ticketmoney = countTicketmoney(data);\r\n        this.calculateAll(data);\r\n      }\r\n      this.validateUnitPrice(data);\r\n      this.validateavemoney(data);\r\n    },\r\n    //验证普票税额（四川）\r\n    validateTickettaxamount() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.edittickettaxamount;\r\n      if (val != data.old_tickettaxamount) {\r\n        val = parseFloat(val);\r\n        data.tickettaxamount = _verify_Money(data, val);\r\n        data.editType = 1;\r\n        data.ticketmoney = countTicketmoney(data);\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_tickettaxamount) {\r\n        data.tickettaxamount = val;\r\n        data.ticketmoney = countTicketmoney(data);\r\n        this.calculateAll(data);\r\n      }\r\n    },\r\n    //验证其他费用\r\n    validateUllagemoney() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editullagemoney;\r\n      if (val != data.old_ullagemoney) {\r\n        val = parseFloat(val);\r\n        data.ullagemoney = _verify_Money(data, val);\r\n        data.editType = 1;\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_ullagemoney) {\r\n        data.ullagemoney = val;\r\n        this.calculateAll(data);\r\n      }\r\n      this.validateUnitPrice(data);\r\n    },\r\n    //备注\r\n    validateRemark() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editremark;\r\n      data.bz = val;\r\n      data.editType = 1;\r\n    },\r\n    validateavemoney(data) {\r\n      let version = indexData.version;\r\n      if (\"sc\" == version) {\r\n        let accountmoney = data.accountmoney;\r\n        let aveold = judgeNumber(data.aveaccountmoneyold);\r\n        if (this.nowdatediff == 0)\r\n          this.nowdatediff = GetDateDiff(data.startdate, data.enddate);\r\n        if (aveold != 0 && (accountmoney / this.nowdatediff).toFixed(2) - aveold > 0) {\r\n          if (((accountmoney / this.nowdatediff).toFixed(2) - aveold) / aveold > 0.3)\r\n            this.$Notice.warning({\r\n              title: \"温馨提示\",\r\n              desc:\r\n                \"电表/协议【\" +\r\n                data.ammetercode +\r\n                \"】\" +\r\n                \"日均电费环比值已经超过30%，请注意填写备注说明！\",\r\n              duration: 10,\r\n            });\r\n        }\r\n      }\r\n    },\r\n    //验证单价\r\n    validateUnitPrice(data) {\r\n      let version = indexData.version;\r\n      let category = data.category; //电表描述类型\r\n      let directsupplyflag = data.directsupplyflag; //1直供2转供\r\n      let ammeteruse = data.ammeteruse; //电表用途\r\n      let unitpirce = data.unitpirce; //台账单价\r\n      if (!judge_negate(category) && !judge_recovery(ammeteruse) && judge_yb(category)) {\r\n        // if (unitpirce) {\r\n        //   if (directsupplyflag == 1 && ammeteruse == 1) {\r\n        //     if (unitpirce) {\r\n        //       if (unitpirce >= 0.25 && unitpirce < 0.5) {\r\n        //         this.errorTips(\r\n        //           \"直供电单价(\" +\r\n        //             unitpirce +\r\n        //             \")【0.25<=\" +\r\n        //             unitpirce +\r\n        //             \"<0.5】\" +\r\n        //             \"请确认单价是否存在错误\"\r\n        //         );\r\n        //       } else if (unitpirce < 0.25 || unitpirce > 1.2) {\r\n        //         this.errorTips(\r\n        //           \"直供电单价(\" +\r\n        //             unitpirce +\r\n        //             \")【小于0.25或大于1.20】\" +\r\n        //             \"单价错误，请确认！\"\r\n        //         );\r\n        //       }\r\n        //     }\r\n        //   } else if (directsupplyflag == 2) {\r\n        //     if (unitpirce >= 0.3 && unitpirce < 0.6) {\r\n        //       this.errorTips(\r\n        //         \"转供电单价(\" +\r\n        //           unitpirce +\r\n        //           \")【0.3<=\" +\r\n        //           unitpirce +\r\n        //           \"<0.6】\" +\r\n        //           \"请确认单价是否存在错误\"\r\n        //       );\r\n        //     } else if (unitpirce < 0.3) {\r\n        //       this.errorTips(\r\n        //         \"转供电单价(\" +\r\n        //           unitpirce +\r\n        //           \")【\" +\r\n        //           unitpirce +\r\n        //           \"<0.3】\" +\r\n        //           \"单价错误，请确认！\"\r\n        //       );\r\n        //     } else if (unitpirce > 1.5) {\r\n        //       this.errorTips(\r\n        //         \"转供电单价(\" +\r\n        //           unitpirce +\r\n        //           \")【\" +\r\n        //           unitpirce +\r\n        //           \">1.5】\" +\r\n        //           \"请确认单价是否存在错误\"\r\n        //       );\r\n        //     }\r\n        //   }\r\n        // }\r\n        if (unitpirce) {\r\n          if (unitpirce != null && unitpirce < unitpirceMax1) {\r\n            // if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n            this.errorTips(\r\n              \"单价范围必须大于0.1元，此台账单价: \" + unitpirce + \"不在范围内，请确认！\"\r\n            );\r\n          }\r\n        }\r\n      }\r\n    },\r\n    openModal(index) {\r\n      this.meterModal = true; //弹出框显示\r\n      let row = this.insideData[index];\r\n      if (row.accountno == dates[1].code) {\r\n        let obj = {\r\n          accountno: dates[0].code,\r\n          ammeterid: row.ammeterid,\r\n        };\r\n        selectByAmmeterId(obj).then((res) => {\r\n          row.nextData = res.data;\r\n        });\r\n      }\r\n      if (row) {\r\n        if (row.ifNext) {\r\n          this.readonly = true;\r\n        } else {\r\n          this.readonly = false;\r\n        }\r\n\r\n        this.currentRow = row; //给弹出框绑定数据\r\n        this.editprevhighreadings =\r\n          row.prevhighreadings === null ? 0 : row.prevhighreadings;\r\n        this.editprevflatreadings =\r\n          row.prevflatreadings === null ? 0 : row.prevflatreadings;\r\n        this.editprevlowreadings = row.prevlowreadings === null ? 0 : row.prevlowreadings;\r\n        this.editcurhighreadings = row.curhighreadings === null ? 0 : row.curhighreadings;\r\n        this.editcurflatreadings = row.curflatreadings === null ? 0 : row.curflatreadings;\r\n        this.editcurlowreadings = row.curlowreadings === null ? 0 : row.curlowreadings;\r\n        if (this.version == \"sc\") {\r\n          this.edithighreadings = row.highreadings === null ? 0 : row.highreadings;\r\n          this.editflatreadings = row.flatreadings === null ? 0 : row.flatreadings;\r\n          this.editlowreadings = row.lowreadings === null ? 0 : row.lowreadings;\r\n        }\r\n      }\r\n    },\r\n\r\n    async onModalOK(name) {\r\n      let attachData = [];\r\n      let data = [];\r\n      if (name === \"current\") {\r\n        data = this.$refs.accountTable.getSelection();\r\n      } else {\r\n        data = this.insideDataFirst;\r\n      }\r\n      this.iftimeoutShow = true;\r\n      for (let i = 0; i < data.length; i++) {\r\n        let it = data[i];\r\n\r\n        if (it.map.iftimeout == \"3\") {\r\n          await this.dataMethods(it);\r\n        }\r\n      }\r\n      if (this.iftimeoutShow) {\r\n        this.openAddBillPerModal(name);\r\n      }\r\n    },\r\n    async dataMethods(it) {\r\n      let attachData = [];\r\n      await attchList({ busiId: it.pcid }).then((res) => {\r\n        attachData = Object.assign([], res.data.rows);\r\n        if (attachData.length < 1) {\r\n          this.iftimeoutShow = false;\r\n          this.errorTips(\"当前台账所选局站已到期或已停租,请在对应台账上传附件说明\");\r\n        }\r\n      });\r\n    },\r\n    openAddBillPerModal(name) {\r\n      this.name = name;\r\n      if (name === \"current\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJ();\r\n        // } else {\r\n        this.selectedAccount();\r\n        // }\r\n      } else if (name === \"all\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJAll();\r\n        // } else {\r\n        this.selectedAllAccount();\r\n        // }\r\n      }\r\n    },\r\n    //加入归集单\r\n    addPreserveGJ() {\r\n      let dataL = this.$refs.accountTable.getSelection();\r\n      if (dataL == null || dataL.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        this.addSubmitDataGJ(dataL);\r\n      }\r\n    },\r\n    //加入归集单\r\n    addPreserveGJAll() {\r\n      let params = this.accountObj;\r\n      params.pageNum = 1;\r\n      params.pageSize = 20000;\r\n      let req = {\r\n        url: \"/business/account/selfAccountList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      let array1 = [];\r\n      this.accountTb.loading = true;\r\n      axios.request(req).then((res) => {\r\n        this.accountTb.loading = false;\r\n        let dataL = res.data.rows;\r\n        this.addSubmitDataGJ(dataL);\r\n      });\r\n    },\r\n    //提交归集单数据\r\n    addSubmitDataGJ(data1) {\r\n      let a = [];\r\n      let str = \"\";\r\n      let str1 = \"\";\r\n      let b = 1;\r\n      let data = data1.filter((item) => item.effective == 1);\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let obj = requiredFieldValidator(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n\r\n          if (item.map && \"sc\" == version) {\r\n            if (item.map.iftimeout == \"3\") {\r\n              if (item.map.show) {\r\n                b = 5;\r\n                str += item.ammetercode + \",\";\r\n              }\r\n            }\r\n          }\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status != 1) {\r\n            b = 3;\r\n          }\r\n          if (\r\n            \"sc\" == version &&\r\n            item.unitpirce > 2 &&\r\n            (item.unitpirceold == null || item.unitpirceold < 2) &&\r\n            that.valiprice\r\n          ) {\r\n            b = 4;\r\n            str += item.ammetercode + \",\";\r\n          }\r\n        });\r\n        if (b === 1) {\r\n          if (submitData.length > 0) {\r\n            this.submit = submitData;\r\n            this.submit2 = submitData;\r\n            this.preserveSc();\r\n          }\r\n          if (str1.length > 0) {\r\n            this.$Notice.warning({\r\n              title: \"注意\",\r\n              desc: str1,\r\n              duration: 0,\r\n            });\r\n          }\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\r\n            \"电表/协议编号为【\" +\r\n              str +\r\n              \"】的台账是退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\"\r\n          );\r\n        } else if (b === 4) {\r\n          this.errorTips(\r\n            \"电表/协议编号为【\" +\r\n              str +\r\n              \"】的台账单价已经超过2元，请发OA邮件给省公司审核，通过后才可加入归集单！\"\r\n          );\r\n        } else if (b === 5) {\r\n          this.errorTips(\r\n            \"电表/协议编号为【\" +\r\n              str +\r\n              \"】的台账关联的局站已到期,上传附件说明后才可加入归集单！\"\r\n          );\r\n        }\r\n\r\n        this.ammeterids = a;\r\n        if (str.length > 0) {\r\n          this.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          this.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //加入归集单，全部非强控\r\n    selectedFQK() {\r\n      let that = this;\r\n      that.setElectroyType();\r\n      that.$refs.addBillPer.initAmmeter(\r\n        that.$refs.showAlarmModel.selectIds3,\r\n        3,\r\n        this.accountObj.country\r\n      );\r\n    },\r\n    //加入归集单，全部有效台账\r\n    selectedAllAccount() {\r\n      let that = this;\r\n      that.setElectroyType();\r\n      that.spinShow = true;\r\n      selectIdsByParams(this.accountObj).then((res) => {\r\n        that.spinShow = false;\r\n        if (res.data.str) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: res.data.str,\r\n            duration: 0,\r\n          });\r\n        }\r\n        if (res.data.ids) {\r\n          if (res.data.ids.length == 0) {\r\n            that.errorTips(\"无有效数据可加入归集单\");\r\n          } else {\r\n            that.$refs.addBillPer.initAmmeter(res.data.ids, 3, this.accountObj.country);\r\n            // that.$refs.addBillPer.initAmmeter(\r\n            //   this.$refs.showAlarmModel.selectIds1,\r\n            //   3,\r\n            //   this.accountObj.country\r\n            // );\r\n          }\r\n        } else {\r\n          that.errorTips(\"无有效数据可加入归集单\");\r\n        }\r\n      });\r\n    },\r\n    //加入归集单，已选择的台账\r\n    selectedAccount() {\r\n      let data = this.$refs.accountTable.getSelection();\r\n      let b = 1;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        let ids = [];\r\n        data.forEach(function (item) {\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n          ids.push(item.pcid);\r\n        });\r\n        if (b === 1) {\r\n          this.$refs.addBillPer.initAmmeter(ids, 3, this.accountObj.country);\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n      }\r\n    },\r\n    setMyStyle(length) {\r\n      this.myStyle = [];\r\n      for (var i = 0; i < length; i++) {\r\n        this.myStyle.push({\r\n          startdate: \"myspan\",\r\n          enddate: \"myspan\",\r\n          prevtotalreadings: \"myspan\",\r\n          curtotalreadings: \"myspan\",\r\n          transformerullage: \"myspan\",\r\n          inputtaxticketmoney: \"myspan\",\r\n          inputticketmoney: \"myspan\",\r\n          ullagemoney: \"myspan\",\r\n          taxrate: \"myspan\",\r\n          tickettaxamount: \"myspan\",\r\n          remark: \"myspan\",\r\n          address: \"myspan\",\r\n        });\r\n      }\r\n    },\r\n    refresh() {\r\n      if (this.$refs.showAlarmModel.fromGuijidan != 1) {\r\n        let obj = this;\r\n        obj.showAlarmModel = false;\r\n        setTimeout(function () {\r\n          obj.getAccountMessages();\r\n        }, 200);\r\n      } else {\r\n        this.showAlarmModel = true;\r\n      }\r\n    },\r\n    againJoin() {\r\n      let data = this.$refs.accountTable.getSelection();\r\n      for (let i = 0; i < data.length; i++) {\r\n        let it = data[i];\r\n        if (it.map.iftimeout == \"3\") {\r\n          this.dataMethods(it);\r\n        }\r\n      }\r\n      if (this.iftimeoutShow) {\r\n        let b = true;\r\n        var that = this;\r\n        if (data == null || data.length == 0) {\r\n          this.errorTips(\"请选择要重新加入归集单的台账\");\r\n        } else {\r\n          let againJoinIds = \"\";\r\n          data.forEach(function (item) {\r\n            let status = item.status;\r\n            if (status != 5) {\r\n              b = false;\r\n            } else {\r\n              againJoinIds += item.pcid + \",\";\r\n            }\r\n          });\r\n          if (b) {\r\n            againJoin(againJoinIds).then((res) => {\r\n              if (res.data.code == 0) {\r\n                that.$Message.info({\r\n                  content: \"提示：操作成功\",\r\n                  duration: 10,\r\n                  closable: true,\r\n                });\r\n                that.getAccountMessages();\r\n              }\r\n            });\r\n          } else {\r\n            that.errorTips(\"只有已退回的台账才能重新加入归集单\");\r\n          }\r\n        }\r\n      }\r\n    },\r\n    beforeLoadData(data, str) {\r\n      var cols = [],\r\n        keys = [];\r\n      for (var i = 0; i < this.accountTb.exportcolumns.length; i++) {\r\n        cols.push(this.accountTb.exportcolumns[i].title);\r\n        keys.push(this.accountTb.exportcolumns[i].key);\r\n      }\r\n      const params = {\r\n        title: cols,\r\n        key: keys,\r\n        data: data,\r\n        autoWidth: true,\r\n        filename: str,\r\n      };\r\n      excel.export_array_to_excel(params);\r\n      return;\r\n    },\r\n    exportCsv(name) {\r\n      this.setElectroyType();\r\n      let params = this.accountObj;\r\n      if (name == \"current\") {\r\n        params.pageNum = this.pageNum;\r\n        params.pageSize = this.pageSize;\r\n      } else if (name == \"all\") {\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n      }\r\n      let req = {\r\n        url: \"/business/accountSC/exporttt\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      this.spinShow = true;\r\n      axios\r\n        .file(req)\r\n        .then((res) => {\r\n          this.spinShow = false;\r\n          const content = res;\r\n          const blob = new Blob([content]);\r\n          const fileName = \"铁塔台账导出数据\" + \".xlsx\";\r\n          if (\"download\" in document.createElement(\"a\")) {\r\n            // 非IE下载\r\n            const elink = document.createElement(\"a\");\r\n            elink.download = fileName;\r\n            elink.style.display = \"none\";\r\n            elink.href = URL.createObjectURL(blob);\r\n            document.body.appendChild(elink);\r\n            elink.click();\r\n            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n            document.body.removeChild(elink);\r\n          } else {\r\n            // IE10+下载\r\n            navigator.msSaveBlob(blob, fileName);\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //税额\r\n    settaxrate() {\r\n      let val = this.edittaxrate;\r\n      let data = this.insideData[this.editIndex];\r\n      data.taxrate = val;\r\n      // data.taxamount = countTaxAmounttt(data);\r\n      data.taxamount = countTaxamount(data);\r\n      data.editType = 1;\r\n      this.calculateAll(data);\r\n    },\r\n    startModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editStartDate;\r\n      data.startdate = val; //修改起始日期\r\n      data.editType = 1;\r\n      //计算定额\r\n      this.getQuota(data.ammeterid, data.startdate, data.enddate, (result) => {\r\n        if (result.data.code === 0) {\r\n          data.quotareadings = Math.round(result.data.msg);\r\n        } else {\r\n          data.quotareadings = 0;\r\n        }\r\n        this.calculateAll(data);\r\n      });\r\n      let opflag = data.opflag;\r\n      //修改opflag修改起日期 原来数字加\r\n      if (opflag != 4 && opflag != 6 && opflag != 7 && opflag != 9) {\r\n        data.opflag = opflag + 4;\r\n      }\r\n      data.remark += \"本期起始日期 从\" + data.old_startdate + \"修改为\" + val + \"; \";\r\n      this.startModal = false;\r\n      this.nextCell(this);\r\n    },\r\n    cancel() {\r\n      this.nextCell(this);\r\n    },\r\n    hcyzstartdate(lett, index) {\r\n      let data = lett.insideData[index];\r\n      let val = lett.editStartDate;\r\n      if (val) {\r\n        if (testNumber(val)) {\r\n          if (val == data.old_startdate) {\r\n            data.startdate = val;\r\n\r\n            lett.nextCell(lett);\r\n          } else {\r\n            lett.validate();\r\n          }\r\n        } else {\r\n          lett.errorTips(\"请输入数字！\");\r\n          lett.nextCell(lett);\r\n        }\r\n      } else {\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    qdModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editPrevtotalreadings;\r\n\r\n      data.prevtotalreadings = val;\r\n      let b = this.ifMaxdegree;\r\n      if (b === true) {\r\n        this.fbModal = true;\r\n      } else if (b === false) {\r\n        this.qxfbModal = true;\r\n      } else {\r\n        this.calculateAll(data);\r\n      }\r\n\r\n      data.editType = 1;\r\n      let opflag = data.opflag;\r\n      //增加4 修改起日期 原来数字加\r\n      if (opflag != 2 && opflag != 5 && opflag != 6 && opflag != 9) {\r\n        data.opflag = opflag + 2;\r\n      }\r\n      data.remark += \"本期起度 从\" + data.old_prevtotalreadings + \"修改为\" + val + \"; \";\r\n\r\n      this.qdModal = false;\r\n      this.nextCell(this);\r\n    },\r\n    fbModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      data.ifMaxdegree = true;\r\n      this.fbModal = false;\r\n\r\n      this.calculateAll(data);\r\n    },\r\n    qxfbModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      data.ifMaxdegree = false;\r\n      this.qxfbModal = false;\r\n\r\n      this.calculateAll(data);\r\n    },\r\n    hcyzprevtotalreadings(lett, index) {\r\n      let data = lett.insideData[index];\r\n      let val = lett.editPrevtotalreadings;\r\n      if (val) {\r\n        if (testNumber(val)) {\r\n          if (val == data.old_prevtotalreadings) {\r\n            data.prevtotalreadings = val;\r\n            lett.nextCell(lett);\r\n          } else {\r\n            lett.validate();\r\n          }\r\n        } else {\r\n          lett.errorTips(\"请输入数字！\");\r\n          lett.nextCell(lett);\r\n        }\r\n      } else {\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    qdcancel() {\r\n      if (this.columnsIndex === 4) {\r\n        let data = this.insideData[this.editIndex].old_prevtotalreadings;\r\n        this.editPrevtotalreadings = data;\r\n        this.insideData[this.editIndex].prevtotalreadings = data;\r\n\r\n        this.$refs[\"curtotalreadings\" + this.editIndex + this.columnsIndex].focus();\r\n      } else if (this.columnsIndex === 5) {\r\n        let data = this.insideData[this.editIndex].old_curtotalreadings;\r\n        this.editcurtotalreadings = data;\r\n        this.insideData[this.editIndex].curtotalreadings = data;\r\n\r\n        this.$refs[\"transformerullage\" + this.editIndex + this.columnsIndex].focus();\r\n      }\r\n    },\r\n    accountnoChange() {\r\n      this.searchList();\r\n    },\r\n    setElectroyType() {\r\n      let types = this.classifications;\r\n      this.accountObj.electrotype = types[types.length - 1];\r\n    },\r\n    self() {\r\n      var lett = this;\r\n      if (lett.startModal) {\r\n        lett.startModalOk();\r\n      } else if (lett.qdModal) {\r\n        lett.qdModalOk();\r\n      } else if (lett.fbModal) {\r\n        lett.fbModalOk();\r\n      } else if (lett.qxfbModal) {\r\n        lett.qxfbModalOk();\r\n      } else {\r\n        let index = lett.editIndex;\r\n        let columns = lett.columnsIndex;\r\n        if (index === -1 && columns === -1) {\r\n          index = 0;\r\n          columns = 1;\r\n          lett.editIndex = index;\r\n          lett.columnsIndex = columns;\r\n          lett.editStartDate = lett.insideData[index].startdate;\r\n          setTimeout(function () {\r\n            lett.$refs[lett.enterOperate(columns).str + index + columns].focus();\r\n          }, 200);\r\n        } else if (columns === 1) {\r\n          lett.hcyzstartdate(lett, index);\r\n        } else if (columns === 3) {\r\n          lett.hcyzprevtotalreadings(lett, index);\r\n        } else {\r\n          lett.validate();\r\n          lett.nextCell(lett);\r\n        }\r\n      }\r\n    },\r\n    handleFormatError(file) {\r\n      this.errorTips(file.name + \" 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件\");\r\n    },\r\n    handleUploadSuccess() {\r\n      console.log(\"上传成功1\");\r\n    },\r\n    handleProgress(event, file) {\r\n      this.$Message.info({\r\n        content: file.name + \" 正在上传。\",\r\n      });\r\n    },\r\n    onExcelUpload(file) {\r\n      if (file.size > 1024 * 1024 * 5) {\r\n        this.errorTips(\"文件大小超过限制！\");\r\n        return;\r\n      }\r\n      if (!file) {\r\n        this.errorTips(\"请选择要上传的文件！\");\r\n        return;\r\n      }\r\n      let fileName = file.name.lastIndexOf(\".\"); //取到文件名开始到最后一个点的长度\r\n      let fileNameLength = file.name.length; //取到文件名长度\r\n      let fileFormat = file.name.substring(fileName + 1, fileNameLength); //截\r\n      if (\"xls\" != fileFormat && \"xlsx\" != fileFormat) {\r\n        return;\r\n      }\r\n      let param = { version: indexData.version };\r\n      let excel = { file: file };\r\n      let that = this;\r\n      that.spinShow = true;\r\n      axios\r\n        .request({\r\n          url: \"/business/accountSC/uploadExcel\",\r\n          method: \"post\",\r\n          data: Object.assign({}, param, excel),\r\n        })\r\n        .then((res) => {\r\n          that.spinShow = false;\r\n          if (res.data.number > 0) {\r\n            that.$Message.info({\r\n              content: \"成功导入\" + res.data.number + \"条数据\",\r\n            });\r\n          } else {\r\n            that.errorTips(\"导入数据失败，请检查数据是否填写正确\");\r\n          }\r\n          if (res.data.list) {\r\n            that.export.run = true;\r\n            that.beforeLoadData(res.data.list, \"导入数据反馈\");\r\n            // this.calculateAll(res.data.list);\r\n            that.pageNum = 1;\r\n            that.getAccountMessages();\r\n          }\r\n        });\r\n      return false;\r\n    },\r\n    getDataFromModal(data, flag) {\r\n      this.chooseResponseCenter(data);\r\n    },\r\n    change() {\r\n      this.getAccountMessages();\r\n    },\r\n    chooseResponseCenter(data) {\r\n      if (!data) {\r\n        if (!this.accountObj.company) {\r\n          this.errorTips(\"请选择所属分公司\");\r\n        }\r\n        if (!this.accountObj.country) {\r\n          this.errorTips(\"请选择所属部门\");\r\n        }\r\n        this.$refs.queryPeople.modal.params = {\r\n          deptId: this.accountObj.country,\r\n          copnId: this.accountObj.company,\r\n        }; // 当前部门和分公司\r\n        this.$refs.queryPeople.choose(); //人员\r\n      } else {\r\n        this.userName = data.name;\r\n        this.accountObj.userId = data.id;\r\n      }\r\n    },\r\n    ellipsis(row) {\r\n      let value = row.remark + row.bz;\r\n      if (!value) return \"\";\r\n      if (value.length > 3) {\r\n        return value.slice(0, 3) + \"...\";\r\n      }\r\n      return value;\r\n    },\r\n    uploadFile(row) {\r\n      this.$refs.uploadFileModal.choose(row.pcid + \"\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.page-class {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  .cl-table {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n  .button-bar {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n.yjjh > .ivu-modal-wrap > .ivu-modal {\r\n  top: 20px !important;\r\n}\r\n\r\n.mytable .ivu-table-cell {\r\n  padding-left: 1px;\r\n  padding-right: 1px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: normal;\r\n  word-break: break-all;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.mytable .myspan {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n}\r\n\r\n.mytable .errorStle {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n  color: red;\r\n}\r\n\r\n.account .filter-divider {\r\n  margin: 0px;\r\n  text-align: center;\r\n}\r\n\r\n.account .header-bar-show {\r\n  max-height: 300px;\r\n  padding-top: 14px;\r\n  overflow: inherit;\r\n  border-bottom: 1px solid #e8eaec;\r\n}\r\n\r\n.account .header-bar-hide {\r\n  max-height: 0;\r\n  padding-top: 0;\r\n  overflow: hidden;\r\n  border-bottom: 0;\r\n}\r\n\r\n.mymodal p {\r\n  font-weight: bold;\r\n  font-size: 140%;\r\n  padding-left: 20px;\r\n}\r\n\r\n.account button {\r\n  margin-right: 10px;\r\n}\r\n::v-deep .cl-table .ivu-table-cell {\r\n  padding: 6px 4px !important;\r\n  .ivu-input {\r\n    padding: 4px !important;\r\n  }\r\n}\r\n</style>\r\n"], "sourceRoot": "src/view/account/sc"}]}