{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\assessRanking.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\assessRanking.vue", "mtime": 1754285403029}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": 1741317456427}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["assessRanking.vue"], "names": [], "mappings": ";;;;;;;;AAOA;AACA,eAAA;AACA,EAAA,IADA,kBACA;AACA,WAAA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,IAAA,EACA;AACA;AAEA;AACA;AACA,UAAA,IAAA,EAAA,KADA;AAEA,UAAA,GAAA,EAAA,KAFA;AAGA,UAAA,IAAA,EAAA,IAHA;AAIA,UAAA,KAAA,EAAA,KAAA,UAAA,GAAA,IAAA,GAAA,IAJA;AAKA,UAAA,MAAA,EAAA;AALA,SANA;AAaA,QAAA,MAAA,EAAA;AACA,UAAA,KAAA,EAAA,KAAA,UAAA,GAAA,MAAA,GAAA,KADA;AAEA;AACA,UAAA,SAAA,EAAA;AACA,YAAA,QAAA,EAAA,EADA;AAEA,YAAA,KAAA,EAAA,MAFA;AAGA,YAAA,UAAA,EAAA;AAHA,WAHA;AAQA,UAAA,SAAA,EAAA,EARA;AASA,UAAA,UAAA,EAAA,CATA;AAUA,UAAA,OAAA,EAAA,EAVA;AAWA,UAAA,GAAA,EAAA,IAXA,CAYA;;AAZA;AAbA,OADA;AA6BA,MAAA,UAAA,EAAA,EA7BA;AA8BA,MAAA,MAAA,EAAA;AA9BA,KAAA;AAgCA,GAlCA;AAmCA,EAAA,KAAA,EAAA;AACA,IAAA,OAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA;AADA,GAnCA;AAwCA;AACA;AACA;AACA,EAAA,KAAA,EAAA;AACA,IAAA,UAAA,EAAA;AACA,MAAA,SAAA,EAAA,IADA;AAEA,MAAA,OAFA,mBAEA,QAFA,EAEA;AACA,aAAA,eAAA,CAAA,QAAA;AACA,OAJA;AAKA,MAAA,IAAA,EAAA;AALA,KADA;AAQA,IAAA,OAAA,EAAA;AACA,MAAA,OADA,mBACA,MADA,EACA,MADA,EACA;AAAA;;AACA,YAAA,MAAA,IAAA,SAAA,IAAA,MAAA,IAAA,IAAA,IAAA,MAAA,IAAA,EAAA,EAAA;AACA,eAAA,UAAA,GAAA,IAAA,CAAA,KAAA,CACA,IAAA,CAAA,SAAA,CAAA;AACA,YAAA,MAAA,EAAA,EADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAAA,CADA,CAAA;AAMA,SAPA,MAOA;AACA,cAAA,OAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CACA,QAAA,CAAA,cAAA,CAAA,oBAAA,CADA,CAAA;AAGA,UAAA,OAAA,CAAA,SAAA,CAAA;AAAA,YAAA,MAAA,EAAA;AAAA,WAAA,EAAA,IAAA;AAEA,eAAA,UAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,CAAA,CAAA;AACA;;AACA,aAAA,SAAA,CAAA,YAAA;AACA,UAAA,MAAA,CAAA,IAAA;AACA,SAFA;AAGA,OApBA;AAqBA,MAAA,IAAA,EAAA,IArBA,CAqBA;;AArBA;AARA,GA3CA;AA2EA,EAAA,OAAA,EAAA;AACA,IAAA,IADA,kBACA;AACA,UAAA,KAAA,GAAA,IAAA;;AACA,UAAA,OAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CACA,QAAA,CAAA,cAAA,CAAA,oBAAA,CADA,CAAA;AAGA,UAAA,WAAA,GAAA,EAAA;AACA,MAAA,WAAA,GAAA,KAAA,UAAA,CAAA,MAAA,CAAA,MAAA,CAAA,UAAA,GAAA,EAAA,GAAA,EAAA,KAAA,EAAA;AACA,QAAA,GAAA,CAAA,KAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CACA,UAAA,EAAA,EAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,GAAA,CAAA,KAAA,GAAA,CAAA,CAAA,GAAA,GAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA,EAAA,CAAA,GAAA,CAAA,CAAA;AAAA,SADA,CAAA;AAGA,eAAA,GAAA;AACA,OALA,EAKA,EALA,CAAA;AAMA,UAAA,KAAA,GAAA,CACA,CACA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CADA,EAOA,CACA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CAPA,EAaA,CACA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CAbA,EAmBA,CACA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CAnBA,EAyBA,CACA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CAzBA,EA+BA,CACA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CA/BA,EAqCA,CACA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,MAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CArCA,CAAA;AA4CA,UAAA,MAAA,GAAA,EAAA;AACA,MAAA,MAAA,GAAA,KAAA,UAAA,CAAA,MAAA,CAAA,MAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,KAAA,EAAA;AACA,QAAA,CAAA,CAAA,IAAA,CACA;AACA,UAAA,CAAA,EAAA,CAAA,GAAA,CADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA,KAHA;AAIA,UAAA,IAAA,EAAA,CAAA,CAAA,IAJA;AAKA,UAAA,QAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA,CAAA,CAAA,IANA;AAOA,UAAA,SAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,EAAA,QADA;AAEA,cAAA,CAAA,EAAA,CAFA;AAGA,cAAA,EAAA,EAAA,CAHA;AAIA,cAAA,CAAA,EAAA,CAJA;AAKA,cAAA,EAAA,EAAA,CALA;AAMA,cAAA,UAAA,EAAA,KAAA,CAAA,CAAA;AANA;AADA;AAPA,SADA,EAmBA;AACA,UAAA,CAAA,EAAA,CAAA,GAAA,CADA;AAEA,UAAA,IAAA,EAAA,cAFA;AAGA,UAAA,cAAA,EAAA,KAHA;AAIA,UAAA,MAAA,EAAA,SAJA;AAKA,UAAA,YAAA,EAAA,CAAA,CAAA,EAAA,MAAA,CALA;AAMA,UAAA,UAAA,EAAA,CAAA,EAAA,EAAA,EAAA,CANA;AAOA,UAAA,IAAA,EAAA,WAAA,CAAA,CAAA,CAPA;AAQA,UAAA,SAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,EAAA,QADA;AAEA,cAAA,CAAA,EAAA,CAFA;AAGA,cAAA,EAAA,EAAA,CAHA;AAIA,cAAA,CAAA,EAAA,CAJA;AAKA,cAAA,EAAA,EAAA,CALA;AAMA,cAAA,UAAA,EAAA,KAAA,CAAA,CAAA;AANA;AADA,WARA;AAkBA,UAAA,OAAA,EAAA;AAAA,YAAA,IAAA,EAAA;AAAA;AAlBA,SAnBA,EADA,CAyCA;;AACA,YAAA,MAAA,CAAA,MAAA,KAAA,KAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,CAAA,CAAA,IAAA,CAAA;AACA,YAAA,CAAA,EAAA,EADA;AAEA,YAAA,IAAA,EAAA,cAFA;AAGA,YAAA,cAAA,EAAA,OAHA;AAIA,YAAA,IAAA,EAAA,KAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA,EAAA,IAJA;AAKA,YAAA,MAAA,EAAA,SALA;AAMA,YAAA,YAAA,EAAA,CAAA,IAAA,EAAA,KAAA,CANA;AAOA,YAAA,UAAA,EAAA,CAAA,EAAA,EAAA,EAAA,CAPA;AAQA,YAAA,SAAA,EAAA;AACA,cAAA,KAAA,EAAA;AACA,gBAAA,IAAA,EAAA,QADA;AAEA,gBAAA,CAAA,EAAA,CAFA;AAGA,gBAAA,EAAA,EAAA,CAHA;AAIA,gBAAA,CAAA,EAAA,CAJA;AAKA,gBAAA,EAAA,EAAA,CALA;AAMA,gBAAA,UAAA,EAAA,KAAA,CAAA,CAAA;AANA;AADA,aARA;AAkBA,YAAA,OAAA,EAAA;AAAA,cAAA,IAAA,EAAA;AAAA;AAlBA,WAAA;AAoBA,iBAAA,CAAA;AACA;;AACA,eAAA,CAAA;AACA,OAlEA,EAkEA,EAlEA,CAAA,CAzDA,CA6HA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;;AACA,UAAA,KAAA,GAAA;AACA,QAAA,QAAA,EAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SADA;AAEA,QAAA,QAAA,EAAA;AAAA,UAAA,SAAA,EAAA;AAAA,YAAA,KAAA,EAAA;AAAA;AAAA,SAFA;AAGA,QAAA,SAAA,EAAA;AACA,UAAA,SAAA,EAAA;AACA,YAAA,QAAA,EAAA,EADA;AAEA,YAAA,KAAA,EAAA,MAFA;AAGA,YAAA,UAAA,EAAA;AAHA,WADA;AAOA,UAAA,SAAA,EAAA,mBACA,KADA,CACA;AADA,YAEA;AACA,mBAAA,KAAA,CAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA;AACA;AAXA,SAHA;AAgBA,QAAA,IAAA,EAAA,KAAA,UAAA,CAAA;AAhBA,OAAA;AAkBA,UAAA,QAAA,GAAA,CACA;AACA;AACA,QAAA,IAAA,EAAA,KAFA;AAGA,QAAA,IAAA,EAAA,QAHA;AAGA;AACA,QAAA,UAAA,EAAA,CAJA;AAIA;AACA,QAAA,KAAA,EAAA,CALA;AAKA;AACA,QAAA,GAAA,EAAA,EANA;AAMA;AACA,QAAA,QAAA,EAAA,IAPA;AAQA,QAAA,MAAA,EAAA,IARA;AASA,QAAA,MAAA,EAAA,IATA;AAUA,QAAA,WAAA,EAAA,SAVA;AAUA;AACA,QAAA,eAAA,EAAA,SAXA;AAYA,QAAA,UAAA,EAAA,KAZA;AAYA;AACA,QAAA,WAAA,EAAA;AACA,UAAA,WAAA,EAAA,SADA;AAEA,UAAA,UAAA,EAAA,CAFA;AAGA,UAAA,aAAA,EAAA,CAHA;AAIA,UAAA,aAAA,EAAA,CAJA;AAKA,UAAA,WAAA,EAAA;AALA;AAbA,OADA,EAsBA;AACA,QAAA,IAAA,EAAA,QADA;AACA;AACA,QAAA,UAAA,EAAA,CAFA;AAEA;AACA,QAAA,KAAA,EAAA,EAHA;AAGA;AACA,QAAA,GAAA,EAAA,EAJA;AAIA;AACA;AACA;AACA;AACA,QAAA,gBAAA,EAAA,KARA;AASA;AACA,QAAA,eAAA,EAAA,IAVA;AAWA,QAAA,gBAAA,EAAA;AAXA,OAtBA,CAAA,CAtKA,CA0MA;;AACA,UAAA,KAAA,GAAA,CACA;AACA,QAAA,SAAA,EAAA;AAAA,UAAA,SAAA,EAAA;AAAA,YAAA,KAAA,EAAA;AAAA;AAAA,SADA;AAEA,QAAA,QAAA,EAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAFA;AAGA,QAAA,SAAA,EAAA;AACA,UAAA,SAAA,EAAA;AACA,YAAA,QAAA,EAAA,EADA;AAEA,YAAA,KAAA,EAAA,MAFA;AAGA,YAAA,UAAA,EAAA;AAHA;AADA;AAHA,OADA,CAAA;AAaA,UAAA,MAAA,GAAA;AACA,QAAA,OAAA,EAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAEA,QAAA,KAAA,EAAA,KAFA;AAGA,QAAA,QAAA,EAAA,QAHA;AAIA,QAAA,KAAA,EAAA,KAJA;AAKA,QAAA,MAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,KAAA,YAAA,CAAA,IANA;AAOA,QAAA,MAAA,EAAA,KAAA,YAAA,CAAA;AAPA,OAAA;AASA,MAAA,OAAA,CAAA,SAAA,CAAA,MAAA,EAjOA,CAkOA;;AACA,MAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,YAAA;AACA,QAAA,OAAA,CAAA,MAAA;AACA,OAFA;AAGA,KAvOA;AAwOA,IAAA,eAxOA,2BAwOA,UAxOA,EAwOA;AACA,WAAA,YAAA,CAAA,IAAA,CAAA,KAAA,GAAA,UAAA,GAAA,IAAA,GAAA,IAAA;AACA,WAAA,YAAA,CAAA,MAAA,CAAA,KAAA,GAAA,UAAA,GAAA,MAAA,GAAA,KAAA;AAEA,UAAA,OAAA,GAAA,KAAA,QAAA,CAAA,IAAA,CACA,QAAA,CAAA,cAAA,CAAA,oBAAA,CADA,CAAA,CAJA,CAOA;;AACA,MAAA,OAAA,CAAA,SAAA,CAAA;AACA,QAAA,IAAA,EAAA,KAAA,YAAA,CAAA,IADA;AAEA,QAAA,MAAA,EAAA,KAAA,YAAA,CAAA;AAFA,OAAA;AAIA;AApPA;AA3EA,CAAA", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"assessment-ranking\" id=\"assessment-ranking\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import {  mapState } from \"vuex\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      chartOptions: {\r\n        grid: \r\n        // {\r\n        //   right: this.isCollapse ? '1%' : '2%',\r\n          \r\n        // },\r\n      { \r\n          show: false, \r\n          top: \"20%\", \r\n          left: \"4%\", \r\n          right: this.isCollapse ? '1%' : '2%', \r\n          bottom: \"18%\", \r\n        },\r\n        legend: { \r\n          right: this.isCollapse ? '0.5%' : '10%',\r\n          // data: this.companyarr.result.map((item) => item.name),\r\n          textStyle: {\r\n          fontSize: 12,\r\n          color: \"#fff\",\r\n          fontFamily: \"PingFangSC-Regular\",\r\n          },\r\n          itemWidth: 20,\r\n          itemHeight: 9,\r\n          itemGap: 15,\r\n          top: \"2%\",\r\n          // right: this.chartOptions.legend.right,\r\n          },\r\n      },\r\n      companyarr: {},\r\n      series: [],\r\n    };\r\n  },\r\n  props: {\r\n    dataArr: {\r\n      type: Object,\r\n    },\r\n  },\r\n  // computed: {\r\n  //   ...mapState({isCollapse: state => state.common.isCollapse})\r\n  // },\r\n  watch: {\r\n    isCollapse: {\r\n      immediate: true,\r\n      handler(newValue) {\r\n        this.updateGridRight(newValue);\r\n      },\r\n      deep: true\r\n    },\r\n    dataArr: {\r\n      handler(newVal, oldVal) {\r\n        if (newVal == undefined || newVal == null || newVal == \"\") {\r\n          this.companyarr = JSON.parse(\r\n            JSON.stringify({\r\n              result: [],\r\n              xdata: [],\r\n            })\r\n          );\r\n        } else {\r\n          let myChart = this.$echarts.init(\r\n            document.getElementById(\"assessment-ranking\")\r\n          );\r\n          myChart.setOption({ series: [] }, true);\r\n\r\n          this.companyarr = JSON.parse(JSON.stringify(newVal));\r\n        }\r\n        this.$nextTick(() => {\r\n          this.init();\r\n        });\r\n      },\r\n      deep: true, // 深度监听\r\n    },\r\n  },\r\n  methods: {\r\n    init() {\r\n      let _this = this;\r\n      let myChart = this.$echarts.init(\r\n        document.getElementById(\"assessment-ranking\")\r\n      );\r\n      let diamondData = [];\r\n      diamondData = this.companyarr.result.reduce((pre, cur, index) => {\r\n        pre[index] = cur.data.map(\r\n          (el, id) => el + (pre[index - 1] ? pre[index - 1][id] : 0)\r\n        );\r\n        return pre;\r\n      }, []);\r\n      const color = [\r\n        [\r\n          { offset: 0, color: \"#FF7D41\" },\r\n          { offset: 0.5, color: \"#FF7D41\" },\r\n          { offset: 0.5, color: \"#EE7036\" },\r\n          { offset: 1, color: \"#EE7036\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#FBC658\" },\r\n          { offset: 0.5, color: \"#FBC658\" },\r\n          { offset: 0.5, color: \"#FBBB54\" },\r\n          { offset: 1, color: \"#FBBB54\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#4A99FA\" },\r\n          { offset: 0.5, color: \"#4A99FA\" },\r\n          { offset: 0.5, color: \"#1C7CFA\" },\r\n          { offset: 1, color: \"#1C7CFA\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#25C5FB\" },\r\n          { offset: 0.5, color: \"#25C5FB\" },\r\n          { offset: 0.5, color: \"#0EB1FB\" },\r\n          { offset: 1, color: \"#0EB1FB\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#00A083\" },\r\n          { offset: 0.5, color: \"#00A083\" },\r\n          { offset: 0.5, color: \"#027964\" },\r\n          { offset: 1, color: \"#027964\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#22DEBB\" },\r\n          { offset: 0.5, color: \"#22DEBB\" },\r\n          { offset: 0.5, color: \"#00C6A1\" },\r\n          { offset: 1, color: \"#00C6A1\" },\r\n        ],\r\n        [\r\n          { offset: 0, color: \"#ACFFF0\" },\r\n          { offset: 0.5, color: \"#ACFFF0\" },\r\n          { offset: 0.5, color: \"#7FE3D1\" },\r\n          { offset: 1, color: \"#7FE3D1\" },\r\n        ],\r\n      ];\r\n      let series = [];\r\n      series = this.companyarr.result.reduce((p, c, i, array) => {\r\n        p.push(\r\n          {\r\n            z: i + 1,\r\n            stack: \"总量\",\r\n            type: \"bar\",\r\n            name: c.name,\r\n            barWidth: 25,\r\n            data: c.data,\r\n            itemStyle: {\r\n              color: {\r\n                type: \"linear\",\r\n                x: 0,\r\n                x2: 1,\r\n                y: 0,\r\n                y2: 0,\r\n                colorStops: color[i],\r\n              },\r\n            },\r\n          },\r\n          {\r\n            z: i + 1,\r\n            type: \"pictorialBar\",\r\n            symbolPosition: \"end\",\r\n            symbol: \"diamond\",\r\n            symbolOffset: [0, \"-50%\"],\r\n            symbolSize: [25, 10],\r\n            data: diamondData[i],\r\n            itemStyle: {\r\n              color: {\r\n                type: \"linear\",\r\n                x: 0,\r\n                x2: 1,\r\n                y: 0,\r\n                y2: 0,\r\n                colorStops: color[i],\r\n              },\r\n            },\r\n            tooltip: { show: false },\r\n          }\r\n        );\r\n        // 是否最后一个了？\r\n        if (series.length === array.length * 2) {\r\n          p.push({\r\n            z: 20,\r\n            type: \"pictorialBar\",\r\n            symbolPosition: \"start\",\r\n            data: _this.companyarr.result[0].data,\r\n            symbol: \"diamond\",\r\n            symbolOffset: [\"0%\", \"50%\"],\r\n            symbolSize: [30, 10],\r\n            itemStyle: {\r\n              color: {\r\n                type: \"linear\",\r\n                x: 0,\r\n                x2: 1,\r\n                y: 0,\r\n                y2: 0,\r\n                colorStops: color[0],\r\n              },\r\n            },\r\n            tooltip: { show: false },\r\n          });\r\n          return p;\r\n        }\r\n        return p;\r\n      }, []);\r\n\r\n      // tooltip\r\n\r\n      // legend\r\n      // const legend = this.chartOptions.legend\r\n      // {\r\n      //   data: _this.companyarr.result.map((item) => item.name),\r\n      //   textStyle: {\r\n      //     fontSize: 12,\r\n      //     color: \"#fff\",\r\n      //     fontFamily: \"PingFangSC-Regular\",\r\n      //   },\r\n      //   itemWidth: 20,\r\n      //   itemHeight: 9,\r\n      //   itemGap: 15,\r\n      //   top: \"2%\",\r\n      //   right: this.chartOptions.legend.right,\r\n      // };\r\n\r\n      // grid\r\n      // const grid = this.chartOptions.grid;\r\n      // { top: \"20%\", left: \"4%\", right: this.chartOptions.grid.right, bottom: \"18%\" };\r\n\r\n      // xAxis\r\n      const xAxis = {\r\n        axisTick: { show: true },\r\n        axisLine: { lineStyle: { color: \"rgba(255,255,255, .2)\" } },\r\n        axisLabel: {\r\n          textStyle: {\r\n            fontSize: 12,\r\n            color: \"#fff\",\r\n            fontFamily: \"PingFangSC-Regular\",\r\n          },\r\n\r\n          formatter: function (\r\n            value //X轴的内容\r\n          ) {\r\n            return value.replace(/\\s/, \"\\n\");\r\n          },\r\n        },\r\n        data: this.companyarr.xdata,\r\n      };\r\n      const dataZoom = [\r\n        {\r\n        // 设置滚动条的隐藏与显示\r\n          show: false,\r\n          type: \"slider\", //这个dataZoom组件是slider型dataZoom组件\r\n          xAxisIndex: 0, //dataZoom-slider组件控制第一个XAxis\r\n          start: 0, //左边在10%位置\r\n          end: 60, //右边在60%位置\r\n          zoomLock: true,\r\n          height: \"10\",\r\n          bottom: \"4%\",\r\n          borderColor: \"#1B3149\", //滑动通道的边框颜色\r\n          backgroundColor: \"#275277\",\r\n          showDetail: false, // 即拖拽时候是否显示详细数值信息 默认tru\r\n          handleStyle: {\r\n            borderColor: \"#cbdbfd\",\r\n            shadowBlur: 6,\r\n            shadowOffsetX: 1,\r\n            shadowOffsetY: 1,\r\n            shadowColor: \"#cbdbfd\",\r\n          },\r\n        },\r\n        {\r\n          type: \"inside\", //这个dataZoom组件是inside型dataZoom组件\r\n          xAxisIndex: 0, //dataZoom-inslide组件控制第一个XAxis\r\n          start: 10, //左边在10%的位置\r\n          end: 60, //右边在60%的位置\r\n          // showDetail: false, // 即拖拽时候是否显示详细数值信息 默认tru\r\n          // zoomLock: true,\r\n          // // 滚轮是否触发缩放\r\n          zoomOnMouseWheel: false,\r\n          // 鼠标滚轮触发滚动\r\n          moveOnMouseMove: true,\r\n          moveOnMouseWheel: true,\r\n        },\r\n      ];\r\n      // yAxis\r\n      const yAxis = [\r\n        {\r\n          splitLine: { lineStyle: { color: \"rgba(255,255,255, .05)\" } },\r\n          axisLine: { show: false },\r\n          axisLabel: {\r\n            textStyle: {\r\n              fontSize: 12,\r\n              color: \"#fff\",\r\n              fontFamily: \"PingFangSC-Regular\",\r\n            },\r\n          },\r\n        },\r\n      ];\r\n      let option = {\r\n        tooltip: { trigger: \"axis\" },\r\n        xAxis,\r\n        dataZoom,\r\n        yAxis,\r\n        series,\r\n        grid: this.chartOptions.grid,\r\n        legend: this.chartOptions.legend,\r\n      };\r\n      myChart.setOption(option);\r\n      // 渲染\r\n      window.addEventListener(\"resize\", function () {\r\n        myChart.resize();\r\n      });\r\n    },\r\n    updateGridRight(isCollapse) {\r\n      this.chartOptions.grid.right = isCollapse ? '0%' : '2%';\r\n      this.chartOptions.legend.right = isCollapse ? '0.5%' : '10%';\r\n      \r\n      let myChart = this.$echarts.init(\r\n        document.getElementById(\"assessment-ranking\")\r\n      );\r\n      // 使用setOption更新图表配置\r\n      myChart.setOption({\r\n        grid: this.chartOptions.grid,\r\n        legend: this.chartOptions.legend\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.assessment-ranking {\r\n  // width: 100%;\r\n  // width: 145rem;\r\n  height: 37.8vh;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/view/carbon/assess/assessReport/components"}]}