{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePagePylon\\addPylonCreditAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePagePylon\\addPylonCreditAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "sources": ["addPylonCreditAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsZA,SACA,mBADA,EAEA,YAFA,EAGA,eAHA,EAIA,OAJA,EAKA,cALA,EAMA,cANA,EAOA,mBAPA,QAQA,eARA;AASA,SAAA,YAAA,EAAA,UAAA,QAAA,iCAAA;AACA,SAAA,SAAA,IAAA,UAAA,QAAA,sBAAA;AACA,SACA,iBADA,EAEA,WAFA,EAGA,iBAHA,EAIA,eAJA,EAKA,kBALA,QAMA,2BANA;AAOA,SAAA,OAAA,QAAA,2BAAA;AACA,SAAA,cAAA,EAAA,iBAAA,EAAA,qBAAA,QAAA,eAAA;AACA,SAAA,iBAAA,QAAA,gBAAA;AACA,SACA,SADA,EAEA,UAFA,EAGA,gBAHA,EAIA,gCAJA,EAKA,+BALA,EAMA,YANA,EAOA,cAPA,QAQA,mCARA;AASA,SACA,iBADA,EAEA,eAFA,EAGA,YAHA,EAIA,YAJA,EAKA,YALA,EAMA,aANA,QAOA,+BAPA;AAQA,SACA,YADA,EAEA,cAFA,EAGA,cAHA,QAIA,uCAJA;AAKA,SAAA,UAAA,QAAA,mDAAA;AACA,SAAA,KAAA,QAAA,cAAA;AACA,OAAA,KAAA,MAAA,cAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AACA,OAAA,iBAAA,MAAA,kCAAA;AACA,OAAA,UAAA,MAAA,gCAAA;AACA,OAAA,aAAA,MAAA,8BAAA;AACA,OAAA,SAAA,MAAA,gBAAA;AACA,OAAA,YAAA,MAAA,sCAAA;AACA,OAAA,UAAA,MAAA,iCAAA;AACA,OAAA,WAAA,MAAA,kCAAA;AACA,OAAA,sBAAA,MAAA,6CAAA;AACA,SAAA,QAAA,QAAA,MAAA;AAEA,OAAA,eAAA,MAAA,qBAAA;AACA,OAAA,OAAA,MAAA,kBAAA;AAEA,IAAA,KAAA,GAAA,SAAA,EAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,qBADA;AAEA,EAAA,MAAA,EAAA,CAAA,eAAA,EAAA,OAAA,CAFA;AAGA,EAAA,UAAA,EAAA;AACA,IAAA,UAAA,EAAA,UADA;AAEA,IAAA,WAAA,EAAA,WAFA;AAGA,IAAA,sBAAA,EAAA,sBAHA;AAIA,IAAA,iBAAA,EAAA,iBAJA;AAKA,IAAA,aAAA,EAAA,aALA;AAMA,IAAA,UAAA,EAAA,UANA;AAOA,IAAA,YAAA,EAAA;AAPA,GAHA;AAYA,EAAA,IAZA,kBAYA;AAAA;;AACA,QAAA,YAAA,GAAA,SAAA,YAAA,CAAA,CAAA,QAAA;AAAA,UAAA,GAAA,QAAA,GAAA;AAAA,UAAA,KAAA,QAAA,KAAA;AACA,UAAA,MAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA,CAAA,SAAA,CAAA,IAAA,CAAA,KAAA,CAAA;AAFA;AAAA;AAAA;;AAAA;AAGA,6BAAA,KAAA,CAAA,aAAA,8HAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,GAAA,CAAA,MAAA,EAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AASA,aAAA,CAAA,CAAA,KAAA,EAAA,IAAA,CAAA,UAAA,CAAA;AACA,KAVA;;AAYA,QAAA,cAAA,GAAA,SAAA,cAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,YAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,8BAAA,KAAA,CAAA,SAAA,mIAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,QAAA,EAAA;AACA,YAAA,YAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,YAAA,CAAA;AACA,KATA;;AAWA,WAAA;AACA,MAAA,GAAA,EAAA,IADA;AAEA,MAAA,OAAA,EAAA,CAFA;AAGA,MAAA,IAAA,EAAA,EAHA;AAIA,MAAA,KAAA,EAAA,EAJA;AAKA,MAAA,OAAA,EAAA,IALA;AAMA,MAAA,MAAA,EAAA,CANA;AAOA,MAAA,UAAA,EAAA,EAPA;AAQA,MAAA,MAAA,EAAA,EARA;AASA,MAAA,OAAA,EAAA,EATA;AAUA,MAAA,UAAA,EAAA,EAVA;AAWA,MAAA,cAAA,EAAA,KAXA;AAYA,MAAA,cAAA,EAAA,KAZA;AAaA,MAAA,WAAA,EAAA,KAbA;AAcA,MAAA,aAAA,EAAA,UAdA;AAeA,MAAA,OAAA,EAAA,EAfA;AAgBA,MAAA,QAAA,EAAA,KAhBA;AAiBA,MAAA,UAAA,EAAA,IAjBA;AAiBA;AACA,MAAA,SAAA,EAAA,CAAA,CAlBA;AAkBA;AACA,MAAA,YAAA,EAAA,CAAA,CAnBA;AAmBA;AACA,MAAA,aAAA,EAAA,EApBA;AAqBA,MAAA,OAAA,EAAA,EArBA;AAqBA;AACA,MAAA,WAAA,EAAA,EAtBA;AAuBA,MAAA,mBAAA,EAAA,EAvBA;AAwBA,MAAA,qBAAA,EAAA,EAxBA;AAyBA,MAAA,WAAA,EAAA,EAzBA;AA0BA,MAAA,oBAAA,EAAA,EA1BA;AA2BA,MAAA,uBAAA,EAAA,EA3BA;AA4BA,MAAA,QAAA,EAAA,KA5BA;AA4BA;AACA,MAAA,SAAA,EAAA,EA7BA;AA6BA;AACA,MAAA,gBAAA,EAAA,EA9BA;AA+BA,MAAA,UAAA,EAAA,EA/BA;AAgCA,MAAA,aAAA,EAAA,EAhCA;AAiCA,MAAA,SAAA,EAAA,EAjCA;AAkCA,MAAA,WAAA,EAAA,EAlCA;AAmCA,MAAA,OAAA,EAAA,KAnCA;AAoCA,MAAA,OAAA,EAAA,IApCA;AAoCA;AACA,MAAA,OAAA,EAAA,IArCA;AAqCA;AACA,MAAA,WAAA,EAAA,IAtCA;AAsCA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,GAAA,EAAA,KADA;AACA;AACA,QAAA,IAAA,EAAA,EAFA;AAEA;AACA,QAAA,SAAA,EAAA,CAHA;AAGA;AACA,QAAA,WAAA,EAAA,CAJA;AAIA;AACA,QAAA,OAAA,EAAA,CALA;AAMA,QAAA,IAAA,EAAA;AANA,OAvCA;AA+CA,MAAA,UAAA,EAAA;AACA,QAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IADA;AACA;AACA,QAAA,OAAA,EAAA,EAFA;AAEA;AACA,QAAA,WAAA,EAAA,EAHA;AAGA;AACA,QAAA,OAAA,EAAA,EAJA;AAIA;AACA,QAAA,WAAA,EAAA,EALA;AAKA;AACA,QAAA,WAAA,EAAA,EANA;AAOA,QAAA,WAAA,EAAA,GAPA;AAOA;AACA,QAAA,aAAA,EAAA,CARA;AAQA;AACA,QAAA,uBAAA,EAAA;AATA,OA/CA;AA0DA,MAAA,SAAA,EAAA;AACA,QAAA,OAAA,EAAA,KADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,IAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,CAHA;AAOA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,KAAA,EAAA,GAFA;AAGA,UAAA,GAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,gBAAA,IAAA,GAAA,KAAA;AACA,mBAAA,CAAA,CACA,QADA,EAEA;AACA,cAAA,KAAA,EAAA;AACA,gBAAA,IAAA,EAAA,SADA;AAEA,gBAAA,IAAA,EAAA;AAFA,eADA;AAKA,cAAA,KAAA,EAAA;AACA,6BAAA;AADA,eALA;AAQA,cAAA,EAAA,EAAA;AACA,gBAAA,KADA,mBACA;AACA,kBAAA,IAAA,CAAA,KAAA,CAAA,qBAAA,CAAA,IAAA,GAAA,MAAA,CAAA,GAAA,CAAA,IAAA;AACA,kBAAA,IAAA,CAAA,cAAA,GAAA,IAAA;AACA;AAJA;AARA,aAFA,EAiBA,SAjBA,CAAA;AAmBA;AA1BA,SAFA,EA8BA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,IAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA9BA,CAPA;AAuCA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,GAAA,EAAA,WAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAJA,EAWA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,SAFA;AAGA,UAAA,GAAA,EAAA,SAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAXA,EAkBA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,IAAA,EAAA,iBAFA;AAGA,UAAA,GAAA,EAAA,iBAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAlBA,EAwBA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,GAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAxBA,EAyBA;AAAA,UAAA,KAAA,EAAA,WAAA;AAAA,UAAA,IAAA,EAAA,kBAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAzBA,EA0BA;AAAA,UAAA,KAAA,EAAA,WAAA;AAAA,UAAA,IAAA,EAAA,qBAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA1BA,EA2BA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA3BA,EA4BA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA5BA,EA6BA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA,cAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA7BA,EA8BA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,GAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA9BA,EA+BA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA,iBAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA/BA,EAgCA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,cAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,MAAA,EAAA;AAJA,SAhCA,CAvCA;AA8EA,QAAA,QAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA,yBAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,CA9EA;AAiFA,QAAA,QAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,WAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA,yBAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,CAjFA;AAqFA,QAAA,IAAA,EAAA,EArFA;AAsFA,QAAA,KAAA,EAAA,CAtFA;AAuFA,QAAA,aAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,IAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,WAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA,yBAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SANA,EAOA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,GAAA,EAAA,WAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAPA,EAcA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,SAFA;AAGA,UAAA,GAAA,EAAA,SAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAdA,EAqBA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,IAAA,EAAA,iBAFA;AAGA,UAAA,GAAA,EAAA,iBAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SArBA,EA2BA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,GAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA3BA,EA4BA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,cAAA;AAAA,UAAA,GAAA,EAAA,cAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA5BA,EA6BA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,GAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA7BA,EA8BA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA,iBAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA9BA,EA+BA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA,cAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA/BA;AAvFA,OA1DA;AAmLA,MAAA,SAAA,EAAA,CAnLA;AAoLA,MAAA,OAAA,EAAA,CApLA;AAqLA,MAAA,QAAA,EAAA,EArLA,CAqLA;;AArLA,KAAA;AAuLA,GA3NA;AA4NA,EAAA,OA5NA,qBA4NA;AACA,SAAA,YAAA,GADA,CACA;;AACA,SAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,SAAA,SAAA,CAAA,OAAA,GAAA,KAAA,SAAA,CAAA,WAAA,CACA,MADA,CACA,KAAA,SAAA,CAAA,QADA,EAEA,MAFA,CAEA,KAAA,SAAA,CAAA,UAFA,CAAA;AAGA,SAAA,aAAA,GAAA,KAAA,CAAA,eAAA,CAAA;AACA,SAAA,SAAA,GAAA,KAAA,CAAA,iBAAA,CAAA;AACA,QAAA,IAAA,GAAA,IAAA;AACA,IAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,UACA,GAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,IACA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IADA,IAEA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAHA,EAIA;AACA,QAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,MAAA,eAAA,CAAA;AAAA,QAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA;AAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,IAAA,CAAA,WAAA;AACA,OAJA;AAKA,KAfA;AAgBA,GArPA;AAsPA,EAAA,QAAA,oBACA,QAAA,CAAA;AACA,IAAA,OAAA,EAAA,iBAAA,KAAA;AAAA,aAAA,KAAA,CAAA,IAAA,CAAA,OAAA;AAAA;AADA,GAAA,CADA,CAtPA;AA2PA,EAAA,OAAA,EAAA;AACA,IAAA,WADA,uBACA,CADA,EACA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,CAAA;AACA,KAHA;AAIA,IAAA,YAJA,wBAIA,CAJA,EAIA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,CAAA;AACA,KANA;AAOA,IAAA,YAPA,0BAOA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA;AACA,KATA;AAUA,IAAA,OAVA,mBAUA,CAVA,EAUA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,IAAA,GAAA,CAAA;;AACA,UAAA,KAAA,KAAA,CAAA,cAAA,CAAA,IAAA,IAAA,KAAA,EAAA;AACA,aAAA,OAAA;AACA,aAAA,GAAA,GAAA,CAAA;AACA,OAHA,MAGA,IAAA,KAAA,KAAA,CAAA,cAAA,CAAA,IAAA,IAAA,IAAA,EAAA;AACA,aAAA,OAAA,GAAA,CAAA;AACA,aAAA,GAAA,GAAA,CAAA,CAAA;AACA;;AACA,UAAA,KAAA,GAAA,IAAA,KAAA,OAAA,GAAA,EAAA,EAAA;AACA,aAAA,OAAA,CAAA,CAAA;AACA;AACA,KAtBA;AAuBA,IAAA,SAvBA,uBAuBA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,IAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,SAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,EAAA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,YAAA,GAAA,CAAA;AACA,KAxCA;AAyCA,IAAA,UAzCA,wBAyCA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,WAAA,KAAA,CAAA,cAAA,CAAA,IAAA,GAAA,KAAA;AACA,KA5CA;AA6CA,IAAA,WA7CA,yBA6CA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,KA/CA;AAgDA,IAAA,UAhDA,wBAgDA,CAAA,CAhDA;AAiDA,IAAA,YAjDA,0BAiDA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,YAAA,IAAA,CAAA,UAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,CAAA,CAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,IAAA;AACA,SAHA,MAGA;AACA,UAAA,kBAAA,CAAA,IAAA,CAAA,UAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA;AACA;AACA,WALA;AAMA;AACA;AACA,KAhEA;AAiEA;AACA,IAAA,oBAlEA,kCAkEA;AACA,UAAA,KAAA,UAAA,CAAA,OAAA,IAAA,IAAA,IAAA,KAAA,UAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AACA;AACA;;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EALA,CAKA;AACA,KAxEA;AAyEA,IAAA,gBAzEA,4BAyEA,IAzEA,EAyEA;AACA,WAAA,UAAA,CAAA,OAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,UAAA,CAAA,WAAA,GAAA,IAAA,CAAA,IAAA,CAFA,CAGA;AACA,KA7EA;AA8EA,IAAA,WA9EA,yBA8EA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,WAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,EAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA;;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,IAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA;;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,MAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA;;AAEA,QAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,kBAAA;AACA,OAvBA;AAwBA,KAxGA;AAyGA,IAAA,UAzGA,wBAyGA;AACA,UAAA,KAAA,UAAA,CAAA,WAAA,IAAA,EAAA,EAAA;AACA,aAAA,UAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,WAAA,OAAA,GAAA,CAAA;AACA,WAAA,kBAAA;AACA,KA/GA;AAgHA,IAAA,eAhHA,6BAgHA;AACA,WAAA,UAAA;AACA,KAlHA;AAmHA,IAAA,cAAA,EAAA,wBAAA,IAAA,EAAA;AACA,UAAA,SAAA,GAAA,EAAA;AACA,UAAA,UAAA,GAAA,EAAA;AACA,UAAA,EAAA,GAAA,KAAA,UAAA,CAAA,SAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,GAAA,GAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,GAAA,IAAA;AACA,UAAA,GAAA,CAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,UAAA,GAAA,CAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,UAAA,GAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA;AACA,UAAA,GAAA,CAAA,YAAA,GAAA,IAAA,CAAA,YAAA;AACA,UAAA,GAAA,CAAA,QAAA,GAAA,IAAA,CAAA,QAAA;AACA,UAAA,GAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA,UAAA,GAAA,CAAA,OAAA,GAAA,IAAA,CAAA,OAAA;AACA,UAAA,GAAA,CAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,UAAA,GAAA,CAAA,OAAA,GAAA,IAAA,CAAA,OAAA;AACA,UAAA,GAAA,CAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,UAAA,GAAA,CAAA,SAAA,GAAA,IAAA;AACA,UAAA,GAAA,CAAA,OAAA,GAAA,IAAA;AACA,UAAA,GAAA,CAAA,eAAA,GAAA,CAAA;AACA,UAAA,GAAA,CAAA,iBAAA,GAAA,CAAA;AACA,UAAA,GAAA,CAAA,SAAA,GAAA,CAAA;AACA,UAAA,GAAA,CAAA,gBAAA,GAAA,CAAA;AACA,UAAA,GAAA,CAAA,mBAAA,GAAA,CAAA;AACA,UAAA,GAAA,CAAA,OAAA,GAAA,IAAA;AACA,UAAA,GAAA,CAAA,SAAA,GAAA,CAAA;AACA,UAAA,GAAA,CAAA,YAAA,GAAA,CAAA;AACA,UAAA,GAAA,CAAA,MAAA,GAAA,IAAA;AACA,UAAA,GAAA,CAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,UAAA,GAAA,CAAA,cAAA,GAAA,IAAA,CAAA,cAAA;AACA,UAAA,GAAA,CAAA,cAAA,GAAA,IAAA,CAAA,cAAA;AACA,UAAA,GAAA,CAAA,eAAA,GAAA,IAAA,CAAA,eAAA;AACA,UAAA,GAAA,CAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,UAAA,GAAA,CAAA,SAAA,GAAA,gCAAA,CAAA,EAAA,CAAA;AACA,UAAA,GAAA,CAAA,OAAA,GAAA,+BAAA,CAAA,EAAA,CAAA;AACA,UAAA,GAAA,CAAA,aAAA,GAAA,CAAA;AACA,UAAA,GAAA,CAAA,uBAAA,GAAA,IAAA,CAAA,uBAAA;AACA,UAAA,GAAA,CAAA,gBAAA,GAAA,IAAA,CAAA,gBAAA;AACA,UAAA,GAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,kBAAA;AACA,UAAA,SAAA,CAAA,IAAA,CAAA,GAAA;AACA,UAAA,UAAA,CAAA,IAAA,CAAA;AAAA,YAAA,MAAA,EAAA,IAAA,CAAA,MAAA;AAAA,YAAA,WAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SArCA;AAsCA,aAAA,UAAA,GAAA,UAAA;AACA;;AAEA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,UAAA,MAAA,GAAA,KAAA,SAAA,CAAA,IAAA;;AACA,UAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,IAAA,GAAA,SAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,SAAA;;AACA,YAAA,QAAA,OAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,iBAAA,IAAA,CAAA,GAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,EAAA;AACA,kBAAA,EAAA,GAAA,GAAA,CAAA,CAAA,CAAA;;AACA,kBAAA,IAAA,CAAA,SAAA,KAAA,EAAA,CAAA,SAAA,EAAA;AACA,gBAAA,GAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA;AACA;AACA;AACA,WAPA;AAQA;;AACA,YAAA,KAAA,GAAA,KAAA,SAAA;AACA,aAAA,SAAA,GAAA,KAAA,GAAA,GAAA,CAAA,MAAA;AACA,aAAA,SAAA,CAAA,IAAA,GAAA,GAAA,CAAA,MAAA,CAAA,KAAA,SAAA,CAAA,IAAA,CAAA;AACA;;AAEA,WAAA,UAAA,CAAA,KAAA,SAAA,CAAA,IAAA,CAAA,MAAA;AACA,KAvLA;AAwLA;AACA,IAAA,QAzLA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0LA,gBAAA,KA1LA,GA0LA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EA1LA;AA2LA,gBAAA,CA3LA,GA2LA,KA3LA;AA4LA,gBAAA,IA5LA,GA4LA,KAAA,SAAA,CAAA,IA5LA;AA6LA,gBAAA,KA7LA,GA6LA,EA7LA;AA8LA,gBAAA,OA9LA,GA8LA,SAAA,CAAA,OA9LA;AA+LA,gBAAA,CA/LA,GA+LA,CA/LA;;AAAA;AAAA,sBA+LA,CAAA,GAAA,KAAA,CAAA,MA/LA;AAAA;AAAA;AAAA;;AAAA,sBAgMA,KAAA,CAAA,CAAA,CAAA,CAAA,QAAA,IAAA,CAhMA;AAAA;AAAA;AAAA;;AAAA,sBAiMA,QAAA,OAAA,IAAA,KAAA,CAAA,CAAA,CAAA,CAAA,WAAA,IAAA,KAAA,CAAA,CAAA,CAAA,CAAA,WAAA,GAAA,IAjMA;AAAA;AAAA;AAAA;;AAAA,sBAmMA,KAAA,CAAA,CAAA,CAAA,CAAA,cAAA,IAAA,IAAA,IACA,KAAA,CAAA,CAAA,CAAA,CAAA,cAAA,IAAA,SADA,IAEA,KAAA,CAAA,CAAA,CAAA,CAAA,cAAA,IAAA,EArMA;AAAA;AAAA;AAAA;;AAuMA,qBAAA,SAAA,CACA,aACA,KAAA,CAAA,CAAA,CAAA,CAAA,WADA,GAEA,SAFA,GAGA,KAAA,CAAA,CAAA,CAAA,CAAA,WAHA,GAIA,8CALA;AAvMA;;AAAA;AAiNA;AACA,gBAAA,MAlNA,GAkNA,eAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAlNA;;AAAA,qBAmNA,MAnNA;AAAA;AAAA;AAAA;;AAoNA,qBAAA,SAAA,CAAA,MAAA;AApNA;;AAAA;AAuNA,gBAAA,CAAA,GAAA,IAAA;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;;AAxNA;AA+LA,gBAAA,CAAA,EA/LA;AAAA;AAAA;;AAAA;AA2NA,oBAAA,CAAA,EAAA;AACA,uBAAA,UAAA,CAAA,KAAA;AACA,iBAFA,MAEA;AACA,uBAAA,SAAA,CAAA,SAAA;AACA;;AA/NA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAiOA,IAAA,UAjOA,wBAiOA;AACA,WAAA,KAAA,CAAA,WAAA,CAAA,UAAA,GAAA,KAAA,UAAA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,KApOA;AAqOA,IAAA,eArOA;AAAA;AAAA;AAAA,gDAqOA,WArOA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAsOA,qBAAA,aAAA,GAAA,CAAA;AAtOA;AAAA,uBAuOA,iBAAA,CAAA;AAAA,kBAAA,WAAA,EAAA;AAAA,iBAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,EAAA;AACA,oBAAA,MAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA;AACA;AACA,iBAJA,CAvOA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA6OA,IAAA,aA7OA,yBA6OA,IA7OA,EA6OA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KA/OA;AAgPA,IAAA,YAhPA,wBAgPA,SAhPA,EAgPA;AACA,UAAA,IAAA,GAAA,EAAA;AACA,WAAA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA;AACA,QAAA,SAAA,CAAA,GAAA,CAAA,UAAA,KAAA,EAAA;AACA,cAAA,KAAA,IAAA,KAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,SAJA;AAKA,OANA;AAOA,WAAA,MAAA,GAAA,IAAA;AACA,KA1PA;AA2PA,IAAA,iBA3PA,6BA2PA,IA3PA,EA2PA;AAAA;;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA,OAFA;AAGA,UAAA,KAAA,GAAA;AACA,QAAA,KAAA,EAAA;AADA,OAAA;AAGA,MAAA,qBAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,MAAA,CAAA,eAAA,GAAA,IAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,eAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,SAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AACA,cAAA,IAAA,CAAA,MAAA,IAAA,IAAA,EAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAA,IAAA,CAAA,gBAAA,CAAA,cAAA,IAAA,GAAA,EAAA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,gBAAA,IAAA,CAAA,gBAAA,CAAA,iBAAA,IAAA,GAAA,EAAA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,gBACA,IAAA,CAAA,gBAAA,CAAA,kBAAA,IAAA,GAAA,IACA,IAAA,CAAA,gBAAA,CAAA,kBAAA,IAAA,GADA,IAEA,IAAA,CAAA,gBAAA,CAAA,qBAAA,IAAA,GAFA,IAGA,IAAA,CAAA,gBAAA,CAAA,aAAA,IAAA,GAHA,IAIA;AACA,YAAA,IAAA,CAAA,gBAAA,CAAA,kBAAA,IAAA,GALA,IAMA,IAAA,CAAA,gBAAA,CAAA,mBAAA,IAAA,GAPA,EAQA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA,aA3BA,CA4BA;;AACA,WA7BA,MA6BA;AACA,iBACA;AACA;AACA;AAEA;AACA;AACA,YAAA,IAAA,CAAA,gBAAA,CAAA,eAAA,IAAA,GAPA,CAOA;AAPA,cAQA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA,eAXA,MAWA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,gBAAA;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;AACA;;AACA,cAAA,MAAA,CAAA,eAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,eAAA,CAAA,MAAA,CAAA,eAAA,CAAA,MAAA,GAAA,CAAA,EAAA,QAAA,GAAA,CAAA;AACA;;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,WAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,GAAA,GAAA;;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UAAA;AACA,SAtDA;AAuDA,OAzDA;AA0DA,KA7TA;AA8TA,IAAA,IA9TA,gBA8TA,KA9TA,EA8TA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA,CADA,CAEA;;AACA,UAAA,KAAA,IAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,IAAA,GAAA,UAAA;AACA,QAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;AACA,gBAAA,KAAA,CAAA,WAAA,IAAA,KAAA,CAAA,WAAA,EAAA;AACA,cAAA,KAAA,CAAA,MAAA,GAAA,KAAA,CAAA,MAAA;AACA;AACA,WAJA;AAKA,SANA;AAOA,aAAA,iBAAA,CAAA,IAAA,CAAA,MAAA,CAAA,OAAA,EAAA;AACA,OAXA,MAWA;AACA,YAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,cAAA,CAAA,IAAA,CAAA,EAAA;AACA,iBAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,EAAA;AACA;;AACA,YAAA,KAAA,IAAA,IAAA,SAAA,EAAA;AACA,cAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,IAAA,CAAA,EAAA;AACA,iBAAA,WAAA;AACA,WAFA,MAEA;AACA,iBAAA,eAAA;AACA;AACA,SANA,MAMA,IAAA,KAAA,IAAA,IAAA,KAAA,EAAA;AACA,cAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,IAAA,CAAA,EAAA;AACA,iBAAA,WAAA;AACA,WAFA,MAEA;AACA,iBAAA,kBAAA;AACA;AACA;AACA;AACA,KA9VA;AA+VA;AACA,IAAA,WAhWA,yBAgWA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CACA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,UADA,EAEA,EAFA,EAGA,KAAA,UAAA,CAAA,OAHA;AAKA,KAvWA;AAwWA;AACA,IAAA,UAzWA,sBAyWA,IAzWA,EAyWA;AAAA;;AACA,UAAA,CAAA,GAAA,EAAA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,GAAA,CAAA;AACA,YAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,YAAA,SAAA,GAAA,KAAA,UAAA,CAAA,SAAA;AACA,YAAA,KAAA,GAAA,CAAA;AACA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,WAAA,IAAA,IAAA,IAAA,IAAA,CAAA,WAAA,IAAA,IAAA,EAAA;AACA,gBAAA,GAAA,GAAA,YAAA,CAAA,IAAA,CAAA;;AACA,gBAAA,GAAA,CAAA,MAAA,EAAA;AACA,kBAAA,IAAA,CAAA,IAAA,IAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,SAAA,GAAA,SAAA;AACA;;AACA,cAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,SAAA;AACA,cAAA,UAAA,CAAA,IAAA,CAAA,IAAA;AACA,cAAA,MAAA;AACA,aAPA,MAOA;AACA,cAAA,GAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,cAFA,GAGA,GAAA,CAAA,GAHA,GAIA,IALA;AAMA;AACA;AACA,SAnBA;AAoBA,QAAA,IAAA,CAAA,UAAA,GAAA,CAAA;;AACA,YAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,GAAA;AACA;;AACA,YAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,MAAA,GAAA,UAAA;AACA,eAAA,OAAA,GAAA,UAAA;AACA,UAAA,YAAA,CAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,aAAA,UAAA,CAAA,MAAA,GAAA,MADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;;AAKA,cAAA,MAAA,CAAA,kBAAA;AACA;AACA,WATA;AAUA;AACA;AACA,KA1ZA;AA2ZA,IAAA,eA3ZA,6BA2ZA;AACA,UAAA,SAAA,GAAA,KAAA,UAAA,CAAA,OAAA;AACA,UAAA,OAAA,GAAA,KAAA,UAAA,CAAA,OAAA;;AACA,UAAA,SAAA,IAAA,IAAA,IAAA,OAAA,IAAA,IAAA,EAAA;AACA,YAAA,GAAA,GAAA;AACA,UAAA,OAAA,EAAA,SADA;AAEA,UAAA,OAAA,EAAA,OAFA;AAGA,UAAA,SAAA,EAAA,KAAA,UAAA,CAAA,SAHA;AAIA,UAAA,WAAA,EAAA,GAJA;AAKA,UAAA,aAAA,EAAA;AALA,SAAA;AAOA,aAAA,KAAA,CAAA,aAAA,CAAA,WAAA,CAAA,GAAA;AACA,OATA,MASA;AACA,aAAA,SAAA,CAAA,WAAA;AACA;AACA,KA1aA;AA2aA;AACA,IAAA,SA5aA,qBA4aA,GA5aA,EA4aA;AACA,WAAA,OAAA,CAAA,KAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,GAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA;AAKA,KAlbA;AAmbA,IAAA,UAnbA,sBAmbA,KAnbA,EAmbA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAAA;AANA,SAAA;AAQA;;AAEA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KA1cA;AA2cA,IAAA,cA3cA,0BA2cA,KA3cA,EA2cA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAAA;AANA,SAAA;AAQA;;AAEA,WAAA,QAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KAleA;AAmeA;AACA,IAAA,kBApeA,gCAoeA;AAAA;;AACA,UAAA,MAAA,GAAA,KAAA,UAAA;AACA,MAAA,MAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA,MAAA,MAAA,CAAA,QAAA,GAAA,KAAA,QAAA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,yCADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,WAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,KAAA,CACA,OADA,CACA,GADA,EAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,EAAA;AACA,cAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,WAFA;AAGA,UAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EALA,CAKA;;AACA,UAAA,cAAA,CAAA,MAAA,CAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,gBAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,YAAA,QAAA,CAAA,KAAA,GAAA,IAAA;AACA,YAAA,QAAA,CAAA,WAAA,GAAA,IAAA;AACA,YAAA,QAAA,CAAA,SAAA,GAAA,IAAA;AACA,YAAA,IAAA,CAAA,IAAA,CAAA,QAAA;AACA,WAPA;AAQA,UAAA,MAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,IAAA,CAAA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,SAAA,CAAA,IAAA,CAAA,MAAA;;AAEA,UAAA,MAAA,CAAA,SAAA,GAAA,CAAA,CAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,CAAA,CAAA;AACA;AACA,OAzBA,EA0BA,KA1BA,CA0BA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OA5BA;AA6BA,KA3gBA;AA4gBA;AACA,IAAA,QA7gBA,oBA6gBA,KA7gBA,EA6gBA;AACA,UAAA,eAAA,GAAA,CAAA;AACA,UAAA,iBAAA,GAAA,CAAA;AACA,UAAA,YAAA,GAAA,CAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,KAAA,CAAA,EAAA;AACA,UAAA,eAAA,IAAA,IAAA,CAAA,eAAA;AACA,UAAA,iBAAA,IAAA,IAAA,CAAA,iBAAA;AACA,UAAA,YAAA,IAAA,IAAA,CAAA,YAAA;AACA;AACA,OANA;AAOA,aAAA;AACA,QAAA,eAAA,EAAA,eADA;AAEA,QAAA,iBAAA,EAAA,iBAFA;AAGA,QAAA,YAAA,EAAA,YAHA;AAIA,QAAA,KAAA,EAAA,IAJA;AAKA,QAAA,WAAA,EAAA,IALA;AAMA,QAAA,SAAA,EAAA;AANA,OAAA;AAQA,KAhiBA;AAiiBA;AACA,IAAA,aAliBA,2BAkiBA;AACA,WAAA,UAAA,GAAA;AACA,QAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IADA;AACA;AACA,QAAA,OAAA,EAAA,KAAA,OAFA;AAGA,QAAA,OAAA,EAAA,MAAA,CAAA,KAAA,OAAA,CAHA;AAGA;AACA,QAAA,WAAA,EAAA,EAJA;AAIA;AACA,QAAA,WAAA,EAAA,EALA;AAKA;AACA,QAAA,WAAA,EAAA,EANA;AAOA,QAAA,WAAA,EAAA,GAPA;AAOA;AACA,QAAA,aAAA,EAAA,CARA;AAQA;AACA,QAAA,uBAAA,EAAA,EATA;AAUA,QAAA,WAAA,EAAA,KAAA;AAVA,OAAA;AAYA,WAAA,kBAAA;AACA,KAhjBA;AAijBA;AACA,IAAA,SAljBA,qBAkjBA,GAljBA,EAkjBA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,UAAA,YAAA,GAAA,GAAA,CAAA,YAAA;AACA,UAAA,eAAA,GAAA,GAAA,CAAA,eAAA;AACA,UAAA,SAAA,GAAA,GAAA,CAAA,SAAA;;AACA,UAAA,YAAA,IAAA,IAAA,IAAA,eAAA,IAAA,IAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA;;AACA,YAAA,eAAA,IAAA,CAAA,EAAA;AACA,UAAA,KAAA,GAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,KAAA,GAAA,YAAA,GAAA,eAAA;AACA;;AAEA,QAAA,GAAA,CAAA,SAAA,GAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA;AACA,KAjkBA;AAkkBA;AACA,IAAA,iBAnkBA,6BAmkBA,IAnkBA,EAmkBA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,QAAA,CADA,CACA;;AACA,UAAA,UAAA,GAAA,IAAA,CAAA,UAAA,CAFA,CAEA;;AACA,UAAA,SAAA,GAAA,IAAA,CAAA,SAAA,CAHA,CAGA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAA,SAAA,EAAA;AACA,cAAA,SAAA,IAAA,IAAA,IAAA,SAAA,GAAA,aAAA,EAAA;AACA;AACA,iBAAA,SAAA,CACA,yBAAA,SAAA,GAAA,YADA;AAGA;AACA;AACA;AACA,KA1lBA;AA2lBA,IAAA,MA3lBA,oBA2lBA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,WAAA;AACA;AACA;;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,oBAFA;AAGA,QAAA,IAAA,EAAA,gBAAA;AACA,cAAA,CAAA,GAAA,IAAA;AACA,cAAA,GAAA,GAAA,EAAA;AACA,cAAA,KAAA,GAAA,MAAA,CAAA,SAAA,CAAA,IAAA;AACA,cAAA,KAAA,GAAA,MAAA,CAAA,SAAA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,IAAA,GAAA,IAAA,CAAA,CAAA,CAAA;;AACA,gBAAA,IAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,kBAAA,IAAA,CAAA,OAAA,EAAA;AACA,gBAAA,CAAA,GAAA,KAAA;AACA;;AACA,cAAA,GAAA,IAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA,aALA,MAKA;AACA,mBAAA,IAAA,CAAA,GAAA,KAAA,CAAA,MAAA,GAAA,CAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,EAAA;AACA,oBAAA,EAAA,GAAA,KAAA,CAAA,CAAA,CAAA;;AACA,oBAAA,EAAA,CAAA,SAAA,KAAA,IAAA,CAAA,SAAA,EAAA;AACA,kBAAA,KAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA;AACA,kBAAA,KAAA,GAAA,KAAA,GAAA,CAAA;AACA;AACA;AACA;AACA;;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,KAAA;;AACA,cAAA,CAAA,EAAA;AACA,gBAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,cAAA,eAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,oBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,kBAAA,MAAA,CAAA,kBAAA;AACA;AACA,eALA;AAMA;AACA,WATA,MASA;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,4BAAA;AACA;AACA,SAtCA;AAuCA,QAAA,QAAA,EAAA,oBAAA,CAAA;AAvCA,OAAA;AAyCA,KA1oBA;AA2oBA;AACA,IAAA,aA5oBA,2BA4oBA;AACA,UAAA,KAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,WAAA,KAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;;AACA,UAAA,KAAA,IAAA,IAAA,IAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,cAAA;AACA,OAFA,MAEA;AACA,aAAA,eAAA,CAAA,KAAA;AACA;AACA,KAppBA;AAqpBA;AACA,IAAA,gBAtpBA,8BAspBA;AAAA;;AACA,UAAA,MAAA,GAAA,KAAA,UAAA;AACA,MAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AACA,MAAA,MAAA,CAAA,QAAA,GAAA,KAAA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,yCADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,UAAA,KAAA,GAAA,EAAA;AACA,UAAA,MAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;AACA,YAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,QAAA,OAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,QAAA,OAAA,CAAA,eAAA,CAAA,KAAA;AACA,OALA;AAMA,KAxqBA;AAyqBA;AACA,IAAA,eA1qBA,2BA0qBA,IA1qBA,EA0qBA;AACA,UAAA,CAAA,GAAA,EAAA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,EAAA;AACA,UAAA,OAAA,GAAA,SAAA,CAAA,OAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA,YAAA,MAAA,GAAA,CAAA;AACA,YAAA,UAAA,GAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,UAAA,CAAA,SAAA;AACA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,GAAA,GAAA,YAAA,CAAA,IAAA,CAAA;;AACA,cAAA,GAAA,CAAA,MAAA,EAAA;AACA,gBAAA,QAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,SAAA,CAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,QAAA,CAAA,IAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,QAAA,CAAA,EAAA;AACA,YAAA,QAAA,GAAA,gBAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,QAAA,CAAA,IAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA,EAAA;AACA,YAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,SAAA;AACA,YAAA,UAAA,CAAA,IAAA,CAAA,IAAA;AACA,YAAA,MAAA;AACA,WAVA,MAUA;AACA,YAAA,GAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,cAFA,GAGA,GAAA,CAAA,GAHA,GAIA,IALA;AAMA;;AAEA,cAAA,IAAA,CAAA,gBAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,SAFA,GAGA,IAAA,CAAA,aAHA,GAIA,SAJA,GAKA,IAAA,CAAA,YALA,GAMA,gBAPA;AAQA;;AAEA,cAAA,IAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,IACA,cACA,IAAA,CAAA,WADA,GAEA,WAFA,GAGA,IAAA,CAAA,OAHA,GAIA,WAJA,GAKA,IAAA,CAAA,UALA,GAMA,eAPA;AAQA;;AACA,cAAA,IAAA,CAAA,SAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;AACA,SAnDA;;AAoDA,YAAA,CAAA,KAAA,CAAA,EAAA;AACA,cAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,iBAAA,MAAA,GAAA,UAAA;AACA,iBAAA,OAAA,GAAA,UAAA;AACA,iBAAA,UAAA;AACA;AACA,SANA,MAMA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,0BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,+BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,2BAAA;AACA;;AACA,QAAA,IAAA,CAAA,UAAA,GAAA,CAAA;;AACA,YAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,GAAA;AACA;;AACA,YAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,IAAA,EAAA,IAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA;AAKA;AACA;AACA,KAlwBA;AAmwBA,IAAA,mBAnwBA,+BAmwBA,IAnwBA,EAmwBA;AACA,WAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,KAAA,SAAA,EAAA;AACA;AACA;AACA;AACA;AACA,aAAA,eAAA,GALA,CAMA;AACA,OAPA,MAOA,IAAA,IAAA,KAAA,KAAA,EAAA;AACA;AACA;AACA;AACA;AACA,aAAA,kBAAA,GALA,CAMA;AACA;AACA,KApxBA;AAqxBA;AACA,IAAA,kBAtxBA,gCAsxBA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,MAAA,mBAAA,CAAA,KAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,aAAA;AACA,SAFA,MAEA;AACA,UAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,CAAA,IAAA,EAAA,EAAA,EAAA,OAAA,CAAA,UAAA,CAAA,OAAA,EADA,CAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAZA;AAaA,KAtyBA;AAuyBA,IAAA,eAvyBA,6BAuyBA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,CAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,cAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,SAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA,SAXA;;AAYA,YAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,EAAA,EAAA,EAAA,KAAA,UAAA,CAAA,OAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,0BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,+BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,2BAAA;AACA;AACA;AACA,KAp0BA;AAq0BA,IAAA,qBAr0BA,mCAq0BA;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,WAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAAA,EAAA;AACA,KAv0BA;AAw0BA,IAAA,SAx0BA,uBAw0BA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,gBAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,MAAA,GAAA,IAAA,CAAA,MAAA;;AACA,cAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,KAAA;AACA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA,SANA;;AAOA,YAAA,CAAA,EAAA;AACA,UAAA,UAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,SADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;;AAKA,cAAA,OAAA,CAAA,kBAAA;AACA;AACA,WATA;AAUA,SAXA,MAWA;AACA,eAAA,SAAA,CAAA,mBAAA;AACA;AACA;AACA,KAr2BA;AAs2BA,IAAA,OAt2BA,qBAs2BA;AACA,UAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,IAAA,CAAA,EAAA;AACA;AACA,YAAA,GAAA,GAAA,IAAA;AACA,QAAA,GAAA,CAAA,cAAA,GAAA,KAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,GAAA,CAAA,kBAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OAPA,MAOA;AACA,aAAA,cAAA,GAAA,IAAA;AACA;AACA,KAj3BA;AAk3BA,IAAA,cAl3BA,0BAk3BA,IAl3BA,EAk3BA,GAl3BA,EAk3BA;AACA,UAAA,IAAA,GAAA,EAAA;AAAA,UACA,IAAA,GAAA,EADA;;AAEA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,SAAA,CAAA,aAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,IAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA,aAAA,CAAA,CAAA,EAAA,KAAA;AACA,QAAA,IAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA,aAAA,CAAA,CAAA,EAAA,GAAA;AACA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,GAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,SAAA,EAAA,IAJA;AAKA,QAAA,QAAA,EAAA;AALA,OAAA;AAOA,MAAA,KAAA,CAAA,qBAAA,CAAA,MAAA;AACA;AACA,KAl4BA;AAm4BA,IAAA,SAn4BA,qBAm4BA,IAn4BA,EAm4BA;AAAA;;AACA,WAAA,MAAA,CAAA,GAAA,GAAA,IAAA;;AACA,UAAA,IAAA,KAAA,SAAA,EAAA;AACA,aAAA,cAAA,CAAA,KAAA,SAAA,CAAA,IAAA,EAAA,YAAA;AACA,OAFA,MAEA,IAAA,IAAA,KAAA,KAAA,EAAA;AACA,YAAA,MAAA,GAAA,KAAA,UAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,KAAA,MAAA,CAAA,IAAA;AACA,YAAA,GAAA,GAAA;AACA,UAAA,GAAA,EAAA,yCADA;AAEA,UAAA,MAAA,EAAA,KAFA;AAGA,UAAA,MAAA,EAAA;AAHA,SAAA;AAKA,aAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,QAAA,KAAA,CACA,OADA,CACA,GADA,EAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;;AACA,cAAA,GAAA,CAAA,IAAA,EAAA;AACA,gBAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,YAAA,cAAA,CAAA,OAAA,CAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,kBAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,cAAA,QAAA,CAAA,KAAA,GAAA,IAAA;AACA,cAAA,QAAA,CAAA,SAAA,GAAA,IAAA;AACA,cAAA,KAAA,CAAA,IAAA,CAAA,QAAA;;AACA,cAAA,OAAA,CAAA,cAAA,CAAA,KAAA,EAAA,YAAA;AACA,aAPA;AAQA;AACA,SAfA,EAgBA,KAhBA,CAgBA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,SAlBA;AAmBA;AACA,KAr6BA;AAs6BA,IAAA,QAt6BA,sBAs6BA;AACA,UAAA,KAAA,YAAA,IAAA,CAAA,EAAA;AACA,YAAA,GAAA,GAAA,KAAA,YAAA,CAAA,KAAA,YAAA,EAAA,IAAA;;AACA,YAAA,GAAA,EAAA;AACA,cAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,oBAAA,KAAA,YAAA;AACA,mBAAA,CAAA;AACA,qBAAA,iBAAA;AACA;;AACA,mBAAA,CAAA;AACA,qBAAA,eAAA;AACA;;AACA,mBAAA,CAAA;AACA,qBAAA,uBAAA;AACA;;AACA,mBAAA,CAAA;AACA,qBAAA,wBAAA;AACA;;AACA,mBAAA,CAAA;AACA,qBAAA,2BAAA;AACA;AAfA;AAiBA,WAlBA,MAkBA;AACA,iBAAA,SAAA,CAAA,QAAA;AACA;AACA;AACA;AACA,KAj8BA;AAk8BA,IAAA,iBAl8BA,+BAk8BA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,aAAA;;AACA,UAAA,MAAA,GAAA,iBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;;AACA,UAAA,MAAA,EAAA;AACA;AACA,aAAA,SAAA,CAAA,MAAA;AACA,aAAA,OAAA,CAAA,KAAA,SAAA,EAAA,SAAA,GAAA,WAAA;AACA,OAJA,MAIA;AACA,aAAA,OAAA,CAAA,KAAA,SAAA,EAAA,SAAA,GAAA,QAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA;AACA,KA/8BA;AAg9BA,IAAA,eAh9BA,6BAg9BA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,WAAA,CAFA,CAIA;;AACA,UAAA,MAAA,GAAA,eAAA,CAAA,IAAA,EAAA,GAAA,CAAA;;AACA,UAAA,MAAA,EAAA;AACA;AACA,aAAA,SAAA,CAAA,MAAA;AACA,OAHA,MAGA;AACA,QAAA,IAAA,CAAA,OAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA;AACA,KA79BA;AA89BA,IAAA,uBA99BA,qCA89BA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,mBAAA;AACA,MAAA,IAAA,CAAA,eAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,iBAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,WAAA,SAAA,CAAA,IAAA;AACA,KAr+BA;AAs+BA,IAAA,yBAt+BA,uCAs+BA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,qBAAA;AACA,MAAA,IAAA,CAAA,iBAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KA3+BA;AA4+BA;AACA,IAAA,wBA7+BA,sCA6+BA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,oBAAA,CAFA,CAGA;;AACA,MAAA,IAAA,CAAA,gBAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,MAAA,IAAA,CAAA,WAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,gBAAA,GAAA,IAAA,CAAA,mBAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,WAAA,SAAA,CAAA,IAAA;AACA,WAAA,iBAAA,CAAA,IAAA;AACA,KAv/BA;AAw/BA;AACA,IAAA,2BAz/BA,yCAy/BA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,uBAAA,CAFA,CAGA;;AACA,MAAA,IAAA,CAAA,mBAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,MAAA,IAAA,CAAA,cAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,gBAAA,GAAA,IAAA,CAAA,mBAAA,CANA,CAOA;;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,cAAA,CAAA,IAAA,CAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,WAAA,SAAA,CAAA,IAAA;AACA,WAAA,iBAAA,CAAA,IAAA;AACA,KArgCA;AAsgCA;AACA,IAAA,UAvgCA,wBAugCA;AACA,UAAA,GAAA,GAAA,KAAA,WAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,MAAA,IAAA,CAAA,OAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,GAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KA7gCA;AA8gCA,IAAA,oBA9gCA,kCA8gCA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,gBAAA,CAFA,CAGA;;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,WAAA,SAAA,CAAA,IAAA;AACA,WAAA,iBAAA,CAAA,IAAA;AACA,KAthCA;AAuhCA,IAAA,SAvhCA,uBAuhCA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,UAAA;AACA,MAAA,IAAA,CAAA,MAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KA5hCA;AA6hCA,IAAA,UA7hCA,sBA6hCA,MA7hCA,EA6hCA;AACA,WAAA,OAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA;AACA,UAAA,SAAA,EAAA,QADA;AAEA,UAAA,OAAA,EAAA,QAFA;AAGA,UAAA,eAAA,EAAA,QAHA;AAIA,UAAA,gBAAA,EAAA,QAJA;AAKA,UAAA,mBAAA,EAAA,QALA;AAMA,UAAA,OAAA,EAAA,QANA;AAOA,UAAA,MAAA,EAAA;AAPA,SAAA;AASA;AACA,KA1iCA;AA2iCA;AACA,IAAA,UA5iCA,sBA4iCA,GA5iCA,EA4iCA,KA5iCA,EA4iCA,OA5iCA,EA4iCA,GA5iCA,EA4iCA;AACA,WAAA,aAAA,GAAA,GAAA,CAAA,SAAA;AACA,WAAA,WAAA,GAAA,GAAA,CAAA,OAAA;AACA,WAAA,mBAAA,GACA,GAAA,CAAA,eAAA,IAAA,IAAA,IAAA,GAAA,CAAA,eAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,eAHA;AAIA,WAAA,oBAAA,GACA,GAAA,CAAA,gBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,gBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,gBAHA;AAIA,WAAA,uBAAA,GACA,GAAA,CAAA,mBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,mBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,mBAHA;AAIA,WAAA,WAAA,GACA,GAAA,CAAA,OAAA,IAAA,IAAA,IAAA,GAAA,CAAA,OAAA,KAAA,CAAA,GAAA,IAAA,GAAA,QAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,EADA;AAEA,WAAA,UAAA,GAAA,GAAA,CAAA,MAAA;AAEA,WAAA,SAAA,GAAA,KAAA;AACA,WAAA,YAAA,GAAA,OAAA;AAEA,UAAA,CAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,YAAA,OAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,CAAA,KAAA,CAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA;AACA,OAJA,EAIA,GAJA,CAAA;AAKA,KAxkCA;AAykCA;AACA,IAAA,QA1kCA,oBA0kCA,IA1kCA,EA0kCA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,YAAA;AACA,UAAA,GAAA,GAAA,EAAA;;AACA,UAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAA,KAAA,GAAA,CAAA;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OAHA,MAGA,IAAA,KAAA,GAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,EAAA;AACA;AACA,YAAA,KAAA,IAAA,IAAA,CAAA,QAAA,GAAA,CAAA,IAAA,KAAA,IAAA,IAAA,CAAA,SAAA,GAAA,CAAA,EAAA;AACA,UAAA,KAAA,GAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,KAAA;AACA;;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OARA,MAQA;AACA,QAAA,OAAA,IAAA,CAAA;AACA;;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,MAAA,GAAA,GAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,KAAA,CAAA;;AACA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,GAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,OAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GACA,GAAA,CAAA,eAAA,IAAA,IAAA,IAAA,GAAA,CAAA,eAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,eAHA;AAIA,QAAA,IAAA,CAAA,oBAAA,GACA,GAAA,CAAA,gBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,gBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,gBAHA;AAIA,QAAA,IAAA,CAAA,uBAAA,GACA,GAAA,CAAA,mBAAA,IAAA,IAAA,IAAA,GAAA,CAAA,mBAAA,KAAA,CAAA,GACA,IADA,GAEA,GAAA,CAAA,mBAHA;AAIA,QAAA,IAAA,CAAA,WAAA,GACA,GAAA,CAAA,OAAA,IAAA,IAAA,IAAA,GAAA,CAAA,OAAA,KAAA,CAAA,GAAA,IAAA,GAAA,QAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,EADA;AAEA,QAAA,IAAA,CAAA,UAAA,GAAA,GAAA,CAAA,MAAA;AACA;;AAEA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA,KAtnCA;AAunCA;AACA,IAAA,YAxnCA,wBAwnCA,MAxnCA,EAwnCA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,cAAA,MAAA;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,WAAA;AACA,UAAA,IAAA,GAAA,KAAA,aAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,SAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,iBAAA;AACA,UAAA,IAAA,GAAA,KAAA,mBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,kBAAA;AACA,UAAA,IAAA,GAAA,KAAA,oBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,qBAAA;AACA,UAAA,IAAA,GAAA,KAAA,uBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,SAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,QAAA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA;AACA;AA5BA;;AA8BA,aAAA;AAAA,QAAA,GAAA,EAAA,GAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA;AACA,KA1pCA;AA2pCA,IAAA,IA3pCA,kBA2pCA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,YAAA;;AACA,UAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAA,KAAA,GAAA,CAAA;AACA,QAAA,OAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,KAAA,EAAA,SAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OATA,MASA;AACA,QAAA,IAAA,CAAA,QAAA;AACA,QAAA,IAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA,KA7qCA;AA8qCA,IAAA,QA9qCA,oBA8qCA,KA9qCA,EA8qCA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,EAAA;;AACA,UAAA,KAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,IAAA,KAAA;AACA;;AACA,aAAA,KAAA;AACA;AAprCA;AA3PA,CAAA", "sourcesContent": ["<!--铁塔挂账电费台账-->\r\n\r\n<template>\r\n  <div class=\"page-class page-card\">\r\n    <div class=\"query-box\">\r\n      <Row :class=\"filterColl ? 'header-bar-show' : 'header-bar-hide'\">\r\n        <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.accountno\"\r\n                  :style=\"formItemWidth\"\r\n                  @on-change=\"accountnoChange\"\r\n                >\r\n                  <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"项目名称:\" prop=\"projectName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.projectName\"\r\n                  placeholder=\"请输入项目名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"'sc' == version\">\r\n              <FormItem\r\n                label=\"电表/协议编码:\"\r\n                prop=\"ammeterName\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.ammeterName\"\r\n                  placeholder=\"请输入电表户号/协议编码\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"供电局电表编号:\"\r\n                prop=\"supplybureauammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.supplybureauammetercode\"\r\n                  placeholder=\"请输入供电局电表编号\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"局站名称:\" prop=\"stationName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.stationName\"\r\n                  placeholder=\"请输入局站名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.company\"\r\n                  @on-change=\"selectChange(accountObj.company)\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                  <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"所属部门：\"\r\n                prop=\"countryName\"\r\n                v-if=\"isAdmin == true\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Input\r\n                  :clearable=\"true\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"accountObj.countryName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n              <FormItem\r\n                label=\"所属部门：\"\r\n                prop=\"country\"\r\n                v-if=\"isAdmin == false\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option value=\"-1\">全部</Option>\r\n                  <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\"></Col>\r\n          </Row>\r\n          <div align=\"right\">\r\n            <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n            <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n            <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n              <Button type=\"default\" style=\"margin-left: 5px\"\r\n                >导出\r\n                <Icon type=\"ios-arrow-down\"></Icon>\r\n              </Button>\r\n              <DropdownMenu slot=\"list\">\r\n                <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n          </div>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon\r\n          :type=\"filterColl ? 'md-arrow-dropup' : 'md-arrow-dropdown'\"\r\n          size=\"20\"\r\n          @click=\"filterColl = !filterColl\"\r\n          :color=\"filterColl ? '#000' : '#1ab394'\"\r\n        ></icon>\r\n      </div>\r\n    </div>\r\n    <div class=\"cl-table\">\r\n      <Row class=\"button-bar\">\r\n        <div class=\"table-button\">\r\n          <Button type=\"primary\" @click=\"addElectricType\">新增</Button>\r\n          <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n          <Button type=\"error\" @click=\"remove\">删除</Button>\r\n          <Button type=\"primary\" @click=\"openCompletedPreModal\">复制归集单台账</Button>\r\n          <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n            <Button type=\"info\" style=\"margin-left: 5px\"\r\n              >加入归集单\r\n              <Icon type=\"ios-arrow-down\"></Icon>\r\n            </Button>\r\n            <DropdownMenu slot=\"list\">\r\n              <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n              <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n          <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n        </div>\r\n      </Row>\r\n      <Table\r\n        ref=\"accountEsTable\"\r\n        border\r\n        :columns=\"tbAccount.columns\"\r\n        :data=\"tbAccount.data\"\r\n        class=\"mytable\"\r\n        :loading=\"tbAccount.loading\"\r\n        :height=\"tableHeight\"\r\n      >\r\n        <!--项目名称-->\r\n        <template slot-scope=\"{ row }\" slot=\"projectName\">\r\n          <div v-if=\"row.status === 5\">\r\n            <span>{{ row.projectName }}</span\r\n            ><span style=\"color: red\">[退回]</span>\r\n          </div>\r\n          <span v-else>{{ row.projectName }}</span>\r\n        </template>\r\n        <!--起始时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"startdate\" v-if=\"row.total == null\">\r\n          <Input\r\n            :ref=\"'startdate' + index + 1\"\r\n            type=\"text\"\r\n            @on-blur=\"validate\"\r\n            v-model=\"editStartDate\"\r\n            v-if=\"editIndex === index && columnsIndex === 1\"\r\n          />\r\n          <span\r\n            :class=\"myStyle[index].startdate\"\r\n            @click=\"selectCall(row, index, 1, 'startdate')\"\r\n            v-else\r\n            >{{ row.startdate }}</span\r\n          >\r\n        </template>\r\n        <!--截止时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"enddate\" v-if=\"row.total == null\">\r\n          <Input\r\n            :ref=\"'enddate' + index + 2\"\r\n            type=\"text\"\r\n            v-model=\"editEndDate\"\r\n            @on-blur=\"validate\"\r\n            v-if=\"editIndex === index && columnsIndex === 2\"\r\n          />\r\n          <span\r\n            :class=\"myStyle[index].enddate\"\r\n            @click=\"selectCall(row, index, 2, 'enddate')\"\r\n            v-else\r\n            >{{ row.enddate }}</span\r\n          >\r\n        </template>\r\n        <!--用电量-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"curusedreadings\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'curusedreadings' + index + 3\"\r\n              type=\"text\"\r\n              v-model=\"editcurusedreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 3\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].curusedreadings\"\r\n              @click=\"selectCall(row, index, 3, 'curusedreadings')\"\r\n              v-else\r\n              >{{ row.curusedreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.curusedreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--输入的普票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputticketmoney' + index + 4\"\r\n              type=\"text\"\r\n              v-model=\"editinputticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 4\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputticketmoney\"\r\n              @click=\"selectCall(row, index, 4, 'inputticketmoney')\"\r\n              v-else\r\n              >{{ row.inputticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--输入的专票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputtaxticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputtaxticketmoney' + index + 5\"\r\n              type=\"text\"\r\n              v-model=\"editinputtaxticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 5\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputtaxticketmoney\"\r\n              @click=\"selectCall(row, index, 5, 'inputtaxticketmoney')\"\r\n              v-else\r\n              >{{ row.inputtaxticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputtaxticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--专票税率-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"taxrate\">\r\n          <div v-if=\"row.total == null\">\r\n            <Select\r\n              :ref=\"'taxrate' + index + 6\"\r\n              type=\"text\"\r\n              v-model=\"edittaxrate\"\r\n              @on-change=\"settaxrate\"\r\n              v-if=\"editIndex === index && columnsIndex === 6\"\r\n              transfer=\"true\"\r\n            >\r\n              <Option selected value=\"13\">13</Option>\r\n              <Option value=\"1\">1</Option>\r\n              <Option value=\"3\">3</Option>\r\n              <Option value=\"6\">6</Option>\r\n              <Option value=\"16\">16</Option>\r\n              <Option value=\"17\">17</Option>\r\n            </Select>\r\n            <span\r\n              :class=\"myStyle[index].taxrate\"\r\n              @click=\"selectCall(row, index, 6, 'taxrate')\"\r\n              v-else\r\n              >{{ row.taxrate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.taxrate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--备注-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              v-model=\"editremark\"\r\n              :ref=\"'remark' + index + 7\"\r\n              type=\"text\"\r\n              @on-blur=\"setremark\"\r\n              v-if=\"editIndex === index && columnsIndex === 7\"\r\n            />\r\n            <Tooltip placement=\"bottom\" max-width=\"200\" v-else>\r\n              <span\r\n                :class=\"myStyle[index].remark\"\r\n                style=\"width: 60px\"\r\n                @click=\"selectCall(row, index, 7, 'remark')\"\r\n                >{{ ellipsis(row.remark) }}</span\r\n              >\r\n              <div slot=\"content\">\r\n                {{ row.remark }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n          <div v-else>\r\n            <Tooltip placement=\"bottom\" max-width=\"200\">\r\n              <span>{{ ellipsis(row.remark) }}</span>\r\n              <div slot=\"content\">\r\n                {{ row.remark }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n        </template>\r\n      </Table>\r\n      <div class=\"table-page\">\r\n        <Page\r\n          size=\"small\"\r\n          :total=\"pageTotal\"\r\n          :current=\"pageNum\"\r\n          :page-size=\"pageSize\"\r\n          show-elevator\r\n          show-sizer\r\n          show-total\r\n          placement=\"top\"\r\n          @on-change=\"handlePage\"\r\n          @on-page-size-change=\"handlePageSize\"\r\n        ></Page>\r\n      </div>\r\n      <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n    </div>\r\n    <div>\r\n      <select-ammeter\r\n        ref=\"selectAmmeter\"\r\n        v-on:listenToSelectAmmeter=\"setAmmeterData\"\r\n      ></select-ammeter>\r\n      <add-bill-per\r\n        ref=\"addBillPer\"\r\n        v-on:refreshList=\"refresh\"\r\n        @isButtonload=\"isButtonload\"\r\n        @buttonload2=\"buttonload2\"\r\n      ></add-bill-per>\r\n      <completed-pre-modal\r\n        ref=\"completedPre\"\r\n        v-on:refreshList=\"refresh\"\r\n      ></completed-pre-modal>\r\n      <country-modal\r\n        ref=\"countryModal\"\r\n        v-on:getDataFromModal=\"getDataFromModal\"\r\n      ></country-modal>\r\n    </div>\r\n    <!--    稽核modal-->\r\n    <Modal\r\n      v-model=\"showCheckModel\"\r\n      width=\"80%\"\r\n      title=\"稽核结果及反馈\"\r\n      :mask-closable=\"false\"\r\n    >\r\n      <check-result-and-response ref=\"chekResultAndResponse\"></check-result-and-response>\r\n    </Modal>\r\n    <Modal v-model=\"showJhModel\" width=\"80%\" title=\"稽核结果\" :mask-closable=\"false\">\r\n      <check-result ref=\"checkResult\"></check-result>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button type=\"primary\" @click=\"nextCheck\">已查阅</Button>\r\n        <Button type=\"text\" @click=\"checkCancel\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <!-- @on-cancel=\"alarmClose\" -->\r\n    <Modal\r\n      v-model=\"showAlarmModel\"\r\n      width=\"80%\"\r\n      title=\"台账预警稽核\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n      class=\"yjjh\"\r\n    >\r\n      <alarm-check\r\n        ref=\"showAlarmModel\"\r\n        @submitChange=\"submitChange\"\r\n        @save=\"save\"\r\n        @close=\"alarmClose\"\r\n      ></alarm-check>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button size=\"large\" @click=\"alarmClose\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  addPredPowerAccount,\r\n  addAccountEs,\r\n  removeAccountEs,\r\n  getUser,\r\n  getDepartments,\r\n  accountEsTotal,\r\n  selectIdsByEsParams,\r\n} from \"@/api/account\";\r\nimport { getResCenter, getcompany } from \"@/api/alertcontrol/alertcontrol\";\r\nimport { againJoin } from \"@/api/accountBillPer\";\r\nimport {\r\n  getClassification,\r\n  getUserdata,\r\n  getUserByUserRole,\r\n  getCountrysdata,\r\n  getCountryByUserId,\r\n} from \"@/api/basedata/ammeter.js\";\r\nimport { editOwn } from \"@/api/accountSC/accountSC\";\r\nimport { getAuditResult, getAuditResultNew, getAuditResultNew_QXM } from \"@/api/account\";\r\nimport { validContractList } from \"@/api/contract\";\r\nimport {\r\n  getDates2,\r\n  testNumber,\r\n  cutDate_yyyymmdd,\r\n  getFirstDateByAccountno_yyyymmdd,\r\n  getLastDateByAccountno_yyyymmdd,\r\n  stringToDate,\r\n  getCurrentDate,\r\n} from \"@/view/account/powerAccountHelper\";\r\nimport {\r\n  _verify_StartDate,\r\n  _verify_EndDate,\r\n  verification,\r\n  unitpirceMin,\r\n  unitpirceMax,\r\n  unitpirceMax1,\r\n} from \"@/view/account/PowerAccountEs\";\r\nimport {\r\n  judge_negate,\r\n  judge_recovery,\r\n  countTaxamount,\r\n} from \"@/view/account/PowerAccountController\";\r\nimport { widthstyle } from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport excel from \"@/libs/excel\";\r\nimport axios from \"@/libs/api.request\";\r\nimport CompletedPreModal from \"@/view/account/completedPreModal\";\r\nimport AddBillPer from \"@/view/account/addBillPreModal\";\r\nimport SelectAmmeter from \"@/view/account/selectAmmeter\";\r\nimport indexData from \"@/config/index\";\r\nimport CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\nimport alarmCheck from \"@/view/account/check/alarmCheck\";\r\nimport checkResult from \"@/view/account/check/checkResult\";\r\nimport checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\nimport { mapState } from \"vuex\";\r\n\r\nimport permissionMixin from \"@/mixins/permission\";\r\nimport pageFun from \"@/mixins/pageFun\";\r\n\r\nlet dates = getDates2();\r\n\r\nexport default {\r\n  name: \"addPredPowerAccount\",\r\n  mixins: [permissionMixin, pageFun],\r\n  components: {\r\n    alarmCheck,\r\n    checkResult,\r\n    checkResultAndResponse,\r\n    CompletedPreModal,\r\n    SelectAmmeter,\r\n    AddBillPer,\r\n    CountryModal,\r\n  },\r\n  data() {\r\n    let renderStatus = (h, { row, index }) => {\r\n      var status = \"\";\r\n      let data = this.tbAccount.data[index];\r\n      for (let item of this.accountStatus) {\r\n        if (item.typeCode == row.status) {\r\n          data.statusName = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", data.statusName);\r\n    };\r\n\r\n    let renderCategory = (h, params) => {\r\n      var categoryname = \"\";\r\n      for (let item of this.categorys) {\r\n        if (item.typeCode == params.row.category) {\r\n          categoryname = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", categoryname);\r\n    };\r\n\r\n    return {\r\n      isT: true,\r\n      number2: 0,\r\n      name: \"\",\r\n      dataL: [],\r\n      isQuery: true,\r\n      number: 0,\r\n      ctgKeyList: [],\r\n      submit: [],\r\n      submit2: [],\r\n      ammeterids: [],\r\n      showCheckModel: false,\r\n      showAlarmModel: false,\r\n      showJhModel: false,\r\n      formItemWidth: widthstyle,\r\n      version: \"\",\r\n      dateList: dates,\r\n      filterColl: true, //搜索面板展开\r\n      editIndex: -1, //当前编辑行\r\n      columnsIndex: -1, //当前编辑列\r\n      editStartDate: \"\",\r\n      myStyle: [], //样式\r\n      editEndDate: \"\",\r\n      editcurusedreadings: \"\",\r\n      edittransformerullage: \"\",\r\n      edittaxrate: \"\",\r\n      editinputticketmoney: \"\",\r\n      editinputtaxticketmoney: \"\",\r\n      spinShow: false, //遮罩\r\n      categorys: [], //描述类型\r\n      editaccountmoney: \"\",\r\n      editremark: \"\",\r\n      accountStatus: [],\r\n      companies: [],\r\n      departments: [],\r\n      isAdmin: false,\r\n      company: null, //用户默认公司\r\n      country: null, //用户默认所属部门\r\n      countryName: null, //用户默认所属部门\r\n      export: {\r\n        run: false, //是否正在执行导出\r\n        data: \"\", //导出数据\r\n        totalPage: 0, //一共多少页\r\n        currentPage: 0, //当前多少页\r\n        percent: 0,\r\n        size: ********,\r\n      },\r\n      accountObj: {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        company: \"\", //分公司\r\n        projectName: \"\", //项目名称\r\n        country: \"\", //所属部门\r\n        ammeterName: \"\", //电表户号/协议编码\r\n        stationName: \"\",\r\n        accountType: \"2\", //台账类型\r\n        accountestype: 2, //台账类型\r\n        supplybureauammetercode: \"\",\r\n      },\r\n      tbAccount: {\r\n        loading: false,\r\n        columns: [],\r\n        headColumn: [\r\n          { type: \"selection\", width: 60, align: \"center\" },\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n        ],\r\n        headColumn2: [\r\n          { type: \"selection\", width: 60, align: \"center\" },\r\n          {\r\n            title: \"稽核结果及反馈\",\r\n            width: 150,\r\n            key: \"action\",\r\n            align: \"center\",\r\n            render: (h, params) => {\r\n              var that = this;\r\n              return h(\r\n                \"Button\",\r\n                {\r\n                  props: {\r\n                    type: \"primary\",\r\n                    size: \"small\",\r\n                  },\r\n                  style: {\r\n                    \"font-size\": \"10px\",\r\n                  },\r\n                  on: {\r\n                    click() {\r\n                      that.$refs.chekResultAndResponse.pcid = params.row.pcid;\r\n                      that.showCheckModel = true;\r\n                    },\r\n                  },\r\n                },\r\n                \"稽核结果及反馈\"\r\n              );\r\n            },\r\n          },\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n        ],\r\n        tailColumn: [\r\n          { title: \"所属分公司\", key: \"companyName\", align: \"center\" },\r\n          { title: \"所属部门\", key: \"countryName\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            key: \"startdate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            key: \"enddate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"用电量(度)\",\r\n            slot: \"curusedreadings\",\r\n            key: \"curusedreadings\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"电价(元)\", key: \"unitpirce\", align: \"center\" },\r\n          { title: \"普票含税金额(元)\", slot: \"inputticketmoney\", align: \"center\" },\r\n          { title: \"专票含税金额(元)\", slot: \"inputtaxticketmoney\", align: \"center\" },\r\n          { title: \"专票税率（%）\", slot: \"taxrate\", align: \"center\" },\r\n          { title: \"专票税额\", key: \"taxamount\", align: \"center\" },\r\n          { title: \"电费\", key: \"accountmoney\", align: \"center\" },\r\n          { title: \"备注\", slot: \"remark\", key: \"remark\", align: \"center\" },\r\n          { title: \"用电类型\", key: \"electrotypename\", align: \"center\", width: 94 },\r\n          {\r\n            title: \"类型描述\",\r\n            key: \"categoryname\",\r\n            align: \"center\",\r\n            render: renderCategory,\r\n          },\r\n        ],\r\n        lnColumn: [\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n        ],\r\n        scColumn: [\r\n          { title: \"电表户号/协议编码\", key: \"ammeterName\", align: \"center\" },\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n        ],\r\n        data: [],\r\n        total: 0,\r\n        exportColumns: [\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n          { title: \"电表户号/协议编码\", key: \"ammeterName\", align: \"center\" },\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n          { title: \"所属分公司\", key: \"companyName\", align: \"center\" },\r\n          { title: \"所属部门\", key: \"countryName\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            key: \"startdate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            key: \"enddate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"用电量(度)\",\r\n            slot: \"curusedreadings\",\r\n            key: \"curusedreadings\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"电价(元)\", key: \"unitpirce\", align: \"center\" },\r\n          { title: \"电费\", slot: \"accountmoney\", key: \"accountmoney\", align: \"center\" },\r\n          { title: \"备注\", slot: \"remark\", key: \"remark\", align: \"center\" },\r\n          { title: \"用电类型\", key: \"electrotypename\", align: \"center\", width: 94 },\r\n          { title: \"类型描述\", key: \"categoryname\", align: \"center\" },\r\n        ],\r\n      },\r\n      pageTotal: 0,\r\n      pageNum: 1,\r\n      pageSize: 10, //当前页\r\n    };\r\n  },\r\n  mounted() {\r\n    this.handleHeight(); //table高度自定义\r\n    this.version = indexData.version;\r\n    this.tbAccount.columns = this.tbAccount.headColumn2\r\n      .concat(this.tbAccount.scColumn)\r\n      .concat(this.tbAccount.tailColumn);\r\n    this.accountStatus = blist(\"accountStatus\");\r\n    this.categorys = blist(\"ammeterCategory\");\r\n    let that = this;\r\n    getUserByUserRole().then((res) => {\r\n      //根据权限获取分公司\r\n      that.companies = res.data.companies;\r\n      if (\r\n        res.data.isCityAdmin == true ||\r\n        res.data.isProAdmin == true ||\r\n        res.data.isSubAdmin == true\r\n      ) {\r\n        that.isAdmin = true;\r\n      }\r\n      getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {\r\n        //根据权限获取所属部门\r\n        that.departments = res.data;\r\n        that.getUserData();\r\n      });\r\n    });\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      loginId: (state) => state.user.loginId,\r\n    }),\r\n  },\r\n  methods: {\r\n    buttonload2(v) {\r\n      this.$refs.showAlarmModel.buttonload2 = v;\r\n    },\r\n    isButtonload(v) {\r\n      this.$refs.showAlarmModel.buttonload = v;\r\n    },\r\n    getTableList() {\r\n      this.$router.push({ path: \"/business/serviceEnergyConsumptionlist\" });\r\n    },\r\n    isShows(t) {\r\n      this.$refs.showAlarmModel.show = t;\r\n      if (this.$refs.showAlarmModel.show == false) {\r\n        this.number2++;\r\n        this.isT = t;\r\n      } else if (this.$refs.showAlarmModel.show == true) {\r\n        this.number2 = 0;\r\n        this.isT = !t;\r\n      }\r\n      if (this.isT && this.number2 < 10) {\r\n        this.isShows(t);\r\n      }\r\n    },\r\n    nextCheck() {\r\n      this.showAlarmModel = true;\r\n      this.isShows(true);\r\n      this.$refs.showAlarmModel.tableData = [];\r\n      this.$refs.showAlarmModel.resultList = [];\r\n      this.$refs.showAlarmModel.tableData1 = [];\r\n      this.$refs.showAlarmModel.resultList1 = [];\r\n      this.$refs.showAlarmModel.tableData2 = [];\r\n      this.$refs.showAlarmModel.resultList2 = [];\r\n      this.$refs.showAlarmModel.tableData3 = [];\r\n      this.$refs.showAlarmModel.resultList3 = [];\r\n      this.$refs.showAlarmModel.tableData4 = [];\r\n      this.$refs.showAlarmModel.resultList4 = [];\r\n      this.$refs.showAlarmModel.tableData5 = [];\r\n      this.$refs.showAlarmModel.resultList5 = [];\r\n      this.showJhModel = false;\r\n      this.$refs.showAlarmModel.activeButton = 6;\r\n    },\r\n    alarmClose() {\r\n      this.showAlarmModel = false;\r\n      this.$refs.showAlarmModel.show = false;\r\n    },\r\n    checkCancel() {\r\n      this.showJhModel = false;\r\n    },\r\n    alarmCheck() {},\r\n    selectChange() {\r\n      let that = this;\r\n      if (that.accountObj.company != undefined) {\r\n        if (that.accountObj.company == \"-1\") {\r\n          that.accountObj.country = -1;\r\n          that.accountObj.countryName = null;\r\n        } else {\r\n          getCountryByUserId(that.accountObj.company).then((res) => {\r\n            if (res.data.departments.length != 0) {\r\n              that.accountObj.country = res.data.departments[0].id;\r\n              that.accountObj.countryName = res.data.departments[0].name;\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //选择所属部门开始\r\n    chooseResponseCenter() {\r\n      if (this.accountObj.company == null || this.accountObj.company == \"-1\") {\r\n        this.$Message.info(\"请先选择分公司\");\r\n        return;\r\n      }\r\n      this.$refs.countryModal.choose(this.accountObj.company); //所属部门\r\n    },\r\n    getDataFromModal(data) {\r\n      this.accountObj.country = data.id;\r\n      this.accountObj.countryName = data.name;\r\n      //选择所属部门结束\r\n    },\r\n    getUserData() {\r\n      let that = this;\r\n      getUserdata().then((res) => {\r\n        //当前登录用户所在公司和所属部门\r\n        if (res.data.companies.length != 0) {\r\n          let companies = res.data.companies;\r\n          if (res.data.companies[0].id == \"**********\") {\r\n            companies = that.companies;\r\n          }\r\n          that.company = companies[0].id;\r\n          that.accountObj.company = companies[0].id;\r\n        }\r\n        if (res.data.departments.length != 0) {\r\n          let departments = res.data.departments;\r\n          if (res.data.companies[0].id == \"**********\" && that.departments.length != 0) {\r\n            departments = that.departments;\r\n          }\r\n          that.country = departments[0].id;\r\n          that.countryName = departments[0].name;\r\n          that.accountObj.country = Number(departments[0].id);\r\n          that.accountObj.countryName = departments[0].name;\r\n        }\r\n\r\n        that.pageNum = 1;\r\n        that.getAccountMessages();\r\n      });\r\n    },\r\n    searchList() {\r\n      if (this.accountObj.countryName == \"\") {\r\n        this.accountObj.country = \"-1\";\r\n      }\r\n      this.pageNum = 1;\r\n      this.getAccountMessages();\r\n    },\r\n    accountnoChange() {\r\n      this.searchList();\r\n    },\r\n    setAmmeterData: function (data) {\r\n      let arrayData = [];\r\n      let ctgKeyList = [];\r\n      let no = this.accountObj.accountno;\r\n      if (data != null && data.length > 0) {\r\n        data.forEach(function (item) {\r\n          let obj = {};\r\n          obj.pcid = null;\r\n          obj.ammeterName = item.ammetername;\r\n          obj.projectName = item.projectname;\r\n          obj.substation = item.substation;\r\n          obj.categoryname = item.categoryname;\r\n          obj.category = item.category;\r\n          obj.ammeterid = item.ammeterid;\r\n          obj.company = item.company;\r\n          obj.companyName = item.companyName;\r\n          obj.country = item.country;\r\n          obj.countryName = item.countryName;\r\n          obj.startdate = null;\r\n          obj.enddate = null;\r\n          obj.curusedreadings = 0;\r\n          obj.transformerullage = 0;\r\n          obj.unitpirce = 0;\r\n          obj.inputticketmoney = 0;\r\n          obj.inputtaxticketmoney = 0;\r\n          obj.taxrate = \"13\";\r\n          obj.taxamount = 0;\r\n          obj.accountmoney = 0;\r\n          obj.remark = null;\r\n          obj.electrotype = item.electrotype;\r\n          obj.stationcode5gr = item.stationcode5gr;\r\n          obj.stationname5gr = item.stationname5gr;\r\n          obj.electrotypename = item.electrotypename;\r\n          obj.stationName = item.stationName;\r\n          obj.startdate = getFirstDateByAccountno_yyyymmdd(no);\r\n          obj.enddate = getLastDateByAccountno_yyyymmdd(no);\r\n          obj.accountestype = 2;\r\n          obj.supplybureauammetercode = item.supplybureauammetercode;\r\n          obj.directsupplyflag = item.directsupplyflag;\r\n          obj.stationaddresscode = item.stationaddresscode;\r\n          arrayData.push(obj);\r\n          ctgKeyList.push({ ctgKey: item.ctgKey, ammetername: item.ammetername });\r\n        });\r\n        this.ctgKeyList = ctgKeyList;\r\n      }\r\n\r\n      let version = indexData.version;\r\n      let origin = this.tbAccount.data;\r\n      if (origin.length < 1) {\r\n        this.tbAccount.data = arrayData;\r\n      } else {\r\n        let tem = arrayData;\r\n        if (\"sc\" == version) {\r\n          origin.forEach((item) => {\r\n            for (let j = tem.length - 1; j >= 0; j--) {\r\n              let jj = tem[j];\r\n              if (item.ammeterid === jj.ammeterid) {\r\n                tem.splice(j, 1);\r\n              }\r\n            }\r\n          });\r\n        }\r\n        let total = this.pageTotal;\r\n        this.pageTotal = total + tem.length;\r\n        this.tbAccount.data = tem.concat(this.tbAccount.data);\r\n      }\r\n\r\n      this.setMyStyle(this.tbAccount.data.length);\r\n    },\r\n    //点击保存\r\n    async preserve() {\r\n      let dataL = this.$refs.accountEsTable.getSelection();\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      let version = indexData.version;\r\n      for (let i = 0; i < dataL.length; i++) {\r\n        if (dataL[i].editType == 1) {\r\n          if (\"sc\" == version && dataL[i].electrotype && dataL[i].electrotype > 1400) {\r\n            if (\r\n              dataL[i].stationcode5gr == null ||\r\n              dataL[i].stationcode5gr == undefined ||\r\n              dataL[i].stationcode5gr == \"\"\r\n            ) {\r\n              this.errorTips(\r\n                \"电表/协议编号【\" +\r\n                  dataL[i].ammeterName +\r\n                  \"】，项目名称【\" +\r\n                  dataL[i].projectName +\r\n                  \"】关联局站的5GR站址为空，请完善局站信息，或者5GR有效性清单失效，请联系无线管理员。\"\r\n              );\r\n              return;\r\n            }\r\n          }\r\n          //截止日期校验\r\n          let result = _verify_EndDate(dataL[i], dataL[i].enddate);\r\n          if (result) {\r\n            this.errorTips(result);\r\n            return;\r\n          }\r\n          b = true;\r\n          array.push(dataL[i]);\r\n        }\r\n      }\r\n      if (b) {\r\n        this.submitData(array);\r\n      } else {\r\n        this.errorTips(\"没有可保存数据\");\r\n      }\r\n    },\r\n    preserveSc() {\r\n      this.$refs.checkResult.ammeterids = this.ammeterids;\r\n      this.showJhModel = true;\r\n    },\r\n    async getContractInfo(stationCode) {\r\n      this.contractCount = 0;\r\n      await validContractList({ stationCode: stationCode }).then((res) => {\r\n        if (res.data) {\r\n          this.contractCount = res.data.length;\r\n        }\r\n      });\r\n    },\r\n    submitChange1(data) {\r\n      this.submit = data;\r\n    },\r\n    submitChange(indexList) {\r\n      let data = [];\r\n      this.submit2.map((item, index) => {\r\n        indexList.map((item2) => {\r\n          if (index == item2) {\r\n            data.push(item);\r\n          }\r\n        });\r\n      });\r\n      this.submit = data;\r\n    },\r\n    getAuditResultNew(data) {\r\n      let arr = [];\r\n      data.forEach((item) => {\r\n        arr.push(item.pcid);\r\n      });\r\n      let param = {\r\n        pcids: arr,\r\n      };\r\n      getAuditResultNew_QXM(param).then((res2) => {\r\n        this.auditResultList = res2.data;\r\n        this.auditResultList.forEach((item) => {\r\n          this.$refs.showAlarmModel.resultList.push(item.msg);\r\n          this.$refs.showAlarmModel.tableData.push(item.powerAuditEntity);\r\n          if (item.staute == \"失败\") {\r\n            // if(item.powerAuditEntity.mutiJtlteCodes=='是'\r\n            // || item.powerAuditEntity.electricityPrices=='否'\r\n            // || item.powerAuditEntity.addressConsistence=='否'\r\n            // || item.powerAuditEntity.reimbursementCycle=='否' || item.powerAuditEntity.electricityContinuity=='否' ||\r\n            // item.powerAuditEntity.shareAccuracy=='否' ||\r\n            // item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n            // item.powerAuditEntity.paymentConsistence=='否'){\r\n            if (item.powerAuditEntity.mutiJtlteCodes == \"是\") {\r\n              this.$refs.showAlarmModel.tableData4.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList4.push(item.msg);\r\n            }\r\n            if (item.powerAuditEntity.electricityPrices == \"否\") {\r\n              this.$refs.showAlarmModel.tableData5.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList5.push(item.msg);\r\n            }\r\n            if (\r\n              item.powerAuditEntity.addressConsistence == \"否\" ||\r\n              item.powerAuditEntity.reimbursementCycle == \"否\" ||\r\n              item.powerAuditEntity.electricityContinuity == \"否\" ||\r\n              item.powerAuditEntity.shareAccuracy == \"否\" ||\r\n              //   item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n              item.powerAuditEntity.paymentConsistence == \"否\" ||\r\n              item.powerAuditEntity.fluctuateContinuity == \"否\"\r\n            ) {\r\n              this.$refs.showAlarmModel.tableData2.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList2.push(item.msg);\r\n            }\r\n            // }\r\n          } else {\r\n            if (\r\n              // item.powerAuditEntity.electricityRationality == \"是\" &&\r\n              // item.powerAuditEntity.consumeContinuity == \"是\" &&\r\n              // item.powerAuditEntity.periodicAnomaly == \"是\"\r\n\r\n              // item.powerAuditEntity.electricityRationality == \"是\" && //电量合理性(省内大数据)\r\n              // item.powerAuditEntity.exclusiveAccuracy == \"是\" && //局站独享共享设置\r\n              item.powerAuditEntity.periodicAnomaly == \"是\" //台账周期合理性\r\n            ) {\r\n              this.$refs.showAlarmModel.tableData1.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList1.push(item.msg);\r\n            } else {\r\n              this.$refs.showAlarmModel.tableData3.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList3.push(item.msg);\r\n            }\r\n          }\r\n          if (this.auditResultList.length > 0) {\r\n            this.auditResultList[this.auditResultList.length - 1].progress = 1;\r\n          }\r\n          this.$refs.showAlarmModel.processData = Number(item.progress) * 100;\r\n          this.$refs.showAlarmModel.scrollList();\r\n        });\r\n      });\r\n    },\r\n    save(value) {\r\n      var that = this;\r\n      //进度条页面链接websoket后调用，传jh字段不走保存流程，走稽核流程\r\n      if (value == 1) {\r\n        that.submit[0].jh = \"1\";\r\n        that.submit[0].ymmc = \"铁塔挂账电费台账\";\r\n        that.submit.forEach((item1) => {\r\n          this.ctgKeyList.forEach((item2) => {\r\n            if (item1.ammeterName == item2.ammetername) {\r\n              item1.ctgKey = item2.ctgKey;\r\n            }\r\n          });\r\n        });\r\n        this.getAuditResultNew(that.submit.reverse());\r\n      } else {\r\n        if (that.submit[0].hasOwnProperty(\"jh\")) {\r\n          delete that.submit[0].jh;\r\n        }\r\n        if (this.name == \"current\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAccount();\r\n          }\r\n        } else if (this.name == \"all\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAllAccount();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //加入归集单，全部非强控\r\n    selectedFQK() {\r\n      let that = this;\r\n      that.$refs.addBillPer.initAmmeter(\r\n        that.$refs.showAlarmModel.selectIds3,\r\n        15,\r\n        this.accountObj.country\r\n      );\r\n    },\r\n    //提交数据\r\n    submitData(data) {\r\n      let a = [];\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let str = \"\";\r\n        let accountno = this.accountObj.accountno;\r\n        let index = 0;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          if (item.projectName != \"小计\" && item.projectName != \"合计\") {\r\n            let obj = verification(item);\r\n            if (obj.result) {\r\n              if (item.pcid == null) {\r\n                item.accountno = accountno;\r\n              }\r\n              a.push(item.ammeterid);\r\n              submitData.push(item);\r\n              number++;\r\n            } else {\r\n              str +=\r\n                \"电表/协议编号为【\" +\r\n                item.ammeterName +\r\n                \"】的台账验证没有通过：【\" +\r\n                obj.str +\r\n                \"】；\";\r\n            }\r\n          }\r\n        });\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          this.errorTips(str);\r\n        }\r\n        if (submitData.length > 0) {\r\n          this.submit = submitData;\r\n          this.submit2 = submitData;\r\n          addAccountEs(submitData).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：成功保存 \" + submitData.length + \" 条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    addElectricType() {\r\n      let companyId = this.accountObj.company;\r\n      let country = this.accountObj.country;\r\n      if (companyId != null && country != null) {\r\n        let obj = {\r\n          company: companyId,\r\n          country: country,\r\n          accountno: this.accountObj.accountno,\r\n          accountType: \"2\",\r\n          accountestype: 2,\r\n        };\r\n        this.$refs.selectAmmeter.initAmmeter(obj);\r\n      } else {\r\n        this.errorTips(\"请选择分公司和部门\");\r\n      }\r\n    },\r\n    //验证错误弹出提示框\r\n    errorTips(str) {\r\n      this.$Notice.error({\r\n        title: \"提示\",\r\n        desc: str,\r\n        duration: 10,\r\n      });\r\n    },\r\n    handlePage(value) {\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n\r\n      this.pageNum = value;\r\n      this.getAccountMessages();\r\n    },\r\n    handlePageSize(value) {\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n\r\n      this.pageSize = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //向后台请求数据 台账稽核结果报表(保存-已查阅) 电表/协议（铁塔台账录入-铁塔电费挂账台账）\r\n    getAccountMessages() {\r\n      let params = this.accountObj;\r\n      params.pageNum = this.pageNum;\r\n      params.pageSize = this.pageSize;\r\n      let req = {\r\n        url: \"/business/accountEs/selectAccountEsList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      this.tbAccount.loading = true;\r\n      axios\r\n        .request(req)\r\n        .then((res) => {\r\n          this.tbAccount.loading = false;\r\n          if (res.data) {\r\n            let data = res.data.rows;\r\n            data.forEach(function (item) {\r\n              item.editType = 0;\r\n            });\r\n            data.push(this.suntotal(data)); //小计\r\n            accountEsTotal(this.accountObj).then((res) => {\r\n              //合计\r\n              let alltotal = res.data;\r\n              alltotal.total = \"合计\";\r\n              alltotal.projectName = \"合计\";\r\n              alltotal._disabled = true;\r\n              data.push(alltotal);\r\n            });\r\n            this.tbAccount.data = data;\r\n            this.pageTotal = res.data.total || 0;\r\n            this.setMyStyle(this.tbAccount.data.length);\r\n\r\n            this.editIndex = -1;\r\n            this.columnsIndex = -1;\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //小计\r\n    suntotal(array) {\r\n      let curusedreadings = 0;\r\n      let transformerullage = 0;\r\n      let accountmoney = 0;\r\n      array.forEach(function (item) {\r\n        if (item.effective === 1) {\r\n          curusedreadings += item.curusedreadings;\r\n          transformerullage += item.transformerullage;\r\n          accountmoney += item.accountmoney;\r\n        }\r\n      });\r\n      return {\r\n        curusedreadings: curusedreadings,\r\n        transformerullage: transformerullage,\r\n        accountmoney: accountmoney,\r\n        total: \"小计\",\r\n        projectName: \"小计\",\r\n        _disabled: true,\r\n      };\r\n    },\r\n    //重置\r\n    onResetHandle() {\r\n      this.accountObj = {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        company: this.company,\r\n        country: Number(this.country), //所属部门\r\n        projectName: \"\", //项目名称\r\n        ammeterName: \"\", //电表户号/协议编码\r\n        stationName: \"\",\r\n        accountType: \"1\", //台账类型\r\n        accountestype: 3, //台账类型\r\n        supplybureauammetercode: \"\",\r\n        countryName: this.countryName,\r\n      };\r\n      this.getAccountMessages();\r\n    },\r\n    //计算单价\r\n    unitPrice(row) {\r\n      let version = indexData.version;\r\n      let accountmoney = row.accountmoney;\r\n      let curusedreadings = row.curusedreadings;\r\n      let taxamount = row.taxamount;\r\n      if (accountmoney != null && curusedreadings != null) {\r\n        let total = null;\r\n        if (curusedreadings == 0) {\r\n          total = 0;\r\n        } else {\r\n          total = accountmoney / curusedreadings;\r\n        }\r\n\r\n        row.unitpirce = total.toFixed(2);\r\n      }\r\n    },\r\n    //验证单价\r\n    validateUnitPrice(data) {\r\n      let category = data.category; //电表描述类型\r\n      let ammeteruse = data.ammeteruse; //电表用途\r\n      let unitpirce = data.unitpirce; //台账单价\r\n      {\r\n        // if (unitpirce) {\r\n        //   if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n        //     this.errorTips(\r\n        //       \"集团要求单价范围在0.3~2元，此台账单价: \" +\r\n        //         unitpirce +\r\n        //         \" 已超过范围，请确认！\"\r\n        //     );\r\n        //   }\r\n        // }\r\n        if (unitpirce) {\r\n          if (unitpirce != null && unitpirce < unitpirceMax1) {\r\n            // if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n            this.errorTips(\r\n              \"单价范围必须大于0.1元，此台账单价: \" + unitpirce + \"不在范围内，请确认！\"\r\n            );\r\n          }\r\n        }\r\n      }\r\n    },\r\n    remove() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      if (data == null || data.length === 0) {\r\n        this.errorTips(\"请选择要删除的数据\");\r\n        return;\r\n      }\r\n      this.$Modal.confirm({\r\n        title: \"提示\",\r\n        content: \"<p>是否确认删除选中信息？</p>\",\r\n        onOk: () => {\r\n          let b = true;\r\n          let ids = \"\";\r\n          let array = this.tbAccount.data;\r\n          let total = this.pageTotal;\r\n          for (let i = 0; i < data.length; i++) {\r\n            let item = data[i];\r\n            if (item.pcid != null && item.pcid.length > 0) {\r\n              if (item.pabriid) {\r\n                b = false;\r\n              }\r\n              ids += item.pcid + \",\";\r\n            } else {\r\n              for (let j = array.length - 1; j >= 0; j--) {\r\n                let jj = array[j];\r\n                if (jj.ammeterid === item.ammeterid) {\r\n                  array.splice(j, 1);\r\n                  total = total - 1;\r\n                }\r\n              }\r\n            }\r\n          }\r\n          this.pageTotal = total;\r\n          if (b) {\r\n            if (ids.length > 0) {\r\n              removeAccountEs(ids).then((res) => {\r\n                if (res.data.code == 0) {\r\n                  this.$Message.success(\"删除成功\");\r\n                  this.getAccountMessages();\r\n                }\r\n              });\r\n            }\r\n          } else {\r\n            this.errorTips(\"选中信息中有信息还没有跟归集单解除关联，请先解除关联\");\r\n          }\r\n        },\r\n        onCancel: () => {},\r\n      });\r\n    },\r\n    //加入归集单\r\n    addPreserveGJ() {\r\n      let dataL = this.$refs.accountEsTable.getSelection();\r\n      this.dataL = this.$refs.accountEsTable.getSelection();\r\n      if (dataL == null || dataL.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        this.addSubmitDataGJ(dataL);\r\n      }\r\n    },\r\n    //加入归集单\r\n    addPreserveGJAll() {\r\n      let params = this.accountObj;\r\n      params.pageNum = 1;\r\n      params.pageSize = 20000;\r\n      let req = {\r\n        url: \"/business/accountEs/selectAccountEsList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      let array1 = [];\r\n      this.tbAccount.loading = true;\r\n      axios.request(req).then((res) => {\r\n        this.tbAccount.loading = false;\r\n        let dataL = res.data.rows;\r\n        this.dataL = res.data.rows;\r\n        this.addSubmitDataGJ(dataL);\r\n      });\r\n    },\r\n    //提交归集单数据\r\n    addSubmitDataGJ(data) {\r\n      let a = [];\r\n      let str = \"\";\r\n      let str1 = \"\";\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let b = 1;\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let obj = verification(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n        });\r\n        if (b === 1) {\r\n          if (submitData.length > 0) {\r\n            this.submit = submitData;\r\n            this.submit2 = submitData;\r\n            this.preserveSc();\r\n          }\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          that.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n      }\r\n    },\r\n    openAddBillPerModal(name) {\r\n      this.name = name;\r\n      if (name === \"current\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJ();\r\n        // } else {\r\n        this.selectedAccount();\r\n        // }\r\n      } else if (name === \"all\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJAll();\r\n        // } else {\r\n        this.selectedAllAccount();\r\n        // }\r\n      }\r\n    },\r\n    //加入归集单，全部有效台账\r\n    selectedAllAccount() {\r\n      let that = this;\r\n      that.spinShow = true;\r\n      selectIdsByEsParams(this.accountObj).then((res) => {\r\n        that.spinShow = false;\r\n        if (res.data.length == 0) {\r\n          that.errorTips(\"无有效数据可加入归集单\");\r\n        } else {\r\n          that.$refs.addBillPer.initAmmeter(res.data, 15, this.accountObj.country);\r\n          // that.$refs.addBillPer.initAmmeter(\r\n          //   this.$refs.showAlarmModel.selectIds1,\r\n          //   15,\r\n          //   this.accountObj.country\r\n          // );\r\n        }\r\n      });\r\n    },\r\n    selectedAccount() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      let b = 1;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        let ids = [];\r\n        data.forEach(function (item) {\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n          ids.push(item.pcid);\r\n        });\r\n        if (b === 1) {\r\n          this.$refs.addBillPer.initAmmeter(ids, 15, this.accountObj.country);\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n      }\r\n    },\r\n    openCompletedPreModal() {\r\n      this.$refs.completedPre.initAmmeter(this.accountObj.country, 15);\r\n    },\r\n    againJoin() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      let b = true;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要重新加入归集单的台账\");\r\n      } else {\r\n        let ids = \"\";\r\n        data.forEach(function (item) {\r\n          let status = item.status;\r\n          if (status != 5) {\r\n            b = false;\r\n          }\r\n          ids += item.pcid + \",\";\r\n        });\r\n        if (b) {\r\n          againJoin(ids).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：操作成功\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        } else {\r\n          this.errorTips(\"只有已退回的台账才能重新加入归集单\");\r\n        }\r\n      }\r\n    },\r\n    refresh() {\r\n      if (this.$refs.showAlarmModel.fromGuijidan != 1) {\r\n        // window.history.go(0);\r\n        let obj = this;\r\n        obj.showAlarmModel = false;\r\n        setTimeout(function () {\r\n          obj.getAccountMessages();\r\n        }, 200);\r\n      } else {\r\n        this.showAlarmModel = true;\r\n      }\r\n    },\r\n    beforeLoadData(data, str) {\r\n      var cols = [],\r\n        keys = [];\r\n      for (var i = 0; i < this.tbAccount.exportColumns.length; i++) {\r\n        cols.push(this.tbAccount.exportColumns[i].title);\r\n        keys.push(this.tbAccount.exportColumns[i].key);\r\n      }\r\n      const params = {\r\n        title: cols,\r\n        key: keys,\r\n        data: data,\r\n        autoWidth: true,\r\n        filename: str,\r\n      };\r\n      excel.export_array_to_excel(params);\r\n      return;\r\n    },\r\n    exportCsv(name) {\r\n      this.export.run = true;\r\n      if (name === \"current\") {\r\n        this.beforeLoadData(this.tbAccount.data, \"铁塔挂账台账导出数据\");\r\n      } else if (name === \"all\") {\r\n        let params = this.accountObj;\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n        let req = {\r\n          url: \"/business/accountEs/selectAccountEsList\",\r\n          method: \"get\",\r\n          params: params,\r\n        };\r\n        this.tbAccount.loading = true;\r\n        axios\r\n          .request(req)\r\n          .then((res) => {\r\n            this.tbAccount.loading = false;\r\n            if (res.data) {\r\n              let array = res.data.rows;\r\n              accountEsTotal(this.accountObj).then((res) => {\r\n                //合计\r\n                let alltotal = res.data;\r\n                alltotal.total = \"合计\";\r\n                alltotal._disabled = true;\r\n                array.push(alltotal);\r\n                this.beforeLoadData(array, \"铁塔挂账台账导出数据\");\r\n              });\r\n            }\r\n          })\r\n          .catch((err) => {\r\n            console.log(err);\r\n          });\r\n      }\r\n    },\r\n    validate() {\r\n      if (this.columnsIndex != 6) {\r\n        let val = this.enterOperate(this.columnsIndex).data;\r\n        if (val) {\r\n          if (testNumber(val)) {\r\n            switch (this.columnsIndex) {\r\n              case 1:\r\n                this.validateStartdate();\r\n                break;\r\n              case 2:\r\n                this.validateEnddate();\r\n                break;\r\n              case 3:\r\n                this.validatecurusedreadings();\r\n                break;\r\n              case 4:\r\n                this.validateinputticketmoney();\r\n                break;\r\n              case 5:\r\n                this.validateinputtaxticketmoney();\r\n                break;\r\n            }\r\n          } else {\r\n            this.errorTips(\"请输入数字！\");\r\n          }\r\n        }\r\n      }\r\n    },\r\n    validateStartdate() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editStartDate;\r\n      let result = _verify_StartDate(data, val);\r\n      if (result) {\r\n        //失败就弹出提示内容\r\n        this.errorTips(result);\r\n        this.myStyle[this.editIndex].startdate = \"errorStle\";\r\n      } else {\r\n        this.myStyle[this.editIndex].startdate = \"myspan\";\r\n        data.startdate = val;\r\n        data.editType = 1;\r\n      }\r\n    },\r\n    validateEnddate() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editEndDate;\r\n\r\n      // 验证截止日期方法\r\n      let result = _verify_EndDate(data, val);\r\n      if (result) {\r\n        //失败就弹出提示内容，并将数据恢复初始化\r\n        this.errorTips(result);\r\n      } else {\r\n        data.enddate = val;\r\n        data.editType = 1;\r\n      }\r\n    },\r\n    validatecurusedreadings() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editcurusedreadings;\r\n      data.curusedreadings = val;\r\n      data.totalusedreadings = val;\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n    },\r\n    validatetransformerullage() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.edittransformerullage;\r\n      data.transformerullage = val;\r\n      data.editType = 1;\r\n    },\r\n    //普票\r\n    validateinputticketmoney() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editinputticketmoney;\r\n      //val = Math.abs(val);\r\n      data.inputticketmoney = parseFloat(val);\r\n      data.ticketmoney = parseFloat(val);\r\n      data.accountmoney = data.inputticketmoney + data.inputtaxticketmoney;\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      this.validateUnitPrice(data);\r\n    },\r\n    //专票\r\n    validateinputtaxticketmoney() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editinputtaxticketmoney;\r\n      //val = Math.abs(val);\r\n      data.inputtaxticketmoney = parseFloat(val);\r\n      data.taxticketmoney = parseFloat(val);\r\n      data.accountmoney = data.inputticketmoney + data.inputtaxticketmoney;\r\n      //data.taxamount = Math.abs(countTaxamount(data));\r\n      data.taxamount = countTaxamount(data);\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      this.validateUnitPrice(data);\r\n    },\r\n    //专票计算税额\r\n    settaxrate() {\r\n      let val = this.edittaxrate;\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      data.taxrate = val;\r\n      data.taxamount = Math.abs(countTaxamount(data));\r\n      data.editType = 1;\r\n    },\r\n    validateaccountmoney() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editaccountmoney;\r\n      //data.accountmoney = Math.abs(val);\r\n      data.accountmoney = val;\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      this.validateUnitPrice(data);\r\n    },\r\n    setremark() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editremark;\r\n      data.remark = val;\r\n      data.editType = 1;\r\n    },\r\n    setMyStyle(length) {\r\n      this.myStyle = [];\r\n      for (var i = 0; i < length; i++) {\r\n        this.myStyle.push({\r\n          startdate: \"myspan\",\r\n          enddate: \"myspan\",\r\n          curusedreadings: \"myspan\",\r\n          inputticketmoney: \"myspan\",\r\n          inputtaxticketmoney: \"myspan\",\r\n          taxrate: \"myspan\",\r\n          remark: \"myspan\",\r\n        });\r\n      }\r\n    },\r\n    //span点击事件将span换成输入框并且获取焦点\r\n    selectCall(row, index, columns, str) {\r\n      this.editStartDate = row.startdate;\r\n      this.editEndDate = row.enddate;\r\n      this.editcurusedreadings =\r\n        row.curusedreadings == null || row.curusedreadings === 0\r\n          ? null\r\n          : row.curusedreadings;\r\n      this.editinputticketmoney =\r\n        row.inputticketmoney == null || row.inputticketmoney === 0\r\n          ? null\r\n          : row.inputticketmoney;\r\n      this.editinputtaxticketmoney =\r\n        row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n          ? null\r\n          : row.inputtaxticketmoney;\r\n      this.edittaxrate =\r\n        row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n      this.editremark = row.remark;\r\n\r\n      this.editIndex = index;\r\n      this.columnsIndex = columns;\r\n\r\n      let a = this;\r\n      setTimeout(function () {\r\n        if (columns != 7) {\r\n          a.$refs[str + index + columns].focus();\r\n        }\r\n      }, 200);\r\n    },\r\n    //跳转到下一格\r\n    nextCell(data) {\r\n      let index = data.editIndex;\r\n      let columns = data.columnsIndex;\r\n      let row = \"\";\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n      } else if (index > -1 && columns === 7) {\r\n        //当跳转的最后一行最后一格的时候\r\n        if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n          index = 0;\r\n        } else {\r\n          index++;\r\n        }\r\n        columns = 1;\r\n      } else {\r\n        columns += 1;\r\n      }\r\n      data.editIndex = index;\r\n      data.columnsIndex = columns;\r\n      row = data.tbAccount.data[index];\r\n      if (row) {\r\n        data.editStartDate = row.startdate;\r\n        data.editEndDate = row.enddate;\r\n        data.editcurusedreadings =\r\n          row.curusedreadings == null || row.curusedreadings === 0\r\n            ? null\r\n            : row.curusedreadings;\r\n        data.editinputticketmoney =\r\n          row.inputticketmoney == null || row.inputticketmoney === 0\r\n            ? null\r\n            : row.inputticketmoney;\r\n        data.editinputtaxticketmoney =\r\n          row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n            ? null\r\n            : row.inputtaxticketmoney;\r\n        data.edittaxrate =\r\n          row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n        data.editremark = row.remark;\r\n      }\r\n\r\n      setTimeout(function () {\r\n        data.$refs[data.enterOperate(columns).str + index + columns].focus();\r\n      }, 200);\r\n    },\r\n    //根据列号返回对应的列名\r\n    enterOperate(number) {\r\n      let str = \"\";\r\n      let data = null;\r\n      switch (number) {\r\n        case 1:\r\n          str = \"startdate\";\r\n          data = this.editStartDate;\r\n          break;\r\n        case 2:\r\n          str = \"enddate\";\r\n          data = this.editEndDate;\r\n          break;\r\n        case 3:\r\n          str = \"curusedreadings\";\r\n          data = this.editcurusedreadings;\r\n          break;\r\n        case 4:\r\n          str = \"inputticketmoney\";\r\n          data = this.editinputticketmoney;\r\n          break;\r\n        case 5:\r\n          str = \"inputtaxticketmoney\";\r\n          data = this.editinputtaxticketmoney;\r\n          break;\r\n        case 6:\r\n          str = \"taxrate\";\r\n          data = this.edittaxrate;\r\n          break;\r\n        case 7:\r\n          str = \"remark\";\r\n          data = this.editremark;\r\n          break;\r\n      }\r\n      return { str: str, data: data };\r\n    },\r\n    pred() {\r\n      var lett = this;\r\n      let index = lett.editIndex;\r\n      let columns = lett.columnsIndex;\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n        lett.editIndex = index;\r\n        lett.columnsIndex = columns;\r\n        lett.editStartDate = lett.tbAccount.data[index].startdate;\r\n        setTimeout(function () {\r\n          lett.$refs[lett.enterOperate(columns).str + index + columns].focus();\r\n        }, 200);\r\n      } else {\r\n        lett.validate();\r\n        lett.setremark();\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    ellipsis(value) {\r\n      if (!value) return \"\";\r\n      if (value.length > 3) {\r\n        return value.slice(0, 3) + \"...\";\r\n      }\r\n      return value;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.page-class {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  .cl-table {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n  .button-bar {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n.yjjh > .ivu-modal-wrap > .ivu-modal {\r\n  top: 20px !important;\r\n}\r\n.mytable .ivu-table-cell {\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: normal;\r\n  word-break: break-all;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.accountEs .filter-divider {\r\n  margin: 0px;\r\n  text-align: center;\r\n}\r\n.accountEs .header-bar-show {\r\n  max-height: 300px;\r\n  padding-top: 14px;\r\n  overflow: inherit;\r\n  border-bottom: 1px solid #e8eaec;\r\n}\r\n.accountEs .header-bar-hide {\r\n  max-height: 0;\r\n  padding-top: 0;\r\n  overflow: hidden;\r\n  border-bottom: 0;\r\n}\r\n\r\n.mytable .myspan {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n}\r\n.mytable .errorStle {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n  color: red;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/view/account/homePagePylon"}]}