{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\PowerAccountList.vue?vue&type=style&index=0&id=29138fee&lang=less&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\PowerAccountList.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouYWNjb3VudGJpbGwgLmZpbHRlci1kaXZpZGVyIHsNCiAgbWFyZ2luOiAwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLmFjY291bnRiaWxsIC5oZWFkZXItYmFyLXNob3cgew0KICBtYXgtaGVpZ2h0OiAzMDBweDsNCiAgLypwYWRkaW5nLXRvcDogMTRweDsqLw0KICBvdmVyZmxvdzogaW5oZXJpdDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOGVhZWM7DQp9DQoNCi5hY2NvdW50YmlsbCAuaGVhZGVyLWJhci1oaWRlIHsNCiAgbWF4LWhlaWdodDogMDsNCiAgcGFkZGluZy10b3A6IDA7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGJvcmRlci1ib3R0b206IDA7DQp9DQoNCi5hY2NvdW50YmlsbCAucm93IHsNCiAgaGVpZ2h0OiAzMHB4Ow0KICBtYXJnaW4tYm90dG9tOiAtNTBweDsNCn0NCg0KLmZvcm0tbGluZS1oZWlnaHQgew0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQoubXl0YWJsZSAuaXZ1LXRhYmxlLWNlbGwgew0KICBwYWRkaW5nLWxlZnQ6IDFweDsNCiAgcGFkZGluZy1yaWdodDogMXB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgd2hpdGUtc3BhY2U6IG5vcm1hbDsNCiAgd29yZC1icmVhazogYnJlYWstYWxsOw0KICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KfQ0KDQojaXB0X2RlcCAuaXZ1LWljb246YmVmb3JlIHsNCiAgZm9udC1zaXplOiAzMHB4Ow0KfQ0K"}, {"version": 3, "sources": ["PowerAccountList.vue"], "names": [], "mappings": ";AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "PowerAccountList.vue", "sourceRoot": "src/view/account", "sourcesContent": ["<style lang=\"less\">\r\n.accountbill .filter-divider {\r\n  margin: 0px;\r\n  text-align: center;\r\n}\r\n\r\n.accountbill .header-bar-show {\r\n  max-height: 300px;\r\n  /*padding-top: 14px;*/\r\n  overflow: inherit;\r\n  border-bottom: 1px solid #e8eaec;\r\n}\r\n\r\n.accountbill .header-bar-hide {\r\n  max-height: 0;\r\n  padding-top: 0;\r\n  overflow: hidden;\r\n  border-bottom: 0;\r\n}\r\n\r\n.accountbill .row {\r\n  height: 30px;\r\n  margin-bottom: -50px;\r\n}\r\n\r\n.form-line-height {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.mytable .ivu-table-cell {\r\n  padding-left: 1px;\r\n  padding-right: 1px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: normal;\r\n  word-break: break-all;\r\n  box-sizing: border-box;\r\n}\r\n\r\n#ipt_dep .ivu-icon:before {\r\n  font-size: 30px;\r\n}\r\n</style>\r\n<template>\r\n  <div>\r\n    <Spin size=\"large\" fix v-if=\"exportloading\"></Spin>\r\n    <div class=\"accountbill\">\r\n      <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n        <Form ref=\"formInline\" :model=\"queryParams\" :label-width=\"150\" inline>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"起始期号：\" prop=\"startAccountno\" class=\"form-line-height\">\r\n                <DatePicker v-model=\"startDatePicker\" type=\"month\" @on-change='startChange'\r\n                            placeholder=\"起始局站电费月台帐\" format=\"yyyyMM\" :style=\"formItemWidth\"></DatePicker>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"截止期号：\" prop=\"endAccountno\" class=\"form-line-height\">\r\n                <DatePicker v-model=\"endDatePicker\" type=\"month\" @on-change='endChange'\r\n                            placeholder=\"截止局站电费月台帐\" format=\"yyyyMM\" :style=\"formItemWidth\"></DatePicker>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"局站名称:\" prop=\"stationName\" class=\"form-line-height\">\r\n                <cl-input v-model=\"queryParams.stationName\" placeholder=\"请输入局站名称\"\r\n                          :style=\"formItemWidth\"/>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"项目名称:\" prop=\"projectname\" class=\"form-line-height\">\r\n                <cl-input v-model=\"queryParams.projectname\" placeholder=\"请输入项目名称\"\r\n                          :style=\"formItemWidth\"/>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\" v-if=\"'sc' == version\">\r\n              <FormItem label=\"电表户号/协议编码:\" prop=\"ammetercode\" class=\"form-line-height\">\r\n                <cl-input v-model=\"queryParams.ammetercode\" placeholder=\"请输入电表户号/协议编码\"\r\n                          :style=\"formItemWidth\"/>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"台账类型：\" prop=\"accountType\" class=\"form-line-height\">\r\n                <Select v-model=\"queryParams.accountType\" :style=\"formItemWidth\">\r\n                  <Option value=\"1\">自有</Option>\r\n                  <Option value=\"2\">铁塔</Option>\r\n                  <Option value=\"3\">自有预估</Option>\r\n                  <Option value=\"4\">铁塔预估</Option>\r\n                  <Option value=\"5\">自有挂账</Option>\r\n                  <Option value=\"6\">自有预付</Option>\r\n                  <Option value=\"7\">铁塔挂账</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"用电类型:\" prop=\"classifications\" class=\"form-line-height\">\r\n                <Cascader :style=\"formItemWidth\" :data=\"classificationData\"\r\n                          v-model=\"classifications\"></Cascader>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"电表用途：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select v-model=\"queryParams.ammeteruse\" :style=\"formItemWidth\">\r\n                  <Option v-for=\"item in ammeteruseList\" :value=\"item.typeCode\" :key=\"item.typeCode\">\r\n                    {{ item.typeName }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\"\r\n                        :style=\"formItemWidth\">\r\n                  <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                  <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                <Input :clearable=true icon=\"ios-archive\" v-model=\"queryParams.countryName\"\r\n                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly\r\n                       :style=\"formItemWidth\">\r\n                </Input>\r\n              </FormItem>\r\n              <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n                <Select v-model=\"queryParams.country\" :style=\"formItemWidth\">\r\n                  <Option value=\"-1\">全部</Option>\r\n                  <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"状态：\" prop=\"status\" class=\"form-line-height\">\r\n                <Select v-model=\"queryParams.status\" :style=\"formItemWidth\">\r\n                  <Option value=\"\">请选择</Option>\r\n                  <Option value=\"1\">未归集</Option>\r\n                  <Option value=\"4\">已归集</Option>\r\n                  <Option value=\"2\">报账中</Option>\r\n                  <Option value=\"3\">报账完成</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"对外结算类型：\" prop=\"directsupplyflag\" class=\"form-line-height\">\r\n                <Select v-model=\"queryParams.directsupplyflag\" :style=\"formItemWidth\">\r\n                  <Option v-for=\"item in directsupplyflags\" :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\">{{\r\n                      item.typeName\r\n                    }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\">\r\n\r\n              <FormItem label=\"资源局站/房屋/站址编码:\" prop=\"resstationcode\" class=\"form-line-height\">\r\n                <cl-input v-model=\"queryParams.resstationcode\" placeholder=\"请输入资源局站/房屋/站址编码\"\r\n                          :style=\"formItemWidth\"/>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"供电局电表编号:\" prop=\"supplybureauammetercode\" class=\"form-line-height\">\r\n                <cl-input v-model=\"queryParams.supplybureauammetercode\" placeholder=\"请输入供电局电表编号\"\r\n                          :style=\"formItemWidth\"/>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"起始财辅账期：\" prop=\"startCfzq\" class=\"form-line-height\">\r\n                <DatePicker v-model=\"startCfzqPicker\" type=\"month\" @on-change='startCfzqChange'\r\n                            placeholder=\"起始财辅账期\" format=\"yyyyMM\" :style=\"formItemWidth\"></DatePicker>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"截止财辅账期：\" prop=\"endCfzq\" class=\"form-line-height\">\r\n                <DatePicker v-model=\"endCfzqPicker\" type=\"month\" @on-change='endCfzqChange'\r\n                            placeholder=\"截止财辅账期\" format=\"yyyyMM\" :style=\"formItemWidth\"></DatePicker>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <div align=\"right\">\r\n            <Button style=\"margin-left: 5px;width:69px;\" type=\"success\" icon=\"ios-search\"\r\n                    @click=\"_onSearchHandle()\">搜索\r\n            </Button>\r\n            <Button style=\"margin-left: 5px;width:69px;\" type=\"info\" icon=\"ios-redo\"\r\n                    @click=\"_onResetHandle\">重置\r\n            </Button>\r\n            <Dropdown @on-click=\"exportCsv\">\r\n              <Button type='default' style=\"margin-left: 5px\">导出\r\n                <Icon type='ios-arrow-down'></Icon>\r\n              </Button>\r\n              <DropdownMenu slot='list' v-if=\"version == 'ln'\">\r\n                <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n\r\n              </DropdownMenu>\r\n              <DropdownMenu slot='list' style=\"text-align: center\" v-if=\"version == 'sc'\">\r\n                <Dropdown placement=\"left\">\r\n                  <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                  <DropdownMenu slot=\"list\">\r\n                    <DropdownItem name=\"current_more\">关联电表/协议</DropdownItem>\r\n                    <!-- <DropdownItem name=\"current\">仅台账信息</DropdownItem>-->\r\n                  </DropdownMenu>\r\n                </Dropdown>\r\n                <Dropdown placement=\"left\">\r\n                  <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n                  <DropdownMenu slot=\"list\">\r\n                    <DropdownItem name=\"all_more\">关联电表/协议</DropdownItem>\r\n                    <!-- <DropdownItem name=\"all\">仅台账信息</DropdownItem>-->\r\n                  </DropdownMenu>\r\n                </Dropdown>\r\n                <!-- <Dropdown placement=\"left\">\r\n                  <DropdownItem name=\"allfinance\">导出全部(账期)</DropdownItem>\r\n                </Dropdown>-->\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n          </div>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n              @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n      </div>\r\n    </div>\r\n    <div>\r\n      <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n      <upload-file-modal ref=\"uploadFileModal\"></upload-file-modal>\r\n    </div>\r\n    <div>\r\n      <Page size=\"small\" :total=\"pageTotal\" :current=\"pageNum\" :page-size=\"pageSize\" show-elevator show-sizer\r\n            show-total\r\n            placement=\"top\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n      <Table ref=\"listTable\"\r\n             border :loading=\"listTb.loading\"\r\n             :columns=\"listTb.columns\"\r\n             :data=\"insideData\"\r\n             class=\"mytable\"></Table>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport {blist} from \"@/libs/tools\";\r\nimport axios from '@/libs/api.request';\r\nimport {\r\n  getClassification,\r\n  getCountryByUserId,\r\n  getCountrysdata,\r\n  getUserByUserRole,\r\n  getUserdata\r\n} from '@/api/basedata/ammeter.js'\r\nimport {allAccountTotal} from '@/api/account';\r\nimport {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nimport CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\nimport excel from '@/libs/excel'\r\nimport indexData from '@/config/index'\r\nimport UploadFileModal from \"./uploadFileModal\";\r\n\r\nexport default {\r\n  name: \"ccountList\",\r\n  components: {CountryModal, UploadFileModal},\r\n  data() {\r\n    let maopao = (h, params) => {\r\n      let str = ''\r\n      let index = params.index;\r\n      if (index < this.pageSize / 2) {\r\n        str = 'bottom'\r\n      } else {\r\n        str = 'top'\r\n      }\r\n\r\n      return h('div', [\r\n        h('Tooltip', {\r\n          props: {placement: str}\r\n        }, [\r\n          h('span', {\r\n            style: {\r\n              display: 'inline-block',\r\n              width: params.column._width * 0.9 + 'px',\r\n              overflow: 'hidden',\r\n              textOverflow: 'ellipsis',\r\n              whiteSpace: 'nowrap',\r\n            },\r\n          }, params.row.remark),\r\n          h('span', {\r\n            slot: 'content',\r\n            style: {whiteSpace: 'normal', wordBreak: 'break-all'}\r\n          }, params.row.remark)\r\n        ])\r\n      ])\r\n    }\r\n    let renderDirectsupplyflag = (h, params) => {\r\n      var directsupplyflag = \"\";\r\n      for (let item of this.directsupplyflags) {\r\n        if (item.typeCode == params.row.directsupplyflag) {\r\n          directsupplyflag = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", directsupplyflag);\r\n    };\r\n    let renderStatus = (h, {row, index}) => {\r\n      var status = \"\";\r\n      for (let item of this.accountStatus) {\r\n        if (item.typeCode == row.status) {\r\n          status = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", status);\r\n    };\r\n    let billType = (h, {row, index}) => {\r\n      var type = ''\r\n      if (row.property === 2) {\r\n        type = '铁塔'\r\n      } else {\r\n        type = '自有'\r\n      }\r\n      if (row.accounttype === 2) {\r\n        if (row.accountestype === 1) {\r\n          type += '预估'\r\n        } else if (row.accountestype === 2) {\r\n          type += '挂账'\r\n        } else if (row.accountestype === 3) {\r\n          type += '预付'\r\n        }\r\n\r\n      }\r\n      if (row.property == null || row.accounttype == null) {\r\n        type = ''\r\n      }\r\n      return h(\"div\", type);\r\n    }\r\n\r\n    let renderCategory = (h, params) => {\r\n      var categoryname = \"\";\r\n      for (let item of this.categorys) {\r\n        if (item.typeCode == params.row.category) {\r\n          categoryname = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", categoryname);\r\n    };\r\n\r\n    let renderAmmeteruse = (h, {row, index}) => {\r\n      var ammeteruse = \"\";\r\n      for (let item of this.ammeteruseList) {\r\n        if (item.typeCode == row.ammeteruse) {\r\n          ammeteruse = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", ammeteruse);\r\n    };\r\n    let photo = (h, {row, index}) => {\r\n      let that = this\r\n      let str = ''\r\n      if (row.projectname != '小计' && row.projectname != '合计') {\r\n        str = '附件'\r\n      }\r\n      return h(\"div\", [h(\"u\", {\r\n        on: {\r\n          click() {\r\n            //打开弹出框\r\n            if (row.projectname != '小计' && row.projectname != '合计') {\r\n              that.uploadFile(row)\r\n            }\r\n          }\r\n        }\r\n      }, str)]);\r\n    };\r\n\r\n    let columns_ln = [\r\n      {title: '项目名称', key: 'projectname', align: 'center', width: 150,},\r\n      {title: '供电局电表编号', key: 'supplybureauammetercode', align: 'center', width: 150,},\r\n      {title: '局站', key: 'stationName', align: 'center', width: 150,},\r\n      {title: '资源局站/房屋/站址编码', key: 'resstationcode', align: 'center', width: 150,},\r\n      {title: '分局/支局', key: 'substation', align: 'center', width: 150,},\r\n      {title: '对外结算类型', align: 'center', key: 'directsupplyflag', width: 80, render: renderDirectsupplyflag},\r\n      {title: '期号', key: 'accountno', align: 'center', width: 90,},\r\n      {title: '所属分公司', key: 'company', align: 'center', width: 90,},\r\n      {title: '所属部门', key: 'country', align: 'center', width: 90,},\r\n      {title: '倍率', key: 'magnification', align: 'center', width: 90,},\r\n      {title: '定额', key: 'quotareadings', align: 'center', width: 90,},\r\n      {title: '浮动比（%）', key: 'quotereadingsratio', align: 'center', width: 90,},\r\n      {title: '起始日期', key: 'startdate', align: 'center', width: 100,},\r\n      {title: '截止日期', key: 'enddate', align: 'center', width: 100,},\r\n      {title: '本期峰段起度', key: 'prevhighreadings', align: 'center', width: 90,},\r\n      {title: '本期平段起度', key: 'prevflatreadings', align: 'center', width: 90,},\r\n      {title: '本期谷段起度', key: 'prevlowreadings', align: 'center', width: 90,},\r\n      {title: '本期起度', key: 'prevtotalreadings', align: 'center', width: 90,},\r\n      {title: '本期峰段止度', key: 'curhighreadings', align: 'center', width: 90,},\r\n      {title: '本期平段止度', key: 'curflatreadings', align: 'center', width: 90,},\r\n      {title: '本期谷段止度', key: 'curlowreadings', align: 'center', width: 90,},\r\n      {title: '本期止度', key: 'curtotalreadings', align: 'center', width: 90,},\r\n      {title: '用电量(度)', key: 'curusedreadings', align: 'center', width: 90,},\r\n      {title: '电损(度)', key: 'transformerullage', align: 'center', width: 90,},\r\n      {title: '总电量(度)', key: 'totalusedreadings', align: 'center', width: 90,},\r\n      {title: '电价(元)', key: 'unitpirce', align: 'center', width: 90,},\r\n      {title: '普票含税金额(元)', key: 'inputticketmoney', align: 'center', width: 60,},\r\n      {title: '专票含税金额(元)', key: 'inputtaxticketmoney', align: 'center', width: 60,},\r\n      {title: '实际普票含税金额(元)', key: 'ticketmoney', align: 'center', width: 90,},\r\n      {title: '实际专票含税金额(元)', key: 'taxticketmoney', align: 'center', width: 90,},\r\n      {title: '专票税率（%）', key: 'taxrate', align: 'center', width: 80,},\r\n      {title: '专票税额', key: 'taxamount', align: 'center', width: 60,},\r\n      {title: '其他(元)', key: 'ullagemoney', align: 'center', width: 90,},\r\n      {title: '实缴费用(元)含税', key: 'accountmoney', align: 'center', width: 90,},\r\n      {title: '总金额（不含税）', key: 'totalBHS', align: 'center', width: 90,},\r\n      {title: '分割比例(%)', key: 'percent', width: 90, align: 'center'},\r\n      {title: '备注', key: 'remark', align: 'center', width: 150, render: maopao},\r\n      {title: '类型描述', key: 'categoryname', align: 'center', width: 90,},\r\n      {title: '用电类型', key: 'electrotypename', align: 'center', width: 94,},\r\n      {title: '电表用途', key: 'ammeterusename', align: 'center', width: 90,},\r\n      {title: '台账类型', key: 'type', align: 'center', width: 90,},\r\n      {title: '状态', key: 'status', align: 'center', width: 90,},\r\n      {title: '录入人', key: 'inputname', align: 'center', width: 90,},\r\n      {title: '归集单事项名称', key: 'note', align: 'center', width: 90,},\r\n    ]\r\n    let columns_sc = [\r\n      {title: '项目名称', key: 'projectname', align: 'center', width: 150,},\r\n      {title: '电表户号/协议编码', key: 'ammetercode', align: 'center', width: 150,},\r\n      {title: '局站', key: 'stationName', align: 'center', width: 150,},\r\n      {title: '资源局站/房屋/站址编码', key: 'resstationcode', align: 'center', width: 150,},\r\n      {title: '分局/支局', key: 'substation', align: 'center', width: 150,},\r\n      {title: '对外结算类型', align: 'center', key: 'directsupplyflag', width: 80, render: renderDirectsupplyflag},\r\n      {title: '期号', key: 'accountno', align: 'center', width: 90,},\r\n      {title: '所属分公司', key: 'company', align: 'center', width: 90,},\r\n      {title: '所属部门', key: 'country', align: 'center', width: 90,},\r\n      {title: '倍率', key: 'magnification', align: 'center', width: 90,},\r\n      {title: '定额', key: 'quotareadings', align: 'center', width: 90,},\r\n      {title: '浮动比（%）', key: 'quotereadingsratio', align: 'center', width: 90,},\r\n      {title: '起始日期', key: 'startdate', align: 'center', width: 100,},\r\n      {title: '截止日期', key: 'enddate', align: 'center', width: 100,},\r\n      {title: '本期峰段起度', key: 'prevhighreadings', align: 'center', width: 90,},\r\n      {title: '本期峰段止度', key: 'curhighreadings', align: 'center', width: 90,},\r\n      {title: '峰段加减电量', key: 'highreadings', align: 'center', width: 90,},\r\n      {title: '本期平段起度', key: 'prevflatreadings', align: 'center', width: 90,},\r\n      {title: '本期平段止度', key: 'curflatreadings', align: 'center', width: 90,},\r\n      {title: '平段加减电量', key: 'flatreadings', align: 'center', width: 90,},\r\n      {title: '本期谷段起度', key: 'prevlowreadings', align: 'center', width: 90,},\r\n      {title: '本期谷段止度', key: 'curlowreadings', align: 'center', width: 90,},\r\n      {title: '谷段加减电量', key: 'lowreadings', align: 'center', width: 90,},\r\n      {title: '本期起度', key: 'prevtotalreadings', align: 'center', width: 90,},\r\n      {title: '本期止度', key: 'curtotalreadings', align: 'center', width: 90,},\r\n      {title: '用电量(度)', key: 'curusedreadings', align: 'center', width: 90,},\r\n      {title: '电损(度)', key: 'transformerullage', align: 'center', width: 90,},\r\n      {title: '总电量(度)', key: 'totalusedreadings', align: 'center', width: 90,},\r\n      {title: '电价(元)', key: 'unitpirce', align: 'center', width: 90,},\r\n      {title: '专票含税金额(元)', key: 'inputtaxticketmoney', align: 'center', width: 60,},\r\n      {title: '普票金额(元)', key: 'inputticketmoney', align: 'center', width: 60,},\r\n      {title: '普票税额(元)', key: 'tickettaxamount', align: 'center', width: 60,},\r\n      {title: '实际普票含税金额(元)', key: 'ticketmoney', align: 'center', width: 90,},\r\n      {title: '实际专票含税金额(元)', key: 'taxticketmoney', align: 'center', width: 90,},\r\n      {title: '税率（%）', key: 'taxrate', align: 'center', width: 80,},\r\n      {title: '税额', key: 'taxamount', align: 'center', width: 60,},\r\n      {title: '其他(元)', key: 'ullagemoney', align: 'center', width: 90,},\r\n      {title: '实缴费用(元)含税', key: 'accountmoney', align: 'center', width: 90,},\r\n      {title: '总金额（不含税）', key: 'totalBHS', align: 'center', width: 90,},\r\n      {title: '分割比例(%)', key: 'percent', width: 90, align: 'center'},\r\n      {title: '备注', key: 'remark', align: 'center', width: 150, render: maopao},\r\n      {title: '类型描述', key: 'categoryname', align: 'center', width: 90,},\r\n      {title: '用电类型', key: 'electrotypename', align: 'center', width: 94,},\r\n      {title: '电表用途', key: 'ammeterusename', align: 'center', width: 90,},\r\n      {title: '台账类型', key: 'type', align: 'center', width: 90,},\r\n      {title: '状态', key: 'status', align: 'center', width: 90,},\r\n      {title: '录入人', key: 'inputname', align: 'center', width: 90,},\r\n      {title: '归集单事项名称', key: 'note', align: 'center', width: 90,},\r\n      {title: '网管C网编号', key: 'nmccode', width: 150, align: 'center'},\r\n      {title: '网管编号L2.1G', key: 'nml2100', width: 90, align: 'center'},\r\n      {title: '网管编号L1.8G', key: 'nml1800', width: 90, align: 'center'},\r\n      {title: '网管编号C+L800M', key: 'nmcl800m', width: 90, align: 'center'},\r\n      {title: '合同对方', key: 'contractothpart', width: 90, align: 'center'},\r\n      {title: '财辅报账单号', key: 'billId', width: 90, align: 'center'},\r\n      {title: '报账人', key: 'fillinname', width: 90, align: 'center'},\r\n      {title: '财辅账期', key: 'budgetsetname', width: 90, align: 'center'},\r\n      {title: '附件', slot: 'file', align: 'center', width: 60, render: photo},\r\n    ]\r\n\r\n    return {\r\n      exportloading: false,\r\n      formItemWidth: widthstyle,\r\n      version: '',\r\n      queryParams: {\r\n        startAccountno: '',//起始局站电费月台帐\r\n        endAccountno: '',//截止局站电费月台帐\r\n        substation: '',//支局\r\n        projectname: '',//项目名称\r\n        ammetercode: '',//电表户号/协议编码\r\n        accountType: '',//台账类型\r\n        company: '',//分公司\r\n        country: '',//所属部门\r\n        status: '',//状态\r\n        ammeteruse: '',\r\n        countryName: '',\r\n        supplybureauammetercode: '',\r\n        resstationcode: '',\r\n        directsupplyflag: '',\r\n        iffinance: 0,\r\n        startYear: '',\r\n        startMonth: '',\r\n        endYear: '',\r\n        endMonth: ''\r\n      },\r\n      startDatePicker: '',\r\n      endDatePicker: '',\r\n      startCfzqPicker: '',\r\n      endCfzqPicker: '',\r\n      companies: [],\r\n      departments: [],\r\n      columns_ln: columns_ln,//辽宁表头\r\n      columns_sc: columns_sc,//四川表头\r\n      isAdmin: false,\r\n      categorys: [],//描述类型\r\n      directsupplyflags: [],\r\n      company: null,//用户默认公司\r\n      country: null,//用户默认所属部门\r\n      countryName: null,//用户默认所属部门\r\n      classificationData: [],//用电类型树\r\n      classifications: [],//选择的用电类型树\r\n      filterColl: true,//搜索面板展开\r\n      exportable: false,\r\n      accountStatus: [],\r\n      ammeteruseList: [],\r\n      billtypes: [],\r\n      export: {\r\n        run: false,//是否正在执行导出\r\n        data: \"\",//导出数据\r\n        totalPage: 0,//一共多少页\r\n        currentPage: 0,//当前多少页\r\n        percent: 0,\r\n        size: ********\r\n      },\r\n      subtotal: '',\r\n      alltotal: '',\r\n      url: 'business/account/selectListByParams',\r\n      listTb: {\r\n        loading: false,\r\n        columns: [],\r\n        data: []\r\n      },\r\n      insideData: [],\r\n      pageTotal: 0,\r\n      pageNum: 1,\r\n      pageSize: 10,//当前页\r\n    }\r\n  },\r\n  methods: {\r\n    selectChange() {\r\n      let that = this;\r\n      if (that.queryParams.company != undefined) {\r\n        if (that.queryParams.company == \"-1\") {\r\n          that.queryParams.country = -1;\r\n          that.queryParams.countryName = null;\r\n        } else {\r\n          getCountryByUserId(that.queryParams.company).then(res => {\r\n            if (res.data.departments.length != 0) {\r\n              that.queryParams.country = res.data.departments[0].id;\r\n              that.queryParams.countryName = res.data.departments[0].name;\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //选择所属部门开始\r\n    chooseResponseCenter() {\r\n      if (this.queryParams.company == null || this.queryParams.company == \"-1\") {\r\n        this.$Message.info(\"请先选择分公司\");\r\n        return;\r\n      }\r\n      this.$refs.countryModal.choose(this.queryParams.company);//所属部门\r\n    },\r\n    getDataFromModal(data) {\r\n      this.queryParams.country = data.id;\r\n      this.queryParams.countryName = data.name;\r\n      //选择所属部门结束\r\n    },\r\n    //翻页\r\n    handlePage(value) {\r\n      this.pageNum = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //改变表格可显示数据数量\r\n    handlePageSize(value) {\r\n      this.pageSize = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //向后台请求数据\r\n    getAccountMessages() {\r\n      if (this.queryParams.countryName == \"\") {\r\n        this.queryParams.country = \"-1\";\r\n      }\r\n      this.setElectroyType();\r\n      let params = this.queryParams;\r\n      params.pageNum = this.pageNum;\r\n      params.pageSize = this.pageSize;\r\n      let req = {\r\n        url: this.url,\r\n        method: \"get\",\r\n        params: params\r\n      };\r\n      let array = [];\r\n      this.listTb.loading = true;\r\n      axios.request(req).then(res => {\r\n        this.listTb.loading = false;\r\n        if (res.data) {\r\n          array = res.data.rows;\r\n          this.setdata(array)\r\n          this.totalBHS(array)\r\n          array.push(this.suntotal(array))//小计\r\n          this.pageTotal = res.data.total || 0\r\n          allAccountTotal(this.queryParams).then(res => {//合计\r\n            let alltotal = res.data\r\n            alltotal.total = '合计'\r\n            alltotal.projectname = '合计'\r\n            array.push(alltotal)\r\n          });\r\n          this.insideData = array\r\n        }\r\n      }).catch(err => {\r\n        console.log(err);\r\n      });\r\n    },\r\n    setdata(array) {\r\n      let accountStatus = this.accountStatus\r\n      let categorys = this.categorys\r\n      let ammeteruseList = this.ammeteruseList\r\n      let billtypes = this.billtypes\r\n      array.forEach(function (row) {\r\n        var status = \"\";\r\n        for (let i of accountStatus) {\r\n          if (i.typeCode == row.status) {\r\n            status = i.typeName;\r\n            break;\r\n          }\r\n        }\r\n        row.status = status\r\n\r\n        var type = ''\r\n        for (let item of billtypes) {\r\n          if (item.typeCode == row.billtype) {\r\n            type = item.typeName;\r\n            break;\r\n          }\r\n        }\r\n\r\n        row.type = type\r\n\r\n        var categoryname = \"\";\r\n        for (let item of categorys) {\r\n          if (item.typeCode == row.category) {\r\n            categoryname = item.typeName;\r\n            break;\r\n          }\r\n        }\r\n        row.categoryname = categoryname\r\n\r\n        var ammeteruse = \"\";\r\n        for (let item of ammeteruseList) {\r\n          if (item.typeCode == row.ammeteruse) {\r\n            ammeteruse = item.typeName;\r\n            break;\r\n          }\r\n        }\r\n\r\n        row.ammeterusename = ammeteruse\r\n      })\r\n    },\r\n    totalBHS(array) {\r\n      array.forEach(function (item) {\r\n        let taxticketmoney = item.taxticketmoney;//专票含税金额\r\n        let ticketmoney = item.ticketmoney;//普票含税金额\r\n        let ullagemoney = item.ullagemoney;//其他费用\r\n        let taxamount = item.taxamount;//税额\r\n        let total = ticketmoney + (taxticketmoney - taxamount) + ullagemoney\r\n        total = total.toFixed(2)\r\n        item.totalBHS = total\r\n      })\r\n    },\r\n    //小计\r\n    suntotal(array) {\r\n      let curusedreadings = 0\r\n      let transformerullage = 0\r\n      let ticketmoney = 0\r\n      let taxticketmoney = 0\r\n      let taxamount = 0\r\n      let ullagemoney = 0\r\n      let accountmoney = 0\r\n      let inputtaxticketmoney = 0\r\n      let inputticketmoney = 0\r\n      let tickettaxamount = 0\r\n      let total = 0\r\n      array.forEach(function (item) {\r\n        if (item.effective === 1) {\r\n          curusedreadings += item.curusedreadings\r\n          transformerullage += item.transformerullage\r\n          ticketmoney += item.ticketmoney\r\n          taxticketmoney += item.taxticketmoney\r\n          taxamount += item.taxamount\r\n          inputtaxticketmoney += item.inputtaxticketmoney\r\n          inputticketmoney += item.inputticketmoney\r\n          ullagemoney += item.ullagemoney\r\n          accountmoney += item.accountmoney\r\n          total += parseFloat(item.totalBHS)\r\n          tickettaxamount += item.tickettaxamount\r\n        }\r\n      })\r\n      return {\r\n        curusedreadings: curusedreadings,\r\n        transformerullage: transformerullage,\r\n        ticketmoney: ticketmoney.toFixed(2),\r\n        taxticketmoney: taxticketmoney.toFixed(2),\r\n        taxamount: taxamount.toFixed(2),\r\n        inputtaxticketmoney: inputtaxticketmoney.toFixed(2),\r\n        inputticketmoney: inputticketmoney.toFixed(2),\r\n        ullagemoney: ullagemoney.toFixed(2),\r\n        accountmoney: accountmoney.toFixed(2),\r\n        totalBHS: total.toFixed(2),\r\n        tickettaxamount: tickettaxamount.toFixed(2),\r\n        total: '小计',\r\n        projectname: '小计',\r\n        _disabled: true\r\n      }\r\n    },\r\n    _onSearchHandle() {\r\n      this.pageNum = 1\r\n      this.getAccountMessages()\r\n    },\r\n    _onResetHandle() {\r\n      this.queryParams = {company: null, country: null, countryName: null};\r\n      this.startDatePicker = '';\r\n      this.endDatePicker = '';\r\n      this.startCfzqPicker = '';\r\n      this.endCfzqPicker = '';\r\n      this.classifications = [];\r\n      this.queryParams.company = this.company;\r\n      this.queryParams.country = Number(this.country);\r\n      this.queryParams.countryName = this.countryName;\r\n    },\r\n    startChange(year) {\r\n      this.queryParams.startAccountno = year\r\n    },\r\n    endChange(year) {\r\n      this.queryParams.endAccountno = year\r\n    },\r\n    startCfzqChange(year) {\r\n      if(year){\r\n        this.queryParams.startYear = year.substring(0,4);\r\n        this.queryParams.startMonth = year.substring(4,6);\r\n      } else{\r\n        this.queryParams.startYear = '';\r\n        this.queryParams.startMonth = '';\r\n      }\r\n    },\r\n    endCfzqChange(year) {\r\n      if(year){\r\n        this.queryParams.endYear = year.substring(0,4);\r\n        this.queryParams.endMonth = year.substring(4,6);\r\n      } else{\r\n        this.queryParams.endYear = '';\r\n        this.queryParams.endMonth = '';\r\n      }\r\n    },\r\n    setElectroyType() {\r\n      let types = this.classifications;\r\n      this.queryParams.electrotype = types[types.length - 1]\r\n    },\r\n    beforeLoadData(data) {\r\n      var cols = [], keys = []\r\n      for (var i = 0; i < this.listTb.columns.length; i++) {\r\n        cols.push(this.listTb.columns[i].title)\r\n        keys.push(this.listTb.columns[i].key)\r\n      }\r\n      const params = {\r\n        title: cols,\r\n        key: keys,\r\n        data: data,\r\n        autoWidth: true,\r\n        filename: '台账导出数据'\r\n      }\r\n      excel.export_array_to_excel(params)\r\n      return\r\n    },\r\n    exportCsv(name) {\r\n      /*                this.errorTips(\"报账高峰13-25号暂停使用\");\r\n                      return;*/\r\n      if (this.queryParams.countryName == \"\") {\r\n        this.queryParams.country = \"-1\";\r\n      }\r\n      this.setElectroyType();\r\n      let params = this.queryParams;\r\n      if (name.split('_').length > 1) {\r\n        // 导出包含电表/协议信息\r\n        params.moreMes = name.split('_')[1];\r\n      } else {\r\n        params.moreMes = \"\";\r\n      }\r\n      name = name.split('_')[0];\r\n      console.log(name);\r\n      console.log(JSON.stringify(params));\r\n      if (name === 'current') {\r\n        params.pageNum = this.pageNum;\r\n        params.pageSize = this.pageSize;\r\n        params.iffinance = 0;\r\n      } else if (name === 'all') {\r\n        if (params.endAccountno == null || params.endAccountno.length == 0 || params.startAccountno == null || params.startAccountno.length == 0) {\r\n          this.errorTips(\"全部导出时，必须选择起止期号\");\r\n          return;\r\n        }\r\n        if (this.version == 'sc')\r\n        {  if ((Monthdiff(params.endAccountno, params.startAccountno)) > 4) {\r\n            this.errorTips(\"全部导出时，一次性只能导出4个月的数据\");\r\n            return;\r\n          }\r\n      }\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n        params.iffinance = 0;\r\n      } else if (name === 'allfinance') {\r\n        if (params.endAccountno == null || params.endAccountno.length == 0 || params.startAccountno == null || params.startAccountno.length == 0) {\r\n          this.errorTips(\"全部导出时，必须选择起止期号\");\r\n          return;\r\n        }\r\n        if (this.version == 'sc') {\r\n          if ((Monthdiff(params.endAccountno, params.startAccountno)) > 4) {\r\n            this.errorTips(\"全部导出时，一次性只能导出4个月的数据\");\r\n            return;\r\n          }\r\n        }\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n        params.iffinance = 1;\r\n      }\r\n      let req = {\r\n        url: \"/business/account/exportcx\",\r\n        method: \"get\",\r\n        params: params\r\n      };\r\n      this.exportloading = true\r\n      axios.file(req).then(res => {\r\n        this.exportloading = false\r\n        const content = res\r\n        const blob = new Blob([content])\r\n        const fileName = '台账查询导出数据' + '.xlsx';\r\n        if ('download' in document.createElement('a')) { // 非IE下载\r\n          const elink = document.createElement('a')\r\n          elink.download = fileName\r\n          elink.style.display = 'none'\r\n          elink.href = URL.createObjectURL(blob)\r\n          document.body.appendChild(elink)\r\n          elink.click()\r\n          URL.revokeObjectURL(elink.href) // 释放URL 对象\r\n          document.body.removeChild(elink)\r\n        } else { // IE10+下载\r\n          navigator.msSaveBlob(blob, fileName)\r\n        }\r\n      }).catch(err => {\r\n        this.exportloading = false;\r\n        console.log(err);\r\n      });\r\n    },\r\n    getUserData() {\r\n      let that = this;\r\n      getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n        if (res.data.companies.length != 0) {\r\n          let companies = res.data.companies;\r\n          if (res.data.companies[0].id == \"**********\") {\r\n            companies = that.companies;\r\n          }\r\n          that.company = companies[0].id;\r\n          that.queryParams.company = companies[0].id;\r\n        }\r\n        if (res.data.departments.length != 0) {\r\n          let departments = res.data.departments;\r\n          if (res.data.companies[0].id == \"**********\" && that.departments.length != 0) {\r\n            departments = that.departments\r\n          }\r\n          that.country = departments[0].id;\r\n          that.countryName = departments[0].name;\r\n          that.queryParams.country = Number(departments[0].id);\r\n          that.queryParams.countryName = departments[0].name;\r\n        }\r\n\r\n        that.pageNum = 1\r\n        that.getAccountMessages();\r\n      });\r\n    },\r\n    getno() {\r\n      let date = new Date();\r\n      //获取当前年月\r\n      let year = date.getFullYear();\r\n      let month = date.getMonth() + 1;\r\n      month = (month < 10 ? \"0\" + month : month);\r\n      let curDate = (year.toString() + month.toString());\r\n      return curDate;\r\n    },\r\n    //验证错误弹出提示框并跳转到下一格\r\n    errorTips(str) {\r\n      this.$Notice.error({\r\n        title: '提示',\r\n        desc: str,\r\n        duration: 10\r\n      });\r\n    },\r\n    uploadFile(row) {\r\n      this.$refs.uploadFileModal.showadd = false;\r\n      this.$refs.uploadFileModal.columns.pop();\r\n      this.$refs.uploadFileModal.choose(row.pcid + '');\r\n    },\r\n  },\r\n  mounted() {\r\n    this.accountStatus = blist(\"accountStatus\");\r\n    this.ammeteruseList = blist('ammeterUse');\r\n    this.categorys = blist(\"ammeterCategory\");\r\n    this.directsupplyflags = blist(\"directSupplyFlag\");\r\n    this.billtypes = blist(\"billpreType\");\r\n    let that = this;\r\n    that.startDatePicker = that.getno()\r\n    that.queryParams.startAccountno = that.getno()\r\n    getUserByUserRole().then(res => {//根据权限获取分公司\r\n      that.companies = res.data.companies;\r\n      if (res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true) {\r\n        that.isAdmin = true;\r\n      }\r\n      getCountrysdata({orgCode: res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n        that.departments = res.data;\r\n        that.getUserData();\r\n      });\r\n    });\r\n\r\n    getClassification().then(res => {//用电类型\r\n      this.classificationData = res.data;\r\n    });\r\n    this.version = indexData.version\r\n    if (this.version == 'ln') {\r\n      this.listTb.columns = this.columns_ln\r\n    } else if (this.version == 'sc') {\r\n      this.listTb.columns = this.columns_sc\r\n    }\r\n\r\n  }\r\n}\r\n\r\nfunction Monthdiff(a, b) {\r\n  var year1 = a.substring(0, 4);\r\n  var year2 = b.substring(0, 4);\r\n  var month1 = a.substring(4, 6).replace(/0/, \"\");\r\n  var month2 = b.substring(4, 6).replace(/0/, \"\");\r\n  return (year1 - year2) * 12 + (month1 - month2)+1;\r\n\r\n}\r\n</script>"]}]}