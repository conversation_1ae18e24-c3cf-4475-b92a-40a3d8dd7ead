{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\addAmmeter.vue?vue&type=style&index=0&id=35011f71&lang=css&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\addAmmeter.vue", "mtime": 1754285403016}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLm1hcmdpbi1yaWdodC13aWR0aCB7DQogIG1hcmdpbi1yaWdodDogMTBweDsNCn0NCg0KLnRlc3RhYSAuaXZ1LXJvdyB7DQogIG1hcmdpbi1sZWZ0OiA1cHg7DQogIG1hcmdpbi1yaWdodDogNXB4Ow0KfQ0KDQoudGVzdGFhIC5yZXF1aXJlU3RhciAuaXZ1LWZvcm0taXRlbS1sYWJlbDpiZWZvcmUgew0KICBjb250ZW50OiAiKiI7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgbWFyZ2luLXJpZ2h0OiA0cHg7DQogIGxpbmUtaGVpZ2h0OiAxOw0KICBmb250LWZhbWlseTogU2ltU3VuOw0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjZWQ0MDE0Ow0KfQ0K"}, {"version": 3, "sources": ["addAmmeter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiyFA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addAmmeter.vue", "sourceRoot": "src/view/basedata/ammeter", "sourcesContent": ["<template>\r\n  <!-- *****添加电表  <AUTHOR> -->\r\n  <div class=\"testaa\">\r\n    <!--重点就是下面的代码了-->\r\n    <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n    <div solt=\"header\">\r\n      <Row>\r\n        <Col span=\"24\" style=\"text-align: right; right: 10px\">\r\n          <Button\r\n            type=\"success\"\r\n            :loading=\"isLoading == 0 ? loading : false\"\r\n            :disabled=\"isDisable\"\r\n            @click=\"onModalOK(0)\"\r\n            >保存</Button\r\n          >\r\n          <Button\r\n            type=\"primary\"\r\n            :loading=\"isLoading == 1 ? loading : false\"\r\n            :disabled=\"isDisable\"\r\n            @click=\"onModalOK(1)\"\r\n            >提交</Button\r\n          >\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n    <cl-wf-btn\r\n      ref=\"clwfbtn\"\r\n      :isStart=\"true\"\r\n      :params=\"workFlowParams\"\r\n      @on-ok=\"doWorkFlow\"\r\n      v-show=\"false\"\r\n    ></cl-wf-btn>\r\n    <select-electric-type\r\n      ref=\"selectElectricType\"\r\n      v-on:listenToSetElectricType=\"setElectricData\"\r\n    ></select-electric-type>\r\n    <country-modal\r\n      ref=\"countryModal\"\r\n      v-on:getDataFromModal=\"getDataFromModal\"\r\n    ></country-modal>\r\n    <!--      选择关联局站-->\r\n    <station-modal\r\n      ref=\"stationModal\"\r\n      v-on:getDataFromStationModal=\"getDataFromStationModal\"\r\n    ></station-modal>\r\n    <ammeter-protocol-list\r\n      ref=\"selectAmmeterProtocol\"\r\n      v-on:listenToSetAmmeterProrocol=\"setAmmeterProrocolData\"\r\n    ></ammeter-protocol-list>\r\n    <customer-list\r\n      ref=\"customerList\"\r\n      v-on:getDataFromCustomerModal=\"getDataFromCustomerModal\"\r\n    ></customer-list>\r\n    <!--        <Modal v-model=\"showModel\" width=\"80%\" :title=\"title\">-->\r\n    <Card class=\"menu-card\">\r\n      <Collapse :value=\"['Panel1', 'Panel2', 'Panel3', 'Panel4', 'Panel5']\">\r\n        <Panel name=\"Panel1\"\r\n          >基本信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"项目名称：\" prop=\"projectname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.projectname\"\r\n                        placeholder=\"**路**号**楼电表\"\r\n                        @on-blur=\"projectNameChange\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.projectname != null &&\r\n                          oldData.projectname != ammeter.projectname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.projectname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag === 1\">\r\n                    <FormItem\r\n                      label=\"供电局电表编号(下户户号)：\"\r\n                      prop=\"supplybureauammetercode\"\r\n                    >\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauammetercode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauammetercode != null &&\r\n                          oldData.supplybureauammetercode !=\r\n                            ammeter.supplybureauammetercode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauammetercode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag === 2\">\r\n                    <FormItem\r\n                      label=\"供电局电表编号(电表编号)：\"\r\n                      prop=\"supplybureauammetercode\"\r\n                    >\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauammetercode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauammetercode != null &&\r\n                          oldData.supplybureauammetercode !=\r\n                            ammeter.supplybureauammetercode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauammetercode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否智能电表：\" prop=\"issmartammeter\">\r\n                      <RadioGroup v-model=\"ammeter.issmartammeter\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.issmartammeter != null &&\r\n                          oldData.issmartammeter != ammeter.issmartammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.issmartammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否实体电表：\" prop=\"isentityammeter\">\r\n                      <RadioGroup v-model=\"ammeter.isentityammeter\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.isentityammeter != null &&\r\n                          oldData.isentityammeter != ammeter.isentityammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.isentityammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"电表用途：\" prop=\"ammeteruse\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.ammeteruse\"\r\n                        category=\"ammeterUse\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammeteruse != null &&\r\n                          oldData.ammeteruse != ammeter.ammeteruse\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldAmmeteruse }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"关联实际报账电表：\"\r\n                      prop=\"parentCode\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.parentCode\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addAmmeterProtocol\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.parentCode != null &&\r\n                          oldData.parentCode != ammeter.parentCode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.parentCode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"供电局名称：\" prop=\"supplybureauname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauname != null &&\r\n                          oldData.supplybureauname != ammeter.supplybureauname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"电表类型：\" prop=\"ammetertype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.ammetertype\"\r\n                        category=\"ammeterType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetertype != null &&\r\n                          oldData.ammetertype != ammeter.ammetertype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldAmmetertype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"包干类型：\" prop=\"packagetype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.packagetype\"\r\n                        :disabled=\"ammeter.islumpsum == 1 ? false : true\"\r\n                        category=\"packageType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      >\r\n                      </cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.packagetype != null &&\r\n                          oldData.packagetype != ammeter.packagetype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPackagetype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"所属分公司：\" prop=\"company\">\r\n                      <Select\r\n                        v-model=\"ammeter.company\"\r\n                        @on-change=\"selectChange(ammeter.company)\"\r\n                      >\r\n                        <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">\r\n                          {{ item.name }}\r\n                        </Option>\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.company != null && oldData.company != ammeter.company\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.companyName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <!--                                            <FormItem label=\"所属部门：\" prop=\"countryName\">-->\r\n                    <!--                                                <Input icon=\"ios-archive\" v-model=\"ammeter.countryName\"-->\r\n                    <!--                                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly/>-->\r\n                    <!--                                            </FormItem>-->\r\n                    <FormItem\r\n                      label=\"所属部门：\"\r\n                      prop=\"countryName\"\r\n                      v-if=\"isAdmin == true\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-if=\"isCityAdmin == true || isEditByCountry == false\"\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseResponseCenter()\"\r\n                        readonly\r\n                      />\r\n                      <Input\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-else-if=\"isEditByCountry == true && isCityAdmin == false\"\r\n                        readonly\r\n                      />\r\n                      <!--                                                <label v-if=\"oldData.countryName != null &&oldData.countryName != ammeter.countryName\" style=\"color: red;\">历史数据：{{oldData.countryName}}</label>-->\r\n                    </FormItem>\r\n                    <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\">\r\n                      <Select v-model=\"ammeter.country\" v-if=\"isEditByCountry == false\">\r\n                        <Option\r\n                          v-for=\"item in departments\"\r\n                          :value=\"item.id\"\r\n                          :key=\"item.id\"\r\n                        >\r\n                          {{ item.name }}\r\n                        </Option>\r\n                      </Select>\r\n                      <Input\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-else-if=\"isEditByCountry == true\"\r\n                        readonly\r\n                      />\r\n                      <!--                                                <label v-if=\"oldData.countryName != null &&oldData.countryName != ammeter.countryName\" style=\"color: red;\">历史数据：{{oldData.countryName}}</label>-->\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"所属分局或支局：\" prop=\"substation\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.substation\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.substation != null &&\r\n                          oldData.substation != ammeter.substation\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.substation }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"详细地址：\" prop=\"address\">\r\n                      <cl-input :maxlength=\"100\" v-model=\"ammeter.address\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.address != null && oldData.address != ammeter.address\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.address }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费名称：\" prop=\"payname\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.payname\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.payname != null && oldData.payname != ammeter.payname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.payname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费类型：\" prop=\"payperiod\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.payperiod\"\r\n                        category=\"payPeriod\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.payperiod != null &&\r\n                          oldData.payperiod != ammeter.payperiod\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPayperiod }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费经办人：\" prop=\"paymanager\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.paymanager\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.paymanager != null &&\r\n                          oldData.paymanager != ammeter.paymanager\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.paymanager }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"付费方式：\" prop=\"paytype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.paytype\"\r\n                        category=\"payType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.paytype != null && oldData.paytype != ammeter.paytype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPaytype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"管理负责人：\" prop=\"ammetermanager\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.ammetermanager\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetermanager != null &&\r\n                          oldData.ammetermanager != ammeter.ammetermanager\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetermanager }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"单价(含税元)：\" prop=\"price\">\r\n                      <InputNumber\r\n                        :maxlength=\"20\"\r\n                        :min=\"0\"\r\n                        v-model=\"ammeter.price\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.price != null && oldData.price != ammeter.price\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.price }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"用电类型：\" prop=\"classifications\">\r\n                      <Cascader\r\n                        :data=\"classificationData\"\r\n                        :change-on-select=\"true\"\r\n                        v-model=\"ammeter.classifications\"\r\n                        @on-change=\"changeClassifications\"\r\n                      ></Cascader>\r\n                      <label\r\n                        v-if=\"\r\n                          ammeter.classifications !== undefined &&\r\n                          ammeter.classifications.length > 0\r\n                        \"\r\n                      >\r\n                        <label v-for=\"(item, i) in ammeter.classifications\" :key=\"i\">\r\n                          <label\r\n                            v-if=\"\r\n                              i === ammeter.classifications.length - 1 &&\r\n                              oldData.electrotype != null &&\r\n                              oldData.electrotype != item\r\n                            \"\r\n                            style=\"color: red\"\r\n                            >历史数据：{{ oldData.electrotypename }}</label\r\n                          >\r\n                        </label>\r\n                      </label>\r\n                      <label\r\n                        v-else-if=\"\r\n                          ammeter.classifications !== undefined &&\r\n                          ammeter.classifications.length <= 0\r\n                        \"\r\n                      >\r\n                        <label v-if=\"oldData.electrotype != null\" style=\"color: red\"\r\n                          >历史数据：{{ oldData.electrotypename }}</label\r\n                        >\r\n                      </label>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"倍率：\" prop=\"magnification\">\r\n                      <InputNumber\r\n                        :min=\"1\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.magnification\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.magnification != null &&\r\n                          oldData.magnification != ammeter.magnification\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.magnification }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"对外结算类型：\" prop=\"directsupplyflag\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.directsupplyflag\"\r\n                        category=\"directSupplyFlag\"\r\n                        :disabled=\"iszgzOnly\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                        @on-change=\"changedirectsupply\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.directsupplyflag != null &&\r\n                          oldData.directsupplyflag != ammeter.directsupplyflag\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldDirectsupplyflag }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem label=\"电价性质：\" prop=\"electrovalencenature\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.electrovalencenature\"\r\n                        category=\"electrovalenceNature\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.electrovalencenature != null &&\r\n                          oldData.electrovalencenature != ammeter.electrovalencenature\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldElectrovalencenature }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"翻表读数(度)：\" prop=\"maxdegree\">\r\n                      <InputNumber\r\n                        :maxlength=\"20\"\r\n                        v-model=\"ammeter.maxdegree\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.maxdegree != null &&\r\n                          oldData.maxdegree != ammeter.maxdegree\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.maxdegree }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"状态：\" prop=\"status\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.status\"\r\n                        @on-change=\"changeStatus\"\r\n                        category=\"status\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"oldData.status != null && oldData.status != ammeter.status\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStatus }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"占局(站)定额电量比例(%)：\" prop=\"quotapowerratio\">\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :min=\"1\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.quotapowerratio\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.quotapowerratio != null &&\r\n                          oldData.quotapowerratio != ammeter.quotapowerratio\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.quotapowerratio }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"合同对方：\" prop=\"contractOthPart\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.contractOthPart\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.contractOthPart != null &&\r\n                          oldData.contractOthPart != ammeter.contractOthPart\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.contractOthPart }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"!isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select\r\n                        v-model=\"ammeter.property\"\r\n                        :disabled=\"propertyReadonly\"\r\n                        @on-change=\"getProperty\"\r\n                      >\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}\r\n                        </Option>\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\">\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}\r\n                        </Option>\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"分割比例(%)：\"\r\n                      prop=\"percent\"\r\n                      v-if=\"ammeter.property === 2 || ammeter.property === 4\"\r\n                    >\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.percent\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.percent != null && oldData.percent != ammeter.percent\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.percent }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"收款客户名称：\"\r\n                      prop=\"customerName\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.customerName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addCustomer\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.customerName != null &&\r\n                          oldData.customerName != ammeter.customerName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.customerName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN')\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"是否铁塔包干：\" prop=\"islumpsum\">\r\n                      <RadioGroup\r\n                        v-model=\"ammeter.islumpsum\"\r\n                        @on-change=\"updatepackagetype\"\r\n                      >\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.islumpsum != null &&\r\n                          oldData.islumpsum != ammeter.islumpsum\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.islumpsum == \"0\" ? \"否\" : \"是\" }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"月包干电量(度)：\" prop=\"ybgPower\">\r\n                      <cl-input :maxlength=\"20\" v-model=\"ammeter.ybgPower\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ybgPower != null && oldData.ybgPower != ammeter.ybgPower\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ybgPower }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干起始日期：\" prop=\"lumpstartdate\">\r\n                      <cl-date-picker\r\n                        type=\"date\"\r\n                        placeholder=\"包干起始日期\"\r\n                        v-model=\"ammeter.lumpstartdate\"\r\n                        style=\"width: 160px\"\r\n                      ></cl-date-picker>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.lumpstartdate != null &&\r\n                          oldData.lumpstartdate != ammeter.lumpstartdate\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.lumpstartdate }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干截止日期：\" prop=\"lumpenddate\">\r\n                      <cl-date-picker\r\n                        type=\"date\"\r\n                        placeholder=\"包干截止日期\"\r\n                        v-model=\"ammeter.lumpenddate\"\r\n                        style=\"width: 160px\"\r\n                      ></cl-date-picker>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.lumpenddate != null &&\r\n                          oldData.lumpenddate != ammeter.lumpenddate\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.lumpenddate }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.packagetype != null &&\r\n                      ammeter.packagetype != 4 &&\r\n                      (configVersion == 'sc' || configVersion == 'SC')\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干费用：\" prop=\"fee\">\r\n                      <InputNumber\r\n                        :min=\"0\"\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.fee\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.fee != null && oldData.fee != ammeter.fee\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.fee }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"月包干电费：\" prop=\"fee\">\r\n                      <InputNumber\r\n                        :min=\"0\"\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.fee\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.fee != null && oldData.fee != ammeter.fee\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.fee }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc' || configVersion == 'SC'\">\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"输配电公司：\"\r\n                      prop=\"transdistricompany\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: true,\r\n                          type: 'number',\r\n                          message: '请选择',\r\n                          trigger: 'change,blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <cl-select\r\n                        v-model=\"ammeter.transdistricompany\"\r\n                        category=\"transdistricompany\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.transdistricompany != null &&\r\n                          oldData.transdistricompany != ammeter.transdistricompany\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldtransdistricompany }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.directsupplyflag == 1 && ammeter.transdistricompany == 1\r\n                    \"\r\n                  >\r\n                    <FormItem\r\n                      label=\"电压等级：\"\r\n                      prop=\"voltageClass\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: true,\r\n                          type: 'number',\r\n                          message: '请选择',\r\n                          trigger: 'change,blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <cl-select\r\n                        v-model=\"ammeter.voltageClass\"\r\n                        category=\"voltageClass\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.voltageClass != null &&\r\n                          oldData.voltageClass != ammeter.voltageClass\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldvoltageClass }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc'\">\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"是否转改直：\"\r\n                      prop=\"iszgz\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.iszgz\" @on-change=\"iszgzchange\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"原转供电表编号：\"\r\n                      prop=\"oldammetername\"\r\n                      v-if=\"ammeter.iszgz == '1'\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: ammeter.iszgz == '1',\r\n                          message: '不能为空',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        :value=\"\r\n                          ammeter.oldammetername\r\n                            ? ammeter.oldammetername.split(',')[0]\r\n                            : null\r\n                        \"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseoldammetername\"\r\n                      />\r\n                    </FormItem>\r\n                    <ChooseAmmeterModel\r\n                      ref=\"chooseAmmeterModel\"\r\n                      v-if=\"ammeter.iszgz == '1'\"\r\n                      v-on:getAmmeterModelModal=\"getAmmeterModelModal\"\r\n                    />\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"是否直购电：\"\r\n                      prop=\"directFlag\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.directFlag\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"是否含办公：\"\r\n                      prop=\"officeFlag\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.officeFlag\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel2\"\r\n          >关联局站信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter1\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"局(站)名称：\"\r\n                      prop=\"stationName\"\r\n                      :class=\"{ requireStar: isRequireFlag }\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.stationName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseResponseCenter(1)\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationName != null &&\r\n                          oldData.stationName != ammeter.stationName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)编码：\" prop=\"stationcode\">\r\n                      <cl-input readonly v-model=\"ammeter.stationcode\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationcode != null &&\r\n                          oldData.stationcode != ammeter.stationcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)状态：\" prop=\"stationstatus\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.stationstatus\"\r\n                        filterable\r\n                        category=\"stationStatus\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationstatus != null &&\r\n                          oldData.stationstatus != ammeter.stationstatus\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStationstatus }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)类型：\" prop=\"stationtype\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.stationtype\"\r\n                        filterable\r\n                        category=\"BUR_STAND_TYPE\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationtype != null &&\r\n                          oldData.stationtype != ammeter.stationtype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStationtype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)地址：\" prop=\"stationaddress\">\r\n                      <cl-input readonly v-model=\"ammeter.stationaddress\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationaddress != null &&\r\n                          oldData.stationaddress != ammeter.stationaddress\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationaddress }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"资源局站id：\" prop=\"stationaddresscode\">\r\n                      <cl-input\r\n                        readonly\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.resstationcode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.resstationcode != null &&\r\n                          oldData.resstationcode != ammeter.resstationcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.resstationcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否有空调：\" prop=\"isairconditioning\">\r\n                      <RadioGroup v-model=\"ammeter.isairconditioning\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.isairconditioning != null &&\r\n                          oldData.isairconditioning != ammeter.isairconditioning\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.isairconditioning == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"核定电量：\" prop=\"vouchelectricity\">\r\n                      <InputNumber\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.vouchelectricity\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.vouchelectricity != null &&\r\n                          oldData.vouchelectricity != ammeter.vouchelectricity\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.vouchelectricity }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"isCDCompany\">\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管C网编号：\" prop=\"nmCcode\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmCcode\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmCcode != null && oldData.nmCcode != ammeter.nmCcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmCcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号L2.1G：\" prop=\"nmL2100\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmL2100\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmL2100 != null && oldData.nmL2100 != ammeter.nmL2100\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmL2100 }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号L1.8G：\" prop=\"nmL1800\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmL1800\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmL1800 != null && oldData.nmL1800 != ammeter.nmL1800\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmL1800 }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号C+L800M：\" prop=\"nmCl800m\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmCl800m\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmCl800m != null && oldData.nmCl800m != ammeter.nmCl800m\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmCl800m }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc' && isMobileBase\">\r\n                  <Col span=\"12\">\r\n                    <FormItem label=\"5GR站址编码：\" prop=\"stationcode5gr\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.stationcode5gr\"\r\n                        readonly\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationcode5gr != null &&\r\n                          oldData.stationcode5gr != ammeter.stationcode5gr\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationcode5gr }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"12\">\r\n                    <FormItem label=\"5GR站址名称：\" prop=\"stationname5gr\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.stationname5gr\"\r\n                        readonly\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationname5gr != null &&\r\n                          oldData.stationname5gr != ammeter.stationname5gr\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationname5gr }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel3\"\r\n          >关联用电类型比例\r\n          <div slot=\"content\" v-if=\"isClassification && ammeter.stationcode != null\">\r\n            <cl-table\r\n              ref=\"ammeterTable\"\r\n              strip\r\n              :columns=\"electro.columns\"\r\n              :data=\"electro.data\"\r\n              :searchable=\"false\"\r\n              :showPage=\"false\"\r\n              selectEnabled\r\n              selectMultiple\r\n            >\r\n              <div slot=\"buttons\">\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"md-add\"\r\n                  type=\"primary\"\r\n                  @click=\"addElectricType\"\r\n                  >新增\r\n                </Button>\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"ios-trash-outline\"\r\n                  type=\"warning\"\r\n                  @click=\"removeElectricType\"\r\n                  >删除\r\n                </Button>\r\n              </div>\r\n            </cl-table>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel4\"\r\n          >业主信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter2\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"联系人：\" prop=\"contractname\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.contractname\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.contractname != null &&\r\n                          oldData.contractname != ammeter.contractname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.contractname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"具体位置：\" prop=\"location\">\r\n                      <cl-input :maxlength=\"100\" v-model=\"ammeter.location\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.location != null && oldData.location != ammeter.location\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.location }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"办公电话：\" prop=\"officephone\">\r\n                      <cl-input :maxlength=\"15\" v-model=\"ammeter.officephone\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.officephone != null &&\r\n                          oldData.officephone != ammeter.officephone\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.officephone }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"移动电话：\" prop=\"telephone\">\r\n                      <cl-input :maxlength=\"15\" v-model=\"ammeter.telephone\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.telephone != null &&\r\n                          oldData.telephone != ammeter.telephone\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.telephone }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"对方单位：\" prop=\"userunit\">\r\n                      <cl-input\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.userunit\"\r\n                      ></cl-input>\r\n                      <ChooseModal\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        ref=\"chooseModalSup\"\r\n                        v-on:getDataFromModal=\"getDataFromModalObject\"\r\n                      />\r\n                      <Input\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        :maxlength=\"50\"\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.userunit\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"handleChooseSup()\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.userunit != null && oldData.userunit != ammeter.userunit\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.userunit }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款账户：\" prop=\"receiptaccountname\">\r\n                      <Select\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        v-model=\"ammeter.receiptaccountname\"\r\n                        style=\"width: 100%\"\r\n                        @on-change=\"onChange\"\r\n                      >\r\n                        <Option\r\n                          v-for=\"item in receiptaccountnameList\"\r\n                          :value=\"item.koinh\"\r\n                          :label=\"item.koinh\"\r\n                          :key=\"item.iid\"\r\n                        ></Option>\r\n                      </Select>\r\n                      <cl-input\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.receiptaccountname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccountname != null &&\r\n                          oldData.receiptaccountname != ammeter.receiptaccountname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccountname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款开户支行：\" prop=\"receiptaccountbank\">\r\n                      <cl-input\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        :maxlength=\"50\"\r\n                        readonly\r\n                        v-model=\"ammeter.receiptaccountbank\"\r\n                      ></cl-input>\r\n                      <cl-input\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.receiptaccountbank\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccountbank != null &&\r\n                          oldData.receiptaccountbank != ammeter.receiptaccountbank\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccountbank }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款账号：\" prop=\"receiptaccounts\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        readonly\r\n                        v-model=\"ammeter.receiptaccounts\"\r\n                      ></cl-input>\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        v-model=\"ammeter.receiptaccounts\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccounts != null &&\r\n                          oldData.receiptaccounts != ammeter.receiptaccounts\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccounts }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"24\">\r\n                    <FormItem label=\"说明：\" prop=\"memo\">\r\n                      <cl-input\r\n                        type=\"textarea\"\r\n                        :rows=\"3\"\r\n                        v-model=\"ammeter.memo\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"oldData.memo != null && oldData.memo != ammeter.memo\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.memo }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel5\"\r\n          >附件信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <attach-file\r\n                :param=\"fileParam\"\r\n                :attachData=\"attachData\"\r\n                v-on:setAttachData=\"setAttachData\"\r\n              />\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n      </Collapse>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addAmmeter,\r\n  listElectricType,\r\n  editAmmeter,\r\n  editAmmeterRecord,\r\n  updateAmmeter,\r\n  checkProjectNameExist,\r\n  checkAmmeterByStation,\r\n  getClassification,\r\n  getClassificationId,\r\n  getUserdata,\r\n  checkClassificationLevel,\r\n  listElectricTypeRatio,\r\n  checkAmmeterExist,\r\n  getUserByUserRole,\r\n  getCountryByUserId,\r\n  getCountrysdata,\r\n  removeAttach,\r\n  attchList,\r\n  getBankCard,\r\n} from \"@/api/basedata/ammeter.js\";\r\nimport { blist, btext } from \"@/libs/tools\";\r\nimport attachFile from \"./../protocol/attachFile\";\r\nimport SelectElectricType from \"./selectElectricType\";\r\nimport countryModal from \"./countryModal\";\r\nimport stationModal from \"./stationModal\";\r\nimport { mapMutations } from \"vuex\";\r\nimport { isEmpty } from \"@/libs/validate\";\r\nimport routers from \"@/router/routers\";\r\nimport { getHomeRoute } from \"@/libs/util\";\r\nimport AmmeterProtocolList from \"@/view/basedata/quota/listAmmeterProtocol\";\r\nimport customerList from \"./customerModal\";\r\nimport ChooseAmmeterModel from \"@/view/basedata/ammeter/chooseAmmeterModel\";\r\nimport ChooseModal from \"@/view/business/gasBusiness/chooseModal\";\r\nimport axios from \"@/libs/api.request\";\r\n\r\nexport default {\r\n  name: \"addAmmeter\",\r\n  components: {\r\n    attachFile,\r\n    AmmeterProtocolList,\r\n    customerList,\r\n    stationModal,\r\n    countryModal,\r\n    SelectElectricType,\r\n    ChooseAmmeterModel,\r\n    ChooseModal,\r\n  },\r\n  data() {\r\n    //不能输入汉字\r\n    const checkData = (rule, value, callback) => {\r\n      if (value) {\r\n        if (/[\\u4E00-\\u9FA5]/g.test(value)) {\r\n          callback(new Error(\"编码不能输入汉字!\"));\r\n        } else if (escape(value).indexOf(\"%u\") >= 0) {\r\n          callback(new Error(\"编码不能输入中文字符!\"));\r\n        } else {\r\n          callback();\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatorNumber = (rule, value, callback) => {\r\n      if (value.length <= 0) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatorNumberZero = (rule, value, callback) => {\r\n      if (value != null && value == 0) {\r\n        callback(new Error(\"只能输入大于0的数\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validateClassifications = (rule, value, callback) => {\r\n      if (value == undefined || value == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        if (value.length <= 0) {\r\n          callback(new Error(\"不能为空\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n\r\n    const validatePrice = (rule, value, callback) => {\r\n      if (value == undefined || value == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        if (value <= 0) {\r\n          callback(new Error(\"单价(含税)不能小于0\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatelumpstartdate = (rule, value, callback) => {\r\n      let data = this.ammeter;\r\n      let start = data.lumpstartdate;\r\n      let end = data.lumpenddate;\r\n      if (start == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      }\r\n      if (start != null && end != null) {\r\n        if (end <= start) {\r\n          callback(new Error(\"包干起始日期不能大于等于截止日期\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatelumpenddate = (rule, value, callback) => {\r\n      let data = this.ammeter;\r\n      let start = data.lumpstartdate;\r\n      let end = data.lumpenddate;\r\n      if (end == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      }\r\n      if (start != null && end != null) {\r\n        if (end <= start) {\r\n          callback(new Error(\"包干截止日期不能小于等于起始日期\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    //更改标题名称及样式\r\n    let renderHeader = (h, params) => {\r\n      let t = h(\r\n        \"span\",\r\n        {\r\n          style: {\r\n            fontWeight: \"normal\",\r\n            color: \"#ed4014\",\r\n            fontSize: \"12px\",\r\n            fontFamily: \"SimSun\",\r\n            marginRight: \"4px\",\r\n            lineHeight: 1,\r\n            display: \"inline-block\",\r\n          },\r\n        },\r\n        \"*\"\r\n      );\r\n      return h(\"div\", [t, h(\"span\", {}, \"所占比例(%)\")]);\r\n    };\r\n    return {\r\n      isRequireFlags: false,\r\n      propertyright: null, //局站产权\r\n      isRequireFlag: false, //局站是否必填\r\n      ischeckStation: false, //是否需要验证局站只能关联5个\r\n      isCDCompany: false, //是否是成都分公司\r\n      configVersion: null, //版本\r\n      isMobileBase: false, //是否生产用电-移动基站\r\n      propertyReadonly: true,\r\n      propertyList: [],\r\n      isDisable: false,\r\n      workFlowParams: {},\r\n      loading: false,\r\n      isLoading: null,\r\n\r\n      isError: false, //用电类型比例验证\r\n      isError1: false, //用电类型比例验证\r\n\r\n      showModel: false,\r\n      isClassification: false,\r\n      title: \"\",\r\n      isEditByCountry: false,\r\n      isCityAdmin: false,\r\n      isAdmin: false,\r\n      chooseIndex: null,\r\n      electroRowNum: null, //关联用电类型的当前行\r\n      electricTypeModel: false,\r\n      companies: [],\r\n      departments: [],\r\n      classificationData: [], //用电类型\r\n      receiptaccountnameList: [], //银行卡列表\r\n      fileParam: {\r\n        busiId: \"\",\r\n        busiAlias: \"附件(协议管理)\",\r\n        categoryCode: \"file\",\r\n        areaCode: \"ln\",\r\n      },\r\n      multiFiles: null,\r\n      files: [],\r\n      removeIds: [],\r\n      attachData: [],\r\n      oldData: [],\r\n      oldCategory: \"\", //原始数据\r\n      oldPackagetype: \"\", //原始数据\r\n      oldPayperiod: \"\", //原始数据\r\n      oldPaytype: \"\", //原始数据\r\n      oldElectronature: \"\", //原始数据\r\n      oldElectrovalencenature: \"\", //原始数据\r\n      oldElectrotype: \"\", //原始数据\r\n      oldStatus: \"\", //原始数据\r\n      oldProperty: \"\", //原始数据\r\n      oldAmmetertype: \"\", //原始数据\r\n      oldStationstatus: \"\", //原始数据\r\n      oldStationtype: \"\", //原始数据\r\n      oldAmmeteruse: \"\", //原始数据\r\n      oldDirectsupplyflag: \"\", //原始数据\r\n      ruleValidate: {\r\n        isentityammeter: [\r\n          { required: true, message: \"不能为空\", trigger: \"change,blur\" },\r\n        ],\r\n        projectname: [\r\n          //项目名称\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        countryName: [\r\n          //所属部门\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        country: [\r\n          //所属部门\r\n          { required: true, type: \"number\", validator: validatorNumber, trigger: \"blur\" },\r\n        ],\r\n        company: [{ required: true, validator: validatorNumber, trigger: \"blur\" }],\r\n        paytype: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        payperiod: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        ammeteruse: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        property: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        ammetertype: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        electrovalencenature: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        classifications: [\r\n          { required: true, validator: validateClassifications, trigger: \"change,blur\" },\r\n        ],\r\n        magnification: [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        directsupplyflag: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          { required: true, type: \"number\",\r\n            validator: validatePrice,\r\n            message: \"不能为空\", trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        packagetype: [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contractOthPart: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\r\n        stationName: [],\r\n        status: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        telephone: [{ pattern: /^1\\d{10}$/, message: \"格式不正确\", trigger: \"blur\" }],\r\n        percent: [\r\n          { type: \"number\", validator: validatorNumberZero, trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([0-9]\\d{0,12}))(\\.\\d{0,4})?$/,\r\n            message: \"只能保留四位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lumpstartdate: [\r\n          {\r\n            required: true,\r\n            type: \"date\",\r\n            validator: validatelumpstartdate,\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lumpenddate: [\r\n          {\r\n            required: true,\r\n            type: \"date\",\r\n            validator: validatelumpenddate,\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        fee: [\r\n          { required: true, type: \"number\", validator: validatorNumber, trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        supplybureauammetercode: [],\r\n        transdistricompany: [],\r\n        voltageClass: [],\r\n      },\r\n      electro: {\r\n        columns: [\r\n          {\r\n            title: \"序号\",\r\n            type: \"index\",\r\n          },\r\n          {\r\n            title: \"用电类型\",\r\n            key: \"typeName\",\r\n          },\r\n          {\r\n            title: \"所占比例(%)\",\r\n            key: \"ratio\",\r\n            renderHeader: renderHeader,\r\n            render: (h, params) => {\r\n              let that = this;\r\n              let ratio = params.row.ratio;\r\n              let isError1 = params.row.idError1;\r\n              let error = h(\r\n                \"label\",\r\n                {\r\n                  style: {\r\n                    color: \"#ed4014\",\r\n                    fontSize: \"12px\",\r\n                    fontFamily: \"SimSun\",\r\n                    paddingTop: \"6px\",\r\n                    lineHeight: 1,\r\n                    fontWeight: \"bold\",\r\n                    display: null != ratio ? \"none\" : \"inline-block\",\r\n                  },\r\n                },\r\n                \"不能为空\"\r\n              );\r\n              let error1 = h(\r\n                \"label\",\r\n                {\r\n                  style: {\r\n                    color: \"#ed4014\",\r\n                    fontSize: \"12px\",\r\n                    fontFamily: \"SimSun\",\r\n                    paddingTop: \"6px\",\r\n                    lineHeight: 1,\r\n                    fontWeight: \"bold\",\r\n                    display: isError1 == true ? \"inline-block\" : \"none\",\r\n                  },\r\n                },\r\n                \"输入比例不合格要求\"\r\n              );\r\n              let result = h(\"InputNumber\", {\r\n                style: {\r\n                  border: null == ratio || isError1 == true ? \"1px solid #ed4014\" : \"\",\r\n                },\r\n                props: {\r\n                  value: ratio,\r\n                  max: 100,\r\n                  min: 0.1,\r\n                },\r\n                on: {\r\n                  \"on-change\": (v) => {\r\n                    if (v == undefined || v == null) {\r\n                      that.isError = true;\r\n                    } else {\r\n                      that.isError = false;\r\n                    }\r\n                    //给data重新赋值\r\n                    // let reg = /^(?:[1-9]?\\d|100)$/;\r\n                    let reg = /^-?(([1-9][0-9]*)|(([0]\\.\\d{1,2}|[1-9][0-9]*\\.\\d{1,2})))$/;\r\n                    if (v != undefined && v != null && !reg.test(v)) {\r\n                      params.row.idError1 = true;\r\n                      that.isError1 = true;\r\n                    } else {\r\n                      params.row.idError1 = false;\r\n                      that.isError1 = false;\r\n                    }\r\n                    params.row.ratio = v;\r\n                    that.electro.data[params.row._index] = params.row;\r\n                  },\r\n                },\r\n              });\r\n              return h(\"div\", [result, error, error1]);\r\n            },\r\n          },\r\n          {\r\n            title: \"关联局站\",\r\n            key: \"stationName\",\r\n            render: (h, params) => {\r\n              let stationName = params.row.stationName;\r\n              let disabled = params.row._disabled;\r\n              if (disabled != undefined && disabled == true) {\r\n                return h(\"Input\", {\r\n                  props: {\r\n                    value: stationName,\r\n                    readonly: true,\r\n                  },\r\n                });\r\n              } else {\r\n                return h(\"Input\", {\r\n                  props: {\r\n                    value: stationName,\r\n                    icon: \"ios-archive\",\r\n                    placeholder: \"点击图标选择\",\r\n                    readonly: true,\r\n                  },\r\n                  on: {\r\n                    \"on-click\": (v) => {\r\n                      this.chooseResponseCenter(2, params, params.row._index);\r\n                    },\r\n                  },\r\n                });\r\n              }\r\n            },\r\n          },\r\n        ],\r\n        data: [],\r\n      },\r\n      ammeter: {\r\n        maxdegree: null,\r\n        ammetername: \"\",\r\n        userunit: \"\",\r\n        id: null,\r\n        country: null,\r\n        company: null,\r\n        countryName: null,\r\n        billStatus: 0,\r\n        electricTypes: [],\r\n        electro: [],\r\n        classifications: [], //用电类型\r\n        iszgz: 0,\r\n        userunitCode: 0,\r\n        directFlag: 0,\r\n        officeFlag: 0,\r\n        transdistricompany: 1,\r\n        voltageClass: \"\",\r\n        issmartammeter: \"0\",\r\n        price: \"\",\r\n        stationcode5gr: null,\r\n        stationname5gr: null,\r\n      },\r\n      iszgzOnly: false,\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    ...mapMutations([\"closeTag\", \"closeTagByName\"]),\r\n    getProperty(v) {},\r\n    OK(type) {\r\n      // if (\r\n      //   this.ammeter.price == undefined ||\r\n      //   this.ammeter.price == null ||\r\n      //   this.ammeter.price == \"\"\r\n      // ) {\r\n      //   this.$Message.error(\"单价(含税元)不能为空\");\r\n      //   return;\r\n      // }\r\n      // if (\r\n      //   this.ammeter.magnification == undefined ||\r\n      //   this.ammeter.magnification == null ||\r\n      //   this.ammeter.magnification == \"\"\r\n      // ) {\r\n      //   this.$Message.error(\"倍率不能为空\");\r\n      //   return;\r\n      // }\r\n      // if (this.ammeter.status == 1) {\r\n      //   if (\r\n      //     this.ammeter.stationName == undefined ||\r\n      //     this.ammeter.stationName == null ||\r\n      //     this.ammeter.stationName == \"\"\r\n      //   ) {\r\n      //     this.$Message.error(\"局(站)名称不能为空\");\r\n      //     return;\r\n      //   }\r\n      // }\r\n      if (type == 1) {\r\n        this.isLoading = 1;\r\n      } else {\r\n        this.isLoading = 0;\r\n      }\r\n      if (this.loading == true) {\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      this.ammeter.electricTypes = this.electro.data;\r\n      this.$refs.ammeter.validate((valid) => {\r\n        if (valid) {\r\n          this.$refs.ammeter1.validate((valid1) => {\r\n            if (valid1) {\r\n              this.$refs.ammeter2.validate((valid2) => {\r\n                if (valid2) {\r\n                  this.checkData(type);\r\n                } else {\r\n                  this.$Message.error(\"业主信息验证没通过\");\r\n                  this.loading = false;\r\n                }\r\n              });\r\n            } else {\r\n              this.$Message.error(\"关联局站信息验证没通过\");\r\n              this.loading = false;\r\n            }\r\n          });\r\n        } else {\r\n          // this.$Message.error(\"基本信息验证没通过\");\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n\r\n    onModalOK(type) {\r\n      this.isDisable = true;\r\n      setTimeout(() => {\r\n        this.isDisable = false; //点击一次时隔两秒后才能再次点击\r\n      }, 3000);\r\n      let attachData = [];\r\n      this.OK(type);\r\n    },\r\n\r\n    setAttachData(data) {\r\n      this.multiFiles = data.data;\r\n      this.removeIds = data.ids;\r\n      if (this.removeIds.length != 0 && data.type == \"remove\") {\r\n        this.removeAttach();\r\n      } else {\r\n        this.upload();\r\n      }\r\n    },\r\n    removeAttach() {\r\n      removeAttach({ ids: this.removeIds.join() }).then(() => {});\r\n    },\r\n    upload() {\r\n      if (this.attachData.length != 0 && this.multiFiles.length != 0) {\r\n        this.loading = true;\r\n        axios\r\n          .request({\r\n            url: \"/common/attachments/uploadMultiFile\",\r\n            method: \"post\",\r\n            data: this.multiFiles,\r\n          })\r\n          .then((res) => {\r\n            if (res.data.code != 0) {\r\n              this.loading = false;\r\n            }\r\n            let that = this;\r\n            attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n              that.attachData = Object.assign([], res.data.rows);\r\n            });\r\n          });\r\n      }\r\n    },\r\n\r\n    //验证数据\r\n    checkData(type) {\r\n      let types = this.ammeter.classifications;\r\n      this.ammeter.electrotype = types[types.length - 1];\r\n      let that = this;\r\n      if (\r\n        this.ammeter.status === 1 &&\r\n        (this.configVersion == \"sc\" || this.configVersion == \"SC\")\r\n      ) {\r\n        //在用状态下验证局站地址不能为空\r\n        if (\r\n          this.ammeter.stationaddress == null ||\r\n          this.ammeter.stationaddress == undefined\r\n        ) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"局站地址不能为空，请在局站管理维护该局站信息！\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      if (this.checkElectricTypeItem()) {\r\n        if (this.ammeter.ammetername != undefined || this.ammeter.ammetername != null) {\r\n          checkAmmeterExist(this.ammeter.id, this.ammeter.ammetername, 0).then((res) => {\r\n            //验证电表是否存在\r\n            let code = res.data.code;\r\n            if (code == 0) {\r\n              this.checkedDate(type);\r\n            } else {\r\n              that.loading = false;\r\n            }\r\n          });\r\n        } else {\r\n          this.checkedDate(type);\r\n        }\r\n      }\r\n    },\r\n    checkedDate(type) {\r\n      let that = this;\r\n      checkProjectNameExist(that.ammeter.id, that.ammeter.projectname, 0).then((res) => {\r\n        //验证项目名称是否存在\r\n        let code = res.data.code;\r\n        if (code == 0) {\r\n          if (that.isCheckStation == true) {\r\n            that.isCheckStation(type);\r\n          } else {\r\n            that.saveData(type); //保存数据\r\n          }\r\n        } else {\r\n          that.loading = false;\r\n        }\r\n      });\r\n    },\r\n    isCheckStation(type) {\r\n      let that = this;\r\n      checkAmmeterByStation({\r\n        id: that.ammeter.id,\r\n        type: 0,\r\n        stationcode: that.ammeter.stationcode,\r\n        electrotype: that.ammeter.electrotype,\r\n        ammeteruse: that.ammeter.ammeteruse,\r\n      }).then((res) => {\r\n        let code = res.data.code;\r\n        if (code == \"error\") {\r\n          that.$Modal.warning({ title: \"温馨提示\", content: res.data.msg });\r\n          that.loading = false;\r\n        } else {\r\n          that.saveData(type); //保存数据\r\n        }\r\n      });\r\n    },\r\n    saveData(type) {\r\n      let that = this;\r\n      this.clearDataByCondition();\r\n      that.ammeter.category = 1; //电表\r\n      addAmmeter(that.ammeter)\r\n        .then((res) => {\r\n          if (res.data != null && res.data != -1 && res.data.success == \"1\") {\r\n            if (type == 1) {\r\n              that.startFlow(res.data);\r\n            } else {\r\n              this.closeTag({ route: this.$route });\r\n              that.warn();\r\n            }\r\n          } else {\r\n            that.loading = false;\r\n            that.$Notice.error({ title: \"提示\", desc: res.data.msg, duration: 10 });\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          that.loading = false;\r\n        });\r\n    },\r\n    //根据条件判断数据是否该清除\r\n    clearDataByCondition() {\r\n      if (this.ammeter.property !== 2 && this.ammeter.property !== 4) {\r\n        //站址产权归属为铁塔 清除分割比例checkAmmeterByStation，是否铁塔按RRU包干\r\n        this.ammeter.percent = null;\r\n      }\r\n      if (this.ammeter.ammeteruse !== 3) {\r\n        //电表用途不是回收电费，清除父电表信息\r\n        this.ammeter.parentId = null;\r\n        this.ammeter.customerId = null;\r\n      }\r\n      if (this.ammeter.directsupplyflag != 1) {\r\n        //只有对外结算类型为直供电才填写该字段，转供电不需填写\r\n        this.ammeter.electrovalencenature = null;\r\n      }\r\n      if (!this.isCDCompany) {\r\n        //成都分公司显示合同对方等，不是，就清除数据\r\n        this.ammeter.contractOthPart = null;\r\n        this.ammeter.nmCcode = null;\r\n        this.ammeter.nmL2100 = null;\r\n        this.ammeter.nmL1800 = null;\r\n        this.ammeter.nmCl800m = null;\r\n      }\r\n    },\r\n    warn() {\r\n      this.$Modal.warning({\r\n        title: \"温馨提示\",\r\n        content: \"保存后的数据要提交审批才能生效！\",\r\n      });\r\n    },\r\n    refreshData() {\r\n      this.initData();\r\n    },\r\n    initData() {\r\n      this.countryName = \"\";\r\n      this.electro.data = [];\r\n      this.oldData = [];\r\n      this.removeIds = [];\r\n      this.multiFiles = [];\r\n      this.files = [];\r\n      this.isCityAdmin = false;\r\n      this.isAdmin = false;\r\n      this.isEditByCountry = false;\r\n      this.$nextTick(() => {\r\n        this.$refs.ammeter.resetFields(); // this.$refs.adduserform.resetFields();\r\n        this.$refs.ammeter1.resetFields(); // this.$refs.adduserform.resetFields();\r\n        this.$refs.ammeter2.resetFields(); // this.$refs.adduserform.resetFields();\r\n      });\r\n      this.showModel = false;\r\n      this.electricTypeModel = false;\r\n      this.directFlag = 0;\r\n      this.officeFlag = 0;\r\n      this.transdistricompany = 1;\r\n    },\r\n    onModalCancel() {\r\n      this.initData();\r\n    },\r\n    /*初始化*/\r\n    initAmmeter(id) {\r\n      this.initData();\r\n      let that = this;\r\n      that.title = \"添加电表\";\r\n      editAmmeter(\"\", 0).then((res) => {\r\n        that.setAmmeter(Object.assign({}, res.data));\r\n       // this.ammeter.price = 0;\r\n        that.getUser();\r\n      });\r\n      getClassification().then((res) => {\r\n        //用电类型\r\n        that.classificationData = res.data;\r\n      });\r\n    },\r\n    changeStatus() {\r\n      if (this.ammeter.status == 1) {\r\n        this.isRequireFlag = true;\r\n        this.ruleValidate.stationName = [\r\n          {\r\n            required: true,\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ];\r\n      } else {\r\n        this.isRequireFlag = false;\r\n        this.ruleValidate.stationName = [\r\n          {\r\n            required: false,\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ];\r\n      }\r\n    },\r\n    selectChange() {\r\n      if (this.ammeter.company != undefined) {\r\n        if (this.ammeter.company == \"1000085\") {\r\n          this.isCDCompany = true;\r\n        } else {\r\n          this.isCDCompany = false;\r\n        }\r\n        getCountryByUserId(this.ammeter.company).then((res) => {\r\n          this.departments = res.data.departments;\r\n          this.ammeter.country = res.data.departments[0].id;\r\n          this.ammeter.countryName = this.departments[0].name;\r\n        });\r\n      }\r\n    },\r\n    getUser() {\r\n      let that = this;\r\n      getUserByUserRole().then((res) => {\r\n        //当前登录用户所在公司\r\n        that.companies = res.data.companies;\r\n        that.isCityAdmin = res.data.isEditAdmin;\r\n        if (\r\n          res.data.isCityAdmin == true ||\r\n          res.data.isProAdmin == true ||\r\n          res.data.isSubAdmin == true\r\n        ) {\r\n          that.isAdmin = true;\r\n        }\r\n        getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {\r\n          //根据权限获取所属部门\r\n          that.departments = res.data;\r\n          that.getUserData();\r\n        });\r\n      });\r\n    },\r\n    getUserData() {\r\n      let that = this;\r\n      getUserdata().then((res) => {\r\n        //当前登录用户所在公司和所属部门\r\n        let companies = that.companies;\r\n        if (res.data.companies != null && res.data.companies.length != 0) {\r\n          if (res.data.companies[0].id != \"2600000000\") {\r\n            companies = res.data.companies;\r\n            // ;\r\n          }\r\n        }\r\n        that.company = companies[0].id;\r\n        if (that.company == \"1000085\") {\r\n          that.isCDCompany = true;\r\n        }\r\n        that.ammeter.company = companies[0].id;\r\n\r\n        let departments = that.departments;\r\n        if (res.data.departments != null && res.data.departments.length != 0) {\r\n          if (res.data.companies[0].id != \"2600000000\") {\r\n            departments = res.data.departments;\r\n          }\r\n        }\r\n        that.country = departments[0].id;\r\n        that.countryName = departments[0].name;\r\n        that.ammeter.country = Number(departments[0].id);\r\n        that.ammeter.countryName = departments[0].name;\r\n      });\r\n    },\r\n    setOldData(data) {\r\n      this.oldCategory = btext({\r\n        category: \"ammeterCategory\",\r\n        v: data.category,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldPackagetype = btext({\r\n        category: \"packageType\",\r\n        v: data.packagetype,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldPayperiod = btext({\r\n        category: \"payPeriod\",\r\n        v: data.payperiod,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldPaytype = btext({\r\n        category: \"payType\",\r\n        v: data.paytype,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldElectronature = btext({\r\n        category: \"electroNature\",\r\n        v: data.electronature,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldElectrovalencenature = btext({\r\n        category: \"electrovalenceNature\",\r\n        v: data.electrovalencenature,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldElectrotype = btext({\r\n        category: \"electroType\",\r\n        v: data.electrotype,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldStatus = btext({\r\n        category: \"status\",\r\n        v: data.status,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldProperty = btext({\r\n        category: \"property\",\r\n        v: data.property,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldAmmetertype = btext({\r\n        category: \"ammeterType\",\r\n        v: data.ammetertype,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldStationstatus = btext({\r\n        category: \"stationStatus\",\r\n        v: data.stationstatus,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldStationtype = btext({\r\n        category: \"BUR_STAND_TYPE\",\r\n        v: data.stationtype,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldAmmeteruse = btext({\r\n        category: \"ammeterUse\",\r\n        v: data.ammeteruse,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n      this.oldDirectsupplyflag = btext({\r\n        category: \"directSupplyFlag\",\r\n        v: data.directsupplyflag,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n    },\r\n\r\n    setAmmeter(form) {\r\n      if (form.status == null) {\r\n        form.status = 1;\r\n        this.isRequireFlag = true;\r\n        this.ruleValidate.stationName = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n      form.issmartammeter = form.issmartammeter == null ? \"0\" : form.issmartammeter + \"\";\r\n      form.iszgz = form.iszgz == null ? \"0\" : form.iszgz + \"\";\r\n      form.directFlag = form.directFlag == null ? \"0\" : form.directFlag + \"\";\r\n      form.officeFlag = form.officeFlag == null ? \"0\" : form.officeFlag + \"\";\r\n      form.isairconditioning =\r\n        form.isairconditioning == null ? \"0\" : form.isairconditioning + \"\";\r\n      form.islumpsum = form.islumpsum == null ? \"0\" : form.islumpsum + \"\";\r\n      this.ammeter = form;\r\n      let electrotype = this.ammeter.electrotype;\r\n      if (\r\n        electrotype === 111 ||\r\n        electrotype === 112 ||\r\n        electrotype === 113 ||\r\n        electrotype === 2\r\n      ) {\r\n        this.isClassification = true;\r\n      }\r\n      if (this.ammeter.magnification == null || this.ammeter.magnification == undefined) {\r\n        this.ammeter.magnification = 1;\r\n      }\r\n      // if (this.ammeter.maxdegree == null || this.ammeter.maxdegree == undefined) {\r\n      //   this.ammeter.maxdegree = 1;\r\n      // }\r\n      // if (this.ammeter.price == null || this.ammeter.price == undefined) {\r\n      //   this.ammeter.price = 1;\r\n      // }\r\n      if (this.ammeter.company != null) {\r\n        this.ammeter.company = this.ammeter.company + \"\";\r\n      }\r\n      this.fileParam.busiId = this.ammeter.id;\r\n    },\r\n\r\n    //修改电表、协议的用电类型时，如用电类型不再与原先选择的局站的局站类型匹配时，系统自动清空原关联局站，需用户重新再关联局站。\r\n    changeClassifications(value) {\r\n      this.clearStation();\r\n      this.isClassification = false;\r\n      if (value.length == 0) {\r\n        this.ammeter.property = null;\r\n        this.propertyReadonly = true;\r\n      } else {\r\n        this.propertyReadonly = false;\r\n        this.ammeter.electrotype = value[value.length - 1];\r\n        let electrotype = this.ammeter.electrotype;\r\n        this.isMobileBase = electrotype > 1400 ? true : false;\r\n        if (electrotype == 1411 || electrotype == 1412) {\r\n          //控制产权归属\r\n          this.ammeter.property = 2;\r\n          this.propertyReadonly = true;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else if (electrotype == 1421 || electrotype == 1422) {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = 4;\r\n          this.ruleValidate.property = [\r\n            { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else if (electrotype == 1431 || electrotype == 1432) {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = 1 + \"\";\r\n          this.ruleValidate.property = [\r\n            { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = null;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n          this.ruleValidate.property = [\r\n            { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        }\r\n        let stationtype = this.ammeter.stationtype;\r\n        if (electrotype === 111 || electrotype === 112 || electrotype === 113) {\r\n          this.isClassification = true;\r\n          if (stationtype !== 10001) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 121 || electrotype === 112) {\r\n          if (stationtype !== 10003 && stationtype !== 10004) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 131 || electrotype === 132 || electrotype === 133) {\r\n          if (stationtype !== 10005) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 1411 || electrotype === 1412) {\r\n          if (\r\n            stationtype !== 10002 ||\r\n            (stationtype == 10002 && this.propertyright !== 3)\r\n          ) {\r\n            this.clearStation();\r\n          }\r\n        } else if (\r\n          electrotype === 1421 ||\r\n          electrotype === 1422 ||\r\n          electrotype === 1431 ||\r\n          electrotype === 1432\r\n        ) {\r\n          if (stationtype !== 10002) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 2) {\r\n          this.isClassification = true;\r\n        }\r\n        if (this.configVersion == \"sc\" && this.ammeter.stationaddresscode) {\r\n          //“51”开头铁塔站址编码控制\r\n          if (\r\n            [1411, 1412].includes(electrotype) &&\r\n            !this.ammeter.stationaddresscode.startsWith(\"51\")\r\n          ) {\r\n            this.clearStation();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    changedirectsupply(value) {\r\n      if (this.ammeter.directsupplyflag == 1) {\r\n        this.isRequireFlags = false;\r\n        this.ruleValidate.supplybureauammetercode = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      } else {\r\n        this.isRequireFlags = true;\r\n        this.ruleValidate.supplybureauammetercode = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n    },\r\n    clearStation() {\r\n      //清除局站信息\r\n      this.ammeter.stationName = null;\r\n      this.ammeter.stationcode = null;\r\n      this.ammeter.stationstatus = null;\r\n      this.ammeter.stationtype = null;\r\n      this.ammeter.stationaddress = null;\r\n      this.ammeter.stationaddresscode = null;\r\n    },\r\n\r\n    //选择所属部门开始\r\n    chooseResponseCenter(index, params, electroRowNum) {\r\n      this.chooseIndex = index;\r\n      this.electroRowNum = electroRowNum;\r\n      if (index == 1 || index == 2) {\r\n        let types = this.ammeter.classifications;\r\n        if (types.length == 0) {\r\n          this.$Modal.warning({ title: \"温馨提示\", content: \"请先选择用电类型！\" });\r\n          return;\r\n        } else if (this.ammeter.ammeteruse == null) {\r\n          this.$Modal.warning({ title: \"温馨提示\", content: \"请先选择电表用途！\" });\r\n          return;\r\n        } else {\r\n          if (this.ammeter.company == null) {\r\n            this.$Message.info(\"请先选择分公司\");\r\n            return;\r\n          }\r\n          this.ammeter.electrotype = types[types.length - 1];\r\n          this.$refs.stationModal.ammeterid = this.ammeter.id;\r\n          this.$refs.stationModal.initDataList(\r\n            this.ammeter.electrotype,\r\n            0,\r\n            this.ammeter.ammeteruse,\r\n            this.ammeter.company,\r\n            params\r\n          ); //局站\r\n          // }\r\n        }\r\n      } else {\r\n        if (this.ammeter.company == null) {\r\n          this.$Message.info(\"请先选择分公司\");\r\n          return;\r\n        }\r\n        this.$refs.countryModal.choose(this.ammeter.company); //所属部门\r\n      }\r\n    },\r\n    //部门\r\n    getDataFromModal(data) {\r\n      this.ammeter.country = data.id;\r\n      this.ammeter.countryName = data.name;\r\n      //选择所属部门结束\r\n    },\r\n    //\r\n    getDataFromModalObject(data, flag) {\r\n      this.handleChooseSup(data); // 传 true 设置 回调值\r\n    },\r\n    onChange(v) {\r\n      this.receiptaccountnameList.forEach((item) => {\r\n        if (item.koinh === v) {\r\n          this.ammeter.receiptaccountbank = item.banka;\r\n          this.ammeter.receiptaccounts = item.bankn;\r\n        }\r\n      });\r\n    },\r\n    //选择对方单位\r\n    handleChooseSup(data) {\r\n      if (!data) {\r\n        this.$refs.chooseModalSup.choose(1); //打开模态框\r\n      } else {\r\n        (this.ammeter.userunit = data.name),\r\n          getBankCard({ lifnr: data.id }).then((res) => {\r\n            this.receiptaccountnameList = res.data.rows;\r\n          });\r\n      }\r\n    },\r\n    //获取局站数据 flag=true需要验证实际报账个数,flag=false不需要验证实际报账个数\r\n    getDataFromStationModal(data, flag) {\r\n      this.ischeckStation = flag;\r\n      if (this.chooseIndex == 2) {\r\n        this.electro.data[this.electroRowNum].stationId = data.id;\r\n        this.electro.data[this.electroRowNum].stationName = data.stationname;\r\n      } else {\r\n        this.propertyright = data.propertyright;\r\n        this.ammeter.stationName = data.stationname;\r\n        this.ammeter.stationcode = data.id;\r\n        //所有的下拉默认值都要字符串才能显示了 跟之前完全变了\r\n        this.ammeter.stationstatus = Number(\r\n          data.status == undefined ? data.STATUS : data.status\r\n        );\r\n        this.ammeter.stationtype = data.stationtype;\r\n        this.ammeter.stationaddress = data.address;\r\n        this.ammeter.stationname5gr = data.stationname5gr;\r\n        this.ammeter.stationcode5gr = data.stationcodeintid;\r\n        this.ammeter.map = data.map;\r\n        this.ammeter.stationaddresscode = data.resstationcode;\r\n        this.ammeter.resstationcode = data.resstationcode;\r\n        //默认生成一条关联用电类型\r\n        let that = this;\r\n        listElectricType({ id: data.stationtype }).then((res) => {\r\n          that.electro.data = Object.assign([], res.data.rows);\r\n          this.electro.data[0].stationId = data.id;\r\n          this.electro.data[0].stationName = data.stationname;\r\n          this.electro.data[0]._disabled = true;\r\n        });\r\n      }\r\n    },\r\n\r\n    /*添加电表关联用电类型比率*/\r\n    addElectricType() {\r\n      this.$refs.selectElectricType.initElectricType();\r\n    },\r\n\r\n    /*移除选中的用电类型比率*/\r\n    removeElectricType() {\r\n      let rows = this.$refs.ammeterTable.getSelection();\r\n      let datas = this.electro.data;\r\n      rows.forEach((item) => {\r\n        if (item._index != undefined) {\r\n          datas.splice(item._index, 1);\r\n        } else {\r\n          datas.forEach((data) => {\r\n            if (data.id === item.id) {\r\n              let index = datas.indexOf(data);\r\n              datas.splice(index, 1);\r\n            }\r\n          });\r\n        }\r\n      });\r\n      this.electro.data = datas;\r\n    },\r\n\r\n    /* 设置用电类型列表*/\r\n    setElectricData: function (data) {\r\n      let origin = this.electro.data;\r\n      if (origin.length < 1) {\r\n        this.electro.data = data;\r\n      } else {\r\n        let tem = data;\r\n        for (let j = 0; j < origin.length; j++) {\r\n          for (let i = 0; i < data.length; i++) {\r\n            let typeId =\r\n              origin[j].electroTypeId != undefined\r\n                ? origin[j].electroTypeId\r\n                : origin[j].id;\r\n            if (data[i].id === typeId) {\r\n              tem.splice(tem.indexOf(data[i]), 1);\r\n            }\r\n          }\r\n        }\r\n        this.electro.data = this.electro.data.concat(tem);\r\n      }\r\n    },\r\n\r\n    //用电类型比例校验\r\n    checkElectricTypeItem() {\r\n      let items = this.electro.data;\r\n      //当“用电类型”选择“111 A类机楼（机房），112 B类机楼（机房），113 C类机楼（机房） ”或“2 管理办公用电”时，才需填用电类型分比且必填，用电类型比例之和必须等于100%\r\n      if (\r\n        this.ammeter.electrotype === 111 ||\r\n        this.ammeter.electrotype === 112 ||\r\n        this.ammeter.electrotype === 113 ||\r\n        this.ammeter.electrotype === 2\r\n      ) {\r\n        let sumRatio = items.reduce((total, item) => {\r\n          return total + item.ratio;\r\n        }, 0);\r\n        if (sumRatio !== 100) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型所占比例和必须为100%，当前值为\" + sumRatio + \"%\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    startFlow(data) {\r\n      let busiTitle = \"新增电表(\" + data.projectname + \")审批\";\r\n      this.workFlowParams = {\r\n        busiId: data.id,\r\n        busiAlias: \"ADD_AMM\",\r\n        busiTitle: busiTitle,\r\n      };\r\n      let that = this;\r\n      setTimeout(function () {\r\n        that.$refs.clwfbtn.onClick();\r\n      }, 200);\r\n    },\r\n    doWorkFlow(data) {\r\n      //流程回调\r\n      this.loading = false;\r\n      this.closeTag({ route: this.$route });\r\n      if (data == 0) {\r\n        this.warn();\r\n      }\r\n    },\r\n    /*选择电表/协议*/\r\n    addAmmeterProtocol() {\r\n      this.$refs.selectAmmeterProtocol.initDataList(1, null);\r\n    },\r\n    /* 选择电表户号/协议编号*/\r\n    setAmmeterProrocolData: function (data) {\r\n      this.ammeter.parentId = data.id;\r\n      if (data.protocolname != null && data.protocolname.length != 0) {\r\n        this.ammeter.parentCode = data.protocolname;\r\n      } else {\r\n        this.ammeter.parentCode = data.ammetername;\r\n      }\r\n    },\r\n    /*选择客户*/\r\n    addCustomer() {\r\n      this.$refs.customerList.choose(2); //打开模态框\r\n    },\r\n    getDataFromCustomerModal: function (data) {\r\n      this.ammeter.customerId = data.id;\r\n      this.ammeter.customerName = data.name;\r\n    },\r\n    //选择包干的时候修改默认包干类型\r\n    updatepackagetype() {\r\n      let data = this.ammeter;\r\n      data.packagetype = null;\r\n    },\r\n    iszgzchange() {\r\n      if (this.ammeter.iszgz == \"1\") {\r\n        this.ammeter.directsupplyflag = 1;\r\n        this.iszgzOnly = true;\r\n      } else {\r\n        this.iszgzOnly = false;\r\n      }\r\n    },\r\n    chooseoldammetername() {\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.status = 0;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.ammeteruse = 1;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.type = 3;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.company = this.ammeter.company;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.country = this.ammeter.country;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.directsupplyflag = 2;\r\n      this.$refs.chooseAmmeterModel.modal.show = true;\r\n      this.$Message.info(\"双击选择！！\");\r\n    },\r\n    getAmmeterModelModal(data) {\r\n      this.ammeter.oldammetername = data.name + \",\" + data.id;\r\n    },\r\n    projectNameChange(val) {\r\n      if (\r\n        !/^.*([^\\u0000-\\u00ff]+路).*$/.test(val) &&\r\n        !/^.*([^\\u0000-\\u00ff]*)([0-9]*号).*$/.test(val) &&\r\n        !/^.*([^\\u0000-\\u00ff]+楼电表).*$/.test(val)\r\n      ) {\r\n        this.$Message.info(\"温馨提示：集团要求格式为(**路**号**楼电表)\");\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    //直接从前台取\r\n    this.categorys = {\r\n      directsupplyflag: blist(\"directSupplyFlag\"),\r\n    };\r\n    this.propertyList = blist(\"property\");\r\n    this.initAmmeter(this.$route.query.id);\r\n\r\n    this.configVersion = this.$config.version;\r\n    if (this.configVersion != \"ln\" && this.configVersion != \"LN\") {\r\n      this.ruleValidate.ammetername = [\r\n        { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n      ];\r\n      this.ruleValidate.customerName = [\r\n        { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n      ];\r\n      this.ruleValidate.userunit = [\r\n        {\r\n          required: true,\r\n          message: \"不能为空\",\r\n\r\n          trigger: \"blur,change\",\r\n        },\r\n      ];\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.margin-right-width {\r\n  margin-right: 10px;\r\n}\r\n\r\n.testaa .ivu-row {\r\n  margin-left: 5px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.testaa .requireStar .ivu-form-item-label:before {\r\n  content: \"*\";\r\n  display: inline-block;\r\n  margin-right: 4px;\r\n  line-height: 1;\r\n  font-family: SimSun;\r\n  font-size: 12px;\r\n  color: #ed4014;\r\n}\r\n</style>\r\n"]}]}