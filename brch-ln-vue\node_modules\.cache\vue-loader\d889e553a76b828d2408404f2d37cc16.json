{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addPreOilAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addPreOilAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBVcGxvYWRGaWxlTW9kYWwgZnJvbSAiQC92aWV3L2FjY291bnQvdXBsb2FkRmlsZU1vZGFsIjsKaW1wb3J0IHsKICAgIHJlbW92ZU9pbEFjY291bnQsCiAgICBzZWxlY3RPaWxJZHMsCiAgICBzYXZlT2lsQWNjb3VudAp9IGZyb20gJ0AvYXBpL2NvYWxIZWF0T2lsQWNjb3VudCc7CmltcG9ydCBjaGVja1Jlc3VsdEFuZFJlc3BvbnNlIGZyb20gIkAvdmlldy9hY2NvdW50L2NoZWNrL2NoZWNrUmVzdWx0QW5kUmVzcG9uc2UiOwppbXBvcnQgY2hlY2tSZXN1bHQgZnJvbSAiQC92aWV3L2FjY291bnQvY2hlY2svY2hlY2tSZXN1bHQiOwppbXBvcnQgYWxhcm1DaGVjayBmcm9tICJAL3ZpZXcvYWNjb3VudC9jaGVjay9hbGFybUNoZWNrIjsKaW1wb3J0IHtnZXREYXRlcyx0ZXN0TnVtYmVyLH0gZnJvbSAnQC92aWV3L2FjY291bnQvcG93ZXJBY2NvdW50SGVscGVyJzsKaW1wb3J0IGF4aW9zIGZyb20gJ0AvbGlicy9hcGkucmVxdWVzdCc7CmltcG9ydCBTZWxlY3RBbW1ldGVyIGZyb20gIi4vc2VsZWN0QW1tZXRlciI7CmltcG9ydCB7X3ZlcmlmeV9GZWVTdGFydERhdGV9IGZyb20gJ0Avdmlldy9hY2NvdW50L1Bvd2VyQWNjb3VudEVzJzsKaW1wb3J0IEFkZEJpbGxQZXIgZnJvbSAiLi9hZGRQcmVPaWxCaWxsUHJlTW9kYWwiOwppbXBvcnQge3JlSm9pbkJpbGxwcmV9IGZyb20gJ0AvYXBpL2FjY291bnRCaWxsUGVyJzsKaW1wb3J0IHt3aWR0aHN0eWxlfSBmcm9tICJAL3ZpZXcvYnVzaW5lc3MvbXNzQWNjb3VudGJpbGwvbXNzQWNjb3VudGJpbGxkYXRhIjsKaW1wb3J0IENvbXBsZXRlZFByZU1vZGFsIGZyb20gIi4vY29tcGxldGVkUHJlTW9kYWwiOwppbXBvcnQgaW5kZXhEYXRhIGZyb20gJ0AvY29uZmlnL2luZGV4JwppbXBvcnQgQ291bnRyeU1vZGFsIGZyb20gIkAvdmlldy9iYXNlZGF0YS9hbW1ldGVyL2NvdW50cnlNb2RhbCI7CmltcG9ydCB7Z2V0VXNlcmRhdGEsZ2V0VXNlckJ5VXNlclJvbGUsZ2V0Q291bnRyeXNkYXRhLGdldENvdW50cnlCeVVzZXJJZH0gZnJvbSAnQC9hcGkvYmFzZWRhdGEvYW1tZXRlci5qcycKbGV0IGRhdGVzPWdldERhdGVzKCk7CmV4cG9ydCBkZWZhdWx0IHsKICAgIG5hbWU6ICdhZGRQcmVPaWxBY2NvdW50JywKICAgIGNvbXBvbmVudHM6IHtVcGxvYWRGaWxlTW9kYWwsIGFsYXJtQ2hlY2ssIGNoZWNrUmVzdWx0LCBjaGVja1Jlc3VsdEFuZFJlc3BvbnNlLENvbXBsZXRlZFByZU1vZGFsLCBTZWxlY3RBbW1ldGVyLEFkZEJpbGxQZXIsQ291bnRyeU1vZGFsfSwKICAgIGRhdGEoKSB7CiAgICAgICAgbGV0IHBob3RvID0gKGgsIHtyb3csIGluZGV4fSkgPT4gewogICAgICAgICAgICBsZXQgdGhhdCA9IHRoaXMKICAgICAgICAgICAgbGV0IHN0ciA9ICcnCiAgICAgICAgICAgIGlmIChyb3cucHJvamVjdG5hbWUgIT0gJ+Wwj+iuoScgJiYgcm93LnByb2plY3RuYW1lICE9ICflkIjorqEnKSB7CiAgICAgICAgICAgICAgICBzdHIgPSAn5LiK5Lyg6ZmE5Lu2JwogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiBoKCJkaXYiLCBbaCgidSIsIHsKICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgY2xpY2soKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIC8v5omT5byA5by55Ye65qGGCiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyb3cucHJvamVjdG5hbWUgIT0gJ+Wwj+iuoScgJiYgcm93LnByb2plY3RuYW1lICE9ICflkIjorqEnKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGF0LnVwbG9hZEZpbGUocm93KQogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9LCBzdHIpXSk7CiAgICAgICAgfTsKICAgICAgICByZXR1cm4gewogICAgICAgICAgICBzdWJtaXQ6W10sCiAgICAgICAgICAgIHN1Ym1pdDI6W10sCiAgICAgICAgICAgIHNob3dDaGVja01vZGVsOmZhbHNlLAogICAgICAgICAgICBzaG93SmhNb2RlbDpmYWxzZSwKICAgICAgICAgICAgc2hvd0FsYXJtTW9kZWw6ZmFsc2UsCiAgICAgICAgICAgIGZvcm1JdGVtV2lkdGg6IHdpZHRoc3R5bGUsCiAgICAgICAgICAgIHZlcnNpb246JycsCiAgICAgICAgICAgIGRhdGVMaXN0OmRhdGVzLAogICAgICAgICAgICBmaWx0ZXJDb2xsOiB0cnVlLC8v5pCc57Si6Z2i5p2/5bGV5byACiAgICAgICAgICAgIGVkaXRJbmRleDogLTEsLy/lvZPliY3nvJbovpHooYwKICAgICAgICAgICAgY29sdW1uc0luZGV4Oi0xLC8v5b2T5YmN57yW6L6R5YiXCiAgICAgICAgICAgIG15U3R5bGU6W10sLy/moLflvI8KICAgICAgICAgICAgZWRpdE9pbFVzZUJvZHk6JycsCiAgICAgICAgICAgIGVkaXRGZWVTdGFydERhdGU6JycsCiAgICAgICAgICAgIGVkaXRPaWxBbW91bnQ6JycsCiAgICAgICAgICAgIGVkaXRQYWlkTW9uZXk6JycsCiAgICAgICAgICAgIHNwaW5TaG93OmZhbHNlLC8v6YGu572pCiAgICAgICAgICAgIGVkaXRyZW1hcms6JycsCiAgICAgICAgICAgIGNvbXBhbmllczpbXSwKICAgICAgICAgICAgZGVwYXJ0bWVudHM6W10sCiAgICAgICAgICAgIGlzQWRtaW46ZmFsc2UsCiAgICAgICAgICAgIGNvbXBhbnk6bnVsbCwvL+eUqOaIt+m7mOiupOWFrOWPuAogICAgICAgICAgICBjb3VudHJ5Om51bGwsLy/nlKjmiLfpu5jorqTmiYDlsZ7pg6jpl6gKICAgICAgICAgICAgY291bnRyeU5hbWU6bnVsbCwvL+eUqOaIt+m7mOiupOaJgOWxnumDqOmXqAogICAgICAgICAgICBhY2NvdW50T2JqOnsKICAgICAgICAgICAgICAgIGFjY291bnRubzpkYXRlc1sxXS5jb2RlLC8v5pyf5Y+3LOm7mOiupOW9k+WJjeaciAogICAgICAgICAgICAgICAgY29tcGFueToiIiwvL+WIhuWFrOWPuAogICAgICAgICAgICAgICAgY291bnRyeToiIiwvL+aJgOWxnumDqOmXqAogICAgICAgICAgICAgICAgb2lsVXNlQm9keTpudWxsLC8v55So6IO95Li75L2TCiAgICAgICAgICAgICAgICBvaWxBY2NvdW50VHlwZTogMiwKICAgICAgICAgICAgICAgIGNvdW50cnlOYW1lOiAiIiwKCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHRiQWNjb3VudDogewogICAgICAgICAgICAgICAgZGlzcEtleTogMCwKICAgICAgICAgICAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAgICAgICAgICAgY29sdW1uczogW10sCiAgICAgICAgICAgICAgICB0YWlsQ29sdW1uOiBbCiAgICAgICAgICAgICAgICAgICAge3R5cGU6ICdzZWxlY3Rpb24nLCB3aWR0aDogNjAsIGFsaWduOiAnY2VudGVyJyx9LAogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLmnJ/lj7ciLAogICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJhY2NvdW50Tm8iLAogICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsCiAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMjAsCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi55So5rK55Li75L2TIiwKICAgICAgICAgICAgICAgICAgICAgICAgc2xvdDogIm9pbFVzZUJvZHkiLAogICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsCiAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAyMDAsCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi6LS555So5Y+R55Sf5pelIiwKICAgICAgICAgICAgICAgICAgICAgICAgc2xvdDogImZlZVN0YXJ0RGF0ZSIsCiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwKICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEyMCwKICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLmsrnph48oTCkiLAogICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAib2lsQW1vdW50IiwKICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLAogICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTIwLAogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIuWNleS7tyjlhYMvTCkiLAogICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJ1bml0UHJpY2UiLAogICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsCiAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMjAsCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi6YeR6aKdKOWFgykiLAogICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAicGFpZE1vbmV5IiwKICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLAogICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTIwLAogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAge3RpdGxlOiAi6ZmE5Lu2IiwgYWxpZ246ICJjZW50ZXIiLCByZW5kZXI6IHBob3RvLCB3aWR0aDogMTMwfSwKICAgICAgICAgICAgICAgICAgICB7dGl0bGU6ICLlpIfms6giLCBzbG90OiAicmVtYXJrIixhbGlnbjogImNlbnRlciIsIHdpZHRoOiAyOTB9LAogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgIGRhdGE6IFtdCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHBhZ2VUb3RhbDogMCwKICAgICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgICAgcGFnZVNpemU6IDEwLC8v5b2T5YmN6aG1CiAgICAgICAgfQogICAgfSwKICAgIG1ldGhvZHM6IHsKICAgICAgICB1cGxvYWRGaWxlKHJvdykgewogICAgICAgICAgICBjb25zb2xlLmxvZyhyb3csICJyb3ciKTsKICAgICAgICAgICAgLy8gbGV0IGlkOwogICAgICAgICAgICAvLyBpZighcm93LmlkMikgewogICAgICAgICAgICAvLyAgICAgZWRpdEFtbWV0ZXIoJycsIDApLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgLy8gICAgICAgICBkZWJ1Z2dlcgogICAgICAgICAgICAvLyAgICAgICAgIGNvbnNvbGUubG9nKHJlcywgInJlcyIpOwogICAgICAgICAgICAvLyAgICAgICAgIHJvdy5pZDIgPSByZXMuZGF0YS5pZDsKCiAgICAgICAgICAgIC8vICAgICAgICAgdGhpcy5pZDIgPSByZXMuZGF0YS5pZAogICAgICAgICAgICAvLyAgICAgICAgIC8vIGRlYnVnZ2VyCiAgICAgICAgICAgIC8vICAgICAgICAgLy8gdGhpcy5maWxlUGFyYW0uYnVzaUlkID0gOwogICAgICAgICAgICAvLyAgICAgICAgIHRoaXMuJHJlZnMudXBsb2FkRmlsZU1vZGFsLmNob29zZShyb3cuaWQyICsgJycpOwogICAgICAgICAgICAvLyAgICAgfSkKICAgICAgICAgICAgLy8gfWVsc2UgewoKICAgICAgICAgICAgaWYocm93LmlkKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLnVwbG9hZEZpbGVNb2RhbC5jaG9vc2Uocm93LmlkICsgJycpOwogICAgICAgICAgICB9ZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6K+35YWI5L+d5a2Y5ZCO5YaN5LiK5Lyg5paH5Lu277yBIik7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgLy8gfQogICAgICAgICAgICAvLyBjb25zb2xlLmxvZyhyb3csICJyb3ciKTsKICAgICAgICB9LAogICAgICAgIHNlbGVjdENoYW5nZSgpewogICAgICAgICAgICBsZXQgdGhhdCA9IHRoaXM7CiAgICAgICAgICAgIGlmICh0aGF0LmFjY291bnRPYmouY29tcGFueSAhPSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgICAgIGlmKHRoYXQuYWNjb3VudE9iai5jb21wYW55ID09ICItMSIpewogICAgICAgICAgICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb3VudHJ5ID0gLTE7CiAgICAgICAgICAgICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnlOYW1lID0gbnVsbDsKICAgICAgICAgICAgICAgIH1lbHNlewogICAgICAgICAgICAgICAgICAgIGdldENvdW50cnlCeVVzZXJJZCh0aGF0LmFjY291bnRPYmouY29tcGFueSkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5kZXBhcnRtZW50cy5sZW5ndGggIT0gMCl7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeSA9IHJlcy5kYXRhLmRlcGFydG1lbnRzWzBdLmlkOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnlOYW1lID0gcmVzLmRhdGEuZGVwYXJ0bWVudHNbMF0ubmFtZTsKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICAvL+mAieaLqeaJgOWxnumDqOmXqOW8gOWniwogICAgICAgIGNob29zZVJlc3BvbnNlQ2VudGVyKCkgewogICAgICAgICAgICBpZih0aGlzLmFjY291bnRPYmouY29tcGFueSA9PSBudWxsIHx8IHRoaXMuYWNjb3VudE9iai5jb21wYW55ID09ICItMSIgKXsKICAgICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbygi6K+35YWI6YCJ5oup5YiG5YWs5Y+4Iik7cmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuJHJlZnMuY291bnRyeU1vZGFsLmNob29zZSh0aGlzLmFjY291bnRPYmouY29tcGFueSk7Ly/miYDlsZ7pg6jpl6gKICAgICAgICB9LAogICAgICAgIGdldERhdGFGcm9tTW9kYWwoZGF0YSkgewogICAgICAgICAgICB0aGlzLmFjY291bnRPYmouY291bnRyeSA9IGRhdGEuaWQ7CiAgICAgICAgICAgIHRoaXMuYWNjb3VudE9iai5jb3VudHJ5TmFtZSA9IGRhdGEubmFtZTsKICAgICAgICAgICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jnu5PmnZ8KICAgICAgICB9LAogICAgICAgIGdldFVzZXJEYXRhKCl7CiAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpczsKICAgICAgICAgICAgZ2V0VXNlcmRhdGEoKS50aGVuKHJlcyA9PiB7Ly/lvZPliY3nmbvlvZXnlKjmiLfmiYDlnKjlhazlj7jlkozmiYDlsZ7pg6jpl6gKICAgICAgICAgICAgICAgIGlmKHJlcy5kYXRhLmNvbXBhbmllcy5sZW5ndGggIT0gMCl7CiAgICAgICAgICAgICAgICAgICAgbGV0IGNvbXBhbmllcyA9IHJlcy5kYXRhLmNvbXBhbmllczsKICAgICAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5jb21wYW5pZXNbMF0uaWQgPT0gIjI2MDAwMDAwMDAiKXsKICAgICAgICAgICAgICAgICAgICAgICAgY29tcGFuaWVzID0gdGhhdC5jb21wYW5pZXM7CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIHRoYXQuY29tcGFueSA9IGNvbXBhbmllc1swXS5pZDsKICAgICAgICAgICAgICAgICAgICB0aGF0LmFjY291bnRPYmouY29tcGFueSA9IGNvbXBhbmllc1swXS5pZDsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIGlmKHJlcy5kYXRhLmRlcGFydG1lbnRzLmxlbmd0aCAhPSAwKXsKICAgICAgICAgICAgICAgICAgICBsZXQgZGVwYXJ0bWVudHMgPSByZXMuZGF0YS5kZXBhcnRtZW50czsKICAgICAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5jb21wYW5pZXNbMF0uaWQgPT0gIjI2MDAwMDAwMDAiICYmIHRoYXQuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApewogICAgICAgICAgICAgICAgICAgICAgICBkZXBhcnRtZW50cyA9IHRoYXQuZGVwYXJ0bWVudHMKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgdGhhdC5jb3VudHJ5ID0gZGVwYXJ0bWVudHNbMF0uaWQ7CiAgICAgICAgICAgICAgICAgICAgdGhhdC5jb3VudHJ5TmFtZSA9IGRlcGFydG1lbnRzWzBdLm5hbWU7CiAgICAgICAgICAgICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnkgPSBOdW1iZXIoZGVwYXJ0bWVudHNbMF0uaWQpOwogICAgICAgICAgICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb3VudHJ5TmFtZSA9IGRlcGFydG1lbnRzWzBdLm5hbWU7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgdGhhdC5wYWdlTnVtID0gMQogICAgICAgICAgICAgICAgdGhhdC5nZXRBY2NvdW50TWVzc2FnZXMoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKICAgICAgICBzZWFyY2hMaXN0KCl7CiAgICAgICAgICAgIGlmKHRoaXMuYWNjb3VudE9iai5jb3VudHJ5TmFtZSA9PSAiIil7CiAgICAgICAgICAgICAgICB0aGlzLmFjY291bnRPYmouY291bnRyeSA9ICItMSI7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy5wYWdlTnVtID0gMTsKICAgICAgICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKQogICAgICAgIH0sCiAgICAgICAgYWNjb3VudG5vQ2hhbmdlKCl7CiAgICAgICAgICAgIHRoaXMuc2VhcmNoTGlzdCgpCiAgICAgICAgfSwKICAgICAgICAvL+eCueWHu+S/neWtmAogICAgICAgIHByZXNlcnZlKCkgewogICAgICAgICAgICBsZXQgZGF0YUwgPSB0aGlzLiRyZWZzLmFjY291bnRFc1RhYmxlLmdldFNlbGVjdGlvbigpOwogICAgICAgICAgICBsZXQgYiA9IGZhbHNlOwogICAgICAgICAgICBsZXQgYXJyYXkgPSBbXTsKICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhTC5sZW5ndGg7IGkgKyspIHsKICAgICAgICAgICAgICAgIGIgPSB0cnVlOwogICAgICAgICAgICAgICAgYXJyYXkucHVzaChkYXRhTFtpXSkKICAgICAgICAgICAgfQogICAgICAgICAgICBpZihiKXsKICAgICAgICAgICAgICAgIHRoaXMuc3VibWl0RGF0YShhcnJheSk7CiAgICAgICAgICAgIH1lbHNlIHsKICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCfmsqHmnInlj6/kv53lrZjmlbDmja4nKQogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBzdWJtaXRDaGFuZ2UoaW5kZXhMaXN0KXsKICAgICAgICAgICAgbGV0IGRhdGE9W107CiAgICAgICAgICAgIHRoaXMuc3VibWl0Mi5tYXAoKGl0ZW0saW5kZXgpPT57CiAgICAgICAgICAgICAgICBpbmRleExpc3QubWFwKChpdGVtMik9PnsKICAgICAgICAgICAgICAgICAgICBpZihpbmRleD09aXRlbTIpewogICAgICAgICAgICAgICAgICAgICAgICBkYXRhLnB1c2goaXRlbSkKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9KTsKICAgICAgICAgICAgdGhpcy5zdWJtaXQ9ZGF0YQogICAgICAgIH0sCgogICAgICAgIC8v5o+Q5Lqk5pWw5o2uCiAgICAgICAgc3VibWl0RGF0YShkYXRhKXsKICAgICAgICAgICAgbGV0IGEgPSBbXTsKICAgICAgICAgICAgbGV0IHRoYXQ9dGhpczsKICAgICAgICAgICAgaWYoZGF0YSAhPSBudWxsICYmIGRhdGEubGVuZ3RoID4gMCl7CiAgICAgICAgICAgICAgICBsZXQgbnVtYmVyID0gMDsKICAgICAgICAgICAgICAgIGxldCBzdWJtaXREYXRhID0gW107CiAgICAgICAgICAgICAgICBsZXQgc3RyID0gJyc7CiAgICAgICAgICAgICAgICBsZXQgYWNjb3VudG5vID0gdGhpcy5hY2NvdW50T2JqLmFjY291bnRubzsKICAgICAgICAgICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICAgIC8vIOagoemqjOaVsOaNrgogICAgICAgICAgICAgICAgICAgIGlmKGl0ZW0uaWQgPT0gbnVsbCl7CiAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0uYWNjb3VudG5vID0gYWNjb3VudG5vCiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIGEucHVzaChpdGVtLmlkKTsKICAgICAgICAgICAgICAgICAgICBpdGVtLm9pbEFjY291bnRUeXBlID0gMjsKICAgICAgICAgICAgICAgICAgICBzdWJtaXREYXRhLnB1c2goaXRlbSk7CiAgICAgICAgICAgICAgICAgICAgbnVtYmVyICsrOwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB0aGF0Lmlkcz1hOwogICAgICAgICAgICAgICAgaWYoc3RyLmxlbmd0aCA+IDApewogICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKHN0cikKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIGlmKHN1Ym1pdERhdGEubGVuZ3RoID4gMCl7CiAgICAgICAgICAgICAgICAgICAgc2F2ZU9pbEFjY291bnQoc3VibWl0RGF0YSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5jb2RlID09IDApIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbyh7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGVudDogJ+aPkOekuu+8muaIkOWKn+S/neWtmCAnICsgcmVzLmRhdGEubnVtICsgJyDmnaHmlbDmja4nLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxMCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbG9zYWJsZTogdHJ1ZQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOwogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgYWRkTmV3T2lsQWNjb3VudCgpIHsKICAgICAgICAgICAgY29uc3QgY3VycmVudERhdGUgPSBuZXcgRGF0ZSgpOwogICAgICAgICAgICBjb25zdCBjdXJyZW50WWVhciA9IGN1cnJlbnREYXRlLmdldEZ1bGxZZWFyKCk7CiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRNb250aCA9IGN1cnJlbnREYXRlLmdldE1vbnRoKCkgKyAxOwogICAgICAgICAgICBpZiAobnVsbCA9PSB0aGlzLnRiQWNjb3VudC5kYXRhKSB7CiAgICAgICAgICAgICAgICB0aGlzLnRiQWNjb3VudC5kYXRhID0gW107CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy50YkFjY291bnQuZGlzcEtleSsrOwogICAgICAgICAgICB0aGlzLnRiQWNjb3VudC5kYXRhLnVuc2hpZnQoewogICAgICAgICAgICAgICAgLy8gYWNjb3VudE5vOmRhdGVzWzFdLmNvZGUsCiAgICAgICAgICAgICAgICAvLyBhY2NvdW50Tm86IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm8sCiAgICAgICAgICAgICAgICBhY2NvdW50Tm86ICh0aGlzLmFjY291bnRPYmouYWNjb3VudG5vID09IC0xIHx8IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm8gPT0gdW5kZWZpbmVkKSA/IGN1cnJlbnRZZWFyKyIiK2N1cnJlbnRNb250aDogdGhpcy5hY2NvdW50T2JqLmFjY291bnRubywKICAgICAgICAgICAgICAgIG9pbFVzZUJvZHk6ICIiLAogICAgICAgICAgICAgICAgZmVlU3RhcnREYXRlOiIiLAogICAgICAgICAgICAgICAgb2lsQW1vdW50OiAiMCIsCiAgICAgICAgICAgICAgICB1bml0UHJpY2U6ICIwIiwKICAgICAgICAgICAgICAgIHBhaWRNb25leToiMCIsCiAgICAgICAgICAgICAgICByZW1hcms6IiIsCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICB0aGlzLnRiQWNjb3VudC5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMubXlTdHlsZS5wdXNoKHsKICAgICAgICAgICAgICAgIG9pbFVzZUJvZHk6ICdteXNwYW4nLAogICAgICAgICAgICAgICAgZmVlU3RhcnREYXRlOiAnbXlzcGFuJywKICAgICAgICAgICAgICAgIG9pbEFtb3VudDogJ215c3BhbicsCiAgICAgICAgICAgICAgICBwYWlkTW9uZXk6ICdteXNwYW4nLAogICAgICAgICAgICAgICAgcmVtYXJrOiAnbXlzcGFuJywKCiAgICAgICAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIC8v6aqM6K+B6ZSZ6K+v5by55Ye65o+Q56S65qGGCiAgICAgICAgZXJyb3JUaXBzKHN0cil7CiAgICAgICAgICAgIHRoaXMuJE5vdGljZS5lcnJvcih7CiAgICAgICAgICAgICAgICB0aXRsZTogJ+aPkOekuicsCiAgICAgICAgICAgICAgICBkZXNjOiBzdHIsCiAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTAKICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKICAgICAgICBoYW5kbGVQYWdlKHZhbHVlKSB7CiAgICAgICAgICAgIGxldCBiID0gZmFsc2U7CiAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YQogICAgICAgICAgICBsZXQgYXJyYXkgPSBbXTsKICAgICAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICBpZihpdGVtLmVkaXRUeXBlID09IDEpewogICAgICAgICAgICAgICAgICAgIGIgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgIGFycmF5LnB1c2goaXRlbSkKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGlmKGIpewogICAgICAgICAgICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7CiAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfmj5DnpLonLAogICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICc8cD7mgqjmnInlt7LnvJbovpHkv6Hmga/ov5jmsqHmnInkv53lrZjvvIzmmK/lkKbkv53lrZjvvJ88L3A+JywKICAgICAgICAgICAgICAgICAgICBvbk9rOiAoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc3VibWl0RGF0YShhcnJheSk7CiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICBvbkNhbmNlbDogKCkgPT4gewoKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgdGhpcy5wYWdlTnVtID0gdmFsdWU7CiAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7CiAgICAgICAgfSwKICAgICAgICBoYW5kbGVQYWdlU2l6ZSh2YWx1ZSkgewogICAgICAgICAgICBsZXQgYiA9IGZhbHNlOwogICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGEKICAgICAgICAgICAgbGV0IGFycmF5ID0gW107CiAgICAgICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgaWYoaXRlbS5lZGl0VHlwZSA9PSAxKXsKICAgICAgICAgICAgICAgICAgICBiID0gdHJ1ZTsKICAgICAgICAgICAgICAgICAgICBhcnJheS5wdXNoKGl0ZW0pCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBpZihiKXsKICAgICAgICAgICAgICAgIHRoaXMuJE1vZGFsLmNvbmZpcm0oewogICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn5o+Q56S6JywKICAgICAgICAgICAgICAgICAgICBjb250ZW50OiAnPHA+5oKo5pyJ5bey57yW6L6R5L+h5oGv6L+Y5rKh5pyJ5L+d5a2Y77yM5piv5ZCm5L+d5a2Y77yfPC9wPicsCiAgICAgICAgICAgICAgICAgICAgb25PazogKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnN1Ym1pdERhdGEoYXJyYXkpOwogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgb25DYW5jZWw6ICgpID0+IHsKCiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy5wYWdlU2l6ZSA9IHZhbHVlOwogICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOwogICAgICAgIH0sCiAgICAgICAgLy/lkJHlkI7lj7Dor7fmsYLmlbDmja4KICAgICAgICBnZXRBY2NvdW50TWVzc2FnZXMoKSB7CiAgICAgICAgICAgIGxldCBwb3N0RGF0YSA9IHRoaXMuYWNjb3VudE9iajsKICAgICAgICAgICAgcG9zdERhdGEucGFnZU51bSA9IHRoaXMucGFnZU51bTsKICAgICAgICAgICAgcG9zdERhdGEucGFnZVNpemUgPSB0aGlzLnBhZ2VTaXplOwogICAgICAgICAgICBsZXQgcmVxID0gewogICAgICAgICAgICAgICAgdXJsIDogIi9idXNpbmVzcy9vaWwvYWNjb3VudC9saXN0IiwKICAgICAgICAgICAgICAgIG1ldGhvZCA6ICJnZXQiLAogICAgICAgICAgICAgICAgcGFyYW1zIDogcG9zdERhdGEKICAgICAgICAgICAgfTsKICAgICAgICAgICAgdGhpcy50YkFjY291bnQubG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgIGF4aW9zLnJlcXVlc3QocmVxKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICB0aGlzLnRiQWNjb3VudC5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEpIHsKICAgICAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHJlcy5kYXRhLnJvd3M7CiAgICAgICAgICAgICAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0uZWRpdFR5cGUgPSAwOwogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgIGlmIChudWxsID09IGRhdGEpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy50YkFjY291bnQuZGF0YSA9IHt9OwogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudGJBY2NvdW50LmRhdGEgPSBkYXRhOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB0aGlzLnBhZ2VUb3RhbCA9IHJlcy5kYXRhLnRvdGFsIHx8IDA7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5zZXRNeVN0eWxlKHRoaXMudGJBY2NvdW50LmRhdGEubGVuZ3RoKTsKICAgICAgICAgICAgICAgICAgICB0aGlzLmVkaXRJbmRleCA9IC0xOwogICAgICAgICAgICAgICAgICAgIHRoaXMuY29sdW1uc0luZGV4ID0gLTE7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pLmNhdGNoKGVyciA9PiB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlcnIpOwogICAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIC8v6YeN572uCiAgICAgICAgb25SZXNldEhhbmRsZSgpewogICAgICAgICAgICB0aGlzLmFjY291bnRPYmogPSB7CiAgICAgICAgICAgICAgICBhY2NvdW50bm86bnVsbCwKICAgICAgICAgICAgICAgIGNvbXBhbnk6dGhpcy5jb21wYW55LAogICAgICAgICAgICAgICAgb2lsVXNlQm9keTpudWxsLAogICAgICAgICAgICAgICAgY291bnRyeTpOdW1iZXIodGhpcy5jb3VudHJ5KSwKICAgICAgICAgICAgICAgIG9pbEFjY291bnRUeXBlOjIsCiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKQogICAgICAgIH0sCiAgICAgICAgLy/orqHnrpfljZXku7cKICAgICAgICB1bml0UHJpY2Uocm93KXsKICAgICAgICAgICAgbGV0IHBhaWRNb25leSA9IHJvdy5wYWlkTW9uZXk7CiAgICAgICAgICAgIGxldCBvaWxBbW91bnQgPSByb3cub2lsQW1vdW50OwogICAgICAgICAgICBpZihwYWlkTW9uZXkgIT0gbnVsbCAmJiBvaWxBbW91bnQgIT0gbnVsbCl7CiAgICAgICAgICAgICAgICByb3cudW5pdHBpcmNlID0gcGFpZE1vbmV5L29pbEFtb3VudC50b0ZpeGVkKDIpOwogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICByZW1vdmUoKXsKICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLiRyZWZzLmFjY291bnRFc1RhYmxlLmdldFNlbGVjdGlvbigpOwogICAgICAgICAgICBpZihkYXRhID09IG51bGwgfHwgZGF0YS5sZW5ndGggPT09IDApewogICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoIuivt+mAieaLqeimgeWIoOmZpOeahOaVsOaNriIpCiAgICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7CiAgICAgICAgICAgICAgICB0aXRsZTogJ+aPkOekuicsCiAgICAgICAgICAgICAgICBjb250ZW50OiAnPHA+5piv5ZCm56Gu6K6k5Yig6Zmk6YCJ5Lit5L+h5oGv77yfPC9wPicsCiAgICAgICAgICAgICAgICBvbk9rOiAoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgbGV0IGIgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgIGxldCBpZHMgPSAnJzsKICAgICAgICAgICAgICAgICAgICBsZXQgdG90YWwgPSB0aGlzLnBhZ2VUb3RhbAogICAgICAgICAgICAgICAgICAgIGZvcihsZXQgaT0wO2k8ZGF0YS5sZW5ndGg7aSsrKXsKICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGl0ZW0gPSBkYXRhW2ldOwogICAgICAgICAgICAgICAgICAgICAgICBpZihpdGVtLmlkICE9IG51bGwgJiYgaXRlbS5pZC5sZW5ndGggPiAwKXsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmKGl0ZW0ucGFicmlpZCl7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYiA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWRzICs9IGl0ZW0uaWQgKyAnLCc7CiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgdGhpcy5wYWdlVG90YWwgPSB0b3RhbAogICAgICAgICAgICAgICAgICAgIGlmKGIpewogICAgICAgICAgICAgICAgICAgICAgICBpZihpZHMubGVuZ3RoID4gMCl7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZW1vdmVPaWxBY2NvdW50KGlkcykudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLmNvZGUgPT0gMCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfWVsc2UgewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygn6YCJ5Lit5L+h5oGv5Lit5pyJ5L+h5oGv6L+Y5rKh5pyJ6Lef5b2S6ZuG5Y2V6Kej6Zmk5YWz6IGU77yM6K+35YWI6Kej6Zmk5YWz6IGUJykKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgb25DYW5jZWw6ICgpID0+IHsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKICAgICAgICBvcGVuQWRkQmlsbFBlck1vZGFsKG5hbWUpIHsKICAgICAgICAgICAgaWYgKG5hbWUgPT09ICdjdXJyZW50JykgewogICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEFjY291bnQoKQogICAgICAgICAgICB9IGVsc2UgaWYgKG5hbWUgPT09ICdhbGwnKSB7CiAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkQWxsQWNjb3VudCgpCiAgICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIC8v5Yqg5YWl5b2S6ZuG5Y2V77yM5YWo6YOo5pyJ5pWI5Y+w6LSmCiAgICAgICAgc2VsZWN0ZWRBbGxBY2NvdW50KCl7CiAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpcwogICAgICAgICAgICB0aGF0LnNwaW5TaG93ID0gdHJ1ZTsKICAgICAgICAgICAgc2VsZWN0T2lsSWRzKHRoaXMuYWNjb3VudE9iaikudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgdGhhdC5zcGluU2hvdyA9IGZhbHNlOwogICAgICAgICAgICAgICAgaWYocmVzLmRhdGEubGVuZ3RoID09IDApewogICAgICAgICAgICAgICAgICAgIHRoYXQuZXJyb3JUaXBzKCfml6DmnInmlYjmlbDmja7lj6/liqDlhaXlvZLpm4bljZUnKQogICAgICAgICAgICAgICAgfWVsc2UgewogICAgICAgICAgICAgICAgICAgIGxldCBpZHMgPSBbXTsKICAgICAgICAgICAgICAgICAgICBmb3IobGV0IGk9MDtpPHJlcy5kYXRhLnJvd3MubGVuZ3RoO2krKyl7CiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBpdGVtID0gcmVzLmRhdGEucm93c1tpXTsKICAgICAgICAgICAgICAgICAgICAgICAgaWRzLnB1c2goaXRlbS5pZCkKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgdGhhdC4kcmVmcy5hZGRCaWxsUGVyLmluaXRBbW1ldGVyKGlkcywgMjEsdGhpcy5hY2NvdW50T2JqLmNvdW50cnkpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIHNlbGVjdGVkQWNjb3VudCgpewogICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMuJHJlZnMuYWNjb3VudEVzVGFibGUuZ2V0U2VsZWN0aW9uKCk7CiAgICAgICAgICAgIGxldCBiID0gMTsKICAgICAgICAgICAgaWYoZGF0YSA9PSBudWxsIHx8IGRhdGEubGVuZ3RoID09IDApewogICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+ivt+mAieaLqeimgeWKoOWFpeW9kumbhuWNleeahOWPsOi0picpCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBsZXQgaWRzID0gW107CiAgICAgICAgICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgICBpZihpdGVtLnN0YXR1cyA9PT0gNSl7CiAgICAgICAgICAgICAgICAgICAgICAgIGIgPSAzCiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIGlmKGl0ZW0uc3RhdHVzID09PSA0KXsKICAgICAgICAgICAgICAgICAgICAgICAgYj00OwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICBpZHMucHVzaChpdGVtLmlkKQogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICBpZihiID09PSAxKXsKICAgICAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLmFkZEJpbGxQZXIuaW5pdEFtbWV0ZXIoaWRzLDIxLHRoaXMuYWNjb3VudE9iai5jb3VudHJ5KTsKICAgICAgICAgICAgICAgIH1lbHNlIGlmKGIgPT09IDIpIHsKICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygn6YCJ5Lit55qE5Y+w6LSm5Lit5a2Y5Zyo5Li05pe25pWw5o2u77yM6K+35YWI5L+d5a2Y5YaN5Yqg5YWl5b2S6ZuG5Y2V77yBJykKICAgICAgICAgICAgICAgIH1lbHNlIGlmKGI9PT0zKXsKICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygn6YCA5Zue55qE5Y+w6LSm5LiN6IO95Yqg5YWl5YW25a6D5b2S6ZuG5Y2V77yM6K+354K55Ye7W+mHjeaWsOWKoOWFpeW9kumbhuWNlV3mjInpkq4nKQogICAgICAgICAgICAgICAgfWVsc2UgaWYoYj09PTQpewogICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCfpgInmi6nnmoTlj7DotKbmnInlt7LliqDlhaXlvZLpm4bljZXnmoTlj7DotKbvvIzkuI3og73liqDlhaXlhbbku5blvZLpm4bljZUnKQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBvcGVuQ29tcGxldGVkUHJlTW9kYWwoKXsKICAgICAgICAgICAgdGhpcy4kcmVmcy5jb21wbGV0ZWRQcmUuaW5pdEFtbWV0ZXIodGhpcy5hY2NvdW50T2JqLmNvdW50cnksMik7CiAgICAgICAgfSwKICAgICAgICBhZ2FpbkpvaW4oKXsKICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLiRyZWZzLmFjY291bnRFc1RhYmxlLmdldFNlbGVjdGlvbigpOwogICAgICAgICAgICBsZXQgYiA9IHRydWU7CiAgICAgICAgICAgIGlmKGRhdGEgPT0gbnVsbCB8fCBkYXRhLmxlbmd0aCA9PSAwKXsKICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCfor7fpgInmi6nopoHph43mlrDliqDlhaXlvZLpm4bljZXnmoTlj7DotKYnKQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgbGV0IGlkcyA9ICcnOwogICAgICAgICAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICAgICAgbGV0IHN0YXR1cyA9IGl0ZW0uc3RhdHVzOwogICAgICAgICAgICAgICAgICAgIGlmKHN0YXR1cyAhPSA1KXsKICAgICAgICAgICAgICAgICAgICAgICAgYiA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICBpZHMrPSBpdGVtLmlkICsnLCcKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgaWYoYil7CiAgICAgICAgICAgICAgICAgICAgcmVKb2luQmlsbHByZShpZHMpLnRoZW4oKHJlcykgPT57CiAgICAgICAgICAgICAgICAgICAgICAgIGlmKHJlcy5kYXRhLmNvZGU9PTApewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50Oifmj5DnpLrvvJrmk43kvZzmiJDlip8nICwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTAsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xvc2FibGU6IHRydWUKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKTsKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICB9ZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+WPquacieW3sumAgOWbnueahOWPsOi0puaJjeiDvemHjeaWsOWKoOWFpeW9kumbhuWNlScpCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIHJlZnJlc2goKXsKICAgICAgICAgICAgbGV0IG9iaiA9IHRoaXMKICAgICAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICBvYmouZ2V0QWNjb3VudE1lc3NhZ2VzKCkKICAgICAgICAgICAgfSwyMDApOwogICAgICAgIH0sCiAgICAgICAgdmFsaWRhdGUoKXsKICAgICAgICAgICAgaWYodGhpcy5jb2x1bW5zSW5kZXggIT0gNSl7CiAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lbnRlck9wZXJhdGUodGhpcy5jb2x1bW5zSW5kZXgpLmRhdGE7CiAgICAgICAgICAgICAgICBpZih2YWwpIHsKICAgICAgICAgICAgICAgICAgICBzd2l0Y2ggKHRoaXMuY29sdW1uc0luZGV4KSB7CiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGNhc2UgMToKICAgICAgICAgICAgICAgICAgICAgICAgLy8gICAgIHRoaXMudmFsaWRhdGVGZWVTdGFydERhdGUoKTsKICAgICAgICAgICAgICAgICAgICAgICAgLy8gICAgIGJyZWFrOwogICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlRmVlU3RhcnREYXRlKCk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy52YWxpZGF0ZU9pbEFtb3VudCgpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVQYWlkTW9uZXkoKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgICAgICAgICAvLyBjYXNlIDU6CiAgICAgICAgICAgICAgICAgICAgICAgIC8vICAgICB0aGlzLnZhbGlkYXRlRmVlU3RhcnREYXRlKCk7CiAgICAgICAgICAgICAgICAgICAgICAgIC8vICAgICBicmVhazsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIHZhbGlkYXRlRmVlU3RhcnREYXRlKCl7CiAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07CiAgICAgICAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRGZWVTdGFydERhdGU7CiAgICAgICAgICAgIGxldCByZXN1bHQgPSBfdmVyaWZ5X0ZlZVN0YXJ0RGF0ZShkYXRhLHZhbCk7CiAgICAgICAgICAgIC8vIGlmKHJlc3VsdCl7Ly/lpLHotKXlsLHlvLnlh7rmj5DnpLrlhoXlrrnvvIzlubblsIbmlbDmja7mgaLlpI3liJ3lp4vljJYKICAgICAgICAgICAgLy8gICAgIHRoaXMuZXJyb3JUaXBzKHJlc3VsdCkKICAgICAgICAgICAgLy8gfWVsc2V7CiAgICAgICAgICAgICAgICBkYXRhLmZlZVN0YXJ0RGF0ZSA9IHZhbDsKICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOwogICAgICAgICAgICAvLyB9CiAgICAgICAgfSwKICAgICAgICB2YWxpZGF0ZU9pbEFtb3VudCgpewogICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOwogICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lZGl0T2lsQW1vdW50OwogICAgICAgICAgICBpZiAoIXRlc3ROdW1iZXIodmFsKSkgewogICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+ivt+i+k+WFpeaVsOWtl++8gScpOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGRhdGEub2lsQW1vdW50ID0gdmFsOwogICAgICAgICAgICBkYXRhLnVuaXRQcmljZSA9IGRhdGEucGFpZE1vbmV5L2RhdGEub2lsQW1vdW50OwogICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsKICAgICAgICB9LAogICAgICAgIHZhbGlkYXRlUGFpZE1vbmV5KCl7CiAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07CiAgICAgICAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRQYWlkTW9uZXk7CiAgICAgICAgICAgIGlmICghdGVzdE51bWJlcih2YWwpKSB7CiAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygn6K+36L6T5YWl5pWw5a2X77yBJyk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgZGF0YS5wYWlkTW9uZXkgPSB2YWw7CiAgICAgICAgICAgIGRhdGEudW5pdFByaWNlID0gZGF0YS5wYWlkTW9uZXkvZGF0YS5vaWxBbW91bnQ7CiAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOwogICAgICAgICAgICAvLyB0aGlzLnVuaXRQcmljZShkYXRhKQogICAgICAgIH0sCiAgICAgICAgc2V0cmVtYXJrKCl7CiAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07CiAgICAgICAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRyZW1hcms7CiAgICAgICAgICAgIGRhdGEucmVtYXJrID0gdmFsOwogICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsKICAgICAgICB9LAogICAgICAgIHNldE9pbFVzZUJvZHkoKXsKICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsKICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdE9pbFVzZUJvZHk7CiAgICAgICAgICAgIGRhdGEub2lsVXNlQm9keSA9IHZhbDsKICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7CiAgICAgICAgfSwKICAgICAgICBzZXRNeVN0eWxlKGxlbmd0aCl7CiAgICAgICAgICAgIHRoaXMubXlTdHlsZT1bXTsKICAgICAgICAgICAgZm9yKHZhciBpPTA7aTxsZW5ndGg7aSsrKXsKICAgICAgICAgICAgICAgIHRoaXMubXlTdHlsZS5wdXNoKHsKICAgICAgICAgICAgICAgICAgICBvaWxVc2VCb2R5OiAnbXlzcGFuJywKICAgICAgICAgICAgICAgICAgICBmZWVTdGFydERhdGU6ICdteXNwYW4nLAogICAgICAgICAgICAgICAgICAgIG9pbEFtb3VudDogJ215c3BhbicsCiAgICAgICAgICAgICAgICAgICAgcGFpZE1vbmV5OiAnbXlzcGFuJywKICAgICAgICAgICAgICAgICAgICByZW1hcms6ICdteXNwYW4nLAogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIC8vc3BhbueCueWHu+S6i+S7tuWwhnNwYW7mjaLmiJDovpPlhaXmoYblubbkuJTojrflj5bnhKbngrkKICAgICAgICBzZWxlY3RDYWxsKHJvdyxpbmRleCxjb2x1bW5zLHN0cil7CiAgICAgICAgICAgIHRoaXMuZWRpdEZlZVN0YXJ0RGF0ZSA9IHJvdy5mZWVTdGFydERhdGU7CiAgICAgICAgICAgIHRoaXMuZWRpdE9pbFVzZUJvZHkgPSByb3cub2lsVXNlQm9keTsKICAgICAgICAgICAgdGhpcy5lZGl0T2lsQW1vdW50ID0gcm93Lm9pbEFtb3VudCA9PSBudWxsIHx8IHJvdy5vaWxBbW91bnQ9PT0wP251bGw6cm93Lm9pbEFtb3VudDsKICAgICAgICAgICAgdGhpcy5lZGl0UGFpZE1vbmV5ID0gcm93LnBhaWRNb25leTsKICAgICAgICAgICAgdGhpcy5lZGl0cmVtYXJrID0gcm93LnJlbWFyazsKICAgICAgICAgICAgdGhpcy5lZGl0SW5kZXggPSBpbmRleDsKICAgICAgICAgICAgdGhpcy5jb2x1bW5zSW5kZXggPSBjb2x1bW5zOwogICAgICAgICAgICBsZXQgYT10aGlzOwogICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgIGEuJHJlZnNbc3RyK2luZGV4K2NvbHVtbnNdLmZvY3VzKCk7CiAgICAgICAgICAgIH0sMjAwKTsKCiAgICAgICAgfSwKICAgICAgICAvL+i3s+i9rOWIsOS4i+S4gOagvAogICAgICAgIG5leHRDZWxsKGRhdGEpewogICAgICAgICAgICBsZXQgaW5kZXggPSBkYXRhLmVkaXRJbmRleDsKICAgICAgICAgICAgbGV0IGNvbHVtbnMgPSBkYXRhLmNvbHVtbnNJbmRleDsKICAgICAgICAgICAgbGV0IHJvdyA9ICcnOwogICAgICAgICAgICBpZihpbmRleCA9PT0gLTEgJiYgY29sdW1ucyA9PT0gLTEpewogICAgICAgICAgICAgICAgaW5kZXggPSAwOwogICAgICAgICAgICAgICAgY29sdW1ucyA9IDE7CiAgICAgICAgICAgIH1lbHNlIGlmKGluZGV4ID4gLTEgJiYgY29sdW1ucyA9PT0gNSl7CiAgICAgICAgICAgICAgICAvL+W9k+i3s+i9rOeahOacgOWQjuS4gOihjOacgOWQjuS4gOagvOeahOaXtuWAmQogICAgICAgICAgICAgICAgaWYgKGluZGV4ID49IGRhdGEucGFnZVNpemUgLSAxIHx8IGluZGV4ID49IGRhdGEucGFnZVRvdGFsIC0gMSkgewogICAgICAgICAgICAgICAgICAgIGluZGV4ID0gMDsKICAgICAgICAgICAgICAgIH1lbHNlewogICAgICAgICAgICAgICAgICAgIGluZGV4ICsrOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgY29sdW1ucyA9IDE7CiAgICAgICAgICAgIH1lbHNlewogICAgICAgICAgICAgICAgY29sdW1ucyArPSAxOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGRhdGEuZWRpdEluZGV4ID0gaW5kZXg7CiAgICAgICAgICAgIGRhdGEuY29sdW1uc0luZGV4ID0gY29sdW1uczsKICAgICAgICAgICAgcm93ID0gZGF0YS50YkFjY291bnQuZGF0YVtpbmRleF07CiAgICAgICAgICAgIGlmKHJvdyl7CiAgICAgICAgICAgICAgICBkYXRhLmVkaXRGZWVTdGFydERhdGUgPSByb3cuZmVlU3RhcnREYXRlOwogICAgICAgICAgICAgICAgZGF0YS5lZGl0T2lsVXNlQm9keSA9IHJvdy5vaWxVc2VCb2R5OwogICAgICAgICAgICAgICAgZGF0YS5lZGl0T2lsQW1vdW50ID0gcm93Lm9pbEFtb3VudCA9PSBudWxsIHx8IHJvdy5vaWxBbW91bnQ9PT0wP251bGw6cm93Lm9pbEFtb3VudDsKICAgICAgICAgICAgICAgIGRhdGEuZWRpdHJlbWFyayA9IHJvdy5yZW1hcms7CiAgICAgICAgICAgICAgICBkYXRhLmVkaXRQYWlkTW9uZXkgPSByb3cucGFpZE1vbmV5OwogICAgICAgICAgICB9CiAgICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgZGF0YS4kcmVmc1tkYXRhLmVudGVyT3BlcmF0ZShjb2x1bW5zKS5zdHIraW5kZXgrY29sdW1uc10uZm9jdXMoKTsKICAgICAgICAgICAgfSwyMDApOwogICAgICAgIH0sCiAgICAgICAgLy/moLnmja7liJflj7fov5Tlm57lr7nlupTnmoTliJflkI0KICAgICAgICBlbnRlck9wZXJhdGUobnVtYmVyKXsKICAgICAgICAgICAgbGV0IHN0ciA9ICcnOwogICAgICAgICAgICBsZXQgZGF0YSA9IG51bGw7CiAgICAgICAgICAgIHN3aXRjaCAobnVtYmVyKSB7CiAgICAgICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgICAgICAgc3RyID0gJ29pbFVzZUJvZHknOwogICAgICAgICAgICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRPaWxVc2VCb2R5OwogICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgICAgICAgIHN0ciA9ICdmZWVTdGFydERhdGUnOwogICAgICAgICAgICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRGZWVTdGFydERhdGU7CiAgICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgICAgICAgc3RyID0gJ29pbEFtb3VudCc7CiAgICAgICAgICAgICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdE9pbEFtb3VudDsKICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICAgICAgICBzdHIgPSAncGFpZE1vbmV5JzsKICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0UGFpZE1vbmV5OwogICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgICAgICAgIHN0ciA9ICdyZW1hcmsnOwogICAgICAgICAgICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRyZW1hcms7CiAgICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIHtzdHI6c3RyLGRhdGE6ZGF0YX07CiAgICAgICAgfSwKICAgICAgICBwcmVkKCl7CiAgICAgICAgICAgIHZhciBsZXR0ID0gdGhpczsKICAgICAgICAgICAgbGV0IGluZGV4ID0gbGV0dC5lZGl0SW5kZXg7CiAgICAgICAgICAgIGxldCBjb2x1bW5zID0gbGV0dC5jb2x1bW5zSW5kZXg7CiAgICAgICAgICAgIGlmKGluZGV4ID09PSAtMSAmJiBjb2x1bW5zID09PSAtMSl7CiAgICAgICAgICAgICAgICBpbmRleCA9IDA7CiAgICAgICAgICAgICAgICBjb2x1bW5zID0gMTsKICAgICAgICAgICAgICAgIGxldHQuZWRpdEluZGV4ID0gaW5kZXg7CiAgICAgICAgICAgICAgICBsZXR0LmNvbHVtbnNJbmRleCA9IGNvbHVtbnM7CiAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgICBsZXR0LiRyZWZzW2xldHQuZW50ZXJPcGVyYXRlKGNvbHVtbnMpLnN0citpbmRleCtjb2x1bW5zXS5mb2N1cygpOwogICAgICAgICAgICAgICAgfSwyMDApOwogICAgICAgICAgICB9ZWxzZXsKICAgICAgICAgICAgICAgIGxldHQudmFsaWRhdGUoKQogICAgICAgICAgICAgICAgbGV0dC5zZXRyZW1hcmsoKQogICAgICAgICAgICAgICAgbGV0dC5uZXh0Q2VsbChsZXR0KQogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBlbGxpcHNpcyAodmFsdWUpIHsKICAgICAgICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuICcnCiAgICAgICAgICAgIGlmICh2YWx1ZS5sZW5ndGggPiAxMDApIHsKICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZS5zbGljZSgwLDEwMCkgKyAnLi4uJwogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiB2YWx1ZQogICAgICAgIH0sCiAgICAgICAgaGFuZGxlVXBsb2FkU3VjY2VzcygpIHsKCiAgICAgICAgfSwKICAgIH0sCiAgICBtb3VudGVkKCkgewogICAgICAgIHRoaXMudmVyc2lvbiA9IGluZGV4RGF0YS52ZXJzaW9uOwogICAgICAgIHRoaXMudGJBY2NvdW50LmNvbHVtbnMgPSB0aGlzLnRiQWNjb3VudC50YWlsQ29sdW1uOwogICAgICAgIGxldCB0aGF0ID0gdGhpcwogICAgICAgIGdldFVzZXJCeVVzZXJSb2xlKCkudGhlbihyZXMgPT4gey8v5qC55o2u5p2D6ZmQ6I635Y+W5YiG5YWs5Y+4CiAgICAgICAgICAgIHRoYXQuY29tcGFuaWVzID0gcmVzLmRhdGEuY29tcGFuaWVzOwogICAgICAgICAgICBpZihyZXMuZGF0YS5pc0NpdHlBZG1pbiA9PSB0cnVlIHx8IHJlcy5kYXRhLmlzUHJvQWRtaW4gPT0gdHJ1ZSB8fCByZXMuZGF0YS5pc1N1YkFkbWluID09IHRydWUpewogICAgICAgICAgICAgICAgdGhhdC5pc0FkbWluID0gdHJ1ZTsKICAgICAgICAgICAgfQogICAgICAgICAgICBnZXRDb3VudHJ5c2RhdGEoe29yZ0NvZGU6cmVzLmRhdGEuY29tcGFuaWVzWzBdLmlkfSkudGhlbihyZXMgPT4gey8v5qC55o2u5p2D6ZmQ6I635Y+W5omA5bGe6YOo6ZeoCiAgICAgICAgICAgICAgICB0aGF0LmRlcGFydG1lbnRzID0gcmVzLmRhdGE7CiAgICAgICAgICAgICAgICB0aGF0LmdldFVzZXJEYXRhKCk7CiAgICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgfQp9Cg=="}, {"version": 3, "sources": ["addPreOilAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi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file": "addPreOilAccount.vue", "sourceRoot": "src/view/account", "sourcesContent": ["<style lang=\"less\">\r\n    .mytable .ivu-table-cell{\r\n        padding-left: 5px;\r\n        padding-right: 5px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: normal;\r\n        word-break: break-all;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .accountEs .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .accountEs .header-bar-show {\r\n        max-height: 300px;\r\n        padding-top: 14px;\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .accountEs .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n\r\n\r\n    .mytable .myspan{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block\r\n    }\r\n    .mytable .errorStle{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block;\r\n        color:red;\r\n    }\r\n    //  @on-change='accountnoChange'\r\n</style>\r\n<template>\r\n    <div>\r\n        <div class=\"accountEs\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.accountno\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{ item.name }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"关键字:\" prop=\"oilUseBody\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"accountObj.oilUseBody\" placeholder=\"用能主体关键字模糊查询\" :style=\"formItemWidth\" />\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.company\" @on-change=\"selectChange(accountObj.company)\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"accountObj.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly :style=\"formItemWidth\"/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <div  align=\"right\">\r\n                        <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n                        <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n                    </div>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <div>\r\n                <Row>\r\n                    <Col span=\"12\">\r\n                        <Page size=\"small\" :total=\"pageTotal\" :current=\"pageNum\" :page-size=\"pageSize\" show-elevator show-sizer show-total\r\n                              placement=\"top\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n                    </Col>\r\n                    <Col span=\"12\">\r\n                        <div align=\"right\" class=\"account\">\r\n                            <Button type=\"success\" @click=\"addNewOilAccount\">新增</Button>\r\n                            <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n                            <Button type=\"error\" @click=\"remove\">删除</Button>\r\n                            <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n                                <Button type='info' style=\"margin-left: 5px\">加入归集单\r\n                                    <Icon type='ios-arrow-down'></Icon>\r\n                                </Button>\r\n                                <DropdownMenu slot='list'>\r\n                                    <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n                                    <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n                                </DropdownMenu>\r\n                            </Dropdown>\r\n                            <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n                        </div>\r\n                    </Col>\r\n                </Row>\r\n            </div>\r\n            <Table ref=\"accountEsTable\"\r\n                   border\r\n                   :columns=\"tbAccount.tailColumn\"\r\n                   :data=\"tbAccount.data\"\r\n                   :key=\"tbAccount.dispKey\"\r\n                   class=\"mytable\">\r\n                <!--用油主体-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"oilUseBody\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=100 v-model=\"editOilUseBody\" :ref=\"'oilUseBody'+index+1\" type=\"text\" @on-blur=\"setOilUseBody\"\r\n                               v-if=\"editIndex === index && columnsIndex === 1\"/>\r\n                        <Tooltip placement=\"right\" max-width=\"200\" v-else>\r\n                            <span :class=\"myStyle[index].oilUseBody\" style=\"width: 60px\" @click=\"selectCall(row,index,1,'oilUseBody')\">\r\n                                {{ ellipsis(row.oilUseBody) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.oilUseBody }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--费用发生日-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"feeStartDate\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'feeStartDate'+index+2\" type=\"text\" v-model=\"editFeeStartDate\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 2\" />\r\n                    <span :class=\"myStyle[index].feeStartDate\" @click=\"selectCall(row,index,2,'feeStartDate')\" v-else>{{ row.feeStartDate }}</span>\r\n                </template>\r\n                <!--油量-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"oilAmount\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'oilAmount'+index+3\" type=\"text\" v-model=\"editOilAmount\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 3\"/>\r\n                    <span :class=\"myStyle[index].oilAmount\" @click=\"selectCall(row,index,3,'oilAmount')\" v-else>{{ row.oilAmount }}</span>\r\n                </template>\r\n                <!--金额-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"paidMoney\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'paidMoney'+index+4\" type=\"text\" v-model=\"editPaidMoney\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 4\"/>\r\n                    <span :class=\"myStyle[index].paidMoney\" @click=\"selectCall(row,index,4,'paidMoney')\" v-else>{{ row.paidMoney }}</span>\r\n                </template>\r\n                <!--备注-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=500 v-model=\"editremark\" :ref=\"'remark'+index+5\" type=\"text\" @on-blur=\"setremark\"\r\n                               v-if=\"editIndex === index && columnsIndex === 5\"/>\r\n                        <Tooltip placement=\"left\" max-width=\"600\" v-else>\r\n                            <span :class=\"myStyle[index].remark\" style=\"width: 60px\" @click=\"selectCall(row,index,5,'remark')\">\r\n                                {{ ellipsis(row.remark) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                    <div v-else>\r\n                        <Tooltip placement=\"bottom\" max-width=\"200\">\r\n                            <span>{{ ellipsis(row.remark)}}</span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n            </Table>\r\n            <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n        </div>\r\n        <div>\r\n            <add-bill-per ref=\"addBillPer\"\r\n                          v-on:refreshList=\"refresh\"></add-bill-per >\r\n            <completed-pre-modal ref=\"completedPre\" v-on:refreshList=\"refresh\"></completed-pre-modal>\r\n            <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n            <upload-file-modal ref=\"uploadFileModal\" v-on:onchange=\"change\"></upload-file-modal>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    import UploadFileModal from \"@/view/account/uploadFileModal\";\r\n    import {\r\n        removeOilAccount,\r\n        selectOilIds,\r\n        saveOilAccount\r\n    } from '@/api/coalHeatOilAccount';\r\n    import checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\n    import checkResult from \"@/view/account/check/checkResult\";\r\n    import alarmCheck from \"@/view/account/check/alarmCheck\";\r\n    import {getDates,testNumber,} from '@/view/account/powerAccountHelper';\r\n    import axios from '@/libs/api.request';\r\n    import SelectAmmeter from \"./selectAmmeter\";\r\n    import {_verify_FeeStartDate} from '@/view/account/PowerAccountEs';\r\n    import AddBillPer from \"./addPreOilBillPreModal\";\r\n    import {reJoinBillpre} from '@/api/accountBillPer';\r\n    import {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\n    import CompletedPreModal from \"./completedPreModal\";\r\n    import indexData from '@/config/index'\r\n    import CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import {getUserdata,getUserByUserRole,getCountrysdata,getCountryByUserId} from '@/api/basedata/ammeter.js'\r\n    let dates=getDates();\r\n    export default {\r\n        name: 'addPreOilAccount',\r\n        components: {UploadFileModal, alarmCheck, checkResult, checkResultAndResponse,CompletedPreModal, SelectAmmeter,AddBillPer,CountryModal},\r\n        data() {\r\n            let photo = (h, {row, index}) => {\r\n                let that = this\r\n                let str = ''\r\n                if (row.projectname != '小计' && row.projectname != '合计') {\r\n                    str = '上传附件'\r\n                }\r\n                return h(\"div\", [h(\"u\", {\r\n                    on: {\r\n                        click() {\r\n                            //打开弹出框\r\n                            if (row.projectname != '小计' && row.projectname != '合计') {\r\n                                that.uploadFile(row)\r\n                            }\r\n                        }\r\n                    }\r\n                }, str)]);\r\n            };\r\n            return {\r\n                submit:[],\r\n                submit2:[],\r\n                showCheckModel:false,\r\n                showJhModel:false,\r\n                showAlarmModel:false,\r\n                formItemWidth: widthstyle,\r\n                version:'',\r\n                dateList:dates,\r\n                filterColl: true,//搜索面板展开\r\n                editIndex: -1,//当前编辑行\r\n                columnsIndex:-1,//当前编辑列\r\n                myStyle:[],//样式\r\n                editOilUseBody:'',\r\n                editFeeStartDate:'',\r\n                editOilAmount:'',\r\n                editPaidMoney:'',\r\n                spinShow:false,//遮罩\r\n                editremark:'',\r\n                companies:[],\r\n                departments:[],\r\n                isAdmin:false,\r\n                company:null,//用户默认公司\r\n                country:null,//用户默认所属部门\r\n                countryName:null,//用户默认所属部门\r\n                accountObj:{\r\n                    accountno:dates[1].code,//期号,默认当前月\r\n                    company:\"\",//分公司\r\n                    country:\"\",//所属部门\r\n                    oilUseBody:null,//用能主体\r\n                    oilAccountType: 2,\r\n                    countryName: \"\",\r\n\r\n                },\r\n                tbAccount: {\r\n                    dispKey: 0,\r\n                    loading: false,\r\n                    columns: [],\r\n                    tailColumn: [\r\n                        {type: 'selection', width: 60, align: 'center',},\r\n                        {\r\n                            title: \"期号\",\r\n                            key: \"accountNo\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {\r\n                            title: \"用油主体\",\r\n                            slot: \"oilUseBody\",\r\n                            align: \"center\",\r\n                            width: 200,\r\n                        },\r\n                        {\r\n                            title: \"费用发生日\",\r\n                            slot: \"feeStartDate\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {\r\n                            title: \"油量(L)\",\r\n                            slot: \"oilAmount\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {\r\n                            title: \"单价(元/L)\",\r\n                            key: \"unitPrice\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {\r\n                            title: \"金额(元)\",\r\n                            slot: \"paidMoney\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {title: \"附件\", align: \"center\", render: photo, width: 130},\r\n                        {title: \"备注\", slot: \"remark\",align: \"center\", width: 290},\r\n                    ],\r\n                    data: []\r\n                },\r\n                pageTotal: 0,\r\n                pageNum: 1,\r\n                pageSize: 10,//当前页\r\n            }\r\n        },\r\n        methods: {\r\n            uploadFile(row) {\r\n                console.log(row, \"row\");\r\n                // let id;\r\n                // if(!row.id2) {\r\n                //     editAmmeter('', 0).then(res => {\r\n                //         debugger\r\n                //         console.log(res, \"res\");\r\n                //         row.id2 = res.data.id;\r\n\r\n                //         this.id2 = res.data.id\r\n                //         // debugger\r\n                //         // this.fileParam.busiId = ;\r\n                //         this.$refs.uploadFileModal.choose(row.id2 + '');\r\n                //     })\r\n                // }else {\r\n\r\n                if(row.id) {\r\n                    this.$refs.uploadFileModal.choose(row.id + '');\r\n                }else {\r\n                    this.errorTips(\"请先保存后再上传文件！\");\r\n                }\r\n                // }\r\n                // console.log(row, \"row\");\r\n            },\r\n            selectChange(){\r\n                let that = this;\r\n                if (that.accountObj.company != undefined) {\r\n                    if(that.accountObj.company == \"-1\"){\r\n                        that.accountObj.country = -1;\r\n                        that.accountObj.countryName = null;\r\n                    }else{\r\n                        getCountryByUserId(that.accountObj.company).then(res => {\r\n                            if(res.data.departments.length != 0){\r\n                                that.accountObj.country = res.data.departments[0].id;\r\n                                that.accountObj.countryName = res.data.departments[0].name;\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            },\r\n            //选择所属部门开始\r\n            chooseResponseCenter() {\r\n                if(this.accountObj.company == null || this.accountObj.company == \"-1\" ){\r\n                    this.$Message.info(\"请先选择分公司\");return;\r\n                }\r\n                this.$refs.countryModal.choose(this.accountObj.company);//所属部门\r\n            },\r\n            getDataFromModal(data) {\r\n                this.accountObj.country = data.id;\r\n                this.accountObj.countryName = data.name;\r\n                //选择所属部门结束\r\n            },\r\n            getUserData(){\r\n                let that = this;\r\n                getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                    if(res.data.companies.length != 0){\r\n                        let companies = res.data.companies;\r\n                        if(res.data.companies[0].id == \"**********\"){\r\n                            companies = that.companies;\r\n                        }\r\n                        that.company = companies[0].id;\r\n                        that.accountObj.company = companies[0].id;\r\n                    }\r\n                    if(res.data.departments.length != 0){\r\n                        let departments = res.data.departments;\r\n                        if(res.data.companies[0].id == \"**********\" && that.departments.length != 0){\r\n                            departments = that.departments\r\n                        }\r\n                        that.country = departments[0].id;\r\n                        that.countryName = departments[0].name;\r\n                        that.accountObj.country = Number(departments[0].id);\r\n                        that.accountObj.countryName = departments[0].name;\r\n                    }\r\n\r\n                    that.pageNum = 1\r\n                    that.getAccountMessages();\r\n                });\r\n            },\r\n            searchList(){\r\n                if(this.accountObj.countryName == \"\"){\r\n                    this.accountObj.country = \"-1\";\r\n                }\r\n                this.pageNum = 1;\r\n                this.getAccountMessages()\r\n            },\r\n            accountnoChange(){\r\n                this.searchList()\r\n            },\r\n            //点击保存\r\n            preserve() {\r\n                let dataL = this.$refs.accountEsTable.getSelection();\r\n                let b = false;\r\n                let array = [];\r\n                for (let i = 0; i < dataL.length; i ++) {\r\n                    b = true;\r\n                    array.push(dataL[i])\r\n                }\r\n                if(b){\r\n                    this.submitData(array);\r\n                }else {\r\n                    this.errorTips('没有可保存数据')\r\n                }\r\n            },\r\n            submitChange(indexList){\r\n                let data=[];\r\n                this.submit2.map((item,index)=>{\r\n                    indexList.map((item2)=>{\r\n                        if(index==item2){\r\n                            data.push(item)\r\n                        }\r\n                    })\r\n                });\r\n                this.submit=data\r\n            },\r\n\r\n            //提交数据\r\n            submitData(data){\r\n                let a = [];\r\n                let that=this;\r\n                if(data != null && data.length > 0){\r\n                    let number = 0;\r\n                    let submitData = [];\r\n                    let str = '';\r\n                    let accountno = this.accountObj.accountno;\r\n                    data.forEach(function (item) {\r\n                        // 校验数据\r\n                        if(item.id == null){\r\n                            item.accountno = accountno\r\n                        }\r\n                        a.push(item.id);\r\n                        item.oilAccountType = 2;\r\n                        submitData.push(item);\r\n                        number ++;\r\n                    });\r\n                    that.ids=a;\r\n                    if(str.length > 0){\r\n                        this.errorTips(str)\r\n                    }\r\n                    if(submitData.length > 0){\r\n                        saveOilAccount(submitData).then((res) => {\r\n                            if (res.data.code == 0) {\r\n                                this.$Message.info({\r\n                                    content: '提示：成功保存 ' + res.data.num + ' 条数据',\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }\r\n                }\r\n            },\r\n            addNewOilAccount() {\r\n                const currentDate = new Date();\r\n                const currentYear = currentDate.getFullYear();\r\n                const currentMonth = currentDate.getMonth() + 1;\r\n                if (null == this.tbAccount.data) {\r\n                    this.tbAccount.data = [];\r\n                }\r\n                this.tbAccount.dispKey++;\r\n                this.tbAccount.data.unshift({\r\n                    // accountNo:dates[1].code,\r\n                    // accountNo: this.accountObj.accountno,\r\n                    accountNo: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined) ? currentYear+\"\"+currentMonth: this.accountObj.accountno,\r\n                    oilUseBody: \"\",\r\n                    feeStartDate:\"\",\r\n                    oilAmount: \"0\",\r\n                    unitPrice: \"0\",\r\n                    paidMoney:\"0\",\r\n                    remark:\"\",\r\n                });\r\n                this.tbAccount.loading = false;\r\n                this.myStyle.push({\r\n                    oilUseBody: 'myspan',\r\n                    feeStartDate: 'myspan',\r\n                    oilAmount: 'myspan',\r\n                    paidMoney: 'myspan',\r\n                    remark: 'myspan',\r\n\r\n                    });\r\n            },\r\n            //验证错误弹出提示框\r\n            errorTips(str){\r\n                this.$Notice.error({\r\n                    title: '提示',\r\n                    desc: str,\r\n                    duration: 10\r\n                });\r\n            },\r\n            handlePage(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageNum = value;\r\n                this.getAccountMessages();\r\n            },\r\n            handlePageSize(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n                this.pageSize = value;\r\n                this.getAccountMessages();\r\n            },\r\n            //向后台请求数据\r\n            getAccountMessages() {\r\n                let postData = this.accountObj;\r\n                postData.pageNum = this.pageNum;\r\n                postData.pageSize = this.pageSize;\r\n                let req = {\r\n                    url : \"/business/oil/account/list\",\r\n                    method : \"get\",\r\n                    params : postData\r\n                };\r\n                this.tbAccount.loading = true;\r\n                axios.request(req).then(res => {\r\n                    this.tbAccount.loading = false;\r\n                    if (res.data) {\r\n                        let data = res.data.rows;\r\n                        data.forEach(function (item) {\r\n                            item.editType = 0;\r\n                        });\r\n                        if (null == data) {\r\n                            this.tbAccount.data = {};\r\n                        } else {\r\n                            this.tbAccount.data = data;\r\n                        }\r\n                        this.pageTotal = res.data.total || 0;\r\n                        this.setMyStyle(this.tbAccount.data.length);\r\n                        this.editIndex = -1;\r\n                        this.columnsIndex = -1;\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            },\r\n            //重置\r\n            onResetHandle(){\r\n                this.accountObj = {\r\n                    accountno:null,\r\n                    company:this.company,\r\n                    oilUseBody:null,\r\n                    country:Number(this.country),\r\n                    oilAccountType:2,\r\n                }\r\n                this.getAccountMessages()\r\n            },\r\n            //计算单价\r\n            unitPrice(row){\r\n                let paidMoney = row.paidMoney;\r\n                let oilAmount = row.oilAmount;\r\n                if(paidMoney != null && oilAmount != null){\r\n                    row.unitpirce = paidMoney/oilAmount.toFixed(2);\r\n                }\r\n            },\r\n            remove(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                if(data == null || data.length === 0){\r\n                    this.errorTips(\"请选择要删除的数据\")\r\n                    return;\r\n                }\r\n                this.$Modal.confirm({\r\n                    title: '提示',\r\n                    content: '<p>是否确认删除选中信息？</p>',\r\n                    onOk: () => {\r\n                        let b = true;\r\n                        let ids = '';\r\n                        let total = this.pageTotal\r\n                        for(let i=0;i<data.length;i++){\r\n                            let item = data[i];\r\n                            if(item.id != null && item.id.length > 0){\r\n                                if(item.pabriid){\r\n                                    b = false;\r\n                                }\r\n                                ids += item.id + ',';\r\n                            }\r\n                        }\r\n                        this.pageTotal = total\r\n                        if(b){\r\n                            if(ids.length > 0){\r\n                                removeOilAccount(ids).then((res) => {\r\n                                    if (res.data.code == 0) {\r\n                                        this.$Message.success('删除成功');\r\n                                        this.getAccountMessages();\r\n                                    }\r\n                                });\r\n                            }\r\n                        }else {\r\n                            this.errorTips('选中信息中有信息还没有跟归集单解除关联，请先解除关联')\r\n                        }\r\n                    },\r\n                    onCancel: () => {\r\n                    }\r\n                });\r\n            },\r\n            openAddBillPerModal(name) {\r\n                if (name === 'current') {\r\n                    this.selectedAccount()\r\n                } else if (name === 'all') {\r\n                    this.selectedAllAccount()\r\n                }\r\n            },\r\n            //加入归集单，全部有效台账\r\n            selectedAllAccount(){\r\n                let that = this\r\n                that.spinShow = true;\r\n                selectOilIds(this.accountObj).then(res => {\r\n                    that.spinShow = false;\r\n                    if(res.data.length == 0){\r\n                        that.errorTips('无有效数据可加入归集单')\r\n                    }else {\r\n                        let ids = [];\r\n                        for(let i=0;i<res.data.rows.length;i++){\r\n                            let item = res.data.rows[i];\r\n                            ids.push(item.id)\r\n                        }\r\n                        that.$refs.addBillPer.initAmmeter(ids, 21,this.accountObj.country);\r\n                    }\r\n                });\r\n            },\r\n            selectedAccount(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = 1;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要加入归集单的台账')\r\n                } else {\r\n                    let ids = [];\r\n                    data.forEach(function (item) {\r\n                        if(item.status === 5){\r\n                            b = 3\r\n                        }\r\n                        if(item.status === 4){\r\n                            b=4;\r\n                        }\r\n                        ids.push(item.id)\r\n                    });\r\n                    if(b === 1){\r\n                        this.$refs.addBillPer.initAmmeter(ids,21,this.accountObj.country);\r\n                    }else if(b === 2) {\r\n                        this.errorTips('选中的台账中存在临时数据，请先保存再加入归集单！')\r\n                    }else if(b===3){\r\n                        this.errorTips('退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮')\r\n                    }else if(b===4){\r\n                        this.errorTips('选择的台账有已加入归集单的台账，不能加入其他归集单')\r\n                    }\r\n                }\r\n            },\r\n            openCompletedPreModal(){\r\n                this.$refs.completedPre.initAmmeter(this.accountObj.country,2);\r\n            },\r\n            againJoin(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = true;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要重新加入归集单的台账')\r\n                } else {\r\n                    let ids = '';\r\n                    data.forEach(function (item) {\r\n                        let status = item.status;\r\n                        if(status != 5){\r\n                            b = false;\r\n                        }\r\n                        ids+= item.id +','\r\n                    });\r\n                    if(b){\r\n                        reJoinBillpre(ids).then((res) =>{\r\n                            if(res.data.code==0){\r\n                                this.$Message.info({\r\n                                    content:'提示：操作成功' ,\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }else {\r\n                        this.errorTips('只有已退回的台账才能重新加入归集单')\r\n                    }\r\n                }\r\n            },\r\n            refresh(){\r\n                let obj = this\r\n                setTimeout(function () {\r\n                    obj.getAccountMessages()\r\n                },200);\r\n            },\r\n            validate(){\r\n                if(this.columnsIndex != 5){\r\n                    let val = this.enterOperate(this.columnsIndex).data;\r\n                    if(val) {\r\n                        switch (this.columnsIndex) {\r\n                            // case 1:\r\n                            //     this.validateFeeStartDate();\r\n                            //     break;\r\n                            case 2:\r\n                                this.validateFeeStartDate();\r\n                                break;\r\n                            case 3:\r\n                                this.validateOilAmount();\r\n                                break;\r\n                            case 4:\r\n                                this.validatePaidMoney();\r\n                                break;\r\n                            // case 5:\r\n                            //     this.validateFeeStartDate();\r\n                            //     break;\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            validateFeeStartDate(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editFeeStartDate;\r\n                let result = _verify_FeeStartDate(data,val);\r\n                // if(result){//失败就弹出提示内容，并将数据恢复初始化\r\n                //     this.errorTips(result)\r\n                // }else{\r\n                    data.feeStartDate = val;\r\n                    data.editType = 1;\r\n                // }\r\n            },\r\n            validateOilAmount(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOilAmount;\r\n                if (!testNumber(val)) {\r\n                    this.errorTips('请输入数字！');\r\n                }\r\n                data.oilAmount = val;\r\n                data.unitPrice = data.paidMoney/data.oilAmount;\r\n                data.editType = 1;\r\n            },\r\n            validatePaidMoney(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editPaidMoney;\r\n                if (!testNumber(val)) {\r\n                    this.errorTips('请输入数字！');\r\n                }\r\n                data.paidMoney = val;\r\n                data.unitPrice = data.paidMoney/data.oilAmount;\r\n                data.editType = 1;\r\n                // this.unitPrice(data)\r\n            },\r\n            setremark(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editremark;\r\n                data.remark = val;\r\n                data.editType = 1;\r\n            },\r\n            setOilUseBody(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOilUseBody;\r\n                data.oilUseBody = val;\r\n                data.editType = 1;\r\n            },\r\n            setMyStyle(length){\r\n                this.myStyle=[];\r\n                for(var i=0;i<length;i++){\r\n                    this.myStyle.push({\r\n                        oilUseBody: 'myspan',\r\n                        feeStartDate: 'myspan',\r\n                        oilAmount: 'myspan',\r\n                        paidMoney: 'myspan',\r\n                        remark: 'myspan',\r\n                    });\r\n                }\r\n            },\r\n            //span点击事件将span换成输入框并且获取焦点\r\n            selectCall(row,index,columns,str){\r\n                this.editFeeStartDate = row.feeStartDate;\r\n                this.editOilUseBody = row.oilUseBody;\r\n                this.editOilAmount = row.oilAmount == null || row.oilAmount===0?null:row.oilAmount;\r\n                this.editPaidMoney = row.paidMoney;\r\n                this.editremark = row.remark;\r\n                this.editIndex = index;\r\n                this.columnsIndex = columns;\r\n                let a=this;\r\n                setTimeout(function () {\r\n                    a.$refs[str+index+columns].focus();\r\n                },200);\r\n\r\n            },\r\n            //跳转到下一格\r\n            nextCell(data){\r\n                let index = data.editIndex;\r\n                let columns = data.columnsIndex;\r\n                let row = '';\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                }else if(index > -1 && columns === 5){\r\n                    //当跳转的最后一行最后一格的时候\r\n                    if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n                        index = 0;\r\n                    }else{\r\n                        index ++;\r\n                    }\r\n                    columns = 1;\r\n                }else{\r\n                    columns += 1;\r\n                }\r\n                data.editIndex = index;\r\n                data.columnsIndex = columns;\r\n                row = data.tbAccount.data[index];\r\n                if(row){\r\n                    data.editFeeStartDate = row.feeStartDate;\r\n                    data.editOilUseBody = row.oilUseBody;\r\n                    data.editOilAmount = row.oilAmount == null || row.oilAmount===0?null:row.oilAmount;\r\n                    data.editremark = row.remark;\r\n                    data.editPaidMoney = row.paidMoney;\r\n                }\r\n                setTimeout(function () {\r\n                    data.$refs[data.enterOperate(columns).str+index+columns].focus();\r\n                },200);\r\n            },\r\n            //根据列号返回对应的列名\r\n            enterOperate(number){\r\n                let str = '';\r\n                let data = null;\r\n                switch (number) {\r\n                    case 1:\r\n                        str = 'oilUseBody';\r\n                        data = this.editOilUseBody;\r\n                        break;\r\n                    case 2:\r\n                        str = 'feeStartDate';\r\n                        data = this.editFeeStartDate;\r\n                        break;\r\n                    case 3:\r\n                        str = 'oilAmount';\r\n                        data = this.editOilAmount;\r\n                        break;\r\n                    case 4:\r\n                        str = 'paidMoney';\r\n                        data = this.editPaidMoney;\r\n                        break;\r\n                    case 5:\r\n                        str = 'remark';\r\n                        data = this.editremark;\r\n                        break;\r\n                }\r\n                return {str:str,data:data};\r\n            },\r\n            pred(){\r\n                var lett = this;\r\n                let index = lett.editIndex;\r\n                let columns = lett.columnsIndex;\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                    lett.editIndex = index;\r\n                    lett.columnsIndex = columns;\r\n                    setTimeout(function () {\r\n                        lett.$refs[lett.enterOperate(columns).str+index+columns].focus();\r\n                    },200);\r\n                }else{\r\n                    lett.validate()\r\n                    lett.setremark()\r\n                    lett.nextCell(lett)\r\n                }\r\n            },\r\n            ellipsis (value) {\r\n                if (!value) return ''\r\n                if (value.length > 100) {\r\n                    return value.slice(0,100) + '...'\r\n                }\r\n                return value\r\n            },\r\n            handleUploadSuccess() {\r\n\r\n            },\r\n        },\r\n        mounted() {\r\n            this.version = indexData.version;\r\n            this.tbAccount.columns = this.tbAccount.tailColumn;\r\n            let that = this\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                    that.getUserData();\r\n                });\r\n            });\r\n        }\r\n    }\r\n</script>\r\n"]}]}