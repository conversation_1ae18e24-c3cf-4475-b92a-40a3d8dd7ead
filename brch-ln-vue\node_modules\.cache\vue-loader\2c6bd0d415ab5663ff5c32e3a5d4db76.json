{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePagePylon\\addPylonCreditAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePagePylon\\addPylonCreditAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRQcmVkUG93ZXJBY2NvdW50LA0KICBhZGRBY2NvdW50RXMsDQogIHJlbW92ZUFjY291bnRFcywNCiAgZ2V0VXNlciwNCiAgZ2V0RGVwYXJ0bWVudHMsDQogIGFjY291bnRFc1RvdGFsLA0KICBzZWxlY3RJZHNCeUVzUGFyYW1zLA0KfSBmcm9tICJAL2FwaS9hY2NvdW50IjsNCmltcG9ydCB7IGdldFJlc0NlbnRlciwgZ2V0Y29tcGFueSB9IGZyb20gIkAvYXBpL2FsZXJ0Y29udHJvbC9hbGVydGNvbnRyb2wiOw0KaW1wb3J0IHsgYWdhaW5Kb2luIH0gZnJvbSAiQC9hcGkvYWNjb3VudEJpbGxQZXIiOw0KaW1wb3J0IHsNCiAgZ2V0Q2xhc3NpZmljYXRpb24sDQogIGdldFVzZXJkYXRhLA0KICBnZXRVc2VyQnlVc2VyUm9sZSwNCiAgZ2V0Q291bnRyeXNkYXRhLA0KICBnZXRDb3VudHJ5QnlVc2VySWQsDQp9IGZyb20gIkAvYXBpL2Jhc2VkYXRhL2FtbWV0ZXIuanMiOw0KaW1wb3J0IHsgZWRpdE93biB9IGZyb20gIkAvYXBpL2FjY291bnRTQy9hY2NvdW50U0MiOw0KaW1wb3J0IHsgZ2V0QXVkaXRSZXN1bHQsIGdldEF1ZGl0UmVzdWx0TmV3LCBnZXRBdWRpdFJlc3VsdE5ld19RWE0gfSBmcm9tICJAL2FwaS9hY2NvdW50IjsNCmltcG9ydCB7IHZhbGlkQ29udHJhY3RMaXN0IH0gZnJvbSAiQC9hcGkvY29udHJhY3QiOw0KaW1wb3J0IHsNCiAgZ2V0RGF0ZXMyLA0KICB0ZXN0TnVtYmVyLA0KICBjdXREYXRlX3l5eXltbWRkLA0KICBnZXRGaXJzdERhdGVCeUFjY291bnRub195eXl5bW1kZCwNCiAgZ2V0TGFzdERhdGVCeUFjY291bnRub195eXl5bW1kZCwNCiAgc3RyaW5nVG9EYXRlLA0KICBnZXRDdXJyZW50RGF0ZSwNCn0gZnJvbSAiQC92aWV3L2FjY291bnQvcG93ZXJBY2NvdW50SGVscGVyIjsNCmltcG9ydCB7DQogIF92ZXJpZnlfU3RhcnREYXRlLA0KICBfdmVyaWZ5X0VuZERhdGUsDQogIHZlcmlmaWNhdGlvbiwNCiAgdW5pdHBpcmNlTWluLA0KICB1bml0cGlyY2VNYXgsDQogIHVuaXRwaXJjZU1heDEsDQp9IGZyb20gIkAvdmlldy9hY2NvdW50L1Bvd2VyQWNjb3VudEVzIjsNCmltcG9ydCB7DQogIGp1ZGdlX25lZ2F0ZSwNCiAganVkZ2VfcmVjb3ZlcnksDQogIGNvdW50VGF4YW1vdW50LA0KfSBmcm9tICJAL3ZpZXcvYWNjb3VudC9Qb3dlckFjY291bnRDb250cm9sbGVyIjsNCmltcG9ydCB7IHdpZHRoc3R5bGUgfSBmcm9tICJAL3ZpZXcvYnVzaW5lc3MvbXNzQWNjb3VudGJpbGwvbXNzQWNjb3VudGJpbGxkYXRhIjsNCmltcG9ydCB7IGJsaXN0IH0gZnJvbSAiQC9saWJzL3Rvb2xzIjsNCmltcG9ydCBleGNlbCBmcm9tICJAL2xpYnMvZXhjZWwiOw0KaW1wb3J0IGF4aW9zIGZyb20gIkAvbGlicy9hcGkucmVxdWVzdCI7DQppbXBvcnQgQ29tcGxldGVkUHJlTW9kYWwgZnJvbSAiQC92aWV3L2FjY291bnQvY29tcGxldGVkUHJlTW9kYWwiOw0KaW1wb3J0IEFkZEJpbGxQZXIgZnJvbSAiQC92aWV3L2FjY291bnQvYWRkQmlsbFByZU1vZGFsIjsNCmltcG9ydCBTZWxlY3RBbW1ldGVyIGZyb20gIkAvdmlldy9hY2NvdW50L3NlbGVjdEFtbWV0ZXIiOw0KaW1wb3J0IGluZGV4RGF0YSBmcm9tICJAL2NvbmZpZy9pbmRleCI7DQppbXBvcnQgQ291bnRyeU1vZGFsIGZyb20gIkAvdmlldy9iYXNlZGF0YS9hbW1ldGVyL2NvdW50cnlNb2RhbCI7DQppbXBvcnQgYWxhcm1DaGVjayBmcm9tICJAL3ZpZXcvYWNjb3VudC9jaGVjay9hbGFybUNoZWNrIjsNCmltcG9ydCBjaGVja1Jlc3VsdCBmcm9tICJAL3ZpZXcvYWNjb3VudC9jaGVjay9jaGVja1Jlc3VsdCI7DQppbXBvcnQgY2hlY2tSZXN1bHRBbmRSZXNwb25zZSBmcm9tICJAL3ZpZXcvYWNjb3VudC9jaGVjay9jaGVja1Jlc3VsdEFuZFJlc3BvbnNlIjsNCmltcG9ydCB7IG1hcFN0YXRlIH0gZnJvbSAidnVleCI7DQoNCmltcG9ydCBwZXJtaXNzaW9uTWl4aW4gZnJvbSAiQC9taXhpbnMvcGVybWlzc2lvbiI7DQppbXBvcnQgcGFnZUZ1biBmcm9tICJAL21peGlucy9wYWdlRnVuIjsNCg0KbGV0IGRhdGVzID0gZ2V0RGF0ZXMyKCk7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogImFkZFByZWRQb3dlckFjY291bnQiLA0KICBtaXhpbnM6IFtwZXJtaXNzaW9uTWl4aW4sIHBhZ2VGdW5dLA0KICBjb21wb25lbnRzOiB7DQogICAgYWxhcm1DaGVjaywNCiAgICBjaGVja1Jlc3VsdCwNCiAgICBjaGVja1Jlc3VsdEFuZFJlc3BvbnNlLA0KICAgIENvbXBsZXRlZFByZU1vZGFsLA0KICAgIFNlbGVjdEFtbWV0ZXIsDQogICAgQWRkQmlsbFBlciwNCiAgICBDb3VudHJ5TW9kYWwsDQogIH0sDQogIGRhdGEoKSB7DQogICAgbGV0IHJlbmRlclN0YXR1cyA9IChoLCB7IHJvdywgaW5kZXggfSkgPT4gew0KICAgICAgdmFyIHN0YXR1cyA9ICIiOw0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW2luZGV4XTsNCiAgICAgIGZvciAobGV0IGl0ZW0gb2YgdGhpcy5hY2NvdW50U3RhdHVzKSB7DQogICAgICAgIGlmIChpdGVtLnR5cGVDb2RlID09IHJvdy5zdGF0dXMpIHsNCiAgICAgICAgICBkYXRhLnN0YXR1c05hbWUgPSBpdGVtLnR5cGVOYW1lOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gaCgiZGl2IiwgZGF0YS5zdGF0dXNOYW1lKTsNCiAgICB9Ow0KDQogICAgbGV0IHJlbmRlckNhdGVnb3J5ID0gKGgsIHBhcmFtcykgPT4gew0KICAgICAgdmFyIGNhdGVnb3J5bmFtZSA9ICIiOw0KICAgICAgZm9yIChsZXQgaXRlbSBvZiB0aGlzLmNhdGVnb3J5cykgew0KICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSBwYXJhbXMucm93LmNhdGVnb3J5KSB7DQogICAgICAgICAgY2F0ZWdvcnluYW1lID0gaXRlbS50eXBlTmFtZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGgoImRpdiIsIGNhdGVnb3J5bmFtZSk7DQogICAgfTsNCg0KICAgIHJldHVybiB7DQogICAgICBpc1Q6IHRydWUsDQogICAgICBudW1iZXIyOiAwLA0KICAgICAgbmFtZTogIiIsDQogICAgICBkYXRhTDogW10sDQogICAgICBpc1F1ZXJ5OiB0cnVlLA0KICAgICAgbnVtYmVyOiAwLA0KICAgICAgY3RnS2V5TGlzdDogW10sDQogICAgICBzdWJtaXQ6IFtdLA0KICAgICAgc3VibWl0MjogW10sDQogICAgICBhbW1ldGVyaWRzOiBbXSwNCiAgICAgIHNob3dDaGVja01vZGVsOiBmYWxzZSwNCiAgICAgIHNob3dBbGFybU1vZGVsOiBmYWxzZSwNCiAgICAgIHNob3dKaE1vZGVsOiBmYWxzZSwNCiAgICAgIGZvcm1JdGVtV2lkdGg6IHdpZHRoc3R5bGUsDQogICAgICB2ZXJzaW9uOiAiIiwNCiAgICAgIGRhdGVMaXN0OiBkYXRlcywNCiAgICAgIGZpbHRlckNvbGw6IHRydWUsIC8v5pCc57Si6Z2i5p2/5bGV5byADQogICAgICBlZGl0SW5kZXg6IC0xLCAvL+W9k+WJjee<PERSON>lu<PERSON>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"}, {"version": 3, "sources": ["addPylonCreditAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addPylonCreditAccount.vue", "sourceRoot": "src/view/account/homePagePylon", "sourcesContent": ["<!--铁塔挂账电费台账-->\r\n\r\n<template>\r\n  <div class=\"page-class page-card\">\r\n    <div class=\"query-box\">\r\n      <Row :class=\"filterColl ? 'header-bar-show' : 'header-bar-hide'\">\r\n        <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.accountno\"\r\n                  :style=\"formItemWidth\"\r\n                  @on-change=\"accountnoChange\"\r\n                >\r\n                  <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"项目名称:\" prop=\"projectName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.projectName\"\r\n                  placeholder=\"请输入项目名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"'sc' == version\">\r\n              <FormItem\r\n                label=\"电表/协议编码:\"\r\n                prop=\"ammeterName\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.ammeterName\"\r\n                  placeholder=\"请输入电表户号/协议编码\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"供电局电表编号:\"\r\n                prop=\"supplybureauammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.supplybureauammetercode\"\r\n                  placeholder=\"请输入供电局电表编号\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"局站名称:\" prop=\"stationName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.stationName\"\r\n                  placeholder=\"请输入局站名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.company\"\r\n                  @on-change=\"selectChange(accountObj.company)\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                  <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"所属部门：\"\r\n                prop=\"countryName\"\r\n                v-if=\"isAdmin == true\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Input\r\n                  :clearable=\"true\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"accountObj.countryName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n              <FormItem\r\n                label=\"所属部门：\"\r\n                prop=\"country\"\r\n                v-if=\"isAdmin == false\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option value=\"-1\">全部</Option>\r\n                  <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\"></Col>\r\n          </Row>\r\n          <div align=\"right\">\r\n            <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n            <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n            <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n              <Button type=\"default\" style=\"margin-left: 5px\"\r\n                >导出\r\n                <Icon type=\"ios-arrow-down\"></Icon>\r\n              </Button>\r\n              <DropdownMenu slot=\"list\">\r\n                <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n          </div>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon\r\n          :type=\"filterColl ? 'md-arrow-dropup' : 'md-arrow-dropdown'\"\r\n          size=\"20\"\r\n          @click=\"filterColl = !filterColl\"\r\n          :color=\"filterColl ? '#000' : '#1ab394'\"\r\n        ></icon>\r\n      </div>\r\n    </div>\r\n    <div class=\"cl-table\">\r\n      <Row class=\"button-bar\">\r\n        <div class=\"table-button\">\r\n          <Button type=\"primary\" @click=\"addElectricType\">新增</Button>\r\n          <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n          <Button type=\"error\" @click=\"remove\">删除</Button>\r\n          <Button type=\"primary\" @click=\"openCompletedPreModal\">复制归集单台账</Button>\r\n          <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n            <Button type=\"info\" style=\"margin-left: 5px\"\r\n              >加入归集单\r\n              <Icon type=\"ios-arrow-down\"></Icon>\r\n            </Button>\r\n            <DropdownMenu slot=\"list\">\r\n              <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n              <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n          <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n        </div>\r\n      </Row>\r\n      <Table\r\n        ref=\"accountEsTable\"\r\n        border\r\n        :columns=\"tbAccount.columns\"\r\n        :data=\"tbAccount.data\"\r\n        class=\"mytable\"\r\n        :loading=\"tbAccount.loading\"\r\n        :height=\"tableHeight\"\r\n      >\r\n        <!--项目名称-->\r\n        <template slot-scope=\"{ row }\" slot=\"projectName\">\r\n          <div v-if=\"row.status === 5\">\r\n            <span>{{ row.projectName }}</span\r\n            ><span style=\"color: red\">[退回]</span>\r\n          </div>\r\n          <span v-else>{{ row.projectName }}</span>\r\n        </template>\r\n        <!--起始时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"startdate\" v-if=\"row.total == null\">\r\n          <Input\r\n            :ref=\"'startdate' + index + 1\"\r\n            type=\"text\"\r\n            @on-blur=\"validate\"\r\n            v-model=\"editStartDate\"\r\n            v-if=\"editIndex === index && columnsIndex === 1\"\r\n          />\r\n          <span\r\n            :class=\"myStyle[index].startdate\"\r\n            @click=\"selectCall(row, index, 1, 'startdate')\"\r\n            v-else\r\n            >{{ row.startdate }}</span\r\n          >\r\n        </template>\r\n        <!--截止时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"enddate\" v-if=\"row.total == null\">\r\n          <Input\r\n            :ref=\"'enddate' + index + 2\"\r\n            type=\"text\"\r\n            v-model=\"editEndDate\"\r\n            @on-blur=\"validate\"\r\n            v-if=\"editIndex === index && columnsIndex === 2\"\r\n          />\r\n          <span\r\n            :class=\"myStyle[index].enddate\"\r\n            @click=\"selectCall(row, index, 2, 'enddate')\"\r\n            v-else\r\n            >{{ row.enddate }}</span\r\n          >\r\n        </template>\r\n        <!--用电量-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"curusedreadings\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'curusedreadings' + index + 3\"\r\n              type=\"text\"\r\n              v-model=\"editcurusedreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 3\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].curusedreadings\"\r\n              @click=\"selectCall(row, index, 3, 'curusedreadings')\"\r\n              v-else\r\n              >{{ row.curusedreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.curusedreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--输入的普票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputticketmoney' + index + 4\"\r\n              type=\"text\"\r\n              v-model=\"editinputticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 4\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputticketmoney\"\r\n              @click=\"selectCall(row, index, 4, 'inputticketmoney')\"\r\n              v-else\r\n              >{{ row.inputticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--输入的专票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputtaxticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputtaxticketmoney' + index + 5\"\r\n              type=\"text\"\r\n              v-model=\"editinputtaxticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 5\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputtaxticketmoney\"\r\n              @click=\"selectCall(row, index, 5, 'inputtaxticketmoney')\"\r\n              v-else\r\n              >{{ row.inputtaxticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputtaxticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--专票税率-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"taxrate\">\r\n          <div v-if=\"row.total == null\">\r\n            <Select\r\n              :ref=\"'taxrate' + index + 6\"\r\n              type=\"text\"\r\n              v-model=\"edittaxrate\"\r\n              @on-change=\"settaxrate\"\r\n              v-if=\"editIndex === index && columnsIndex === 6\"\r\n              transfer=\"true\"\r\n            >\r\n              <Option selected value=\"13\">13</Option>\r\n              <Option value=\"1\">1</Option>\r\n              <Option value=\"3\">3</Option>\r\n              <Option value=\"6\">6</Option>\r\n              <Option value=\"16\">16</Option>\r\n              <Option value=\"17\">17</Option>\r\n            </Select>\r\n            <span\r\n              :class=\"myStyle[index].taxrate\"\r\n              @click=\"selectCall(row, index, 6, 'taxrate')\"\r\n              v-else\r\n              >{{ row.taxrate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.taxrate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--备注-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              v-model=\"editremark\"\r\n              :ref=\"'remark' + index + 7\"\r\n              type=\"text\"\r\n              @on-blur=\"setremark\"\r\n              v-if=\"editIndex === index && columnsIndex === 7\"\r\n            />\r\n            <Tooltip placement=\"bottom\" max-width=\"200\" v-else>\r\n              <span\r\n                :class=\"myStyle[index].remark\"\r\n                style=\"width: 60px\"\r\n                @click=\"selectCall(row, index, 7, 'remark')\"\r\n                >{{ ellipsis(row.remark) }}</span\r\n              >\r\n              <div slot=\"content\">\r\n                {{ row.remark }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n          <div v-else>\r\n            <Tooltip placement=\"bottom\" max-width=\"200\">\r\n              <span>{{ ellipsis(row.remark) }}</span>\r\n              <div slot=\"content\">\r\n                {{ row.remark }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n        </template>\r\n      </Table>\r\n      <div class=\"table-page\">\r\n        <Page\r\n          size=\"small\"\r\n          :total=\"pageTotal\"\r\n          :current=\"pageNum\"\r\n          :page-size=\"pageSize\"\r\n          show-elevator\r\n          show-sizer\r\n          show-total\r\n          placement=\"top\"\r\n          @on-change=\"handlePage\"\r\n          @on-page-size-change=\"handlePageSize\"\r\n        ></Page>\r\n      </div>\r\n      <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n    </div>\r\n    <div>\r\n      <select-ammeter\r\n        ref=\"selectAmmeter\"\r\n        v-on:listenToSelectAmmeter=\"setAmmeterData\"\r\n      ></select-ammeter>\r\n      <add-bill-per\r\n        ref=\"addBillPer\"\r\n        v-on:refreshList=\"refresh\"\r\n        @isButtonload=\"isButtonload\"\r\n        @buttonload2=\"buttonload2\"\r\n      ></add-bill-per>\r\n      <completed-pre-modal\r\n        ref=\"completedPre\"\r\n        v-on:refreshList=\"refresh\"\r\n      ></completed-pre-modal>\r\n      <country-modal\r\n        ref=\"countryModal\"\r\n        v-on:getDataFromModal=\"getDataFromModal\"\r\n      ></country-modal>\r\n    </div>\r\n    <!--    稽核modal-->\r\n    <Modal\r\n      v-model=\"showCheckModel\"\r\n      width=\"80%\"\r\n      title=\"稽核结果及反馈\"\r\n      :mask-closable=\"false\"\r\n    >\r\n      <check-result-and-response ref=\"chekResultAndResponse\"></check-result-and-response>\r\n    </Modal>\r\n    <Modal v-model=\"showJhModel\" width=\"80%\" title=\"稽核结果\" :mask-closable=\"false\">\r\n      <check-result ref=\"checkResult\"></check-result>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button type=\"primary\" @click=\"nextCheck\">已查阅</Button>\r\n        <Button type=\"text\" @click=\"checkCancel\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <!-- @on-cancel=\"alarmClose\" -->\r\n    <Modal\r\n      v-model=\"showAlarmModel\"\r\n      width=\"80%\"\r\n      title=\"台账预警稽核\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n      class=\"yjjh\"\r\n    >\r\n      <alarm-check\r\n        ref=\"showAlarmModel\"\r\n        @submitChange=\"submitChange\"\r\n        @save=\"save\"\r\n        @close=\"alarmClose\"\r\n      ></alarm-check>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button size=\"large\" @click=\"alarmClose\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  addPredPowerAccount,\r\n  addAccountEs,\r\n  removeAccountEs,\r\n  getUser,\r\n  getDepartments,\r\n  accountEsTotal,\r\n  selectIdsByEsParams,\r\n} from \"@/api/account\";\r\nimport { getResCenter, getcompany } from \"@/api/alertcontrol/alertcontrol\";\r\nimport { againJoin } from \"@/api/accountBillPer\";\r\nimport {\r\n  getClassification,\r\n  getUserdata,\r\n  getUserByUserRole,\r\n  getCountrysdata,\r\n  getCountryByUserId,\r\n} from \"@/api/basedata/ammeter.js\";\r\nimport { editOwn } from \"@/api/accountSC/accountSC\";\r\nimport { getAuditResult, getAuditResultNew, getAuditResultNew_QXM } from \"@/api/account\";\r\nimport { validContractList } from \"@/api/contract\";\r\nimport {\r\n  getDates2,\r\n  testNumber,\r\n  cutDate_yyyymmdd,\r\n  getFirstDateByAccountno_yyyymmdd,\r\n  getLastDateByAccountno_yyyymmdd,\r\n  stringToDate,\r\n  getCurrentDate,\r\n} from \"@/view/account/powerAccountHelper\";\r\nimport {\r\n  _verify_StartDate,\r\n  _verify_EndDate,\r\n  verification,\r\n  unitpirceMin,\r\n  unitpirceMax,\r\n  unitpirceMax1,\r\n} from \"@/view/account/PowerAccountEs\";\r\nimport {\r\n  judge_negate,\r\n  judge_recovery,\r\n  countTaxamount,\r\n} from \"@/view/account/PowerAccountController\";\r\nimport { widthstyle } from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport excel from \"@/libs/excel\";\r\nimport axios from \"@/libs/api.request\";\r\nimport CompletedPreModal from \"@/view/account/completedPreModal\";\r\nimport AddBillPer from \"@/view/account/addBillPreModal\";\r\nimport SelectAmmeter from \"@/view/account/selectAmmeter\";\r\nimport indexData from \"@/config/index\";\r\nimport CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\nimport alarmCheck from \"@/view/account/check/alarmCheck\";\r\nimport checkResult from \"@/view/account/check/checkResult\";\r\nimport checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\nimport { mapState } from \"vuex\";\r\n\r\nimport permissionMixin from \"@/mixins/permission\";\r\nimport pageFun from \"@/mixins/pageFun\";\r\n\r\nlet dates = getDates2();\r\n\r\nexport default {\r\n  name: \"addPredPowerAccount\",\r\n  mixins: [permissionMixin, pageFun],\r\n  components: {\r\n    alarmCheck,\r\n    checkResult,\r\n    checkResultAndResponse,\r\n    CompletedPreModal,\r\n    SelectAmmeter,\r\n    AddBillPer,\r\n    CountryModal,\r\n  },\r\n  data() {\r\n    let renderStatus = (h, { row, index }) => {\r\n      var status = \"\";\r\n      let data = this.tbAccount.data[index];\r\n      for (let item of this.accountStatus) {\r\n        if (item.typeCode == row.status) {\r\n          data.statusName = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", data.statusName);\r\n    };\r\n\r\n    let renderCategory = (h, params) => {\r\n      var categoryname = \"\";\r\n      for (let item of this.categorys) {\r\n        if (item.typeCode == params.row.category) {\r\n          categoryname = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", categoryname);\r\n    };\r\n\r\n    return {\r\n      isT: true,\r\n      number2: 0,\r\n      name: \"\",\r\n      dataL: [],\r\n      isQuery: true,\r\n      number: 0,\r\n      ctgKeyList: [],\r\n      submit: [],\r\n      submit2: [],\r\n      ammeterids: [],\r\n      showCheckModel: false,\r\n      showAlarmModel: false,\r\n      showJhModel: false,\r\n      formItemWidth: widthstyle,\r\n      version: \"\",\r\n      dateList: dates,\r\n      filterColl: true, //搜索面板展开\r\n      editIndex: -1, //当前编辑行\r\n      columnsIndex: -1, //当前编辑列\r\n      editStartDate: \"\",\r\n      myStyle: [], //样式\r\n      editEndDate: \"\",\r\n      editcurusedreadings: \"\",\r\n      edittransformerullage: \"\",\r\n      edittaxrate: \"\",\r\n      editinputticketmoney: \"\",\r\n      editinputtaxticketmoney: \"\",\r\n      spinShow: false, //遮罩\r\n      categorys: [], //描述类型\r\n      editaccountmoney: \"\",\r\n      editremark: \"\",\r\n      accountStatus: [],\r\n      companies: [],\r\n      departments: [],\r\n      isAdmin: false,\r\n      company: null, //用户默认公司\r\n      country: null, //用户默认所属部门\r\n      countryName: null, //用户默认所属部门\r\n      export: {\r\n        run: false, //是否正在执行导出\r\n        data: \"\", //导出数据\r\n        totalPage: 0, //一共多少页\r\n        currentPage: 0, //当前多少页\r\n        percent: 0,\r\n        size: ********,\r\n      },\r\n      accountObj: {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        company: \"\", //分公司\r\n        projectName: \"\", //项目名称\r\n        country: \"\", //所属部门\r\n        ammeterName: \"\", //电表户号/协议编码\r\n        stationName: \"\",\r\n        accountType: \"2\", //台账类型\r\n        accountestype: 2, //台账类型\r\n        supplybureauammetercode: \"\",\r\n      },\r\n      tbAccount: {\r\n        loading: false,\r\n        columns: [],\r\n        headColumn: [\r\n          { type: \"selection\", width: 60, align: \"center\" },\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n        ],\r\n        headColumn2: [\r\n          { type: \"selection\", width: 60, align: \"center\" },\r\n          {\r\n            title: \"稽核结果及反馈\",\r\n            width: 150,\r\n            key: \"action\",\r\n            align: \"center\",\r\n            render: (h, params) => {\r\n              var that = this;\r\n              return h(\r\n                \"Button\",\r\n                {\r\n                  props: {\r\n                    type: \"primary\",\r\n                    size: \"small\",\r\n                  },\r\n                  style: {\r\n                    \"font-size\": \"10px\",\r\n                  },\r\n                  on: {\r\n                    click() {\r\n                      that.$refs.chekResultAndResponse.pcid = params.row.pcid;\r\n                      that.showCheckModel = true;\r\n                    },\r\n                  },\r\n                },\r\n                \"稽核结果及反馈\"\r\n              );\r\n            },\r\n          },\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n        ],\r\n        tailColumn: [\r\n          { title: \"所属分公司\", key: \"companyName\", align: \"center\" },\r\n          { title: \"所属部门\", key: \"countryName\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            key: \"startdate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            key: \"enddate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"用电量(度)\",\r\n            slot: \"curusedreadings\",\r\n            key: \"curusedreadings\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"电价(元)\", key: \"unitpirce\", align: \"center\" },\r\n          { title: \"普票含税金额(元)\", slot: \"inputticketmoney\", align: \"center\" },\r\n          { title: \"专票含税金额(元)\", slot: \"inputtaxticketmoney\", align: \"center\" },\r\n          { title: \"专票税率（%）\", slot: \"taxrate\", align: \"center\" },\r\n          { title: \"专票税额\", key: \"taxamount\", align: \"center\" },\r\n          { title: \"电费\", key: \"accountmoney\", align: \"center\" },\r\n          { title: \"备注\", slot: \"remark\", key: \"remark\", align: \"center\" },\r\n          { title: \"用电类型\", key: \"electrotypename\", align: \"center\", width: 94 },\r\n          {\r\n            title: \"类型描述\",\r\n            key: \"categoryname\",\r\n            align: \"center\",\r\n            render: renderCategory,\r\n          },\r\n        ],\r\n        lnColumn: [\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n        ],\r\n        scColumn: [\r\n          { title: \"电表户号/协议编码\", key: \"ammeterName\", align: \"center\" },\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n        ],\r\n        data: [],\r\n        total: 0,\r\n        exportColumns: [\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n          { title: \"电表户号/协议编码\", key: \"ammeterName\", align: \"center\" },\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n          { title: \"所属分公司\", key: \"companyName\", align: \"center\" },\r\n          { title: \"所属部门\", key: \"countryName\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            key: \"startdate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            key: \"enddate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"用电量(度)\",\r\n            slot: \"curusedreadings\",\r\n            key: \"curusedreadings\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"电价(元)\", key: \"unitpirce\", align: \"center\" },\r\n          { title: \"电费\", slot: \"accountmoney\", key: \"accountmoney\", align: \"center\" },\r\n          { title: \"备注\", slot: \"remark\", key: \"remark\", align: \"center\" },\r\n          { title: \"用电类型\", key: \"electrotypename\", align: \"center\", width: 94 },\r\n          { title: \"类型描述\", key: \"categoryname\", align: \"center\" },\r\n        ],\r\n      },\r\n      pageTotal: 0,\r\n      pageNum: 1,\r\n      pageSize: 10, //当前页\r\n    };\r\n  },\r\n  mounted() {\r\n    this.handleHeight(); //table高度自定义\r\n    this.version = indexData.version;\r\n    this.tbAccount.columns = this.tbAccount.headColumn2\r\n      .concat(this.tbAccount.scColumn)\r\n      .concat(this.tbAccount.tailColumn);\r\n    this.accountStatus = blist(\"accountStatus\");\r\n    this.categorys = blist(\"ammeterCategory\");\r\n    let that = this;\r\n    getUserByUserRole().then((res) => {\r\n      //根据权限获取分公司\r\n      that.companies = res.data.companies;\r\n      if (\r\n        res.data.isCityAdmin == true ||\r\n        res.data.isProAdmin == true ||\r\n        res.data.isSubAdmin == true\r\n      ) {\r\n        that.isAdmin = true;\r\n      }\r\n      getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {\r\n        //根据权限获取所属部门\r\n        that.departments = res.data;\r\n        that.getUserData();\r\n      });\r\n    });\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      loginId: (state) => state.user.loginId,\r\n    }),\r\n  },\r\n  methods: {\r\n    buttonload2(v) {\r\n      this.$refs.showAlarmModel.buttonload2 = v;\r\n    },\r\n    isButtonload(v) {\r\n      this.$refs.showAlarmModel.buttonload = v;\r\n    },\r\n    getTableList() {\r\n      this.$router.push({ path: \"/business/serviceEnergyConsumptionlist\" });\r\n    },\r\n    isShows(t) {\r\n      this.$refs.showAlarmModel.show = t;\r\n      if (this.$refs.showAlarmModel.show == false) {\r\n        this.number2++;\r\n        this.isT = t;\r\n      } else if (this.$refs.showAlarmModel.show == true) {\r\n        this.number2 = 0;\r\n        this.isT = !t;\r\n      }\r\n      if (this.isT && this.number2 < 10) {\r\n        this.isShows(t);\r\n      }\r\n    },\r\n    nextCheck() {\r\n      this.showAlarmModel = true;\r\n      this.isShows(true);\r\n      this.$refs.showAlarmModel.tableData = [];\r\n      this.$refs.showAlarmModel.resultList = [];\r\n      this.$refs.showAlarmModel.tableData1 = [];\r\n      this.$refs.showAlarmModel.resultList1 = [];\r\n      this.$refs.showAlarmModel.tableData2 = [];\r\n      this.$refs.showAlarmModel.resultList2 = [];\r\n      this.$refs.showAlarmModel.tableData3 = [];\r\n      this.$refs.showAlarmModel.resultList3 = [];\r\n      this.$refs.showAlarmModel.tableData4 = [];\r\n      this.$refs.showAlarmModel.resultList4 = [];\r\n      this.$refs.showAlarmModel.tableData5 = [];\r\n      this.$refs.showAlarmModel.resultList5 = [];\r\n      this.showJhModel = false;\r\n      this.$refs.showAlarmModel.activeButton = 6;\r\n    },\r\n    alarmClose() {\r\n      this.showAlarmModel = false;\r\n      this.$refs.showAlarmModel.show = false;\r\n    },\r\n    checkCancel() {\r\n      this.showJhModel = false;\r\n    },\r\n    alarmCheck() {},\r\n    selectChange() {\r\n      let that = this;\r\n      if (that.accountObj.company != undefined) {\r\n        if (that.accountObj.company == \"-1\") {\r\n          that.accountObj.country = -1;\r\n          that.accountObj.countryName = null;\r\n        } else {\r\n          getCountryByUserId(that.accountObj.company).then((res) => {\r\n            if (res.data.departments.length != 0) {\r\n              that.accountObj.country = res.data.departments[0].id;\r\n              that.accountObj.countryName = res.data.departments[0].name;\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //选择所属部门开始\r\n    chooseResponseCenter() {\r\n      if (this.accountObj.company == null || this.accountObj.company == \"-1\") {\r\n        this.$Message.info(\"请先选择分公司\");\r\n        return;\r\n      }\r\n      this.$refs.countryModal.choose(this.accountObj.company); //所属部门\r\n    },\r\n    getDataFromModal(data) {\r\n      this.accountObj.country = data.id;\r\n      this.accountObj.countryName = data.name;\r\n      //选择所属部门结束\r\n    },\r\n    getUserData() {\r\n      let that = this;\r\n      getUserdata().then((res) => {\r\n        //当前登录用户所在公司和所属部门\r\n        if (res.data.companies.length != 0) {\r\n          let companies = res.data.companies;\r\n          if (res.data.companies[0].id == \"**********\") {\r\n            companies = that.companies;\r\n          }\r\n          that.company = companies[0].id;\r\n          that.accountObj.company = companies[0].id;\r\n        }\r\n        if (res.data.departments.length != 0) {\r\n          let departments = res.data.departments;\r\n          if (res.data.companies[0].id == \"**********\" && that.departments.length != 0) {\r\n            departments = that.departments;\r\n          }\r\n          that.country = departments[0].id;\r\n          that.countryName = departments[0].name;\r\n          that.accountObj.country = Number(departments[0].id);\r\n          that.accountObj.countryName = departments[0].name;\r\n        }\r\n\r\n        that.pageNum = 1;\r\n        that.getAccountMessages();\r\n      });\r\n    },\r\n    searchList() {\r\n      if (this.accountObj.countryName == \"\") {\r\n        this.accountObj.country = \"-1\";\r\n      }\r\n      this.pageNum = 1;\r\n      this.getAccountMessages();\r\n    },\r\n    accountnoChange() {\r\n      this.searchList();\r\n    },\r\n    setAmmeterData: function (data) {\r\n      let arrayData = [];\r\n      let ctgKeyList = [];\r\n      let no = this.accountObj.accountno;\r\n      if (data != null && data.length > 0) {\r\n        data.forEach(function (item) {\r\n          let obj = {};\r\n          obj.pcid = null;\r\n          obj.ammeterName = item.ammetername;\r\n          obj.projectName = item.projectname;\r\n          obj.substation = item.substation;\r\n          obj.categoryname = item.categoryname;\r\n          obj.category = item.category;\r\n          obj.ammeterid = item.ammeterid;\r\n          obj.company = item.company;\r\n          obj.companyName = item.companyName;\r\n          obj.country = item.country;\r\n          obj.countryName = item.countryName;\r\n          obj.startdate = null;\r\n          obj.enddate = null;\r\n          obj.curusedreadings = 0;\r\n          obj.transformerullage = 0;\r\n          obj.unitpirce = 0;\r\n          obj.inputticketmoney = 0;\r\n          obj.inputtaxticketmoney = 0;\r\n          obj.taxrate = \"13\";\r\n          obj.taxamount = 0;\r\n          obj.accountmoney = 0;\r\n          obj.remark = null;\r\n          obj.electrotype = item.electrotype;\r\n          obj.stationcode5gr = item.stationcode5gr;\r\n          obj.stationname5gr = item.stationname5gr;\r\n          obj.electrotypename = item.electrotypename;\r\n          obj.stationName = item.stationName;\r\n          obj.startdate = getFirstDateByAccountno_yyyymmdd(no);\r\n          obj.enddate = getLastDateByAccountno_yyyymmdd(no);\r\n          obj.accountestype = 2;\r\n          obj.supplybureauammetercode = item.supplybureauammetercode;\r\n          obj.directsupplyflag = item.directsupplyflag;\r\n          obj.stationaddresscode = item.stationaddresscode;\r\n          arrayData.push(obj);\r\n          ctgKeyList.push({ ctgKey: item.ctgKey, ammetername: item.ammetername });\r\n        });\r\n        this.ctgKeyList = ctgKeyList;\r\n      }\r\n\r\n      let version = indexData.version;\r\n      let origin = this.tbAccount.data;\r\n      if (origin.length < 1) {\r\n        this.tbAccount.data = arrayData;\r\n      } else {\r\n        let tem = arrayData;\r\n        if (\"sc\" == version) {\r\n          origin.forEach((item) => {\r\n            for (let j = tem.length - 1; j >= 0; j--) {\r\n              let jj = tem[j];\r\n              if (item.ammeterid === jj.ammeterid) {\r\n                tem.splice(j, 1);\r\n              }\r\n            }\r\n          });\r\n        }\r\n        let total = this.pageTotal;\r\n        this.pageTotal = total + tem.length;\r\n        this.tbAccount.data = tem.concat(this.tbAccount.data);\r\n      }\r\n\r\n      this.setMyStyle(this.tbAccount.data.length);\r\n    },\r\n    //点击保存\r\n    async preserve() {\r\n      let dataL = this.$refs.accountEsTable.getSelection();\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      let version = indexData.version;\r\n      for (let i = 0; i < dataL.length; i++) {\r\n        if (dataL[i].editType == 1) {\r\n          if (\"sc\" == version && dataL[i].electrotype && dataL[i].electrotype > 1400) {\r\n            if (\r\n              dataL[i].stationcode5gr == null ||\r\n              dataL[i].stationcode5gr == undefined ||\r\n              dataL[i].stationcode5gr == \"\"\r\n            ) {\r\n              this.errorTips(\r\n                \"电表/协议编号【\" +\r\n                  dataL[i].ammeterName +\r\n                  \"】，项目名称【\" +\r\n                  dataL[i].projectName +\r\n                  \"】关联局站的5GR站址为空，请完善局站信息，或者5GR有效性清单失效，请联系无线管理员。\"\r\n              );\r\n              return;\r\n            }\r\n          }\r\n          //截止日期校验\r\n          let result = _verify_EndDate(dataL[i], dataL[i].enddate);\r\n          if (result) {\r\n            this.errorTips(result);\r\n            return;\r\n          }\r\n          b = true;\r\n          array.push(dataL[i]);\r\n        }\r\n      }\r\n      if (b) {\r\n        this.submitData(array);\r\n      } else {\r\n        this.errorTips(\"没有可保存数据\");\r\n      }\r\n    },\r\n    preserveSc() {\r\n      this.$refs.checkResult.ammeterids = this.ammeterids;\r\n      this.showJhModel = true;\r\n    },\r\n    async getContractInfo(stationCode) {\r\n      this.contractCount = 0;\r\n      await validContractList({ stationCode: stationCode }).then((res) => {\r\n        if (res.data) {\r\n          this.contractCount = res.data.length;\r\n        }\r\n      });\r\n    },\r\n    submitChange1(data) {\r\n      this.submit = data;\r\n    },\r\n    submitChange(indexList) {\r\n      let data = [];\r\n      this.submit2.map((item, index) => {\r\n        indexList.map((item2) => {\r\n          if (index == item2) {\r\n            data.push(item);\r\n          }\r\n        });\r\n      });\r\n      this.submit = data;\r\n    },\r\n    getAuditResultNew(data) {\r\n      let arr = [];\r\n      data.forEach((item) => {\r\n        arr.push(item.pcid);\r\n      });\r\n      let param = {\r\n        pcids: arr,\r\n      };\r\n      getAuditResultNew_QXM(param).then((res2) => {\r\n        this.auditResultList = res2.data;\r\n        this.auditResultList.forEach((item) => {\r\n          this.$refs.showAlarmModel.resultList.push(item.msg);\r\n          this.$refs.showAlarmModel.tableData.push(item.powerAuditEntity);\r\n          if (item.staute == \"失败\") {\r\n            // if(item.powerAuditEntity.mutiJtlteCodes=='是'\r\n            // || item.powerAuditEntity.electricityPrices=='否'\r\n            // || item.powerAuditEntity.addressConsistence=='否'\r\n            // || item.powerAuditEntity.reimbursementCycle=='否' || item.powerAuditEntity.electricityContinuity=='否' ||\r\n            // item.powerAuditEntity.shareAccuracy=='否' ||\r\n            // item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n            // item.powerAuditEntity.paymentConsistence=='否'){\r\n            if (item.powerAuditEntity.mutiJtlteCodes == \"是\") {\r\n              this.$refs.showAlarmModel.tableData4.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList4.push(item.msg);\r\n            }\r\n            if (item.powerAuditEntity.electricityPrices == \"否\") {\r\n              this.$refs.showAlarmModel.tableData5.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList5.push(item.msg);\r\n            }\r\n            if (\r\n              item.powerAuditEntity.addressConsistence == \"否\" ||\r\n              item.powerAuditEntity.reimbursementCycle == \"否\" ||\r\n              item.powerAuditEntity.electricityContinuity == \"否\" ||\r\n              item.powerAuditEntity.shareAccuracy == \"否\" ||\r\n              //   item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n              item.powerAuditEntity.paymentConsistence == \"否\" ||\r\n              item.powerAuditEntity.fluctuateContinuity == \"否\"\r\n            ) {\r\n              this.$refs.showAlarmModel.tableData2.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList2.push(item.msg);\r\n            }\r\n            // }\r\n          } else {\r\n            if (\r\n              // item.powerAuditEntity.electricityRationality == \"是\" &&\r\n              // item.powerAuditEntity.consumeContinuity == \"是\" &&\r\n              // item.powerAuditEntity.periodicAnomaly == \"是\"\r\n\r\n              // item.powerAuditEntity.electricityRationality == \"是\" && //电量合理性(省内大数据)\r\n              // item.powerAuditEntity.exclusiveAccuracy == \"是\" && //局站独享共享设置\r\n              item.powerAuditEntity.periodicAnomaly == \"是\" //台账周期合理性\r\n            ) {\r\n              this.$refs.showAlarmModel.tableData1.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList1.push(item.msg);\r\n            } else {\r\n              this.$refs.showAlarmModel.tableData3.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList3.push(item.msg);\r\n            }\r\n          }\r\n          if (this.auditResultList.length > 0) {\r\n            this.auditResultList[this.auditResultList.length - 1].progress = 1;\r\n          }\r\n          this.$refs.showAlarmModel.processData = Number(item.progress) * 100;\r\n          this.$refs.showAlarmModel.scrollList();\r\n        });\r\n      });\r\n    },\r\n    save(value) {\r\n      var that = this;\r\n      //进度条页面链接websoket后调用，传jh字段不走保存流程，走稽核流程\r\n      if (value == 1) {\r\n        that.submit[0].jh = \"1\";\r\n        that.submit[0].ymmc = \"铁塔挂账电费台账\";\r\n        that.submit.forEach((item1) => {\r\n          this.ctgKeyList.forEach((item2) => {\r\n            if (item1.ammeterName == item2.ammetername) {\r\n              item1.ctgKey = item2.ctgKey;\r\n            }\r\n          });\r\n        });\r\n        this.getAuditResultNew(that.submit.reverse());\r\n      } else {\r\n        if (that.submit[0].hasOwnProperty(\"jh\")) {\r\n          delete that.submit[0].jh;\r\n        }\r\n        if (this.name == \"current\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAccount();\r\n          }\r\n        } else if (this.name == \"all\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAllAccount();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //加入归集单，全部非强控\r\n    selectedFQK() {\r\n      let that = this;\r\n      that.$refs.addBillPer.initAmmeter(\r\n        that.$refs.showAlarmModel.selectIds3,\r\n        15,\r\n        this.accountObj.country\r\n      );\r\n    },\r\n    //提交数据\r\n    submitData(data) {\r\n      let a = [];\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let str = \"\";\r\n        let accountno = this.accountObj.accountno;\r\n        let index = 0;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          if (item.projectName != \"小计\" && item.projectName != \"合计\") {\r\n            let obj = verification(item);\r\n            if (obj.result) {\r\n              if (item.pcid == null) {\r\n                item.accountno = accountno;\r\n              }\r\n              a.push(item.ammeterid);\r\n              submitData.push(item);\r\n              number++;\r\n            } else {\r\n              str +=\r\n                \"电表/协议编号为【\" +\r\n                item.ammeterName +\r\n                \"】的台账验证没有通过：【\" +\r\n                obj.str +\r\n                \"】；\";\r\n            }\r\n          }\r\n        });\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          this.errorTips(str);\r\n        }\r\n        if (submitData.length > 0) {\r\n          this.submit = submitData;\r\n          this.submit2 = submitData;\r\n          addAccountEs(submitData).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：成功保存 \" + submitData.length + \" 条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    addElectricType() {\r\n      let companyId = this.accountObj.company;\r\n      let country = this.accountObj.country;\r\n      if (companyId != null && country != null) {\r\n        let obj = {\r\n          company: companyId,\r\n          country: country,\r\n          accountno: this.accountObj.accountno,\r\n          accountType: \"2\",\r\n          accountestype: 2,\r\n        };\r\n        this.$refs.selectAmmeter.initAmmeter(obj);\r\n      } else {\r\n        this.errorTips(\"请选择分公司和部门\");\r\n      }\r\n    },\r\n    //验证错误弹出提示框\r\n    errorTips(str) {\r\n      this.$Notice.error({\r\n        title: \"提示\",\r\n        desc: str,\r\n        duration: 10,\r\n      });\r\n    },\r\n    handlePage(value) {\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n\r\n      this.pageNum = value;\r\n      this.getAccountMessages();\r\n    },\r\n    handlePageSize(value) {\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n\r\n      this.pageSize = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //向后台请求数据 台账稽核结果报表(保存-已查阅) 电表/协议（铁塔台账录入-铁塔电费挂账台账）\r\n    getAccountMessages() {\r\n      let params = this.accountObj;\r\n      params.pageNum = this.pageNum;\r\n      params.pageSize = this.pageSize;\r\n      let req = {\r\n        url: \"/business/accountEs/selectAccountEsList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      this.tbAccount.loading = true;\r\n      axios\r\n        .request(req)\r\n        .then((res) => {\r\n          this.tbAccount.loading = false;\r\n          if (res.data) {\r\n            let data = res.data.rows;\r\n            data.forEach(function (item) {\r\n              item.editType = 0;\r\n            });\r\n            data.push(this.suntotal(data)); //小计\r\n            accountEsTotal(this.accountObj).then((res) => {\r\n              //合计\r\n              let alltotal = res.data;\r\n              alltotal.total = \"合计\";\r\n              alltotal.projectName = \"合计\";\r\n              alltotal._disabled = true;\r\n              data.push(alltotal);\r\n            });\r\n            this.tbAccount.data = data;\r\n            this.pageTotal = res.data.total || 0;\r\n            this.setMyStyle(this.tbAccount.data.length);\r\n\r\n            this.editIndex = -1;\r\n            this.columnsIndex = -1;\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //小计\r\n    suntotal(array) {\r\n      let curusedreadings = 0;\r\n      let transformerullage = 0;\r\n      let accountmoney = 0;\r\n      array.forEach(function (item) {\r\n        if (item.effective === 1) {\r\n          curusedreadings += item.curusedreadings;\r\n          transformerullage += item.transformerullage;\r\n          accountmoney += item.accountmoney;\r\n        }\r\n      });\r\n      return {\r\n        curusedreadings: curusedreadings,\r\n        transformerullage: transformerullage,\r\n        accountmoney: accountmoney,\r\n        total: \"小计\",\r\n        projectName: \"小计\",\r\n        _disabled: true,\r\n      };\r\n    },\r\n    //重置\r\n    onResetHandle() {\r\n      this.accountObj = {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        company: this.company,\r\n        country: Number(this.country), //所属部门\r\n        projectName: \"\", //项目名称\r\n        ammeterName: \"\", //电表户号/协议编码\r\n        stationName: \"\",\r\n        accountType: \"1\", //台账类型\r\n        accountestype: 3, //台账类型\r\n        supplybureauammetercode: \"\",\r\n        countryName: this.countryName,\r\n      };\r\n      this.getAccountMessages();\r\n    },\r\n    //计算单价\r\n    unitPrice(row) {\r\n      let version = indexData.version;\r\n      let accountmoney = row.accountmoney;\r\n      let curusedreadings = row.curusedreadings;\r\n      let taxamount = row.taxamount;\r\n      if (accountmoney != null && curusedreadings != null) {\r\n        let total = null;\r\n        if (curusedreadings == 0) {\r\n          total = 0;\r\n        } else {\r\n          total = accountmoney / curusedreadings;\r\n        }\r\n\r\n        row.unitpirce = total.toFixed(2);\r\n      }\r\n    },\r\n    //验证单价\r\n    validateUnitPrice(data) {\r\n      let category = data.category; //电表描述类型\r\n      let ammeteruse = data.ammeteruse; //电表用途\r\n      let unitpirce = data.unitpirce; //台账单价\r\n      {\r\n        // if (unitpirce) {\r\n        //   if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n        //     this.errorTips(\r\n        //       \"集团要求单价范围在0.3~2元，此台账单价: \" +\r\n        //         unitpirce +\r\n        //         \" 已超过范围，请确认！\"\r\n        //     );\r\n        //   }\r\n        // }\r\n        if (unitpirce) {\r\n          if (unitpirce != null && unitpirce < unitpirceMax1) {\r\n            // if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n            this.errorTips(\r\n              \"单价范围必须大于0.1元，此台账单价: \" + unitpirce + \"不在范围内，请确认！\"\r\n            );\r\n          }\r\n        }\r\n      }\r\n    },\r\n    remove() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      if (data == null || data.length === 0) {\r\n        this.errorTips(\"请选择要删除的数据\");\r\n        return;\r\n      }\r\n      this.$Modal.confirm({\r\n        title: \"提示\",\r\n        content: \"<p>是否确认删除选中信息？</p>\",\r\n        onOk: () => {\r\n          let b = true;\r\n          let ids = \"\";\r\n          let array = this.tbAccount.data;\r\n          let total = this.pageTotal;\r\n          for (let i = 0; i < data.length; i++) {\r\n            let item = data[i];\r\n            if (item.pcid != null && item.pcid.length > 0) {\r\n              if (item.pabriid) {\r\n                b = false;\r\n              }\r\n              ids += item.pcid + \",\";\r\n            } else {\r\n              for (let j = array.length - 1; j >= 0; j--) {\r\n                let jj = array[j];\r\n                if (jj.ammeterid === item.ammeterid) {\r\n                  array.splice(j, 1);\r\n                  total = total - 1;\r\n                }\r\n              }\r\n            }\r\n          }\r\n          this.pageTotal = total;\r\n          if (b) {\r\n            if (ids.length > 0) {\r\n              removeAccountEs(ids).then((res) => {\r\n                if (res.data.code == 0) {\r\n                  this.$Message.success(\"删除成功\");\r\n                  this.getAccountMessages();\r\n                }\r\n              });\r\n            }\r\n          } else {\r\n            this.errorTips(\"选中信息中有信息还没有跟归集单解除关联，请先解除关联\");\r\n          }\r\n        },\r\n        onCancel: () => {},\r\n      });\r\n    },\r\n    //加入归集单\r\n    addPreserveGJ() {\r\n      let dataL = this.$refs.accountEsTable.getSelection();\r\n      this.dataL = this.$refs.accountEsTable.getSelection();\r\n      if (dataL == null || dataL.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        this.addSubmitDataGJ(dataL);\r\n      }\r\n    },\r\n    //加入归集单\r\n    addPreserveGJAll() {\r\n      let params = this.accountObj;\r\n      params.pageNum = 1;\r\n      params.pageSize = 20000;\r\n      let req = {\r\n        url: \"/business/accountEs/selectAccountEsList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      let array1 = [];\r\n      this.tbAccount.loading = true;\r\n      axios.request(req).then((res) => {\r\n        this.tbAccount.loading = false;\r\n        let dataL = res.data.rows;\r\n        this.dataL = res.data.rows;\r\n        this.addSubmitDataGJ(dataL);\r\n      });\r\n    },\r\n    //提交归集单数据\r\n    addSubmitDataGJ(data) {\r\n      let a = [];\r\n      let str = \"\";\r\n      let str1 = \"\";\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let b = 1;\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let obj = verification(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n        });\r\n        if (b === 1) {\r\n          if (submitData.length > 0) {\r\n            this.submit = submitData;\r\n            this.submit2 = submitData;\r\n            this.preserveSc();\r\n          }\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          that.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n      }\r\n    },\r\n    openAddBillPerModal(name) {\r\n      this.name = name;\r\n      if (name === \"current\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJ();\r\n        // } else {\r\n        this.selectedAccount();\r\n        // }\r\n      } else if (name === \"all\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJAll();\r\n        // } else {\r\n        this.selectedAllAccount();\r\n        // }\r\n      }\r\n    },\r\n    //加入归集单，全部有效台账\r\n    selectedAllAccount() {\r\n      let that = this;\r\n      that.spinShow = true;\r\n      selectIdsByEsParams(this.accountObj).then((res) => {\r\n        that.spinShow = false;\r\n        if (res.data.length == 0) {\r\n          that.errorTips(\"无有效数据可加入归集单\");\r\n        } else {\r\n          that.$refs.addBillPer.initAmmeter(res.data, 15, this.accountObj.country);\r\n          // that.$refs.addBillPer.initAmmeter(\r\n          //   this.$refs.showAlarmModel.selectIds1,\r\n          //   15,\r\n          //   this.accountObj.country\r\n          // );\r\n        }\r\n      });\r\n    },\r\n    selectedAccount() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      let b = 1;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        let ids = [];\r\n        data.forEach(function (item) {\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n          ids.push(item.pcid);\r\n        });\r\n        if (b === 1) {\r\n          this.$refs.addBillPer.initAmmeter(ids, 15, this.accountObj.country);\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n      }\r\n    },\r\n    openCompletedPreModal() {\r\n      this.$refs.completedPre.initAmmeter(this.accountObj.country, 15);\r\n    },\r\n    againJoin() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      let b = true;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要重新加入归集单的台账\");\r\n      } else {\r\n        let ids = \"\";\r\n        data.forEach(function (item) {\r\n          let status = item.status;\r\n          if (status != 5) {\r\n            b = false;\r\n          }\r\n          ids += item.pcid + \",\";\r\n        });\r\n        if (b) {\r\n          againJoin(ids).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：操作成功\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        } else {\r\n          this.errorTips(\"只有已退回的台账才能重新加入归集单\");\r\n        }\r\n      }\r\n    },\r\n    refresh() {\r\n      if (this.$refs.showAlarmModel.fromGuijidan != 1) {\r\n        // window.history.go(0);\r\n        let obj = this;\r\n        obj.showAlarmModel = false;\r\n        setTimeout(function () {\r\n          obj.getAccountMessages();\r\n        }, 200);\r\n      } else {\r\n        this.showAlarmModel = true;\r\n      }\r\n    },\r\n    beforeLoadData(data, str) {\r\n      var cols = [],\r\n        keys = [];\r\n      for (var i = 0; i < this.tbAccount.exportColumns.length; i++) {\r\n        cols.push(this.tbAccount.exportColumns[i].title);\r\n        keys.push(this.tbAccount.exportColumns[i].key);\r\n      }\r\n      const params = {\r\n        title: cols,\r\n        key: keys,\r\n        data: data,\r\n        autoWidth: true,\r\n        filename: str,\r\n      };\r\n      excel.export_array_to_excel(params);\r\n      return;\r\n    },\r\n    exportCsv(name) {\r\n      this.export.run = true;\r\n      if (name === \"current\") {\r\n        this.beforeLoadData(this.tbAccount.data, \"铁塔挂账台账导出数据\");\r\n      } else if (name === \"all\") {\r\n        let params = this.accountObj;\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n        let req = {\r\n          url: \"/business/accountEs/selectAccountEsList\",\r\n          method: \"get\",\r\n          params: params,\r\n        };\r\n        this.tbAccount.loading = true;\r\n        axios\r\n          .request(req)\r\n          .then((res) => {\r\n            this.tbAccount.loading = false;\r\n            if (res.data) {\r\n              let array = res.data.rows;\r\n              accountEsTotal(this.accountObj).then((res) => {\r\n                //合计\r\n                let alltotal = res.data;\r\n                alltotal.total = \"合计\";\r\n                alltotal._disabled = true;\r\n                array.push(alltotal);\r\n                this.beforeLoadData(array, \"铁塔挂账台账导出数据\");\r\n              });\r\n            }\r\n          })\r\n          .catch((err) => {\r\n            console.log(err);\r\n          });\r\n      }\r\n    },\r\n    validate() {\r\n      if (this.columnsIndex != 6) {\r\n        let val = this.enterOperate(this.columnsIndex).data;\r\n        if (val) {\r\n          if (testNumber(val)) {\r\n            switch (this.columnsIndex) {\r\n              case 1:\r\n                this.validateStartdate();\r\n                break;\r\n              case 2:\r\n                this.validateEnddate();\r\n                break;\r\n              case 3:\r\n                this.validatecurusedreadings();\r\n                break;\r\n              case 4:\r\n                this.validateinputticketmoney();\r\n                break;\r\n              case 5:\r\n                this.validateinputtaxticketmoney();\r\n                break;\r\n            }\r\n          } else {\r\n            this.errorTips(\"请输入数字！\");\r\n          }\r\n        }\r\n      }\r\n    },\r\n    validateStartdate() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editStartDate;\r\n      let result = _verify_StartDate(data, val);\r\n      if (result) {\r\n        //失败就弹出提示内容\r\n        this.errorTips(result);\r\n        this.myStyle[this.editIndex].startdate = \"errorStle\";\r\n      } else {\r\n        this.myStyle[this.editIndex].startdate = \"myspan\";\r\n        data.startdate = val;\r\n        data.editType = 1;\r\n      }\r\n    },\r\n    validateEnddate() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editEndDate;\r\n\r\n      // 验证截止日期方法\r\n      let result = _verify_EndDate(data, val);\r\n      if (result) {\r\n        //失败就弹出提示内容，并将数据恢复初始化\r\n        this.errorTips(result);\r\n      } else {\r\n        data.enddate = val;\r\n        data.editType = 1;\r\n      }\r\n    },\r\n    validatecurusedreadings() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editcurusedreadings;\r\n      data.curusedreadings = val;\r\n      data.totalusedreadings = val;\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n    },\r\n    validatetransformerullage() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.edittransformerullage;\r\n      data.transformerullage = val;\r\n      data.editType = 1;\r\n    },\r\n    //普票\r\n    validateinputticketmoney() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editinputticketmoney;\r\n      //val = Math.abs(val);\r\n      data.inputticketmoney = parseFloat(val);\r\n      data.ticketmoney = parseFloat(val);\r\n      data.accountmoney = data.inputticketmoney + data.inputtaxticketmoney;\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      this.validateUnitPrice(data);\r\n    },\r\n    //专票\r\n    validateinputtaxticketmoney() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editinputtaxticketmoney;\r\n      //val = Math.abs(val);\r\n      data.inputtaxticketmoney = parseFloat(val);\r\n      data.taxticketmoney = parseFloat(val);\r\n      data.accountmoney = data.inputticketmoney + data.inputtaxticketmoney;\r\n      //data.taxamount = Math.abs(countTaxamount(data));\r\n      data.taxamount = countTaxamount(data);\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      this.validateUnitPrice(data);\r\n    },\r\n    //专票计算税额\r\n    settaxrate() {\r\n      let val = this.edittaxrate;\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      data.taxrate = val;\r\n      data.taxamount = Math.abs(countTaxamount(data));\r\n      data.editType = 1;\r\n    },\r\n    validateaccountmoney() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editaccountmoney;\r\n      //data.accountmoney = Math.abs(val);\r\n      data.accountmoney = val;\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      this.validateUnitPrice(data);\r\n    },\r\n    setremark() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editremark;\r\n      data.remark = val;\r\n      data.editType = 1;\r\n    },\r\n    setMyStyle(length) {\r\n      this.myStyle = [];\r\n      for (var i = 0; i < length; i++) {\r\n        this.myStyle.push({\r\n          startdate: \"myspan\",\r\n          enddate: \"myspan\",\r\n          curusedreadings: \"myspan\",\r\n          inputticketmoney: \"myspan\",\r\n          inputtaxticketmoney: \"myspan\",\r\n          taxrate: \"myspan\",\r\n          remark: \"myspan\",\r\n        });\r\n      }\r\n    },\r\n    //span点击事件将span换成输入框并且获取焦点\r\n    selectCall(row, index, columns, str) {\r\n      this.editStartDate = row.startdate;\r\n      this.editEndDate = row.enddate;\r\n      this.editcurusedreadings =\r\n        row.curusedreadings == null || row.curusedreadings === 0\r\n          ? null\r\n          : row.curusedreadings;\r\n      this.editinputticketmoney =\r\n        row.inputticketmoney == null || row.inputticketmoney === 0\r\n          ? null\r\n          : row.inputticketmoney;\r\n      this.editinputtaxticketmoney =\r\n        row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n          ? null\r\n          : row.inputtaxticketmoney;\r\n      this.edittaxrate =\r\n        row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n      this.editremark = row.remark;\r\n\r\n      this.editIndex = index;\r\n      this.columnsIndex = columns;\r\n\r\n      let a = this;\r\n      setTimeout(function () {\r\n        if (columns != 7) {\r\n          a.$refs[str + index + columns].focus();\r\n        }\r\n      }, 200);\r\n    },\r\n    //跳转到下一格\r\n    nextCell(data) {\r\n      let index = data.editIndex;\r\n      let columns = data.columnsIndex;\r\n      let row = \"\";\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n      } else if (index > -1 && columns === 7) {\r\n        //当跳转的最后一行最后一格的时候\r\n        if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n          index = 0;\r\n        } else {\r\n          index++;\r\n        }\r\n        columns = 1;\r\n      } else {\r\n        columns += 1;\r\n      }\r\n      data.editIndex = index;\r\n      data.columnsIndex = columns;\r\n      row = data.tbAccount.data[index];\r\n      if (row) {\r\n        data.editStartDate = row.startdate;\r\n        data.editEndDate = row.enddate;\r\n        data.editcurusedreadings =\r\n          row.curusedreadings == null || row.curusedreadings === 0\r\n            ? null\r\n            : row.curusedreadings;\r\n        data.editinputticketmoney =\r\n          row.inputticketmoney == null || row.inputticketmoney === 0\r\n            ? null\r\n            : row.inputticketmoney;\r\n        data.editinputtaxticketmoney =\r\n          row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n            ? null\r\n            : row.inputtaxticketmoney;\r\n        data.edittaxrate =\r\n          row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n        data.editremark = row.remark;\r\n      }\r\n\r\n      setTimeout(function () {\r\n        data.$refs[data.enterOperate(columns).str + index + columns].focus();\r\n      }, 200);\r\n    },\r\n    //根据列号返回对应的列名\r\n    enterOperate(number) {\r\n      let str = \"\";\r\n      let data = null;\r\n      switch (number) {\r\n        case 1:\r\n          str = \"startdate\";\r\n          data = this.editStartDate;\r\n          break;\r\n        case 2:\r\n          str = \"enddate\";\r\n          data = this.editEndDate;\r\n          break;\r\n        case 3:\r\n          str = \"curusedreadings\";\r\n          data = this.editcurusedreadings;\r\n          break;\r\n        case 4:\r\n          str = \"inputticketmoney\";\r\n          data = this.editinputticketmoney;\r\n          break;\r\n        case 5:\r\n          str = \"inputtaxticketmoney\";\r\n          data = this.editinputtaxticketmoney;\r\n          break;\r\n        case 6:\r\n          str = \"taxrate\";\r\n          data = this.edittaxrate;\r\n          break;\r\n        case 7:\r\n          str = \"remark\";\r\n          data = this.editremark;\r\n          break;\r\n      }\r\n      return { str: str, data: data };\r\n    },\r\n    pred() {\r\n      var lett = this;\r\n      let index = lett.editIndex;\r\n      let columns = lett.columnsIndex;\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n        lett.editIndex = index;\r\n        lett.columnsIndex = columns;\r\n        lett.editStartDate = lett.tbAccount.data[index].startdate;\r\n        setTimeout(function () {\r\n          lett.$refs[lett.enterOperate(columns).str + index + columns].focus();\r\n        }, 200);\r\n      } else {\r\n        lett.validate();\r\n        lett.setremark();\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    ellipsis(value) {\r\n      if (!value) return \"\";\r\n      if (value.length > 3) {\r\n        return value.slice(0, 3) + \"...\";\r\n      }\r\n      return value;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.page-class {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  .cl-table {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n  .button-bar {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n.yjjh > .ivu-modal-wrap > .ivu-modal {\r\n  top: 20px !important;\r\n}\r\n.mytable .ivu-table-cell {\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: normal;\r\n  word-break: break-all;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.accountEs .filter-divider {\r\n  margin: 0px;\r\n  text-align: center;\r\n}\r\n.accountEs .header-bar-show {\r\n  max-height: 300px;\r\n  padding-top: 14px;\r\n  overflow: inherit;\r\n  border-bottom: 1px solid #e8eaec;\r\n}\r\n.accountEs .header-bar-hide {\r\n  max-height: 0;\r\n  padding-top: 0;\r\n  overflow: hidden;\r\n  border-bottom: 0;\r\n}\r\n\r\n.mytable .myspan {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n}\r\n.mytable .errorStle {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n  color: red;\r\n}\r\n</style>\r\n"]}]}