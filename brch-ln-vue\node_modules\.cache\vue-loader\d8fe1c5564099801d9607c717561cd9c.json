{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addAdvanceAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addAdvanceAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBtYXBTdGF0ZSB9IGZyb20gInZ1ZXgiOw0KaW1wb3J0IHsgZ2V0QXVkaXRSZXN1bHQsIGdldEF1ZGl0UmVzdWx0TmV3LCBnZXRBdWRpdFJlc3VsdE5ld19RWE0gfSBmcm9tICJAL2FwaS9hY2NvdW50IjsNCmltcG9ydCB7DQogIHRlbXBvcmFyeVN0b3JhZ2UyLA0KICBhZGRQcmVkUG93ZXJBY2NvdW50LA0KICBhZGRBY2NvdW50RXMsDQogIHJlbW92ZUFjY291bnRFcywNCiAgZ2V0VXNlciwNCiAgZ2V0RGVwYXJ0bWVudHMsDQogIGFjY291bnRFc1RvdGFsLA0KICBzZWxlY3RJZHNCeUVzUGFyYW1zLA0KfSBmcm9tICJAL2FwaS9hY2NvdW50IjsNCmltcG9ydCB7IGdldFJlc0NlbnRlciwgZ2V0Y29tcGFueSB9IGZyb20gIkAvYXBpL2FsZXJ0Y29udHJvbC9hbGVydGNvbnRyb2wiOw0KaW1wb3J0IHsgYWdhaW5Kb2luIH0gZnJvbSAiQC9hcGkvYWNjb3VudEJpbGxQZXIiOw0KaW1wb3J0IHsNCiAgZ2V0Q2xhc3NpZmljYXRpb24sDQogIGdldFVzZXJkYXRhLA0KICBnZXRVc2VyQnlVc2VyUm9sZSwNCiAgZ2V0Q291bnRyeXNkYXRhLA0KICBnZXRDb3VudHJ5QnlVc2VySWQsDQp9IGZyb20gIkAvYXBpL2Jhc2VkYXRhL2FtbWV0ZXIuanMiOw0KaW1wb3J0IGNoZWNrUmVzdWx0QW5kUmVzcG9uc2UgZnJvbSAiQC92aWV3L2FjY291bnQvY2hlY2svY2hlY2tSZXN1bHRBbmRSZXNwb25zZSI7DQppbXBvcnQgY2hlY2tSZXN1bHQgZnJvbSAiQC92aWV3L2FjY291bnQvY2hlY2svY2hlY2tSZXN1bHQiOw0KaW1wb3J0IGFsYXJtQ2hlY2sgZnJvbSAiQC92aWV3L2FjY291bnQvY2hlY2svYWxhcm1DaGVjayI7DQppbXBvcnQgew0KICBnZXREYXRlczIsDQogIGdldERhdGVzLA0KICB0ZXN0TnVtYmVyLA0KICBnZXRGaXJzdERhdGVCeUFjY291bnRub195eXl5bW1kZCwNCiAgZ2V0TGFzdERhdGVCeUFjY291bnRub195eXl5bW1kZCwNCiAgY3V0RGF0ZV95eXl5bW1kZCwNCiAgc3RyaW5nVG9EYXRlLA0KICBnZXRDdXJyZW50RGF0ZSwNCn0gZnJvbSAiQC92aWV3L2FjY291bnQvcG93ZXJBY2NvdW50SGVscGVyIjsNCmltcG9ydCB7DQogIF92ZXJpZnlfU3RhcnREYXRlLA0KICBfdmVyaWZ5X0VuZERhdGUsDQogIHZlcmlmaWNhdGlvbiwNCiAgdW5pdHBpcmNlTWluLA0KICB1bml0cGlyY2VNYXgsDQogIHVuaXRwaXJjZU1heDEsDQp9IGZyb20gIkAvdmlldy9hY2NvdW50L1Bvd2VyQWNjb3VudEVzIjsNCmltcG9ydCB7IGp1ZGdlX25lZ2F0ZSwganVkZ2VfcmVjb3ZlcnkgfSBmcm9tICJAL3ZpZXcvYWNjb3VudC9Qb3dlckFjY291bnRDb250cm9sbGVyIjsNCmltcG9ydCBTZWxlY3RBbW1ldGVyIGZyb20gIkAvdmlldy9hY2NvdW50L3NlbGVjdEFtbWV0ZXIiOw0KaW1wb3J0IEFkZEJpbGxQZXIgZnJvbSAiQC92aWV3L2FjY291bnQvYWRkQmlsbFByZU1vZGFsIjsNCmltcG9ydCBDb21wbGV0ZWRQcmVNb2RhbCBmcm9tICJAL3ZpZXcvYWNjb3VudC9jb21wbGV0ZWRQcmVNb2RhbCI7DQppbXBvcnQgeyB3aWR0aHN0eWxlIH0gZnJvbSAiQC92aWV3L2J1c2luZXNzL21zc0FjY291bnRiaWxsL21zc0FjY291bnRiaWxsZGF0YSI7DQppbXBvcnQgQ291bnRyeU1vZGFsIGZyb20gIkAvdmlldy9iYXNlZGF0YS9hbW1ldGVyL2NvdW50cnlNb2RhbCI7DQppbXBvcnQgZXhjZWwgZnJvbSAiQC9saWJzL2V4Y2VsIjsNCmltcG9ydCB7IGJsaXN0IH0gZnJvbSAiQC9saWJzL3Rvb2xzIjsNCmltcG9ydCBheGlvcyBmcm9tICJAL2xpYnMvYXBpLnJlcXVlc3QiOw0KaW1wb3J0IGluZGV4RGF0YSBmcm9tICJAL2NvbmZpZy9pbmRleCI7DQppbXBvcnQgcGVybWlzc2lvbk1peGluIGZyb20gIkAvbWl4aW5zL3Blcm1pc3Npb24iOw0KaW1wb3J0IHBhZ2VGdW4gZnJvbSAiQC9taXhpbnMvcGFnZUZ1biI7DQoNCmxldCBkYXRlcyA9IGdldERhdGVzKCk7DQpleHBvcnQgZGVmYXVsdCB7DQogIG1peGluczogW3Blcm1pc3Npb25NaXhpbiwgcGFnZUZ1bl0sDQogIG5hbWU6ICJhZGRQcmVkUG93ZXJBY2NvdW50IiwNCiAgY29tcG9uZW50czogew0KICAgIGFsYXJtQ2hlY2ssDQogICAgY2hlY2tSZXN1bHQsDQogICAgY2hlY2tSZXN1bHRBbmRSZXNwb25zZSwNCiAgICBDb21wbGV0ZWRQcmVNb2RhbCwNCiAgICBTZWxlY3RBbW1ldGVyLA0KICAgIEFkZEJpbGxQZXIsDQogICAgQ291bnRyeU1vZGFsLA0KICB9LA0KICBkYXRhKCkgew0KICAgIGxldCByZW5kZXJTdGF0dXMgPSAoaCwgeyByb3csIGluZGV4IH0pID0+IHsNCiAgICAgIHZhciBzdGF0dXMgPSAiIjsNCiAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVtpbmRleF07DQogICAgICBmb3IgKGxldCBpdGVtIG9mIHRoaXMuYWNjb3VudFN0YXR1cykgew0KICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSByb3cuc3RhdHVzKSB7DQogICAgICAgICAgZGF0YS5zdGF0dXNOYW1lID0gaXRlbS50eXBlTmFtZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGgoImRpdiIsIGRhdGEuc3RhdHVzTmFtZSk7DQogICAgfTsNCg0KICAgIGxldCByZW5kZXJDYXRlZ29yeSA9IChoLCBwYXJhbXMpID0+IHsNCiAgICAgIHZhciBjYXRlZ29yeW5hbWUgPSAiIjsNCiAgICAgIGZvciAobGV0IGl0ZW0gb2YgdGhpcy5jYXRlZ29yeXMpIHsNCiAgICAgICAgaWYgKGl0ZW0udHlwZUNvZGUgPT0gcGFyYW1zLnJvdy5jYXRlZ29yeSkgew0KICAgICAgICAgIGNhdGVnb3J5bmFtZSA9IGl0ZW0udHlwZU5hbWU7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiBoKCJkaXYiLCBjYXRlZ29yeW5hbWUpOw0KICAgIH07DQoNCiAgICByZXR1cm4gew0KICAgICAgaXNUOiB0cnVlLA0KICAgICAgbnVtYmVyMjogMCwNCiAgICAgIG5hbWU6ICIiLA0KICAgICAgZGF0YUw6IFtdLA0KICAgICAgaXNRdWVyeTogdHJ1ZSwNCiAgICAgIG51bWJlcjogMCwNCiAgICAgIGN0Z0tleUxpc3Q6IFtdLA0KICAgICAgc3VibWl0OiBbXSwNCiAgICAgIHN1Ym1pdDI6IFtdLA0KICAgICAgYW1tZXRlcmlkczogW10sDQogICAgICBzaG93Q2hlY2tNb2RlbDogZmFsc2UsDQogICAgICBzaG93QWxhcm1Nb2RlbDogZmFsc2UsDQogICAgICBzaG93SmhNb2RlbDogZmFsc2UsDQogICAgICBmb3JtSXRlbVdpZHRoOiB3aWR0aHN0eWxlLA0KICAgICAgdmVyc2lvbjogIiIsDQogICAgICBkYXRlTGlzdDogZGF0ZXMsDQogICAgICBmaWx0ZXJDb2xsOiB0cnVlLCAvL+aQnOe0oumdouadv+WxleW8gA0KICAgICAgZWRpdEluZGV4OiAtMSwgLy/lvZPliY3nvJbovpHooYwNCiAgICAgIGNvbHVtbnNJbmRleDogLTEsIC8v5b2T5YmN57yW6L6R5YiXDQogICAgICBlZGl0U3RhcnREYXRlOiAiIiwNCiAgICAgIG15U3R5bGU6IFtdLCAvL+agt+W8jw0KICAgICAgZWRpdEVuZERhdGU6ICIiLA0KICAgICAgZWRpdGN1cnVzZWRyZWFkaW5nczogIiIsDQogICAgICBlZGl0Y3VydG90YWxyZWFkaW5nczogIiIsDQogICAgICBlZGl0dHJhbnNmb3JtZXJ1bGxhZ2U6ICIiLA0KICAgICAgc3BpblNob3c6IGZhbHNlLCAvL+mBrue9qQ0KICAgICAgY2F0ZWdvcnlzOiBbXSwgLy/mj4/ov7DnsbvlnosNCiAgICAgIGVkaXRhY2NvdW50bW9uZXk6ICIiLA0KICAgICAgZWRpdHJlbWFyazogIiIsDQogICAgICBhY2NvdW50U3RhdHVzOiBbXSwNCiAgICAgIGNvbXBhbmllczogW10sDQogICAgICBkZXBhcnRtZW50czogW10sDQogICAgICBpc0FkbWluOiBmYWxzZSwNCiAgICAgIGNvbXBhbnk6IG51bGwsIC8v55So5oi36buY6K6k5YWs5Y+4DQogICAgICBjb3VudHJ5OiBudWxsLCAvL+eUqOaIt+m7mOiupOaJgOWxnumDqOmXqA0KICAgICAgY291bnRyeU5hbWU6IG51bGwsIC8v55So5oi36buY6K6k5omA5bGe6YOo6ZeoDQogICAgICBleHBvcnQ6IHsNCiAgICAgICAgcnVuOiBmYWxzZSwgLy/mmK/lkKbmraPlnKjmiafooYzlr7zlh7oNCiAgICAgICAgZGF0YTogIiIsIC8v5a+85Ye65pWw5o2uDQogICAgICAgIHRvdGFsUGFnZTogMCwgLy/kuIDlhbHlpJrlsJHpobUNCiAgICAgICAgY3VycmVudFBhZ2U6IDAsIC8v5b2T5YmN5aSa5bCR6aG1DQogICAgICAgIHBlcmNlbnQ6IDAsDQogICAgICAgIHNpemU6IDEwMDAwMDAwLA0KICAgICAgfSwNCiAgICAgIGFjY291bnRPYmo6IHsNCiAgICAgICAgc3VwcGx5YnVyZWF1YW1tZXRlcmNvZGU6ICIiLA0KICAgICAgICBhY2NvdW50bm86IGRhdGVzWzBdLmNvZGUsIC8v5pyf5Y+3LOm7mOiupOW9k+WJjeaciA0KICAgICAgICBjb21wYW55OiAiIiwgLy/liIblhazlj7gNCiAgICAgICAgcHJvamVjdE5hbWU6ICIiLCAvL+mhueebruWQjeensA0KICAgICAgICBjb3VudHJ5OiAiIiwgLy/miYDlsZ7pg6jpl6gNCiAgICAgICAgYW1tZXRlck5hbWU6ICIiLCAvL+eUteihqOaIt+WPty/ljY/orq7nvJbnoIENCiAgICAgICAgc3RhdGlvbk5hbWU6ICIiLA0KICAgICAgICBhY2NvdW50VHlwZTogIjEiLCAvL+WPsOi0puexu+Weiw0KICAgICAgICBhY2NvdW50ZXN0eXBlOiAzLCAvL+WPsOi0puexu+Weiw0KICAgICAgICBjb3VudHJ5TmFtZTogIiIsDQogICAgICB9LA0KICAgICAgdGJBY2NvdW50OiB7DQogICAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICBjb2x1bW5zOiBbDQogICAgICAgICAgeyB0eXBlOiAic2VsZWN0aW9uIiwgd2lkdGg6IDYwLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIueoveaguOe7k+aenOWPiuWPjemmiCIsDQogICAgICAgICAgICBrZXk6ICJhY3Rpb24iLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgcmVuZGVyOiAoaCwgcGFyYW1zKSA9PiB7DQogICAgICAgICAgICAgIHZhciB0aGF0ID0gdGhpczsNCiAgICAgICAgICAgICAgcmV0dXJuIGgoDQogICAgICAgICAgICAgICAgIkJ1dHRvbiIsDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgcHJvcHM6IHsNCiAgICAgICAgICAgICAgICAgICAgdHlwZTogInByaW1hcnkiLA0KICAgICAgICAgICAgICAgICAgICBzaXplOiAic21hbGwiLA0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgIHN0eWxlOiB7DQogICAgICAgICAgICAgICAgICAgICJmb250LXNpemUiOiAiMTBweCIsDQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgb246IHsNCiAgICAgICAgICAgICAgICAgICAgY2xpY2soKSB7DQogICAgICAgICAgICAgICAgICAgICAgdGhhdC4kcmVmcy5jaGVrUmVzdWx0QW5kUmVzcG9uc2UucGNpZCA9IHBhcmFtcy5yb3cucGNpZDsNCiAgICAgICAgICAgICAgICAgICAgICB0aGF0LnNob3dDaGVja01vZGVsID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAi56i95qC457uT5p6c5Y+K5Y+N6aaIIg0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIG1pbldpZHRoOiAxMjAsDQogICAgICAgICAgICBtYXhXaWR0aDogMTUwLA0KICAgICAgICAgIH0sDQogICAgICAgICAgeyB0aXRsZTogIumhueebruWQjeensCIsIGtleTogInByb2plY3ROYW1lIiwgc2xvdDogInByb2plY3ROYW1lIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIueUteihqOaIt+WPty/ljY/orq7nvJbnoIEiLCBrZXk6ICJhbW1ldGVyTmFtZSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5L6b55S15bGA57yW56CBIiwNCiAgICAgICAgICAgIGtleTogInN1cHBseWJ1cmVhdWFtbWV0ZXJjb2RlIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLmiYDlsZ7liIblhazlj7giLCBrZXk6ICJjb21wYW55TmFtZSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLmiYDlsZ7pg6jpl6giLCBrZXk6ICJjb3VudHJ5TmFtZSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLlsYDnq5kiLCBrZXk6ICJzdGF0aW9uTmFtZSIsIGFsaWduOiAiY2VudGVyIiwgd2lkdGg6IDE2MCB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi6LW35aeL5pel5pyfIiwNCiAgICAgICAgICAgIHNsb3Q6ICJzdGFydGRhdGUiLA0KICAgICAgICAgICAga2V5OiAic3RhcnRkYXRlIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA5MCwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5oiq5q2i5pel5pyfIiwNCiAgICAgICAgICAgIHNsb3Q6ICJlbmRkYXRlIiwNCiAgICAgICAgICAgIGtleTogImVuZGRhdGUiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDkwLA0KICAgICAgICAgIH0sDQogICAgICAgICAgeyB0aXRsZTogIuacrOacn+i1t+W6piIsIGtleTogInByZXZ0b3RhbHJlYWRpbmdzIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmnKzmnJ/mraLluqYiLA0KICAgICAgICAgICAgc2xvdDogImN1cnRvdGFscmVhZGluZ3MiLA0KICAgICAgICAgICAga2V5OiAiY3VydG90YWxyZWFkaW5ncyIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi55So55S16YePKOW6pikiLCBrZXk6ICJjdXJ1c2VkcmVhZGluZ3MiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi55S15Lu3KOWFgykiLCBrZXk6ICJ1bml0cGlyY2UiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi55S16LS5Iiwgc2xvdDogImFjY291bnRtb25leSIsIGtleTogImFjY291bnRtb25leSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLlpIfms6giLCBzbG90OiAicmVtYXJrIiwga2V5OiAicmVtYXJrIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIueUqOeUteexu+WeiyIsIGtleTogImVsZWN0cm90eXBlbmFtZSIsIGFsaWduOiAiY2VudGVyIiwgd2lkdGg6IDk0IH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLnsbvlnovmj4/ov7AiLA0KICAgICAgICAgICAga2V5OiAiY2F0ZWdvcnluYW1lIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHJlbmRlcjogcmVuZGVyQ2F0ZWdvcnksDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgZGF0YTogW10sDQogICAgICAgIHRvdGFsOiAwLA0KICAgICAgICBleHBvcnRDb2x1bW5zOiBbDQogICAgICAgICAgeyB0aXRsZTogIumhueebruWQjeensCIsIGtleTogInByb2plY3ROYW1lIiwgc2xvdDogInByb2plY3ROYW1lIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIueUteihqOaIt+WPty/ljY/orq7nvJbnoIEiLCBrZXk6ICJhbW1ldGVyTmFtZSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLkvpvnlLXlsYDnlLXooajnvJblj7ciLCBrZXk6ICJzdXBwbHlidXJlYXVhbW1ldGVyY29kZSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLmiYDlsZ7liIblhazlj7giLCBrZXk6ICJjb21wYW55TmFtZSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLmiYDlsZ7pg6jpl6giLCBrZXk6ICJjb3VudHJ5TmFtZSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLlsYDnq5kiLCBrZXk6ICJzdGF0aW9uTmFtZSIsIGFsaWduOiAiY2VudGVyIiwgd2lkdGg6IDYwIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLotbflp4vml6XmnJ8iLA0KICAgICAgICAgICAgc2xvdDogInN0YXJ0ZGF0ZSIsDQogICAgICAgICAgICBrZXk6ICJzdGFydGRhdGUiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDkwLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmiKrmraLml6XmnJ8iLA0KICAgICAgICAgICAgc2xvdDogImVuZGRhdGUiLA0KICAgICAgICAgICAga2V5OiAiZW5kZGF0ZSIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB3aWR0aDogOTAsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5pys5pyf6LW35bqmIiwga2V5OiAicHJldnRvdGFscmVhZGluZ3MiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuacrOacn+atouW6piIsDQogICAgICAgICAgICBzbG90OiAiY3VydG90YWxyZWFkaW5ncyIsDQogICAgICAgICAgICBrZXk6ICJjdXJ0b3RhbHJlYWRpbmdzIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLnlKjnlLXph48o5bqmKSIsIGtleTogImN1cnVzZWRyZWFkaW5ncyIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLnlLXku7co5YWDKSIsIGtleTogInVuaXRwaXJjZSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLnlLXotLkiLCBzbG90OiAiYWNjb3VudG1vbmV5Iiwga2V5OiAiYWNjb3VudG1vbmV5IiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuWkh+azqCIsIHNsb3Q6ICJyZW1hcmsiLCBrZXk6ICJyZW1hcmsiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi55So55S157G75Z6LIiwga2V5OiAiZWxlY3Ryb3R5cGVuYW1lIiwgYWxpZ246ICJjZW50ZXIiLCB3aWR0aDogOTQgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuexu+Wei+aPj+i/sCIsDQogICAgICAgICAgICBrZXk6ICJjYXRlZ29yeW5hbWUiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgcmVuZGVyOiByZW5kZXJDYXRlZ29yeSwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICAgIHBhZ2VUb3RhbDogMCwNCiAgICAgIHBhZ2VOdW06IDEsDQogICAgICBwYWdlU2l6ZTogMTAsIC8v5b2T5YmN6aG1DQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmhhbmRsZUhlaWdodCgpOyAvL3RhYmxl6auY5bqm6Ieq5a6a5LmJDQoNCiAgICB0aGlzLnZlcnNpb24gPSBpbmRleERhdGEudmVyc2lvbjsNCg0KICAgIHRoaXMuYWNjb3VudFN0YXR1cyA9IGJsaXN0KCJhY2NvdW50U3RhdHVzIik7DQogICAgdGhpcy5jYXRlZ29yeXMgPSBibGlzdCgiYW1tZXRlckNhdGVnb3J5Iik7DQogICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgIGdldFVzZXJCeVVzZXJSb2xlKCkudGhlbigocmVzKSA9PiB7DQogICAgICAvL+agueaNruadg+mZkOiOt+WPluWIhuWFrOWPuA0KICAgICAgdGhhdC5jb21wYW5pZXMgPSByZXMuZGF0YS5jb21wYW5pZXM7DQogICAgICBpZiAoDQogICAgICAgIHJlcy5kYXRhLmlzQ2l0eUFkbWluID09IHRydWUgfHwNCiAgICAgICAgcmVzLmRhdGEuaXNQcm9BZG1pbiA9PSB0cnVlIHx8DQogICAgICAgIHJlcy5kYXRhLmlzU3ViQWRtaW4gPT0gdHJ1ZQ0KICAgICAgKSB7DQogICAgICAgIHRoYXQuaXNBZG1pbiA9IHRydWU7DQogICAgICB9DQogICAgICBnZXRDb3VudHJ5c2RhdGEoeyBvcmdDb2RlOiByZXMuZGF0YS5jb21wYW5pZXNbMF0uaWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIC8v5qC55o2u5p2D6ZmQ6I635Y+W5omA5bGe6YOo6ZeoDQogICAgICAgIHRoYXQuZGVwYXJ0bWVudHMgPSByZXMuZGF0YTsNCiAgICAgICAgdGhhdC5nZXRVc2VyRGF0YSgpOw0KICAgICAgfSk7DQogICAgfSk7DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLi4ubWFwU3RhdGUoew0KICAgICAgbG9naW5JZDogKHN0YXRlKSA9PiBzdGF0ZS51c2VyLmxvZ2luSWQsDQogICAgfSksDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBidXR0b25sb2FkMih2KSB7DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLmJ1dHRvbmxvYWQyID0gdjsNCiAgICB9LA0KICAgIGlzQnV0dG9ubG9hZCh2KSB7DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLmJ1dHRvbmxvYWQgPSB2Ow0KICAgIH0sDQogICAgaXNTaG93cyh0KSB7DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnNob3cgPSB0Ow0KICAgICAgaWYgKHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwuc2hvdyA9PSBmYWxzZSkgew0KICAgICAgICB0aGlzLm51bWJlcjIrKzsNCiAgICAgICAgdGhpcy5pc1QgPSB0cnVlOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnNob3cgPT0gdHJ1ZSkgew0KICAgICAgICB0aGlzLm51bWJlcjIgPSAwOw0KICAgICAgICB0aGlzLmlzVCA9IGZhbHNlOw0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuaXNUICYmIHRoaXMubnVtYmVyMiA8IDEwKSB7DQogICAgICAgIHRoaXMuaXNTaG93cyh0KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIG5leHRDaGVjaygpIHsNCiAgICAgIHRoaXMuc2hvd0FsYXJtTW9kZWwgPSB0cnVlOw0KICAgICAgdGhpcy5pc1Nob3dzKHRydWUpOw0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGEgPSBbXTsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwucmVzdWx0TGlzdCA9IFtdOw0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGExID0gW107DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnJlc3VsdExpc3QxID0gW107DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnRhYmxlRGF0YTIgPSBbXTsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwucmVzdWx0TGlzdDIgPSBbXTsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwudGFibGVEYXRhMyA9IFtdOw0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5yZXN1bHRMaXN0MyA9IFtdOw0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGE0ID0gW107DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnJlc3VsdExpc3Q0ID0gW107DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnRhYmxlRGF0YTUgPSBbXTsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwucmVzdWx0TGlzdDUgPSBbXTsNCiAgICAgIHRoaXMuc2hvd0poTW9kZWwgPSBmYWxzZTsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwuYWN0aXZlQnV0dG9uID0gNjsNCiAgICB9LA0KICAgIGFsYXJtQ2xvc2UoKSB7DQogICAgICB0aGlzLnNob3dBbGFybU1vZGVsID0gZmFsc2U7DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnNob3cgPSBmYWxzZTsNCiAgICB9LA0KICAgIGNoZWNrQ2FuY2VsKCkgew0KICAgICAgdGhpcy5zaG93SmhNb2RlbCA9IGZhbHNlOw0KICAgIH0sDQogICAgYWxhcm1DaGVjaygpIHt9LA0KICAgIHNlbGVjdENoYW5nZSgpIHsNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIGlmICh0aGF0LmFjY291bnRPYmouY29tcGFueSAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgaWYgKHRoYXQuYWNjb3VudE9iai5jb21wYW55ID09ICItMSIpIHsNCiAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeSA9IC0xOw0KICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb3VudHJ5TmFtZSA9IG51bGw7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgZ2V0Q291bnRyeUJ5VXNlcklkKHRoYXQuYWNjb3VudE9iai5jb21wYW55KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuZGF0YS5kZXBhcnRtZW50cy5sZW5ndGggIT0gMCkgew0KICAgICAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeSA9IHJlcy5kYXRhLmRlcGFydG1lbnRzWzBdLmlkOw0KICAgICAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeU5hbWUgPSByZXMuZGF0YS5kZXBhcnRtZW50c1swXS5uYW1lOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvL+mAieaLqeaJgOWxnumDqOmXqOW8gOWniw0KICAgIGNob29zZVJlc3BvbnNlQ2VudGVyKCkgew0KICAgICAgaWYgKHRoaXMuYWNjb3VudE9iai5jb21wYW55ID09IG51bGwgfHwgdGhpcy5hY2NvdW50T2JqLmNvbXBhbnkgPT0gIi0xIikgew0KICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIuivt+WFiOmAieaLqeWIhuWFrOWPuCIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLiRyZWZzLmNvdW50cnlNb2RhbC5jaG9vc2UodGhpcy5hY2NvdW50T2JqLmNvbXBhbnkpOyAvL+aJgOWxnumDqOmXqA0KICAgIH0sDQogICAgZ2V0RGF0YUZyb21Nb2RhbChkYXRhKSB7DQogICAgICB0aGlzLmFjY291bnRPYmouY291bnRyeSA9IGRhdGEuaWQ7DQogICAgICB0aGlzLmFjY291bnRPYmouY291bnRyeU5hbWUgPSBkYXRhLm5hbWU7DQogICAgICAvL+mAieaLqeaJgOWxnumDqOmXqOe7k+adnw0KICAgIH0sDQogICAgZ2V0VXNlckRhdGEoKSB7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICBnZXRVc2VyZGF0YSgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAvL+W9k+WJjeeZu+W9leeUqOaIt+aJgOWcqOWFrOWPuOWSjOaJgOWxnumDqOmXqA0KICAgICAgICBpZiAocmVzLmRhdGEuY29tcGFuaWVzLmxlbmd0aCAhPSAwKSB7DQogICAgICAgICAgbGV0IGNvbXBhbmllcyA9IHJlcy5kYXRhLmNvbXBhbmllczsNCiAgICAgICAgICBpZiAocmVzLmRhdGEuY29tcGFuaWVzWzBdLmlkID09ICIyNjAwMDAwMDAwIikgew0KICAgICAgICAgICAgY29tcGFuaWVzID0gdGhhdC5jb21wYW5pZXM7DQogICAgICAgICAgfQ0KICAgICAgICAgIHRoYXQuY29tcGFueSA9IGNvbXBhbmllc1swXS5pZDsNCiAgICAgICAgICB0aGF0LmFjY291bnRPYmouY29tcGFueSA9IGNvbXBhbmllc1swXS5pZDsNCiAgICAgICAgfQ0KICAgICAgICBpZiAocmVzLmRhdGEuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApIHsNCiAgICAgICAgICBsZXQgZGVwYXJ0bWVudHMgPSByZXMuZGF0YS5kZXBhcnRtZW50czsNCiAgICAgICAgICBpZiAocmVzLmRhdGEuY29tcGFuaWVzWzBdLmlkID09ICIyNjAwMDAwMDAwIiAmJiB0aGF0LmRlcGFydG1lbnRzLmxlbmd0aCAhPSAwKSB7DQogICAgICAgICAgICBkZXBhcnRtZW50cyA9IHRoYXQuZGVwYXJ0bWVudHM7DQogICAgICAgICAgfQ0KICAgICAgICAgIHRoYXQuY291bnRyeSA9IGRlcGFydG1lbnRzWzBdLmlkOw0KICAgICAgICAgIHRoYXQuY291bnRyeU5hbWUgPSBkZXBhcnRtZW50c1swXS5uYW1lOw0KICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb3VudHJ5ID0gTnVtYmVyKGRlcGFydG1lbnRzWzBdLmlkKTsNCiAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeU5hbWUgPSBkZXBhcnRtZW50c1swXS5uYW1lOw0KICAgICAgICB9DQoNCiAgICAgICAgdGhhdC5wYWdlTnVtID0gMTsNCiAgICAgICAgdGhhdC5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgc2VhcmNoTGlzdCgpIHsNCiAgICAgIGlmICh0aGlzLmFjY291bnRPYmouY291bnRyeU5hbWUgPT0gIiIpIHsNCiAgICAgICAgdGhpcy5hY2NvdW50T2JqLmNvdW50cnkgPSAiLTEiOw0KICAgICAgfQ0KICAgICAgdGhpcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgfSwNCiAgICBhY2NvdW50bm9DaGFuZ2UoKSB7DQogICAgICB0aGlzLnNlYXJjaExpc3QoKTsNCiAgICB9LA0KICAgIHNldEFtbWV0ZXJEYXRhOiBmdW5jdGlvbiAoZGF0YSkgew0KICAgICAgbGV0IGFycmF5RGF0YSA9IFtdOw0KICAgICAgbGV0IGN0Z0tleUxpc3QgPSBbXTsNCiAgICAgIGxldCBubyA9IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm87DQoNCiAgICAgIGlmIChkYXRhICE9IG51bGwgJiYgZGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgIGxldCBvYmogPSB7fTsNCiAgICAgICAgICBvYmoucGNpZCA9IGl0ZW0ucGNpZDsNCiAgICAgICAgICBvYmouYW1tZXRlck5hbWUgPSBpdGVtLmFtbWV0ZXJuYW1lOw0KICAgICAgICAgIG9iai5wcm9qZWN0TmFtZSA9IGl0ZW0ucHJvamVjdG5hbWU7DQogICAgICAgICAgb2JqLnN1YnN0YXRpb24gPSBpdGVtLnN1YnN0YXRpb247DQogICAgICAgICAgb2JqLmNhdGVnb3J5bmFtZSA9IGl0ZW0uY2F0ZWdvcnluYW1lOw0KICAgICAgICAgIG9iai5jYXRlZ29yeSA9IGl0ZW0uY2F0ZWdvcnk7DQogICAgICAgICAgb2JqLmFtbWV0ZXJpZCA9IGl0ZW0uYW1tZXRlcmlkOw0KICAgICAgICAgIG9iai5jb21wYW55ID0gaXRlbS5jb21wYW55Ow0KICAgICAgICAgIG9iai5jb21wYW55TmFtZSA9IGl0ZW0uY29tcGFueU5hbWU7DQogICAgICAgICAgb2JqLmNvdW50cnkgPSBpdGVtLmNvdW50cnk7DQogICAgICAgICAgb2JqLmFtbWV0ZXJ1c2UgPSBpdGVtLmFtbWV0ZXJ1c2U7DQogICAgICAgICAgb2JqLmNvdW50cnlOYW1lID0gaXRlbS5jb3VudHJ5TmFtZTsNCiAgICAgICAgICBvYmouc3RhcnRkYXRlID0gbnVsbDsNCiAgICAgICAgICBvYmouZW5kZGF0ZSA9IG51bGw7DQogICAgICAgICAgb2JqLnByZXZ0b3RhbHJlYWRpbmdzID0gMDsNCiAgICAgICAgICBvYmouY3VydG90YWxyZWFkaW5ncyA9IDA7DQogICAgICAgICAgb2JqLmN1cnVzZWRyZWFkaW5ncyA9IDA7DQogICAgICAgICAgb2JqLnRyYW5zZm9ybWVydWxsYWdlID0gMDsNCiAgICAgICAgICBvYmoudW5pdHBpcmNlID0gMDsNCiAgICAgICAgICBvYmouYWNjb3VudG1vbmV5ID0gMDsNCiAgICAgICAgICBvYmoucmVtYXJrID0gbnVsbDsNCiAgICAgICAgICBvYmouZWxlY3Ryb3R5cGUgPSBpdGVtLmVsZWN0cm90eXBlOw0KICAgICAgICAgIG9iai5zdGF0aW9uY29kZTVnciA9IGl0ZW0uc3RhdGlvbmNvZGU1Z3I7DQogICAgICAgICAgb2JqLnN0YXRpb25uYW1lNWdyID0gaXRlbS5zdGF0aW9ubmFtZTVncjsNCiAgICAgICAgICBvYmouZWxlY3Ryb3R5cGVuYW1lID0gaXRlbS5lbGVjdHJvdHlwZW5hbWU7DQogICAgICAgICAgb2JqLnN0YXRpb25OYW1lID0gaXRlbS5zdGF0aW9uTmFtZTsNCiAgICAgICAgICBvYmouc3RhcnRkYXRlID0gZ2V0Rmlyc3REYXRlQnlBY2NvdW50bm9feXl5eW1tZGQobm8pOw0KICAgICAgICAgIG9iai5lbmRkYXRlID0gZ2V0TGFzdERhdGVCeUFjY291bnRub195eXl5bW1kZChubyk7DQogICAgICAgICAgb2JqLmFjY291bnRlc3R5cGUgPSAzOw0KICAgICAgICAgIG9iai5zdXBwbHlidXJlYXVhbW1ldGVyY29kZSA9IGl0ZW0uc3VwcGx5YnVyZWF1YW1tZXRlcmNvZGU7DQogICAgICAgICAgYXJyYXlEYXRhLnB1c2gob2JqKTsNCiAgICAgICAgICBjdGdLZXlMaXN0LnB1c2goeyBjdGdLZXk6IGl0ZW0uY3RnS2V5LCBhbW1ldGVybmFtZTogaXRlbS5hbW1ldGVybmFtZSB9KTsNCiAgICAgICAgfSk7DQogICAgICAgIHRoaXMuY3RnS2V5TGlzdCA9IGN0Z0tleUxpc3Q7DQogICAgICB9DQogICAgICBpZiAoYXJyYXlEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgYXJyYXlEYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICBsZXQgcmVxID0gew0KICAgICAgICAgICAgdXJsOiAiL2J1c2luZXNzL2FjY291bnRFcy9zZWxlY3RQcmV2dG90YWxyZWFkaW5ncyIsDQogICAgICAgICAgICBtZXRob2Q6ICJwb3N0IiwNCiAgICAgICAgICAgIHBhcmFtczogeyBhbW1ldGVyaWQ6IGl0ZW0uYW1tZXRlcmlkIH0sDQogICAgICAgICAgfTsNCiAgICAgICAgICBheGlvcy5yZXF1ZXN0KHJlcSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLmRhdGEpIHsNCiAgICAgICAgICAgICAgaXRlbS5wcmV2dG90YWxyZWFkaW5ncyA9IHJlcy5kYXRhOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgbGV0IG9yaWdpbiA9IHRoaXMudGJBY2NvdW50LmRhdGE7DQogICAgICBpZiAob3JpZ2luLmxlbmd0aCA8IDEpIHsNCiAgICAgICAgdGhpcy50YkFjY291bnQuZGF0YSA9IGFycmF5RGF0YTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldCB0ZW0gPSBhcnJheURhdGE7DQogICAgICAgIGxldCB0b3RhbCA9IHRoaXMucGFnZVRvdGFsOw0KICAgICAgICB0aGlzLnBhZ2VUb3RhbCA9IHRvdGFsICsgdGVtLmxlbmd0aDsNCiAgICAgICAgdGhpcy50YkFjY291bnQuZGF0YSA9IHRlbS5jb25jYXQodGhpcy50YkFjY291bnQuZGF0YSk7DQogICAgICB9DQoNCiAgICAgIHRoaXMuc2V0TXlTdHlsZSh0aGlzLnRiQWNjb3VudC5kYXRhLmxlbmd0aCk7DQogICAgfSwNCiAgICAvL+aaguWtmA0KICAgIHRlbXBvcmFyeVN0b3JhZ2UoKSB7DQogICAgICBsZXQgYXJyYXkgPSBbXTsNCiAgICAgIHRoaXMudGJBY2NvdW50LmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICBpZiAoaXRlbS5lZGl0VHlwZSA9PSAxKSB7DQogICAgICAgICAgYXJyYXkucHVzaChpdGVtKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tDQogICAgICBsZXQgZGF0YSA9IGFycmF5Ow0KICAgICAgaWYgKGRhdGEgIT0gbnVsbCAmJiBkYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbGV0IG51bWJlciA9IDA7DQogICAgICAgIGxldCBzdWJtaXREYXRhID0gW107DQogICAgICAgIGxldCBzdHIgPSAiIjsNCiAgICAgICAgbGV0IGFjY291bnRubyA9IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm87DQogICAgICAgIGxldCBpbmRleCA9IDA7DQogICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgIGlmIChpdGVtLnByb2plY3ROYW1lICE9ICLlsI/orqEiICYmIGl0ZW0ucHJvamVjdE5hbWUgIT0gIuWQiOiuoSIpIHsNCiAgICAgICAgICAgIGlmIChpdGVtLnBjaWQgPT0gbnVsbCkgew0KICAgICAgICAgICAgICBpdGVtLmFjY291bnRubyA9IGFjY291bnRubzsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIE9iamVjdC5hc3NpZ24oaXRlbSwgeyB0b3RhbHVzZWRyZWFkaW5nczogaXRlbS5jdXJ1c2VkcmVhZGluZ3MgfSk7IC8vIOehruS/nemihOS7mOaAu+eUtemHjyDnrYnkuo7nlKjnlLXph48NCiAgICAgICAgICAgIHN1Ym1pdERhdGEucHVzaChpdGVtKTsNCiAgICAgICAgICAgIG51bWJlcisrOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICAgIGlmIChzdWJtaXREYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0ZW1wb3JhcnlTdG9yYWdlMihzdWJtaXREYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuZGF0YS5jb2RlID09IDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKHsNCiAgICAgICAgICAgICAgICBjb250ZW50OiAi5o+Q56S677ya5oiQ5Yqf5pqC5a2YICIgKyBzdWJtaXREYXRhLmxlbmd0aCArICIg5p2h5pWw5o2uIiwNCiAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTAsDQogICAgICAgICAgICAgICAgY2xvc2FibGU6IHRydWUsDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvL+eCueWHu+S/neWtmA0KICAgIHByZXNlcnZlKCkgew0KICAgICAgbGV0IGRhdGFMID0gdGhpcy4kcmVmcy5hY2NvdW50RXNUYWJsZS5nZXRTZWxlY3Rpb24oKTsNCiAgICAgIGxldCBiID0gZmFsc2U7DQogICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGE7DQogICAgICBsZXQgYXJyYXkgPSBbXTsNCiAgICAgIGxldCB2ZXJzaW9uID0gaW5kZXhEYXRhLnZlcnNpb247DQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGRhdGFMLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGlmIChkYXRhTFtpXS5lZGl0VHlwZSA9PSAxKSB7DQogICAgICAgICAgaWYgKA0KICAgICAgICAgICAgInNjIiA9PSB2ZXJzaW9uICYmDQogICAgICAgICAgICBkYXRhTFtpXS5lbGVjdHJvdHlwZSAmJg0KICAgICAgICAgICAgZGF0YUxbaV0uZWxlY3Ryb3R5cGUgPiAxNDAwICYmDQogICAgICAgICAgICAoZGF0YUxbaV0uc3RhdGlvbmNvZGU1Z3IgPT0gbnVsbCB8fA0KICAgICAgICAgICAgICBkYXRhTFtpXS5zdGF0aW9uY29kZTVnciA9PSB1bmRlZmluZWQgfHwNCiAgICAgICAgICAgICAgZGF0YUxbaV0uc3RhdGlvbmNvZGU1Z3IgPT0gIiIpDQogICAgICAgICAgKSB7DQogICAgICAgICAgICB0aGlzLmVycm9yVGlwcygNCiAgICAgICAgICAgICAgIueUteihqC/ljY/orq7nvJblj7fjgJAiICsNCiAgICAgICAgICAgICAgICBkYXRhTFtpXS5hbW1ldGVyTmFtZSArDQogICAgICAgICAgICAgICAgIuOAke+8jOmhueebruWQjeensOOAkCIgKw0KICAgICAgICAgICAgICAgIGRhdGFMW2ldLnByb2plY3ROYW1lICsNCiAgICAgICAgICAgICAgICAi44CR5YWz6IGU5bGA56uZ55qENUdS56uZ5Z2A5Li656m677yM6K+35a6M5ZaE5bGA56uZ5L+h5oGv77yM5oiW6ICFNUdS5pyJ5pWI5oCn5riF5Y2V5aSx5pWI77yM6K+36IGU57O75peg57q/566h55CG5ZGY44CCIg0KICAgICAgICAgICAgKTsNCiAgICAgICAgICB9DQogICAgICAgICAgYiA9IHRydWU7DQogICAgICAgICAgYXJyYXkucHVzaChkYXRhTFtpXSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGlmIChiKSB7DQogICAgICAgIHRoaXMuc3VibWl0RGF0YShhcnJheSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmVycm9yVGlwcygi5rKh5pyJ5Y+v5L+d5a2Y5pWw5o2uIik7DQogICAgICB9DQogICAgfSwNCiAgICAvL+Wbm+W3neiDveiAl+eoveaguOa1geeoiw0KICAgIHByZXNlcnZlU2MoKSB7DQogICAgICB0aGlzLiRyZWZzLmNoZWNrUmVzdWx0LmFtbWV0ZXJpZHMgPSB0aGlzLmFtbWV0ZXJpZHM7DQogICAgICB0aGlzLnNob3dKaE1vZGVsID0gdHJ1ZTsNCiAgICB9LA0KICAgIHN1Ym1pdENoYW5nZShpbmRleExpc3QpIHsNCiAgICAgIGxldCBkYXRhID0gW107DQogICAgICB0aGlzLnN1Ym1pdDIubWFwKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICBpbmRleExpc3QubWFwKChpdGVtMikgPT4gew0KICAgICAgICAgIGlmIChpbmRleCA9PSBpdGVtMikgew0KICAgICAgICAgICAgZGF0YS5wdXNoKGl0ZW0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICAgIHRoaXMuc3VibWl0ID0gZGF0YTsNCiAgICB9LA0KICAgIHN1Ym1pdENoYW5nZTEoZGF0YSkgew0KICAgICAgdGhpcy5zdWJtaXQgPSBkYXRhOw0KICAgIH0sDQogICAgZ2V0QXVkaXRSZXN1bHROZXcoZGF0YSkgew0KICAgICAgbGV0IGFyciA9IFtdOw0KICAgICAgZGF0YS5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGFyci5wdXNoKGl0ZW0ucGNpZCk7DQogICAgICB9KTsNCiAgICAgIGxldCBwYXJhbSA9IHsNCiAgICAgICAgcGNpZHM6IGFyciwNCiAgICAgIH07DQogICAgICBnZXRBdWRpdFJlc3VsdE5ld19RWE0ocGFyYW0pLnRoZW4oKHJlczIpID0+IHsNCiAgICAgICAgdGhpcy5hdWRpdFJlc3VsdExpc3QgPSByZXMyLmRhdGE7DQogICAgICAgIGlmICh0aGlzLmF1ZGl0UmVzdWx0TGlzdCAmJiB0aGlzLmF1ZGl0UmVzdWx0TGlzdC5sZW5ndGggPT0gMCkgew0KICAgICAgICAgIHRoaXMubnVtYmVyKys7DQogICAgICAgICAgdGhpcy5pc1F1ZXJ5ID0gdHJ1ZTsNCiAgICAgICAgfSBlbHNlIGlmIChkYXRhLmxlbmd0aCAhPSB0aGlzLmF1ZGl0UmVzdWx0TGlzdC5sZW5ndGgpIHsNCiAgICAgICAgICB0aGlzLm51bWJlcisrOw0KICAgICAgICAgIHRoaXMuaXNRdWVyeSA9IHRydWU7DQogICAgICAgIH0gZWxzZSBpZiAoZGF0YS5sZW5ndGggPT0gdGhpcy5hdWRpdFJlc3VsdExpc3QubGVuZ3RoKSB7DQogICAgICAgICAgdGhpcy5pc1F1ZXJ5ID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5udW1iZXIgPSAwOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuaXNRdWVyeSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMubnVtYmVyID0gMDsNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5pc1F1ZXJ5ICYmIHRoaXMubnVtYmVyIDwgMykgew0KICAgICAgICAgIGlmIChkYXRhLmxlbmd0aCA+IDMwMCkgew0KICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB0aGlzLmdldEF1ZGl0UmVzdWx0TmV3KGRhdGEpLCA1MDAwKTsNCiAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEubGVuZ3RoID4gMTAwICYmIGRhdGEubGVuZ3RoIDwgMzAwKSB7DQogICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHRoaXMuZ2V0QXVkaXRSZXN1bHROZXcoZGF0YSksIDMwMDApOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHRoaXMuZ2V0QXVkaXRSZXN1bHROZXcoZGF0YSksIDIwMDApOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmF1ZGl0UmVzdWx0TGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnJlc3VsdExpc3QucHVzaChpdGVtLm1zZyk7DQogICAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnRhYmxlRGF0YS5wdXNoKGl0ZW0ucG93ZXJBdWRpdEVudGl0eSk7DQogICAgICAgICAgICBpZiAoaXRlbS5zdGF1dGUgPT0gIuWksei0pSIpIHsNCiAgICAgICAgICAgICAgLy8gaWYoaXRlbS5wb3dlckF1ZGl0RW50aXR5Lm11dGlKdGx0ZUNvZGVzPT0n5pivJw0KICAgICAgICAgICAgICAvLyB8fCBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZWxlY3RyaWNpdHlQcmljZXM9PSflkKYnDQogICAgICAgICAgICAgIC8vIHx8IGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5hZGRyZXNzQ29uc2lzdGVuY2U9PSflkKYnDQogICAgICAgICAgICAgIC8vIHx8IGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5yZWltYnVyc2VtZW50Q3ljbGU9PSflkKYnIHx8IGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5lbGVjdHJpY2l0eUNvbnRpbnVpdHk9PSflkKYnIHx8DQogICAgICAgICAgICAgIC8vIGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5zaGFyZUFjY3VyYWN5PT0n5ZCmJyB8fA0KICAgICAgICAgICAgICAvLyBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZXhjbHVzaXZlQWNjdXJhY3k9PSflkKYnfHwNCiAgICAgICAgICAgICAgLy8gaXRlbS5wb3dlckF1ZGl0RW50aXR5LnBheW1lbnRDb25zaXN0ZW5jZT09J+WQpicpew0KICAgICAgICAgICAgICBpZiAoaXRlbS5wb3dlckF1ZGl0RW50aXR5Lm11dGlKdGx0ZUNvZGVzID09ICLmmK8iKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGE0LnB1c2goaXRlbS5wb3dlckF1ZGl0RW50aXR5KTsNCiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnJlc3VsdExpc3Q0LnB1c2goaXRlbS5tc2cpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIGlmIChpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZWxlY3RyaWNpdHlQcmljZXMgPT0gIuWQpiIpIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnRhYmxlRGF0YTUucHVzaChpdGVtLnBvd2VyQXVkaXRFbnRpdHkpOw0KICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwucmVzdWx0TGlzdDUucHVzaChpdGVtLm1zZyk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgaWYgKA0KICAgICAgICAgICAgICAgIGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5hZGRyZXNzQ29uc2lzdGVuY2UgPT0gIuWQpiIgfHwNCiAgICAgICAgICAgICAgICBpdGVtLnBvd2VyQXVkaXRFbnRpdHkucmVpbWJ1cnNlbWVudEN5Y2xlID09ICLlkKYiIHx8DQogICAgICAgICAgICAgICAgaXRlbS5wb3dlckF1ZGl0RW50aXR5LmVsZWN0cmljaXR5Q29udGludWl0eSA9PSAi5ZCmIiB8fA0KICAgICAgICAgICAgICAgIGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5zaGFyZUFjY3VyYWN5ID09ICLlkKYiIHx8DQogICAgICAgICAgICAgICAgLy8gICBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZXhjbHVzaXZlQWNjdXJhY3k9PSflkKYnfHwNCiAgICAgICAgICAgICAgICBpdGVtLnBvd2VyQXVkaXRFbnRpdHkucGF5bWVudENvbnNpc3RlbmNlID09ICLlkKYiIHx8DQogICAgICAgICAgICAgICAgaXRlbS5wb3dlckF1ZGl0RW50aXR5LmZsdWN0dWF0ZUNvbnRpbnVpdHkgPT0gIuWQpiINCiAgICAgICAgICAgICAgKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGEyLnB1c2goaXRlbS5wb3dlckF1ZGl0RW50aXR5KTsNCiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnJlc3VsdExpc3QyLnB1c2goaXRlbS5tc2cpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBpZiAoDQogICAgICAgICAgICAgICAgLy8gaXRlbS5wb3dlckF1ZGl0RW50aXR5LmVsZWN0cmljaXR5UmF0aW9uYWxpdHkgPT0gIuaYryIgJiYgLy/nlLXph4/lkIjnkIbmgKco55yB5YaF5aSn5pWw5o2uKQ0KICAgICAgICAgICAgICAgIC8vIGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5leGNsdXNpdmVBY2N1cmFjeSA9PSAi5pivIiAmJiAvL+WxgOermeeLrOS6q+WFseS6q+iuvue9rg0KICAgICAgICAgICAgICAgIGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5wZXJpb2RpY0Fub21hbHkgPT0gIuaYryIgLy/lj7DotKblkajmnJ/lkIjnkIbmgKcNCiAgICAgICAgICAgICAgKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGExLnB1c2goaXRlbS5wb3dlckF1ZGl0RW50aXR5KTsNCiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnJlc3VsdExpc3QxLnB1c2goaXRlbS5tc2cpOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwudGFibGVEYXRhMy5wdXNoKGl0ZW0ucG93ZXJBdWRpdEVudGl0eSk7DQogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5yZXN1bHRMaXN0My5wdXNoKGl0ZW0ubXNnKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgaWYgKHRoaXMuYXVkaXRSZXN1bHRMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgdGhpcy5hdWRpdFJlc3VsdExpc3RbdGhpcy5hdWRpdFJlc3VsdExpc3QubGVuZ3RoIC0gMV0ucHJvZ3Jlc3MgPSAxOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5wcm9jZXNzRGF0YSA9IE51bWJlcihpdGVtLnByb2dyZXNzKSAqIDEwMDsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwuc2Nyb2xsTGlzdCgpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHNhdmUodmFsdWUpIHsNCiAgICAgIHZhciB0aGF0ID0gdGhpczsNCiAgICAgIC8v6L+b5bqm5p2h6aG16Z2i6ZO+5o6ld2Vic29rZXTlkI7osIPnlKjvvIzkvKBqaOWtl+auteS4jei1sOS/neWtmOa1geeoi++8jOi1sOeoveaguOa1geeoiw0KICAgICAgaWYgKHZhbHVlID09IDEpIHsNCiAgICAgICAgdGhhdC5zdWJtaXRbMF0uamggPSAiMSI7DQogICAgICAgIHRoYXQuc3VibWl0WzBdLnltbWMgPSAi6Ieq5pyJ6aKE5LuY55S16LS55Y+w6LSmIjsNCiAgICAgICAgdGhhdC5zdWJtaXQuZm9yRWFjaCgoaXRlbTEpID0+IHsNCiAgICAgICAgICB0aGlzLmN0Z0tleUxpc3QuZm9yRWFjaCgoaXRlbTIpID0+IHsNCiAgICAgICAgICAgIGlmIChpdGVtMS5hbW1ldGVyTmFtZSA9PSBpdGVtMi5hbW1ldGVybmFtZSkgew0KICAgICAgICAgICAgICBpdGVtMS5jdGdLZXkgPSBpdGVtMi5jdGdLZXk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0pOw0KICAgICAgICB0aGlzLmdldEF1ZGl0UmVzdWx0TmV3KHRoYXQuc3VibWl0KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmICh0aGF0LnN1Ym1pdFswXS5oYXNPd25Qcm9wZXJ0eSgiamgiKSkgew0KICAgICAgICAgIGRlbGV0ZSB0aGF0LnN1Ym1pdFswXS5qaDsNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5uYW1lID09ICJjdXJyZW50Iikgew0KICAgICAgICAgIGlmICh0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLmZyb21HdWlqaWRhbiA9PSAxKSB7DQogICAgICAgICAgICB0aGlzLnNlbGVjdGVkRlFLKCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRBY2NvdW50KCk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMubmFtZSA9PSAiYWxsIikgew0KICAgICAgICAgIGlmICh0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLmZyb21HdWlqaWRhbiA9PSAxKSB7DQogICAgICAgICAgICB0aGlzLnNlbGVjdGVkRlFLKCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRBbGxBY2NvdW50KCk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvL+WKoOWFpeW9kumbhuWNle+8jOWFqOmDqOmdnuW8uuaOpw0KICAgIHNlbGVjdGVkRlFLKCkgew0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgdGhhdC4kcmVmcy5hZGRCaWxsUGVyLmluaXRBbW1ldGVyKA0KICAgICAgICB0aGF0LiRyZWZzLnNob3dBbGFybU1vZGVsLnNlbGVjdElkczMsDQogICAgICAgIDEzLA0KICAgICAgICB0aGlzLmFjY291bnRPYmouY291bnRyeQ0KICAgICAgKTsNCiAgICB9LA0KICAgIC8v5o+Q5Lqk5pWw5o2uDQogICAgc3VibWl0RGF0YShkYXRhKSB7DQogICAgICBsZXQgYSA9IFtdOw0KICAgICAgbGV0IHZlcnNpb24gPSBpbmRleERhdGEudmVyc2lvbjsNCiAgICAgIGlmIChkYXRhICE9IG51bGwgJiYgZGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgIGxldCBudW1iZXIgPSAwOw0KICAgICAgICBsZXQgc3VibWl0RGF0YSA9IFtdOw0KICAgICAgICBsZXQgc3RyID0gIiI7DQogICAgICAgIGxldCBhY2NvdW50bm8gPSB0aGlzLmFjY291bnRPYmouYWNjb3VudG5vOw0KICAgICAgICBsZXQgaW5kZXggPSAwOw0KICAgICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgIGlmIChpdGVtLnByb2plY3ROYW1lICE9ICLlsI/orqEiICYmIGl0ZW0ucHJvamVjdE5hbWUgIT0gIuWQiOiuoSIpIHsNCiAgICAgICAgICAgIGxldCBvYmogPSB2ZXJpZmljYXRpb24oaXRlbSk7DQogICAgICAgICAgICBpZiAob2JqLnJlc3VsdCkgew0KICAgICAgICAgICAgICBpZiAoaXRlbS5wY2lkID09IG51bGwpIHsNCiAgICAgICAgICAgICAgICBpdGVtLmFjY291bnRubyA9IGFjY291bnRubzsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBPYmplY3QuYXNzaWduKGl0ZW0sIHsgdG90YWx1c2VkcmVhZGluZ3M6IGl0ZW0uY3VydXNlZHJlYWRpbmdzIH0pOyAvLyDnoa7kv53pooTku5jmgLvnlLXph48g562J5LqO55So55S16YePDQoNCiAgICAgICAgICAgICAgYS5wdXNoKGl0ZW0uYW1tZXRlcmlkKTsNCiAgICAgICAgICAgICAgc3VibWl0RGF0YS5wdXNoKGl0ZW0pOw0KICAgICAgICAgICAgICBudW1iZXIrKzsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHN0ciArPQ0KICAgICAgICAgICAgICAgICLnlLXooagv5Y2P6K6u57yW5Y+35Li644CQIiArDQogICAgICAgICAgICAgICAgaXRlbS5hbW1ldGVyTmFtZSArDQogICAgICAgICAgICAgICAgIuOAkeeahOWPsOi0pumqjOivgeayoeaciemAmui/h++8muOAkCIgKw0KICAgICAgICAgICAgICAgIG9iai5zdHIgKw0KICAgICAgICAgICAgICAgICLjgJHvvJsiOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICAgIHRoYXQuYW1tZXRlcmlkcyA9IGE7DQogICAgICAgIGlmIChzdHIubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKHN0cik7DQogICAgICAgIH0NCiAgICAgICAgaWYgKHN1Ym1pdERhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuc3VibWl0ID0gc3VibWl0RGF0YTsNCiAgICAgICAgICB0aGlzLnN1Ym1pdDIgPSBzdWJtaXREYXRhOw0KICAgICAgICAgIC8vICAgdGhpcy5wcmVzZXJ2ZVNjKCkNCiAgICAgICAgICBhZGRBY2NvdW50RXMoc3VibWl0RGF0YSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLmRhdGEuY29kZSA9PSAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbyh7DQogICAgICAgICAgICAgICAgY29udGVudDogIuaPkOekuu+8muaIkOWKn+S/neWtmCAiICsgc3VibWl0RGF0YS5sZW5ndGggKyAiIOadoeaVsOaNriIsDQogICAgICAgICAgICAgICAgZHVyYXRpb246IDEwLA0KICAgICAgICAgICAgICAgIGNsb3NhYmxlOiB0cnVlLA0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgYWRkRWxlY3RyaWNUeXBlKCkgew0KICAgICAgbGV0IGNvbXBhbnlJZCA9IHRoaXMuYWNjb3VudE9iai5jb21wYW55Ow0KICAgICAgbGV0IGNvdW50cnkgPSB0aGlzLmFjY291bnRPYmouY291bnRyeTsNCiAgICAgIGlmIChjb21wYW55SWQgIT0gbnVsbCAmJiBjb3VudHJ5ICE9IG51bGwpIHsNCiAgICAgICAgbGV0IG9iaiA9IHsNCiAgICAgICAgICBjb21wYW55OiBjb21wYW55SWQsDQogICAgICAgICAgY291bnRyeTogY291bnRyeSwNCiAgICAgICAgICBhY2NvdW50bm86IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm8sDQogICAgICAgICAgYWNjb3VudFR5cGU6ICIxIiwNCiAgICAgICAgICBhY2NvdW50ZXN0eXBlOiAzLA0KICAgICAgICB9Ow0KICAgICAgICB0aGlzLiRyZWZzLnNlbGVjdEFtbWV0ZXIuaW5pdEFtbWV0ZXIob2JqKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fpgInmi6nliIblhazlj7jlkozpg6jpl6giKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8v6aqM6K+B6ZSZ6K+v5by55Ye65o+Q56S65qGGDQogICAgZXJyb3JUaXBzKHN0cikgew0KICAgICAgdGhpcy4kTm90aWNlLmVycm9yKHsNCiAgICAgICAgdGl0bGU6ICLmj5DnpLoiLA0KICAgICAgICBkZXNjOiBzdHIsDQogICAgICAgIGR1cmF0aW9uOiAxMCwNCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlUGFnZSh2YWx1ZSkgew0KICAgICAgbGV0IGIgPSBmYWxzZTsNCiAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YTsNCiAgICAgIGxldCBhcnJheSA9IFtdOw0KICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgIGlmIChpdGVtLmVkaXRUeXBlID09IDEpIHsNCiAgICAgICAgICBiID0gdHJ1ZTsNCiAgICAgICAgICBhcnJheS5wdXNoKGl0ZW0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIGlmIChiKSB7DQogICAgICAgIHRoaXMuJE1vZGFsLmNvbmZpcm0oew0KICAgICAgICAgIHRpdGxlOiAi5o+Q56S6IiwNCiAgICAgICAgICBjb250ZW50OiAiPHA+5oKo5pyJ5bey57yW6L6R5L+h5oGv6L+Y5rKh5pyJ5L+d5a2Y77yM5piv5ZCm5L+d5a2Y77yfPC9wPiIsDQogICAgICAgICAgb25PazogKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5zdWJtaXREYXRhKGFycmF5KTsNCiAgICAgICAgICB9LA0KICAgICAgICAgIG9uQ2FuY2VsOiAoKSA9PiB7fSwNCiAgICAgICAgfSk7DQogICAgICB9DQoNCiAgICAgIHRoaXMucGFnZU51bSA9IHZhbHVlOw0KICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICB9LA0KICAgIGhhbmRsZVBhZ2VTaXplKHZhbHVlKSB7DQogICAgICBsZXQgYiA9IGZhbHNlOw0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhOw0KICAgICAgbGV0IGFycmF5ID0gW107DQogICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgaWYgKGl0ZW0uZWRpdFR5cGUgPT0gMSkgew0KICAgICAgICAgIGIgPSB0cnVlOw0KICAgICAgICAgIGFycmF5LnB1c2goaXRlbSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgaWYgKGIpIHsNCiAgICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7DQogICAgICAgICAgdGl0bGU6ICLmj5DnpLoiLA0KICAgICAgICAgIGNvbnRlbnQ6ICI8cD7mgqjmnInlt7LnvJbovpHkv6Hmga/ov5jmsqHmnInkv53lrZjvvIzmmK/lkKbkv53lrZjvvJ88L3A+IiwNCiAgICAgICAgICBvbk9rOiAoKSA9PiB7DQogICAgICAgICAgICB0aGlzLnN1Ym1pdERhdGEoYXJyYXkpOw0KICAgICAgICAgIH0sDQogICAgICAgICAgb25DYW5jZWw6ICgpID0+IHt9LA0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5wYWdlU2l6ZSA9IHZhbHVlOw0KICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICB9LA0KICAgIC8v5ZCR5ZCO5Y+w6K+35rGC5pWw5o2uDQogICAgZ2V0QWNjb3VudE1lc3NhZ2VzKCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHRoaXMuYWNjb3VudE9iajsNCiAgICAgIHBhcmFtcy5wYWdlTnVtID0gdGhpcy5wYWdlTnVtOw0KICAgICAgcGFyYW1zLnBhZ2VTaXplID0gdGhpcy5wYWdlU2l6ZTsNCiAgICAgIGxldCByZXEgPSB7DQogICAgICAgIHVybDogIi9idXNpbmVzcy9hY2NvdW50RXMvc2VsZWN0QWNjb3VudEVzTGlzdCIsDQogICAgICAgIG1ldGhvZDogImdldCIsDQogICAgICAgIHBhcmFtczogcGFyYW1zLA0KICAgICAgfTsNCiAgICAgIHRoaXMudGJBY2NvdW50LmxvYWRpbmcgPSB0cnVlOw0KICAgICAgYXhpb3MNCiAgICAgICAgLnJlcXVlc3QocmVxKQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgdGhpcy50YkFjY291bnQubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIGlmIChyZXMuZGF0YSkgew0KICAgICAgICAgICAgbGV0IGRhdGEgPSByZXMuZGF0YS5yb3dzOw0KICAgICAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgICAgIGl0ZW0uZWRpdFR5cGUgPSAwOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgICBkYXRhLnB1c2godGhpcy5zdW50b3RhbChkYXRhKSk7IC8v5bCP6K6hDQogICAgICAgICAgICBhY2NvdW50RXNUb3RhbCh0aGlzLmFjY291bnRPYmopLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICAvL+WQiOiuoQ0KICAgICAgICAgICAgICBsZXQgYWxsdG90YWwgPSByZXMuZGF0YTsNCiAgICAgICAgICAgICAgYWxsdG90YWwudG90YWwgPSAi5ZCI6K6hIjsNCiAgICAgICAgICAgICAgYWxsdG90YWwucHJvamVjdE5hbWUgPSAi5ZCI6K6hIjsNCiAgICAgICAgICAgICAgYWxsdG90YWwuX2Rpc2FibGVkID0gdHJ1ZTsNCiAgICAgICAgICAgICAgZGF0YS5wdXNoKGFsbHRvdGFsKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgdGhpcy50YkFjY291bnQuZGF0YSA9IGRhdGE7DQogICAgICAgICAgICB0aGlzLnBhZ2VUb3RhbCA9IHJlcy5kYXRhLnRvdGFsIHx8IDA7DQogICAgICAgICAgICB0aGlzLnNldE15U3R5bGUodGhpcy50YkFjY291bnQuZGF0YS5sZW5ndGgpOw0KDQogICAgICAgICAgICB0aGlzLmVkaXRJbmRleCA9IC0xOw0KICAgICAgICAgICAgdGhpcy5jb2x1bW5zSW5kZXggPSAtMTsNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoZXJyKSA9PiB7DQogICAgICAgICAgY29uc29sZS5sb2coZXJyKTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICAvL+Wwj+iuoQ0KICAgIHN1bnRvdGFsKGFycmF5KSB7DQogICAgICBsZXQgY3VydXNlZHJlYWRpbmdzID0gMDsNCiAgICAgIGxldCB0cmFuc2Zvcm1lcnVsbGFnZSA9IDA7DQogICAgICBsZXQgYWNjb3VudG1vbmV5ID0gMDsNCiAgICAgIGFycmF5LmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgaWYgKGl0ZW0uZWZmZWN0aXZlID09PSAxKSB7DQogICAgICAgICAgY3VydXNlZHJlYWRpbmdzICs9IGl0ZW0uY3VydXNlZHJlYWRpbmdzOw0KICAgICAgICAgIHRyYW5zZm9ybWVydWxsYWdlICs9IGl0ZW0udHJhbnNmb3JtZXJ1bGxhZ2U7DQogICAgICAgICAgYWNjb3VudG1vbmV5ICs9IGl0ZW0uYWNjb3VudG1vbmV5Ow0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIHJldHVybiB7DQogICAgICAgIGN1cnVzZWRyZWFkaW5nczogY3VydXNlZHJlYWRpbmdzLA0KICAgICAgICB0cmFuc2Zvcm1lcnVsbGFnZTogdHJhbnNmb3JtZXJ1bGxhZ2UsDQogICAgICAgIGFjY291bnRtb25leTogYWNjb3VudG1vbmV5LA0KICAgICAgICB0b3RhbDogIuWwj+iuoSIsDQogICAgICAgIHByb2plY3ROYW1lOiAi5bCP6K6hIiwNCiAgICAgICAgX2Rpc2FibGVkOiB0cnVlLA0KICAgICAgfTsNCiAgICB9LA0KICAgIC8v6YeN572uDQogICAgb25SZXNldEhhbmRsZSgpIHsNCiAgICAgIHRoaXMuYWNjb3VudE9iaiA9IHsNCiAgICAgICAgYWNjb3VudG5vOiBkYXRlc1swXS5jb2RlLCAvL+acn+WPtyzpu5jorqTlvZPliY3mnIgNCiAgICAgICAgY29tcGFueTogdGhpcy5jb21wYW55LA0KICAgICAgICBwcm9qZWN0TmFtZTogIiIsIC8v6aG555uu5ZCN56ewDQogICAgICAgIGNvdW50cnk6IE51bWJlcih0aGlzLmNvdW50cnkpLCAvL+aJgOWxnumDqOmXqA0KICAgICAgICBhbW1ldGVyTmFtZTogIiIsIC8v55S16KGo5oi35Y+3L+WNj+iurue8lueggQ0KICAgICAgICBzdGF0aW9uTmFtZTogIiIsDQogICAgICAgIGFjY291bnRUeXBlOiAiMSIsIC8v5Y+w6LSm57G75Z6LDQogICAgICAgIGFjY291bnRlc3R5cGU6IDMsIC8v5Y+w6LSm57G75Z6LDQogICAgICAgIHN1cHBseWJ1cmVhdWFtbWV0ZXJjb2RlOiAiIiwNCiAgICAgICAgY291bnRyeU5hbWU6IHRoaXMuY291bnRyeU5hbWUsDQogICAgICB9Ow0KICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICB9LA0KICAgIC8v6K6h566X5Y2V5Lu3DQogICAgdW5pdFByaWNlKHJvdykgew0KICAgICAgbGV0IGFjY291bnRtb25leSA9IHJvdy5hY2NvdW50bW9uZXk7DQogICAgICBsZXQgY3VydXNlZHJlYWRpbmdzID0gcm93LmN1cnVzZWRyZWFkaW5nczsNCiAgICAgIGlmIChhY2NvdW50bW9uZXkgIT0gbnVsbCAmJiBjdXJ1c2VkcmVhZGluZ3MgIT0gbnVsbCkgew0KICAgICAgICBsZXQgdG90YWwgPSBudWxsOw0KICAgICAgICBpZiAoY3VydXNlZHJlYWRpbmdzID09IDApIHsNCiAgICAgICAgICB0b3RhbCA9IDA7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdG90YWwgPSBhY2NvdW50bW9uZXkgLyBjdXJ1c2VkcmVhZGluZ3M7DQogICAgICAgIH0NCg0KICAgICAgICByb3cudW5pdHBpcmNlID0gdG90YWwudG9GaXhlZCgyKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8v6aqM6K+B5Y2V5Lu3DQogICAgdmFsaWRhdGVVbml0UHJpY2UoZGF0YSkgew0KICAgICAgbGV0IGNhdGVnb3J5ID0gZGF0YS5jYXRlZ29yeTsgLy/nlLXooajmj4/ov7DnsbvlnosNCiAgICAgIGxldCBhbW1ldGVydXNlID0gZGF0YS5hbW1ldGVydXNlOyAvL+eUteihqOeUqOmAlA0KICAgICAgbGV0IHVuaXRwaXJjZSA9IGRhdGEudW5pdHBpcmNlOyAvL+WPsOi0puWNleS7tw0KICAgICAgaWYgKCFqdWRnZV9uZWdhdGUoY2F0ZWdvcnkpICYmICFqdWRnZV9yZWNvdmVyeShhbW1ldGVydXNlKSkgew0KICAgICAgICAvLyBpZiAodW5pdHBpcmNlKSB7DQogICAgICAgIC8vICAgaWYgKHVuaXRwaXJjZSA8IHVuaXRwaXJjZU1pbiB8fCB1bml0cGlyY2UgPiB1bml0cGlyY2VNYXgpIHsNCiAgICAgICAgLy8gICAgIHRoaXMuZXJyb3JUaXBzKA0KICAgICAgICAvLyAgICAgICAi6ZuG5Zui6KaB5rGC5Y2V5Lu36IyD5Zu05ZyoMC4zfjLlhYPvvIzmraTlj7DotKbljZXku7c6ICIgKw0KICAgICAgICAvLyAgICAgICAgIHVuaXRwaXJjZSArDQogICAgICAgIC8vICAgICAgICAgIiDlt7LotoXov4fojIPlm7TvvIzor7fnoa7orqTvvIEiDQogICAgICAgIC8vICAgICApOw0KICAgICAgICAvLyAgIH0NCiAgICAgICAgLy8gfQ0KICAgICAgICBpZiAodW5pdHBpcmNlKSB7DQogICAgICAgICAgaWYgKHVuaXRwaXJjZSAhPSBudWxsICYmIHVuaXRwaXJjZSA8IHVuaXRwaXJjZU1heDEpIHsNCiAgICAgICAgICAgIC8vIGlmICh1bml0cGlyY2UgPCB1bml0cGlyY2VNaW4gfHwgdW5pdHBpcmNlID4gdW5pdHBpcmNlTWF4KSB7DQogICAgICAgICAgICB0aGlzLmVycm9yVGlwcygNCiAgICAgICAgICAgICAgIuWNleS7t+iMg+WbtOW/hemhu+Wkp+S6jjAuMeWFg++8jOatpOWPsOi0puWNleS7tzogIiArIHVuaXRwaXJjZSArICLkuI3lnKjojIPlm7TlhoXvvIzor7fnoa7orqTvvIEiDQogICAgICAgICAgICApOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgcmVtb3ZlKCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLiRyZWZzLmFjY291bnRFc1RhYmxlLmdldFNlbGVjdGlvbigpOw0KICAgICAgaWYgKGRhdGEgPT0gbnVsbCB8fCBkYXRhLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLmVycm9yVGlwcygi6K+36YCJ5oup6KaB5Yig6Zmk55qE5pWw5o2uIik7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMuJE1vZGFsLmNvbmZpcm0oew0KICAgICAgICB0aXRsZTogIuaPkOekuiIsDQogICAgICAgIGNvbnRlbnQ6ICI8cD7mmK/lkKbnoa7orqTliKDpmaTpgInkuK3kv6Hmga/vvJ88L3A+IiwNCiAgICAgICAgb25PazogKCkgPT4gew0KICAgICAgICAgIGxldCBiID0gdHJ1ZTsNCiAgICAgICAgICBsZXQgaWRzID0gIiI7DQogICAgICAgICAgbGV0IGFycmF5ID0gdGhpcy50YkFjY291bnQuZGF0YTsNCiAgICAgICAgICBsZXQgdG90YWwgPSB0aGlzLnBhZ2VUb3RhbDsNCiAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGRhdGEubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICAgIGxldCBpdGVtID0gZGF0YVtpXTsNCiAgICAgICAgICAgIGlmIChpdGVtLnBjaWQgIT0gbnVsbCAmJiBpdGVtLnBjaWQubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICBpZiAoaXRlbS5wYWJyaWlkKSB7DQogICAgICAgICAgICAgICAgYiA9IGZhbHNlOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIGlkcyArPSBpdGVtLnBjaWQgKyAiLCI7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBmb3IgKGxldCBqID0gYXJyYXkubGVuZ3RoIC0gMTsgaiA+PSAwOyBqLS0pIHsNCiAgICAgICAgICAgICAgICBsZXQgamogPSBhcnJheVtqXTsNCiAgICAgICAgICAgICAgICBpZiAoamouYW1tZXRlcmlkID09PSBpdGVtLmFtbWV0ZXJpZCkgew0KICAgICAgICAgICAgICAgICAgYXJyYXkuc3BsaWNlKGosIDEpOw0KICAgICAgICAgICAgICAgICAgdG90YWwgPSB0b3RhbCAtIDE7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMucGFnZVRvdGFsID0gdG90YWw7DQogICAgICAgICAgaWYgKGIpIHsNCiAgICAgICAgICAgIGlmIChpZHMubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICByZW1vdmVBY2NvdW50RXMoaWRzKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEuY29kZSA9PSAwKSB7DQogICAgICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLnN1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICAgICAgICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6YCJ5Lit5L+h5oGv5Lit5pyJ5L+h5oGv6L+Y5rKh5pyJ6Lef5b2S6ZuG5Y2V6Kej6Zmk5YWz6IGU77yM6K+35YWI6Kej6Zmk5YWz6IGUIik7DQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBvbkNhbmNlbDogKCkgPT4ge30sDQogICAgICB9KTsNCiAgICB9LA0KICAgIC8v5Yqg5YWl5b2S6ZuG5Y2VDQogICAgYWRkUHJlc2VydmVHSigpIHsNCiAgICAgIGxldCBkYXRhTCA9IHRoaXMuJHJlZnMuYWNjb3VudEVzVGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICB0aGlzLmRhdGFMID0gdGhpcy4kcmVmcy5hY2NvdW50RXNUYWJsZS5nZXRTZWxlY3Rpb24oKTsNCiAgICAgIGlmIChkYXRhTCA9PSBudWxsIHx8IGRhdGFMLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fpgInmi6nopoHliqDlhaXlvZLpm4bljZXnmoTlj7DotKYiKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuYWRkU3VibWl0RGF0YUdKKGRhdGFMKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8v5Yqg5YWl5b2S6ZuG5Y2VDQogICAgYWRkUHJlc2VydmVHSkFsbCgpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB0aGlzLmFjY291bnRPYmo7DQogICAgICBwYXJhbXMucGFnZU51bSA9IDE7DQogICAgICBwYXJhbXMucGFnZVNpemUgPSAyMDAwMDsNCiAgICAgIGxldCByZXEgPSB7DQogICAgICAgIHVybDogIi9idXNpbmVzcy9hY2NvdW50RXMvc2VsZWN0QWNjb3VudEVzTGlzdCIsDQogICAgICAgIG1ldGhvZDogImdldCIsDQogICAgICAgIHBhcmFtczogcGFyYW1zLA0KICAgICAgfTsNCiAgICAgIGxldCBhcnJheSA9IFtdOw0KICAgICAgbGV0IGFycmF5MSA9IFtdOw0KICAgICAgdGhpcy50YkFjY291bnQubG9hZGluZyA9IHRydWU7DQogICAgICBheGlvcy5yZXF1ZXN0KHJlcSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMudGJBY2NvdW50LmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgbGV0IGRhdGFMID0gcmVzLmRhdGEucm93czsNCiAgICAgICAgdGhpcy5kYXRhTCA9IHJlcy5kYXRhLnJvd3M7DQogICAgICAgIHRoaXMuYWRkU3VibWl0RGF0YUdKKGRhdGFMKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy/mj5DkuqTlvZLpm4bljZXmlbDmja4NCiAgICBhZGRTdWJtaXREYXRhR0ooZGF0YSkgew0KICAgICAgbGV0IGEgPSBbXTsNCiAgICAgIGxldCBiID0gMTsNCiAgICAgIGxldCBzdHIgPSAiIjsNCiAgICAgIGxldCBzdHIxID0gIiI7DQogICAgICBsZXQgdmVyc2lvbiA9IGluZGV4RGF0YS52ZXJzaW9uOw0KICAgICAgaWYgKGRhdGEgIT0gbnVsbCAmJiBkYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbGV0IG51bWJlciA9IDA7DQogICAgICAgIGxldCBzdWJtaXREYXRhID0gW107DQogICAgICAgIGxldCBubyA9IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm87DQogICAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgbGV0IG9iaiA9IHZlcmlmaWNhdGlvbihpdGVtKTsNCiAgICAgICAgICBpZiAob2JqLnJlc3VsdCkgew0KICAgICAgICAgICAgbGV0IHl5eXltbWRkID0gY3V0RGF0ZV95eXl5bW1kZChpdGVtLnN0YXJ0ZGF0ZSk7DQogICAgICAgICAgICBpdGVtLnN0YXJ0eWVhciA9IHl5eXltbWRkLnl5eXk7DQogICAgICAgICAgICBpdGVtLnN0YXJ0bW9udGggPSB5eXl5bW1kZC5tbTsNCiAgICAgICAgICAgIHl5eXltbWRkID0gY3V0RGF0ZV95eXl5bW1kZChpdGVtLmVuZGRhdGUpOw0KICAgICAgICAgICAgaXRlbS5lbmR5ZWFyID0geXl5eW1tZGQueXl5eTsNCiAgICAgICAgICAgIGl0ZW0uZW5kbW9udGggPSB5eXl5bW1kZC5tbTsNCiAgICAgICAgICAgIGEucHVzaChpdGVtLmFtbWV0ZXJpZCk7DQogICAgICAgICAgICBzdWJtaXREYXRhLnB1c2goaXRlbSk7DQogICAgICAgICAgICBudW1iZXIrKzsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgc3RyICs9DQogICAgICAgICAgICAgICLnlLXooagv5Y2P6K6u57yW5Y+35Li644CQIiArDQogICAgICAgICAgICAgIGl0ZW0uYW1tZXRlcmNvZGUgKw0KICAgICAgICAgICAgICAi44CR55qE5Y+w6LSm6aqM6K+B5rKh5pyJ6YCa6L+H77ya44CQIiArDQogICAgICAgICAgICAgIG9iai5zdHIgKw0KICAgICAgICAgICAgICAi44CR77ybIjsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBpZiAoaXRlbS5tYWduaWZpY2F0aW9uZXJyID09IDIpIHsNCiAgICAgICAgICAgIHN0cjEgKz0NCiAgICAgICAgICAgICAgIueUteihqC/ljY/orq7nvJblj7fkuLrjgJAiICsNCiAgICAgICAgICAgICAgaXRlbS5hbW1ldGVyY29kZSArDQogICAgICAgICAgICAgICLjgJHnmoTlj7DotKblgI3njofjgJAiICsNCiAgICAgICAgICAgICAgaXRlbS5tYWduaWZpY2F0aW9uICsNCiAgICAgICAgICAgICAgIuOAkeS4jueUteihqOWAjeeOh+OAkCIgKw0KICAgICAgICAgICAgICBpdGVtLmFtbW11bHR0aW1lcyArDQogICAgICAgICAgICAgICLjgJHkuI3kuIDoh7TvvIEgIDxiciAvPiAiOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGlmIChpdGVtLnBlcmNlbnRlcnIgPT0gMikgew0KICAgICAgICAgICAgc3RyMSArPQ0KICAgICAgICAgICAgICAi55S16KGoL+WNj+iurue8luWPt+S4uuOAkCIgKw0KICAgICAgICAgICAgICBpdGVtLmFtbWV0ZXJjb2RlICsNCiAgICAgICAgICAgICAgIuOAkeeahOWPsOi0puWIhuWJsuavlOS+i+OAkCIgKw0KICAgICAgICAgICAgICBpdGVtLnBlcmNlbnQgKw0KICAgICAgICAgICAgICAi44CR5LiO55S16KGo5YiG5Ymy5q+U5L6L44CQIiArDQogICAgICAgICAgICAgIGl0ZW0uYW1tcGVyY2VudCArDQogICAgICAgICAgICAgICLjgJHkuI3kuIDoh7TvvIEgPGJyIC8+ICI7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChpdGVtLmVmZmVjdGl2ZSAhPSAxKSB7DQogICAgICAgICAgICBiID0gMjsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGl0ZW0uc3RhdHVzID09PSA1KSB7DQogICAgICAgICAgICBiID0gMzsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGl0ZW0uc3RhdHVzID09PSA0KSB7DQogICAgICAgICAgICBiID0gNDsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgICB0aGF0LmFtbWV0ZXJpZHMgPSBhOw0KICAgICAgICBpZiAoYiA9PT0gMSkgew0KICAgICAgICAgIGlmIChzdWJtaXREYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHRoaXMuc3VibWl0ID0gc3VibWl0RGF0YTsNCiAgICAgICAgICAgIHRoaXMuc3VibWl0MiA9IHN1Ym1pdERhdGE7DQogICAgICAgICAgICB0aGlzLnByZXNlcnZlU2MoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSBpZiAoYiA9PT0gMikgew0KICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCLpgInkuK3nmoTlj7DotKbkuK3lrZjlnKjkuLTml7bmlbDmja7vvIzor7flhYjkv53lrZjlho3liqDlhaXlvZLpm4bljZXvvIEiKTsNCiAgICAgICAgfSBlbHNlIGlmIChiID09PSAzKSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoIumAgOWbnueahOWPsOi0puS4jeiDveWKoOWFpeWFtuWug+W9kumbhuWNle+8jOivt+eCueWHu1vph43mlrDliqDlhaXlvZLpm4bljZVd5oyJ6ZKuIik7DQogICAgICAgIH0gZWxzZSBpZiAoYiA9PT0gNCkgew0KICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCLpgInmi6nnmoTlj7DotKbmnInlt7LliqDlhaXlvZLpm4bljZXnmoTlj7DotKbvvIzkuI3og73liqDlhaXlhbbku5blvZLpm4bljZUiKTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoc3RyLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGF0LmVycm9yVGlwcyhzdHIpOw0KICAgICAgICB9DQogICAgICAgIGlmIChzdHIxLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGF0LiROb3RpY2Uud2FybmluZyh7DQogICAgICAgICAgICB0aXRsZTogIuazqOaEjyIsDQogICAgICAgICAgICBkZXNjOiBzdHIxLA0KICAgICAgICAgICAgZHVyYXRpb246IDAsDQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIG9wZW5BZGRCaWxsUGVyTW9kYWwobmFtZSkgew0KICAgICAgdGhpcy5uYW1lID0gbmFtZTsNCiAgICAgIGlmIChuYW1lID09PSAiY3VycmVudCIpIHsNCiAgICAgICAgLy/pnIDopoHnqL3moLgNCiAgICAgICAgLy8gaWYgKHRoaXMuaGFzQnV0dG9uUGVybSgiamhzZCIpKSB7DQogICAgICAgIC8vICAgdGhpcy5hZGRQcmVzZXJ2ZUdKKCk7DQogICAgICAgIC8vIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRBY2NvdW50KCk7DQogICAgICAgIC8vIH0NCiAgICAgIH0gZWxzZSBpZiAobmFtZSA9PT0gImFsbCIpIHsNCiAgICAgICAgLy/pnIDopoHnqL3moLgNCiAgICAgICAgLy8gaWYgKHRoaXMuaGFzQnV0dG9uUGVybSgiamhzZCIpKSB7DQogICAgICAgIC8vICAgdGhpcy5hZGRQcmVzZXJ2ZUdKQWxsKCk7DQogICAgICAgIC8vIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRBbGxBY2NvdW50KCk7DQogICAgICAgIC8vIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8v5Yqg5YWl5b2S6ZuG5Y2V77yM5YWo6YOo5pyJ5pWI5Y+w6LSmDQogICAgc2VsZWN0ZWRBbGxBY2NvdW50KCkgew0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgdGhhdC5zcGluU2hvdyA9IHRydWU7DQogICAgICBzZWxlY3RJZHNCeUVzUGFyYW1zKHRoaXMuYWNjb3VudE9iaikudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoYXQuc3BpblNob3cgPSBmYWxzZTsNCiAgICAgICAgaWYgKHJlcy5kYXRhLmxlbmd0aCA9PSAwKSB7DQogICAgICAgICAgdGhhdC5lcnJvclRpcHMoIuaXoOacieaViOaVsOaNruWPr+WKoOWFpeW9kumbhuWNlSIpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoYXQuJHJlZnMuYWRkQmlsbFBlci5pbml0QW1tZXRlcigNCiAgICAgICAgICAgIHJlcy5kYXRhLA0KICAgICAgICAgICAgLy8gdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5zZWxlY3RJZHMxLA0KICAgICAgICAgICAgMTMsDQogICAgICAgICAgICB0aGlzLmFjY291bnRPYmouY291bnRyeQ0KICAgICAgICAgICk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgc2VsZWN0ZWRBY2NvdW50KCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLiRyZWZzLmFjY291bnRFc1RhYmxlLmdldFNlbGVjdGlvbigpOw0KICAgICAgbGV0IGIgPSAxOw0KICAgICAgaWYgKGRhdGEgPT0gbnVsbCB8fCBkYXRhLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fpgInmi6nopoHliqDlhaXlvZLpm4bljZXnmoTlj7DotKYiKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldCBpZHMgPSBbXTsNCiAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgaWYgKGl0ZW0uZWZmZWN0aXZlICE9IDEpIHsNCiAgICAgICAgICAgIGIgPSAyOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoaXRlbS5zdGF0dXMgPT09IDUpIHsNCiAgICAgICAgICAgIGIgPSAzOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoaXRlbS5zdGF0dXMgPT09IDQpIHsNCiAgICAgICAgICAgIGIgPSA0Ow0KICAgICAgICAgIH0NCiAgICAgICAgICBpZHMucHVzaChpdGVtLnBjaWQpOw0KICAgICAgICB9KTsNCiAgICAgICAgaWYgKGIgPT09IDEpIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLmFkZEJpbGxQZXIuaW5pdEFtbWV0ZXIoaWRzLCAxMywgdGhpcy5hY2NvdW50T2JqLmNvdW50cnkpOw0KICAgICAgICB9IGVsc2UgaWYgKGIgPT09IDIpIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6YCJ5Lit55qE5Y+w6LSm5Lit5a2Y5Zyo5Li05pe25pWw5o2u77yM6K+35YWI5L+d5a2Y5YaN5Yqg5YWl5b2S6ZuG5Y2V77yBIik7DQogICAgICAgIH0gZWxzZSBpZiAoYiA9PT0gMykgew0KICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCLpgIDlm57nmoTlj7DotKbkuI3og73liqDlhaXlhbblroPlvZLpm4bljZXvvIzor7fngrnlh7tb6YeN5paw5Yqg5YWl5b2S6ZuG5Y2VXeaMiemSriIpOw0KICAgICAgICB9IGVsc2UgaWYgKGIgPT09IDQpIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6YCJ5oup55qE5Y+w6LSm5pyJ5bey5Yqg5YWl5b2S6ZuG5Y2V55qE5Y+w6LSm77yM5LiN6IO95Yqg5YWl5YW25LuW5b2S6ZuG5Y2VIik7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIG9wZW5Db21wbGV0ZWRQcmVNb2RhbCgpIHsNCiAgICAgIHRoaXMuJHJlZnMuY29tcGxldGVkUHJlLmluaXRBbW1ldGVyKHRoaXMuYWNjb3VudE9iai5jb3VudHJ5LCAxMyk7DQogICAgfSwNCiAgICBhZ2FpbkpvaW4oKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMuJHJlZnMuYWNjb3VudEVzVGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICBsZXQgYiA9IHRydWU7DQogICAgICBpZiAoZGF0YSA9PSBudWxsIHx8IGRhdGEubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy5lcnJvclRpcHMoIuivt+mAieaLqeimgemHjeaWsOWKoOWFpeW9kumbhuWNleeahOWPsOi0piIpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGV0IGlkcyA9ICIiOw0KICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICBsZXQgc3RhdHVzID0gaXRlbS5zdGF0dXM7DQogICAgICAgICAgaWYgKHN0YXR1cyAhPSA1KSB7DQogICAgICAgICAgICBiID0gZmFsc2U7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlkcyArPSBpdGVtLnBjaWQgKyAiLCI7DQogICAgICAgIH0pOw0KICAgICAgICBpZiAoYikgew0KICAgICAgICAgIGFnYWluSm9pbihpZHMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgaWYgKHJlcy5kYXRhLmNvZGUgPT0gMCkgew0KICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oew0KICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICLmj5DnpLrvvJrmk43kvZzmiJDlip8iLA0KICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxMCwNCiAgICAgICAgICAgICAgICBjbG9zYWJsZTogdHJ1ZSwNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoIuWPquacieW3sumAgOWbnueahOWPsOi0puaJjeiDvemHjeaWsOWKoOWFpeW9kumbhuWNlSIpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICByZWZyZXNoKCkgew0KICAgICAgaWYgKHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwuZnJvbUd1aWppZGFuICE9IDEpIHsNCiAgICAgICAgLy8gd2luZG93Lmhpc3RvcnkuZ28oMCk7DQogICAgICAgIHRoaXMuc2hvd0FsYXJtTW9kZWwgPSBmYWxzZTsNCiAgICAgICAgbGV0IG9iaiA9IHRoaXM7DQogICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgew0KICAgICAgICAgIG9iai5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICAgICAgfSwgMjAwKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2hvd0FsYXJtTW9kZWwgPSB0cnVlOw0KICAgICAgfQ0KICAgIH0sDQogICAgYmVmb3JlTG9hZERhdGEoZGF0YSwgc3RyKSB7DQogICAgICB2YXIgY29scyA9IFtdLA0KICAgICAgICBrZXlzID0gW107DQogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRoaXMudGJBY2NvdW50LmV4cG9ydENvbHVtbnMubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29scy5wdXNoKHRoaXMudGJBY2NvdW50LmV4cG9ydENvbHVtbnNbaV0udGl0bGUpOw0KICAgICAgICBrZXlzLnB1c2godGhpcy50YkFjY291bnQuZXhwb3J0Q29sdW1uc1tpXS5rZXkpOw0KICAgICAgfQ0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICB0aXRsZTogY29scywNCiAgICAgICAga2V5OiBrZXlzLA0KICAgICAgICBkYXRhOiBkYXRhLA0KICAgICAgICBhdXRvV2lkdGg6IHRydWUsDQogICAgICAgIGZpbGVuYW1lOiBzdHIsDQogICAgICB9Ow0KICAgICAgZXhjZWwuZXhwb3J0X2FycmF5X3RvX2V4Y2VsKHBhcmFtcyk7DQogICAgICByZXR1cm47DQogICAgfSwNCiAgICBleHBvcnRDc3YobmFtZSkgew0KICAgICAgdGhpcy5leHBvcnQucnVuID0gdHJ1ZTsNCiAgICAgIGlmIChuYW1lID09PSAiY3VycmVudCIpIHsNCiAgICAgICAgdGhpcy5iZWZvcmVMb2FkRGF0YSh0aGlzLnRiQWNjb3VudC5kYXRhLCAi6aKE5LuY5Y+w6LSm5a+85Ye65pWw5o2uIik7DQogICAgICB9IGVsc2UgaWYgKG5hbWUgPT09ICJhbGwiKSB7DQogICAgICAgIGxldCBwYXJhbXMgPSB0aGlzLmFjY291bnRPYmo7DQogICAgICAgIHBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgICAgcGFyYW1zLnBhZ2VTaXplID0gdGhpcy5leHBvcnQuc2l6ZTsNCiAgICAgICAgbGV0IHJlcSA9IHsNCiAgICAgICAgICB1cmw6ICIvYnVzaW5lc3MvYWNjb3VudEVzL3NlbGVjdEFjY291bnRFc0xpc3QiLA0KICAgICAgICAgIG1ldGhvZDogImdldCIsDQogICAgICAgICAgcGFyYW1zOiBwYXJhbXMsDQogICAgICAgIH07DQogICAgICAgIHRoaXMudGJBY2NvdW50LmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICBheGlvcw0KICAgICAgICAgIC5yZXF1ZXN0KHJlcSkNCiAgICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICB0aGlzLnRiQWNjb3VudC5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICBpZiAocmVzLmRhdGEpIHsNCiAgICAgICAgICAgICAgbGV0IGFycmF5ID0gcmVzLmRhdGEucm93czsNCiAgICAgICAgICAgICAgYWNjb3VudEVzVG90YWwodGhpcy5hY2NvdW50T2JqKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgICAvL+WQiOiuoQ0KICAgICAgICAgICAgICAgIGxldCBhbGx0b3RhbCA9IHJlcy5kYXRhOw0KICAgICAgICAgICAgICAgIGFsbHRvdGFsLnRvdGFsID0gIuWQiOiuoSI7DQogICAgICAgICAgICAgICAgYWxsdG90YWwuX2Rpc2FibGVkID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICBhcnJheS5wdXNoKGFsbHRvdGFsKTsNCiAgICAgICAgICAgICAgICB0aGlzLmJlZm9yZUxvYWREYXRhKGFycmF5LCAi6aKE5LuY5Y+w6LSm5a+85Ye65pWw5o2uIik7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgLmNhdGNoKChlcnIpID0+IHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKGVycik7DQogICAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICB2YWxpZGF0ZSgpIHsNCiAgICAgIGlmICh0aGlzLmNvbHVtbnNJbmRleCAhPSA1KSB7DQogICAgICAgIGxldCB2YWwgPSB0aGlzLmVudGVyT3BlcmF0ZSh0aGlzLmNvbHVtbnNJbmRleCkuZGF0YTsNCiAgICAgICAgaWYgKHZhbCkgew0KICAgICAgICAgIGlmICh0ZXN0TnVtYmVyKHZhbCkpIHsNCiAgICAgICAgICAgIHN3aXRjaCAodGhpcy5jb2x1bW5zSW5kZXgpIHsNCiAgICAgICAgICAgICAgY2FzZSAxOg0KICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVTdGFydGRhdGUoKTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAyOg0KICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVFbmRkYXRlKCk7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgMzoNCiAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlY3VydG90YWxyZWFkaW5ncygpOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlIDQ6DQogICAgICAgICAgICAgICAgdGhpcy52YWxpZGF0ZWFjY291bnRtb25leSgpOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6K+36L6T5YWl5pWw5a2X77yBIik7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICB2YWxpZGF0ZVN0YXJ0ZGF0ZSgpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICBsZXQgdmFsID0gdGhpcy5lZGl0U3RhcnREYXRlOw0KICAgICAgbGV0IHJlc3VsdCA9IF92ZXJpZnlfU3RhcnREYXRlKGRhdGEsIHZhbCk7DQogICAgICBpZiAocmVzdWx0KSB7DQogICAgICAgIC8v5aSx6LSl5bCx5by55Ye65o+Q56S65YaF5a65DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKHJlc3VsdCk7DQogICAgICAgIHRoaXMubXlTdHlsZVt0aGlzLmVkaXRJbmRleF0uc3RhcnRkYXRlID0gImVycm9yU3RsZSI7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLm15U3R5bGVbdGhpcy5lZGl0SW5kZXhdLnN0YXJ0ZGF0ZSA9ICJteXNwYW4iOw0KICAgICAgICBkYXRhLnN0YXJ0ZGF0ZSA9IHZhbDsNCiAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICB9DQogICAgfSwNCiAgICB2YWxpZGF0ZUVuZGRhdGUoKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdEVuZERhdGU7DQoNCiAgICAgIGxldCByZXN1bHQgPSBfdmVyaWZ5X0VuZERhdGUoZGF0YSwgdmFsLCAi5aSn5LqO5b2T5YmNIik7DQogICAgICBpZiAocmVzdWx0KSB7DQogICAgICAgIC8v5aSx6LSl5bCx5by55Ye65o+Q56S65YaF5a6577yM5bm25bCG5pWw5o2u5oGi5aSN5Yid5aeL5YyWDQogICAgICAgIHRoaXMuZXJyb3JUaXBzKHJlc3VsdCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBkYXRhLmVuZGRhdGUgPSB2YWw7DQogICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgfQ0KICAgIH0sDQogICAgdmFsaWRhdGVjdXJ0b3RhbHJlYWRpbmdzKCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRjdXJ0b3RhbHJlYWRpbmdzOw0KICAgICAgdmFsID0gTWF0aC5hYnModmFsKTsNCiAgICAgIGxldCBwcmV2dG90YWxyZWFkaW5ncyA9IE1hdGguYWJzKGRhdGEucHJldnRvdGFscmVhZGluZ3MpOw0KICAgICAgaWYgKHByZXZ0b3RhbHJlYWRpbmdzID4gdmFsKSB7DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLmraLluqbkuI3og73lsI/kuo7otbfluqbvvIEiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBkYXRhLmN1cnRvdGFscmVhZGluZ3MgPSB2YWw7DQogICAgICBkYXRhLnRvdGFsdXNlZHJlYWRpbmdzID0gdmFsOw0KICAgICAgZGF0YS5jdXJ1c2VkcmVhZGluZ3MgPSB2YWwgLSBwcmV2dG90YWxyZWFkaW5nczsNCiAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgdGhpcy51bml0UHJpY2UoZGF0YSk7DQogICAgICBpZiAoaW5kZXhEYXRhLnZlcnNpb24gPT0gInNjIikgew0KICAgICAgICAvL+mqjOivgeS4iuacn+WPsOi0puaYr+WQpuWujOaIkOaKpei0pg0KICAgICAgICBheGlvcw0KICAgICAgICAgIC5yZXF1ZXN0KHsNCiAgICAgICAgICAgIHVybDogIi9idXNpbmVzcy9hY2NvdW50U0MvdmFsT2xkQWNvdW50IiwNCiAgICAgICAgICAgIG1ldGhvZDogInBvc3QiLA0KICAgICAgICAgICAgcGFyYW1zOiB7IHBjaWQ6IGRhdGEucGNpZCwgYW1tZXRlcmlkOiBkYXRhLmFtbWV0ZXJpZCB9LA0KICAgICAgICAgIH0pDQogICAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgbGV0IG1zZyA9ICIiOw0KICAgICAgICAgICAgaWYgKHJlcy5kYXRhLm1zZykgbXNnID0gcmVzLmRhdGEubXNnOw0KICAgICAgICAgICAgLy8gaWYoZGF0YS5zdGFydGRhdGUuZW5kc1dpdGgoIjAxMDEiKSkNCiAgICAgICAgICAgIC8vICAgICBtc2cgKz0i44CQ6K+l6LW35aeL5pel5pyf5piv6buY6K6k5YC877yM6K+35rOo5oSP5L+u5pS544CRIjsNCiAgICAgICAgICAgIGlmIChtc2cgIT0gIiIpDQogICAgICAgICAgICAgIHRoaXMuJE5vdGljZS53YXJuaW5nKHsNCiAgICAgICAgICAgICAgICB0aXRsZTogIuazqOaEjyIsDQogICAgICAgICAgICAgICAgZGVzYzogIueUteihqC/ljY/orq7jgJAiICsgZGF0YS5hbW1ldGVyY29kZSArICLjgJEiICsgbXNnLA0KICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxMCwNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICB2YWxpZGF0ZXRyYW5zZm9ybWVydWxsYWdlKCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXR0cmFuc2Zvcm1lcnVsbGFnZTsNCiAgICAgIGRhdGEudHJhbnNmb3JtZXJ1bGxhZ2UgPSB2YWw7DQogICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICB9LA0KICAgIHZhbGlkYXRlYWNjb3VudG1vbmV5KCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRhY2NvdW50bW9uZXk7DQogICAgICBkYXRhLmFjY291bnRtb25leSA9IE1hdGguYWJzKHZhbCk7DQogICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgIHRoaXMudW5pdFByaWNlKGRhdGEpOw0KICAgICAgdGhpcy52YWxpZGF0ZVVuaXRQcmljZShkYXRhKTsNCiAgICB9LA0KICAgIHNldHJlbWFyaygpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICBsZXQgdmFsID0gdGhpcy5lZGl0cmVtYXJrOw0KICAgICAgZGF0YS5yZW1hcmsgPSB2YWw7DQogICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICB9LA0KICAgIHNldE15U3R5bGUobGVuZ3RoKSB7DQogICAgICB0aGlzLm15U3R5bGUgPSBbXTsNCiAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbGVuZ3RoOyBpKyspIHsNCiAgICAgICAgdGhpcy5teVN0eWxlLnB1c2goew0KICAgICAgICAgIHN0YXJ0ZGF0ZTogIm15c3BhbiIsDQogICAgICAgICAgZW5kZGF0ZTogIm15c3BhbiIsDQogICAgICAgICAgY3VydG90YWxyZWFkaW5nczogIm15c3BhbiIsDQogICAgICAgICAgYWNjb3VudG1vbmV5OiAibXlzcGFuIiwNCiAgICAgICAgICByZW1hcms6ICJteXNwYW4iLA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vc3BhbueCueWHu+S6i+S7tuWwhnNwYW7mjaLmiJDovpPlhaXmoYblubbkuJTojrflj5bnhKbngrkNCiAgICBzZWxlY3RDYWxsKHJvdywgaW5kZXgsIGNvbHVtbnMsIHN0cikgew0KICAgICAgdGhpcy5lZGl0U3RhcnREYXRlID0gcm93LnN0YXJ0ZGF0ZTsNCiAgICAgIHRoaXMuZWRpdEVuZERhdGUgPSByb3cuZW5kZGF0ZTsNCiAgICAgIHRoaXMuZWRpdGN1cnRvdGFscmVhZGluZ3MgPQ0KICAgICAgICByb3cuY3VydG90YWxyZWFkaW5ncyA9PSBudWxsIHx8IHJvdy5jdXJ0b3RhbHJlYWRpbmdzID09PSAwDQogICAgICAgICAgPyBudWxsDQogICAgICAgICAgOiByb3cuY3VydG90YWxyZWFkaW5nczsNCiAgICAgIHRoaXMuZWRpdGFjY291bnRtb25leSA9DQogICAgICAgIHJvdy5hY2NvdW50bW9uZXkgPT0gbnVsbCB8fCByb3cuYWNjb3VudG1vbmV5ID09PSAwID8gbnVsbCA6IHJvdy5hY2NvdW50bW9uZXk7DQogICAgICB0aGlzLmVkaXRyZW1hcmsgPSByb3cucmVtYXJrOw0KDQogICAgICB0aGlzLmVkaXRJbmRleCA9IGluZGV4Ow0KICAgICAgdGhpcy5jb2x1bW5zSW5kZXggPSBjb2x1bW5zOw0KDQogICAgICBsZXQgYSA9IHRoaXM7DQogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgaWYgKGNvbHVtbnMgIT0gOCkgew0KICAgICAgICAgIGEuJHJlZnNbc3RyICsgaW5kZXggKyBjb2x1bW5zXS5mb2N1cygpOw0KICAgICAgICB9DQogICAgICB9LCAyMDApOw0KICAgIH0sDQogICAgLy/ot7PovazliLDkuIvkuIDmoLwNCiAgICBuZXh0Q2VsbChkYXRhKSB7DQogICAgICBsZXQgaW5kZXggPSBkYXRhLmVkaXRJbmRleDsNCiAgICAgIGxldCBjb2x1bW5zID0gZGF0YS5jb2x1bW5zSW5kZXg7DQogICAgICBsZXQgcm93ID0gIiI7DQogICAgICBpZiAoaW5kZXggPT09IC0xICYmIGNvbHVtbnMgPT09IC0xKSB7DQogICAgICAgIGluZGV4ID0gMDsNCiAgICAgICAgY29sdW1ucyA9IDE7DQogICAgICB9IGVsc2UgaWYgKGluZGV4ID4gLTEgJiYgY29sdW1ucyA9PT0gNSkgew0KICAgICAgICAvL+W9k+i3s+i9rOeahOacgOWQjuS4gOihjOacgOWQjuS4gOagvOeahOaXtuWAmQ0KICAgICAgICBpZiAoaW5kZXggPj0gZGF0YS5wYWdlU2l6ZSAtIDEgfHwgaW5kZXggPj0gZGF0YS5wYWdlVG90YWwgLSAxKSB7DQogICAgICAgICAgaW5kZXggPSAwOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGluZGV4Kys7DQogICAgICAgIH0NCiAgICAgICAgY29sdW1ucyA9IDE7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb2x1bW5zICs9IDE7DQogICAgICB9DQogICAgICBkYXRhLmVkaXRJbmRleCA9IGluZGV4Ow0KICAgICAgZGF0YS5jb2x1bW5zSW5kZXggPSBjb2x1bW5zOw0KICAgICAgcm93ID0gZGF0YS50YkFjY291bnQuZGF0YVtpbmRleF07DQogICAgICBpZiAocm93KSB7DQogICAgICAgIGRhdGEuZWRpdFN0YXJ0RGF0ZSA9IHJvdy5zdGFydGRhdGU7DQogICAgICAgIGRhdGEuZWRpdEVuZERhdGUgPSByb3cuZW5kZGF0ZTsNCiAgICAgICAgZGF0YS5lZGl0Y3VydG90YWxyZWFkaW5ncyA9DQogICAgICAgICAgcm93LmN1cnRvdGFscmVhZGluZ3MgPT0gbnVsbCB8fCByb3cuY3VydG90YWxyZWFkaW5ncyA9PT0gMA0KICAgICAgICAgICAgPyBudWxsDQogICAgICAgICAgICA6IHJvdy5jdXJ0b3RhbHJlYWRpbmdzOw0KICAgICAgICBkYXRhLmVkaXRhY2NvdW50bW9uZXkgPQ0KICAgICAgICAgIHJvdy5hY2NvdW50bW9uZXkgPT0gbnVsbCB8fCByb3cuYWNjb3VudG1vbmV5ID09PSAwID8gbnVsbCA6IHJvdy5hY2NvdW50bW9uZXk7DQogICAgICAgIGRhdGEuZWRpdHJlbWFyayA9IHJvdy5yZW1hcms7DQogICAgICB9DQoNCiAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgew0KICAgICAgICBkYXRhLiRyZWZzW2RhdGEuZW50ZXJPcGVyYXRlKGNvbHVtbnMpLnN0ciArIGluZGV4ICsgY29sdW1uc10uZm9jdXMoKTsNCiAgICAgIH0sIDIwMCk7DQogICAgfSwNCiAgICAvL+agueaNruWIl+WPt+i/lOWbnuWvueW6lOeahOWIl+WQjQ0KICAgIGVudGVyT3BlcmF0ZShudW1iZXIpIHsNCiAgICAgIGxldCBzdHIgPSAiIjsNCiAgICAgIGxldCBkYXRhID0gbnVsbDsNCiAgICAgIHN3aXRjaCAobnVtYmVyKSB7DQogICAgICAgIGNhc2UgMToNCiAgICAgICAgICBzdHIgPSAic3RhcnRkYXRlIjsNCiAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0U3RhcnREYXRlOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlIDI6DQogICAgICAgICAgc3RyID0gImVuZGRhdGUiOw0KICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRFbmREYXRlOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlIDM6DQogICAgICAgICAgc3RyID0gImN1cnRvdGFscmVhZGluZ3MiOw0KICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRjdXJ0b3RhbHJlYWRpbmdzOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlIDQ6DQogICAgICAgICAgc3RyID0gImFjY291bnRtb25leSI7DQogICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdGFjY291bnRtb25leTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSA1Og0KICAgICAgICAgIHN0ciA9ICJyZW1hcmsiOw0KICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRyZW1hcms7DQogICAgICAgICAgYnJlYWs7DQogICAgICB9DQogICAgICByZXR1cm4geyBzdHI6IHN0ciwgZGF0YTogZGF0YSB9Ow0KICAgIH0sDQogICAgcHJlZCgpIHsNCiAgICAgIHZhciBsZXR0ID0gdGhpczsNCiAgICAgIGxldCBpbmRleCA9IGxldHQuZWRpdEluZGV4Ow0KICAgICAgbGV0IGNvbHVtbnMgPSBsZXR0LmNvbHVtbnNJbmRleDsNCiAgICAgIGlmIChpbmRleCA9PT0gLTEgJiYgY29sdW1ucyA9PT0gLTEpIHsNCiAgICAgICAgaW5kZXggPSAwOw0KICAgICAgICBjb2x1bW5zID0gMTsNCiAgICAgICAgbGV0dC5lZGl0SW5kZXggPSBpbmRleDsNCiAgICAgICAgbGV0dC5jb2x1bW5zSW5kZXggPSBjb2x1bW5zOw0KICAgICAgICBsZXR0LmVkaXRTdGFydERhdGUgPSBsZXR0LnRiQWNjb3VudC5kYXRhW2luZGV4XS5zdGFydGRhdGU7DQogICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgew0KICAgICAgICAgIGxldHQuJHJlZnNbbGV0dC5lbnRlck9wZXJhdGUoY29sdW1ucykuc3RyICsgaW5kZXggKyBjb2x1bW5zXS5mb2N1cygpOw0KICAgICAgICB9LCAyMDApOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGV0dC52YWxpZGF0ZSgpOw0KICAgICAgICBsZXR0LnNldHJlbWFyaygpOw0KICAgICAgICBsZXR0Lm5leHRDZWxsKGxldHQpOw0KICAgICAgfQ0KICAgIH0sDQogICAgZWxsaXBzaXModmFsdWUpIHsNCiAgICAgIGlmICghdmFsdWUpIHJldHVybiAiIjsNCiAgICAgIGlmICh2YWx1ZS5sZW5ndGggPiAzKSB7DQogICAgICAgIHJldHVybiB2YWx1ZS5zbGljZSgwLCAzKSArICIuLi4iOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHZhbHVlOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["addAdvanceAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addAdvanceAccount.vue", "sourceRoot": "src/view/account/homePageAccount", "sourcesContent": ["<!--自有预付电费台账-->\r\n\r\n<template>\r\n  <div class=\"page-class page-card\">\r\n    <div class=\"query-box\">\r\n      <Row :class=\"filterColl ? 'header-bar-show' : 'header-bar-hide'\">\r\n        <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.accountno\"\r\n                  :style=\"formItemWidth\"\r\n                  @on-change=\"accountnoChange\"\r\n                >\r\n                  <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"项目名称:\" prop=\"projectName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.projectName\"\r\n                  placeholder=\"请输入项目名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"电表/协议编码:\"\r\n                prop=\"ammeterName\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.ammeterName\"\r\n                  placeholder=\"请输入电表户号/协议编码\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"供电局电表编号:\"\r\n                prop=\"supplybureauammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.supplybureauammetercode\"\r\n                  placeholder=\"请输入供电局电表编号\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"局站名称:\" prop=\"stationName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.stationName\"\r\n                  placeholder=\"请输入局站名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.company\"\r\n                  @on-change=\"selectChange(accountObj.company)\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                  <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"所属部门：\"\r\n                prop=\"countryName\"\r\n                v-if=\"isAdmin == true\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Input\r\n                  :clearable=\"true\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"accountObj.countryName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n              <FormItem\r\n                label=\"所属部门：\"\r\n                prop=\"country\"\r\n                v-if=\"isAdmin == false\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option value=\"-1\">全部</Option>\r\n                  <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\"></Col>\r\n          </Row>\r\n          <div align=\"right\">\r\n            <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n            <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n            <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n              <Button type=\"default\" style=\"margin-left: 5px\"\r\n                >导出\r\n                <Icon type=\"ios-arrow-down\"></Icon>\r\n              </Button>\r\n              <DropdownMenu slot=\"list\">\r\n                <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n          </div>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon\r\n          :type=\"filterColl ? 'md-arrow-dropup' : 'md-arrow-dropdown'\"\r\n          size=\"20\"\r\n          @click=\"filterColl = !filterColl\"\r\n          :color=\"filterColl ? '#000' : '#1ab394'\"\r\n        ></icon>\r\n      </div>\r\n    </div>\r\n    <div class=\"cl-table\">\r\n      <Row class=\"button-bar\">\r\n        <div class=\"table-button\">\r\n          <Button type=\"primary\" @click=\"addElectricType\">新增</Button>\r\n          <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n          <Button type=\"error\" @click=\"remove\">删除</Button>\r\n          <Button type=\"primary\" @click=\"openCompletedPreModal\">复制归集单台账</Button>\r\n          <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n            <Button type=\"info\" style=\"margin-left: 5px\"\r\n              >加入归集单\r\n              <Icon type=\"ios-arrow-down\"></Icon>\r\n            </Button>\r\n            <DropdownMenu slot=\"list\">\r\n              <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n              <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n          <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n        </div>\r\n      </Row>\r\n      <Table\r\n        ref=\"accountEsTable\"\r\n        border\r\n        :columns=\"tbAccount.columns\"\r\n        :data=\"tbAccount.data\"\r\n        class=\"mytable\"\r\n        :loading=\"tbAccount.loading\"\r\n        :height=\"tableHeight\"\r\n      >\r\n        <!--项目名称-->\r\n        <template slot-scope=\"{ row }\" slot=\"projectName\">\r\n          <div v-if=\"row.status === 5\">\r\n            <span>{{ row.projectName }}</span\r\n            ><span style=\"color: red\">[退回]</span>\r\n          </div>\r\n          <span v-else>{{ row.projectName }}</span>\r\n        </template>\r\n        <!--起始时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"startdate\" v-if=\"row.total == null\">\r\n          <Input\r\n            :ref=\"'startdate' + index + 1\"\r\n            type=\"text\"\r\n            @on-blur=\"validate\"\r\n            v-model=\"editStartDate\"\r\n            v-if=\"editIndex === index && columnsIndex === 1\"\r\n          />\r\n          <span\r\n            :class=\"myStyle[index].startdate\"\r\n            @click=\"selectCall(row, index, 1, 'startdate')\"\r\n            v-else\r\n            >{{ row.startdate }}</span\r\n          >\r\n        </template>\r\n        <!--截止时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"enddate\" v-if=\"row.total == null\">\r\n          <Input\r\n            :ref=\"'enddate' + index + 2\"\r\n            type=\"text\"\r\n            v-model=\"editEndDate\"\r\n            @on-blur=\"validate\"\r\n            v-if=\"editIndex === index && columnsIndex === 2\"\r\n          />\r\n          <span\r\n            :class=\"myStyle[index].enddate\"\r\n            @click=\"selectCall(row, index, 2, 'enddate')\"\r\n            v-else\r\n            >{{ row.enddate }}</span\r\n          >\r\n        </template>\r\n        <!--止度-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"curtotalreadings\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'curtotalreadings' + index + 3\"\r\n              type=\"text\"\r\n              v-model=\"editcurtotalreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 3\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].curtotalreadings\"\r\n              @click=\"selectCall(row, index, 3, 'curtotalreadings')\"\r\n              v-else\r\n              >{{ row.curtotalreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.curtotalreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--预估电费-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"accountmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'accountmoney' + index + 4\"\r\n              type=\"text\"\r\n              v-model=\"editaccountmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 4\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].accountmoney\"\r\n              @click=\"selectCall(row, index, 4, 'accountmoney')\"\r\n              v-else\r\n              >{{ row.accountmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.accountmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--备注-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              v-model=\"editremark\"\r\n              :ref=\"'remark' + index + 5\"\r\n              type=\"text\"\r\n              @on-blur=\"setremark\"\r\n              v-if=\"editIndex === index && columnsIndex === 5\"\r\n            />\r\n            <Tooltip placement=\"bottom\" max-width=\"200\" v-else>\r\n              <span\r\n                :class=\"myStyle[index].remark\"\r\n                style=\"width: 60px\"\r\n                @click=\"selectCall(row, index, 5, 'remark')\"\r\n                >{{ ellipsis(row.remark) }}</span\r\n              >\r\n              <div slot=\"content\">\r\n                {{ row.remark }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n          <div v-else>\r\n            <Tooltip placement=\"bottom\" max-width=\"200\">\r\n              <span>{{ ellipsis(row.remark) }}</span>\r\n              <div slot=\"content\">\r\n                {{ row.remark }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n        </template>\r\n      </Table>\r\n      <div class=\"table-page\">\r\n        <Page\r\n          size=\"small\"\r\n          :total=\"pageTotal\"\r\n          :current=\"pageNum\"\r\n          :page-size=\"pageSize\"\r\n          show-elevator\r\n          show-sizer\r\n          show-total\r\n          placement=\"top\"\r\n          @on-change=\"handlePage\"\r\n          @on-page-size-change=\"handlePageSize\"\r\n        ></Page>\r\n      </div>\r\n      <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n    </div>\r\n    <div>\r\n      <select-ammeter\r\n        ref=\"selectAmmeter\"\r\n        v-on:listenToSelectAmmeter=\"setAmmeterData\"\r\n      ></select-ammeter>\r\n      <add-bill-per\r\n        ref=\"addBillPer\"\r\n        v-on:refreshList=\"refresh\"\r\n        @buttonload2=\"buttonload2\"\r\n        @isButtonload=\"isButtonload\"\r\n      ></add-bill-per>\r\n      <completed-pre-modal\r\n        ref=\"completedPre\"\r\n        v-on:refreshList=\"refresh\"\r\n      ></completed-pre-modal>\r\n      <country-modal\r\n        ref=\"countryModal\"\r\n        v-on:getDataFromModal=\"getDataFromModal\"\r\n      ></country-modal>\r\n    </div>\r\n    <!--    稽核modal-->\r\n    <Modal\r\n      v-model=\"showCheckModel\"\r\n      width=\"80%\"\r\n      title=\"稽核结果及反馈\"\r\n      :mask-closable=\"false\"\r\n    >\r\n      <check-result-and-response ref=\"chekResultAndResponse\"></check-result-and-response>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"showJhModel\"\r\n      width=\"80%\"\r\n      title=\"稽核结果\"\r\n      @on-cancel=\"alarmClose\"\r\n      :mask-closable=\"false\"\r\n    >\r\n      <check-result ref=\"checkResult\"></check-result>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button type=\"primary\" @click=\"nextCheck\">已查阅</Button>\r\n        <Button type=\"text\" @click=\"checkCancel\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"showAlarmModel\"\r\n      width=\"80%\"\r\n      title=\"台账预警稽核\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n      class=\"yjjh\"\r\n    >\r\n      <alarm-check\r\n        ref=\"showAlarmModel\"\r\n        @save=\"save\"\r\n        @submitChange=\"submitChange\"\r\n        @close=\"alarmClose\"\r\n      ></alarm-check>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button size=\"large\" @click=\"alarmClose\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { mapState } from \"vuex\";\r\nimport { getAuditResult, getAuditResultNew, getAuditResultNew_QXM } from \"@/api/account\";\r\nimport {\r\n  temporaryStorage2,\r\n  addPredPowerAccount,\r\n  addAccountEs,\r\n  removeAccountEs,\r\n  getUser,\r\n  getDepartments,\r\n  accountEsTotal,\r\n  selectIdsByEsParams,\r\n} from \"@/api/account\";\r\nimport { getResCenter, getcompany } from \"@/api/alertcontrol/alertcontrol\";\r\nimport { againJoin } from \"@/api/accountBillPer\";\r\nimport {\r\n  getClassification,\r\n  getUserdata,\r\n  getUserByUserRole,\r\n  getCountrysdata,\r\n  getCountryByUserId,\r\n} from \"@/api/basedata/ammeter.js\";\r\nimport checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\nimport checkResult from \"@/view/account/check/checkResult\";\r\nimport alarmCheck from \"@/view/account/check/alarmCheck\";\r\nimport {\r\n  getDates2,\r\n  getDates,\r\n  testNumber,\r\n  getFirstDateByAccountno_yyyymmdd,\r\n  getLastDateByAccountno_yyyymmdd,\r\n  cutDate_yyyymmdd,\r\n  stringToDate,\r\n  getCurrentDate,\r\n} from \"@/view/account/powerAccountHelper\";\r\nimport {\r\n  _verify_StartDate,\r\n  _verify_EndDate,\r\n  verification,\r\n  unitpirceMin,\r\n  unitpirceMax,\r\n  unitpirceMax1,\r\n} from \"@/view/account/PowerAccountEs\";\r\nimport { judge_negate, judge_recovery } from \"@/view/account/PowerAccountController\";\r\nimport SelectAmmeter from \"@/view/account/selectAmmeter\";\r\nimport AddBillPer from \"@/view/account/addBillPreModal\";\r\nimport CompletedPreModal from \"@/view/account/completedPreModal\";\r\nimport { widthstyle } from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nimport CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\nimport excel from \"@/libs/excel\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport axios from \"@/libs/api.request\";\r\nimport indexData from \"@/config/index\";\r\nimport permissionMixin from \"@/mixins/permission\";\r\nimport pageFun from \"@/mixins/pageFun\";\r\n\r\nlet dates = getDates();\r\nexport default {\r\n  mixins: [permissionMixin, pageFun],\r\n  name: \"addPredPowerAccount\",\r\n  components: {\r\n    alarmCheck,\r\n    checkResult,\r\n    checkResultAndResponse,\r\n    CompletedPreModal,\r\n    SelectAmmeter,\r\n    AddBillPer,\r\n    CountryModal,\r\n  },\r\n  data() {\r\n    let renderStatus = (h, { row, index }) => {\r\n      var status = \"\";\r\n      let data = this.tbAccount.data[index];\r\n      for (let item of this.accountStatus) {\r\n        if (item.typeCode == row.status) {\r\n          data.statusName = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", data.statusName);\r\n    };\r\n\r\n    let renderCategory = (h, params) => {\r\n      var categoryname = \"\";\r\n      for (let item of this.categorys) {\r\n        if (item.typeCode == params.row.category) {\r\n          categoryname = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", categoryname);\r\n    };\r\n\r\n    return {\r\n      isT: true,\r\n      number2: 0,\r\n      name: \"\",\r\n      dataL: [],\r\n      isQuery: true,\r\n      number: 0,\r\n      ctgKeyList: [],\r\n      submit: [],\r\n      submit2: [],\r\n      ammeterids: [],\r\n      showCheckModel: false,\r\n      showAlarmModel: false,\r\n      showJhModel: false,\r\n      formItemWidth: widthstyle,\r\n      version: \"\",\r\n      dateList: dates,\r\n      filterColl: true, //搜索面板展开\r\n      editIndex: -1, //当前编辑行\r\n      columnsIndex: -1, //当前编辑列\r\n      editStartDate: \"\",\r\n      myStyle: [], //样式\r\n      editEndDate: \"\",\r\n      editcurusedreadings: \"\",\r\n      editcurtotalreadings: \"\",\r\n      edittransformerullage: \"\",\r\n      spinShow: false, //遮罩\r\n      categorys: [], //描述类型\r\n      editaccountmoney: \"\",\r\n      editremark: \"\",\r\n      accountStatus: [],\r\n      companies: [],\r\n      departments: [],\r\n      isAdmin: false,\r\n      company: null, //用户默认公司\r\n      country: null, //用户默认所属部门\r\n      countryName: null, //用户默认所属部门\r\n      export: {\r\n        run: false, //是否正在执行导出\r\n        data: \"\", //导出数据\r\n        totalPage: 0, //一共多少页\r\n        currentPage: 0, //当前多少页\r\n        percent: 0,\r\n        size: ********,\r\n      },\r\n      accountObj: {\r\n        supplybureauammetercode: \"\",\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        company: \"\", //分公司\r\n        projectName: \"\", //项目名称\r\n        country: \"\", //所属部门\r\n        ammeterName: \"\", //电表户号/协议编码\r\n        stationName: \"\",\r\n        accountType: \"1\", //台账类型\r\n        accountestype: 3, //台账类型\r\n        countryName: \"\",\r\n      },\r\n      tbAccount: {\r\n        loading: false,\r\n        columns: [\r\n          { type: \"selection\", width: 60, align: \"center\" },\r\n          {\r\n            title: \"稽核结果及反馈\",\r\n            key: \"action\",\r\n            align: \"center\",\r\n            render: (h, params) => {\r\n              var that = this;\r\n              return h(\r\n                \"Button\",\r\n                {\r\n                  props: {\r\n                    type: \"primary\",\r\n                    size: \"small\",\r\n                  },\r\n                  style: {\r\n                    \"font-size\": \"10px\",\r\n                  },\r\n                  on: {\r\n                    click() {\r\n                      that.$refs.chekResultAndResponse.pcid = params.row.pcid;\r\n                      that.showCheckModel = true;\r\n                    },\r\n                  },\r\n                },\r\n                \"稽核结果及反馈\"\r\n              );\r\n            },\r\n            minWidth: 120,\r\n            maxWidth: 150,\r\n          },\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n          { title: \"电表户号/协议编码\", key: \"ammeterName\", align: \"center\" },\r\n          {\r\n            title: \"供电局编码\",\r\n            key: \"supplybureauammetercode\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"所属分公司\", key: \"companyName\", align: \"center\" },\r\n          { title: \"所属部门\", key: \"countryName\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 160 },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            key: \"startdate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            key: \"enddate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          { title: \"本期起度\", key: \"prevtotalreadings\", align: \"center\" },\r\n          {\r\n            title: \"本期止度\",\r\n            slot: \"curtotalreadings\",\r\n            key: \"curtotalreadings\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"用电量(度)\", key: \"curusedreadings\", align: \"center\" },\r\n          { title: \"电价(元)\", key: \"unitpirce\", align: \"center\" },\r\n          { title: \"电费\", slot: \"accountmoney\", key: \"accountmoney\", align: \"center\" },\r\n          { title: \"备注\", slot: \"remark\", key: \"remark\", align: \"center\" },\r\n          { title: \"用电类型\", key: \"electrotypename\", align: \"center\", width: 94 },\r\n          {\r\n            title: \"类型描述\",\r\n            key: \"categoryname\",\r\n            align: \"center\",\r\n            render: renderCategory,\r\n          },\r\n        ],\r\n        data: [],\r\n        total: 0,\r\n        exportColumns: [\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n          { title: \"电表户号/协议编码\", key: \"ammeterName\", align: \"center\" },\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n          { title: \"所属分公司\", key: \"companyName\", align: \"center\" },\r\n          { title: \"所属部门\", key: \"countryName\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            key: \"startdate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            key: \"enddate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          { title: \"本期起度\", key: \"prevtotalreadings\", align: \"center\" },\r\n          {\r\n            title: \"本期止度\",\r\n            slot: \"curtotalreadings\",\r\n            key: \"curtotalreadings\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"用电量(度)\", key: \"curusedreadings\", align: \"center\" },\r\n          { title: \"电价(元)\", key: \"unitpirce\", align: \"center\" },\r\n          { title: \"电费\", slot: \"accountmoney\", key: \"accountmoney\", align: \"center\" },\r\n          { title: \"备注\", slot: \"remark\", key: \"remark\", align: \"center\" },\r\n          { title: \"用电类型\", key: \"electrotypename\", align: \"center\", width: 94 },\r\n          {\r\n            title: \"类型描述\",\r\n            key: \"categoryname\",\r\n            align: \"center\",\r\n            render: renderCategory,\r\n          },\r\n        ],\r\n      },\r\n      pageTotal: 0,\r\n      pageNum: 1,\r\n      pageSize: 10, //当前页\r\n    };\r\n  },\r\n  mounted() {\r\n    this.handleHeight(); //table高度自定义\r\n\r\n    this.version = indexData.version;\r\n\r\n    this.accountStatus = blist(\"accountStatus\");\r\n    this.categorys = blist(\"ammeterCategory\");\r\n    let that = this;\r\n    getUserByUserRole().then((res) => {\r\n      //根据权限获取分公司\r\n      that.companies = res.data.companies;\r\n      if (\r\n        res.data.isCityAdmin == true ||\r\n        res.data.isProAdmin == true ||\r\n        res.data.isSubAdmin == true\r\n      ) {\r\n        that.isAdmin = true;\r\n      }\r\n      getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {\r\n        //根据权限获取所属部门\r\n        that.departments = res.data;\r\n        that.getUserData();\r\n      });\r\n    });\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      loginId: (state) => state.user.loginId,\r\n    }),\r\n  },\r\n  methods: {\r\n    buttonload2(v) {\r\n      this.$refs.showAlarmModel.buttonload2 = v;\r\n    },\r\n    isButtonload(v) {\r\n      this.$refs.showAlarmModel.buttonload = v;\r\n    },\r\n    isShows(t) {\r\n      this.$refs.showAlarmModel.show = t;\r\n      if (this.$refs.showAlarmModel.show == false) {\r\n        this.number2++;\r\n        this.isT = true;\r\n      } else if (this.$refs.showAlarmModel.show == true) {\r\n        this.number2 = 0;\r\n        this.isT = false;\r\n      }\r\n      if (this.isT && this.number2 < 10) {\r\n        this.isShows(t);\r\n      }\r\n    },\r\n    nextCheck() {\r\n      this.showAlarmModel = true;\r\n      this.isShows(true);\r\n      this.$refs.showAlarmModel.tableData = [];\r\n      this.$refs.showAlarmModel.resultList = [];\r\n      this.$refs.showAlarmModel.tableData1 = [];\r\n      this.$refs.showAlarmModel.resultList1 = [];\r\n      this.$refs.showAlarmModel.tableData2 = [];\r\n      this.$refs.showAlarmModel.resultList2 = [];\r\n      this.$refs.showAlarmModel.tableData3 = [];\r\n      this.$refs.showAlarmModel.resultList3 = [];\r\n      this.$refs.showAlarmModel.tableData4 = [];\r\n      this.$refs.showAlarmModel.resultList4 = [];\r\n      this.$refs.showAlarmModel.tableData5 = [];\r\n      this.$refs.showAlarmModel.resultList5 = [];\r\n      this.showJhModel = false;\r\n      this.$refs.showAlarmModel.activeButton = 6;\r\n    },\r\n    alarmClose() {\r\n      this.showAlarmModel = false;\r\n      this.$refs.showAlarmModel.show = false;\r\n    },\r\n    checkCancel() {\r\n      this.showJhModel = false;\r\n    },\r\n    alarmCheck() {},\r\n    selectChange() {\r\n      let that = this;\r\n      if (that.accountObj.company != undefined) {\r\n        if (that.accountObj.company == \"-1\") {\r\n          that.accountObj.country = -1;\r\n          that.accountObj.countryName = null;\r\n        } else {\r\n          getCountryByUserId(that.accountObj.company).then((res) => {\r\n            if (res.data.departments.length != 0) {\r\n              that.accountObj.country = res.data.departments[0].id;\r\n              that.accountObj.countryName = res.data.departments[0].name;\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //选择所属部门开始\r\n    chooseResponseCenter() {\r\n      if (this.accountObj.company == null || this.accountObj.company == \"-1\") {\r\n        this.$Message.info(\"请先选择分公司\");\r\n        return;\r\n      }\r\n      this.$refs.countryModal.choose(this.accountObj.company); //所属部门\r\n    },\r\n    getDataFromModal(data) {\r\n      this.accountObj.country = data.id;\r\n      this.accountObj.countryName = data.name;\r\n      //选择所属部门结束\r\n    },\r\n    getUserData() {\r\n      let that = this;\r\n      getUserdata().then((res) => {\r\n        //当前登录用户所在公司和所属部门\r\n        if (res.data.companies.length != 0) {\r\n          let companies = res.data.companies;\r\n          if (res.data.companies[0].id == \"**********\") {\r\n            companies = that.companies;\r\n          }\r\n          that.company = companies[0].id;\r\n          that.accountObj.company = companies[0].id;\r\n        }\r\n        if (res.data.departments.length != 0) {\r\n          let departments = res.data.departments;\r\n          if (res.data.companies[0].id == \"**********\" && that.departments.length != 0) {\r\n            departments = that.departments;\r\n          }\r\n          that.country = departments[0].id;\r\n          that.countryName = departments[0].name;\r\n          that.accountObj.country = Number(departments[0].id);\r\n          that.accountObj.countryName = departments[0].name;\r\n        }\r\n\r\n        that.pageNum = 1;\r\n        that.getAccountMessages();\r\n      });\r\n    },\r\n    searchList() {\r\n      if (this.accountObj.countryName == \"\") {\r\n        this.accountObj.country = \"-1\";\r\n      }\r\n      this.pageNum = 1;\r\n      this.getAccountMessages();\r\n    },\r\n    accountnoChange() {\r\n      this.searchList();\r\n    },\r\n    setAmmeterData: function (data) {\r\n      let arrayData = [];\r\n      let ctgKeyList = [];\r\n      let no = this.accountObj.accountno;\r\n\r\n      if (data != null && data.length > 0) {\r\n        data.forEach(function (item) {\r\n          let obj = {};\r\n          obj.pcid = item.pcid;\r\n          obj.ammeterName = item.ammetername;\r\n          obj.projectName = item.projectname;\r\n          obj.substation = item.substation;\r\n          obj.categoryname = item.categoryname;\r\n          obj.category = item.category;\r\n          obj.ammeterid = item.ammeterid;\r\n          obj.company = item.company;\r\n          obj.companyName = item.companyName;\r\n          obj.country = item.country;\r\n          obj.ammeteruse = item.ammeteruse;\r\n          obj.countryName = item.countryName;\r\n          obj.startdate = null;\r\n          obj.enddate = null;\r\n          obj.prevtotalreadings = 0;\r\n          obj.curtotalreadings = 0;\r\n          obj.curusedreadings = 0;\r\n          obj.transformerullage = 0;\r\n          obj.unitpirce = 0;\r\n          obj.accountmoney = 0;\r\n          obj.remark = null;\r\n          obj.electrotype = item.electrotype;\r\n          obj.stationcode5gr = item.stationcode5gr;\r\n          obj.stationname5gr = item.stationname5gr;\r\n          obj.electrotypename = item.electrotypename;\r\n          obj.stationName = item.stationName;\r\n          obj.startdate = getFirstDateByAccountno_yyyymmdd(no);\r\n          obj.enddate = getLastDateByAccountno_yyyymmdd(no);\r\n          obj.accountestype = 3;\r\n          obj.supplybureauammetercode = item.supplybureauammetercode;\r\n          arrayData.push(obj);\r\n          ctgKeyList.push({ ctgKey: item.ctgKey, ammetername: item.ammetername });\r\n        });\r\n        this.ctgKeyList = ctgKeyList;\r\n      }\r\n      if (arrayData.length > 0) {\r\n        arrayData.forEach(function (item) {\r\n          let req = {\r\n            url: \"/business/accountEs/selectPrevtotalreadings\",\r\n            method: \"post\",\r\n            params: { ammeterid: item.ammeterid },\r\n          };\r\n          axios.request(req).then((res) => {\r\n            if (res.data) {\r\n              item.prevtotalreadings = res.data;\r\n            }\r\n          });\r\n        });\r\n      }\r\n\r\n      let origin = this.tbAccount.data;\r\n      if (origin.length < 1) {\r\n        this.tbAccount.data = arrayData;\r\n      } else {\r\n        let tem = arrayData;\r\n        let total = this.pageTotal;\r\n        this.pageTotal = total + tem.length;\r\n        this.tbAccount.data = tem.concat(this.tbAccount.data);\r\n      }\r\n\r\n      this.setMyStyle(this.tbAccount.data.length);\r\n    },\r\n    //暂存\r\n    temporaryStorage() {\r\n      let array = [];\r\n      this.tbAccount.data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          array.push(item);\r\n        }\r\n      });\r\n      // ----------------------\r\n      let data = array;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let str = \"\";\r\n        let accountno = this.accountObj.accountno;\r\n        let index = 0;\r\n        data.forEach(function (item) {\r\n          if (item.projectName != \"小计\" && item.projectName != \"合计\") {\r\n            if (item.pcid == null) {\r\n              item.accountno = accountno;\r\n            }\r\n            Object.assign(item, { totalusedreadings: item.curusedreadings }); // 确保预付总电量 等于用电量\r\n            submitData.push(item);\r\n            number++;\r\n          }\r\n        });\r\n        if (submitData.length > 0) {\r\n          temporaryStorage2(submitData).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：成功暂存 \" + submitData.length + \" 条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //点击保存\r\n    preserve() {\r\n      let dataL = this.$refs.accountEsTable.getSelection();\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      let version = indexData.version;\r\n      for (let i = 0; i < dataL.length; i++) {\r\n        if (dataL[i].editType == 1) {\r\n          if (\r\n            \"sc\" == version &&\r\n            dataL[i].electrotype &&\r\n            dataL[i].electrotype > 1400 &&\r\n            (dataL[i].stationcode5gr == null ||\r\n              dataL[i].stationcode5gr == undefined ||\r\n              dataL[i].stationcode5gr == \"\")\r\n          ) {\r\n            this.errorTips(\r\n              \"电表/协议编号【\" +\r\n                dataL[i].ammeterName +\r\n                \"】，项目名称【\" +\r\n                dataL[i].projectName +\r\n                \"】关联局站的5GR站址为空，请完善局站信息，或者5GR有效性清单失效，请联系无线管理员。\"\r\n            );\r\n          }\r\n          b = true;\r\n          array.push(dataL[i]);\r\n        }\r\n      }\r\n      if (b) {\r\n        this.submitData(array);\r\n      } else {\r\n        this.errorTips(\"没有可保存数据\");\r\n      }\r\n    },\r\n    //四川能耗稽核流程\r\n    preserveSc() {\r\n      this.$refs.checkResult.ammeterids = this.ammeterids;\r\n      this.showJhModel = true;\r\n    },\r\n    submitChange(indexList) {\r\n      let data = [];\r\n      this.submit2.map((item, index) => {\r\n        indexList.map((item2) => {\r\n          if (index == item2) {\r\n            data.push(item);\r\n          }\r\n        });\r\n      });\r\n      this.submit = data;\r\n    },\r\n    submitChange1(data) {\r\n      this.submit = data;\r\n    },\r\n    getAuditResultNew(data) {\r\n      let arr = [];\r\n      data.forEach((item) => {\r\n        arr.push(item.pcid);\r\n      });\r\n      let param = {\r\n        pcids: arr,\r\n      };\r\n      getAuditResultNew_QXM(param).then((res2) => {\r\n        this.auditResultList = res2.data;\r\n        if (this.auditResultList && this.auditResultList.length == 0) {\r\n          this.number++;\r\n          this.isQuery = true;\r\n        } else if (data.length != this.auditResultList.length) {\r\n          this.number++;\r\n          this.isQuery = true;\r\n        } else if (data.length == this.auditResultList.length) {\r\n          this.isQuery = false;\r\n          this.number = 0;\r\n        } else {\r\n          this.isQuery = false;\r\n          this.number = 0;\r\n        }\r\n        if (this.isQuery && this.number < 3) {\r\n          if (data.length > 300) {\r\n            setTimeout(() => this.getAuditResultNew(data), 5000);\r\n          } else if (data.length > 100 && data.length < 300) {\r\n            setTimeout(() => this.getAuditResultNew(data), 3000);\r\n          } else {\r\n            setTimeout(() => this.getAuditResultNew(data), 2000);\r\n          }\r\n        } else {\r\n          this.auditResultList.forEach((item) => {\r\n            this.$refs.showAlarmModel.resultList.push(item.msg);\r\n            this.$refs.showAlarmModel.tableData.push(item.powerAuditEntity);\r\n            if (item.staute == \"失败\") {\r\n              // if(item.powerAuditEntity.mutiJtlteCodes=='是'\r\n              // || item.powerAuditEntity.electricityPrices=='否'\r\n              // || item.powerAuditEntity.addressConsistence=='否'\r\n              // || item.powerAuditEntity.reimbursementCycle=='否' || item.powerAuditEntity.electricityContinuity=='否' ||\r\n              // item.powerAuditEntity.shareAccuracy=='否' ||\r\n              // item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n              // item.powerAuditEntity.paymentConsistence=='否'){\r\n              if (item.powerAuditEntity.mutiJtlteCodes == \"是\") {\r\n                this.$refs.showAlarmModel.tableData4.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList4.push(item.msg);\r\n              }\r\n              if (item.powerAuditEntity.electricityPrices == \"否\") {\r\n                this.$refs.showAlarmModel.tableData5.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList5.push(item.msg);\r\n              }\r\n              if (\r\n                item.powerAuditEntity.addressConsistence == \"否\" ||\r\n                item.powerAuditEntity.reimbursementCycle == \"否\" ||\r\n                item.powerAuditEntity.electricityContinuity == \"否\" ||\r\n                item.powerAuditEntity.shareAccuracy == \"否\" ||\r\n                //   item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n                item.powerAuditEntity.paymentConsistence == \"否\" ||\r\n                item.powerAuditEntity.fluctuateContinuity == \"否\"\r\n              ) {\r\n                this.$refs.showAlarmModel.tableData2.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList2.push(item.msg);\r\n              }\r\n            } else {\r\n              if (\r\n                // item.powerAuditEntity.electricityRationality == \"是\" && //电量合理性(省内大数据)\r\n                // item.powerAuditEntity.exclusiveAccuracy == \"是\" && //局站独享共享设置\r\n                item.powerAuditEntity.periodicAnomaly == \"是\" //台账周期合理性\r\n              ) {\r\n                this.$refs.showAlarmModel.tableData1.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList1.push(item.msg);\r\n              } else {\r\n                this.$refs.showAlarmModel.tableData3.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList3.push(item.msg);\r\n              }\r\n            }\r\n            if (this.auditResultList.length > 0) {\r\n              this.auditResultList[this.auditResultList.length - 1].progress = 1;\r\n            }\r\n            this.$refs.showAlarmModel.processData = Number(item.progress) * 100;\r\n            this.$refs.showAlarmModel.scrollList();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    save(value) {\r\n      var that = this;\r\n      //进度条页面链接websoket后调用，传jh字段不走保存流程，走稽核流程\r\n      if (value == 1) {\r\n        that.submit[0].jh = \"1\";\r\n        that.submit[0].ymmc = \"自有预付电费台账\";\r\n        that.submit.forEach((item1) => {\r\n          this.ctgKeyList.forEach((item2) => {\r\n            if (item1.ammeterName == item2.ammetername) {\r\n              item1.ctgKey = item2.ctgKey;\r\n            }\r\n          });\r\n        });\r\n        this.getAuditResultNew(that.submit);\r\n      } else {\r\n        if (that.submit[0].hasOwnProperty(\"jh\")) {\r\n          delete that.submit[0].jh;\r\n        }\r\n        if (this.name == \"current\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAccount();\r\n          }\r\n        } else if (this.name == \"all\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAllAccount();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //加入归集单，全部非强控\r\n    selectedFQK() {\r\n      let that = this;\r\n      that.$refs.addBillPer.initAmmeter(\r\n        that.$refs.showAlarmModel.selectIds3,\r\n        13,\r\n        this.accountObj.country\r\n      );\r\n    },\r\n    //提交数据\r\n    submitData(data) {\r\n      let a = [];\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let str = \"\";\r\n        let accountno = this.accountObj.accountno;\r\n        let index = 0;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          if (item.projectName != \"小计\" && item.projectName != \"合计\") {\r\n            let obj = verification(item);\r\n            if (obj.result) {\r\n              if (item.pcid == null) {\r\n                item.accountno = accountno;\r\n              }\r\n              Object.assign(item, { totalusedreadings: item.curusedreadings }); // 确保预付总电量 等于用电量\r\n\r\n              a.push(item.ammeterid);\r\n              submitData.push(item);\r\n              number++;\r\n            } else {\r\n              str +=\r\n                \"电表/协议编号为【\" +\r\n                item.ammeterName +\r\n                \"】的台账验证没有通过：【\" +\r\n                obj.str +\r\n                \"】；\";\r\n            }\r\n          }\r\n        });\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          this.errorTips(str);\r\n        }\r\n        if (submitData.length > 0) {\r\n          this.submit = submitData;\r\n          this.submit2 = submitData;\r\n          //   this.preserveSc()\r\n          addAccountEs(submitData).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：成功保存 \" + submitData.length + \" 条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    addElectricType() {\r\n      let companyId = this.accountObj.company;\r\n      let country = this.accountObj.country;\r\n      if (companyId != null && country != null) {\r\n        let obj = {\r\n          company: companyId,\r\n          country: country,\r\n          accountno: this.accountObj.accountno,\r\n          accountType: \"1\",\r\n          accountestype: 3,\r\n        };\r\n        this.$refs.selectAmmeter.initAmmeter(obj);\r\n      } else {\r\n        this.errorTips(\"请选择分公司和部门\");\r\n      }\r\n    },\r\n    //验证错误弹出提示框\r\n    errorTips(str) {\r\n      this.$Notice.error({\r\n        title: \"提示\",\r\n        desc: str,\r\n        duration: 10,\r\n      });\r\n    },\r\n    handlePage(value) {\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n\r\n      this.pageNum = value;\r\n      this.getAccountMessages();\r\n    },\r\n    handlePageSize(value) {\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n\r\n      this.pageSize = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //向后台请求数据\r\n    getAccountMessages() {\r\n      let params = this.accountObj;\r\n      params.pageNum = this.pageNum;\r\n      params.pageSize = this.pageSize;\r\n      let req = {\r\n        url: \"/business/accountEs/selectAccountEsList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      this.tbAccount.loading = true;\r\n      axios\r\n        .request(req)\r\n        .then((res) => {\r\n          this.tbAccount.loading = false;\r\n          if (res.data) {\r\n            let data = res.data.rows;\r\n            data.forEach(function (item) {\r\n              item.editType = 0;\r\n            });\r\n            data.push(this.suntotal(data)); //小计\r\n            accountEsTotal(this.accountObj).then((res) => {\r\n              //合计\r\n              let alltotal = res.data;\r\n              alltotal.total = \"合计\";\r\n              alltotal.projectName = \"合计\";\r\n              alltotal._disabled = true;\r\n              data.push(alltotal);\r\n            });\r\n            this.tbAccount.data = data;\r\n            this.pageTotal = res.data.total || 0;\r\n            this.setMyStyle(this.tbAccount.data.length);\r\n\r\n            this.editIndex = -1;\r\n            this.columnsIndex = -1;\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //小计\r\n    suntotal(array) {\r\n      let curusedreadings = 0;\r\n      let transformerullage = 0;\r\n      let accountmoney = 0;\r\n      array.forEach(function (item) {\r\n        if (item.effective === 1) {\r\n          curusedreadings += item.curusedreadings;\r\n          transformerullage += item.transformerullage;\r\n          accountmoney += item.accountmoney;\r\n        }\r\n      });\r\n      return {\r\n        curusedreadings: curusedreadings,\r\n        transformerullage: transformerullage,\r\n        accountmoney: accountmoney,\r\n        total: \"小计\",\r\n        projectName: \"小计\",\r\n        _disabled: true,\r\n      };\r\n    },\r\n    //重置\r\n    onResetHandle() {\r\n      this.accountObj = {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        company: this.company,\r\n        projectName: \"\", //项目名称\r\n        country: Number(this.country), //所属部门\r\n        ammeterName: \"\", //电表户号/协议编码\r\n        stationName: \"\",\r\n        accountType: \"1\", //台账类型\r\n        accountestype: 3, //台账类型\r\n        supplybureauammetercode: \"\",\r\n        countryName: this.countryName,\r\n      };\r\n      this.getAccountMessages();\r\n    },\r\n    //计算单价\r\n    unitPrice(row) {\r\n      let accountmoney = row.accountmoney;\r\n      let curusedreadings = row.curusedreadings;\r\n      if (accountmoney != null && curusedreadings != null) {\r\n        let total = null;\r\n        if (curusedreadings == 0) {\r\n          total = 0;\r\n        } else {\r\n          total = accountmoney / curusedreadings;\r\n        }\r\n\r\n        row.unitpirce = total.toFixed(2);\r\n      }\r\n    },\r\n    //验证单价\r\n    validateUnitPrice(data) {\r\n      let category = data.category; //电表描述类型\r\n      let ammeteruse = data.ammeteruse; //电表用途\r\n      let unitpirce = data.unitpirce; //台账单价\r\n      if (!judge_negate(category) && !judge_recovery(ammeteruse)) {\r\n        // if (unitpirce) {\r\n        //   if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n        //     this.errorTips(\r\n        //       \"集团要求单价范围在0.3~2元，此台账单价: \" +\r\n        //         unitpirce +\r\n        //         \" 已超过范围，请确认！\"\r\n        //     );\r\n        //   }\r\n        // }\r\n        if (unitpirce) {\r\n          if (unitpirce != null && unitpirce < unitpirceMax1) {\r\n            // if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n            this.errorTips(\r\n              \"单价范围必须大于0.1元，此台账单价: \" + unitpirce + \"不在范围内，请确认！\"\r\n            );\r\n          }\r\n        }\r\n      }\r\n    },\r\n    remove() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      if (data == null || data.length === 0) {\r\n        this.errorTips(\"请选择要删除的数据\");\r\n        return;\r\n      }\r\n      this.$Modal.confirm({\r\n        title: \"提示\",\r\n        content: \"<p>是否确认删除选中信息？</p>\",\r\n        onOk: () => {\r\n          let b = true;\r\n          let ids = \"\";\r\n          let array = this.tbAccount.data;\r\n          let total = this.pageTotal;\r\n          for (let i = 0; i < data.length; i++) {\r\n            let item = data[i];\r\n            if (item.pcid != null && item.pcid.length > 0) {\r\n              if (item.pabriid) {\r\n                b = false;\r\n              }\r\n              ids += item.pcid + \",\";\r\n            } else {\r\n              for (let j = array.length - 1; j >= 0; j--) {\r\n                let jj = array[j];\r\n                if (jj.ammeterid === item.ammeterid) {\r\n                  array.splice(j, 1);\r\n                  total = total - 1;\r\n                }\r\n              }\r\n            }\r\n          }\r\n          this.pageTotal = total;\r\n          if (b) {\r\n            if (ids.length > 0) {\r\n              removeAccountEs(ids).then((res) => {\r\n                if (res.data.code == 0) {\r\n                  this.$Message.success(\"删除成功\");\r\n                  this.getAccountMessages();\r\n                }\r\n              });\r\n            }\r\n          } else {\r\n            this.errorTips(\"选中信息中有信息还没有跟归集单解除关联，请先解除关联\");\r\n          }\r\n        },\r\n        onCancel: () => {},\r\n      });\r\n    },\r\n    //加入归集单\r\n    addPreserveGJ() {\r\n      let dataL = this.$refs.accountEsTable.getSelection();\r\n      this.dataL = this.$refs.accountEsTable.getSelection();\r\n      if (dataL == null || dataL.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        this.addSubmitDataGJ(dataL);\r\n      }\r\n    },\r\n    //加入归集单\r\n    addPreserveGJAll() {\r\n      let params = this.accountObj;\r\n      params.pageNum = 1;\r\n      params.pageSize = 20000;\r\n      let req = {\r\n        url: \"/business/accountEs/selectAccountEsList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      let array1 = [];\r\n      this.tbAccount.loading = true;\r\n      axios.request(req).then((res) => {\r\n        this.tbAccount.loading = false;\r\n        let dataL = res.data.rows;\r\n        this.dataL = res.data.rows;\r\n        this.addSubmitDataGJ(dataL);\r\n      });\r\n    },\r\n    //提交归集单数据\r\n    addSubmitDataGJ(data) {\r\n      let a = [];\r\n      let b = 1;\r\n      let str = \"\";\r\n      let str1 = \"\";\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let obj = verification(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n        });\r\n        that.ammeterids = a;\r\n        if (b === 1) {\r\n          if (submitData.length > 0) {\r\n            this.submit = submitData;\r\n            this.submit2 = submitData;\r\n            this.preserveSc();\r\n          }\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n        if (str.length > 0) {\r\n          that.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n      }\r\n    },\r\n    openAddBillPerModal(name) {\r\n      this.name = name;\r\n      if (name === \"current\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJ();\r\n        // } else {\r\n        this.selectedAccount();\r\n        // }\r\n      } else if (name === \"all\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJAll();\r\n        // } else {\r\n        this.selectedAllAccount();\r\n        // }\r\n      }\r\n    },\r\n    //加入归集单，全部有效台账\r\n    selectedAllAccount() {\r\n      let that = this;\r\n      that.spinShow = true;\r\n      selectIdsByEsParams(this.accountObj).then((res) => {\r\n        that.spinShow = false;\r\n        if (res.data.length == 0) {\r\n          that.errorTips(\"无有效数据可加入归集单\");\r\n        } else {\r\n          that.$refs.addBillPer.initAmmeter(\r\n            res.data,\r\n            // this.$refs.showAlarmModel.selectIds1,\r\n            13,\r\n            this.accountObj.country\r\n          );\r\n        }\r\n      });\r\n    },\r\n    selectedAccount() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      let b = 1;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        let ids = [];\r\n        data.forEach(function (item) {\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n          ids.push(item.pcid);\r\n        });\r\n        if (b === 1) {\r\n          this.$refs.addBillPer.initAmmeter(ids, 13, this.accountObj.country);\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n      }\r\n    },\r\n    openCompletedPreModal() {\r\n      this.$refs.completedPre.initAmmeter(this.accountObj.country, 13);\r\n    },\r\n    againJoin() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      let b = true;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要重新加入归集单的台账\");\r\n      } else {\r\n        let ids = \"\";\r\n        data.forEach(function (item) {\r\n          let status = item.status;\r\n          if (status != 5) {\r\n            b = false;\r\n          }\r\n          ids += item.pcid + \",\";\r\n        });\r\n        if (b) {\r\n          againJoin(ids).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：操作成功\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        } else {\r\n          this.errorTips(\"只有已退回的台账才能重新加入归集单\");\r\n        }\r\n      }\r\n    },\r\n    refresh() {\r\n      if (this.$refs.showAlarmModel.fromGuijidan != 1) {\r\n        // window.history.go(0);\r\n        this.showAlarmModel = false;\r\n        let obj = this;\r\n        setTimeout(function () {\r\n          obj.getAccountMessages();\r\n        }, 200);\r\n      } else {\r\n        this.showAlarmModel = true;\r\n      }\r\n    },\r\n    beforeLoadData(data, str) {\r\n      var cols = [],\r\n        keys = [];\r\n      for (var i = 0; i < this.tbAccount.exportColumns.length; i++) {\r\n        cols.push(this.tbAccount.exportColumns[i].title);\r\n        keys.push(this.tbAccount.exportColumns[i].key);\r\n      }\r\n      const params = {\r\n        title: cols,\r\n        key: keys,\r\n        data: data,\r\n        autoWidth: true,\r\n        filename: str,\r\n      };\r\n      excel.export_array_to_excel(params);\r\n      return;\r\n    },\r\n    exportCsv(name) {\r\n      this.export.run = true;\r\n      if (name === \"current\") {\r\n        this.beforeLoadData(this.tbAccount.data, \"预付台账导出数据\");\r\n      } else if (name === \"all\") {\r\n        let params = this.accountObj;\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n        let req = {\r\n          url: \"/business/accountEs/selectAccountEsList\",\r\n          method: \"get\",\r\n          params: params,\r\n        };\r\n        this.tbAccount.loading = true;\r\n        axios\r\n          .request(req)\r\n          .then((res) => {\r\n            this.tbAccount.loading = false;\r\n            if (res.data) {\r\n              let array = res.data.rows;\r\n              accountEsTotal(this.accountObj).then((res) => {\r\n                //合计\r\n                let alltotal = res.data;\r\n                alltotal.total = \"合计\";\r\n                alltotal._disabled = true;\r\n                array.push(alltotal);\r\n                this.beforeLoadData(array, \"预付台账导出数据\");\r\n              });\r\n            }\r\n          })\r\n          .catch((err) => {\r\n            console.log(err);\r\n          });\r\n      }\r\n    },\r\n    validate() {\r\n      if (this.columnsIndex != 5) {\r\n        let val = this.enterOperate(this.columnsIndex).data;\r\n        if (val) {\r\n          if (testNumber(val)) {\r\n            switch (this.columnsIndex) {\r\n              case 1:\r\n                this.validateStartdate();\r\n                break;\r\n              case 2:\r\n                this.validateEnddate();\r\n                break;\r\n              case 3:\r\n                this.validatecurtotalreadings();\r\n                break;\r\n              case 4:\r\n                this.validateaccountmoney();\r\n                break;\r\n            }\r\n          } else {\r\n            this.errorTips(\"请输入数字！\");\r\n          }\r\n        }\r\n      }\r\n    },\r\n    validateStartdate() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editStartDate;\r\n      let result = _verify_StartDate(data, val);\r\n      if (result) {\r\n        //失败就弹出提示内容\r\n        this.errorTips(result);\r\n        this.myStyle[this.editIndex].startdate = \"errorStle\";\r\n      } else {\r\n        this.myStyle[this.editIndex].startdate = \"myspan\";\r\n        data.startdate = val;\r\n        data.editType = 1;\r\n      }\r\n    },\r\n    validateEnddate() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editEndDate;\r\n\r\n      let result = _verify_EndDate(data, val, \"大于当前\");\r\n      if (result) {\r\n        //失败就弹出提示内容，并将数据恢复初始化\r\n        this.errorTips(result);\r\n      } else {\r\n        data.enddate = val;\r\n        data.editType = 1;\r\n      }\r\n    },\r\n    validatecurtotalreadings() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editcurtotalreadings;\r\n      val = Math.abs(val);\r\n      let prevtotalreadings = Math.abs(data.prevtotalreadings);\r\n      if (prevtotalreadings > val) {\r\n        this.errorTips(\"止度不能小于起度！\");\r\n        return;\r\n      }\r\n\r\n      data.curtotalreadings = val;\r\n      data.totalusedreadings = val;\r\n      data.curusedreadings = val - prevtotalreadings;\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      if (indexData.version == \"sc\") {\r\n        //验证上期台账是否完成报账\r\n        axios\r\n          .request({\r\n            url: \"/business/accountSC/valOldAcount\",\r\n            method: \"post\",\r\n            params: { pcid: data.pcid, ammeterid: data.ammeterid },\r\n          })\r\n          .then((res) => {\r\n            let msg = \"\";\r\n            if (res.data.msg) msg = res.data.msg;\r\n            // if(data.startdate.endsWith(\"0101\"))\r\n            //     msg +=\"【该起始日期是默认值，请注意修改】\";\r\n            if (msg != \"\")\r\n              this.$Notice.warning({\r\n                title: \"注意\",\r\n                desc: \"电表/协议【\" + data.ammetercode + \"】\" + msg,\r\n                duration: 10,\r\n              });\r\n          });\r\n      }\r\n    },\r\n    validatetransformerullage() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.edittransformerullage;\r\n      data.transformerullage = val;\r\n      data.editType = 1;\r\n    },\r\n    validateaccountmoney() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editaccountmoney;\r\n      data.accountmoney = Math.abs(val);\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      this.validateUnitPrice(data);\r\n    },\r\n    setremark() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editremark;\r\n      data.remark = val;\r\n      data.editType = 1;\r\n    },\r\n    setMyStyle(length) {\r\n      this.myStyle = [];\r\n      for (var i = 0; i < length; i++) {\r\n        this.myStyle.push({\r\n          startdate: \"myspan\",\r\n          enddate: \"myspan\",\r\n          curtotalreadings: \"myspan\",\r\n          accountmoney: \"myspan\",\r\n          remark: \"myspan\",\r\n        });\r\n      }\r\n    },\r\n    //span点击事件将span换成输入框并且获取焦点\r\n    selectCall(row, index, columns, str) {\r\n      this.editStartDate = row.startdate;\r\n      this.editEndDate = row.enddate;\r\n      this.editcurtotalreadings =\r\n        row.curtotalreadings == null || row.curtotalreadings === 0\r\n          ? null\r\n          : row.curtotalreadings;\r\n      this.editaccountmoney =\r\n        row.accountmoney == null || row.accountmoney === 0 ? null : row.accountmoney;\r\n      this.editremark = row.remark;\r\n\r\n      this.editIndex = index;\r\n      this.columnsIndex = columns;\r\n\r\n      let a = this;\r\n      setTimeout(function () {\r\n        if (columns != 8) {\r\n          a.$refs[str + index + columns].focus();\r\n        }\r\n      }, 200);\r\n    },\r\n    //跳转到下一格\r\n    nextCell(data) {\r\n      let index = data.editIndex;\r\n      let columns = data.columnsIndex;\r\n      let row = \"\";\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n      } else if (index > -1 && columns === 5) {\r\n        //当跳转的最后一行最后一格的时候\r\n        if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n          index = 0;\r\n        } else {\r\n          index++;\r\n        }\r\n        columns = 1;\r\n      } else {\r\n        columns += 1;\r\n      }\r\n      data.editIndex = index;\r\n      data.columnsIndex = columns;\r\n      row = data.tbAccount.data[index];\r\n      if (row) {\r\n        data.editStartDate = row.startdate;\r\n        data.editEndDate = row.enddate;\r\n        data.editcurtotalreadings =\r\n          row.curtotalreadings == null || row.curtotalreadings === 0\r\n            ? null\r\n            : row.curtotalreadings;\r\n        data.editaccountmoney =\r\n          row.accountmoney == null || row.accountmoney === 0 ? null : row.accountmoney;\r\n        data.editremark = row.remark;\r\n      }\r\n\r\n      setTimeout(function () {\r\n        data.$refs[data.enterOperate(columns).str + index + columns].focus();\r\n      }, 200);\r\n    },\r\n    //根据列号返回对应的列名\r\n    enterOperate(number) {\r\n      let str = \"\";\r\n      let data = null;\r\n      switch (number) {\r\n        case 1:\r\n          str = \"startdate\";\r\n          data = this.editStartDate;\r\n          break;\r\n        case 2:\r\n          str = \"enddate\";\r\n          data = this.editEndDate;\r\n          break;\r\n        case 3:\r\n          str = \"curtotalreadings\";\r\n          data = this.editcurtotalreadings;\r\n          break;\r\n        case 4:\r\n          str = \"accountmoney\";\r\n          data = this.editaccountmoney;\r\n          break;\r\n        case 5:\r\n          str = \"remark\";\r\n          data = this.editremark;\r\n          break;\r\n      }\r\n      return { str: str, data: data };\r\n    },\r\n    pred() {\r\n      var lett = this;\r\n      let index = lett.editIndex;\r\n      let columns = lett.columnsIndex;\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n        lett.editIndex = index;\r\n        lett.columnsIndex = columns;\r\n        lett.editStartDate = lett.tbAccount.data[index].startdate;\r\n        setTimeout(function () {\r\n          lett.$refs[lett.enterOperate(columns).str + index + columns].focus();\r\n        }, 200);\r\n      } else {\r\n        lett.validate();\r\n        lett.setremark();\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    ellipsis(value) {\r\n      if (!value) return \"\";\r\n      if (value.length > 3) {\r\n        return value.slice(0, 3) + \"...\";\r\n      }\r\n      return value;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.page-class {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  .cl-table {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n  .button-bar {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n.yjjh > .ivu-modal-wrap > .ivu-modal {\r\n  top: 20px !important;\r\n}\r\n.mytable .ivu-table-cell {\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: normal;\r\n  word-break: break-all;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.accountEs .filter-divider {\r\n  margin: 0px;\r\n  text-align: center;\r\n}\r\n.accountEs .header-bar-show {\r\n  max-height: 300px;\r\n  padding-top: 14px;\r\n  overflow: inherit;\r\n  border-bottom: 1px solid #e8eaec;\r\n}\r\n.accountEs .header-bar-hide {\r\n  max-height: 0;\r\n  padding-top: 0;\r\n  overflow: hidden;\r\n  border-bottom: 0;\r\n}\r\n\r\n.mytable .myspan {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n}\r\n.mytable .errorStle {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n  color: red;\r\n}\r\n</style>\r\n"]}]}