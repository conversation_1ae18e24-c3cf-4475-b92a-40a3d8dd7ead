{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\editAmmeter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\editAmmeter.vue", "mtime": 1754285403018}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": 1741317456427}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["editAmmeter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6tDA,SACA,gBADA,EAEA,iBAFA,EAGA,eAHA,EAIA,WAJA,EAKA,iBALA,EAMA,aANA,EAOA,qBAPA,EAQA,qBARA,EASA,iBATA,EAUA,mBAVA,EAWA,WAXA,EAYA,wBAZA,EAaA,qBAAA,IAAA,sBAbA,EAcA,mBAdA,EAeA,iBAfA,EAgBA,kBAhBA,EAiBA,YAAA,IAAA,aAjBA,EAkBA,SAlBA,EAmBA,YAnBA,EAoBA,WApBA,QAqBA,2BArBA;AAsBA,SAAA,YAAA,EAAA,aAAA,QAAA,iCAAA;AACA,SAAA,KAAA,EAAA,KAAA,QAAA,cAAA;AACA,OAAA,kBAAA,MAAA,sBAAA;AACA,OAAA,YAAA,MAAA,gBAAA;AACA,OAAA,YAAA,MAAA,gBAAA;AACA,SAAA,OAAA,QAAA,iBAAA,C,CACA;;AACA,SAAA,YAAA,QAAA,MAAA;AACA,OAAA,OAAA,MAAA,kBAAA;AACA,SAAA,YAAA,QAAA,aAAA;AACA,OAAA,oBAAA,MAAA,mDAAA;AACA,OAAA,mBAAA,MAAA,2CAAA;AACA,OAAA,YAAA,MAAA,iBAAA;AACA,OAAA,WAAA,MAAA,yCAAA;AACA,OAAA,kBAAA,MAAA,4CAAA;AACA,OAAA,UAAA,MAAA,0BAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,eADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,UAAA,EAAA,UADA;AAEA,IAAA,YAAA,EAAA,YAFA;AAGA,IAAA,YAAA,EAAA,YAHA;AAIA,IAAA,YAAA,EAAA,YAJA;AAKA,IAAA,kBAAA,EAAA,kBALA;AAMA,IAAA,oBAAA,EAAA,oBANA;AAOA,IAAA,mBAAA,EAAA,mBAPA;AAQA,IAAA,kBAAA,EAAA,kBARA;AASA,IAAA,WAAA,EAAA;AATA,GAFA;AAaA,EAAA,IAbA,kBAaA;AAAA;;AACA;AACA,QAAA,SAAA,GAAA,SAAA,SAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,EAAA;AACA,YAAA,mBAAA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,UAAA,QAAA,CAAA,IAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AACA,SAFA,MAEA,IAAA,MAAA,CAAA,KAAA,CAAA,CAAA,OAAA,CAAA,IAAA,KAAA,CAAA,EAAA;AACA,UAAA,QAAA,CAAA,IAAA,KAAA,CAAA,aAAA,CAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,QAAA;AACA;AACA;;AACA,MAAA,QAAA;AACA,KAXA;;AAYA,QAAA,eAAA,GAAA,SAAA,eAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KANA;;AAOA,QAAA,mBAAA,GAAA,SAAA,mBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,IAAA,IAAA,IAAA,KAAA,IAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KANA;;AAOA,QAAA,oBAAA,GAAA,SAAA,oBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,IAAA,IAAA,IAAA,KAAA,GAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,aAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KANA;;AAOA,QAAA,uBAAA,GAAA,SAAA,uBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,KAAA,IAAA,SAAA,IAAA,KAAA,IAAA,IAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,YAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA;AACA;;AACA,MAAA,QAAA;AACA,KATA;;AAUA,QAAA,qBAAA,GAAA,SAAA,qBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA,CAAA,OAAA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,aAAA;AACA,UAAA,GAAA,GAAA,IAAA,CAAA,WAAA;;AACA,UAAA,KAAA,IAAA,IAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA;;AACA,UAAA,KAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,EAAA;AACA,YAAA,GAAA,IAAA,KAAA,EAAA;AACA,UAAA,QAAA,CAAA,IAAA,KAAA,CAAA,kBAAA,CAAA,CAAA;AACA;AACA;;AACA,MAAA,QAAA;AACA,KAbA;;AAcA,QAAA,mBAAA,GAAA,SAAA,mBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA,CAAA,OAAA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,aAAA;AACA,UAAA,GAAA,GAAA,IAAA,CAAA,WAAA;;AACA,UAAA,GAAA,IAAA,IAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA;;AACA,UAAA,KAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,EAAA;AACA,YAAA,GAAA,IAAA,KAAA,EAAA;AACA,UAAA,QAAA,CAAA,IAAA,KAAA,CAAA,kBAAA,CAAA,CAAA;AACA;AACA;;AACA,MAAA,QAAA;AACA,KAbA,CA3DA,CAyEA;;;AACA,QAAA,YAAA,GAAA,SAAA,YAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,CAAA,GAAA,CAAA,CACA,MADA,EAEA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,UAAA,EAAA,QADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,QAAA,EAAA,MAHA;AAIA,UAAA,UAAA,EAAA,QAJA;AAKA,UAAA,WAAA,EAAA,KALA;AAMA,UAAA,UAAA,EAAA,CANA;AAOA,UAAA,OAAA,EAAA;AAPA;AADA,OAFA,EAaA,GAbA,CAAA;AAeA,aAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,EAAA,EAAA,EAAA,SAAA,CAAA,CAAA,CAAA;AACA,KAjBA;;AAkBA,WAAA;AACA,MAAA,aAAA,EAAA,IADA;AACA;AACA,MAAA,aAAA,EAAA,KAFA;AAEA;AACA,MAAA,MAAA,EAAA,KAHA;AAIA,MAAA,gBAAA,EAAA,IAJA;AAKA,MAAA,cAAA,EAAA,KALA;AAKA;AACA,MAAA,iBAAA,EAAA,IANA;AAMA;AACA,MAAA,WAAA,EAAA,KAPA;AAOA;AACA,MAAA,YAAA,EAAA,KARA;AASA,MAAA,aAAA,EAAA,IATA;AASA;AACA,MAAA,YAAA,EAAA,EAVA;AAWA,MAAA,gBAAA,EAAA,IAXA;AAaA,MAAA,cAAA,EAAA,EAbA;AAcA,MAAA,SAAA,EAAA,EAdA;AAeA,MAAA,UAAA,EAAA,KAfA;AAgBA,MAAA,YAAA,EAAA,KAhBA;AAiBA,MAAA,QAAA,EAAA,IAjBA;AAmBA,MAAA,OAAA,EAAA,KAnBA;AAmBA;AACA,MAAA,QAAA,EAAA,KApBA;AAoBA;AAEA,MAAA,QAAA,EAAA,IAtBA;AAsBA;AAEA,MAAA,OAAA,EAAA,KAxBA;AAyBA,MAAA,SAAA,EAAA,IAzBA;AA2BA,MAAA,SAAA,EAAA,KA3BA;AA4BA,MAAA,gBAAA,EAAA,KA5BA;AA6BA,MAAA,KAAA,EAAA,EA7BA;AA8BA,MAAA,eAAA,EAAA,KA9BA;AA+BA,MAAA,WAAA,EAAA,KA/BA;AAgCA,MAAA,OAAA,EAAA,KAhCA;AAiCA,MAAA,WAAA,EAAA,IAjCA;AAkCA,MAAA,aAAA,EAAA,IAlCA;AAkCA;AACA,MAAA,iBAAA,EAAA,KAnCA;AAoCA,MAAA,SAAA,EAAA,EApCA;AAqCA,MAAA,WAAA,EAAA,EArCA;AAsCA,MAAA,kBAAA,EAAA,EAtCA;AAsCA;AAEA,MAAA,OAAA,EAAA,EAxCA;AAyCA,MAAA,WAAA,EAAA,EAzCA;AAyCA;AACA,MAAA,cAAA,EAAA,EA1CA;AA0CA;AACA,MAAA,YAAA,EAAA,EA3CA;AA2CA;AACA,MAAA,UAAA,EAAA,EA5CA;AA4CA;AACA,MAAA,gBAAA,EAAA,EA7CA;AA6CA;AACA,MAAA,uBAAA,EAAA,EA9CA;AA8CA;AACA,MAAA,cAAA,EAAA,EA/CA;AA+CA;AACA,MAAA,SAAA,EAAA,EAhDA;AAgDA;AACA,MAAA,WAAA,EAAA,EAjDA;AAiDA;AACA,MAAA,cAAA,EAAA,EAlDA;AAkDA;AACA,MAAA,gBAAA,EAAA,EAnDA;AAmDA;AACA,MAAA,cAAA,EAAA,EApDA;AAoDA;AACA,MAAA,aAAA,EAAA,EArDA;AAqDA;AACA,MAAA,mBAAA,EAAA,EAtDA;AAsDA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,eAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,WAAA,EAAA,CACA;AACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,CAJA;AAQA,QAAA,WAAA,EAAA,CACA;AACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,CARA;AAYA,QAAA,OAAA,EAAA,CACA;AACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,SAAA,EAAA,eAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,CAZA;AAgBA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,SAAA,EAAA,eAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAhBA;AAiBA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAjBA;AAyBA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAzBA;AAiCA,QAAA,UAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAjCA;AAyCA,QAAA,WAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAzCA;AAiDA,QAAA,QAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAjDA;AAyDA,QAAA,oBAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAzDA;AAiEA,QAAA,eAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,SAAA,EAAA,uBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAjEA;AAoEA,QAAA,aAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,OAAA,EAAA,mCADA;AAEA,UAAA,OAAA,EAAA,UAFA;AAGA,UAAA,OAAA,EAAA;AAHA,SAFA,CApEA;AA4EA,QAAA,gBAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CA5EA;AAoFA,QAAA,KAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,mCAHA;AAIA,UAAA,OAAA,EAAA,MAJA;AAKA,UAAA,OAAA,EAAA;AALA,SADA,CApFA;AA6FA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CA7FA;AAgGA,QAAA,eAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAhGA;AAiGA,QAAA,WAAA,EAAA,EAjGA;AAkGA,QAAA,MAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,OAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAlGA;AA0GA,QAAA,SAAA,EAAA,CAAA;AAAA,UAAA,OAAA,EAAA,WAAA;AAAA,UAAA,OAAA,EAAA,OAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CA1GA;AA2GA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,SAAA,EAAA,mBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,OAAA,EAAA,iCADA;AAEA,UAAA,OAAA,EAAA,UAFA;AAGA,UAAA,OAAA,EAAA;AAHA,SAFA,CA3GA;AAmHA,QAAA,aAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,SAAA,EAAA,qBAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAnHA;AA2HA,QAAA,WAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,SAAA,EAAA,mBAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CA3HA;AAmIA,QAAA,GAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,SAAA,EAAA,eAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,OAAA,EAAA,mCADA;AAEA,UAAA,OAAA,EAAA,UAFA;AAGA,UAAA,OAAA,EAAA;AAHA,SAFA,CAnIA;AA2IA,QAAA,uBAAA,EAAA,EA3IA;AA4IA,QAAA,kBAAA,EAAA,EA5IA;AA6IA,QAAA,YAAA,EAAA,EA7IA;AA8IA,QAAA,YAAA,EAAA,EA9IA;AA+IA,QAAA,QAAA,EAAA,EA/IA;AAgJA,QAAA,WAAA,EAAA;AAhJA,OAvDA;AAyMA,MAAA,OAAA,EAAA;AACA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA;AAFA,SADA,EAKA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA;AAFA,SALA,EAUA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,GAAA,EAAA,OAFA;AAGA,UAAA,YAAA,EAAA,YAHA;AAIA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,gBAAA,IAAA,GAAA,KAAA;AACA,gBAAA,KAAA,GAAA,MAAA,CAAA,GAAA,CAAA,KAAA;AACA,gBAAA,QAAA,GAAA,MAAA,CAAA,GAAA,CAAA,QAAA;AACA,gBAAA,KAAA,GAAA,CAAA,CACA,OADA,EAEA;AACA,cAAA,KAAA,EAAA;AACA,gBAAA,KAAA,EAAA,SADA;AAEA,gBAAA,QAAA,EAAA,MAFA;AAGA,gBAAA,UAAA,EAAA,QAHA;AAIA,gBAAA,UAAA,EAAA,KAJA;AAKA,gBAAA,UAAA,EAAA,CALA;AAMA,gBAAA,UAAA,EAAA,MANA;AAOA,gBAAA,OAAA,EAAA,QAAA,KAAA,GAAA,MAAA,GAAA;AAPA;AADA,aAFA,EAaA,MAbA,CAAA;AAeA,gBAAA,MAAA,GAAA,CAAA,CACA,OADA,EAEA;AACA,cAAA,KAAA,EAAA;AACA,gBAAA,KAAA,EAAA,SADA;AAEA,gBAAA,QAAA,EAAA,MAFA;AAGA,gBAAA,UAAA,EAAA,QAHA;AAIA,gBAAA,UAAA,EAAA,KAJA;AAKA,gBAAA,UAAA,EAAA,CALA;AAMA,gBAAA,UAAA,EAAA,MANA;AAOA,gBAAA,OAAA,EAAA,QAAA,IAAA,IAAA,GAAA,cAAA,GAAA;AAPA;AADA,aAFA,EAaA,WAbA,CAAA;AAeA,gBAAA,MAAA,GAAA,CAAA,CAAA,aAAA,EAAA;AACA,cAAA,KAAA,EAAA;AACA,gBAAA,MAAA,EAAA,QAAA,KAAA,IAAA,QAAA,IAAA,IAAA,GAAA,mBAAA,GAAA;AADA,eADA;AAIA,cAAA,KAAA,EAAA;AACA,gBAAA,KAAA,EAAA,KADA;AAEA,gBAAA,GAAA,EAAA,GAFA;AAGA,gBAAA,GAAA,EAAA;AAHA,eAJA;AASA,cAAA,EAAA,EAAA;AACA,6BAAA,kBAAA,CAAA,EAAA;AACA,sBAAA,CAAA,IAAA,SAAA,IAAA,CAAA,IAAA,IAAA,EAAA;AACA,oBAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA,mBAFA,MAEA;AACA,oBAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA,mBALA,CAMA;AACA;;;AACA,sBAAA,GAAA,GAAA,2DAAA;;AACA,sBAAA,CAAA,IAAA,SAAA,IAAA,CAAA,IAAA,IAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA;AACA,oBAAA,MAAA,CAAA,GAAA,CAAA,QAAA,GAAA,IAAA;AACA,oBAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,mBAHA,MAGA;AACA,oBAAA,MAAA,CAAA,GAAA,CAAA,QAAA,GAAA,KAAA;AACA,oBAAA,IAAA,CAAA,QAAA,GAAA,KAAA;AACA;;AACA,kBAAA,MAAA,CAAA,GAAA,CAAA,KAAA,GAAA,CAAA;AACA,kBAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA,CAAA,MAAA,IAAA,MAAA,CAAA,GAAA;AACA;AAnBA;AATA,aAAA,CAAA;AA+BA,mBAAA,CAAA,CAAA,KAAA,EAAA,CAAA,MAAA,EAAA,KAAA,EAAA,MAAA,CAAA,CAAA;AACA;AAtEA,SAVA,EAkFA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,gBAAA,WAAA,GAAA,MAAA,CAAA,GAAA,CAAA,WAAA;AACA,gBAAA,QAAA,GAAA,MAAA,CAAA,GAAA,CAAA,SAAA;;AACA,gBAAA,QAAA,IAAA,SAAA,IAAA,QAAA,IAAA,IAAA,EAAA;AACA,qBAAA,CAAA,CAAA,OAAA,EAAA;AACA,gBAAA,KAAA,EAAA;AACA,kBAAA,KAAA,EAAA,WADA;AAEA,kBAAA,QAAA,EAAA;AAFA;AADA,eAAA,CAAA;AAMA,aAPA,MAOA;AACA,qBAAA,CAAA,CAAA,OAAA,EAAA;AACA,gBAAA,KAAA,EAAA;AACA,kBAAA,KAAA,EAAA,WADA;AAEA,kBAAA,IAAA,EAAA,aAFA;AAGA,kBAAA,WAAA,EAAA,QAHA;AAIA,kBAAA,QAAA,EAAA;AAJA,iBADA;AAOA,gBAAA,EAAA,EAAA;AACA,8BAAA,iBAAA,CAAA,EAAA;AACA,oBAAA,KAAA,CAAA,oBAAA,CAAA,CAAA,EAAA,MAAA,EAAA,MAAA,CAAA,GAAA,CAAA,MAAA;AACA;AAHA;AAPA,eAAA,CAAA;AAaA;AACA;AA5BA,SAlFA,CADA;AAkHA,QAAA,IAAA,EAAA;AAlHA,OAzMA;AA6TA,MAAA,SAAA,EAAA,EA7TA;AA8TA,MAAA,KAAA,EAAA,EA9TA;AA+TA,MAAA,UAAA,EAAA,IA/TA;AAgUA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,UAFA;AAGA,QAAA,YAAA,EAAA,MAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OAhUA;AAsUA,MAAA,sBAAA,EAAA,EAtUA;AAsUA;AACA,MAAA,UAAA,EAAA,EAvUA;AAwUA,MAAA,OAAA,EAAA;AACA,QAAA,EAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,IAFA;AAGA,QAAA,OAAA,EAAA,IAHA;AAIA,QAAA,WAAA,EAAA,EAJA;AAKA,QAAA,aAAA,EAAA,EALA;AAMA,QAAA,OAAA,EAAA,EANA;AAOA,QAAA,eAAA,EAAA,EAPA;AAOA;AACA,QAAA,KAAA,EAAA,CARA;AASA,QAAA,UAAA,EAAA,CATA;AAUA,QAAA,UAAA,EAAA,CAVA;AAWA,QAAA,kBAAA,EAAA,CAXA;AAYA,QAAA,YAAA,EAAA,EAZA;AAaA,QAAA,cAAA,EAAA,IAbA;AAcA,QAAA,cAAA,EAAA;AAdA,OAxUA;AAwVA,MAAA,SAAA,EAAA,KAxVA;AAyVA,MAAA,OAAA,EAAA,KAzVA;AA0VA,MAAA,WAAA,EAAA,IA1VA;AA2VA,MAAA,aAAA,EAAA,KA3VA;AA4VA,MAAA,YAAA,EAAA;AACA,QAAA,OAAA,EAAA,KADA;AAEA,QAAA,MAAA,EAAA,CACA;AACA,UAAA,YAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,KAAA,EAAA,GAJA;AAKA,UAAA,IAAA,EAAA;AALA,SADA,CAFA;AAWA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,OAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SADA,EAOA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,GAAA,EAAA,IAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAPA,EAaA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,UAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAbA,CAXA;AA+BA,QAAA,IAAA,EAAA,EA/BA;AAgCA,QAAA,KAAA,EAAA,CAhCA;AAiCA,QAAA,QAAA,EAAA;AAjCA;AA5VA,KAAA;AAgYA,GAzeA;AA2eA,EAAA,OAAA,oBACA,YAAA,CAAA,CAAA,UAAA,EAAA,gBAAA,CAAA,CADA;AAGA,IAAA,UAHA,wBAGA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,EAAA,CADA,CAEA;;AACA,UAAA,IAAA,GAAA,IAAA,CAAA,WAAA,EAAA,CAHA,CAGA;AACA;;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,QAAA,KAAA,CAAA,CALA,CAMA;;AACA,UAAA,GAAA,GAAA,IAAA,CAAA,OAAA,EAAA,CAPA,CAQA;;AACA,UAAA,IAAA,GAAA,IAAA,CAAA,QAAA,EAAA,CATA,CAUA;;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,UAAA,EAAA,CAXA,CAYA;;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,UAAA,EAAA;AAEA,UAAA,IAAA,GACA,IAAA,GACA,GADA,GAEA,KAAA,OAAA,CAAA,KAAA,CAFA,GAGA,GAHA,GAIA,KAAA,OAAA,CAAA,GAAA,CAJA,GAKA,GALA,GAMA,KAAA,OAAA,CAAA,IAAA,CANA,GAOA,GAPA,GAQA,KAAA,OAAA,CAAA,MAAA,CARA,GASA,GATA,GAUA,KAAA,OAAA,CAAA,MAAA,CAXA;AAYA,aAAA,IAAA;AACA,KA/BA;AAiCA;AACA,IAAA,OAlCA,mBAkCA,CAlCA,EAkCA;AACA,aAAA,CAAA,GAAA,EAAA,GAAA,MAAA,CAAA,GAAA,CAAA;AACA,KApCA;AAqCA,IAAA,sBArCA,kCAqCA,IArCA,EAqCA,IArCA,EAqCA;AACA,WAAA,eAAA,CAAA,IAAA,EADA,CACA;AACA,KAvCA;AAwCA,IAAA,QAxCA,oBAwCA,CAxCA,EAwCA;AAAA;;AACA,WAAA,sBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,KAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,eAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,OANA;AAOA,KAhDA;AAiDA;AACA,IAAA,eAlDA,2BAkDA,IAlDA,EAkDA;AAAA;;AACA,UAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,eAAA,CAAA,MAAA,CAAA,CAAA,EADA,CACA;AACA,OAFA,MAEA;AACA,aAAA,OAAA,CAAA,QAAA,GAAA,IAAA,CAAA,IAAA,EACA,WAAA,CAAA;AAAA,UAAA,KAAA,EAAA,IAAA,CAAA;AAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA;AACA,UAAA,MAAA,CAAA,sBAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,SAHA,CADA;AAKA;AACA,KA5DA;AA6DA;AACA,IAAA,UA9DA,sBA8DA,IA9DA,EA8DA;AAAA;;AACA,UAAA,OAAA,GAAA,KAAA,UAAA,EAAA;AACA,UAAA,MAAA,GAAA;AACA,QAAA,gBAAA,EAAA,KAAA,OAAA,CAAA,kBADA;AAEA,QAAA,YAAA,EAAA;AAFA,OAAA,CAFA,CAMA;;AACA,MAAA,YAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA;AACA,QAAA,MAAA,CAAA,OAAA,CAAA,GAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,IAAA;AACA,OAJA;AAKA,KA1EA;AA4EA,IAAA,EA5EA,cA4EA,IA5EA,EA4EA;AAAA;;AACA,WAAA,gBAAA,GAAA,IAAA;;AACA,UAAA,IAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,GAAA,CAAA;AACA,OAFA,MAEA;AACA,aAAA,SAAA,GAAA,CAAA;AACA;;AACA,UAAA,KAAA,OAAA,IAAA,IAAA,EAAA;AACA;AACA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,aAAA,GAAA,KAAA,OAAA,CAAA,IAAA;AACA,WAAA,KAAA,CAAA,OAAA,CAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,QAAA,CAAA,UAAA,MAAA,EAAA;AACA,gBAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,QAAA,CAAA,UAAA,MAAA,EAAA;AACA,oBAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,OAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA;AACA,oBAAA,MAAA,CAAA,QAAA,CAAA,IAAA;AACA,mBAHA,MAGA;AACA,oBAAA,MAAA,CAAA,SAAA,CAAA,IAAA;AACA;AACA,iBAPA,MAOA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,WAAA;;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;AACA,eAZA;AAaA,aAdA,MAcA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,aAAA;;AACA,cAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;AACA,WAnBA;AAoBA,SArBA,MAqBA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,WAAA;;AACA,UAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;AACA,OA1BA;AA2BA,KAnHA;AAqHA,IAAA,SArHA,qBAqHA,IArHA,EAqHA;AAAA;;AACA,UAAA,UAAA,GAAA,EAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,OAAA;;AACA,UAAA,KAAA,OAAA,CAAA,GAAA,CAAA,SAAA,IAAA,GAAA,EAAA;AACA,QAAA,SAAA,CAAA;AAAA,UAAA,MAAA,EAAA,KAAA,SAAA,CAAA;AAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,UAAA,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,YAAA;;AACA,cAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;;AACA,YAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;AACA;;AACA,UAAA,MAAA,CAAA,EAAA,CAAA,IAAA;AACA,SAXA;AAYA,OAbA,MAaA;AACA,aAAA,EAAA,CAAA,IAAA;AACA;AACA,KAxIA;AAyIA;AACA,IAAA,SA1IA,qBA0IA,IA1IA,EA0IA;AACA,UAAA,KAAA,GAAA,KAAA,OAAA,CAAA,eAAA;AACA,WAAA,OAAA,CAAA,WAAA,GAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UACA,KAAA,OAAA,CAAA,MAAA,KAAA,CAAA,KACA,KAAA,aAAA,IAAA,IAAA,IAAA,KAAA,aAAA,IAAA,IADA,CADA,EAGA;AACA;AACA,YACA,KAAA,OAAA,CAAA,cAAA,IAAA,IAAA,IACA,KAAA,OAAA,CAAA,cAAA,IAAA,SAFA,EAGA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA;;AACA,UAAA,KAAA,gBAAA,EAAA,EAAA;AACA;AACA,YAAA,KAAA,qBAAA,EAAA,EAAA;AACA,cAAA,KAAA,aAAA,IAAA,IAAA,IAAA,KAAA,aAAA,IAAA,IAAA,EAAA;AACA,gBAAA,KAAA,OAAA,CAAA,eAAA,IAAA,CAAA,IAAA,KAAA,OAAA,CAAA,UAAA,GAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,WAAA,CAAA,IAAA;AACA,aAFA,MAEA;AACA,cAAA,iBAAA,CAAA,KAAA,OAAA,CAAA,EAAA,EAAA,KAAA,OAAA,CAAA,WAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CACA,UAAA,GAAA,EAAA;AACA;AACA,oBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,oBAAA,IAAA,IAAA,CAAA,EAAA;AACA,kBAAA,IAAA,CAAA,WAAA,CAAA,IAAA;AACA,iBAFA,MAEA;AACA,kBAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA;AACA,eATA;AAWA;AACA,WAhBA,MAgBA;AACA,YAAA,IAAA,CAAA,WAAA,CAAA,IAAA;AACA;AACA;AACA;AACA,KAvLA;AAwLA,IAAA,gBAxLA,8BAwLA;AACA,UAAA,WAAA,GAAA,KAAA,OAAA,CAAA,WAAA;AACA,UAAA,WAAA,GAAA,KAAA,OAAA,CAAA,WAAA;;AACA,UAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,EAAA;AACA,YAAA,WAAA,KAAA,KAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA,OATA,MASA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,EAAA;AACA,YAAA,WAAA,KAAA,KAAA,IAAA,WAAA,KAAA,KAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA,qBAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA,OATA,MASA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,EAAA;AACA,YAAA,WAAA,KAAA,KAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA,OATA,MASA,IACA,WAAA,KAAA,IAAA,IACA,WAAA,KAAA,IADA,IAEA,WAAA,KAAA,IAFA,IAGA,WAAA,KAAA,IAHA,IAIA,WAAA,KAAA,IAJA,IAKA,WAAA,KAAA,IANA,EAOA;AACA,YAAA,WAAA,KAAA,KAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA;;AACA,UAAA,KAAA,aAAA,IAAA,IAAA,IAAA,KAAA,OAAA,CAAA,kBAAA,EAAA;AACA;AACA,YACA,CAAA,IAAA,EAAA,IAAA,EAAA,QAAA,CAAA,WAAA,KACA,CAAA,KAAA,OAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,IAAA,CAFA,EAGA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA;;AACA,aAAA,IAAA;AACA,KAtPA;AAuPA,IAAA,OAvPA,qBAuPA;AACA;AACA,WAAA,iBAAA,GAAA,IAAA;AACA,WAAA,QAAA,CAAA,KAAA,gBAAA,EAHA,CAGA;AACA,KA3PA;AA4PA,IAAA,WA5PA,yBA4PA;AACA,WAAA,iBAAA,GAAA,IAAA;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,OAAA,EAAA,KAAA;AAAA,OAAA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,KAhQA;AAiQA,IAAA,WAjQA,uBAiQA,IAjQA,EAiQA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,qBAAA,CAAA,IAAA,CAAA,OAAA,CAAA,EAAA,EAAA,IAAA,CAAA,OAAA,CAAA,WAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,YAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,YAAA,IAAA,IAAA,CAAA,EAAA;AACA,cACA,IAAA,CAAA,OAAA,CAAA,WAAA,IAAA,SAAA,IACA,IAAA,CAAA,OAAA,CAAA,WAAA,IAAA,IADA,KAEA,IAAA,CAAA,OAAA,CAAA,WAAA,IAAA,IAAA,IAAA,IAAA,CAAA,OAAA,CAAA,WAAA,IAAA,IAFA,CADA,EAIA;AACA;AACA,gBAAA,IAAA,CAAA,aAAA,IAAA,IAAA,EAAA;AACA;AACA,cAAA,aAAA,CAAA,IAAA,CAAA,OAAA,CAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,gBAAA,IAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA,CAAA,aAAA;;AACA,oBAAA,IAAA,CAAA,aAAA,IAAA,CAAA,EAAA;AACA,kBAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AACA,oBAAA,KAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;;AAIA,kBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,iBANA,MAMA;AACA,kBAAA,IAAA,CAAA,cAAA,CAAA,IAAA;AACA;AACA,eAZA;AAaA,aAfA,MAeA;AACA,kBAAA,IAAA,CAAA,aAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AACA,kBAAA,KAAA,EAAA,MADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AAIA,gBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,eANA,MAMA;AACA,gBAAA,IAAA,CAAA,cAAA,CAAA,IAAA;AACA;AACA;AACA,WAhCA,MAgCA;AACA,YAAA,IAAA,CAAA,cAAA,CAAA,IAAA;AACA;AACA,SApCA,MAoCA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA;AACA,OA1CA;AA2CA,KA9SA;AA+SA,IAAA,cA/SA,0BA+SA,IA/SA,EA+SA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,qBAAA,CAAA;AACA,QAAA,EAAA,EAAA,IAAA,CAAA,OAAA,CAAA,EADA;AAEA,QAAA,IAAA,EAAA,CAFA;AAGA,QAAA,WAAA,EAAA,IAAA,CAAA,OAAA,CAAA,WAHA;AAIA,QAAA,WAAA,EAAA,IAAA,CAAA,OAAA,CAAA,WAJA;AAKA,QAAA,UAAA,EAAA,IAAA,CAAA,OAAA,CAAA;AALA,OAAA,CAAA,CAMA,IANA,CAMA,UAAA,GAAA,EAAA;AACA,YAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,YAAA,IAAA,IAAA,OAAA,EAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA;;AACA,cACA,CAAA,IAAA,CAAA,iBAAA,IAAA,IAAA,IAAA,IAAA,CAAA,iBAAA,IAAA,KAAA,KACA,IAAA,CAAA,OAAA,CAAA,WAAA,IAAA,KADA,IAEA,GAAA,CAAA,IAAA,CAAA,KAHA,EAIA;AACA;AACA,gBAAA,IAAA,CAAA,QAAA,IAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,MAAA,GAAA,IAAA;AACA,aAFA,MAEA;AACA,cAAA,IAAA,CAAA,OAAA;AACA;AACA,WAXA,MAWA;AACA,YAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AAAA,cAAA,KAAA,EAAA,MAAA;AAAA,cAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA;AAAA,aAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA;AACA,SAjBA,MAiBA;AACA,UAAA,IAAA,CAAA,QAAA,CAAA,IAAA,EADA,CACA;AACA;AACA,OA5BA;AA6BA,KA9UA;AA+UA,IAAA,QA/UA,oBA+UA,IA/UA,EA+UA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,YAAA,CAAA,KAAA,OAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CACA,IADA,CACA,UAAA,GAAA,EAAA;AACA;AACA,YAAA,SAAA,GAAA,EAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,SAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,SAAA,GAAA,GAAA;AACA;;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA,WAAA,SAAA,GAAA;AAFA,WAAA;;AAIA,UAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,SATA,MASA;AACA,UAAA,mBAAA,CAAA;AAAA,YAAA,EAAA,EAAA,IAAA,CAAA,OAAA,CAAA;AAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,gBAAA,GAAA,CAAA,IAAA,IAAA,CAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AACA,gBAAA,KAAA,EAAA,MADA;AAEA,gBAAA,OAAA,EAAA;AAFA,eAAA;AAIA,cAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA,aANA,MAMA;AACA,cAAA,IAAA,CAAA,oBAAA;AACA,cAAA,aAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,oBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA;AACA,kBAAA,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA;AACA,oBAAA,KAAA,EAAA,IADA;AAEA,oBAAA,IAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GAFA;AAGA,oBAAA,QAAA,EAAA;AAHA,mBAAA;AAKA,kBAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA;AACA;;AACA,oBAAA,IAAA,IAAA,CAAA,EAAA;AACA,kBAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,OAAA;AACA,iBAFA,MAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,MAAA,CAAA,MAAA;;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AAAA,oBAAA,KAAA,EAAA,MAAA,CAAA;AAAA,mBAAA;;AACA,kBAAA,IAAA,CAAA,IAAA;AACA;AACA,eA3BA,EA4BA,KA5BA,CA4BA,UAAA,GAAA,EAAA;AACA,gBAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,eA/BA;AAgCA;AACA,WA3CA;AA4CA;AACA,OA3DA,EA4DA,KA5DA,CA4DA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,OA/DA;AAgEA,KAjZA;AAkZA,IAAA,kBAlZA,8BAkZA,KAlZA,EAkZA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA;;AACA,UAAA,KAAA,OAAA,CAAA,gBAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,YAAA,CAAA,uBAAA,GAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAAA;AAGA,aAAA,YAAA,CAAA,KAAA,GAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAAA;AAGA,OARA,MAQA;AACA;AACA,aAAA,YAAA,CAAA,uBAAA,GAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAAA;AAGA,aAAA,YAAA,CAAA,KAAA,GAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAAA;AAGA;AACA,KAraA;AAsaA;AACA,IAAA,oBAvaA,kCAuaA;AACA,UAAA,KAAA,OAAA,CAAA,QAAA,KAAA,CAAA,IAAA,KAAA,OAAA,CAAA,QAAA,KAAA,CAAA,EAAA;AACA;AACA,aAAA,OAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,UAAA,KAAA,OAAA,CAAA,UAAA,KAAA,CAAA,EAAA;AACA;AACA,aAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AACA,aAAA,OAAA,CAAA,UAAA,GAAA,IAAA;AACA;;AACA,UAAA,KAAA,OAAA,CAAA,gBAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,OAAA,CAAA,oBAAA,GAAA,IAAA;AACA;;AACA,UAAA,CAAA,KAAA,WAAA,EAAA;AACA;AACA,aAAA,OAAA,CAAA,eAAA,GAAA,IAAA;AACA,aAAA,OAAA,CAAA,OAAA,GAAA,IAAA;AACA,aAAA,OAAA,CAAA,OAAA,GAAA,IAAA;AACA,aAAA,OAAA,CAAA,OAAA,GAAA,IAAA;AACA,aAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AACA;AACA,KA7bA;AA8bA,IAAA,IA9bA,kBA8bA;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,OAAA,EAAA;AAFA,OAAA;AAIA,KAncA;AAocA,IAAA,WApcA,yBAocA;AACA,WAAA,QAAA;AACA,KAtcA;AAucA,IAAA,QAvcA,sBAucA;AAAA;;AACA,WAAA,WAAA,GAAA,EAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,EAAA;AACA,WAAA,SAAA,GAAA,EAAA;AACA,WAAA,UAAA,GAAA,EAAA;AACA,WAAA,KAAA,GAAA,EAAA;AACA,WAAA,OAAA,GAAA,EAAA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,eAAA,GAAA,KAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,OAAA,CAAA,WAAA,GADA,CACA;;;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,QAAA,CAAA,WAAA,GAFA,CAEA;;;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,QAAA,CAAA,WAAA,GAHA,CAGA;;AACA,OAJA;AAKA,WAAA,SAAA,GAAA,KAAA;AACA,WAAA,iBAAA,GAAA,KAAA;AACA,KAxdA;AAydA,IAAA,aAzdA,2BAydA;AACA,WAAA,QAAA;AACA,KA3dA;;AA4dA;AACA,IAAA,WA7dA,uBA6dA,EA7dA,EA6dA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,EAAA,EAAA,iBAAA;AACA,WAAA,QAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,EAAA,IAAA,SAAA,EAAA;AACA,aAAA,KAAA,GAAA,MAAA;AACA,aAAA,eAAA,GAAA,IAAA,CAFA,CAGA;;AACA,QAAA,iBAAA,CAAA;AAAA,UAAA,EAAA,EAAA;AAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,GAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,EAAA,IAAA,SAAA,IAAA,GAAA,CAAA,IAAA,CAAA,EAAA,IAAA,IAAA,EAAA;AACA,gBAAA,QAAA,GAAA,CAAA,IAAA,CAAA,SAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,GAAA,UAAA,CAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA;AACA;;AACA,YAAA,OAAA,CAAA,UAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA;;AACA,YAAA,OAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,WAAA;;AACA,YAAA,OAAA,CAAA,OAAA,CAAA,EAAA,GAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,CAAA,MAAA,GAAA,EAAA;AACA,YAAA,mBAAA,CAAA,OAAA,CAAA,OAAA,CAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,OAAA,CAAA,OAAA,CAAA,eAAA,GAAA,GAAA,CAAA,IAAA;AACA,aAFA;AAGA,YAAA,SAAA,CAAA;AAAA,cAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AAAA,aAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,aAFA;AAGA,WAdA,MAcA;AACA,YAAA,WAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,kBAAA,QAAA,IAAA,CAAA,IAAA,CAAA,SAAA,EAAA;AACA,gBAAA,IAAA,CAAA,IAAA,CAAA,SAAA,GAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA;AACA;;AACA,cAAA,OAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA;;AACA,cAAA,IAAA,CAAA,SAAA,CAAA,MAAA,GAAA,IAAA,CAAA,OAAA,CAAA,EAAA;;AACA,cAAA,OAAA,CAAA,qBAAA,CAAA,EAAA,EAAA,IAAA,EAAA,IAAA,CAAA,IAAA,CAAA,WAAA;;AACA,cAAA,mBAAA,CAAA,OAAA,CAAA,OAAA,CAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,OAAA,CAAA,OAAA,CAAA,eAAA,GAAA,GAAA,CAAA,IAAA;AACA,eAFA;AAGA,cAAA,SAAA,CAAA;AAAA,gBAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AAAA,eAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,IAAA,CAAA,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,eAFA;AAGA,aAbA;AAcA;;AACA,UAAA,OAAA,CAAA,YAAA;AACA,SAjCA;AAkCA,aAAA,OAAA;AACA,OAvCA,MAuCA;AACA,aAAA,KAAA,GAAA,MAAA;AACA,QAAA,WAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,UAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA;;AACA,UAAA,OAAA,CAAA,OAAA;AACA,SAHA;AAIA;;AACA,MAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,OAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAHA;AAIA,KAnhBA;AAohBA,IAAA,qBAphBA,iCAohBA,EAphBA,EAohBA,QAphBA,EAohBA,WAphBA,EAohBA;AAAA;;AACA,MAAA,sBAAA,CAAA;AAAA,QAAA,SAAA,EAAA,EAAA;AAAA,QAAA,eAAA,EAAA;AAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,SAAA,IAAA,IAAA,IAAA,IAAA,CAAA,SAAA,IAAA,SAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA;AACA,YAAA,IAAA,CAAA,WAAA,GAAA,IAAA;AACA,WAHA,MAGA,IAAA,IAAA,CAAA,SAAA,IAAA,WAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA;AACA;AACA,SAPA;AAQA,QAAA,OAAA,CAAA,OAAA,CAAA,IAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,OAVA;AAWA,KAhiBA;AAiiBA,IAAA,YAjiBA,0BAiiBA;AACA,UAAA,KAAA,OAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,aAAA,GAAA,IAAA;AACA,aAAA,YAAA,CAAA,WAAA,GAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAAA;AAGA,OALA,MAKA;AACA,aAAA,aAAA,GAAA,KAAA;AACA,aAAA,YAAA,CAAA,WAAA,GAAA,CACA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAAA;AAGA;AACA,KA7iBA;AA8iBA,IAAA,YA9iBA,0BA8iBA;AAAA;;AACA,UAAA,KAAA,OAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,YAAA,KAAA,OAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,eAAA,WAAA,GAAA,IAAA;AACA,SAFA,MAEA;AACA,eAAA,WAAA,GAAA,KAAA;AACA;;AACA,QAAA,kBAAA,CAAA,KAAA,OAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA;AACA,UAAA,OAAA,CAAA,OAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EAAA;AACA,UAAA,OAAA,CAAA,OAAA,CAAA,WAAA,GAAA,OAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA;AACA,SAJA;AAKA;AACA,KA3jBA;AA4jBA,IAAA,OA5jBA,qBA4jBA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA;;AACA,YACA,GAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,IACA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IADA,IAEA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAHA,EAIA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,QAAA,eAAA,CAAA;AAAA,UAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA;AAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,UAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,SAHA;AAIA,OAfA;AAgBA,KA9kBA;AA+kBA,IAAA,UA/kBA,sBA+kBA,IA/kBA,EA+kBA;AACA,WAAA,WAAA,GAAA,KAAA,CAAA,iBAAA,EAAA,IAAA,CAAA,QAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,WAAA,cAAA,GAAA,KAAA,CACA,aADA,EAEA,IAAA,CAAA,WAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,YAAA,GAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,SAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,WAAA,UAAA,GAAA,KAAA,CAAA,SAAA,EAAA,IAAA,CAAA,OAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,WAAA,gBAAA,GAAA,KAAA,CACA,eADA,EAEA,IAAA,CAAA,aAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,uBAAA,GAAA,KAAA,CACA,sBADA,EAEA,IAAA,CAAA,oBAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,cAAA,GAAA,KAAA,CACA,aADA,EAEA,IAAA,CAAA,WAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,SAAA,GAAA,KAAA,CAAA,QAAA,EAAA,IAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,WAAA,WAAA,GAAA,KAAA,CAAA,UAAA,EAAA,IAAA,CAAA,QAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,WAAA,cAAA,GAAA,KAAA,CACA,aADA,EAEA,IAAA,CAAA,WAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,gBAAA,GAAA,KAAA,CACA,eADA,EAEA,IAAA,CAAA,aAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,cAAA,GAAA,KAAA,CACA,gBADA,EAEA,IAAA,CAAA,WAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,mBAAA,GAAA,KAAA,CACA,kBADA,EAEA,IAAA,CAAA,gBAFA,EAGA,UAHA,EAIA,UAJA,CAAA;AAMA,WAAA,aAAA,GAAA,KAAA,CAAA,YAAA,EAAA,IAAA,CAAA,UAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,WAAA,eAAA,GAAA,KAAA,CAAA;AACA,QAAA,QAAA,EAAA,cADA;AAEA,QAAA,CAAA,EAAA,IAAA,CAAA,YAFA;AAGA,QAAA,UAAA,EAAA,UAHA;AAIA,QAAA,UAAA,EAAA;AAJA,OAAA,CAAA;AAMA,KA5oBA;AA8oBA,IAAA,UA9oBA,sBA8oBA,IA9oBA,EA8oBA;AACA,UAAA,IAAA,CAAA,MAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AACA,aAAA,aAAA,GAAA,IAAA;AACA,aAAA,YAAA,CAAA,WAAA,GAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAAA;AAGA,OANA,MAMA;AACA,aAAA,aAAA,GAAA,KAAA;AACA,aAAA,YAAA,CAAA,WAAA,GAAA,CACA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAAA;AAGA;;AACA,UAAA,IAAA,CAAA,oBAAA,IAAA,CAAA,IAAA,IAAA,CAAA,oBAAA,IAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA;AACA;;AACA,MAAA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAA,cAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,cAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,eAAA,GACA,IAAA,CAAA,eAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA,eAAA,GAAA,EADA;AAEA,MAAA,IAAA,CAAA,iBAAA,GACA,IAAA,CAAA,iBAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,iBAAA,GAAA,EADA;AAEA,MAAA,IAAA,CAAA,eAAA,GACA,IAAA,CAAA,eAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA,eAAA,GAAA,EADA;AAEA,MAAA,IAAA,CAAA,YAAA,GAAA,IAAA,CAAA,YAAA,IAAA,IAAA,GAAA,EAAA,GAAA,IAAA,CAAA,YAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,SAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,IAAA,GAAA,EAAA,KAAA,aAAA,GAAA,IAAA;AACA,WAAA,OAAA,GAAA,IAAA;AACA,UAAA,WAAA,GAAA,KAAA,OAAA,CAAA,WAAA;AACA,WAAA,YAAA,GAAA,WAAA,GAAA,IAAA,GAAA,IAAA,GAAA,KAAA;;AACA,UACA,WAAA,KAAA,GAAA,IACA,WAAA,KAAA,GADA,IAEA,WAAA,KAAA,GAFA,IAGA,WAAA,KAAA,CAJA,EAKA;AACA,aAAA,gBAAA,GAAA,IAAA;AACA;;AACA,UACA,WAAA,IAAA,IAAA,IAAA,WAAA,KAAA,IAAA,IAAA,WAAA,KAAA,IAAA,IACA,KAAA,OAAA,CAAA,QAAA,KAAA,CAFA,EAGA;AACA,aAAA,gBAAA,GAAA,KAAA;AACA;;AACA,UAAA,KAAA,OAAA,CAAA,aAAA,IAAA,IAAA,EAAA;AACA,aAAA,OAAA,CAAA,aAAA,GAAA,CAAA;AACA;;AACA,UAAA,KAAA,OAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,aAAA,OAAA,CAAA,OAAA,GAAA,KAAA,OAAA,CAAA,OAAA,GAAA,EAAA;;AACA,YAAA,KAAA,OAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,eAAA,WAAA,GAAA,IAAA;AACA;AACA;;AACA,UAAA,KAAA,OAAA,CAAA,aAAA,IAAA,IAAA,EAAA;AACA,aAAA,UAAA,GAAA,IAAA;AACA;;AACA,WAAA,QAAA,GAAA,KAAA,OAAA,CAAA,WAAA,CA1DA,CA0DA;;AACA,WAAA,SAAA,GAAA,IAAA;AACA,KA1sBA;AA4sBA;AACA,IAAA,qBA7sBA,iCA6sBA,KA7sBA,EA6sBA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,WAAA,YAAA;;AACA,UAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AACA,aAAA,gBAAA,GAAA,IAAA;AACA,OAJA,MAIA;AACA,aAAA,gBAAA,GAAA,KAAA;AACA,aAAA,OAAA,CAAA,WAAA,GAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,YAAA,WAAA,GAAA,KAAA,OAAA,CAAA,WAAA;AACA,aAAA,YAAA,GAAA,WAAA,GAAA,IAAA,GAAA,IAAA,GAAA,KAAA;;AACA,YAAA,WAAA,KAAA,IAAA,IAAA,WAAA,KAAA,IAAA,EAAA;AACA;AACA,eAAA,OAAA,CAAA,QAAA,GAAA,CAAA;AACA,eAAA,gBAAA,GAAA,IAAA;AACA,eAAA,YAAA,CAAA,uBAAA,GAAA,CACA;AAAA,YAAA,QAAA,EAAA,IAAA;AAAA,YAAA,OAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WADA,CAAA;AAGA,SAPA,MAOA,IAAA,WAAA,KAAA,IAAA,IAAA,WAAA,KAAA,IAAA,EAAA;AACA,eAAA,gBAAA,GAAA,KAAA;AACA,eAAA,OAAA,CAAA,QAAA,GAAA,CAAA;AACA,eAAA,YAAA,CAAA,uBAAA,GAAA,CACA;AAAA,YAAA,QAAA,EAAA,IAAA;AAAA,YAAA,OAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WADA,CAAA;AAGA,SANA,MAMA,IAAA,WAAA,KAAA,IAAA,IAAA,WAAA,KAAA,IAAA,EAAA;AACA,eAAA,gBAAA,GAAA,KAAA;AACA,eAAA,OAAA,CAAA,QAAA,GAAA,CAAA;AACA,eAAA,YAAA,CAAA,uBAAA,GAAA,CACA;AAAA,YAAA,QAAA,EAAA,IAAA;AAAA,YAAA,OAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WADA,CAAA;AAGA,SANA,MAMA;AACA,eAAA,gBAAA,GAAA,KAAA;AACA,eAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AACA,eAAA,YAAA,CAAA,uBAAA,GAAA,CACA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,YAAA,OAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WADA,CAAA;AAGA,SA9BA,CA+BA;AACA;AACA;;;AACA,YAAA,WAAA,GAAA,KAAA,OAAA,CAAA,WAAA;;AACA,YAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,EAAA;AACA,eAAA,gBAAA,GAAA,IAAA;;AACA,cAAA,WAAA,KAAA,KAAA,EAAA;AACA,iBAAA,YAAA;AACA;AACA,SALA,MAKA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,EAAA;AACA,cAAA,WAAA,KAAA,KAAA,IAAA,WAAA,KAAA,KAAA,EAAA;AACA,iBAAA,YAAA;AACA;AACA,SAJA,MAIA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,IAAA,WAAA,KAAA,GAAA,EAAA;AACA,cAAA,WAAA,KAAA,KAAA,EAAA;AACA,iBAAA,YAAA;AACA;AACA,SAJA,MAIA,IAAA,WAAA,KAAA,IAAA,IAAA,WAAA,KAAA,IAAA,EAAA;AACA,cACA,WAAA,KAAA,KAAA,IACA,WAAA,IAAA,KAAA,IAAA,KAAA,aAAA,KAAA,CAFA,EAGA;AACA,iBAAA,YAAA;AACA;AACA,SAPA,MAOA,IACA,WAAA,KAAA,IAAA,IACA,WAAA,KAAA,IADA,IAEA,WAAA,KAAA,IAFA,IAGA,WAAA,KAAA,IAJA,EAKA;AACA,cAAA,WAAA,KAAA,KAAA,EAAA;AACA,iBAAA,YAAA;AACA;AACA,SATA,MASA,IAAA,WAAA,KAAA,CAAA,EAAA;AACA,eAAA,gBAAA,GAAA,IAAA,CADA,CAEA;AACA;AACA;AACA;AACA;AACA,SAvEA,CAwEA;AACA;;;AACA,YAAA,KAAA,aAAA,IAAA,IAAA,IAAA,KAAA,OAAA,CAAA,kBAAA,EAAA;AACA;AACA,cACA,CAAA,IAAA,EAAA,IAAA,EAAA,QAAA,CAAA,WAAA,KACA,CAAA,KAAA,OAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,IAAA,CAFA,EAGA;AACA,iBAAA,YAAA;AACA;AACA;AACA;AACA,KAxyBA;AAyyBA,IAAA,YAzyBA,0BAyyBA;AACA;AACA,WAAA,OAAA,CAAA,WAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,WAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,aAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,WAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,cAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,kBAAA,GAAA,IAAA;AACA,KAjzBA;AAkzBA;AACA,IAAA,oBAnzBA,gCAmzBA,KAnzBA,EAmzBA,MAnzBA,EAmzBA,aAnzBA,EAmzBA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,WAAA,aAAA,GAAA,aAAA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,KAAA,IAAA,CAAA,EAAA;AACA,YAAA,KAAA,GAAA,KAAA,OAAA,CAAA,eAAA;;AACA,YAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WAAA;AACA;AACA,SAHA,MAGA,IAAA,KAAA,OAAA,CAAA,UAAA,IAAA,IAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WAAA;AACA;AACA,SAHA,MAGA;AACA,cAAA,KAAA,OAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,iBAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AACA;AACA;;AACA,eAAA,OAAA,CAAA,WAAA,GAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CALA,CAMA;AACA;AACA;;AACA,eAAA,KAAA,CAAA,YAAA,CAAA,SAAA,GAAA,KAAA,OAAA,CAAA,EAAA;AACA,eAAA,KAAA,CAAA,YAAA,CAAA,YAAA,CACA,KAAA,OAAA,CAAA,WADA,EAEA,CAFA,EAGA,KAAA,OAAA,CAAA,UAHA,EAIA,KAAA,OAAA,CAAA,OAJA,EAKA,MALA,EAVA,CAgBA;AACA;AACA;AACA,OA3BA,MA2BA;AACA,YAAA,KAAA,OAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,eAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AACA;AACA;;AACA,aAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,KAAA,OAAA,CAAA,OAAA,EALA,CAKA;AACA;AACA,KAx1BA;AAy1BA,IAAA,gBAz1BA,4BAy1BA,IAz1BA,EAy1BA,IAz1BA,EAy1BA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,OAAA,CAAA,WAAA,GAAA,IAAA,CAAA,IAAA,CAFA,CAGA;AACA;AACA,KA91BA;AA+1BA;AACA,IAAA,uBAh2BA,mCAg2BA,IAh2BA,EAg2BA,IAh2BA,EAg2BA,QAh2BA,EAg2BA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,iBAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,QAAA;;AACA,UAAA,KAAA,WAAA,IAAA,CAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA,KAAA,aAAA,EAAA,SAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA,KAAA,aAAA,EAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,OAHA,MAGA;AACA,aAAA,aAAA,GAAA,IAAA,CAAA,aAAA;AACA,aAAA,OAAA,CAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,aAAA,OAAA,CAAA,WAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,OAAA,CAAA,aAAA,GAAA,MAAA,CACA,IAAA,CAAA,MAAA,IAAA,SAAA,GAAA,IAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MADA,CAAA;AAGA,aAAA,OAAA,CAAA,WAAA,GAAA,MAAA,CAAA,IAAA,CAAA,WAAA,CAAA;AACA,aAAA,OAAA,CAAA,cAAA,GAAA,IAAA,CAAA,OAAA,CARA,CASA;;AACA,aAAA,OAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,cAAA;AACA,aAAA,OAAA,CAAA,cAAA,GAAA,IAAA,CAAA,cAAA,CAXA,CAYA;;AACA,aAAA,OAAA,CAAA,cAAA,GAAA,IAAA,CAAA,cAAA;AACA,aAAA,OAAA,CAAA,cAAA,GAAA,IAAA,CAAA,gBAAA,CAdA,CAeA;;AACA,YAAA,IAAA,GAAA,IAAA;AACA,QAAA,gBAAA,CAAA;AAAA,UAAA,EAAA,EAAA,IAAA,CAAA;AAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,MAAA,GAAA,IAAA,CAAA,OAAA,CAAA,IAAA;AACA,cAAA,WAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,cAAA,KAAA,GAAA,CAAA;;AACA,cAAA,MAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,KAAA;AACA,WAFA,MAEA;AACA,YAAA,MAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,WAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;AACA,oBAAA,IAAA,CAAA,EAAA,KAAA,KAAA,CAAA,EAAA,EAAA;AACA,kBAAA,WAAA,CAAA,CAAA,CAAA,CAAA,SAAA,GAAA,IAAA,CAAA,EAAA;AACA,kBAAA,WAAA,CAAA,CAAA,CAAA,CAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,kBAAA,WAAA,CAAA,CAAA,CAAA,CAAA,SAAA,GAAA,IAAA;AACA,sBAAA,KAAA,GAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,kBAAA,MAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA;AACA,iBANA,MAMA;AACA,kBAAA,KAAA;AACA;AACA,eAVA;AAWA,aAZA;AAaA;;AACA,cAAA,KAAA,GAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,IAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,EAAA,SAAA,GAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,EAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,EAAA,SAAA,GAAA,IAAA;AACA,WALA,MAKA;AACA,YAAA,MAAA,CAAA,OAAA,CAAA,WAAA,CAAA,CAAA,CAAA;AACA;AACA,SA7BA;AA8BA;AACA,KAv5BA;;AAy5BA;AACA,IAAA,eA15BA,6BA05BA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,gBAAA;AACA,KA55BA;AA85BA,IAAA,aA95BA,yBA85BA,IA95BA,EA85BA;AACA,WAAA,UAAA,GAAA,IAAA,CAAA,IAAA;AACA,WAAA,SAAA,GAAA,IAAA,CAAA,GAAA;AACA,WAAA,UAAA,CAAA,MAAA,GAAA,KAAA,OAAA,CAAA,EAAA;;AACA,UAAA,KAAA,SAAA,CAAA,MAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,IAAA,QAAA,EAAA;AACA,aAAA,YAAA;AACA,OAFA,MAEA;AACA,aAAA,MAAA;AACA;AACA,KAv6BA;AAw6BA,IAAA,YAx6BA,0BAw6BA;AACA,MAAA,aAAA,CAAA;AAAA,QAAA,GAAA,EAAA,KAAA,SAAA,CAAA,IAAA;AAAA,OAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA;AACA,KA16BA;AA26BA,IAAA,MA36BA,oBA26BA;AAAA;;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,IAAA,CAAA,IAAA,KAAA,UAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA;AACA,aAAA,OAAA,GAAA,IAAA;AACA,QAAA,KAAA,CACA,OADA,CACA;AACA,UAAA,GAAA,EAAA,qCADA;AAEA,UAAA,MAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,KAAA;AAHA,SADA,EAMA,IANA,CAMA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,YAAA,OAAA,CAAA,OAAA,GAAA,KAAA;AACA;;AACA,cAAA,IAAA,GAAA,OAAA;AACA,UAAA,SAAA,CAAA;AAAA,YAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,WAFA;AAGA,SAdA;AAeA;AACA,KA/7BA;;AAi8BA;AACA,IAAA,kBAl8BA,gCAk8BA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;AACA,UAAA,KAAA,GAAA,KAAA,OAAA,CAAA,IAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,MAAA,IAAA,SAAA,EAAA;AACA,UAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,EAAA,KAAA,IAAA,CAAA,EAAA,EAAA;AACA,kBAAA,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,cAAA,KAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA;AACA;AACA,WALA;AAMA;AACA,OAXA;AAYA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA;AACA,KAl9BA;;AAo9BA;AACA,IAAA,eAAA,EAAA,yBAAA,IAAA,EAAA;AACA,UAAA,MAAA,GAAA,KAAA,OAAA,CAAA,IAAA;;AACA,UAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,GAAA,IAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,IAAA;;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,MAAA,GACA,MAAA,CAAA,CAAA,CAAA,CAAA,aAAA,IAAA,SAAA,GACA,MAAA,CAAA,CAAA,CAAA,CAAA,aADA,GAEA,MAAA,CAAA,CAAA,CAAA,CAAA,EAHA;;AAIA,gBAAA,IAAA,CAAA,CAAA,CAAA,CAAA,EAAA,KAAA,MAAA,EAAA;AACA,cAAA,GAAA,CAAA,MAAA,CAAA,GAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACA;AACA;AACA;;AACA,aAAA,OAAA,CAAA,IAAA,GAAA,KAAA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA,CAAA;AACA;AACA,KAx+BA;AA0+BA;AACA,IAAA,qBA3+BA,mCA2+BA;AACA,UAAA,KAAA,GAAA,KAAA,OAAA,CAAA,IAAA,CADA,CAEA;;AACA,UACA,KAAA,OAAA,CAAA,WAAA,KAAA,GAAA,IACA,KAAA,OAAA,CAAA,WAAA,KAAA,GADA,IAEA,KAAA,OAAA,CAAA,WAAA,KAAA,GAFA,IAGA,KAAA,OAAA,CAAA,WAAA,KAAA,CAJA,EAKA;AACA,YAAA,QAAA,GAAA,KAAA,CAAA,MAAA,CAAA,UAAA,KAAA,EAAA,IAAA,EAAA;AACA,iBAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,SAFA,EAEA,CAFA,CAAA;;AAGA,YAAA,QAAA,KAAA,GAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,CAAA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA,0BAAA,QAAA,GAAA;AAFA,WAAA;AAIA,eAAA,OAAA,GAAA,KAAA;AACA,iBAAA,KAAA;AACA;AACA;;AACA,aAAA,IAAA;AACA,KAjgCA;AAkgCA,IAAA,QAlgCA,sBAkgCA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,WAAA,SAAA,GAAA;AACA,QAAA,MAAA,EAAA,IAAA,CAAA,OAAA,CAAA,EADA;AAEA,QAAA,QAAA,EAAA,IAAA,CAAA,OAAA,CAAA,SAFA;AAGA,QAAA,UAAA,EAAA,IAAA,CAAA,OAAA,CAAA;AAHA,OAAA;AAKA,KA1gCA;AA2gCA,IAAA,SA3gCA,qBA2gCA,IA3gCA,EA2gCA;AACA,UAAA,SAAA,GAAA,YAAA;AACA,UAAA,SAAA,GAAA,UAAA,KAAA,QAAA,GAAA,KAAA;;AACA,UAAA,IAAA,CAAA,eAAA,IAAA,CAAA,IAAA,IAAA,CAAA,UAAA,GAAA,CAAA,EAAA;AACA,QAAA,SAAA,GAAA,gBAAA;AACA,QAAA,SAAA,GAAA,UAAA,KAAA,QAAA,GAAA,KAAA;AACA;;AACA,WAAA,cAAA,GAAA;AACA,QAAA,MAAA,EAAA,IAAA,CAAA,EADA;AAEA,QAAA,SAAA,EAAA,SAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OAAA;AAKA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,OAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA,KA3hCA;AA4hCA,IAAA,UA5hCA,sBA4hCA,IA5hCA,EA4hCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAA,QAAA,CAAA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,OAAA;;AACA,UAAA,IAAA,IAAA,CAAA,EAAA;AACA,aAAA,IAAA;AACA;AACA,KA3iCA;;AA4iCA;AACA,IAAA,kBA7iCA,gCA6iCA;AACA,WAAA,KAAA,CAAA,qBAAA,CAAA,YAAA,CAAA,CAAA,EAAA,KAAA,OAAA,CAAA,EAAA;AACA,KA/iCA;;AAgjCA;AACA,IAAA,sBAAA,EAAA,gCAAA,IAAA,EAAA;AACA,WAAA,OAAA,CAAA,QAAA,GAAA,IAAA,CAAA,EAAA;;AACA,UAAA,IAAA,CAAA,YAAA,IAAA,IAAA,IAAA,IAAA,CAAA,YAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,OAAA,CAAA,UAAA,GAAA,IAAA,CAAA,YAAA;AACA,OAFA,MAEA;AACA,aAAA,OAAA,CAAA,UAAA,GAAA,IAAA,CAAA,WAAA;AACA;AACA,KAxjCA;;AAyjCA;AACA,IAAA,WA1jCA,yBA0jCA;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,CAAA,EADA,CACA;AACA,KA5jCA;AA6jCA,IAAA,wBAAA,EAAA,kCAAA,IAAA,EAAA;AACA,WAAA,OAAA,CAAA,UAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,OAAA,CAAA,YAAA,GAAA,IAAA,CAAA,IAAA;AACA,KAhkCA;AAikCA;AACA,IAAA,iBAlkCA,+BAkkCA;AACA,UAAA,IAAA,GAAA,KAAA,OAAA;AACA,MAAA,IAAA,CAAA,WAAA,GAAA,IAAA;AACA,KArkCA;AAskCA,IAAA,WAtkCA,yBAskCA;AACA,UAAA,KAAA,OAAA,CAAA,KAAA,IAAA,GAAA,EAAA;AACA,aAAA,OAAA,CAAA,gBAAA,GAAA,CAAA;AACA,aAAA,SAAA,GAAA,IAAA;AACA,OAHA,MAGA;AACA,aAAA,SAAA,GAAA,KAAA;AACA;AACA,KA7kCA;AA8kCA,IAAA,oBA9kCA,kCA8kCA;AACA,UAAA,KAAA,aAAA,EAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,MAAA,GAAA,CAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,UAAA,GAAA,CAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,IAAA,GAAA,CAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,OAAA,GAAA,KAAA,OAAA,CAAA,OAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,OAAA,GAAA,KAAA,OAAA,CAAA,OAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,WAAA,CAAA,gBAAA,GAAA,CAAA;AACA,WAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,IAAA,GAAA,IAAA;AACA,WAAA,QAAA,CAAA,IAAA,CAAA,QAAA;AACA,KAxlCA;AAylCA,IAAA,oBAzlCA,gCAylCA,IAzlCA,EAylCA;AACA,WAAA,OAAA,CAAA,cAAA,GAAA,IAAA,CAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,WAAA,GAAA,IAAA,CAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,EAAA;AACA,KA5lCA;AA6lCA,IAAA,aA7lCA,2BA6lCA;AACA,UAAA,CAAA,KAAA,WAAA,EAAA,KAAA,WAAA,GAAA,KAAA,OAAA,CAAA,cAAA;;AACA,UAAA,KAAA,OAAA,EAAA;AACA,aAAA,OAAA,CAAA,cAAA,GAAA,KAAA,OAAA,CAAA,WAAA,GAAA,GAAA,GAAA,KAAA,OAAA,CAAA,EAAA;AACA,OAFA,MAEA;AACA,YAAA,KAAA,WAAA,IAAA,KAAA,OAAA,CAAA,WAAA,EAAA;AACA,eAAA,OAAA,CAAA,cAAA,GAAA,IAAA;AACA,SAFA,MAEA;AACA,eAAA,OAAA,CAAA,cAAA,GAAA,KAAA,WAAA;AACA;AACA;AACA,KAxmCA;AAymCA,IAAA,iBAzmCA,6BAymCA,GAzmCA,EAymCA;AACA;AACA,UACA,CAAA,6BAAA,IAAA,CAAA,GAAA,CAAA,IACA,CAAA,qCAAA,IAAA,CAAA,GAAA,CADA,IAEA,CAAA,+BAAA,IAAA,CAAA,GAAA,CAHA,EAIA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,2BAAA;AACA;AACA;AAlnCA,IA3eA;AA+lDA,EAAA,OA/lDA,qBA+lDA;AACA;AACA,SAAA,SAAA,GAAA;AACA,MAAA,gBAAA,EAAA,KAAA,CAAA,kBAAA;AADA,KAAA;AAGA,SAAA,YAAA,GAAA,KAAA,CAAA,UAAA,CAAA;AACA,SAAA,WAAA,CAAA,KAAA,MAAA,CAAA,KAAA,CAAA,EAAA;AAEA,SAAA,aAAA,GAAA,KAAA,OAAA,CAAA,OAAA;;AACA,QAAA,KAAA,aAAA,IAAA,IAAA,IAAA,KAAA,aAAA,IAAA,IAAA,EAAA;AACA,WAAA,YAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AACA,QAAA,QAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,MAFA;AAGA,QAAA,OAAA,EAAA;AAHA,OAAA;AAKA,WAAA,YAAA,CAAA,YAAA,GAAA;AACA,QAAA,QAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,MAFA;AAGA,QAAA,OAAA,EAAA;AAHA,OAAA;AAKA,WAAA,YAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,QAAA,QAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,MAFA;AAGA,QAAA,OAAA,EAAA;AAHA,OAAA;AAKA;AACA;AAznDA,CAAA", "sourcesContent": ["<template>\r\n  <div class=\"testaa\">\r\n    <!--重点就是下面的代码了-->\r\n    <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n    <div solt=\"header\">\r\n      <Row>\r\n        <Col span=\"24\" style=\"text-align: right; right: 10px\">\r\n          <Button\r\n            type=\"success\"\r\n            :loading=\"isLoading == 0 ? loading : false\"\r\n            @click=\"onModalOK1(0)\"\r\n            >保存</Button\r\n          >\r\n          <Button\r\n            type=\"primary\"\r\n            :loading=\"isLoading == 1 ? loading : false\"\r\n            @click=\"onModalOK1(1)\"\r\n            >提交</Button\r\n          >\r\n          <Button v-if=\"isShowFlow\" type=\"success\" @click=\"showFlow\">流程图</Button>\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n    <Modal\r\n      v-model=\"modal1\"\r\n      title=\"温馨提示\"\r\n      okText=\"是\"\r\n      cancelText=\"否\"\r\n      @on-ok=\"okModel\"\r\n      @on-cancel=\"cancelModel\"\r\n    >\r\n      <p style=\"margin: 25px 0 25px 40px\">是否新型室分?</p>\r\n    </Modal>\r\n    <cl-wf-btn\r\n      ref=\"clwfbtn\"\r\n      :isStart=\"true\"\r\n      :params=\"workFlowParams\"\r\n      @on-ok=\"doWorkFlow\"\r\n      v-show=\"false\"\r\n    ></cl-wf-btn>\r\n    <!-- 查看流程 -->\r\n    <Modal v-model=\"showWorkFlow\" title=\"电表流程及审批意见跟踪表\" :width=\"800\">\r\n      <WorkFlowInfoComponet\r\n        :wfHisParams=\"hisParams\"\r\n        v-if=\"showWorkFlow\"\r\n      ></WorkFlowInfoComponet>\r\n    </Modal>\r\n    <select-electric-type\r\n      ref=\"selectElectricType\"\r\n      v-on:listenToSetElectricType=\"setElectricData\"\r\n    ></select-electric-type>\r\n    <country-modal\r\n      ref=\"countryModal\"\r\n      v-on:getDataFromModal=\"getDataFromModal\"\r\n    ></country-modal>\r\n    <station-modal\r\n      ref=\"stationModal\"\r\n      v-on:getDataFromStationModal=\"getDataFromStationModal\"\r\n    ></station-modal>\r\n    <ammeter-protocol-list\r\n      ref=\"selectAmmeterProtocol\"\r\n      v-on:listenToSetAmmeterProrocol=\"setAmmeterProrocolData\"\r\n    ></ammeter-protocol-list>\r\n    <customer-list\r\n      ref=\"customerList\"\r\n      v-on:getDataFromCustomerModal=\"getDataFromCustomerModal\"\r\n    ></customer-list>\r\n    <!--        <Modal v-model=\"showModel\" width=\"80%\" :title=\"title\">-->\r\n    <Card class=\"menu-card\">\r\n      <Collapse :value=\"['Panel1', 'Panel2', 'Panel3', 'Panel4', 'Panel5']\">\r\n        <Panel name=\"Panel1\"\r\n          >基本信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"项目名称：\" prop=\"projectname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.projectname\"\r\n                        placeholder=\"**路**号**楼电表\"\r\n                        @on-blur=\"projectNameChange\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.projectname != null &&\r\n                          oldData.projectname != ammeter.projectname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.projectname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag === 1\">\r\n                    <FormItem\r\n                      label=\"供电局电表编号(下户户号)：\"\r\n                      prop=\"supplybureauammetercode\"\r\n                    >\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauammetercode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauammetercode != null &&\r\n                          oldData.supplybureauammetercode !=\r\n                            ammeter.supplybureauammetercode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauammetercode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag === 2\">\r\n                    <FormItem\r\n                      label=\"供电局电表编号(电表编号)：\"\r\n                      prop=\"supplybureauammetercode\"\r\n                    >\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauammetercode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauammetercode != null &&\r\n                          oldData.supplybureauammetercode !=\r\n                            ammeter.supplybureauammetercode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauammetercode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否智能电表：\" prop=\"issmartammeter\">\r\n                      <RadioGroup v-model=\"ammeter.issmartammeter\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.issmartammeter != null &&\r\n                          oldData.issmartammeter != ammeter.issmartammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.issmartammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否实体电表：\" prop=\"isentityammeter\">\r\n                      <RadioGroup v-model=\"ammeter.isentityammeter\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.isentityammeter != null &&\r\n                          oldData.isentityammeter != ammeter.isentityammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.isentityammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\" v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n                    <FormItem label=\"电表编号：\" prop=\"ammetername\">\r\n                      <cl-input\r\n                        readonly\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.ammetername\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetername != null &&\r\n                          oldData.ammetername != ammeter.ammetername\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetername }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"configVersion != 'ln' && configVersion != 'LN'\">\r\n                    <FormItem label=\"电表编号：\" prop=\"ammetername\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.ammetername\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetername != null &&\r\n                          oldData.ammetername != ammeter.ammetername\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetername }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"电表用途：\" prop=\"ammeteruse\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.ammeteruse\"\r\n                        category=\"ammeterUse\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammeteruse != null &&\r\n                          oldData.ammeteruse != ammeter.ammeteruse\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldAmmeteruse }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"关联实际报账电表：\"\r\n                      prop=\"parentCode\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.parentCode\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addAmmeterProtocol\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.parentCode != null &&\r\n                          oldData.parentCode != ammeter.parentCode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.parentCode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"供电局名称：\" prop=\"supplybureauname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauname != null &&\r\n                          oldData.supplybureauname != ammeter.supplybureauname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"电表类型：\" prop=\"ammetertype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.ammetertype\"\r\n                        category=\"ammeterType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetertype != null &&\r\n                          oldData.ammetertype != ammeter.ammetertype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldAmmetertype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"包干类型：\" prop=\"packagetype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.packagetype\"\r\n                        :disabled=\"ammeter.islumpsum == 1 ? false : true\"\r\n                        category=\"packageType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      >\r\n                      </cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.packagetype != null &&\r\n                          oldData.packagetype != ammeter.packagetype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPackagetype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"所属分公司：\" prop=\"company\">\r\n                      <Select\r\n                        v-model=\"ammeter.company\"\r\n                        @on-change=\"selectChange(ammeter.company)\"\r\n                      >\r\n                        <Option\r\n                          v-for=\"item in companies\"\r\n                          :value=\"item.id\"\r\n                          :key=\"item.id\"\r\n                          >{{ item.name }}</Option\r\n                        >\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.company != null && oldData.company != ammeter.company\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.companyName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <!--                                            <FormItem label=\"所属部门\" prop=\"countryName\">-->\r\n                    <!--                                                <Input icon=\"ios-archive\" v-model=\"ammeter.countryName\"-->\r\n                    <!--                                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly/>-->\r\n                    <!--                                            </FormItem>-->\r\n                    <FormItem\r\n                      label=\"所属部门：\"\r\n                      prop=\"countryName\"\r\n                      v-if=\"isAdmin == true\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-if=\"isCityAdmin == true || isEditByCountry == false\"\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseResponseCenter()\"\r\n                        readonly\r\n                      />\r\n                      <Input\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-else-if=\"isEditByCountry == true && isCityAdmin == false\"\r\n                        readonly\r\n                      />\r\n                      <!--                                                <label v-if=\"oldData.countryName != null &&oldData.countryName != ammeter.countryName\" style=\"color: red;\">历史数据：{{oldData.countryName}}</label>-->\r\n                    </FormItem>\r\n                    <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\">\r\n                      <Select v-model=\"ammeter.country\" v-if=\"isEditByCountry == false\">\r\n                        <Option\r\n                          v-for=\"item in departments\"\r\n                          :value=\"item.id\"\r\n                          :key=\"item.id\"\r\n                          >{{ item.name }}</Option\r\n                        >\r\n                      </Select>\r\n                      <Input\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-else-if=\"isEditByCountry == true\"\r\n                        readonly\r\n                      />\r\n                      <!--                                                <label v-if=\"oldData.countryName != null &&oldData.countryName != ammeter.countryName\" style=\"color: red;\">历史数据：{{oldData.countryName}}</label>-->\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"所属分局或支局：\" prop=\"substation\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.substation\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.substation != null &&\r\n                          oldData.substation != ammeter.substation\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.substation }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"详细地址：\" prop=\"address\">\r\n                      <cl-input :maxlength=\"100\" v-model=\"ammeter.address\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.address != null && oldData.address != ammeter.address\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.address }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费名称：\" prop=\"payname\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.payname\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.payname != null && oldData.payname != ammeter.payname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.payname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费类型：\" prop=\"payperiod\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.payperiod\"\r\n                        category=\"payPeriod\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.payperiod != null &&\r\n                          oldData.payperiod != ammeter.payperiod\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPayperiod }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费经办人：\" prop=\"paymanager\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.paymanager\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.paymanager != null &&\r\n                          oldData.paymanager != ammeter.paymanager\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.paymanager }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"付费方式：\" prop=\"paytype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.paytype\"\r\n                        category=\"payType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.paytype != null && oldData.paytype != ammeter.paytype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPaytype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"管理负责人：\" prop=\"ammetermanager\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.ammetermanager\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetermanager != null &&\r\n                          oldData.ammetermanager != ammeter.ammetermanager\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetermanager }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"单价(元)：\" prop=\"price\">\r\n                      <InputNumber\r\n                        :min=\"0\"\r\n                        :maxlength=\"20\"\r\n                        v-model=\"ammeter.price\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.price != null && oldData.price != ammeter.price\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.price }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"用电类型：\" prop=\"classifications\">\r\n                      <Cascader\r\n                        :data=\"classificationData\"\r\n                        :change-on-select=\"true\"\r\n                        v-model=\"ammeter.classifications\"\r\n                        @on-change=\"changeClassifications\"\r\n                      ></Cascader>\r\n                      <label\r\n                        v-if=\"\r\n                          ammeter.classifications !== undefined &&\r\n                          ammeter.classifications.length > 0\r\n                        \"\r\n                      >\r\n                        <label v-for=\"(item, i) in ammeter.classifications\" :key=\"i\">\r\n                          <label\r\n                            v-if=\"\r\n                              i === ammeter.classifications.length - 1 &&\r\n                              oldData.electrotype != null &&\r\n                              oldData.electrotype != item\r\n                            \"\r\n                            style=\"color: red\"\r\n                            >历史数据：{{ oldData.electrotypename }}</label\r\n                          >\r\n                        </label>\r\n                      </label>\r\n                      <label\r\n                        v-else-if=\"\r\n                          ammeter.classifications !== undefined &&\r\n                          ammeter.classifications.length <= 0\r\n                        \"\r\n                      >\r\n                        <label v-if=\"oldData.electrotype != null\" style=\"color: red\"\r\n                          >历史数据：{{ oldData.electrotypename }}</label\r\n                        >\r\n                      </label>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"倍率：\" prop=\"magnification\">\r\n                      <InputNumber\r\n                        :min=\"1\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.magnification\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.magnification != null &&\r\n                          oldData.magnification != ammeter.magnification\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.magnification }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"对外结算类型：\" prop=\"directsupplyflag\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.directsupplyflag\"\r\n                        category=\"directSupplyFlag\"\r\n                        :disabled=\"iszgzOnly\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                        @on-change=\"changedirectsupply\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.directsupplyflag != null &&\r\n                          oldData.directsupplyflag != ammeter.directsupplyflag\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldDirectsupplyflag }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem label=\"电价性质：\" prop=\"electrovalencenature\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.electrovalencenature\"\r\n                        category=\"electrovalenceNature\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.electrovalencenature != null &&\r\n                          oldData.electrovalencenature != ammeter.electrovalencenature\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldElectrovalencenature }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"翻表读数(度)：\" prop=\"maxdegree\">\r\n                      <InputNumber\r\n                        :maxlength=\"20\"\r\n                        v-model=\"ammeter.maxdegree\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.maxdegree != null &&\r\n                          oldData.maxdegree != ammeter.maxdegree\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.maxdegree }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"状态：\" prop=\"status\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.status\"\r\n                        @on-change=\"changeStatus\"\r\n                        category=\"status\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"oldData.status != null && oldData.status != ammeter.status\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStatus }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"占局(站)定额电量比例(%)：\" prop=\"quotapowerratio\">\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :min=\"1\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.quotapowerratio\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.quotapowerratio != null &&\r\n                          oldData.quotapowerratio != ammeter.quotapowerratio\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.quotapowerratio }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"合同对方：\" prop=\"contractOthPart\">\r\n                      <cl-input\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.contractOthPart\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.contractOthPart != null &&\r\n                          oldData.contractOthPart != ammeter.contractOthPart\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.contractOthPart }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-else-if=\"!isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\">\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}</Option\r\n                        >\r\n                      </Select>\r\n                      <!--                                            <cl-select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\"-->\r\n                      <!--                                                       category=\"property\"-->\r\n                      <!--                                                       labelField=\"typeName\" valueField=\"typeCode\"></cl-select>-->\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"ammeter.ischangeammeter == 1 && ammeter.billStatus < 2\">\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\">\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}</Option\r\n                        >\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter == 1\">\r\n                    <FormItem label=\"是否换表：\" prop=\"ischangeammeter\">\r\n                      <RadioGroup v-model=\"ammeter.ischangeammeter\">\r\n                        <Radio label=\"0\" disabled>\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\" disabled>\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ischangeammeter != null &&\r\n                          oldData.ischangeammeter != ammeter.ischangeammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.ischangeammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter == 1\">\r\n                    <FormItem label=\"原电表/协议编号：\" prop=\"oldAmmeterName\">\r\n                      <cl-input v-model=\"ammeter.oldAmmeterName\" readonly></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.oldAmmeterName != null &&\r\n                          oldData.oldAmmeterName != ammeter.oldAmmeterName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.oldAmmeterName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter == 1\">\r\n                    <FormItem label=\"原电表还需报账电量(度)：\" prop=\"oldBillPower\">\r\n                      <cl-input\r\n                        :maxlength=\"20\"\r\n                        v-model=\"ammeter.oldBillPower\"\r\n                        :placeholder=\"\r\n                          [2, 4].includes(ammeter.property) ? '需填写【分摊后电量】' : ''\r\n                        \"\r\n                      ></cl-input>\r\n                      <label style=\"color: red; margin-left: -100px; margin-top: 5px\"\r\n                        >注意：该电量将计入新表的总电量，没有请填0</label\r\n                      >\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.oldBillPower != null &&\r\n                          oldData.oldBillPower != ammeter.oldBillPower\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.oldBillPower }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter != 1\">\r\n                    <FormItem\r\n                      label=\"分割比例(%)：\"\r\n                      prop=\"percent\"\r\n                      v-if=\"ammeter.property === 2 || ammeter.property === 4\"\r\n                    >\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.percent\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.percent != null && oldData.percent != ammeter.percent\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.percent }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter != 1\">\r\n                    <FormItem\r\n                      label=\"收款客户名称：\"\r\n                      prop=\"customerName\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.customerName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addCustomer\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.customerName != null &&\r\n                          oldData.customerName != ammeter.customerName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.customerName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"ammeter.ischangeammeter == 1 && ammeter.billStatus < 2\">\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter == 1\">\r\n                    <FormItem\r\n                      label=\"分割比例(%)：\"\r\n                      prop=\"percent\"\r\n                      v-if=\"ammeter.property === 2 || ammeter.property === 4\"\r\n                    >\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.percent\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.percent != null && oldData.percent != ammeter.percent\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.percent }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.ischangeammeter == 1\">\r\n                    <FormItem\r\n                      label=\"收款客户名称：\"\r\n                      prop=\"customerName\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.customerName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addCustomer\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.customerName != null &&\r\n                          oldData.customerName != ammeter.customerName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.customerName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <!--                                    <Col span=\"6\">-->\r\n                  <!--                                        <FormItem label=\"生产占比(%)：\" prop=\"generationof\">-->\r\n                  <!--                                            <InputNumber :min=\"1\" :maxlength=15 v-model=\"ammeter.generationof\"></InputNumber>-->\r\n                  <!--                                            <label v-if=\"oldData.generationof != null &&oldData.generationof != ammeter.generationof\" style=\"color: red;\">历史数据：{{oldData.generationof}}</label>-->\r\n                  <!--                                        </FormItem>-->\r\n                  <!--                                    </Col>-->\r\n                </Row>\r\n                <Row\r\n                  v-if=\"\r\n                    ammeter.ischangeammeter != 1 ||\r\n                    (ammeter.ischangeammeter == 1 && ammeter.billStatus > 1)\r\n                  \"\r\n                >\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\">\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}</Option\r\n                        >\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"分割比例(%)：\"\r\n                      prop=\"percent\"\r\n                      v-if=\"ammeter.property === 2 || ammeter.property === 4\"\r\n                    >\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.percent\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.percent != null && oldData.percent != ammeter.percent\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.percent }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"收款客户名称：\"\r\n                      prop=\"customerName\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.customerName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addCustomer\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.customerName != null &&\r\n                          oldData.customerName != ammeter.customerName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.customerName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <!--                                    <Col span=\"6\">-->\r\n                  <!--                                        <FormItem label=\"生产占比(%)：\" prop=\"generationof\">-->\r\n                  <!--                                            <InputNumber :min=\"1\" :maxlength=15 v-model=\"ammeter.generationof\"></InputNumber>-->\r\n                  <!--                                            <label v-if=\"oldData.generationof != null &&oldData.generationof != ammeter.generationof\" style=\"color: red;\">历史数据：{{oldData.generationof}}</label>-->\r\n                  <!--                                        </FormItem>-->\r\n                  <!--                                    </Col>-->\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"是否铁塔包干：\"\r\n                      prop=\"islumpsum\"\r\n                      v-if=\"ammeter.property == 2\"\r\n                      class=\"form-line-height\"\r\n                    >\r\n                      <RadioGroup\r\n                        v-model=\"ammeter.islumpsum\"\r\n                        @on-change=\"updatepackagetype\"\r\n                      >\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.islumpsum != null &&\r\n                          oldData.islumpsum != ammeter.islumpsum\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >原是否铁塔按RRU包干：{{\r\n                          oldData.islumpsum == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"月包干电量(度)：\" prop=\"ybgPower\">\r\n                      <cl-input :maxlength=\"20\" v-model=\"ammeter.ybgPower\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ybgPower != null && oldData.ybgPower != ammeter.ybgPower\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ybgPower }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干起始日期：\" prop=\"lumpstartdate\">\r\n                      <cl-date-picker\r\n                        type=\"date\"\r\n                        placeholder=\"包干起始日期\"\r\n                        v-model=\"ammeter.lumpstartdate\"\r\n                        style=\"width: 160px\"\r\n                      ></cl-date-picker>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.lumpstartdate != null &&\r\n                          oldData.lumpstartdate != ammeter.lumpstartdate\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.lumpstartdate }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干截止日期：\" prop=\"lumpenddate\">\r\n                      <cl-date-picker\r\n                        type=\"date\"\r\n                        placeholder=\"包干截止日期\"\r\n                        v-model=\"ammeter.lumpenddate\"\r\n                        style=\"width: 160px\"\r\n                      ></cl-date-picker>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.lumpenddate != null &&\r\n                          oldData.lumpenddate != ammeter.lumpenddate\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.lumpenddate }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"ammeter.packagetype != null && ammeter.islumpsum == 1\"\r\n                  >\r\n                    <FormItem label=\"月包干电费：\" prop=\"fee\">\r\n                      <InputNumber\r\n                        :min=\"0\"\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.fee\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.fee != null && oldData.fee != ammeter.fee\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.fee }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc' || configVersion == 'SC'\">\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"输配电公司：\"\r\n                      prop=\"transdistricompany\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: true,\r\n                          type: 'number',\r\n                          message: '请选择',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <cl-select\r\n                        v-model=\"ammeter.transdistricompany\"\r\n                        category=\"transdistricompany\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.transdistricompany != null &&\r\n                          oldData.transdistricompany != ammeter.transdistricompany\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldtransdistricompany }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.directsupplyflag == 1 && ammeter.transdistricompany == 1\r\n                    \"\r\n                  >\r\n                    <FormItem\r\n                      label=\"电压等级：\"\r\n                      prop=\"voltageClass\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: true,\r\n                          type: 'number',\r\n                          message: '请选择',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <cl-select\r\n                        v-model=\"ammeter.voltageClass\"\r\n                        category=\"voltageClass\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.voltageClass != null &&\r\n                          oldData.voltageClass != ammeter.voltageClass\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldvoltageClass }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc'\">\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"是否转改直：\"\r\n                      prop=\"iszgz\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.iszgz\" @on-change=\"iszgzchange\">\r\n                        <Radio label=\"0\" :disabled=\"disablediszgz\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"原转供电表编号：\"\r\n                      prop=\"oldammetername\"\r\n                      v-if=\"ammeter.iszgz == '1'\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: ammeter.iszgz == '1',\r\n                          message: '不能为空',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        :value=\"\r\n                          ammeter.oldammetername\r\n                            ? ammeter.oldammetername.split(',')[0]\r\n                            : null\r\n                        \"\r\n                        readonly\r\n                        :disabled=\"disablediszgz\"\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseoldammetername\"\r\n                      />\r\n                    </FormItem>\r\n                    <ChooseAmmeterModel\r\n                      ref=\"chooseAmmeterModel\"\r\n                      v-if=\"ammeter.iszgz == '1'\"\r\n                      v-on:getAmmeterModelModal=\"getAmmeterModelModal\"\r\n                    />\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"原转供电表为当前电表：\" v-if=\"ammeter.iszgz == '1'\">\r\n                      <i-switch\r\n                        v-model=\"iszgzme\"\r\n                        @on-change=\"iszgzmechange\"\r\n                        :disabled=\"disablediszgz\"\r\n                      />\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"是否直购电：\"\r\n                      prop=\"directFlag\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.directFlag\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"是否含办公：\"\r\n                      prop=\"officeFlag\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.officeFlag\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel2\"\r\n          >关联局站信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter1\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"局(站)名称：\"\r\n                      prop=\"stationName\"\r\n                      :class=\"{ requireStar: isRequireFlag }\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.stationName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseResponseCenter(1)\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationName != null &&\r\n                          oldData.stationName != ammeter.stationName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)编码：\" prop=\"stationcode\">\r\n                      <cl-input readonly v-model=\"ammeter.stationcode\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationcode != null &&\r\n                          oldData.stationcode != ammeter.stationcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)状态：\" prop=\"stationstatus\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.stationstatus\"\r\n                        filterable\r\n                        category=\"stationStatus\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationstatus != null &&\r\n                          oldData.stationstatus != ammeter.stationstatus\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStationstatus }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)类型：\" prop=\"stationtype\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.stationtype\"\r\n                        filterable\r\n                        category=\"BUR_STAND_TYPE\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationtype != null &&\r\n                          oldData.stationtype != ammeter.stationtype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStationtype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)地址：\" prop=\"stationaddress\">\r\n                      <cl-input readonly v-model=\"ammeter.stationaddress\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationaddress != null &&\r\n                          oldData.stationaddress != ammeter.stationaddress\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationaddress }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"资源局站id\" prop=\"resstationcode\">\r\n                      <cl-input\r\n                        readonly\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.resstationcode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.resstationcode != null &&\r\n                          oldData.resstationcode != ammeter.resstationcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.resstationcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否有空调：\" prop=\"isairconditioning\">\r\n                      <RadioGroup v-model=\"ammeter.isairconditioning\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.isairconditioning != null &&\r\n                          oldData.isairconditioning != ammeter.isairconditioning\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.isairconditioning == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"核定电量：\" prop=\"vouchelectricity\">\r\n                      <InputNumber\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.vouchelectricity\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.vouchelectricity != null &&\r\n                          oldData.vouchelectricity != ammeter.vouchelectricity\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.vouchelectricity }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"isCDCompany\">\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管C网编号：\" prop=\"nmCcode\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmCcode\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmCcode != null && oldData.nmCcode != ammeter.nmCcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmCcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号L2.1G：\" prop=\"nmL2100\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmL2100\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmL2100 != null && oldData.nmL2100 != ammeter.nmL2100\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmL2100 }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号L1.8G：\" prop=\"nmL1800\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmL1800\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmL1800 != null && oldData.nmL1800 != ammeter.nmL1800\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmL1800 }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号C+L800M：\" prop=\"nmCl800m\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmCl800m\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmCl800m != null && oldData.nmCl800m != ammeter.nmCl800m\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmCl800m }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc' && isMobileBase\">\r\n                  <Col span=\"12\">\r\n                    <FormItem label=\"5GR站址编码：\" prop=\"stationcode5gr\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.stationcode5gr\"\r\n                        readonly\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationcode5gr != null &&\r\n                          oldData.stationcode5gr != ammeter.stationcode5gr\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationcode5gr }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"12\">\r\n                    <FormItem label=\"5GR站址名称：\" prop=\"stationname5gr\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.stationname5gr\"\r\n                        readonly\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationname5gr != null &&\r\n                          oldData.stationname5gr != ammeter.stationname5gr\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationname5gr }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel3\"\r\n          >关联用电类型比例\r\n          <!--                        <span style=\"font-size: 10px;color:red\">（用电类型为：A类机楼、B类机楼、C类机楼、管理办公用电时必须关联用电类型比例）</span>-->\r\n          <!-- <div slot=\"content\" v-if=\"isClassification && ammeter.stationcode!= null\">-->\r\n          <div slot=\"content\" v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n            <cl-table\r\n              ref=\"ammeterTable\"\r\n              strip\r\n              :columns=\"electro.columns\"\r\n              :data=\"electro.data\"\r\n              :searchable=\"false\"\r\n              :showPage=\"false\"\r\n              selectEnabled\r\n              selectMultiple\r\n            >\r\n              <div slot=\"buttons\">\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"md-add\"\r\n                  type=\"primary\"\r\n                  @click=\"addElectricType\"\r\n                  >新增\r\n                </Button>\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"ios-trash-outline\"\r\n                  type=\"warning\"\r\n                  @click=\"removeElectricType\"\r\n                  >删除\r\n                </Button>\r\n              </div>\r\n            </cl-table>\r\n          </div>\r\n          <div slot=\"content\" v-else-if=\"isClassification && ammeter.stationcode != null\">\r\n            <cl-table\r\n              ref=\"ammeterTable\"\r\n              strip\r\n              :columns=\"electro.columns\"\r\n              :data=\"electro.data\"\r\n              :searchable=\"false\"\r\n              :showPage=\"false\"\r\n              selectEnabled\r\n              selectMultiple\r\n            >\r\n              <div slot=\"buttons\">\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"md-add\"\r\n                  type=\"primary\"\r\n                  @click=\"addElectricType\"\r\n                  >新增\r\n                </Button>\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"ios-trash-outline\"\r\n                  type=\"warning\"\r\n                  @click=\"removeElectricType\"\r\n                  >删除\r\n                </Button>\r\n              </div>\r\n            </cl-table>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel4\"\r\n          >业主信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter2\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"联系人：\" prop=\"contractname\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.contractname\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.contractname != null &&\r\n                          oldData.contractname != ammeter.contractname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.contractname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"具体位置：\" prop=\"location\">\r\n                      <cl-input :maxlength=\"100\" v-model=\"ammeter.location\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.location != null && oldData.location != ammeter.location\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.location }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"办公电话：\" prop=\"officephone\">\r\n                      <cl-input :maxlength=\"15\" v-model=\"ammeter.officephone\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.officephone != null &&\r\n                          oldData.officephone != ammeter.officephone\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.officephone }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"移动电话：\" prop=\"telephone\">\r\n                      <cl-input :maxlength=\"15\" v-model=\"ammeter.telephone\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.telephone != null &&\r\n                          oldData.telephone != ammeter.telephone\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.telephone }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"对方单位：\" prop=\"userunit\">\r\n                      <cl-input\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.userunit\"\r\n                      ></cl-input>\r\n                      <ChooseModal\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        ref=\"chooseModalSup2\"\r\n                        v-on:getDataFromModal=\"getDataFromModalObject\"\r\n                      />\r\n                      <Input\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        :maxlength=\"50\"\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.userunit\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"handleChooseSup()\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.userunit != null && oldData.userunit != ammeter.userunit\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.userunit }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款账户：\" prop=\"receiptaccountname\">\r\n                      <Select\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        v-model=\"ammeter.receiptaccountname\"\r\n                        style=\"width: 100%\"\r\n                        @on-change=\"onChange\"\r\n                      >\r\n                        <Option\r\n                          v-for=\"item in receiptaccountnameList\"\r\n                          :value=\"item.koinh\"\r\n                          :label=\"item.koinh\"\r\n                          :key=\"item.iid\"\r\n                        ></Option>\r\n                      </Select>\r\n                      <cl-input\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.receiptaccountname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccountname != null &&\r\n                          oldData.receiptaccountname != ammeter.receiptaccountname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccountname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款开户支行：\" prop=\"receiptaccountbank\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        readonly\r\n                        v-model=\"ammeter.receiptaccountbank\"\r\n                      ></cl-input>\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        v-model=\"ammeter.receiptaccountbank\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccountbank != null &&\r\n                          oldData.receiptaccountbank != ammeter.receiptaccountbank\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccountbank }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款账号：\" prop=\"receiptaccounts\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-show=\"this.configVersion == 'sc'\"\r\n                        readonly\r\n                        v-model=\"ammeter.receiptaccounts\"\r\n                      ></cl-input>\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-show=\"this.configVersion == 'ln'\"\r\n                        v-model=\"ammeter.receiptaccounts\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccounts != null &&\r\n                          oldData.receiptaccounts != ammeter.receiptaccounts\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccounts }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"24\">\r\n                    <FormItem label=\"说明：\" prop=\"memo\">\r\n                      <cl-input\r\n                        type=\"textarea\"\r\n                        :rows=\"3\"\r\n                        v-model=\"ammeter.memo\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"oldData.memo != null && oldData.memo != ammeter.memo\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.memo }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel5\"\r\n          >附件信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <attach-file\r\n                :param=\"fileParam\"\r\n                :attachData=\"attachData\"\r\n                v-on:setAttachData=\"setAttachData\"\r\n              />\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n      </Collapse>\r\n    </Card>\r\n    <!--            <div slot=\"footer\">-->\r\n    <!--                <Button type=\"text\" size=\"large\" @click=\"onModalCancel\">取消</Button>-->\r\n    <!--                <Button type=\"primary\" size=\"large\" @click=\"onModalOK\">保存</Button>-->\r\n    <!--            </div>-->\r\n    <!--        </Modal>-->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listElectricType,\r\n  checkAmmeterExist,\r\n  getCountrysdata,\r\n  editAmmeter,\r\n  editAmmeterRecord,\r\n  updateAmmeter,\r\n  checkProjectNameExist,\r\n  checkAmmeterByStation,\r\n  getClassification,\r\n  getClassificationId,\r\n  getUserdata,\r\n  checkClassificationLevel,\r\n  listElectricTypeRatio,\r\n  checkAcountByUpdate,\r\n  getUserByUserRole,\r\n  getCountryByUserId,\r\n  removeAttach,\r\n  attchList,\r\n  checkStation,\r\n  getBankCard,\r\n} from \"@/api/basedata/ammeter.js\";\r\nimport { isInTodoList, getstationold } from \"@/api/alertcontrol/alertcontrol\";\r\nimport { blist, btext } from \"@/libs/tools\";\r\nimport SelectElectricType from \"./selectElectricType\";\r\nimport countryModal from \"./countryModal\";\r\nimport stationModal from \"./stationModal\";\r\nimport { isEmpty } from \"@/libs/validate\";\r\n// import stationLNModal from \"./stationModalLN\";\r\nimport { mapMutations } from \"vuex\";\r\nimport routers from \"@/router/routers\";\r\nimport { getHomeRoute } from \"@/libs/util\";\r\nimport WorkFlowInfoComponet from \"@/view/basic/system/workflow/workFlowInfoComponet\";\r\nimport AmmeterProtocolList from \"@/view/basedata/quota/listAmmeterProtocol\";\r\nimport customerList from \"./customerModal\";\r\nimport ChooseModal from \"@/view/business/gasBusiness/chooseModal\";\r\nimport ChooseAmmeterModel from \"@/view/basedata/ammeter/chooseAmmeterModel\";\r\nimport attachFile from \"./../protocol/attachFile\";\r\nimport axios from \"@/libs/api.request\";\r\n\r\nexport default {\r\n  name: \"updateAmmeter\",\r\n  components: {\r\n    attachFile,\r\n    stationModal,\r\n    customerList,\r\n    countryModal,\r\n    SelectElectricType,\r\n    WorkFlowInfoComponet,\r\n    AmmeterProtocolList,\r\n    ChooseAmmeterModel,\r\n    ChooseModal,\r\n  },\r\n  data() {\r\n    //不能输入汉字\r\n    const checkData = (rule, value, callback) => {\r\n      if (value) {\r\n        if (/[\\u4E00-\\u9FA5]/g.test(value)) {\r\n          callback(new Error(\"编码不能输入汉字!\"));\r\n        } else if (escape(value).indexOf(\"%u\") >= 0) {\r\n          callback(new Error(\"编码不能输入中文字符!\"));\r\n        } else {\r\n          callback();\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatorNumber = (rule, value, callback) => {\r\n      if (value.length <= 0) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatorNumberZero = (rule, value, callback) => {\r\n      if (value != null && value == 0) {\r\n        callback(new Error(\"只能输入大于0的数\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatorNumberZero1 = (rule, value, callback) => {\r\n      if (value != null && value < 0) {\r\n        callback(new Error(\"只能输入大于等于0的数\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validateClassifications = (rule, value, callback) => {\r\n      if (value == undefined || value == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        if (value.length <= 0) {\r\n          callback(new Error(\"不能为空\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatelumpstartdate = (rule, value, callback) => {\r\n      let data = this.ammeter;\r\n      let start = data.lumpstartdate;\r\n      let end = data.lumpenddate;\r\n      if (start == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      }\r\n      if (start != null && end != null) {\r\n        if (end <= start) {\r\n          callback(new Error(\"包干起始日期不能大于等于截止日期\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatelumpenddate = (rule, value, callback) => {\r\n      let data = this.ammeter;\r\n      let start = data.lumpstartdate;\r\n      let end = data.lumpenddate;\r\n      if (end == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      }\r\n      if (start != null && end != null) {\r\n        if (end <= start) {\r\n          callback(new Error(\"包干截止日期不能小于等于起始日期\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    //更改标题名称及样式\r\n    let renderHeader = (h, params) => {\r\n      let t = h(\r\n        \"span\",\r\n        {\r\n          style: {\r\n            fontWeight: \"normal\",\r\n            color: \"#ed4014\",\r\n            fontSize: \"12px\",\r\n            fontFamily: \"SimSun\",\r\n            marginRight: \"4px\",\r\n            lineHeight: 1,\r\n            display: \"inline-block\",\r\n          },\r\n        },\r\n        \"*\"\r\n      );\r\n      return h(\"div\", [t, h(\"span\", {}, \"所占比例(%)\")]);\r\n    };\r\n    return {\r\n      propertyright: null, //局站产权\r\n      isRequireFlag: false, //局站是否必填\r\n      modal1: false,\r\n      checkStationType: null,\r\n      ischeckStation: false, //是否需要验证局站只能关联5个\r\n      isoldcheckStation: null, //判断用户关联局站没有,默认没有\r\n      isCDCompany: false, //是否是成都分公司\r\n      isMobileBase: false,\r\n      configVersion: null, //版本\r\n      propertyList: [],\r\n      propertyReadonly: true,\r\n\r\n      workFlowParams: {},\r\n      hisParams: {},\r\n      isShowFlow: false,\r\n      showWorkFlow: false,\r\n      flowName: null,\r\n\r\n      isError: false, //用电类型比例验证\r\n      isError1: false, //用电类型比例验证\r\n\r\n      ismodal1: null, //是否已经提示过是否新型室分\r\n\r\n      loading: false,\r\n      isLoading: null,\r\n\r\n      showModel: false,\r\n      isClassification: false,\r\n      title: \"\",\r\n      isEditByCountry: false,\r\n      isCityAdmin: false,\r\n      isAdmin: false,\r\n      chooseIndex: null,\r\n      electroRowNum: null, //关联用电类型的当前行\r\n      electricTypeModel: false,\r\n      companies: [],\r\n      departments: [],\r\n      classificationData: [], //用电类型\r\n\r\n      oldData: [],\r\n      oldCategory: \"\", //原始数据\r\n      oldPackagetype: \"\", //原始数据\r\n      oldPayperiod: \"\", //原始数据\r\n      oldPaytype: \"\", //原始数据\r\n      oldElectronature: \"\", //原始数据\r\n      oldElectrovalencenature: \"\", //原始数据\r\n      oldElectrotype: \"\", //原始数据\r\n      oldStatus: \"\", //原始数据\r\n      oldProperty: \"\", //原始数据\r\n      oldAmmetertype: \"\", //原始数据\r\n      oldStationstatus: \"\", //原始数据\r\n      oldStationtype: \"\", //原始数据\r\n      oldAmmeteruse: \"\", //原始数据\r\n      oldDirectsupplyflag: \"\", //原始数据\r\n      ruleValidate: {\r\n        isentityammeter: [\r\n          { required: true, message: \"不能为空\", trigger: \"change,blur\" },\r\n        ],\r\n        projectname: [\r\n          //项目名称\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        countryName: [\r\n          //所属部门\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        country: [\r\n          //所属部门\r\n          { required: true, type: \"number\", validator: validatorNumber, trigger: \"blur\" },\r\n        ],\r\n        company: [{ required: true, validator: validatorNumber, trigger: \"blur\" }],\r\n        paytype: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        payperiod: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        ammeteruse: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        ammetertype: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        property: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        electrovalencenature: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        classifications: [\r\n          { required: true, validator: validateClassifications, trigger: \"change,blur\" },\r\n        ],\r\n        magnification: [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        directsupplyflag: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        packagetype: [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contractOthPart: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\r\n        stationName: [],\r\n        status: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        telephone: [{ pattern: /^1\\d{10}$/, message: \"格式不正确\", trigger: \"blur\" }],\r\n        percent: [\r\n          { type: \"number\", validator: validatorNumberZero, trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([0-9]\\d{0,12}))(\\.\\d{0,4})?$/,\r\n            message: \"只能保留四位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lumpstartdate: [\r\n          {\r\n            required: true,\r\n            type: \"date\",\r\n            validator: validatelumpstartdate,\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lumpenddate: [\r\n          {\r\n            required: true,\r\n            type: \"date\",\r\n            validator: validatelumpenddate,\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        fee: [\r\n          { required: true, type: \"number\", validator: validatorNumber, trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        supplybureauammetercode: [],\r\n        transdistricompany: [],\r\n        voltageClass: [],\r\n        customerName: [],\r\n        userunit: [],\r\n        ammetername: [],\r\n      },\r\n      electro: {\r\n        columns: [\r\n          {\r\n            title: \"序号\",\r\n            type: \"index\",\r\n          },\r\n          {\r\n            title: \"用电类型\",\r\n            key: \"typeName\",\r\n          },\r\n\r\n          {\r\n            title: \"所占比例(%)\",\r\n            key: \"ratio\",\r\n            renderHeader: renderHeader,\r\n            render: (h, params) => {\r\n              let that = this;\r\n              let ratio = params.row.ratio;\r\n              let isError1 = params.row.idError1;\r\n              let error = h(\r\n                \"label\",\r\n                {\r\n                  style: {\r\n                    color: \"#ed4014\",\r\n                    fontSize: \"12px\",\r\n                    fontFamily: \"SimSun\",\r\n                    paddingTop: \"6px\",\r\n                    lineHeight: 1,\r\n                    fontWeight: \"bold\",\r\n                    display: null != ratio ? \"none\" : \"inline-block\",\r\n                  },\r\n                },\r\n                \"不能为空\"\r\n              );\r\n              let error1 = h(\r\n                \"label\",\r\n                {\r\n                  style: {\r\n                    color: \"#ed4014\",\r\n                    fontSize: \"12px\",\r\n                    fontFamily: \"SimSun\",\r\n                    paddingTop: \"6px\",\r\n                    lineHeight: 1,\r\n                    fontWeight: \"bold\",\r\n                    display: isError1 == true ? \"inline-block\" : \"none\",\r\n                  },\r\n                },\r\n                \"输入比例不合格要求\"\r\n              );\r\n              let result = h(\"InputNumber\", {\r\n                style: {\r\n                  border: null == ratio || isError1 == true ? \"1px solid #ed4014\" : \"\",\r\n                },\r\n                props: {\r\n                  value: ratio,\r\n                  max: 100,\r\n                  min: 0.1,\r\n                },\r\n                on: {\r\n                  \"on-change\": (v) => {\r\n                    if (v == undefined || v == null) {\r\n                      that.isError = true;\r\n                    } else {\r\n                      that.isError = false;\r\n                    }\r\n                    //给data重新赋值\r\n                    // let reg = /^(?:[1-9]?\\d|100)$/;\r\n                    let reg = /^-?(([1-9][0-9]*)|(([0]\\.\\d{1,2}|[1-9][0-9]*\\.\\d{1,2})))$/;\r\n                    if (v != undefined && v != null && !reg.test(v)) {\r\n                      params.row.idError1 = true;\r\n                      that.isError1 = true;\r\n                    } else {\r\n                      params.row.idError1 = false;\r\n                      that.isError1 = false;\r\n                    }\r\n                    params.row.ratio = v;\r\n                    that.electro.data[params.row._index] = params.row;\r\n                  },\r\n                },\r\n              });\r\n              return h(\"div\", [result, error, error1]);\r\n            },\r\n          },\r\n          {\r\n            title: \"关联局站\",\r\n            key: \"stationName\",\r\n            render: (h, params) => {\r\n              let stationName = params.row.stationName;\r\n              let disabled = params.row._disabled;\r\n              if (disabled != undefined && disabled == true) {\r\n                return h(\"Input\", {\r\n                  props: {\r\n                    value: stationName,\r\n                    readonly: true,\r\n                  },\r\n                });\r\n              } else {\r\n                return h(\"Input\", {\r\n                  props: {\r\n                    value: stationName,\r\n                    icon: \"ios-archive\",\r\n                    placeholder: \"点击图标选择\",\r\n                    readonly: true,\r\n                  },\r\n                  on: {\r\n                    \"on-click\": (v) => {\r\n                      this.chooseResponseCenter(2, params, params.row._index);\r\n                    },\r\n                  },\r\n                });\r\n              }\r\n            },\r\n          },\r\n        ],\r\n        data: [],\r\n      },\r\n      removeIds: [],\r\n      files: [],\r\n      multiFiles: null,\r\n      fileParam: {\r\n        busiId: \"\",\r\n        busiAlias: \"附件(协议管理)\",\r\n        categoryCode: \"file\",\r\n        areaCode: \"ln\",\r\n      },\r\n      receiptaccountnameList: [], //银行卡列表\r\n      attachData: [],\r\n      ammeter: {\r\n        id: null,\r\n        country: null,\r\n        company: null,\r\n        countryName: \"\",\r\n        electricTypes: [],\r\n        electro: [],\r\n        classifications: [], //用电类型\r\n        iszgz: 0,\r\n        directFlag: 0,\r\n        officeFlag: 0,\r\n        transdistricompany: 1,\r\n        voltageClass: \"\",\r\n        stationcode5gr: null,\r\n        stationname5gr: null,\r\n      },\r\n      iszgzOnly: false,\r\n      iszgzme: false,\r\n      iszgzmename: null,\r\n      disablediszgz: false,\r\n      electricType: {\r\n        loading: false,\r\n        filter: [\r\n          {\r\n            formItemType: \"input\",\r\n            prop: \"name\",\r\n            label: \"用电类型\",\r\n            width: 100,\r\n            size: \"small\",\r\n          },\r\n        ],\r\n        columns: [\r\n          {\r\n            title: \"序号\",\r\n            type: \"index\",\r\n            align: \"center\",\r\n            width: 70,\r\n          },\r\n          {\r\n            title: \"id\",\r\n            key: \"id\",\r\n            align: \"center\",\r\n            width: 80,\r\n          },\r\n          {\r\n            title: \"用电类型\",\r\n            key: \"typeName\",\r\n            align: \"center\",\r\n            width: 80,\r\n          },\r\n        ],\r\n        data: [],\r\n        total: 0,\r\n        pageSize: 10,\r\n      },\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    ...mapMutations([\"closeTag\", \"closeTagByName\"]),\r\n\r\n    getNowTime() {\r\n      var date = new Date();\r\n      //年 getFullYear()：四位数字返回年份\r\n      var year = date.getFullYear(); //getFullYear()代替getYear()\r\n      //月 getMonth()：0 ~ 11\r\n      var month = date.getMonth() + 1;\r\n      //日 getDate()：(1 ~ 31)\r\n      var day = date.getDate();\r\n      //时 getHours()：(0 ~ 23)\r\n      var hour = date.getHours();\r\n      //分 getMinutes()： (0 ~ 59)\r\n      var minute = date.getMinutes();\r\n      //秒 getSeconds()：(0 ~ 59)\r\n      var second = date.getSeconds();\r\n\r\n      var time =\r\n        year +\r\n        \"-\" +\r\n        this.addZero(month) +\r\n        \"-\" +\r\n        this.addZero(day) +\r\n        \" \" +\r\n        this.addZero(hour) +\r\n        \":\" +\r\n        this.addZero(minute) +\r\n        \":\" +\r\n        this.addZero(second);\r\n      return time;\r\n    },\r\n\r\n    //小于10的拼接上0字符串\r\n    addZero(s) {\r\n      return s < 10 ? \"0\" + s : s;\r\n    },\r\n    getDataFromModalObject(data, flag) {\r\n      this.handleChooseSup(data); // 传 true 设置 回调值\r\n    },\r\n    onChange(v) {\r\n      this.receiptaccountnameList.forEach((item) => {\r\n        if (item.koinh === v) {\r\n          console.log(v);\r\n          this.ammeter.receiptaccountbank = item.banka;\r\n          this.ammeter.receiptaccounts = item.bankn;\r\n        }\r\n      });\r\n    },\r\n    //选择对方单位\r\n    handleChooseSup(data) {\r\n      if (!data) {\r\n        this.$refs.chooseModalSup2.choose(1); //打开模态框\r\n      } else {\r\n        (this.ammeter.userunit = data.name),\r\n          getBankCard({ lifnr: data.id }).then((res) => {\r\n            console.log(res.data);\r\n            this.receiptaccountnameList = res.data.rows;\r\n          });\r\n      }\r\n    },\r\n    //*****校验当前局站是是否过期\r\n    onModalOK1(type) {\r\n      let nowTime = this.getNowTime();\r\n      let params = {\r\n        stationaddr_code: this.ammeter.stationaddresscode,\r\n        serveenddate: nowTime,\r\n      };\r\n      //校验局站过期时间\r\n      checkStation(params).then((res) => {\r\n        console.log(res.data);\r\n        this.ammeter.map = res.data;\r\n        this.onModalOK(type);\r\n      });\r\n    },\r\n\r\n    OK(type) {\r\n      this.checkStationType = type;\r\n      if (type == 1) {\r\n        this.isLoading = 1;\r\n      } else {\r\n        this.isLoading = 0;\r\n      }\r\n      if (this.loading == true) {\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      this.ammeter.electricTypes = this.electro.data;\r\n      this.$refs.ammeter.validate((valid) => {\r\n        if (valid) {\r\n          this.$refs.ammeter1.validate((valid1) => {\r\n            if (valid1) {\r\n              this.$refs.ammeter2.validate((valid2) => {\r\n                if (valid2) {\r\n                  if (this.ammeter.status == 0) {\r\n                    //停用电表协议不验证 局站、用电类型\r\n                    this.saveData(type);\r\n                  } else {\r\n                    this.checkData(type);\r\n                  }\r\n                } else {\r\n                  this.$Message.error(\"业主信息验证没通过\");\r\n                  this.loading = false;\r\n                }\r\n              });\r\n            } else {\r\n              this.$Message.error(\"关联局站信息验证没通过\");\r\n              this.loading = false;\r\n            }\r\n          });\r\n        } else {\r\n          this.$Message.error(\"基本信息验证没通过\");\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n\r\n    onModalOK(type) {\r\n      let attachData = [];\r\n      console.log(this.ammeter);\r\n      if (this.ammeter.map.iftimeout == \"3\") {\r\n        attchList({ busiId: this.fileParam.busiId }).then((res) => {\r\n          this.loading = false;\r\n          console.log(res);\r\n          attachData = Object.assign([], res.data.rows);\r\n          console.log(attachData, \"attachData\");\r\n          if (attachData.length < 1) {\r\n            this.$Message.error(\"当前选择局站已过期,必须上传附件\");\r\n            this.loading = false;\r\n            return;\r\n          }\r\n          this.OK(type);\r\n        });\r\n      } else {\r\n        this.OK(type);\r\n      }\r\n    },\r\n    //验证数据\r\n    checkData(type) {\r\n      let types = this.ammeter.classifications;\r\n      this.ammeter.electrotype = types[types.length - 1];\r\n      let that = this;\r\n      if (\r\n        this.ammeter.status === 1 &&\r\n        (this.configVersion == \"sc\" || this.configVersion == \"SC\")\r\n      ) {\r\n        //在用状态下验证局站地址不能为空\r\n        if (\r\n          this.ammeter.stationaddress == null ||\r\n          this.ammeter.stationaddress == undefined\r\n        ) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"局站地址不能为空，请在局站管理维护该局站信息！\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      if (this.checkStationElec()) {\r\n        //验证用电类型和局站类型是否匹配\r\n        if (this.checkElectricTypeItem()) {\r\n          if (this.configVersion != \"ln\" && this.configVersion != \"LN\") {\r\n            if (this.ammeter.ischangeammeter == 1 && this.ammeter.billStatus < 2) {\r\n              that.checkedDate(type);\r\n            } else {\r\n              checkAmmeterExist(this.ammeter.id, this.ammeter.ammetername, 0).then(\r\n                (res) => {\r\n                  //验证电表是否存在\r\n                  let code = res.data.code;\r\n                  if (code == 0) {\r\n                    that.checkedDate(type);\r\n                  } else {\r\n                    that.loading = false;\r\n                  }\r\n                }\r\n              );\r\n            }\r\n          } else {\r\n            that.checkedDate(type);\r\n          }\r\n        }\r\n      }\r\n    },\r\n    checkStationElec() {\r\n      let electrotype = this.ammeter.electrotype;\r\n      let stationtype = this.ammeter.stationtype;\r\n      if (electrotype === 111 || electrotype === 112 || electrotype === 113) {\r\n        if (stationtype !== 10001) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      } else if (electrotype === 121 || electrotype === 112) {\r\n        if (stationtype !== 10003 && stationtype !== 10004) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\" + stationtype,\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      } else if (electrotype === 131 || electrotype === 132 || electrotype === 133) {\r\n        if (stationtype !== 10005) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      } else if (\r\n        electrotype === 1411 ||\r\n        electrotype === 1412 ||\r\n        electrotype === 1421 ||\r\n        electrotype === 1422 ||\r\n        electrotype === 1431 ||\r\n        electrotype === 1432\r\n      ) {\r\n        if (stationtype !== 10002) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      if (this.configVersion == \"sc\" && this.ammeter.stationaddresscode) {\r\n        //“51”开头铁塔站址编码控制\r\n        if (\r\n          [1411, 1412].includes(electrotype) &&\r\n          !this.ammeter.stationaddresscode.startsWith(\"51\")\r\n        ) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站站址编码不匹配(51开头为铁塔站址编码)，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    okModel() {\r\n      //不验证个数\r\n      this.isoldcheckStation = null;\r\n      this.saveData(this.checkStationType); //保存数据\r\n    },\r\n    cancelModel() {\r\n      this.isoldcheckStation = null;\r\n      this.$Modal.warning({ title: \"温馨提示\", content: this.errorMessage });\r\n      this.loading = false;\r\n    },\r\n    checkedDate(type) {\r\n      let that = this;\r\n      checkProjectNameExist(that.ammeter.id, that.ammeter.projectname, 0).then((res) => {\r\n        //验证项目名称是否存在\r\n        let code = res.data.code;\r\n        if (code == 0) {\r\n          if (\r\n            that.ammeter.stationcode != undefined &&\r\n            that.ammeter.stationcode != null &&\r\n            (that.ammeter.electrotype == 1411 || that.ammeter.electrotype == 1412)\r\n          ) {\r\n            //判断是否铁塔\r\n            if (that.propertyright == null) {\r\n              //判断是否铁塔\r\n              getstationold(that.ammeter.stationcode).then((res) => {\r\n                //验证项目名称是否存在\r\n                that.propertyright = res.data.propertyright;\r\n                if (that.propertyright != 3) {\r\n                  this.$Modal.warning({\r\n                    title: \"温馨提示\",\r\n                    content: \"用电类型和局站类型或产权不匹配，请确认\",\r\n                  });\r\n                  this.loading = false;\r\n                } else {\r\n                  that.isCheckStation(type);\r\n                }\r\n              });\r\n            } else {\r\n              if (that.propertyright != 3) {\r\n                this.$Modal.warning({\r\n                  title: \"温馨提示\",\r\n                  content: \"用电类型和局站类型或产权不匹配，请确认\",\r\n                });\r\n                this.loading = false;\r\n              } else {\r\n                that.isCheckStation(type);\r\n              }\r\n            }\r\n          } else {\r\n            that.isCheckStation(type);\r\n          }\r\n        } else {\r\n          that.loading = false;\r\n        }\r\n      });\r\n    },\r\n    isCheckStation(type) {\r\n      let that = this;\r\n      checkAmmeterByStation({\r\n        id: that.ammeter.id,\r\n        type: 0,\r\n        electrotype: that.ammeter.electrotype,\r\n        stationcode: that.ammeter.stationcode,\r\n        ammeteruse: that.ammeter.ammeteruse,\r\n      }).then((res) => {\r\n        let code = res.data.code;\r\n        if (code == \"error\") {\r\n          this.errorMessage = res.data.msg;\r\n          if (\r\n            (that.isoldcheckStation == null || that.isoldcheckStation == false) &&\r\n            that.ammeter.stationtype == 10002 &&\r\n            res.data.flag5\r\n          ) {\r\n            //编辑数据时判断是否选择关联局站，没有关联弹出是否室分\r\n            if (that.ismodal1 == null) {\r\n              that.modal1 = true;\r\n            } else {\r\n              that.okModel();\r\n            }\r\n          } else {\r\n            that.$Modal.warning({ title: \"温馨提示\", content: res.data.msg });\r\n            that.loading = false;\r\n          }\r\n        } else {\r\n          that.saveData(type); //保存数据\r\n        }\r\n      });\r\n    },\r\n    saveData(type) {\r\n      let that = this;\r\n      isInTodoList(this.ammeter.id, 1)\r\n        .then((res) => {\r\n          //存在于代办中时，报出提示\r\n          let ownername = \"\";\r\n          if (res.data.length > 0) {\r\n            for (let i = 0; i < res.data.length; i++) {\r\n              ownername += res.data[i].ownername + \" \";\r\n            }\r\n            this.$Modal.warning({\r\n              title: \"温馨提示\",\r\n              content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可修改数据\",\r\n            });\r\n            this.loading = false;\r\n          } else {\r\n            checkAcountByUpdate({ id: that.ammeter.id }).then((res) => {\r\n              //修改数据前验证台账\r\n              if (res.data == -1) {\r\n                that.$Modal.warning({\r\n                  title: \"温馨提示\",\r\n                  content: \"该数据已填写台账或正在报账中，处理后才可修改数据\",\r\n                });\r\n                that.loading = false;\r\n              } else {\r\n                that.clearDataByCondition();\r\n                updateAmmeter(that.ammeter)\r\n                  .then((res) => {\r\n                    if (res.data.code != 0 && res.data.msg) {\r\n                      that.$Notice.error({\r\n                        title: \"提示\",\r\n                        desc: res.data.msg,\r\n                        duration: 10,\r\n                      });\r\n                      that.loading = false;\r\n                      return;\r\n                    }\r\n                    if (type == 1) {\r\n                      that.startFlow(that.ammeter);\r\n                    } else {\r\n                      // this.closeTagByName({// 关闭已经打开的 ，避免冲突\r\n                      //     route: getHomeRoute(routers, \"ammeter\"),\r\n                      // });\r\n                      // //跳转至修改页面 并关闭当前页\r\n                      // this.closeTag({\r\n                      //     route: this.$route, next: {\r\n                      //         name: \"ammeter\", query: {}\r\n                      //     }\r\n                      // });\r\n                      console.log(\"this.$route\", this.$route);\r\n                      this.closeTag({ route: this.$route });\r\n                      that.warn();\r\n                    }\r\n                  })\r\n                  .catch((err) => {\r\n                    that.loading = false;\r\n                    console.log(err);\r\n                  });\r\n              }\r\n            });\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n          this.loading = false;\r\n        });\r\n    },\r\n    changedirectsupply(value) {\r\n      console.log(value);\r\n      if (this.ammeter.directsupplyflag == 1) {\r\n        //this.ammeter.property = null;\r\n        this.ruleValidate.supplybureauammetercode = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n        this.ruleValidate.price = [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      } else {\r\n        //this.ammeter.property = null;\r\n        this.ruleValidate.supplybureauammetercode = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n        this.ruleValidate.price = [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n    },\r\n    //根据条件判断数据是否该清除\r\n    clearDataByCondition() {\r\n      if (this.ammeter.property !== 2 && this.ammeter.property !== 4) {\r\n        //站址产权归属为铁塔 清除分割比例checkAmmeterByStation，是否铁塔按RRU包干\r\n        this.ammeter.percent = null;\r\n      }\r\n      if (this.ammeter.ammeteruse !== 3) {\r\n        //电表用途不是回收电费，清除父电表信息\r\n        this.ammeter.parentId = null;\r\n        this.ammeter.customerId = null;\r\n      }\r\n      if (this.ammeter.directsupplyflag != 1) {\r\n        //只有对外结算类型为直供电才填写该字段，转供电不需填写\r\n        this.ammeter.electrovalencenature = null;\r\n      }\r\n      if (!this.isCDCompany) {\r\n        //成都分公司显示合同对方等，不是，就清除数据\r\n        this.ammeter.contractOthPart = null;\r\n        this.ammeter.nmCcode = null;\r\n        this.ammeter.nmL2100 = null;\r\n        this.ammeter.nmL1800 = null;\r\n        this.ammeter.nmCl800m = null;\r\n      }\r\n    },\r\n    warn() {\r\n      this.$Modal.warning({\r\n        title: \"温馨提示\",\r\n        content: \"保存后的数据要提交审批才能生效！\",\r\n      });\r\n    },\r\n    refreshData() {\r\n      this.initData();\r\n    },\r\n    initData() {\r\n      this.countryName = \"\";\r\n      this.electro.data = [];\r\n      this.removeIds = [];\r\n      this.multiFiles = [];\r\n      this.files = [];\r\n      this.oldData = [];\r\n      this.isCityAdmin = false;\r\n      this.isAdmin = false;\r\n      this.isEditByCountry = false;\r\n      this.$nextTick(() => {\r\n        this.$refs.ammeter.resetFields(); // this.$refs.adduserform.resetFields();\r\n        this.$refs.ammeter1.resetFields(); // this.$refs.adduserform.resetFields();\r\n        this.$refs.ammeter2.resetFields(); // this.$refs.adduserform.resetFields();\r\n      });\r\n      this.showModel = false;\r\n      this.electricTypeModel = false;\r\n    },\r\n    onModalCancel() {\r\n      this.initData();\r\n    },\r\n    /*初始化*/\r\n    initAmmeter(id) {\r\n      console.log(id, \"initAmmeter(id)\");\r\n      this.initData();\r\n      let that = this;\r\n      if (id != undefined) {\r\n        this.title = \"修改电表\";\r\n        this.isEditByCountry = true;\r\n        //获取上一次修改历史\r\n        editAmmeterRecord({ id: id }).then((res) => {\r\n          console.log(\"获取数据---\", res);\r\n          if (res.data.id != undefined && res.data.id != null) {\r\n            if (null != res.data.maxdegree) {\r\n              res.data.maxdegree = parseFloat(res.data.maxdegree);\r\n            }\r\n            this.setAmmeter(Object.assign({}, res.data));\r\n            this.listElectricTypeRatio(id, res.data.id, res.data.stationcode);\r\n            this.ammeter.id = id;\r\n            that.fileParam.busiId = id;\r\n            getClassificationId(this.ammeter.electrotype).then((res) => {\r\n              this.ammeter.classifications = res.data;\r\n            });\r\n            attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n              that.attachData = Object.assign([], res.data.rows);\r\n            });\r\n          } else {\r\n            editAmmeter(id).then((res1) => {\r\n              if (null != res1.data.maxdegree) {\r\n                res1.data.maxdegree = parseFloat(res1.data.maxdegree);\r\n              }\r\n              this.setAmmeter(res1.data);\r\n              that.fileParam.busiId = that.ammeter.id;\r\n              this.listElectricTypeRatio(id, null, res1.data.stationcode);\r\n              getClassificationId(this.ammeter.electrotype).then((res) => {\r\n                this.ammeter.classifications = res.data;\r\n              });\r\n              attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n                that.attachData = Object.assign([], res.data.rows);\r\n              });\r\n            });\r\n          }\r\n          this.$forceUpdate();\r\n        });\r\n        this.getUser();\r\n      } else {\r\n        this.title = \"添加电表\";\r\n        editAmmeter(\"\", 0).then((res) => {\r\n          this.setAmmeter(Object.assign({}, res.data));\r\n          this.getUser();\r\n        });\r\n      }\r\n      getClassification().then((res) => {\r\n        //用电类型\r\n        this.classificationData = res.data;\r\n      });\r\n    },\r\n    listElectricTypeRatio(id, recordId, stationcode) {\r\n      listElectricTypeRatio({ ammeterId: id, ammeterRecordId: recordId }).then((res) => {\r\n        res.data.rows.forEach((item) => {\r\n          if (item.stationId == null || item.stationId == undefined) {\r\n            item.stationId = null;\r\n            item.stationName = null;\r\n          } else if (item.stationId == stationcode) {\r\n            item._disabled = true;\r\n          }\r\n        });\r\n        this.electro.data = Object.assign([], res.data.rows);\r\n      });\r\n    },\r\n    changeStatus() {\r\n      if (this.ammeter.status == 1) {\r\n        this.isRequireFlag = true;\r\n        this.ruleValidate.stationName = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      } else {\r\n        this.isRequireFlag = false;\r\n        this.ruleValidate.stationName = [\r\n          { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n    },\r\n    selectChange() {\r\n      if (this.ammeter.company != undefined) {\r\n        if (this.ammeter.company == \"1000085\") {\r\n          this.isCDCompany = true;\r\n        } else {\r\n          this.isCDCompany = false;\r\n        }\r\n        getCountryByUserId(this.ammeter.company).then((res) => {\r\n          this.departments = res.data.departments;\r\n          this.ammeter.country = res.data.departments[0].id;\r\n          this.ammeter.countryName = this.departments[0].name;\r\n        });\r\n      }\r\n    },\r\n    getUser() {\r\n      let that = this;\r\n      getUserByUserRole().then((res) => {\r\n        //当前登录用户所在公司\r\n        that.companies = res.data.companies;\r\n        that.isCityAdmin = res.data.isEditAdmin;\r\n        if (\r\n          res.data.isCityAdmin == true ||\r\n          res.data.isProAdmin == true ||\r\n          res.data.isSubAdmin == true\r\n        ) {\r\n          that.isAdmin = true;\r\n        }\r\n        getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {\r\n          //根据权限获取所属部门\r\n          that.departments = res.data;\r\n        });\r\n      });\r\n    },\r\n    setOldData(data) {\r\n      this.oldCategory = btext(\"ammeterCategory\", data.category, \"typeCode\", \"typeName\");\r\n      this.oldPackagetype = btext(\r\n        \"packageType\",\r\n        data.packagetype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldPayperiod = btext(\"payPeriod\", data.payperiod, \"typeCode\", \"typeName\");\r\n      this.oldPaytype = btext(\"payType\", data.paytype, \"typeCode\", \"typeName\");\r\n      this.oldElectronature = btext(\r\n        \"electroNature\",\r\n        data.electronature,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldElectrovalencenature = btext(\r\n        \"electrovalenceNature\",\r\n        data.electrovalencenature,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldElectrotype = btext(\r\n        \"electroType\",\r\n        data.electrotype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldStatus = btext(\"status\", data.status, \"typeCode\", \"typeName\");\r\n      this.oldProperty = btext(\"property\", data.property, \"typeCode\", \"typeName\");\r\n      this.oldAmmetertype = btext(\r\n        \"ammeterType\",\r\n        data.ammetertype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldStationstatus = btext(\r\n        \"stationStatus\",\r\n        data.stationstatus,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldStationtype = btext(\r\n        \"BUR_STAND_TYPE\",\r\n        data.stationtype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldDirectsupplyflag = btext(\r\n        \"directSupplyFlag\",\r\n        data.directsupplyflag,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldAmmeteruse = btext(\"ammeterUse\", data.ammeteruse, \"typeCode\", \"typeName\");\r\n      this.oldvoltageClass = btext({\r\n        category: \"voltageClass\",\r\n        v: data.voltageClass,\r\n        valueField: \"typeCode\",\r\n        labelField: \"typeName\",\r\n      });\r\n    },\r\n\r\n    setAmmeter(form) {\r\n      if (form.status == null || form.status === 1) {\r\n        form.status = 1;\r\n        this.isRequireFlag = true;\r\n        this.ruleValidate.stationName = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      } else {\r\n        this.isRequireFlag = false;\r\n        this.ruleValidate.stationName = [\r\n          { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n      if (form.electrovalencenature != 1 && form.electrovalencenature != 2) {\r\n        form.electrovalencenature = null;\r\n      }\r\n      form.issmartammeter = form.issmartammeter == null ? \"0\" : form.issmartammeter + \"\";\r\n      form.isentityammeter =\r\n        form.isentityammeter == null ? null : form.isentityammeter + \"\";\r\n      form.isairconditioning =\r\n        form.isairconditioning == null ? \"0\" : form.isairconditioning + \"\";\r\n      form.ischangeammeter =\r\n        form.ischangeammeter == null ? null : form.ischangeammeter + \"\";\r\n      form.oldBillPower = form.oldBillPower == null ? \"\" : form.oldBillPower + \"\";\r\n      form.islumpsum = form.islumpsum == null ? \"0\" : form.islumpsum + \"\";\r\n      form.iszgz = form.iszgz == null ? \"0\" : form.iszgz + \"\";\r\n      form.directFlag = form.directFlag == null ? \"0\" : form.directFlag + \"\";\r\n      form.officeFlag = form.officeFlag == null ? \"0\" : form.officeFlag + \"\";\r\n      if (form.iszgz == \"1\") this.disablediszgz = true;\r\n      this.ammeter = form;\r\n      let electrotype = this.ammeter.electrotype;\r\n      this.isMobileBase = electrotype > 1400 ? true : false;\r\n      if (\r\n        electrotype === 111 ||\r\n        electrotype === 112 ||\r\n        electrotype === 113 ||\r\n        electrotype === 2\r\n      ) {\r\n        this.isClassification = true;\r\n      }\r\n      if (\r\n        (electrotype != null && electrotype !== 1411 && electrotype !== 1412) ||\r\n        this.ammeter.property !== 2\r\n      ) {\r\n        this.propertyReadonly = false;\r\n      }\r\n      if (this.ammeter.magnification == null) {\r\n        this.ammeter.magnification = 1;\r\n      }\r\n      if (this.ammeter.company != null) {\r\n        this.ammeter.company = this.ammeter.company + \"\";\r\n        if (this.ammeter.company == \"1000085\") {\r\n          this.isCDCompany = true;\r\n        }\r\n      }\r\n      if (this.ammeter.processinstId != null) {\r\n        this.isShowFlow = true;\r\n      }\r\n      this.flowName = this.ammeter.projectname; //用于提交流程使用原项目名称\r\n      this.showModel = true;\r\n    },\r\n\r\n    //修改电表、协议的用电类型时，如用电类型不再与原先选择的局站的局站类型匹配时，系统自动清空原关联局站，需用户重新再关联局站。\r\n    changeClassifications(value) {\r\n      this.isClassification = false;\r\n      this.clearStation();\r\n      if (value.length == 0) {\r\n        // this.clearStation();\r\n        this.ammeter.property = null;\r\n        this.propertyReadonly = true;\r\n      } else {\r\n        this.propertyReadonly = false;\r\n        this.ammeter.electrotype = value[value.length - 1];\r\n        let electrotype = this.ammeter.electrotype;\r\n        this.isMobileBase = electrotype > 1400 ? true : false;\r\n        if (electrotype === 1411 || electrotype === 1412) {\r\n          //控制产权归属\r\n          this.ammeter.property = 2;\r\n          this.propertyReadonly = true;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else if (electrotype === 1421 || electrotype === 1422) {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = 4;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else if (electrotype === 1431 || electrotype === 1432) {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = 1;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = null;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        }\r\n        // checkClassificationLevel(this.ammeter.electrotype).then(res => {\r\n        //     let code = res.data.msg;\r\n        //     if (code !== '1') {\r\n        let stationtype = this.ammeter.stationtype;\r\n        if (electrotype === 111 || electrotype === 112 || electrotype === 113) {\r\n          this.isClassification = true;\r\n          if (stationtype !== 10001) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 121 || electrotype === 112) {\r\n          if (stationtype !== 10003 && stationtype !== 10004) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 131 || electrotype === 132 || electrotype === 133) {\r\n          if (stationtype !== 10005) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 1411 || electrotype === 1412) {\r\n          if (\r\n            stationtype !== 10002 ||\r\n            (stationtype == 10002 && this.propertyright !== 3)\r\n          ) {\r\n            this.clearStation();\r\n          }\r\n        } else if (\r\n          electrotype === 1421 ||\r\n          electrotype === 1422 ||\r\n          electrotype === 1431 ||\r\n          electrotype === 1432\r\n        ) {\r\n          if (stationtype !== 10002) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 2) {\r\n          this.isClassification = true;\r\n          //     if(stationtype !== 20001){ this.clearStation();}\r\n          // }else if(electrotype === 31 || electrotype === 32 || electrotype === 33){\r\n          //     if(stationtype !== 20002 || stationtype !== -2){ this.clearStation();}\r\n          // }else if(electrotype === 4){\r\n          //     if(stationtype !== -1 || stationtype !== -2){ this.clearStation();}\r\n        }\r\n        //     }\r\n        // });\r\n        if (this.configVersion == \"sc\" && this.ammeter.stationaddresscode) {\r\n          //“51”开头铁塔站址编码控制\r\n          if (\r\n            [1411, 1412].includes(electrotype) &&\r\n            !this.ammeter.stationaddresscode.startsWith(\"51\")\r\n          ) {\r\n            this.clearStation();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    clearStation() {\r\n      //清除局站信息\r\n      this.ammeter.stationName = null;\r\n      this.ammeter.stationcode = null;\r\n      this.ammeter.stationstatus = null;\r\n      this.ammeter.stationtype = null;\r\n      this.ammeter.stationaddress = null;\r\n      this.ammeter.stationaddresscode = null;\r\n    },\r\n    //选择所属部门开始\r\n    chooseResponseCenter(index, params, electroRowNum) {\r\n      this.chooseIndex = index;\r\n      this.electroRowNum = electroRowNum;\r\n      if (index == 1 || index == 2) {\r\n        let types = this.ammeter.classifications;\r\n        if (types.length == 0) {\r\n          this.$Modal.warning({ title: \"温馨提示\", content: \"请先选择用电类型！\" });\r\n          return;\r\n        } else if (this.ammeter.ammeteruse == null) {\r\n          this.$Modal.warning({ title: \"温馨提示\", content: \"请先选择电表用途！\" });\r\n          return;\r\n        } else {\r\n          if (this.ammeter.company == null) {\r\n            this.$Message.info(\"请先选择分公司\");\r\n            return;\r\n          }\r\n          this.ammeter.electrotype = types[types.length - 1];\r\n          // if(this.configVersion=='ln' || this.configVersion =='LN'){\r\n          //     this.$refs.stationModalLN.initDataList(this.ammeter.electrotype,0,this.ammeter.ammeteruse,params);//局站\r\n          // }else{\r\n          this.$refs.stationModal.ammeterid = this.ammeter.id;\r\n          this.$refs.stationModal.initDataList(\r\n            this.ammeter.electrotype,\r\n            0,\r\n            this.ammeter.ammeteruse,\r\n            this.ammeter.company,\r\n            params\r\n          ); //局站\r\n          // }\r\n        }\r\n      } else {\r\n        if (this.ammeter.company == null) {\r\n          this.$Message.info(\"请先选择分公司\");\r\n          return;\r\n        }\r\n        this.$refs.countryModal.choose(this.ammeter.company); //所属部门\r\n      }\r\n    },\r\n    getDataFromModal(data, flag) {\r\n      this.ammeter.country = data.id;\r\n      this.ammeter.countryName = data.name;\r\n      //this.chooseResponseCenter(4, data);\r\n      //选择所属部门结束\r\n    },\r\n    //获取局站数据\r\n    getDataFromStationModal(data, flag, ismodal1) {\r\n      this.ischeckStation = flag;\r\n      this.isoldcheckStation = flag;\r\n      this.ismodal1 = ismodal1;\r\n      if (this.chooseIndex == 2) {\r\n        this.electro.data[this.electroRowNum].stationId = data.id;\r\n        this.electro.data[this.electroRowNum].stationName = data.stationname;\r\n      } else {\r\n        this.propertyright = data.propertyright;\r\n        this.ammeter.stationName = data.stationname;\r\n        this.ammeter.stationcode = data.id;\r\n        this.ammeter.stationstatus = Number(\r\n          data.status == undefined ? data.STATUS : data.status\r\n        );\r\n        this.ammeter.stationtype = Number(data.stationtype);\r\n        this.ammeter.stationaddress = data.address;\r\n        // if (data.stationtype == 10002 && data.propertyright == 3) {//只有当局站类型为‘生产用房-移动基站’且产权为‘租用’时，存放站址编码\r\n        this.ammeter.stationaddresscode = data.resstationcode;\r\n        this.ammeter.resstationcode = data.resstationcode;\r\n        // }\r\n        this.ammeter.stationname5gr = data.stationname5gr;\r\n        this.ammeter.stationcode5gr = data.stationcodeintid;\r\n        //默认生成一条关联用电类型\r\n        let that = this;\r\n        listElectricType({ id: data.stationtype }).then((res) => {\r\n          let result = that.electro.data;\r\n          let electroData = Object.assign([], res.data.rows);\r\n          let count = 0;\r\n          if (result.length == 0) {\r\n            count++;\r\n          } else {\r\n            result.forEach((item) => {\r\n              electroData.forEach((item1) => {\r\n                if (item.id === item1.id) {\r\n                  electroData[0].stationId = data.id;\r\n                  electroData[0].stationName = data.stationname;\r\n                  electroData[0]._disabled = true;\r\n                  let index = result.indexOf(item);\r\n                  result.splice(index, 1);\r\n                } else {\r\n                  count++;\r\n                }\r\n              });\r\n            });\r\n          }\r\n          if (count > 0) {\r\n            that.electro.data = Object.assign([], res.data.rows);\r\n            that.electro.data[0].stationId = data.id;\r\n            that.electro.data[0].stationName = data.stationname;\r\n            that.electro.data[0]._disabled = true;\r\n          } else {\r\n            result.unshift(electroData[0]);\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    /*添加电表关联用电类型比率*/\r\n    addElectricType() {\r\n      this.$refs.selectElectricType.initElectricType();\r\n    },\r\n\r\n    setAttachData(data) {\r\n      this.multiFiles = data.data;\r\n      this.removeIds = data.ids;\r\n      this.multiFiles.busiId = this.ammeter.id;\r\n      if (this.removeIds.length != 0 && data.type == \"remove\") {\r\n        this.removeAttach();\r\n      } else {\r\n        this.upload();\r\n      }\r\n    },\r\n    removeAttach() {\r\n      removeAttach({ ids: this.removeIds.join() }).then(() => {});\r\n    },\r\n    upload() {\r\n      if (this.attachData.length != 0 && this.multiFiles.length != 0) {\r\n        // this.$Message.info(\"提示:上传文件过大可能导致上传失败！\");\r\n        this.loading = true;\r\n        axios\r\n          .request({\r\n            url: \"/common/attachments/uploadMultiFile\",\r\n            method: \"post\",\r\n            data: this.multiFiles,\r\n          })\r\n          .then((res) => {\r\n            if (res.data.code != 0) {\r\n              this.loading = false;\r\n            }\r\n            let that = this;\r\n            attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n              that.attachData = Object.assign([], res.data.rows);\r\n            });\r\n          });\r\n      }\r\n    },\r\n\r\n    /*移除选中的用电类型比率*/\r\n    removeElectricType() {\r\n      let rows = this.$refs.ammeterTable.getSelection();\r\n      let datas = this.electro.data;\r\n      rows.forEach((item) => {\r\n        if (item._index != undefined) {\r\n          datas.splice(item._index, 1);\r\n        } else {\r\n          datas.forEach((data) => {\r\n            if (data.id === item.id) {\r\n              let index = datas.indexOf(data);\r\n              datas.splice(index, 1);\r\n            }\r\n          });\r\n        }\r\n      });\r\n      this.electro.data = datas;\r\n    },\r\n\r\n    /* 设置用电类型列表*/\r\n    setElectricData: function (data) {\r\n      let origin = this.electro.data;\r\n      if (origin.length < 1) {\r\n        this.electro.data = data;\r\n      } else {\r\n        let tem = data;\r\n        for (let j = 0; j < origin.length; j++) {\r\n          for (let i = 0; i < data.length; i++) {\r\n            let typeId =\r\n              origin[j].electroTypeId != undefined\r\n                ? origin[j].electroTypeId\r\n                : origin[j].id;\r\n            if (data[i].id === typeId) {\r\n              tem.splice(tem.indexOf(data[i]), 1);\r\n            }\r\n          }\r\n        }\r\n        this.electro.data = this.electro.data.concat(tem);\r\n      }\r\n    },\r\n\r\n    //用电类型比例校验\r\n    checkElectricTypeItem() {\r\n      let items = this.electro.data;\r\n      //当“用电类型”选择“111 A类机楼（机房），112 B类机楼（机房），113 C类机楼（机房） ”或“2 管理办公用电”时，才需填用电类型分比且必填，用电类型比例之和必须等于100%\r\n      if (\r\n        this.ammeter.electrotype === 111 ||\r\n        this.ammeter.electrotype === 112 ||\r\n        this.ammeter.electrotype === 113 ||\r\n        this.ammeter.electrotype === 2\r\n      ) {\r\n        let sumRatio = items.reduce((total, item) => {\r\n          return total + item.ratio;\r\n        }, 0);\r\n        if (sumRatio !== 100) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型所占比例和必须为100%，当前值为\" + sumRatio + \"%\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    showFlow() {\r\n      this.showWorkFlow = true;\r\n      let that = this;\r\n      this.hisParams = {\r\n        busiId: that.ammeter.id,\r\n        busiType: that.ammeter.busiAlias,\r\n        procInstId: that.ammeter.processinstId,\r\n      };\r\n    },\r\n    startFlow(data) {\r\n      let busiAlias = \"MODIFY_AMM\";\r\n      let busiTitle = \"修改电表(\" + this.flowName + \")审批\";\r\n      if (data.ischangeammeter == 1 && data.billStatus < 2) {\r\n        busiAlias = \"AMM_SWITCH_AMM\";\r\n        busiTitle = \"电表换表(\" + this.flowName + \")审批\";\r\n      }\r\n      this.workFlowParams = {\r\n        busiId: data.id,\r\n        busiAlias: busiAlias,\r\n        busiTitle: busiTitle,\r\n      };\r\n      let that = this;\r\n      setTimeout(function () {\r\n        that.$refs.clwfbtn.onClick();\r\n      }, 200);\r\n    },\r\n    doWorkFlow(data) {\r\n      //流程回调\r\n      // this.closeTagByName({// 关闭已经打开的 ，避免冲突\r\n      //     route: getHomeRoute(routers, \"ammeter\"),\r\n      // });\r\n      // //跳转至修改页面 并关闭当前页\r\n      // this.closeTag({\r\n      //     route: this.$route, next: {\r\n      //         name: \"ammeter\", query: {}\r\n      //     }\r\n      // });\r\n      this.closeTag({ route: this.$route });\r\n      if (data == 0) {\r\n        this.warn();\r\n      }\r\n    },\r\n    /*选择电表/协议*/\r\n    addAmmeterProtocol() {\r\n      this.$refs.selectAmmeterProtocol.initDataList(1, this.ammeter.id);\r\n    },\r\n    /* 选择电表户号/协议编号*/\r\n    setAmmeterProrocolData: function (data) {\r\n      this.ammeter.parentId = data.id;\r\n      if (data.protocolname != null && data.protocolname.length != 0) {\r\n        this.ammeter.parentCode = data.protocolname;\r\n      } else {\r\n        this.ammeter.parentCode = data.ammetername;\r\n      }\r\n    },\r\n    /*选择客户*/\r\n    addCustomer() {\r\n      this.$refs.customerList.choose(2); //打开模态框\r\n    },\r\n    getDataFromCustomerModal: function (data) {\r\n      this.ammeter.customerId = data.id;\r\n      this.ammeter.customerName = data.name;\r\n    },\r\n    //选择包干的时候修改默认包干类型\r\n    updatepackagetype() {\r\n      let data = this.ammeter;\r\n      data.packagetype = null;\r\n    },\r\n    iszgzchange() {\r\n      if (this.ammeter.iszgz == \"1\") {\r\n        this.ammeter.directsupplyflag = 1;\r\n        this.iszgzOnly = true;\r\n      } else {\r\n        this.iszgzOnly = false;\r\n      }\r\n    },\r\n    chooseoldammetername() {\r\n      if (this.disablediszgz) return;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.status = 0;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.ammeteruse = 1;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.type = 3;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.company = this.ammeter.company;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.country = this.ammeter.country;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.directsupplyflag = 2;\r\n      this.$refs.chooseAmmeterModel.modal.show = true;\r\n      this.$Message.info(\"双击选择！！\");\r\n    },\r\n    getAmmeterModelModal(data) {\r\n      this.ammeter.oldammetername = data.name + \",\" + data.id;\r\n      this.iszgzmename = data.name + \",\" + data.id;\r\n    },\r\n    iszgzmechange() {\r\n      if (!this.iszgzmename) this.iszgzmename = this.ammeter.oldammetername;\r\n      if (this.iszgzme) {\r\n        this.ammeter.oldammetername = this.ammeter.ammetername + \",\" + this.ammeter.id;\r\n      } else {\r\n        if (this.iszgzmename == this.ammeter.ammetername) {\r\n          this.ammeter.oldammetername = null;\r\n        } else {\r\n          this.ammeter.oldammetername = this.iszgzmename;\r\n        }\r\n      }\r\n    },\r\n    projectNameChange(val) {\r\n      // var patt=/^([^\\u0000-\\u00ff]+路)([^\\u0000-\\u00ff]*)([0-9]*号)([^\\u0000-\\u00ff]+楼电表)$/;\r\n      if (\r\n        !/^.*([^\\u0000-\\u00ff]+路).*$/.test(val) &&\r\n        !/^.*([^\\u0000-\\u00ff]*)([0-9]*号).*$/.test(val) &&\r\n        !/^.*([^\\u0000-\\u00ff]+楼电表).*$/.test(val)\r\n      ) {\r\n        this.$Message.info(\"温馨提示：集团要求格式为(**路**号**楼电表)\");\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    //直接从前台取\r\n    this.categorys = {\r\n      directsupplyflag: blist(\"directSupplyFlag\"),\r\n    };\r\n    this.propertyList = blist(\"property\");\r\n    this.initAmmeter(this.$route.query.id);\r\n\r\n    this.configVersion = this.$config.version;\r\n    if (this.configVersion != \"ln\" && this.configVersion != \"LN\") {\r\n      this.ruleValidate.ammetername.push({\r\n        required: true,\r\n        message: \"不能为空\",\r\n        trigger: \"blur\",\r\n      });\r\n      this.ruleValidate.customerName = {\r\n        required: true,\r\n        message: \"不能为空\",\r\n        trigger: \"blur\",\r\n      };\r\n      this.ruleValidate.userunit.push({\r\n        required: true,\r\n        message: \"不能为空\",\r\n        trigger: \"blur\",\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.margin-right-width {\r\n  margin-right: 10px;\r\n}\r\n.testaa .ivu-row {\r\n  margin-left: 5px;\r\n  margin-right: 5px;\r\n}\r\n.testaa .requireStar .ivu-form-item-label:before {\r\n  content: \"*\";\r\n  display: inline-block;\r\n  margin-right: 4px;\r\n  line-height: 1;\r\n  font-family: SimSun;\r\n  font-size: 12px;\r\n  color: #ed4014;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/view/basedata/ammeter"}]}