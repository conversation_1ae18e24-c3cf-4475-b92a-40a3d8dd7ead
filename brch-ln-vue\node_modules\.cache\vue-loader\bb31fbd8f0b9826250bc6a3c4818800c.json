{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addPreOilAccount.vue?vue&type=template&id=60ee63c0&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addPreOilAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}