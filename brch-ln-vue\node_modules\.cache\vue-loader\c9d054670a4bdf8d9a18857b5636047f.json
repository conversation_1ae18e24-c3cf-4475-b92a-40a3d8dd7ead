{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\quota\\listQuota.vue?vue&type=style&index=0&id=2d373030&lang=less&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\quota\\listQuota.vue", "mtime": 1754285403021}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749178874080}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgp0ZC50ZC1pZCB7CiAgICBmb250LXdlaWdodDogYm9sZDsKICAgIGNvbG9yOiBncmVlbjsKICAgIGN1cnNvcjogcG9pbnRlcjsKfQoubm9hY2NvdW50IC5maWx0ZXItZGl2aWRlciB7CiAgICBtYXJnaW46IDBweDsKICAgIHRleHQtYWxpZ246IGNlbnRlcjsKfQoubm9hY2NvdW50IC5oZWFkZXItYmFyLXNob3cgewogICAgbWF4LWhlaWdodDogMzAwcHg7CiAgICAvKnBhZGRpbmctdG9wOiAxNHB4OyovCiAgICBvdmVyZmxvdzogaW5oZXJpdDsKICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZThlYWVjOwp9Ci5ub2FjY291bnQgLmhlYWRlci1iYXItaGlkZSB7CiAgICBtYXgtaGVpZ2h0OiAwOwogICAgcGFkZGluZy10b3A6IDA7CiAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgYm9yZGVyLWJvdHRvbTogMDsKfQouZm9ybS1saW5lLWhlaWdodHsKICAgIG1hcmdpbi1ib3R0b206MTBweDsKfQo="}, {"version": 3, "sources": ["listQuota.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAirBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "listQuota.vue", "sourceRoot": "src/view/basedata/quota", "sourcesContent": ["import {addQuota,viewQuota} from \"../../../api/basedata/quota/quota\";\r\n<template>\r\n    <div>\r\n        <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n<!--        <add-quota-page ref=\"addQuotaPage\" ></add-quota-page>-->\r\n        <view-quota-page ref=\"viewQuotaPage\" ></view-quota-page>\r\n        <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n        <div class=\"noaccount\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"formInline\" :model=\"queryParams\">\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"电表/协议编号：\" :label-width=\"140\" prop=\"ammeterCode\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.ammeterCode\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"项目名称：\" prop=\"projectName\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.projectName\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"单据状态：\" prop=\"billStatus\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-select v-model=\"queryParams.billStatus\"\r\n                                           category=\"basicBillStatus\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n<!--                        <Col span=\"5\" v-if=\"isProAdmin\">-->\r\n<!--                            <FormItem label=\"所属分公司：\" prop=\"company\" :label-width=\"100\" class=\"form-line-height\">-->\r\n<!--                                <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\">-->\r\n<!--                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>-->\r\n<!--                                </Select>-->\r\n<!--                            </FormItem>-->\r\n<!--                        </Col>-->\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n<!--                            <FormItem label=\"所属部门\" prop=\"country\">-->\r\n<!--                                <Select v-model=\"queryParams.country\">-->\r\n<!--                                    <Option value=\"-1\">全部</Option>-->\r\n<!--                                    <Option v-for=\"item in departments\" :value=\"item.id\" >{{item.name}}</Option>-->\r\n<!--                                </Select>-->\r\n<!--                            </FormItem>-->\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" :label-width=\"140\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"queryParams.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\" :label-width=\"140\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.country\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <div class=\"form-line-height\">\r\n                                <Button style=\"margin-left: 5px;width:69px;\" type=\"success\"  icon=\"ios-search\" @click=\"_onSearchHandle()\" >搜索 </Button>\r\n                                <Button style=\"margin-left: 5px;width:69px;\" type=\"info\" icon=\"ios-redo\" @click=\"_onResetHandle\" >重置</Button>\r\n                            </div>\r\n                        </Col>\r\n                    </Row>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <cl-table ref=\"quotaTable\"\r\n                  :searchLayout=\"quota.filter\"\r\n                  :query-params=\"queryParams\"\r\n                  :columns=\"quota.columns\"\r\n                  :data=\"quota.data\"\r\n                  select-enabled\r\n                  select-multiple\r\n                  @on-selection-change=\"handleSelectRow\"\r\n                  :loading=\"quota.loading\"\r\n                  :total=\"quota.total\"\r\n                  :pageSize=\"quota.pageSize\"\r\n                  :searchable=\"false\"\r\n                  :exportable=\"false\"\r\n                  @on-query=\"query\">\r\n            <div slot=\"buttons\">\r\n                <Button type=\"primary\" @click=\"addQuota\">添加</Button>\r\n                <Button type=\"success\" v-if=\"'sc' == version\" @click=\"editQuota\">修改</Button>\r\n                <Button type=\"error\"   v-if=\"'sc' == version\" @click=\"removeQuota\">删除</Button>\r\n                <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n                    <Button type='default' style=\"margin-left: 5px\" >导出\r\n                        <Icon type='ios-arrow-down'></Icon>\r\n                    </Button>\r\n                    <DropdownMenu slot='list'>\r\n                        <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                        <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n                    </DropdownMenu>\r\n                </Dropdown>\r\n            </div>\r\n        </cl-table>\r\n        <cl-wf-btn ref=\"clwfbtn\" :isStart=\"true\" :params=\"workFlowParams\" @on-ok=\"doWorkFlow\" v-show=\"false\"></cl-wf-btn>\r\n        <!-- 查看流程 -->\r\n        <Modal v-model=\"showWorkFlow\" title=\"定额流程及审批意见跟踪表\" :width=\"800\">\r\n            <WorkFlowInfoComponet :wfHisParams=\"hisParams\" v-if=\"showWorkFlow\"></WorkFlowInfoComponet>\r\n        </Modal>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import {getUserByUserRole,getCountryByUserId,getUserdata,getCountrysdata} from '@/api/basedata/ammeter.js'\r\n    import {isInTodoList}from\"@/api/alertcontrol/alertcontrol\";\r\n    import {removeQuota,listQuota,checkStartFlow} from '@/api/basedata/quota/quota.js';\r\n    import {blist,btext} from \"@/libs/tools\";\r\n    import viewQuotaPage from './viewQuota.vue';\r\n    import countryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import ProcessInfo from '@/view/basic/system/workflow/process-info';\r\n    import WorkFlowInfoComponet from '@/view/basic/system/workflow/workFlowInfoComponet'\r\n    import axios from '@/libs/api.request';\r\n    import excel from '@/libs/excel'\r\n    import {mapMutations} from \"vuex\";\r\n    import routers from '@/router/routers';\r\n    import {getHomeRoute} from '@/libs/util';\r\n    import indexData from '@/config/index'\r\n\r\nexport default {\r\n    name: 'quota',\r\n    components: {\r\n        ProcessInfo,\r\n        WorkFlowInfoComponet,\r\n        viewQuotaPage,\r\n        countryModal\r\n    },\r\n\r\n    data() {\r\n        //查看详情\r\n        let renterViewQuota = (h, params) => {\r\n            let column = params.column.key;\r\n            return h(\"div\", [h(\"u\", {\r\n                on: {\r\n                    click: () => {\r\n                        this.viewQuota(params.row);\r\n                    }\r\n                }\r\n            }, params.row[column])]);\r\n        };\r\n        let renderW = (h, params) => {\r\n            let that = this;\r\n            let text, type = \"\";\r\n            if (params.row.billStatus != 0 && params.row.billStatus != 3 && params.row.processinstId != null) {\r\n                text = \"查看\";\r\n                type = \"success\";\r\n            } else if (params.row.billStatus == 0 || params.row.billStatus == 3) {\r\n                text = \"提交\";\r\n                type = \"primary\";\r\n            }\r\n            if(type == \"\"){\r\n                return h(\"div\", {}, text);\r\n            }\r\n            return h(\"Button\", {\r\n                props: {\r\n                    type: type, size: \"small\"\r\n                }, on: {\r\n                    click() {\r\n                        if (params.row.billStatus!=0 && params.row.billStatus != 3 && params.row.processinstId != null) {\r\n                            that.showFlow(params.row, params.row.processinstId);\r\n                        } else if (params.row.billStatus == 0 || params.row.billStatus == 3) {\r\n                            that.loading = true;\r\n                            that.startFlow(params.row);\r\n                        }\r\n                    }\r\n                }\r\n            }, text);\r\n        };\r\n        //单据状态\r\n        let renderBillStatus = (h, params) => {\r\n            let value = \"\";\r\n            for (let item of this.billStatus) {\r\n                if (item.typeCode == params.row.billStatus) {\r\n                    value = item.typeName;\r\n                    break;\r\n                }\r\n            }\r\n            return h(\"div\", value);\r\n        };\r\n        return {\r\n            loading:false,//提交按钮重复提交\r\n            filterColl: true,//搜索面板\r\n            isAdmin:false,\r\n            isProAdmin:false,\r\n            company:null,//用户默认公司\r\n            country:null,//用户默认所属部门\r\n            countryName:null,//用户默认所属部门\r\n            version:'',\r\n            billStatus:[],//单据状态\r\n            demoList: [],\r\n            companies:[],\r\n            departments:[],\r\n            queryParams:{country:null,company:null,countryName:null},\r\n            multipleSelectionRow: [],\r\n\r\n            workFlowParams: {},\r\n            hisParams: {},\r\n            showWorkFlow: false,\r\n            exportColumns:[{title: '电表/协议编号',key: 'ammeterCode'},\r\n                {title: '项目名称',key: 'projectName'},\r\n                {title: '单据状态',key:'billStatusStr'},\r\n                {title: '1月定额值（度）',key: 'janQuotaValue'},\r\n                {title: '2月定额值（度）',key: 'febQuotaValue'},\r\n                {title: '3月定额值（度）',key: 'marQuotaValue'},\r\n                {title: '4月定额值（度）',key: 'aprQuotaValue'},\r\n                {title: '5月定额值（度）',key: 'mayQuotaValue'},\r\n                {title: '6月定额值（度）',key: 'junQuotaValue'},\r\n                {title: '7月定额值（度）',key: 'julQuotaValue'},\r\n                {title: '8月定额值（度）',key: 'augQuotaValue'},\r\n                {title: '9月定额值（度）',key: 'sepQuotaValue'},\r\n                {title: '10月定额值（度）',key: 'octQuotaValue'},\r\n                {title: '11月定额值（度）',key: 'novQuotaValue'},\r\n                {title: '12月定额值（度）',key: 'decQuotaValue'}],\r\n            export: {\r\n                run: false,//是否正在执行导出\r\n                data: \"\",//导出数据\r\n                totalPage: 0,//一共多少页\r\n                currentPage: 0,//当前多少页\r\n                percent: 0,\r\n                size: 200000\r\n            },\r\n            quota: {\r\n                loading: false,\r\n                columns: [\r\n                    {\r\n                        title: '电表/协议编号',\r\n                        key: 'ammeterCode',\r\n                        className: \"td-id\",\r\n                        render: renterViewQuota,\r\n                        align: 'center',\r\n                        minWidth:130,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '项目名称',\r\n                        key: 'projectName',\r\n                        align: 'center',\r\n                        minWidth:130,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '单据状态',\r\n                        align: 'center',\r\n                        render: renderBillStatus,\r\n                        minWidth:80,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '1月定额值（度）',\r\n                        key: 'janQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '2月定额值（度）',\r\n                        key: 'febQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '3月定额值（度）',\r\n                        key: 'marQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '4月定额值（度）',\r\n                        key: 'aprQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '5月定额值（度）',\r\n                        key: 'mayQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '6月定额值（度）',\r\n                        key: 'junQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '7月定额值（度）',\r\n                        key: 'julQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '8月定额值（度）',\r\n                        key: 'augQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '9月定额值（度）',\r\n                        key: 'sepQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '10月定额值（度）',\r\n                        key: 'octQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '11月定额值（度）',\r\n                        key: 'novQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '12月定额值（度）',\r\n                        key: 'decQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: \"流程\",\r\n                        fixed: 'right',\r\n                        key: \"action\",\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200,\r\n                        render: renderW\r\n                    }\r\n                ],\r\n                data: [],\r\n                total: 0,\r\n                pageSize: 10\r\n            }\r\n        }\r\n    },\r\n    methods: {\r\n        ...mapMutations([\"closeTag\", \"closeTagByName\"]),\r\n        onModalOK() {\r\n            this.$Message.error('确定')\r\n        },\r\n        onModalCancel() {\r\n            this.$Message.error('取消')\r\n        },\r\n        selectChange(){\r\n            let that = this;\r\n            if (this.queryParams.company != undefined) {\r\n                if(this.queryParams.company == \"-1\"){\r\n                    that.queryParams.country = -1;\r\n                    that.queryParams.countryName = null;\r\n                }else{\r\n                    getCountryByUserId(that.queryParams.company).then(res => {\r\n                        if(res.data.departments.length != 0){\r\n                            that.queryParams.country = res.data.departments[0].id;\r\n                            that.queryParams.countryName = res.data.departments[0].name;\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n\r\n        /*删除*/\r\n        removeQuota(id){\r\n            let multipleSelection = [];\r\n            if (this.multipleSelectionRow.length > 0) {\r\n                for(let item of this.multipleSelectionRow){\r\n                    if(item.billStatus != 0){\r\n                        this.$Message.info(\"所选数据包含非草稿数据，不能删除！\");\r\n                        return ;\r\n                    }\r\n                    multipleSelection.push(item.id);\r\n                }\r\n                id = multipleSelection.join(',');\r\n                this.$Modal.confirm({\r\n                    title: '温馨提示',\r\n                    content: '<p>确认删除吗?</p>',\r\n                    onOk: () => {\r\n                        this.quota.loading = true;\r\n                        removeQuota({ids: id}).then(res => {\r\n                            this.quota.loading = false;\r\n                            this.$Message.success(\"删除成功\");\r\n                            this._onSearchHandle();\r\n                        });\r\n                        this.multipleSelectionRow = [];\r\n                    },\r\n                });\r\n            } else {\r\n                this.$Message.info(\"请至少选择一行\");\r\n            }\r\n\r\n        },\r\n\r\n        /*编辑*/\r\n        editQuota() {\r\n            if (this.multipleSelectionRow.length == 1) {\r\n                let row = this.multipleSelectionRow[0];\r\n                isInTodoList(row.id,3).then(res=>{\r\n                    //存在于代办中时，报出提示\r\n                    let ownername=\"\";\r\n                    if(res.data.length>0) {\r\n                        for (let i = 0; i < res.data.length; i++) {\r\n                            ownername += res.data[i].ownername + ' ';\r\n                        }\r\n                        this.$Modal.warning({title:\"温馨提示\",content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可修改数据\"});\r\n                    }else{\r\n                        this.closeTagByName({\r\n                            route: getHomeRoute(routers, \"editQuota\"),\r\n                        });\r\n                        this.$router.push({\r\n                            name: \"editQuota\",\r\n                            query:{id:row.id},\r\n                            replace:true\r\n                        })\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            } else {\r\n                this.$Message.info(\"请选择其中一行\");\r\n            }\r\n        },\r\n\r\n        /*添加*/\r\n        addQuota() {\r\n\r\n            this.$router.push({\r\n                name: \"addQuota\",\r\n                query:{},\r\n                replace:true\r\n            })\r\n        },\r\n        /*查看*/\r\n        viewQuota(row) {\r\n            this.$refs.viewQuotaPage.initQuota(row.id);\r\n        },\r\n        _onResetHandle(){\r\n            this.multipleSelectionRow=[];\r\n            this.queryParams.company= this.company;\r\n            this.queryParams.country= this.country;\r\n            this.$refs.quotaTable.query(this.queryParams);\r\n            this.queryParams.countryName = this.countryName;\r\n        },\r\n        _onSearchHandle(){\r\n            this.multipleSelectionRow=[];\r\n            if(this.queryParams.countryName == \"\"){\r\n                this.queryParams.country = \"-1\";\r\n            }\r\n            this.$refs.quotaTable.query(this.queryParams);\r\n        },\r\n        setDisabled(){\r\n            for(let item of this.$refs.quotaTable.insideData){\r\n                if(item.billStatus != 0){\r\n                    item._disabled = true;//禁止选择\r\n                }\r\n            }\r\n        },\r\n        handleSelectRow(val){\r\n            this.multipleSelectionRow = [];\r\n            val.forEach(item => {\r\n                this.multipleSelectionRow.push(item);\r\n            });\r\n        },\r\n        startFlowSubmit(row){\r\n            let busiAlias = \"ADD_QUOTA\";\r\n            let busiTitle = \"新增定额(\" + row.projectName + \")审批\";\r\n            if (row.billStatus === 3) {\r\n                busiAlias = \"MODIFY_QUOTA\";\r\n                busiTitle = \"修改定额(\" + row.projectName + \")审批\";\r\n            }\r\n            this.workFlowParams = {\r\n                busiId: row.id,\r\n                busiAlias: busiAlias,\r\n                busiTitle: busiTitle\r\n            };\r\n            let that = this;\r\n            this.$Modal.confirm({\r\n                title: '定额提交流程',\r\n                content: '<p>是否提交定额 (' + row.projectName + ') 到流程</p>',\r\n                onOk: () => {\r\n                    that.loading = true;\r\n                    setTimeout(function () {\r\n                        that.$refs.clwfbtn.onClick();\r\n                    }, 300);\r\n                },onCancel: () => {\r\n                    that.loading = false;\r\n                }\r\n            });\r\n        },\r\n        startFlow(row) {\r\n            let that = this;\r\n            isInTodoList(row.id,3).then(res => {\r\n                //存在于代办中时，报出提示\r\n                let ownername = \"\";\r\n                if (res.data.length > 0) {\r\n                    for (let i = 0; i < res.data.length; i++) {\r\n                        ownername += res.data[i].ownername + ' ';\r\n                    }\r\n                    that.$Modal.warning({title:\"温馨提示\",content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可继续提交流程\"});\r\n                    that.loading = false;\r\n                }else if(row.billStatus == 3 || row.billStatus == 4){\r\n                    checkStartFlow({id:row.id}).then(res1 => {\r\n                        /*提交流程验证用户是否有数据需要提交*/\r\n                        that.loading = false;\r\n                        if (res1.data.id == null || res1.data.id == undefined) {\r\n                            that.$Modal.warning({title:\"温馨提示\",content: \"您没有可提交的数据\"});\r\n                        }else{\r\n                            that.startFlowSubmit(row);\r\n                        }\r\n                    });\r\n                }else{\r\n                    that.loading = false;\r\n                    that.startFlowSubmit(row);\r\n                }\r\n            });\r\n        },\r\n        showFlow(row, procInstId) {\r\n            this.showWorkFlow = true;\r\n            this.hisParams = {\r\n                busiId: row.id,\r\n                busiType: row.busiAlias,\r\n                procInstId: procInstId\r\n            }\r\n        },\r\n        doWorkFlow(data) { //流程回调\r\n            this.loading = false;\r\n            this.$refs.quotaTable.query();\r\n        },\r\n        query(params) {\r\n            this.quota.loading = true;\r\n            listQuota(params).then(res => {\r\n                this.quota.loading = false;\r\n                this.quota.total = res.data.total\r\n                this.quota.data = Object.assign([], res.data.rows)\r\n            });\r\n        },\r\n        beforeLoadData(data) {\r\n            let cols=[],keys=[]\r\n            for (let i = 0; i < this.exportColumns.length; i++) {\r\n                cols.push(this.exportColumns[i].title)\r\n                keys.push(this.exportColumns[i].key)\r\n            }\r\n            const params = {\r\n                title: cols,\r\n                key: keys,\r\n                data: data,\r\n                autoWidth: true,\r\n                filename: '定额数据导出'\r\n            };\r\n            this.queryParams.pageSize = this.quota.pageSize;\r\n            excel.export_array_to_excel(params);\r\n            this.$Spin.hide();\r\n            return\r\n        },\r\n        exportLoading(){\r\n            this.$Spin.show({\r\n                render: (h) => {\r\n                    return h('div', [\r\n                        h('Progress', {\r\n                            style: {\r\n                                width: '800px'\r\n                            },\r\n                        }),\r\n                        h('div', '导出中，请勿刷新页面......')\r\n                    ])\r\n                }\r\n            });\r\n        },\r\n        exportCsv(name) {\r\n            this.exportLoading();\r\n            this.export.run = true;\r\n            let params = this.queryParams;\r\n            if (name === 'current') {\r\n                this.beforeLoadData(this.setValueByForEach(this.quota.data));\r\n                return;\r\n            } else if (name === 'all') {\r\n                params.pageNum = 1;\r\n                params.pageSize = this.export.size\r\n            }\r\n            let req = {\r\n                url : \"/business/quota/list\",\r\n                method : \"get\",\r\n                params : params\r\n            };\r\n            axios.request(req).then(res => {\r\n                if (res.data) {\r\n                    let array = res.data.rows;\r\n                    this.beforeLoadData(this.setValueByForEach(array))\r\n                }\r\n            }).catch(err => {\r\n                console.log(err);\r\n            });\r\n        },\r\n        setValueByForEach(array){\r\n            array.forEach(function (item) {\r\n                item.billStatusStr = btext(\"basicBillStatus\", item.billStatus,'typeCode','typeName');\r\n            });\r\n            return array;\r\n        },\r\n        //选择所属部门开始\r\n        chooseResponseCenter() {\r\n            if(this.queryParams.company == null || this.queryParams.company == \"-1\"){\r\n                this.$Message.info(\"请先选择分公司\");return;\r\n            }\r\n            this.$refs.countryModal.choose(this.queryParams.company);//所属部门\r\n        },\r\n        getDataFromModal(data) {\r\n            this.queryParams.country = data.id;\r\n            this.queryParams.countryName = data.name;\r\n            //选择所属部门结束\r\n        },\r\n        getUserData(){\r\n            let that = this;\r\n            getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                let companies = that.companies;\r\n                if(res.data.companies != null && res.data.companies.length != 0){\r\n                    if(res.data.companies[0].id != \"2600000000\"){\r\n                        companies = res.data.companies;;\r\n                    }else{\r\n                        that.isProAdmin = true;\r\n                    }\r\n                }\r\n                that.company = companies[0].id;\r\n                that.queryParams.company = companies[0].id;\r\n\r\n                let departments = that.departments;\r\n                if(res.data.departments != null && res.data.departments.length != 0){\r\n                    if(res.data.companies[0].id != \"2600000000\"){\r\n                        departments = res.data.departments;\r\n                    }\r\n                }\r\n                that.country = departments[0].id;\r\n                that.countryName = departments[0].name;\r\n                that.queryParams.country = Number(departments[0].id);\r\n                that.queryParams.countryName = departments[0].name;\r\n                this.query({pageNum: 1,type:0,pageSize: this.quota.pageSize,company:this.company,country:this.country});\r\n            });\r\n        },\r\n        init() {\r\n            this.version = indexData.version\r\n            this.billStatus = blist(\"basicBillStatus\");//单据状态\r\n            let that = this;\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                    that.getUserData();\r\n                });\r\n            });\r\n\r\n        },\r\n    },\r\n\r\n    mounted() {\r\n        this.init();\r\n    },\r\n    // watch:{\r\n    //     '$route':\"init\"\r\n    // },\r\n\r\n}\r\n</script>\r\n\r\n\r\n<style lang=\"less\">\r\n    td.td-id {\r\n        font-weight: bold;\r\n        color: green;\r\n        cursor: pointer;\r\n    }\r\n    .noaccount .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .noaccount .header-bar-show {\r\n        max-height: 300px;\r\n        /*padding-top: 14px;*/\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .noaccount .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n    .form-line-height{\r\n        margin-bottom:10px;\r\n    }\r\n</style>"]}]}