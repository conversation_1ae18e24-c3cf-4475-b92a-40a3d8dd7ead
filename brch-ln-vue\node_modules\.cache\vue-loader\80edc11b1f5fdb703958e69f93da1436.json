{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addHeatAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addHeatAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICAgIF92ZXJpZnlfU3RhcnREYXRlMSwNCiAgICBqdWRnZU51bWJlciwNCiAgICBfdmVyaWZ5X0VuZERhdGUxLA0KICAgIF92ZXJpZnlfUHJldlRvdGFsUmVhZGluZ3MsDQogICAgX3ZlcmlmeV9DdXJUb3RhbFJlYWRpbmdzLA0KICAgIG90aGVyX25vX2FtbWV0ZXJvcl9wcm90b2NvbCwNCiAgICBzZWxmX25vX2FtbWV0ZXJvcl9wcm90b2NvbCwNCiAgICBIRkxfYW1tZXRlcm9yLA0KICAgIGp1ZGdpbmdfZWRpdGFiaWxpdHksDQogICAganVkZ2luZ19lZGl0YWJpbGl0eTEsDQogICAgX3ZlcmlmeV9Nb25leSwNCiAgICBfY2FsY3VsYXRlVXNlZFJlYWRpbmdzLA0KICAgIF9jYWxjdWxhdGVUb3RhbFJlYWRpbmdzLA0KICAgIF9jYWxjdWxhdGVVbml0UHJpY2VCeVVzZWRNb25leSwNCiAgICBfY2FsY3VsYXRlQWNjb3VudE1vbmV5LA0KICAgIF9jYWxjdWxhdGVRdW90ZXJlYWRpbmdzcmF0aW8sDQogICAgcmVxdWlyZWRGaWVsZFZhbGlkYXRvciwNCiAgICBjb3VudFRheGFtb3VudCwNCiAgICBjYWxjdWxhdGVBY3R1YWxNb25leSwNCiAgICBqdWRnZV9uZWdhdGUsDQogICAganVkZ2VfcmVjb3ZlcnksDQogICAganVkZ2VfeWIsDQogICAgdW5pdHBpcmNlTWluLA0KICAgIHVuaXRwaXJjZU1heA0KfSBmcm9tICdAL3ZpZXcvYWNjb3VudC9Qb3dlckFjY291bnRDb250cm9sbGVyJzsNCiAgICBpbXBvcnQgew0KICAgICAgICBzYXZlSGVhdEFjY291bnQsDQogICAgICAgIHJlbW92ZUhlYXRBY2NvdW50LA0KICAgICAgICBzZWxlY3RIZWF0SWRzDQogICAgfSBmcm9tICdAL2FwaS9jb2FsSGVhdE9pbEFjY291bnQnOw0KICAgIGltcG9ydCBjaGVja1Jlc3VsdEFuZFJlc3BvbnNlIGZyb20gIkAvdmlldy9hY2NvdW50L2NoZWNrL2NoZWNrUmVzdWx0QW5kUmVzcG9uc2UiOw0KICAgIGltcG9ydCBjaGVja1Jlc3VsdCBmcm9tICJAL3ZpZXcvYWNjb3VudC9jaGVjay9jaGVja1Jlc3VsdCI7DQogICAgaW1wb3J0IGFsYXJtQ2hlY2sgZnJvbSAiQC92aWV3L2FjY291bnQvY2hlY2svYWxhcm1DaGVjayI7DQogICAgaW1wb3J0IHtnZXREYXRlcyx0ZXN0TnVtYmVyLH0gZnJvbSAnQC92aWV3L2FjY291bnQvcG93ZXJBY2NvdW50SGVscGVyJzsNCiAgICBpbXBvcnQgYXhpb3MgZnJvbSAnQC9saWJzL2FwaS5yZXF1ZXN0JzsNCiAgICBpbXBvcnQgU2VsZWN0QW1tZXRlciBmcm9tICIuL3NlbGVjdEFtbWV0ZXIiOw0KICAgIGltcG9ydCB7X3ZlcmlmeV9GZWVTdGFydERhdGV9IGZyb20gJ0Avdmlldy9hY2NvdW50L1Bvd2VyQWNjb3VudEVzJzsNCiAgICBpbXBvcnQgQWRkQmlsbFBlciBmcm9tICIuL2FkZEhlYXRCaWxsUHJlTW9kYWwiOw0KICAgIGltcG9ydCB7cmVKb2luQmlsbHByZX0gZnJvbSAnQC9hcGkvYWNjb3VudEJpbGxQZXInOw0KICAgIGltcG9ydCB7d2lkdGhzdHlsZX0gZnJvbSAiQC92aWV3L2J1c2luZXNzL21zc0FjY291bnRiaWxsL21zc0FjY291bnRiaWxsZGF0YSI7DQogICAgaW1wb3J0IENvbXBsZXRlZFByZU1vZGFsIGZyb20gIi4vY29tcGxldGVkUHJlTW9kYWwiOw0KICAgIGltcG9ydCBpbmRleERhdGEgZnJvbSAnQC9jb25maWcvaW5kZXgnDQogICAgaW1wb3J0IENvdW50cnlNb2RhbCBmcm9tICJAL3ZpZXcvYmFzZWRhdGEvYW1tZXRlci9jb3VudHJ5TW9kYWwiOw0KICAgIGltcG9ydCB7Z2V0VXNlcmRhdGEsZ2V0VXNlckJ5VXNlclJvbGUsZ2V0Q291bnRyeXNkYXRhLGdldENvdW50cnlCeVVzZXJJZH0gZnJvbSAnQC9hcGkvYmFzZWRhdGEvYW1tZXRlci5qcycNCiAgICBsZXQgZGF0ZXM9Z2V0RGF0ZXMoKTsNCiAgICBleHBvcnQgZGVmYXVsdCB7DQogICAgICAgIG5hbWU6ICdhZGRDb2FsQWNjb3VudCcsDQogICAgICAgIGNvbXBvbmVudHM6IHthbGFybUNoZWNrLCBjaGVja1Jlc3VsdCwgY2hlY2tSZXN1bHRBbmRSZXNwb25zZSxDb21wbGV0ZWRQcmVNb2RhbCwgU2VsZWN0QW1tZXRlcixBZGRCaWxsUGVyLENvdW50cnlNb2RhbH0sDQogICAgICAgIGRhdGEoKSB7DQogICAgICAgICAgICBsZXQgcGhvdG8gPSAoaCwge3JvdywgaW5kZXh9KSA9PiB7DQogICAgICAgICAgICAgICAgbGV0IHRoYXQgPSB0aGlzDQogICAgICAgICAgICAgICAgbGV0IHN0ciA9ICcnDQogICAgICAgICAgICAgICAgaWYgKHJvdy5wcm9qZWN0bmFtZSAhPSAn5bCP6K6hJyAmJiByb3cucHJvamVjdG5hbWUgIT0gJ+WQiOiuoScpIHsNCiAgICAgICAgICAgICAgICAgICAgc3RyID0gJ+S4iuS8oOmZhOS7ticNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgcmV0dXJuIGgoImRpdiIsIFtoKCJ1Iiwgew0KICAgICAgICAgICAgICAgICAgICBvbjogew0KICAgICAgICAgICAgICAgICAgICAgICAgY2xpY2soKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLy/miZPlvIDlvLnlh7rmoYYNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocm93LnByb2plY3RuYW1lICE9ICflsI/orqEnICYmIHJvdy5wcm9qZWN0bmFtZSAhPSAn5ZCI6K6hJykgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGF0LnVwbG9hZEZpbGUocm93KQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0sIHN0cildKTsNCiAgICAgICAgICAgIH07DQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgIHN1Ym1pdDpbXSwNCiAgICAgICAgICAgICAgICBzdWJtaXQyOltdLA0KICAgICAgICAgICAgICAgIHNob3dDaGVja01vZGVsOmZhbHNlLA0KICAgICAgICAgICAgICAgIHNob3dKaE1vZGVsOmZhbHNlLA0KICAgICAgICAgICAgICAgIHNob3dBbGFybU1vZGVsOmZhbHNlLA0KICAgICAgICAgICAgICAgIGZvcm1JdGVtV2lkdGg6IHdpZHRoc3R5bGUsDQogICAgICAgICAgICAgICAgdmVyc2lvbjonJywNCiAgICAgICAgICAgICAgICBkYXRlTGlzdDpkYXRlcywNCiAgICAgICAgICAgICAgICBmaWx0ZXJDb2xsOiB0cnVlLC8v5pCc57Si6Z2i5p2/5bGV5byADQogICAgICAgICAgICAgICAgZWRpdEluZGV4OiAtMSwvL+W9k+WJjee8lui+keihjA0KICAgICAgICAgICAgICAgIGNvbHVtbnNJbmRleDotMSwvL+W9k+WJjee8lui+keWIlw0KICAgICAgICAgICAgICAgIG15U3R5bGU6W10sLy/moLflvI8NCiAgICAgICAgICAgICAgICBlZGl0U3RhcnREYXRlOicnLA0KICAgICAgICAgICAgICAgIGVkaXRFbmREYXRlOicnLA0KICAgICAgICAgICAgICAgIGVkaXRIZWF0VXNlQm9keTonJywNCiAgICAgICAgICAgICAgICBlZGl0SGVhdEFyZWFTaXplOicnLA0KICAgICAgICAgICAgICAgIGVkaXRIZWF0QW1vdW50OicnLA0KICAgICAgICAgICAgICAgIGVkaXRVbml0UHJpY2U6JycsDQogICAgICAgICAgICAgICAgZWRpdFRpY2tldFR5cGU6JycsDQogICAgICAgICAgICAgICAgZWRpdFRheFJhdGU6JycsDQogICAgICAgICAgICAgICAgZWRpdE90aGVyTW9uZXk6JycsDQogICAgICAgICAgICAgICAgc3BpblNob3c6ZmFsc2UsLy/pga7nvakNCiAgICAgICAgICAgICAgICBjYXRlZ29yeXM6W10sLy/mj4/ov7DnsbvlnosNCiAgICAgICAgICAgICAgICBlZGl0cmVtYXJrOicnLA0KICAgICAgICAgICAgICAgIGFjY291bnRTdGF0dXM6W10sDQogICAgICAgICAgICAgICAgY29tcGFuaWVzOltdLA0KICAgICAgICAgICAgICAgIGNvYWxUeXBlczogW10sDQogICAgICAgICAgICAgICAgY29hbFVzZVR5cGVzOiBbXSwNCiAgICAgICAgICAgICAgICBkZXBhcnRtZW50czpbXSwNCiAgICAgICAgICAgICAgICBpc0FkbWluOmZhbHNlLA0KICAgICAgICAgICAgICAgIGNvbXBhbnk6bnVsbCwvL+eUqOaIt+m7mOiupOWFrOWPuA0KICAgICAgICAgICAgICAgIGNvdW50cnk6bnVsbCwvL+eUqOaIt+m7mOiupOaJgOWxnumDqOmXqA0KICAgICAgICAgICAgICAgIGNvdW50cnlOYW1lOm51bGwsLy/nlKjmiLfpu5jorqTmiYDlsZ7pg6jpl6gNCiAgICAgICAgICAgICAgICBhY2NvdW50T2JqOnsNCiAgICAgICAgICAgICAgICAgICAgYWNjb3VudG5vOmRhdGVzWzFdLmNvZGUsLy/mnJ/lj7cs6buY6K6k5b2T5YmN5pyIDQogICAgICAgICAgICAgICAgICAgIGNvbXBhbnk6IiIsLy/liIblhazlj7gNCiAgICAgICAgICAgICAgICAgICAgY291bnRyeToiIiwvL+aJgOWxnumDqOmXqA0KICAgICAgICAgICAgICAgICAgICBjb2FsVXNlQm9keTpudWxsLC8v55So6IO95Li75L2TDQogICAgICAgICAgICAgICAgICAgIGNvdW50cnlOYW1lOiAiIiwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIHRiQWNjb3VudDogew0KICAgICAgICAgICAgICAgICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICBjb2x1bW5zOiBbXSwNCiAgICAgICAgICAgICAgICAgICAgdGFpbENvbHVtbjogWw0KICAgICAgICAgICAgICAgICAgICAgICAge3R5cGU6ICdzZWxlY3Rpb24nLCB3aWR0aDogNjAsIGFsaWduOiAnY2VudGVyJyx9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5pyf5Y+3IiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJhY2NvdW50Tm8iLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogOTAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5byA5aeL5pe26Ze0IiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAic3RhcnREYXRlIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEwMCwNCiAgICAgICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLnu5PmnZ/ml7bpl7QiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNsb3Q6ICJlbmREYXRlIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEwMCwNCiAgICAgICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLnlKjog73kuLvkvZMiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNsb3Q6ICJoZWF0VXNlQm9keSIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi6YeH5pqW6Z2i56evKOOOoSkiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNsb3Q6ICJoZWF0QXJlYVNpemUiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwLA0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIueDreWKmyjnmb7kuIfljYPnhKYpIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBzbG90OiAiaGVhdEFtb3VudCIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAiaGVhdEFtb3VudCIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMjAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5Y2V5Lu3KOWFgy/lubPmlrnnsbMpIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAidW5pdFByaWNlIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBrZXk6ICJ1bml0UHJpY2UiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwLA0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIuelqOaNruexu+WeiyIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgc2xvdDogInRpY2tldEltcG9ydFR5cGUiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogNjAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5pmu56Wo5ZCr56iO6YeR6aKdKOWFgykiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleTogInRpY2tldE1vbmV5IiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEwMCwNCiAgICAgICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLkuJPnpajlkKvnqI7ph5Hpop0o5YWDKSIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAidGF4VGlja2V0TW9uZXkiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwLA0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIuS4k+elqOeojueOh++8iCXvvIkiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNsb3Q6ICJ0YXhSYXRlU2hvdyIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5LiT56Wo56iO6aKdIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJ0YXhBbW91bnQiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogODAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5YW25LuWKOWFgykiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNsb3Q6ICJvdGhlckZlZSIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiA4MCwNCiAgICAgICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLlrp7nvLTotLnnlKgo5YWDKeWQq+eojiIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAicGFpZE1vbmV5IiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEyMCwNCiAgICAgICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICAvLyB7dGl0bGU6ICLpmYTku7YiLCBhbGlnbjogImNlbnRlciIsIHJlbmRlcjogcGhvdG8sIHdpZHRoOiAxMDB9LA0KICAgICAgICAgICAgICAgICAgICAgICAge3RpdGxlOiAi5aSH5rOoIiwgc2xvdDogInJlbWFyayIsYWxpZ246ICJjZW50ZXIiLCB3aWR0aDogMTUwfSwNCiAgICAgICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgICAgICAgICAgZGF0YTogW10sDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBwYWdlVG90YWw6IDAsDQogICAgICAgICAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgICAgICAgICBwYWdlU2l6ZTogMTAsLy/lvZPliY3pobUNCiAgICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgbWV0aG9kczogew0KICAgICAgICAgICAgc2VsZWN0Q2hhbmdlKCl7DQogICAgICAgICAgICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgICAgICAgICAgIGlmICh0aGF0LmFjY291bnRPYmouY29tcGFueSAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICAgICAgaWYodGhhdC5hY2NvdW50T2JqLmNvbXBhbnkgPT0gIi0xIil7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeSA9IC0xOw0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnlOYW1lID0gbnVsbDsNCiAgICAgICAgICAgICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgICAgICAgICAgICBnZXRDb3VudHJ5QnlVc2VySWQodGhhdC5hY2NvdW50T2JqLmNvbXBhbnkpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5kZXBhcnRtZW50cy5sZW5ndGggIT0gMCl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb3VudHJ5ID0gcmVzLmRhdGEuZGVwYXJ0bWVudHNbMF0uaWQ7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb3VudHJ5TmFtZSA9IHJlcy5kYXRhLmRlcGFydG1lbnRzWzBdLm5hbWU7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jlvIDlp4sNCiAgICAgICAgICAgIGNob29zZVJlc3BvbnNlQ2VudGVyKCkgew0KICAgICAgICAgICAgICAgIGlmKHRoaXMuYWNjb3VudE9iai5jb21wYW55ID09IG51bGwgfHwgdGhpcy5hY2NvdW50T2JqLmNvbXBhbnkgPT0gIi0xIiApew0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIuivt+WFiOmAieaLqeWIhuWFrOWPuCIpO3JldHVybjsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5jb3VudHJ5TW9kYWwuY2hvb3NlKHRoaXMuYWNjb3VudE9iai5jb21wYW55KTsvL+aJgOWxnumDqOmXqA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGdldERhdGFGcm9tTW9kYWwoZGF0YSkgew0KICAgICAgICAgICAgICAgIHRoaXMuYWNjb3VudE9iai5jb3VudHJ5ID0gZGF0YS5pZDsNCiAgICAgICAgICAgICAgICB0aGlzLmFjY291bnRPYmouY291bnRyeU5hbWUgPSBkYXRhLm5hbWU7DQogICAgICAgICAgICAgICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jnu5PmnZ8NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBnZXRVc2VyRGF0YSgpew0KICAgICAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgICAgICAgICAgICBnZXRVc2VyZGF0YSgpLnRoZW4ocmVzID0+IHsvL+W9k+WJjeeZu+W9leeUqOaIt+aJgOWcqOWFrOWPuOWSjOaJgOWxnumDqOmXqA0KICAgICAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5jb21wYW5pZXMubGVuZ3RoICE9IDApew0KICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGNvbXBhbmllcyA9IHJlcy5kYXRhLmNvbXBhbmllczsNCiAgICAgICAgICAgICAgICAgICAgICAgIGlmKHJlcy5kYXRhLmNvbXBhbmllc1swXS5pZCA9PSAiMjYwMDAwMDAwMCIpew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbXBhbmllcyA9IHRoYXQuY29tcGFuaWVzOw0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5jb21wYW55ID0gY29tcGFuaWVzWzBdLmlkOw0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvbXBhbnkgPSBjb21wYW5pZXNbMF0uaWQ7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgaWYocmVzLmRhdGEuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApew0KICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGRlcGFydG1lbnRzID0gcmVzLmRhdGEuZGVwYXJ0bWVudHM7DQogICAgICAgICAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5jb21wYW5pZXNbMF0uaWQgPT0gIjI2MDAwMDAwMDAiICYmIHRoYXQuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlcGFydG1lbnRzID0gdGhhdC5kZXBhcnRtZW50cw0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5jb3VudHJ5ID0gZGVwYXJ0bWVudHNbMF0uaWQ7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGF0LmNvdW50cnlOYW1lID0gZGVwYXJ0bWVudHNbMF0ubmFtZTsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb3VudHJ5ID0gTnVtYmVyKGRlcGFydG1lbnRzWzBdLmlkKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb3VudHJ5TmFtZSA9IGRlcGFydG1lbnRzWzBdLm5hbWU7DQogICAgICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgICAgICB0aGF0LnBhZ2VOdW0gPSAxDQogICAgICAgICAgICAgICAgICAgIHRoYXQuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgc2VhcmNoTGlzdCgpew0KICAgICAgICAgICAgICAgIGlmKHRoaXMuYWNjb3VudE9iai5jb3VudHJ5TmFtZSA9PSAiIil7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuYWNjb3VudE9iai5jb3VudHJ5ID0gIi0xIjsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgdGhpcy5wYWdlTnVtID0gMQ0KICAgICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCkNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBhY2NvdW50bm9DaGFuZ2UoKXsNCiAgICAgICAgICAgICAgICB0aGlzLnNlYXJjaExpc3QoKQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8v54K55Ye75L+d5a2YDQogICAgICAgICAgICBwcmVzZXJ2ZSgpIHsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YUwgPSB0aGlzLiRyZWZzLmFjY291bnRFc1RhYmxlLmdldFNlbGVjdGlvbigpOw0KICAgICAgICAgICAgICAgIGxldCBiID0gZmFsc2U7DQogICAgICAgICAgICAgICAgbGV0IGFycmF5ID0gW107DQogICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhTC5sZW5ndGg7IGkgKyspIHsNCiAgICAgICAgICAgICAgICAgICAgYiA9IHRydWU7DQogICAgICAgICAgICAgICAgICAgIGFycmF5LnB1c2goZGF0YUxbaV0pDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGlmKGIpew0KICAgICAgICAgICAgICAgICAgICB0aGlzLnN1Ym1pdERhdGEoYXJyYXkpOw0KICAgICAgICAgICAgICAgIH1lbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+ayoeacieWPr+S/neWtmOaVsOaNricpDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHN1Ym1pdENoYW5nZShpbmRleExpc3Qpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhPVtdOw0KICAgICAgICAgICAgICAgIHRoaXMuc3VibWl0Mi5tYXAoKGl0ZW0saW5kZXgpPT57DQogICAgICAgICAgICAgICAgICAgIGluZGV4TGlzdC5tYXAoKGl0ZW0yKT0+ew0KICAgICAgICAgICAgICAgICAgICAgICAgaWYoaW5kZXg9PWl0ZW0yKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhLnB1c2goaXRlbSkNCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB0aGlzLnN1Ym1pdD1kYXRhDQogICAgICAgICAgICB9LA0KDQogICAgICAgICAgICAvL+aPkOS6pOaVsOaNrg0KICAgICAgICAgICAgc3VibWl0RGF0YShkYXRhKXsNCiAgICAgICAgICAgICAgICBsZXQgYSA9IFtdOw0KICAgICAgICAgICAgICAgIGxldCB0aGF0PXRoaXM7DQogICAgICAgICAgICAgICAgaWYoZGF0YSAhPSBudWxsICYmIGRhdGEubGVuZ3RoID4gMCl7DQogICAgICAgICAgICAgICAgICAgIGxldCBudW1iZXIgPSAwOw0KICAgICAgICAgICAgICAgICAgICBsZXQgc3VibWl0RGF0YSA9IFtdOw0KICAgICAgICAgICAgICAgICAgICBsZXQgc3RyID0gJyc7DQogICAgICAgICAgICAgICAgICAgIGxldCBhY2NvdW50bm8gPSB0aGlzLmFjY291bnRPYmouYWNjb3VudG5vOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOagoemqjOaVsOaNrg0KICAgICAgICAgICAgICAgICAgICAgICAgaWYoaXRlbS5pZCA9PSBudWxsKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpdGVtLmFjY291bnRubyA9IGFjY291bnRubw0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgYS5wdXNoKGl0ZW0uaWQpOw0KICAgICAgICAgICAgICAgICAgICAgICAgc3VibWl0RGF0YS5wdXNoKGl0ZW0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgbnVtYmVyICsrOw0KICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5pZHM9YTsNCiAgICAgICAgICAgICAgICAgICAgaWYoc3RyLmxlbmd0aCA+IDApew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoc3RyKQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIGlmKHN1Ym1pdERhdGEubGVuZ3RoID4gMCl7DQogICAgICAgICAgICAgICAgICAgICAgICBzYXZlSGVhdEFjY291bnQoc3VibWl0RGF0YSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLmNvZGUgPT0gMCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGVudDogJ+aPkOekuu+8muaIkOWKn+S/neWtmCAnICsgcmVzLmRhdGEubnVtICsgJyDmnaHmlbDmja4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHVyYXRpb246IDEwLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xvc2FibGU6IHRydWUNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBhZGROZXdDb2FsQWNjb3VudCgpIHsNCiAgICAgICAgICAgICAgICAvLyBsZXQgY29tcGFueUlkID0gdGhpcy5hY2NvdW50T2JqLmNvbXBhbnk7DQogICAgICAgICAgICAgICAgLy8gbGV0IGNvdW50cnkgPSB0aGlzLmFjY291bnRPYmouY291bnRyeTsNCiAgICAgICAgICAgICAgICAvLyBpZihjb21wYW55SWQgIT0gbnVsbCAmJiBjb3VudHJ5ICE9IG51bGwpew0KICAgICAgICAgICAgICAgIC8vICAgICBsZXQgb2JqID0gew0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgY29tcGFueTpjb21wYW55SWQsDQogICAgICAgICAgICAgICAgLy8gICAgICAgICBjb3VudHJ5OmNvdW50cnksDQogICAgICAgICAgICAgICAgLy8gICAgICAgICBhY2NvdW50bm86dGhpcy5hY2NvdW50T2JqLmFjY291bnRubywNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIGFjY291bnRUeXBlOicxJywNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIGFjY291bnRlc3R5cGU6MQ0KICAgICAgICAgICAgICAgIC8vICAgICB9DQogICAgICAgICAgICAgICAgLy8gfWVsc2V7DQogICAgICAgICAgICAgICAgLy8gICAgIHRoaXMuZXJyb3JUaXBzKCfor7fpgInmi6nliIblhazlj7jlkozpg6jpl6gnKQ0KICAgICAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgICAgICBjb25zdCBjdXJyZW50RGF0ZSA9IG5ldyBEYXRlKCk7DQogICAgICAgICAgICAgICAgY29uc3QgY3VycmVudFllYXIgPSBjdXJyZW50RGF0ZS5nZXRGdWxsWWVhcigpOw0KICAgICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRNb250aCA9IGN1cnJlbnREYXRlLmdldE1vbnRoKCkgKyAxOw0KICAgICAgICAgICAgICAgIGlmIChudWxsID09IHRoaXMudGJBY2NvdW50LmRhdGEpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy50YkFjY291bnQuZGF0YSA9IFtdOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB0aGlzLnRiQWNjb3VudC5kYXRhLnVuc2hpZnQoew0KICAgICAgICAgICAgICAgICAgICAvLyBhY2NvdW50Tm86IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm8sDQogICAgICAgICAgICAgICAgICAgIGFjY291bnRObzogKHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm8gPT0gLTEgfHwgdGhpcy5hY2NvdW50T2JqLmFjY291bnRubyA9PSB1bmRlZmluZWQpID8gY3VycmVudFllYXIrIiIrY3VycmVudE1vbnRoOiB0aGlzLmFjY291bnRPYmouYWNjb3VudG5vLA0KICAgICAgICAgICAgICAgICAgICAvLyBhY2NvdW50Tm86ZGF0ZXNbMV0uY29kZSwgbmV3IERhdGUoeWVhciwgbW9udGgsIDApOw0KICAgICAgICAgICAgICAgICAgICBzdGFydERhdGU6ICh0aGlzLmFjY291bnRPYmouYWNjb3VudG5vID09IC0xIHx8IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm8gPT0gdW5kZWZpbmVkKQ0KICAgICAgICAgICAgICAgICAgICA/DQogICAgICAgICAgICAgICAgICAgIGN1cnJlbnRZZWFyICsgIi4iICsgY3VycmVudE1vbnRoICsgIi4iICsgIjAxIg0KICAgICAgICAgICAgICAgICAgICA6DQogICAgICAgICAgICAgICAgICAgIHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm8uc2xpY2UoMCw0KSArICIuIiArIHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm8uc2xpY2UoNCkgKyAiLiIgKyAiMDEiLA0KICAgICAgICAgICAgICAgICAgICBlbmREYXRlOiAodGhpcy5hY2NvdW50T2JqLmFjY291bnRubyA9PSAtMSB8fCB0aGlzLmFjY291bnRPYmouYWNjb3VudG5vID09IHVuZGVmaW5lZCkNCiAgICAgICAgICAgICAgICAgICAgPw0KICAgICAgICAgICAgICAgICAgICBjdXJyZW50WWVhciArICIuIiArIGN1cnJlbnRNb250aCArICIuIiArDQogICAgICAgICAgICAgICAgICAgICAgICBuZXcgRGF0ZShjdXJyZW50WWVhciwgY3VycmVudE1vbnRoLCAwKS5nZXREYXRlKCkNCiAgICAgICAgICAgICAgICAgICAgOg0KICAgICAgICAgICAgICAgICAgICB0aGlzLmFjY291bnRPYmouYWNjb3VudG5vLnNsaWNlKDAsNCkgKyAiLiIgKyB0aGlzLmFjY291bnRPYmouYWNjb3VudG5vLnNsaWNlKDQpICsgIi4iICsNCiAgICAgICAgICAgICAgICAgICAgICAgIG5ldyBEYXRlKHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm8uc2xpY2UoMCw0KSwgdGhpcy5hY2NvdW50T2JqLmFjY291bnRuby5zbGljZSg0KSwgMCkuZ2V0RGF0ZSgpLA0KICAgICAgICAgICAgICAgICAgICBoZWF0VXNlQm9keTogIiIsDQogICAgICAgICAgICAgICAgICAgIGhlYXRBcmVhU2l6ZToiMCIsDQogICAgICAgICAgICAgICAgICAgIGhlYXRBbW91bnQ6ICIwIiwNCiAgICAgICAgICAgICAgICAgICAgdW5pdFByaWNlOiIwIiwNCiAgICAgICAgICAgICAgICAgICAgdGlja2V0SW1wb3J0VHlwZToiIiwNCiAgICAgICAgICAgICAgICAgICAgdGlja2V0TW9uZXk6IjAiLA0KICAgICAgICAgICAgICAgICAgICB0YXhUaWNrZXRNb25leToiMCIsDQogICAgICAgICAgICAgICAgICAgIHRheFJhdGVTaG93OiIiLA0KICAgICAgICAgICAgICAgICAgICB0YXhBbW91bnQ6IjAiLA0KICAgICAgICAgICAgICAgICAgICBvdGhlckZlZToiMCIsDQogICAgICAgICAgICAgICAgICAgIHBhaWRNb25leToiMCIsDQogICAgICAgICAgICAgICAgICAgIHJlbWFyazoiIiwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB0aGlzLm15U3R5bGUucHVzaCh7DQogICAgICAgICAgICAgICAgICAgIHN0YXJ0RGF0ZTogJ215c3BhbicsDQogICAgICAgICAgICAgICAgICAgIGVuZERhdGU6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICAvLyBjdXJ0b3RhbHJlYWRpbmdzOiAnbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgaGVhdFVzZUJvZHk6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICBoZWF0QXJlYVNpemU6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICBoZWF0QW1vdW50OiAnbXlzcGFuJywNCg0KICAgICAgICAgICAgICAgICAgICB0aWNrZXRJbXBvcnRUeXBlOiAnbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgdGlja2V0TW9uZXk6Im15c3BhbiIsDQogICAgICAgICAgICAgICAgICAgIHRheFRpY2tldE1vbmV5OiJteXNwYW4iLA0KICAgICAgICAgICAgICAgICAgICB0YXhSYXRlU2hvdzogJ215c3BhbicsDQogICAgICAgICAgICAgICAgICAgIHRheEFtb3VudDogJ215c3BhbicsDQogICAgICAgICAgICAgICAgICAgIG90aGVyRmVlOiAnbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgcGFpZE1vbmV5OiAnbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgdW5pdFByaWNlOiAnbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgcmVtYXJrOiAnbXlzcGFuJywNCg0KICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvL+mqjOivgemUmeivr+W8ueWHuuaPkOekuuahhg0KICAgICAgICAgICAgZXJyb3JUaXBzKHN0cil7DQogICAgICAgICAgICAgICAgdGhpcy4kTm90aWNlLmVycm9yKHsNCiAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfmj5DnpLonLA0KICAgICAgICAgICAgICAgICAgICBkZXNjOiBzdHIsDQogICAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxMA0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGhhbmRsZVBhZ2UodmFsdWUpIHsNCiAgICAgICAgICAgICAgICBsZXQgYiA9IGZhbHNlOw0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YQ0KICAgICAgICAgICAgICAgIGxldCBhcnJheSA9IFtdOw0KICAgICAgICAgICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgICAgICAgICAgICBpZihpdGVtLmVkaXRUeXBlID09IDEpew0KICAgICAgICAgICAgICAgICAgICAgICAgYiA9IHRydWU7DQogICAgICAgICAgICAgICAgICAgICAgICBhcnJheS5wdXNoKGl0ZW0pDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICBpZihiKXsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7DQogICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogJ+aPkOekuicsDQogICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiAnPHA+5oKo5pyJ5bey57yW6L6R5L+h5oGv6L+Y5rKh5pyJ5L+d5a2Y77yM5piv5ZCm5L+d5a2Y77yfPC9wPicsDQogICAgICAgICAgICAgICAgICAgICAgICBvbk9rOiAoKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zdWJtaXREYXRhKGFycmF5KTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICBvbkNhbmNlbDogKCkgPT4gew0KDQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIHRoaXMucGFnZU51bSA9IHZhbHVlOw0KICAgICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaGFuZGxlUGFnZVNpemUodmFsdWUpIHsNCiAgICAgICAgICAgICAgICBsZXQgYiA9IGZhbHNlOw0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YQ0KICAgICAgICAgICAgICAgIGxldCBhcnJheSA9IFtdOw0KICAgICAgICAgICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgICAgICAgICAgICBpZihpdGVtLmVkaXRUeXBlID09IDEpew0KICAgICAgICAgICAgICAgICAgICAgICAgYiA9IHRydWU7DQogICAgICAgICAgICAgICAgICAgICAgICBhcnJheS5wdXNoKGl0ZW0pDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICBpZihiKXsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7DQogICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogJ+aPkOekuicsDQogICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiAnPHA+5oKo5pyJ5bey57yW6L6R5L+h5oGv6L+Y5rKh5pyJ5L+d5a2Y77yM5piv5ZCm5L+d5a2Y77yfPC9wPicsDQogICAgICAgICAgICAgICAgICAgICAgICBvbk9rOiAoKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zdWJtaXREYXRhKGFycmF5KTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICBvbkNhbmNlbDogKCkgPT4gew0KDQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIHRoaXMucGFnZVNpemUgPSB2YWx1ZTsNCiAgICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8v5ZCR5ZCO5Y+w6K+35rGC5pWw5o2uDQogICAgICAgICAgICBnZXRBY2NvdW50TWVzc2FnZXMoKSB7DQogICAgICAgICAgICAgICAgbGV0IHBvc3REYXRhID0gdGhpcy5hY2NvdW50T2JqOw0KICAgICAgICAgICAgICAgIHBvc3REYXRhLnBhZ2VOdW0gPSB0aGlzLnBhZ2VOdW07DQogICAgICAgICAgICAgICAgcG9zdERhdGEucGFnZVNpemUgPSB0aGlzLnBhZ2VTaXplOw0KICAgICAgICAgICAgICAgIGxldCByZXEgPSB7DQogICAgICAgICAgICAgICAgICAgIHVybCA6ICIvYnVzaW5lc3MvaGVhdC9hY2NvdW50L2xpc3QiLA0KICAgICAgICAgICAgICAgICAgICBtZXRob2QgOiAiZ2V0IiwNCiAgICAgICAgICAgICAgICAgICAgcGFyYW1zIDogcG9zdERhdGENCiAgICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgICAgIHRoaXMudGJBY2NvdW50LmxvYWRpbmcgPSB0cnVlDQogICAgICAgICAgICAgICAgYXhpb3MucmVxdWVzdChyZXEpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy50YkFjY291bnQubG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YSkgew0KICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGRhdGEgPSByZXMuZGF0YS5yb3dzOw0KICAgICAgICAgICAgICAgICAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS5lZGl0VHlwZSA9IDA7DQogICAgICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgICAgICAgLy8gZGF0YS5wdXNoKHRoaXMuc3VudG90YWwoZGF0YSkpLy/lsI/orqENCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudGJBY2NvdW50LmRhdGEgPSBkYXRhDQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnBhZ2VUb3RhbCA9IHJlcy5kYXRhLnRvdGFsIHx8IDANCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0TXlTdHlsZSh0aGlzLnRiQWNjb3VudC5kYXRhLmxlbmd0aCk7DQoNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZWRpdEluZGV4ID0gLTE7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmNvbHVtbnNJbmRleCA9IC0xOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coZXJyKTsNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvL+mHjee9rg0KICAgICAgICAgICAgb25SZXNldEhhbmRsZSgpew0KICAgICAgICAgICAgICAgIHRoaXMuYWNjb3VudE9iaiA9IHsNCiAgICAgICAgICAgICAgICAgICAgYWNjb3VudG5vOm51bGwsDQogICAgICAgICAgICAgICAgICAgIGNvbXBhbnk6dGhpcy5jb21wYW55LA0KICAgICAgICAgICAgICAgICAgICBoZWF0VXNlQm9keTpudWxsLA0KICAgICAgICAgICAgICAgICAgICBjb3VudHJ5Ok51bWJlcih0aGlzLmNvdW50cnkpLA0KICAgICAgICAgICAgICAgIH07DQogICAgICAgICAgICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHJlbW92ZSgpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy4kcmVmcy5hY2NvdW50RXNUYWJsZS5nZXRTZWxlY3Rpb24oKTsNCiAgICAgICAgICAgICAgICBpZihkYXRhID09IG51bGwgfHwgZGF0YS5sZW5ndGggPT09IDApew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6K+36YCJ5oup6KaB5Yig6Zmk55qE5pWw5o2uIikNCiAgICAgICAgICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB0aGlzLiRNb2RhbC5jb25maXJtKHsNCiAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfmj5DnpLonLA0KICAgICAgICAgICAgICAgICAgICBjb250ZW50OiAnPHA+5piv5ZCm56Gu6K6k5Yig6Zmk6YCJ5Lit5L+h5oGv77yfPC9wPicsDQogICAgICAgICAgICAgICAgICAgIG9uT2s6ICgpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBiID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBpZHMgPSAnJzsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCB0b3RhbCA9IHRoaXMucGFnZVRvdGFsDQogICAgICAgICAgICAgICAgICAgICAgICBmb3IobGV0IGk9MDtpPGRhdGEubGVuZ3RoO2krKyl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGl0ZW0gPSBkYXRhW2ldOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmKGl0ZW0uaWQgIT0gbnVsbCAmJiBpdGVtLmlkLmxlbmd0aCA+IDApew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZihpdGVtLnBhYnJpaWQpew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYiA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkcyArPSBpdGVtLmlkICsgJywnOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMucGFnZVRvdGFsID0gdG90YWw7DQogICAgICAgICAgICAgICAgICAgICAgICBpZihiKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZihpZHMubGVuZ3RoID4gMCl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlbW92ZUhlYXRBY2NvdW50KGlkcykudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEuY29kZSA9PSAwKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICB9ZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+mAieS4reS/oeaBr+S4reacieS/oeaBr+i/mOayoeaciei3n+W9kumbhuWNleino+mZpOWFs+iBlO+8jOivt+WFiOino+mZpOWFs+iBlCcpDQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgIG9uQ2FuY2VsOiAoKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvcGVuQWRkQmlsbFBlck1vZGFsKG5hbWUpIHsNCiAgICAgICAgICAgICAgICBpZiAobmFtZSA9PT0gJ2N1cnJlbnQnKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRBY2NvdW50KCkNCiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5hbWUgPT09ICdhbGwnKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRBbGxBY2NvdW50KCkNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/liqDlhaXlvZLpm4bljZXvvIzlhajpg6jmnInmlYjlj7DotKYNCiAgICAgICAgICAgIHNlbGVjdGVkQWxsQWNjb3VudCgpew0KICAgICAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpcw0KICAgICAgICAgICAgICAgIHRoYXQuc3BpblNob3cgPSB0cnVlOw0KICAgICAgICAgICAgICAgIHNlbGVjdEhlYXRJZHModGhpcy5hY2NvdW50T2JqKS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgICAgICAgICAgIHRoYXQuc3BpblNob3cgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgaWYocmVzLmRhdGEubGVuZ3RoID09IDApew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5lcnJvclRpcHMoJ+aXoOacieaViOaVsOaNruWPr+WKoOWFpeW9kumbhuWNlScpDQogICAgICAgICAgICAgICAgICAgIH1lbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBpZHMgPSBbXTsNCiAgICAgICAgICAgICAgICAgICAgICAgIGZvcihsZXQgaT0wO2k8cmVzLmRhdGEucm93cy5sZW5ndGg7aSsrKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbSA9IHJlcy5kYXRhLnJvd3NbaV07DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWRzLnB1c2goaXRlbS5pZCkNCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuJHJlZnMuYWRkQmlsbFBlci5pbml0QW1tZXRlcihpZHMsIDE5LHRoaXMuYWNjb3VudE9iai5jb3VudHJ5KTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHNlbGVjdGVkQWNjb3VudCgpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy4kcmVmcy5hY2NvdW50RXNUYWJsZS5nZXRTZWxlY3Rpb24oKTsNCiAgICAgICAgICAgICAgICBsZXQgYiA9IDE7DQogICAgICAgICAgICAgICAgaWYoZGF0YSA9PSBudWxsIHx8IGRhdGEubGVuZ3RoID09IDApew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygn6K+36YCJ5oup6KaB5Yqg5YWl5b2S6ZuG5Y2V55qE5Y+w6LSmJykNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICBsZXQgaWRzID0gW107DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgICAgICAgICAgICAgICAgaWYoaXRlbS5zdGF0dXMgPT09IDUpew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGIgPSAzDQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICBpZihpdGVtLnN0YXR1cyA9PT0gNCl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYj00Ow0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgaWRzLnB1c2goaXRlbS5pZCkNCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIGlmKGIgPT09IDEpew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5hZGRCaWxsUGVyLmluaXRBbW1ldGVyKGlkcywxOSx0aGlzLmFjY291bnRPYmouY291bnRyeSk7DQogICAgICAgICAgICAgICAgICAgIH1lbHNlIGlmKGIgPT09IDIpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCfpgInkuK3nmoTlj7DotKbkuK3lrZjlnKjkuLTml7bmlbDmja7vvIzor7flhYjkv53lrZjlho3liqDlhaXlvZLpm4bljZXvvIEnKQ0KICAgICAgICAgICAgICAgICAgICB9ZWxzZSBpZihiPT09Myl7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygn6YCA5Zue55qE5Y+w6LSm5LiN6IO95Yqg5YWl5YW25a6D5b2S6ZuG5Y2V77yM6K+354K55Ye7W+mHjeaWsOWKoOWFpeW9kumbhuWNlV3mjInpkq4nKQ0KICAgICAgICAgICAgICAgICAgICB9ZWxzZSBpZihiPT09NCl7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygn6YCJ5oup55qE5Y+w6LSm5pyJ5bey5Yqg5YWl5b2S6ZuG5Y2V55qE5Y+w6LSm77yM5LiN6IO95Yqg5YWl5YW25LuW5b2S6ZuG5Y2VJykNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvcGVuQ29tcGxldGVkUHJlTW9kYWwoKXsNCiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLmNvbXBsZXRlZFByZS5pbml0QW1tZXRlcih0aGlzLmFjY291bnRPYmouY291bnRyeSwyKTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBhZ2FpbkpvaW4oKXsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMuJHJlZnMuYWNjb3VudEVzVGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICAgICAgICAgICAgbGV0IGIgPSB0cnVlOw0KICAgICAgICAgICAgICAgIGlmKGRhdGEgPT0gbnVsbCB8fCBkYXRhLmxlbmd0aCA9PSAwKXsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+ivt+mAieaLqeimgemHjeaWsOWKoOWFpeW9kumbhuWNleeahOWPsOi0picpDQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgbGV0IGlkcyA9ICcnOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBzdGF0dXMgPSBpdGVtLnN0YXR1czsNCiAgICAgICAgICAgICAgICAgICAgICAgIGlmKHN0YXR1cyAhPSA1KXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBiID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICBpZHMrPSBpdGVtLmlkICsnLCcNCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIGlmKGIpew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVKb2luQmlsbHByZShpZHMpLnRoZW4oKHJlcykgPT57DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYocmVzLmRhdGEuY29kZT09MCl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbyh7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50Oifmj5DnpLrvvJrmk43kvZzmiJDlip8nICwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxMCwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsb3NhYmxlOiB0cnVlDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgICAgIH1lbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCflj6rmnInlt7LpgIDlm57nmoTlj7DotKbmiY3og73ph43mlrDliqDlhaXlvZLpm4bljZUnKQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHJlZnJlc2goKXsNCiAgICAgICAgICAgICAgICBsZXQgb2JqID0gdGhpcw0KICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgew0KICAgICAgICAgICAgICAgICAgICBvYmouZ2V0QWNjb3VudE1lc3NhZ2VzKCkNCiAgICAgICAgICAgICAgICB9LDIwMCk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgc2V0dGlja2V0SW1wb3J0VHlwZSgpIHsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgICAgICAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRUaWNrZXRUeXBlOw0KICAgICAgICAgICAgICAgIGRhdGEudGlja2V0SW1wb3J0VHlwZSA9IHZhbDsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhkYXRhLnRpY2tldEltcG9ydFR5cGUsICJkYXRhLnRpY2tldEltcG9ydFR5cGUiKQ0KICAgICAgICAgICAgICAgIGlmKGRhdGEudGlja2V0SW1wb3J0VHlwZSA9PSAn5LiT56WoJykgew0KICAgICAgICAgICAgICAgICAgICBkYXRhLnRpY2tldE1vbmV5ID0gMDsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS50YXhUaWNrZXRNb25leSA9IGRhdGEuaGVhdEFyZWFTaXplKmRhdGEudW5pdFByaWNlKjE7DQogICAgICAgICAgICAgICAgICAgIGxldCBwYWlkTW9uZXkgPSBkYXRhLnRpY2tldE1vbmV5KjErZGF0YS50YXhUaWNrZXRNb25leSoxK2RhdGEub3RoZXJGZWUqMTsNCiAgICAgICAgICAgICAgICAgICAgIGRhdGEucGFpZE1vbmV5ID0gcGFpZE1vbmV5LnRvRml4ZWQoMik7DQogICAgICAgICAgICAgICAgICAgICBkYXRhLnRheEFtb3VudCA9IGRhdGEudGF4UmF0ZVNob3cqZGF0YS50YXhUaWNrZXRNb25leSoxLzEwMDsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICAgICAgfWVsc2UgaWYoZGF0YS50aWNrZXRJbXBvcnRUeXBlID09ICfmma7npagnKSB7DQogICAgICAgICAgICAgICAgICAgIGRhdGEudGlja2V0TW9uZXkgPSBkYXRhLmhlYXRBcmVhU2l6ZSpkYXRhLnVuaXRQcmljZSoxOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLnRheFRpY2tldE1vbmV5ID0gMDsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHBhaWRNb25leSA9IGRhdGEudGlja2V0TW9uZXkqMStkYXRhLnRheFRpY2tldE1vbmV5KjErZGF0YS5vdGhlckZlZSoxOw0KICAgICAgICAgICAgICAgICAgICAgZGF0YS5wYWlkTW9uZXkgPSBwYWlkTW9uZXkudG9GaXhlZCgyKTsNCiAgICAgICAgICAgICAgICAgICAgIGRhdGEudGF4QW1vdW50ID0gMDsNCiAgICAgICAgICAgICAgICAgICAgLy8gIGRhdGEudGF4UmF0ZVNob3cgPSAiIjsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHNldHRheHJhdGUoKSB7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lZGl0VGF4UmF0ZTsNCiAgICAgICAgICAgICAgICBkYXRhLnRheFJhdGVTaG93ID0gdmFsOw0KICAgICAgICAgICAgICAgIGRhdGEudGF4QW1vdW50ID0gdmFsKmRhdGEudGF4VGlja2V0TW9uZXkqMS8xMDA7DQogICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgdmFsaWRhdGUoKXsNCiAgICAgICAgICAgICAgICBpZih0aGlzLmNvbHVtbnNJbmRleCAhPSA1KXsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZW50ZXJPcGVyYXRlKHRoaXMuY29sdW1uc0luZGV4KS5kYXRhOw0KICAgICAgICAgICAgICAgICAgICBpZih2YWwpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHN3aXRjaCAodGhpcy5jb2x1bW5zSW5kZXgpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDE6DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVTdGFydGRhdGUoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAyOg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlRW5kZGF0ZSgpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDM6DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVGZWVTdGFydERhdGUoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSA0Og0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlSGVhdEFyZWFTaXplKCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgNjoNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy52YWxpZGF0ZVVuaXRQcmljZSgpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDc6DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVUaWNrZXRJbXBvcnRUeXBlKCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgODoNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy52YWxpZGF0ZVRheFJhdGVTaG93KCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGNhc2UgMzoNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyAgICAgdGhpcy52YWxpZGF0ZUZlZVN0YXJ0RGF0ZSgpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDk6DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVPdGhlck1vbmV5KCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8v6aqM6K+B6LW35aeL5pe26Ze0DQogICAgICAgICAgICB2YWxpZGF0ZVN0YXJ0ZGF0ZSgpIHsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgICAgICAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRTdGFydERhdGU7DQogICAgICAgICAgICAgICAgbGV0IGlzRGlhbiA9IHZhbFs0XT09Jy4nICYmIHZhbFs3XT09Jy4nIHx8IHZhbFs0XT09Jy4nICYmIHZhbFs2XT09Jy4nIDsNCiAgICAgICAgICAgICAgICBpZighaXNEaWFuKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCLlvIDlp4vml7bpl7TmoLzlvI/kuI3mraPnoa7vvIEiKTsNCiAgICAgICAgICAgICAgICAgICAgdmFsID0gIiI7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIC8vIGRlYnVnZ2VyDQogICAgICAgICAgICAgICAgLy8gaWYgKHZhbCAhPSBkYXRhLm9sZF9zdGFydGRhdGUpIHsNCiAgICAgICAgICAgICAgICAvLyAgICAgLy8g6aqM6K+B6LW35aeL5pe26Ze05pa55rOVDQogICAgICAgICAgICAgICAgLy8gICAgIGxldCByZXN1bHQgPSBfdmVyaWZ5X1N0YXJ0RGF0ZTEoZGF0YSwgdmFsKTsNCiAgICAgICAgICAgICAgICAvLyAgICAgY29uc29sZS5sb2cocmVzdWx0LCAicmVzdWx0Iik7DQogICAgICAgICAgICAgICAgLy8gICAgIGlmIChyZXN1bHQpIHsvL+Wksei0peWwseW8ueWHuuaPkOekuuWGheWuue+8jOW5tuWwhuaVsOaNruaBouWkjeWIneWni+WMlg0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgdGhpcy5lcnJvclRpcHMocmVzdWx0KTsNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIHRoaXMubXlTdHlsZVt0aGlzLmVkaXRJbmRleF0uc3RhcnREYXRlID0gImVycm9yU3RsZSI7DQogICAgICAgICAgICAgICAgLy8gICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgLy8gICAgICAgICB0aGlzLm15U3R5bGVbdGhpcy5lZGl0SW5kZXhdLnN0YXJ0RGF0ZSA9ICJteXNwYW4iOw0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgdGhpcy5zdGFydE1vZGFsID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAvLyAgICAgfQ0KICAgICAgICAgICAgICAgIC8vIH0gZWxzZSBpZiAodmFsID09IGRhdGEub2xkX3N0YXJ0ZGF0ZSkgew0KICAgICAgICAgICAgICAgIC8vICAgICBkYXRhLnN0YXJ0RGF0ZSA9IHZhbDsNCiAgICAgICAgICAgICAgICAvLyB9ZWxzZSB7DQogICAgICAgICAgICAgICAgaWYoZGF0YS5zdGFydERhdGUgIT0iIiAmJiBkYXRhLmVuZERhdGUgIT0iIikgew0KICAgICAgICAgICAgICAgICAgICBsZXQgcmlxaUxlbmdoID0gZGF0YS5lbmREYXRlLnNwbGl0KCIuIilbMl0qMSAtIGRhdGEuc3RhcnREYXRlLnNwbGl0KCIuIilbMl0qMTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5oZWF0QW1vdW50ID0gZGF0YS5oZWF0QXJlYVNpemUqcmlxaUxlbmdoKjYwKjAuNyozLjYvMTAwMDAwMDsNCiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coZGF0YS5oZWF0QW1vdW50LCAiZGF0YS5oZWF0QW1vdW50IikNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIGRhdGEuc3RhcnREYXRlID0gdmFsOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgICAgICAgICAvLyB9DQoNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvL+mqjOivgeaIquatouaXtumXtA0KICAgICAgICAgICAgdmFsaWRhdGVFbmRkYXRlKCkgew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdEVuZERhdGU7DQogICAgICAgICAgICAgICAgbGV0IGlzRGlhbiA9IHZhbFs0XT09Jy4nICYmIHZhbFs3XT09Jy4nIHx8IHZhbFs0XT09Jy4nICYmIHZhbFs2XT09Jy4nIDsNCiAgICAgICAgICAgICAgICBpZighaXNEaWFuKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCLnu5PmnZ/ml7bpl7TmoLzlvI/kuI3mraPnoa7vvIEiKTsNCiAgICAgICAgICAgICAgICAgICAgdmFsID0gIiI7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGlmKGRhdGEuc3RhcnREYXRlICE9IiIgJiYgZGF0YS5lbmREYXRlICE9IiIpIHsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHJpcWlMZW5naCA9IGRhdGEuZW5kRGF0ZS5zcGxpdCgiLiIpWzJdKjEgLSBkYXRhLnN0YXJ0RGF0ZS5zcGxpdCgiLiIpWzJdKjE7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuaGVhdEFtb3VudCA9IGRhdGEuaGVhdEFyZWFTaXplKnJpcWlMZW5naCo2MCowLjcqMy42LzEwMDAwMDA7DQogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGRhdGEuaGVhdEFtb3VudCwgImRhdGEuaGVhdEFtb3VudCIpDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIC8vIGlmICh2YWwgIT0gZGF0YS5vbGRfZW5kZGF0ZSkgew0KICAgICAgICAgICAgICAgIC8vICAgICAvLyDpqozor4HmiKrmraLml6XmnJ/mlrnms5UNCiAgICAgICAgICAgICAgICAvLyAgICAgbGV0IHJlc3VsdCA9IF92ZXJpZnlfRW5kRGF0ZTEoZGF0YSwgdmFsKTsNCiAgICAgICAgICAgICAgICAvLyAgICAgaWYgKHJlc3VsdCkgew0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgdGhpcy5lcnJvclRpcHMocmVzdWx0KTsNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIHRoaXMubXlTdHlsZVt0aGlzLmVkaXRJbmRleF0uZW5kRGF0ZSA9ICJlcnJvclN0bGUiOw0KICAgICAgICAgICAgICAgIC8vICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgdGhpcy5teVN0eWxlW3RoaXMuZWRpdEluZGV4XS5lbmREYXRlID0gIm15c3BhbiI7DQoNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIHRoaXMudXBkYXRlZW5kZGF0ZShkYXRhLCB2YWwpDQoNCiAgICAgICAgICAgICAgICAvLyAgICAgfQ0KICAgICAgICAgICAgICAgIC8vIH0gZWxzZSBpZiAodmFsID09IGRhdGEub2xkX2VuZGRhdGUpIHsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lbmREYXRlID0gdmFsOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgICAgICAgICAvLyB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgdmFsaWRhdGVIZWF0QXJlYVNpemUoKSB7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lZGl0SGVhdEFyZWFTaXplOw0KICAgICAgICAgICAgICAgIGRhdGEuaGVhdEFyZWFTaXplID0gdmFsOw0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGRhdGEuaGVhdEFyZWFTaXplLCAiZGF0YS5oZWF0QXJlYVNpemUiKQ0KICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGRhdGEuc3RhcnREYXRlLnNwbGl0KCIuIilbMl0qMSwgImRhdGEuc3RhcnREYXRlLnNwbGl0KCIpDQogICAgICAgICAgICAgICAgY29uc29sZS5sb2coZGF0YS5lbmREYXRlLnNwbGl0KCIuIilbMl0qMSwgImRhdGEuZW5kRGF0ZS5zcGxpdCgiKQ0KICAgICAgICAgICAgICAgIGlmKGRhdGEuc3RhcnREYXRlICE9IiIgJiYgZGF0YS5lbmREYXRlICE9IiIpIHsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHJpcWlMZW5naCA9IGRhdGEuZW5kRGF0ZS5zcGxpdCgiLiIpWzJdKjEgLSBkYXRhLnN0YXJ0RGF0ZS5zcGxpdCgiLiIpWzJdKjEgKyAxOw0KICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhyaXFpTGVuZ2gsICJyaXFpTGVuZ2giKQ0KDQogICAgICAgICAgICAgICAgICAgIGRhdGEuaGVhdEFtb3VudCA9ICh2YWwqcmlxaUxlbmdoKjI0KjYwKjAuNyozLjYvMTAwMDAwMCkudG9GaXhlZCg2KTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgY29uc29sZS5sb2coZGF0YS50aWNrZXRJbXBvcnRUeXBlLCAiZGF0YS50aWNrZXRJbXBvcnRUeXBlIikNCiAgICAgICAgICAgICAgICAvLyBkZWJ1Z2dlcg0KICAgICAgICAgICAgICAgIGlmKGRhdGEudGlja2V0SW1wb3J0VHlwZSA9PSAn5LiT56WoJykgew0KICAgICAgICAgICAgICAgICAgICBkYXRhLnRpY2tldE1vbmV5ID0gMDsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS50YXhUaWNrZXRNb25leSA9IGRhdGEuaGVhdEFyZWFTaXplKmRhdGEudW5pdFByaWNlKjE7DQogICAgICAgICAgICAgICAgICAgIGxldCBwYWlkTW9uZXkgPSBkYXRhLnRpY2tldE1vbmV5KjErZGF0YS50YXhUaWNrZXRNb25leSoxK2RhdGEub3RoZXJGZWUqMTsNCiAgICAgICAgICAgICAgICAgICAgIGRhdGEucGFpZE1vbmV5ID0gcGFpZE1vbmV5LnRvRml4ZWQoMik7DQogICAgICAgICAgICAgICAgICAgICBkYXRhLnRheEFtb3VudCA9IGRhdGEudGF4UmF0ZVNob3cqZGF0YS50YXhUaWNrZXRNb25leSoxLzEwMDsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICAgICAgfWVsc2UgaWYoZGF0YS50aWNrZXRJbXBvcnRUeXBlID09ICfmma7npagnKSB7DQogICAgICAgICAgICAgICAgICAgIGRhdGEudGlja2V0TW9uZXkgPSBkYXRhLmhlYXRBcmVhU2l6ZSpkYXRhLnVuaXRQcmljZSoxOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLnRheFRpY2tldE1vbmV5ID0gMDsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHBhaWRNb25leSA9IGRhdGEudGlja2V0TW9uZXkqMStkYXRhLnRheFRpY2tldE1vbmV5KjErZGF0YS5vdGhlckZlZSoxOw0KICAgICAgICAgICAgICAgICAgICAgZGF0YS5wYWlkTW9uZXkgPSBwYWlkTW9uZXkudG9GaXhlZCgyKTsNCiAgICAgICAgICAgICAgICAgICAgIGRhdGEudGF4QW1vdW50ID0gMDsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIC8vIGVsc2Ugew0KICAgICAgICAgICAgICAgIC8vICAgICB0aGlzLmVycm9yVGlwcygi5byA5aeL5oiW6ICF57uT5p2f5pe26Ze05LiN6IO95Li656m677yBIik7DQogICAgICAgICAgICAgICAgLy8gICAgIGRhdGEuaGVhdEFtb3VudCA9ICIiOw0KICAgICAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB2YWxpZGF0ZVRpY2tldEltcG9ydFR5cGUoKSB7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lZGl0VGlja2V0VHlwZTsNCiAgICAgICAgICAgICAgICBkYXRhLnRpY2tldEltcG9ydFR5cGUgPSB2YWw7DQogICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgdmFsaWRhdGVUYXhSYXRlU2hvdygpIHsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgICAgICAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRUYXhSYXRlOw0KICAgICAgICAgICAgICAgIGRhdGEudGF4UmF0ZVNob3cgPSB2YWw7DQogICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgdmFsaWRhdGVGZWVTdGFydERhdGUoKXsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgICAgICAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRGZWVTdGFydERhdGU7DQogICAgICAgICAgICAgICAgbGV0IHJlc3VsdCA9IF92ZXJpZnlfRmVlU3RhcnREYXRlKGRhdGEsdmFsKTsNCiAgICAgICAgICAgICAgICBpZihyZXN1bHQpey8v5aSx6LSl5bCx5by55Ye65o+Q56S65YaF5a6577yM5bm25bCG5pWw5o2u5oGi5aSN5Yid5aeL5YyWDQogICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKHJlc3VsdCkNCiAgICAgICAgICAgICAgICB9ZWxzZXsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5mZWVTdGFydERhdGUgPSB2YWw7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB2YWxpZGF0ZVVuaXRQcmljZSgpIHsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgICAgICAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRVbml0UHJpY2U7DQogICAgICAgICAgICAgICAgZGF0YS51bml0UHJpY2UgPSB2YWw7DQogICAgICAgICAgICAgICAgY29uc29sZS5sb2coZGF0YS50aWNrZXRJbXBvcnRUeXBlLCAiZGF0YS50aWNrZXRJbXBvcnRUeXBlIikNCiAgICAgICAgICAgICAgICAvLyBkZWJ1Z2dlcg0KICAgICAgICAgICAgICAgIGlmKGRhdGEudGlja2V0SW1wb3J0VHlwZSA9PSAn5LiT56WoJykgew0KICAgICAgICAgICAgICAgICAgICBkYXRhLnRpY2tldE1vbmV5ID0gMDsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS50YXhUaWNrZXRNb25leSA9IGRhdGEuaGVhdEFyZWFTaXplKmRhdGEudW5pdFByaWNlKjE7DQogICAgICAgICAgICAgICAgICAgIGxldCBwYWlkTW9uZXkgPSBkYXRhLnRpY2tldE1vbmV5KjErZGF0YS50YXhUaWNrZXRNb25leSoxK2RhdGEub3RoZXJGZWUqMTsNCiAgICAgICAgICAgICAgICAgICAgIGRhdGEucGFpZE1vbmV5ID0gcGFpZE1vbmV5LnRvRml4ZWQoMik7DQogICAgICAgICAgICAgICAgICAgICBkYXRhLnRheEFtb3VudCA9IGRhdGEudGF4UmF0ZVNob3cqZGF0YS50YXhUaWNrZXRNb25leSoxLzEwMDsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICAgICAgfWVsc2UgaWYoZGF0YS50aWNrZXRJbXBvcnRUeXBlID09ICfmma7npagnKSB7DQogICAgICAgICAgICAgICAgICAgIGRhdGEudGlja2V0TW9uZXkgPSBkYXRhLmhlYXRBcmVhU2l6ZSpkYXRhLnVuaXRQcmljZSoxOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLnRheFRpY2tldE1vbmV5ID0gMDsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHBhaWRNb25leSA9IGRhdGEudGlja2V0TW9uZXkqMStkYXRhLnRheFRpY2tldE1vbmV5KjErZGF0YS5vdGhlckZlZSoxOw0KICAgICAgICAgICAgICAgICAgICAgZGF0YS5wYWlkTW9uZXkgPSBwYWlkTW9uZXkudG9GaXhlZCgyKTsNCiAgICAgICAgICAgICAgICAgICAgIGRhdGEudGF4QW1vdW50ID0gMDsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHZhbGlkYXRlT3RoZXJNb25leSgpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdE90aGVyTW9uZXk7DQogICAgICAgICAgICAgICAgaWYgKCF0ZXN0TnVtYmVyKHZhbCkpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+ivt+i+k+WFpeaVsOWtl++8gScpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBkYXRhLm90aGVyRmVlID0gdmFsOw0KICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgICAgICAgIGxldCBwYWlkTW9uZXkgPSBkYXRhLnRpY2tldE1vbmV5KjErZGF0YS50YXhUaWNrZXRNb25leSoxK2RhdGEub3RoZXJGZWUqMTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5wYWlkTW9uZXkgPSBwYWlkTW9uZXkudG9GaXhlZCgyKTsNCiAgICAgICAgICAgICAgICAvLyBkZWJ1Z2dlcg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHNldHJlbWFyaygpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdHJlbWFyazsNCiAgICAgICAgICAgICAgICBkYXRhLnJlbWFyayA9IHZhbDsNCiAgICAgICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBzZXRIZWF0VXNlQm9keSgpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdEhlYXRVc2VCb2R5Ow0KICAgICAgICAgICAgICAgIGRhdGEuaGVhdFVzZUJvZHkgPSB2YWw7DQogICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgc2V0TXlTdHlsZShsZW5ndGgpew0KICAgICAgICAgICAgICAgIHRoaXMubXlTdHlsZT1bXTsNCiAgICAgICAgICAgICAgICBmb3IodmFyIGk9MDtpPGxlbmd0aDtpKyspew0KICAgICAgICAgICAgICAgICAgICB0aGlzLm15U3R5bGUucHVzaCh7DQogICAgICAgICAgICAgICAgICAgICAgICBzdGFydERhdGU6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgZW5kRGF0ZTogJ215c3BhbicsDQogICAgICAgICAgICAgICAgICAgICAgICBjb2FsVXNlQm9keTonbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgICAgIGZlZVN0YXJ0RGF0ZTonbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgICAgIGNvYWxBbW91bnQ6J215c3BhbicsDQogICAgICAgICAgICAgICAgICAgICAgICB0YXhUaWNrZXRNb25leTonbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlbWFyazonbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8vc3BhbueCueWHu+S6i+S7tuWwhnNwYW7mjaLmiJDovpPlhaXmoYblubbkuJTojrflj5bnhKbngrkNCiAgICAgICAgICAgIHNlbGVjdENhbGwocm93LGluZGV4LGNvbHVtbnMsc3RyKXsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRTdGFydERhdGUgPSByb3cuc3RhcnREYXRlOw0KICAgICAgICAgICAgICAgIHRoaXMuZWRpdEVuZERhdGUgPSByb3cuZW5kRGF0ZTsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRIZWF0VXNlQm9keSA9IHJvdy5oZWF0VXNlQm9keTsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRIZWF0QXJlYVNpemUgPSByb3cuaGVhdEFyZWFTaXplOw0KICAgICAgICAgICAgICAgIHRoaXMuZWRpdEhlYXRBbW91bnQgPSByb3cuaGVhdEFtb3VudDsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRVbml0UHJpY2UgPSByb3cudW5pdFByaWNlOw0KICAgICAgICAgICAgICAgIHRoaXMuZWRpdFRpY2tldFR5cGUgPSByb3cudGlja2V0SW1wb3J0VHlwZTsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRUYXhSYXRlID0gcm93LnRheFJhdGVTaG93Ow0KICAgICAgICAgICAgICAgIHRoaXMuZWRpdE90aGVyTW9uZXkgPSByb3cub3RoZXJGZWU7DQogICAgICAgICAgICAgICAgdGhpcy5lZGl0cmVtYXJrID0gcm93LnJlbWFyazsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRJbmRleCA9IGluZGV4Ow0KICAgICAgICAgICAgICAgIHRoaXMuY29sdW1uc0luZGV4ID0gY29sdW1uczsNCiAgICAgICAgICAgICAgICBsZXQgYT10aGlzOw0KICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgew0KICAgICAgICAgICAgICAgICAgICBhLiRyZWZzW3N0citpbmRleCtjb2x1bW5zXS5mb2N1cygpOw0KICAgICAgICAgICAgICAgIH0sMjAwKTsNCg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8v6Lez6L2s5Yiw5LiL5LiA5qC8DQogICAgICAgICAgICBuZXh0Q2VsbChkYXRhKXsNCiAgICAgICAgICAgICAgICBsZXQgaW5kZXggPSBkYXRhLmVkaXRJbmRleDsNCiAgICAgICAgICAgICAgICBsZXQgY29sdW1ucyA9IGRhdGEuY29sdW1uc0luZGV4Ow0KICAgICAgICAgICAgICAgIGxldCByb3cgPSAnJzsNCiAgICAgICAgICAgICAgICBpZihpbmRleCA9PT0gLTEgJiYgY29sdW1ucyA9PT0gLTEpew0KICAgICAgICAgICAgICAgICAgICBpbmRleCA9IDA7DQogICAgICAgICAgICAgICAgICAgIGNvbHVtbnMgPSAxOw0KICAgICAgICAgICAgICAgIH1lbHNlIGlmKGluZGV4ID4gLTEgJiYgY29sdW1ucyA9PT0gNSl7DQogICAgICAgICAgICAgICAgICAgIC8v5b2T6Lez6L2s55qE5pyA5ZCO5LiA6KGM5pyA5ZCO5LiA5qC855qE5pe25YCZDQogICAgICAgICAgICAgICAgICAgIGlmIChpbmRleCA+PSBkYXRhLnBhZ2VTaXplIC0gMSB8fCBpbmRleCA+PSBkYXRhLnBhZ2VUb3RhbCAtIDEpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4ID0gMDsNCiAgICAgICAgICAgICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgICAgICAgICAgICBpbmRleCArKzsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICBjb2x1bW5zID0gMTsNCiAgICAgICAgICAgICAgICB9ZWxzZXsNCiAgICAgICAgICAgICAgICAgICAgY29sdW1ucyArPSAxOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBkZWJ1Z2dlcg0KICAgICAgICAgICAgICAgIGRhdGEuZWRpdEluZGV4ID0gaW5kZXg7DQogICAgICAgICAgICAgICAgZGF0YS5jb2x1bW5zSW5kZXggPSBjb2x1bW5zOw0KICAgICAgICAgICAgICAgIHJvdyA9IGRhdGEudGJBY2NvdW50LmRhdGFbaW5kZXhdOw0KICAgICAgICAgICAgICAgIGlmKHJvdyl7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZWRpdFN0YXJ0RGF0ZSA9IHJvdy5zdGFydERhdGU7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZWRpdEVuZERhdGUgPSByb3cuZW5kRGF0ZTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0SGVhdFVzZUJvZHkgPSByb3cuaGVhdFVzZUJvZHk7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZWRpdEhlYXRBcmVhU2l6ZSA9IHJvdy5oZWF0QXJlYVNpemU7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZWRpdEhlYXRBbW91bnQgPSByb3cuaGVhdEFtb3VudDsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0VW5pdFByaWNlID0gcm93LnVuaXRQcmljZTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0VGlja2V0VHlwZSA9IHJvdy50aWNrZXRJbXBvcnRUeXBlOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRUYXhSYXRlID0gcm93LnRheFJhdGVTaG93Ow0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRPdGhlck1vbmV5ID0gcm93Lm90aGVyRmVlOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRyZW1hcmsgPSByb3cucmVtYXJrOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS4kcmVmc1tkYXRhLmVudGVyT3BlcmF0ZShjb2x1bW5zKS5zdHIraW5kZXgrY29sdW1uc10uZm9jdXMoKTsNCiAgICAgICAgICAgICAgICB9LDIwMCk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/moLnmja7liJflj7fov5Tlm57lr7nlupTnmoTliJflkI0NCiAgICAgICAgICAgIGVudGVyT3BlcmF0ZShudW1iZXIpew0KICAgICAgICAgICAgICAgIGxldCBzdHIgPSAnJzsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IG51bGw7DQogICAgICAgICAgICAgICAgc3dpdGNoIChudW1iZXIpIHsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSAxOg0KICAgICAgICAgICAgICAgICAgICAgICAgc3RyID0gJ3N0YXJ0RGF0ZSc7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0U3RhcnREYXRlOw0KICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgIGNhc2UgMjoNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICdlbmREYXRlJzsNCiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRFbmREYXRlOw0KICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgIGNhc2UgMzoNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICdoZWF0VXNlQm9keScNCiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRIZWF0VXNlQm9keTsNCiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICBjYXNlIDQ6DQogICAgICAgICAgICAgICAgICAgICAgICBzdHIgPSAnaGVhdEFyZWFTaXplJzsNCiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRIZWF0QXJlYVNpemU7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSA1Og0KICAgICAgICAgICAgICAgICAgICAgICAgc3RyID0gJ2hlYXRBbW91bnQnOw0KICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdEhlYXRBbW91bnQ7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSA2Og0KICAgICAgICAgICAgICAgICAgICAgICAgc3RyID0gJ3VuaXRQcmljZSc7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0VW5pdFByaWNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgIGNhc2UgNzoNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICdlZGl0VGlja2V0VHlwZSc7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy50aWNrZXRJbXBvcnRUeXBlOw0KICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgIGNhc2UgODoNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICdlZGl0VGF4UmF0ZSc7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy50YXhSYXRlU2hvdzsNCiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICBjYXNlIDk6DQogICAgICAgICAgICAgICAgICAgICAgICBzdHIgPSAnb3RoZXJGZWUnOw0KICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdE90aGVyTW9uZXk7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSAxMDoNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICdyZW1hcmsnOw0KICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdHJlbWFyazsNCiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICByZXR1cm4ge3N0cjpzdHIsZGF0YTpkYXRhfTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBwcmVkKCl7DQogICAgICAgICAgICAgICAgdmFyIGxldHQgPSB0aGlzOw0KICAgICAgICAgICAgICAgIGxldCBpbmRleCA9IGxldHQuZWRpdEluZGV4Ow0KICAgICAgICAgICAgICAgIGxldCBjb2x1bW5zID0gbGV0dC5jb2x1bW5zSW5kZXg7DQogICAgICAgICAgICAgICAgaWYoaW5kZXggPT09IC0xICYmIGNvbHVtbnMgPT09IC0xKXsNCiAgICAgICAgICAgICAgICAgICAgaW5kZXggPSAwOw0KICAgICAgICAgICAgICAgICAgICBjb2x1bW5zID0gMTsNCiAgICAgICAgICAgICAgICAgICAgbGV0dC5lZGl0SW5kZXggPSBpbmRleDsNCiAgICAgICAgICAgICAgICAgICAgbGV0dC5jb2x1bW5zSW5kZXggPSBjb2x1bW5zOw0KICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldHQuJHJlZnNbbGV0dC5lbnRlck9wZXJhdGUoY29sdW1ucykuc3RyK2luZGV4K2NvbHVtbnNdLmZvY3VzKCk7DQogICAgICAgICAgICAgICAgICAgIH0sMjAwKTsNCiAgICAgICAgICAgICAgICB9ZWxzZXsNCiAgICAgICAgICAgICAgICAgICAgbGV0dC52YWxpZGF0ZSgpDQogICAgICAgICAgICAgICAgICAgIGxldHQuc2V0cmVtYXJrKCkNCiAgICAgICAgICAgICAgICAgICAgbGV0dC5uZXh0Q2VsbChsZXR0KQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBlbGxpcHNpcyAodmFsdWUpIHsNCiAgICAgICAgICAgICAgICBpZiAoIXZhbHVlKSByZXR1cm4gJycNCiAgICAgICAgICAgICAgICBpZiAodmFsdWUubGVuZ3RoID4gMTAwKSB7DQogICAgICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZS5zbGljZSgwLDEwMCkgKyAnLi4uJw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWUNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBoYW5kbGVQcm9ncmVzcyhldmVudCwgZmlsZSkgew0KICAgICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbyh7DQogICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6IGZpbGUubmFtZSArICIg5q2j5Zyo5LiK5Lyg44CCIiwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBoYW5kbGVVcGxvYWRTdWNjZXNzKCkgew0KDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaGFuZGxlRm9ybWF0RXJyb3IoZmlsZSkgew0KICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKA0KICAgICAgICAgICAgICAgICAgICBmaWxlLm5hbWUgKyAiIOagvOW8j+S4jeato+ehruOAguWPquiDveS4iuS8oOWQjue8gOWQjeS4uiB4bHPmiJbogIUgeGxzeCDnmoTmlofku7YiDQogICAgICAgICAgICAgICAgKTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvLyDlr7zlhaUNCiAgICAgICAgICAgIG9uRXhjZWxVcGxvYWQoZmlsZSkgew0KICAgICAgICAgICAgICAgIGlmICghZmlsZSkgew0KICAgICAgICAgICAgICAgICAgICB0aGlzLiROb3RpY2UuZXJyb3Ioew0KICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfmj5DnpLonLA0KICAgICAgICAgICAgICAgICAgICAgICAgZGVzYzogJ+ivt+mAieaLqeimgeS4iuS8oOeahOaWh+S7tu+8gScsDQogICAgICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTANCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBsZXQgZmlsZU5hbWUgPSBmaWxlLm5hbWUubGFzdEluZGV4T2YoIi4iKTsvL+WPluWIsOaWh+S7tuWQjeW8gOWni+WIsOacgOWQjuS4gOS4queCueeahOmVv+W6pg0KICAgICAgICAgICAgICAgIGxldCBmaWxlTmFtZUxlbmd0aCA9IGZpbGUubmFtZS5sZW5ndGg7Ly/lj5bliLDmlofku7blkI3plb/luqYNCiAgICAgICAgICAgICAgICBsZXQgZmlsZUZvcm1hdCA9IGZpbGUubmFtZS5zdWJzdHJpbmcoZmlsZU5hbWUgKyAxLCBmaWxlTmFtZUxlbmd0aCk7Ly/miKoNCiAgICAgICAgICAgICAgICBpZigneGxzJyAhPSBmaWxlRm9ybWF0ICYmICd4bHN4JyAhPSBmaWxlRm9ybWF0KXsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kTm90aWNlLmVycm9yKHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn5o+Q56S6JywNCiAgICAgICAgICAgICAgICAgICAgICAgIGRlc2M6IGZpbGUubmFtZSArICcg5qC85byP5LiN5q2j56Gu44CC5Y+q6IO95LiK5Lyg5ZCO57yA5ZCN5Li6IHhsc+aIluiAhSB4bHN4IOeahOaWh+S7ticsDQogICAgICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTANCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgbGV0IHBhcmFtID0ge30NCiAgICAgICAgICAgICAgICBsZXQgZXhjZWwgPSB7ZmlsZTogZmlsZX0NCiAgICAgICAgICAgICAgICBsZXQgdGhhdCA9IHRoaXMNCiAgICAgICAgICAgICAgICB0aGF0LnNwaW5TaG93ID0gdHJ1ZQ0KICAgICAgICAgICAgICAgIGF4aW9zLnJlcXVlc3Qoew0KICAgICAgICAgICAgICAgICAgICB1cmw6ICcvYnVzaW5lc3MvaGVhdC9hY2NvdW50L2ltcG9ydCcsDQogICAgICAgICAgICAgICAgICAgIG1ldGhvZDogJ3Bvc3QnLA0KICAgICAgICAgICAgICAgICAgICBkYXRhOiBPYmplY3QuYXNzaWduKHt9LCBwYXJhbSwgZXhjZWwpDQogICAgICAgICAgICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIHRoYXQuc3BpblNob3cgPSBmYWxzZQ0KICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEuc3RyKSB7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGF0LiRNZXNzYWdlLmluZm8oew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6IHJlcy5kYXRhLnN0ciwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogMCwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbG9zYWJsZTogdHJ1ZQ0KICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQoNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuc2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5zcGluU2hvdyA9IGZhbHNlDQogICAgICAgICAgICAgICAgICAgIHRoYXQuc2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlcnIpOw0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCkNCiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgICAgICAgIH0sDQoNCiAgICAgICAgICAgIC8vIOWvvOWFpeaooeadv+S4i+i9vQ0KICAgICAgICAgICAgbG9hZFRlbXBsYXRlKCkgew0KICAgICAgICAgICAgICAgIGxldCByZXEgPSB7DQogICAgICAgICAgICAgICAgICAgIHVybCA6ICIvYnVzaW5lc3MvaGVhdC9hY2NvdW50L3RlbXBsYXRlL2xvYWQiLA0KICAgICAgICAgICAgICAgICAgICBtZXRob2QgOiAiZ2V0IiwNCiAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2VUeXBlOiAnYmxvYicsDQogICAgICAgICAgICAgICAgfTsNCiAgICAgICAgICAgICAgICBheGlvcy5maWxlKHJlcSkNCiAgICAgICAgICAgICAgICAgICAgLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc3BpblNob3cgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvbnRlbnQgPSByZXM7DQogICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2NvbnRlbnRdKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbGVOYW1lID0gIueDreWKm+WPsOi0puWvvOWFpeaooeadvy54bHN4IjsNCiAgICAgICAgICAgICAgICAgICAgICAgIGlmICgiZG93bmxvYWQiIGluIGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImEiKSkgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOmdnklF5LiL6L29DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZWxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJhIik7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxpbmsuZG93bmxvYWQgPSBmaWxlTmFtZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbGluay5zdHlsZS5kaXNwbGF5ID0gIm5vbmUiOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsaW5rLmhyZWYgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQoZWxpbmspOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsaW5rLmNsaWNrKCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgVVJMLnJldm9rZU9iamVjdFVSTChlbGluay5ocmVmKTsgLy8g6YeK5pS+VVJMIOWvueixoQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoZWxpbmspOw0KICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBJRTEwK+S4i+i9vQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hdmlnYXRvci5tc1NhdmVCbG9iKGJsb2IsIGZpbGVOYW1lKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIG1vdW50ZWQoKSB7DQogICAgICAgICAgICB0aGlzLnZlcnNpb24gPSBpbmRleERhdGEudmVyc2lvbg0KICAgICAgICAgICAgdGhpcy50YkFjY291bnQuY29sdW1ucyA9IHRoaXMudGJBY2NvdW50LnRhaWxDb2x1bW4uY29uY2F0KHRoaXMudGJBY2NvdW50LnBob3RvQ29sdW1uKS5jb25jYXQodGhpcy50YkFjY291bnQucmVtYXJrQ29sdW1uKTsNCiAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpcw0KICAgICAgICAgICAgZ2V0VXNlckJ5VXNlclJvbGUoKS50aGVuKHJlcyA9PiB7Ly/moLnmja7mnYPpmZDojrflj5bliIblhazlj7gNCiAgICAgICAgICAgICAgICB0aGF0LmNvbXBhbmllcyA9IHJlcy5kYXRhLmNvbXBhbmllczsNCiAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5pc0NpdHlBZG1pbiA9PSB0cnVlIHx8IHJlcy5kYXRhLmlzUHJvQWRtaW4gPT0gdHJ1ZSB8fCByZXMuZGF0YS5pc1N1YkFkbWluID09IHRydWUpew0KICAgICAgICAgICAgICAgICAgICB0aGF0LmlzQWRtaW4gPSB0cnVlOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBnZXRDb3VudHJ5c2RhdGEoe29yZ0NvZGU6cmVzLmRhdGEuY29tcGFuaWVzWzBdLmlkfSkudGhlbihyZXMgPT4gey8v5qC55o2u5p2D6ZmQ6I635Y+W5omA5bGe6YOo6ZeoDQogICAgICAgICAgICAgICAgICAgIHRoYXQuZGVwYXJ0bWVudHMgPSByZXMuZGF0YTsNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5nZXRVc2VyRGF0YSgpOw0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICB9DQo="}, {"version": 3, "sources": ["addHeatAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addHeatAccount.vue", "sourceRoot": "src/view/account", "sourcesContent": ["<style lang=\"less\">\r\n    .mytable .ivu-table-cell{\r\n        padding-left: 5px;\r\n        padding-right: 5px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: normal;\r\n        word-break: break-all;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .accountEs .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .accountEs .header-bar-show {\r\n        max-height: 300px;\r\n        padding-top: 14px;\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .accountEs .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n\r\n\r\n    .mytable .myspan{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block\r\n    }\r\n    .mytable .errorStle{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block;\r\n        color:red;\r\n    }\r\n    .mytable .ivu-table-row{\r\n        max-height: 370px!important;\r\n    }\r\n</style>\r\n<template>\r\n    <div>\r\n        <div class=\"accountEs\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n                    <Row>\r\n                        <!-- @on-change='accountnoChange' -->\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.accountno\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{ item.name }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"关键字:\" prop=\"heatUseBody\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"accountObj.heatUseBody\" placeholder=\"用能主体关键字模糊查询\" :style=\"formItemWidth\" />\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.company\" @on-change=\"selectChange(accountObj.company)\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"accountObj.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly :style=\"formItemWidth\"/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <div  align=\"right\">\r\n                        <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n                        <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n                    </div>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <div>\r\n                <Row>\r\n                    <Col span=\"12\">\r\n                        <Page size=\"small\" :total=\"pageTotal\" :current=\"pageNum\" :page-size=\"pageSize\" show-elevator show-sizer show-total\r\n                              placement=\"top\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n                    </Col>\r\n                    <Col span=\"12\">\r\n                        <div align=\"right\" class=\"account\">\r\n                            <Button type=\"primary\" @click=\"addNewCoalAccount\">新增</Button>\r\n                            <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n                            <Button type=\"error\" @click=\"remove\">删除</Button>\r\n                            <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n                                <Button type='info' style=\"margin-left: 5px\">加入归集单\r\n                                    <Icon type='ios-arrow-down'></Icon>\r\n                                </Button>\r\n                                <DropdownMenu slot='list'>\r\n                                    <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n                                    <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n                                </DropdownMenu>\r\n                            </Dropdown>\r\n                            <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n                            <Button type=\"primary\" @click=\"loadTemplate\">导入模板下载</Button>\r\n                            <Upload style=\"float:right;\" :on-format-error=\"handleFormatError\"\r\n                                    :before-upload='onExcelUpload' :on-progress=\"handleProgress\"\r\n                                    :on-success=\"handleUploadSuccess\" :max-size=\"10240\" action=\"_blank\"\r\n                                    accept=\".csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\r\n                                    :format=\"['xls','xlsx']\">\r\n                                <Button icon='ios-cloud-upload'>导入</Button>\r\n                            </Upload>\r\n                        </div>\r\n                    </Col>\r\n                </Row>\r\n            </div>\r\n            <Table ref=\"accountEsTable\"\r\n                   border\r\n                   :columns=\"tbAccount.tailColumn\"\r\n                   :data=\"tbAccount.data\"\r\n                   class=\"mytable\">\r\n                <!--备注-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=500 v-model=\"editremark\" :ref=\"'remark'+index+10\" type=\"text\" @on-blur=\"setremark\"\r\n                               v-if=\"editIndex === index && columnsIndex === 10\"/>\r\n                        <Tooltip placement=\"left\" max-width=\"600\" v-else>\r\n                            <span :class=\"myStyle[index].remark\" style=\"width: 60px\" @click=\"selectCall(row,index,10,'remark')\">\r\n                                {{ ellipsis(row.remark) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                    <div v-else>\r\n                        <Tooltip placement=\"bottom\" max-width=\"200\">\r\n                            <span>{{ ellipsis(row.remark)}}</span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--用能主体-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"heatUseBody\" v-if=\"row.total == null\">\r\n                    <div>\r\n                        <Input :maxlength=100 v-model=\"editHeatUseBody\" :ref=\"'heatUseBody'+index+3\" type=\"text\" @on-blur=\"setHeatUseBody\"\r\n                               v-if=\"editIndex === index && columnsIndex === 3\"/>\r\n                            <!-- <span v-else :class=\"myStyle[index].heatUseBody\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\"  @click=\"selectCall(row,index,3,'heatUseBody')\">{{ ellipsis(row.heatUseBody) }}</span> -->\r\n\r\n                        <Tooltip placement=\"right\" max-width=\"200\" v-else>\r\n                            <span :class=\"myStyle[index].heatUseBody\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,3,'heatUseBody')\">\r\n                                {{ ellipsis(row.heatUseBody) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.heatUseBody }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--开始时间-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"startDate\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'startDate'+index+1\" type=\"text\" v-model=\"editStartDate\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 1\" />\r\n                    <span :class=\"myStyle[index].startDate\" @click=\"selectCall(row,index,1,'startDate')\" v-else>{{ row.startDate }}</span>\r\n                </template>\r\n                <!--结束时间-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"endDate\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'endDate'+index+2\" type=\"text\" v-model=\"editEndDate\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 2\" />\r\n                    <span :class=\"myStyle[index].endDate\" @click=\"selectCall(row,index,2,'endDate')\" v-else>{{ row.endDate }}</span>\r\n                </template>\r\n                <!--采暖面积-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"heatAreaSize\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'heatAreaSize'+index+4\" type=\"text\" v-model=\"editHeatAreaSize\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 4\" />\r\n                    <span :class=\"myStyle[index].heatAreaSize\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,4,'heatAreaSize')\" v-else>{{ row.heatAreaSize }}</span>\r\n                </template>\r\n                <!--热力-->\r\n                <!-- <template slot-scope=\"{ row, index }\" slot=\"heatAmount\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'heatAmount'+index+5\" type=\"text\" v-model=\"editHeatAmount\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 5\" />\r\n                    <span :class=\"myStyle[index].heatAmount\" @click=\"selectCall(row,index,5,'heatAmount')\" v-else>{{ row.heatAmount }}</span>\r\n                </template> -->\r\n                <!--单价-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"unitPrice\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'unitPrice'+index+6\" type=\"text\" v-model=\"editUnitPrice\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 6\" />\r\n                    <span :class=\"myStyle[index].unitPrice\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,6,'unitPrice')\" v-else>{{ row.unitPrice }}</span>\r\n                </template>\r\n                <!--票据类型-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"ticketImportType\" v-if=\"row.total == null\">\r\n                    <Select :ref=\"'ticketImportType'+index+7\" type=\"text\" v-model=\"editTicketType\" @on-change=\"setticketImportType\"\r\n                            v-if=\"editIndex === index && columnsIndex === 7\" transfer=\"true\">\r\n                        <Option value=\"专票\" label=\"专票\"></Option>\r\n                        <Option value=\"普票\" label=\"普票\"></Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].ticketImportType\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,7,'ticketImportType')\" v-else>{{ row.ticketImportType }}</span>\r\n                </template>\r\n                <!--专票税率-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"taxRateShow\" v-if=\"row.total == null\">\r\n                    <Select :ref=\"'taxRateShow'+index+8\" type=\"text\" v-model=\"editTaxRate\" @on-change=\"settaxrate\"\r\n                            v-if=\"editIndex === index && columnsIndex === 8\" transfer=\"true\">\r\n                        <Option value=\"1\">1</Option>\r\n                        <Option value=\"3\">3</Option>\r\n                        <Option value=\"6\">6</Option>\r\n                        <Option value=\"13\">13</Option>\r\n                        <Option value=\"16\">16</Option>\r\n                        <Option value=\"17\">17</Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].taxRateShow\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,8,'taxRateShow')\" v-else>{{ row.taxRateShow }}</span>\r\n                </template>\r\n                <!--其他费用-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"otherFee\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'otherFee'+index+9\" type=\"text\" v-model=\"editOtherMoney\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 9\" />\r\n                    <span v-else :class=\"myStyle[index].otherFee\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,9,'otherFee')\">{{ row.otherFee }}</span>\r\n                </template>\r\n            </Table>\r\n            <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n        </div>\r\n        <div>\r\n            <add-bill-per ref=\"addBillPer\"\r\n                          v-on:refreshList=\"refresh\"></add-bill-per >\r\n            <completed-pre-modal ref=\"completedPre\" v-on:refreshList=\"refresh\"></completed-pre-modal>\r\n            <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport {\r\n    _verify_StartDate1,\r\n    judgeNumber,\r\n    _verify_EndDate1,\r\n    _verify_PrevTotalReadings,\r\n    _verify_CurTotalReadings,\r\n    other_no_ammeteror_protocol,\r\n    self_no_ammeteror_protocol,\r\n    HFL_ammeteror,\r\n    judging_editability,\r\n    judging_editability1,\r\n    _verify_Money,\r\n    _calculateUsedReadings,\r\n    _calculateTotalReadings,\r\n    _calculateUnitPriceByUsedMoney,\r\n    _calculateAccountMoney,\r\n    _calculateQuotereadingsratio,\r\n    requiredFieldValidator,\r\n    countTaxamount,\r\n    calculateActualMoney,\r\n    judge_negate,\r\n    judge_recovery,\r\n    judge_yb,\r\n    unitpirceMin,\r\n    unitpirceMax\r\n} from '@/view/account/PowerAccountController';\r\n    import {\r\n        saveHeatAccount,\r\n        removeHeatAccount,\r\n        selectHeatIds\r\n    } from '@/api/coalHeatOilAccount';\r\n    import checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\n    import checkResult from \"@/view/account/check/checkResult\";\r\n    import alarmCheck from \"@/view/account/check/alarmCheck\";\r\n    import {getDates,testNumber,} from '@/view/account/powerAccountHelper';\r\n    import axios from '@/libs/api.request';\r\n    import SelectAmmeter from \"./selectAmmeter\";\r\n    import {_verify_FeeStartDate} from '@/view/account/PowerAccountEs';\r\n    import AddBillPer from \"./addHeatBillPreModal\";\r\n    import {reJoinBillpre} from '@/api/accountBillPer';\r\n    import {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\n    import CompletedPreModal from \"./completedPreModal\";\r\n    import indexData from '@/config/index'\r\n    import CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import {getUserdata,getUserByUserRole,getCountrysdata,getCountryByUserId} from '@/api/basedata/ammeter.js'\r\n    let dates=getDates();\r\n    export default {\r\n        name: 'addCoalAccount',\r\n        components: {alarmCheck, checkResult, checkResultAndResponse,CompletedPreModal, SelectAmmeter,AddBillPer,CountryModal},\r\n        data() {\r\n            let photo = (h, {row, index}) => {\r\n                let that = this\r\n                let str = ''\r\n                if (row.projectname != '小计' && row.projectname != '合计') {\r\n                    str = '上传附件'\r\n                }\r\n                return h(\"div\", [h(\"u\", {\r\n                    on: {\r\n                        click() {\r\n                            //打开弹出框\r\n                            if (row.projectname != '小计' && row.projectname != '合计') {\r\n                                that.uploadFile(row)\r\n                            }\r\n                        }\r\n                    }\r\n                }, str)]);\r\n            };\r\n            return {\r\n                submit:[],\r\n                submit2:[],\r\n                showCheckModel:false,\r\n                showJhModel:false,\r\n                showAlarmModel:false,\r\n                formItemWidth: widthstyle,\r\n                version:'',\r\n                dateList:dates,\r\n                filterColl: true,//搜索面板展开\r\n                editIndex: -1,//当前编辑行\r\n                columnsIndex:-1,//当前编辑列\r\n                myStyle:[],//样式\r\n                editStartDate:'',\r\n                editEndDate:'',\r\n                editHeatUseBody:'',\r\n                editHeatAreaSize:'',\r\n                editHeatAmount:'',\r\n                editUnitPrice:'',\r\n                editTicketType:'',\r\n                editTaxRate:'',\r\n                editOtherMoney:'',\r\n                spinShow:false,//遮罩\r\n                categorys:[],//描述类型\r\n                editremark:'',\r\n                accountStatus:[],\r\n                companies:[],\r\n                coalTypes: [],\r\n                coalUseTypes: [],\r\n                departments:[],\r\n                isAdmin:false,\r\n                company:null,//用户默认公司\r\n                country:null,//用户默认所属部门\r\n                countryName:null,//用户默认所属部门\r\n                accountObj:{\r\n                    accountno:dates[1].code,//期号,默认当前月\r\n                    company:\"\",//分公司\r\n                    country:\"\",//所属部门\r\n                    coalUseBody:null,//用能主体\r\n                    countryName: \"\",\r\n                },\r\n                tbAccount: {\r\n                    loading: true,\r\n                    columns: [],\r\n                    tailColumn: [\r\n                        {type: 'selection', width: 60, align: 'center',},\r\n                        {\r\n                            title: \"期号\",\r\n                            key: \"accountNo\",\r\n                            align: \"center\",\r\n                            width: 90,\r\n                        },\r\n                        {\r\n                            title: \"开始时间\",\r\n                            slot: \"startDate\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"结束时间\",\r\n                            slot: \"endDate\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"用能主体\",\r\n                            slot: \"heatUseBody\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"采暖面积(㎡)\",\r\n                            slot: \"heatAreaSize\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"热力(百万千焦)\",\r\n                            // slot: \"heatAmount\",\r\n                            key: \"heatAmount\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {\r\n                            title: \"单价(元/平方米)\",\r\n                            slot: \"unitPrice\",\r\n                            // key: \"unitPrice\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"票据类型\",\r\n                            slot: \"ticketImportType\",\r\n                            align: \"center\",\r\n                            width: 60,\r\n                        },\r\n                        {\r\n                            title: \"普票含税金额(元)\",\r\n                            key: \"ticketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票含税金额(元)\",\r\n                            key: \"taxTicketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票税率（%）\",\r\n                            slot: \"taxRateShow\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票税额\",\r\n                            key: \"taxAmount\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"其他(元)\",\r\n                            slot: \"otherFee\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"实缴费用(元)含税\",\r\n                            key: \"paidMoney\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        // {title: \"附件\", align: \"center\", render: photo, width: 100},\r\n                        {title: \"备注\", slot: \"remark\",align: \"center\", width: 150},\r\n                    ],\r\n                    data: [],\r\n                },\r\n                pageTotal: 0,\r\n                pageNum: 1,\r\n                pageSize: 10,//当前页\r\n            }\r\n        },\r\n        methods: {\r\n            selectChange(){\r\n                let that = this;\r\n                if (that.accountObj.company != undefined) {\r\n                    if(that.accountObj.company == \"-1\"){\r\n                        that.accountObj.country = -1;\r\n                        that.accountObj.countryName = null;\r\n                    }else{\r\n                        getCountryByUserId(that.accountObj.company).then(res => {\r\n                            if(res.data.departments.length != 0){\r\n                                that.accountObj.country = res.data.departments[0].id;\r\n                                that.accountObj.countryName = res.data.departments[0].name;\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            },\r\n            //选择所属部门开始\r\n            chooseResponseCenter() {\r\n                if(this.accountObj.company == null || this.accountObj.company == \"-1\" ){\r\n                    this.$Message.info(\"请先选择分公司\");return;\r\n                }\r\n                this.$refs.countryModal.choose(this.accountObj.company);//所属部门\r\n            },\r\n            getDataFromModal(data) {\r\n                this.accountObj.country = data.id;\r\n                this.accountObj.countryName = data.name;\r\n                //选择所属部门结束\r\n            },\r\n            getUserData(){\r\n                let that = this;\r\n                getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                    if(res.data.companies.length != 0){\r\n                        let companies = res.data.companies;\r\n                        if(res.data.companies[0].id == \"**********\"){\r\n                            companies = that.companies;\r\n                        }\r\n                        that.company = companies[0].id;\r\n                        that.accountObj.company = companies[0].id;\r\n                    }\r\n                    if(res.data.departments.length != 0){\r\n                        let departments = res.data.departments;\r\n                        if(res.data.companies[0].id == \"**********\" && that.departments.length != 0){\r\n                            departments = that.departments\r\n                        }\r\n                        that.country = departments[0].id;\r\n                        that.countryName = departments[0].name;\r\n                        that.accountObj.country = Number(departments[0].id);\r\n                        that.accountObj.countryName = departments[0].name;\r\n                    }\r\n\r\n                    that.pageNum = 1\r\n                    that.getAccountMessages();\r\n                });\r\n            },\r\n            searchList(){\r\n                if(this.accountObj.countryName == \"\"){\r\n                    this.accountObj.country = \"-1\";\r\n                }\r\n                this.pageNum = 1\r\n                this.getAccountMessages()\r\n            },\r\n            accountnoChange(){\r\n                this.searchList()\r\n            },\r\n            //点击保存\r\n            preserve() {\r\n                let dataL = this.$refs.accountEsTable.getSelection();\r\n                let b = false;\r\n                let array = [];\r\n                for (let i = 0; i < dataL.length; i ++) {\r\n                    b = true;\r\n                    array.push(dataL[i])\r\n                }\r\n                if(b){\r\n                    this.submitData(array);\r\n                }else {\r\n                    this.errorTips('没有可保存数据')\r\n                }\r\n            },\r\n            submitChange(indexList){\r\n                let data=[];\r\n                this.submit2.map((item,index)=>{\r\n                    indexList.map((item2)=>{\r\n                        if(index==item2){\r\n                            data.push(item)\r\n                        }\r\n                    })\r\n                });\r\n                this.submit=data\r\n            },\r\n\r\n            //提交数据\r\n            submitData(data){\r\n                let a = [];\r\n                let that=this;\r\n                if(data != null && data.length > 0){\r\n                    let number = 0;\r\n                    let submitData = [];\r\n                    let str = '';\r\n                    let accountno = this.accountObj.accountno;\r\n                    data.forEach(function (item) {\r\n                        // 校验数据\r\n                        if(item.id == null){\r\n                            item.accountno = accountno\r\n                        }\r\n                        a.push(item.id);\r\n                        submitData.push(item);\r\n                        number ++;\r\n                    });\r\n                    that.ids=a;\r\n                    if(str.length > 0){\r\n                        this.errorTips(str)\r\n                    }\r\n                    if(submitData.length > 0){\r\n                        saveHeatAccount(submitData).then((res) => {\r\n                            if (res.data.code == 0) {\r\n                                this.$Message.info({\r\n                                    content: '提示：成功保存 ' + res.data.num + ' 条数据',\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }\r\n                }\r\n            },\r\n            addNewCoalAccount() {\r\n                // let companyId = this.accountObj.company;\r\n                // let country = this.accountObj.country;\r\n                // if(companyId != null && country != null){\r\n                //     let obj = {\r\n                //         company:companyId,\r\n                //         country:country,\r\n                //         accountno:this.accountObj.accountno,\r\n                //         accountType:'1',\r\n                //         accountestype:1\r\n                //     }\r\n                // }else{\r\n                //     this.errorTips('请选择分公司和部门')\r\n                // }\r\n                const currentDate = new Date();\r\n                const currentYear = currentDate.getFullYear();\r\n                const currentMonth = currentDate.getMonth() + 1;\r\n                if (null == this.tbAccount.data) {\r\n                    this.tbAccount.data = [];\r\n                }\r\n                this.tbAccount.data.unshift({\r\n                    // accountNo: this.accountObj.accountno,\r\n                    accountNo: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined) ? currentYear+\"\"+currentMonth: this.accountObj.accountno,\r\n                    // accountNo:dates[1].code, new Date(year, month, 0);\r\n                    startDate: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined)\r\n                    ?\r\n                    currentYear + \".\" + currentMonth + \".\" + \"01\"\r\n                    :\r\n                    this.accountObj.accountno.slice(0,4) + \".\" + this.accountObj.accountno.slice(4) + \".\" + \"01\",\r\n                    endDate: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined)\r\n                    ?\r\n                    currentYear + \".\" + currentMonth + \".\" +\r\n                        new Date(currentYear, currentMonth, 0).getDate()\r\n                    :\r\n                    this.accountObj.accountno.slice(0,4) + \".\" + this.accountObj.accountno.slice(4) + \".\" +\r\n                        new Date(this.accountObj.accountno.slice(0,4), this.accountObj.accountno.slice(4), 0).getDate(),\r\n                    heatUseBody: \"\",\r\n                    heatAreaSize:\"0\",\r\n                    heatAmount: \"0\",\r\n                    unitPrice:\"0\",\r\n                    ticketImportType:\"\",\r\n                    ticketMoney:\"0\",\r\n                    taxTicketMoney:\"0\",\r\n                    taxRateShow:\"\",\r\n                    taxAmount:\"0\",\r\n                    otherFee:\"0\",\r\n                    paidMoney:\"0\",\r\n                    remark:\"\",\r\n                });\r\n                this.myStyle.push({\r\n                    startDate: 'myspan',\r\n                    endDate: 'myspan',\r\n                    // curtotalreadings: 'myspan',\r\n                    heatUseBody: 'myspan',\r\n                    heatAreaSize: 'myspan',\r\n                    heatAmount: 'myspan',\r\n\r\n                    ticketImportType: 'myspan',\r\n                    ticketMoney:\"myspan\",\r\n                    taxTicketMoney:\"myspan\",\r\n                    taxRateShow: 'myspan',\r\n                    taxAmount: 'myspan',\r\n                    otherFee: 'myspan',\r\n                    paidMoney: 'myspan',\r\n                    unitPrice: 'myspan',\r\n                    remark: 'myspan',\r\n\r\n                    });\r\n            },\r\n            //验证错误弹出提示框\r\n            errorTips(str){\r\n                this.$Notice.error({\r\n                    title: '提示',\r\n                    desc: str,\r\n                    duration: 10\r\n                });\r\n            },\r\n            handlePage(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageNum = value;\r\n                this.getAccountMessages();\r\n            },\r\n            handlePageSize(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageSize = value;\r\n                this.getAccountMessages();\r\n            },\r\n            //向后台请求数据\r\n            getAccountMessages() {\r\n                let postData = this.accountObj;\r\n                postData.pageNum = this.pageNum;\r\n                postData.pageSize = this.pageSize;\r\n                let req = {\r\n                    url : \"/business/heat/account/list\",\r\n                    method : \"get\",\r\n                    params : postData\r\n                };\r\n                this.tbAccount.loading = true\r\n                axios.request(req).then(res => {\r\n                    this.tbAccount.loading = false\r\n                    if (res.data) {\r\n                        let data = res.data.rows;\r\n                        data.forEach(function (item) {\r\n                            item.editType = 0;\r\n                        })\r\n                        // data.push(this.suntotal(data))//小计\r\n                        this.tbAccount.data = data\r\n                        this.pageTotal = res.data.total || 0\r\n                        this.setMyStyle(this.tbAccount.data.length);\r\n\r\n                        this.editIndex = -1;\r\n                        this.columnsIndex = -1;\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            },\r\n            //重置\r\n            onResetHandle(){\r\n                this.accountObj = {\r\n                    accountno:null,\r\n                    company:this.company,\r\n                    heatUseBody:null,\r\n                    country:Number(this.country),\r\n                };\r\n                this.getAccountMessages()\r\n            },\r\n            remove(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                if(data == null || data.length === 0){\r\n                    this.errorTips(\"请选择要删除的数据\")\r\n                    return;\r\n                }\r\n                this.$Modal.confirm({\r\n                    title: '提示',\r\n                    content: '<p>是否确认删除选中信息？</p>',\r\n                    onOk: () => {\r\n                        let b = true;\r\n                        let ids = '';\r\n                        let total = this.pageTotal\r\n                        for(let i=0;i<data.length;i++){\r\n                            let item = data[i];\r\n                            if(item.id != null && item.id.length > 0){\r\n                                if(item.pabriid){\r\n                                    b = false;\r\n                                }\r\n                                ids += item.id + ',';\r\n                            }\r\n                        }\r\n                        this.pageTotal = total;\r\n                        if(b){\r\n                            if(ids.length > 0){\r\n                                removeHeatAccount(ids).then((res) => {\r\n                                    if (res.data.code == 0) {\r\n                                        this.$Message.success('删除成功');\r\n                                        this.getAccountMessages();\r\n                                    }\r\n                                });\r\n                            }\r\n                        }else {\r\n                            this.errorTips('选中信息中有信息还没有跟归集单解除关联，请先解除关联')\r\n                        }\r\n                    },\r\n                    onCancel: () => {\r\n                    }\r\n                });\r\n            },\r\n            openAddBillPerModal(name) {\r\n                if (name === 'current') {\r\n                    this.selectedAccount()\r\n                } else if (name === 'all') {\r\n                    this.selectedAllAccount()\r\n                }\r\n            },\r\n            //加入归集单，全部有效台账\r\n            selectedAllAccount(){\r\n                let that = this\r\n                that.spinShow = true;\r\n                selectHeatIds(this.accountObj).then(res => {\r\n                    that.spinShow = false;\r\n                    if(res.data.length == 0){\r\n                        that.errorTips('无有效数据可加入归集单')\r\n                    }else {\r\n                        let ids = [];\r\n                        for(let i=0;i<res.data.rows.length;i++){\r\n                            let item = res.data.rows[i];\r\n                            ids.push(item.id)\r\n                        }\r\n                        that.$refs.addBillPer.initAmmeter(ids, 19,this.accountObj.country);\r\n                    }\r\n                });\r\n            },\r\n            selectedAccount(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = 1;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要加入归集单的台账')\r\n                } else {\r\n                    let ids = [];\r\n                    data.forEach(function (item) {\r\n                        if(item.status === 5){\r\n                            b = 3\r\n                        }\r\n                        if(item.status === 4){\r\n                            b=4;\r\n                        }\r\n                        ids.push(item.id)\r\n                    });\r\n                    if(b === 1){\r\n                        this.$refs.addBillPer.initAmmeter(ids,19,this.accountObj.country);\r\n                    }else if(b === 2) {\r\n                        this.errorTips('选中的台账中存在临时数据，请先保存再加入归集单！')\r\n                    }else if(b===3){\r\n                        this.errorTips('退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮')\r\n                    }else if(b===4){\r\n                        this.errorTips('选择的台账有已加入归集单的台账，不能加入其他归集单')\r\n                    }\r\n                }\r\n            },\r\n            openCompletedPreModal(){\r\n                this.$refs.completedPre.initAmmeter(this.accountObj.country,2);\r\n            },\r\n            againJoin(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = true;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要重新加入归集单的台账')\r\n                } else {\r\n                    let ids = '';\r\n                    data.forEach(function (item) {\r\n                        let status = item.status;\r\n                        if(status != 5){\r\n                            b = false;\r\n                        }\r\n                        ids+= item.id +','\r\n                    });\r\n                    if(b){\r\n                        reJoinBillpre(ids).then((res) =>{\r\n                            if(res.data.code==0){\r\n                                this.$Message.info({\r\n                                    content:'提示：操作成功' ,\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }else {\r\n                        this.errorTips('只有已退回的台账才能重新加入归集单')\r\n                    }\r\n                }\r\n            },\r\n            refresh(){\r\n                let obj = this\r\n                setTimeout(function () {\r\n                    obj.getAccountMessages()\r\n                },200);\r\n            },\r\n            setticketImportType() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTicketType;\r\n                data.ticketImportType = val;\r\n                console.log(data.ticketImportType, \"data.ticketImportType\")\r\n                if(data.ticketImportType == '专票') {\r\n                    data.ticketMoney = 0;\r\n                    data.taxTicketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = data.taxRateShow*data.taxTicketMoney*1/100;\r\n                    data.editType = 1;\r\n                }else if(data.ticketImportType == '普票') {\r\n                    data.ticketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    data.taxTicketMoney = 0;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = 0;\r\n                    //  data.taxRateShow = \"\";\r\n                    data.editType = 1;\r\n                }\r\n            },\r\n            settaxrate() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxRate;\r\n                data.taxRateShow = val;\r\n                data.taxAmount = val*data.taxTicketMoney*1/100;\r\n                data.editType = 1;\r\n            },\r\n            validate(){\r\n                if(this.columnsIndex != 5){\r\n                    let val = this.enterOperate(this.columnsIndex).data;\r\n                    if(val) {\r\n                        switch (this.columnsIndex) {\r\n                            case 1:\r\n                                this.validateStartdate();\r\n                                break;\r\n                            case 2:\r\n                                this.validateEnddate();\r\n                                break;\r\n                            case 3:\r\n                                this.validateFeeStartDate();\r\n                                break;\r\n                            case 4:\r\n                                this.validateHeatAreaSize();\r\n                                break;\r\n                            case 6:\r\n                                this.validateUnitPrice();\r\n                                break;\r\n                            case 7:\r\n                                this.validateTicketImportType();\r\n                                break;\r\n                            case 8:\r\n                                this.validateTaxRateShow();\r\n                                break;\r\n                            // case 3:\r\n                            //     this.validateFeeStartDate();\r\n                            //     break;\r\n                            case 9:\r\n                                this.validateOtherMoney();\r\n                                break;\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            //验证起始时间\r\n            validateStartdate() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editStartDate;\r\n                let isDian = val[4]=='.' && val[7]=='.' || val[4]=='.' && val[6]=='.' ;\r\n                if(!isDian) {\r\n                    this.errorTips(\"开始时间格式不正确！\");\r\n                    val = \"\";\r\n                }\r\n                // debugger\r\n                // if (val != data.old_startdate) {\r\n                //     // 验证起始时间方法\r\n                //     let result = _verify_StartDate1(data, val);\r\n                //     console.log(result, \"result\");\r\n                //     if (result) {//失败就弹出提示内容，并将数据恢复初始化\r\n                //         this.errorTips(result);\r\n                //         this.myStyle[this.editIndex].startDate = \"errorStle\";\r\n                //     } else {\r\n                //         this.myStyle[this.editIndex].startDate = \"myspan\";\r\n                //         this.startModal = true;\r\n                //     }\r\n                // } else if (val == data.old_startdate) {\r\n                //     data.startDate = val;\r\n                // }else {\r\n                if(data.startDate !=\"\" && data.endDate !=\"\") {\r\n                    let riqiLengh = data.endDate.split(\".\")[2]*1 - data.startDate.split(\".\")[2]*1;\r\n                    data.heatAmount = data.heatAreaSize*riqiLengh*60*0.7*3.6/1000000;\r\n                    console.log(data.heatAmount, \"data.heatAmount\")\r\n                }\r\n                    data.startDate = val;\r\n                    data.editType = 1;\r\n                // }\r\n\r\n            },\r\n            //验证截止时间\r\n            validateEnddate() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editEndDate;\r\n                let isDian = val[4]=='.' && val[7]=='.' || val[4]=='.' && val[6]=='.' ;\r\n                if(!isDian) {\r\n                    this.errorTips(\"结束时间格式不正确！\");\r\n                    val = \"\";\r\n                }\r\n                if(data.startDate !=\"\" && data.endDate !=\"\") {\r\n                    let riqiLengh = data.endDate.split(\".\")[2]*1 - data.startDate.split(\".\")[2]*1;\r\n                    data.heatAmount = data.heatAreaSize*riqiLengh*60*0.7*3.6/1000000;\r\n                    console.log(data.heatAmount, \"data.heatAmount\")\r\n                }\r\n                // if (val != data.old_enddate) {\r\n                //     // 验证截止日期方法\r\n                //     let result = _verify_EndDate1(data, val);\r\n                //     if (result) {\r\n                //         this.errorTips(result);\r\n                //         this.myStyle[this.editIndex].endDate = \"errorStle\";\r\n                //     } else {\r\n                //         this.myStyle[this.editIndex].endDate = \"myspan\";\r\n\r\n                //         this.updateenddate(data, val)\r\n\r\n                //     }\r\n                // } else if (val == data.old_enddate) {\r\n                    data.endDate = val;\r\n                    data.editType = 1;\r\n                // }\r\n            },\r\n            validateHeatAreaSize() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editHeatAreaSize;\r\n                data.heatAreaSize = val;\r\n                console.log(data.heatAreaSize, \"data.heatAreaSize\")\r\n                data.editType = 1;\r\n                console.log(data.startDate.split(\".\")[2]*1, \"data.startDate.split(\")\r\n                console.log(data.endDate.split(\".\")[2]*1, \"data.endDate.split(\")\r\n                if(data.startDate !=\"\" && data.endDate !=\"\") {\r\n                    let riqiLengh = data.endDate.split(\".\")[2]*1 - data.startDate.split(\".\")[2]*1 + 1;\r\n                    console.log(riqiLengh, \"riqiLengh\")\r\n\r\n                    data.heatAmount = (val*riqiLengh*24*60*0.7*3.6/1000000).toFixed(6);\r\n                }\r\n                console.log(data.ticketImportType, \"data.ticketImportType\")\r\n                // debugger\r\n                if(data.ticketImportType == '专票') {\r\n                    data.ticketMoney = 0;\r\n                    data.taxTicketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = data.taxRateShow*data.taxTicketMoney*1/100;\r\n                    data.editType = 1;\r\n                }else if(data.ticketImportType == '普票') {\r\n                    data.ticketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    data.taxTicketMoney = 0;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = 0;\r\n                    data.editType = 1;\r\n                }\r\n                // else {\r\n                //     this.errorTips(\"开始或者结束时间不能为空！\");\r\n                //     data.heatAmount = \"\";\r\n                // }\r\n            },\r\n            validateTicketImportType() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTicketType;\r\n                data.ticketImportType = val;\r\n                data.editType = 1;\r\n            },\r\n            validateTaxRateShow() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxRate;\r\n                data.taxRateShow = val;\r\n                data.editType = 1;\r\n            },\r\n            validateFeeStartDate(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editFeeStartDate;\r\n                let result = _verify_FeeStartDate(data,val);\r\n                if(result){//失败就弹出提示内容，并将数据恢复初始化\r\n                    this.errorTips(result)\r\n                }else{\r\n                    data.feeStartDate = val;\r\n                    data.editType = 1;\r\n                }\r\n            },\r\n            validateUnitPrice() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editUnitPrice;\r\n                data.unitPrice = val;\r\n                console.log(data.ticketImportType, \"data.ticketImportType\")\r\n                // debugger\r\n                if(data.ticketImportType == '专票') {\r\n                    data.ticketMoney = 0;\r\n                    data.taxTicketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = data.taxRateShow*data.taxTicketMoney*1/100;\r\n                    data.editType = 1;\r\n                }else if(data.ticketImportType == '普票') {\r\n                    data.ticketMoney = data.heatAreaSize*data.unitPrice*1;\r\n                    data.taxTicketMoney = 0;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                     data.taxAmount = 0;\r\n                    data.editType = 1;\r\n                }\r\n                data.editType = 1;\r\n            },\r\n            validateOtherMoney(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOtherMoney;\r\n                if (!testNumber(val)) {\r\n                    this.errorTips('请输入数字！');\r\n                }\r\n                data.otherFee = val;\r\n                data.editType = 1;\r\n                let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                    data.paidMoney = paidMoney.toFixed(2);\r\n                // debugger\r\n            },\r\n            setremark(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editremark;\r\n                data.remark = val;\r\n                data.editType = 1;\r\n            },\r\n            setHeatUseBody(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editHeatUseBody;\r\n                data.heatUseBody = val;\r\n                data.editType = 1;\r\n            },\r\n            setMyStyle(length){\r\n                this.myStyle=[];\r\n                for(var i=0;i<length;i++){\r\n                    this.myStyle.push({\r\n                        startDate: 'myspan',\r\n                        endDate: 'myspan',\r\n                        coalUseBody:'myspan',\r\n                        feeStartDate:'myspan',\r\n                        coalAmount:'myspan',\r\n                        taxTicketMoney:'myspan',\r\n                        remark:'myspan',\r\n                    });\r\n                }\r\n            },\r\n            //span点击事件将span换成输入框并且获取焦点\r\n            selectCall(row,index,columns,str){\r\n                this.editStartDate = row.startDate;\r\n                this.editEndDate = row.endDate;\r\n                this.editHeatUseBody = row.heatUseBody;\r\n                this.editHeatAreaSize = row.heatAreaSize;\r\n                this.editHeatAmount = row.heatAmount;\r\n                this.editUnitPrice = row.unitPrice;\r\n                this.editTicketType = row.ticketImportType;\r\n                this.editTaxRate = row.taxRateShow;\r\n                this.editOtherMoney = row.otherFee;\r\n                this.editremark = row.remark;\r\n                this.editIndex = index;\r\n                this.columnsIndex = columns;\r\n                let a=this;\r\n                setTimeout(function () {\r\n                    a.$refs[str+index+columns].focus();\r\n                },200);\r\n\r\n            },\r\n            //跳转到下一格\r\n            nextCell(data){\r\n                let index = data.editIndex;\r\n                let columns = data.columnsIndex;\r\n                let row = '';\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                }else if(index > -1 && columns === 5){\r\n                    //当跳转的最后一行最后一格的时候\r\n                    if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n                        index = 0;\r\n                    }else{\r\n                        index ++;\r\n                    }\r\n                    columns = 1;\r\n                }else{\r\n                    columns += 1;\r\n                }\r\n                debugger\r\n                data.editIndex = index;\r\n                data.columnsIndex = columns;\r\n                row = data.tbAccount.data[index];\r\n                if(row){\r\n                    data.editStartDate = row.startDate;\r\n                    data.editEndDate = row.endDate;\r\n                    data.editHeatUseBody = row.heatUseBody;\r\n                    data.editHeatAreaSize = row.heatAreaSize;\r\n                    data.editHeatAmount = row.heatAmount;\r\n                    data.editUnitPrice = row.unitPrice;\r\n                    data.editTicketType = row.ticketImportType;\r\n                    data.editTaxRate = row.taxRateShow;\r\n                    data.editOtherMoney = row.otherFee;\r\n                    data.editremark = row.remark;\r\n                }\r\n                setTimeout(function () {\r\n                    data.$refs[data.enterOperate(columns).str+index+columns].focus();\r\n                },200);\r\n            },\r\n            //根据列号返回对应的列名\r\n            enterOperate(number){\r\n                let str = '';\r\n                let data = null;\r\n                switch (number) {\r\n                    case 1:\r\n                        str = 'startDate';\r\n                        data = this.editStartDate;\r\n                        break;\r\n                    case 2:\r\n                        str = 'endDate';\r\n                        data = this.editEndDate;\r\n                        break;\r\n                    case 3:\r\n                        str = 'heatUseBody'\r\n                        data = this.editHeatUseBody;\r\n                        break;\r\n                    case 4:\r\n                        str = 'heatAreaSize';\r\n                        data = this.editHeatAreaSize;\r\n                        break;\r\n                    case 5:\r\n                        str = 'heatAmount';\r\n                        data = this.editHeatAmount;\r\n                        break;\r\n                    case 6:\r\n                        str = 'unitPrice';\r\n                        data = this.editUnitPrice;\r\n                        break;\r\n                    case 7:\r\n                        str = 'editTicketType';\r\n                        data = this.ticketImportType;\r\n                        break;\r\n                    case 8:\r\n                        str = 'editTaxRate';\r\n                        data = this.taxRateShow;\r\n                        break;\r\n                    case 9:\r\n                        str = 'otherFee';\r\n                        data = this.editOtherMoney;\r\n                        break;\r\n                    case 10:\r\n                        str = 'remark';\r\n                        data = this.editremark;\r\n                        break;\r\n                }\r\n                return {str:str,data:data};\r\n            },\r\n            pred(){\r\n                var lett = this;\r\n                let index = lett.editIndex;\r\n                let columns = lett.columnsIndex;\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                    lett.editIndex = index;\r\n                    lett.columnsIndex = columns;\r\n                    setTimeout(function () {\r\n                        lett.$refs[lett.enterOperate(columns).str+index+columns].focus();\r\n                    },200);\r\n                }else{\r\n                    lett.validate()\r\n                    lett.setremark()\r\n                    lett.nextCell(lett)\r\n                }\r\n            },\r\n            ellipsis (value) {\r\n                if (!value) return ''\r\n                if (value.length > 100) {\r\n                    return value.slice(0,100) + '...'\r\n                }\r\n                return value\r\n            },\r\n            handleProgress(event, file) {\r\n                this.$Message.info({\r\n                    content: file.name + \" 正在上传。\",\r\n                });\r\n            },\r\n            handleUploadSuccess() {\r\n\r\n            },\r\n            handleFormatError(file) {\r\n                this.errorTips(\r\n                    file.name + \" 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件\"\r\n                );\r\n            },\r\n            // 导入\r\n            onExcelUpload(file) {\r\n                if (!file) {\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: '请选择要上传的文件！',\r\n                        duration: 10\r\n                    });\r\n                    return\r\n                }\r\n                let fileName = file.name.lastIndexOf(\".\");//取到文件名开始到最后一个点的长度\r\n                let fileNameLength = file.name.length;//取到文件名长度\r\n                let fileFormat = file.name.substring(fileName + 1, fileNameLength);//截\r\n                if('xls' != fileFormat && 'xlsx' != fileFormat){\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: file.name + ' 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件',\r\n                        duration: 10\r\n                    });\r\n                    return;\r\n                }\r\n                let param = {}\r\n                let excel = {file: file}\r\n                let that = this\r\n                that.spinShow = true\r\n                axios.request({\r\n                    url: '/business/heat/account/import',\r\n                    method: 'post',\r\n                    data: Object.assign({}, param, excel)\r\n                }).then((res) => {\r\n                    that.spinShow = false\r\n                    if (res.data.str) {\r\n                        that.$Message.info({\r\n                            content: res.data.str,\r\n                            duration: 0,\r\n                            closable: true\r\n                        });\r\n\r\n                        that.show = false;\r\n                    }\r\n                    this.getAccountMessages();\r\n                }).catch(err => {\r\n                    that.spinShow = false\r\n                    that.show = false;\r\n                    console.log(err);\r\n                });\r\n                this.getAccountMessages()\r\n                return false\r\n            },\r\n\r\n            // 导入模板下载\r\n            loadTemplate() {\r\n                let req = {\r\n                    url : \"/business/heat/account/template/load\",\r\n                    method : \"get\",\r\n                    responseType: 'blob',\r\n                };\r\n                axios.file(req)\r\n                    .then(res => {\r\n                        this.spinShow = false;\r\n                        const content = res;\r\n                        const blob = new Blob([content]);\r\n                        const fileName = \"热力台账导入模板.xlsx\";\r\n                        if (\"download\" in document.createElement(\"a\")) {\r\n                            // 非IE下载\r\n                            const elink = document.createElement(\"a\");\r\n                            elink.download = fileName;\r\n                            elink.style.display = \"none\";\r\n                            elink.href = URL.createObjectURL(blob);\r\n                            document.body.appendChild(elink);\r\n                            elink.click();\r\n                            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n                            document.body.removeChild(elink);\r\n                        } else {\r\n                            // IE10+下载\r\n                            navigator.msSaveBlob(blob, fileName);\r\n                        }\r\n                    });\r\n            }\r\n        },\r\n        mounted() {\r\n            this.version = indexData.version\r\n            this.tbAccount.columns = this.tbAccount.tailColumn.concat(this.tbAccount.photoColumn).concat(this.tbAccount.remarkColumn);\r\n            let that = this\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                    that.getUserData();\r\n                });\r\n            });\r\n        }\r\n    }\r\n</script>\r\n"]}]}