{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\business\\mssAccountbill\\basicMes.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\business\\mssAccountbill\\basicMes.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgY29uZmlnIGZyb20gIkAvY29uZmlnL2luZGV4IjsNCmltcG9ydCB7IHJ1bGVWYWxpZGF0ZWJpbGxCYXNpYywgd2lkdGhzdHlsZSwgbnVtYmVyUnVsZSB9IGZyb20gIi4vbXNzQWNjb3VudGJpbGxkYXRhIjsNCmltcG9ydCB7IGJsaXN0IH0gZnJvbSAiQC9saWJzL3Rvb2xzIjsNCmltcG9ydCB7IGRlZXBDbG9uZSB9IGZyb20gIkAvbGlicy91dGlsIjsNCmltcG9ydCBDaG9vc2VNb2RhbCBmcm9tICIuL2Nob29zZU1vZGFsIjsNCmltcG9ydCBhdHRhY2hGaWxlIGZyb20gIkAvdmlldy9iYXNlZGF0YS9wcm90b2NvbC9hdHRhY2hGaWxlIjsNCmltcG9ydCBpbnZvaWNlRmlsZSBmcm9tICIuL2ludm9pY2VGaWxlIjsNCmltcG9ydCBheGlvcyBmcm9tICJAL2xpYnMvYXBpLnJlcXVlc3QiOw0KaW1wb3J0IHsgYXR0Y2hMaXN0LCByZW1vdmVBdHRhY2ggfSBmcm9tICJAL2FwaS9iYXNlZGF0YS9hbW1ldGVyLmpzIjsNCmltcG9ydCB7DQogIGJpbGxWaWV3QnlJZCwNCiAgYmlsbEVkaXRCeUlkLA0KICBiaWxsRWRpdFNhdmUsDQogIGJpbGxhZGREYXRhLA0KICBnZXRDYXRlZ29yeXMsDQogIGJpbGxhZGREYXRhTG4sDQp9IGZyb20gIkAvYXBpL21zc2FjY291bnRiaWxsL2RhdGEiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJiYXNpY01lcyIsDQogIGNvbXBvbmVudHM6IHsgQ2hvb3NlTW9kYWwsIGF0dGFjaEZpbGUsaW52b2ljZUZpbGUgfSwNCiAgcHJvcHM6IFsidGRsYWJsZSJdLA0KICBkYXRhKCkgew0KICAgIGxldCByZW5kZXJTdXAgPSAoaCwgcGFyYW1zKSA9PiB7DQogICAgICB2YXIgdGhhdCA9IHRoaXM7DQogICAgICB2YXIgdmFsdWUgPSB0aGF0LnN3aXRjaFByb3AocGFyYW1zKTsgLy/ojrflj5blgLwNCiAgICAgIGlmICgic3VwcGxpZXJDb2RlIiA9PSBwYXJhbXMuY29sdW1uLmtleSB8fCAic3VwcGxpZXJOYW1lIiA9PSBwYXJhbXMuY29sdW1uLmtleSkgew0KICAgICAgICByZXR1cm4gaCgiZGl2IiwgWw0KICAgICAgICAgIGgoIklucHV0Iiwgew0KICAgICAgICAgICAgcHJvcHM6IHsNCiAgICAgICAgICAgICAgdmFsdWU6IHZhbHVlLA0KICAgICAgICAgICAgICByZWFkb25seTogdHJ1ZSwNCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICLor7fpgInmi6nkvpvlupTllYYv5a6i5oi3IiwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvbjogew0KICAgICAgICAgICAgICAib24tY2hhbmdlIihldmVudCkgew0KICAgICAgICAgICAgICAgIHRoYXQuc3dpdGNocm93UHJvcChwYXJhbXMsIGV2ZW50LnRhcmdldC52YWx1ZSk7DQogICAgICAgICAgICAgICAgdGhhdC5zdXBwbGllci5kYXRhW3BhcmFtcy5pbmRleF0gPSBwYXJhbXMucm93Ow0KICAgICAgICAgICAgICAgIHRoYXQuc2V0U3VwcGxpZXJUb2JpbGwoKTsNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgfSksDQogICAgICAgIF0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIGgoImRpdiIsIFsNCiAgICAgICAgICBoKCJJbnB1dE51bWJlciIsIHsNCiAgICAgICAgICAgIHByb3BzOiB7DQogICAgICAgICAgICAgIHZhbHVlOiB2YWx1ZSwNCiAgICAgICAgICAgICAgc3RlcDogMC4xLA0KICAgICAgICAgICAgICAvLyBtaW46IDAsDQogICAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6K+35aGr5YaZ6YeR6aKdIiwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvbjogew0KICAgICAgICAgICAgICAib24tY2hhbmdlIih2YWx1ZSkgew0KICAgICAgICAgICAgICAgIHRoYXQuc3dpdGNocm93UHJvcChwYXJhbXMsIHZhbHVlKTsNCiAgICAgICAgICAgICAgICB0aGF0LnN1cHBsaWVyLmRhdGFbcGFyYW1zLmluZGV4XSA9IHBhcmFtcy5yb3c7DQogICAgICAgICAgICAgICAgdGhhdC5zZXRTdXBwbGllclRvYmlsbCgpOw0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9KSwNCiAgICAgICAgXSk7DQogICAgICB9DQogICAgfTsNCiAgICBsZXQgcmVuZGVyU3VtID0gKGgsIHBhcmFtcykgPT4gew0KICAgICAgdmFyIHRoYXQgPSB0aGlzOw0KICAgICAgcmV0dXJuIGgoImRpdiIsIFsNCiAgICAgICAgaCgiSW5wdXROdW1iZXIiLCB7DQogICAgICAgICAgcHJvcHM6IHsNCiAgICAgICAgICAgIHZhbHVlOiBwYXJhbXMucm93LnN1bSwNCiAgICAgICAgICAgIHN0ZXA6IDAuMSwNCiAgICAgICAgICAgIC8vIG1pbjogMCwNCiAgICAgICAgICAgIHBsYWNlaG9sZGVyOiAi6K+36L6T5YWl6YeR6aKdIiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIG9uOiB7DQogICAgICAgICAgICAib24tY2hhbmdlIih2YWx1ZSkgew0KICAgICAgICAgICAgICBwYXJhbXMucm93LnN1bSA9IHZhbHVlOw0KICAgICAgICAgICAgICB0aGF0Lml0ZW1UYWJsZS5kYXRhW3BhcmFtcy5pbmRleF0gPSBwYXJhbXMucm93Ow0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9LA0KICAgICAgICB9KSwNCiAgICAgIF0pOw0KICAgIH07DQogICAgcmV0dXJuIHsNCiAgICAgIGRpc2FibGVDb21wYW55TmFtZVR4dDogZmFsc2UsDQogICAgICBjb21wYW55TmFtZUxpc3Q6IFsiQTAwNiIsICJCMDA2Il0sDQogICAgICBpc0V4aXN0S2luZEdpZnREaXNhYmxlZDogZmFsc2UsDQogICAgICB2ZXJzaW9uOiBjb25maWcudmVyc2lvbiwNCiAgICAgIGFjY291bnRDb2RlOiBudWxsLA0KICAgICAgZm9ybUl0ZW1XaWR0aDogd2lkdGhzdHlsZSwNCiAgICAgIGJpbGx0eXBlZGlzYWJsZWQ6IGZhbHNlLA0KICAgICAgZm9ybVZhbGlkOiB7IGJhc2ljTWVzRm9ybTogZmFsc2UsIGh0TWVzRm9ybTogZmFsc2UgfSwNCiAgICAgIGNvbGxhcHNldmFsdWU6IFsiMSIsICIyIiwgIjMiLCAiNCJdLA0KICAgICAgYmFzaWNNZXM6IHsNCiAgICAgICAgcGF5bWVudFR5cGU6IG51bGwsDQogICAgICAgIHBpY2tpbmdNb2RlOiBudWxsLA0KICAgICAgICBpbnZvaWNlVHlwZTogbnVsbCwNCiAgICAgICAgYmlsbHR5cGU6IG51bGwsDQogICAgICAgIGZpbGxJbk5hbWU6IG51bGwsDQogICAgICAgIGZpbGxJbkRlcDogbnVsbCwNCiAgICAgICAgZmlsbEluQ29zdENlbnRlck5hbWU6IG51bGwsDQogICAgICAgIGJ1c2loYXBwZW5kdGltZWZsYWc6IG51bGwsDQogICAgICAgIGlzR2R0ZWxJbnZvaWNlOiBudWxsLA0KICAgICAgICB0ZWxlcGhvbmU6IG51bGwsDQogICAgICAgIGZvcm1BbW91bnQ6IG51bGwsDQogICAgICAgIGJ1ZGdldHNldG5hbWU6IG51bGwsDQogICAgICAgIGhhcHBlbkRhdGU6IG51bGwsDQogICAgICAgIGNvbXBhbnlOYW1lVHh0OiBudWxsLA0KICAgICAgICBiaXpUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgaW5wdXRUYXhUdXJuU3VtOiBudWxsLA0KICAgICAgICBpbnB1dFRheFR1cm5CaXpUeXBlOiBudWxsLA0KICAgICAgICBpc0V4aXN0S2luZEdpZnQ6IG51bGwsDQogICAgICAgIGtpbmRHaWZ0VGF4U3VtOiBudWxsLA0KICAgICAgICBraW5kR2lmdFN1bTogbnVsbCwNCiAgICAgICAgaXNTdGFmZlBheW1lbnQ6IG51bGwsDQogICAgICAgIHBheXRheGF0dHI6IG51bGwsDQogICAgICAgIGlzRW1lcmdlbmN5OiBudWxsLA0KICAgICAgICBpc0lucHV0VGF4OiBudWxsLA0KICAgICAgICBhYnN0cmFjdFZhbHVlOiBudWxsLA0KICAgICAgICBjb250cmFjdG5vOiBudWxsLA0KICAgICAgICBjb250cmFjdE5hbWU6IG51bGwsDQogICAgICB9LA0KICAgICAgY2F0ZWdvcnlzOiB7fSwNCiAgICAgIHBlcnNvblR5cGU6IG51bGwsDQogICAgICBhdHRhY2hTaG93OiBmYWxzZSwNCiAgICAgIGlmSGFzVW5pb246IGZhbHNlLA0KICAgICAgc3VwcGxpZXI6IHsNCiAgICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICAgIGNvbHVtbnM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuS+m+W6lOWVhi/lrqLmiLfnvJbnoIEiLA0KICAgICAgICAgICAga2V5OiAic3VwcGxpZXJDb2RlIiwNCiAgICAgICAgICAgIHJlbmRlcjogcmVuZGVyU3VwLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLkvpvlupTllYYv5a6i5oi35ZCN56ewIiwNCiAgICAgICAgICAgIGtleTogInN1cHBsaWVyTmFtZSIsDQogICAgICAgICAgICByZW5kZXI6IHJlbmRlclN1cCwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5piv5ZCm5YWz6IGU5pa5IiwNCiAgICAgICAgICAgIGtleTogIkFwcHJvdmVNb25leSIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuS4gOiIrOe6s+eojuS6uiIsDQogICAgICAgICAgICBrZXk6ICJpc1JlbGVhc2UiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmiqXotKbph5Hpop3vvIjkuI3lkKvnqI7ku7fvvIkiLA0KICAgICAgICAgICAga2V5OiAic3VtIiwNCiAgICAgICAgICAgIHJlbmRlcjogcmVuZGVyU3VwLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLnqI7pop0iLA0KICAgICAgICAgICAga2V5OiAiaW5wdXRUYXhTdW0iLCAvLywgcmVuZGVyOiByZW5kZXJTdXANCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBkYXRhOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgc3VwcGxpZXJDb2RlOiBudWxsLA0KICAgICAgICAgICAgc3VwcGxpZXJOYW1lOiBudWxsLA0KICAgICAgICAgICAgQXBwcm92ZU1vbmV5OiBudWxsLA0KICAgICAgICAgICAgaXNSZWxlYXNlOiBudWxsLA0KICAgICAgICAgICAgc3VtOiBudWxsLA0KICAgICAgICAgICAgaW5wdXRUYXhTdW06IG51bGwsDQogICAgICAgICAgICBjZWxsQ2xhc3NOYW1lOiB7DQogICAgICAgICAgICAgIC8vIHN1cHBsaWVyQ29kZTogImRlbW8tdGFibGUtaW5mby1jZWxsLXN1bSIsDQogICAgICAgICAgICAgIC8vIHN1cHBsaWVyTmFtZTogImRlbW8tdGFibGUtaW5mby1jZWxsLXN1bSIsDQogICAgICAgICAgICAgIC8vIHN1bTogJ2RlbW8tdGFibGUtaW5mby1jZWxsLWlucHV0VGF4U3VtJw0KICAgICAgICAgICAgICAvLyAsaW5wdXRUYXhTdW06ICdkZW1vLXRhYmxlLWluZm8tY2VsbC1pbnB1dFRheFN1bScNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgdG90YWw6IDAsDQogICAgICB9LA0KICAgICAgcnVsZVZhbGlkYXRlOiBydWxlVmFsaWRhdGViaWxsQmFzaWMsDQogICAgICBydWxlVmFsaWRhdGVodDogew0KICAgICAgICAvKmNvbnRyYWN0bm86IFt7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9XSwNCiAgICAgICAgICAgICAgICAgICAgIGNvbnRyYWN0TmFtZTogW3tyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn1dKi8NCiAgICAgIH0sDQogICAgICBpdGVtVGFibGU6IHsgc2hvdzogZmFsc2UsIGRhdGE6IFtdIH0sDQogICAgICBpdGVtVGFibGUxOiB7DQogICAgICAgIHNob3c6IHRydWUsDQogICAgICAgIGRhdGE6IFtdLA0KICAgICAgICB1cmw6ICJtc3NhY2NvdW50L21zc1N1cHBsaWVySXRlbTIvbGlzdCIsDQogICAgICAgIHF1ZXJ5OiBudWxsLA0KICAgICAgICBjb2x1bW5zOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmlLbmrL7mlrnlkI3np7AiLA0KICAgICAgICAgICAga2V5OiAia29pbmgiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmlLbmrL7mlrnnsbvlnosiLA0KICAgICAgICAgICAga2V5OiAiYnZ0eXAiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLlvIDmiLfooYwiLA0KICAgICAgICAgICAga2V5OiAiYmFua2EiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLpk7booYzotKblj7ciLA0KICAgICAgICAgICAga2V5OiAiYmFua24iLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmiYDlnKjnnIEiLA0KICAgICAgICAgICAga2V5OiAicHJvdnoiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmiYDlnKjluIIiLA0KICAgICAgICAgICAga2V5OiAib3J0MDEiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmiYDlsZ7pk7booYwiLA0KICAgICAgICAgICAga2V5OiAiYnZ0eXAiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLlvIDmiLfooYzooYzlj7ciLA0KICAgICAgICAgICAga2V5OiAiYnJuY2giLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLph5Hpop3vvIjlkKvnqI7vvIkiLA0KICAgICAgICAgICAga2V5OiAic3VtIiwNCiAgICAgICAgICAgIHJlbmRlcjogcmVuZGVyU3VtLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICB9LA0KICAgICAgZmlsZVBhcmFtOiB7DQogICAgICAgIGJ1c2lJZDogIiIsDQogICAgICAgIGJ1c2lBbGlhczogIumZhOS7tijpooTmj5ApIiwNCiAgICAgICAgY2F0ZWdvcnlDb2RlOiAiZmlsZSIsDQogICAgICAgIGFyZWFDb2RlOiAibG4iLA0KICAgICAgfSwNCiAgICAgIGF0dGFjaERhdGE6IFtdLA0KICAgICAgaW52b2ljZVBhcmFtOiB7DQogICAgICAgIGJ1c2lJZDogIiIsDQogICAgICAgIGJ1c2lBbGlhczogIumZhOS7tijmiqXotKbljZXlj5HnpagpIiwNCiAgICAgICAgY2F0ZWdvcnlDb2RlOiAiZmlsZSIsDQogICAgICAgIGFyZWFDb2RlOiAibG4iLA0KICAgICAgICBpbnZvaWNlRmxhZzogIjEiDQogICAgICB9LA0KICAgICAgaW52b2ljZURhdGE6IFtdLA0KICAgICAgaXRlbVRhYmxlMjogew0KICAgICAgICBzaG93OiB0cnVlLA0KICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgdXJsOiAibXNzYWNjb3VudC9tc3NBYmNjdXN0b21lckJhbmsvbGlzdCIsDQogICAgICAgIHF1ZXJ5OiBudWxsLA0KICAgICAgICBjb2x1bW5zOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLlrqLmiLfnvJblj7ciLA0KICAgICAgICAgICAga2V5OiAia3VubnIiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmlLbmrL7mlrnnsbvlnosiLA0KICAgICAgICAgICAga2V5OiAia29pbmgiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLlvIDmiLfooYwiLA0KICAgICAgICAgICAga2V5OiAiemJhbmthIiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5omA5Zyo55yBIiwNCiAgICAgICAgICAgIGtleTogInByb3Z6IiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5omA5Zyo5biCIiwNCiAgICAgICAgICAgIGtleTogImNpdHkiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLpk7booYzotKblj7ciLA0KICAgICAgICAgICAga2V5OiAiYmFua24iLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmiYDlsZ7pk7booYwiLA0KICAgICAgICAgICAga2V5OiAiYmdydXAiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLlvIDmiLfooYzooYzlj7ciLA0KICAgICAgICAgICAga2V5OiAiYmFua2wiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLph5Hpop3vvIjlkKvnqI7vvIkiLA0KICAgICAgICAgICAga2V5OiAic3VtIiwNCiAgICAgICAgICAgIHJlbmRlcjogcmVuZGVyU3VtLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICB9LA0KICAgIH07DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDlvLrliLbmm7TmlrDmlofmnKzmoYbnmoTlgLwNCiAgICBjaGFuZ2VNZXNzYWdlKCkgew0KICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICB9LA0KICAgIGdldEZpbGVvcmdDb2RlKCkgew0KICAgICAgdGhpcy4kZW1pdCgiZ2V0RmlsZW9yZ0NvZGUiKTsNCiAgICB9LA0KICAgIGhhbmRsZUNob29zZVN1cChkYXRhKSB7DQogICAgICBpZiAoIWRhdGEpIHsNCiAgICAgICAgaWYgKHRoaXMuYmFzaWNNZXMuYWNjb3VudENvZGUgPT0gIjEwMDQwMTA3Iikgew0KICAgICAgICAgIC8vIOmTgeWhlOaKpei0piDlj6rog70g5p+l6K+i6ZOB5aGUDQogICAgICAgICAgdGhpcy4kcmVmcy5jaG9vc2VNb2RhbFN1cC5tb2RhbDEucXVlcnlwYXJhbXMubmFtZTEgPSAi5Lit5Zu96ZOB5aGUIjsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLiRyZWZzLmNob29zZU1vZGFsU3VwLmNob29zZSgxKTsgLy/miZPlvIDmqKHmgIHmoYYNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8v5Yik5pat6YCJ5oup55qE5L6b5bqU5ZWG5piv5ZCm5ZKM5bey5LiK5Lyg5Y+R56Wo55qE5L6b5bqU5ZWG5piv5ZCm5LiA6Ie0DQogICAgICAgIGlmKHRoaXMuaW52b2ljZURhdGEubGVuZ3RoID4gMCl7DQogICAgICAgICAgaWYoZGF0YS5uYW1lICE9IHRoaXMuaW52b2ljZURhdGFbMF0uaW52b2ljZVN1cHBsaWVyKXsNCiAgICAgICAgICAgIHRoaXMuJE1vZGFsLmNvbmZpcm0oew0KICAgICAgICAgICAgICB0aXRsZTogJ+S+m+W6lOWVhuehruiupOmAieaLqScsIGNvbnRlbnQ6ICc8cCBjbGFzcz0id2FybmluZyI+IOW9k+WJjemAieaLqeeahOS+m+W6lOWVhuS4juW3suS4iuS8oOWPkeelqOeahOS+m+W6lOWVhuS4jeS4gOiHtO+8jOaYr+WQpuehruiupOmAieaLqeivpeS+m+W6lOWVhjwvcD4nLCBvbk9rOiAoKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5iYXNpY01lcy5zdXBwbGllckNvZGUgPSBudWxsOw0KICAgICAgICAgICAgICAgIHRoaXMuYmFzaWNNZXMuc3VwcGxpZXJOYW1lID0gbnVsbDsNCiAgICAgICAgICAgICAgICB0aGlzLmJhc2ljTWVzLnN1cHBsaWVyQ29kZSA9IGRhdGEuaWQ7DQogICAgICAgICAgICAgICAgdGhpcy5iYXNpY01lcy5zdXBwbGllck5hbWUgPSBkYXRhLm5hbWU7DQogICAgICAgICAgICAgICAgdGhpcy5zdXBwbGllci5kYXRhWzBdLnN1cHBsaWVyQ29kZSA9IGRhdGEuaWQ7DQogICAgICAgICAgICAgICAgdGhpcy5zdXBwbGllci5kYXRhWzBdLnN1cHBsaWVyTmFtZSA9IGRhdGEubmFtZTsNCiAgICAgICAgICAgICAgICB0aGlzLnRvU2hvd1BheWVlKCk7IC8v6Kem5Y+R5pS55Y+Y5aSW6YOo5pS25qy+5Lq65L+h5oGvDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgIH0NCiAgICAgICAgfSANCiAgICAgICAgdGhpcy5iYXNpY01lcy5zdXBwbGllckNvZGUgPSBudWxsOw0KICAgICAgICB0aGlzLmJhc2ljTWVzLnN1cHBsaWVyTmFtZSA9IG51bGw7DQogICAgICAgIHRoaXMuYmFzaWNNZXMuc3VwcGxpZXJDb2RlID0gZGF0YS5pZDsNCiAgICAgICAgdGhpcy5iYXNpY01lcy5zdXBwbGllck5hbWUgPSBkYXRhLm5hbWU7DQogICAgICAgIHRoaXMuc3VwcGxpZXIuZGF0YVswXS5zdXBwbGllckNvZGUgPSBkYXRhLmlkOw0KICAgICAgICB0aGlzLnN1cHBsaWVyLmRhdGFbMF0uc3VwcGxpZXJOYW1lID0gZGF0YS5uYW1lOw0KICAgICAgICB0aGlzLnRvU2hvd1BheWVlKCk7IC8v6Kem5Y+R5pS55Y+Y5aSW6YOo5pS25qy+5Lq65L+h5oGvDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVDaG9vc2VNYXMoZGF0YSkgew0KICAgICAgaWYgKCFkYXRhKSB7DQogICAgICAgIHRoaXMuJHJlZnMuY2hvb3NlTW9kYWxTdXAuY2hvb3NlKDIpOyAvL+aJk+W8gOaooeaAgeahhg0KICAgICAgfSBlbHNlIHsNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUNob29zZUh0KGRhdGEpIHsNCiAgICAgIGlmICghZGF0YSkgew0KICAgICAgICB0aGlzLiRyZWZzLmNob29zZU1vZGFsU3VwLmNob29zZSgzKTsgLy/miZPlvIDmqKHmgIHmoYYNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuYmFzaWNNZXMuY29udHJhY3RubyA9IGRhdGEuaWQ7DQogICAgICAgIHRoaXMuYmFzaWNNZXMuY29udHJhY3ROYW1lID0gZGF0YS5uYW1lOw0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0RGF0YUZyb21Nb2RhbChkYXRhLCBmbGFnKSB7DQogICAgICBpZiAoZmxhZyA9PSAxKSB7DQogICAgICAgIHRoaXMucGVyc29uVHlwZSA9IDE7DQogICAgICAgIHRoaXMuYmFzaWNNZXMuc3VwcGxpZXJ0eXBlID0gIjEiOw0KICAgICAgICB0aGlzLmhhbmRsZUNob29zZVN1cChkYXRhKTsgLy8g5LygIHRydWUg6K6+572uIOWbnuiwg+WAvA0KICAgICAgfSBlbHNlIGlmIChmbGFnID09IDIpIHsNCiAgICAgICAgdGhpcy5wZXJzb25UeXBlID0gMjsNCiAgICAgICAgdGhpcy5iYXNpY01lcy5zdXBwbGllcnR5cGUgPSAiMiI7DQogICAgICAgIHRoaXMuaGFuZGxlQ2hvb3NlU3VwKGRhdGEpOyAvLyDkvKAgdHJ1ZSDorr7nva4g5Zue6LCD5YC8DQogICAgICB9IGVsc2UgaWYgKGZsYWcgPT0gMykgew0KICAgICAgICB0aGlzLmhhbmRsZUNob29zZUh0KGRhdGEpOyAvLyDkvKAgdHJ1ZSDorr7nva4g5Zue6LCD5YC8DQogICAgICB9DQogICAgfSwNCiAgICBzZXRUZGxhYmxlKCkgew0KICAgICAgdmFyIHR5cGUgPSB0aGlzLmJhc2ljTWVzLmJpbGx0eXBlOw0KICAgICAgdGhpcy4kZW1pdCgic2V0VGRsYWJsZSIsIHR5cGUpOw0KICAgICAgaWYgKHR5cGUgPT0gMSkgew0KICAgICAgICAvL+mAieaLqeKAnOaKpemUgOKAneaXtu+8jOOAkOS4muWKoeexu+Wei+OAkeWbuuWumuS4uuKAnOWIl+W5tuS7mOKAne+8jOOAkOS4muWKoeWcuuaZr+OAkeWbuuWumuS4uuKAnOaMgui0puW5tuS7mOasvuKAnS3miqXplIDljZXvvIzjgJDmlLbmlK/mlrnlvI/jgJHlj6/pgInigJzpm4bkuK3mlK/ku5jigJ3lkozigJzlt7LmlK/ku5gt5pys5Zyw5bey5Li75Yqo5pSv5LuY4oCdDQogICAgICAgIHRoaXMuc2V0Y2F0ZWdvcnlzQ2FzZShbMl0sIFs5XSwgWzIsIDQsIDVdKTsNCiAgICAgICAgdGhpcy5iYXNpY01lcy5wYXltZW50VHlwZSA9IDQ7IC8vIOaKpemUgO+8jOmihOS7mO+8jOaMgui0puaUr+S7mO+8jOaUtuS7mOaWueW8j+e8uuecgemDveaUueS4uuKAnOmbhuS4reaUr+S7mOKAnQ0KICAgICAgfSBlbHNlIGlmICh0eXBlID09IDIpIHsNCiAgICAgICAgLy/pgInmi6nigJzmjILotKbigJ3ml7bvvIzjgJDkuJrliqHnsbvlnovjgJHlm7rlrprigJzliJfotKbigJ3vvIzjgJDkuJrliqHlnLrmma/jgJHlm7rlrprkuLrigJzmjILotKbkuI3ku5jmrL7vvIjlkKvpooTliJfvvInigJ0t5oyC6LSm5Y2V77yM44CQ5pS25pSv5pa55byP44CR5Zu65a6a5Li64oCc5LiN5raJ5Y+K6ZO26KGM5pS25LuY4oCdDQogICAgICAgIHRoaXMuc2V0Y2F0ZWdvcnlzQ2FzZShbMF0sIFs3XSwgWzhdKTsNCiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PSAzKSB7DQogICAgICAgIC8v6YCJ5oup4oCc5oyC6LSm5pSv5LuY4oCd5pe277yM44CQ5Lia5Yqh57G75Z6L44CR5Zu65a6a5Li64oCc5LuY5qy+4oCd77yM44CQ5Lia5Yqh5Zy65pmv44CR5Zu65a6a5Li64oCc5oyC6LSm5ZCO5LuY5qy+77yI5riF5YG/5bqU5LuY5qy+77yJ4oCdIO+8jOOAkOaUtuaUr+aWueW8j+OAkeWPr+mAieKAnOmbhuS4reaUr+S7mOKAneWSjOKAnOW3suaUr+S7mC3mnKzlnLDlt7LkuLvliqjmlK/ku5jigJ0NCiAgICAgICAgdGhpcy5zZXRjYXRlZ29yeXNDYXNlKFsxXSwgWzJdLCBbMiwgNCwgNV0pOw0KICAgICAgICB0aGlzLmJhc2ljTWVzLnBheW1lbnRUeXBlID0gNDsgLy8g5oql6ZSA77yM6aKE5LuY77yM5oyC6LSm5pSv5LuY77yM5pS25LuY5pa55byP57y655yB6YO95pS55Li64oCc6ZuG5Lit5pSv5LuY4oCdDQogICAgICB9IGVsc2UgaWYgKHR5cGUgPT0gNCkgew0KICAgICAgICAvL+mAieaLqeKAnOmihOS7mOKAneaXtu+8jOOAkOS4muWKoeexu+Wei+OAkeWbuuWumuS4uuKAnOS7mOasvuKAne+8jOOAkOS4muWKoeWcuuaZr+OAkeWbuuWumuS4uuKAnOmihOS7mOasvuKAnSDvvIzjgJDmlLbmlK/mlrnlvI/jgJHlj6/pgInigJzpm4bkuK3mlK/ku5jigJ3lkozigJzlt7LmlK/ku5gt5pys5Zyw5bey5Li75Yqo5pSv5LuY4oCd77yM44CQ56Wo5o2u57G75Z6L44CR5Zu65a6a5Li64oCc5peg5Y+R56Wo4oCdDQogICAgICAgIHRoaXMuc2V0Y2F0ZWdvcnlzQ2FzZShbMV0sIFs4XSwgWzIsIDQsIDVdKTsNCiAgICAgICAgdGhpcy5iYXNpY01lcy5wYXltZW50VHlwZSA9IDQ7IC8vIOaKpemUgO+8jOmihOS7mO+8jOaMgui0puaUr+S7mO+8jOaUtuS7mOaWueW8j+e8uuecgemDveaUueS4uuKAnOmbhuS4reaUr+S7mOKAnQ0KICAgICAgICAvL+OAkOelqOaNruexu+Wei+OAkeWbuuWumuS4uuKAnOaXoOWPkeelqOKAnQ0KICAgICAgICB0aGlzLnNldENhdGVnb3J5cyh0aGlzLmNhdGVnb3J5cy5pbnZvaWNlVHlwZSwgWzldKTsNCiAgICAgICAgdGhpcy5iYXNpY01lcy5pbnZvaWNlVHlwZSA9IDk7DQogICAgICB9IGVsc2UgaWYgKHR5cGUgPT0gNSkgew0KICAgICAgICAvL+mAieaLqeKAnOmihOS7mOWGsumUgOKAneaXtu+8jOOAkOS4muWKoeexu+Wei+OAkeWbuuWumuS4uuKAnOWIl+i0puKAne+8jOOAkOS4muWKoeWcuuaZr+OAkeWbuuWumuS4uuKAnOWGsuWJjeacn+WAn+asvijpooTku5jmrL4p4oCdIO+8jOOAkOaUtuaUr+aWueW8j+OAkeWbuuWumuS4uuKAnOS4jea2ieWPiumTtuihjOaUtuS7mOKAnQ0KICAgICAgICB0aGlzLnNldGNhdGVnb3J5c0Nhc2UoWzBdLCBbMV0sIFs4XSk7DQogICAgICB9IGVsc2UgaWYgKHR5cGUgPT0gNykgew0KICAgICAgICAvL+mAieaLqeKAnOWJjeacn+mihOS7mOWGsumUgOKAneaXtu+8jOOAkOS4muWKoeexu+Wei+OAkeWbuuWumuS4uuKAnOWIl+i0puKAne+8jOOAkOS4muWKoeWcuuaZr+OAkeWbuuWumuS4uuKAnOWGsuWJjeacn+WAn+asvijpooTku5jmrL4p4oCdIO+8jOOAkOaUtuaUr+aWueW8j+OAkeWbuuWumuS4uuKAnOS4jea2ieWPiumTtuihjOaUtuS7mOKAnQ0KICAgICAgICB0aGlzLnNldGNhdGVnb3J5c0Nhc2UoWzBdLCBbMV0sIFs4XSk7DQogICAgICB9IGVsc2UgaWYgKHR5cGUgPT0gMTApIHsNCiAgICAgICAgLy/pgInmi6nigJzpooTkvLDlhrLplIDigJ3ml7bvvIzjgJDkuJrliqHnsbvlnovjgJHlm7rlrprkuLrigJzliJfotKbigJ3vvIzjgJDkuJrliqHlnLrmma/jgJHlm7rlrprkuLrigJzlhrLpooTliJco5Yay5pqC5LywKeKAnSDvvIzjgJDmlLbmlK/mlrnlvI/jgJHlm7rlrprkuLrigJzkuI3mtonlj4rpk7booYzmlLbku5jigJ0NCiAgICAgICAgdGhpcy5zZXRjYXRlZ29yeXNDYXNlKFswXSwgWzNdLCBbOF0pOw0KICAgICAgfSBlbHNlIGlmICh0eXBlID09IDkpIHsNCiAgICAgICAgLy/pgInmi6kg4oCc6aKE5Lyw4oCd5pe277yM44CQ5Lia5Yqh57G75Z6L44CR5Zu65a6a5Li64oCc5YiX6LSm4oCd77yM44CQ5Lia5Yqh5Zy65pmv44CR5Zu65a6a5Li64oCc5oyC6LSm5LiN5LuY5qy+KOWQq+mihOWIlynigJ3jgILjgJDmlLbmlK/mlrnlvI/jgJHlm7rlrprkuLrigJzkuI3mtonlj4rpk7booYzmlLbku5jigJ0NCiAgICAgICAgdGhpcy5zZXRjYXRlZ29yeXNDYXNlKFswXSwgWzddLCBbOF0pOw0KICAgICAgfSBlbHNlIGlmICh0eXBlID09IDgpIHsNCiAgICAgICAgLy/pgInmi6kg4oCc5pS25qy+4oCd5pe277yM5pSv5LuY5pa55byP5Y+q5pyJ4oCc6ZuG5Lit5pS25qy+4oCd5oiW4oCc5pys5Zyw5pS25qy+4oCdDQogICAgICAgIHRoaXMuY2xlYXJDYXRlZ29yeXModGhpcy5jYXRlZ29yeXMuaW52b2ljZVR5cGUpOyAvLyDkuI3mjqfliLYNCiAgICAgICAgaWYgKHRoaXMudmVyc2lvbiA9PSAic2MiKSB7DQogICAgICAgICAgLyppZiAoaWZIYXNVbmlvbikNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2V0Y2F0ZWdvcnlzQ2FzZShbMF0sIFs2XSwgWzYsIDddKTsNCiAgICAgICAgICAgICAgICAgICAgICAgZWxzZSovDQogICAgICAgICAgdGhpcy5zZXRjYXRlZ29yeXNDYXNlKFswXSwgWzQsIDZdLCBbNiwgN10pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuc2V0Y2F0ZWdvcnlzQ2FzZShbMF0sIFs2XSwgWzYsIDddKTsNCiAgICAgICAgICB0aGlzLmJhc2ljTWVzLmludm9pY2VUeXBlID0gOTsNCiAgICAgICAgICBpZiAodGhpcy5zdXBwbGllci5kYXRhWzBdLnN1cHBsaWVyQ29kZSA9PSBudWxsKSB7DQogICAgICAgICAgICB0aGlzLnN1cHBsaWVyLmRhdGFbMF0uc3VwcGxpZXJDb2RlID0gIjkyMjEwMDAwMDAiOw0KICAgICAgICAgICAgdGhpcy5zdXBwbGllci5kYXRhWzBdLnN1cHBsaWVyTmFtZSA9ICLmlLbmrL4t5YW25LuWIjsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PSAxMSkgew0KICAgICAgICAvL+iwg+i0pu+8muaUr+S7mOaWueW8j+S4uuKAnOS4jea2ieWPiumTtuihjOaUtuS7mOKAne+8jOS4muWKoeexu+Wei+S4uiLliJfotKbigJ3vvIzkuJrliqHlnLrmma/kuLrigJzlvoDmnaXovazplIDigJ3vvIwNCiAgICAgICAgLy8g5piO57uG5YiX6LSm5bGe5oCn77ya4oCc5LiN5L2/55So5oiQ5pys6aKE566X4oCdDQogICAgICAgIHRoaXMuc2V0Y2F0ZWdvcnlzQ2FzZShbMF0sIFs1XSwgWzhdKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuY2xlYXJDYXRlZ29yeXModGhpcy5jYXRlZ29yeXMuYml6VHlwZUNvZGUpOw0KICAgICAgICB0aGlzLmNsZWFyQ2F0ZWdvcnlzKHRoaXMuY2F0ZWdvcnlzLnBpY2tpbmdNb2RlKTsNCiAgICAgICAgdGhpcy5jbGVhckNhdGVnb3J5cyh0aGlzLmNhdGVnb3J5cy5wYXltZW50VHlwZSk7DQogICAgICAgIHRoaXMuY2xlYXJDYXRlZ29yeXModGhpcy5jYXRlZ29yeXMuaW52b2ljZVR5cGUpOw0KICAgICAgICB0aGlzLmJhc2ljTWVzLmJpelR5cGVDb2RlID0gbnVsbDsNCiAgICAgICAgdGhpcy5iYXNpY01lcy5waWNraW5nTW9kZSA9IG51bGw7DQogICAgICAgIHRoaXMuYmFzaWNNZXMucGF5bWVudFR5cGUgPSBudWxsOw0KICAgICAgICB0aGlzLmJhc2ljTWVzLmludm9pY2VUeXBlID0gbnVsbDsNCiAgICAgIH0NCiAgICAgIC8vIHRoaXMuJG5leHRUaWNrKCgpPT57DQogICAgICAvLyAgICAgdGhpcy4kcmVmc1siYmFzaWNNZXNGb3JtIl0uY2xlYXJWYWxpZGF0ZShbJ2JpbGx0eXBlJ10pOw0KICAgICAgLy8gfSkNCg0KICAgICAgLy8gdGhpcy4kcmVmcy5iYXNpY01lc0Zvcm0ucmVzZXRGaWVsZHMoKTsNCiAgICB9LA0KICAgIHNldGNhdGVnb3J5c0Nhc2UoZmFsZzEsIGZsYWcyLCBmbGFnMykgew0KICAgICAgdGhpcy5zZXRDYXRlZ29yeXModGhpcy5jYXRlZ29yeXMuYml6VHlwZUNvZGUsIGZhbGcxKTsNCiAgICAgIHRoaXMuYmFzaWNNZXMuYml6VHlwZUNvZGUgPSBmYWxnMVswXTsgLy/orr7nva4g5Lia5Yqh57G75Z6LIOm7mOiupOmAieaLqQ0KICAgICAgdGhpcy5zZXRDYXRlZ29yeXModGhpcy5jYXRlZ29yeXMucGlja2luZ01vZGUsIGZsYWcyKTsNCiAgICAgIHRoaXMuYmFzaWNNZXMucGlja2luZ01vZGUgPSBmbGFnMlswXTsgLy/orr7nva4g5Lia5Yqh5Zy65pmvIOm7mOiupOmAieaLqQ0KICAgICAgdGhpcy5zZXRDYXRlZ29yeXModGhpcy5jYXRlZ29yeXMucGF5bWVudFR5cGUsIGZsYWczKTsNCiAgICAgIHRoaXMuYmFzaWNNZXMucGF5bWVudFR5cGUgPSBmbGFnM1swXTsgLy/orr7nva4g5pS25pSv5pa55byPIOm7mOiupOmAieaLqQ0KICAgICAgLy8vLw0KICAgICAgdGhpcy5jbGVhckNhdGVnb3J5cyh0aGlzLmNhdGVnb3J5cy5pbnZvaWNlVHlwZSk7DQogICAgICAvLyB0aGlzLmJhc2ljTWVzLmludm9pY2VUeXBlID0gbnVsbDsNCiAgICB9LA0KICAgIHNldENhdGVnb3J5cyhkYXRhLCBmbGFncykgew0KICAgICAgZm9yIChsZXQgaXRlbSBvZiBkYXRhKSB7DQogICAgICAgIC8vIGlmIChmbGFncy5pbmRleE9mKGl0ZW0udHlwZUNvZGUpID4gLTEpIHsNCiAgICAgICAgaWYgKGZsYWdzLmluZGV4T2YoTnVtYmVyKGl0ZW0udHlwZUNvZGUpKSA+IC0xKSB7DQogICAgICAgICAgaXRlbS5kZWxldGVkRmxhZyA9IDA7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgaXRlbS5kZWxldGVkRmxhZyA9IDE7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8v6K6+572u5a2X5YW45YC8OuiHquacieaKpei0puWNleS4jeimgemihOaPkA0KICAgIHNldERpY3RzT3B0cyhrZXksIHZhbCkgew0KICAgICAgdGhpcy5jYXRlZ29yeXNba2V5XSA9IHRoaXMuY2F0ZWdvcnlzW2tleV0uZmlsdGVyKChkKSA9PiBkLnR5cGVDb2RlICE9IHZhbCk7DQogICAgfSwNCiAgICBjbGVhckNhdGVnb3J5cyhkYXRhKSB7DQogICAgICBmb3IgKGxldCBpdGVtIG9mIGRhdGEpIHsNCiAgICAgICAgaXRlbS5kZWxldGVkRmxhZyA9IDA7DQogICAgICB9DQogICAgfSwNCiAgICB2YWxpYmFzaWNNZXNGb3JtKGZvcm1uYW1lKSB7DQogICAgICB0aGlzLiRyZWZzW2Zvcm1uYW1lXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgdGhpcy5mb3JtVmFsaWRbZm9ybW5hbWVdID0gdmFsaWQ7DQogICAgICB9KTsNCiAgICB9LA0KICAgIHNldFN1cHBsaWVyVG9iaWxsKCkgew0KICAgICAgdGhpcy5iYXNpY01lcy5zdXBwbGllckNvZGUgPSB0aGlzLnN1cHBsaWVyLmRhdGFbMF0uc3VwcGxpZXJDb2RlOw0KICAgICAgdGhpcy5iYXNpY01lcy5zdXBwbGllck5hbWUgPSB0aGlzLnN1cHBsaWVyLmRhdGFbMF0uc3VwcGxpZXJOYW1lOw0KICAgICAgdGhpcy5iYXNpY01lcy5zdW0gPSB0aGlzLnN1cHBsaWVyLmRhdGFbMF0uc3VtOw0KICAgICAgdGhpcy5iYXNpY01lcy5pbnB1dFRheFN1bSA9IHRoaXMuc3VwcGxpZXIuZGF0YVswXS5pbnB1dFRheFN1bTsNCiAgICAgIGlmICh0aGlzLmJhc2ljTWVzLmlucHV0VGF4U3VtICE9IG51bGwpIHsNCiAgICAgICAgdGhpcy4kZW1pdCgic2V0SW5wdXRUYXhTdW0iLCB0aGlzLmJhc2ljTWVzLmlucHV0VGF4U3VtKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHN3aXRjaFByb3AocGFyYW1zKSB7DQogICAgICB2YXIgdmFsdWUgPSBudWxsOw0KICAgICAgc3dpdGNoIChwYXJhbXMuY29sdW1uLmtleSkgew0KICAgICAgICBjYXNlICJzdW0iOg0KICAgICAgICAgIHZhbHVlID0gcGFyYW1zLnJvdy5zdW07DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgImlucHV0VGF4U3VtIjoNCiAgICAgICAgICB2YWx1ZSA9IHBhcmFtcy5yb3cuaW5wdXRUYXhTdW07DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgInN1cHBsaWVyQ29kZSI6DQogICAgICAgICAgdmFsdWUgPSBwYXJhbXMucm93LnN1cHBsaWVyQ29kZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAic3VwcGxpZXJOYW1lIjoNCiAgICAgICAgICB2YWx1ZSA9IHBhcmFtcy5yb3cuc3VwcGxpZXJOYW1lOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHZhbHVlOw0KICAgIH0sDQogICAgc3dpdGNocm93UHJvcChwYXJhbXMsIHZhbHVlKSB7DQogICAgICBzd2l0Y2ggKHBhcmFtcy5jb2x1bW4ua2V5KSB7DQogICAgICAgIGNhc2UgInN1bSI6DQogICAgICAgICAgcGFyYW1zLnJvdy5zdW0gPSB2YWx1ZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAiaW5wdXRUYXhTdW0iOg0KICAgICAgICAgIHBhcmFtcy5yb3cuaW5wdXRUYXhTdW0gPSB2YWx1ZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAic3VwcGxpZXJDb2RlIjoNCiAgICAgICAgICBwYXJhbXMucm93LnN1cHBsaWVyQ29kZSA9IHZhbHVlOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICJzdXBwbGllck5hbWUiOg0KICAgICAgICAgIHBhcmFtcy5yb3cuc3VwcGxpZXJOYW1lID0gdmFsdWU7DQogICAgICAgICAgYnJlYWs7DQogICAgICB9DQogICAgfSwNCiAgICB2YWxpZEhhbmRlbCgpIHsNCiAgICAgIHZhciBtZXMgPSAiIjsNCiAgICAgIHZhciBzdXBwbGllckNvZGUgPSB0aGlzLnN1cHBsaWVyLmRhdGFbMF0uc3VwcGxpZXJDb2RlOw0KICAgICAgdmFyIGlucHV0VGF4U3VtID0gdGhpcy5zdXBwbGllci5kYXRhWzBdLmlucHV0VGF4U3VtOw0KICAgICAgdmFyIHN1bSA9IHRoaXMuc3VwcGxpZXIuZGF0YVswXS5zdW07DQogICAgICBpZiAodGhpcy5iYXNpY01lcy5pc1N0YWZmUGF5bWVudCA9PSAwICYmIHN1cHBsaWVyQ29kZSA9PSBudWxsKSB7DQogICAgICAgIG1lcyArPSAi6K+36YCJ5oup5L6b5bqU5ZWGL+WuouaItyI7DQogICAgICB9IGVsc2UgaWYgKHN1bSA9PSBudWxsKSB7DQogICAgICAgIHRoaXMuc3VwcGxpZXIuZGF0YVswXS5jZWxsQ2xhc3NOYW1lLnN1bSA9ICJkZW1vLXRhYmxlLWVycm9yIjsNCiAgICAgICAgbWVzICs9ICLkvpvlupTllYYv5a6i5oi3IOaKpei0pumHkemine+8iOS4jeWQq+eojuS7t++8ieS4jeiDveS4uuepuiI7DQogICAgICB9IGVsc2UgaWYgKHN1bSAmJiAhbnVtYmVyUnVsZS50ZXN0KHN1bSkpIHsNCiAgICAgICAgdGhpcy5zdXBwbGllci5kYXRhWzBdLmNlbGxDbGFzc05hbWUuc3VtID0gImRlbW8tdGFibGUtZXJyb3IiOw0KICAgICAgICBtZXMgKz0gIuS+m+W6lOWVhi/lrqLmiLcg5oql6LSm6YeR6aKd77yI5LiN5ZCr56iO5Lu377yJ6L6T5YWl5pyJ6K+vIjsNCiAgICAgIH0gZWxzZSBpZiAoaW5wdXRUYXhTdW0gPT0gbnVsbCkgew0KICAgICAgICB0aGlzLnN1cHBsaWVyLmRhdGFbMF0uaW5wdXRUYXhTdW0gPSAwOw0KICAgICAgICAvLyB0aGlzLnN1cHBsaWVyLmRhdGFbMF0uY2VsbENsYXNzTmFtZS5pbnB1dFRheFN1bSA9ICdkZW1vLXRhYmxlLWVycm9yJzsNCiAgICAgICAgLy8gbWVzICs9ICLkvpvlupTllYYv5a6i5oi3IOeojuminSDkuI3og73kuLrnqboiOw0KICAgICAgfSBlbHNlIGlmIChpbnB1dFRheFN1bSAmJiAhbnVtYmVyUnVsZS50ZXN0KGlucHV0VGF4U3VtKSkgew0KICAgICAgICB0aGlzLnN1cHBsaWVyLmRhdGFbMF0uY2VsbENsYXNzTmFtZS5pbnB1dFRheFN1bSA9ICJkZW1vLXRhYmxlLWVycm9yIjsNCiAgICAgICAgbWVzICs9ICLkvpvlupTllYYv5a6i5oi3IOeojuminSDovpPlhaXmnInor68iOw0KICAgICAgfQ0KICAgICAgaWYgKHN1bSAmJiBudW1iZXJSdWxlLnRlc3Qoc3VtKSkgew0KICAgICAgICB0aGlzLnN1cHBsaWVyLmRhdGFbMF0uY2VsbENsYXNzTmFtZS5zdW0gPSAiZGVtby10YWJsZS1pbmZvLWNlbGwtaW5wdXRUYXhTdW0iOw0KICAgICAgfQ0KICAgICAgaWYgKGlucHV0VGF4U3VtICYmIG51bWJlclJ1bGUudGVzdChpbnB1dFRheFN1bSkpIHsNCiAgICAgICAgdGhpcy5zdXBwbGllci5kYXRhWzBdLmNlbGxDbGFzc05hbWUuaW5wdXRUYXhTdW0gPQ0KICAgICAgICAgICJkZW1vLXRhYmxlLWluZm8tY2VsbC1pbnB1dFRheFN1bSI7DQogICAgICB9DQogICAgICByZXR1cm4gbWVzOw0KICAgIH0sDQogICAgdmFsaWRJdGVtVGFibGUoKSB7DQogICAgICBpZiAodGhpcy5pdGVtVGFibGUuZGF0YS5sZW5ndGggPT0gMCkgew0KICAgICAgICAvLyDlpJbpg6jmlLbmrL7kurog5Y+v5Li656m6DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfQ0KICAgICAgbGV0IF9pdGVtVGFibGUgPSBbXTsNCiAgICAgIGxldCBmbGFnID0gZmFsc2U7DQogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRoaXMuaXRlbVRhYmxlLmRhdGEubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgdmFyIGlrID0gdGhpcy5pdGVtVGFibGUuZGF0YVtpXTsNCiAgICAgICAgaWYgKGlrKSB7DQogICAgICAgICAgdmFyIHZhbHVlID0gaWsuc3VtOw0KICAgICAgICAgIGlmICh2YWx1ZSAmJiBudW1iZXJSdWxlLnRlc3QodmFsdWUpKSB7DQogICAgICAgICAgICBfaXRlbVRhYmxlLnB1c2godGhpcy5pdGVtVGFibGUuZGF0YVtpXSk7DQogICAgICAgICAgICBmbGFnID0gdHJ1ZTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGlmICghZmxhZykgew0KICAgICAgICB0aGlzLmNvbGxhcHNldmFsdWUgPSBbIjQiXTsNCiAgICAgICAgdGhpcy4kTWVzc2FnZS5lcnJvcigi6Iez5bCR5aGr5YaZ5LiA6aG577yI5ZCr56iO77yJ6YeR6aKdLOaIluiAhemHkemineacieivryIpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaWYgKHRoaXMucGVyc29uVHlwZSA9PSAxKSB7DQogICAgICAgICAgdGhpcy5iYXNpY01lcy5zdXBwbGllckl0ZW0yID0gX2l0ZW1UYWJsZTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmJhc2ljTWVzLmN1c3RvbWVyQmFuayA9IF9pdGVtVGFibGU7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiBmbGFnOw0KICAgIH0sDQogICAgaW52b2ljZVR5cGVDaGFuZ2UoKSB7DQogICAgICAvLyBkZWJ1Z2dlcg0KICAgICAgLy8gaWYgKHRoaXMudmVyc2lvbiA9PSAnc2MnKSB7DQogICAgICAvLyDlvZPnpajmja7nsbvlnovpgInmi6nigJzlop7lgLznqI7kuJPnlKjlj5HnpajigJ3ml7bvvIzluKblh7rigJzmmK/lkKblr7nlpJblvIDlhbfkuJPnpajigJ3pgInpobnvvIzpu5jorqTpgInmi6nmmK/vvIwNCiAgICAgIC8vIOaOp+WItuKAnOi9rOWUrumHkeminSjlkKvnqI7ku7cp4oCd5ZKM4oCc6L2s5ZSu56iO6aKd4oCd5b+F5aGr44CC5aaC5p6c6YCJ5oup5ZCm77yM5q2k5Lik6aG55pS55Li66Z2e5b+F5aGr44CCDQogICAgICAvLyBpZiAodGhpcy5iYXNpY01lcy5pbnZvaWNlVHlwZSA9PSAxKSB7DQogICAgICAvLyAgICAgdGhpcy5iYXNpY01lcy5pc0dkdGVsSW52b2ljZSA9ICIxIjsNCiAgICAgIC8vIH0gZWxzZSB7DQogICAgICAvLyAgICAgdGhpcy5iYXNpY01lcy5pc0dkdGVsSW52b2ljZSA9ICIwIjsNCiAgICAgIC8vIH0NCiAgICAgIC8vICDnpajmja7nsbvlnovpgInigJzlop7lgLznqI7kuJPnpajigJ3vvIzmmI7nu4bmnInku6PlnqvlpJbljZXkvY3nlLXotLnvvIzigJzmmK/lkKbovazllK7msLTnlLXigJ3pu5jorqTkuLrigJzmmK/igJ0NCiAgICAgIC8vICAgICB0aGlzLiRlbWl0KCJzZXRUZGxhYmxlIiwgdGhpcy5iYXNpY01lcy5iaWxsdHlwZSk7DQogICAgICAvLyB9DQogICAgICAvLyBlbHNlIHsNCiAgICAgIC8vICAgICB0aGlzLmJhc2ljTWVzLmludm9pY2VUeXBlDQogICAgICAvLyB9DQogICAgfSwNCiAgICBraW5kR2lmdFN1bUJsdXIoKSB7DQogICAgICBpZiAodGhpcy52ZXJzaW9uID09ICJzYyIpIHsNCiAgICAgICAgLy8g6L2s5ZSu56iO6aKd5qC55o2u55So5oi35aGr5YaZ55qE6L2s5ZSu6YeR6aKdKOWQq+eojuS7tynoh6rliqjorqHnrpfvvIzorqHnrpfop4TliJnvvJoNCiAgICAgICAgLy8g6L2s5ZSu56iO6aKdPei9rOWUrumHkeminSjlkKvnqI7ku7cpKjAuMTMvMS4xMw0KICAgICAgICBpZiAobnVtYmVyUnVsZS50ZXN0KHRoaXMuYmFzaWNNZXMua2luZEdpZnRTdW0pKSB7DQogICAgICAgICAgLy8vL+WSjOKAnOi9rOWUrueojumineKAnemHkemineS4jeWGjeiHquWKqOiuoeeul++8jOeUseeUqOaIt+iHquW3seWhq+WGmQ0KICAgICAgICAgIC8vIGxldCBzdW0gPSBwYXJzZUZsb2F0KHRoaXMuYmFzaWNNZXMua2luZEdpZnRTdW0pOw0KICAgICAgICAgIC8vIHRoaXMuYmFzaWNNZXMua2luZEdpZnRUYXhTdW0gPSAoc3VtICogMC4xMyAvIDEuMTMpLnRvRml4ZWQoMik7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5iYXNpY01lcy5raW5kR2lmdFN1bSA9IG51bGw7DQogICAgICAgICAgdGhpcy5iYXNpY01lcy5raW5kR2lmdFRheFN1bSA9IG51bGw7DQogICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLovazllK7ph5Hpop0o5ZCr56iO5Lu3KS0t6K+36L6T5YWl5pWw5a2XIik7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIHNldGJ1ZGdldFR5cGVEZWZhdWx0KCkgew0KICAgICAgdGhpcy4kZW1pdCgic2V0YnVkZ2V0VHlwZURlZmF1bHQiKTsNCiAgICB9LA0KICAgIHNldEF0dGFjaERhdGEoZGF0YSkgew0KICAgICAgdGhpcy5tdWx0aUZpbGVzID0gZGF0YS5kYXRhOw0KICAgICAgdGhpcy5yZW1vdmVJZHMgPSBkYXRhLmlkczsNCiAgICAgIGlmICh0aGlzLnJlbW92ZUlkcy5sZW5ndGggIT0gMCAmJiBkYXRhLnR5cGUgPT0gInJlbW92ZSIpIHsNCiAgICAgICAgdGhpcy5yZW1vdmVBdHRhY2goKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMudXBsb2FkKCk7DQogICAgICB9DQogICAgfSwNCiAgICB1cGxvYWQoKSB7DQogICAgICBpZiAodGhpcy5hdHRhY2hEYXRhLmxlbmd0aCAhPSAwICYmIHRoaXMubXVsdGlGaWxlcy5sZW5ndGggIT0gMCkgew0KICAgICAgICAvLyB0aGlzLiRNZXNzYWdlLmluZm8oIuaPkOekujrkuIrkvKDmlofku7bov4flpKflj6/og73lr7zoh7TkuIrkvKDlpLHotKXvvIEiKTsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgYXhpb3MNCiAgICAgICAgICAucmVxdWVzdCh7DQogICAgICAgICAgICB1cmw6ICIvY29tbW9uL2F0dGFjaG1lbnRzL3VwbG9hZE11bHRpRmlsZSIsDQogICAgICAgICAgICBtZXRob2Q6ICJwb3N0IiwNCiAgICAgICAgICAgIGRhdGE6IHRoaXMubXVsdGlGaWxlcywNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuZGF0YS5jb2RlICE9IDApIHsNCiAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICAgICAgICBhdHRjaExpc3QoeyBidXNpSWQ6IHRoYXQuZmlsZVBhcmFtLmJ1c2lJZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgdGhhdC5hdHRhY2hEYXRhID0gT2JqZWN0LmFzc2lnbihbXSwgcmVzLmRhdGEucm93cyk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHJlbW92ZUF0dGFjaCgpIHsNCiAgICAgIHJlbW92ZUF0dGFjaCh7IGlkczogdGhpcy5yZW1vdmVJZHMuam9pbigpIH0pLnRoZW4oKCkgPT4ge30pOw0KICAgIH0sDQogICAgc2V0SW52b2ljZURhdGEoZGF0YSkgew0KICAgICAgaWYoZGF0YS50eXBlID09ICd1cGxvYWQnKXsNCiAgICAgICAgLy/kuIrkvKDnmoTlj5HnpajnmoTkvpvlupTllYblnKjog73ogJfns7vnu5/mmK/lkKblrZjlnKggMSDlrZjlnKggMCDkuI3lrZjlnKgNCiAgICAgICAgaWYoZGF0YS5zdXBwbGllckV4aXN0ID09ICcxJyl7DQogICAgICAgICAgLy/oh6rliqjloavlhYXkvpvlupTllYbkv6Hmga8NCiAgICAgICAgICBpZih0aGlzLmJhc2ljTWVzLnN1cHBsaWVyQ29kZSAhPSBkYXRhLnN1cHBsaWVyQ29kZSAmJiB0aGlzLmJhc2ljTWVzLnN1cHBsaWVyTmFtZSAhPSBkYXRhLnN1cHBsaWVyTmFtZSl7DQogICAgICAgICAgICBpZih0aGlzLmJhc2ljTWVzLnN1cHBsaWVyQ29kZSAmJiB0aGlzLmJhc2ljTWVzLnN1cHBsaWVyTmFtZSl7DQogICAgICAgICAgICAgIC8v5b2T5bey6YCJ5oup5L6b5bqU5ZWG5pe2LOaPkOekuuaYr+WQpumcgOimgeabv+aNog0KICAgICAgICAgICAgICB0aGlzLiRNb2RhbC5jb25maXJtKHsNCiAgICAgICAgICAgICAgICB0aXRsZTogJ+abv+aNouS+m+W6lOWVhicsIGNvbnRlbnQ6ICc8cCBjbGFzcz0id2FybmluZyI+IOW9k+WJjemAieaLqeeahOS+m+W6lOWVhuS4juS4iuS8oOWPkeelqOeahOS+m+W6lOWVhuS4jeS4gOiHtO+8jOaYr+WQpuabv+aNouS+m+W6lOWVhjwvcD4nLCBvbk9rOiAoKSA9PiB7DQogICAgICAgICAgICAgICAgICB0aGlzLmJhc2ljTWVzLnN1cHBsaWVyQ29kZSA9IGRhdGEuc3VwcGxpZXJDb2RlOw0KICAgICAgICAgICAgICAgICAgdGhpcy5iYXNpY01lcy5zdXBwbGllck5hbWUgPSBkYXRhLnN1cHBsaWVyTmFtZTsNCiAgICAgICAgICAgICAgICAgIHRoaXMuc3VwcGxpZXIuZGF0YVswXS5zdXBwbGllckNvZGUgPSBkYXRhLnN1cHBsaWVyQ29kZTsNCiAgICAgICAgICAgICAgICAgIHRoaXMuc3VwcGxpZXIuZGF0YVswXS5zdXBwbGllck5hbWUgPSBkYXRhLnN1cHBsaWVyTmFtZTsNCiAgICAgICAgICAgICAgICAgIHRoaXMucGVyc29uVHlwZSA9IDE7DQogICAgICAgICAgICAgICAgICB0aGlzLnRvU2hvd1BheWVlKCk7IC8v6Kem5Y+R5pS55Y+Y5aSW6YOo5pS25qy+5Lq65L+h5oGvDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0gZWxzZXsNCiAgICAgICAgICAgICAgLy/lvZPmnKrpgInmi6nkvpvlupTllYbml7bvvIznm7TmjqXloavlhYUNCiAgICAgICAgICAgICAgdGhpcy5iYXNpY01lcy5zdXBwbGllckNvZGUgPSBkYXRhLnN1cHBsaWVyQ29kZTsNCiAgICAgICAgICAgICAgdGhpcy5iYXNpY01lcy5zdXBwbGllck5hbWUgPSBkYXRhLnN1cHBsaWVyTmFtZTsNCiAgICAgICAgICAgICAgdGhpcy5zdXBwbGllci5kYXRhWzBdLnN1cHBsaWVyQ29kZSA9IGRhdGEuc3VwcGxpZXJDb2RlOw0KICAgICAgICAgICAgICB0aGlzLnN1cHBsaWVyLmRhdGFbMF0uc3VwcGxpZXJOYW1lID0gZGF0YS5zdXBwbGllck5hbWU7DQogICAgICAgICAgICAgIHRoaXMucGVyc29uVHlwZSA9IDE7DQogICAgICAgICAgICAgIHRoaXMudG9TaG93UGF5ZWUoKTsgLy/op6blj5HmlLnlj5jlpJbpg6jmlLbmrL7kurrkv6Hmga8NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy/mj5DnpLrlj5HnpajkvpvlupTllYbkv6Hmga/kuI3lrZjlnKgNCiAgICAgICAgICB0aGlzLiRNZXNzYWdlLndhcm5pbmcoYOS4iuS8oOeahOWPkeelqOeahOS+m+W6lOWVhuS/oeaBr+S4jeWtmOWcqGApOw0KICAgICAgICB9DQogICAgICB9DQogICAgICBhdHRjaExpc3QoeyBidXNpSWQ6IHRoaXMuaW52b2ljZVBhcmFtLmJ1c2lJZCwgaW52b2ljZUZsYWc6IHRoaXMuaW52b2ljZVBhcmFtLmludm9pY2VGbGFnIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLmludm9pY2VEYXRhID0gT2JqZWN0LmFzc2lnbihbXSwgcmVzLmRhdGEucm93cyk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8v6YCJ5oup5L6b5bqU5ZWGL+S+m+W6lOWVhuacieWAvC0+5pS55Y+Y5aSW6YOo5pS25qy+5Lq65L+h5oGvDQogICAgdG9TaG93UGF5ZWUoKSB7DQogICAgICB0aGlzLml0ZW1UYWJsZS5xdWVyeSA9IG51bGw7DQogICAgICBpZiAodGhpcy5iYXNpY01lcy5zdXBwbGllckNvZGUgIT0gbnVsbCkgew0KICAgICAgICBpZiAodGhpcy5wZXJzb25UeXBlID09IDEpIHsNCiAgICAgICAgICB0aGlzLml0ZW1UYWJsZTEucXVlcnkgPSB7IGxpZm5yOiB0aGlzLmJhc2ljTWVzLnN1cHBsaWVyQ29kZSB9Ow0KICAgICAgICAgIHRoaXMuaXRlbVRhYmxlID0gZGVlcENsb25lKHRoaXMuaXRlbVRhYmxlMSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5pdGVtVGFibGUyLnF1ZXJ5ID0geyBrdW5ucjogdGhpcy5iYXNpY01lcy5zdXBwbGllckNvZGUgfTsNCiAgICAgICAgICB0aGlzLml0ZW1UYWJsZSA9IGRlZXBDbG9uZSh0aGlzLml0ZW1UYWJsZTIpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBiYXNpY01lczogew0KICAgICAgZGVlcDogdHJ1ZSwgLy/mt7Hluqbnm5HlkKwNCiAgICAgIGhhbmRsZXIodmFsLCBvbGRWYWwpIHsNCiAgICAgICAgdGhpcy50b1Nob3dQYXllZSgpOyAvL+inpuWPkeaUueWPmOWklumDqOaUtuasvuS6uuS/oeaBrw0KICAgICAgICBpZiAodGhpcy52ZXJzaW9uID09ICJzYyIpIHsNCiAgICAgICAgICAvLyDlm5vlt50NCiAgICAgICAgICAvL+aKpei0puWNleS4muWKoeWcuuaZr+S4uuKAnOaMgui0puW5tuS7mOasvuKAneaXtu+8jOelqOaNruexu+Wei+S4jeiDvemAieaLqeWIsOKAnOaXoOWPkeelqOKAnemAiemhueOAgg0KICAgICAgICAgIGlmICh2YWwucGlja2luZ01vZGUgPT0gOSkgew0KICAgICAgICAgICAgZm9yIChsZXQgaXRlbSBvZiB0aGlzLmNhdGVnb3J5cy5pbnZvaWNlVHlwZSkgew0KICAgICAgICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSA5KSB7DQogICAgICAgICAgICAgICAgaXRlbS5kZWxldGVkRmxhZyA9IDE7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgaXRlbS5kZWxldGVkRmxhZyA9IDA7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgZm9yIChsZXQgaXRlbSBvZiB0aGlzLmNhdGVnb3J5cy5pbnZvaWNlVHlwZSkgew0KICAgICAgICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSA5KSB7DQogICAgICAgICAgICAgICAgaXRlbS5kZWxldGVkRmxhZyA9IDA7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgLy/mlLbmlK/mlrnlvI/pgInmi6nigJzpm4bkuK3mlK/ku5jigJ3ml7bvvIzmmK/lkKbliqDmgKXlm7rljJbkuLrigJzmmK/igJ3vvIzkuI3lj6/kv67mlLnvvJsNCiAgICAgICAgICAvLyDpgInmi6nlhbbku5bmlLbmlK/mlrnlvI/ml7bvvIzmmK/lkKbliqDmgKXlm7rljJbkuLrigJzlkKbigJ3vvIzkuI3lj6/kv67mlLkNCiAgICAgICAgICBpZiAodmFsLnBheW1lbnRUeXBlID09IDQpIHsNCiAgICAgICAgICAgIC8v6ZuG5Lit5pSv5LuYDQogICAgICAgICAgICB2YWwuaXNFbWVyZ2VuY3kgPSAiMSI7IC8v5pivDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHZhbC5pc0VtZXJnZW5jeSA9ICIwIjsgLy/lkKYNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgaWYgKHZhbC5pc0V4aXN0S2luZEdpZnQgPT0gIjAiKSB7DQogICAgICAgICAgICB2YWwuaW5wdXRUYXhUdXJuU3VtID0gbnVsbDsNCiAgICAgICAgICAgIHZhbC5pbnB1dFRheFR1cm5CaXpUeXBlID0gbnVsbDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0sDQogICAgfSwNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICB0aGlzLiRyZWZzLmJhc2ljTWVzRm9ybS5yZXNldEZpZWxkcygpOyAvLyB0aGlzLiRyZWZzLmFkZHVzZXJmb3JtLnJlc2V0RmllbGRzKCk7DQogICAgICAvLyB0aGlzLiRyZWZzLmNob29zZU1vZGFsU3VwLnJlc2V0RmllbGRzKCk7ICAgICAgIC8vIHRoaXMuJHJlZnMuYWRkdXNlcmZvcm0ucmVzZXRGaWVsZHMoKTsNCiAgICAgIHRoaXMuJHJlZnMuaHRNZXNGb3JtLnJlc2V0RmllbGRzKCk7DQogICAgICBpZiAodGhpcy5pdGVtVGFibGUuc2hvdykgew0KICAgICAgICB0aGlzLiRyZWZzLml0ZW1UYWJsZS5yZXNldEZpZWxkcygpOyAvLyB0aGlzLiRyZWZzLmFkZHVzZXJmb3JtLnJlc2V0RmllbGRzKCk7DQogICAgICB9DQogICAgfSk7DQoNCiAgICBpZiAodGhpcy52ZXJzaW9uID09ICJsbiIpDQogICAgICBiaWxsYWRkRGF0YUxuKHRoaXMuJHJvdXRlLnF1ZXJ5LmRhdGEpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAvLyB0aGlzLmJhc2ljTWVzID0gcmVzLmRhdGE7DQogICAgICAgIGxldCBkYXRhID0gcmVzLmRhdGEgfHwge307DQogICAgICAgIE9iamVjdC5hc3NpZ24odGhpcy5iYXNpY01lcywgZGF0YSk7DQogICAgICAgIC8vIGRlYnVnZ2VyDQogICAgICAgIHRoaXMuYmFzaWNNZXMuZm9ybUFtb3VudCA9IDE7DQogICAgICAgIHRoaXMuYmFzaWNNZXMuaXNFeGlzdEtpbmRHaWZ0ID0gIjAiOw0KICAgICAgICB0aGlzLmJhc2ljTWVzLmFjY291bnRDb2RlID0gdGhpcy5hY2NvdW50Q29kZTsNCiAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbnlOYW1lVHh0KSB7DQogICAgICAgICAgdGhpcy5kaXNhYmxlQ29tcGFueU5hbWVUeHQgPSB0cnVlOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuZGlzYWJsZUNvbXBhbnlOYW1lVHh0ID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgICAgdGhpcy4kZW1pdCgic2V0QmlsbElkIiwgdGhpcy5iYXNpY01lcy5pZCk7DQogICAgICAgIHRoaXMuZmlsZVBhcmFtLmJ1c2lJZCA9IHRoaXMuYmFzaWNNZXMuaWQ7DQogICAgICAgIHRoaXMuaW52b2ljZVBhcmFtLmJ1c2lJZCA9IHRoaXMuYmFzaWNNZXMuaWQ7DQogICAgICAgIGlmICh0aGlzLnZlcnNpb24gPT0gImxuIikgdGhpcy5hdHRhY2hTaG93ID0gdHJ1ZTsNCiAgICAgICAgLy8gdGhpcy5iYXNpY01lcy5iaWxsdHlwZT1wYXJzZUludCh0aGlzLmJhc2ljTWVzLmJpbGx0eXBlKTsNCiAgICAgICAgLy8gdGhpcy5iYXNpY01lcy5pbnZvaWNlVHlwZSA9IHBhcnNlSW50KHRoaXMuYmFzaWNNZXMuaW52b2ljZVR5cGUpOw0KICAgICAgICAvLyB0aGlzLmJhc2ljTWVzLmJpelR5cGVDb2RlID0gcGFyc2VJbnQodGhpcy5iYXNpY01lcy5iaXpUeXBlQ29kZSk7DQogICAgICAgIC8vIHRoaXMuYmFzaWNNZXMucGlja2luZ01vZGUgPSBwYXJzZUludCh0aGlzLmJhc2ljTWVzLnBpY2tpbmdNb2RlKTsNCiAgICAgICAgLy8gdGhpcy5iYXNpY01lcy5pc1N0YWZmUGF5bWVudCA9IHBhcnNlSW50KHRoaXMuYmFzaWNNZXMuaXNTdGFmZlBheW1lbnQpOw0KICAgICAgICAvLw0KICAgICAgICBpZiAodGhpcy52ZXJzaW9uID09ICJsbiIgJiYgdGhpcy5iYXNpY01lcy5hY2NvdW50Q29kZSA9PSAiMTAwMzA0MDEiKSB7DQogICAgICAgICAgLy8NCiAgICAgICAgICAvLyDovr3lroHpgqPovrnopoHmsYLmjqfliLbkuIvoh6rmnInmiqXotKbljZXpgqPovrnlsY/olL3osIPmiqXotKbljZXpooTmj5DvvIzpooTmj5DlhrLplIDnmoTpgInpobkNCiAgICAgICAgICAvLyDlj6rmnInpk4HloZTmiqXotKbljZXmiY3og73pgInpooTmj5DvvIzpooTmj5DlhrLplIANCiAgICAgICAgICAvL+iHquacieeahOmihOaPkOWxj+iUve+8jOiHquaciemihOaPkOWGsumUgOW8gOedgO+8jOi/mei+ueS7jjjmnIjku73lvIDlp4voh6rmnInkuI3lhYHorrjotbDpooTmj5DljZUNCiAgICAgICAgICB0aGlzLnNldENhdGVnb3J5cyh0aGlzLmNhdGVnb3J5cy5iaWxsdHlwZSwgWzEsIDIsIDMsIDQsIDUsIDYsIDcsIDgsIDEwLCAxMV0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICBlbHNlDQogICAgICBiaWxsYWRkRGF0YSgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAvLyB0aGlzLmJhc2ljTWVzID0gcmVzLmRhdGE7DQogICAgICAgIGxldCBkYXRhID0gcmVzLmRhdGEgfHwge307DQogICAgICAgIE9iamVjdC5hc3NpZ24odGhpcy5iYXNpY01lcywgZGF0YSk7DQogICAgICAgIHRoaXMuYmFzaWNNZXMuYWNjb3VudENvZGUgPSB0aGlzLmFjY291bnRDb2RlOw0KICAgICAgICB0aGlzLiRlbWl0KCJzZXRCaWxsSWQiLCB0aGlzLmJhc2ljTWVzLmlkKTsNCiAgICAgICAgdGhpcy5maWxlUGFyYW0uYnVzaUlkID0gdGhpcy5iYXNpY01lcy5pZDsNCiAgICAgICAgdGhpcy5pbnZvaWNlUGFyYW0uYnVzaUlkID0gdGhpcy5iYXNpY01lcy5pZDsNCiAgICAgICAgaWYgKHRoaXMudmVyc2lvbiA9PSAibG4iKSB0aGlzLmF0dGFjaFNob3cgPSB0cnVlOw0KICAgICAgICAvLyB0aGlzLmJhc2ljTWVzLmJpbGx0eXBlPXBhcnNlSW50KHRoaXMuYmFzaWNNZXMuYmlsbHR5cGUpOw0KICAgICAgICAvLyB0aGlzLmJhc2ljTWVzLmludm9pY2VUeXBlID0gcGFyc2VJbnQodGhpcy5iYXNpY01lcy5pbnZvaWNlVHlwZSk7DQogICAgICAgIC8vIHRoaXMuYmFzaWNNZXMuYml6VHlwZUNvZGUgPSBwYXJzZUludCh0aGlzLmJhc2ljTWVzLmJpelR5cGVDb2RlKTsNCiAgICAgICAgLy8gdGhpcy5iYXNpY01lcy5waWNraW5nTW9kZSA9IHBhcnNlSW50KHRoaXMuYmFzaWNNZXMucGlja2luZ01vZGUpOw0KICAgICAgICAvLyB0aGlzLmJhc2ljTWVzLmlzU3RhZmZQYXltZW50ID0gcGFyc2VJbnQodGhpcy5iYXNpY01lcy5pc1N0YWZmUGF5bWVudCk7DQogICAgICAgIC8vDQogICAgICAgIGlmICh0aGlzLnZlcnNpb24gPT0gImxuIiAmJiB0aGlzLmJhc2ljTWVzLmFjY291bnRDb2RlID09ICIxMDAzMDQwMSIpIHsNCiAgICAgICAgICAvLw0KICAgICAgICAgIC8vIOi+veWugemCo+i+ueimgeaxguaOp+WItuS4i+iHquacieaKpei0puWNlemCo+i+ueWxj+iUveiwg+aKpei0puWNlemihOaPkO+8jOmihOaPkOWGsumUgOeahOmAiemhuQ0KICAgICAgICAgIC8vIOWPquaciemTgeWhlOaKpei0puWNleaJjeiDvemAiemihOaPkO+8jOmihOaPkOWGsumUgA0KICAgICAgICAgIC8v6Ieq5pyJ55qE6aKE5o+Q5bGP6JS977yM6Ieq5pyJ6aKE5o+Q5Yay6ZSA5byA552A77yM6L+Z6L655LuOOOaciOS7veW8gOWni+iHquacieS4jeWFgeiuuOi1sOmihOaPkOWNlQ0KICAgICAgICAgIHRoaXMuc2V0Q2F0ZWdvcnlzKHRoaXMuY2F0ZWdvcnlzLmJpbGx0eXBlLCBbMSwgMiwgMywgNCwgNSwgNiwgNywgOCwgMTAsIDExXSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIC8vIGdldENhdGVnb3J5cygpLnRoZW4ocmVzID0+IHsNCiAgICAvLyAgIHRoaXMuY2F0ZWdvcnlzID0gcmVzLmRhdGEuY2F0ZWdvcnlzOw0KICAgIC8vIH0pOw0KICAgIC8v55u05o6l5LuO5YmN5Y+w5Y+WDQogICAgLyogICAgICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS5kYXRhKQ0KICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMuJHJvdXRlLnF1ZXJ5LmRhdGEpOyovDQogICAgdGhpcy5jYXRlZ29yeXMgPSB7DQogICAgICBiaWxsdHlwZTogYmxpc3QoImJpbGx0eXBlIiksDQogICAgICBwYXltZW50VHlwZTogYmxpc3QoInBheW1lbnRUeXBlIiksDQogICAgICBpbnZvaWNlVHlwZTogYmxpc3QoImludm9pY2VUeXBlIiksDQogICAgICBiaXpUeXBlQ29kZTogYmxpc3QoImJpelR5cGUiKSwNCiAgICAgIHBpY2tpbmdNb2RlOiBibGlzdCgicGlja2luZ01vZGUiKSwNCiAgICAgIGlzU3RhZmZQYXltZW50OiBibGlzdCgiaXNTdGFmZlBheW1lbnQiKSwNCiAgICB9Ow0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["basicMes.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo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file": "basicMes.vue", "sourceRoot": "src/view/business/mssAccountbill", "sourcesContent": ["<style lang=\"less\">\r\n.floatright {\r\n  margin: 5px;\r\n}\r\n\r\n.ivu-table .demo-table-info-cell-sum {\r\n  /* background-color: #ff6600; */\r\n  color: #fff;\r\n}\r\n\r\n.ivu-table .demo-table-info-cell-inputTaxSum {\r\n  background-color: green;\r\n  color: #fff;\r\n}\r\n\r\n.ivu-table .demo-table-error {\r\n  background-color: red;\r\n  color: #fff;\r\n}\r\n\r\n.classNameSum {\r\n  background-color: #2db7f5;\r\n  color: #fff;\r\n}\r\n</style>\r\n<template>\r\n  <div>\r\n    <!-- <Spin size=\"large\" fix v-if=\"basicMes.id==null\"> -->\r\n    <!-- <Icon type=\"ios-loading\" class=\"demo-spin-icon-load\"></Icon> -->\r\n    <!-- </Spin> -->\r\n    <Collapse v-model=\"collapsevalue\">\r\n      <Panel name=\"1\">\r\n        基本信息\r\n        <!-- <h3 slot=\"title\">基本信息</h3> -->\r\n        <div slot=\"content\">\r\n          <Form\r\n            ref=\"basicMesForm\"\r\n            :model=\"basicMes\"\r\n            :rules=\"ruleValidate\"\r\n            :label-width=\"100\"\r\n            inline\r\n          >\r\n            <FormItem label=\"报账单类型:\" prop=\"billtype\">\r\n              <Select\r\n                @on-change=\"setTdlable\"\r\n                v-model=\"basicMes.billtype\"\r\n                :style=\"formItemWidth\"\r\n                :disabled=\"billtypedisabled\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in categorys.billtype\"\r\n                  :key=\"item.typeCode\"\r\n                  :value=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                >\r\n                  {{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"报账人:\" prop=\"fillInName\">\r\n              <cl-input v-model=\"basicMes.fillInName\" readonly :style=\"formItemWidth\" />\r\n            </FormItem>\r\n            <FormItem label=\"公司代码：\" prop=\"companyNameTxt\">\r\n              <!--                            <Input v-model=\"basicMes.companyNameTxt\" :style=\"formItemWidth\"/>-->\r\n              <Select\r\n                v-model=\"basicMes.companyNameTxt\"\r\n                :disabled=\"disableCompanyNameTxt\"\r\n                :style=\"formItemWidth\"\r\n              >\r\n                <Option v-for=\"item in companyNameList\" :value=\"item\" :key=\"item\">{{\r\n                  item\r\n                }}</Option>\r\n              </Select>\r\n              <!--                          <Input v-model=\"basicMes.companyNameTxt\" :style=\"formItemWidth\" icon=\"ios-archive\"-->\r\n              <!--                                 @on-click=\"getFileorgCode\"/>-->\r\n            </FormItem>\r\n            <FormItem label=\"报账单位(财辅):\" prop=\"fillInCostCenterName\">\r\n              <!--fillInDep-->\r\n              <Input\r\n                v-model=\"basicMes.fillInCostCenterName\"\r\n                readonly\r\n                :style=\"formItemWidth\"\r\n                placeholder=\"点击公司代码选择\"\r\n              />\r\n            </FormItem>\r\n            <FormItem label=\"报账人电话:\" prop=\"telephone\">\r\n              <cl-input\r\n                v-model=\"basicMes.telephone\"\r\n                placeholder=\"请输入电话\"\r\n                :style=\"formItemWidth\"\r\n              />\r\n            </FormItem>\r\n            <FormItem label=\"附单据张数:\" prop=\"formAmount\">\r\n              <InputNumber\r\n                v-model=\"basicMes.formAmount\"\r\n                :step=\"1\"\r\n                :min=\"1\"\r\n                placeholder=\"请输入数量\"\r\n                :style=\"formItemWidth\"\r\n              />\r\n            </FormItem>\r\n            <FormItem label=\"报账期间:\" prop=\"budgetsetname\">\r\n              <cl-date-picker\r\n                type=\"month\"\r\n                format=\"yyyy-MM\"\r\n                placeholder=\"请选择报账期间\"\r\n                v-model=\"basicMes.budgetsetname\"\r\n                :style=\"formItemWidth\"\r\n              ></cl-date-picker>\r\n            </FormItem>\r\n            <FormItem label=\"费用发生日:\" prop=\"happenDate\">\r\n              <cl-date-picker\r\n                type=\"date\"\r\n                placeholder=\"请选择费用发生日\"\r\n                v-model=\"basicMes.happenDate\"\r\n                :style=\"formItemWidth\"\r\n              ></cl-date-picker>\r\n            </FormItem>\r\n            <FormItem label=\"收支方式:\" prop=\"paymentType\">\r\n              <Select v-model=\"basicMes.paymentType\" :style=\"formItemWidth\">\r\n                <Option\r\n                  v-for=\"item in categorys.paymentType\"\r\n                  :key=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                  :value=\"item.typeCode * 1\"\r\n                  >{{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"业务类型：\" prop=\"bizTypeCode\">\r\n              <Select v-model=\"basicMes.bizTypeCode\" :style=\"formItemWidth\">\r\n                <Option\r\n                  v-for=\"item in categorys.bizTypeCode\"\r\n                  :key=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                  :value=\"item.typeCode * 1\"\r\n                  >{{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"业务场景：\" prop=\"pickingMode\">\r\n              <Select\r\n                v-model=\"basicMes.pickingMode\"\r\n                :style=\"formItemWidth\"\r\n                @on-change=\"setbudgetTypeDefault\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in categorys.pickingMode\"\r\n                  :key=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                  :value=\"item.typeCode * 1\"\r\n                  >{{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n              <!--<cl-select :value=\"basicMes.pickingMode\" category=\"pickingMode\" labelField=\"typeName\"\r\n                                       valueField=\"typeCode\" style=\"width:160px;\"/>-->\r\n            </FormItem>\r\n            <FormItem label=\"票据类型：\" prop=\"invoiceType\">\r\n              <Select\r\n                v-model=\"basicMes.invoiceType\"\r\n                :style=\"formItemWidth\"\r\n                @on-change=\"invoiceTypeChange\"\r\n              >\r\n                <Option\r\n                  v-for=\"item in categorys.invoiceType\"\r\n                  :key=\"item.typeCode\"\r\n                  :disabled=\"item.deletedFlag == 1\"\r\n                  :value=\"item.typeCode * 1\"\r\n                  >{{ item.typeName }}\r\n                </Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"转售金额(不含税价)：\"\r\n              prop=\"kindGiftSum\"\r\n              :rules=\"{\r\n                required: basicMes.isExistKindGift == '1',\r\n                message: '不能为空',\r\n                trigger: 'blur',\r\n              }\"\r\n            >\r\n              <Input\r\n                v-model=\"basicMes.kindGiftSum\"\r\n                :style=\"formItemWidth\"\r\n                @on-blur=\"kindGiftSumBlur\"\r\n                :disabled=\"basicMes.isExistKindGift == '0'\"\r\n              />\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"转售税额：\"\r\n              prop=\"kindGiftTaxSum\"\r\n              :rules=\"{\r\n                required: basicMes.isExistKindGift == '1',\r\n                message: '不能为空',\r\n                trigger: 'blur',\r\n              }\"\r\n            >\r\n              <Input\r\n                v-model=\"basicMes.kindGiftTaxSum\"\r\n                :style=\"formItemWidth\"\r\n                :disabled=\"basicMes.isExistKindGift == '0'\"\r\n              />\r\n              <!--:readonly=\"version=='sc'\"-->\r\n            </FormItem>\r\n            <!--<FormItem label=\"是否对外开具专票：\" prop=\"isGdtelInvoice\"\r\n                                  v-if=\"version=='sc' && basicMes.invoiceType == 1\">\r\n                            <RadioGroup v-model=\"basicMes.isGdtelInvoice\">\r\n                                <Radio label=\"0\">\r\n                                    <span>否</span>\r\n                                </Radio>\r\n                                <Radio label=\"1\">\r\n                                    <span>是</span>\r\n                                </Radio>\r\n                            </RadioGroup>\r\n                        </FormItem>-->\r\n            <FormItem label=\"是否员工代垫：\" prop=\"isStaffPayment\">\r\n              <RadioGroup v-model=\"basicMes.isStaffPayment\">\r\n                <Radio label=\"0\">\r\n                  <span>否</span>\r\n                </Radio>\r\n                <Radio label=\"1\">\r\n                  <span>是</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"业务发生时间:\" prop=\"busihappendtimeflag\">\r\n              <RadioGroup v-model=\"basicMes.busihappendtimeflag\">\r\n                <Radio label=\"1\">\r\n                  <Icon type=\"ios-calendar\" />\r\n                  <span>营改增日期前</span>\r\n                </Radio>\r\n                <Radio label=\"2\">\r\n                  <Icon type=\"ios-calendar-outline\" />\r\n                  <span>营改增日期后</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"是否转售水电：\" prop=\"isExistKindGift\">\r\n              <RadioGroup v-model=\"basicMes.isExistKindGift\">\r\n                <Radio label=\"0\" :disabled=\"version == 'sc'\">\r\n                  <span>否</span>\r\n                </Radio>\r\n                <Radio label=\"1\" :disabled=\"version == 'sc'\">\r\n                  <span>是</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"纳税属性：\" prop=\"paytaxattr\">\r\n              <RadioGroup v-model=\"basicMes.paytaxattr\">\r\n                <Radio label=\"1\">\r\n                  <span>属地纳税</span>\r\n                </Radio>\r\n                <Radio label=\"2\">\r\n                  <span>汇总纳税</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem label=\"是否加急：\" prop=\"isEmergency\">\r\n              <RadioGroup v-model=\"basicMes.isEmergency\">\r\n                <Radio label=\"0\" :disabled=\"version == 'sc'\">\r\n                  <span>否</span>\r\n                </Radio>\r\n                <Radio label=\"1\" :disabled=\"version == 'sc'\">\r\n                  <span>是</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"是否涉及进项税转出：\"\r\n              prop=\"isInputTax\"\r\n              v-if=\"basicMes.isExistKindGift == '0'\"\r\n            >\r\n              <RadioGroup v-model=\"basicMes.isInputTax\">\r\n                <Radio label=\"0\">\r\n                  <span>否</span>\r\n                </Radio>\r\n                <Radio label=\"1\">\r\n                  <span>是</span>\r\n                </Radio>\r\n              </RadioGroup>\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"进项税转出金额：\"\r\n              prop=\"inputTaxTurnSum\"\r\n              v-if=\"basicMes.isInputTax == 1 && basicMes.isExistKindGift == '0'\"\r\n            >\r\n              <InputNumber\r\n                v-model=\"basicMes.inputTaxTurnSum\"\r\n                :step=\"0.1\"\r\n                placeholder=\"请输入进项税转出金额\"\r\n                :style=\"formItemWidth\"\r\n              />\r\n            </FormItem>\r\n            <FormItem\r\n              label=\"进项税转出业务类型：\"\r\n              prop=\"inputTaxTurnBizType\"\r\n              v-if=\"basicMes.isInputTax == 1 && basicMes.isExistKindGift == '0'\"\r\n            >\r\n              <Select v-model=\"basicMes.inputTaxTurnBizType\" :style=\"formItemWidth\">\r\n                <Option value=\"1\">免税项目</Option>\r\n                <Option value=\"2\">非免税项目</Option>\r\n              </Select>\r\n            </FormItem>\r\n            <FormItem label=\"说明：\" prop=\"abstractValue\">\r\n              <cl-input\r\n                v-model=\"basicMes.abstractValue\"\r\n                type=\"textarea\"\r\n                style=\"width: 400px\"\r\n              />\r\n            </FormItem>\r\n          </Form>\r\n        </div>\r\n      </Panel>\r\n      <Panel name=\"2\">\r\n        供应商信息\r\n        <div slot=\"content\">\r\n          <ChooseModal ref=\"chooseModalSup\" v-on:getDataFromModal=\"getDataFromModal\" />\r\n          <Button class=\"floatright\" type=\"success\" @click=\"handleChooseSup()\"\r\n            >选择供应商</Button\r\n          >\r\n          <Button class=\"floatright\" type=\"success\" @click=\"handleChooseMas()\"\r\n            >选择客户</Button\r\n          >\r\n          <!--<span style=\"color: blue;float: right;\" v-if=\"basicMes.billtype == 9 && version == 'sc'\">#预估报账请选择供应商【G951100081】(其他支付对象-成本结算业务预估)#</span>-->\r\n          <span\r\n            style=\"color: blue; float: right\"\r\n            v-if=\"basicMes.billtype == 8 && version == 'ln'\"\r\n            >#报账【收款】供应商不是必选项#</span\r\n          >\r\n          <Table\r\n            :show-page=\"false\"\r\n            :searchable=\"false\"\r\n            ref=\"testTable\"\r\n            :columns=\"supplier.columns\"\r\n            :data=\"supplier.data\"\r\n          ></Table>\r\n        </div>\r\n      </Panel>\r\n      <Panel name=\"Panel6\"\r\n        >发票信息\r\n        <div slot=\"content\">\r\n          <Row class=\"form-panel\">\r\n            <invoice-file\r\n              :param=\"invoiceParam\"\r\n              :attachData=\"invoiceData\"\r\n              v-on:setAttachData=\"setInvoiceData\"\r\n            />\r\n          </Row>\r\n        </div>\r\n      </Panel>\r\n      <Panel name=\"3\">\r\n        合同信息\r\n        <!--  <h3 slot=\"title\">合同信息</h3> -->\r\n        <div slot=\"content\">\r\n          <Form\r\n            ref=\"htMesForm\"\r\n            :model=\"basicMes\"\r\n            :rules=\"ruleValidateht\"\r\n            :label-width=\"140\"\r\n            inline\r\n          >\r\n            <FormItem label=\"合同编码：\" prop=\"contractno\">\r\n              <cl-input v-model=\"basicMes.contractno\" readonly />\r\n            </FormItem>\r\n            <FormItem label=\"合同名称：\" prop=\"contractName\">\r\n              <cl-input v-model=\"basicMes.contractName\" style=\"width: 300px\" readonly />\r\n            </FormItem>\r\n            <FormItem style=\"width: 100px\">\r\n              <Button type=\"success\" @click=\"handleChooseHt()\">选择合同</Button>\r\n            </FormItem>\r\n          </Form>\r\n        </div>\r\n      </Panel>\r\n      <Panel name=\"4\">\r\n        外部收款人信息\r\n        <div slot=\"content\">\r\n          <cl-table\r\n            ref=\"itemTable\"\r\n            v-if=\"itemTable.show\"\r\n            :url=\"itemTable.url\"\r\n            :searchable=\"false\"\r\n            :query-params=\"itemTable.query\"\r\n            :columns=\"itemTable.columns\"\r\n            select-enabled\r\n            :pageSize=\"5\"\r\n          ></cl-table>\r\n        </div>\r\n      </Panel>\r\n      <Panel v-if=\"attachShow\" name=\"Panel5\"\r\n        >附件信息\r\n        <div slot=\"content\">\r\n          <Row class=\"form-panel\">\r\n            <attach-file\r\n              :param=\"fileParam\"\r\n              :attachData=\"attachData\"\r\n              v-on:setAttachData=\"setAttachData\"\r\n            />\r\n          </Row>\r\n        </div>\r\n      </Panel>\r\n    </Collapse>\r\n  </div>\r\n</template>\r\n<script>\r\nimport config from \"@/config/index\";\r\nimport { ruleValidatebillBasic, widthstyle, numberRule } from \"./mssAccountbilldata\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport { deepClone } from \"@/libs/util\";\r\nimport ChooseModal from \"./chooseModal\";\r\nimport attachFile from \"@/view/basedata/protocol/attachFile\";\r\nimport invoiceFile from \"./invoiceFile\";\r\nimport axios from \"@/libs/api.request\";\r\nimport { attchList, removeAttach } from \"@/api/basedata/ammeter.js\";\r\nimport {\r\n  billViewById,\r\n  billEditById,\r\n  billEditSave,\r\n  billaddData,\r\n  getCategorys,\r\n  billaddDataLn,\r\n} from \"@/api/mssaccountbill/data\";\r\n\r\nexport default {\r\n  name: \"basicMes\",\r\n  components: { ChooseModal, attachFile,invoiceFile },\r\n  props: [\"tdlable\"],\r\n  data() {\r\n    let renderSup = (h, params) => {\r\n      var that = this;\r\n      var value = that.switchProp(params); //获取值\r\n      if (\"supplierCode\" == params.column.key || \"supplierName\" == params.column.key) {\r\n        return h(\"div\", [\r\n          h(\"Input\", {\r\n            props: {\r\n              value: value,\r\n              readonly: true,\r\n              placeholder: \"请选择供应商/客户\",\r\n            },\r\n            on: {\r\n              \"on-change\"(event) {\r\n                that.switchrowProp(params, event.target.value);\r\n                that.supplier.data[params.index] = params.row;\r\n                that.setSupplierTobill();\r\n              },\r\n            },\r\n          }),\r\n        ]);\r\n      } else {\r\n        return h(\"div\", [\r\n          h(\"InputNumber\", {\r\n            props: {\r\n              value: value,\r\n              step: 0.1,\r\n              // min: 0,\r\n              placeholder: \"请填写金额\",\r\n            },\r\n            on: {\r\n              \"on-change\"(value) {\r\n                that.switchrowProp(params, value);\r\n                that.supplier.data[params.index] = params.row;\r\n                that.setSupplierTobill();\r\n              },\r\n            },\r\n          }),\r\n        ]);\r\n      }\r\n    };\r\n    let renderSum = (h, params) => {\r\n      var that = this;\r\n      return h(\"div\", [\r\n        h(\"InputNumber\", {\r\n          props: {\r\n            value: params.row.sum,\r\n            step: 0.1,\r\n            // min: 0,\r\n            placeholder: \"请输入金额\",\r\n          },\r\n          on: {\r\n            \"on-change\"(value) {\r\n              params.row.sum = value;\r\n              that.itemTable.data[params.index] = params.row;\r\n            },\r\n          },\r\n        }),\r\n      ]);\r\n    };\r\n    return {\r\n      disableCompanyNameTxt: false,\r\n      companyNameList: [\"A006\", \"B006\"],\r\n      isExistKindGiftDisabled: false,\r\n      version: config.version,\r\n      accountCode: null,\r\n      formItemWidth: widthstyle,\r\n      billtypedisabled: false,\r\n      formValid: { basicMesForm: false, htMesForm: false },\r\n      collapsevalue: [\"1\", \"2\", \"3\", \"4\"],\r\n      basicMes: {\r\n        paymentType: null,\r\n        pickingMode: null,\r\n        invoiceType: null,\r\n        billtype: null,\r\n        fillInName: null,\r\n        fillInDep: null,\r\n        fillInCostCenterName: null,\r\n        busihappendtimeflag: null,\r\n        isGdtelInvoice: null,\r\n        telephone: null,\r\n        formAmount: null,\r\n        budgetsetname: null,\r\n        happenDate: null,\r\n        companyNameTxt: null,\r\n        bizTypeCode: null,\r\n        inputTaxTurnSum: null,\r\n        inputTaxTurnBizType: null,\r\n        isExistKindGift: null,\r\n        kindGiftTaxSum: null,\r\n        kindGiftSum: null,\r\n        isStaffPayment: null,\r\n        paytaxattr: null,\r\n        isEmergency: null,\r\n        isInputTax: null,\r\n        abstractValue: null,\r\n        contractno: null,\r\n        contractName: null,\r\n      },\r\n      categorys: {},\r\n      personType: null,\r\n      attachShow: false,\r\n      ifHasUnion: false,\r\n      supplier: {\r\n        loading: false,\r\n        columns: [\r\n          {\r\n            title: \"供应商/客户编码\",\r\n            key: \"supplierCode\",\r\n            render: renderSup,\r\n          },\r\n          {\r\n            title: \"供应商/客户名称\",\r\n            key: \"supplierName\",\r\n            render: renderSup,\r\n          },\r\n          {\r\n            title: \"是否关联方\",\r\n            key: \"ApproveMoney\",\r\n          },\r\n          {\r\n            title: \"一般纳税人\",\r\n            key: \"isRelease\",\r\n          },\r\n          {\r\n            title: \"报账金额（不含税价）\",\r\n            key: \"sum\",\r\n            render: renderSup,\r\n          },\r\n          {\r\n            title: \"税额\",\r\n            key: \"inputTaxSum\", //, render: renderSup\r\n          },\r\n        ],\r\n        data: [\r\n          {\r\n            supplierCode: null,\r\n            supplierName: null,\r\n            ApproveMoney: null,\r\n            isRelease: null,\r\n            sum: null,\r\n            inputTaxSum: null,\r\n            cellClassName: {\r\n              // supplierCode: \"demo-table-info-cell-sum\",\r\n              // supplierName: \"demo-table-info-cell-sum\",\r\n              // sum: 'demo-table-info-cell-inputTaxSum'\r\n              // ,inputTaxSum: 'demo-table-info-cell-inputTaxSum'\r\n            },\r\n          },\r\n        ],\r\n        total: 0,\r\n      },\r\n      ruleValidate: ruleValidatebillBasic,\r\n      ruleValidateht: {\r\n        /*contractno: [{required: true, message: \"不能为空\", trigger: \"blur\"}],\r\n                     contractName: [{required: true, message: \"不能为空\", trigger: \"blur\"}]*/\r\n      },\r\n      itemTable: { show: false, data: [] },\r\n      itemTable1: {\r\n        show: true,\r\n        data: [],\r\n        url: \"mssaccount/mssSupplierItem2/list\",\r\n        query: null,\r\n        columns: [\r\n          {\r\n            title: \"收款方名称\",\r\n            key: \"koinh\",\r\n          },\r\n          {\r\n            title: \"收款方类型\",\r\n            key: \"bvtyp\",\r\n          },\r\n          {\r\n            title: \"开户行\",\r\n            key: \"banka\",\r\n          },\r\n          {\r\n            title: \"银行账号\",\r\n            key: \"bankn\",\r\n          },\r\n          {\r\n            title: \"所在省\",\r\n            key: \"provz\",\r\n          },\r\n          {\r\n            title: \"所在市\",\r\n            key: \"ort01\",\r\n          },\r\n          {\r\n            title: \"所属银行\",\r\n            key: \"bvtyp\",\r\n          },\r\n          {\r\n            title: \"开户行行号\",\r\n            key: \"brnch\",\r\n          },\r\n          {\r\n            title: \"金额（含税）\",\r\n            key: \"sum\",\r\n            render: renderSum,\r\n          },\r\n        ],\r\n      },\r\n      fileParam: {\r\n        busiId: \"\",\r\n        busiAlias: \"附件(预提)\",\r\n        categoryCode: \"file\",\r\n        areaCode: \"ln\",\r\n      },\r\n      attachData: [],\r\n      invoiceParam: {\r\n        busiId: \"\",\r\n        busiAlias: \"附件(报账单发票)\",\r\n        categoryCode: \"file\",\r\n        areaCode: \"ln\",\r\n        invoiceFlag: \"1\"\r\n      },\r\n      invoiceData: [],\r\n      itemTable2: {\r\n        show: true,\r\n        data: [],\r\n        url: \"mssaccount/mssAbccustomerBank/list\",\r\n        query: null,\r\n        columns: [\r\n          {\r\n            title: \"客户编号\",\r\n            key: \"kunnr\",\r\n          },\r\n          {\r\n            title: \"收款方类型\",\r\n            key: \"koinh\",\r\n          },\r\n          {\r\n            title: \"开户行\",\r\n            key: \"zbanka\",\r\n          },\r\n          {\r\n            title: \"所在省\",\r\n            key: \"provz\",\r\n          },\r\n          {\r\n            title: \"所在市\",\r\n            key: \"city\",\r\n          },\r\n          {\r\n            title: \"银行账号\",\r\n            key: \"bankn\",\r\n          },\r\n          {\r\n            title: \"所属银行\",\r\n            key: \"bgrup\",\r\n          },\r\n          {\r\n            title: \"开户行行号\",\r\n            key: \"bankl\",\r\n          },\r\n          {\r\n            title: \"金额（含税）\",\r\n            key: \"sum\",\r\n            render: renderSum,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    // 强制更新文本框的值\r\n    changeMessage() {\r\n      this.$forceUpdate();\r\n    },\r\n    getFileorgCode() {\r\n      this.$emit(\"getFileorgCode\");\r\n    },\r\n    handleChooseSup(data) {\r\n      if (!data) {\r\n        if (this.basicMes.accountCode == \"********\") {\r\n          // 铁塔报账 只能 查询铁塔\r\n          this.$refs.chooseModalSup.modal1.queryparams.name1 = \"中国铁塔\";\r\n        }\r\n        this.$refs.chooseModalSup.choose(1); //打开模态框\r\n      } else {\r\n        //判断选择的供应商是否和已上传发票的供应商是否一致\r\n        if(this.invoiceData.length > 0){\r\n          if(data.name != this.invoiceData[0].invoiceSupplier){\r\n            this.$Modal.confirm({\r\n              title: '供应商确认选择', content: '<p class=\"warning\"> 当前选择的供应商与已上传发票的供应商不一致，是否确认选择该供应商</p>', onOk: () => {\r\n                this.basicMes.supplierCode = null;\r\n                this.basicMes.supplierName = null;\r\n                this.basicMes.supplierCode = data.id;\r\n                this.basicMes.supplierName = data.name;\r\n                this.supplier.data[0].supplierCode = data.id;\r\n                this.supplier.data[0].supplierName = data.name;\r\n                this.toShowPayee(); //触发改变外部收款人信息\r\n              }\r\n            });\r\n            return;\r\n          }\r\n        } \r\n        this.basicMes.supplierCode = null;\r\n        this.basicMes.supplierName = null;\r\n        this.basicMes.supplierCode = data.id;\r\n        this.basicMes.supplierName = data.name;\r\n        this.supplier.data[0].supplierCode = data.id;\r\n        this.supplier.data[0].supplierName = data.name;\r\n        this.toShowPayee(); //触发改变外部收款人信息\r\n      }\r\n    },\r\n    handleChooseMas(data) {\r\n      if (!data) {\r\n        this.$refs.chooseModalSup.choose(2); //打开模态框\r\n      } else {\r\n      }\r\n    },\r\n    handleChooseHt(data) {\r\n      if (!data) {\r\n        this.$refs.chooseModalSup.choose(3); //打开模态框\r\n      } else {\r\n        this.basicMes.contractno = data.id;\r\n        this.basicMes.contractName = data.name;\r\n      }\r\n    },\r\n    getDataFromModal(data, flag) {\r\n      if (flag == 1) {\r\n        this.personType = 1;\r\n        this.basicMes.suppliertype = \"1\";\r\n        this.handleChooseSup(data); // 传 true 设置 回调值\r\n      } else if (flag == 2) {\r\n        this.personType = 2;\r\n        this.basicMes.suppliertype = \"2\";\r\n        this.handleChooseSup(data); // 传 true 设置 回调值\r\n      } else if (flag == 3) {\r\n        this.handleChooseHt(data); // 传 true 设置 回调值\r\n      }\r\n    },\r\n    setTdlable() {\r\n      var type = this.basicMes.billtype;\r\n      this.$emit(\"setTdlable\", type);\r\n      if (type == 1) {\r\n        //选择“报销”时，【业务类型】固定为“列并付”，【业务场景】固定为“挂账并付款”-报销单，【收支方式】可选“集中支付”和“已支付-本地已主动支付”\r\n        this.setcategorysCase([2], [9], [2, 4, 5]);\r\n        this.basicMes.paymentType = 4; // 报销，预付，挂账支付，收付方式缺省都改为“集中支付”\r\n      } else if (type == 2) {\r\n        //选择“挂账”时，【业务类型】固定“列账”，【业务场景】固定为“挂账不付款（含预列）”-挂账单，【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [7], [8]);\r\n      } else if (type == 3) {\r\n        //选择“挂账支付”时，【业务类型】固定为“付款”，【业务场景】固定为“挂账后付款（清偿应付款）” ，【收支方式】可选“集中支付”和“已支付-本地已主动支付”\r\n        this.setcategorysCase([1], [2], [2, 4, 5]);\r\n        this.basicMes.paymentType = 4; // 报销，预付，挂账支付，收付方式缺省都改为“集中支付”\r\n      } else if (type == 4) {\r\n        //选择“预付”时，【业务类型】固定为“付款”，【业务场景】固定为“预付款” ，【收支方式】可选“集中支付”和“已支付-本地已主动支付”，【票据类型】固定为“无发票”\r\n        this.setcategorysCase([1], [8], [2, 4, 5]);\r\n        this.basicMes.paymentType = 4; // 报销，预付，挂账支付，收付方式缺省都改为“集中支付”\r\n        //【票据类型】固定为“无发票”\r\n        this.setCategorys(this.categorys.invoiceType, [9]);\r\n        this.basicMes.invoiceType = 9;\r\n      } else if (type == 5) {\r\n        //选择“预付冲销”时，【业务类型】固定为“列账”，【业务场景】固定为“冲前期借款(预付款)” ，【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [1], [8]);\r\n      } else if (type == 7) {\r\n        //选择“前期预付冲销”时，【业务类型】固定为“列账”，【业务场景】固定为“冲前期借款(预付款)” ，【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [1], [8]);\r\n      } else if (type == 10) {\r\n        //选择“预估冲销”时，【业务类型】固定为“列账”，【业务场景】固定为“冲预列(冲暂估)” ，【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [3], [8]);\r\n      } else if (type == 9) {\r\n        //选择 “预估”时，【业务类型】固定为“列账”，【业务场景】固定为“挂账不付款(含预列)”。【收支方式】固定为“不涉及银行收付”\r\n        this.setcategorysCase([0], [7], [8]);\r\n      } else if (type == 8) {\r\n        //选择 “收款”时，支付方式只有“集中收款”或“本地收款”\r\n        this.clearCategorys(this.categorys.invoiceType); // 不控制\r\n        if (this.version == \"sc\") {\r\n          /*if (ifHasUnion)\r\n                        this.setcategorysCase([0], [6], [6, 7]);\r\n                       else*/\r\n          this.setcategorysCase([0], [4, 6], [6, 7]);\r\n        } else {\r\n          this.setcategorysCase([0], [6], [6, 7]);\r\n          this.basicMes.invoiceType = 9;\r\n          if (this.supplier.data[0].supplierCode == null) {\r\n            this.supplier.data[0].supplierCode = \"9221000000\";\r\n            this.supplier.data[0].supplierName = \"收款-其他\";\r\n          }\r\n        }\r\n      } else if (type == 11) {\r\n        //调账：支付方式为“不涉及银行收付”，业务类型为\"列账”，业务场景为“往来转销”，\r\n        // 明细列账属性：“不使用成本预算”\r\n        this.setcategorysCase([0], [5], [8]);\r\n      } else {\r\n        this.clearCategorys(this.categorys.bizTypeCode);\r\n        this.clearCategorys(this.categorys.pickingMode);\r\n        this.clearCategorys(this.categorys.paymentType);\r\n        this.clearCategorys(this.categorys.invoiceType);\r\n        this.basicMes.bizTypeCode = null;\r\n        this.basicMes.pickingMode = null;\r\n        this.basicMes.paymentType = null;\r\n        this.basicMes.invoiceType = null;\r\n      }\r\n      // this.$nextTick(()=>{\r\n      //     this.$refs[\"basicMesForm\"].clearValidate(['billtype']);\r\n      // })\r\n\r\n      // this.$refs.basicMesForm.resetFields();\r\n    },\r\n    setcategorysCase(falg1, flag2, flag3) {\r\n      this.setCategorys(this.categorys.bizTypeCode, falg1);\r\n      this.basicMes.bizTypeCode = falg1[0]; //设置 业务类型 默认选择\r\n      this.setCategorys(this.categorys.pickingMode, flag2);\r\n      this.basicMes.pickingMode = flag2[0]; //设置 业务场景 默认选择\r\n      this.setCategorys(this.categorys.paymentType, flag3);\r\n      this.basicMes.paymentType = flag3[0]; //设置 收支方式 默认选择\r\n      ////\r\n      this.clearCategorys(this.categorys.invoiceType);\r\n      // this.basicMes.invoiceType = null;\r\n    },\r\n    setCategorys(data, flags) {\r\n      for (let item of data) {\r\n        // if (flags.indexOf(item.typeCode) > -1) {\r\n        if (flags.indexOf(Number(item.typeCode)) > -1) {\r\n          item.deletedFlag = 0;\r\n        } else {\r\n          item.deletedFlag = 1;\r\n        }\r\n      }\r\n    },\r\n    //设置字典值:自有报账单不要预提\r\n    setDictsOpts(key, val) {\r\n      this.categorys[key] = this.categorys[key].filter((d) => d.typeCode != val);\r\n    },\r\n    clearCategorys(data) {\r\n      for (let item of data) {\r\n        item.deletedFlag = 0;\r\n      }\r\n    },\r\n    valibasicMesForm(formname) {\r\n      this.$refs[formname].validate((valid) => {\r\n        this.formValid[formname] = valid;\r\n      });\r\n    },\r\n    setSupplierTobill() {\r\n      this.basicMes.supplierCode = this.supplier.data[0].supplierCode;\r\n      this.basicMes.supplierName = this.supplier.data[0].supplierName;\r\n      this.basicMes.sum = this.supplier.data[0].sum;\r\n      this.basicMes.inputTaxSum = this.supplier.data[0].inputTaxSum;\r\n      if (this.basicMes.inputTaxSum != null) {\r\n        this.$emit(\"setInputTaxSum\", this.basicMes.inputTaxSum);\r\n      }\r\n    },\r\n    switchProp(params) {\r\n      var value = null;\r\n      switch (params.column.key) {\r\n        case \"sum\":\r\n          value = params.row.sum;\r\n          break;\r\n        case \"inputTaxSum\":\r\n          value = params.row.inputTaxSum;\r\n          break;\r\n        case \"supplierCode\":\r\n          value = params.row.supplierCode;\r\n          break;\r\n        case \"supplierName\":\r\n          value = params.row.supplierName;\r\n          break;\r\n      }\r\n      return value;\r\n    },\r\n    switchrowProp(params, value) {\r\n      switch (params.column.key) {\r\n        case \"sum\":\r\n          params.row.sum = value;\r\n          break;\r\n        case \"inputTaxSum\":\r\n          params.row.inputTaxSum = value;\r\n          break;\r\n        case \"supplierCode\":\r\n          params.row.supplierCode = value;\r\n          break;\r\n        case \"supplierName\":\r\n          params.row.supplierName = value;\r\n          break;\r\n      }\r\n    },\r\n    validHandel() {\r\n      var mes = \"\";\r\n      var supplierCode = this.supplier.data[0].supplierCode;\r\n      var inputTaxSum = this.supplier.data[0].inputTaxSum;\r\n      var sum = this.supplier.data[0].sum;\r\n      if (this.basicMes.isStaffPayment == 0 && supplierCode == null) {\r\n        mes += \"请选择供应商/客户\";\r\n      } else if (sum == null) {\r\n        this.supplier.data[0].cellClassName.sum = \"demo-table-error\";\r\n        mes += \"供应商/客户 报账金额（不含税价）不能为空\";\r\n      } else if (sum && !numberRule.test(sum)) {\r\n        this.supplier.data[0].cellClassName.sum = \"demo-table-error\";\r\n        mes += \"供应商/客户 报账金额（不含税价）输入有误\";\r\n      } else if (inputTaxSum == null) {\r\n        this.supplier.data[0].inputTaxSum = 0;\r\n        // this.supplier.data[0].cellClassName.inputTaxSum = 'demo-table-error';\r\n        // mes += \"供应商/客户 税额 不能为空\";\r\n      } else if (inputTaxSum && !numberRule.test(inputTaxSum)) {\r\n        this.supplier.data[0].cellClassName.inputTaxSum = \"demo-table-error\";\r\n        mes += \"供应商/客户 税额 输入有误\";\r\n      }\r\n      if (sum && numberRule.test(sum)) {\r\n        this.supplier.data[0].cellClassName.sum = \"demo-table-info-cell-inputTaxSum\";\r\n      }\r\n      if (inputTaxSum && numberRule.test(inputTaxSum)) {\r\n        this.supplier.data[0].cellClassName.inputTaxSum =\r\n          \"demo-table-info-cell-inputTaxSum\";\r\n      }\r\n      return mes;\r\n    },\r\n    validItemTable() {\r\n      if (this.itemTable.data.length == 0) {\r\n        // 外部收款人 可为空\r\n        return true;\r\n      }\r\n      let _itemTable = [];\r\n      let flag = false;\r\n      for (var i = 0; i < this.itemTable.data.length; i++) {\r\n        var ik = this.itemTable.data[i];\r\n        if (ik) {\r\n          var value = ik.sum;\r\n          if (value && numberRule.test(value)) {\r\n            _itemTable.push(this.itemTable.data[i]);\r\n            flag = true;\r\n          }\r\n        }\r\n      }\r\n      if (!flag) {\r\n        this.collapsevalue = [\"4\"];\r\n        this.$Message.error(\"至少填写一项（含税）金额,或者金额有误\");\r\n      } else {\r\n        if (this.personType == 1) {\r\n          this.basicMes.supplierItem2 = _itemTable;\r\n        } else {\r\n          this.basicMes.customerBank = _itemTable;\r\n        }\r\n      }\r\n      return flag;\r\n    },\r\n    invoiceTypeChange() {\r\n      // debugger\r\n      // if (this.version == 'sc') {\r\n      // 当票据类型选择“增值税专用发票”时，带出“是否对外开具专票”选项，默认选择是，\r\n      // 控制“转售金额(含税价)”和“转售税额”必填。如果选择否，此两项改为非必填。\r\n      // if (this.basicMes.invoiceType == 1) {\r\n      //     this.basicMes.isGdtelInvoice = \"1\";\r\n      // } else {\r\n      //     this.basicMes.isGdtelInvoice = \"0\";\r\n      // }\r\n      //  票据类型选“增值税专票”，明细有代垫外单位电费，“是否转售水电”默认为“是”\r\n      //     this.$emit(\"setTdlable\", this.basicMes.billtype);\r\n      // }\r\n      // else {\r\n      //     this.basicMes.invoiceType\r\n      // }\r\n    },\r\n    kindGiftSumBlur() {\r\n      if (this.version == \"sc\") {\r\n        // 转售税额根据用户填写的转售金额(含税价)自动计算，计算规则：\r\n        // 转售税额=转售金额(含税价)*0.13/1.13\r\n        if (numberRule.test(this.basicMes.kindGiftSum)) {\r\n          ////和“转售税额”金额不再自动计算，由用户自己填写\r\n          // let sum = parseFloat(this.basicMes.kindGiftSum);\r\n          // this.basicMes.kindGiftTaxSum = (sum * 0.13 / 1.13).toFixed(2);\r\n        } else {\r\n          this.basicMes.kindGiftSum = null;\r\n          this.basicMes.kindGiftTaxSum = null;\r\n          this.$Message.info(\"转售金额(含税价)--请输入数字\");\r\n        }\r\n      }\r\n    },\r\n    setbudgetTypeDefault() {\r\n      this.$emit(\"setbudgetTypeDefault\");\r\n    },\r\n    setAttachData(data) {\r\n      this.multiFiles = data.data;\r\n      this.removeIds = data.ids;\r\n      if (this.removeIds.length != 0 && data.type == \"remove\") {\r\n        this.removeAttach();\r\n      } else {\r\n        this.upload();\r\n      }\r\n    },\r\n    upload() {\r\n      if (this.attachData.length != 0 && this.multiFiles.length != 0) {\r\n        // this.$Message.info(\"提示:上传文件过大可能导致上传失败！\");\r\n        this.loading = true;\r\n        axios\r\n          .request({\r\n            url: \"/common/attachments/uploadMultiFile\",\r\n            method: \"post\",\r\n            data: this.multiFiles,\r\n          })\r\n          .then((res) => {\r\n            if (res.data.code != 0) {\r\n              this.loading = false;\r\n            }\r\n            let that = this;\r\n            attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n              that.attachData = Object.assign([], res.data.rows);\r\n            });\r\n          });\r\n      }\r\n    },\r\n    removeAttach() {\r\n      removeAttach({ ids: this.removeIds.join() }).then(() => {});\r\n    },\r\n    setInvoiceData(data) {\r\n      if(data.type == 'upload'){\r\n        //上传的发票的供应商在能耗系统是否存在 1 存在 0 不存在\r\n        if(data.supplierExist == '1'){\r\n          //自动填充供应商信息\r\n          if(this.basicMes.supplierCode != data.supplierCode && this.basicMes.supplierName != data.supplierName){\r\n            if(this.basicMes.supplierCode && this.basicMes.supplierName){\r\n              //当已选择供应商时,提示是否需要替换\r\n              this.$Modal.confirm({\r\n                title: '替换供应商', content: '<p class=\"warning\"> 当前选择的供应商与上传发票的供应商不一致，是否替换供应商</p>', onOk: () => {\r\n                  this.basicMes.supplierCode = data.supplierCode;\r\n                  this.basicMes.supplierName = data.supplierName;\r\n                  this.supplier.data[0].supplierCode = data.supplierCode;\r\n                  this.supplier.data[0].supplierName = data.supplierName;\r\n                  this.personType = 1;\r\n                  this.toShowPayee(); //触发改变外部收款人信息\r\n                }\r\n              });\r\n            } else{\r\n              //当未选择供应商时，直接填充\r\n              this.basicMes.supplierCode = data.supplierCode;\r\n              this.basicMes.supplierName = data.supplierName;\r\n              this.supplier.data[0].supplierCode = data.supplierCode;\r\n              this.supplier.data[0].supplierName = data.supplierName;\r\n              this.personType = 1;\r\n              this.toShowPayee(); //触发改变外部收款人信息\r\n            }\r\n          }\r\n        } else {\r\n          //提示发票供应商信息不存在\r\n          this.$Message.warning(`上传的发票的供应商信息不存在`);\r\n        }\r\n      }\r\n      attchList({ busiId: this.invoiceParam.busiId, invoiceFlag: this.invoiceParam.invoiceFlag }).then((res) => {\r\n        this.invoiceData = Object.assign([], res.data.rows);\r\n      });\r\n    },\r\n    //选择供应商/供应商有值->改变外部收款人信息\r\n    toShowPayee() {\r\n      this.itemTable.query = null;\r\n      if (this.basicMes.supplierCode != null) {\r\n        if (this.personType == 1) {\r\n          this.itemTable1.query = { lifnr: this.basicMes.supplierCode };\r\n          this.itemTable = deepClone(this.itemTable1);\r\n        } else {\r\n          this.itemTable2.query = { kunnr: this.basicMes.supplierCode };\r\n          this.itemTable = deepClone(this.itemTable2);\r\n        }\r\n      }\r\n    },\r\n  },\r\n  watch: {\r\n    basicMes: {\r\n      deep: true, //深度监听\r\n      handler(val, oldVal) {\r\n        this.toShowPayee(); //触发改变外部收款人信息\r\n        if (this.version == \"sc\") {\r\n          // 四川\r\n          //报账单业务场景为“挂账并付款”时，票据类型不能选择到“无发票”选项。\r\n          if (val.pickingMode == 9) {\r\n            for (let item of this.categorys.invoiceType) {\r\n              if (item.typeCode == 9) {\r\n                item.deletedFlag = 1;\r\n              } else {\r\n                item.deletedFlag = 0;\r\n              }\r\n            }\r\n          } else {\r\n            for (let item of this.categorys.invoiceType) {\r\n              if (item.typeCode == 9) {\r\n                item.deletedFlag = 0;\r\n              }\r\n            }\r\n          }\r\n          //收支方式选择“集中支付”时，是否加急固化为“是”，不可修改；\r\n          // 选择其他收支方式时，是否加急固化为“否”，不可修改\r\n          if (val.paymentType == 4) {\r\n            //集中支付\r\n            val.isEmergency = \"1\"; //是\r\n          } else {\r\n            val.isEmergency = \"0\"; //否\r\n          }\r\n        } else {\r\n          if (val.isExistKindGift == \"0\") {\r\n            val.inputTaxTurnSum = null;\r\n            val.inputTaxTurnBizType = null;\r\n          }\r\n        }\r\n      },\r\n    },\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.$refs.basicMesForm.resetFields(); // this.$refs.adduserform.resetFields();\r\n      // this.$refs.chooseModalSup.resetFields();       // this.$refs.adduserform.resetFields();\r\n      this.$refs.htMesForm.resetFields();\r\n      if (this.itemTable.show) {\r\n        this.$refs.itemTable.resetFields(); // this.$refs.adduserform.resetFields();\r\n      }\r\n    });\r\n\r\n    if (this.version == \"ln\")\r\n      billaddDataLn(this.$route.query.data).then((res) => {\r\n        // this.basicMes = res.data;\r\n        let data = res.data || {};\r\n        Object.assign(this.basicMes, data);\r\n        // debugger\r\n        this.basicMes.formAmount = 1;\r\n        this.basicMes.isExistKindGift = \"0\";\r\n        this.basicMes.accountCode = this.accountCode;\r\n        if (res.data.companyNameTxt) {\r\n          this.disableCompanyNameTxt = true;\r\n        } else {\r\n          this.disableCompanyNameTxt = false;\r\n        }\r\n        this.$emit(\"setBillId\", this.basicMes.id);\r\n        this.fileParam.busiId = this.basicMes.id;\r\n        this.invoiceParam.busiId = this.basicMes.id;\r\n        if (this.version == \"ln\") this.attachShow = true;\r\n        // this.basicMes.billtype=parseInt(this.basicMes.billtype);\r\n        // this.basicMes.invoiceType = parseInt(this.basicMes.invoiceType);\r\n        // this.basicMes.bizTypeCode = parseInt(this.basicMes.bizTypeCode);\r\n        // this.basicMes.pickingMode = parseInt(this.basicMes.pickingMode);\r\n        // this.basicMes.isStaffPayment = parseInt(this.basicMes.isStaffPayment);\r\n        //\r\n        if (this.version == \"ln\" && this.basicMes.accountCode == \"********\") {\r\n          //\r\n          // 辽宁那边要求控制下自有报账单那边屏蔽调报账单预提，预提冲销的选项\r\n          // 只有铁塔报账单才能选预提，预提冲销\r\n          //自有的预提屏蔽，自有预提冲销开着，这边从8月份开始自有不允许走预提单\r\n          this.setCategorys(this.categorys.billtype, [1, 2, 3, 4, 5, 6, 7, 8, 10, 11]);\r\n        }\r\n      });\r\n    else\r\n      billaddData().then((res) => {\r\n        // this.basicMes = res.data;\r\n        let data = res.data || {};\r\n        Object.assign(this.basicMes, data);\r\n        this.basicMes.accountCode = this.accountCode;\r\n        this.$emit(\"setBillId\", this.basicMes.id);\r\n        this.fileParam.busiId = this.basicMes.id;\r\n        this.invoiceParam.busiId = this.basicMes.id;\r\n        if (this.version == \"ln\") this.attachShow = true;\r\n        // this.basicMes.billtype=parseInt(this.basicMes.billtype);\r\n        // this.basicMes.invoiceType = parseInt(this.basicMes.invoiceType);\r\n        // this.basicMes.bizTypeCode = parseInt(this.basicMes.bizTypeCode);\r\n        // this.basicMes.pickingMode = parseInt(this.basicMes.pickingMode);\r\n        // this.basicMes.isStaffPayment = parseInt(this.basicMes.isStaffPayment);\r\n        //\r\n        if (this.version == \"ln\" && this.basicMes.accountCode == \"********\") {\r\n          //\r\n          // 辽宁那边要求控制下自有报账单那边屏蔽调报账单预提，预提冲销的选项\r\n          // 只有铁塔报账单才能选预提，预提冲销\r\n          //自有的预提屏蔽，自有预提冲销开着，这边从8月份开始自有不允许走预提单\r\n          this.setCategorys(this.categorys.billtype, [1, 2, 3, 4, 5, 6, 7, 8, 10, 11]);\r\n        }\r\n      });\r\n  },\r\n  created() {\r\n    // getCategorys().then(res => {\r\n    //   this.categorys = res.data.categorys;\r\n    // });\r\n    //直接从前台取\r\n    /*        if (this.$route.query.data)\r\n          console.log(this.$route.query.data);*/\r\n    this.categorys = {\r\n      billtype: blist(\"billtype\"),\r\n      paymentType: blist(\"paymentType\"),\r\n      invoiceType: blist(\"invoiceType\"),\r\n      bizTypeCode: blist(\"bizType\"),\r\n      pickingMode: blist(\"pickingMode\"),\r\n      isStaffPayment: blist(\"isStaffPayment\"),\r\n    };\r\n  },\r\n};\r\n</script>\r\n"]}]}