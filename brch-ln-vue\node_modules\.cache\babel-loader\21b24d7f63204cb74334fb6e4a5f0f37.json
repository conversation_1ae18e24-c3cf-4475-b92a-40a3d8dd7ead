{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\listAmmeter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\listAmmeter.vue", "mtime": 1754285403019}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": 1741317456427}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXM2Lm51bWJlci5jb25zdHJ1Y3RvciI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzNi5mdW5jdGlvbi5uYW1lIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXM2Lm9iamVjdC5hc3NpZ24iOwppbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tICJFOlxcY2wtcHJvamVjdFxcbG4tbmVuZ2hhb1xcYnJjaC1sbi12dWVcXG5vZGVfbW9kdWxlc1xcQGJhYmVsXFxydW50aW1lL2hlbHBlcnMvZXNtL29iamVjdFNwcmVhZCI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzNy5zeW1ib2wuYXN5bmMtaXRlcmF0b3IiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lczYuc3ltYm9sIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS5pdGVyYWJsZSI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCmltcG9ydCB7IHdoaXRlTGlzdCwgd2hpdGVJbnNlcnQgfSBmcm9tICdAL2FwaS9hY2NvdW50JzsKaW1wb3J0IHsgc2VsZWN0Q2hhbmdlQW1tZXRlciwgbGlzdEFtbWV0ZXIsIHJlbW92ZUFtbWV0ZXIgYXMgX3JlbW92ZUFtbWV0ZXIsIGNoZWNrQWNvdW50QnlVcGRhdGUsIGdldFVzZXJCeVVzZXJSb2xlLCBnZXRDb3VudHJ5QnlVc2VySWQsIGdldFVzZXJkYXRhLCBnZXRDb3VudHJ5c2RhdGEsIGdldENsYXNzaWZpY2F0aW9uLCBjaGVja1N0YXJ0RmxvdyB9IGZyb20gJ0AvYXBpL2Jhc2VkYXRhL2FtbWV0ZXIuanMnOwppbXBvcnQgeyBpc0luVG9kb0xpc3QgfSBmcm9tICJAL2FwaS9hbGVydGNvbnRyb2wvYWxlcnRjb250cm9sIjsKaW1wb3J0IHsgYmxpc3QsIGJ0ZXh0IH0gZnJvbSAiQC9saWJzL3Rvb2xzIjsKaW1wb3J0IHZpZXdBbW1ldGVyUGFnZSBmcm9tICcuL3ZpZXdBbW1ldGVyLnZ1ZSc7CmltcG9ydCB2aWV3UXVvdGFQYWdlIGZyb20gJ0Avdmlldy9iYXNlZGF0YS9xdW90YS92aWV3UXVvdGEudnVlJzsKaW1wb3J0IHZpZXdTdGF0aW9uUGFnZSBmcm9tICdAL3ZpZXcvYmFzZWRhdGEvc3RhdGlvbi92aWV3U3RhdGlvbi52dWUnOwppbXBvcnQgY291bnRyeU1vZGFsIGZyb20gIi4vY291bnRyeU1vZGFsIjsKaW1wb3J0IFByb2Nlc3NJbmZvIGZyb20gJ0Avdmlldy9iYXNpYy9zeXN0ZW0vd29ya2Zsb3cvcHJvY2Vzcy1pbmZvJzsKaW1wb3J0IFdvcmtGbG93SW5mb0NvbXBvbmV0IGZyb20gJ0Avdmlldy9iYXNpYy9zeXN0ZW0vd29ya2Zsb3cvd29ya0Zsb3dJbmZvQ29tcG9uZXQnOwppbXBvcnQgYXR0YWNoRmlsZSBmcm9tICJAL3ZpZXcvYmFzZWRhdGEvd2hpdGVsaXN0L2F0dGFjaEZpbGUudnVlIjsKaW1wb3J0IHsgYXR0Y2hMaXN0LCByZW1vdmVBdHRhY2ggYXMgX3JlbW92ZUF0dGFjaCB9IGZyb20gJ0AvYXBpL2Jhc2VkYXRhL2FtbWV0ZXIuanMnOwppbXBvcnQgYXhpb3MgZnJvbSAnQC9saWJzL2FwaS5yZXF1ZXN0JzsKaW1wb3J0IGV4Y2VsIGZyb20gJ0AvbGlicy9leGNlbCc7CmltcG9ydCB7IG1hcE11dGF0aW9ucyB9IGZyb20gInZ1ZXgiOwppbXBvcnQgcm91dGVycyBmcm9tICdAL3JvdXRlci9yb3V0ZXJzJzsKaW1wb3J0IHsgZ2V0SG9tZVJvdXRlIH0gZnJvbSAnQC9saWJzL3V0aWwnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ2FtbWV0ZXInLAogIGNvbXBvbmVudHM6IHsKICAgIFByb2Nlc3NJbmZvOiBQcm9jZXNzSW5mbywKICAgIFdvcmtGbG93SW5mb0NvbXBvbmV0OiBXb3JrRmxvd0luZm9Db21wb25ldCwKICAgIC8vIGFkZEFtbWV0ZXJQYWdlLAogICAgLy8gZWRpdEFtbWV0ZXJQYWdlLAogICAgdmlld0FtbWV0ZXJQYWdlOiB2aWV3QW1tZXRlclBhZ2UsCiAgICB2aWV3U3RhdGlvblBhZ2U6IHZpZXdTdGF0aW9uUGFnZSwKICAgIHZpZXdRdW90YVBhZ2U6IHZpZXdRdW90YVBhZ2UsCiAgICBjb3VudHJ5TW9kYWw6IGNvdW50cnlNb2RhbCwKICAgIGF0dGFjaEZpbGU6IGF0dGFjaEZpbGUKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwoKICAgIC8v54q25oCBCiAgICB2YXIgcmVuZGVyU3RhdHVzID0gZnVuY3Rpb24gcmVuZGVyU3RhdHVzKGgsIHBhcmFtcykgewogICAgICB2YXIgdmFsdWUgPSAiIjsKICAgICAgdmFyIF9pdGVyYXRvck5vcm1hbENvbXBsZXRpb24gPSB0cnVlOwogICAgICB2YXIgX2RpZEl0ZXJhdG9yRXJyb3IgPSBmYWxzZTsKICAgICAgdmFyIF9pdGVyYXRvckVycm9yID0gdW5kZWZpbmVkOwoKICAgICAgdHJ5IHsKICAgICAgICBmb3IgKHZhciBfaXRlcmF0b3IgPSBfdGhpcy5zdGF0dXNbU3ltYm9sLml0ZXJhdG9yXSgpLCBfc3RlcDsgIShfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uID0gKF9zdGVwID0gX2l0ZXJhdG9yLm5leHQoKSkuZG9uZSk7IF9pdGVyYXRvck5vcm1hbENvbXBsZXRpb24gPSB0cnVlKSB7CiAgICAgICAgICB2YXIgaXRlbSA9IF9zdGVwLnZhbHVlOwoKICAgICAgICAgIGlmIChpdGVtLnR5cGVDb2RlID09IHBhcmFtcy5yb3cuc3RhdHVzKSB7CiAgICAgICAgICAgIHZhbHVlID0gaXRlbS50eXBlTmFtZTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfZGlkSXRlcmF0b3JFcnJvciA9IHRydWU7CiAgICAgICAgX2l0ZXJhdG9yRXJyb3IgPSBlcnI7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIGlmICghX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbiAmJiBfaXRlcmF0b3IucmV0dXJuICE9IG51bGwpIHsKICAgICAgICAgICAgX2l0ZXJhdG9yLnJldHVybigpOwogICAgICAgICAgfQogICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICBpZiAoX2RpZEl0ZXJhdG9yRXJyb3IpIHsKICAgICAgICAgICAgdGhyb3cgX2l0ZXJhdG9yRXJyb3I7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICByZXR1cm4gaCgiZGl2IiwgdmFsdWUpOwogICAgfTsgLy/nlKjnlLXnsbvlnosKCgogICAgdmFyIHJlbmRlckVsZWN0cm9UeXBlID0gZnVuY3Rpb24gcmVuZGVyRWxlY3Ryb1R5cGUoaCwgcGFyYW1zKSB7CiAgICAgIHZhciB2YWx1ZSA9ICIiOwogICAgICB2YXIgX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjIgPSB0cnVlOwogICAgICB2YXIgX2RpZEl0ZXJhdG9yRXJyb3IyID0gZmFsc2U7CiAgICAgIHZhciBfaXRlcmF0b3JFcnJvcjIgPSB1bmRlZmluZWQ7CgogICAgICB0cnkgewogICAgICAgIGZvciAodmFyIF9pdGVyYXRvcjIgPSBfdGhpcy5lbGVjdHJvVHlwZVtTeW1ib2wuaXRlcmF0b3JdKCksIF9zdGVwMjsgIShfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uMiA9IChfc3RlcDIgPSBfaXRlcmF0b3IyLm5leHQoKSkuZG9uZSk7IF9pdGVyYXRvck5vcm1hbENvbXBsZXRpb24yID0gdHJ1ZSkgewogICAgICAgICAgdmFyIGl0ZW0gPSBfc3RlcDIudmFsdWU7CgogICAgICAgICAgaWYgKGl0ZW0udHlwZUNvZGUgPT0gcGFyYW1zLnJvdy5lbGVjdHJvdHlwZSkgewogICAgICAgICAgICB2YWx1ZSA9IGl0ZW0udHlwZU5hbWU7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgX2RpZEl0ZXJhdG9yRXJyb3IyID0gdHJ1ZTsKICAgICAgICBfaXRlcmF0b3JFcnJvcjIgPSBlcnI7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIGlmICghX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjIgJiYgX2l0ZXJhdG9yMi5yZXR1cm4gIT0gbnVsbCkgewogICAgICAgICAgICBfaXRlcmF0b3IyLnJldHVybigpOwogICAgICAgICAgfQogICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICBpZiAoX2RpZEl0ZXJhdG9yRXJyb3IyKSB7CiAgICAgICAgICAgIHRocm93IF9pdGVyYXRvckVycm9yMjsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIHJldHVybiBoKCJkaXYiLCB2YWx1ZSk7CiAgICB9OyAvL+eUteihqOexu+WeiwoKCiAgICB2YXIgcmVuZGVyQW1tZXRlclR5cGUgPSBmdW5jdGlvbiByZW5kZXJBbW1ldGVyVHlwZShoLCBwYXJhbXMpIHsKICAgICAgdmFyIHZhbHVlID0gIiI7CiAgICAgIHZhciBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uMyA9IHRydWU7CiAgICAgIHZhciBfZGlkSXRlcmF0b3JFcnJvcjMgPSBmYWxzZTsKICAgICAgdmFyIF9pdGVyYXRvckVycm9yMyA9IHVuZGVmaW5lZDsKCiAgICAgIHRyeSB7CiAgICAgICAgZm9yICh2YXIgX2l0ZXJhdG9yMyA9IF90aGlzLmFtbWV0ZXJUeXBlW1N5bWJvbC5pdGVyYXRvcl0oKSwgX3N0ZXAzOyAhKF9pdGVyYXRvck5vcm1hbENvbXBsZXRpb24zID0gKF9zdGVwMyA9IF9pdGVyYXRvcjMubmV4dCgpKS5kb25lKTsgX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjMgPSB0cnVlKSB7CiAgICAgICAgICB2YXIgaXRlbSA9IF9zdGVwMy52YWx1ZTsKCiAgICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSBwYXJhbXMucm93LmFtbWV0ZXJ0eXBlKSB7CiAgICAgICAgICAgIHZhbHVlID0gaXRlbS50eXBlTmFtZTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfZGlkSXRlcmF0b3JFcnJvcjMgPSB0cnVlOwogICAgICAgIF9pdGVyYXRvckVycm9yMyA9IGVycjsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0cnkgewogICAgICAgICAgaWYgKCFfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uMyAmJiBfaXRlcmF0b3IzLnJldHVybiAhPSBudWxsKSB7CiAgICAgICAgICAgIF9pdGVyYXRvcjMucmV0dXJuKCk7CiAgICAgICAgICB9CiAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgIGlmIChfZGlkSXRlcmF0b3JFcnJvcjMpIHsKICAgICAgICAgICAgdGhyb3cgX2l0ZXJhdG9yRXJyb3IzOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgcmV0dXJuIGgoImRpdiIsIHZhbHVlKTsKICAgIH07IC8v55So55S15oCn6LSoCgoKICAgIHZhciByZW5kZXJFbGVjdHJvTmF0dXJlID0gZnVuY3Rpb24gcmVuZGVyRWxlY3Ryb05hdHVyZShoLCBwYXJhbXMpIHsKICAgICAgdmFyIHZhbHVlID0gIiI7CiAgICAgIHZhciBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uNCA9IHRydWU7CiAgICAgIHZhciBfZGlkSXRlcmF0b3JFcnJvcjQgPSBmYWxzZTsKICAgICAgdmFyIF9pdGVyYXRvckVycm9yNCA9IHVuZGVmaW5lZDsKCiAgICAgIHRyeSB7CiAgICAgICAgZm9yICh2YXIgX2l0ZXJhdG9yNCA9IF90aGlzLmVsZWN0cm9OYXVyZVtTeW1ib2wuaXRlcmF0b3JdKCksIF9zdGVwNDsgIShfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uNCA9IChfc3RlcDQgPSBfaXRlcmF0b3I0Lm5leHQoKSkuZG9uZSk7IF9pdGVyYXRvck5vcm1hbENvbXBsZXRpb240ID0gdHJ1ZSkgewogICAgICAgICAgdmFyIGl0ZW0gPSBfc3RlcDQudmFsdWU7CgogICAgICAgICAgaWYgKGl0ZW0udHlwZUNvZGUgPT0gcGFyYW1zLnJvdy5lbGVjdHJvbmF0dXJlKSB7CiAgICAgICAgICAgIHZhbHVlID0gaXRlbS50eXBlTmFtZTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfZGlkSXRlcmF0b3JFcnJvcjQgPSB0cnVlOwogICAgICAgIF9pdGVyYXRvckVycm9yNCA9IGVycjsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0cnkgewogICAgICAgICAgaWYgKCFfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uNCAmJiBfaXRlcmF0b3I0LnJldHVybiAhPSBudWxsKSB7CiAgICAgICAgICAgIF9pdGVyYXRvcjQucmV0dXJuKCk7CiAgICAgICAgICB9CiAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgIGlmIChfZGlkSXRlcmF0b3JFcnJvcjQpIHsKICAgICAgICAgICAgdGhyb3cgX2l0ZXJhdG9yRXJyb3I0OwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgcmV0dXJuIGgoImRpdiIsIHZhbHVlKTsKICAgIH07IC8v5LuY6LS55pa55byPCgoKICAgIHZhciByZW5kZXJQYXlUeXBlID0gZnVuY3Rpb24gcmVuZGVyUGF5VHlwZShoLCBwYXJhbXMpIHsKICAgICAgdmFyIHZhbHVlID0gIiI7CiAgICAgIHZhciBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uNSA9IHRydWU7CiAgICAgIHZhciBfZGlkSXRlcmF0b3JFcnJvcjUgPSBmYWxzZTsKICAgICAgdmFyIF9pdGVyYXRvckVycm9yNSA9IHVuZGVmaW5lZDsKCiAgICAgIHRyeSB7CiAgICAgICAgZm9yICh2YXIgX2l0ZXJhdG9yNSA9IF90aGlzLnBheVR5cGVbU3ltYm9sLml0ZXJhdG9yXSgpLCBfc3RlcDU7ICEoX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjUgPSAoX3N0ZXA1ID0gX2l0ZXJhdG9yNS5uZXh0KCkpLmRvbmUpOyBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uNSA9IHRydWUpIHsKICAgICAgICAgIHZhciBpdGVtID0gX3N0ZXA1LnZhbHVlOwoKICAgICAgICAgIGlmIChpdGVtLnR5cGVDb2RlID09IHBhcmFtcy5yb3cucGF5dHlwZSkgewogICAgICAgICAgICB2YWx1ZSA9IGl0ZW0udHlwZU5hbWU7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgX2RpZEl0ZXJhdG9yRXJyb3I1ID0gdHJ1ZTsKICAgICAgICBfaXRlcmF0b3JFcnJvcjUgPSBlcnI7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIGlmICghX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjUgJiYgX2l0ZXJhdG9yNS5yZXR1cm4gIT0gbnVsbCkgewogICAgICAgICAgICBfaXRlcmF0b3I1LnJldHVybigpOwogICAgICAgICAgfQogICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICBpZiAoX2RpZEl0ZXJhdG9yRXJyb3I1KSB7CiAgICAgICAgICAgIHRocm93IF9pdGVyYXRvckVycm9yNTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIHJldHVybiBoKCJkaXYiLCB2YWx1ZSk7CiAgICB9OyAvL+eUteS7t+aAp+i0qAoKCiAgICB2YXIgcmVuZGVyRWxlY3Ryb3ZhbGVuY2VOYXR1cmUgPSBmdW5jdGlvbiByZW5kZXJFbGVjdHJvdmFsZW5jZU5hdHVyZShoLCBwYXJhbXMpIHsKICAgICAgdmFyIHZhbHVlID0gIiI7CiAgICAgIHZhciBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uNiA9IHRydWU7CiAgICAgIHZhciBfZGlkSXRlcmF0b3JFcnJvcjYgPSBmYWxzZTsKICAgICAgdmFyIF9pdGVyYXRvckVycm9yNiA9IHVuZGVmaW5lZDsKCiAgICAgIHRyeSB7CiAgICAgICAgZm9yICh2YXIgX2l0ZXJhdG9yNiA9IF90aGlzLmVsZWN0cm92YWxlbmNlTmF0dXJlW1N5bWJvbC5pdGVyYXRvcl0oKSwgX3N0ZXA2OyAhKF9pdGVyYXRvck5vcm1hbENvbXBsZXRpb242ID0gKF9zdGVwNiA9IF9pdGVyYXRvcjYubmV4dCgpKS5kb25lKTsgX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjYgPSB0cnVlKSB7CiAgICAgICAgICB2YXIgaXRlbSA9IF9zdGVwNi52YWx1ZTsKCiAgICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSBwYXJhbXMucm93LmVsZWN0cm92YWxlbmNlbmF0dXJlKSB7CiAgICAgICAgICAgIHZhbHVlID0gaXRlbS50eXBlTmFtZTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfZGlkSXRlcmF0b3JFcnJvcjYgPSB0cnVlOwogICAgICAgIF9pdGVyYXRvckVycm9yNiA9IGVycjsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0cnkgewogICAgICAgICAgaWYgKCFfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uNiAmJiBfaXRlcmF0b3I2LnJldHVybiAhPSBudWxsKSB7CiAgICAgICAgICAgIF9pdGVyYXRvcjYucmV0dXJuKCk7CiAgICAgICAgICB9CiAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgIGlmIChfZGlkSXRlcmF0b3JFcnJvcjYpIHsKICAgICAgICAgICAgdGhyb3cgX2l0ZXJhdG9yRXJyb3I2OwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgcmV0dXJuIGgoImRpdiIsIHZhbHVlKTsKICAgIH07IC8v5Y2V5o2u54q25oCBCgoKICAgIHZhciByZW5kZXJCaWxsU3RhdHVzID0gZnVuY3Rpb24gcmVuZGVyQmlsbFN0YXR1cyhoLCBwYXJhbXMpIHsKICAgICAgdmFyIHZhbHVlID0gIiI7CiAgICAgIHZhciBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uNyA9IHRydWU7CiAgICAgIHZhciBfZGlkSXRlcmF0b3JFcnJvcjcgPSBmYWxzZTsKICAgICAgdmFyIF9pdGVyYXRvckVycm9yNyA9IHVuZGVmaW5lZDsKCiAgICAgIHRyeSB7CiAgICAgICAgZm9yICh2YXIgX2l0ZXJhdG9yNyA9IF90aGlzLmJpbGxTdGF0dXNbU3ltYm9sLml0ZXJhdG9yXSgpLCBfc3RlcDc7ICEoX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjcgPSAoX3N0ZXA3ID0gX2l0ZXJhdG9yNy5uZXh0KCkpLmRvbmUpOyBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uNyA9IHRydWUpIHsKICAgICAgICAgIHZhciBpdGVtID0gX3N0ZXA3LnZhbHVlOwoKICAgICAgICAgIGlmIChpdGVtLnR5cGVDb2RlID09IHBhcmFtcy5yb3cuYmlsbFN0YXR1cykgewogICAgICAgICAgICB2YWx1ZSA9IGl0ZW0udHlwZU5hbWU7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgX2RpZEl0ZXJhdG9yRXJyb3I3ID0gdHJ1ZTsKICAgICAgICBfaXRlcmF0b3JFcnJvcjcgPSBlcnI7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIGlmICghX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjcgJiYgX2l0ZXJhdG9yNy5yZXR1cm4gIT0gbnVsbCkgewogICAgICAgICAgICBfaXRlcmF0b3I3LnJldHVybigpOwogICAgICAgICAgfQogICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICBpZiAoX2RpZEl0ZXJhdG9yRXJyb3I3KSB7CiAgICAgICAgICAgIHRocm93IF9pdGVyYXRvckVycm9yNzsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIHJldHVybiBoKCJkaXYiLCB2YWx1ZSk7CiAgICB9OyAvL+S6p+adg+W9kuWxngoKCiAgICB2YXIgcmVuZGVyUHJvcGVydHkgPSBmdW5jdGlvbiByZW5kZXJQcm9wZXJ0eShoLCBwYXJhbXMpIHsKICAgICAgdmFyIHZhbHVlID0gIiI7CiAgICAgIHZhciBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uOCA9IHRydWU7CiAgICAgIHZhciBfZGlkSXRlcmF0b3JFcnJvcjggPSBmYWxzZTsKICAgICAgdmFyIF9pdGVyYXRvckVycm9yOCA9IHVuZGVmaW5lZDsKCiAgICAgIHRyeSB7CiAgICAgICAgZm9yICh2YXIgX2l0ZXJhdG9yOCA9IF90aGlzLnByb3BlcnR5W1N5bWJvbC5pdGVyYXRvcl0oKSwgX3N0ZXA4OyAhKF9pdGVyYXRvck5vcm1hbENvbXBsZXRpb244ID0gKF9zdGVwOCA9IF9pdGVyYXRvcjgubmV4dCgpKS5kb25lKTsgX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjggPSB0cnVlKSB7CiAgICAgICAgICB2YXIgaXRlbSA9IF9zdGVwOC52YWx1ZTsKCiAgICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSBwYXJhbXMucm93LnByb3BlcnR5KSB7CiAgICAgICAgICAgIHZhbHVlID0gaXRlbS50eXBlTmFtZTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfZGlkSXRlcmF0b3JFcnJvcjggPSB0cnVlOwogICAgICAgIF9pdGVyYXRvckVycm9yOCA9IGVycjsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0cnkgewogICAgICAgICAgaWYgKCFfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uOCAmJiBfaXRlcmF0b3I4LnJldHVybiAhPSBudWxsKSB7CiAgICAgICAgICAgIF9pdGVyYXRvcjgucmV0dXJuKCk7CiAgICAgICAgICB9CiAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgIGlmIChfZGlkSXRlcmF0b3JFcnJvcjgpIHsKICAgICAgICAgICAgdGhyb3cgX2l0ZXJhdG9yRXJyb3I4OwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgcmV0dXJuIGgoImRpdiIsIHZhbHVlKTsKICAgIH07IC8v5a+55aSW57uT566X57G75Z6LCgoKICAgIHZhciByZW5kZXJEaXJlY3RzdXBwbHlmbGFnID0gZnVuY3Rpb24gcmVuZGVyRGlyZWN0c3VwcGx5ZmxhZyhoLCBwYXJhbXMpIHsKICAgICAgdmFyIHZhbHVlID0gIiI7CiAgICAgIHZhciBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uOSA9IHRydWU7CiAgICAgIHZhciBfZGlkSXRlcmF0b3JFcnJvcjkgPSBmYWxzZTsKICAgICAgdmFyIF9pdGVyYXRvckVycm9yOSA9IHVuZGVmaW5lZDsKCiAgICAgIHRyeSB7CiAgICAgICAgZm9yICh2YXIgX2l0ZXJhdG9yOSA9IF90aGlzLmRpcmVjdHN1cHBseUZsYWdbU3ltYm9sLml0ZXJhdG9yXSgpLCBfc3RlcDk7ICEoX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjkgPSAoX3N0ZXA5ID0gX2l0ZXJhdG9yOS5uZXh0KCkpLmRvbmUpOyBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uOSA9IHRydWUpIHsKICAgICAgICAgIHZhciBpdGVtID0gX3N0ZXA5LnZhbHVlOwoKICAgICAgICAgIGlmIChpdGVtLnR5cGVDb2RlID09IHBhcmFtcy5yb3cuZGlyZWN0c3VwcGx5ZmxhZykgewogICAgICAgICAgICB2YWx1ZSA9IGl0ZW0udHlwZU5hbWU7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgX2RpZEl0ZXJhdG9yRXJyb3I5ID0gdHJ1ZTsKICAgICAgICBfaXRlcmF0b3JFcnJvcjkgPSBlcnI7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIGlmICghX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjkgJiYgX2l0ZXJhdG9yOS5yZXR1cm4gIT0gbnVsbCkgewogICAgICAgICAgICBfaXRlcmF0b3I5LnJldHVybigpOwogICAgICAgICAgfQogICAgICAgIH0gZmluYWxseSB7CiAgICAgICAgICBpZiAoX2RpZEl0ZXJhdG9yRXJyb3I5KSB7CiAgICAgICAgICAgIHRocm93IF9pdGVyYXRvckVycm9yOTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIHJldHVybiBoKCJkaXYiLCB2YWx1ZSk7CiAgICB9OyAvL+afpeeci+ivpuaDhQoKCiAgICB2YXIgcmVudGVyVmlld0FtbWV0ZXIgPSBmdW5jdGlvbiByZW50ZXJWaWV3QW1tZXRlcihoLCBwYXJhbXMpIHsKICAgICAgdmFyIGNvbHVtbiA9IHBhcmFtcy5jb2x1bW4ua2V5OwogICAgICByZXR1cm4gaCgiZGl2IiwgW2goInUiLCB7CiAgICAgICAgc3R5bGU6IHsKICAgICAgICAgIGRpc3BsYXk6ICJpbmxpbmUiCiAgICAgICAgfSwKICAgICAgICBvbjogewogICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICAgICAgICBfdGhpcy52aWV3QW1tZXRlcihwYXJhbXMucm93LmlkKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIHBhcmFtcy5yb3dbY29sdW1uXSldKTsKICAgIH07IC8v5p+l55yL5bGA56uZ6K+m5oOFCgoKICAgIHZhciByZW50ZXJWaWV3U3RhdGlvbiA9IGZ1bmN0aW9uIHJlbnRlclZpZXdTdGF0aW9uKGgsIHBhcmFtcykgewogICAgICB2YXIgY29sdW1uID0gcGFyYW1zLmNvbHVtbi5rZXk7CiAgICAgIHJldHVybiBoKCJkaXYiLCBbaCgidSIsIHsKICAgICAgICBvbjogewogICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICAgICAgICBfdGhpcy52aWV3U3RhdGlvbihwYXJhbXMucm93LnN0YXRpb25jb2RlKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIHBhcmFtcy5yb3dbY29sdW1uXSldKTsKICAgIH07IC8v6LWE5rqQ5bGA56uZaWQKCgogICAgdmFyIHRlcm1uYW1lRm9ybWF0ID0gZnVuY3Rpb24gdGVybW5hbWVGb3JtYXQoaCwgcGFyYW1zKSB7CiAgICAgIGlmICghcGFyYW1zLnJvdy5yZXNzdGF0aW9uY29kZSkgewogICAgICAgIC8vIOi/lOWbnue6ouiJsuaWh+Wtl+aPkOekuu+8muivt+e7tOaKpAogICAgICAgIHJldHVybiBoKCdkaXYnLCB7CiAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICBjb2xvcjogJyNmMDAnCiAgICAgICAgICB9LAogICAgICAgICAgb246IHsKICAgICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICAgICAgICAgIF90aGlzLiRNZXNzYWdlLmluZm8oIuWcqOS/ruaUueeUteihqOmhtemdou+8jOWFs+iBlOWxgOermeS/oeaBr+WkhOe7tOaKpCIpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwgJ+ivt+e7tOaKpCcpOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiBoKCdkaXYnLCBwYXJhbXMucm93LnJlc3N0YXRpb25jb2RlKTsKICAgICAgfQogICAgfTsKCiAgICB2YXIgcmVuZGVyVyA9IGZ1bmN0aW9uIHJlbmRlclcoaCwgcGFyYW1zKSB7CiAgICAgIHZhciB0aGF0ID0gX3RoaXM7CiAgICAgIHZhciB0ZXh0LAogICAgICAgICAgdHlwZSA9ICIiOwogICAgICB2YXIgcm93ID0gcGFyYW1zLnJvdzsKCiAgICAgIGlmIChyb3cuYmlsbFN0YXR1cyAhPSAwICYmIHJvdy5iaWxsU3RhdHVzICE9IDMgJiYgcm93LnByb2Nlc3NpbnN0SWQgIT0gbnVsbCkgewogICAgICAgIHRleHQgPSAi5p+l55yLIjsKICAgICAgICB0eXBlID0gInN1Y2Nlc3MiOwogICAgICB9IGVsc2UgaWYgKHBhcmFtcy5yb3cuYmlsbFN0YXR1cyA9PSAwIHx8IHBhcmFtcy5yb3cuYmlsbFN0YXR1cyA9PSAzKSB7CiAgICAgICAgdGV4dCA9ICLmj5DkuqQiOwogICAgICAgIHR5cGUgPSAicHJpbWFyeSI7CiAgICAgIH0KCiAgICAgIGlmICh0eXBlID09ICIiKSB7CiAgICAgICAgcmV0dXJuIGgoImRpdiIsIHt9LCB0ZXh0KTsKICAgICAgfQoKICAgICAgcmV0dXJuIGgoIkJ1dHRvbiIsIHsKICAgICAgICBwcm9wczogewogICAgICAgICAgdHlwZTogdHlwZSwKICAgICAgICAgIHNpemU6ICJzbWFsbCIKICAgICAgICB9LAogICAgICAgIG9uOiB7CiAgICAgICAgICBjbGljazogZnVuY3Rpb24gY2xpY2soKSB7CiAgICAgICAgICAgIGlmIChyb3cuYmlsbFN0YXR1cyAhPSAwICYmIHJvdy5iaWxsU3RhdHVzICE9IDMgJiYgcm93LnByb2Nlc3NpbnN0SWQgIT0gbnVsbCkgewogICAgICAgICAgICAgIHRoYXQuc2hvd0Zsb3cocGFyYW1zLnJvdywgcGFyYW1zLnJvdy5wcm9jZXNzaW5zdElkKTsKICAgICAgICAgICAgfSBlbHNlIGlmIChwYXJhbXMucm93LmJpbGxTdGF0dXMgPT0gMCB8fCBwYXJhbXMucm93LmJpbGxTdGF0dXMgPT0gMykgewogICAgICAgICAgICAgIHRoYXQubG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgdGhhdC5zdGFydEZsb3cocGFyYW1zLnJvdyk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIHRleHQpOwogICAgfTsKCiAgICB2YXIgcmVuZGVyUXVvdGEgPSBmdW5jdGlvbiByZW5kZXJRdW90YShoLCBwYXJhbXMpIHsKICAgICAgdmFyIHRoYXQgPSBfdGhpczsKICAgICAgcmV0dXJuIGgoIkJ1dHRvbiIsIHsKICAgICAgICBwcm9wczogewogICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgc2l6ZTogInNtYWxsIgogICAgICAgIH0sCiAgICAgICAgYXR0cnM6IHsKICAgICAgICAgIGRpc2FibGVkOiBwYXJhbXMucm93LnF1b3RhSWQgPyBmYWxzZSA6IHRydWUKICAgICAgICB9LAogICAgICAgIHN0eWxlOiB7CiAgICAgICAgICBvcGFjaXR5OiBwYXJhbXMucm93LnF1b3RhSWQgPyAxIDogMC40CiAgICAgICAgfSwKICAgICAgICBvbjogewogICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICAgICAgICB0aGF0LnZpZXdRdW90YShwYXJhbXMucm93LnF1b3RhSWQpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSwgIuafpeeciyIpOwogICAgfTsKCiAgICB2YXIgdmFsaXNoZXJlZGVwYXJ0bmFtZSA9IGZ1bmN0aW9uIHZhbGlzaGVyZWRlcGFydG5hbWUocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7CiAgICAgIC8vY29uc29sZS5sb2cocnVsZSwgdmFsdWUsICJydWxlLCB2YWx1ZTU1NTU1NTU1NTU1NTU1NTUiKTsKICAgICAgaWYgKHZhbHVlID09IG51bGwgfHwgdmFsdWUgPT0gJycpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+S4jeiDveS4uuepuicpKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICB9CiAgICB9OwoKICAgIHJldHVybiB7CiAgICAgIG11bHRpRmlsZXM6IG51bGwsCiAgICAgIHJlbW92ZUlkczogW10sCiAgICAgIGF0dGFjaERhdGE6IFtdLAogICAgICBmaWxlUGFyYW06IHsKICAgICAgICBidXNpSWQ6ICIiLAogICAgICAgIGJ1c2lBbGlhczogIumZhOS7tijljY/orq7nrqHnkIYpIiwKICAgICAgICBjYXRlZ29yeUNvZGU6ICJmaWxlIiwKICAgICAgICBhcmVhQ29kZTogImxuIgogICAgICB9LAogICAgICBydWxlVmFsaWRhdGU6IHsKICAgICAgICBhcHBseVJlYXNvbjogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgdmFsaWRhdG9yOiB2YWxpc2hlcmVkZXBhcnRuYW1lLAogICAgICAgICAgdHJpZ2dlcjogJ2NoYW5nZSwgYmx1cicKICAgICAgICB9XSwKICAgICAgICB3aGl0ZWxpc3RUeXBlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICB2YWxpZGF0b3I6IHZhbGlzaGVyZWRlcGFydG5hbWUsCiAgICAgICAgICB0cmlnZ2VyOiAnY2hhbmdlLCBibHVyJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGFkZFdoaXRlTGlzdDogewogICAgICAgIHdoaXRlbGlzdFR5cGU6ICIiLAogICAgICAgIGFwcGx5UmVhc29uOiAiIiwKICAgICAgICBpZDogIiIKICAgICAgfSwKICAgICAgYXR0YWNoOiB7CiAgICAgICAgZmlsZUZvcm06IHsKICAgICAgICAgIGZpbGU6IG51bGwKICAgICAgICB9LAogICAgICAgIGZvcm1MYXlvdXQ6IFt7CiAgICAgICAgICBsYWJlbDogJ+S4iuS8oOmZhOS7ticsCiAgICAgICAgICBwcm9wOiAnZmlsZScsCiAgICAgICAgICBmb3JtSXRlbVR5cGU6ICdmaWxlJywKICAgICAgICAgIHdpZHRoOiAzMDAsCiAgICAgICAgICBmb3JtYXQ6IHRoaXMuZm9ybWF0CiAgICAgICAgfV0sCiAgICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgICAgY29sdW1uczogW10sCiAgICAgICAgZGF0YTogW10KICAgICAgfSwKICAgICAgd2hpdGVMaXN0OiBmYWxzZSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHNob3dXb3JrRmxvdzogZmFsc2UsCiAgICAgIGNvbmZpZ1ZlcnNpb246IG51bGwsCiAgICAgIC8v54mI5pysCiAgICAgIGlzRGlzYWJsZTogZmFsc2UsCiAgICAgIGZpbHRlckNvbGw6IHRydWUsCiAgICAgIC8v5pCc57Si6Z2i5p2/5bGV5byACiAgICAgIGlzQWRtaW46IGZhbHNlLAogICAgICBjb21wYW55OiBudWxsLAogICAgICAvL+eUqOaIt+m7mOiupOWFrOWPuAogICAgICBjb3VudHJ5OiBudWxsLAogICAgICAvL+eUqOaIt+m7mOiupOaJgOWxnumDqOmXqAogICAgICBjb3VudHJ5TmFtZTogbnVsbCwKICAgICAgLy/nlKjmiLfpu5jorqTmiYDlsZ7pg6jpl6gKICAgICAgY2xhc3NpZmljYXRpb25EYXRhOiBbXSwKICAgICAgLy/nlKjnlLXnsbvlnosKICAgICAgY2xhc3NpZmljYXRpb25zOiBbXSwKICAgICAgLy/nlKjnlLXnsbvlnosKICAgICAgZGVtb0xpc3Q6IFtdLAogICAgICBpc2VudGl0eWFtbWV0ZXI6IHt9LAogICAgICBpc2VudGl0eWFtbWV0ZXJzOiBbXSwKICAgICAgc3RhdHVzOiBbXSwKICAgICAgLy/nirbmgIEKICAgICAgYmlsbFN0YXR1czogW10sCiAgICAgIC8v5Y2V5o2u54q25oCBCiAgICAgIGVsZWN0cm9UeXBlOiBbXSwKICAgICAgLy/nlKjnlLXnsbvlnosKICAgICAgYW1tZXRlclR5cGU6IFtdLAogICAgICAvL+eUteihqOexu+WeiwogICAgICBlbGVjdHJvTmF0dXJlOiBbXSwKICAgICAgLy/nlKjnlLXmgKfotKgKICAgICAgcGF5VHlwZTogW10sCiAgICAgIC8v5LuY6LS55pa55byPCiAgICAgIGVsZWN0cm92YWxlbmNlTmF0dXJlOiBbXSwKICAgICAgLy/nlLXku7fmgKfotKgKICAgICAgZGlyZWN0c3VwcGx5RmxhZzogW10sCiAgICAgIC8v5a+55aSW57uT566X57G75Z6LCiAgICAgIHByb3BlcnR5OiBbXSwKICAgICAgLy/kuqfmnYPlvZLlsZ4KICAgICAgcXVlcnlQYXJhbXNMaXN0OiB7CiAgICAgICAgcHJvamVjdG5hbWU6ICcnLAogICAgICAgIC8v6aG555uu5ZCN56ewCiAgICAgICAgbWV0ZXJDb2RlOiAnJywKICAgICAgICAvL+eUteihqOe8luWPtwogICAgICAgIHN0YXRpb25OYW1lOiAnJywKICAgICAgICAvL+WxgOermeWQjeensAogICAgICAgIGNpdHk6ICcnLAogICAgICAgIC8v5Zyw5biCCiAgICAgICAgZGlzdHJpY3Q6ICcnLAogICAgICAgIC8v5Yy65Y6/CiAgICAgICAgc3RhdHVzOiAnMScsCiAgICAgICAgLy/nirbmgIEKICAgICAgICBjb21wYW55OiAnJywKICAgICAgICBjb3VudHJ5TmFtZTogJycsCiAgICAgICAgYmkxMXN0YXR1c05hbWU6ICcnCiAgICAgIH0sCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgY291bnRyeTogbnVsbCwKICAgICAgICBjb21wYW55OiBudWxsLAogICAgICAgIGNvdW50cnlOYW1lOiBudWxsLAogICAgICAgIHJlc3N0YXRpb25jb2RlOiBudWxsLAogICAgICAgIHN0YXRpb25jb2RlNWdyOiBudWxsLAogICAgICAgIHN0YXRpb25uYW1lNWdyOiBudWxsCiAgICAgIH0sCiAgICAgIGNvbXBhbmllczogW10sCiAgICAgIGRlcGFydG1lbnRzOiBbXSwKICAgICAgbXVsdGlwbGVTZWxlY3Rpb25Sb3c6IFtdLAogICAgICB3b3JrRmxvd1BhcmFtczoge30sCiAgICAgIGhpc1BhcmFtczoge30sCiAgICAgIGV4cG9ydENvbHVtbnM6IFt7CiAgICAgICAgdGl0bGU6ICfnlLXooajnvJblj7cnLAogICAgICAgIGtleTogJ2FtbWV0ZXJuYW1lJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfpobnnm67lkI3np7AnLAogICAgICAgIGtleTogJ3Byb2plY3RuYW1lJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICflhbPogZTlsYDnq5nlkI0nLAogICAgICAgIGtleTogJ3N0YXRpb25OYW1lJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfmiYDlsZ7liIblhazlj7gnLAogICAgICAgIGtleTogJ2NvbXBhbnlOYW1lJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfmiYDlsZ7pg6jpl6gnLAogICAgICAgIGtleTogJ2NvdW50cnlOYW1lJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfnirbmgIEnLAogICAgICAgIGtleTogJ3N0YXR1c1N0cicKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5Y2V5o2u54q25oCBJywKICAgICAgICBrZXk6ICdiaWxsU3RhdHVzU3RyJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfnlKjnlLXnsbvlnosnLAogICAgICAgIGtleTogJ2VsZWN0cm90eXBlbmFtZScKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5a+55aSW57uT566X57G75Z6LJywKICAgICAgICBrZXk6ICdkaXJlY3RzdXBwbHlmbGFnU3RyJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfku5jotLnmlrnlvI8nLAogICAgICAgIGtleTogJ3BheXR5cGVTdHInCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+eUteS7t+aAp+i0qCcsCiAgICAgICAga2V5OiAnZWxlY3Ryb3ZhbGVuY2VuYXR1cmVTdHInCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+eUteihqOexu+WeiycsCiAgICAgICAga2V5OiAnYW1tZXRlcnR5cGVTdHInCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+S6p+adg+W9kuWxnicsCiAgICAgICAga2V5OiAncHJvcGVydHlTdHInCiAgICAgIH0sIC8vIHt0aXRsZTogJ+aUr+WxgC/liIblsYAnLGtleTogJ3N1YnN0YXRpb24nfSwKICAgICAgewogICAgICAgIHRpdGxlOiAn566h55CG6LSf6LSj5Lq6JywKICAgICAgICBrZXk6ICdhbW1ldGVybWFuYWdlcicKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5Yib5bu65pe26Ze0JywKICAgICAgICBrZXk6ICdjcmVhdGVUaW1lJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfkvpvnlLXlsYDnlLXooajmiLflj7fmiJbnvJblj7cnLAogICAgICAgIGtleTogJ3N1cHBseWJ1cmVhdWFtbWV0ZXJjb2RlJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfotYTmupDlsYDnq5lpZO+8micsCiAgICAgICAga2V5OiAndGVybW5hbWUnCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+WAjeeOh++8micsCiAgICAgICAga2V5OiAnbWFnbmlmaWNhdGlvbicKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5Y2V5Lu377yaJywKICAgICAgICBrZXk6ICdwcmljZScKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn5YiG5Ymy5q+U5L6L77yaJywKICAgICAgICBrZXk6ICdwZXJjZW50JwogICAgICB9XSwKICAgICAgZXhwb3J0OiB7CiAgICAgICAgcnVuOiBmYWxzZSwKICAgICAgICAvL+aYr+WQpuato+WcqOaJp+ihjOWvvOWHugogICAgICAgIGRhdGE6ICIiLAogICAgICAgIC8v5a+85Ye65pWw5o2uCiAgICAgICAgdG90YWxQYWdlOiAwLAogICAgICAgIC8v5LiA5YWx5aSa5bCR6aG1CiAgICAgICAgY3VycmVudFBhZ2U6IDAsCiAgICAgICAgLy/lvZPliY3lpJrlsJHpobUKICAgICAgICBwZXJjZW50OiAwLAogICAgICAgIHNpemU6IDIwMDAwMAogICAgICB9LAogICAgICBhbW1ldGVyOiB7CiAgICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgICAgY29sdW1uczogW3sKICAgICAgICAgIHRpdGxlOiAn55S16KGo57yW5Y+3JywKICAgICAgICAgIGtleTogJ2FtbWV0ZXJuYW1lJywKICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgIGNsYXNzTmFtZTogInRkLWlkIiwKICAgICAgICAgIHJlbmRlcjogcmVudGVyVmlld0FtbWV0ZXIsCiAgICAgICAgICBtaW5XaWR0aDogMTIwLAogICAgICAgICAgbWF4V2lkdGg6IDIwMAogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAn6aG555uu5ZCN56ewJywKICAgICAgICAgIGtleTogJ3Byb2plY3RuYW1lJywKICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgIGNsYXNzTmFtZTogInRkLWlkIiwKICAgICAgICAgIHJlbmRlcjogcmVudGVyVmlld0FtbWV0ZXIsCiAgICAgICAgICBtaW5XaWR0aDogMTIwLAogICAgICAgICAgbWF4V2lkdGg6IDIwMAogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAn5YWz6IGU5bGA56uZ5ZCNJywKICAgICAgICAgIGtleTogJ3N0YXRpb25OYW1lJywKICAgICAgICAgIGNsYXNzTmFtZTogInRkLWlkIiwKICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgIHJlbmRlcjogcmVudGVyVmlld1N0YXRpb24sCiAgICAgICAgICBtaW5XaWR0aDogMTAwLAogICAgICAgICAgbWF4V2lkdGg6IDIwMAogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAn6LWE5rqQ5bGA56uZaWQnLAogICAgICAgICAga2V5OiAndGVybW5hbWUnLAogICAgICAgICAgY2xhc3NOYW1lOiAidGQtaWQiLAogICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgcmVuZGVyOiB0ZXJtbmFtZUZvcm1hdCwKICAgICAgICAgIG1pbldpZHRoOiAxMDAsCiAgICAgICAgICBtYXhXaWR0aDogMjAwCiAgICAgICAgfSwgewogICAgICAgICAgdGl0bGU6ICfmiYDlsZ7liIblhazlj7gnLAogICAgICAgICAga2V5OiAnY29tcGFueU5hbWUnLAogICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgbWluV2lkdGg6IDEwMCwKICAgICAgICAgIG1heFdpZHRoOiAyMDAKICAgICAgICB9LCB7CiAgICAgICAgICB0aXRsZTogJ+aJgOWxnumDqOmXqCcsCiAgICAgICAgICBrZXk6ICdjb3VudHJ5TmFtZScsCiAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICBtaW5XaWR0aDogODAsCiAgICAgICAgICBtYXhXaWR0aDogMjAwCiAgICAgICAgfSwgewogICAgICAgICAgdGl0bGU6ICfnirbmgIEnLAogICAgICAgICAga2V5OiAnc3RhdHVzJywKICAgICAgICAgIHJlbmRlcjogcmVuZGVyU3RhdHVzLAogICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgbWluV2lkdGg6IDU1LAogICAgICAgICAgbWF4V2lkdGg6IDIwMAogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAn5Y2V5o2u54q25oCBJywKICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgIGtleTogJ2JpbGxTdGF0dXMnLAogICAgICAgICAgcmVuZGVyOiByZW5kZXJCaWxsU3RhdHVzLAogICAgICAgICAgbWluV2lkdGg6IDgwLAogICAgICAgICAgbWF4V2lkdGg6IDIwMAogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAn55So55S157G75Z6LJywKICAgICAgICAgIGtleTogJ2VsZWN0cm90eXBlbmFtZScsCiAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICBtaW5XaWR0aDogOTAsCiAgICAgICAgICBtYXhXaWR0aDogMjAwCiAgICAgICAgfSwgewogICAgICAgICAgdGl0bGU6ICflr7nlpJbnu5PnrpfnsbvlnosnLAogICAgICAgICAga2V5OiAnZGlyZWN0c3VwcGx5ZmxhZycsCiAgICAgICAgICByZW5kZXI6IHJlbmRlckRpcmVjdHN1cHBseWZsYWcsCiAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICBtaW5XaWR0aDogODAsCiAgICAgICAgICBtYXhXaWR0aDogMjAwCiAgICAgICAgfSwgewogICAgICAgICAgdGl0bGU6ICfku5jotLnmlrnlvI8nLAogICAgICAgICAga2V5OiAncGF5dHlwZScsCiAgICAgICAgICByZW5kZXI6IHJlbmRlclBheVR5cGUsCiAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICBtaW5XaWR0aDogNzAsCiAgICAgICAgICBtYXhXaWR0aDogMjAwCiAgICAgICAgfSwgewogICAgICAgICAgdGl0bGU6ICfnlLXku7fmgKfotKgnLAogICAgICAgICAga2V5OiAnZWxlY3Ryb3ZhbGVuY2VuYXR1cmUnLAogICAgICAgICAgcmVuZGVyOiByZW5kZXJFbGVjdHJvdmFsZW5jZU5hdHVyZSwKICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgIG1pbldpZHRoOiA3MCwKICAgICAgICAgIG1heFdpZHRoOiAyMDAKICAgICAgICB9LCB7CiAgICAgICAgICB0aXRsZTogJ+eUteihqOexu+WeiycsCiAgICAgICAgICBrZXk6ICdhbW1ldGVydHlwZScsCiAgICAgICAgICByZW5kZXI6IHJlbmRlckFtbWV0ZXJUeXBlLAogICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgbWluV2lkdGg6IDcwLAogICAgICAgICAgbWF4V2lkdGg6IDIwMAogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAn5Lqn5p2D5b2S5bGeJywKICAgICAgICAgIGtleTogJ3Byb3BlcnR5JywKICAgICAgICAgIHJlbmRlcjogcmVuZGVyUHJvcGVydHksCiAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICBtaW5XaWR0aDogNzAsCiAgICAgICAgICBtYXhXaWR0aDogMjAwCiAgICAgICAgfSwgLy8gewogICAgICAgIC8vICAgICB0aXRsZTogJ+aUr+WxgC/liIblsYAnLAogICAgICAgIC8vICAgICBrZXk6ICdzdWJzdGF0aW9uJywKICAgICAgICAvLyAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgIC8vICAgICBtaW5XaWR0aDogODAsCiAgICAgICAgLy8gICAgIG1heFdpZHRoOjIwMAogICAgICAgIC8vIH0sCiAgICAgICAgewogICAgICAgICAgdGl0bGU6ICfnrqHnkIbotJ/otKPkuronLAogICAgICAgICAga2V5OiAnYW1tZXRlcm1hbmFnZXInLAogICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgbWluV2lkdGg6IDkwLAogICAgICAgICAgbWF4V2lkdGg6IDIwMAogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAn5Yib5bu65pe26Ze0JywKICAgICAgICAgIGtleTogJ2NyZWF0ZVRpbWUnLAogICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgbWluV2lkdGg6IDExMCwKICAgICAgICAgIG1heFdpZHRoOiAyMDAKICAgICAgICB9LCB7CiAgICAgICAgICB0aXRsZTogJ+S4iuS8oOmZhOS7ticsCiAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICBtaW5XaWR0aDogNzAsCiAgICAgICAgICBtYXhXaWR0aDogMjAwLAogICAgICAgICAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCwgcGFyYW1zKSB7CiAgICAgICAgICAgIHZhciByb3cgPSBwYXJhbXMucm93OwogICAgICAgICAgICB2YXIgdGV4dCA9IHJvdy5pc0F0dGFjaCA9PT0gMCA/ICfmnKrkuIrkvKAnIDogJ+W3suS4iuS8oCc7CiAgICAgICAgICAgIHJldHVybiBoKCdzcGFuJywge30sIHRleHQpOwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIHRpdGxlOiAi5rWB56iLIiwKICAgICAgICAgIGZpeGVkOiAncmlnaHQnLAogICAgICAgICAga2V5OiAiYWN0aW9uIiwKICAgICAgICAgIG1pbldpZHRoOiA2MCwKICAgICAgICAgIG1heFdpZHRoOiAyMDAsCiAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICByZW5kZXI6IHJlbmRlclcKICAgICAgICB9LCB7CiAgICAgICAgICB0aXRsZTogIuafpeeci+WumuminSIsCiAgICAgICAgICBmaXhlZDogJ3JpZ2h0JywKICAgICAgICAgIGtleTogImFjdGlvbiIsCiAgICAgICAgICBtaW5XaWR0aDogNjUsCiAgICAgICAgICBtYXhXaWR0aDogMjAwLAogICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgcmVuZGVyOiByZW5kZXJRdW90YQogICAgICAgIH1dLAogICAgICAgIGRhdGE6IFtdLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9CiAgICB9OwogIH0sCiAgbWV0aG9kczogX29iamVjdFNwcmVhZCh7fSwgbWFwTXV0YXRpb25zKFsiY2xvc2VUYWciLCAiY2xvc2VUYWdCeU5hbWUiXSksIHsKICAgIC8v6aqM6K+B6ZSZ6K+v5by55Ye65o+Q56S65qGGCiAgICBlcnJvclRpcHM6IGZ1bmN0aW9uIGVycm9yVGlwcyhzdHIpIHsKICAgICAgdGhpcy4kTm90aWNlLmVycm9yKHsKICAgICAgICB0aXRsZTogJ+aPkOekuicsCiAgICAgICAgZGVzYzogc3RyLAogICAgICAgIGR1cmF0aW9uOiAxMAogICAgICB9KTsKICAgIH0sCiAgICAvLyB0eXBlTGlzdAogICAgc3VibWl0V2hpdGVMaXN0OiBmdW5jdGlvbiBzdWJtaXRXaGl0ZUxpc3QoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwoKICAgICAgdmFyIGFyciA9IFtdOwogICAgICB2YXIgZGF0YSA9IHRoaXMuJHJlZnMuYW1tZXRlclRhYmxlLmdldFNlbGVjdGlvbigpOwogICAgICBjb25zb2xlLmxvZyhkYXRhWzBdLmFtbWV0ZXJuYW1lLCAiZGF0YS5hbW1ldGVybmFtZSIpOwogICAgICBjb25zb2xlLmxvZyh0aGlzLmF0dGFjaC5maWxlRm9ybS5maWxlLCAidGhpcy5hdHRhY2guZmlsZUZvcm0uZmlsZSIpOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICB0aGlzLmFkZFdoaXRlTGlzdC53aGl0ZWxpc3RUeXBlLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICBhcnIucHVzaCh7CiAgICAgICAgICB3aGl0ZWxpc3RUeXBlOiBpdGVtCiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICB2YXIgcGFyYW1zID0gewogICAgICAgIHR5cGVMaXN0OiBhcnIsCiAgICAgICAgYXBwbHlBcmd1bWVudDogdGhpcy5hZGRXaGl0ZUxpc3QuYXBwbHlSZWFzb24sCiAgICAgICAgbWV0ZXJDb2RlOiBkYXRhWzBdLmFtbWV0ZXJuYW1lLAogICAgICAgICJmaiI6ICLmnKrkuIrkvKDpmYTku7bvvJsiLAogICAgICAgIC8vIHR5cGVMaXN0LndoaXRlbGlzdFR5cGU6ICcxJywKICAgICAgICBkd2pzbHg6IGRhdGFbMF0uZGlyZWN0c3VwcGx5ZmxhZyA9PSAxID8gJ+ebtOS+mycgOiBkYXRhWzBdLmRpcmVjdHN1cHBseWZsYWcgPT0gMiA/ICfovazkvpsnIDogIiIKICAgICAgfTsKICAgICAgY29uc29sZS5sb2cocGFyYW1zLCAicGFyYW1zIik7CiAgICAgIHdoaXRlSW5zZXJ0KHBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgY29uc29sZS5sb2cocmVzLCAicmVzNjY2NjY2NjY2NjY2Iik7CiAgICAgICAgX3RoaXMyLndoaXRlTGlzdCA9IGZhbHNlOwoKICAgICAgICBpZiAocmVzLmRhdGEuY29kZSA9PSA1MDApIHsKICAgICAgICAgIF90aGlzMi5sb2FkaW5nID0gZmFsc2U7IC8vIHRoaXMuJE1lc3NhZ2UuZXJyb3IocmVzLmRhdGEubXNnKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXMyLiRNZXNzYWdlLnN1Y2Nlc3MoIuS/neWtmOaIkOWKnyIpOwoKICAgICAgICAgIF90aGlzMi5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICBfdGhpczIuaWQxID0gcmVzLmRhdGEuaWQ7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICByZW1vdmVBdHRhY2g6IGZ1bmN0aW9uIHJlbW92ZUF0dGFjaCgpIHsKICAgICAgX3JlbW92ZUF0dGFjaCh7CiAgICAgICAgaWRzOiB0aGlzLnJlbW92ZUlkcy5qb2luKCkKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7fSk7CiAgICB9LAogICAgc2V0QXR0YWNoRGF0YTogZnVuY3Rpb24gc2V0QXR0YWNoRGF0YShkYXRhKSB7CiAgICAgIGNvbnNvbGUubG9nKGRhdGEsICJkYXRhNTU1NTU1NTU1NTU1NTU1Iik7CiAgICAgIHRoaXMubXVsdGlGaWxlcyA9IGRhdGEuZGF0YTsKICAgICAgdGhpcy5yZW1vdmVJZHMgPSBkYXRhLmlkczsKCiAgICAgIGlmICh0aGlzLnJlbW92ZUlkcy5sZW5ndGggIT0gMCAmJiBkYXRhLnR5cGUgPT0gJ3JlbW92ZScpIHsKICAgICAgICB0aGlzLnJlbW92ZUF0dGFjaCgpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMudXBsb2FkKCk7CiAgICAgIH0KICAgIH0sCiAgICB1cGxvYWQ6IGZ1bmN0aW9uIHVwbG9hZCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CgogICAgICBpZiAodGhpcy5hdHRhY2hEYXRhLmxlbmd0aCAhPSAwICYmIHRoaXMubXVsdGlGaWxlcy5sZW5ndGggIT0gMCkgewogICAgICAgIC8vIHRoaXMuJE1lc3NhZ2UuaW5mbygi5o+Q56S6OuS4iuS8oOaWh+S7tui/h+Wkp+WPr+iDveWvvOiHtOS4iuS8oOWksei0pe+8gSIpOwogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICAgYXhpb3MucmVxdWVzdCh7CiAgICAgICAgICB1cmw6ICcvY29tbW9uL2F0dGFjaG1lbnRzL3VwbG9hZE11bHRpRmlsZScsCiAgICAgICAgICBtZXRob2Q6ICdwb3N0JywKICAgICAgICAgIGRhdGE6IHRoaXMubXVsdGlGaWxlcwogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgaWYgKHJlcy5kYXRhLmNvZGUgIT0gMCkgewogICAgICAgICAgICBfdGhpczMubG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgfQoKICAgICAgICAgIHZhciB0aGF0ID0gX3RoaXMzOyAvLyBpZih0aGF0LmZpbGVQYXJhbS5idXNpSWQgPT0gIiIpIHsKICAgICAgICAgIC8vICAgICB0aGlzLiRNZXNzYWdlLmVycm9yKCJjdW93dSIpOwogICAgICAgICAgLy8gfWVsc2UgewoKICAgICAgICAgIGF0dGNoTGlzdCh7CiAgICAgICAgICAgIGJ1c2lJZDogdGhhdC5maWxlUGFyYW0uYnVzaUlkCiAgICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgY29uc29sZS5sb2cocmVzLCAiYXR0Y2hMaXN0Iik7CiAgICAgICAgICAgIHRoYXQuYXR0YWNoRGF0YSA9IE9iamVjdC5hc3NpZ24oW10sIHJlcy5kYXRhLnJvd3MpOwogICAgICAgICAgfSk7IC8vIH0KICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIGdldEFjY291bnRNZXNzYWdlczogZnVuY3Rpb24gZ2V0QWNjb3VudE1lc3NhZ2VzKCkgewogICAgICAvLyB0aGlzLmxpc3RUYi5sb2FkaW5nID0gdHJ1ZTsKICAgICAgd2hpdGVMaXN0KHsvLyBtZXRlckNvZGU6IHRoaXMucXVlcnlQYXJhbXNMaXN0Lm1ldGVyQ29kZSwKICAgICAgICAvLyBwcm9qZWN0bmFtZTogdGhpcy5xdWVyeVBhcmFtc0xpc3QucHJvamVjdG5hbWUsCiAgICAgICAgLy8gc3RhdGlvbk5hbWU6IHRoaXMucXVlcnlQYXJhbXNMaXN0LnN0YXRpb25OYW1lLAogICAgICAgIC8vIGNvbXBhbnk6IHRoaXMucXVlcnlQYXJhbXNMaXN0LmNvbXBhbnksCiAgICAgICAgLy8gY291bnRyeU5hbWU6IHRoaXMucXVlcnlQYXJhbXNMaXN0LmNvdW50cnlOYW1lLAogICAgICAgIC8vIHN0YXR1czogdGhpcy5xdWVyeVBhcmFtc0xpc3Quc3RhdHVzLAogICAgICAgIC8vIHNpemU6IHRoaXMucGFnZVNpemUsCiAgICAgICAgLy8gY3VycmVudDogdGhpcy5wYWdlTnVtCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIC8vIHRoaXMubGlzdFRiLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICBjb25zb2xlLmxvZyhyZXMsICJxdWVyeVBhcmFtc0xpc3QgcmVzIik7CiAgICAgICAgY29uc29sZS5sb2cocmVzLmRhdGEubGVuZ3RoLCAicmVzLmRhdGEubGVuZ3RoIik7IC8vIHRoaXMucGFnZVRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgICAgLy8gdGhpcy5pbnNpZGVEYXRhID0gcmVzLmRhdGEucm93czsKICAgICAgfSk7CiAgICB9LAogICAgYXBwbHlXOiBmdW5jdGlvbiBhcHBseVcoKSB7CiAgICAgIHZhciBkYXRhID0gdGhpcy4kcmVmcy5hbW1ldGVyVGFibGUuZ2V0U2VsZWN0aW9uKCk7CiAgICAgIGNvbnNvbGUubG9nKGRhdGEsICJkYXRhNTU1NTU1NTU1NTU1NTUiKTsKICAgICAgdGhpcy5maWxlUGFyYW0uYnVzaUlkID0gZGF0YVswXS5pZDsKCiAgICAgIGlmIChkYXRhLmxlbmd0aCA+IDEpIHsKICAgICAgICB0aGlzLmVycm9yVGlwcygi5Y+q6IO96YCJ5oup5LiA5Liq55S16KGo55Sz6K+35Yqg5YWl55m95ZCN5Y2VIik7CiAgICAgIH0gZWxzZSBpZiAoZGF0YS5sZW5ndGggPT0gMCkgewogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fpgInmi6nkuIDkuKrnlLXooajnlLPor7fliqDlhaXnmb3lkI3ljZUiKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOwogICAgICAgIC8vIGRhdGFbMF0ubmFtZQogICAgICAgIHRoaXMuYXR0YWNoRGF0YSA9IFtdOwogICAgICAgIHRoaXMuYWRkV2hpdGVMaXN0ID0gewogICAgICAgICAgd2hpdGVsaXN0VHlwZTogIiIsCiAgICAgICAgICBhcHBseVJlYXNvbjogIiIsCiAgICAgICAgICBpZDogIiIKICAgICAgICB9OwogICAgICAgIHRoaXMud2hpdGVMaXN0ID0gdHJ1ZTsKICAgICAgfQogICAgfSwKICAgIG9uTW9kYWxPSzogZnVuY3Rpb24gb25Nb2RhbE9LKCkgewogICAgICB0aGlzLiRNZXNzYWdlLmVycm9yKCfnoa7lrponKTsKICAgIH0sCiAgICBvbk1vZGFsQ2FuY2VsOiBmdW5jdGlvbiBvbk1vZGFsQ2FuY2VsKCkgewogICAgICB0aGlzLiRNZXNzYWdlLmVycm9yKCflj5bmtognKTsKICAgIH0sCgogICAgLyrliKDpmaQqLwogICAgcmVtb3ZlQW1tZXRlcjogZnVuY3Rpb24gcmVtb3ZlQW1tZXRlcihpZCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKCiAgICAgIHZhciBtdWx0aXBsZVNlbGVjdGlvbiA9IFtdOwoKICAgICAgaWYgKHRoaXMubXVsdGlwbGVTZWxlY3Rpb25Sb3cubGVuZ3RoID4gMCkgewogICAgICAgIHZhciBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uMTAgPSB0cnVlOwogICAgICAgIHZhciBfZGlkSXRlcmF0b3JFcnJvcjEwID0gZmFsc2U7CiAgICAgICAgdmFyIF9pdGVyYXRvckVycm9yMTAgPSB1bmRlZmluZWQ7CgogICAgICAgIHRyeSB7CiAgICAgICAgICBmb3IgKHZhciBfaXRlcmF0b3IxMCA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb25Sb3dbU3ltYm9sLml0ZXJhdG9yXSgpLCBfc3RlcDEwOyAhKF9pdGVyYXRvck5vcm1hbENvbXBsZXRpb24xMCA9IChfc3RlcDEwID0gX2l0ZXJhdG9yMTAubmV4dCgpKS5kb25lKTsgX2l0ZXJhdG9yTm9ybWFsQ29tcGxldGlvbjEwID0gdHJ1ZSkgewogICAgICAgICAgICB2YXIgaXRlbSA9IF9zdGVwMTAudmFsdWU7CgogICAgICAgICAgICBpZiAoaXRlbS5iaWxsU3RhdHVzICE9IDApIHsKICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIuaJgOmAieaVsOaNruWMheWQq+mdnuiNieeov+aVsOaNru+8jOS4jeiDveWIoOmZpO+8gSIpOwogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgbXVsdGlwbGVTZWxlY3Rpb24ucHVzaChpdGVtLmlkKTsKICAgICAgICAgIH0KICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgIF9kaWRJdGVyYXRvckVycm9yMTAgPSB0cnVlOwogICAgICAgICAgX2l0ZXJhdG9yRXJyb3IxMCA9IGVycjsKICAgICAgICB9IGZpbmFsbHkgewogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgaWYgKCFfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uMTAgJiYgX2l0ZXJhdG9yMTAucmV0dXJuICE9IG51bGwpIHsKICAgICAgICAgICAgICBfaXRlcmF0b3IxMC5yZXR1cm4oKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgICAgaWYgKF9kaWRJdGVyYXRvckVycm9yMTApIHsKICAgICAgICAgICAgICB0aHJvdyBfaXRlcmF0b3JFcnJvcjEwOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICBpZCA9IG11bHRpcGxlU2VsZWN0aW9uLmpvaW4oJywnKTsKICAgICAgICB0aGlzLiRNb2RhbC5jb25maXJtKHsKICAgICAgICAgIHRpdGxlOiAn5rip6aao5o+Q56S6JywKICAgICAgICAgIGNvbnRlbnQ6ICc8cD7noa7orqTliKDpmaTlkJc/PC9wPicsCiAgICAgICAgICBvbk9rOiBmdW5jdGlvbiBvbk9rKCkgewogICAgICAgICAgICBfdGhpczQuYW1tZXRlci5sb2FkaW5nID0gdHJ1ZTsKCiAgICAgICAgICAgIF9yZW1vdmVBbW1ldGVyKHsKICAgICAgICAgICAgICBpZHM6IGlkCiAgICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgIF90aGlzNC4kTWVzc2FnZS5zdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKCiAgICAgICAgICAgICAgX3RoaXM0Ll9vblNlYXJjaEhhbmRsZSgpOwoKICAgICAgICAgICAgICBfdGhpczQuYW1tZXRlci5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgX3RoaXM0Lm11bHRpcGxlU2VsZWN0aW9uUm93ID0gW107CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLor7foh7PlsJHpgInmi6nkuIDooYwiKTsKICAgICAgfQogICAgfSwKCiAgICAvKue8lui+kSovCiAgICBlZGl0QW1tZXRlcjogZnVuY3Rpb24gZWRpdEFtbWV0ZXIoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwoKICAgICAgaWYgKHRoaXMubXVsdGlwbGVTZWxlY3Rpb25Sb3cubGVuZ3RoID09IDEpIHsKICAgICAgICB2YXIgcm93ID0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvblJvd1swXTsKICAgICAgICBzZWxlY3RDaGFuZ2VBbW1ldGVyKHsKICAgICAgICAgIGlkOiByb3cuaWQKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgIC8v5a2Y5Zyo5LqO5Luj5Yqe5Lit5pe277yM5oql5Ye65o+Q56S6CiAgICAgICAgICBpZiAocmVzLmRhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgICBfdGhpczUuJE1vZGFsLndhcm5pbmcoewogICAgICAgICAgICAgIHRpdGxlOiAi5rip6aao5o+Q56S6IiwKICAgICAgICAgICAgICBjb250ZW50OiAi6K+l55S16KGo5bey57uP5a2Y5Zyo5o2i6KGo55S16KGo44CQ55S16KGo57yW5Y+377yaIiArIHJlcy5kYXRhWzBdLmFtbWV0ZXJuYW1lICsgIu+8jOmhueebruWQjeensO+8miIgKyByZXMuZGF0YVswXS5wcm9qZWN0bmFtZSArICLjgJEs5LiN5YWB6K645YaN5L+u5pS5IgogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGlzSW5Ub2RvTGlzdChyb3cuaWQsIDEpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgIC8v5a2Y5Zyo5LqO5Luj5Yqe5Lit5pe277yM5oql5Ye65o+Q56S6CiAgICAgICAgICAgICAgdmFyIG93bmVybmFtZSA9ICIiOwoKICAgICAgICAgICAgICBpZiAocmVzLmRhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCByZXMuZGF0YS5sZW5ndGg7IGkrKykgewogICAgICAgICAgICAgICAgICBvd25lcm5hbWUgKz0gcmVzLmRhdGFbaV0ub3duZXJuYW1lICsgJyAnOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIF90aGlzNS4kTW9kYWwud2FybmluZyh7CiAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5rip6aao5o+Q56S6IiwKICAgICAgICAgICAgICAgICAgY29udGVudDogIuivpeaVsOaNruWtmOWcqOS6jiIgKyBvd25lcm5hbWUgKyAi55qE5rWB56iL5Luj5Yqe5Lit77yM5aSE55CG5ZCO5omN5Y+v5L+u5pS55pWw5o2uIgogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIGNoZWNrQWNvdW50QnlVcGRhdGUoewogICAgICAgICAgICAgICAgICBpZDogcm93LmlkCiAgICAgICAgICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICAgICAgLy/kv67mlLnmlbDmja7liY3pqozor4Hlj7DotKYKICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhID09IC0xKSB7CiAgICAgICAgICAgICAgICAgICAgX3RoaXM1LiRNb2RhbC53YXJuaW5nKHsKICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5rip6aao5o+Q56S6IiwKICAgICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICLor6XmlbDmja7lt7Lloavlhpnlj7DotKbmiJbmraPlnKjmiqXotKbkuK3vvIzlpITnkIblkI7miY3lj6/kv67mlLnmlbDmja4iCiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgX3RoaXM1LmNsb3NlVGFnQnlOYW1lKHsKICAgICAgICAgICAgICAgICAgICAgIHJvdXRlOiBnZXRIb21lUm91dGUocm91dGVycywgImVkaXRBbW1ldGVyIikKICAgICAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgICAgICAgX3RoaXM1LiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICBuYW1lOiAiZWRpdEFtbWV0ZXIiLAogICAgICAgICAgICAgICAgICAgICAgcXVlcnk6IHsKICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IHJvdy5pZAogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIHJlcGxhY2U6IHRydWUKICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyKSB7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coZXJyKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLor7fpgInmi6nlhbbkuK3kuIDooYwiKTsKICAgICAgfQogICAgfSwKCiAgICAvKuaNouihqCovCiAgICBjaGFuZ2VBbW1ldGVyOiBmdW5jdGlvbiBjaGFuZ2VBbW1ldGVyKCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKCiAgICAgIGlmICh0aGlzLm11bHRpcGxlU2VsZWN0aW9uUm93Lmxlbmd0aCA9PSAxKSB7CiAgICAgICAgdmFyIHJvdyA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb25Sb3dbMF07IC8vIGlmKHJvdy5wcm9wZXJ0eSE9MikgewoKICAgICAgICBzZWxlY3RDaGFuZ2VBbW1ldGVyKHsKICAgICAgICAgIGlkOiByb3cuaWQKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgIC8v5a2Y5Zyo5LqO5Luj5Yqe5Lit5pe277yM5oql5Ye65o+Q56S6CiAgICAgICAgICBpZiAocmVzLmRhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgICBfdGhpczYuJE1vZGFsLndhcm5pbmcoewogICAgICAgICAgICAgIHRpdGxlOiAi5rip6aao5o+Q56S6IiwKICAgICAgICAgICAgICBjb250ZW50OiAi6K+l55S16KGo5bey57uP5a2Y5Zyo5o2i6KGo55S16KGo44CQ55S16KGo57yW5Y+377yaIiArIHJlcy5kYXRhWzBdLmFtbWV0ZXJuYW1lICsgIu+8jOmhueebruWQjeensO+8miIgKyByZXMuZGF0YVswXS5wcm9qZWN0bmFtZSArICLjgJEiCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgaXNJblRvZG9MaXN0KHJvdy5pZCwgMSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgLy/lrZjlnKjkuo7ku6Plip7kuK3ml7bvvIzmiqXlh7rmj5DnpLoKICAgICAgICAgICAgICB2YXIgb3duZXJuYW1lID0gIiI7CgogICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHJlcy5kYXRhLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICAgICAgICAgIG93bmVybmFtZSArPSByZXMuZGF0YVtpXS5vd25lcm5hbWUgKyAnICc7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgX3RoaXM2LiRNb2RhbC53YXJuaW5nKHsKICAgICAgICAgICAgICAgICAgdGl0bGU6ICLmuKnppqjmj5DnpLoiLAogICAgICAgICAgICAgICAgICBjb250ZW50OiAi6K+l5pWw5o2u5a2Y5Zyo5LqOIiArIG93bmVybmFtZSArICLnmoTmtYHnqIvku6Plip7kuK3vvIzlpITnkIblkI7miY3lj6/kv67mlLnmlbDmja4iCiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXM2LmNoZWNrQ2hhbmdlQW1tZXRlcihyb3cpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7IC8vIH1lbHNlewogICAgICAgIC8vICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIuivpeihqOermeWdgOS6p+adg+W9kuWxnuS4uumTgeWhlO+8jOS4jeiDvei/m+ihjOaNouihqOaTjeS9nCIpOwogICAgICAgIC8vIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIuivt+mAieaLqeWFtuS4reS4gOihjCIpOwogICAgICB9CiAgICB9LAogICAgY2hlY2tDaGFuZ2VBbW1ldGVyOiBmdW5jdGlvbiBjaGVja0NoYW5nZUFtbWV0ZXIocm93KSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwoKICAgICAgaWYgKHJvdy5iaWxsU3RhdHVzICE9IDApIHsKICAgICAgICB0aGlzLiRNb2RhbC5jb25maXJtKHsKICAgICAgICAgIHRpdGxlOiAn5rip6aao5o+Q56S6JywKICAgICAgICAgIGNvbnRlbnQ6ICc8cD7mjaLooajmtYHnqIvnu5PmnZ/vvIzml6fooajlsIblgZznlKjvvIzmlrDooajlkK/nlKjvvIzor7fnoa7orqTmmK/lkKbmjaLooajvvJ88L3A+JywKICAgICAgICAgIG9uT2s6IGZ1bmN0aW9uIG9uT2soKSB7CiAgICAgICAgICAgIF90aGlzNy5jbG9zZVRhZ0J5TmFtZSh7CiAgICAgICAgICAgICAgcm91dGU6IGdldEhvbWVSb3V0ZShyb3V0ZXJzLCAiY2hhbmdlQW1tZXRlciIpCiAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgX3RoaXM3LiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgICAgbmFtZTogImNoYW5nZUFtbWV0ZXIiLAogICAgICAgICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICAgICAgICBpZDogcm93LmlkCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICByZXBsYWNlOiB0cnVlCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbygi6I2J56i/54q25oCB5LiN6IO95pON5L2c5o2i6KGo5pWw5o2uIik7CiAgICAgIH0KICAgIH0sCgogICAgLyrmn6XnnIsqLwogICAgdmlld0FtbWV0ZXI6IGZ1bmN0aW9uIHZpZXdBbW1ldGVyKGlkKSB7CiAgICAgIHRoaXMuJHJlZnMudmlld0FtbWV0ZXJQYWdlLmluaXRBbW1ldGVyKGlkKTsKICAgIH0sCgogICAgLyrmn6XnnIvlsYDnq5kqLwogICAgdmlld1N0YXRpb246IGZ1bmN0aW9uIHZpZXdTdGF0aW9uKGlkKSB7CiAgICAgIHRoaXMuJHJlZnMudmlld1N0YXRpb25QYWdlLmluaXRTdGF0aW9uKGlkKTsKICAgIH0sCgogICAgLyrmn6XnnIvlrprpop0qLwogICAgdmlld1F1b3RhOiBmdW5jdGlvbiB2aWV3UXVvdGEoaWQpIHsKICAgICAgdGhpcy4kcmVmcy52aWV3UXVvdGFQYWdlLmluaXRRdW90YShpZCk7CiAgICB9LAoKICAgIC8q5re75YqgKi8KICAgIGFkZEFtbWV0ZXI6IGZ1bmN0aW9uIGFkZEFtbWV0ZXIoKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBuYW1lOiAiYWRkQW1tZXRlciIsCiAgICAgICAgcXVlcnk6IHt9LAogICAgICAgIHJlcGxhY2U6IHRydWUKICAgICAgfSk7IC8vIHRoaXMuJHJlZnMuYWRkQW1tZXRlclBhZ2UuaW5pdEFtbWV0ZXIoKTsKICAgIH0sCiAgICBzZWxlY3RDaGFuZ2U6IGZ1bmN0aW9uIHNlbGVjdENoYW5nZSgpIHsKICAgICAgdmFyIHRoYXQgPSB0aGlzOwoKICAgICAgaWYgKHRoaXMucXVlcnlQYXJhbXMuY29tcGFueSAhPSB1bmRlZmluZWQpIHsKICAgICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5jb21wYW55ID09ICItMSIpIHsKICAgICAgICAgIHRoYXQucXVlcnlQYXJhbXMuY291bnRyeSA9IC0xOwogICAgICAgICAgdGhhdC5xdWVyeVBhcmFtcy5jb3VudHJ5TmFtZSA9IG51bGw7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGdldENvdW50cnlCeVVzZXJJZCh0aGF0LnF1ZXJ5UGFyYW1zLmNvbXBhbnkpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBpZiAocmVzLmRhdGEuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApIHsKICAgICAgICAgICAgICB0aGF0LnF1ZXJ5UGFyYW1zLmNvdW50cnkgPSByZXMuZGF0YS5kZXBhcnRtZW50c1swXS5pZDsKICAgICAgICAgICAgICB0aGF0LnF1ZXJ5UGFyYW1zLmNvdW50cnlOYW1lID0gcmVzLmRhdGEuZGVwYXJ0bWVudHNbMF0ubmFtZTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgc2V0RWxlY3Ryb3lUeXBlOiBmdW5jdGlvbiBzZXRFbGVjdHJveVR5cGUoKSB7CiAgICAgIHZhciB0eXBlcyA9IHRoaXMuY2xhc3NpZmljYXRpb25zOwoKICAgICAgaWYgKHR5cGVzLmxlbmd0aCAhPSAwKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbGVjdHJvdHlwZSA9IHR5cGVzW3R5cGVzLmxlbmd0aCAtIDFdOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZWxlY3Ryb3R5cGUgPSBudWxsOwogICAgICB9CiAgICB9LAogICAgX29uUmVzZXRIYW5kbGU6IGZ1bmN0aW9uIF9vblJlc2V0SGFuZGxlKCkgewogICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uUm93ID0gW107CiAgICAgIHRoaXMuY2xhc3NpZmljYXRpb25zID0gW107CiAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7CiAgICAgICAgdHlwZTogMCwKICAgICAgICBjb21wYW55OiBudWxsLAogICAgICAgIGNvdW50cnk6IG51bGwsCiAgICAgICAgY291bnRyeU5hbWU6IG51bGwsCiAgICAgICAgc3RhdGlvbmNvZGU1Z3I6IG51bGwsCiAgICAgICAgc3RhdGlvbm5hbWU1Z3I6IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb21wYW55ID0gdGhpcy5jb21wYW55OwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvdW50cnkgPSBOdW1iZXIodGhpcy5jb3VudHJ5KTsKICAgICAgdGhpcy4kcmVmcy5hbW1ldGVyVGFibGUucXVlcnkodGhpcy5xdWVyeVBhcmFtcyk7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY291bnRyeU5hbWUgPSB0aGlzLmNvdW50cnlOYW1lOwogICAgfSwKICAgIF9vblNlYXJjaEhhbmRsZTogZnVuY3Rpb24gX29uU2VhcmNoSGFuZGxlKCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKCiAgICAgIHRoaXMuaXNEaXNhYmxlID0gdHJ1ZTsKICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM4LmlzRGlzYWJsZSA9IGZhbHNlOyAvL+eCueWHu+S4gOasoeaXtumalOS4pOenkuWQjuaJjeiDveWGjeasoeeCueWHuwogICAgICB9LCAyMDAwKTsKICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvblJvdyA9IFtdOwogICAgICB0aGlzLnNldEVsZWN0cm95VHlwZSgpOwoKICAgICAgaWYgKHRoaXMucXVlcnlQYXJhbXMuY291bnRyeU5hbWUgPT0gIiIpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvdW50cnkgPSAiLTEiOwogICAgICB9CgogICAgICB0aGlzLiRyZWZzLmFtbWV0ZXJUYWJsZS5xdWVyeSh0aGlzLnF1ZXJ5UGFyYW1zKTsgLy8gdGhpcy5xdWVyeSh0aGlzLnF1ZXJ5UGFyYW1zKTsKICAgIH0sCiAgICBzZXREaXNhYmxlZDogZnVuY3Rpb24gc2V0RGlzYWJsZWQoKSB7CiAgICAgIHZhciBfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uMTEgPSB0cnVlOwogICAgICB2YXIgX2RpZEl0ZXJhdG9yRXJyb3IxMSA9IGZhbHNlOwogICAgICB2YXIgX2l0ZXJhdG9yRXJyb3IxMSA9IHVuZGVmaW5lZDsKCiAgICAgIHRyeSB7CiAgICAgICAgZm9yICh2YXIgX2l0ZXJhdG9yMTEgPSB0aGlzLiRyZWZzLmFtbWV0ZXJUYWJsZS5pbnNpZGVEYXRhW1N5bWJvbC5pdGVyYXRvcl0oKSwgX3N0ZXAxMTsgIShfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uMTEgPSAoX3N0ZXAxMSA9IF9pdGVyYXRvcjExLm5leHQoKSkuZG9uZSk7IF9pdGVyYXRvck5vcm1hbENvbXBsZXRpb24xMSA9IHRydWUpIHsKICAgICAgICAgIHZhciBpdGVtID0gX3N0ZXAxMS52YWx1ZTsKCiAgICAgICAgICBpZiAoaXRlbS5iaWxsU3RhdHVzICE9IDApIHsKICAgICAgICAgICAgaXRlbS5fZGlzYWJsZWQgPSB0cnVlOyAvL+emgeatoumAieaLqQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgX2RpZEl0ZXJhdG9yRXJyb3IxMSA9IHRydWU7CiAgICAgICAgX2l0ZXJhdG9yRXJyb3IxMSA9IGVycjsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0cnkgewogICAgICAgICAgaWYgKCFfaXRlcmF0b3JOb3JtYWxDb21wbGV0aW9uMTEgJiYgX2l0ZXJhdG9yMTEucmV0dXJuICE9IG51bGwpIHsKICAgICAgICAgICAgX2l0ZXJhdG9yMTEucmV0dXJuKCk7CiAgICAgICAgICB9CiAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgIGlmIChfZGlkSXRlcmF0b3JFcnJvcjExKSB7CiAgICAgICAgICAgIHRocm93IF9pdGVyYXRvckVycm9yMTE7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgaGFuZGxlU2VsZWN0Um93OiBmdW5jdGlvbiBoYW5kbGVTZWxlY3RSb3codmFsKSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwoKICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvblJvdyA9IFtdOwogICAgICB2YWwuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIF90aGlzOS5tdWx0aXBsZVNlbGVjdGlvblJvdy5wdXNoKGl0ZW0pOwogICAgICB9KTsKICAgIH0sCiAgICBzdGFydEZsb3dTdWJtaXQ6IGZ1bmN0aW9uIHN0YXJ0Rmxvd1N1Ym1pdChyb3cpIHsKICAgICAgdmFyIGJ1c2lBbGlhcyA9ICJBRERfQU1NIjsKICAgICAgdmFyIGJ1c2lUaXRsZSA9ICLmlrDlop7nlLXooagoIiArIHJvdy5wcm9qZWN0bmFtZSArICIp5a6h5om5IjsKCiAgICAgIGlmIChyb3cuYmlsbFN0YXR1cyA9PT0gMykgewogICAgICAgIGJ1c2lBbGlhcyA9ICJNT0RJRllfQU1NIjsKICAgICAgICBidXNpVGl0bGUgPSAi5L+u5pS555S16KGoKCIgKyByb3cucHJvamVjdG5hbWUgKyAiKeWuoeaJuSI7CiAgICAgIH0KCiAgICAgIGlmIChyb3cuaXNjaGFuZ2VhbW1ldGVyID09IDEgJiYgcm93LmJpbGxTdGF0dXMgPCAyKSB7CiAgICAgICAgYnVzaUFsaWFzID0gIkFNTV9TV0lUQ0hfQU1NIjsKICAgICAgICBidXNpVGl0bGUgPSAi55S16KGo5o2i6KGoKCIgKyByb3cucHJvamVjdG5hbWUgKyAiKeWuoeaJuSI7CiAgICAgIH0KCiAgICAgIHRoaXMud29ya0Zsb3dQYXJhbXMgPSB7CiAgICAgICAgYnVzaUlkOiByb3cuaWQsCiAgICAgICAgYnVzaUFsaWFzOiBidXNpQWxpYXMsCiAgICAgICAgYnVzaVRpdGxlOiBidXNpVGl0bGUKICAgICAgfTsKICAgICAgdmFyIHRoYXQgPSB0aGlzOwogICAgICB0aGlzLiRNb2RhbC5jb25maXJtKHsKICAgICAgICB0aXRsZTogJ+eUteihqOaPkOS6pOa1geeoiycsCiAgICAgICAgY29udGVudDogJzxwPuaYr+WQpuaPkOS6pOeUteihqCAoJyArIHJvdy5wcm9qZWN0bmFtZSArICcpIOWIsOa1geeoizwvcD4nLAogICAgICAgIG9uT2s6IGZ1bmN0aW9uIG9uT2soKSB7CiAgICAgICAgICB0aGF0LmxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIHRoYXQuJHJlZnMuY2x3ZmJ0bi5vbkNsaWNrKCk7CiAgICAgICAgICB9LCAzMDApOwogICAgICAgIH0sCiAgICAgICAgb25DYW5jZWw6IGZ1bmN0aW9uIG9uQ2FuY2VsKCkgewogICAgICAgICAgdGhhdC5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBzdGFydEZsb3c6IGZ1bmN0aW9uIHN0YXJ0Rmxvdyhyb3cpIHsKICAgICAgdmFyIHRoYXQgPSB0aGlzOwogICAgICBpc0luVG9kb0xpc3Qocm93LmlkLCAxKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAvL+WtmOWcqOS6juS7o+WKnuS4reaXtu+8jOaKpeWHuuaPkOekugogICAgICAgIHZhciBvd25lcm5hbWUgPSAiIjsKCiAgICAgICAgaWYgKHJlcy5kYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgcmVzLmRhdGEubGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgb3duZXJuYW1lICs9IHJlcy5kYXRhW2ldLm93bmVybmFtZSArICcgJzsKICAgICAgICAgIH0KCiAgICAgICAgICB0aGF0LiRNb2RhbC53YXJuaW5nKHsKICAgICAgICAgICAgdGl0bGU6ICLmuKnppqjmj5DnpLoiLAogICAgICAgICAgICBjb250ZW50OiAi6K+l5pWw5o2u5a2Y5Zyo5LqOIiArIG93bmVybmFtZSArICLnmoTmtYHnqIvku6Plip7kuK3vvIzlpITnkIblkI7miY3lj6/nu6fnu63mj5DkuqTmtYHnqIsiCiAgICAgICAgICB9KTsKICAgICAgICAgIHRoYXQubG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0gZWxzZSBpZiAocm93LmJpbGxTdGF0dXMgPT0gMyB8fCByb3cuYmlsbFN0YXR1cyA9PSA0KSB7CiAgICAgICAgICBjaGVja1N0YXJ0Rmxvdyh7CiAgICAgICAgICAgIGlkOiByb3cuaWQKICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlczEpIHsKICAgICAgICAgICAgLyrmj5DkuqTmtYHnqIvpqozor4HnlKjmiLfmmK/lkKbmnInmlbDmja7pnIDopoHmj5DkuqQqLwogICAgICAgICAgICB0aGF0LmxvYWRpbmcgPSBmYWxzZTsKCiAgICAgICAgICAgIGlmIChyZXMxLmRhdGEuaWQgPT0gbnVsbCB8fCByZXMxLmRhdGEuaWQgPT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgICAgdGhhdC4kTW9kYWwud2FybmluZyh7CiAgICAgICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsCiAgICAgICAgICAgICAgICBjb250ZW50OiAi5oKo5rKh5pyJ5Y+v5o+Q5Lqk55qE5pWw5o2uIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoYXQuc3RhcnRGbG93U3VibWl0KHJvdyk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGF0LmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIHRoYXQuc3RhcnRGbG93U3VibWl0KHJvdyk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBzaG93RmxvdzogZnVuY3Rpb24gc2hvd0Zsb3cocm93LCBwcm9jSW5zdElkKSB7CiAgICAgIHRoaXMuc2hvd1dvcmtGbG93ID0gdHJ1ZTsKICAgICAgdGhpcy5oaXNQYXJhbXMgPSB7CiAgICAgICAgYnVzaUlkOiByb3cuaWQsCiAgICAgICAgYnVzaVR5cGU6IHJvdy5idXNpQWxpYXMsCiAgICAgICAgcHJvY0luc3RJZDogcHJvY0luc3RJZAogICAgICB9OwogICAgfSwKICAgIGRvV29ya0Zsb3c6IGZ1bmN0aW9uIGRvV29ya0Zsb3coZGF0YSkgewogICAgICAvL+a1geeoi+WbnuiwgwogICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgdGhpcy4kcmVmcy5hbW1ldGVyVGFibGUucXVlcnkoKTsgLy8gdGhpcy5xdWVyeSh0aGlzLnF1ZXJ5UGFyYW1zKTsKICAgIH0sCiAgICBxdWVyeTogZnVuY3Rpb24gcXVlcnkocGFyYW1zKSB7CiAgICAgIHZhciBfdGhpczEwID0gdGhpczsKCiAgICAgIHRoaXMuYW1tZXRlci5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdEFtbWV0ZXIocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczEwLmFtbWV0ZXIubG9hZGluZyA9IGZhbHNlOwogICAgICAgIF90aGlzMTAuYW1tZXRlci50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOwogICAgICAgIF90aGlzMTAuYW1tZXRlci5kYXRhID0gT2JqZWN0LmFzc2lnbihbXSwgcmVzLmRhdGEucm93cyk7CiAgICAgIH0pOwogICAgfSwKICAgIGJlZm9yZUxvYWREYXRhOiBmdW5jdGlvbiBiZWZvcmVMb2FkRGF0YShkYXRhKSB7CiAgICAgIHZhciBjb2xzID0gW10sCiAgICAgICAgICBrZXlzID0gW107CgogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRoaXMuZXhwb3J0Q29sdW1ucy5sZW5ndGg7IGkrKykgewogICAgICAgIGNvbHMucHVzaCh0aGlzLmV4cG9ydENvbHVtbnNbaV0udGl0bGUpOwogICAgICAgIGtleXMucHVzaCh0aGlzLmV4cG9ydENvbHVtbnNbaV0ua2V5KTsKICAgICAgfQoKICAgICAgdmFyIHBhcmFtcyA9IHsKICAgICAgICB0aXRsZTogY29scywKICAgICAgICBrZXk6IGtleXMsCiAgICAgICAgZGF0YTogZGF0YSwKICAgICAgICBhdXRvV2lkdGg6IHRydWUsCiAgICAgICAgZmlsZW5hbWU6ICfnlLXooajmlbDmja7lr7zlh7onCiAgICAgIH07CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZVNpemUgPSB0aGlzLmFtbWV0ZXIucGFnZVNpemU7CiAgICAgIGV4Y2VsLmV4cG9ydF9hcnJheV90b19leGNlbChwYXJhbXMpOwogICAgICB0aGlzLiRTcGluLmhpZGUoKTsKICAgICAgcmV0dXJuOwogICAgfSwKICAgIGV4cG9ydExvYWRpbmc6IGZ1bmN0aW9uIGV4cG9ydExvYWRpbmcoKSB7CiAgICAgIHRoaXMuJFNwaW4uc2hvdyh7CiAgICAgICAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCkgewogICAgICAgICAgcmV0dXJuIGgoJ2RpdicsIFtoKCdQcm9ncmVzcycsIHsKICAgICAgICAgICAgc3R5bGU6IHsKICAgICAgICAgICAgICB3aWR0aDogJzgwMHB4JwogICAgICAgICAgICB9CiAgICAgICAgICB9KSwgaCgnZGl2JywgJ+WvvOWHuuS4re+8jOivt+WLv+WIt+aWsOmhtemdoi4uLi4uLicpXSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBleHBvcnRDc3Y6IGZ1bmN0aW9uIGV4cG9ydENzdihuYW1lKSB7CiAgICAgIHZhciBfdGhpczExID0gdGhpczsKCiAgICAgIHRoaXMuZXhwb3J0TG9hZGluZygpOwogICAgICB0aGlzLmV4cG9ydC5ydW4gPSB0cnVlOwogICAgICB2YXIgcGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsKCiAgICAgIGlmIChuYW1lID09PSAnY3VycmVudCcpIHsKICAgICAgICB0aGlzLmJlZm9yZUxvYWREYXRhKHRoaXMuc2V0VmFsdWVCeUZvckVhY2godGhpcy5hbW1ldGVyLmRhdGEpKTsKICAgICAgICByZXR1cm47CiAgICAgIH0gZWxzZSBpZiAobmFtZSA9PT0gJ2FsbCcpIHsKICAgICAgICBwYXJhbXMucGFnZU51bSA9IDE7CiAgICAgICAgcGFyYW1zLnBhZ2VTaXplID0gdGhpcy5leHBvcnQuc2l6ZTsKICAgICAgfSAvLyBsZXQgcmVxID0gewogICAgICAvLyAgICAgdXJsIDogIi9idXNpbmVzcy9hbW1ldGVyb3Jwcm90b2NvbC9saXN0IiwKICAgICAgLy8gICAgIG1ldGhvZCA6ICJnZXQiLAogICAgICAvLyAgICAgcGFyYW1zIDogcGFyYW1zCiAgICAgIC8vIH07CiAgICAgIC8vIHRoaXMuYW1tZXRlci5sb2FkaW5nID0gdHJ1ZTsKICAgICAgLy8gYXhpb3MucmVxdWVzdChyZXEpLnRoZW4ocmVzID0+IHsKICAgICAgLy8gICAgIHRoaXMuYW1tZXRlci5sb2FkaW5nID0gZmFsc2U7CiAgICAgIC8vICAgICBpZiAocmVzLmRhdGEpIHsKICAgICAgLy8gICAgICAgICBsZXQgYXJyYXkgPSByZXMuZGF0YS5yb3dzOwogICAgICAvLyAgICAgICAgIHRoaXMuYmVmb3JlTG9hZERhdGEodGhpcy5zZXRWYWx1ZUJ5Rm9yRWFjaChhcnJheSkpOwogICAgICAvLyAgICAgfQogICAgICAvLyB9KS5jYXRjaChlcnIgPT4gewogICAgICAvLyAgICAgY29uc29sZS5sb2coZXJyKTsKICAgICAgLy8gfSk7CgoKICAgICAgdmFyIHJlcSA9IHsKICAgICAgICB1cmw6ICIvYnVzaW5lc3MvYW1tZXRlcm9ycHJvdG9jb2wvZXhwb3J0TWV0ZXJBbGwiLAogICAgICAgIG1ldGhvZDogInBvc3QiLAogICAgICAgIHBhcmFtczogcGFyYW1zCiAgICAgIH07CiAgICAgIGF4aW9zLmZpbGUocmVxKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICB2YXIgYmxvYiA9IG5ldyBCbG9iKFtyZXNdKTsKICAgICAgICB2YXIgZmlsZU5hbWUgPSAn55S16KGo5pWw5o2u5a+85Ye6Lnhscyc7IC8vIOWIm+W7uuS4gOS4quS4i+i9vemTvuaOpQoKICAgICAgICBjb25zb2xlLmxvZygn5LiL6L295paH5Lu2OicsIGZpbGVOYW1lKTsKICAgICAgICB2YXIgdXJsID0gVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTsKICAgICAgICB2YXIgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTsKICAgICAgICBhLmhyZWYgPSB1cmw7CiAgICAgICAgYS5kb3dubG9hZCA9IGZpbGVOYW1lOyAvLyDorr7nva7kuIvovb3mlofku7blkI0KCiAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChhKTsKICAgICAgICBhLmNsaWNrKCk7IC8vIOa4heeQhgoKICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoYSk7CiAgICAgICAgICBVUkwucmV2b2tlT2JqZWN0VVJMKHVybCk7IC8vIOmHiuaUvuWGheWtmAogICAgICAgIH0sIDEwMCk7CgogICAgICAgIF90aGlzMTEuJFNwaW4uaGlkZSgpOwogICAgICB9KTsKICAgIH0sCiAgICBzZXRWYWx1ZUJ5Rm9yRWFjaDogZnVuY3Rpb24gc2V0VmFsdWVCeUZvckVhY2goYXJyYXkpIHsKICAgICAgYXJyYXkuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIGl0ZW0uY2F0ZWdvcnlTdHIgPSBidGV4dCgiYW1tZXRlckNhdGVnb3J5IiwgaXRlbS5jYXRlZ29yeSwgJ3R5cGVDb2RlJywgJ3R5cGVOYW1lJyk7CiAgICAgICAgaXRlbS5wYWNrYWdldHlwZVN0ciA9IGJ0ZXh0KCJwYWNrYWdlVHlwZSIsIGl0ZW0ucGFja2FnZXR5cGUsICd0eXBlQ29kZScsICd0eXBlTmFtZScpOwogICAgICAgIGl0ZW0ucGF5cGVyaW9kU3RyID0gYnRleHQoInBheVBlcmlvZCIsIGl0ZW0ucGF5cGVyaW9kLCAndHlwZUNvZGUnLCAndHlwZU5hbWUnKTsKICAgICAgICBpdGVtLnBheXR5cGVTdHIgPSBidGV4dCgicGF5VHlwZSIsIGl0ZW0ucGF5dHlwZSwgJ3R5cGVDb2RlJywgJ3R5cGVOYW1lJyk7CiAgICAgICAgaXRlbS5lbGVjdHJvbmF0dXJlU3RyID0gYnRleHQoImVsZWN0cm9OYXR1cmUiLCBpdGVtLmVsZWN0cm9uYXR1cmUsICd0eXBlQ29kZScsICd0eXBlTmFtZScpOwogICAgICAgIGl0ZW0uZWxlY3Ryb3ZhbGVuY2VuYXR1cmVTdHIgPSBidGV4dCgiZWxlY3Ryb3ZhbGVuY2VOYXR1cmUiLCBpdGVtLmVsZWN0cm92YWxlbmNlbmF0dXJlLCAndHlwZUNvZGUnLCAndHlwZU5hbWUnKTsKICAgICAgICBpdGVtLmVsZWN0cm90eXBlU3RyID0gYnRleHQoImVsZWN0cm9UeXBlIiwgaXRlbS5lbGVjdHJvdHlwZSwgJ3R5cGVDb2RlJywgJ3R5cGVOYW1lJyk7CiAgICAgICAgaXRlbS5zdGF0dXNTdHIgPSBidGV4dCgic3RhdHVzIiwgaXRlbS5zdGF0dXMsICd0eXBlQ29kZScsICd0eXBlTmFtZScpOwogICAgICAgIGl0ZW0ucHJvcGVydHlTdHIgPSBidGV4dCgicHJvcGVydHkiLCBpdGVtLnByb3BlcnR5LCAndHlwZUNvZGUnLCAndHlwZU5hbWUnKTsKICAgICAgICBpdGVtLmFtbWV0ZXJ0eXBlU3RyID0gYnRleHQoImFtbWV0ZXJUeXBlIiwgaXRlbS5hbW1ldGVydHlwZSwgJ3R5cGVDb2RlJywgJ3R5cGVOYW1lJyk7CiAgICAgICAgaXRlbS5zdGF0aW9uc3RhdHVzU3RyID0gYnRleHQoInN0YXRpb25TdGF0dXMiLCBpdGVtLnN0YXRpb25zdGF0dXMsICd0eXBlQ29kZScsICd0eXBlTmFtZScpOwogICAgICAgIGl0ZW0uc3RhdGlvbnR5cGVTdHIgPSBidGV4dCgiQlVSX1NUQU5EX1RZUEUiLCBpdGVtLnN0YXRpb250eXBlLCAndHlwZUNvZGUnLCAndHlwZU5hbWUnKTsKICAgICAgICBpdGVtLmFtbWV0ZXJ1c2VTdHIgPSBidGV4dCgiYW1tZXRlclVzZSIsIGl0ZW0uYW1tZXRlcnVzZSwgJ3R5cGVDb2RlJywgJ3R5cGVOYW1lJyk7CiAgICAgICAgaXRlbS5kaXJlY3RzdXBwbHlmbGFnU3RyID0gYnRleHQoImRpcmVjdFN1cHBseUZsYWciLCBpdGVtLmRpcmVjdHN1cHBseWZsYWcsICd0eXBlQ29kZScsICd0eXBlTmFtZScpOwogICAgICAgIGl0ZW0uYmlsbFN0YXR1c1N0ciA9IGJ0ZXh0KCJiYXNpY0JpbGxTdGF0dXMiLCBpdGVtLmJpbGxTdGF0dXMsICd0eXBlQ29kZScsICd0eXBlTmFtZScpOwogICAgICAgIGl0ZW0uc3VwcGx5YnVyZWF1YW1tZXRlcmNvZGU7CiAgICAgICAgaXRlbS5tYWduaWZpY2F0aW9uOwogICAgICB9KTsKICAgICAgcmV0dXJuIGFycmF5OwogICAgfSwKICAgIC8v6YCJ5oup5omA5bGe6YOo6Zeo5byA5aeLCiAgICBjaG9vc2VSZXNwb25zZUNlbnRlcjogZnVuY3Rpb24gY2hvb3NlUmVzcG9uc2VDZW50ZXIoKSB7CiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLmNvbXBhbnkgPT0gbnVsbCB8fCB0aGlzLnF1ZXJ5UGFyYW1zLmNvbXBhbnkgPT0gIi0xIikgewogICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbygi6K+35YWI6YCJ5oup5YiG5YWs5Y+4Iik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB0aGlzLiRyZWZzLmNvdW50cnlNb2RhbC5jaG9vc2UodGhpcy5xdWVyeVBhcmFtcy5jb21wYW55KTsgLy/miYDlsZ7pg6jpl6gKICAgIH0sCiAgICBnZXREYXRhRnJvbU1vZGFsOiBmdW5jdGlvbiBnZXREYXRhRnJvbU1vZGFsKGRhdGEpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb3VudHJ5ID0gZGF0YS5pZDsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb3VudHJ5TmFtZSA9IGRhdGEubmFtZTsgLy/pgInmi6nmiYDlsZ7pg6jpl6jnu5PmnZ8KICAgIH0sCiAgICBnZXRVc2VyRGF0YTogZnVuY3Rpb24gZ2V0VXNlckRhdGEoKSB7CiAgICAgIHZhciBfdGhpczEyID0gdGhpczsKCiAgICAgIHZhciB0aGF0ID0gdGhpczsKICAgICAgZ2V0VXNlcmRhdGEoKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAvL+W9k+WJjeeZu+W9leeUqOaIt+aJgOWcqOWFrOWPuOWSjOaJgOWxnumDqOmXqAogICAgICAgIHZhciBjb21wYW5pZXMgPSB0aGF0LmNvbXBhbmllczsKCiAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllcyAhPSBudWxsICYmIHJlcy5kYXRhLmNvbXBhbmllcy5sZW5ndGggIT0gMCkgewogICAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllc1swXS5pZCAhPSAiMjYwMDAwMDAwMCIpIHsKICAgICAgICAgICAgY29tcGFuaWVzID0gcmVzLmRhdGEuY29tcGFuaWVzOwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgdGhhdC5jb21wYW55ID0gY29tcGFuaWVzWzBdLmlkOwogICAgICAgIHRoYXQucXVlcnlQYXJhbXMuY29tcGFueSA9IGNvbXBhbmllc1swXS5pZDsKICAgICAgICB2YXIgZGVwYXJ0bWVudHMgPSB0aGF0LmRlcGFydG1lbnRzOwoKICAgICAgICBpZiAocmVzLmRhdGEuZGVwYXJ0bWVudHMgIT0gbnVsbCAmJiByZXMuZGF0YS5kZXBhcnRtZW50cy5sZW5ndGggIT0gMCkgewogICAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllc1swXS5pZCAhPSAiMjYwMDAwMDAwMCIpIHsKICAgICAgICAgICAgZGVwYXJ0bWVudHMgPSByZXMuZGF0YS5kZXBhcnRtZW50czsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIHRoYXQuY291bnRyeSA9IGRlcGFydG1lbnRzWzBdLmlkOwogICAgICAgIHRoYXQuY291bnRyeU5hbWUgPSBkZXBhcnRtZW50c1swXS5uYW1lOwogICAgICAgIHRoYXQucXVlcnlQYXJhbXMuY291bnRyeSA9IE51bWJlcihkZXBhcnRtZW50c1swXS5pZCk7CiAgICAgICAgdGhhdC5xdWVyeVBhcmFtcy5jb3VudHJ5TmFtZSA9IGRlcGFydG1lbnRzWzBdLm5hbWU7CgogICAgICAgIF90aGlzMTIuX29uU2VhcmNoSGFuZGxlKCk7IC8vIHRoaXMucXVlcnkoe3BhZ2VOdW06IDEsdHlwZTowLHBhZ2VTaXplOiB0aGlzLmFtbWV0ZXIucGFnZVNpemUsY29tcGFueTp0aGlzLmNvbXBhbnksY291bnRyeTp0aGlzLmNvdW50cnl9KTsKCiAgICAgIH0pOwogICAgfSwKICAgIGluaXQ6IGZ1bmN0aW9uIGluaXQoKSB7CiAgICAgIHZhciBfdGhpczEzID0gdGhpczsKCiAgICAgIHRoaXMuc3RhdHVzID0gYmxpc3QoInN0YXR1cyIpOyAvL+eKtuaAgQoKICAgICAgdGhpcy5iaWxsU3RhdHVzID0gYmxpc3QoImJhc2ljQmlsbFN0YXR1cyIpOyAvL+WNleaNrueKtuaAgQoKICAgICAgdGhpcy5hbW1ldGVyVHlwZSA9IGJsaXN0KCJhbW1ldGVyVHlwZSIpOyAvL+eUteihqOexu+WeiwoKICAgICAgdGhpcy5lbGVjdHJvVHlwZSA9IGJsaXN0KCJlbGVjdHJvVHlwZSIpOyAvL+eUqOeUteexu+WeiwoKICAgICAgdGhpcy5lbGVjdHJvTmF1cmUgPSBibGlzdCgiZWxlY3Ryb05hdHVyZSIpOyAvL+eUqOeUteaAp+i0qAoKICAgICAgdGhpcy5wYXlUeXBlID0gYmxpc3QoInBheVR5cGUiKTsgLy/ku5jotLnmlrnlvI8KCiAgICAgIHRoaXMuZWxlY3Ryb3ZhbGVuY2VOYXR1cmUgPSBibGlzdCgiZWxlY3Ryb3ZhbGVuY2VOYXR1cmUiKTsgLy/nlLXku7fmgKfotKgKCiAgICAgIHRoaXMucHJvcGVydHkgPSBibGlzdCgicHJvcGVydHkiKTsgLy/kuqfmnYPlvZLlsZ4KCiAgICAgIHRoaXMuZGlyZWN0c3VwcGx5RmxhZyA9IGJsaXN0KCJkaXJlY3RTdXBwbHlGbGFnIik7IC8v5a+55aSW57uT566X57G75Z6LCgogICAgICB0aGlzLmlzZW50aXR5YW1tZXRlcnMucHVzaCh7CiAgICAgICAgdHlwZUNvZGU6IDAsCiAgICAgICAgdHlwZU5hbWU6ICflkKYnCiAgICAgIH0pOwogICAgICB0aGlzLmlzZW50aXR5YW1tZXRlcnMucHVzaCh7CiAgICAgICAgdHlwZUNvZGU6IDEsCiAgICAgICAgdHlwZU5hbWU6ICfmmK8nCiAgICAgIH0pOwogICAgICB2YXIgdGhhdCA9IHRoaXM7CiAgICAgIGdldFVzZXJCeVVzZXJSb2xlKCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgLy/moLnmja7mnYPpmZDojrflj5bliIblhazlj7gKICAgICAgICB0aGF0LmNvbXBhbmllcyA9IHJlcy5kYXRhLmNvbXBhbmllczsKCiAgICAgICAgaWYgKHJlcy5kYXRhLmlzQ2l0eUFkbWluID09IHRydWUgfHwgcmVzLmRhdGEuaXNQcm9BZG1pbiA9PSB0cnVlIHx8IHJlcy5kYXRhLmlzU3ViQWRtaW4gPT0gdHJ1ZSkgewogICAgICAgICAgdGhhdC5pc0FkbWluID0gdHJ1ZTsKICAgICAgICB9CgogICAgICAgIGdldENvdW50cnlzZGF0YSh7CiAgICAgICAgICBvcmdDb2RlOiByZXMuZGF0YS5jb21wYW5pZXNbMF0uaWQKICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgIC8v5qC55o2u5p2D6ZmQ6I635Y+W5omA5bGe6YOo6ZeoCiAgICAgICAgICB0aGF0LmRlcGFydG1lbnRzID0gcmVzLmRhdGE7CiAgICAgICAgICB0aGF0LmdldFVzZXJEYXRhKCk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICBnZXRDbGFzc2lmaWNhdGlvbigpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIC8v55So55S157G75Z6LCiAgICAgICAgX3RoaXMxMy5jbGFzc2lmaWNhdGlvbkRhdGEgPSByZXMuZGF0YTsKICAgICAgfSk7IC8vIHRoaXMuX29uU2VhcmNoSGFuZGxlKCk7IAogICAgfQogIH0pLAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB0aGlzLmluaXQoKTsKICAgIHRoaXMuY29uZmlnVmVyc2lvbiA9IHRoaXMuJGNvbmZpZy52ZXJzaW9uOwoKICAgIGlmICh0aGlzLmNvbmZpZ1ZlcnNpb24gPT0gJ2xuJyB8fCB0aGlzLmNvbmZpZ1ZlcnNpb24gPT0gJ0xOJykgewogICAgICB0aGlzLmV4cG9ydENvbHVtbnMudW5zaGlmdCh7CiAgICAgICAgdGl0bGU6ICfkvpvnlLXlsYDnlLXooajnvJblj7cnLAogICAgICAgIGtleTogJ3N1cHBseWJ1cmVhdWFtbWV0ZXJjb2RlJwogICAgICB9KTsKICAgICAgdGhpcy5hbW1ldGVyLmNvbHVtbnMudW5zaGlmdCh7CiAgICAgICAgdGl0bGU6ICfkvpvnlLXlsYDnlLXooajnvJblj7cnLAogICAgICAgIGtleTogJ3N1cHBseWJ1cmVhdWFtbWV0ZXJjb2RlJywKICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgbWluV2lkdGg6IDEwMCwKICAgICAgICBtYXhXaWR0aDogMjAwCiAgICAgIH0pOwogICAgfSAvLyB0aGlzLmZpbGVQYXJhbS5idXNpSWQgPSAiNjY2NjY2IjsKCiAgfQp9Ow=="}, {"version": 3, "sources": ["listAmmeter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwOA,SAAA,SAAA,EAAA,WAAA,QAAA,eAAA;AACA,SAAA,mBAAA,EAAA,WAAA,EAAA,aAAA,IAAA,cAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,kBAAA,EAAA,WAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,cAAA,QAAA,2BAAA;AACA,SAAA,YAAA,QAAA,iCAAA;AACA,SAAA,KAAA,EAAA,KAAA,QAAA,cAAA;AACA,OAAA,eAAA,MAAA,mBAAA;AACA,OAAA,aAAA,MAAA,qCAAA;AACA,OAAA,eAAA,MAAA,yCAAA;AACA,OAAA,YAAA,MAAA,gBAAA;AACA,OAAA,WAAA,MAAA,2CAAA;AACA,OAAA,oBAAA,MAAA,mDAAA;AAEA,OAAA,UAAA,MAAA,0CAAA;AACA,SAAA,SAAA,EAAA,YAAA,IAAA,aAAA,QAAA,2BAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AAEA,OAAA,KAAA,MAAA,cAAA;AAEA,SAAA,YAAA,QAAA,MAAA;AACA,OAAA,OAAA,MAAA,kBAAA;AACA,SAAA,YAAA,QAAA,aAAA;AACA,eAAA;AACA,EAAA,IAAA,EAAA,SADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,WAAA,EAAA,WADA;AAEA,IAAA,oBAAA,EAAA,oBAFA;AAGA;AACA;AACA,IAAA,eAAA,EAAA,eALA;AAMA,IAAA,eAAA,EAAA,eANA;AAOA,IAAA,aAAA,EAAA,aAPA;AAQA,IAAA,YAAA,EAAA,YARA;AASA,IAAA,UAAA,EAAA;AATA,GAFA;AAcA,EAAA,IAdA,kBAcA;AAAA;;AACA;AACA,QAAA,YAAA,GAAA,SAAA,YAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,KAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,6BAAA,KAAA,CAAA,MAAA,8HAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,MAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,KATA,CAFA,CAYA;;;AACA,QAAA,iBAAA,GAAA,SAAA,iBAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,KAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,8BAAA,KAAA,CAAA,WAAA,mIAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,WAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,KATA,CAbA,CAuBA;;;AACA,QAAA,iBAAA,GAAA,SAAA,iBAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,KAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,8BAAA,KAAA,CAAA,WAAA,mIAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,WAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,KATA,CAxBA,CAkCA;;;AACA,QAAA,mBAAA,GAAA,SAAA,mBAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,KAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,8BAAA,KAAA,CAAA,YAAA,mIAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,aAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,KATA,CAnCA,CA6CA;;;AACA,QAAA,aAAA,GAAA,SAAA,aAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,KAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,8BAAA,KAAA,CAAA,OAAA,mIAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,OAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,KATA,CA9CA,CAwDA;;;AACA,QAAA,0BAAA,GAAA,SAAA,0BAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,KAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,8BAAA,KAAA,CAAA,oBAAA,mIAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,oBAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,KATA,CAzDA,CAmEA;;;AACA,QAAA,gBAAA,GAAA,SAAA,gBAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,KAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,8BAAA,KAAA,CAAA,UAAA,mIAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,UAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,KATA,CApEA,CA8EA;;;AACA,QAAA,cAAA,GAAA,SAAA,cAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,KAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,8BAAA,KAAA,CAAA,QAAA,mIAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,QAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,KATA,CA/EA,CAyFA;;;AACA,QAAA,sBAAA,GAAA,SAAA,sBAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,KAAA,GAAA,EAAA;AADA;AAAA;AAAA;;AAAA;AAEA,8BAAA,KAAA,CAAA,gBAAA,mIAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,GAAA,CAAA,gBAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,aAAA,CAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,KATA,CA1FA,CAoGA;;;AACA,QAAA,iBAAA,GAAA,SAAA,iBAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,MAAA,GAAA,MAAA,CAAA,MAAA,CAAA,GAAA;AACA,aAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,GAAA,EAAA;AACA,QAAA,KAAA,EAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAEA,QAAA,EAAA,EAAA;AACA,UAAA,KAAA,EAAA,iBAAA;AACA,YAAA,KAAA,CAAA,WAAA,CAAA,MAAA,CAAA,GAAA,CAAA,EAAA;AACA;AAHA;AAFA,OAAA,EAOA,MAAA,CAAA,GAAA,CAAA,MAAA,CAPA,CAAA,CAAA,CAAA;AAQA,KAVA,CArGA,CAgHA;;;AACA,QAAA,iBAAA,GAAA,SAAA,iBAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,MAAA,GAAA,MAAA,CAAA,MAAA,CAAA,GAAA;AACA,aAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,GAAA,EAAA;AACA,QAAA,EAAA,EAAA;AACA,UAAA,KAAA,EAAA,iBAAA;AACA,YAAA,KAAA,CAAA,WAAA,CAAA,MAAA,CAAA,GAAA,CAAA,WAAA;AACA;AAHA;AADA,OAAA,EAMA,MAAA,CAAA,GAAA,CAAA,MAAA,CANA,CAAA,CAAA,CAAA;AAOA,KATA,CAjHA,CA2HA;;;AACA,QAAA,cAAA,GAAA,SAAA,cAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,CAAA,MAAA,CAAA,GAAA,CAAA,cAAA,EAAA;AACA;AACA,eAAA,CAAA,CAAA,KAAA,EAAA;AACA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AADA,WADA;AAIA,UAAA,EAAA,EAAA;AACA,YAAA,KAAA,EAAA,iBAAA;AACA,cAAA,KAAA,CAAA,QAAA,CAAA,IAAA,CAAA,mBAAA;AACA;AAHA;AAJA,SAAA,EASA,KATA,CAAA;AAUA,OAZA,MAYA;AACA,eAAA,CAAA,CAAA,KAAA,EAAA,MAAA,CAAA,GAAA,CAAA,cAAA,CAAA;AACA;AAEA,KAjBA;;AAkBA,QAAA,OAAA,GAAA,SAAA,OAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA;AACA,UAAA,IAAA;AAAA,UAAA,IAAA,GAAA,EAAA;AACA,UAAA,GAAA,GAAA,MAAA,CAAA,GAAA;;AACA,UAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,GAAA,CAAA,aAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,GAAA,IAAA;AACA,QAAA,IAAA,GAAA,SAAA;AACA,OAHA,MAGA,IAAA,MAAA,CAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,MAAA,CAAA,GAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,QAAA,IAAA,GAAA,IAAA;AACA,QAAA,IAAA,GAAA,SAAA;AACA;;AACA,UAAA,IAAA,IAAA,EAAA,EAAA;AACA,eAAA,CAAA,CAAA,KAAA,EAAA,EAAA,EAAA,IAAA,CAAA;AACA;;AACA,aAAA,CAAA,CAAA,QAAA,EACA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,IAAA,EAAA,IADA;AACA,UAAA,IAAA,EAAA;AADA,SADA;AAIA,QAAA,EAAA,EAAA;AACA,UAAA,KADA,mBACA;AACA,gBAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,GAAA,CAAA,aAAA,IAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,EAAA,MAAA,CAAA,GAAA,CAAA,aAAA;AACA,aAFA,MAEA,IAAA,MAAA,CAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,MAAA,CAAA,GAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA,cAAA,IAAA,CAAA,SAAA,CAAA,MAAA,CAAA,GAAA;AACA;AACA;AARA;AAJA,OADA,EAgBA,IAhBA,CAAA;AAkBA,KAhCA;;AAiCA,QAAA,WAAA,GAAA,SAAA,WAAA,CAAA,CAAA,EAAA,MAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA;AACA,aAAA,CAAA,CAAA,QAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,IAAA,EAAA,SADA;AACA,UAAA,IAAA,EAAA;AADA,SADA;AAGA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,MAAA,CAAA,GAAA,CAAA,OAAA,GAAA,KAAA,GAAA;AADA,SAHA;AAKA,QAAA,KAAA,EAAA;AACA,UAAA,OAAA,EAAA,MAAA,CAAA,GAAA,CAAA,OAAA,GAAA,CAAA,GAAA;AADA,SALA;AAOA,QAAA,EAAA,EAAA;AACA,UAAA,KADA,mBACA;AACA,YAAA,IAAA,CAAA,SAAA,CAAA,MAAA,CAAA,GAAA,CAAA,OAAA;AACA;AAHA;AAPA,OAAA,EAYA,IAZA,CAAA;AAaA,KAfA;;AAgBA,QAAA,mBAAA,GAAA,SAAA,mBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA;AACA,UAAA,KAAA,IAAA,IAAA,IAAA,KAAA,IAAA,EAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KAPA;;AAQA,WAAA;AACA,MAAA,UAAA,EAAA,IADA;AAEA,MAAA,SAAA,EAAA,EAFA;AAGA,MAAA,UAAA,EAAA,EAHA;AAIA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,UAFA;AAGA,QAAA,YAAA,EAAA,MAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OAJA;AAUA,MAAA,YAAA,EAAA;AACA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,SAAA,EAAA,mBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,aAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,SAAA,EAAA,mBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAJA,OAVA;AAkBA,MAAA,YAAA,EAAA;AACA,QAAA,aAAA,EAAA,EADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,EAAA,EAAA;AAHA,OAlBA;AAuBA,MAAA,MAAA,EAAA;AACA,QAAA,QAAA,EAAA;AACA,UAAA,IAAA,EAAA;AADA,SADA;AAIA,QAAA,UAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,YAAA,EAAA,MAHA;AAIA,UAAA,KAAA,EAAA,GAJA;AAKA,UAAA,MAAA,EAAA,KAAA;AALA,SADA,CAJA;AAaA,QAAA,OAAA,EAAA,KAbA;AAcA,QAAA,OAAA,EAAA,EAdA;AAeA,QAAA,IAAA,EAAA;AAfA,OAvBA;AAwCA,MAAA,SAAA,EAAA,KAxCA;AAyCA,MAAA,OAAA,EAAA,KAzCA;AA0CA,MAAA,YAAA,EAAA,KA1CA;AA2CA,MAAA,aAAA,EAAA,IA3CA;AA2CA;AACA,MAAA,SAAA,EAAA,KA5CA;AA6CA,MAAA,UAAA,EAAA,IA7CA;AA6CA;AACA,MAAA,OAAA,EAAA,KA9CA;AA+CA,MAAA,OAAA,EAAA,IA/CA;AA+CA;AACA,MAAA,OAAA,EAAA,IAhDA;AAgDA;AACA,MAAA,WAAA,EAAA,IAjDA;AAiDA;AAEA,MAAA,kBAAA,EAAA,EAnDA;AAmDA;AACA,MAAA,eAAA,EAAA,EApDA;AAoDA;AAEA,MAAA,QAAA,EAAA,EAtDA;AAuDA,MAAA,eAAA,EAAA,EAvDA;AAwDA,MAAA,gBAAA,EAAA,EAxDA;AAyDA,MAAA,MAAA,EAAA,EAzDA;AAyDA;AACA,MAAA,UAAA,EAAA,EA1DA;AA0DA;AACA,MAAA,WAAA,EAAA,EA3DA;AA2DA;AACA,MAAA,WAAA,EAAA,EA5DA;AA4DA;AACA,MAAA,aAAA,EAAA,EA7DA;AA6DA;AACA,MAAA,OAAA,EAAA,EA9DA;AA8DA;AACA,MAAA,oBAAA,EAAA,EA/DA;AA+DA;AACA,MAAA,gBAAA,EAAA,EAhEA;AAgEA;AACA,MAAA,QAAA,EAAA,EAjEA;AAiEA;AACA,MAAA,eAAA,EAAA;AACA,QAAA,WAAA,EAAA,EADA;AACA;AACA,QAAA,SAAA,EAAA,EAFA;AAEA;AACA,QAAA,WAAA,EAAA,EAHA;AAGA;AACA,QAAA,IAAA,EAAA,EAJA;AAIA;AACA,QAAA,QAAA,EAAA,EALA;AAKA;AACA,QAAA,MAAA,EAAA,GANA;AAMA;AACA,QAAA,OAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,EARA;AASA,QAAA,cAAA,EAAA;AATA,OAlEA;AA6EA,MAAA,WAAA,EAAA;AAAA,QAAA,OAAA,EAAA,IAAA;AAAA,QAAA,OAAA,EAAA,IAAA;AAAA,QAAA,WAAA,EAAA,IAAA;AAAA,QAAA,cAAA,EAAA,IAAA;AAAA,QAAA,cAAA,EAAA,IAAA;AAAA,QAAA,cAAA,EAAA;AAAA,OA7EA;AA8EA,MAAA,SAAA,EAAA,EA9EA;AA+EA,MAAA,WAAA,EAAA,EA/EA;AAgFA,MAAA,oBAAA,EAAA,EAhFA;AAkFA,MAAA,cAAA,EAAA,EAlFA;AAmFA,MAAA,SAAA,EAAA,EAnFA;AAoFA,MAAA,aAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAJA,EAKA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OALA,EAMA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OANA,EAOA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAPA,EAQA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OARA,EASA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OATA,EAUA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAVA,EAWA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAXA,EAYA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAZA,EAaA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAbA,EAcA;AACA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAfA,EAgBA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAhBA,EAiBA;AAAA,QAAA,KAAA,EAAA,YAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAjBA,EAkBA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAlBA,EAmBA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OAnBA,EAoBA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OApBA,EAqBA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OArBA,CApFA;AA2GA,MAAA,MAAA,EAAA;AACA,QAAA,GAAA,EAAA,KADA;AACA;AACA,QAAA,IAAA,EAAA,EAFA;AAEA;AACA,QAAA,SAAA,EAAA,CAHA;AAGA;AACA,QAAA,WAAA,EAAA,CAJA;AAIA;AACA,QAAA,OAAA,EAAA,CALA;AAMA,QAAA,IAAA,EAAA;AANA,OA3GA;AAmHA,MAAA,OAAA,EAAA;AACA,QAAA,OAAA,EAAA,KADA;AAEA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,SAAA,EAAA,OAJA;AAKA,UAAA,MAAA,EAAA,iBALA;AAMA,UAAA,QAAA,EAAA,GANA;AAOA,UAAA,QAAA,EAAA;AAPA,SADA,EAUA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,SAAA,EAAA,OAJA;AAKA,UAAA,MAAA,EAAA,iBALA;AAMA,UAAA,QAAA,EAAA,GANA;AAOA,UAAA,QAAA,EAAA;AAPA,SAVA,EAmBA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,SAAA,EAAA,OAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,MAAA,EAAA,iBALA;AAMA,UAAA,QAAA,EAAA,GANA;AAOA,UAAA,QAAA,EAAA;AAPA,SAnBA,EA4BA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,GAAA,EAAA,UAFA;AAGA,UAAA,SAAA,EAAA,OAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,MAAA,EAAA,cALA;AAMA,UAAA,QAAA,EAAA,GANA;AAOA,UAAA,QAAA,EAAA;AAPA,SA5BA,EAqCA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,GAJA;AAKA,UAAA,QAAA,EAAA;AALA,SArCA,EA4CA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SA5CA,EAmDA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,GAAA,EAAA,QAFA;AAGA,UAAA,MAAA,EAAA,YAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,QAAA,EAAA,EALA;AAMA,UAAA,QAAA,EAAA;AANA,SAnDA,EA2DA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,GAAA,EAAA,YAHA;AAIA,UAAA,MAAA,EAAA,gBAJA;AAKA,UAAA,QAAA,EAAA,EALA;AAMA,UAAA,QAAA,EAAA;AANA,SA3DA,EAmEA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,iBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SAnEA,EA0EA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,GAAA,EAAA,kBAFA;AAGA,UAAA,MAAA,EAAA,sBAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,QAAA,EAAA,EALA;AAMA,UAAA,QAAA,EAAA;AANA,SA1EA,EAkFA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,SAFA;AAGA,UAAA,MAAA,EAAA,aAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,QAAA,EAAA,EALA;AAMA,UAAA,QAAA,EAAA;AANA,SAlFA,EA0FA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,sBAFA;AAGA,UAAA,MAAA,EAAA,0BAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,QAAA,EAAA,EALA;AAMA,UAAA,QAAA,EAAA;AANA,SA1FA,EAkGA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,MAAA,EAAA,iBAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,QAAA,EAAA,EALA;AAMA,UAAA,QAAA,EAAA;AANA,SAlGA,EA0GA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,UAFA;AAGA,UAAA,MAAA,EAAA,cAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,QAAA,EAAA,EALA;AAMA,UAAA,QAAA,EAAA;AANA,SA1GA,EAkHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA,gBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA;AALA,SAzHA,EAgIA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,YAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,GAJA;AAKA,UAAA,QAAA,EAAA;AALA,SAhIA,EAuIA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,QAAA,EAAA,EAHA;AAIA,UAAA,QAAA,EAAA,GAJA;AAKA,UAAA,MAAA,EAAA,gBAAA,CAAA,EAAA,MAAA,EAAA;AACA,gBAAA,GAAA,GAAA,MAAA,CAAA,GAAA;AACA,gBAAA,IAAA,GAAA,GAAA,CAAA,QAAA,KAAA,CAAA,GAAA,KAAA,GAAA,KAAA;AACA,mBAAA,CAAA,CAAA,MAAA,EAAA,EAAA,EAAA,IAAA,CAAA;AACA;AATA,SAvIA,EAkJA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,KAAA,EAAA,OAFA;AAGA,UAAA,GAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA,GALA;AAMA,UAAA,KAAA,EAAA,QANA;AAOA,UAAA,MAAA,EAAA;AAPA,SAlJA,EA2JA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,OAFA;AAGA,UAAA,GAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA,GALA;AAMA,UAAA,KAAA,EAAA,QANA;AAOA,UAAA,MAAA,EAAA;AAPA,SA3JA,CAFA;AAsKA,QAAA,IAAA,EAAA,EAtKA;AAuKA,QAAA,QAAA,EAAA;AAvKA;AAnHA,KAAA;AA6RA,GAlfA;AAmfA,EAAA,OAAA,oBACA,YAAA,CAAA,CAAA,UAAA,EAAA,gBAAA,CAAA,CADA;AAEA;AACA,IAAA,SAHA,qBAGA,GAHA,EAGA;AACA,WAAA,OAAA,CAAA,KAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,GAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA;AAKA,KATA;AAUA;AACA,IAAA,eAXA,6BAWA;AAAA;;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,WAAA,EAAA,kBAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,MAAA,CAAA,QAAA,CAAA,IAAA,EAAA,2BAAA;AAEA,WAAA,OAAA,GAAA,IAAA;AACA,WAAA,YAAA,CAAA,aAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA;AACA,UAAA,aAAA,EAAA;AADA,SAAA;AAGA,OAJA;AAKA,UAAA,MAAA,GAAA;AACA,QAAA,QAAA,EAAA,GADA;AAEA,QAAA,aAAA,EAAA,KAAA,YAAA,CAAA,WAFA;AAGA,QAAA,SAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA,WAHA;AAIA,cAAA,QAJA;AAKA;AACA,QAAA,MAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,IAAA,CAAA,GAAA,IAAA,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,IAAA,CAAA,GAAA,IAAA,GAAA;AANA,OAAA;AASA,MAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA,QAAA;AACA,MAAA,WAAA,CACA,MADA,CAAA,CAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA,EAAA,iBAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,GAAA,KAAA,CADA,CAEA;AACA,SAHA,MAGA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,UAAA,MAAA,CAAA,GAAA,GAAA,GAAA,CAAA,IAAA,CAAA,EAAA;AACA;AACA,OAbA;AAcA,KA/CA;AAgDA,IAAA,YAhDA,0BAgDA;AACA,MAAA,aAAA,CAAA;AAAA,QAAA,GAAA,EAAA,KAAA,SAAA,CAAA,IAAA;AAAA,OAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAEA,CAFA;AAGA,KApDA;AAqDA,IAAA,aArDA,yBAqDA,IArDA,EAqDA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,EAAA,qBAAA;AACA,WAAA,UAAA,GAAA,IAAA,CAAA,IAAA;AACA,WAAA,SAAA,GAAA,IAAA,CAAA,GAAA;;AACA,UAAA,KAAA,SAAA,CAAA,MAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,IAAA,QAAA,EAAA;AACA,aAAA,YAAA;AACA,OAFA,MAEA;AACA,aAAA,MAAA;AACA;AACA,KA9DA;AA+DA,IAAA,MA/DA,oBA+DA;AAAA;;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,IAAA,CAAA,IAAA,KAAA,UAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AAEA;AACA,aAAA,OAAA,GAAA,IAAA;AACA,QAAA,KAAA,CAAA,OAAA,CAAA;AACA,UAAA,GAAA,EAAA,qCADA;AAEA,UAAA,MAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,KAAA;AAHA,SAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;;AACA,cAAA,IAAA,GAAA,MAAA,CAJA,CAKA;AACA;AACA;;AACA,UAAA,SAAA,CAAA;AAAA,YAAA,MAAA,EAAA,IAAA,CAAA,SAAA,CAAA;AAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,GAAA,EAAA,WAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,WAHA,EARA,CAYA;AAEA,SAlBA;AAmBA;AACA,KAxFA;AAyFA,IAAA,kBAzFA,gCAyFA;AACA;AACA,MAAA,SAAA,CAAA,CACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,OAAA,CAAA,CASA,IATA,CASA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA,EAAA,qBAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA,iBAAA,EAHA,CAIA;AACA;AACA,OAfA;AAgBA,KA3GA;AA4GA,IAAA,MA5GA,oBA4GA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,YAAA,CAAA,YAAA,EAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,EAAA,oBAAA;AACA,WAAA,SAAA,CAAA,MAAA,GAAA,IAAA,CAAA,CAAA,CAAA,CAAA,EAAA;;AACA,UAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,iBAAA;AACA,OAFA,MAEA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,gBAAA;AACA,OAFA,MAEA;AACA;AACA;AACA,aAAA,UAAA,GAAA,EAAA;AACA,aAAA,YAAA,GAAA;AACA,UAAA,aAAA,EAAA,EADA;AAEA,UAAA,WAAA,EAAA,EAFA;AAGA,UAAA,EAAA,EAAA;AAHA,SAAA;AAKA,aAAA,SAAA,GAAA,IAAA;AAEA;AACA,KAhIA;AAiIA,IAAA,SAjIA,uBAiIA;AACA,WAAA,QAAA,CAAA,KAAA,CAAA,IAAA;AACA,KAnIA;AAoIA,IAAA,aApIA,2BAoIA;AACA,WAAA,QAAA,CAAA,KAAA,CAAA,IAAA;AACA,KAtIA;;AAwIA;AACA,IAAA,aAzIA,yBAyIA,EAzIA,EAyIA;AAAA;;AACA,UAAA,iBAAA,GAAA,EAAA;;AACA,UAAA,KAAA,oBAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AAAA;AAAA;AAAA;;AAAA;AACA,iCAAA,KAAA,oBAAA,wIAAA;AAAA,gBAAA,IAAA;;AACA,gBAAA,IAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,mBAAA,QAAA,CAAA,IAAA,CAAA,mBAAA;AACA;AACA;;AACA,YAAA,iBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQA,QAAA,EAAA,GAAA,iBAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA,eAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,OAAA,CAAA,OAAA,GAAA,IAAA;;AACA,YAAA,cAAA,CAAA;AAAA,cAAA,GAAA,EAAA;AAAA,aAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,cAAA,MAAA,CAAA,eAAA;;AACA,cAAA,MAAA,CAAA,OAAA,CAAA,OAAA,GAAA,KAAA;AACA,aAJA;;AAKA,YAAA,MAAA,CAAA,oBAAA,GAAA,EAAA;AACA;AAXA,SAAA;AAaA,OAtBA,MAsBA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AACA;AAEA,KArKA;;AAuKA;AACA,IAAA,WAxKA,yBAwKA;AAAA;;AACA,UAAA,KAAA,oBAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,GAAA,GAAA,KAAA,oBAAA,CAAA,CAAA,CAAA;AACA,QAAA,mBAAA,CAAA;AAAA,UAAA,EAAA,EAAA,GAAA,CAAA;AAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AAAA,cAAA,KAAA,EAAA,MAAA;AACA,cAAA,OAAA,EAAA,sBAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,WAAA,GAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,WAAA,GAAA;AADA,aAAA;AAGA,WAJA,MAIA;AACA,YAAA,YAAA,CAAA,GAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,kBAAA,SAAA,GAAA,EAAA;;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,qBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,kBAAA,SAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,SAAA,GAAA,GAAA;AACA;;AACA,gBAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AAAA,kBAAA,KAAA,EAAA,MAAA;AACA,kBAAA,OAAA,EAAA,WAAA,SAAA,GAAA;AADA,iBAAA;AAGA,eAPA,MAOA;AACA,gBAAA,mBAAA,CAAA;AAAA,kBAAA,EAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,sBAAA,GAAA,CAAA,IAAA,IAAA,CAAA,CAAA,EAAA;AACA,oBAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AAAA,sBAAA,KAAA,EAAA,MAAA;AAAA,sBAAA,OAAA,EAAA;AAAA,qBAAA;AACA,mBAFA,MAEA;AACA,oBAAA,MAAA,CAAA,cAAA,CAAA;AACA,sBAAA,KAAA,EAAA,YAAA,CAAA,OAAA,EAAA,aAAA;AADA,qBAAA;;AAGA,oBAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,sBAAA,IAAA,EAAA,aADA;AAEA,sBAAA,KAAA,EAAA;AAAA,wBAAA,EAAA,EAAA,GAAA,CAAA;AAAA,uBAFA;AAGA,sBAAA,OAAA,EAAA;AAHA,qBAAA;AAKA;AACA,iBAdA;AAeA;AACA,aA3BA,EA2BA,KA3BA,CA2BA,UAAA,GAAA,EAAA;AACA,cAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,aA7BA;AA8BA;AACA,SAtCA;AAuCA,OAzCA,MAyCA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AACA;AAEA,KAtNA;;AAuNA;AACA,IAAA,aAxNA,2BAwNA;AAAA;;AACA,UAAA,KAAA,oBAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,GAAA,GAAA,KAAA,oBAAA,CAAA,CAAA,CAAA,CADA,CAEA;;AACA,QAAA,mBAAA,CAAA;AAAA,UAAA,EAAA,EAAA,GAAA,CAAA;AAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AACA,cAAA,KAAA,EAAA,MADA;AAEA,cAAA,OAAA,EAAA,sBAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,WAAA,GAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,WAAA,GAAA;AAFA,aAAA;AAIA,WALA,MAKA;AACA,YAAA,YAAA,CAAA,GAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,kBAAA,SAAA,GAAA,EAAA;;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,qBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,kBAAA,SAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,SAAA,GAAA,GAAA;AACA;;AACA,gBAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AACA,kBAAA,KAAA,EAAA,MADA;AAEA,kBAAA,OAAA,EAAA,WAAA,SAAA,GAAA;AAFA,iBAAA;AAIA,eARA,MAQA;AACA,gBAAA,MAAA,CAAA,kBAAA,CAAA,GAAA;AACA;AACA,aAdA;AAeA;AACA,SAxBA,EAHA,CA4BA;AACA;AACA;AACA,OA/BA,MA+BA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AACA;AACA,KA3PA;AA4PA,IAAA,kBA5PA,8BA4PA,GA5PA,EA4PA;AAAA;;AACA,UAAA,GAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA,mCAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,cAAA,CAAA;AACA,cAAA,KAAA,EAAA,YAAA,CAAA,OAAA,EAAA,eAAA;AADA,aAAA;;AAGA,YAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,cAAA,IAAA,EAAA,eADA;AAEA,cAAA,KAAA,EAAA;AAAA,gBAAA,EAAA,EAAA,GAAA,CAAA;AAAA,eAFA;AAGA,cAAA,OAAA,EAAA;AAHA,aAAA;AAKA;AAZA,SAAA;AAeA,OAhBA,MAgBA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,cAAA;AACA;AACA,KAhRA;;AAkRA;AACA,IAAA,WAnRA,uBAmRA,EAnRA,EAmRA;AACA,WAAA,KAAA,CAAA,eAAA,CAAA,WAAA,CAAA,EAAA;AACA,KArRA;;AAsRA;AACA,IAAA,WAvRA,uBAuRA,EAvRA,EAuRA;AACA,WAAA,KAAA,CAAA,eAAA,CAAA,WAAA,CAAA,EAAA;AACA,KAzRA;;AA0RA;AACA,IAAA,SA3RA,qBA2RA,EA3RA,EA2RA;AACA,WAAA,KAAA,CAAA,aAAA,CAAA,SAAA,CAAA,EAAA;AACA,KA7RA;;AA+RA;AACA,IAAA,UAhSA,wBAgSA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA;AAHA,OAAA,EADA,CAMA;AACA,KAvSA;AAwSA,IAAA,YAxSA,0BAwSA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,KAAA,WAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,YAAA,KAAA,WAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,WAAA,CAAA,OAAA,GAAA,CAAA,CAAA;AACA,UAAA,IAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,SAHA,MAGA;AACA,UAAA,kBAAA,CAAA,IAAA,CAAA,WAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,WAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EAAA;AACA,cAAA,IAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA;AACA;AACA,WALA;AAMA;AACA;AACA,KAvTA;AAwTA,IAAA,eAxTA,6BAwTA;AACA,UAAA,KAAA,GAAA,KAAA,eAAA;;AACA,UAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA;AACA,KA/TA;AAiUA,IAAA,cAjUA,4BAiUA;AACA,WAAA,oBAAA,GAAA,EAAA;AACA,WAAA,eAAA,GAAA,EAAA;AACA,WAAA,WAAA,GAAA;AAAA,QAAA,IAAA,EAAA,CAAA;AAAA,QAAA,OAAA,EAAA,IAAA;AAAA,QAAA,OAAA,EAAA,IAAA;AAAA,QAAA,WAAA,EAAA,IAAA;AAAA,QAAA,cAAA,EAAA,IAAA;AAAA,QAAA,cAAA,EAAA;AAAA,OAAA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,MAAA,CAAA,KAAA,OAAA,CAAA;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,KAAA,CAAA,KAAA,WAAA;AACA,WAAA,WAAA,CAAA,WAAA,GAAA,KAAA,WAAA;AACA,KAzUA;AA0UA,IAAA,eA1UA,6BA0UA;AAAA;;AACA,WAAA,SAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,KAAA,CADA,CACA;AACA,OAFA,EAEA,IAFA,CAAA;AAGA,WAAA,oBAAA,GAAA,EAAA;AACA,WAAA,eAAA;;AACA,UAAA,KAAA,WAAA,CAAA,WAAA,IAAA,EAAA,EAAA;AACA,aAAA,WAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,KAAA,CAAA,KAAA,WAAA,EAVA,CAWA;AACA,KAtVA;AAuVA,IAAA,WAvVA,yBAuVA;AAAA;AAAA;AAAA;;AAAA;AACA,+BAAA,KAAA,KAAA,CAAA,YAAA,CAAA,UAAA,wIAAA;AAAA,cAAA,IAAA;;AACA,cAAA,IAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CADA,CACA;AACA;AACA;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KA7VA;AA8VA,IAAA,eA9VA,2BA8VA,GA9VA,EA8VA;AAAA;;AACA,WAAA,oBAAA,GAAA,EAAA;AACA,MAAA,GAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,MAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAnWA;AAoWA,IAAA,eApWA,2BAoWA,GApWA,EAoWA;AACA,UAAA,SAAA,GAAA,SAAA;AACA,UAAA,SAAA,GAAA,UAAA,GAAA,CAAA,WAAA,GAAA,KAAA;;AACA,UAAA,GAAA,CAAA,UAAA,KAAA,CAAA,EAAA;AACA,QAAA,SAAA,GAAA,YAAA;AACA,QAAA,SAAA,GAAA,UAAA,GAAA,CAAA,WAAA,GAAA,KAAA;AACA;;AACA,UAAA,GAAA,CAAA,eAAA,IAAA,CAAA,IAAA,GAAA,CAAA,UAAA,GAAA,CAAA,EAAA;AACA,QAAA,SAAA,GAAA,gBAAA;AACA,QAAA,SAAA,GAAA,UAAA,GAAA,CAAA,WAAA,GAAA,KAAA;AACA;;AACA,WAAA,cAAA,GAAA;AACA,QAAA,MAAA,EAAA,GAAA,CAAA,EADA;AAEA,QAAA,SAAA,EAAA,SAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OAAA;AAKA,UAAA,IAAA,GAAA,IAAA;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,QADA;AAEA,QAAA,OAAA,EAAA,gBAAA,GAAA,CAAA,WAAA,GAAA,WAFA;AAGA,QAAA,IAAA,EAAA,gBAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA,UAAA,UAAA,CAAA,YAAA;AACA,YAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,OAAA;AACA,WAFA,EAEA,GAFA,CAAA;AAGA,SARA;AAQA,QAAA,QAAA,EAAA,oBAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA;AAVA,OAAA;AAYA,KAjYA;AAkYA,IAAA,SAlYA,qBAkYA,GAlYA,EAkYA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,YAAA,CAAA,GAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,YAAA,SAAA,GAAA,EAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,SAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,SAAA,GAAA,GAAA;AACA;;AACA,UAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,OAAA,EAAA,WAAA,SAAA,GAAA;AAAA,WAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA,SANA,MAMA,IAAA,GAAA,CAAA,UAAA,IAAA,CAAA,IAAA,GAAA,CAAA,UAAA,IAAA,CAAA,EAAA;AACA,UAAA,cAAA,CAAA;AAAA,YAAA,EAAA,EAAA,GAAA,CAAA;AAAA,WAAA,CAAA,CAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,KAAA;;AACA,gBAAA,IAAA,CAAA,IAAA,CAAA,EAAA,IAAA,IAAA,IAAA,IAAA,CAAA,IAAA,CAAA,EAAA,IAAA,SAAA,EAAA;AACA,cAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AAAA,gBAAA,KAAA,EAAA,MAAA;AAAA,gBAAA,OAAA,EAAA;AAAA,eAAA;AACA,aAFA,MAEA;AACA,cAAA,IAAA,CAAA,eAAA,CAAA,GAAA;AACA;AACA,WARA;AASA,SAVA,MAUA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,KAAA;AACA,UAAA,IAAA,CAAA,eAAA,CAAA,GAAA;AACA;AACA,OAvBA;AAwBA,KA5ZA;AA6ZA,IAAA,QA7ZA,oBA6ZA,GA7ZA,EA6ZA,UA7ZA,EA6ZA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,WAAA,SAAA,GAAA;AACA,QAAA,MAAA,EAAA,GAAA,CAAA,EADA;AAEA,QAAA,QAAA,EAAA,GAAA,CAAA,SAFA;AAGA,QAAA,UAAA,EAAA;AAHA,OAAA;AAKA,KApaA;AAqaA,IAAA,UAraA,sBAqaA,IAraA,EAqaA;AAAA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,KAAA,GAFA,CAGA;AACA,KAzaA;AA0aA,IAAA,KA1aA,iBA0aA,MA1aA,EA0aA;AAAA;;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,WAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,OAAA,CAAA,OAAA,GAAA,KAAA;AACA,QAAA,OAAA,CAAA,OAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,OAAA,CAAA,OAAA,CAAA,IAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,OAJA;AAKA,KAjbA;AAkbA,IAAA,cAlbA,0BAkbA,IAlbA,EAkbA;AACA,UAAA,IAAA,GAAA,EAAA;AAAA,UAAA,IAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,aAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,IAAA,CAAA,IAAA,CAAA,KAAA,aAAA,CAAA,CAAA,EAAA,KAAA;AACA,QAAA,IAAA,CAAA,IAAA,CAAA,KAAA,aAAA,CAAA,CAAA,EAAA,GAAA;AACA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,GAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,SAAA,EAAA,IAJA;AAKA,QAAA,QAAA,EAAA;AALA,OAAA;AAOA,WAAA,WAAA,CAAA,QAAA,GAAA,KAAA,OAAA,CAAA,QAAA;AACA,MAAA,KAAA,CAAA,qBAAA,CAAA,MAAA;AACA,WAAA,KAAA,CAAA,IAAA;AACA;AACA,KAncA;AAocA,IAAA,aApcA,2BAocA;AACA,WAAA,KAAA,CAAA,IAAA,CAAA;AACA,QAAA,MAAA,EAAA,gBAAA,CAAA,EAAA;AACA,iBAAA,CAAA,CAAA,KAAA,EAAA,CACA,CAAA,CAAA,UAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,KAAA,EAAA;AADA;AADA,WAAA,CADA,EAMA,CAAA,CAAA,KAAA,EAAA,kBAAA,CANA,CAAA,CAAA;AAQA;AAVA,OAAA;AAYA,KAjdA;AAkdA,IAAA,SAldA,qBAkdA,IAldA,EAkdA;AAAA;;AACA,WAAA,aAAA;AACA,WAAA,MAAA,CAAA,GAAA,GAAA,IAAA;AACA,UAAA,MAAA,GAAA,KAAA,WAAA;;AACA,UAAA,IAAA,KAAA,SAAA,EAAA;AACA,aAAA,cAAA,CAAA,KAAA,iBAAA,CAAA,KAAA,OAAA,CAAA,IAAA,CAAA;AACA;AACA,OAHA,MAGA,IAAA,IAAA,KAAA,KAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,KAAA,MAAA,CAAA,IAAA;AACA,OAVA,CAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,4CADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,MAAA,KAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,IAAA,GAAA,IAAA,IAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AACA,YAAA,QAAA,GAAA,YAAA,CAFA,CAGA;;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,QAAA;AACA,YAAA,GAAA,GAAA,GAAA,CAAA,eAAA,CAAA,IAAA,CAAA;AACA,YAAA,CAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA;AACA,QAAA,CAAA,CAAA,IAAA,GAAA,GAAA;AACA,QAAA,CAAA,CAAA,QAAA,GAAA,QAAA,CARA,CAQA;;AACA,QAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA;AACA,QAAA,CAAA,CAAA,KAAA,GAVA,CAYA;;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA;AACA,UAAA,GAAA,CAAA,eAAA,CAAA,GAAA,EAFA,CAEA;AACA,SAHA,EAGA,GAHA,CAAA;;AAIA,QAAA,OAAA,CAAA,KAAA,CAAA,IAAA;AACA,OAlBA;AAmBA,KApgBA;AAqgBA,IAAA,iBArgBA,6BAqgBA,KArgBA,EAqgBA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,KAAA,CAAA,iBAAA,EAAA,IAAA,CAAA,QAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,KAAA,CAAA,aAAA,EAAA,IAAA,CAAA,WAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,SAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,UAAA,GAAA,KAAA,CAAA,SAAA,EAAA,IAAA,CAAA,OAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,KAAA,CAAA,eAAA,EAAA,IAAA,CAAA,aAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,uBAAA,GAAA,KAAA,CAAA,sBAAA,EAAA,IAAA,CAAA,oBAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,KAAA,CAAA,aAAA,EAAA,IAAA,CAAA,WAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,KAAA,CAAA,QAAA,EAAA,IAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,KAAA,CAAA,UAAA,EAAA,IAAA,CAAA,QAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,KAAA,CAAA,aAAA,EAAA,IAAA,CAAA,WAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,KAAA,CAAA,eAAA,EAAA,IAAA,CAAA,aAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,KAAA,CAAA,gBAAA,EAAA,IAAA,CAAA,WAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,KAAA,CAAA,YAAA,EAAA,IAAA,CAAA,UAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,KAAA,CAAA,kBAAA,EAAA,IAAA,CAAA,gBAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,KAAA,CAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,EAAA,UAAA,EAAA,UAAA,CAAA;AACA,QAAA,IAAA,CAAA,uBAAA;AACA,QAAA,IAAA,CAAA,aAAA;AACA,OAlBA;AAmBA,aAAA,KAAA;AACA,KA1hBA;AA2hBA;AACA,IAAA,oBA5hBA,kCA4hBA;AACA,UAAA,KAAA,WAAA,CAAA,OAAA,IAAA,IAAA,IAAA,KAAA,WAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AAAA;AACA;;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,KAAA,WAAA,CAAA,OAAA,EAJA,CAIA;AACA,KAjiBA;AAkiBA,IAAA,gBAliBA,4BAkiBA,IAliBA,EAkiBA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,WAAA,CAAA,WAAA,GAAA,IAAA,CAAA,IAAA,CAFA,CAGA;AACA,KAtiBA;AAuiBA,IAAA,WAviBA,yBAuiBA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,WAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,SAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,SAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,EAAA;AACA,YAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;AACA;AACA;;AACA,QAAA,IAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AAEA,YAAA,WAAA,GAAA,IAAA,CAAA,WAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,EAAA;AACA,YAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA;AACA;AACA;;AACA,QAAA,IAAA,CAAA,OAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,QAAA,IAAA,CAAA,WAAA,CAAA,OAAA,GAAA,MAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;;AACA,QAAA,OAAA,CAAA,eAAA,GApBA,CAqBA;;AACA,OAtBA;AAuBA,KAhkBA;AAikBA,IAAA,IAjkBA,kBAikBA;AAAA;;AACA,WAAA,MAAA,GAAA,KAAA,CAAA,QAAA,CAAA,CADA,CACA;;AACA,WAAA,UAAA,GAAA,KAAA,CAAA,iBAAA,CAAA,CAFA,CAEA;;AACA,WAAA,WAAA,GAAA,KAAA,CAAA,aAAA,CAAA,CAHA,CAGA;;AACA,WAAA,WAAA,GAAA,KAAA,CAAA,aAAA,CAAA,CAJA,CAIA;;AACA,WAAA,YAAA,GAAA,KAAA,CAAA,eAAA,CAAA,CALA,CAKA;;AACA,WAAA,OAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CANA,CAMA;;AACA,WAAA,oBAAA,GAAA,KAAA,CAAA,sBAAA,CAAA,CAPA,CAOA;;AACA,WAAA,QAAA,GAAA,KAAA,CAAA,UAAA,CAAA,CARA,CAQA;;AACA,WAAA,gBAAA,GAAA,KAAA,CAAA,kBAAA,CAAA,CATA,CASA;;AACA,WAAA,gBAAA,CAAA,IAAA,CAAA;AAAA,QAAA,QAAA,EAAA,CAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,WAAA,gBAAA,CAAA,IAAA,CAAA;AAAA,QAAA,QAAA,EAAA,CAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,QAAA,eAAA,CAAA;AAAA,UAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA;AAAA,SAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,UAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,WAAA;AACA,SAHA;AAIA,OATA;AAUA,MAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,QAAA,OAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA,EAvBA,CA0BA;AACA;AA5lBA,IAnfA;AAilCA,EAAA,OAjlCA,qBAilCA;AACA,SAAA,IAAA;AACA,SAAA,aAAA,GAAA,KAAA,OAAA,CAAA,OAAA;;AACA,QAAA,KAAA,aAAA,IAAA,IAAA,IAAA,KAAA,aAAA,IAAA,IAAA,EAAA;AACA,WAAA,aAAA,CAAA,OAAA,CACA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,GAAA,EAAA;AAAA,OADA;AAGA,WAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CACA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,GAAA,EAAA,yBAFA;AAGA,QAAA,KAAA,EAAA,QAHA;AAIA,QAAA,QAAA,EAAA,GAJA;AAKA,QAAA,QAAA,EAAA;AALA,OADA;AASA,KAhBA,CAiBA;;AACA;AAnmCA,CAAA", "sourcesContent": ["<template>\r\n  <!-- *****电表管理  <AUTHOR> -->\r\n    <div>\r\n        <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n<!--        <add-ammeter-page ref=\"addAmmeterPage\"></add-ammeter-page>-->\r\n        <view-ammeter-page ref=\"viewAmmeterPage\"></view-ammeter-page>\r\n        <view-station-page ref=\"viewStationPage\"></view-station-page>\r\n        <view-quota-page ref=\"viewQuotaPage\" ></view-quota-page>\r\n        <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n        <div class=\"noaccount\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"formInline\" :model=\"queryParams\" >\r\n                    <Row>\r\n                        <Col span=\"5\" v-if=\"configVersion=='ln'|| configVersion=='LN'\">\r\n                            <FormItem label=\"供电局电表编号：\" prop=\"supplybureauammetercode\" :label-width=\"110\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.supplybureauammetercode\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\" v-else>\r\n                            <FormItem label=\"电表编号：\" prop=\"ammetername\" :label-width=\"110\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.ammetername\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"项目名称：\" prop=\"projectname\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.projectname\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"电表类型：\" prop=\"ammetertype\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-select clearable v-model=\"queryParams.ammetertype\"\r\n                                           category=\"ammeterType\" style=\"width:30vm\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"7\">\r\n                            <FormItem label=\"用电类型：\" prop=\"classifications\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <Cascader clearable :data=\"classificationData\" :change-on-select=\"true\" v-model=\"classifications\"></Cascader>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" :label-width=\"110\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" :label-width=\"100\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"queryParams.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" :label-width=\"100\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.country\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n<!--                        <Col span=\"5\">-->\r\n<!--                            <FormItem label=\"所属分局或支局\" prop=\"substation\" class=\"form-line-height\">-->\r\n<!--                                <cl-input v-model=\"queryParams.substation\"></cl-input>-->\r\n<!--                            </FormItem>-->\r\n<!--                        </Col>-->\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"单据状态：\" prop=\"billStatus\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-select clearable v-model=\"queryParams.billStatus\"\r\n                                           category=\"basicBillStatus\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n<!--                        <Col span=\"5\">-->\r\n<!--                            <FormItem label=\"管理负责人\" prop=\"ammetermanager\">-->\r\n<!--                                <cl-input v-model=\"queryParams.ammetermanager\"></cl-input>-->\r\n<!--                            </FormItem>-->\r\n<!--                        </Col>-->\r\n                        <Col span=\"4\">\r\n                            <FormItem label=\"电价性质：\" prop=\"electrovalencenature\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-select clearable v-model=\"queryParams.electrovalencenature\"\r\n                                           category=\"electrovalenceNature\" style=\"width:30vm\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"3\">\r\n                          <FormItem label=\"状态：\" prop=\"status\" :label-width=\"100\" class=\"form-line-height\">\r\n                            <cl-select v-model=\"queryParams.status\"\r\n                                       category=\"status\" style=\"width:30vm\"\r\n                                       labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                          </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"对外结算类型：\" prop=\"directsupplyflag\" :label-width=\"110\" class=\"form-line-height\">\r\n                                <cl-select clearable v-model=\"queryParams.directsupplyflag\"\r\n                                           category=\"directSupplyFlag\" style=\"width:30vm\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"局站名称：\" prop=\"stationName\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.stationName\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"资源局站id：\" prop=\"resstationcode\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.resstationcode\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"4\">\r\n                          <FormItem label=\"局站编码：\" prop=\"stationcode\" :label-width=\"100\" class=\"form-line-height\">\r\n                            <cl-input v-model=\"queryParams.stationcode\"></cl-input>\r\n                          </FormItem>\r\n                        </Col>\r\n                        <Col span=\"3\">\r\n                            <FormItem label=\"是否实体：\" prop=\"isentityammeter\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.isentityammeter\">\r\n                                <Option v-for=\"item in isentityammeters\" :value=\"item.typeCode\"\r\n                                        :key=\"item.typeCode\">{{item.typeName}}\r\n                                </Option>\r\n                               </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                  <Row>\r\n                    <Col span=\"5\">\r\n                      <FormItem label=\"5GR站址编码:\" :label-width=\"100\" class=\"form-line-height\">\r\n                        <cl-input v-model=\"queryParams.stationcode5gr\"></cl-input>\r\n                      </FormItem>\r\n                    </Col>\r\n                    <Col span=\"5\">\r\n                      <FormItem label=\"5GR站址名称:\" :label-width=\"100\" class=\"form-line-height\">\r\n                        <cl-input v-model=\"queryParams.stationname5gr\"></cl-input>\r\n                      </FormItem>\r\n                    </Col>\r\n                    <Col span=\"5\">\r\n                      <div class=\"form-line-height\">\r\n                        <Button style=\"margin-left: 5px;width:69px;\" type=\"success\" :disabled=\"isDisable\"  icon=\"ios-search\" @click=\"_onSearchHandle()\" >搜索 </Button>\r\n                        <Button style=\"margin-left: 5px;width:69px;\" type=\"info\" icon=\"ios-redo\" @click=\"_onResetHandle\" >重置</Button>\r\n                      </div>\r\n                    </Col>\r\n                  </Row>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <cl-table ref=\"ammeterTable\"\r\n                  :searchLayout=\"ammeter.filter\"\r\n                  :query-params=\"queryParams\"\r\n                  :columns=\"ammeter.columns\"\r\n                  :data=\"ammeter.data\"\r\n                  :loading=\"ammeter.loading\"\r\n                  select-enabled\r\n                  select-multiple\r\n                  @on-selection-change=\"handleSelectRow\"\r\n                  :total=\"ammeter.total\"\r\n                  :pageSize=\"ammeter.pageSize\"\r\n                  @on-query=\"query\"\r\n                  :searchable=\"false\"\r\n                  :exportable=\"false\">\r\n            <div slot=\"buttons\">\r\n                <!-- <Button type=\"primary\" @click=\"applyW\">加入白名单</Button> -->\r\n                <Button type=\"primary\" @click=\"addAmmeter\">添加</Button>\r\n                <Button type=\"success\" @click=\"editAmmeter\">修改</Button>\r\n                <Button type=\"warning\" @click=\"changeAmmeter\">换表</Button>\r\n                <Button type=\"error\" @click=\"removeAmmeter\">删除</Button>\r\n                <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n                    <Button type='default' style=\"margin-left: 5px\" >导出\r\n                        <Icon type='ios-arrow-down'></Icon>\r\n                    </Button>\r\n                    <DropdownMenu slot='list'>\r\n                        <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                        <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n                    </DropdownMenu>\r\n                </Dropdown>\r\n            </div>\r\n        </cl-table>\r\n        <cl-wf-btn ref=\"clwfbtn\" :isStart=\"true\" :params=\"workFlowParams\" @on-ok=\"doWorkFlow\" v-show=\"false\"></cl-wf-btn>\r\n        <!-- 查看流程 -->\r\n        <Modal v-model=\"showWorkFlow\" title=\"电表流程及审批意见跟踪表\" :width=\"800\">\r\n            <WorkFlowInfoComponet :wfHisParams=\"hisParams\" v-if=\"showWorkFlow\"></WorkFlowInfoComponet>\r\n        </Modal>\r\n        <!-- 加入白名单 -->\r\n        <Modal v-model=\"whiteList\" title=\"加入白名单\" @on-ok=\"submitWhiteList\" :width=\"800\">\r\n\r\n            <Form :model=\"addWhiteList\" ref=\"addWhiteList\" :rules=\"ruleValidate\" :label-width=\"80\"\r\n                class=\"margin-right-width\">\r\n                <Row>\r\n                    <Col span=\"12\">\r\n                        <FormItem label=\"白名单类型：\" :label-width=\"120\" prop=\"whitelistType\">\r\n\r\n                        <Select\r\n                                ref=\"selects\"\r\n                                :multiple=\"true\"\r\n                                :clearable=\"true\"\r\n                                v-model=\"addWhiteList.whitelistType\">\r\n                                <Option value=\"1\">一站多表</Option>\r\n                                <Option value=\"2\">一表多站</Option>\r\n                                <Option value=\"3\">单价</Option>\r\n                        </Select>\r\n                    </FormItem>\r\n                    </Col>\r\n                </Row>\r\n                <Row>\r\n                    <Col span=\"24\">\r\n                        <FormItem label=\"申请理由：\" :label-width=\"120\" prop=\"applyReason\">\r\n                            <cl-input type=\"textarea\" :rows=\"3\" v-model=\"addWhiteList.applyReason\"></cl-input>\r\n                            <!-- <label v-if=\"oldData.memo != null &&oldData.memo != ammeter.memo\"\r\n                                    style=\"color: red;\">历史数据：{{oldData.memo}}</label> -->\r\n                        </FormItem>\r\n                    </Col>\r\n                </Row>\r\n                <Row style=\"margin-left: 20.8px;\">\r\n                    <Col span=\"24\" style=\"position: relative;\">\r\n                        <!-- <cl-form v-model=\"attach.fileForm\" :label-width=\"120\" :layout=\"attach.formLayout\"></cl-form> -->\r\n                        <attach-file :param=\"fileParam\" :attachData=\"attachData\"\r\n                                         v-on:setAttachData=\"setAttachData\"/>\r\n                        <span style=\"position: absolute; top: 28px; left: 417px;\">支持pdf/word/jpg\\png文件上传</span>\r\n                    </Col>\r\n                </Row>\r\n            </Form>\r\n        </Modal>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import { whiteList, whiteInsert } from '@/api/account';\r\n    import {selectChangeAmmeter,listAmmeter, removeAmmeter,checkAcountByUpdate,getUserByUserRole,getCountryByUserId,getUserdata,getCountrysdata,getClassification,checkStartFlow} from '@/api/basedata/ammeter.js'\r\n    import {isInTodoList}from\"@/api/alertcontrol/alertcontrol\";\r\n    import {blist,btext} from \"@/libs/tools\";\r\n    import viewAmmeterPage from './viewAmmeter.vue'\r\n    import viewQuotaPage from '@/view/basedata/quota/viewQuota.vue';\r\n    import viewStationPage from '@/view/basedata/station/viewStation.vue'\r\n    import countryModal from \"./countryModal\";\r\n    import ProcessInfo from '@/view/basic/system/workflow/process-info';\r\n    import WorkFlowInfoComponet from '@/view/basic/system/workflow/workFlowInfoComponet'\r\n\r\n    import attachFile from \"@/view/basedata/whitelist/attachFile.vue\";\r\n    import { attchList, removeAttach } from '@/api/basedata/ammeter.js'\r\n    import axios from '@/libs/api.request'\r\n\r\n    import excel from '@/libs/excel'\r\n\r\n    import {mapMutations} from \"vuex\";\r\n    import routers from '@/router/routers';\r\n    import {getHomeRoute} from '@/libs/util';\r\n    export default {\r\n        name: 'ammeter',\r\n        components: {\r\n            ProcessInfo,\r\n            WorkFlowInfoComponet,\r\n            // addAmmeterPage,\r\n            // editAmmeterPage,\r\n            viewAmmeterPage,\r\n            viewStationPage,\r\n            viewQuotaPage,\r\n            countryModal,\r\n            attachFile\r\n        },\r\n\r\n        data() {\r\n            //状态\r\n            let renderStatus = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.status) {\r\n                    if (item.typeCode == params.row.status) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //用电类型\r\n            let renderElectroType = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.electroType) {\r\n                    if (item.typeCode == params.row.electrotype) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //电表类型\r\n            let renderAmmeterType = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.ammeterType) {\r\n                    if (item.typeCode == params.row.ammetertype) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //用电性质\r\n            let renderElectroNature = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.electroNaure) {\r\n                    if (item.typeCode == params.row.electronature) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //付费方式\r\n            let renderPayType = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.payType) {\r\n                    if (item.typeCode == params.row.paytype) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //电价性质\r\n            let renderElectrovalenceNature = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.electrovalenceNature) {\r\n                    if (item.typeCode == params.row.electrovalencenature) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //单据状态\r\n            let renderBillStatus = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.billStatus) {\r\n                    if (item.typeCode == params.row.billStatus) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //产权归属\r\n            let renderProperty = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.property) {\r\n                    if (item.typeCode == params.row.property) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //对外结算类型\r\n            let renderDirectsupplyflag = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.directsupplyFlag) {\r\n                    if (item.typeCode == params.row.directsupplyflag) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //查看详情\r\n            let renterViewAmmeter = (h, params) => {\r\n                let column = params.column.key;\r\n                return h(\"div\", [h(\"u\", {\r\n                    style:{display:\"inline\"},\r\n                    on: {\r\n                        click: () => {\r\n                            this.viewAmmeter(params.row.id);\r\n                        }\r\n                    }\r\n                }, params.row[column])]);\r\n            };\r\n            //查看局站详情\r\n            let renterViewStation = (h, params) => {\r\n                let column = params.column.key;\r\n                return h(\"div\", [h(\"u\", {\r\n                    on: {\r\n                        click: () => {\r\n                            this.viewStation(params.row.stationcode);\r\n                        }\r\n                    }\r\n                }, params.row[column])]);\r\n            };\r\n          //资源局站id\r\n          let termnameFormat = (h, params) => {\r\n            if (!params.row.resstationcode) {\r\n              // 返回红色文字提示：请维护\r\n              return h('div', {\r\n                style: {\r\n                  color: '#f00'\r\n                },\r\n                on: {\r\n                  click: () => {\r\n                    this.$Message.info(\"在修改电表页面，关联局站信息处维护\");\r\n                  }\r\n                }\r\n              }, '请维护')\r\n            } else {\r\n              return h('div', params.row.resstationcode)\r\n            }\r\n\r\n          };\r\n            let renderW = (h, params) => {\r\n                let that = this;\r\n                let text, type = \"\";\r\n                let row = params.row;\r\n                if (row.billStatus != 0 && row.billStatus != 3 && row.processinstId != null) {\r\n                    text = \"查看\";\r\n                    type = \"success\";\r\n                } else if (params.row.billStatus == 0 || params.row.billStatus == 3) {\r\n                    text = \"提交\";\r\n                    type = \"primary\";\r\n                }\r\n                if(type == \"\"){\r\n                    return h(\"div\", {}, text);\r\n                }\r\n                return h(\"Button\",\r\n                {\r\n                    props: {\r\n                        type: type, size: \"small\"\r\n                    },\r\n                    on: {\r\n                        click() {\r\n                            if (row.billStatus!=0 && row.billStatus != 3 && row.processinstId != null) {\r\n                                that.showFlow(params.row, params.row.processinstId);\r\n                            } else if (params.row.billStatus == 0 || params.row.billStatus == 3) {\r\n                                that.loading=true;\r\n                                that.startFlow(params.row);\r\n                            }\r\n                        }\r\n                    }\r\n                },\r\n                text\r\n                );\r\n            };\r\n            let renderQuota = (h, params) => {\r\n                let that = this;\r\n                return h(\"Button\", {\r\n                    props: {\r\n                        type: \"success\", size: \"small\"\r\n                    },attrs: {\r\n                        disabled: params.row.quotaId?false:true\r\n                    },style: {\r\n                        opacity:params.row.quotaId?1:0.4\r\n                    }, on: {\r\n                        click() {\r\n                            that.viewQuota(params.row.quotaId);\r\n                        }\r\n                    }\r\n                }, \"查看\");\r\n            };\r\n            const valisheredepartname = (rule, value, callback) => {\r\n                //console.log(rule, value, \"rule, value5555555555555555\");\r\n                if (value == null || value == '') {\r\n                    callback(new Error('不能为空'))\r\n                } else {\r\n                    callback()\r\n                }\r\n            };\r\n            return {\r\n                multiFiles: null,\r\n                removeIds: [],\r\n                attachData: [],\r\n                fileParam: {\r\n                    busiId: \"\",\r\n                    busiAlias: \"附件(协议管理)\",\r\n                    categoryCode: \"file\",\r\n                    areaCode: \"ln\"\r\n                },\r\n                ruleValidate: {\r\n                    applyReason: [\r\n                        {required: true, validator: valisheredepartname, trigger: 'change, blur'}\r\n                    ],\r\n                    whitelistType: [\r\n                        {required: true, validator: valisheredepartname, trigger: 'change, blur'}\r\n                    ]\r\n                },\r\n                addWhiteList: {\r\n                    whitelistType: \"\",\r\n                    applyReason: \"\",\r\n                    id: \"\"\r\n                },\r\n                attach: {\r\n                    fileForm: {\r\n                        file: null\r\n                    },\r\n                    formLayout: [\r\n                        {\r\n                            label: '上传附件',\r\n                            prop: 'file',\r\n                            formItemType: 'file',\r\n                            width: 300,\r\n                            format: this.format\r\n                        }\r\n                    ],\r\n                    loading: false,\r\n                    columns: [],\r\n                    data:[],\r\n                },\r\n                whiteList: false,\r\n                loading:false,\r\n                showWorkFlow: false,\r\n                configVersion:null,//版本\r\n                isDisable:false,\r\n                filterColl: true,//搜索面板展开\r\n                isAdmin:false,\r\n                company:null,//用户默认公司\r\n                country:null,//用户默认所属部门\r\n                countryName:null,//用户默认所属部门\r\n\r\n                classificationData:[],//用电类型\r\n                classifications:[],//用电类型\r\n\r\n                demoList: [],\r\n                isentityammeter:{},\r\n                isentityammeters:[],\r\n                status:[],//状态\r\n                billStatus:[],//单据状态\r\n                electroType:[],//用电类型\r\n                ammeterType:[],//电表类型\r\n                electroNature:[],//用电性质\r\n                payType:[],//付费方式\r\n                electrovalenceNature:[],//电价性质\r\n                directsupplyFlag:[],//对外结算类型\r\n                property:[],//产权归属\r\n                queryParamsList: {\r\n                projectname: '',//项目名称\r\n                meterCode: '',//电表编号\r\n                stationName: '',//局站名称\r\n                city: '',//地市\r\n                district: '',//区县\r\n                status: '1',//状态\r\n                company: '',\r\n                countryName: '',\r\n                bi11statusName: '',\r\n                },\r\n                queryParams:{country:null,company:null,countryName:null,resstationcode:null, stationcode5gr: null, stationname5gr: null},\r\n                companies:[],\r\n                departments:[],\r\n                multipleSelectionRow: [],\r\n\r\n                workFlowParams: {},\r\n                hisParams: {},\r\n                exportColumns:[\r\n                    {title: '电表编号',key: 'ammetername'},\r\n                    {title: '项目名称',key: 'projectname'},\r\n                    {title: '关联局站名',key: 'stationName'},\r\n                    {title: '所属分公司',key: 'companyName'},\r\n                    {title: '所属部门', key: 'countryName'},\r\n                    {title: '状态',key: 'statusStr'},\r\n                    {title: '单据状态',key: 'billStatusStr'},\r\n                    {title: '用电类型',key: 'electrotypename'},\r\n                    {title: '对外结算类型',key: 'directsupplyflagStr'},\r\n                    {title: '付费方式',key: 'paytypeStr'},\r\n                    {title: '电价性质',key: 'electrovalencenatureStr'},\r\n                    {title: '电表类型',key: 'ammetertypeStr'},\r\n                    {title: '产权归属',key: 'propertyStr'},\r\n                    // {title: '支局/分局',key: 'substation'},\r\n                    {title: '管理负责人',key: 'ammetermanager'},\r\n                    {title: '创建时间',key: 'createTime'},\r\n                    {title: '供电局电表户号或编号',key: 'supplybureauammetercode'},\r\n                    {title: '资源局站id：',key: 'termname'},\r\n                    {title: '倍率：',key: 'magnification'},\r\n                    {title: '单价：',key: 'price'},\r\n                    {title: '分割比例：',key: 'percent'},\r\n                ],\r\n                export: {\r\n                    run: false,//是否正在执行导出\r\n                    data: \"\",//导出数据\r\n                    totalPage: 0,//一共多少页\r\n                    currentPage: 0,//当前多少页\r\n                    percent: 0,\r\n                    size: 200000\r\n                },\r\n                ammeter: {\r\n                    loading: false,\r\n                    columns: [\r\n                        {\r\n                            title: '电表编号',\r\n                            key: 'ammetername',\r\n                            align: 'center',\r\n                            className: \"td-id\",\r\n                            render: renterViewAmmeter,\r\n                            minWidth: 120,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '项目名称',\r\n                            key: 'projectname',\r\n                            align: 'center',\r\n                            className: \"td-id\",\r\n                            render: renterViewAmmeter,\r\n                            minWidth: 120,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '关联局站名',\r\n                            key: 'stationName',\r\n                            className: \"td-id\",\r\n                            align: 'center',\r\n                            render: renterViewStation,\r\n                            minWidth: 100,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                          title: '资源局站id',\r\n                          key: 'termname',\r\n                          className: \"td-id\",\r\n                          align: 'center',\r\n                          render: termnameFormat,\r\n                          minWidth: 100,\r\n                          maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '所属分公司',\r\n                            key: 'companyName',\r\n                            align: 'center',\r\n                            minWidth: 100,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '所属部门',\r\n                            key: 'countryName',\r\n                            align: 'center',\r\n                            minWidth: 80,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '状态',\r\n                            key: 'status',\r\n                            render: renderStatus,\r\n                            align: 'center',\r\n                            minWidth: 55,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '单据状态',\r\n                            align: 'center',\r\n                            key: 'billStatus',\r\n                            render: renderBillStatus,\r\n                            minWidth: 80,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '用电类型',\r\n                            key: 'electrotypename',\r\n                            align: 'center',\r\n                            minWidth: 90,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '对外结算类型',\r\n                            key: 'directsupplyflag',\r\n                            render: renderDirectsupplyflag,\r\n                            align: 'center',\r\n                            minWidth: 80,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '付费方式',\r\n                            key: 'paytype',\r\n                            render: renderPayType,\r\n                            align: 'center',\r\n                            minWidth: 70,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '电价性质',\r\n                            key: 'electrovalencenature',\r\n                            render: renderElectrovalenceNature,\r\n                            align: 'center',\r\n                            minWidth: 70,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '电表类型',\r\n                            key: 'ammetertype',\r\n                            render: renderAmmeterType,\r\n                            align: 'center',\r\n                            minWidth: 70,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '产权归属',\r\n                            key: 'property',\r\n                            render: renderProperty,\r\n                            align: 'center',\r\n                            minWidth: 70,\r\n                            maxWidth:200\r\n                        },\r\n                        // {\r\n                        //     title: '支局/分局',\r\n                        //     key: 'substation',\r\n                        //     align: 'center',\r\n                        //     minWidth: 80,\r\n                        //     maxWidth:200\r\n                        // },\r\n                        {\r\n                            title: '管理负责人',\r\n                            key: 'ammetermanager',\r\n                            align: 'center',\r\n                            minWidth: 90,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '创建时间',\r\n                            key: 'createTime',\r\n                            align: 'center',\r\n                            minWidth: 110,\r\n                            maxWidth:200\r\n                        },\r\n                      {\r\n                        title: '上传附件',\r\n                        align: 'center',\r\n                        minWidth: 70,\r\n                        maxWidth: 200,\r\n                        render: (h, params) => {\r\n                          const row = params.row;\r\n                          const text = row.isAttach === 0 ? '未上传' : '已上传';\r\n                          return h('span', {}, text);\r\n                        }\r\n                      },\r\n                        {\r\n                            title: \"流程\",\r\n                            fixed: 'right',\r\n                            key: \"action\",\r\n                            minWidth: 60,\r\n                            maxWidth:200,\r\n                            align: 'center',\r\n                            render: renderW\r\n                        },\r\n                        {\r\n                            title: \"查看定额\",\r\n                            fixed: 'right',\r\n                            key: \"action\",\r\n                            minWidth: 65,\r\n                            maxWidth:200,\r\n                            align: 'center',\r\n                            render: renderQuota\r\n                        }],\r\n                    data: [],\r\n                    pageSize:10\r\n                }\r\n            }\r\n        },\r\n        methods: {\r\n            ...mapMutations([\"closeTag\", \"closeTagByName\"]),\r\n            //验证错误弹出提示框\r\n            errorTips(str){\r\n                this.$Notice.error({\r\n                    title: '提示',\r\n                    desc: str,\r\n                    duration: 10\r\n                });\r\n            },\r\n            // typeList\r\n            submitWhiteList() {\r\n                let arr = [];\r\n                let data = this.$refs.ammeterTable.getSelection();\r\n                console.log(data[0].ammetername, \"data.ammetername\");\r\n                console.log(this.attach.fileForm.file, \"this.attach.fileForm.file\");\r\n\r\n                this.loading = true;\r\n                this.addWhiteList.whitelistType.forEach(item => {\r\n                    arr.push({\r\n                        whitelistType: item,\r\n                    });\r\n                })\r\n                let params = {\r\n                    typeList: arr,\r\n                    applyArgument: this.addWhiteList.applyReason,\r\n                    meterCode: data[0].ammetername,\r\n                    \"fj\": \"未上传附件；\",\r\n                    // typeList.whitelistType: '1',\r\n                    dwjslx: data[0].directsupplyflag == 1?'直供':data[0].directsupplyflag == 2?'转供':\"\",\r\n\r\n                    }\r\n                console.log(params, \"params\");\r\n                whiteInsert(\r\n                    params\r\n                ).then(res => {\r\n                    console.log(res, \"res666666666666\");\r\n                    this.whiteList = false;\r\n                    if(res.data.code == 500) {\r\n                    this.loading = false;\r\n                    // this.$Message.error(res.data.msg);\r\n                    }else{\r\n                    this.$Message.success(\"保存成功\");\r\n                    this.loading = false;\r\n                    this.id1 = res.data.id;\r\n                    }\r\n                })\r\n            },\r\n          removeAttach(){\r\n            removeAttach({ids:this.removeIds.join()}).then(() => {\r\n\r\n            });\r\n          },\r\n            setAttachData(data){\r\n            console.log(data, \"data555555555555555\");\r\n            this.multiFiles = data.data;\r\n            this.removeIds = data.ids;\r\n            if(this.removeIds.length!= 0 && data.type == 'remove'){\r\n              this.removeAttach();\r\n            }else{\r\n              this.upload();\r\n            }\r\n          },\r\n            upload(){\r\n            if (this.attachData.length != 0 && this.multiFiles.length != 0){\r\n\r\n              // this.$Message.info(\"提示:上传文件过大可能导致上传失败！\");\r\n              this.loading = true;\r\n              axios.request({\r\n                url: '/common/attachments/uploadMultiFile',\r\n                method: 'post',\r\n                data: this.multiFiles\r\n              }).then((res) => {\r\n                if(res.data.code != 0){\r\n                  this.loading = false;\r\n                }\r\n                let that = this;\r\n                // if(that.fileParam.busiId == \"\") {\r\n                //     this.$Message.error(\"cuowu\");\r\n                // }else {\r\n                    attchList({busiId:that.fileParam.busiId}).then(res => {\r\n                    console.log(res, \"attchList\");\r\n                    that.attachData = Object.assign([], res.data.rows);\r\n                });\r\n                // }\r\n\r\n              })\r\n            }\r\n          },\r\n          getAccountMessages() {\r\n            // this.listTb.loading = true;\r\n            whiteList({\r\n                // meterCode: this.queryParamsList.meterCode,\r\n                // projectname: this.queryParamsList.projectname,\r\n                // stationName: this.queryParamsList.stationName,\r\n                // company: this.queryParamsList.company,\r\n                // countryName: this.queryParamsList.countryName,\r\n                // status: this.queryParamsList.status,\r\n                // size: this.pageSize,\r\n                // current: this.pageNum\r\n                }).then(res => {\r\n            // this.listTb.loading = false;\r\n                console.log(res, \"queryParamsList res\");\r\n                console.log(res.data.length, \"res.data.length\");\r\n                // this.pageTotal = res.data.total;\r\n                // this.insideData = res.data.rows;\r\n            })\r\n            },\r\n            applyW() {\r\n                let data = this.$refs.ammeterTable.getSelection();\r\n                console.log(data, \"data55555555555555\");\r\n                this.fileParam.busiId = data[0].id;\r\n                if(data.length > 1) {\r\n                    this.errorTips(\"只能选择一个电表申请加入白名单\");\r\n                }else if(data.length == 0) {\r\n                    this.errorTips(\"请选择一个电表申请加入白名单\");\r\n                }else {\r\n                    // this.getAccountMessages();\r\n                    // data[0].name\r\n                    this.attachData = [];\r\n                    this.addWhiteList = {\r\n                        whitelistType: \"\",\r\n                        applyReason: \"\",\r\n                        id: \"\"\r\n                };\r\n                    this.whiteList = true;\r\n\r\n                }\r\n            },\r\n            onModalOK() {\r\n                this.$Message.error('确定')\r\n            },\r\n            onModalCancel() {\r\n                this.$Message.error('取消')\r\n            },\r\n\r\n            /*删除*/\r\n            removeAmmeter(id) {\r\n                let multipleSelection = [];\r\n                if (this.multipleSelectionRow.length > 0) {\r\n                    for(let item of this.multipleSelectionRow){\r\n                        if(item.billStatus != 0){\r\n                            this.$Message.info(\"所选数据包含非草稿数据，不能删除！\");\r\n                            return ;\r\n                        }\r\n                        multipleSelection.push(item.id);\r\n                    }\r\n                    id = multipleSelection.join(',');\r\n                    this.$Modal.confirm({\r\n                        title: '温馨提示',\r\n                        content: '<p>确认删除吗?</p>',\r\n                        onOk: () => {\r\n                            this.ammeter.loading = true;\r\n                            removeAmmeter({ids: id}).then(res => {\r\n                                this.$Message.success(\"删除成功\");\r\n                                this._onSearchHandle();\r\n                                this.ammeter.loading = false;\r\n                            });\r\n                            this.multipleSelectionRow = [];\r\n                        },\r\n                    });\r\n                } else {\r\n                    this.$Message.info(\"请至少选择一行\");\r\n                }\r\n\r\n            },\r\n\r\n            /*编辑*/\r\n            editAmmeter() {\r\n                if (this.multipleSelectionRow.length == 1) {\r\n                    let row = this.multipleSelectionRow[0];\r\n                    selectChangeAmmeter({id:row.id}).then(res => {\r\n                        //存在于代办中时，报出提示\r\n                        if (res.data.length > 0) {\r\n                            this.$Modal.warning({title: \"温馨提示\",\r\n                                content: \"该电表已经存在换表电表【电表编号：\" + res.data[0].ammetername + \"，项目名称：\" + res.data[0].projectname + \"】,不允许再修改\"\r\n                            });\r\n                        } else {\r\n                            isInTodoList(row.id, 1).then(res => {\r\n                                //存在于代办中时，报出提示\r\n                                let ownername = \"\";\r\n                                if (res.data.length > 0) {\r\n                                    for (let i = 0; i < res.data.length; i++) {\r\n                                        ownername += res.data[i].ownername + ' ';\r\n                                    }\r\n                                    this.$Modal.warning({title: \"温馨提示\",\r\n                                        content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可修改数据\"\r\n                                    });\r\n                                } else {\r\n                                    checkAcountByUpdate({id: row.id}).then(res => {\r\n                                        //修改数据前验证台账\r\n                                        if (res.data == -1) {\r\n                                            this.$Modal.warning({title: \"温馨提示\", content: \"该数据已填写台账或正在报账中，处理后才可修改数据\"});\r\n                                        } else {\r\n                                            this.closeTagByName({\r\n                                                route: getHomeRoute(routers, \"editAmmeter\"),\r\n                                            });\r\n                                            this.$router.push({\r\n                                                name: \"editAmmeter\",\r\n                                                query: {id: row.id},\r\n                                                replace: true\r\n                                            })\r\n                                        }\r\n                                    })\r\n                                }\r\n                            }).catch(err => {\r\n                                console.log(err);\r\n                            });\r\n                        }\r\n                    });\r\n                } else {\r\n                    this.$Message.info(\"请选择其中一行\");\r\n                }\r\n\r\n            },\r\n            /*换表*/\r\n            changeAmmeter() {\r\n                if (this.multipleSelectionRow.length == 1) {\r\n                    let row = this.multipleSelectionRow[0];\r\n                    // if(row.property!=2) {\r\n                        selectChangeAmmeter({id: row.id}).then(res => {\r\n                            //存在于代办中时，报出提示\r\n                            if (res.data.length > 0) {\r\n                                this.$Modal.warning({\r\n                                    title: \"温馨提示\",\r\n                                    content: \"该电表已经存在换表电表【电表编号：\" + res.data[0].ammetername + \"，项目名称：\" + res.data[0].projectname + \"】\"\r\n                                });\r\n                            } else {\r\n                                isInTodoList(row.id, 1).then(res => {\r\n                                    //存在于代办中时，报出提示\r\n                                    let ownername = \"\";\r\n                                    if (res.data.length > 0) {\r\n                                        for (let i = 0; i < res.data.length; i++) {\r\n                                            ownername += res.data[i].ownername + ' ';\r\n                                        }\r\n                                        this.$Modal.warning({\r\n                                            title: \"温馨提示\",\r\n                                            content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可修改数据\"\r\n                                        });\r\n                                    } else {\r\n                                        this.checkChangeAmmeter(row);\r\n                                    }\r\n                                });\r\n                            }\r\n                        });\r\n                    // }else{\r\n                    //     this.$Message.info(\"该表站址产权归属为铁塔，不能进行换表操作\");\r\n                    // }\r\n                } else {\r\n                    this.$Message.info(\"请选择其中一行\");\r\n                }\r\n            },\r\n            checkChangeAmmeter(row){\r\n                if(row.billStatus !=0) {\r\n                    this.$Modal.confirm({\r\n                        title: '温馨提示',\r\n                        content: '<p>换表流程结束，旧表将停用，新表启用，请确认是否换表？</p>',\r\n                        onOk: () => {\r\n                            this.closeTagByName({\r\n                                route: getHomeRoute(routers, \"changeAmmeter\"),\r\n                            });\r\n                            this.$router.push({\r\n                                name: \"changeAmmeter\",\r\n                                query: {id: row.id},\r\n                                replace: true\r\n                            })\r\n                        },\r\n                    });\r\n\r\n                }else{\r\n                    this.$Message.info(\"草稿状态不能操作换表数据\");\r\n                }\r\n            },\r\n\r\n            /*查看*/\r\n            viewAmmeter(id) {\r\n                this.$refs.viewAmmeterPage.initAmmeter(id);\r\n            },\r\n            /*查看局站*/\r\n            viewStation(id) {\r\n                this.$refs.viewStationPage.initStation(id);\r\n            },\r\n            /*查看定额*/\r\n            viewQuota(id) {\r\n                this.$refs.viewQuotaPage.initQuota(id);\r\n            },\r\n\r\n            /*添加*/\r\n            addAmmeter() {\r\n                this.$router.push({\r\n                    name: \"addAmmeter\",\r\n                    query:{},\r\n                    replace:true\r\n                })\r\n                // this.$refs.addAmmeterPage.initAmmeter();\r\n            },\r\n            selectChange(){\r\n                let that = this;\r\n                if (this.queryParams.company != undefined) {\r\n                    if(this.queryParams.company == \"-1\"){\r\n                        that.queryParams.country = -1;\r\n                        that.queryParams.countryName = null;\r\n                    }else{\r\n                        getCountryByUserId(that.queryParams.company).then(res => {\r\n                            if(res.data.departments.length != 0){\r\n                                that.queryParams.country = res.data.departments[0].id;\r\n                                that.queryParams.countryName = res.data.departments[0].name;\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            },\r\n            setElectroyType(){\r\n                let types = this.classifications;\r\n                if(types.length != 0){\r\n                    this.queryParams.electrotype = types[types.length-1];\r\n                }else{\r\n                    this.queryParams.electrotype = null;\r\n                }\r\n            },\r\n\r\n            _onResetHandle(){\r\n                this.multipleSelectionRow=[];\r\n                this.classifications = [];\r\n                this.queryParams = {type:0,company:null,country:null,countryName:null, stationcode5gr: null, stationname5gr: null};\r\n                this.queryParams.company= this.company;\r\n                this.queryParams.country= Number(this.country);\r\n                this.$refs.ammeterTable.query(this.queryParams);\r\n                this.queryParams.countryName = this.countryName;\r\n            },\r\n            _onSearchHandle(){\r\n                this.isDisable=true\r\n                setTimeout(()=>{\r\n                    this.isDisable=false   //点击一次时隔两秒后才能再次点击\r\n                },2000)\r\n                this.multipleSelectionRow=[];\r\n                this.setElectroyType();\r\n                if(this.queryParams.countryName == \"\"){\r\n                    this.queryParams.country = \"-1\";\r\n                }\r\n                this.$refs.ammeterTable.query(this.queryParams);\r\n                // this.query(this.queryParams);\r\n            },\r\n            setDisabled(){\r\n                for(let item of this.$refs.ammeterTable.insideData){\r\n                    if(item.billStatus != 0){\r\n                        item._disabled = true;//禁止选择\r\n                    }\r\n                }\r\n            },\r\n            handleSelectRow(val){\r\n                this.multipleSelectionRow = [];\r\n                val.forEach(item => {\r\n                    this.multipleSelectionRow.push(item);\r\n                });\r\n            },\r\n            startFlowSubmit(row){\r\n                let busiAlias = \"ADD_AMM\";\r\n                let busiTitle = \"新增电表(\"+row.projectname+\")审批\";\r\n                if(row.billStatus === 3){\r\n                    busiAlias = \"MODIFY_AMM\";\r\n                    busiTitle = \"修改电表(\"+row.projectname+\")审批\";\r\n                }\r\n                if(row.ischangeammeter == 1 && row.billStatus<2){\r\n                    busiAlias = \"AMM_SWITCH_AMM\";\r\n                    busiTitle = \"电表换表(\"+row.projectname+\")审批\";\r\n                }\r\n                this.workFlowParams = {\r\n                    busiId: row.id,\r\n                    busiAlias: busiAlias,\r\n                    busiTitle: busiTitle\r\n                }\r\n                let that = this;\r\n                this.$Modal.confirm({\r\n                    title: '电表提交流程',\r\n                    content: '<p>是否提交电表 (' + row.projectname + ') 到流程</p>',\r\n                    onOk: () => {\r\n                        that.loading = true;\r\n                        setTimeout(function () {\r\n                            that.$refs.clwfbtn.onClick();\r\n                        }, 300);\r\n                    },onCancel: () => {\r\n                        that.loading = false;\r\n                    }\r\n                });\r\n            },\r\n            startFlow(row) {\r\n                let that = this;\r\n                isInTodoList(row.id,1).then(res => {\r\n                    //存在于代办中时，报出提示\r\n                    let ownername = \"\";\r\n                    if (res.data.length > 0) {\r\n                        for (let i = 0; i < res.data.length; i++) {\r\n                            ownername += res.data[i].ownername + ' ';\r\n                        }\r\n                        that.$Modal.warning({title:\"温馨提示\",content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可继续提交流程\"});\r\n                        that.loading = false;\r\n                    }else if(row.billStatus == 3 || row.billStatus == 4){\r\n                        checkStartFlow({id:row.id}).then(res1 => {\r\n                            /*提交流程验证用户是否有数据需要提交*/\r\n                            that.loading = false;\r\n                            if (res1.data.id == null || res1.data.id == undefined) {\r\n                                that.$Modal.warning({title:\"温馨提示\",content: \"您没有可提交的数据\"});\r\n                            }else{\r\n                                that.startFlowSubmit(row);\r\n                            }\r\n                        });\r\n                    }else{\r\n                        that.loading = false;\r\n                        that.startFlowSubmit(row);\r\n                    }\r\n                });\r\n            },\r\n            showFlow(row, procInstId) {\r\n                this.showWorkFlow = true;\r\n                this.hisParams = {\r\n                    busiId: row.id,\r\n                    busiType: row.busiAlias,\r\n                    procInstId: procInstId\r\n                }\r\n            },\r\n            doWorkFlow(data) { //流程回调\r\n                this.loading = false;\r\n                this.$refs.ammeterTable.query();\r\n                // this.query(this.queryParams);\r\n            },\r\n            query(params) {\r\n                this.ammeter.loading = true;\r\n                listAmmeter(params).then(res => {\r\n                    this.ammeter.loading = false;\r\n                    this.ammeter.total = res.data.total\r\n                    this.ammeter.data = Object.assign([], res.data.rows)\r\n                });\r\n            },\r\n            beforeLoadData(data) {\r\n                let cols=[],keys=[]\r\n                for (let i = 0; i < this.exportColumns.length; i++) {\r\n                    cols.push(this.exportColumns[i].title)\r\n                    keys.push(this.exportColumns[i].key)\r\n                }\r\n                const params = {\r\n                  title: cols,\r\n                  key: keys,\r\n                  data: data,\r\n                  autoWidth: true,\r\n                  filename: '电表数据导出'\r\n                };\r\n                this.queryParams.pageSize = this.ammeter.pageSize;\r\n                excel.export_array_to_excel(params);\r\n                this.$Spin.hide();\r\n                return\r\n            },\r\n            exportLoading(){\r\n                this.$Spin.show({\r\n                    render: (h) => {\r\n                        return h('div', [\r\n                            h('Progress', {\r\n                                style: {\r\n                                    width: '800px'\r\n                                },\r\n                            }),\r\n                            h('div', '导出中，请勿刷新页面......')\r\n                        ])\r\n                    }\r\n                });\r\n            },\r\n            exportCsv(name) {\r\n                this.exportLoading();\r\n                this.export.run = true;\r\n                let params = this.queryParams;\r\n                if (name === 'current') {\r\n                    this.beforeLoadData(this.setValueByForEach(this.ammeter.data))\r\n                    return;\r\n                } else if (name === 'all') {\r\n                    params.pageNum = 1;\r\n                    params.pageSize = this.export.size;\r\n                }\r\n                // let req = {\r\n                //     url : \"/business/ammeterorprotocol/list\",\r\n                //     method : \"get\",\r\n                //     params : params\r\n                // };\r\n                // this.ammeter.loading = true;\r\n                // axios.request(req).then(res => {\r\n                //     this.ammeter.loading = false;\r\n                //     if (res.data) {\r\n                //         let array = res.data.rows;\r\n                //         this.beforeLoadData(this.setValueByForEach(array));\r\n                //     }\r\n                // }).catch(err => {\r\n                //     console.log(err);\r\n                // });\r\n                let req = {\r\n                    url : \"/business/ammeterorprotocol/exportMeterAll\",\r\n                    method : \"post\",\r\n                    params : params\r\n                };\r\n                axios.file(req).then(res => {\r\n                  const blob = new Blob([res])\r\n                  const fileName = '电表数据导出.xls';\r\n                  // 创建一个下载链接\r\n                  console.log('下载文件:', fileName);\r\n                  const url = URL.createObjectURL(blob);\r\n                  const a = document.createElement('a');\r\n                  a.href = url;\r\n                  a.download = fileName; // 设置下载文件名\r\n                  document.body.appendChild(a);\r\n                  a.click();\r\n\r\n                  // 清理\r\n                  setTimeout(() => {\r\n                    document.body.removeChild(a);\r\n                    URL.revokeObjectURL(url); // 释放内存\r\n                  }, 100);\r\n                  this.$Spin.hide();\r\n                })\r\n            },\r\n            setValueByForEach(array){\r\n                array.forEach(function (item) {\r\n                    item.categoryStr = btext(\"ammeterCategory\", item.category,'typeCode','typeName');\r\n                    item.packagetypeStr = btext(\"packageType\", item.packagetype,'typeCode','typeName');\r\n                    item.payperiodStr = btext(\"payPeriod\", item.payperiod,'typeCode','typeName');\r\n                    item.paytypeStr = btext(\"payType\", item.paytype,'typeCode','typeName');\r\n                    item.electronatureStr = btext(\"electroNature\", item.electronature,'typeCode','typeName');\r\n                    item.electrovalencenatureStr = btext(\"electrovalenceNature\", item.electrovalencenature,'typeCode','typeName');\r\n                    item.electrotypeStr = btext(\"electroType\", item.electrotype,'typeCode','typeName');\r\n                    item.statusStr = btext(\"status\", item.status,'typeCode','typeName');\r\n                    item.propertyStr = btext(\"property\", item.property,'typeCode','typeName');\r\n                    item.ammetertypeStr = btext(\"ammeterType\", item.ammetertype,'typeCode','typeName');\r\n                    item.stationstatusStr = btext(\"stationStatus\", item.stationstatus,'typeCode','typeName');\r\n                    item.stationtypeStr = btext(\"BUR_STAND_TYPE\", item.stationtype,'typeCode','typeName');\r\n                    item.ammeteruseStr = btext(\"ammeterUse\", item.ammeteruse,'typeCode','typeName');\r\n                    item.directsupplyflagStr = btext(\"directSupplyFlag\", item.directsupplyflag,'typeCode','typeName');\r\n                    item.billStatusStr = btext(\"basicBillStatus\", item.billStatus,'typeCode','typeName');\r\n                    item.supplybureauammetercode;\r\n                    item.magnification\r\n                });\r\n                return array;\r\n            },\r\n            //选择所属部门开始\r\n            chooseResponseCenter() {\r\n                if(this.queryParams.company == null || this.queryParams.company == \"-1\" ){\r\n                    this.$Message.info(\"请先选择分公司\");return;\r\n                }\r\n                this.$refs.countryModal.choose(this.queryParams.company);//所属部门\r\n            },\r\n            getDataFromModal(data) {\r\n                this.queryParams.country = data.id;\r\n                this.queryParams.countryName = data.name;\r\n                //选择所属部门结束\r\n            },\r\n            getUserData(){\r\n                let that = this;\r\n                getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                    let companies = that.companies;\r\n                    if(res.data.companies != null && res.data.companies.length != 0){\r\n                        if(res.data.companies[0].id != \"2600000000\"){\r\n                            companies = res.data.companies;\r\n                        }\r\n                    }\r\n                    that.company = companies[0].id;\r\n                    that.queryParams.company = companies[0].id;\r\n\r\n                    let departments = that.departments;\r\n                    if(res.data.departments != null && res.data.departments.length != 0){\r\n                        if(res.data.companies[0].id != \"2600000000\"){\r\n                            departments = res.data.departments;\r\n                        }\r\n                    }\r\n                    that.country = departments[0].id;\r\n                    that.countryName = departments[0].name;\r\n                    that.queryParams.country = Number(departments[0].id);\r\n                    that.queryParams.countryName = departments[0].name;\r\n                    this._onSearchHandle();\r\n                   // this.query({pageNum: 1,type:0,pageSize: this.ammeter.pageSize,company:this.company,country:this.country});\r\n                });\r\n            },\r\n            init(){\r\n                this.status = blist(\"status\");//状态\r\n                this.billStatus = blist(\"basicBillStatus\");//单据状态\r\n                this.ammeterType=blist(\"ammeterType\")//电表类型\r\n                this.electroType = blist(\"electroType\");//用电类型\r\n                this.electroNaure = blist(\"electroNature\");//用电性质\r\n                this.payType = blist(\"payType\");//付费方式\r\n                this.electrovalenceNature = blist(\"electrovalenceNature\");//电价性质\r\n                this.property = blist(\"property\");//产权归属\r\n                this.directsupplyFlag = blist(\"directSupplyFlag\");//对外结算类型\r\n                this.isentityammeters.push({typeCode: 0, typeName: '否'});\r\n                this.isentityammeters.push({typeCode: 1, typeName: '是'});\r\n                let that = this;\r\n                getUserByUserRole().then(res => {//根据权限获取分公司\r\n                    that.companies = res.data.companies;\r\n                    if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                        that.isAdmin = true;\r\n                    }\r\n                    getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                        that.departments = res.data;\r\n                        that.getUserData();\r\n                    });\r\n                });\r\n                getClassification().then(res => {//用电类型\r\n                    this.classificationData = res.data;\r\n                });\r\n                // this._onSearchHandle(); \r\n            }\r\n        },\r\n        mounted() {\r\n            this.init();\r\n            this.configVersion = this.$config.version;\r\n            if(this.configVersion=='ln'||this.configVersion=='LN'){\r\n                this.exportColumns.unshift(\r\n                    {title: '供电局电表编号',key: 'supplybureauammetercode'},\r\n                );\r\n                this.ammeter.columns.unshift(\r\n                    {\r\n                        title: '供电局电表编号',\r\n                        key: 'supplybureauammetercode',\r\n                        align: 'center',\r\n                        minWidth: 100,\r\n                        maxWidth:200\r\n                    }\r\n                )\r\n            }\r\n            // this.fileParam.busiId = \"666666\";\r\n        },\r\n        // watch:{\r\n        //     '$route':\"init\"\r\n        // },\r\n\r\n    }\r\n</script>\r\n\r\n<style lang=\"less\">\r\n    td.td-id {\r\n        font-weight: bold;\r\n        color: green;\r\n        cursor: pointer;\r\n    }\r\n    .noaccount .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .noaccount .header-bar-show {\r\n        max-height: 300px;\r\n        /*padding-top: 14px;*/\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .noaccount .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n    .noaccount .row{\r\n        height:30px;\r\n        margin-bottom: -50px;\r\n    }\r\n    .form-line-height{\r\n        margin-bottom:10px;\r\n    }\r\n</style>\r\n"], "sourceRoot": "src/view/basedata/ammeter"}]}