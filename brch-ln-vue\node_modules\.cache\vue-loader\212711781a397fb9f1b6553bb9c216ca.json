{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\statistics\\energymeter\\list-station.vue?vue&type=style&index=0&id=00c39bae&lang=less&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\statistics\\energymeter\\list-station.vue", "mtime": 1754285403055}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749178874080}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnBhZ2UtY2xhc3Mgew0KICBoZWlnaHQ6IDEwMCU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQo="}, {"version": 3, "sources": ["list-station.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6WA;AACA;AACA;AACA;AACA;AACA", "file": "list-station.vue", "sourceRoot": "src/view/statistics/energymeter", "sourcesContent": ["<template>\r\n  <!-- 电表/局站分析报表 -->\r\n  <div class=\"page-class page-card common-wh\">\r\n    <div class=\"query-box\">\r\n      <Row :class=\"filterColl ? 'header-bar-show' : 'header-bar-hide'\">\r\n        <Form ref=\"queryform\" :model=\"queryParams\" :label-width=\"120\">\r\n          <Row class=\"form-row\">\r\n            <Col span=\"5\">\r\n              <FormItem label=\"数据部门:\" prop=\"company\">\r\n                <Select v-model=\"queryParams.company\" clearable>\r\n                  <Option\r\n                    v-for=\"(item, index) in dicts['company']\"\r\n                    :key=\"index\"\r\n                    :value=\"item.id\"\r\n                    >{{ item.name }}</Option\r\n                  >\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"5\">\r\n              <FormItem label=\"状态:\" prop=\"status\">\r\n                <Select v-model=\"queryParams.status\" clearable>\r\n                  <Option\r\n                    v-for=\"(item, index) in dicts['status']\"\r\n                    :key=\"index\"\r\n                    :value=\"item.typeCode\"\r\n                    >{{ item.typeName }}</Option\r\n                  >\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"5\">\r\n              <FormItem label=\"是否大工业用电:\" prop=\"isbigfactories\">\r\n                <Select v-model=\"queryParams.isbigfactories\" clearable>\r\n                  <Option\r\n                    v-for=\"(item, index) in dicts['isbigfactories']\"\r\n                    :key=\"index\"\r\n                    :value=\"item.typeCode\"\r\n                    >{{ item.typeName }}</Option\r\n                  >\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <div style=\"float: right; margin-right: 10px\">\r\n              <Button\r\n                style=\"margin-left: 5px; width: 69px\"\r\n                type=\"success\"\r\n                icon=\"ios-search\"\r\n                @click=\"_onSearchHandle\"\r\n                >搜索\r\n              </Button>\r\n              <Button\r\n                style=\"margin-left: 5px; width: 69px\"\r\n                type=\"info\"\r\n                icon=\"ios-redo\"\r\n                @click=\"_onResetHandle\"\r\n                >重置</Button\r\n              >\r\n            </div>\r\n          </Row>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon\r\n          :type=\"filterColl ? 'md-arrow-dropup' : 'md-arrow-dropdown'\"\r\n          size=\"20\"\r\n          @click=\"filterColl = !filterColl\"\r\n          :color=\"filterColl ? '#000' : '#1ab394'\"\r\n        ></icon>\r\n      </div>\r\n    </div>\r\n    <cl-table\r\n      ref=\"clTable\"\r\n      title=\"列表\"\r\n      :height=\"tableHeight\"\r\n      :query-params=\"queryParams\"\r\n      :columns=\"tableSet.columns\"\r\n      :loading=\"tableSet.loading\"\r\n      :total=\"tableSet.total\"\r\n      :pageSize=\"tableSet.pageSize\"\r\n      :showPage=\"tableSet.showPage\"\r\n      :data=\"tableList\"\r\n      :sum-columns=\"[]\"\r\n      @on-query=\"tableQuery\"\r\n      :searchable=\"false\"\r\n      :exportable=\"false\"\r\n    >\r\n      <div slot=\"buttons\" class=\"table-btns\">\r\n        <Button type=\"default\" @click=\"exportCsv\">导出</Button>\r\n      </div>\r\n    </cl-table>\r\n    <!-- 弹窗：明细 -->\r\n    <modal-list title=\"局站明细\" ref=\"list\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { getUserByUserRole } from \"@/api/basedata/ammeter.js\";\r\nimport { getStationList } from \"@/api/statistics/index\";\r\nimport pageFun from \"@/mixins/pageFun\";\r\nimport { blist, btext } from \"@/libs/tools\";\r\nimport axios from \"@/libs/api.request\";\r\nimport modalList from \"./modal-list\";\r\n\r\nexport default {\r\n  mixins: [pageFun],\r\n  props: [\"flag\"],\r\n  components: { modalList },\r\n  data() {\r\n    return {\r\n      //搜索面板\r\n      filterColl: true, //搜索面板展开\r\n      queryParams: {\r\n        //查询参数\r\n        company: null,\r\n        isbigfactories: null,\r\n        status: null,\r\n      },\r\n      queryedParams: {}, //搜索后的参数\r\n      dicts: {\r\n        company: [], //数据部门\r\n        isbigfactories: [], //是否大工业用电\r\n        status: [], //状态\r\n      },\r\n      //--搜索面板end--\r\n      tableSet: {\r\n        loading: false,\r\n        showPage: true,\r\n        pageTotal: 0,\r\n        pageNumber: 1,\r\n        pageSize: 10, //当前页\r\n        columns: [\r\n          {\r\n            title: \"地市\",\r\n            key: \"orgName\",\r\n            fixed: \"left\",\r\n            width: 150,\r\n            align: \"center\",\r\n          },\r\n        ],\r\n      },\r\n      tableList: [],\r\n      spinShow: false,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.handleHeight(); //table高度自定义\r\n    this.handleColumn();\r\n    this.getDicts(); //部门下拉\r\n  },\r\n  methods: {\r\n    handleColumn() {\r\n      let dicts = [\r\n        {\r\n          name: \"生产用房-通信机房\",\r\n          key: \"productionBuilding10001\",\r\n          stationtype: 10001,\r\n        },\r\n        {\r\n          name: \"生产用房-移动基站\",\r\n          key: \"productionBuilding10002\",\r\n          stationtype: 10002,\r\n        },\r\n        {\r\n          name: \"生产用房-数据中心-对外IDC机柜机房\",\r\n          key: \"productionBuilding10003\",\r\n          stationtype: 10003,\r\n        },\r\n        {\r\n          name: \"生产用房-数据中心-自用业务平台和IT支撑用房\",\r\n          key: \"productionBuilding10004\",\r\n          stationtype: 10004,\r\n        },\r\n        {\r\n          name: \"生产用房-接入局所及室外机柜\",\r\n          key: \"productionBuilding10005\",\r\n          stationtype: 10005,\r\n        },\r\n        {\r\n          name: \"生产用房-其他\",\r\n          key: \"productionBuildingOther\",\r\n          stationtype: -1,\r\n        },\r\n        {\r\n          name: \"非生产用房-管理用房\",\r\n          key: \"nonProductionBuilding20001\",\r\n          stationtype: 20001,\r\n        },\r\n        {\r\n          name: \"非生产用房-渠道用房\",\r\n          key: \"nonProductionBuilding20002\",\r\n          stationtype: 20002,\r\n        },\r\n        {\r\n          name: \"非生产用房-其他\",\r\n          key: \"nonProductionBuildingOther\",\r\n          stationtype: -2,\r\n        },\r\n      ];\r\n      let arr = [];\r\n      let that = this;\r\n      dicts.forEach((item) => {\r\n        arr.push({\r\n          title: item.name,\r\n          align: \"center\",\r\n          children: [\r\n            {\r\n              title: \"数量\",\r\n              align: \"center\",\r\n              width: 130,\r\n              key: `${item.key}`,\r\n              render: (h, params) => {\r\n                let column = params.column.key;\r\n                let row = params.row;\r\n                let info = h(\r\n                  \"u\",\r\n                  {\r\n                    style: {\r\n                      color: \"green\",\r\n                    },\r\n                    on: {\r\n                      click() {\r\n                        that.toOpenModal(row, item);\r\n                      },\r\n                    },\r\n                  },\r\n                  row[column]\r\n                );\r\n                let info2 = h(\"span\", {}, row[column]);\r\n                if (row.orgId) {\r\n                  return h(\"div\", [info]);\r\n                } else {\r\n                  return h(\"div\", [info2]);\r\n                }\r\n              },\r\n            },\r\n            {\r\n              title: \"占比\",\r\n              align: \"center\",\r\n              width: 100,\r\n              key: `${item.key}Percentage`,\r\n            },\r\n          ],\r\n        });\r\n      });\r\n      this.tableSet.columns = this.tableSet.columns.concat(arr);\r\n    },\r\n    //打开弹窗\r\n    toOpenModal(row, item) {\r\n      let params = {\r\n        company: row.orgId,\r\n        isbigfactories: this.queryedParams.isbigfactories,\r\n        status: this.queryedParams.status,\r\n        stationtype: item.stationtype,\r\n      };\r\n      this.$refs[\"list\"].openModal(params);\r\n    },\r\n    //表格-筛选-重置\r\n    _onResetHandle() {\r\n      this.$refs[\"queryform\"].resetFields();\r\n      this.queryParams.company = this.dicts.company[0] && this.dicts.company[0].id;\r\n      this._onSearchHandle(); //搜索列表\r\n    },\r\n    //表格-筛选-搜索\r\n    _onSearchHandle() {\r\n      this.queryedParams = { ...this.queryParams };\r\n      this.$refs.clTable.query(this.queryedParams);\r\n    },\r\n    //查询\r\n    tableQuery(params) {\r\n      Object.assign(params, {\r\n        company: params.company == \"-1\" ? \"\" : params.company,\r\n        pageNumber: params.pageNum,\r\n      });\r\n      delete params.pageNum;\r\n      this.tableSet.loading = true;\r\n      getStationList(params).then((res) => {\r\n        this.tableSet.loading = false;\r\n        let data = res.data.rows;\r\n        this.tableSet.total = res.data.total;\r\n        this.tableList = data;\r\n      });\r\n    },\r\n    //导出\r\n    exportCsv() {\r\n      if (this.tableSet.total == 0) {\r\n        this.$Message.warning(\"暂无数据可导出\");\r\n        return;\r\n      }\r\n      this.exportLoading();\r\n      let params = this.$refs.clTable.insideQueryParams;\r\n      params.pageNumber = 1;\r\n      params.pageSize = this.tableSet.total;\r\n      axios\r\n        .file({\r\n          url: \"/business/cost/extAndTransElec/export\",\r\n          method: \"get\",\r\n          params: params,\r\n        })\r\n        .then((res) => {\r\n          const content = res;\r\n          const blob = new Blob([content]);\r\n\r\n          let fileName = `电表/局站分析报表.xlsx`;\r\n          if (\"download\" in document.createElement(\"a\")) {\r\n            // 非IE下载\r\n            const elink = document.createElement(\"a\");\r\n            elink.download = fileName;\r\n            elink.style.display = \"none\";\r\n            elink.href = URL.createObjectURL(blob);\r\n            document.body.appendChild(elink);\r\n            elink.click();\r\n            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n            document.body.removeChild(elink);\r\n          } else {\r\n            // IE10+下载\r\n            navigator.msSaveBlob(blob, fileName);\r\n          }\r\n          this.$Spin.hide();\r\n        });\r\n    },\r\n    exportLoading() {\r\n      this.$Spin.show({\r\n        render: (h) => {\r\n          return h(\"div\", [\r\n            h(\"Progress\", {\r\n              style: {\r\n                width: \"800px\",\r\n              },\r\n            }),\r\n            h(\"div\", \"导出中，请勿刷新页面......\"),\r\n          ]);\r\n        },\r\n      });\r\n    },\r\n    //下拉选项\r\n    getDicts() {\r\n      this.dicts.status = blist(\"status\"); //状态\r\n      this.dicts.isbigfactories = [\r\n        {\r\n          typeCode: \"1\",\r\n          typeName: \"是\",\r\n        },\r\n        {\r\n          typeCode: \"0\",\r\n          typeName: \"否\",\r\n        },\r\n      ]; //是否大工业用电\r\n\r\n      //数据部门/所属分公司\r\n      getUserByUserRole().then((res) => {\r\n        let { companies, isProAdmin } = res.data;\r\n        if (isProAdmin) {\r\n          companies.unshift({\r\n            name: \"全省\",\r\n            id: \"-1\",\r\n          });\r\n        }\r\n        this.dicts.company = companies;\r\n        this._onResetHandle(); //搜索列表\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.page-class {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n</style>\r\n"]}]}