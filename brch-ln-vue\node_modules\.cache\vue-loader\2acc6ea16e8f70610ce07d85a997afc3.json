{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\totalview\\dataCompare.vue?vue&type=style&index=0&id=65ca69e7&lang=less&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\totalview\\dataCompare.vue", "mtime": 1754285403047}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749178874080}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmNoYXJ0LWJveCB7DQogIG1pbi1oZWlnaHQ6IDMwMHB4Ow0KfQ0KDQoucGFnZS1jbGFzcyB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIHBhZGRpbmc6IDIwcHggMjBweCAwIDIwcHg7DQogIGhlaWdodDogMTAwJTsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgLmZvcm0taGVhZCB7DQogICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgfQ0KICAuYXZ1ZS1jcnVkIHsNCiAgICBmbGV4OiAxOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["dataCompare.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiWA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dataCompare.vue", "sourceRoot": "src/view/carbon/discharge/totalview", "sourcesContent": ["<template>\r\n  <div class=\"page-class common-el\" id=\"modularForm\">\r\n    <el-form\r\n      class=\"form-head\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      :model=\"queryParams\"\r\n      label-width=\"80px\"\r\n    >\r\n      <el-row>\r\n        <el-col :span=\"21\">\r\n          <el-form-item label=\"数据年份\">\r\n            <avue-select\r\n              :clearable=\"false\"\r\n              v-model=\"queryParams.dataYear\"\r\n              placeholder=\"请选择\"\r\n              @change=\"handleYearChange\"\r\n              :dic=\"yearOption\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"数据部门\">\r\n            <avue-select\r\n              :clearable=\"false\"\r\n              v-model=\"queryParams.companyId\"\r\n              placeholder=\"请选择\"\r\n              :dic=\"companyOption\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"数据类型\">\r\n            <avue-select\r\n              :clearable=\"false\"\r\n              v-model=\"queryParams.dataType\"\r\n              placeholder=\"请选择\"\r\n              :dic=\"dictList.discharge_carbon_data_type\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button class=\"searchBtn\" @click=\"handleSearch\">搜索</el-button>\r\n            <el-button class=\"resetBtn\" @click=\"reset\">重置</el-button>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"5\" v-if=\"dictList.discharge_carbon_time_type.length > 0\">\r\n          <el-form-item>\r\n            <el-radio-group v-model=\"queryParams.timeType\" @change=\"handleTimeTypeChange\">\r\n              <el-radio-button\r\n                v-for=\"item in dictList.discharge_carbon_time_type\"\r\n                :key=\"item.value\"\r\n                :label=\"item.value\"\r\n              >\r\n                {{ item.label }}\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"5\" v-if=\"queryParams.timeType == '3'\">\r\n          <el-form-item>\r\n            <div class=\"date_box\">\r\n              <el-date-picker\r\n                v-model=\"queryParams.customTime\"\r\n                type=\"monthrange\"\r\n                style=\"width: 100%\"\r\n                @change=\"handleSearch\"\r\n                :picker-options=\"expireTimeOption\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始月份\"\r\n                end-placeholder=\"结束月份\"\r\n              >\r\n              </el-date-picker>\r\n            </div>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <avue-crud\r\n      ref=\"crud\"\r\n      :key=\"displayKey\"\r\n      :data=\"tableData\"\r\n      :table-loading=\"tableLoading\"\r\n      :option=\"tableOption\"\r\n    >\r\n      <template slot=\"menuLeft\"> </template>\r\n    </avue-crud>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { totalCompare, getCompanyList } from \"@/api/carbon/discharge/total\";\r\n// import {fetchCompanyList} from \"@/api/admin/dept\";\r\nimport compareEchart from \"@/components/echarts/compareEchart.vue\";\r\n// import emptyStrate from \"@/components/empty\";\r\n// import store from '@/store';\r\nimport { energyTypeListByType } from \"@/api/carbon/discharge/energyType\";\r\nexport default {\r\n  //import引入的组件需要注入到对象中才能使用\r\n  components: {\r\n    compareEchart,\r\n    // emptyStrate\r\n  },\r\n  data() {\r\n    return {\r\n      displayKey: 0,\r\n      // echarts所需数组\r\n      dataYoyList: [],\r\n      dataMomList: [],\r\n      dateMonthList: [],\r\n      tableLoading: true,\r\n      tableData: [],\r\n      oilStatus: {},\r\n      queryParams: {\r\n        dataYear: new Date().getFullYear(),\r\n        dataType: \"1\",\r\n        timeType: \"1\",\r\n        customTime: [],\r\n        companyId: \"0\",\r\n      },\r\n      tableOption: {\r\n        border: false,\r\n        index: false,\r\n        height: \"auto\",\r\n        calcHeight: 30,\r\n        stripe: true,\r\n        menuAlign: \"center\",\r\n        headerAlign: \"center\",\r\n        align: \"center\",\r\n        refreshBtn: false,\r\n        showClomnuBtn: false,\r\n        searchMenuSpan: 4,\r\n        searchSize: \"mini\",\r\n        card: true,\r\n        addBtn: false,\r\n        editBtn: false,\r\n        delBtn: false,\r\n        columnBtn: false,\r\n        searchBtn: false,\r\n        emptyBtn: false,\r\n        gridBtn: false,\r\n        menu: false,\r\n        dialogWidth: 500,\r\n        dialogMenuPosition: \"center\",\r\n        dialogCustomClass: \"singleRowDialog\",\r\n        labelWidth: 100,\r\n        column: [\r\n          {\r\n            label: \"数据部门\",\r\n            prop: \"companyId\",\r\n            formatter: (val, value, label) => {\r\n              return this.getCompanyNameById(value);\r\n            },\r\n            overHidden: true,\r\n          },\r\n          {\r\n            label: \"时间周期\",\r\n            prop: \"dataMonth\",\r\n            overHidden: true,\r\n          },\r\n          {\r\n            label: \"年份\",\r\n            prop: \"dataYear\",\r\n            overHidden: true,\r\n          },\r\n          {\r\n            label: \"碳排放总量（单位：tCO₂）\",\r\n            display: false,\r\n            children: [\r\n              {\r\n                label: \"碳排总量（tCO₂）\",\r\n                prop: \"dataValue\",\r\n                overHidden: true,\r\n                display: false,\r\n              },\r\n              {\r\n                label: \"同比\",\r\n                prop: \"dataYoY\",\r\n                overHidden: true,\r\n                display: false,\r\n              },\r\n              {\r\n                label: \"环比\",\r\n                prop: \"dataMoM\",\r\n                overHidden: true,\r\n                display: false,\r\n              },\r\n            ],\r\n          },\r\n        ],\r\n      },\r\n      yearOption: [],\r\n      companyOption: [],\r\n      dictList: {\r\n        //数据类型\r\n        discharge_carbon_data_type: [],\r\n        //时间类型\r\n        discharge_carbon_time_type: [],\r\n      },\r\n      //自定义时间限制\r\n      expireTimeOption: {\r\n        dataYear: new Date().getFullYear(),\r\n        // disabledDate:(date) => {\r\n        //   return (\r\n        //     date.getTime() < new Date(this.dataYear,0,1).getTime() ||\r\n        //     date.getTime() > new Date(this.dataYear,11,31).getTime()\r\n        //   );\r\n        // }\r\n        disabledDate(time) {\r\n          let year = new Date().getFullYear();\r\n          let month = new Date().getMonth(); // 上月\r\n          let days = new Date(year, month, 0).getDate(); // 上月总天数\r\n          return time.getTime() > Date.now() - 24 * 60 * 60 * 1000 * `${days}`;\r\n        },\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getCompanyList();\r\n    this.initDict(this.dictList);\r\n    this.getOilTypeStatus();\r\n    for (let i = 0; i < 10; i++) {\r\n      this.yearOption.push({\r\n        value: this.queryParams.dataYear - i,\r\n        label: this.queryParams.dataYear - i,\r\n      });\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getList();\r\n  },\r\n  watch: {\r\n    \"dictList.discharge_carbon_data_type\": {\r\n      immediate: true,\r\n      handler(newVal) {\r\n        if (newVal == undefined || newVal == null || newVal == \"\") return;\r\n        this.dealOilStatus();\r\n      },\r\n      deep: true, // 深度监听\r\n    },\r\n  },\r\n  methods: {\r\n    reset() {\r\n      this.queryParams = {\r\n        dataYear: new Date().getFullYear(),\r\n        dataType: \"1\",\r\n        timeType: \"1\",\r\n        customTime: [],\r\n        companyId: \"0\",\r\n      };\r\n      this.handleSearch();\r\n    },\r\n    handleSearch() {\r\n      // store.commit(\"SET_EMPTY\", false);\r\n      this.getList();\r\n    },\r\n    // 查询列表\r\n    getList() {\r\n      this.tableLoading = true;\r\n      this.dataYoyList = [];\r\n      this.dateMonthList = [];\r\n      this.dataMomList = [];\r\n      totalCompare(this.queryParams).then((res) => {\r\n        // if (res.data.code == 200) {\r\n        this.tableData = res.data;\r\n        this.tableData.forEach((node) => {\r\n          this.dataYoyList.push(node.dataYoY.replace(\"%\", \"\"));\r\n          this.dataMomList.push(node.dataMoM.replace(\"%\", \"\"));\r\n          this.dateMonthList.push(node.dataMonth);\r\n        });\r\n        // console.log(\"echarts data\", this.dataYoyList, this.dataMomList, this.dateMonthList)\r\n        if (this.queryParams.dataType == \"2\") {\r\n          this.tableOption.column[3].label = \"标准煤使用量（单位：tce）\";\r\n          this.tableOption.column[3].children[0].label = \"标准煤使用量（tce）\";\r\n        } else {\r\n          let unitLabel = this.getEnergyLabel();\r\n          this.tableOption.column[3].label = unitLabel + \"碳排放总量（单位：tCO₂）\";\r\n          this.tableOption.column[3].children[0].label = unitLabel + \"碳排总量（tCO₂）\";\r\n        }\r\n        this.tableLoading = false;\r\n        this.$nextTick(() => {\r\n          this.displayKey++;\r\n        });\r\n        // }\r\n      });\r\n    },\r\n    handleTimeTypeChange() {\r\n      if (this.queryParams.timeType < \"3\") {\r\n        this.handleSearch();\r\n      } else {\r\n        this.queryParams.customTime = [];\r\n      }\r\n    },\r\n    dealOilStatus() {\r\n      if (this.oilStatus != {} && this.dictList.discharge_carbon_data_type.length > 0) {\r\n        let i = 0;\r\n        while (i < this.dictList.discharge_carbon_data_type.length) {\r\n          if (this.oilStatus[this.dictList.discharge_carbon_data_type[i].label] == \"2\") {\r\n            this.dictList.discharge_carbon_data_type.splice(i, 1);\r\n          } else {\r\n            i += 1;\r\n          }\r\n        }\r\n      }\r\n    },\r\n    getOilTypeStatus() {\r\n      energyTypeListByType({ type: \"4\" }).then((res) => {\r\n        if (res.data.code == 200) {\r\n          res.data.data.forEach((item) => {\r\n            this.oilStatus[item.secondName] = item.status;\r\n          });\r\n          this.dealOilStatus();\r\n        }\r\n      });\r\n    },\r\n    getCompanyList() {\r\n      getCompanyList().then((res) => {\r\n        if (res.data) {\r\n          res.data.forEach((item) => {\r\n            this.companyOption.push({ value: item.id, label: item.orgName });\r\n          });\r\n          this.companyOption.unshift({ value: \"0\", label: \"全省\" });\r\n        }\r\n      });\r\n    },\r\n    getCompanyNameById(id) {\r\n      for (let i = 0; i < this.companyOption.length; i++) {\r\n        if (id == this.companyOption[i].value) {\r\n          return this.companyOption[i].label;\r\n        }\r\n      }\r\n      return id;\r\n    },\r\n    handleYearChange(event) {\r\n      this.expireTimeOption.dataYear = event.value;\r\n      this.queryParams.customTime = [];\r\n    },\r\n    getEnergyLabel() {\r\n      let ret = \"\";\r\n      if (this.queryParams.dataType == \"1\") {\r\n        ret = \"\";\r\n      } else {\r\n        for (let i = 0; i < this.dictList.discharge_carbon_data_type.length; i++) {\r\n          if (\r\n            this.dictList.discharge_carbon_data_type[i].value == this.queryParams.dataType\r\n          ) {\r\n            ret = this.dictList.discharge_carbon_data_type[i].label + \"-\";\r\n            return ret;\r\n          }\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.chart-box {\r\n  min-height: 300px;\r\n}\r\n\r\n.page-class {\r\n  background: #fff;\r\n  padding: 20px 20px 0 20px;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .form-head {\r\n    margin-bottom: 10px;\r\n  }\r\n  .avue-crud {\r\n    flex: 1;\r\n  }\r\n}\r\n</style>\r\n"]}]}