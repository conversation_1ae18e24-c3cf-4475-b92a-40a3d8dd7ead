{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addCoalAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addCoalAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQovLyBpbXBvcnQgew0KLy8gICBhZGRBbW1ldGVyLA0KLy8gICBsaXN0RWxlY3RyaWNUeXBlLA0KLy8gICBlZGl0QW1tZXRlciwNCi8vICAgZWRpdEFtbWV0ZXJSZWNvcmQsDQovLyAgIHVwZGF0ZUFtbWV0ZXIsDQovLyAgIGNoZWNrUHJvamVjdE5hbWVFeGlzdCwNCi8vICAgY2hlY2tBbW1ldGVyQnlTdGF0aW9uLA0KLy8gICBnZXRDbGFzc2lmaWNhdGlvbiwNCi8vICAgZ2V0Q2xhc3NpZmljYXRpb25JZCwNCi8vICAgZ2V0VXNlcmRhdGEsDQovLyAgIGNoZWNrQ2xhc3NpZmljYXRpb25MZXZlbCwNCi8vICAgbGlzdEVsZWN0cmljVHlwZVJhdGlvLA0KLy8gICBjaGVja0FtbWV0ZXJFeGlzdCwNCi8vICAgZ2V0VXNlckJ5VXNlclJvbGUsDQovLyAgIGdldENvdW50cnlCeVVzZXJJZCwNCi8vICAgZ2V0Q291bnRyeXNkYXRhLCByZW1vdmVBdHRhY2gsIGF0dGNoTGlzdCwgZ2V0QmFua0NhcmQNCi8vIH0gZnJvbSAnQC9hcGkvYmFzZWRhdGEvYW1tZXRlci5qcycNCmltcG9ydCB7DQogICAgX3ZlcmlmeV9TdGFydERhdGUsDQogICAganVkZ2VOdW1iZXIsDQogICAgX3ZlcmlmeV9FbmREYXRlLA0KICAgIF92ZXJpZnlfUHJldlRvdGFsUmVhZGluZ3MsDQogICAgX3ZlcmlmeV9DdXJUb3RhbFJlYWRpbmdzLA0KICAgIG90aGVyX25vX2FtbWV0ZXJvcl9wcm90b2NvbCwNCiAgICBzZWxmX25vX2FtbWV0ZXJvcl9wcm90b2NvbCwNCiAgICBIRkxfYW1tZXRlcm9yLA0KICAgIGp1ZGdpbmdfZWRpdGFiaWxpdHksDQogICAganVkZ2luZ19lZGl0YWJpbGl0eTEsDQogICAgX3ZlcmlmeV9Nb25leSwNCiAgICBfY2FsY3VsYXRlVXNlZFJlYWRpbmdzLA0KICAgIF9jYWxjdWxhdGVUb3RhbFJlYWRpbmdzLA0KICAgIF9jYWxjdWxhdGVVbml0UHJpY2VCeVVzZWRNb25leSwNCiAgICBfY2FsY3VsYXRlQWNjb3VudE1vbmV5LA0KICAgIF9jYWxjdWxhdGVRdW90ZXJlYWRpbmdzcmF0aW8sDQogICAgcmVxdWlyZWRGaWVsZFZhbGlkYXRvciwNCiAgICBjb3VudFRheGFtb3VudDEsDQogICAgY291bnRUYXhhbW91bnQsDQogICAgY2FsY3VsYXRlQWN0dWFsTW9uZXksDQogICAganVkZ2VfbmVnYXRlLA0KICAgIGp1ZGdlX3JlY292ZXJ5LA0KICAgIGp1ZGdlX3liLA0KICAgIHVuaXRwaXJjZU1pbiwNCiAgICB1bml0cGlyY2VNYXgsDQogICAgdW5pdHBpcmNlTWF4MQ0KfSBmcm9tICdAL3ZpZXcvYWNjb3VudC9Qb3dlckFjY291bnRDb250cm9sbGVyJzsNCiAgICBpbXBvcnQgew0KICAgICAgICBzYXZlQ29hbEFjY291bnQsDQogICAgICAgIHJlbW92ZUNvYWxBY2NvdW50LA0KICAgICAgICBzZWxlY3RDb2FsSWRzDQogICAgfSBmcm9tICdAL2FwaS9jb2FsSGVhdE9pbEFjY291bnQnOw0KICAgIGltcG9ydCB7dmVyaWZpY2F0aW9ufSBmcm9tICdAL3ZpZXcvYWNjb3VudC9jb2FsQWNjb3VudCc7DQogICAgaW1wb3J0IGNoZWNrUmVzdWx0QW5kUmVzcG9uc2UgZnJvbSAiQC92aWV3L2FjY291bnQvY2hlY2svY2hlY2tSZXN1bHRBbmRSZXNwb25zZSI7DQogICAgaW1wb3J0IGNoZWNrUmVzdWx0IGZyb20gIkAvdmlldy9hY2NvdW50L2NoZWNrL2NoZWNrUmVzdWx0IjsNCiAgICBpbXBvcnQgYWxhcm1DaGVjayBmcm9tICJAL3ZpZXcvYWNjb3VudC9jaGVjay9hbGFybUNoZWNrIjsNCiAgICBpbXBvcnQge2dldERhdGVzLHRlc3ROdW1iZXIsfSBmcm9tICdAL3ZpZXcvYWNjb3VudC9wb3dlckFjY291bnRIZWxwZXInOw0KICAgIGltcG9ydCBheGlvcyBmcm9tICdAL2xpYnMvYXBpLnJlcXVlc3QnOw0KICAgIGltcG9ydCBTZWxlY3RBbW1ldGVyIGZyb20gIi4vc2VsZWN0QW1tZXRlciI7DQogICAgaW1wb3J0IHtfdmVyaWZ5X0ZlZVN0YXJ0RGF0ZX0gZnJvbSAnQC92aWV3L2FjY291bnQvUG93ZXJBY2NvdW50RXMnOw0KICAgIGltcG9ydCBBZGRCaWxsUGVyIGZyb20gIi4vYWRkQ29hbEJpbGxQcmVNb2RhbCI7DQogICAgaW1wb3J0IHtyZUpvaW5CaWxscHJlfSBmcm9tICdAL2FwaS9hY2NvdW50QmlsbFBlcic7DQogICAgaW1wb3J0IHtibGlzdDF9IGZyb20gIkAvbGlicy90b29scyI7DQogICAgaW1wb3J0IHt3aWR0aHN0eWxlfSBmcm9tICJAL3ZpZXcvYnVzaW5lc3MvbXNzQWNjb3VudGJpbGwvbXNzQWNjb3VudGJpbGxkYXRhIjsNCiAgICBpbXBvcnQgQ29tcGxldGVkUHJlTW9kYWwgZnJvbSAiLi9jb21wbGV0ZWRQcmVNb2RhbCI7DQogICAgaW1wb3J0IGluZGV4RGF0YSBmcm9tICdAL2NvbmZpZy9pbmRleCcNCiAgICBpbXBvcnQgQ291bnRyeU1vZGFsIGZyb20gIkAvdmlldy9iYXNlZGF0YS9hbW1ldGVyL2NvdW50cnlNb2RhbCI7DQogICAgaW1wb3J0IHsgZ2V0VXNlcmRhdGEsIGdldFVzZXJCeVVzZXJSb2xlLCBnZXRDb3VudHJ5c2RhdGEsIGdldENvdW50cnlCeVVzZXJJZCwgZWRpdEFtbWV0ZXINCg0KICAgIH0gZnJvbSAnQC9hcGkvYmFzZWRhdGEvYW1tZXRlci5qcycNCiAgICBpbXBvcnQgVXBsb2FkRmlsZU1vZGFsIGZyb20gIkAvdmlldy9hY2NvdW50L3VwbG9hZEZpbGVNb2RhbCI7DQogICAgbGV0IGRhdGVzPWdldERhdGVzKCk7DQogICAgZXhwb3J0IGRlZmF1bHQgew0KICAgICAgICBuYW1lOiAnYWRkQ29hbEFjY291bnQnLA0KICAgICAgICBjb21wb25lbnRzOiB7VXBsb2FkRmlsZU1vZGFsLCBhbGFybUNoZWNrLCBjaGVja1Jlc3VsdCwgY2hlY2tSZXN1bHRBbmRSZXNwb25zZSxDb21wbGV0ZWRQcmVNb2RhbCwgU2VsZWN0QW1tZXRlcixBZGRCaWxsUGVyLENvdW50cnlNb2RhbH0sDQogICAgICAgIGRhdGEoKSB7DQogICAgICAgICAgICBsZXQgcGhvdG8gPSAoaCwge3JvdywgaW5kZXh9KSA9PiB7DQogICAgICAgICAgICAgICAgbGV0IHRoYXQgPSB0aGlzDQogICAgICAgICAgICAgICAgbGV0IHN0ciA9ICfkuIrkvKDpmYTku7YnDQogICAgICAgICAgICAgICAgcmV0dXJuIGgoImRpdiIsIFtoKCJ1Iiwgew0KICAgICAgICAgICAgICAgICAgICBvbjogew0KICAgICAgICAgICAgICAgICAgICAgICAgY2xpY2soKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLy/miZPlvIDlvLnlh7rmoYYNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBpZiAocm93LmlkKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQudXBsb2FkRmlsZShyb3cpDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gfQ0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfSwgc3RyKV0pOw0KICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgaWQyOiAiIiwNCiAgICAgICAgICAgICAgICBmaWxlUGFyYW06ew0KICAgICAgICAgICAgICAgICAgICBidXNpSWQ6IiIsDQogICAgICAgICAgICAgICAgICAgIGJ1c2lBbGlhczoi6ZmE5Lu2KOWNj+iurueuoeeQhikiLA0KICAgICAgICAgICAgICAgICAgICBjYXRlZ29yeUNvZGU6ImZpbGUiLA0KICAgICAgICAgICAgICAgICAgICBhcmVhQ29kZToibG4iDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBzdWJtaXQ6W10sDQogICAgICAgICAgICAgICAgc3VibWl0MjpbXSwNCiAgICAgICAgICAgICAgICBmb3JtSXRlbVdpZHRoOiB3aWR0aHN0eWxlLA0KICAgICAgICAgICAgICAgIHZlcnNpb246JycsDQogICAgICAgICAgICAgICAgZGF0ZUxpc3Q6ZGF0ZXMsDQogICAgICAgICAgICAgICAgZmlsdGVyQ29sbDogdHJ1ZSwvL+aQnOe0oumdouadv+WxleW8gA0KICAgICAgICAgICAgICAgIGVkaXRJbmRleDogLTEsLy/lvZPliY3nvJbovpHooYwNCiAgICAgICAgICAgICAgICBjb2x1bW5zSW5kZXg6LTEsLy/lvZPliY3nvJbovpHliJcNCiAgICAgICAgICAgICAgICBteVN0eWxlOltdLC8v5qC35byPDQogICAgICAgICAgICAgICAgZWRpdENvYWxVc2VCb2R5OicnLA0KICAgICAgICAgICAgICAgIGVkaXRGZWVTdGFydERhdGU6JycsDQogICAgICAgICAgICAgICAgZWRpdENvYWxBbW91bnQ6JycsDQogICAgICAgICAgICAgICAgZWRpdFRpY2tldE1vbmV5OicnLA0KICAgICAgICAgICAgICAgIGVkaXRUYXhUaWNrZXRNb25leTonJywNCiAgICAgICAgICAgICAgICBlZGl0VGF4UmF0ZTonJywNCiAgICAgICAgICAgICAgICBlZGl0T3RoZXJGZWU6JycsDQogICAgICAgICAgICAgICAgZWRpdENvYWxVc2U6JycsDQogICAgICAgICAgICAgICAgZWRpdENvYWxUeXBlOicnLA0KICAgICAgICAgICAgICAgIHNwaW5TaG93OmZhbHNlLC8v6YGu572pDQogICAgICAgICAgICAgICAgZWRpdHJlbWFyazonJywNCiAgICAgICAgICAgICAgICBjb21wYW5pZXM6W10sDQogICAgICAgICAgICAgICAgY29hbFR5cGVzOiBbXSwNCiAgICAgICAgICAgICAgICBjb2FsVXNlVHlwZXM6IFtdLA0KICAgICAgICAgICAgICAgIGRlcGFydG1lbnRzOltdLA0KICAgICAgICAgICAgICAgIGlzQWRtaW46ZmFsc2UsDQogICAgICAgICAgICAgICAgY29tcGFueTpudWxsLC8v55So5oi36buY6K6k5YWs5Y+4DQogICAgICAgICAgICAgICAgY291bnRyeTpudWxsLC8v55So5oi36buY6K6k5omA5bGe6YOo6ZeoDQogICAgICAgICAgICAgICAgY291bnRyeU5hbWU6bnVsbCwvL+eUqOaIt+m7mOiupOaJgOWxnumDqOmXqA0KICAgICAgICAgICAgICAgIGFjY291bnRPYmo6ew0KICAgICAgICAgICAgICAgICAgICBhY2NvdW50bm86ZGF0ZXNbMV0uY29kZSwvL+acn+WPtyzpu5jorqTlvZPliY3mnIgNCiAgICAgICAgICAgICAgICAgICAgY29tcGFueToiIiwvL+WIhuWFrOWPuA0KICAgICAgICAgICAgICAgICAgICBjb3VudHJ5OiIiLC8v5omA5bGe6YOo6ZeoDQogICAgICAgICAgICAgICAgICAgIGNvYWxVc2VCb2R5OiAiIiwvL+eUqOeFpOS4u+S9kw0KICAgICAgICAgICAgICAgICAgICBjb2FsVXNlVHlwZToxLA0KICAgICAgICAgICAgICAgICAgICBjb2FsVHlwZToxLA0KICAgICAgICAgICAgICAgICAgICBmZWVTdGFydERhdGU6ICIiLA0KICAgICAgICAgICAgICAgICAgICBjb3VudHJ5TmFtZTogIiIsDQoNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIHRiQWNjb3VudDogew0KICAgICAgICAgICAgICAgICAgICBkaXNwS2V5OiAwLA0KICAgICAgICAgICAgICAgICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICBjb2x1bW5zOiBbXSwNCiAgICAgICAgICAgICAgICAgICAgdGFpbENvbHVtbjogWw0KICAgICAgICAgICAgICAgICAgICAgICAge3R5cGU6ICdzZWxlY3Rpb24nLCB3aWR0aDogNjAsIGFsaWduOiAnY2VudGVyJyx9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5pyf5Y+3IiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJhY2NvdW50Tm8iLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogOTAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi55So6IO95Li75L2TIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAiY29hbFVzZUJvZHkiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGtleTogImNvYWxVc2VCb2R5IiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEwMCwNCiAgICAgICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLotLnnlKjlj5HnlJ/ml6UiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNsb3Q6ICJmZWVTdGFydERhdGUiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGtleTogImZlZVN0YXJ0RGF0ZSIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi54Wk54Kt55So6YePKHQpIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAiY29hbEFtb3VudCIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5Y2V5Lu3KOWFgy/lkKgpIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJ1bml0UHJpY2UiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwLA0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIuaZruelqOWQq+eojumHkeminSjlhYMpIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAidGlja2V0TW9uZXkiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwLA0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIuS4k+elqOWQq+eojumHkeminSjlhYMpIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAidGF4VGlja2V0TW9uZXkiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwLA0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIuS4k+elqOeojueOh++8iCXvvIkiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNsb3Q6ICJ0YXhSYXRlU2hvdyIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5LiT56Wo56iO6aKdIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJ0YXhBbW91bnQiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogODAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5YW25LuWKOWFgykiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGtleTogIm90aGVyRmVlIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAib3RoZXJGZWUiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogODAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5a6e57y06LS555SoKOWFgynlkKvnqI4iLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleTogInBhaWRNb25leSIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi57G75Z6LIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAiY29hbEltcG9ydFR5cGUiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogNjAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi55So6YCUIiwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAiY29hbEltcG9ydFVzZSIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAsDQogICAgICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgICAgICAgLy8ge3RpdGxlOiAi6ZmE5Lu2Iiwgc2xvdDogJ2ZpbGUnLCBhbGlnbjogImNlbnRlciIsIHJlbmRlcjogcGhvdG8sIHdpZHRoOiAxMDB9LA0KICAgICAgICAgICAgICAgICAgICAgICAge3RpdGxlOiAi5aSH5rOoIiwgc2xvdDogInJlbWFyayIsYWxpZ246ICJjZW50ZXIiLCB3aWR0aDogMTUwfSx7DQogICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogJ+mZhOS7ticsDQogICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAnZmlsZScsDQogICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsDQogICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogNjAsDQogICAgICAgICAgICAgICAgICAgICAgICAvLyBmaXhlZDogJ3JpZ2h0JywNCiAgICAgICAgICAgICAgICAgICAgICAgIHJlbmRlcjogcGhvdG8NCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgICAgICBmaWxlQ29sdW1uOiBbew0KICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfpmYTku7YnLA0KICAgICAgICAgICAgICAgICAgICAgICAgc2xvdDogJ2ZpbGUnLA0KICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLA0KICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDYwLA0KICAgICAgICAgICAgICAgICAgICAgICAgLy8gZml4ZWQ6ICdyaWdodCcsDQogICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI6IHBob3RvDQogICAgICAgICAgICAgICAgICAgIH0sXSwNCiAgICAgICAgICAgICAgICAgICAgZGF0YTogW10NCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIHBhZ2VUb3RhbDogMCwNCiAgICAgICAgICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAgICAgICAgIHBhZ2VTaXplOiAxMCwvL+W9k+WJjemhtQ0KICAgICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBtZXRob2RzOiB7DQogICAgICAgICAgICB1cGxvYWRGaWxlKHJvdykgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKHJvdywgInJvdyIpOw0KICAgICAgICAgICAgICAgIC8vIGxldCBpZDsNCiAgICAgICAgICAgICAgICAvLyBpZighcm93LmlkMikgew0KICAgICAgICAgICAgICAgIC8vICAgICBlZGl0QW1tZXRlcignJywgMCkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgICAgIC8vICAgICAgICAgY29uc29sZS5sb2cocmVzLCAicmVzIik7DQogICAgICAgICAgICAgICAgLy8gICAgICAgICByb3cuaWQyID0gcmVzLmRhdGEuaWQ7DQoNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIHRoaXMuaWQyID0gcmVzLmRhdGEuaWQNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIC8vIHRoaXMuZmlsZVBhcmFtLmJ1c2lJZCA9IDsNCiAgICAgICAgICAgICAgICAvLyAgICAgICAgIHRoaXMuJHJlZnMudXBsb2FkRmlsZU1vZGFsLmNob29zZShyb3cuaWQyICsgJycpOw0KICAgICAgICAgICAgICAgIC8vICAgICB9KQ0KICAgICAgICAgICAgICAgIC8vIH1lbHNlIHsNCg0KICAgICAgICAgICAgICAgIGlmKHJvdy5pZCkgew0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLnVwbG9hZEZpbGVNb2RhbC5jaG9vc2Uocm93LmlkICsgJycpOw0KICAgICAgICAgICAgICAgIH1lbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoIuivt+WFiOS/neWtmOWQjuWGjeS4iuS8oOaWh+S7tu+8gSIpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAvLyB9DQogICAgICAgICAgICAgICAgLy8gY29uc29sZS5sb2cocm93LCAicm93Iik7DQogICAgICAgICAgICB9LA0KICAgICAgICAgIGNoYW5nZSgpIHsNCiAgICAgICAgICAgIC8vIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgfSwNCiAgICAgICAgICAgIHNlbGVjdENoYW5nZSgpew0KICAgICAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgICAgICAgICAgICBpZiAodGhhdC5hY2NvdW50T2JqLmNvbXBhbnkgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgICAgIGlmKHRoYXQuYWNjb3VudE9iai5jb21wYW55ID09ICItMSIpew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnkgPSAtMTsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb3VudHJ5TmFtZSA9IG51bGw7DQogICAgICAgICAgICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgICAgICAgICAgICAgZ2V0Q291bnRyeUJ5VXNlcklkKHRoYXQuYWNjb3VudE9iai5jb21wYW55KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYocmVzLmRhdGEuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeSA9IHJlcy5kYXRhLmRlcGFydG1lbnRzWzBdLmlkOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeU5hbWUgPSByZXMuZGF0YS5kZXBhcnRtZW50c1swXS5uYW1lOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8v6YCJ5oup5omA5bGe6YOo6Zeo5byA5aeLDQogICAgICAgICAgICBjaG9vc2VSZXNwb25zZUNlbnRlcigpIHsNCiAgICAgICAgICAgICAgICBpZih0aGlzLmFjY291bnRPYmouY29tcGFueSA9PSBudWxsIHx8IHRoaXMuYWNjb3VudE9iai5jb21wYW55ID09ICItMSIgKXsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLor7flhYjpgInmi6nliIblhazlj7giKTtyZXR1cm47DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuY291bnRyeU1vZGFsLmNob29zZSh0aGlzLmFjY291bnRPYmouY29tcGFueSk7Ly/miYDlsZ7pg6jpl6gNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBnZXREYXRhRnJvbU1vZGFsKGRhdGEpIHsNCiAgICAgICAgICAgICAgICB0aGlzLmFjY291bnRPYmouY291bnRyeSA9IGRhdGEuaWQ7DQogICAgICAgICAgICAgICAgdGhpcy5hY2NvdW50T2JqLmNvdW50cnlOYW1lID0gZGF0YS5uYW1lOw0KICAgICAgICAgICAgICAgIC8v6YCJ5oup5omA5bGe6YOo6Zeo57uT5p2fDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZ2V0VXNlckRhdGEoKXsNCiAgICAgICAgICAgICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICAgICAgICAgICAgZ2V0VXNlcmRhdGEoKS50aGVuKHJlcyA9PiB7Ly/lvZPliY3nmbvlvZXnlKjmiLfmiYDlnKjlhazlj7jlkozmiYDlsZ7pg6jpl6gNCiAgICAgICAgICAgICAgICAgICAgaWYocmVzLmRhdGEuY29tcGFuaWVzLmxlbmd0aCAhPSAwKXsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBjb21wYW5pZXMgPSByZXMuZGF0YS5jb21wYW5pZXM7DQogICAgICAgICAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5jb21wYW5pZXNbMF0uaWQgPT0gIjI2MDAwMDAwMDAiKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb21wYW5pZXMgPSB0aGF0LmNvbXBhbmllczsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuY29tcGFueSA9IGNvbXBhbmllc1swXS5pZDsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuYWNjb3VudE9iai5jb21wYW55ID0gY29tcGFuaWVzWzBdLmlkOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIGlmKHJlcy5kYXRhLmRlcGFydG1lbnRzLmxlbmd0aCAhPSAwKXsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBkZXBhcnRtZW50cyA9IHJlcy5kYXRhLmRlcGFydG1lbnRzOw0KICAgICAgICAgICAgICAgICAgICAgICAgaWYocmVzLmRhdGEuY29tcGFuaWVzWzBdLmlkID09ICIyNjAwMDAwMDAwIiAmJiB0aGF0LmRlcGFydG1lbnRzLmxlbmd0aCAhPSAwKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZXBhcnRtZW50cyA9IHRoYXQuZGVwYXJ0bWVudHMNCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuY291bnRyeSA9IGRlcGFydG1lbnRzWzBdLmlkOw0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5jb3VudHJ5TmFtZSA9IGRlcGFydG1lbnRzWzBdLm5hbWU7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeSA9IE51bWJlcihkZXBhcnRtZW50c1swXS5pZCk7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeU5hbWUgPSBkZXBhcnRtZW50c1swXS5uYW1lOw0KICAgICAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5wYWdlTnVtID0gMQ0KICAgICAgICAgICAgICAgICAgICB0aGF0LmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHNlYXJjaExpc3QoKXsNCiAgICAgICAgICAgICAgICBpZih0aGlzLmFjY291bnRPYmouY291bnRyeU5hbWUgPT0gIiIpew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmFjY291bnRPYmouY291bnRyeSA9ICItMSI7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIHRoaXMucGFnZU51bSA9IDENCiAgICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYWNjb3VudG5vQ2hhbmdlKCl7DQogICAgICAgICAgICAgICAgdGhpcy5zZWFyY2hMaXN0KCkNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvL+eCueWHu+S/neWtmA0KICAgICAgICAgICAgcHJlc2VydmUoKSB7DQogICAgICAgICAgICAgICAgbGV0IGRhdGFMID0gdGhpcy4kcmVmcy5hY2NvdW50RXNUYWJsZS5nZXRTZWxlY3Rpb24oKTsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygiZGF0YUwiLCBkYXRhTCk7DQogICAgICAgICAgICAgICAgbGV0IGIgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICBsZXQgYXJyYXkgPSBbXTsNCiAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGRhdGFMLmxlbmd0aDsgaSArKykgew0KICAgICAgICAgICAgICAgICAgICBiID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgYXJyYXkucHVzaChkYXRhTFtpXSkNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgaWYoYil7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuc3VibWl0RGF0YShhcnJheSk7DQogICAgICAgICAgICAgICAgfWVsc2Ugew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygn5rKh5pyJ5Y+v5L+d5a2Y5pWw5o2uJykNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgc3VibWl0Q2hhbmdlKGluZGV4TGlzdCl7DQogICAgICAgICAgICAgICAgbGV0IGRhdGE9W107DQogICAgICAgICAgICAgICAgdGhpcy5zdWJtaXQyLm1hcCgoaXRlbSxpbmRleCk9PnsNCiAgICAgICAgICAgICAgICAgICAgaW5kZXhMaXN0Lm1hcCgoaXRlbTIpPT57DQogICAgICAgICAgICAgICAgICAgICAgICBpZihpbmRleD09aXRlbTIpew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEucHVzaChpdGVtKQ0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIHRoaXMuc3VibWl0PWRhdGENCiAgICAgICAgICAgIH0sDQoNCiAgICAgICAgICAgIC8v5o+Q5Lqk5pWw5o2uDQogICAgICAgICAgICBzdWJtaXREYXRhKGRhdGEpew0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGRhdGEsICJkYXRhIik7DQogICAgICAgICAgICAgICAgbGV0IGEgPSBbXTsNCiAgICAgICAgICAgICAgICBsZXQgdGhhdD10aGlzOw0KICAgICAgICAgICAgICAgIGlmKGRhdGEgIT0gbnVsbCAmJiBkYXRhLmxlbmd0aCA+IDApew0KICAgICAgICAgICAgICAgICAgICBsZXQgbnVtYmVyID0gMDsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHN1Ym1pdERhdGEgPSBbXTsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHN0ciA9ICcnOw0KICAgICAgICAgICAgICAgICAgICBsZXQgYWNjb3VudG5vID0gdGhpcy5hY2NvdW50T2JqLmFjY291bnRubzsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAvLyDmoKHpqozmlbDmja4NCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBvYmogPSB2ZXJpZmljYXRpb24oaXRlbSk7DQogICAgICAgICAgICAgICAgICAgICAgICBpZiAob2JqLnJlc3VsdCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmKGl0ZW0uaWQgPT0gbnVsbCl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0uYWNjb3VudG5vID0gYWNjb3VudG5vDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGEucHVzaChpdGVtLmlkKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdWJtaXREYXRhLnB1c2goaXRlbSk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgbnVtYmVyICsrOw0KICAgICAgICAgICAgICAgICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHIgKz0gJ+WPsOi0puWPt+S4uuOAkCcgK2l0ZW0uaWQgKyAn44CR55qE5Y+w6LSm6aqM6K+B5rKh5pyJ6YCa6L+H77ya44CQJyArIG9iai5zdHIgKyAn44CR77ybJzsNCiAgICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICB0aGF0Lmlkcz1hOw0KICAgICAgICAgICAgICAgICAgICBpZihzdHIubGVuZ3RoID4gMCl7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcyhzdHIpDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgaWYoc3VibWl0RGF0YS5sZW5ndGggPiAwKXsNCiAgICAgICAgICAgICAgICAgICAgICAgIHNhdmVDb2FsQWNjb3VudChzdWJtaXREYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLmNvZGUgPT0gMCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGVudDogJ+aPkOekuu+8muaIkOWKn+S/neWtmCAnICsgcmVzLmRhdGEubnVtICsgJyDmnaHmlbDmja4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHVyYXRpb246IDEwLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xvc2FibGU6IHRydWUNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYWRkTmV3Q29hbEFjY291bnQoKSB7DQogICAgICAgICAgICAgICAgY29uc29sZS5sb2codGhpcy5hY2NvdW50T2JqLmFjY291bnRubywgInRoaXMuYWNjb3VudE9iai5hY2NvdW50bm8iKTsNCiAgICAgICAgICAgICAgICBjb25zdCBjdXJyZW50RGF0ZSA9IG5ldyBEYXRlKCk7DQogICAgICAgICAgICAgICAgY29uc3QgY3VycmVudFllYXIgPSBjdXJyZW50RGF0ZS5nZXRGdWxsWWVhcigpOw0KICAgICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRNb250aCA9IGN1cnJlbnREYXRlLmdldE1vbnRoKCkgKyAxOw0KICAgICAgICAgICAgICAgIGlmIChudWxsID09IHRoaXMudGJBY2NvdW50LmRhdGEpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy50YkFjY291bnQuZGF0YSA9IFtdOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB0aGlzLnRiQWNjb3VudC5kaXNwS2V5Kys7DQogICAgICAgICAgICAgICAgdGhpcy50YkFjY291bnQuZGF0YS51bnNoaWZ0KHsNCiAgICAgICAgICAgICAgICAgICAgLy8gYWNjb3VudE5vOmRhdGVzWzFdLmNvZGUsDQogICAgICAgICAgICAgICAgICAgIGFjY291bnRObzogKHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm8gPT0gLTEgfHwgdGhpcy5hY2NvdW50T2JqLmFjY291bnRubyA9PSB1bmRlZmluZWQpID8gY3VycmVudFllYXIrIiIrY3VycmVudE1vbnRoOiB0aGlzLmFjY291bnRPYmouYWNjb3VudG5vLA0KICAgICAgICAgICAgICAgICAgICBjb2FsVXNlQm9keTogIiIsDQogICAgICAgICAgICAgICAgICAgIGZlZVN0YXJ0RGF0ZToiIiwNCiAgICAgICAgICAgICAgICAgICAgY29hbEFtb3VudDogMCwNCiAgICAgICAgICAgICAgICAgICAgdW5pdFByaWNlOiAwLA0KICAgICAgICAgICAgICAgICAgICB0aWNrZXRNb25leTowLA0KICAgICAgICAgICAgICAgICAgICB0YXhUaWNrZXRNb25leTowLA0KICAgICAgICAgICAgICAgICAgICB0YXhBbW91bnQ6IDAsDQogICAgICAgICAgICAgICAgICAgIG90aGVyRmVlOiAwLA0KICAgICAgICAgICAgICAgICAgICBwYWlkTW9uZXk6MCwNCiAgICAgICAgICAgICAgICAgICAgLy8gdGF4UmF0ZVNob3c6IDEsDQogICAgICAgICAgICAgICAgICAgIC8vIGNvYWxJbXBvcnRUeXBlOiLnhaTngq0iLA0KICAgICAgICAgICAgICAgICAgICAvLyBjb2FsSW1wb3J0VXNlOiLlj5HnlLXnlKjnhaQiLA0KICAgICAgICAgICAgICAgICAgICB0YXhSYXRlU2hvdzogIiIsDQogICAgICAgICAgICAgICAgICAgIGNvYWxJbXBvcnRUeXBlOiIiLA0KICAgICAgICAgICAgICAgICAgICBjb2FsSW1wb3J0VXNlOiIiLA0KICAgICAgICAgICAgICAgICAgICByZW1hcms6IiIsDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgdGhpcy5teVN0eWxlLnB1c2goew0KICAgICAgICAgICAgICAgICAgICAgICAgc3RhcnRkYXRlOiAnbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgICAgIGVuZGRhdGU6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgcHJldnRvdGFscmVhZGluZ3M6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgY3VydG90YWxyZWFkaW5nczogJ215c3BhbicsDQogICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2Zvcm1lcnVsbGFnZTogJ215c3BhbicsDQogICAgICAgICAgICAgICAgICAgICAgICBpbnB1dHRheHRpY2tldG1vbmV5OiAnbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0dGlja2V0bW9uZXk6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgdWxsYWdlbW9uZXk6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgdGF4cmF0ZTogJ215c3BhbicsDQogICAgICAgICAgICAgICAgICAgICAgICB0aWNrZXR0YXhhbW91bnQ6ICdteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgcmVtYXJrOiAnbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgICAgIHRpY2tldE1vbmV5OiJteXNwYW4iLA0KICAgICAgICAgICAgICAgICAgICAgICAgdGF4VGlja2V0TW9uZXk6Im15c3BhbiIsDQoNCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/pqozor4HplJnor6/lvLnlh7rmj5DnpLrmoYYNCiAgICAgICAgICAgIGVycm9yVGlwcyhzdHIpew0KICAgICAgICAgICAgICAgIHRoaXMuJE5vdGljZS5lcnJvcih7DQogICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn5o+Q56S6JywNCiAgICAgICAgICAgICAgICAgICAgZGVzYzogc3RyLA0KICAgICAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTANCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBoYW5kbGVQYWdlKHZhbHVlKSB7DQogICAgICAgICAgICAgICAgbGV0IGIgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGE7DQogICAgICAgICAgICAgICAgbGV0IGFycmF5ID0gW107DQogICAgICAgICAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgICAgICAgICAgIGlmKGl0ZW0uZWRpdFR5cGUgPT0gMSl7DQogICAgICAgICAgICAgICAgICAgICAgICBiID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgICAgIGFycmF5LnB1c2goaXRlbSkNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIGlmKGIpew0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRNb2RhbC5jb25maXJtKHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn5o+Q56S6JywNCiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICc8cD7mgqjmnInlt7LnvJbovpHkv6Hmga/ov5jmsqHmnInkv53lrZjvvIzmmK/lkKbkv53lrZjvvJ88L3A+JywNCiAgICAgICAgICAgICAgICAgICAgICAgIG9uT2s6ICgpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnN1Ym1pdERhdGEoYXJyYXkpOw0KICAgICAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2FuY2VsOiAoKSA9PiB7DQoNCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgdGhpcy5wYWdlTnVtID0gdmFsdWU7DQogICAgICAgICAgICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBoYW5kbGVQYWdlU2l6ZSh2YWx1ZSkgew0KICAgICAgICAgICAgICAgIGxldCBiID0gZmFsc2U7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhOw0KICAgICAgICAgICAgICAgIGxldCBhcnJheSA9IFtdOw0KICAgICAgICAgICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgICAgICAgICAgICBpZihpdGVtLmVkaXRUeXBlID09IDEpew0KICAgICAgICAgICAgICAgICAgICAgICAgYiA9IHRydWU7DQogICAgICAgICAgICAgICAgICAgICAgICBhcnJheS5wdXNoKGl0ZW0pDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICBpZihiKXsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7DQogICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogJ+aPkOekuicsDQogICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiAnPHA+5oKo5pyJ5bey57yW6L6R5L+h5oGv6L+Y5rKh5pyJ5L+d5a2Y77yM5piv5ZCm5L+d5a2Y77yfPC9wPicsDQogICAgICAgICAgICAgICAgICAgICAgICBvbk9rOiAoKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zdWJtaXREYXRhKGFycmF5KTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgICAgICBvbkNhbmNlbDogKCkgPT4gew0KDQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIHRoaXMucGFnZVNpemUgPSB2YWx1ZTsNCiAgICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8v5ZCR5ZCO5Y+w6K+35rGC5pWw5o2uDQogICAgICAgICAgICBnZXRBY2NvdW50TWVzc2FnZXMoKSB7DQogICAgICAgICAgICAgICAgY29uc3QgcG9zdERhdGEgPSB0aGlzLmFjY291bnRPYmo7DQogICAgICAgICAgICAgICAgcG9zdERhdGEucGFnZU51bSA9IHRoaXMucGFnZU51bTsNCiAgICAgICAgICAgICAgICBwb3N0RGF0YS5wYWdlU2l6ZSA9IHRoaXMucGFnZVNpemU7DQogICAgICAgICAgICAgICAgbGV0IHJlcSA9IHsNCiAgICAgICAgICAgICAgICAgICAgdXJsIDogIi9idXNpbmVzcy9jb2FsL2FjY291bnQvbGlzdCIsDQogICAgICAgICAgICAgICAgICAgIG1ldGhvZCA6ICJnZXQiLA0KICAgICAgICAgICAgICAgICAgICBwYXJhbXMgOiBwb3N0RGF0YQ0KICAgICAgICAgICAgICAgIH07DQogICAgICAgICAgICAgICAgdGhpcy50YkFjY291bnQubG9hZGluZyA9IHRydWUNCiAgICAgICAgICAgICAgICBheGlvcy5yZXF1ZXN0KHJlcSkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgICAgICAgICB0aGlzLnRiQWNjb3VudC5sb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhKSB7DQogICAgICAgICAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHJlcy5kYXRhLnJvd3M7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpdGVtLmVkaXRUeXBlID0gMDsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy50YkFjY291bnQuZGF0YSA9IGRhdGE7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnBhZ2VUb3RhbCA9IHJlcy5kYXRhLnRvdGFsIHx8IDA7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNldE15U3R5bGUodGhpcy50YkFjY291bnQuZGF0YS5sZW5ndGgpOw0KDQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVkaXRJbmRleCA9IC0xOw0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jb2x1bW5zSW5kZXggPSAtMTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGVycik7DQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/ph43nva4NCiAgICAgICAgICAgIG9uUmVzZXRIYW5kbGUoKXsNCiAgICAgICAgICAgICAgICB0aGlzLmFjY291bnRPYmogPSB7DQogICAgICAgICAgICAgICAgICAgIGFjY291bnRubzpudWxsLA0KICAgICAgICAgICAgICAgICAgICBjb21wYW55OnRoaXMuY29tcGFueSwNCiAgICAgICAgICAgICAgICAgICAgY29hbFVzZUJvZHk6bnVsbCwNCiAgICAgICAgICAgICAgICAgICAgY291bnRyeTpOdW1iZXIodGhpcy5jb3VudHJ5KSwNCiAgICAgICAgICAgICAgICAgICAgY29hbFVzZVR5cGU6bnVsbCwNCiAgICAgICAgICAgICAgICAgICAgY29hbFR5cGU6bnVsbCwNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8v6K6h566X5Y2V5Lu3DQogICAgICAgICAgICB1bml0UHJpY2Uocm93KXsNCiAgICAgICAgICAgICAgICBsZXQgdGlja2V0TW9uZXkgPSByb3cudGlja2V0TW9uZXk7DQogICAgICAgICAgICAgICAgbGV0IHRheFRpY2tldE1vbmV5ID0gcm93LnRheFRpY2tldE1vbmV5Ow0KICAgICAgICAgICAgICAgIGxldCBjb2FsQW1vdW50ID0gcm93LmNvYWxBbW91bnQ7DQogICAgICAgICAgICAgICAgaWYodGlja2V0TW9uZXkgIT0gbnVsbCB8fCB0YXhUaWNrZXRNb25leSAhPSBudWxsKXsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHRvdGFsID0gbnVsbDsNCiAgICAgICAgICAgICAgICAgICAgdG90YWwgPSB0aWNrZXRNb25leSArIHRheFRpY2tldE1vbmV5Ow0KICAgICAgICAgICAgICAgICAgICByb3cudW5pdFByaWNlID0gdG90YWwvY29hbEFtb3VudC50b0ZpeGVkKDIpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICByZW1vdmUoKXsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMuJHJlZnMuYWNjb3VudEVzVGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICAgICAgICAgICAgaWYoZGF0YSA9PSBudWxsIHx8IGRhdGEubGVuZ3RoID09PSAwKXsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoIuivt+mAieaLqeimgeWIoOmZpOeahOaVsOaNriIpDQogICAgICAgICAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7DQogICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn5o+Q56S6JywNCiAgICAgICAgICAgICAgICAgICAgY29udGVudDogJzxwPuaYr+WQpuehruiupOWIoOmZpOmAieS4reS/oeaBr++8nzwvcD4nLA0KICAgICAgICAgICAgICAgICAgICBvbk9rOiAoKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICBsZXQgYiA9IHRydWU7DQogICAgICAgICAgICAgICAgICAgICAgICBsZXQgaWRzID0gJyc7DQogICAgICAgICAgICAgICAgICAgICAgICBsZXQgdG90YWwgPSB0aGlzLnBhZ2VUb3RhbA0KICAgICAgICAgICAgICAgICAgICAgICAgZm9yKGxldCBpPTA7aTxkYXRhLmxlbmd0aDtpKyspew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxldCBpdGVtID0gZGF0YVtpXTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZihpdGVtLmlkICE9IG51bGwgJiYgaXRlbS5pZC5sZW5ndGggPiAwKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYoaXRlbS5wYWJyaWlkKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGIgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZHMgKz0gaXRlbS5pZCArICcsJzsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnBhZ2VUb3RhbCA9IHRvdGFsDQogICAgICAgICAgICAgICAgICAgICAgICBpZihiKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZihpZHMubGVuZ3RoID4gMCl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlbW92ZUNvYWxBY2NvdW50KGlkcykudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEuY29kZSA9PSAwKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICB9ZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+mAieS4reS/oeaBr+S4reacieS/oeaBr+i/mOayoeaciei3n+W9kumbhuWNleino+mZpOWFs+iBlO+8jOivt+WFiOino+mZpOWFs+iBlCcpDQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICAgIG9uQ2FuY2VsOiAoKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvcGVuQWRkQmlsbFBlck1vZGFsKG5hbWUpIHsNCiAgICAgICAgICAgICAgICBpZiAobmFtZSA9PT0gJ2N1cnJlbnQnKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRBY2NvdW50KCkNCiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5hbWUgPT09ICdhbGwnKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRBbGxBY2NvdW50KCkNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/liqDlhaXlvZLpm4bljZXvvIzlhajpg6jmnInmlYjlj7DotKYNCiAgICAgICAgICAgIHNlbGVjdGVkQWxsQWNjb3VudCgpew0KICAgICAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpcw0KICAgICAgICAgICAgICAgIHRoYXQuc3BpblNob3cgPSB0cnVlOw0KICAgICAgICAgICAgICAgIHNlbGVjdENvYWxJZHModGhpcy5hY2NvdW50T2JqKS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgICAgICAgICAgIHRoYXQuc3BpblNob3cgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgaWYocmVzLmRhdGEubGVuZ3RoID09IDApew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5lcnJvclRpcHMoJ+aXoOacieaViOaVsOaNruWPr+WKoOWFpeW9kumbhuWNlScpDQogICAgICAgICAgICAgICAgICAgIH1lbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBpZHMgPSBbXTsNCiAgICAgICAgICAgICAgICAgICAgICAgIGZvcihsZXQgaT0wO2k8cmVzLmRhdGEucm93cy5sZW5ndGg7aSsrKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgaXRlbSA9IHJlcy5kYXRhLnJvd3NbaV07DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWRzLnB1c2goaXRlbS5pZCkNCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuJHJlZnMuYWRkQmlsbFBlci5pbml0QW1tZXRlcihpZHMsIDIwLHRoaXMuYWNjb3VudE9iai5jb3VudHJ5KTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHNlbGVjdGVkQWNjb3VudCgpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy4kcmVmcy5hY2NvdW50RXNUYWJsZS5nZXRTZWxlY3Rpb24oKTsNCiAgICAgICAgICAgICAgICBsZXQgYiA9IDE7DQogICAgICAgICAgICAgICAgaWYoZGF0YSA9PSBudWxsIHx8IGRhdGEubGVuZ3RoID09IDApew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygn6K+36YCJ5oup6KaB5Yqg5YWl5b2S6ZuG5Y2V55qE5Y+w6LSmJykNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICBsZXQgaWRzID0gW107DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgICAgICAgICAgICAgICAgaWYoaXRlbS5zdGF0dXMgPT09IDUpew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGIgPSAzDQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICBpZihpdGVtLnN0YXR1cyA9PT0gNCl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYj00Ow0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgaWRzLnB1c2goaXRlbS5pZCkNCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIGlmKGIgPT09IDEpew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5hZGRCaWxsUGVyLmluaXRBbW1ldGVyKGlkcywyMCx0aGlzLmFjY291bnRPYmouY291bnRyeSk7DQogICAgICAgICAgICAgICAgICAgIH1lbHNlIGlmKGIgPT09IDIpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCfpgInkuK3nmoTlj7DotKbkuK3lrZjlnKjkuLTml7bmlbDmja7vvIzor7flhYjkv53lrZjlho3liqDlhaXlvZLpm4bljZXvvIEnKQ0KICAgICAgICAgICAgICAgICAgICB9ZWxzZSBpZihiPT09Myl7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygn6YCA5Zue55qE5Y+w6LSm5LiN6IO95Yqg5YWl5YW25a6D5b2S6ZuG5Y2V77yM6K+354K55Ye7W+mHjeaWsOWKoOWFpeW9kumbhuWNlV3mjInpkq4nKQ0KICAgICAgICAgICAgICAgICAgICB9ZWxzZSBpZihiPT09NCl7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygn6YCJ5oup55qE5Y+w6LSm5pyJ5bey5Yqg5YWl5b2S6ZuG5Y2V55qE5Y+w6LSm77yM5LiN6IO95Yqg5YWl5YW25LuW5b2S6ZuG5Y2VJykNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBvcGVuQ29tcGxldGVkUHJlTW9kYWwoKXsNCiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLmNvbXBsZXRlZFByZS5pbml0QW1tZXRlcih0aGlzLmFjY291bnRPYmouY291bnRyeSwyKTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBhZ2FpbkpvaW4oKXsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMuJHJlZnMuYWNjb3VudEVzVGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICAgICAgICAgICAgbGV0IGIgPSB0cnVlOw0KICAgICAgICAgICAgICAgIGlmKGRhdGEgPT0gbnVsbCB8fCBkYXRhLmxlbmd0aCA9PSAwKXsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+ivt+mAieaLqeimgemHjeaWsOWKoOWFpeW9kumbhuWNleeahOWPsOi0picpDQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgbGV0IGlkcyA9ICcnOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBzdGF0dXMgPSBpdGVtLnN0YXR1czsNCiAgICAgICAgICAgICAgICAgICAgICAgIGlmKHN0YXR1cyAhPSA1KXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBiID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICBpZHMrPSBpdGVtLmlkICsnLCcNCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIGlmKGIpew0KICAgICAgICAgICAgICAgICAgICAgICAgcmVKb2luQmlsbHByZShpZHMpLnRoZW4oKHJlcykgPT57DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYocmVzLmRhdGEuY29kZT09MCl7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbyh7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50Oifmj5DnpLrvvJrmk43kvZzmiJDlip8nICwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxMCwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsb3NhYmxlOiB0cnVlDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgICAgIH1lbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCflj6rmnInlt7LpgIDlm57nmoTlj7DotKbmiY3og73ph43mlrDliqDlhaXlvZLpm4bljZUnKQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHJlZnJlc2goKXsNCiAgICAgICAgICAgICAgICBsZXQgb2JqID0gdGhpcw0KICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgew0KICAgICAgICAgICAgICAgICAgICBvYmouZ2V0QWNjb3VudE1lc3NhZ2VzKCkNCiAgICAgICAgICAgICAgICB9LDIwMCk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgdmFsaWRhdGUoKXsNCiAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lbnRlck9wZXJhdGUodGhpcy5jb2x1bW5zSW5kZXgpLmRhdGE7DQogICAgICAgICAgICAgICAgaWYodmFsKSB7DQogICAgICAgICAgICAgICAgICAgIHN3aXRjaCAodGhpcy5jb2x1bW5zSW5kZXgpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgMzoNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlRmVlU3RhcnREYXRlKCk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDQ6DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy52YWxpZGF0ZUNvYWxBbW91bnQoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgNjoNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlVGlja2V0TW9uZXkoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgNzoNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlVGF4VGlja2V0TW9uZXkoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgMTA6DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy52YWxpZGF0ZU90aGVyTW9uZXkoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB2YWxpZGF0ZUZlZVN0YXJ0RGF0ZSgpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdEZlZVN0YXJ0RGF0ZTsNCiAgICAgICAgICAgICAgICBsZXQgcmVzdWx0ID0gX3ZlcmlmeV9GZWVTdGFydERhdGUoZGF0YSx2YWwpOw0KICAgICAgICAgICAgICAgIGlmKHJlc3VsdCl7Ly/lpLHotKXlsLHlvLnlh7rmj5DnpLrlhoXlrrnvvIzlubblsIbmlbDmja7mgaLlpI3liJ3lp4vljJYNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMocmVzdWx0KQ0KICAgICAgICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgICAgICAgICBkYXRhLmZlZVN0YXJ0RGF0ZSA9IHZhbDsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHZhbGlkYXRlQ29hbEFtb3VudCgpew0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMuZWRpdENvYWxBbW91bnQsICJ0aGlzLmVkaXRDb2FsQW1vdW50Iik7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lZGl0Q29hbEFtb3VudDsNCiAgICAgICAgICAgICAgICBpZiAoIXRlc3ROdW1iZXIodmFsKSkgew0KICAgICAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygn6K+36L6T5YWl5pWw5a2X77yBJyk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIC8vIGlmICh0ZXN0TnVtYmVyKHZhbCkgPT0gMCkgew0KICAgICAgICAgICAgICAgIC8vICAgICB0aGlzLmVycm9yVGlwcygn6K+36L6T5YWl5pWw5a2X77yBJyk7DQogICAgICAgICAgICAgICAgLy8gfQ0KICAgICAgICAgICAgICAgIGRhdGEuY29hbEFtb3VudCA9IHZhbDsNCiAgICAgICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgICAgICAgICBsZXQgdW5pdFByaWNlID0gZGF0YS5wYWlkTW9uZXk/KGRhdGEucGFpZE1vbmV5L2RhdGEuY29hbEFtb3VudCk6MDsNCiAgICAgICAgICAgICAgICBkYXRhLnVuaXRQcmljZSA9IHVuaXRQcmljZS50b0ZpeGVkKDIpOw0KDQoNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvL+mqjOivgeWNleS7tw0KICAgICAgICAgICAgdmFsaWRhdGVVbml0UHJpY2UoZGF0YSl7DQogICAgICAgICAgICAgICAgbGV0IGNhdGVnb3J5ID0gZGF0YS5jYXRlZ29yeTsvL+eUteihqOaPj+i/sOexu+Weiw0KICAgICAgICAgICAgICAgIGxldCBhbW1ldGVydXNlID0gZGF0YS5hbW1ldGVydXNlOy8v55S16KGo55So6YCUDQogICAgICAgICAgICAgICAgbGV0IHVuaXRwaXJjZSAgPSBkYXRhLnVuaXRQcmljZTsvL+WPsOi0puWNleS7tw0KICAgICAgICAgICAgICAgIGlmKCFqdWRnZV9uZWdhdGUoY2F0ZWdvcnkpICYmICFqdWRnZV9yZWNvdmVyeShhbW1ldGVydXNlKSAmJiBqdWRnZV95YihjYXRlZ29yeSkpew0KICAgICAgICAgICAgICAgICAgICAvLyBpZih1bml0UHJpY2Upew0KICAgICAgICAgICAgICAgICAgICAvLyAgICAgaWYodW5pdFByaWNlIDwgdW5pdHBpcmNlTWluIHx8IHVuaXRQcmljZSA+IHVuaXRwaXJjZU1heCl7DQogICAgICAgICAgICAgICAgICAgIC8vICAgICAgICAgdGhpcy5lcnJvclRpcHMoJ+mbhuWbouimgeaxguWNleS7t+iMg+WbtOWcqDAuM34y5YWD77yM5q2k5Y+w6LSm5Y2V5Lu3OiAnKyB1bml0UHJpY2UgKycg5bey6LaF6L+H6IyD5Zu077yM6K+356Gu6K6k77yBJykNCiAgICAgICAgICAgICAgICAgICAgLy8gICAgIH0NCiAgICAgICAgICAgICAgICAgICAgLy8gfQ0KICAgICAgICAgICAgICAgICAgICBpZiAodW5pdHBpcmNlKSB7DQogICAgICAgICAgICAgICAgICAgICAgICBpZiAodW5pdHBpcmNlICE9IG51bGwgJiYgdW5pdHBpcmNlIDwgdW5pdHBpcmNlTWF4MSkgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICLljZXku7fojIPlm7Tlv4XpobvlpKfkuo4wLjHlhYPvvIzmraTlj7DotKbljZXku7c6ICIgKw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1bml0cGlyY2UgKw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAi5LiN5Zyo6IyD5Zu05YaF77yM6K+356Gu6K6k77yBIg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7DQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy8gdmFsaWRhdGVUaWNrZXRNb25leSgpew0KICAgICAgICAgICAgLy8gICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAvLyAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdFRpY2tldE1vbmV5Ow0KICAgICAgICAgICAgLy8gICAgIGlmICghdGVzdE51bWJlcih2YWwpKSB7DQogICAgICAgICAgICAvLyAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCfor7fovpPlhaXmlbDlrZfvvIEnKTsNCiAgICAgICAgICAgIC8vICAgICB9DQogICAgICAgICAgICAvLyAgICAgZGF0YS50aWNrZXRNb25leSA9IHZhbDsNCiAgICAgICAgICAgIC8vICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCg0KICAgICAgICAgICAgLy8gfSwNCiAgICAgICAgICAgIC8vIHZhbGlkYXRlVGF4VGlja2V0TW9uZXkoKXsNCiAgICAgICAgICAgIC8vICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgICAgICAgLy8gICAgIGxldCB2YWwgPSB0aGlzLmVkaXRUYXhUaWNrZXRNb25leTsNCiAgICAgICAgICAgIC8vICAgICBpZiAoIXRlc3ROdW1iZXIodmFsKSkgew0KICAgICAgICAgICAgLy8gICAgICAgICB0aGlzLmVycm9yVGlwcygn6K+36L6T5YWl5pWw5a2X77yBJyk7DQogICAgICAgICAgICAvLyAgICAgfQ0KICAgICAgICAgICAgLy8gICAgIGRhdGEudGF4VGlja2V0TW9uZXkgPSB2YWw7DQogICAgICAgICAgICAvLyAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICAvLyB9LA0KDQogICAgICAgICAgICAvL+iuoeeulyDnlKjnlLXph48s5oC755S16YePLOWNleS7tyzmgLvotLnnlKgs5rWu5Yqo5q+ULg0KICAgICAgICAgICAgY2FsY3VsYXRlQWxsKHJvdykgew0KICAgICAgICAgICAgICAgIHJvdy5jdXJ1c2VkcmVhZGluZ3MgPSBfY2FsY3VsYXRlVXNlZFJlYWRpbmdzKHJvdyk7DQogICAgICAgICAgICAgICAgcm93LnRvdGFsdXNlZHJlYWRpbmdzID0gX2NhbGN1bGF0ZVRvdGFsUmVhZGluZ3Mocm93KTsNCiAgICAgICAgICAgICAgICBpZihyb3cuaXNjaGFuZ2VhbW1ldGVyID09IDEgJiYgcm93LmlzbmV3ID09IDEpew0KICAgICAgICAgICAgICAgICAgICBpZihyb3cub2xkYmlsbHBvd2VyPjApew0KICAgICAgICAgICAgICAgICAgICAgICAgcm93LnRvdGFsdXNlZHJlYWRpbmdzID0gcGFyc2VGbG9hdChyb3cudG90YWx1c2VkcmVhZGluZ3MpK01hdGguYWJzKHJvdy5vbGRiaWxscG93ZXIpDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgbGV0IHJlbWFyayA9IHJvdy5yZW1hcmsNCiAgICAgICAgICAgICAgICAgICAgaWYocmVtYXJrLmluZGV4T2YoIuaNouihqCIpID09IC0xKXsNCiAgICAgICAgICAgICAgICAgICAgICAgIHJvdy5yZW1hcmsrPSAi5o2i6KGo77yM57uT5riF5Y6f55S16KGo6K+75pWw44CQIityb3cub2xkYmlsbHBvd2VyKyLjgJHvvJsiDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgaWYgKHJvdy50aWNrZXRNb25leSB8fCByb3cudGF4VGlja2V0TW9uZXkpIHsNCiAgICAgICAgICAgICAgICAgICAgcm93LmFjY291bnRtb25leSA9IF9jYWxjdWxhdGVBY2NvdW50TW9uZXkocm93KTsNCiAgICAgICAgICAgICAgICAgICAgcm93LnVuaXRQcmljZSA9IF9jYWxjdWxhdGVVbml0UHJpY2VCeVVzZWRNb25leShyb3cpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICByb3cucXVvdGVyZWFkaW5nc3JhdGlvID0gX2NhbGN1bGF0ZVF1b3RlcmVhZGluZ3NyYXRpbyhyb3cpOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHZhbGlkYXRlT3RoZXJNb25leSgpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdE90aGVyRmVlOw0KICAgICAgICAgICAgICAgIGlmICghdGVzdE51bWJlcih2YWwpKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCfor7fovpPlhaXmlbDlrZfvvIEnKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgZGF0YS5vdGhlckZlZSA9IHZhbDsNCiAgICAgICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgICAgICAgICBsZXQgcGFpZE1vbmV5ID0gZGF0YS50aWNrZXRNb25leSoxK2RhdGEudGF4VGlja2V0TW9uZXkqMStkYXRhLm90aGVyRmVlKjE7DQogICAgICAgICAgICAgICAgZGF0YS5wYWlkTW9uZXkgPSBwYWlkTW9uZXkudG9GaXhlZCgyKTsNCiAgICAgICAgICAgICAgICBsZXQgdW5pdFByaWNlID0gZGF0YS5jb2FsQW1vdW50PyhkYXRhLnBhaWRNb25leS9kYXRhLmNvYWxBbW91bnQpOjA7DQogICAgICAgICAgICAgICAgZGF0YS51bml0UHJpY2UgPSB1bml0UHJpY2UudG9GaXhlZCgyKTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvL+mqjOivgeaZruelqA0KICAgICAgICAgICAgdmFsaWRhdGVUaWNrZXRNb25leSgpIHsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgICAgICAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRUaWNrZXRNb25leTsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhkYXRhLnBhaWRNb25leSwgZGF0YS5jb2FsQW1vdW50LCAiZGF0YS5wYWlkTW9uZXksIGRhdGEuY29hbEFtb3VudCIpOw0KICAgICAgICAgICAgICAgIGlmICh2YWwgIT0gZGF0YS5vbGRfdGlja2V0bW9uZXkpIHsNCiAgICAgICAgICAgICAgICAgICAgdmFsID0gcGFyc2VGbG9hdCh2YWwpOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLnRpY2tldE1vbmV5ID0gX3ZlcmlmeV9Nb25leShkYXRhLCB2YWwpOw0KICAgICAgICAgICAgICAgICAgICAvLyBkYXRhLmlucHV0dGlja2V0bW9uZXkgPSBfdmVyaWZ5X01vbmV5KGRhdGEsIHZhbCkNCiAgICAgICAgICAgICAgICAgICAgLy8gZGF0YS50aWNrZXRtb25leSA9IGNhbGN1bGF0ZUFjdHVhbE1vbmV5KGRhdGEsdmFsKQ0KICAgICAgICAgICAgICAgICAgICBsZXQgcGFpZE1vbmV5ID0gZGF0YS50aWNrZXRNb25leSoxK2RhdGEudGF4VGlja2V0TW9uZXkqMStkYXRhLm90aGVyRmVlKjE7DQogICAgICAgICAgICAgICAgICAgICBkYXRhLnBhaWRNb25leSA9IHBhaWRNb25leS50b0ZpeGVkKDIpOw0KICAgICAgICAgICAgICAgICAgICBsZXQgdW5pdFByaWNlID0gZGF0YS5jb2FsQW1vdW50PyhkYXRhLnBhaWRNb25leS9kYXRhLmNvYWxBbW91bnQqMSk6MDsNCiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2codW5pdFByaWNlLCAidW5pdFByaWNlIikNCiAgICAgICAgICAgICAgICAgICAgZGF0YS51bml0UHJpY2UgPSB1bml0UHJpY2UudG9GaXhlZCgyKTsNCiAgICAgICAgICAgICAgICAgICAgLy8gbGV0IHVuaXRwaXJjZSA9IGRhdGEuY29hbEFtb3VudD8oZGF0YS5wYWlkTW9uZXkvZGF0YS5jb2FsQW1vdW50KjEpOjA7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgICAgICAgICAgICAvLyB0aGlzLmNhbGN1bGF0ZUFsbChkYXRhKTsNCiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHZhbCA9PSBkYXRhLm9sZF90aWNrZXRtb25leSkgew0KICAgICAgICAgICAgICAgICAgICBkYXRhLnRpY2tldE1vbmV5ID0gdmFsOw0KICAgICAgICAgICAgICAgICAgICAvLyBkYXRhLmlucHV0dGlja2V0bW9uZXkgPSB2YWw7DQogICAgICAgICAgICAgICAgICAgIC8vIGRhdGEudGlja2V0bW9uZXkgPSBjYWxjdWxhdGVBY3R1YWxNb25leShkYXRhLHZhbCkNCiAgICAgICAgICAgICAgICAgICAgLy8gdGhpcy5jYWxjdWxhdGVBbGwoZGF0YSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIC8vIHRoaXMudmFsaWRhdGVVbml0UHJpY2UoZGF0YSkNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICAvL+mqjOivgeS4k+elqA0KICAgICAgICAgICAgdmFsaWRhdGVUYXhUaWNrZXRNb25leSgpIHsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgICAgICAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRUYXhUaWNrZXRNb25leTsNCiAgICAgICAgICAgICAgICBpZiAodmFsICE9IGRhdGEub2xkX3RheHRpY2tldG1vbmV5KSB7DQogICAgICAgICAgICAgICAgICAgIHZhbCA9IHBhcnNlRmxvYXQodmFsKTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS50YXhUaWNrZXRNb25leSA9IF92ZXJpZnlfTW9uZXkoZGF0YSwgdmFsKQ0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHBhaWRNb25leSA9IGRhdGEudGlja2V0TW9uZXkqMStkYXRhLnRheFRpY2tldE1vbmV5KjErZGF0YS5vdGhlckZlZSoxOw0KICAgICAgICAgICAgICAgICAgICAgZGF0YS5wYWlkTW9uZXkgPSBwYWlkTW9uZXkudG9GaXhlZCgyKTsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHVuaXRQcmljZSA9IGRhdGEuY29hbEFtb3VudD8oZGF0YS5wYWlkTW9uZXkvZGF0YS5jb2FsQW1vdW50KTowOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLnVuaXRQcmljZSA9IHVuaXRQcmljZS50b0ZpeGVkKDIpOw0KICAgICAgICAgICAgICAgICAgICAvLyBkYXRhLnRheHRpY2tldG1vbmV5ID0gY2FsY3VsYXRlQWN0dWFsTW9uZXkoZGF0YSx2YWwpDQogICAgICAgICAgICAgICAgICAgIC8vIGRhdGEudGF4QW1vdW50ID0gZGF0YS50YXhUaWNrZXRNb25leSpkYXRhLnRheFJhdGVTaG93Ow0KICAgICAgICAgICAgICAgICAgICBkYXRhLnRheEFtb3VudCA9IGNvdW50VGF4YW1vdW50MShkYXRhKTsNCiAgICAgICAgICAgICAgICAgICAgLy8gdGhpcy5jYWxjdWxhdGVBbGwoZGF0YSk7DQogICAgICAgICAgICAgICAgfSBlbHNlIGlmICh2YWwgPT0gZGF0YS5vbGRfdGF4dGlja2V0bW9uZXkpIHsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS50YXhUaWNrZXRNb25leSA9IHZhbDsNCiAgICAgICAgICAgICAgICAgICAgLy8gZGF0YS50YXh0aWNrZXRtb25leSA9IGNhbGN1bGF0ZUFjdHVhbE1vbmV5KGRhdGEsdmFsKQ0KICAgICAgICAgICAgICAgICAgICAvLyBkYXRhLnRheEFtb3VudCA9IChkYXRhLnRheFRpY2tldE1vbmV5KjEpKihkYXRhLnRheFJhdGVTaG93KjEpOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLnRheEFtb3VudCA9IGNvdW50VGF4YW1vdW50MShkYXRhKTsNCiAgICAgICAgICAgICAgICAgICAgLy8gdGhpcy5jYWxjdWxhdGVBbGwoZGF0YSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGRhdGEudGF4UmF0ZVNob3csICIudGF4UmF0ZVNob3dkYXRhNTU1NTU1NTU1NSIpOw0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGRhdGEudGF4VGlja2V0TW9uZXksICIudGF4VGlja2V0TW9uZXkiKTsNCiAgICAgICAgICAgICAgICAvLyB0aGlzLnZhbGlkYXRlVW5pdFByaWNlKGRhdGEpDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/kv53lrZjlj6/nvJbovpHooajmoLznmoTliJ3lp4vljJbmlbDmja4NCiAgICAgICAgICAgIHNldE5ld0ZpZWxkKGRhdGEpIHsNCiAgICAgICAgICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICAgICAgICAgICAgaXRlbS5vbGRfc3RhcnRkYXRlID0gaXRlbS5zdGFydGRhdGU7DQogICAgICAgICAgICAgICAgICAgIGl0ZW0ub2xkX3ByZXZ0b3RhbHJlYWRpbmdzID0gaXRlbS5wcmV2dG90YWxyZWFkaW5nczsNCg0KICAgICAgICAgICAgICAgICAgICBpdGVtLm11bHR0aW1lcyA9IGl0ZW0ubWFnbmlmaWNhdGlvbg0KICAgICAgICAgICAgICAgICAgICBpdGVtLm9sZF9lbmRkYXRlID0gaXRlbS5lbmRkYXRlOw0KICAgICAgICAgICAgICAgICAgICBpdGVtLm9sZF9jdXJ0b3RhbHJlYWRpbmdzID0gaXRlbS5jdXJ0b3RhbHJlYWRpbmdzOw0KICAgICAgICAgICAgICAgICAgICBpdGVtLm9sZF90cmFuc2Zvcm1lcnVsbGFnZSA9IGl0ZW0udHJhbnNmb3JtZXJ1bGxhZ2U7DQogICAgICAgICAgICAgICAgICAgIGl0ZW0ub2xkX3RheHRpY2tldG1vbmV5ID0gaXRlbS5pbnB1dHRheHRpY2tldG1vbmV5Ow0KICAgICAgICAgICAgICAgICAgICBpdGVtLm9sZF90aWNrZXRtb25leSA9IGl0ZW0uaW5wdXR0aWNrZXRtb25leTsNCiAgICAgICAgICAgICAgICAgICAgaXRlbS5vbGRfdWxsYWdlbW9uZXkgPSBpdGVtLnVsbGFnZW1vbmV5Ow0KICAgICAgICAgICAgICAgICAgICBpdGVtLm9sZF9wcmV2aGlnaHJlYWRpbmdzID0gaXRlbS5wcmV2aGlnaHJlYWRpbmdzOw0KICAgICAgICAgICAgICAgICAgICBpdGVtLm9sZF9wcmV2ZmxhdHJlYWRpbmdzID0gaXRlbS5wcmV2ZmxhdHJlYWRpbmdzOw0KICAgICAgICAgICAgICAgICAgICBpdGVtLm9sZF9wcmV2bG93cmVhZGluZ3MgPSBpdGVtLnByZXZsb3dyZWFkaW5nczsNCg0KICAgICAgICAgICAgICAgICAgICBpdGVtLm9sZF9jdXJoaWdocmVhZGluZ3MgPSBpdGVtLmN1cmhpZ2hyZWFkaW5nczsNCiAgICAgICAgICAgICAgICAgICAgaXRlbS5vbGRfY3VyZmxhdHJlYWRpbmdzID0gaXRlbS5jdXJmbGF0cmVhZGluZ3M7DQogICAgICAgICAgICAgICAgICAgIGl0ZW0ub2xkX2N1cmxvd3JlYWRpbmdzID0gaXRlbS5jdXJsb3dyZWFkaW5nczsNCiAgICAgICAgICAgICAgICAgICAgaXRlbS5vbGRfY3VydG90YWxyZWFkaW5ncyA9IGl0ZW0uY3VydG90YWxyZWFkaW5nczsNCg0KICAgICAgICAgICAgICAgICAgICBpdGVtLnZlcnNpb24gPSBpbmRleERhdGEudmVyc2lvbjsNCiAgICAgICAgICAgICAgICAgICAgaXRlbS5lZGl0VHlwZSA9IDA7DQogICAgICAgICAgICAgICAgICAgIGl0ZW0uaXNGUEcgPSBqdWRnaW5nX2VkaXRhYmlsaXR5MShpdGVtKTsNCiAgICAgICAgICAgICAgICAgICAgaXRlbS5pc1dCID0ganVkZ2luZ19lZGl0YWJpbGl0eShpdGVtKQ0KICAgICAgICAgICAgICAgICAgICBpZiAoIWl0ZW0ucmVtYXJrKQ0KICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS5yZW1hcmsgPSAnJzsNCiAgICAgICAgICAgICAgICAgICAgaWYgKCFpdGVtLmJ6KQ0KICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS5ieiA9ICcnOw0KICAgICAgICAgICAgICAgICAgICBpdGVtLnRyYW5zZm9ybWVydWxsYWdlID0ganVkZ2VOdW1iZXIoaXRlbS50cmFuc2Zvcm1lcnVsbGFnZSkNCiAgICAgICAgICAgICAgICAgICAgaXRlbS5pbnB1dHRheHRpY2tldG1vbmV5ID0ganVkZ2VOdW1iZXIoaXRlbS5pbnB1dHRheHRpY2tldG1vbmV5KQ0KICAgICAgICAgICAgICAgICAgICBpdGVtLmlucHV0dGlja2V0bW9uZXkgPSBqdWRnZU51bWJlcihpdGVtLmlucHV0dGlja2V0bW9uZXkpDQogICAgICAgICAgICAgICAgICAgIGl0ZW0udGF4VGlja2V0TW9uZXkgPSBqdWRnZU51bWJlcihpdGVtLnRheFRpY2tldE1vbmV5KQ0KICAgICAgICAgICAgICAgICAgICBpdGVtLnRpY2tldE1vbmV5ID0ganVkZ2VOdW1iZXIoaXRlbS50aWNrZXRNb25leSkNCiAgICAgICAgICAgICAgICAgICAgaXRlbS51bGxhZ2Vtb25leSA9IGp1ZGdlTnVtYmVyKGl0ZW0udWxsYWdlbW9uZXkpDQogICAgICAgICAgICAgICAgICAgIGl0ZW0uY3VydXNlZHJlYWRpbmdzID0ganVkZ2VOdW1iZXIoaXRlbS5jdXJ1c2VkcmVhZGluZ3MpDQogICAgICAgICAgICAgICAgICAgIGl0ZW0uYWNjb3VudG1vbmV5ID0ganVkZ2VOdW1iZXIoaXRlbS5hY2NvdW50bW9uZXkpDQogICAgICAgICAgICAgICAgICAgIGlmICgoaXRlbS50YXhyYXRlID09IG51bGwgfHwgaXRlbS50YXhyYXRlID09IDApICYmIGl0ZW0udG90YWwgPT0gbnVsbCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS50YXhyYXRlID0gJzEzJw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIGlmIChpdGVtLnRheHJhdGUgJiYgaXRlbS50YXhBbW91bnQgPT0gbnVsbCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS50YXhBbW91bnQgPSBjb3VudFRheGFtb3VudDEoaXRlbSk7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGRhdGEsICJkYXRhNTU1NTU1NTU1NSIpOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHNldHJlbWFyaygpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdHJlbWFyazsNCiAgICAgICAgICAgICAgICBkYXRhLnJlbWFyayA9IHZhbDsNCiAgICAgICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBzZXRDb2FsVXNlQm9keSgpew0KICAgICAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdENvYWxVc2VCb2R5Ow0KICAgICAgICAgICAgICAgIGRhdGEuY29hbFVzZUJvZHkgPSB2YWw7DQogICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy/kuJPnpajnqI7pop0NCiAgICAgICAgICAgIHNldHRheHJhdGUoKSB7DQogICAgICAgICAgICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdFRheFJhdGU7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgICAgICAgICAgICBkYXRhLnRheFJhdGVTaG93ID0gdmFsOw0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGRhdGEudGF4UmF0ZVNob3csICIudGF4UmF0ZVNob3dkYXRhNTU1NTU1NTU1NSIpOw0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGRhdGEudGF4VGlja2V0TW9uZXksICIudGF4VGlja2V0TW9uZXkiKTsNCiAgICAgICAgICAgICAgICBkYXRhLnRheEFtb3VudCA9IGNvdW50VGF4YW1vdW50MShkYXRhKTsNCiAgICAgICAgICAgICAgICAvLyBkYXRhLnRheEFtb3VudCA9IChkYXRhLnRheFRpY2tldE1vbmV5KjEpKihkYXRhLnRheFJhdGVTaG93KjEpOw0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGRhdGEudGF4QW1vdW50LCAiLnRheEFtb3VudCIpOw0KICAgICAgICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHNldENvYWxUeXBlKCkgew0KICAgICAgICAgICAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRDb2FsVHlwZTsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgICAgICAgICAgIGRhdGEuY29hbEltcG9ydFR5cGUgPSB2YWw7DQogICAgICAgICAgICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgc2V0Q29hbFVzZSgpIHsNCiAgICAgICAgICAgICAgICBsZXQgdmFsID0gdGhpcy5lZGl0Q29hbFVzZTsNCiAgICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgICAgICAgICAgIGRhdGEuY29hbEltcG9ydFVzZSA9IHZhbDsNCiAgICAgICAgICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBzZXRNeVN0eWxlKGxlbmd0aCl7DQogICAgICAgICAgICAgICAgdGhpcy5teVN0eWxlPVtdOw0KICAgICAgICAgICAgICAgIGZvcih2YXIgaT0wO2k8bGVuZ3RoO2krKyl7DQogICAgICAgICAgICAgICAgICAgIHRoaXMubXlTdHlsZS5wdXNoKHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGNvYWxVc2VCb2R5OidteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgZmVlU3RhcnREYXRlOidteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgY29hbEFtb3VudDonbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgICAgIHRpY2tldE1vbmV5OidteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgdGF4VGlja2V0TW9uZXk6J215c3BhbicsDQogICAgICAgICAgICAgICAgICAgICAgICB0YXhSYXRlU2hvdzonbXlzcGFuJywNCiAgICAgICAgICAgICAgICAgICAgICAgIG90aGVyRmVlOidteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgY29hbEltcG9ydFR5cGU6J215c3BhbicsDQogICAgICAgICAgICAgICAgICAgICAgICBjb2FsSW1wb3J0VXNlOidteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICAgICAgcmVtYXJrOidteXNwYW4nLA0KICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy9zcGFu54K55Ye75LqL5Lu25bCGc3BhbuaNouaIkOi+k+WFpeahhuW5tuS4lOiOt+WPlueEpueCuQ0KICAgICAgICAgICAgc2VsZWN0Q2FsbChyb3csaW5kZXgsY29sdW1ucyxzdHIpew0KICAgICAgICAgICAgICAgIHRoaXMuZWRpdEZlZVN0YXJ0RGF0ZSA9IHJvdy5mZWVTdGFydERhdGU7DQogICAgICAgICAgICAgICAgdGhpcy5lZGl0Q29hbFVzZUJvZHkgPSByb3cuY29hbFVzZUJvZHk7DQogICAgICAgICAgICAgICAgdGhpcy5lZGl0Q29hbEFtb3VudCA9IHJvdy5jb2FsQW1vdW50ID09IG51bGwgfHwgcm93LmNvYWxBbW91bnQ9PT0wP251bGw6cm93LmNvYWxBbW91bnQ7DQogICAgICAgICAgICAgICAgdGhpcy5lZGl0Q29hbFR5cGUgPSByb3cuY29hbEltcG9ydFR5cGU7DQogICAgICAgICAgICAgICAgdGhpcy5lZGl0Q29hbFVzZSA9IHJvdy5jb2FsSW1wb3J0VXNlOw0KICAgICAgICAgICAgICAgIHRoaXMuZWRpdFRpY2tldE1vbmV5ID0gcm93LnRpY2tldE1vbmV5Ow0KICAgICAgICAgICAgICAgIHRoaXMuZWRpdFRheFRpY2tldE1vbmV5ID0gcm93LnRheFRpY2tldE1vbmV5Ow0KICAgICAgICAgICAgICAgIHRoaXMuZWRpdFRheFJhdGUgPSByb3cudGF4UmF0ZVNob3c7DQogICAgICAgICAgICAgICAgdGhpcy5lZGl0T3RoZXJGZWUgPSByb3cub3RoZXJGZWU7DQogICAgICAgICAgICAgICAgdGhpcy5lZGl0cmVtYXJrID0gcm93LnJlbWFyazsNCiAgICAgICAgICAgICAgICB0aGlzLmVkaXRJbmRleCA9IGluZGV4Ow0KICAgICAgICAgICAgICAgIHRoaXMuY29sdW1uc0luZGV4ID0gY29sdW1uczsNCiAgICAgICAgICAgICAgICBsZXQgYT10aGlzOw0KICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgew0KICAgICAgICAgICAgICAgICAgICBhLiRyZWZzW3N0citpbmRleCtjb2x1bW5zXS5mb2N1cygpOw0KICAgICAgICAgICAgICAgIH0sMjAwKTsNCg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8v6Lez6L2s5Yiw5LiL5LiA5qC8DQogICAgICAgICAgICBuZXh0Q2VsbChkYXRhKXsNCiAgICAgICAgICAgICAgICBsZXQgaW5kZXggPSBkYXRhLmVkaXRJbmRleDsNCiAgICAgICAgICAgICAgICBsZXQgY29sdW1ucyA9IGRhdGEuY29sdW1uc0luZGV4Ow0KICAgICAgICAgICAgICAgIGxldCByb3cgPSAnJzsNCiAgICAgICAgICAgICAgICBpZihpbmRleCA9PT0gLTEgJiYgY29sdW1ucyA9PT0gLTEpew0KICAgICAgICAgICAgICAgICAgICBpbmRleCA9IDA7DQogICAgICAgICAgICAgICAgICAgIGNvbHVtbnMgPSAxOw0KICAgICAgICAgICAgICAgIH1lbHNlIGlmKGluZGV4ID4gLTEgJiYgY29sdW1ucyA9PT0gMTUpew0KICAgICAgICAgICAgICAgICAgICAvL+W9k+i3s+i9rOeahOacgOWQjuS4gOihjOacgOWQjuS4gOagvOeahOaXtuWAmQ0KICAgICAgICAgICAgICAgICAgICBpZiAoaW5kZXggPj0gZGF0YS5wYWdlU2l6ZSAtIDEgfHwgaW5kZXggPj0gZGF0YS5wYWdlVG90YWwgLSAxKSB7DQogICAgICAgICAgICAgICAgICAgICAgICBpbmRleCA9IDA7DQogICAgICAgICAgICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXggKys7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgY29sdW1ucyA9IDE7DQogICAgICAgICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgICAgICAgIGNvbHVtbnMgKz0gMTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgZGF0YS5lZGl0SW5kZXggPSBpbmRleDsNCiAgICAgICAgICAgICAgICBkYXRhLmNvbHVtbnNJbmRleCA9IGNvbHVtbnM7DQogICAgICAgICAgICAgICAgcm93ID0gZGF0YS50YkFjY291bnQuZGF0YVtpbmRleF07DQogICAgICAgICAgICAgICAgaWYocm93KXsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0RmVlU3RhcnREYXRlID0gcm93LmZlZVN0YXJ0RGF0ZTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0Q29hbFVzZUJvZHkgPSByb3cuY29hbFVzZUJvZHk7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZWRpdENvYWxBbW91bnQgPSByb3cuY29hbEFtb3VudCA9PSBudWxsIHx8IHJvdy5jb2FsQW1vdW50PT09MD9udWxsOnJvdy5jb2FsQW1vdW50Ow0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRDb2FsVHlwZSA9IHJvdy5jb2FsSW1wb3J0VHlwZTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0Q29hbFVzZSA9IHJvdy5jb2FsSW1wb3J0VXNlOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRUaWNrZXRNb25leSA9IHJvdy50aWNrZXRNb25leTsNCiAgICAgICAgICAgICAgICAgICAgZGF0YS5lZGl0VGF4VGlja2V0TW9uZXkgPSByb3cudGF4VGlja2V0TW9uZXk7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZWRpdFRheFJhdGUgPSByb3cudGF4UmF0ZVNob3c7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuZWRpdE90aGVyRmVlID0gcm93Lm90aGVyRmVlOw0KICAgICAgICAgICAgICAgICAgICBkYXRhLmVkaXRyZW1hcms9cm93LnJlbWFyazsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7DQogICAgICAgICAgICAgICAgICAgIGRhdGEuJHJlZnNbZGF0YS5lbnRlck9wZXJhdGUoY29sdW1ucykuc3RyK2luZGV4K2NvbHVtbnNdLmZvY3VzKCk7DQogICAgICAgICAgICAgICAgfSwyMDApOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8v5qC55o2u5YiX5Y+36L+U5Zue5a+55bqU55qE5YiX5ZCNDQogICAgICAgICAgICBlbnRlck9wZXJhdGUobnVtYmVyKXsNCiAgICAgICAgICAgICAgICBsZXQgc3RyID0gJyc7DQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSBudWxsOw0KICAgICAgICAgICAgICAgIHN3aXRjaCAobnVtYmVyKSB7DQogICAgICAgICAgICAgICAgICAgIGNhc2UgMjoNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICdjb2FsVXNlQm9keSc7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0Q29hbFVzZUJvZHk7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSAzOg0KICAgICAgICAgICAgICAgICAgICAgICAgc3RyID0gJ2ZlZVN0YXJ0RGF0ZSc7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0RmVlU3RhcnREYXRlOw0KICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgIGNhc2UgNDoNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICdjb2FsQW1vdW50JzsNCiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRDb2FsQW1vdW50Ow0KICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgIGNhc2UgNjoNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICd0aWNrZXRNb25leSc7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0VGlja2V0TW9uZXk7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSA3Og0KICAgICAgICAgICAgICAgICAgICAgICAgc3RyID0gJ3RheFRpY2tldE1vbmV5JzsNCiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRUYXhUaWNrZXRNb25leTsNCiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICBjYXNlIDg6DQogICAgICAgICAgICAgICAgICAgICAgICBzdHIgPSAndGF4UmF0ZVNob3cnOw0KICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdFRheFJhdGU7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSAxMDoNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICdvdGhlckZlZSc7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0T3RoZXJGZWU7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSAxMjoNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICdjb2FsSW1wb3J0VHlwZSc7DQogICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0Q29hbFR5cGU7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgY2FzZSAxMzoNCiAgICAgICAgICAgICAgICAgICAgICAgIHN0ciA9ICdjb2FsSW1wb3J0VXNlJzsNCiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRDb2FsVXNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgICAgICAgIGNhc2UgMTU6DQogICAgICAgICAgICAgICAgICAgICAgICBzdHIgPSAncmVtYXJrJzsNCiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRyZW1hcms7DQogICAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgcmV0dXJuIHtzdHI6c3RyLGRhdGE6ZGF0YX07DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgcHJlZCgpew0KICAgICAgICAgICAgICAgIHZhciBsZXR0ID0gdGhpczsNCiAgICAgICAgICAgICAgICBsZXQgaW5kZXggPSBsZXR0LmVkaXRJbmRleDsNCiAgICAgICAgICAgICAgICBsZXQgY29sdW1ucyA9IGxldHQuY29sdW1uc0luZGV4Ow0KICAgICAgICAgICAgICAgIGlmKGluZGV4ID09PSAtMSAmJiBjb2x1bW5zID09PSAtMSl7DQogICAgICAgICAgICAgICAgICAgIGluZGV4ID0gMDsNCiAgICAgICAgICAgICAgICAgICAgY29sdW1ucyA9IDE7DQogICAgICAgICAgICAgICAgICAgIGxldHQuZWRpdEluZGV4ID0gaW5kZXg7DQogICAgICAgICAgICAgICAgICAgIGxldHQuY29sdW1uc0luZGV4ID0gY29sdW1uczsNCiAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7DQogICAgICAgICAgICAgICAgICAgICAgICBsZXR0LiRyZWZzW2xldHQuZW50ZXJPcGVyYXRlKGNvbHVtbnMpLnN0citpbmRleCtjb2x1bW5zXS5mb2N1cygpOw0KICAgICAgICAgICAgICAgICAgICB9LDIwMCk7DQogICAgICAgICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgICAgICAgIGxldHQudmFsaWRhdGUoKQ0KICAgICAgICAgICAgICAgICAgICBsZXR0LnNldHJlbWFyaygpDQogICAgICAgICAgICAgICAgICAgIGxldHQubmV4dENlbGwobGV0dCkNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZWxsaXBzaXMgKHZhbHVlKSB7DQogICAgICAgICAgICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuICcnDQogICAgICAgICAgICAgICAgaWYgKHZhbHVlLmxlbmd0aCA+IDEwMCkgew0KICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWUuc2xpY2UoMCwxMDApICsgJy4uLicNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaGFuZGxlVXBsb2FkU3VjY2VzcygpIHsNCg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGhhbmRsZUZvcm1hdEVycm9yKGZpbGUpIHsNCiAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygNCiAgICAgICAgICAgICAgICAgICAgZmlsZS5uYW1lICsgIiDmoLzlvI/kuI3mraPnoa7jgILlj6rog73kuIrkvKDlkI7nvIDlkI3kuLogeGxz5oiW6ICFIHhsc3gg55qE5paH5Lu2Ig0KICAgICAgICAgICAgICAgICk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaGFuZGxlUHJvZ3Jlc3MoZXZlbnQsIGZpbGUpIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oew0KICAgICAgICAgICAgICAgICAgICBjb250ZW50OiBmaWxlLm5hbWUgKyAiIOato+WcqOS4iuS8oOOAgiIsDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaGFuZGxlVXBsb2FkU3VjY2VzcygpIHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygi5LiK5Lyg5oiQ5YqfIik7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgLy8g5a+85YWlDQogICAgICAgICAgICBvbkV4Y2VsVXBsb2FkKGZpbGUpIHsNCiAgICAgICAgICAgICAgICBpZiAoIWZpbGUpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kTm90aWNlLmVycm9yKHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn5o+Q56S6JywNCiAgICAgICAgICAgICAgICAgICAgICAgIGRlc2M6ICfor7fpgInmi6nopoHkuIrkvKDnmoTmlofku7bvvIEnLA0KICAgICAgICAgICAgICAgICAgICAgICAgZHVyYXRpb246IDEwDQogICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgbGV0IGZpbGVOYW1lID0gZmlsZS5uYW1lLmxhc3RJbmRleE9mKCIuIik7Ly/lj5bliLDmlofku7blkI3lvIDlp4vliLDmnIDlkI7kuIDkuKrngrnnmoTplb/luqYNCiAgICAgICAgICAgICAgICBsZXQgZmlsZU5hbWVMZW5ndGggPSBmaWxlLm5hbWUubGVuZ3RoOy8v5Y+W5Yiw5paH5Lu25ZCN6ZW/5bqmDQogICAgICAgICAgICAgICAgbGV0IGZpbGVGb3JtYXQgPSBmaWxlLm5hbWUuc3Vic3RyaW5nKGZpbGVOYW1lICsgMSwgZmlsZU5hbWVMZW5ndGgpOy8v5oiqDQogICAgICAgICAgICAgICAgaWYoJ3hscycgIT0gZmlsZUZvcm1hdCAmJiAneGxzeCcgIT0gZmlsZUZvcm1hdCl7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJE5vdGljZS5lcnJvcih7DQogICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogJ+aPkOekuicsDQogICAgICAgICAgICAgICAgICAgICAgICBkZXNjOiBmaWxlLm5hbWUgKyAnIOagvOW8j+S4jeato+ehruOAguWPquiDveS4iuS8oOWQjue8gOWQjeS4uiB4bHPmiJbogIUgeGxzeCDnmoTmlofku7YnLA0KICAgICAgICAgICAgICAgICAgICAgICAgZHVyYXRpb246IDEwDQogICAgICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGxldCBwYXJhbSA9IHt9DQogICAgICAgICAgICAgICAgbGV0IGV4Y2VsID0ge2ZpbGU6IGZpbGV9DQogICAgICAgICAgICAgICAgbGV0IHRoYXQgPSB0aGlzDQogICAgICAgICAgICAgICAgdGhhdC5zcGluU2hvdyA9IHRydWUNCiAgICAgICAgICAgICAgICBheGlvcy5yZXF1ZXN0KHsNCiAgICAgICAgICAgICAgICAgICAgdXJsOiAnL2J1c2luZXNzL2NvYWwvYWNjb3VudC9pbXBvcnQnLA0KICAgICAgICAgICAgICAgICAgICBtZXRob2Q6ICdwb3N0JywNCiAgICAgICAgICAgICAgICAgICAgZGF0YTogT2JqZWN0LmFzc2lnbih7fSwgcGFyYW0sIGV4Y2VsKQ0KICAgICAgICAgICAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICAgICAgICB0aGF0LnNwaW5TaG93ID0gZmFsc2UNCiAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLnN0cikgew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC4kTWVzc2FnZS5pbmZvKHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiByZXMuZGF0YS5zdHIsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgZHVyYXRpb246IDAsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xvc2FibGU6IHRydWUNCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOw0KDQogICAgICAgICAgICAgICAgICAgICAgICB0aGF0LnNob3cgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgICAgfQ0KIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5zcGluU2hvdyA9IGZhbHNlDQogICAgICAgICAgICAgICAgICAgIHRoYXQuc2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlcnIpOw0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICAgICAgfSwNCg0KICAgICAgICAgICAgLy8g5a+85YWl5qih5p2/5LiL6L29DQogICAgICAgICAgICBsb2FkVGVtcGxhdGUoKSB7DQogICAgICAgICAgICAgICAgbGV0IHJlcSA9IHsNCiAgICAgICAgICAgICAgICAgICAgdXJsIDogIi9idXNpbmVzcy9jb2FsL2FjY291bnQvdGVtcGxhdGUvbG9hZCIsDQogICAgICAgICAgICAgICAgICAgIG1ldGhvZCA6ICJnZXQiLA0KICAgICAgICAgICAgICAgICAgICByZXNwb25zZVR5cGU6ICdibG9iJywNCiAgICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgICAgIGF4aW9zLmZpbGUocmVxKQ0KICAgICAgICAgICAgICAgICAgICAudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zcGluU2hvdyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgY29udGVudCA9IHJlczsNCiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbY29udGVudF0pOw0KICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSAi55So54Wk5Y+w6LSm5a+85YWl5qih5p2/Lnhsc3giOw0KICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCJkb3dubG9hZCIgaW4gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpKSB7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g6Z2eSUXkuIvovb0NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBlbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImEiKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbGluay5kb3dubG9hZCA9IGZpbGVOYW1lOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsaW5rLnN0eWxlLmRpc3BsYXkgPSAibm9uZSI7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxpbmsuaHJlZiA9IFVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChlbGluayk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxpbmsuY2xpY2soKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBVUkwucmV2b2tlT2JqZWN0VVJMKGVsaW5rLmhyZWYpOyAvLyDph4rmlL5VUkwg5a+56LGhDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChlbGluayk7DQogICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIElFMTAr5LiL6L29DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgbmF2aWdhdG9yLm1zU2F2ZUJsb2IoYmxvYiwgZmlsZU5hbWUpOw0KICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBtb3VudGVkKCkgew0KICAgICAgICAgICAgdGhpcy52ZXJzaW9uID0gaW5kZXhEYXRhLnZlcnNpb24NCiAgICAgICAgICAgIHRoaXMudGJBY2NvdW50LmNvbHVtbnMgPSB0aGlzLnRiQWNjb3VudC50YWlsQ29sdW1uOw0KICAgICAgICAgICAgdGhpcy5jb2FsVHlwZXMgPSBibGlzdDEoImNvYWxUeXBlIik7DQogICAgICAgICAgICB0aGlzLmNvYWxVc2VUeXBlcyA9IGJsaXN0MSgiY29hbFVzZVR5cGUiKTsNCiAgICAgICAgICAgIHRoaXMuYWNjb3VudE9iai5jb2FsVXNlVHlwZSA9IHRoaXMuY29hbFVzZVR5cGVzWzBdLnR5cGVDb2RlDQogICAgICAgICAgICB0aGlzLmFjY291bnRPYmouY29hbFR5cGUgPSB0aGlzLmNvYWxUeXBlc1swXS50eXBlQ29kZQ0KICAgICAgICAgICAgbGV0IHRoYXQgPSB0aGlzDQogICAgICAgICAgICBnZXRVc2VyQnlVc2VyUm9sZSgpLnRoZW4ocmVzID0+IHsvL+agueaNruadg+mZkOiOt+WPluWIhuWFrOWPuA0KICAgICAgICAgICAgICAgIHRoYXQuY29tcGFuaWVzID0gcmVzLmRhdGEuY29tcGFuaWVzOw0KICAgICAgICAgICAgICAgIGlmKHJlcy5kYXRhLmlzQ2l0eUFkbWluID09IHRydWUgfHwgcmVzLmRhdGEuaXNQcm9BZG1pbiA9PSB0cnVlIHx8IHJlcy5kYXRhLmlzU3ViQWRtaW4gPT0gdHJ1ZSl7DQogICAgICAgICAgICAgICAgICAgIHRoYXQuaXNBZG1pbiA9IHRydWU7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGdldENvdW50cnlzZGF0YSh7b3JnQ29kZTpyZXMuZGF0YS5jb21wYW5pZXNbMF0uaWR9KS50aGVuKHJlcyA9PiB7Ly/moLnmja7mnYPpmZDojrflj5bmiYDlsZ7pg6jpl6gNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5kZXBhcnRtZW50cyA9IHJlcy5kYXRhOw0KICAgICAgICAgICAgICAgICAgICB0aGF0LmdldFVzZXJEYXRhKCk7DQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgLy8gdGhpcy50YkFjY291bnQudGFpbENvbHVtbiA9IHRoaXMudGJBY2NvdW50LnRhaWxDb2x1bW4uY29uY2F0KHRoaXMudGJBY2NvdW50LmZpbGVDb2x1bW4pDQogICAgICAgICAgICAgICAgLy8gdGhpcy50YkFjY291bnQudGFpbENvbHVtbi5wdXNoKHRoaXMudGJBY2NvdW50LmZpbGVDb2x1bW5bMF0pDQogICAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgIH0NCg=="}, {"version": 3, "sources": ["addCoalAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addCoalAccount.vue", "sourceRoot": "src/view/account", "sourcesContent": ["<style lang=\"less\">\r\n    .mytable .ivu-table-cell{\r\n        padding-left: 5px;\r\n        padding-right: 5px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: normal;\r\n        word-break: break-all;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .accountEs .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .accountEs .header-bar-show {\r\n        max-height: 300px;\r\n        padding-top: 14px;\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .accountEs .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n\r\n\r\n    .mytable .myspan{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block\r\n    }\r\n    .mytable .errorStle{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block;\r\n        color:red;\r\n    }\r\n</style>\r\n<template>\r\n    <div>\r\n        <div class=\"accountEs\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n                 <!-- @on-change='accountnoChange' -->\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.accountno\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{ item.name }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"关键字:\" prop=\"projectName\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"accountObj.coalUseBody\" placeholder=\"用能主体关键字模糊查询\" :style=\"formItemWidth\" />\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"用煤类型:\" prop=\"coalType\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.coalType\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in coalTypes\" :value=\"item.typeCode\" :key=\"item.typeCode\">{{ item.typeName }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"用途:\" prop=\"coalUseType\" class=\"form-line-height\">\r\n                                <Select clearable v-model=\"accountObj.coalUseType\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in coalUseTypes\" :value=\"item.typeCode\" :key=\"item.typeCode\">{{ item.typeName }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.company\" @on-change=\"selectChange(accountObj.company)\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"accountObj.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly :style=\"formItemWidth\"/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <div  align=\"right\">\r\n                        <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n                        <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n                    </div>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <div>\r\n                <Row>\r\n                    <Col span=\"12\">\r\n                        <Page size=\"small\" :total=\"pageTotal\" :current=\"pageNum\" :page-size=\"pageSize\" show-elevator show-sizer show-total\r\n                              placement=\"top\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n                    </Col>\r\n                    <Col span=\"12\">\r\n                        <div align=\"right\" class=\"account\">\r\n                            <Button type=\"primary\" @click=\"addNewCoalAccount\">新增</Button>\r\n                            <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n                            <Button type=\"error\" @click=\"remove\">删除</Button>\r\n                            <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n                                <Button type='info' style=\"margin-left: 5px\">加入归集单\r\n                                    <Icon type='ios-arrow-down'></Icon>\r\n                                </Button>\r\n                                <DropdownMenu slot='list'>\r\n                                    <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n                                    <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n                                </DropdownMenu>\r\n                            </Dropdown>\r\n                            <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n                            <Button type=\"primary\" @click=\"loadTemplate\">导入模板下载</Button>\r\n                            <Upload style=\"float:right;\" :on-format-error=\"handleFormatError\"\r\n                                    :before-upload='onExcelUpload' :on-progress=\"handleProgress\"\r\n                                    :on-success=\"handleUploadSuccess\" :max-size=\"10240\" action=\"_blank\"\r\n                                    accept=\".csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\r\n                                    :format=\"['xls','xlsx']\">\r\n                                <Button icon='ios-cloud-upload'>导入</Button>\r\n                            </Upload>\r\n                        </div>\r\n                    </Col>\r\n                </Row>\r\n            </div>\r\n            <Table ref=\"accountEsTable\"\r\n                   border\r\n                   :columns=\"tbAccount.tailColumn\"\r\n                   :data=\"tbAccount.data\"\r\n                   :key=\"tbAccount.dispKey\"\r\n                   class=\"mytable\">\r\n                <!--备注-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=500 v-model=\"editremark\" :ref=\"'remark'+index+15\" type=\"text\" @on-blur=\"setremark\"\r\n                               v-if=\"editIndex === index && columnsIndex === 15\"/>\r\n                        <Tooltip placement=\"left\" max-width=\"600\" v-else>\r\n                            <span :class=\"myStyle[index].remark\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,15,'remark')\">{{ ellipsis(row.remark) }}</span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                    <div v-else>\r\n                        <Tooltip placement=\"bottom\" max-width=\"200\">\r\n                            <span>{{ ellipsis(row.remark)}}</span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--用能主体--><!-- {{ ellipsis(row.coalUseBody) }} -->\r\n                <template slot-scope=\"{ row, index }\" slot=\"coalUseBody\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=100 v-model=\"editCoalUseBody\" :ref=\"'coalUseBody'+index+2\" type=\"text\" max=\"\" @on-blur=\"setCoalUseBody\"\r\n                               v-if=\"editIndex === index && columnsIndex === 2\"/>\r\n                        <Tooltip placement=\"right\" max-width=\"200\" v-else>\r\n                            <span :class=\"myStyle[index].coalUseBody\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,2,'coalUseBody')\">\r\n\r\n                                {{ ellipsis(row.coalUseBody) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.coalUseBody }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--费用发生日-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"feeStartDate\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :ref=\"'feeStartDate'+index+3\" type=\"text\" v-model=\"editFeeStartDate\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 3\" />\r\n                        <span :class=\"myStyle[index].feeStartDate\" style=\"display: inline-block; width: 90px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,3,'feeStartDate')\" v-else>{{ row.feeStartDate }}</span>\r\n                    </div>\r\n                    <div v-else>\r\n                        <span>{{ row.feeStartDate }}</span>\r\n                    </div>\r\n                </template>\r\n                <!--煤炭数量-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"coalAmount\">\r\n                    <Input :ref=\"'coalAmount'+index+4\" type=\"text\" v-model=\"editCoalAmount\" @on-change=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 4\" />\r\n                    <span :class=\"myStyle[index].coalAmount\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,4,'coalAmount')\" v-else>{{ row.coalAmount }}</span>\r\n                </template>\r\n                <!--普票含税金额-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"ticketMoney\">\r\n                    <Input :ref=\"'ticketMoney'+index+6\" type=\"text\" v-model=\"editTicketMoney\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 6\"/>\r\n                    <span :class=\"myStyle[index].ticketMoney\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,6,'ticketMoney')\" v-else>{{ row.ticketMoney }}</span>\r\n                </template>\r\n                <!--专票含税金额-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"taxTicketMoney\">\r\n                    <Input :ref=\"'taxTicketMoney'+index+7\" type=\"text\" v-model=\"editTaxTicketMoney\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 7\" />\r\n                    <span :class=\"myStyle[index].taxTicketMoney\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,7,'taxTicketMoney')\" v-else>{{ row.taxTicketMoney }}</span>\r\n                </template>\r\n                <!--专票税率-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"taxRateShow\">\r\n\r\n                    <div v-if=\"row.total == null\">\r\n                    <Select :ref=\"'taxRateShow'+index+8\" type=\"text\" v-model=\"editTaxRate\" @on-change=\"settaxrate\"\r\n                            v-if=\"editIndex === index && columnsIndex === 8\" transfer=\"true\">\r\n                        <Option value=\"1\">1</Option>\r\n                        <Option value=\"3\">3</Option>\r\n                        <Option value=\"6\">6</Option>\r\n                        <Option selected value=\"13\">13</Option>\r\n                        <Option value=\"16\">16</Option>\r\n                        <Option value=\"17\">17</Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].taxRateShow\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,8,'taxRateShow')\"\r\n                    v-else>{{ row.taxRateShow }}</span>\r\n                </div>\r\n                    <div v-else>\r\n                        <span>{{ row.taxRateShow }}</span>\r\n                    </div>\r\n                </template>\r\n                <!--其他费用-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"otherFee\">\r\n                    <Input :ref=\"'otherFee'+index+10\" type=\"text\" v-model=\"editOtherFee\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 10\" />\r\n                    <span :class=\"myStyle[index].otherFee\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,10,'otherFee')\" v-else>{{ row.otherFee }}</span>\r\n                </template>\r\n                <!--类型-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"coalImportType\">\r\n                    <Select :ref=\"'coalImportType'+index+12\" type=\"text\" v-model=\"editCoalType\" @on-change=\"setCoalType\"\r\n                            v-if=\"editIndex === index && columnsIndex === 12\" transfer=\"true\">\r\n                        <Option value=\"煤炭\" label=\"煤炭\"></Option>\r\n                        <Option value=\"焦炭\" label=\"焦炭\"></Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].coalImportType\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,12,'coalImportType')\" v-else>{{ row.coalImportType }}</span>\r\n                </template>\r\n                <!--用途-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"coalImportUse\"\r\n                          v-if=\"row.total == null\">\r\n                    <Select :ref=\"'coalImportUse'+index+13\" type=\"text\" v-model=\"editCoalUse\" @on-change=\"setCoalUse\"\r\n                            v-if=\"editIndex === index && columnsIndex === 13\" transfer=\"true\">\r\n                        <Option value=\"发电用煤\" label=\"发电用煤\"></Option>\r\n                        <Option value=\"取暖用煤\" label=\"取暖用煤\"></Option>\r\n                        <Option value=\"其他\" label=\"其他\"></Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].coalImportUse\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,13,'coalImportUse')\" v-else>{{ row.coalImportUse }}</span>\r\n                </template>\r\n            </Table>\r\n            <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n        </div>\r\n        <div>\r\n            <add-bill-per ref=\"addBillPer\"\r\n                          v-on:refreshList=\"refresh\"></add-bill-per >\r\n            <completed-pre-modal ref=\"completedPre\" v-on:refreshList=\"refresh\"></completed-pre-modal>\r\n            <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n            <upload-file-modal ref=\"uploadFileModal\" v-on:onchange=\"change\"></upload-file-modal>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n// import {\r\n//   addAmmeter,\r\n//   listElectricType,\r\n//   editAmmeter,\r\n//   editAmmeterRecord,\r\n//   updateAmmeter,\r\n//   checkProjectNameExist,\r\n//   checkAmmeterByStation,\r\n//   getClassification,\r\n//   getClassificationId,\r\n//   getUserdata,\r\n//   checkClassificationLevel,\r\n//   listElectricTypeRatio,\r\n//   checkAmmeterExist,\r\n//   getUserByUserRole,\r\n//   getCountryByUserId,\r\n//   getCountrysdata, removeAttach, attchList, getBankCard\r\n// } from '@/api/basedata/ammeter.js'\r\nimport {\r\n    _verify_StartDate,\r\n    judgeNumber,\r\n    _verify_EndDate,\r\n    _verify_PrevTotalReadings,\r\n    _verify_CurTotalReadings,\r\n    other_no_ammeteror_protocol,\r\n    self_no_ammeteror_protocol,\r\n    HFL_ammeteror,\r\n    judging_editability,\r\n    judging_editability1,\r\n    _verify_Money,\r\n    _calculateUsedReadings,\r\n    _calculateTotalReadings,\r\n    _calculateUnitPriceByUsedMoney,\r\n    _calculateAccountMoney,\r\n    _calculateQuotereadingsratio,\r\n    requiredFieldValidator,\r\n    countTaxamount1,\r\n    countTaxamount,\r\n    calculateActualMoney,\r\n    judge_negate,\r\n    judge_recovery,\r\n    judge_yb,\r\n    unitpirceMin,\r\n    unitpirceMax,\r\n    unitpirceMax1\r\n} from '@/view/account/PowerAccountController';\r\n    import {\r\n        saveCoalAccount,\r\n        removeCoalAccount,\r\n        selectCoalIds\r\n    } from '@/api/coalHeatOilAccount';\r\n    import {verification} from '@/view/account/coalAccount';\r\n    import checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\n    import checkResult from \"@/view/account/check/checkResult\";\r\n    import alarmCheck from \"@/view/account/check/alarmCheck\";\r\n    import {getDates,testNumber,} from '@/view/account/powerAccountHelper';\r\n    import axios from '@/libs/api.request';\r\n    import SelectAmmeter from \"./selectAmmeter\";\r\n    import {_verify_FeeStartDate} from '@/view/account/PowerAccountEs';\r\n    import AddBillPer from \"./addCoalBillPreModal\";\r\n    import {reJoinBillpre} from '@/api/accountBillPer';\r\n    import {blist1} from \"@/libs/tools\";\r\n    import {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\n    import CompletedPreModal from \"./completedPreModal\";\r\n    import indexData from '@/config/index'\r\n    import CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import { getUserdata, getUserByUserRole, getCountrysdata, getCountryByUserId, editAmmeter\r\n\r\n    } from '@/api/basedata/ammeter.js'\r\n    import UploadFileModal from \"@/view/account/uploadFileModal\";\r\n    let dates=getDates();\r\n    export default {\r\n        name: 'addCoalAccount',\r\n        components: {UploadFileModal, alarmCheck, checkResult, checkResultAndResponse,CompletedPreModal, SelectAmmeter,AddBillPer,CountryModal},\r\n        data() {\r\n            let photo = (h, {row, index}) => {\r\n                let that = this\r\n                let str = '上传附件'\r\n                return h(\"div\", [h(\"u\", {\r\n                    on: {\r\n                        click() {\r\n                            //打开弹出框\r\n                            // if (row.id) {\r\n                                that.uploadFile(row)\r\n                            // }\r\n                        }\r\n                    }\r\n                }, str)]);\r\n            };\r\n            return {\r\n                id2: \"\",\r\n                fileParam:{\r\n                    busiId:\"\",\r\n                    busiAlias:\"附件(协议管理)\",\r\n                    categoryCode:\"file\",\r\n                    areaCode:\"ln\"\r\n                },\r\n                submit:[],\r\n                submit2:[],\r\n                formItemWidth: widthstyle,\r\n                version:'',\r\n                dateList:dates,\r\n                filterColl: true,//搜索面板展开\r\n                editIndex: -1,//当前编辑行\r\n                columnsIndex:-1,//当前编辑列\r\n                myStyle:[],//样式\r\n                editCoalUseBody:'',\r\n                editFeeStartDate:'',\r\n                editCoalAmount:'',\r\n                editTicketMoney:'',\r\n                editTaxTicketMoney:'',\r\n                editTaxRate:'',\r\n                editOtherFee:'',\r\n                editCoalUse:'',\r\n                editCoalType:'',\r\n                spinShow:false,//遮罩\r\n                editremark:'',\r\n                companies:[],\r\n                coalTypes: [],\r\n                coalUseTypes: [],\r\n                departments:[],\r\n                isAdmin:false,\r\n                company:null,//用户默认公司\r\n                country:null,//用户默认所属部门\r\n                countryName:null,//用户默认所属部门\r\n                accountObj:{\r\n                    accountno:dates[1].code,//期号,默认当前月\r\n                    company:\"\",//分公司\r\n                    country:\"\",//所属部门\r\n                    coalUseBody: \"\",//用煤主体\r\n                    coalUseType:1,\r\n                    coalType:1,\r\n                    feeStartDate: \"\",\r\n                    countryName: \"\",\r\n\r\n                },\r\n                tbAccount: {\r\n                    dispKey: 0,\r\n                    loading: true,\r\n                    columns: [],\r\n                    tailColumn: [\r\n                        {type: 'selection', width: 60, align: 'center',},\r\n                        {\r\n                            title: \"期号\",\r\n                            key: \"accountNo\",\r\n                            align: \"center\",\r\n                            width: 90,\r\n                        },\r\n                        {\r\n                            title: \"用能主体\",\r\n                            slot: \"coalUseBody\",\r\n                            // key: \"coalUseBody\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"费用发生日\",\r\n                            slot: \"feeStartDate\",\r\n                            // key: \"feeStartDate\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"煤炭用量(t)\",\r\n                            slot: \"coalAmount\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"单价(元/吨)\",\r\n                            key: \"unitPrice\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"普票含税金额(元)\",\r\n                            slot: \"ticketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票含税金额(元)\",\r\n                            slot: \"taxTicketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票税率（%）\",\r\n                            slot: \"taxRateShow\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票税额\",\r\n                            key: \"taxAmount\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"其他(元)\",\r\n                            // key: \"otherFee\",\r\n                            slot: \"otherFee\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"实缴费用(元)含税\",\r\n                            key: \"paidMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"类型\",\r\n                            slot: \"coalImportType\",\r\n                            align: \"center\",\r\n                            width: 60,\r\n                        },\r\n                        {\r\n                            title: \"用途\",\r\n                            slot: \"coalImportUse\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        // {title: \"附件\", slot: 'file', align: \"center\", render: photo, width: 100},\r\n                        {title: \"备注\", slot: \"remark\",align: \"center\", width: 150},{\r\n                        title: '附件',\r\n                        slot: 'file',\r\n                        align: 'center',\r\n                        width: 60,\r\n                        // fixed: 'right',\r\n                        render: photo\r\n                    }\r\n                    ],\r\n                    fileColumn: [{\r\n                        title: '附件',\r\n                        slot: 'file',\r\n                        align: 'center',\r\n                        width: 60,\r\n                        // fixed: 'right',\r\n                        render: photo\r\n                    },],\r\n                    data: []\r\n                },\r\n                pageTotal: 0,\r\n                pageNum: 1,\r\n                pageSize: 10,//当前页\r\n            }\r\n        },\r\n        methods: {\r\n            uploadFile(row) {\r\n                console.log(row, \"row\");\r\n                // let id;\r\n                // if(!row.id2) {\r\n                //     editAmmeter('', 0).then(res => {\r\n                //         console.log(res, \"res\");\r\n                //         row.id2 = res.data.id;\r\n\r\n                //         this.id2 = res.data.id\r\n                //         // this.fileParam.busiId = ;\r\n                //         this.$refs.uploadFileModal.choose(row.id2 + '');\r\n                //     })\r\n                // }else {\r\n\r\n                if(row.id) {\r\n                    this.$refs.uploadFileModal.choose(row.id + '');\r\n                }else {\r\n                    this.errorTips(\"请先保存后再上传文件！\");\r\n                }\r\n                // }\r\n                // console.log(row, \"row\");\r\n            },\r\n          change() {\r\n            // this.getAccountMessages();\r\n          },\r\n            selectChange(){\r\n                let that = this;\r\n                if (that.accountObj.company != undefined) {\r\n                    if(that.accountObj.company == \"-1\"){\r\n                        that.accountObj.country = -1;\r\n                        that.accountObj.countryName = null;\r\n                    }else{\r\n                        getCountryByUserId(that.accountObj.company).then(res => {\r\n                            if(res.data.departments.length != 0){\r\n                                that.accountObj.country = res.data.departments[0].id;\r\n                                that.accountObj.countryName = res.data.departments[0].name;\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            },\r\n            //选择所属部门开始\r\n            chooseResponseCenter() {\r\n                if(this.accountObj.company == null || this.accountObj.company == \"-1\" ){\r\n                    this.$Message.info(\"请先选择分公司\");return;\r\n                }\r\n                this.$refs.countryModal.choose(this.accountObj.company);//所属部门\r\n            },\r\n            getDataFromModal(data) {\r\n                this.accountObj.country = data.id;\r\n                this.accountObj.countryName = data.name;\r\n                //选择所属部门结束\r\n            },\r\n            getUserData(){\r\n                let that = this;\r\n                getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                    if(res.data.companies.length != 0){\r\n                        let companies = res.data.companies;\r\n                        if(res.data.companies[0].id == \"**********\"){\r\n                            companies = that.companies;\r\n                        }\r\n                        that.company = companies[0].id;\r\n                        that.accountObj.company = companies[0].id;\r\n                    }\r\n                    if(res.data.departments.length != 0){\r\n                        let departments = res.data.departments;\r\n                        if(res.data.companies[0].id == \"**********\" && that.departments.length != 0){\r\n                            departments = that.departments\r\n                        }\r\n                        that.country = departments[0].id;\r\n                        that.countryName = departments[0].name;\r\n                        that.accountObj.country = Number(departments[0].id);\r\n                        that.accountObj.countryName = departments[0].name;\r\n                    }\r\n\r\n                    that.pageNum = 1\r\n                    that.getAccountMessages();\r\n                });\r\n            },\r\n            searchList(){\r\n                if(this.accountObj.countryName == \"\"){\r\n                    this.accountObj.country = \"-1\";\r\n                }\r\n                this.pageNum = 1\r\n                this.getAccountMessages()\r\n            },\r\n            accountnoChange(){\r\n                this.searchList()\r\n            },\r\n            //点击保存\r\n            preserve() {\r\n                let dataL = this.$refs.accountEsTable.getSelection();\r\n                console.log(\"dataL\", dataL);\r\n                let b = false;\r\n                let array = [];\r\n                for (let i = 0; i < dataL.length; i ++) {\r\n                    b = true;\r\n                    array.push(dataL[i])\r\n                }\r\n                if(b){\r\n                    this.submitData(array);\r\n                }else {\r\n                    this.errorTips('没有可保存数据')\r\n                }\r\n            },\r\n            submitChange(indexList){\r\n                let data=[];\r\n                this.submit2.map((item,index)=>{\r\n                    indexList.map((item2)=>{\r\n                        if(index==item2){\r\n                            data.push(item)\r\n                        }\r\n                    })\r\n                });\r\n                this.submit=data\r\n            },\r\n\r\n            //提交数据\r\n            submitData(data){\r\n                console.log(data, \"data\");\r\n                let a = [];\r\n                let that=this;\r\n                if(data != null && data.length > 0){\r\n                    let number = 0;\r\n                    let submitData = [];\r\n                    let str = '';\r\n                    let accountno = this.accountObj.accountno;\r\n                    data.forEach(function (item) {\r\n                        // 校验数据\r\n                        let obj = verification(item);\r\n                        if (obj.result) {\r\n                            if(item.id == null){\r\n                                item.accountno = accountno\r\n                            }\r\n                            a.push(item.id);\r\n                            submitData.push(item);\r\n                            number ++;\r\n                         }else{\r\n                             str += '台账号为【' +item.id + '】的台账验证没有通过：【' + obj.str + '】；';\r\n                         }\r\n                    });\r\n                    that.ids=a;\r\n                    if(str.length > 0){\r\n                        this.errorTips(str)\r\n                    }\r\n                    if(submitData.length > 0){\r\n                        saveCoalAccount(submitData).then((res) => {\r\n                             if (res.data.code == 0) {\r\n                                this.$Message.info({\r\n                                    content: '提示：成功保存 ' + res.data.num + ' 条数据',\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                             }\r\n                        })\r\n                    }\r\n                }\r\n            },\r\n            addNewCoalAccount() {\r\n                console.log(this.accountObj.accountno, \"this.accountObj.accountno\");\r\n                const currentDate = new Date();\r\n                const currentYear = currentDate.getFullYear();\r\n                const currentMonth = currentDate.getMonth() + 1;\r\n                if (null == this.tbAccount.data) {\r\n                    this.tbAccount.data = [];\r\n                }\r\n                this.tbAccount.dispKey++;\r\n                this.tbAccount.data.unshift({\r\n                    // accountNo:dates[1].code,\r\n                    accountNo: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined) ? currentYear+\"\"+currentMonth: this.accountObj.accountno,\r\n                    coalUseBody: \"\",\r\n                    feeStartDate:\"\",\r\n                    coalAmount: 0,\r\n                    unitPrice: 0,\r\n                    ticketMoney:0,\r\n                    taxTicketMoney:0,\r\n                    taxAmount: 0,\r\n                    otherFee: 0,\r\n                    paidMoney:0,\r\n                    // taxRateShow: 1,\r\n                    // coalImportType:\"煤炭\",\r\n                    // coalImportUse:\"发电用煤\",\r\n                    taxRateShow: \"\",\r\n                    coalImportType:\"\",\r\n                    coalImportUse:\"\",\r\n                    remark:\"\",\r\n                });\r\n                this.myStyle.push({\r\n                        startdate: 'myspan',\r\n                        enddate: 'myspan',\r\n                        prevtotalreadings: 'myspan',\r\n                        curtotalreadings: 'myspan',\r\n                        transformerullage: 'myspan',\r\n                        inputtaxticketmoney: 'myspan',\r\n                        inputticketmoney: 'myspan',\r\n                        ullagemoney: 'myspan',\r\n                        taxrate: 'myspan',\r\n                        tickettaxamount: 'myspan',\r\n                        remark: 'myspan',\r\n                        ticketMoney:\"myspan\",\r\n                        taxTicketMoney:\"myspan\",\r\n\r\n                    });\r\n            },\r\n            //验证错误弹出提示框\r\n            errorTips(str){\r\n                this.$Notice.error({\r\n                    title: '提示',\r\n                    desc: str,\r\n                    duration: 10\r\n                });\r\n            },\r\n            handlePage(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data;\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageNum = value;\r\n                this.getAccountMessages();\r\n            },\r\n            handlePageSize(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data;\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageSize = value;\r\n                this.getAccountMessages();\r\n            },\r\n            //向后台请求数据\r\n            getAccountMessages() {\r\n                const postData = this.accountObj;\r\n                postData.pageNum = this.pageNum;\r\n                postData.pageSize = this.pageSize;\r\n                let req = {\r\n                    url : \"/business/coal/account/list\",\r\n                    method : \"get\",\r\n                    params : postData\r\n                };\r\n                this.tbAccount.loading = true\r\n                axios.request(req).then(res => {\r\n                    this.tbAccount.loading = false\r\n                    if (res.data) {\r\n                        let data = res.data.rows;\r\n                        data.forEach(function (item) {\r\n                            item.editType = 0;\r\n                        });\r\n                        this.tbAccount.data = data;\r\n                        this.pageTotal = res.data.total || 0;\r\n                        this.setMyStyle(this.tbAccount.data.length);\r\n\r\n                        this.editIndex = -1;\r\n                        this.columnsIndex = -1;\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            },\r\n            //重置\r\n            onResetHandle(){\r\n                this.accountObj = {\r\n                    accountno:null,\r\n                    company:this.company,\r\n                    coalUseBody:null,\r\n                    country:Number(this.country),\r\n                    coalUseType:null,\r\n                    coalType:null,\r\n                }\r\n                this.getAccountMessages()\r\n            },\r\n            //计算单价\r\n            unitPrice(row){\r\n                let ticketMoney = row.ticketMoney;\r\n                let taxTicketMoney = row.taxTicketMoney;\r\n                let coalAmount = row.coalAmount;\r\n                if(ticketMoney != null || taxTicketMoney != null){\r\n                    let total = null;\r\n                    total = ticketMoney + taxTicketMoney;\r\n                    row.unitPrice = total/coalAmount.toFixed(2);\r\n                }\r\n            },\r\n            remove(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                if(data == null || data.length === 0){\r\n                    this.errorTips(\"请选择要删除的数据\")\r\n                    return;\r\n                }\r\n                this.$Modal.confirm({\r\n                    title: '提示',\r\n                    content: '<p>是否确认删除选中信息？</p>',\r\n                    onOk: () => {\r\n                        let b = true;\r\n                        let ids = '';\r\n                        let total = this.pageTotal\r\n                        for(let i=0;i<data.length;i++){\r\n                            let item = data[i];\r\n                            if(item.id != null && item.id.length > 0){\r\n                                if(item.pabriid){\r\n                                    b = false;\r\n                                }\r\n                                ids += item.id + ',';\r\n                            }\r\n                        }\r\n                        this.pageTotal = total\r\n                        if(b){\r\n                            if(ids.length > 0){\r\n                                removeCoalAccount(ids).then((res) => {\r\n                                    if (res.data.code == 0) {\r\n                                        this.$Message.success('删除成功');\r\n                                        this.getAccountMessages();\r\n                                    }\r\n                                });\r\n                            }\r\n                        }else {\r\n                            this.errorTips('选中信息中有信息还没有跟归集单解除关联，请先解除关联')\r\n                        }\r\n                    },\r\n                    onCancel: () => {\r\n                    }\r\n                });\r\n            },\r\n            openAddBillPerModal(name) {\r\n                if (name === 'current') {\r\n                    this.selectedAccount()\r\n                } else if (name === 'all') {\r\n                    this.selectedAllAccount()\r\n                }\r\n            },\r\n            //加入归集单，全部有效台账\r\n            selectedAllAccount(){\r\n                let that = this\r\n                that.spinShow = true;\r\n                selectCoalIds(this.accountObj).then(res => {\r\n                    that.spinShow = false;\r\n                    if(res.data.length == 0){\r\n                        that.errorTips('无有效数据可加入归集单')\r\n                    }else {\r\n                        let ids = [];\r\n                        for(let i=0;i<res.data.rows.length;i++){\r\n                            let item = res.data.rows[i];\r\n                            ids.push(item.id)\r\n                        }\r\n                        that.$refs.addBillPer.initAmmeter(ids, 20,this.accountObj.country);\r\n                    }\r\n                });\r\n            },\r\n            selectedAccount(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = 1;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要加入归集单的台账')\r\n                } else {\r\n                    let ids = [];\r\n                    data.forEach(function (item) {\r\n                        if(item.status === 5){\r\n                            b = 3\r\n                        }\r\n                        if(item.status === 4){\r\n                            b=4;\r\n                        }\r\n                        ids.push(item.id)\r\n                    });\r\n                    if(b === 1){\r\n                        this.$refs.addBillPer.initAmmeter(ids,20,this.accountObj.country);\r\n                    }else if(b === 2) {\r\n                        this.errorTips('选中的台账中存在临时数据，请先保存再加入归集单！')\r\n                    }else if(b===3){\r\n                        this.errorTips('退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮')\r\n                    }else if(b===4){\r\n                        this.errorTips('选择的台账有已加入归集单的台账，不能加入其他归集单')\r\n                    }\r\n                }\r\n            },\r\n            openCompletedPreModal(){\r\n                this.$refs.completedPre.initAmmeter(this.accountObj.country,2);\r\n            },\r\n            againJoin(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = true;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要重新加入归集单的台账')\r\n                } else {\r\n                    let ids = '';\r\n                    data.forEach(function (item) {\r\n                        let status = item.status;\r\n                        if(status != 5){\r\n                            b = false;\r\n                        }\r\n                        ids+= item.id +','\r\n                    });\r\n                    if(b){\r\n                        reJoinBillpre(ids).then((res) =>{\r\n                            if(res.data.code==0){\r\n                                this.$Message.info({\r\n                                    content:'提示：操作成功' ,\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }else {\r\n                        this.errorTips('只有已退回的台账才能重新加入归集单')\r\n                    }\r\n                }\r\n            },\r\n            refresh(){\r\n                let obj = this\r\n                setTimeout(function () {\r\n                    obj.getAccountMessages()\r\n                },200);\r\n            },\r\n            validate(){\r\n                let val = this.enterOperate(this.columnsIndex).data;\r\n                if(val) {\r\n                    switch (this.columnsIndex) {\r\n                        case 3:\r\n                            this.validateFeeStartDate();\r\n                            break;\r\n                        case 4:\r\n                            this.validateCoalAmount();\r\n                            break;\r\n                        case 6:\r\n                            this.validateTicketMoney();\r\n                            break;\r\n                        case 7:\r\n                            this.validateTaxTicketMoney();\r\n                            break;\r\n                        case 10:\r\n                            this.validateOtherMoney();\r\n                            break;\r\n                    }\r\n                }\r\n            },\r\n            validateFeeStartDate(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editFeeStartDate;\r\n                let result = _verify_FeeStartDate(data,val);\r\n                if(result){//失败就弹出提示内容，并将数据恢复初始化\r\n                    this.errorTips(result)\r\n                }else{\r\n                    data.feeStartDate = val;\r\n                    data.editType = 1;\r\n                }\r\n            },\r\n            validateCoalAmount(){\r\n                console.log(this.editCoalAmount, \"this.editCoalAmount\");\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editCoalAmount;\r\n                if (!testNumber(val)) {\r\n                    this.errorTips('请输入数字！');\r\n                }\r\n                // if (testNumber(val) == 0) {\r\n                //     this.errorTips('请输入数字！');\r\n                // }\r\n                data.coalAmount = val;\r\n                data.editType = 1;\r\n                let unitPrice = data.paidMoney?(data.paidMoney/data.coalAmount):0;\r\n                data.unitPrice = unitPrice.toFixed(2);\r\n\r\n\r\n            },\r\n            //验证单价\r\n            validateUnitPrice(data){\r\n                let category = data.category;//电表描述类型\r\n                let ammeteruse = data.ammeteruse;//电表用途\r\n                let unitpirce  = data.unitPrice;//台账单价\r\n                if(!judge_negate(category) && !judge_recovery(ammeteruse) && judge_yb(category)){\r\n                    // if(unitPrice){\r\n                    //     if(unitPrice < unitpirceMin || unitPrice > unitpirceMax){\r\n                    //         this.errorTips('集团要求单价范围在0.3~2元，此台账单价: '+ unitPrice +' 已超过范围，请确认！')\r\n                    //     }\r\n                    // }\r\n                    if (unitpirce) {\r\n                        if (unitpirce != null && unitpirce < unitpirceMax1) {\r\n                            this.errorTips(\r\n                            \"单价范围必须大于0.1元，此台账单价: \" +\r\n                                unitpirce +\r\n                                \"不在范围内，请确认！\"\r\n                            );\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            // validateTicketMoney(){\r\n            //     let data = this.tbAccount.data[this.editIndex];\r\n            //     let val = this.editTicketMoney;\r\n            //     if (!testNumber(val)) {\r\n            //         this.errorTips('请输入数字！');\r\n            //     }\r\n            //     data.ticketMoney = val;\r\n            //     data.editType = 1;\r\n\r\n            // },\r\n            // validateTaxTicketMoney(){\r\n            //     let data = this.tbAccount.data[this.editIndex];\r\n            //     let val = this.editTaxTicketMoney;\r\n            //     if (!testNumber(val)) {\r\n            //         this.errorTips('请输入数字！');\r\n            //     }\r\n            //     data.taxTicketMoney = val;\r\n            //     data.editType = 1;\r\n            // },\r\n\r\n            //计算 用电量,总电量,单价,总费用,浮动比.\r\n            calculateAll(row) {\r\n                row.curusedreadings = _calculateUsedReadings(row);\r\n                row.totalusedreadings = _calculateTotalReadings(row);\r\n                if(row.ischangeammeter == 1 && row.isnew == 1){\r\n                    if(row.oldbillpower>0){\r\n                        row.totalusedreadings = parseFloat(row.totalusedreadings)+Math.abs(row.oldbillpower)\r\n                    }\r\n                    let remark = row.remark\r\n                    if(remark.indexOf(\"换表\") == -1){\r\n                        row.remark+= \"换表，结清原电表读数【\"+row.oldbillpower+\"】；\"\r\n                    }\r\n                }\r\n                if (row.ticketMoney || row.taxTicketMoney) {\r\n                    row.accountmoney = _calculateAccountMoney(row);\r\n                    row.unitPrice = _calculateUnitPriceByUsedMoney(row);\r\n                }\r\n                row.quotereadingsratio = _calculateQuotereadingsratio(row);\r\n            },\r\n            validateOtherMoney(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOtherFee;\r\n                if (!testNumber(val)) {\r\n                    this.errorTips('请输入数字！');\r\n                }\r\n                data.otherFee = val;\r\n                data.editType = 1;\r\n                let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                data.paidMoney = paidMoney.toFixed(2);\r\n                let unitPrice = data.coalAmount?(data.paidMoney/data.coalAmount):0;\r\n                data.unitPrice = unitPrice.toFixed(2);\r\n            },\r\n            //验证普票\r\n            validateTicketMoney() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTicketMoney;\r\n                console.log(data.paidMoney, data.coalAmount, \"data.paidMoney, data.coalAmount\");\r\n                if (val != data.old_ticketmoney) {\r\n                    val = parseFloat(val);\r\n                    data.ticketMoney = _verify_Money(data, val);\r\n                    // data.inputticketmoney = _verify_Money(data, val)\r\n                    // data.ticketmoney = calculateActualMoney(data,val)\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                    let unitPrice = data.coalAmount?(data.paidMoney/data.coalAmount*1):0;\r\n                    console.log(unitPrice, \"unitPrice\")\r\n                    data.unitPrice = unitPrice.toFixed(2);\r\n                    // let unitpirce = data.coalAmount?(data.paidMoney/data.coalAmount*1):0;\r\n                    data.editType = 1;\r\n                    // this.calculateAll(data);\r\n                } else if (val == data.old_ticketmoney) {\r\n                    data.ticketMoney = val;\r\n                    // data.inputticketmoney = val;\r\n                    // data.ticketmoney = calculateActualMoney(data,val)\r\n                    // this.calculateAll(data);\r\n                }\r\n                // this.validateUnitPrice(data)\r\n            },\r\n            //验证专票\r\n            validateTaxTicketMoney() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxTicketMoney;\r\n                if (val != data.old_taxticketmoney) {\r\n                    val = parseFloat(val);\r\n                    data.taxTicketMoney = _verify_Money(data, val)\r\n                    data.editType = 1;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                    let unitPrice = data.coalAmount?(data.paidMoney/data.coalAmount):0;\r\n                    data.unitPrice = unitPrice.toFixed(2);\r\n                    // data.taxticketmoney = calculateActualMoney(data,val)\r\n                    // data.taxAmount = data.taxTicketMoney*data.taxRateShow;\r\n                    data.taxAmount = countTaxamount1(data);\r\n                    // this.calculateAll(data);\r\n                } else if (val == data.old_taxticketmoney) {\r\n                    data.taxTicketMoney = val;\r\n                    // data.taxticketmoney = calculateActualMoney(data,val)\r\n                    // data.taxAmount = (data.taxTicketMoney*1)*(data.taxRateShow*1);\r\n                    data.taxAmount = countTaxamount1(data);\r\n                    // this.calculateAll(data);\r\n                }\r\n                console.log(data.taxRateShow, \".taxRateShowdata5555555555\");\r\n                console.log(data.taxTicketMoney, \".taxTicketMoney\");\r\n                // this.validateUnitPrice(data)\r\n            },\r\n            //保存可编辑表格的初始化数据\r\n            setNewField(data) {\r\n                data.forEach(function (item) {\r\n                    item.old_startdate = item.startdate;\r\n                    item.old_prevtotalreadings = item.prevtotalreadings;\r\n\r\n                    item.multtimes = item.magnification\r\n                    item.old_enddate = item.enddate;\r\n                    item.old_curtotalreadings = item.curtotalreadings;\r\n                    item.old_transformerullage = item.transformerullage;\r\n                    item.old_taxticketmoney = item.inputtaxticketmoney;\r\n                    item.old_ticketmoney = item.inputticketmoney;\r\n                    item.old_ullagemoney = item.ullagemoney;\r\n                    item.old_prevhighreadings = item.prevhighreadings;\r\n                    item.old_prevflatreadings = item.prevflatreadings;\r\n                    item.old_prevlowreadings = item.prevlowreadings;\r\n\r\n                    item.old_curhighreadings = item.curhighreadings;\r\n                    item.old_curflatreadings = item.curflatreadings;\r\n                    item.old_curlowreadings = item.curlowreadings;\r\n                    item.old_curtotalreadings = item.curtotalreadings;\r\n\r\n                    item.version = indexData.version;\r\n                    item.editType = 0;\r\n                    item.isFPG = judging_editability1(item);\r\n                    item.isWB = judging_editability(item)\r\n                    if (!item.remark)\r\n                        item.remark = '';\r\n                    if (!item.bz)\r\n                        item.bz = '';\r\n                    item.transformerullage = judgeNumber(item.transformerullage)\r\n                    item.inputtaxticketmoney = judgeNumber(item.inputtaxticketmoney)\r\n                    item.inputticketmoney = judgeNumber(item.inputticketmoney)\r\n                    item.taxTicketMoney = judgeNumber(item.taxTicketMoney)\r\n                    item.ticketMoney = judgeNumber(item.ticketMoney)\r\n                    item.ullagemoney = judgeNumber(item.ullagemoney)\r\n                    item.curusedreadings = judgeNumber(item.curusedreadings)\r\n                    item.accountmoney = judgeNumber(item.accountmoney)\r\n                    if ((item.taxrate == null || item.taxrate == 0) && item.total == null) {\r\n                        item.taxrate = '13'\r\n                    }\r\n                    if (item.taxrate && item.taxAmount == null) {\r\n                        item.taxAmount = countTaxamount1(item);\r\n                    }\r\n                })\r\n                console.log(data, \"data5555555555\");\r\n            },\r\n            setremark(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editremark;\r\n                data.remark = val;\r\n                data.editType = 1;\r\n            },\r\n            setCoalUseBody(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editCoalUseBody;\r\n                data.coalUseBody = val;\r\n                data.editType = 1;\r\n            },\r\n            //专票税额\r\n            settaxrate() {\r\n                let val = this.editTaxRate;\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                data.taxRateShow = val;\r\n                console.log(data.taxRateShow, \".taxRateShowdata5555555555\");\r\n                console.log(data.taxTicketMoney, \".taxTicketMoney\");\r\n                data.taxAmount = countTaxamount1(data);\r\n                // data.taxAmount = (data.taxTicketMoney*1)*(data.taxRateShow*1);\r\n                console.log(data.taxAmount, \".taxAmount\");\r\n                data.editType = 1;\r\n            },\r\n            setCoalType() {\r\n                let val = this.editCoalType;\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                data.coalImportType = val;\r\n                data.editType = 1;\r\n            },\r\n            setCoalUse() {\r\n                let val = this.editCoalUse;\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                data.coalImportUse = val;\r\n                data.editType = 1;\r\n            },\r\n            setMyStyle(length){\r\n                this.myStyle=[];\r\n                for(var i=0;i<length;i++){\r\n                    this.myStyle.push({\r\n                        coalUseBody:'myspan',\r\n                        feeStartDate:'myspan',\r\n                        coalAmount:'myspan',\r\n                        ticketMoney:'myspan',\r\n                        taxTicketMoney:'myspan',\r\n                        taxRateShow:'myspan',\r\n                        otherFee:'myspan',\r\n                        coalImportType:'myspan',\r\n                        coalImportUse:'myspan',\r\n                        remark:'myspan',\r\n                    });\r\n                }\r\n            },\r\n            //span点击事件将span换成输入框并且获取焦点\r\n            selectCall(row,index,columns,str){\r\n                this.editFeeStartDate = row.feeStartDate;\r\n                this.editCoalUseBody = row.coalUseBody;\r\n                this.editCoalAmount = row.coalAmount == null || row.coalAmount===0?null:row.coalAmount;\r\n                this.editCoalType = row.coalImportType;\r\n                this.editCoalUse = row.coalImportUse;\r\n                this.editTicketMoney = row.ticketMoney;\r\n                this.editTaxTicketMoney = row.taxTicketMoney;\r\n                this.editTaxRate = row.taxRateShow;\r\n                this.editOtherFee = row.otherFee;\r\n                this.editremark = row.remark;\r\n                this.editIndex = index;\r\n                this.columnsIndex = columns;\r\n                let a=this;\r\n                setTimeout(function () {\r\n                    a.$refs[str+index+columns].focus();\r\n                },200);\r\n\r\n            },\r\n            //跳转到下一格\r\n            nextCell(data){\r\n                let index = data.editIndex;\r\n                let columns = data.columnsIndex;\r\n                let row = '';\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                }else if(index > -1 && columns === 15){\r\n                    //当跳转的最后一行最后一格的时候\r\n                    if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n                        index = 0;\r\n                    }else{\r\n                        index ++;\r\n                    }\r\n                    columns = 1;\r\n                }else{\r\n                    columns += 1;\r\n                }\r\n                data.editIndex = index;\r\n                data.columnsIndex = columns;\r\n                row = data.tbAccount.data[index];\r\n                if(row){\r\n                    data.editFeeStartDate = row.feeStartDate;\r\n                    data.editCoalUseBody = row.coalUseBody;\r\n                    data.editCoalAmount = row.coalAmount == null || row.coalAmount===0?null:row.coalAmount;\r\n                    data.editCoalType = row.coalImportType;\r\n                    data.editCoalUse = row.coalImportUse;\r\n                    data.editTicketMoney = row.ticketMoney;\r\n                    data.editTaxTicketMoney = row.taxTicketMoney;\r\n                    data.editTaxRate = row.taxRateShow;\r\n                    data.editOtherFee = row.otherFee;\r\n                    data.editremark=row.remark;\r\n                }\r\n                setTimeout(function () {\r\n                    data.$refs[data.enterOperate(columns).str+index+columns].focus();\r\n                },200);\r\n            },\r\n            //根据列号返回对应的列名\r\n            enterOperate(number){\r\n                let str = '';\r\n                let data = null;\r\n                switch (number) {\r\n                    case 2:\r\n                        str = 'coalUseBody';\r\n                        data = this.editCoalUseBody;\r\n                        break;\r\n                    case 3:\r\n                        str = 'feeStartDate';\r\n                        data = this.editFeeStartDate;\r\n                        break;\r\n                    case 4:\r\n                        str = 'coalAmount';\r\n                        data = this.editCoalAmount;\r\n                        break;\r\n                    case 6:\r\n                        str = 'ticketMoney';\r\n                        data = this.editTicketMoney;\r\n                        break;\r\n                    case 7:\r\n                        str = 'taxTicketMoney';\r\n                        data = this.editTaxTicketMoney;\r\n                        break;\r\n                    case 8:\r\n                        str = 'taxRateShow';\r\n                        data = this.editTaxRate;\r\n                        break;\r\n                    case 10:\r\n                        str = 'otherFee';\r\n                        data = this.editOtherFee;\r\n                        break;\r\n                    case 12:\r\n                        str = 'coalImportType';\r\n                        data = this.editCoalType;\r\n                        break;\r\n                    case 13:\r\n                        str = 'coalImportUse';\r\n                        data = this.editCoalUse;\r\n                        break;\r\n                    case 15:\r\n                        str = 'remark';\r\n                        data = this.editremark;\r\n                        break;\r\n                }\r\n                return {str:str,data:data};\r\n            },\r\n            pred(){\r\n                var lett = this;\r\n                let index = lett.editIndex;\r\n                let columns = lett.columnsIndex;\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                    lett.editIndex = index;\r\n                    lett.columnsIndex = columns;\r\n                    setTimeout(function () {\r\n                        lett.$refs[lett.enterOperate(columns).str+index+columns].focus();\r\n                    },200);\r\n                }else{\r\n                    lett.validate()\r\n                    lett.setremark()\r\n                    lett.nextCell(lett)\r\n                }\r\n            },\r\n            ellipsis (value) {\r\n                if (!value) return ''\r\n                if (value.length > 100) {\r\n                    return value.slice(0,100) + '...'\r\n                }\r\n                return value\r\n            },\r\n            handleUploadSuccess() {\r\n\r\n            },\r\n            handleFormatError(file) {\r\n                this.errorTips(\r\n                    file.name + \" 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件\"\r\n                );\r\n            },\r\n            handleProgress(event, file) {\r\n                this.$Message.info({\r\n                    content: file.name + \" 正在上传。\",\r\n                });\r\n            },\r\n            handleUploadSuccess() {\r\n                console.log(\"上传成功\");\r\n            },\r\n            // 导入\r\n            onExcelUpload(file) {\r\n                if (!file) {\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: '请选择要上传的文件！',\r\n                        duration: 10\r\n                    });\r\n                    return\r\n                }\r\n                let fileName = file.name.lastIndexOf(\".\");//取到文件名开始到最后一个点的长度\r\n                let fileNameLength = file.name.length;//取到文件名长度\r\n                let fileFormat = file.name.substring(fileName + 1, fileNameLength);//截\r\n                if('xls' != fileFormat && 'xlsx' != fileFormat){\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: file.name + ' 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件',\r\n                        duration: 10\r\n                    });\r\n                    return;\r\n                }\r\n                let param = {}\r\n                let excel = {file: file}\r\n                let that = this\r\n                that.spinShow = true\r\n                axios.request({\r\n                    url: '/business/coal/account/import',\r\n                    method: 'post',\r\n                    data: Object.assign({}, param, excel)\r\n                }).then((res) => {\r\n                    that.spinShow = false\r\n                    if (res.data.str) {\r\n                        that.$Message.info({\r\n                            content: res.data.str,\r\n                            duration: 0,\r\n                            closable: true\r\n                        });\r\n\r\n                        that.show = false;\r\n                    }\r\n this.getAccountMessages();\r\n                }).catch(err => {\r\n                    that.spinShow = false\r\n                    that.show = false;\r\n                    console.log(err);\r\n                });\r\n                return false\r\n            },\r\n\r\n            // 导入模板下载\r\n            loadTemplate() {\r\n                let req = {\r\n                    url : \"/business/coal/account/template/load\",\r\n                    method : \"get\",\r\n                    responseType: 'blob',\r\n                };\r\n                axios.file(req)\r\n                    .then(res => {\r\n                        this.spinShow = false;\r\n                        const content = res;\r\n                        const blob = new Blob([content]);\r\n                        const fileName = \"用煤台账导入模板.xlsx\";\r\n                        if (\"download\" in document.createElement(\"a\")) {\r\n                            // 非IE下载\r\n                            const elink = document.createElement(\"a\");\r\n                            elink.download = fileName;\r\n                            elink.style.display = \"none\";\r\n                            elink.href = URL.createObjectURL(blob);\r\n                            document.body.appendChild(elink);\r\n                            elink.click();\r\n                            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n                            document.body.removeChild(elink);\r\n                        } else {\r\n                            // IE10+下载\r\n                            navigator.msSaveBlob(blob, fileName);\r\n                        }\r\n                });\r\n            }\r\n        },\r\n        mounted() {\r\n            this.version = indexData.version\r\n            this.tbAccount.columns = this.tbAccount.tailColumn;\r\n            this.coalTypes = blist1(\"coalType\");\r\n            this.coalUseTypes = blist1(\"coalUseType\");\r\n            this.accountObj.coalUseType = this.coalUseTypes[0].typeCode\r\n            this.accountObj.coalType = this.coalTypes[0].typeCode\r\n            let that = this\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                    that.getUserData();\r\n                });\r\n                // this.tbAccount.tailColumn = this.tbAccount.tailColumn.concat(this.tbAccount.fileColumn)\r\n                // this.tbAccount.tailColumn.push(this.tbAccount.fileColumn[0])\r\n            });\r\n        }\r\n    }\r\n</script>\r\n"]}]}