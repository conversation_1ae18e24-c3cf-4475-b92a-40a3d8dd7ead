{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\addAmmeter.vue?vue&type=template&id=35011f71&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\addAmmeter.vue", "mtime": 1754285403016}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}