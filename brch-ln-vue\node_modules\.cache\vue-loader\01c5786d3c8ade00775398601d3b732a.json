{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\costdigit\\station-electric\\modal-info.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\costdigit\\station-electric\\modal-info.vue", "mtime": 1754285269231}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYXhpb3MgZnJvbSAiQC9saWJzL2FwaS5yZXF1ZXN0IjsNCmltcG9ydCB7IGdldENvc3RTdGFFbGVjdHJpY0luZm8gfSBmcm9tICJAL2FwaS9jb3N0ZGlnaXQvaW5kZXgiOw0KaW1wb3J0IHsgZGVlcENsb25lIH0gZnJvbSAiQC9saWJzL3V0aWwiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHNob3dNb2RhbDogZmFsc2UsDQogICAgICBidG5sb2FkaW5nOiBmYWxzZSwgLy/noa7orqTmj5DkuqQNCiAgICAgIHBhZ2VQYXJhbXM6IHt9LA0KICAgICAgZGljdHM6IHt9LA0KICAgICAgZm9ybURhdGE6IHt9LA0KICAgICAgdGFibGVMaXN0OiBbXSwNCiAgICAgIHRhYmxlU2V0OiB7DQogICAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICBzaG93UGFnZTogdHJ1ZSwNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICB0b3RhbDogMCwNCiAgICAgICAgY29sdW1uczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5bGA56uZ57yW56CBIiwNCiAgICAgICAgICAgIGtleTogInN0YXRpb25Db2RlIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIC8vIHsNCiAgICAgICAgICAvLyAgIHRpdGxlOiAi6LWE5rqQ57yW56CBIiwNCiAgICAgICAgICAvLyAgIGtleTogInB1ZUNvZGUiLA0KICAgICAgICAgIC8vICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgIC8vIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLlsYDnq5nlkI3np7AiLA0KICAgICAgICAgICAga2V5OiAic3RhdGlvbiIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgfSwNCiAgICAgICAgICAvLyB7DQogICAgICAgICAgLy8gICB0aXRsZTogIjVHUuermeWdgOe8lueggSIsDQogICAgICAgICAgLy8gICBrZXk6ICJzdGF0aW9uY29kZTVnciIsDQogICAgICAgICAgLy8gICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgLy8gfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuaXpeacnyIsDQogICAgICAgICAgICBrZXk6ICJycSIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIueUqOeUtemHjyIsDQogICAgICAgICAgICBrZXk6ICJ5d2RsIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICB9Ow0KICB9LA0KICBtb3VudGVkKCkge30sDQogIG1ldGhvZHM6IHsNCiAgICAvL+W8ueeqlzrmiZPlvIANCiAgICBvcGVuTW9kYWwodmFsKSB7DQogICAgICB0aGlzLnNob3dNb2RhbCA9IHRydWU7DQogICAgICAvLyDmlbDmja7mnaXmupAgMSDml6Dnur/lpKfmlbDmja4gMiDmmbrmhafmnLrmiL8NCiAgICAgIHRoaXMucGFnZVBhcmFtcyA9IHsNCiAgICAgICAgc3RhdGlvbkNvZGU6IHZhbC5zdGF0aW9uQ29kZSwNCiAgICAgICAgc291cmNlOiB2YWwuc291cmNlLA0KICAgICAgICB0anlmOiB2YWwudGp5ZiwNCiAgICAgICAgY2l0eUNvZGU6IHZhbC5jaXR5Q29kZSwNCiAgICAgICAgY291bnR5Q29kZTogdmFsLmNvdW50eUNvZGUsDQogICAgICB9Ow0KICAgICAgT2JqZWN0LmFzc2lnbih0aGlzLmZvcm1EYXRhLCB2YWwpOw0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLiRyZWZzLmNsVGFibGUucXVlcnkodGhpcy5wYWdlUGFyYW1zKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy/mn6Xor6INCiAgICB0YWJsZVF1ZXJ5KHBhcmFtcykgew0KICAgICAgdGhpcy50YWJsZVNldC5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGdldENvc3RTdGFFbGVjdHJpY0luZm8ocGFyYW1zKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy50YWJsZVNldC5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIGxldCBkYXRhID0gcmVzLmRhdGEucm93czsNCiAgICAgICAgdGhpcy50YWJsZVNldC50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOw0KICAgICAgICB0aGlzLnRhYmxlTGlzdCA9IGRhdGE7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8v5a+85Ye6DQogICAgZXhwb3J0Q3N2KG5hbWUpIHsNCiAgICAgIGlmICh0aGlzLnRhYmxlU2V0LnRvdGFsID09IDApIHsNCiAgICAgICAgdGhpcy4kTWVzc2FnZS53YXJuaW5nKCLmmoLml6DmlbDmja7lj6/lr7zlh7oiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5leHBvcnRMb2FkaW5nKCk7DQogICAgICBsZXQgcGFyYW1zID0gdGhpcy5kZWVwQ2xvbmUodGhpcy4kcmVmcy5jbFRhYmxlLmluc2lkZVF1ZXJ5UGFyYW1zKTsNCiAgICAgIGlmIChuYW1lID09ICJhbGwiKSB7DQogICAgICAgIHBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgICAgcGFyYW1zLnBhZ2VTaXplID0gdGhpcy50YWJsZVNldC50b3RhbDsNCiAgICAgIH0NCiAgICAgIGF4aW9zDQogICAgICAgIC5maWxlKHsNCiAgICAgICAgICB1cmw6ICIvYnVzaW5lc3MvY29zdC9zdGF0aW9uRWxlY3RyaWMveHEvZXhwb3J0IiwNCiAgICAgICAgICBtZXRob2Q6ICJwb3N0IiwNCiAgICAgICAgICBkYXRhOiBwYXJhbXMsDQogICAgICAgIH0pDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBjb25zdCBjb250ZW50ID0gcmVzOw0KICAgICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbY29udGVudF0pOw0KDQogICAgICAgICAgbGV0IGZpbGVOYW1lID0gYCR7dGhpcy5mb3JtRGF0YS55Zn0t55S16YeP5p+l6K+i6K+m5oOFLnhsc3hgOw0KICAgICAgICAgIGlmICgiZG93bmxvYWQiIGluIGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImEiKSkgew0KICAgICAgICAgICAgLy8g6Z2eSUXkuIvovb0NCiAgICAgICAgICAgIGNvbnN0IGVsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpOw0KICAgICAgICAgICAgZWxpbmsuZG93bmxvYWQgPSBmaWxlTmFtZTsNCiAgICAgICAgICAgIGVsaW5rLnN0eWxlLmRpc3BsYXkgPSAibm9uZSI7DQogICAgICAgICAgICBlbGluay5ocmVmID0gVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTsNCiAgICAgICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQoZWxpbmspOw0KICAgICAgICAgICAgZWxpbmsuY2xpY2soKTsNCiAgICAgICAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwoZWxpbmsuaHJlZik7IC8vIOmHiuaUvlVSTCDlr7nosaENCiAgICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoZWxpbmspOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyBJRTEwK+S4i+i9vQ0KICAgICAgICAgICAgbmF2aWdhdG9yLm1zU2F2ZUJsb2IoYmxvYiwgZmlsZU5hbWUpOw0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLiRTcGluLmhpZGUoKTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICBleHBvcnRMb2FkaW5nKCkgew0KICAgICAgdGhpcy4kU3Bpbi5zaG93KHsNCiAgICAgICAgcmVuZGVyOiAoaCkgPT4gew0KICAgICAgICAgIHJldHVybiBoKCJkaXYiLCBbDQogICAgICAgICAgICBoKCJQcm9ncmVzcyIsIHsNCiAgICAgICAgICAgICAgc3R5bGU6IHsNCiAgICAgICAgICAgICAgICB3aWR0aDogIjgwMHB4IiwNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIH0pLA0KICAgICAgICAgICAgaCgiZGl2IiwgIuWvvOWHuuS4re+8jOivt+WLv+WIt+aWsOmhtemdoi4uLi4uLiIpLA0KICAgICAgICAgIF0pOw0KICAgICAgICB9LA0KICAgICAgfSk7DQogICAgfSwNCiAgICBjbGVhckZvcm0oKSB7DQogICAgICBmb3IgKGxldCBrZXkgaW4gdGhpcy5mb3JtRGF0YSkgew0KICAgICAgICB0aGlzLmZvcm1EYXRhW2tleV0gPSBudWxsOw0KICAgICAgfQ0KICAgICAgdGhpcy4kcmVmc1sibXlmb3JtIl0gJiYgdGhpcy4kcmVmc1sibXlmb3JtIl0ucmVzZXRGaWVsZHMoKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["modal-info.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4FA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "modal-info.vue", "sourceRoot": "src/view/costdigit/station-electric", "sourcesContent": ["<template>\r\n  <Modal class=\"common-wh\" v-model=\"showModal\" title=\"详情\" width=\"70%\">\r\n    <Form ref=\"myform\" :model=\"formData\" :label-width=\"140\">\r\n      <Row>\r\n        <Col span=\"8\">\r\n          <FormItem label=\"局站名称:\" prop=\"station\">\r\n            <Input v-model=\"formData.station\" disabled> </Input>\r\n          </FormItem>\r\n        </Col>\r\n        <!-- <Col span=\"8\">\r\n          <FormItem label=\"站址类型:\" prop=\"stationType\">\r\n            <Input v-model=\"formData.stationType\" disabled> </Input>\r\n          </FormItem>\r\n        </Col> -->\r\n        <Col span=\"8\">\r\n          <FormItem label=\"局站编码:\" prop=\"stationCode\">\r\n            <Input v-model=\"formData.stationCode\" disabled> </Input>\r\n          </FormItem>\r\n        </Col>\r\n        <!-- <Col span=\"8\">\r\n          <FormItem label=\"资源编码:\" prop=\"pueCode\">\r\n            <Input v-model=\"formData.pueCode\" disabled> </Input>\r\n          </FormItem>\r\n        </Col>\r\n        <Col span=\"8\">\r\n          <FormItem label=\"5gr站址编码:\" prop=\"stationcode5gr\">\r\n            <Input v-model=\"formData.stationcode5gr\" disabled> </Input>\r\n          </FormItem>\r\n        </Col> -->\r\n        <!-- <Col span=\"8\">\r\n          <FormItem label=\"数据来源:\" prop=\"sourceName\">\r\n            <Input v-model=\"formData.sourceName\" disabled> </Input>\r\n          </FormItem>\r\n        </Col> -->\r\n        <Col span=\"8\">\r\n          <FormItem label=\"月份:\" prop=\"yf\">\r\n            <Input v-model=\"formData.yf\" disabled> </Input>\r\n          </FormItem>\r\n        </Col>\r\n        <Col span=\"8\">\r\n          <FormItem label=\"月总电量:\" prop=\"ywdl\">\r\n            <Input v-model=\"formData.ywdl\" disabled> </Input>\r\n          </FormItem>\r\n        </Col>\r\n        <Col span=\"8\">\r\n          <FormItem label=\"日均电量:\" prop=\"rjdl\">\r\n            <Input v-model=\"formData.rjdl\" disabled> </Input>\r\n          </FormItem>\r\n        </Col>\r\n      </Row>\r\n    </Form>\r\n    <div class=\"list-title\">\r\n      <div>用电详情</div>\r\n      <!-- <Button\r\n        type=\"primary\"\r\n        style=\"margin-left: 5px\"\r\n        @click=\"exportCsv\"\r\n        :disabled=\"tableList.length == 0\"\r\n        >导出\r\n      </Button> -->\r\n      <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n        <Button type=\"default\" style=\"margin-left: 5px\"\r\n          >导出\r\n          <Icon type=\"ios-arrow-down\"></Icon>\r\n        </Button>\r\n        <DropdownMenu slot=\"list\">\r\n          <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n          <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n        </DropdownMenu>\r\n      </Dropdown>\r\n    </div>\r\n    <cl-table\r\n      ref=\"clTable\"\r\n      height=\"300\"\r\n      :searchable=\"false\"\r\n      :exportable=\"false\"\r\n      :columns=\"tableSet.columns\"\r\n      :loading=\"tableSet.loading\"\r\n      :total=\"tableSet.total\"\r\n      :pageSize=\"tableSet.pageSize\"\r\n      :showPage=\"tableSet.showPage\"\r\n      @on-query=\"tableQuery\"\r\n      :data=\"tableList\"\r\n      v-if=\"showModal\"\r\n    >\r\n    </cl-table>\r\n    <div slot=\"footer\">\r\n      <Button type=\"default\" class=\"cancelBtn\" @click=\"showModal = false\">取消</Button>\r\n    </div>\r\n  </Modal>\r\n</template>\r\n<script>\r\nimport axios from \"@/libs/api.request\";\r\nimport { getCostStaElectricInfo } from \"@/api/costdigit/index\";\r\nimport { deepClone } from \"@/libs/util\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      showModal: false,\r\n      btnloading: false, //确认提交\r\n      pageParams: {},\r\n      dicts: {},\r\n      formData: {},\r\n      tableList: [],\r\n      tableSet: {\r\n        loading: false,\r\n        showPage: true,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        total: 0,\r\n        columns: [\r\n          {\r\n            title: \"局站编码\",\r\n            key: \"stationCode\",\r\n            align: \"center\",\r\n          },\r\n          // {\r\n          //   title: \"资源编码\",\r\n          //   key: \"pueCode\",\r\n          //   align: \"center\",\r\n          // },\r\n          {\r\n            title: \"局站名称\",\r\n            key: \"station\",\r\n            align: \"center\",\r\n          },\r\n          // {\r\n          //   title: \"5GR站址编码\",\r\n          //   key: \"stationcode5gr\",\r\n          //   align: \"center\",\r\n          // },\r\n          {\r\n            title: \"日期\",\r\n            key: \"rq\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"用电量\",\r\n            key: \"ywdl\",\r\n            align: \"center\",\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  mounted() {},\r\n  methods: {\r\n    //弹窗:打开\r\n    openModal(val) {\r\n      this.showModal = true;\r\n      // 数据来源 1 无线大数据 2 智慧机房\r\n      this.pageParams = {\r\n        stationCode: val.stationCode,\r\n        source: val.source,\r\n        tjyf: val.tjyf,\r\n        cityCode: val.cityCode,\r\n        countyCode: val.countyCode,\r\n      };\r\n      Object.assign(this.formData, val);\r\n      this.$nextTick(() => {\r\n        this.$refs.clTable.query(this.pageParams);\r\n      });\r\n    },\r\n    //查询\r\n    tableQuery(params) {\r\n      this.tableSet.loading = true;\r\n      getCostStaElectricInfo(params).then((res) => {\r\n        this.tableSet.loading = false;\r\n        let data = res.data.rows;\r\n        this.tableSet.total = res.data.total;\r\n        this.tableList = data;\r\n      });\r\n    },\r\n    //导出\r\n    exportCsv(name) {\r\n      if (this.tableSet.total == 0) {\r\n        this.$Message.warning(\"暂无数据可导出\");\r\n        return;\r\n      }\r\n      this.exportLoading();\r\n      let params = this.deepClone(this.$refs.clTable.insideQueryParams);\r\n      if (name == \"all\") {\r\n        params.pageNum = 1;\r\n        params.pageSize = this.tableSet.total;\r\n      }\r\n      axios\r\n        .file({\r\n          url: \"/business/cost/stationElectric/xq/export\",\r\n          method: \"post\",\r\n          data: params,\r\n        })\r\n        .then((res) => {\r\n          const content = res;\r\n          const blob = new Blob([content]);\r\n\r\n          let fileName = `${this.formData.yf}-电量查询详情.xlsx`;\r\n          if (\"download\" in document.createElement(\"a\")) {\r\n            // 非IE下载\r\n            const elink = document.createElement(\"a\");\r\n            elink.download = fileName;\r\n            elink.style.display = \"none\";\r\n            elink.href = URL.createObjectURL(blob);\r\n            document.body.appendChild(elink);\r\n            elink.click();\r\n            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n            document.body.removeChild(elink);\r\n          } else {\r\n            // IE10+下载\r\n            navigator.msSaveBlob(blob, fileName);\r\n          }\r\n          this.$Spin.hide();\r\n        });\r\n    },\r\n    exportLoading() {\r\n      this.$Spin.show({\r\n        render: (h) => {\r\n          return h(\"div\", [\r\n            h(\"Progress\", {\r\n              style: {\r\n                width: \"800px\",\r\n              },\r\n            }),\r\n            h(\"div\", \"导出中，请勿刷新页面......\"),\r\n          ]);\r\n        },\r\n      });\r\n    },\r\n    clearForm() {\r\n      for (let key in this.formData) {\r\n        this.formData[key] = null;\r\n      }\r\n      this.$refs[\"myform\"] && this.$refs[\"myform\"].resetFields();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.list-title {\r\n  padding: 20px;\r\n  font-size: 16px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n</style>\r\n"]}]}