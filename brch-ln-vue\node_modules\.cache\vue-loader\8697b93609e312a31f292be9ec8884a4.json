{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePagePylon\\addPylonPredAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePagePylon\\addPylonPredAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBtYXBTdGF0ZSB9IGZyb20gInZ1ZXgiOw0KaW1wb3J0IHsgZ2V0QXVkaXRSZXN1bHQsIGdldEF1ZGl0UmVzdWx0TmV3LCBnZXRBdWRpdFJlc3VsdE5ld19RWE0gfSBmcm9tICJAL2FwaS9hY2NvdW50IjsNCmltcG9ydCB7DQogIHJlbW92ZUFsbCwNCiAgYWRkUHJlZFBvd2VyQWNjb3VudCwNCiAgYWRkQWNjb3VudEVzLA0KICByZW1vdmVBY2NvdW50RXMsDQogIGdldFVzZXIsDQogIGdldERlcGFydG1lbnRzLA0KICBhY2NvdW50RXNUb3RhbCwNCiAgc2VsZWN0SWRzQnlFc1BhcmFtcywNCn0gZnJvbSAiQC9hcGkvYWNjb3VudCI7DQppbXBvcnQgeyBnZXRSZXNDZW50ZXIsIGdldGNvbXBhbnkgfSBmcm9tICJAL2FwaS9hbGVydGNvbnRyb2wvYWxlcnRjb250cm9sIjsNCmltcG9ydCB7IGFnYWluSm9pbiB9IGZyb20gIkAvYXBpL2FjY291bnRCaWxsUGVyIjsNCmltcG9ydCB7DQogIGdldENsYXNzaWZpY2F0aW9uLA0KICBnZXRVc2VyZGF0YSwNCiAgZ2V0VXNlckJ5VXNlclJvbGUsDQogIGdldENvdW50cnlzZGF0YSwNCiAgZ2V0Q291bnRyeUJ5VXNlcklkLA0KfSBmcm9tICJAL2FwaS9iYXNlZGF0YS9hbW1ldGVyLmpzIjsNCmltcG9ydCB7IGVkaXRPd24gfSBmcm9tICJAL2FwaS9hY2NvdW50U0MvYWNjb3VudFNDIjsNCmltcG9ydCB7IHZhbGlkQ29udHJhY3RMaXN0IH0gZnJvbSAiQC9hcGkvY29udHJhY3QiOw0KaW1wb3J0IHsNCiAgZ2V0RGF0ZXMyLA0KICB0ZXN0TnVtYmVyLA0KICBjdXREYXRlX3l5eXltbWRkLA0KICBnZXRGaXJzdERhdGVCeUFjY291bnRub195eXl5bW1kZCwNCiAgZ2V0TGFzdERhdGVCeUFjY291bnRub195eXl5bW1kZCwNCiAgc3RyaW5nVG9EYXRlLA0KICBnZXRDdXJyZW50RGF0ZSwNCn0gZnJvbSAiQC92aWV3L2FjY291bnQvcG93ZXJBY2NvdW50SGVscGVyIjsNCmltcG9ydCB7DQogIF92ZXJpZnlfU3RhcnREYXRlLA0KICBfdmVyaWZ5X0VuZERhdGUsDQogIHZlcmlmaWNhdGlvbiwNCiAgdW5pdHBpcmNlTWluLA0KICB1bml0cGlyY2VNYXgsDQogIHVuaXRwaXJjZU1heDEsDQp9IGZyb20gIkAvdmlldy9hY2NvdW50L1Bvd2VyQWNjb3VudEVzIjsNCmltcG9ydCB7DQogIGp1ZGdlX25lZ2F0ZSwNCiAganVkZ2VfcmVjb3ZlcnksDQogIHJlcXVpcmVkRmllbGRWYWxpZGF0b3IsDQp9IGZyb20gIkAvdmlldy9hY2NvdW50L1Bvd2VyQWNjb3VudENvbnRyb2xsZXIiOw0KaW1wb3J0IHsgd2lkdGhzdHlsZSB9IGZyb20gIkAvdmlldy9idXNpbmVzcy9tc3NBY2NvdW50YmlsbC9tc3NBY2NvdW50YmlsbGRhdGEiOw0KaW1wb3J0IEFkZEJpbGxQZXIgZnJvbSAiQC92aWV3L2FjY291bnQvYWRkQmlsbFByZU1vZGFsIjsNCmltcG9ydCBTZWxlY3RBbW1ldGVyIGZyb20gIkAvdmlldy9hY2NvdW50L3NlbGVjdEFtbWV0ZXIiOw0KaW1wb3J0IENvbXBsZXRlZFByZU1vZGFsIGZyb20gIkAvdmlldy9hY2NvdW50L2NvbXBsZXRlZFByZU1vZGFsIjsNCmltcG9ydCB7IGJsaXN0IH0gZnJvbSAiQC9saWJzL3Rvb2xzIjsNCmltcG9ydCBheGlvcyBmcm9tICJAL2xpYnMvYXBpLnJlcXVlc3QiOw0KaW1wb3J0IGV4Y2VsIGZyb20gIkAvbGlicy9leGNlbCI7DQppbXBvcnQgaW5kZXhEYXRhIGZyb20gIkAvY29uZmlnL2luZGV4IjsNCmltcG9ydCBDb3VudHJ5TW9kYWwgZnJvbSAiQC92aWV3L2Jhc2VkYXRhL2FtbWV0ZXIvY291bnRyeU1vZGFsIjsNCmltcG9ydCBhbGFybUNoZWNrIGZyb20gIkAvdmlldy9hY2NvdW50L2NoZWNrL2FsYXJtQ2hlY2siOw0KaW1wb3J0IGNoZWNrUmVzdWx0IGZyb20gIkAvdmlldy9hY2NvdW50L2NoZWNrL2NoZWNrUmVzdWx0IjsNCmltcG9ydCBjaGVja1Jlc3VsdEFuZFJlc3BvbnNlIGZyb20gIkAvdmlldy9hY2NvdW50L2NoZWNrL2NoZWNrUmVzdWx0QW5kUmVzcG9uc2UiOw0KaW1wb3J0IFF1ZXJ5UGVvcGxlTW9kYWwgZnJvbSAiQC92aWV3L2FjY291bnQvcXVlcnlQZW9wbGVNb2RhbCI7DQppbXBvcnQgcGVybWlzc2lvbk1peGluIGZyb20gIkAvbWl4aW5zL3Blcm1pc3Npb24iOw0KDQppbXBvcnQgcGFnZUZ1biBmcm9tICJAL21peGlucy9wYWdlRnVuIjsNCg0KbGV0IGRhdGVzID0gZ2V0RGF0ZXMyKCk7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJhZGRQcmVkUG93ZXJBY2NvdW50IiwNCiAgbWl4aW5zOiBbcGVybWlzc2lvbk1peGluLCBwYWdlRnVuXSwNCiAgY29tcG9uZW50czogew0KICAgIGFsYXJtQ2hlY2ssDQogICAgY2hlY2tSZXN1bHQsDQogICAgY2hlY2tSZXN1bHRBbmRSZXNwb25zZSwNCiAgICBDb21wbGV0ZWRQcmVNb2RhbCwNCiAgICBTZWxlY3RBbW1ldGVyLA0KICAgIEFkZEJpbGxQZXIsDQogICAgQ291bnRyeU1vZGFsLA0KICB9LA0KICBkYXRhKCkgew0KICAgIGxldCByZW5kZXJTdGF0dXMgPSAoaCwgeyByb3csIGluZGV4IH0pID0+IHsNCiAgICAgIHZhciBzdGF0dXMgPSAiIjsNCiAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVtpbmRleF07DQogICAgICBmb3IgKGxldCBpdGVtIG9mIHRoaXMuYWNjb3VudFN0YXR1cykgew0KICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSByb3cuc3RhdHVzKSB7DQogICAgICAgICAgZGF0YS5zdGF0dXNOYW1lID0gaXRlbS50eXBlTmFtZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGgoImRpdiIsIGRhdGEuc3RhdHVzTmFtZSk7DQogICAgfTsNCg0KICAgIGxldCByZW5kZXJDYXRlZ29yeSA9IChoLCBwYXJhbXMpID0+IHsNCiAgICAgIHZhciBjYXRlZ29yeW5hbWUgPSAiIjsNCiAgICAgIGZvciAobGV0IGl0ZW0gb2YgdGhpcy5jYXRlZ29yeXMpIHsNCiAgICAgICAgaWYgKGl0ZW0udHlwZUNvZGUgPT0gcGFyYW1zLnJvdy5jYXRlZ29yeSkgew0KICAgICAgICAgIGNhdGVnb3J5bmFtZSA9IGl0ZW0udHlwZU5hbWU7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiBoKCJkaXYiLCBjYXRlZ29yeW5hbWUpOw0KICAgIH07DQoNCiAgICByZXR1cm4gew0KICAgICAgaXNUOiB0cnVlLA0KICAgICAgbnVtYmVyMjogMCwNCiAgICAgIG5hbWU6ICIiLA0KICAgICAgZGF0YUw6IFtdLA0KICAgICAgaXNRdWVyeTogdHJ1ZSwNCiAgICAgIG51bWJlcjogMCwNCiAgICAgIGNhbmNlbFRpbWU6ICIiLA0KICAgICAgY3RnS2V5TGlzdDogW10sDQogICAgICBzdWJtaXQ6IFtdLA0KICAgICAgc3VibWl0MjogW10sDQogICAgICBhbW1ldGVyaWRzOiBbXSwNCiAgICAgIHNob3dDaGVja01vZGVsOiBmYWxzZSwNCiAgICAgIHNob3dBbGFybU1vZGVsOiBmYWxzZSwNCiAgICAgIHNob3dKaE1vZGVsOiBmYWxzZSwNCiAgICAgIGZvcm1JdGVtV2lkdGg6IHdpZHRoc3R5bGUsDQogICAgICB2ZXJzaW9uOiAiIiwNCiAgICAgIGRhdGVMaXN0OiBkYXRlcywNCiAgICAgIGZpbHRlckNvbGw6IHRydWUsIC8v5pCc57Si6Z2i5p2/5bGV5byADQogICAgICBlZGl0SW5kZXg6IC0xLCAvL+W9k+WJjee8lui+keihjA0KICAgICAgY29sdW1uc0luZGV4OiAtMSwgLy/lvZPliY3nvJbovpHliJcNCiAgICAgIGVkaXRTdGFydERhdGU6ICIiLA0KICAgICAgbXlTdHlsZTogW10sIC8v5qC35byPDQogICAgICBlZGl0RW5kRGF0ZTogIiIsDQogICAgICBlZGl0Y3VydXNlZHJlYWRpbmdzOiAiIiwNCiAgICAgIGVkaXR0cmFuc2Zvcm1lcnVsbGFnZTogIiIsDQogICAgICBzcGluU2hvdzogZmFsc2UsIC8v6YGu572pDQogICAgICBjYXRlZ29yeXM6IFtdLCAvL+aPj+i/sOexu+Weiw0KICAgICAgZWRpdGFjY291bnRtb25leTogIiIsDQogICAgICBlZGl0cmVtYXJrOiAiIiwNCiAgICAgIGFjY291bnRTdGF0dXM6IFtdLA0KICAgICAgY29tcGFuaWVzOiBbXSwNCiAgICAgIGRlcGFydG1lbnRzOiBbXSwNCiAgICAgIGlzQWRtaW46IGZhbHNlLA0KICAgICAgY29tcGFueTogbnVsbCwgLy/nlKjmiLfpu5jorqTlhazlj7gNCiAgICAgIGNvdW50cnk6IG51bGwsIC8v55So5oi36buY6K6k5omA5bGe6YOo6ZeoDQogICAgICBjb3VudHJ5TmFtZTogbnVsbCwgLy/nlKjmiLfpu5jorqTmiYDlsZ7pg6jpl6gNCiAgICAgIGV4cG9ydDogew0KICAgICAgICBydW46IGZhbHNlLCAvL+aYr+WQpuato+WcqOaJp+ihjOWvvOWHug0KICAgICAgICBkYXRhOiAiIiwgLy/lr7zlh7rmlbDmja4NCiAgICAgICAgdG90YWxQYWdlOiAwLCAvL+S4gOWFseWkmuWwkemhtQ0KICAgICAgICBjdXJyZW50UGFnZTogMCwgLy/lvZPliY3lpJrlsJHpobUNCiAgICAgICAgcGVyY2VudDogMCwNCiAgICAgICAgc2l6ZTogMTAwMDAwMDAsDQogICAgICB9LA0KICAgICAgYWNjb3VudE9iajogew0KICAgICAgICBhY2NvdW50bm86IGRhdGVzWzBdLmNvZGUsIC8v5pyf5Y+3LOm7mOiupOW9k+WJjeaciA0KICAgICAgICBjb21wYW55OiAiIiwgLy/liIblhazlj7gNCiAgICAgICAgcHJvamVjdE5hbWU6ICIiLCAvL+mhueebruWQjeensA0KICAgICAgICBjb3VudHJ5OiAiIiwgLy/miYDlsZ7pg6jpl6gNCiAgICAgICAgYW1tZXRlck5hbWU6ICIiLCAvL+eUteihqOaIt+WPty/ljY/orq7nvJbnoIENCiAgICAgICAgc3RhdGlvbk5hbWU6ICIiLA0KICAgICAgICBhY2NvdW50VHlwZTogIjIiLCAvL+WPsOi0puexu+Weiw0KICAgICAgICBhY2NvdW50ZXN0eXBlOiAxLCAvL+WPsOi0puexu+Weiw0KICAgICAgICBzdXBwbHlidXJlYXVhbW1ldGVyY29kZTogIiIsDQogICAgICB9LA0KICAgICAgdGJBY2NvdW50OiB7DQogICAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICBjb2x1bW5zOiBbXSwNCiAgICAgICAgaGVhZENvbHVtbjogWw0KICAgICAgICAgIHsgdHlwZTogInNlbGVjdGlvbiIsIHdpZHRoOiA2MCwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIumhueebruWQjeensCIsIGtleTogInByb2plY3ROYW1lIiwgc2xvdDogInByb2plY3ROYW1lIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGhlYWRDb2x1bW4yOiBbDQogICAgICAgICAgeyB0eXBlOiAic2VsZWN0aW9uIiwgd2lkdGg6IDYwLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIueoveaguOe7k+aenOWPiuWPjemmiCIsDQogICAgICAgICAgICBrZXk6ICJhY3Rpb24iLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgcmVuZGVyOiAoaCwgcGFyYW1zKSA9PiB7DQogICAgICAgICAgICAgIHZhciB0aGF0ID0gdGhpczsNCiAgICAgICAgICAgICAgcmV0dXJuIGgoDQogICAgICAgICAgICAgICAgIkJ1dHRvbiIsDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgcHJvcHM6IHsNCiAgICAgICAgICAgICAgICAgICAgdHlwZTogInByaW1hcnkiLA0KICAgICAgICAgICAgICAgICAgICBzaXplOiAic21hbGwiLA0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgIHN0eWxlOiB7DQogICAgICAgICAgICAgICAgICAgICJmb250LXNpemUiOiAiMTBweCIsDQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgb246IHsNCiAgICAgICAgICAgICAgICAgICAgY2xpY2soKSB7DQogICAgICAgICAgICAgICAgICAgICAgdGhhdC4kcmVmcy5jaGVrUmVzdWx0QW5kUmVzcG9uc2UucGNpZCA9IHBhcmFtcy5yb3cucGNpZDsNCiAgICAgICAgICAgICAgICAgICAgICB0aGF0LnNob3dDaGVja01vZGVsID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAi56i95qC457uT5p6c5Y+K5Y+N6aaIIg0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIG1pbldpZHRoOiAxMjAsDQogICAgICAgICAgICBtYXhXaWR0aDogMTUwLA0KICAgICAgICAgIH0sDQogICAgICAgICAgeyB0aXRsZTogIumhueebruWQjeensCIsIGtleTogInByb2plY3ROYW1lIiwgc2xvdDogInByb2plY3ROYW1lIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIHRhaWxDb2x1bW46IFsNCiAgICAgICAgICB7IHRpdGxlOiAi5omA5bGe5YiG5YWs5Y+4Iiwga2V5OiAiY29tcGFueU5hbWUiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5omA5bGe6YOo6ZeoIiwga2V5OiAiY291bnRyeU5hbWUiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5bGA56uZIiwga2V5OiAic3RhdGlvbk5hbWUiLCBhbGlnbjogImNlbnRlciIsIHdpZHRoOiA2MCB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi6LW35aeL5pel5pyfIiwNCiAgICAgICAgICAgIHNsb3Q6ICJzdGFydGRhdGUiLA0KICAgICAgICAgICAga2V5OiAic3RhcnRkYXRlIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA5MCwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5oiq5q2i5pel5pyfIiwNCiAgICAgICAgICAgIHNsb3Q6ICJlbmRkYXRlIiwNCiAgICAgICAgICAgIGtleTogImVuZGRhdGUiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDkwLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLnlKjnlLXph48o5bqmKSIsDQogICAgICAgICAgICBzbG90OiAiY3VydXNlZHJlYWRpbmdzIiwNCiAgICAgICAgICAgIGtleTogImN1cnVzZWRyZWFkaW5ncyIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi55S15Lu3KOWFgykiLCBrZXk6ICJ1bml0cGlyY2UiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi55S16LS5Iiwgc2xvdDogImFjY291bnRtb25leSIsIGtleTogImFjY291bnRtb25leSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLnlKjnlLXnsbvlnosiLCBrZXk6ICJlbGVjdHJvdHlwZW5hbWUiLCBhbGlnbjogImNlbnRlciIsIHdpZHRoOiA5NCB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi57G75Z6L5o+P6L+wIiwNCiAgICAgICAgICAgIGtleTogImNhdGVnb3J5bmFtZSIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICByZW5kZXI6IHJlbmRlckNhdGVnb3J5LA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGxuQ29sdW1uOiBbDQogICAgICAgICAgeyB0aXRsZTogIuS+m+eUteWxgOeUteihqOe8luWPtyIsIGtleTogInN1cHBseWJ1cmVhdWFtbWV0ZXJjb2RlIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIHNjQ29sdW1uOiBbDQogICAgICAgICAgeyB0aXRsZTogIueUteihqOaIt+WPty/ljY/orq7nvJbnoIEiLCBrZXk6ICJhbW1ldGVyTmFtZSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLkvpvnlLXlsYDnlLXooajnvJblj7ciLCBrZXk6ICJzdXBwbHlidXJlYXVhbW1ldGVyY29kZSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBsbnJlbWFyazogW3sgdGl0bGU6ICLlpIfms6giLCBzbG90OiAicmVtYXJrIiwga2V5OiAicmVtYXJrIiwgYWxpZ246ICJjZW50ZXIiIH1dLA0KICAgICAgICBzY3JlbWFyazogW3sgdGl0bGU6ICLpooTkvLDljp/nlLEiLCBzbG90OiAicmVtYXJrIiwga2V5OiAicmVtYXJrIiwgYWxpZ246ICJjZW50ZXIiIH1dLA0KICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgdG90YWw6IDAsDQogICAgICAgIGV4cG9ydENvbHVtbnM6IFsNCiAgICAgICAgICB7IHRpdGxlOiAi6aG555uu5ZCN56ewIiwga2V5OiAicHJvamVjdE5hbWUiLCBzbG90OiAicHJvamVjdE5hbWUiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi55S16KGo5oi35Y+3L+WNj+iurue8lueggSIsIGtleTogImFtbWV0ZXJOYW1lIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuS+m+eUteWxgOeUteihqOe8luWPtyIsIGtleTogInN1cHBseWJ1cmVhdWFtbWV0ZXJjb2RlIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuaJgOWxnuWIhuWFrOWPuCIsIGtleTogImNvbXBhbnlOYW1lIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuaJgOWxnumDqOmXqCIsIGtleTogImNvdW50cnlOYW1lIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuWxgOermSIsIGtleTogInN0YXRpb25OYW1lIiwgYWxpZ246ICJjZW50ZXIiLCB3aWR0aDogNjAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIui1t+Wni+aXpeacnyIsDQogICAgICAgICAgICBzbG90OiAic3RhcnRkYXRlIiwNCiAgICAgICAgICAgIGtleTogInN0YXJ0ZGF0ZSIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB3aWR0aDogOTAsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuaIquatouaXpeacnyIsDQogICAgICAgICAgICBzbG90OiAiZW5kZGF0ZSIsDQogICAgICAgICAgICBrZXk6ICJlbmRkYXRlIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA5MCwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi55So55S16YePKOW6pikiLA0KICAgICAgICAgICAgc2xvdDogImN1cnVzZWRyZWFkaW5ncyIsDQogICAgICAgICAgICBrZXk6ICJjdXJ1c2VkcmVhZGluZ3MiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgeyB0aXRsZTogIueUteS7tyjlhYMpIiwga2V5OiAidW5pdHBpcmNlIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLpooTkvLDnlLXotLkiLA0KICAgICAgICAgICAgc2xvdDogImFjY291bnRtb25leSIsDQogICAgICAgICAgICBrZXk6ICJhY2NvdW50bW9uZXkiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgeyB0aXRsZTogIuWkh+azqCIsIHNsb3Q6ICJyZW1hcmsiLCBrZXk6ICJyZW1hcmsiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi55So55S157G75Z6LIiwga2V5OiAiZWxlY3Ryb3R5cGVuYW1lIiwgYWxpZ246ICJjZW50ZXIiLCB3aWR0aDogOTQgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi57G75Z6L5o+P6L+wIiwga2V5OiAiY2F0ZWdvcnluYW1lIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgIF0sDQogICAgICB9LA0KICAgICAgcGFnZVRvdGFsOiAwLA0KICAgICAgcGFnZU51bTogMSwNCiAgICAgIHBhZ2VTaXplOiAxMCwgLy/lvZPliY3pobUNCiAgICB9Ow0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMuaGFuZGxlSGVpZ2h0KCk7IC8vdGFibGXpq5jluqboh6rlrprkuYkNCg0KICAgIHRoaXMudmVyc2lvbiA9IGluZGV4RGF0YS52ZXJzaW9uOw0KDQogICAgdGhpcy50YkFjY291bnQuY29sdW1ucyA9IHRoaXMudGJBY2NvdW50LmhlYWRDb2x1bW4yDQogICAgICAuY29uY2F0KHRoaXMudGJBY2NvdW50LnNjQ29sdW1uKQ0KICAgICAgLmNvbmNhdCh0aGlzLnRiQWNjb3VudC50YWlsQ29sdW1uKQ0KICAgICAgLmNvbmNhdCh0aGlzLnRiQWNjb3VudC5zY3JlbWFyayk7DQoNCiAgICB0aGlzLmFjY291bnRTdGF0dXMgPSBibGlzdCgiYWNjb3VudFN0YXR1cyIpOw0KICAgIHRoaXMuY2F0ZWdvcnlzID0gYmxpc3QoImFtbWV0ZXJDYXRlZ29yeSIpOw0KICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICBnZXRVc2VyQnlVc2VyUm9sZSgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgLy/moLnmja7mnYPpmZDojrflj5bliIblhazlj7gNCiAgICAgIHRoYXQuY29tcGFuaWVzID0gcmVzLmRhdGEuY29tcGFuaWVzOw0KICAgICAgaWYgKA0KICAgICAgICByZXMuZGF0YS5pc0NpdHlBZG1pbiA9PSB0cnVlIHx8DQogICAgICAgIHJlcy5kYXRhLmlzUHJvQWRtaW4gPT0gdHJ1ZSB8fA0KICAgICAgICByZXMuZGF0YS5pc1N1YkFkbWluID09IHRydWUNCiAgICAgICkgew0KICAgICAgICB0aGF0LmlzQWRtaW4gPSB0cnVlOw0KICAgICAgfQ0KICAgICAgZ2V0Q291bnRyeXNkYXRhKHsgb3JnQ29kZTogcmVzLmRhdGEuY29tcGFuaWVzWzBdLmlkIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAvL+agueaNruadg+mZkOiOt+WPluaJgOWxnumDqOmXqA0KICAgICAgICB0aGF0LmRlcGFydG1lbnRzID0gcmVzLmRhdGE7DQogICAgICAgIHRoYXQuZ2V0VXNlckRhdGEoKTsNCiAgICAgIH0pOw0KICAgIH0pOw0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC4uLm1hcFN0YXRlKHsNCiAgICAgIGxvZ2luSWQ6IChzdGF0ZSkgPT4gc3RhdGUudXNlci5sb2dpbklkLA0KICAgIH0pLA0KICB9LA0KICBtZXRob2RzOiB7DQogICAgYnV0dG9ubG9hZDIodikgew0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5idXR0b25sb2FkMiA9IHY7DQogICAgfSwNCiAgICBpc0J1dHRvbmxvYWQodikgew0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5idXR0b25sb2FkID0gdjsNCiAgICB9LA0KICAgIGlzU2hvd3ModCkgew0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5zaG93ID0gdDsNCiAgICAgIGlmICh0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnNob3cgPT0gZmFsc2UpIHsNCiAgICAgICAgdGhpcy5udW1iZXIyKys7DQogICAgICAgIHRoaXMuaXNUID0gdDsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5zaG93ID09IHRydWUpIHsNCiAgICAgICAgdGhpcy5udW1iZXIyID0gMDsNCiAgICAgICAgdGhpcy5pc1QgPSAhdDsNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmlzVCAmJiB0aGlzLm51bWJlcjIgPCAxMCkgew0KICAgICAgICB0aGlzLmlzU2hvd3ModCk7DQogICAgICB9DQogICAgfSwNCiAgICBuZXh0Q2hlY2soKSB7DQogICAgICB0aGlzLnNob3dBbGFybU1vZGVsID0gdHJ1ZTsNCiAgICAgIHRoaXMuaXNTaG93cyh0cnVlKTsNCiAgICAgIC8vIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwuc2hvdz10cnVlDQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnRhYmxlRGF0YSA9IFtdOw0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5yZXN1bHRMaXN0ID0gW107DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnRhYmxlRGF0YTEgPSBbXTsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwucmVzdWx0TGlzdDEgPSBbXTsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwudGFibGVEYXRhMiA9IFtdOw0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5yZXN1bHRMaXN0MiA9IFtdOw0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGEzID0gW107DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnJlc3VsdExpc3QzID0gW107DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnRhYmxlRGF0YTQgPSBbXTsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwucmVzdWx0TGlzdDQgPSBbXTsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwudGFibGVEYXRhNSA9IFtdOw0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5yZXN1bHRMaXN0NSA9IFtdOw0KICAgICAgLy8gc2V0VGltZW91dCgoKSA9PiB7DQogICAgICB0aGlzLnNob3dKaE1vZGVsID0gZmFsc2U7DQogICAgICAvLyB0aGlzLnNob3dBbGFybU1vZGVsPXRydWU7DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLmFjdGl2ZUJ1dHRvbiA9IDY7DQogICAgICAvLyB9KQ0KICAgIH0sDQogICAgYWxhcm1DbG9zZSgpIHsNCiAgICAgIC8vIHdpbmRvdy5oaXN0b3J5LmdvKDApOw0KICAgICAgdGhpcy5zaG93QWxhcm1Nb2RlbCA9IGZhbHNlOw0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5zaG93ID0gZmFsc2U7DQogICAgfSwNCiAgICBjaGVja0NhbmNlbCgpIHsNCiAgICAgIHRoaXMuc2hvd0poTW9kZWwgPSBmYWxzZTsNCiAgICB9LA0KICAgIGFsYXJtQ2hlY2soKSB7fSwNCiAgICBzZWxlY3RDaGFuZ2UoKSB7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICBpZiAodGhhdC5hY2NvdW50T2JqLmNvbXBhbnkgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgIGlmICh0aGF0LmFjY291bnRPYmouY29tcGFueSA9PSAiLTEiKSB7DQogICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnkgPSAtMTsNCiAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeU5hbWUgPSBudWxsOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGdldENvdW50cnlCeVVzZXJJZCh0aGF0LmFjY291bnRPYmouY29tcGFueSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLmRhdGEuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApIHsNCiAgICAgICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnkgPSByZXMuZGF0YS5kZXBhcnRtZW50c1swXS5pZDsNCiAgICAgICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnlOYW1lID0gcmVzLmRhdGEuZGVwYXJ0bWVudHNbMF0ubmFtZTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jlvIDlp4sNCiAgICBjaG9vc2VSZXNwb25zZUNlbnRlcigpIHsNCiAgICAgIGlmICh0aGlzLmFjY291bnRPYmouY29tcGFueSA9PSBudWxsIHx8IHRoaXMuYWNjb3VudE9iai5jb21wYW55ID09ICItMSIpIHsNCiAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLor7flhYjpgInmi6nliIblhazlj7giKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy4kcmVmcy5jb3VudHJ5TW9kYWwuY2hvb3NlKHRoaXMuYWNjb3VudE9iai5jb21wYW55KTsgLy/miYDlsZ7pg6jpl6gNCiAgICB9LA0KICAgIGdldERhdGFGcm9tTW9kYWwoZGF0YSkgew0KICAgICAgdGhpcy5hY2NvdW50T2JqLmNvdW50cnkgPSBkYXRhLmlkOw0KICAgICAgdGhpcy5hY2NvdW50T2JqLmNvdW50cnlOYW1lID0gZGF0YS5uYW1lOw0KICAgICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jnu5PmnZ8NCiAgICB9LA0KICAgIGdldFVzZXJEYXRhKCkgew0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgZ2V0VXNlcmRhdGEoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgLy/lvZPliY3nmbvlvZXnlKjmiLfmiYDlnKjlhazlj7jlkozmiYDlsZ7pg6jpl6gNCiAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllcy5sZW5ndGggIT0gMCkgew0KICAgICAgICAgIGxldCBjb21wYW5pZXMgPSByZXMuZGF0YS5jb21wYW5pZXM7DQogICAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllc1swXS5pZCA9PSAiMjYwMDAwMDAwMCIpIHsNCiAgICAgICAgICAgIGNvbXBhbmllcyA9IHRoYXQuY29tcGFuaWVzOw0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGF0LmNvbXBhbnkgPSBjb21wYW5pZXNbMF0uaWQ7DQogICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvbXBhbnkgPSBjb21wYW5pZXNbMF0uaWQ7DQogICAgICAgIH0NCiAgICAgICAgaWYgKHJlcy5kYXRhLmRlcGFydG1lbnRzLmxlbmd0aCAhPSAwKSB7DQogICAgICAgICAgbGV0IGRlcGFydG1lbnRzID0gcmVzLmRhdGEuZGVwYXJ0bWVudHM7DQogICAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllc1swXS5pZCA9PSAiMjYwMDAwMDAwMCIgJiYgdGhhdC5kZXBhcnRtZW50cy5sZW5ndGggIT0gMCkgew0KICAgICAgICAgICAgZGVwYXJ0bWVudHMgPSB0aGF0LmRlcGFydG1lbnRzOw0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGF0LmNvdW50cnkgPSBkZXBhcnRtZW50c1swXS5pZDsNCiAgICAgICAgICB0aGF0LmNvdW50cnlOYW1lID0gZGVwYXJ0bWVudHNbMF0ubmFtZTsNCiAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeSA9IE51bWJlcihkZXBhcnRtZW50c1swXS5pZCk7DQogICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnlOYW1lID0gZGVwYXJ0bWVudHNbMF0ubmFtZTsNCiAgICAgICAgfQ0KDQogICAgICAgIHRoYXQucGFnZU51bSA9IDE7DQogICAgICAgIHRoYXQuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIHNlYXJjaExpc3QoKSB7DQogICAgICBpZiAodGhpcy5hY2NvdW50T2JqLmNvdW50cnlOYW1lID09ICIiKSB7DQogICAgICAgIHRoaXMuYWNjb3VudE9iai5jb3VudHJ5ID0gIi0xIjsNCiAgICAgIH0NCiAgICAgIHRoaXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgIH0sDQogICAgYWNjb3VudG5vQ2hhbmdlKCkgew0KICAgICAgdGhpcy5zZWFyY2hMaXN0KCk7DQogICAgfSwNCiAgICBzZXRBbW1ldGVyRGF0YTogZnVuY3Rpb24gKGRhdGEpIHsNCiAgICAgIGxldCBhcnJheURhdGEgPSBbXTsNCiAgICAgIGxldCBjdGdLZXlMaXN0ID0gW107DQogICAgICBsZXQgbm8gPSB0aGlzLmFjY291bnRPYmouYWNjb3VudG5vOw0KICAgICAgaWYgKGRhdGEgIT0gbnVsbCAmJiBkYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgbGV0IG9iaiA9IHt9Ow0KICAgICAgICAgIG9iai5wY2lkID0gbnVsbDsNCiAgICAgICAgICBvYmouYW1tZXRlck5hbWUgPSBpdGVtLmFtbWV0ZXJuYW1lOw0KICAgICAgICAgIG9iai5wcm9qZWN0TmFtZSA9IGl0ZW0ucHJvamVjdG5hbWU7DQogICAgICAgICAgb2JqLnN1YnN0YXRpb24gPSBpdGVtLnN1YnN0YXRpb247DQogICAgICAgICAgb2JqLmNhdGVnb3J5bmFtZSA9IGl0ZW0uY2F0ZWdvcnluYW1lOw0KICAgICAgICAgIG9iai5jYXRlZ29yeSA9IGl0ZW0uY2F0ZWdvcnk7DQogICAgICAgICAgb2JqLmFtbWV0ZXJpZCA9IGl0ZW0uYW1tZXRlcmlkOw0KICAgICAgICAgIG9iai5jb21wYW55ID0gaXRlbS5jb21wYW55Ow0KICAgICAgICAgIG9iai5jb21wYW55TmFtZSA9IGl0ZW0uY29tcGFueU5hbWU7DQogICAgICAgICAgb2JqLmNvdW50cnkgPSBpdGVtLmNvdW50cnk7DQogICAgICAgICAgb2JqLmNvdW50cnlOYW1lID0gaXRlbS5jb3VudHJ5TmFtZTsNCiAgICAgICAgICBvYmouc3RhcnRkYXRlID0gbnVsbDsNCiAgICAgICAgICBvYmouZW5kZGF0ZSA9IG51bGw7DQogICAgICAgICAgb2JqLmN1cnVzZWRyZWFkaW5ncyA9IDA7DQogICAgICAgICAgb2JqLnRyYW5zZm9ybWVydWxsYWdlID0gMDsNCiAgICAgICAgICBvYmoudW5pdHBpcmNlID0gMDsNCiAgICAgICAgICBvYmouYWNjb3VudG1vbmV5ID0gMDsNCiAgICAgICAgICBvYmoucmVtYXJrID0gbnVsbDsNCiAgICAgICAgICBvYmouZWxlY3Ryb3R5cGUgPSBpdGVtLmVsZWN0cm90eXBlOw0KICAgICAgICAgIG9iai5zdGF0aW9uY29kZTVnciA9IGl0ZW0uc3RhdGlvbmNvZGU1Z3I7DQogICAgICAgICAgb2JqLnN0YXRpb25uYW1lNWdyID0gaXRlbS5zdGF0aW9ubmFtZTVncjsNCiAgICAgICAgICBvYmouZWxlY3Ryb3R5cGVuYW1lID0gaXRlbS5lbGVjdHJvdHlwZW5hbWU7DQogICAgICAgICAgb2JqLnN0YXRpb25OYW1lID0gaXRlbS5zdGF0aW9uTmFtZTsNCiAgICAgICAgICBvYmouc3RhcnRkYXRlID0gZ2V0Rmlyc3REYXRlQnlBY2NvdW50bm9feXl5eW1tZGQobm8pOw0KICAgICAgICAgIG9iai5lbmRkYXRlID0gZ2V0TGFzdERhdGVCeUFjY291bnRub195eXl5bW1kZChubyk7DQogICAgICAgICAgb2JqLmFjY291bnRlc3R5cGUgPSAxOw0KICAgICAgICAgIG9iai5zdXBwbHlidXJlYXVhbW1ldGVyY29kZSA9IGl0ZW0uc3VwcGx5YnVyZWF1YW1tZXRlcmNvZGU7DQogICAgICAgICAgb2JqLmRpcmVjdHN1cHBseWZsYWcgPSBpdGVtLmRpcmVjdHN1cHBseWZsYWc7DQogICAgICAgICAgb2JqLnN0YXRpb25hZGRyZXNzY29kZSA9IGl0ZW0uc3RhdGlvbmFkZHJlc3Njb2RlOw0KICAgICAgICAgIGFycmF5RGF0YS5wdXNoKG9iaik7DQogICAgICAgICAgY3RnS2V5TGlzdC5wdXNoKHsgY3RnS2V5OiBpdGVtLmN0Z0tleSwgYW1tZXRlcm5hbWU6IGl0ZW0uYW1tZXRlcm5hbWUgfSk7DQogICAgICAgIH0pOw0KICAgICAgICB0aGlzLmN0Z0tleUxpc3QgPSBjdGdLZXlMaXN0Ow0KICAgICAgfQ0KDQogICAgICBsZXQgdmVyc2lvbiA9IGluZGV4RGF0YS52ZXJzaW9uOw0KICAgICAgbGV0IG9yaWdpbiA9IHRoaXMudGJBY2NvdW50LmRhdGE7DQogICAgICBpZiAob3JpZ2luLmxlbmd0aCA8IDEpIHsNCiAgICAgICAgdGhpcy50YkFjY291bnQuZGF0YSA9IGFycmF5RGF0YTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldCB0ZW0gPSBhcnJheURhdGE7DQogICAgICAgIGlmICgic2MiID09IHZlcnNpb24pIHsNCiAgICAgICAgICBvcmlnaW4uZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgZm9yIChsZXQgaiA9IHRlbS5sZW5ndGggLSAxOyBqID49IDA7IGotLSkgew0KICAgICAgICAgICAgICBsZXQgamogPSB0ZW1bal07DQogICAgICAgICAgICAgIGlmIChpdGVtLmFtbWV0ZXJpZCA9PT0gamouYW1tZXRlcmlkKSB7DQogICAgICAgICAgICAgICAgdGVtLnNwbGljZShqLCAxKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICAgIGxldCB0b3RhbCA9IHRoaXMucGFnZVRvdGFsOw0KICAgICAgICB0aGlzLnBhZ2VUb3RhbCA9IHRvdGFsICsgdGVtLmxlbmd0aDsNCiAgICAgICAgdGhpcy50YkFjY291bnQuZGF0YSA9IHRlbS5jb25jYXQodGhpcy50YkFjY291bnQuZGF0YSk7DQogICAgICB9DQoNCiAgICAgIHRoaXMuc2V0TXlTdHlsZSh0aGlzLnRiQWNjb3VudC5kYXRhLmxlbmd0aCk7DQogICAgfSwNCiAgICAvL+eCueWHu+S/neWtmA0KICAgIGFzeW5jIHByZXNlcnZlKCkgew0KICAgICAgbGV0IGRhdGFMID0gdGhpcy4kcmVmcy5hY2NvdW50RXNUYWJsZS5nZXRTZWxlY3Rpb24oKTsNCiAgICAgIGxldCBiID0gZmFsc2U7DQogICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGE7DQogICAgICBsZXQgYXJyYXkgPSBbXTsNCiAgICAgIGxldCB2ZXJzaW9uID0gaW5kZXhEYXRhLnZlcnNpb247DQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGRhdGFMLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGlmIChkYXRhTFtpXS5lZGl0VHlwZSA9PSAxKSB7DQogICAgICAgICAgaWYgKCJzYyIgPT0gdmVyc2lvbiAmJiBkYXRhTFtpXS5lbGVjdHJvdHlwZSAmJiBkYXRhTFtpXS5lbGVjdHJvdHlwZSA+IDE0MDApIHsNCiAgICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICAgZGF0YUxbaV0uc3RhdGlvbmNvZGU1Z3IgPT0gbnVsbCB8fA0KICAgICAgICAgICAgICBkYXRhTFtpXS5zdGF0aW9uY29kZTVnciA9PSB1bmRlZmluZWQgfHwNCiAgICAgICAgICAgICAgZGF0YUxbaV0uc3RhdGlvbmNvZGU1Z3IgPT0gIiINCiAgICAgICAgICAgICkgew0KICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygNCiAgICAgICAgICAgICAgICAi55S16KGoL+WNj+iurue8luWPt+OAkCIgKw0KICAgICAgICAgICAgICAgICAgZGF0YUxbaV0uYW1tZXRlck5hbWUgKw0KICAgICAgICAgICAgICAgICAgIuOAke+8jOmhueebruWQjeensOOAkCIgKw0KICAgICAgICAgICAgICAgICAgZGF0YUxbaV0ucHJvamVjdE5hbWUgKw0KICAgICAgICAgICAgICAgICAgIuOAkeWFs+iBlOWxgOermeeahDVHUuermeWdgOS4uuepuu+8jOivt+WujOWWhOWxgOermeS/oeaBr++8jOaIluiAhTVHUuacieaViOaAp+a4heWNleWkseaViO+8jOivt+iBlOezu+aXoOe6v+euoeeQhuWRmOOAgiINCiAgICAgICAgICAgICAgKTsNCiAgICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICAvL+aIquatouaXpeacn+agoemqjA0KICAgICAgICAgIGxldCByZXN1bHQgPSBfdmVyaWZ5X0VuZERhdGUoZGF0YUxbaV0sIGRhdGFMW2ldLmVuZGRhdGUpOw0KICAgICAgICAgIGlmIChyZXN1bHQpIHsNCiAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKHJlc3VsdCk7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KICAgICAgICAgIGIgPSB0cnVlOw0KICAgICAgICAgIGFycmF5LnB1c2goZGF0YUxbaV0pOw0KICAgICAgICB9DQogICAgICB9DQogICAgICAvLyB9KTsNCiAgICAgIGlmIChiKSB7DQogICAgICAgIHRoaXMuc3VibWl0RGF0YShhcnJheSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmVycm9yVGlwcygi5rKh5pyJ5Y+v5L+d5a2Y5pWw5o2uIik7DQogICAgICB9DQogICAgfSwNCiAgICBwcmVzZXJ2ZVNjKCkgew0KICAgICAgdGhpcy4kcmVmcy5jaGVja1Jlc3VsdC5hbW1ldGVyaWRzID0gdGhpcy5hbW1ldGVyaWRzOw0KICAgICAgdGhpcy5zaG93SmhNb2RlbCA9IHRydWU7DQogICAgfSwNCiAgICBzdWJtaXRDaGFuZ2UoaW5kZXhMaXN0KSB7DQogICAgICBsZXQgZGF0YSA9IFtdOw0KICAgICAgdGhpcy5zdWJtaXQyLm1hcCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgaW5kZXhMaXN0Lm1hcCgoaXRlbTIpID0+IHsNCiAgICAgICAgICBpZiAoaW5kZXggPT0gaXRlbTIpIHsNCiAgICAgICAgICAgIGRhdGEucHVzaChpdGVtKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgICB0aGlzLnN1Ym1pdCA9IGRhdGE7DQogICAgfSwNCiAgICBhc3luYyBnZXRDb250cmFjdEluZm8oc3RhdGlvbkNvZGUpIHsNCiAgICAgIHRoaXMuY29udHJhY3RDb3VudCA9IDA7DQogICAgICBhd2FpdCB2YWxpZENvbnRyYWN0TGlzdCh7IHN0YXRpb25Db2RlOiBzdGF0aW9uQ29kZSB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5kYXRhKSB7DQogICAgICAgICAgdGhpcy5jb250cmFjdENvdW50ID0gcmVzLmRhdGEubGVuZ3RoOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldEF1ZGl0UmVzdWx0TmV3KGRhdGEpIHsNCiAgICAgIGxldCBhcnIgPSBbXTsNCiAgICAgIGRhdGEuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICBhcnIucHVzaChpdGVtLnBjaWQpOw0KICAgICAgfSk7DQogICAgICBsZXQgcGFyYW0gPSB7DQogICAgICAgIHBjaWRzOiBhcnIsDQogICAgICB9Ow0KICAgICAgZ2V0QXVkaXRSZXN1bHROZXdfUVhNKHBhcmFtKS50aGVuKChyZXMyKSA9PiB7DQogICAgICAgIHRoaXMuYXVkaXRSZXN1bHRMaXN0ID0gcmVzMi5kYXRhOw0KICAgICAgICB0aGlzLmF1ZGl0UmVzdWx0TGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5yZXN1bHRMaXN0LnB1c2goaXRlbS5tc2cpOw0KICAgICAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwudGFibGVEYXRhLnB1c2goaXRlbS5wb3dlckF1ZGl0RW50aXR5KTsNCiAgICAgICAgICBpZiAoaXRlbS5zdGF1dGUgPT0gIuWksei0pSIpIHsNCiAgICAgICAgICAgIC8vIGlmKGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5tdXRpSnRsdGVDb2Rlcz09J+aYrycNCiAgICAgICAgICAgIC8vIHx8IGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5lbGVjdHJpY2l0eVByaWNlcz09J+WQpicNCiAgICAgICAgICAgIC8vIHx8IGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5hZGRyZXNzQ29uc2lzdGVuY2U9PSflkKYnDQogICAgICAgICAgICAvLyB8fCBpdGVtLnBvd2VyQXVkaXRFbnRpdHkucmVpbWJ1cnNlbWVudEN5Y2xlPT0n5ZCmJyB8fCBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZWxlY3RyaWNpdHlDb250aW51aXR5PT0n5ZCmJyB8fA0KICAgICAgICAgICAgLy8gaXRlbS5wb3dlckF1ZGl0RW50aXR5LnNoYXJlQWNjdXJhY3k9PSflkKYnIHx8DQogICAgICAgICAgICAvLyBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZXhjbHVzaXZlQWNjdXJhY3k9PSflkKYnfHwNCiAgICAgICAgICAgIC8vIGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5wYXltZW50Q29uc2lzdGVuY2U9PSflkKYnKXsNCiAgICAgICAgICAgIGlmIChpdGVtLnBvd2VyQXVkaXRFbnRpdHkubXV0aUp0bHRlQ29kZXMgPT0gIuaYryIpIHsNCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGE0LnB1c2goaXRlbS5wb3dlckF1ZGl0RW50aXR5KTsNCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5yZXN1bHRMaXN0NC5wdXNoKGl0ZW0ubXNnKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmIChpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZWxlY3RyaWNpdHlQcmljZXMgPT0gIuWQpiIpIHsNCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGE1LnB1c2goaXRlbS5wb3dlckF1ZGl0RW50aXR5KTsNCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5yZXN1bHRMaXN0NS5wdXNoKGl0ZW0ubXNnKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICAgaXRlbS5wb3dlckF1ZGl0RW50aXR5LmFkZHJlc3NDb25zaXN0ZW5jZSA9PSAi5ZCmIiB8fA0KICAgICAgICAgICAgICBpdGVtLnBvd2VyQXVkaXRFbnRpdHkucmVpbWJ1cnNlbWVudEN5Y2xlID09ICLlkKYiIHx8DQogICAgICAgICAgICAgIGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5lbGVjdHJpY2l0eUNvbnRpbnVpdHkgPT0gIuWQpiIgfHwNCiAgICAgICAgICAgICAgaXRlbS5wb3dlckF1ZGl0RW50aXR5LnNoYXJlQWNjdXJhY3kgPT0gIuWQpiIgfHwNCiAgICAgICAgICAgICAgLy8gICBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZXhjbHVzaXZlQWNjdXJhY3k9PSflkKYnfHwNCiAgICAgICAgICAgICAgaXRlbS5wb3dlckF1ZGl0RW50aXR5LnBheW1lbnRDb25zaXN0ZW5jZSA9PSAi5ZCmIiB8fA0KICAgICAgICAgICAgICBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZmx1Y3R1YXRlQ29udGludWl0eSA9PSAi5ZCmIg0KICAgICAgICAgICAgKSB7DQogICAgICAgICAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwudGFibGVEYXRhMi5wdXNoKGl0ZW0ucG93ZXJBdWRpdEVudGl0eSk7DQogICAgICAgICAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwucmVzdWx0TGlzdDIucHVzaChpdGVtLm1zZyk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICAgLy8gaXRlbS5wb3dlckF1ZGl0RW50aXR5LmVsZWN0cmljaXR5UmF0aW9uYWxpdHkgPT0gIuaYryIgJiYgLy/nlLXph4/lkIjnkIbmgKco55yB5YaF5aSn5pWw5o2uKQ0KICAgICAgICAgICAgICAvLyBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZXhjbHVzaXZlQWNjdXJhY3kgPT0gIuaYryIgJiYgLy/lsYDnq5nni6zkuqvlhbHkuqvorr7nva4NCiAgICAgICAgICAgICAgaXRlbS5wb3dlckF1ZGl0RW50aXR5LnBlcmlvZGljQW5vbWFseSA9PSAi5pivIiAvL+WPsOi0puWRqOacn+WQiOeQhuaApw0KICAgICAgICAgICAgKSB7DQogICAgICAgICAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwudGFibGVEYXRhMS5wdXNoKGl0ZW0ucG93ZXJBdWRpdEVudGl0eSk7DQogICAgICAgICAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwucmVzdWx0TGlzdDEucHVzaChpdGVtLm1zZyk7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnRhYmxlRGF0YTMucHVzaChpdGVtLnBvd2VyQXVkaXRFbnRpdHkpOw0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnJlc3VsdExpc3QzLnB1c2goaXRlbS5tc2cpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAodGhpcy5hdWRpdFJlc3VsdExpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy5hdWRpdFJlc3VsdExpc3RbdGhpcy5hdWRpdFJlc3VsdExpc3QubGVuZ3RoIC0gMV0ucHJvZ3Jlc3MgPSAxOw0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnByb2Nlc3NEYXRhID0gTnVtYmVyKGl0ZW0ucHJvZ3Jlc3MpICogMTAwOw0KICAgICAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwuc2Nyb2xsTGlzdCgpOw0KICAgICAgICB9LCAxMDAwKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgc2F2ZSh2YWx1ZSkgew0KICAgICAgdmFyIHRoYXQgPSB0aGlzOw0KICAgICAgLy/ov5vluqbmnaHpobXpnaLpk77mjqV3ZWJzb2tldOWQjuiwg+eUqO+8jOS8oGpo5a2X5q615LiN6LWw5L+d5a2Y5rWB56iL77yM6LWw56i95qC45rWB56iLDQogICAgICBpZiAodmFsdWUgPT0gMSkgew0KICAgICAgICB0aGF0LnN1Ym1pdFswXS5qaCA9ICIxIjsNCiAgICAgICAgdGhhdC5zdWJtaXRbMF0ueW1tYyA9ICLpk4HloZTpooTkvLDnlLXotLnlj7DotKYiOw0KICAgICAgICB0aGF0LnN1Ym1pdC5mb3JFYWNoKChpdGVtMSkgPT4gew0KICAgICAgICAgIHRoaXMuY3RnS2V5TGlzdC5mb3JFYWNoKChpdGVtMikgPT4gew0KICAgICAgICAgICAgaWYgKGl0ZW0xLmFtbWV0ZXJOYW1lID09IGl0ZW0yLmFtbWV0ZXJuYW1lKSB7DQogICAgICAgICAgICAgIGl0ZW0xLmN0Z0tleSA9IGl0ZW0yLmN0Z0tleTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSk7DQogICAgICAgIGlmICh0aGlzLmN0Z0tleUxpc3QubGVuZ3RoID09IDAgJiYgdGhpcy5kYXRhTC5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhpcy5kYXRhTC5mb3JFYWNoKChpdGVtMikgPT4gew0KICAgICAgICAgICAgdGhpcy5jdGdLZXlMaXN0LnB1c2goew0KICAgICAgICAgICAgICBjdGdLZXk6IGl0ZW0yLmN0Z0tleSwNCiAgICAgICAgICAgICAgYW1tZXRlcm5hbWU6IGl0ZW0yLmFtbWV0ZXJuYW1lLA0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCg0KICAgICAgICB0aGlzLmdldEF1ZGl0UmVzdWx0TmV3KHRoaXMuc3VibWl0KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmICh0aGF0LnN1Ym1pdFswXS5oYXNPd25Qcm9wZXJ0eSgiamgiKSkgew0KICAgICAgICAgIGRlbGV0ZSB0aGF0LnN1Ym1pdFswXS5qaDsNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5uYW1lID09ICJjdXJyZW50Iikgew0KICAgICAgICAgIGlmICh0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLmZyb21HdWlqaWRhbiA9PSAxKSB7DQogICAgICAgICAgICB0aGlzLnNlbGVjdGVkRlFLKCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRBY2NvdW50KCk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMubmFtZSA9PSAiYWxsIikgew0KICAgICAgICAgIGlmICh0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLmZyb21HdWlqaWRhbiA9PSAxKSB7DQogICAgICAgICAgICB0aGlzLnNlbGVjdGVkRlFLKCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRBbGxBY2NvdW50KCk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvL+WKoOWFpeW9kumbhuWNle+8jOWFqOmDqOmdnuW8uuaOpw0KICAgIHNlbGVjdGVkRlFLKCkgew0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgdGhhdC4kcmVmcy5hZGRCaWxsUGVyLmluaXRBbW1ldGVyKA0KICAgICAgICB0aGF0LiRyZWZzLnNob3dBbGFybU1vZGVsLnNlbGVjdElkczMsDQogICAgICAgIDcsDQogICAgICAgIHRoaXMuYWNjb3VudE9iai5jb3VudHJ5DQogICAgICApOw0KICAgIH0sDQogICAgLy/mj5DkuqTmlbDmja4NCiAgICBzdWJtaXREYXRhKGRhdGEpIHsNCiAgICAgIGxldCBhID0gW107DQogICAgICBsZXQgdmVyc2lvbiA9IGluZGV4RGF0YS52ZXJzaW9uOw0KICAgICAgaWYgKGRhdGEgIT0gbnVsbCAmJiBkYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbGV0IG51bWJlciA9IDA7DQogICAgICAgIGxldCBzdWJtaXREYXRhID0gW107DQogICAgICAgIGxldCBzdHIgPSAiIjsNCiAgICAgICAgbGV0IGFjY291bnRubyA9IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm87DQogICAgICAgIGxldCBpbmRleCA9IDA7DQogICAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgaWYgKGl0ZW0ucHJvamVjdE5hbWUgIT0gIuWwj+iuoSIgJiYgaXRlbS5wcm9qZWN0TmFtZSAhPSAi5ZCI6K6hIikgew0KICAgICAgICAgICAgbGV0IG9iaiA9IHZlcmlmaWNhdGlvbihpdGVtKTsNCiAgICAgICAgICAgIGlmIChvYmoucmVzdWx0KSB7DQogICAgICAgICAgICAgIGlmIChpdGVtLnBjaWQgPT0gbnVsbCkgew0KICAgICAgICAgICAgICAgIGl0ZW0uYWNjb3VudG5vID0gYWNjb3VudG5vOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIGEucHVzaChpdGVtLmFtbWV0ZXJpZCk7DQogICAgICAgICAgICAgIHN1Ym1pdERhdGEucHVzaChpdGVtKTsNCiAgICAgICAgICAgICAgbnVtYmVyKys7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBzdHIgKz0NCiAgICAgICAgICAgICAgICAi55S16KGoL+WNj+iurue8luWPt+S4uuOAkCIgKw0KICAgICAgICAgICAgICAgIGl0ZW0uYW1tZXRlck5hbWUgKw0KICAgICAgICAgICAgICAgICLjgJHnmoTlj7DotKbpqozor4HmsqHmnInpgJrov4fvvJrjgJAiICsNCiAgICAgICAgICAgICAgICBvYmouc3RyICsNCiAgICAgICAgICAgICAgICAi44CR77ybIjsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgICB0aGF0LmFtbWV0ZXJpZHMgPSBhOw0KICAgICAgICBpZiAoc3RyLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcyhzdHIpOw0KICAgICAgICB9DQogICAgICAgIGlmIChzdWJtaXREYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLnN1Ym1pdCA9IHN1Ym1pdERhdGE7DQoNCiAgICAgICAgICB0aGlzLnN1Ym1pdDIgPSBzdWJtaXREYXRhOw0KICAgICAgICAgIGFkZEFjY291bnRFcyhzdWJtaXREYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuZGF0YS5jb2RlID09IDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKHsNCiAgICAgICAgICAgICAgICBjb250ZW50OiAi5o+Q56S677ya5oiQ5Yqf5L+d5a2YICIgKyBzdWJtaXREYXRhLmxlbmd0aCArICIg5p2h5pWw5o2uIiwNCiAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTAsDQogICAgICAgICAgICAgICAgY2xvc2FibGU6IHRydWUsDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBhZGRFbGVjdHJpY1R5cGUoKSB7DQogICAgICBsZXQgY29tcGFueUlkID0gdGhpcy5hY2NvdW50T2JqLmNvbXBhbnk7DQogICAgICBsZXQgY291bnRyeSA9IHRoaXMuYWNjb3VudE9iai5jb3VudHJ5Ow0KICAgICAgaWYgKGNvbXBhbnlJZCAhPSBudWxsICYmIGNvdW50cnkgIT0gbnVsbCkgew0KICAgICAgICBsZXQgb2JqID0gew0KICAgICAgICAgIGNvbXBhbnk6IGNvbXBhbnlJZCwNCiAgICAgICAgICBjb3VudHJ5OiBjb3VudHJ5LA0KICAgICAgICAgIGFjY291bnRubzogdGhpcy5hY2NvdW50T2JqLmFjY291bnRubywNCiAgICAgICAgICBhY2NvdW50VHlwZTogIjIiLA0KICAgICAgICAgIGFjY291bnRlc3R5cGU6IDEsDQogICAgICAgIH07DQogICAgICAgIHRoaXMuJHJlZnMuc2VsZWN0QW1tZXRlci5pbml0QW1tZXRlcihvYmopOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5lcnJvclRpcHMoIuivt+mAieaLqeWIhuWFrOWPuOWSjOmDqOmXqCIpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy/pqozor4HplJnor6/lvLnlh7rmj5DnpLrmoYYNCiAgICBlcnJvclRpcHMoc3RyKSB7DQogICAgICB0aGlzLiROb3RpY2UuZXJyb3Ioew0KICAgICAgICB0aXRsZTogIuaPkOekuiIsDQogICAgICAgIGRlc2M6IHN0ciwNCiAgICAgICAgZHVyYXRpb246IDEwLA0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVQYWdlKHZhbHVlKSB7DQogICAgICBsZXQgYiA9IGZhbHNlOw0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhOw0KICAgICAgbGV0IGFycmF5ID0gW107DQogICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgaWYgKGl0ZW0uZWRpdFR5cGUgPT0gMSkgew0KICAgICAgICAgIGIgPSB0cnVlOw0KICAgICAgICAgIGFycmF5LnB1c2goaXRlbSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgaWYgKGIpIHsNCiAgICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7DQogICAgICAgICAgdGl0bGU6ICLmj5DnpLoiLA0KICAgICAgICAgIGNvbnRlbnQ6ICI8cD7mgqjmnInlt7LnvJbovpHkv6Hmga/ov5jmsqHmnInkv53lrZjvvIzmmK/lkKbkv53lrZjvvJ88L3A+IiwNCiAgICAgICAgICBvbk9rOiAoKSA9PiB7DQogICAgICAgICAgICB0aGlzLnN1Ym1pdERhdGEoYXJyYXkpOw0KICAgICAgICAgIH0sDQogICAgICAgICAgb25DYW5jZWw6ICgpID0+IHt9LA0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5wYWdlTnVtID0gdmFsdWU7DQogICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgIH0sDQogICAgaGFuZGxlUGFnZVNpemUodmFsdWUpIHsNCiAgICAgIGxldCBiID0gZmFsc2U7DQogICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGE7DQogICAgICBsZXQgYXJyYXkgPSBbXTsNCiAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICBpZiAoaXRlbS5lZGl0VHlwZSA9PSAxKSB7DQogICAgICAgICAgYiA9IHRydWU7DQogICAgICAgICAgYXJyYXkucHVzaChpdGVtKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICBpZiAoYikgew0KICAgICAgICB0aGlzLiRNb2RhbC5jb25maXJtKHsNCiAgICAgICAgICB0aXRsZTogIuaPkOekuiIsDQogICAgICAgICAgY29udGVudDogIjxwPuaCqOacieW3sue8lui+keS/oeaBr+i/mOayoeacieS/neWtmO+8jOaYr+WQpuS/neWtmO+8nzwvcD4iLA0KICAgICAgICAgIG9uT2s6ICgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuc3VibWl0RGF0YShhcnJheSk7DQogICAgICAgICAgfSwNCiAgICAgICAgICBvbkNhbmNlbDogKCkgPT4ge30sDQogICAgICAgIH0pOw0KICAgICAgfQ0KDQogICAgICB0aGlzLnBhZ2VTaXplID0gdmFsdWU7DQogICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgIH0sDQogICAgLy/lkJHlkI7lj7Dor7fmsYLmlbDmja4NCiAgICBnZXRBY2NvdW50TWVzc2FnZXMoKSB7DQogICAgICBsZXQgcGFyYW1zID0gdGhpcy5hY2NvdW50T2JqOw0KICAgICAgcGFyYW1zLnBhZ2VOdW0gPSB0aGlzLnBhZ2VOdW07DQogICAgICBwYXJhbXMucGFnZVNpemUgPSB0aGlzLnBhZ2VTaXplOw0KICAgICAgbGV0IHJlcSA9IHsNCiAgICAgICAgdXJsOiAiL2J1c2luZXNzL2FjY291bnRFcy9zZWxlY3RBY2NvdW50RXNMaXN0IiwNCiAgICAgICAgbWV0aG9kOiAiZ2V0IiwNCiAgICAgICAgcGFyYW1zOiBwYXJhbXMsDQogICAgICB9Ow0KICAgICAgdGhpcy50YkFjY291bnQubG9hZGluZyA9IHRydWU7DQogICAgICBheGlvcw0KICAgICAgICAucmVxdWVzdChyZXEpDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLnRiQWNjb3VudC5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgaWYgKHJlcy5kYXRhKSB7DQogICAgICAgICAgICBsZXQgZGF0YSA9IHJlcy5kYXRhLnJvd3M7DQogICAgICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICAgICAgaXRlbS5lZGl0VHlwZSA9IDA7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIGRhdGEucHVzaCh0aGlzLnN1bnRvdGFsKGRhdGEpKTsgLy/lsI/orqENCiAgICAgICAgICAgIGFjY291bnRFc1RvdGFsKHRoaXMuYWNjb3VudE9iaikudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgIC8v5ZCI6K6hDQogICAgICAgICAgICAgIGxldCBhbGx0b3RhbCA9IHJlcy5kYXRhOw0KICAgICAgICAgICAgICBhbGx0b3RhbC50b3RhbCA9ICLlkIjorqEiOw0KICAgICAgICAgICAgICBhbGx0b3RhbC5wcm9qZWN0TmFtZSA9ICLlkIjorqEiOw0KICAgICAgICAgICAgICBhbGx0b3RhbC5fZGlzYWJsZWQgPSB0cnVlOw0KICAgICAgICAgICAgICBkYXRhLnB1c2goYWxsdG90YWwpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB0aGlzLnRiQWNjb3VudC5kYXRhID0gZGF0YTsNCiAgICAgICAgICAgIHRoaXMucGFnZVRvdGFsID0gcmVzLmRhdGEudG90YWwgfHwgMDsNCiAgICAgICAgICAgIHRoaXMuc2V0TXlTdHlsZSh0aGlzLnRiQWNjb3VudC5kYXRhLmxlbmd0aCk7DQoNCiAgICAgICAgICAgIHRoaXMuZWRpdEluZGV4ID0gLTE7DQogICAgICAgICAgICB0aGlzLmNvbHVtbnNJbmRleCA9IC0xOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnIpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmxvZyhlcnIpOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8v5bCP6K6hDQogICAgc3VudG90YWwoYXJyYXkpIHsNCiAgICAgIGxldCBjdXJ1c2VkcmVhZGluZ3MgPSAwOw0KICAgICAgbGV0IHRyYW5zZm9ybWVydWxsYWdlID0gMDsNCiAgICAgIGxldCBhY2NvdW50bW9uZXkgPSAwOw0KICAgICAgYXJyYXkuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICBpZiAoaXRlbS5lZmZlY3RpdmUgPT09IDEpIHsNCiAgICAgICAgICBjdXJ1c2VkcmVhZGluZ3MgKz0gaXRlbS5jdXJ1c2VkcmVhZGluZ3M7DQogICAgICAgICAgdHJhbnNmb3JtZXJ1bGxhZ2UgKz0gaXRlbS50cmFuc2Zvcm1lcnVsbGFnZTsNCiAgICAgICAgICBhY2NvdW50bW9uZXkgKz0gaXRlbS5hY2NvdW50bW9uZXk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgY3VydXNlZHJlYWRpbmdzOiBjdXJ1c2VkcmVhZGluZ3MsDQogICAgICAgIHRyYW5zZm9ybWVydWxsYWdlOiB0cmFuc2Zvcm1lcnVsbGFnZSwNCiAgICAgICAgYWNjb3VudG1vbmV5OiBhY2NvdW50bW9uZXksDQogICAgICAgIHRvdGFsOiAi5bCP6K6hIiwNCiAgICAgICAgcHJvamVjdE5hbWU6ICLlsI/orqEiLA0KICAgICAgICBfZGlzYWJsZWQ6IHRydWUsDQogICAgICB9Ow0KICAgIH0sDQogICAgLy/ph43nva4NCiAgICBvblJlc2V0SGFuZGxlKCkgew0KICAgICAgdGhpcy5hY2NvdW50T2JqID0gew0KICAgICAgICBzdXBwbHlidXJlYXVhbW1ldGVyY29kZTogIiIsDQogICAgICAgIGFjY291bnRubzogZGF0ZXNbMF0uY29kZSwgLy/mnJ/lj7cs6buY6K6k5b2T5YmN5pyIDQogICAgICAgIGNvbXBhbnk6IHRoaXMuY29tcGFueSwNCiAgICAgICAgcHJvamVjdE5hbWU6ICIiLCAvL+mhueebruWQjeensA0KICAgICAgICBjb3VudHJ5OiBOdW1iZXIodGhpcy5jb3VudHJ5KSwgLy/miYDlsZ7pg6jpl6gNCiAgICAgICAgYW1tZXRlck5hbWU6ICIiLCAvL+eUteihqOaIt+WPty/ljY/orq7nvJbnoIENCiAgICAgICAgc3RhdGlvbk5hbWU6ICIiLA0KICAgICAgICBhY2NvdW50VHlwZTogIjEiLCAvL+WPsOi0puexu+Weiw0KICAgICAgICBhY2NvdW50ZXN0eXBlOiAzLCAvL+WPsOi0puexu+Weiw0KICAgICAgICBzdXBwbHlidXJlYXVhbW1ldGVyY29kZTogIiIsDQogICAgICAgIGNvdW50cnlOYW1lOiB0aGlzLmNvdW50cnlOYW1lLA0KICAgICAgfTsNCiAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgfSwNCiAgICAvL+iuoeeul+WNleS7tw0KICAgIHVuaXRQcmljZShyb3cpIHsNCiAgICAgIGxldCBhY2NvdW50bW9uZXkgPSByb3cuYWNjb3VudG1vbmV5Ow0KICAgICAgbGV0IGN1cnVzZWRyZWFkaW5ncyA9IHJvdy5jdXJ1c2VkcmVhZGluZ3M7DQogICAgICBpZiAoYWNjb3VudG1vbmV5ICE9IG51bGwgJiYgY3VydXNlZHJlYWRpbmdzICE9IG51bGwpIHsNCiAgICAgICAgbGV0IHRvdGFsID0gbnVsbDsNCiAgICAgICAgaWYgKGN1cnVzZWRyZWFkaW5ncyA9PSAwKSB7DQogICAgICAgICAgdG90YWwgPSAwOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRvdGFsID0gYWNjb3VudG1vbmV5IC8gY3VydXNlZHJlYWRpbmdzOw0KICAgICAgICB9DQoNCiAgICAgICAgcm93LnVuaXRwaXJjZSA9IHRvdGFsLnRvRml4ZWQoMik7DQogICAgICB9DQogICAgfSwNCiAgICAvL+mqjOivgeWNleS7tw0KICAgIHZhbGlkYXRlVW5pdFByaWNlKGRhdGEpIHsNCiAgICAgIGxldCBjYXRlZ29yeSA9IGRhdGEuY2F0ZWdvcnk7IC8v55S16KGo5o+P6L+w57G75Z6LDQogICAgICBsZXQgYW1tZXRlcnVzZSA9IGRhdGEuYW1tZXRlcnVzZTsgLy/nlLXooajnlKjpgJQNCiAgICAgIGxldCB1bml0cGlyY2UgPSBkYXRhLnVuaXRwaXJjZTsgLy/lj7DotKbljZXku7cNCiAgICAgIGlmICghanVkZ2VfbmVnYXRlKGNhdGVnb3J5KSAmJiAhanVkZ2VfcmVjb3ZlcnkoYW1tZXRlcnVzZSkpIHsNCiAgICAgICAgLy8gaWYgKHVuaXRwaXJjZSkgew0KICAgICAgICAvLyAgIGlmICh1bml0cGlyY2UgPCB1bml0cGlyY2VNaW4gfHwgdW5pdHBpcmNlID4gdW5pdHBpcmNlTWF4KSB7DQogICAgICAgIC8vICAgICB0aGlzLmVycm9yVGlwcygNCiAgICAgICAgLy8gICAgICAgIumbhuWbouimgeaxguWNleS7t+iMg+WbtOWcqDAuM34y5YWD77yM5q2k5Y+w6LSm5Y2V5Lu3OiAiICsNCiAgICAgICAgLy8gICAgICAgICB1bml0cGlyY2UgKw0KICAgICAgICAvLyAgICAgICAgICIg5bey6LaF6L+H6IyD5Zu077yM6K+356Gu6K6k77yBIg0KICAgICAgICAvLyAgICAgKTsNCiAgICAgICAgLy8gICB9DQogICAgICAgIC8vIH0NCiAgICAgICAgaWYgKHVuaXRwaXJjZSkgew0KICAgICAgICAgIGlmICh1bml0cGlyY2UgIT0gbnVsbCAmJiB1bml0cGlyY2UgPCB1bml0cGlyY2VNYXgxKSB7DQogICAgICAgICAgICAvLyBpZiAodW5pdHBpcmNlIDwgdW5pdHBpcmNlTWluIHx8IHVuaXRwaXJjZSA+IHVuaXRwaXJjZU1heCkgew0KICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoDQogICAgICAgICAgICAgICLljZXku7fojIPlm7Tlv4XpobvlpKfkuo4wLjHlhYPvvIzmraTlj7DotKbljZXku7c6ICIgKyB1bml0cGlyY2UgKyAi5LiN5Zyo6IyD5Zu05YaF77yM6K+356Gu6K6k77yBIg0KICAgICAgICAgICAgKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIHJlbW92ZSgpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy4kcmVmcy5hY2NvdW50RXNUYWJsZS5nZXRTZWxlY3Rpb24oKTsNCiAgICAgIGlmIChkYXRhID09IG51bGwgfHwgZGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy5lcnJvclRpcHMoIuivt+mAieaLqeimgeWIoOmZpOeahOaVsOaNriIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLiRNb2RhbC5jb25maXJtKHsNCiAgICAgICAgdGl0bGU6ICLmj5DnpLoiLA0KICAgICAgICBjb250ZW50OiAiPHA+5piv5ZCm56Gu6K6k5Yig6Zmk6YCJ5Lit5L+h5oGv77yfPC9wPiIsDQogICAgICAgIG9uT2s6ICgpID0+IHsNCiAgICAgICAgICBsZXQgYiA9IHRydWU7DQogICAgICAgICAgbGV0IGlkcyA9ICIiOw0KICAgICAgICAgIGxldCBhcnJheSA9IHRoaXMudGJBY2NvdW50LmRhdGE7DQogICAgICAgICAgbGV0IHRvdGFsID0gdGhpcy5wYWdlVG90YWw7DQogICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhLmxlbmd0aDsgaSsrKSB7DQogICAgICAgICAgICBsZXQgaXRlbSA9IGRhdGFbaV07DQogICAgICAgICAgICBpZiAoaXRlbS5wY2lkICE9IG51bGwgJiYgaXRlbS5wY2lkLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgaWYgKGl0ZW0ucGFicmlpZCkgew0KICAgICAgICAgICAgICAgIGIgPSBmYWxzZTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBpZHMgKz0gaXRlbS5wY2lkICsgIiwiOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgZm9yIChsZXQgaiA9IGFycmF5Lmxlbmd0aCAtIDE7IGogPj0gMDsgai0tKSB7DQogICAgICAgICAgICAgICAgbGV0IGpqID0gYXJyYXlbal07DQogICAgICAgICAgICAgICAgaWYgKGpqLmFtbWV0ZXJpZCA9PT0gaXRlbS5hbW1ldGVyaWQpIHsNCiAgICAgICAgICAgICAgICAgIGFycmF5LnNwbGljZShqLCAxKTsNCiAgICAgICAgICAgICAgICAgIHRvdGFsID0gdG90YWwgLSAxOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLnBhZ2VUb3RhbCA9IHRvdGFsOw0KICAgICAgICAgIGlmIChiKSB7DQogICAgICAgICAgICBpZiAoaWRzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgcmVtb3ZlQWNjb3VudEVzKGlkcykudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLmNvZGUgPT0gMCkgew0KICAgICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5zdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoIumAieS4reS/oeaBr+S4reacieS/oeaBr+i/mOayoeaciei3n+W9kumbhuWNleino+mZpOWFs+iBlO+8jOivt+WFiOino+mZpOWFs+iBlCIpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgb25DYW5jZWw6ICgpID0+IHt9LA0KICAgICAgfSk7DQogICAgfSwNCiAgICAvL+S4gOmUruWIoOmZpOaVsOaNrg0KICAgIGRlbGV0ZUFsbCgpIHsNCiAgICAgIHRoaXMuJE1vZGFsLmNvbmZpcm0oew0KICAgICAgICB0aXRsZTogIuaPkOekuiIsDQogICAgICAgIGNvbnRlbnQ6ICI8cD7noa7lrprkuIDplK7liKDpmaTlkJfvvJ88L3A+IiwNCiAgICAgICAgb25PazogKCkgPT4gew0KICAgICAgICAgIHRoaXMudGJBY2NvdW50LmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgIGxldCBwYXJhbXMgPSB0aGlzLmFjY291bnRPYmo7DQogICAgICAgICAgcGFyYW1zLnJlbW92ZUFsbEZsYWcgPSB0cnVlOw0KICAgICAgICAgIGRlbGV0ZSBwYXJhbXMucGFnZVNpemU7DQogICAgICAgICAgZGVsZXRlIHBhcmFtcy5wYWdlTnVtOw0KICAgICAgICAgIHJlbW92ZUFsbChwYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgdGhpcy50YkFjY291bnQubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgaWYgKHJlcy5kYXRhLm51bSA+IDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5zdWNjZXNzKCLkuIDplK7liKDpmaTmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5zZWFyY2hMaXN0KCk7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmVycm9yKCLkuIDplK7liKDpmaTlpLHotKUiKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgb25DYW5jZWw6ICgpID0+IHt9LA0KICAgICAgfSk7DQogICAgfSwNCiAgICAvL+WKoOWFpeW9kumbhuWNlQ0KICAgIGFkZFByZXNlcnZlR0ooKSB7DQogICAgICBsZXQgZGF0YUwgPSB0aGlzLiRyZWZzLmFjY291bnRFc1RhYmxlLmdldFNlbGVjdGlvbigpOw0KICAgICAgdGhpcy5kYXRhTCA9IHRoaXMuJHJlZnMuYWNjb3VudEVzVGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICBpZiAoZGF0YUwgPT0gbnVsbCB8fCBkYXRhTC5sZW5ndGggPT0gMCkgew0KICAgICAgICB0aGlzLmVycm9yVGlwcygi6K+36YCJ5oup6KaB5Yqg5YWl5b2S6ZuG5Y2V55qE5Y+w6LSmIik7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmFkZFN1Ym1pdERhdGFHSihkYXRhTCk7DQogICAgICB9DQogICAgfSwNCiAgICAvL+WKoOWFpeW9kumbhuWNlQ0KICAgIGFkZFByZXNlcnZlR0pBbGwoKSB7DQogICAgICBsZXQgcGFyYW1zID0gdGhpcy5hY2NvdW50T2JqOw0KICAgICAgcGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgcGFyYW1zLnBhZ2VTaXplID0gMjAwMDA7DQogICAgICBsZXQgcmVxID0gew0KICAgICAgICB1cmw6ICIvYnVzaW5lc3MvYWNjb3VudEVzL3NlbGVjdEFjY291bnRFc0xpc3QiLA0KICAgICAgICBtZXRob2Q6ICJnZXQiLA0KICAgICAgICBwYXJhbXM6IHBhcmFtcywNCiAgICAgIH07DQogICAgICBsZXQgYXJyYXkgPSBbXTsNCiAgICAgIGxldCBhcnJheTEgPSBbXTsNCiAgICAgIHRoaXMudGJBY2NvdW50LmxvYWRpbmcgPSB0cnVlOw0KICAgICAgYXhpb3MucmVxdWVzdChyZXEpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnRiQWNjb3VudC5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIGxldCBkYXRhTCA9IHJlcy5kYXRhLnJvd3M7DQogICAgICAgIHRoaXMuZGF0YUwgPSByZXMuZGF0YS5yb3dzOw0KICAgICAgICB0aGlzLmFkZFN1Ym1pdERhdGFHSihkYXRhTCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8v5o+Q5Lqk5b2S6ZuG5Y2V5pWw5o2uDQogICAgYWRkU3VibWl0RGF0YUdKKGRhdGEpIHsNCiAgICAgIGxldCBhID0gW107DQogICAgICBsZXQgc3RyID0gIiI7DQogICAgICBsZXQgc3RyMSA9ICIiOw0KICAgICAgbGV0IHZlcnNpb24gPSBpbmRleERhdGEudmVyc2lvbjsNCiAgICAgIGlmIChkYXRhICE9IG51bGwgJiYgZGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgIGxldCBiID0gMTsNCiAgICAgICAgbGV0IG51bWJlciA9IDA7DQogICAgICAgIGxldCBzdWJtaXREYXRhID0gW107DQogICAgICAgIGxldCBubyA9IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm87DQogICAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgbGV0IG9iaiA9IHZlcmlmaWNhdGlvbihpdGVtKTsNCiAgICAgICAgICBpZiAob2JqLnJlc3VsdCkgew0KICAgICAgICAgICAgbGV0IHl5eXltbWRkID0gY3V0RGF0ZV95eXl5bW1kZChpdGVtLnN0YXJ0ZGF0ZSk7DQogICAgICAgICAgICBpdGVtLnN0YXJ0eWVhciA9IHl5eXltbWRkLnl5eXk7DQogICAgICAgICAgICBpdGVtLnN0YXJ0bW9udGggPSB5eXl5bW1kZC5tbTsNCiAgICAgICAgICAgIHl5eXltbWRkID0gY3V0RGF0ZV95eXl5bW1kZChpdGVtLmVuZGRhdGUpOw0KICAgICAgICAgICAgaXRlbS5lbmR5ZWFyID0geXl5eW1tZGQueXl5eTsNCiAgICAgICAgICAgIGl0ZW0uZW5kbW9udGggPSB5eXl5bW1kZC5tbTsNCiAgICAgICAgICAgIGEucHVzaChpdGVtLmFtbWV0ZXJpZCk7DQogICAgICAgICAgICBzdWJtaXREYXRhLnB1c2goaXRlbSk7DQogICAgICAgICAgICBudW1iZXIrKzsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgc3RyICs9DQogICAgICAgICAgICAgICLnlLXooagv5Y2P6K6u57yW5Y+35Li644CQIiArDQogICAgICAgICAgICAgIGl0ZW0uYW1tZXRlcmNvZGUgKw0KICAgICAgICAgICAgICAi44CR55qE5Y+w6LSm6aqM6K+B5rKh5pyJ6YCa6L+H77ya44CQIiArDQogICAgICAgICAgICAgIG9iai5zdHIgKw0KICAgICAgICAgICAgICAi44CR77ybIjsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBpZiAoaXRlbS5tYWduaWZpY2F0aW9uZXJyID09IDIpIHsNCiAgICAgICAgICAgIHN0cjEgKz0NCiAgICAgICAgICAgICAgIueUteihqC/ljY/orq7nvJblj7fkuLrjgJAiICsNCiAgICAgICAgICAgICAgaXRlbS5hbW1ldGVyY29kZSArDQogICAgICAgICAgICAgICLjgJHnmoTlj7DotKblgI3njofjgJAiICsNCiAgICAgICAgICAgICAgaXRlbS5tYWduaWZpY2F0aW9uICsNCiAgICAgICAgICAgICAgIuOAkeS4jueUteihqOWAjeeOh+OAkCIgKw0KICAgICAgICAgICAgICBpdGVtLmFtbW11bHR0aW1lcyArDQogICAgICAgICAgICAgICLjgJHkuI3kuIDoh7TvvIEgIDxiciAvPiAiOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGlmIChpdGVtLnBlcmNlbnRlcnIgPT0gMikgew0KICAgICAgICAgICAgc3RyMSArPQ0KICAgICAgICAgICAgICAi55S16KGoL+WNj+iurue8luWPt+S4uuOAkCIgKw0KICAgICAgICAgICAgICBpdGVtLmFtbWV0ZXJjb2RlICsNCiAgICAgICAgICAgICAgIuOAkeeahOWPsOi0puWIhuWJsuavlOS+i+OAkCIgKw0KICAgICAgICAgICAgICBpdGVtLnBlcmNlbnQgKw0KICAgICAgICAgICAgICAi44CR5LiO55S16KGo5YiG5Ymy5q+U5L6L44CQIiArDQogICAgICAgICAgICAgIGl0ZW0uYW1tcGVyY2VudCArDQogICAgICAgICAgICAgICLjgJHkuI3kuIDoh7TvvIEgPGJyIC8+ICI7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChpdGVtLmVmZmVjdGl2ZSAhPSAxKSB7DQogICAgICAgICAgICBiID0gMjsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGl0ZW0uc3RhdHVzID09PSA1KSB7DQogICAgICAgICAgICBiID0gMzsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGl0ZW0uc3RhdHVzID09PSA0KSB7DQogICAgICAgICAgICBiID0gNDsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgICB0aGF0LmFtbWV0ZXJpZHMgPSBhOw0KICAgICAgICBpZiAoYiA9PT0gMSkgew0KICAgICAgICAgIC8vIHRoaXMuJHJlZnMuYWRkQmlsbFBlci5pbml0QW1tZXRlcihpZHMsNyx0aGlzLmFjY291bnRPYmouY291bnRyeSk7DQogICAgICAgICAgaWYgKHN1Ym1pdERhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy5zdWJtaXQgPSBzdWJtaXREYXRhOw0KICAgICAgICAgICAgdGhpcy5zdWJtaXQyID0gc3VibWl0RGF0YTsNCiAgICAgICAgICAgIHRoaXMucHJlc2VydmVTYygpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIGlmIChiID09PSAyKSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoIumAieS4reeahOWPsOi0puS4reWtmOWcqOS4tOaXtuaVsOaNru+8jOivt+WFiOS/neWtmOWGjeWKoOWFpeW9kumbhuWNle+8gSIpOw0KICAgICAgICB9IGVsc2UgaWYgKGIgPT09IDMpIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6YCA5Zue55qE5Y+w6LSm5LiN6IO95Yqg5YWl5YW25a6D5b2S6ZuG5Y2V77yM6K+354K55Ye7W+mHjeaWsOWKoOWFpeW9kumbhuWNlV3mjInpkq4iKTsNCiAgICAgICAgfSBlbHNlIGlmIChiID09PSA0KSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoIumAieaLqeeahOWPsOi0puacieW3suWKoOWFpeW9kumbhuWNleeahOWPsOi0pu+8jOS4jeiDveWKoOWFpeWFtuS7luW9kumbhuWNlSIpOw0KICAgICAgICB9DQogICAgICAgIGlmIChzdHIubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoYXQuZXJyb3JUaXBzKHN0cik7DQogICAgICAgIH0NCiAgICAgICAgaWYgKHN0cjEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoYXQuJE5vdGljZS53YXJuaW5nKHsNCiAgICAgICAgICAgIHRpdGxlOiAi5rOo5oSPIiwNCiAgICAgICAgICAgIGRlc2M6IHN0cjEsDQogICAgICAgICAgICBkdXJhdGlvbjogMCwNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgb3BlbkFkZEJpbGxQZXJNb2RhbChuYW1lKSB7DQogICAgICB0aGlzLm5hbWUgPSBuYW1lOw0KICAgICAgaWYgKG5hbWUgPT09ICJjdXJyZW50Iikgew0KICAgICAgICAvL+mcgOimgeeoveaguA0KICAgICAgICAvLyBpZiAodGhpcy5oYXNCdXR0b25QZXJtKCJqaHNkIikpIHsNCiAgICAgICAgLy8gICB0aGlzLmFkZFByZXNlcnZlR0ooKTsNCiAgICAgICAgLy8gfSBlbHNlIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZEFjY291bnQoKTsNCiAgICAgICAgLy8gfQ0KICAgICAgfSBlbHNlIGlmIChuYW1lID09PSAiYWxsIikgew0KICAgICAgICAvL+mcgOimgeeoveaguA0KICAgICAgICAvLyBpZiAodGhpcy5oYXNCdXR0b25QZXJtKCJqaHNkIikpIHsNCiAgICAgICAgLy8gICB0aGlzLmFkZFByZXNlcnZlR0pBbGwoKTsNCiAgICAgICAgLy8gfSBlbHNlIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZEFsbEFjY291bnQoKTsNCiAgICAgICAgLy8gfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy/liqDlhaXlvZLpm4bljZXvvIzlhajpg6jmnInmlYjlj7DotKYNCiAgICBzZWxlY3RlZEFsbEFjY291bnQoKSB7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICB0aGF0LnNwaW5TaG93ID0gdHJ1ZTsNCiAgICAgIHNlbGVjdElkc0J5RXNQYXJhbXModGhpcy5hY2NvdW50T2JqKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhhdC5zcGluU2hvdyA9IGZhbHNlOw0KICAgICAgICBpZiAocmVzLmRhdGEubGVuZ3RoID09IDApIHsNCiAgICAgICAgICB0aGF0LmVycm9yVGlwcygi5peg5pyJ5pWI5pWw5o2u5Y+v5Yqg5YWl5b2S6ZuG5Y2VIik7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhhdC4kcmVmcy5hZGRCaWxsUGVyLmluaXRBbW1ldGVyKHJlcy5kYXRhLCA3LCB0aGlzLmFjY291bnRPYmouY291bnRyeSk7DQogICAgICAgICAgLy8gdGhhdC4kcmVmcy5hZGRCaWxsUGVyLmluaXRBbW1ldGVyKA0KICAgICAgICAgIC8vICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5zZWxlY3RJZHMxLA0KICAgICAgICAgIC8vICAgNywNCiAgICAgICAgICAvLyAgIHRoaXMuYWNjb3VudE9iai5jb3VudHJ5DQogICAgICAgICAgLy8gKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBzZWxlY3RlZEFjY291bnQoKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMuJHJlZnMuYWNjb3VudEVzVGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICBsZXQgYiA9IDE7DQogICAgICBpZiAoZGF0YSA9PSBudWxsIHx8IGRhdGEubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy5lcnJvclRpcHMoIuivt+mAieaLqeimgeWKoOWFpeW9kumbhuWNleeahOWPsOi0piIpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGV0IGlkcyA9IFtdOw0KICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICBpZiAoaXRlbS5lZmZlY3RpdmUgIT0gMSkgew0KICAgICAgICAgICAgYiA9IDI7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChpdGVtLnN0YXR1cyA9PT0gNSkgew0KICAgICAgICAgICAgYiA9IDM7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChpdGVtLnN0YXR1cyA9PT0gNCkgew0KICAgICAgICAgICAgYiA9IDQ7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlkcy5wdXNoKGl0ZW0ucGNpZCk7DQogICAgICAgIH0pOw0KICAgICAgICBpZiAoYiA9PT0gMSkgew0KICAgICAgICAgIHRoaXMuJHJlZnMuYWRkQmlsbFBlci5pbml0QW1tZXRlcihpZHMsIDcsIHRoaXMuYWNjb3VudE9iai5jb3VudHJ5KTsNCiAgICAgICAgfSBlbHNlIGlmIChiID09PSAyKSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoIumAieS4reeahOWPsOi0puS4reWtmOWcqOS4tOaXtuaVsOaNru+8jOivt+WFiOS/neWtmOWGjeWKoOWFpeW9kumbhuWNle+8gSIpOw0KICAgICAgICB9IGVsc2UgaWYgKGIgPT09IDMpIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6YCA5Zue55qE5Y+w6LSm5LiN6IO95Yqg5YWl5YW25a6D5b2S6ZuG5Y2V77yM6K+354K55Ye7W+mHjeaWsOWKoOWFpeW9kumbhuWNlV3mjInpkq4iKTsNCiAgICAgICAgfSBlbHNlIGlmIChiID09PSA0KSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoIumAieaLqeeahOWPsOi0puacieW3suWKoOWFpeW9kumbhuWNleeahOWPsOi0pu+8jOS4jeiDveWKoOWFpeWFtuS7luW9kumbhuWNlSIpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBvcGVuQ29tcGxldGVkUHJlTW9kYWwoKSB7DQogICAgICB0aGlzLiRyZWZzLmNvbXBsZXRlZFByZS5pbml0QW1tZXRlcih0aGlzLmFjY291bnRPYmouY291bnRyeSwgNyk7DQogICAgfSwNCiAgICBhZ2FpbkpvaW4oKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMuJHJlZnMuYWNjb3VudEVzVGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICBsZXQgYiA9IHRydWU7DQogICAgICBpZiAoZGF0YSA9PSBudWxsIHx8IGRhdGEubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy5lcnJvclRpcHMoIuivt+mAieaLqeimgemHjeaWsOWKoOWFpeW9kumbhuWNleeahOWPsOi0piIpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGV0IGlkcyA9ICIiOw0KICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICBsZXQgc3RhdHVzID0gaXRlbS5zdGF0dXM7DQogICAgICAgICAgaWYgKHN0YXR1cyAhPSA1KSB7DQogICAgICAgICAgICBiID0gZmFsc2U7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlkcyArPSBpdGVtLnBjaWQgKyAiLCI7DQogICAgICAgIH0pOw0KICAgICAgICBpZiAoYikgew0KICAgICAgICAgIGFnYWluSm9pbihpZHMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgaWYgKHJlcy5kYXRhLmNvZGUgPT0gMCkgew0KICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oew0KICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICLmj5DnpLrvvJrmk43kvZzmiJDlip8iLA0KICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxMCwNCiAgICAgICAgICAgICAgICBjbG9zYWJsZTogdHJ1ZSwNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoIuWPquacieW3sumAgOWbnueahOWPsOi0puaJjeiDvemHjeaWsOWKoOWFpeW9kumbhuWNlSIpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBzdWJtaXRDaGFuZ2UxKGRhdGEpIHsNCiAgICAgIHRoaXMuc3VibWl0ID0gZGF0YTsNCiAgICB9LA0KICAgIHJlZnJlc2goKSB7DQogICAgICBpZiAodGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5mcm9tR3VpamlkYW4gIT0gMSkgew0KICAgICAgICBsZXQgb2JqID0gdGhpczsNCiAgICAgICAgb2JqLnNob3dBbGFybU1vZGVsID0gZmFsc2U7DQogICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgew0KICAgICAgICAgIG9iai5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICAgICAgfSwgMjAwKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2hvd0FsYXJtTW9kZWwgPSB0cnVlOw0KICAgICAgfQ0KICAgIH0sDQogICAgYmVmb3JlTG9hZERhdGEoZGF0YSwgc3RyKSB7DQogICAgICB2YXIgY29scyA9IFtdLA0KICAgICAgICBrZXlzID0gW107DQogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRoaXMudGJBY2NvdW50LmV4cG9ydENvbHVtbnMubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29scy5wdXNoKHRoaXMudGJBY2NvdW50LmV4cG9ydENvbHVtbnNbaV0udGl0bGUpOw0KICAgICAgICBrZXlzLnB1c2godGhpcy50YkFjY291bnQuZXhwb3J0Q29sdW1uc1tpXS5rZXkpOw0KICAgICAgfQ0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICB0aXRsZTogY29scywNCiAgICAgICAga2V5OiBrZXlzLA0KICAgICAgICBkYXRhOiBkYXRhLA0KICAgICAgICBhdXRvV2lkdGg6IHRydWUsDQogICAgICAgIGZpbGVuYW1lOiBzdHIsDQogICAgICB9Ow0KICAgICAgZXhjZWwuZXhwb3J0X2FycmF5X3RvX2V4Y2VsKHBhcmFtcyk7DQogICAgICByZXR1cm47DQogICAgfSwNCiAgICBleHBvcnRDc3YobmFtZSkgew0KICAgICAgdGhpcy5leHBvcnQucnVuID0gdHJ1ZTsNCiAgICAgIGlmIChuYW1lID09PSAiY3VycmVudCIpIHsNCiAgICAgICAgdGhpcy5iZWZvcmVMb2FkRGF0YSh0aGlzLnRiQWNjb3VudC5kYXRhLCAi6aKE5Lyw5Y+w6LSm5a+85Ye65pWw5o2uIik7DQogICAgICB9IGVsc2UgaWYgKG5hbWUgPT09ICJhbGwiKSB7DQogICAgICAgIGxldCBwYXJhbXMgPSB0aGlzLmFjY291bnRPYmo7DQogICAgICAgIHBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgICAgcGFyYW1zLnBhZ2VTaXplID0gdGhpcy5leHBvcnQuc2l6ZTsNCiAgICAgICAgbGV0IHJlcSA9IHsNCiAgICAgICAgICB1cmw6ICIvYnVzaW5lc3MvYWNjb3VudEVzL3NlbGVjdEFjY291bnRFc0xpc3QiLA0KICAgICAgICAgIG1ldGhvZDogImdldCIsDQogICAgICAgICAgcGFyYW1zOiBwYXJhbXMsDQogICAgICAgIH07DQogICAgICAgIHRoaXMudGJBY2NvdW50LmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICBheGlvcw0KICAgICAgICAgIC5yZXF1ZXN0KHJlcSkNCiAgICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICB0aGlzLnRiQWNjb3VudC5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICBpZiAocmVzLmRhdGEpIHsNCiAgICAgICAgICAgICAgbGV0IGFycmF5ID0gcmVzLmRhdGEucm93czsNCiAgICAgICAgICAgICAgYWNjb3VudEVzVG90YWwodGhpcy5hY2NvdW50T2JqKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgICAvL+WQiOiuoQ0KICAgICAgICAgICAgICAgIGxldCBhbGx0b3RhbCA9IHJlcy5kYXRhOw0KICAgICAgICAgICAgICAgIGFsbHRvdGFsLnRvdGFsID0gIuWQiOiuoSI7DQogICAgICAgICAgICAgICAgYWxsdG90YWwuX2Rpc2FibGVkID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICBhcnJheS5wdXNoKGFsbHRvdGFsKTsNCiAgICAgICAgICAgICAgICB0aGlzLmJlZm9yZUxvYWREYXRhKGFycmF5LCAi6aKE5Lyw5Y+w6LSm5a+85Ye65pWw5o2uIik7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgLmNhdGNoKChlcnIpID0+IHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKGVycik7DQogICAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICB2YWxpZGF0ZSgpIHsNCiAgICAgIGlmICh0aGlzLmNvbHVtbnNJbmRleCAhPSA1KSB7DQogICAgICAgIGxldCB2YWwgPSB0aGlzLmVudGVyT3BlcmF0ZSh0aGlzLmNvbHVtbnNJbmRleCkuZGF0YTsNCiAgICAgICAgaWYgKHZhbCkgew0KICAgICAgICAgIGlmICh0ZXN0TnVtYmVyKHZhbCkpIHsNCiAgICAgICAgICAgIHN3aXRjaCAodGhpcy5jb2x1bW5zSW5kZXgpIHsNCiAgICAgICAgICAgICAgY2FzZSAxOg0KICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVTdGFydGRhdGUoKTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAyOg0KICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVFbmRkYXRlKCk7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgMzoNCiAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlY3VydXNlZHJlYWRpbmdzKCk7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgNDoNCiAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlYWNjb3VudG1vbmV5KCk7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fovpPlhaXmlbDlrZfvvIEiKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIHZhbGlkYXRlU3RhcnRkYXRlKCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRTdGFydERhdGU7DQogICAgICBsZXQgcmVzdWx0ID0gX3ZlcmlmeV9TdGFydERhdGUoZGF0YSwgdmFsKTsNCiAgICAgIGlmIChyZXN1bHQpIHsNCiAgICAgICAgLy/lpLHotKXlsLHlvLnlh7rmj5DnpLrlhoXlrrkNCiAgICAgICAgdGhpcy5lcnJvclRpcHMocmVzdWx0KTsNCiAgICAgICAgdGhpcy5teVN0eWxlW3RoaXMuZWRpdEluZGV4XS5zdGFydGRhdGUgPSAiZXJyb3JTdGxlIjsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMubXlTdHlsZVt0aGlzLmVkaXRJbmRleF0uc3RhcnRkYXRlID0gIm15c3BhbiI7DQogICAgICAgIGRhdGEuc3RhcnRkYXRlID0gdmFsOw0KICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHZhbGlkYXRlRW5kZGF0ZSgpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICBsZXQgdmFsID0gdGhpcy5lZGl0RW5kRGF0ZTsNCiAgICAgIC8vIOmqjOivgeaIquatouaXpeacn+aWueazlQ0KICAgICAgbGV0IHJlc3VsdCA9IF92ZXJpZnlfRW5kRGF0ZShkYXRhLCB2YWwpOw0KICAgICAgaWYgKHJlc3VsdCkgew0KICAgICAgICAvL+Wksei0peWwseW8ueWHuuaPkOekuuWGheWuue+8jOW5tuWwhuaVsOaNruaBouWkjeWIneWni+WMlg0KICAgICAgICB0aGlzLmVycm9yVGlwcyhyZXN1bHQpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgZGF0YS5lbmRkYXRlID0gdmFsOw0KICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHZhbGlkYXRlY3VydXNlZHJlYWRpbmdzKCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRjdXJ1c2VkcmVhZGluZ3M7DQogICAgICBkYXRhLmN1cnVzZWRyZWFkaW5ncyA9IHZhbDsNCiAgICAgIGRhdGEudG90YWx1c2VkcmVhZGluZ3MgPSB2YWw7DQogICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgIHRoaXMudW5pdFByaWNlKGRhdGEpOw0KICAgIH0sDQogICAgdmFsaWRhdGV0cmFuc2Zvcm1lcnVsbGFnZSgpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICBsZXQgdmFsID0gdGhpcy5lZGl0dHJhbnNmb3JtZXJ1bGxhZ2U7DQogICAgICBkYXRhLnRyYW5zZm9ybWVydWxsYWdlID0gdmFsOw0KICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgfSwNCiAgICB2YWxpZGF0ZWFjY291bnRtb25leSgpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICBsZXQgdmFsID0gdGhpcy5lZGl0YWNjb3VudG1vbmV5Ow0KICAgICAgZGF0YS5hY2NvdW50bW9uZXkgPSB2YWw7IC8v5YWB6K646LSf5pWwIE1hdGguYWJzKHZhbCkNCiAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgdGhpcy51bml0UHJpY2UoZGF0YSk7DQogICAgICB0aGlzLnZhbGlkYXRlVW5pdFByaWNlKGRhdGEpOw0KICAgIH0sDQogICAgc2V0cmVtYXJrKCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRyZW1hcms7DQogICAgICBkYXRhLnJlbWFyayA9IHZhbDsNCiAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgIH0sDQogICAgc2V0TXlTdHlsZShsZW5ndGgpIHsNCiAgICAgIHRoaXMubXlTdHlsZSA9IFtdOw0KICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBsZW5ndGg7IGkrKykgew0KICAgICAgICB0aGlzLm15U3R5bGUucHVzaCh7DQogICAgICAgICAgc3RhcnRkYXRlOiAibXlzcGFuIiwNCiAgICAgICAgICBlbmRkYXRlOiAibXlzcGFuIiwNCiAgICAgICAgICBjdXJ1c2VkcmVhZGluZ3M6ICJteXNwYW4iLA0KICAgICAgICAgIGFjY291bnRtb25leTogIm15c3BhbiIsDQogICAgICAgICAgcmVtYXJrOiAibXlzcGFuIiwNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICAvL3NwYW7ngrnlh7vkuovku7blsIZzcGFu5o2i5oiQ6L6T5YWl5qGG5bm25LiU6I635Y+W54Sm54K5DQogICAgc2VsZWN0Q2FsbChyb3csIGluZGV4LCBjb2x1bW5zLCBzdHIpIHsNCiAgICAgIHRoaXMuZWRpdFN0YXJ0RGF0ZSA9IHJvdy5zdGFydGRhdGU7DQogICAgICB0aGlzLmVkaXRFbmREYXRlID0gcm93LmVuZGRhdGU7DQogICAgICB0aGlzLmVkaXRjdXJ1c2VkcmVhZGluZ3MgPQ0KICAgICAgICByb3cuY3VydXNlZHJlYWRpbmdzID09IG51bGwgfHwgcm93LmN1cnVzZWRyZWFkaW5ncyA9PT0gMA0KICAgICAgICAgID8gbnVsbA0KICAgICAgICAgIDogcm93LmN1cnVzZWRyZWFkaW5nczsNCiAgICAgIHRoaXMuZWRpdGFjY291bnRtb25leSA9DQogICAgICAgIHJvdy5hY2NvdW50bW9uZXkgPT0gbnVsbCB8fCByb3cuYWNjb3VudG1vbmV5ID09PSAwID8gbnVsbCA6IHJvdy5hY2NvdW50bW9uZXk7DQogICAgICB0aGlzLmVkaXRyZW1hcmsgPSByb3cucmVtYXJrOw0KDQogICAgICB0aGlzLmVkaXRJbmRleCA9IGluZGV4Ow0KICAgICAgdGhpcy5jb2x1bW5zSW5kZXggPSBjb2x1bW5zOw0KDQogICAgICBsZXQgYSA9IHRoaXM7DQogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgaWYgKGNvbHVtbnMgIT0gOCkgew0KICAgICAgICAgIGEuJHJlZnNbc3RyICsgaW5kZXggKyBjb2x1bW5zXS5mb2N1cygpOw0KICAgICAgICB9DQogICAgICB9LCAyMDApOw0KICAgIH0sDQogICAgLy/ot7PovazliLDkuIvkuIDmoLwNCiAgICBuZXh0Q2VsbChkYXRhKSB7DQogICAgICBsZXQgaW5kZXggPSBkYXRhLmVkaXRJbmRleDsNCiAgICAgIGxldCBjb2x1bW5zID0gZGF0YS5jb2x1bW5zSW5kZXg7DQogICAgICBsZXQgcm93ID0gIiI7DQogICAgICBpZiAoaW5kZXggPT09IC0xICYmIGNvbHVtbnMgPT09IC0xKSB7DQogICAgICAgIGluZGV4ID0gMDsNCiAgICAgICAgY29sdW1ucyA9IDE7DQogICAgICB9IGVsc2UgaWYgKGluZGV4ID4gLTEgJiYgY29sdW1ucyA9PT0gNSkgew0KICAgICAgICAvL+W9k+i3s+i9rOeahOacgOWQjuS4gOihjOacgOWQjuS4gOagvOeahOaXtuWAmQ0KICAgICAgICBpZiAoaW5kZXggPj0gZGF0YS5wYWdlU2l6ZSAtIDEgfHwgaW5kZXggPj0gZGF0YS5wYWdlVG90YWwgLSAxKSB7DQogICAgICAgICAgaW5kZXggPSAwOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGluZGV4Kys7DQogICAgICAgIH0NCiAgICAgICAgY29sdW1ucyA9IDE7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb2x1bW5zICs9IDE7DQogICAgICB9DQogICAgICBkYXRhLmVkaXRJbmRleCA9IGluZGV4Ow0KICAgICAgZGF0YS5jb2x1bW5zSW5kZXggPSBjb2x1bW5zOw0KICAgICAgcm93ID0gZGF0YS50YkFjY291bnQuZGF0YVtpbmRleF07DQogICAgICBpZiAocm93KSB7DQogICAgICAgIGRhdGEuZWRpdFN0YXJ0RGF0ZSA9IHJvdy5zdGFydGRhdGU7DQogICAgICAgIGRhdGEuZWRpdEVuZERhdGUgPSByb3cuZW5kZGF0ZTsNCiAgICAgICAgZGF0YS5lZGl0Y3VydXNlZHJlYWRpbmdzID0NCiAgICAgICAgICByb3cuY3VydXNlZHJlYWRpbmdzID09IG51bGwgfHwgcm93LmN1cnVzZWRyZWFkaW5ncyA9PT0gMA0KICAgICAgICAgICAgPyBudWxsDQogICAgICAgICAgICA6IHJvdy5jdXJ1c2VkcmVhZGluZ3M7DQogICAgICAgIGRhdGEuZWRpdGFjY291bnRtb25leSA9DQogICAgICAgICAgcm93LmFjY291bnRtb25leSA9PSBudWxsIHx8IHJvdy5hY2NvdW50bW9uZXkgPT09IDAgPyBudWxsIDogcm93LmFjY291bnRtb25leTsNCiAgICAgICAgZGF0YS5lZGl0cmVtYXJrID0gcm93LnJlbWFyazsNCiAgICAgIH0NCg0KICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7DQogICAgICAgIGRhdGEuJHJlZnNbZGF0YS5lbnRlck9wZXJhdGUoY29sdW1ucykuc3RyICsgaW5kZXggKyBjb2x1bW5zXS5mb2N1cygpOw0KICAgICAgfSwgMjAwKTsNCiAgICB9LA0KICAgIC8v5qC55o2u5YiX5Y+36L+U5Zue5a+55bqU55qE5YiX5ZCNDQogICAgZW50ZXJPcGVyYXRlKG51bWJlcikgew0KICAgICAgbGV0IHN0ciA9ICIiOw0KICAgICAgbGV0IGRhdGEgPSBudWxsOw0KICAgICAgc3dpdGNoIChudW1iZXIpIHsNCiAgICAgICAgY2FzZSAxOg0KICAgICAgICAgIHN0ciA9ICJzdGFydGRhdGUiOw0KICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRTdGFydERhdGU7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgMjoNCiAgICAgICAgICBzdHIgPSAiZW5kZGF0ZSI7DQogICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdEVuZERhdGU7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgMzoNCiAgICAgICAgICBzdHIgPSAiY3VydXNlZHJlYWRpbmdzIjsNCiAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0Y3VydXNlZHJlYWRpbmdzOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlIDQ6DQogICAgICAgICAgc3RyID0gImFjY291bnRtb25leSI7DQogICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdGFjY291bnRtb25leTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSA1Og0KICAgICAgICAgIHN0ciA9ICJyZW1hcmsiOw0KICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRyZW1hcms7DQogICAgICAgICAgYnJlYWs7DQogICAgICB9DQogICAgICByZXR1cm4geyBzdHI6IHN0ciwgZGF0YTogZGF0YSB9Ow0KICAgIH0sDQogICAgcHJlZCgpIHsNCiAgICAgIHZhciBsZXR0ID0gdGhpczsNCiAgICAgIGxldCBpbmRleCA9IGxldHQuZWRpdEluZGV4Ow0KICAgICAgbGV0IGNvbHVtbnMgPSBsZXR0LmNvbHVtbnNJbmRleDsNCiAgICAgIGlmIChpbmRleCA9PT0gLTEgJiYgY29sdW1ucyA9PT0gLTEpIHsNCiAgICAgICAgaW5kZXggPSAwOw0KICAgICAgICBjb2x1bW5zID0gMTsNCiAgICAgICAgbGV0dC5lZGl0SW5kZXggPSBpbmRleDsNCiAgICAgICAgbGV0dC5jb2x1bW5zSW5kZXggPSBjb2x1bW5zOw0KICAgICAgICBsZXR0LmVkaXRTdGFydERhdGUgPSBsZXR0LnRiQWNjb3VudC5kYXRhW2luZGV4XS5zdGFydGRhdGU7DQogICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgew0KICAgICAgICAgIGxldHQuJHJlZnNbbGV0dC5lbnRlck9wZXJhdGUoY29sdW1ucykuc3RyICsgaW5kZXggKyBjb2x1bW5zXS5mb2N1cygpOw0KICAgICAgICB9LCAyMDApOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGV0dC52YWxpZGF0ZSgpOw0KICAgICAgICBsZXR0LnNldHJlbWFyaygpOw0KICAgICAgICBsZXR0Lm5leHRDZWxsKGxldHQpOw0KICAgICAgfQ0KICAgIH0sDQogICAgZWxsaXBzaXModmFsdWUpIHsNCiAgICAgIGlmICghdmFsdWUpIHJldHVybiAiIjsNCiAgICAgIGlmICh2YWx1ZS5sZW5ndGggPiAzKSB7DQogICAgICAgIHJldHVybiB2YWx1ZS5zbGljZSgwLCAzKSArICIuLi4iOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHZhbHVlOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["addPylonPredAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4WA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addPylonPredAccount.vue", "sourceRoot": "src/view/account/homePagePylon", "sourcesContent": ["<template>\r\n  <div class=\"page-class page-card\">\r\n    <div class=\"query-box\">\r\n      <Row :class=\"filterColl ? 'header-bar-show' : 'header-bar-hide'\">\r\n        <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.accountno\"\r\n                  :style=\"formItemWidth\"\r\n                  @on-change=\"accountnoChange\"\r\n                >\r\n                  <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"项目名称:\" prop=\"projectName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.projectName\"\r\n                  placeholder=\"请输入项目名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"'sc' == version\">\r\n              <FormItem\r\n                label=\"电表/协议编码:\"\r\n                prop=\"ammeterName\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.ammeterName\"\r\n                  placeholder=\"请输入电表户号/协议编码\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"供电局电表编号:\"\r\n                prop=\"supplybureauammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.supplybureauammetercode\"\r\n                  placeholder=\"请输入供电局电表编号\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"局站名称:\" prop=\"stationName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.stationName\"\r\n                  placeholder=\"请输入局站名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.company\"\r\n                  @on-change=\"selectChange(accountObj.company)\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                  <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"所属部门：\"\r\n                prop=\"countryName\"\r\n                v-if=\"isAdmin == true\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Input\r\n                  :clearable=\"true\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"accountObj.countryName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n              <FormItem\r\n                label=\"所属部门：\"\r\n                prop=\"country\"\r\n                v-if=\"isAdmin == false\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option value=\"-1\">全部</Option>\r\n                  <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\"></Col>\r\n          </Row>\r\n          <div align=\"right\">\r\n            <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n            <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n            <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n              <Button type=\"default\" style=\"margin-left: 5px\"\r\n                >导出\r\n                <Icon type=\"ios-arrow-down\"></Icon>\r\n              </Button>\r\n              <DropdownMenu slot=\"list\">\r\n                <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n          </div>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon\r\n          :type=\"filterColl ? 'md-arrow-dropup' : 'md-arrow-dropdown'\"\r\n          size=\"20\"\r\n          @click=\"filterColl = !filterColl\"\r\n          :color=\"filterColl ? '#000' : '#1ab394'\"\r\n        ></icon>\r\n      </div>\r\n    </div>\r\n    <div class=\"cl-table\">\r\n      <Row class=\"button-bar\">\r\n        <div class=\"table-button\">\r\n          <Button type=\"primary\" @click=\"addElectricType\">新增</Button>\r\n          <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n          <Button type=\"error\" @click=\"remove\">删除</Button>\r\n          <Button type=\"error\" @click=\"deleteAll()\">一键删除</Button>\r\n          <Button type=\"primary\" @click=\"openCompletedPreModal\">复制归集单台账</Button>\r\n          <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n            <Button type=\"info\" style=\"margin-left: 5px\"\r\n              >加入归集单\r\n              <Icon type=\"ios-arrow-down\"></Icon>\r\n            </Button>\r\n            <DropdownMenu slot=\"list\">\r\n              <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n              <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n          <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n        </div>\r\n      </Row>\r\n      <Table\r\n        ref=\"accountEsTable\"\r\n        border\r\n        :columns=\"tbAccount.columns\"\r\n        :data=\"tbAccount.data\"\r\n        class=\"mytable\"\r\n        :loading=\"tbAccount.loading\"\r\n        :height=\"tableHeight\"\r\n      >\r\n        <!--项目名称-->\r\n        <template slot-scope=\"{ row }\" slot=\"projectName\">\r\n          <div v-if=\"row.status === 5\">\r\n            <span>{{ row.projectName }}</span\r\n            ><span style=\"color: red\">[退回]</span>\r\n          </div>\r\n          <span v-else>{{ row.projectName }}</span>\r\n        </template>\r\n        <!--起始时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"startdate\" v-if=\"row.total == null\">\r\n          <Input\r\n            :ref=\"'startdate' + index + 1\"\r\n            type=\"text\"\r\n            @on-blur=\"validate\"\r\n            v-model=\"editStartDate\"\r\n            v-if=\"editIndex === index && columnsIndex === 1\"\r\n          />\r\n          <span\r\n            :class=\"myStyle[index].startdate\"\r\n            @click=\"selectCall(row, index, 1, 'startdate')\"\r\n            v-else\r\n            >{{ row.startdate }}</span\r\n          >\r\n        </template>\r\n        <!--截止时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"enddate\" v-if=\"row.total == null\">\r\n          <Input\r\n            :ref=\"'enddate' + index + 2\"\r\n            type=\"text\"\r\n            v-model=\"editEndDate\"\r\n            @on-blur=\"validate\"\r\n            v-if=\"editIndex === index && columnsIndex === 2\"\r\n          />\r\n          <span\r\n            :class=\"myStyle[index].enddate\"\r\n            @click=\"selectCall(row, index, 2, 'enddate')\"\r\n            v-else\r\n            >{{ row.enddate }}</span\r\n          >\r\n        </template>\r\n        <!--用电量-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"curusedreadings\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'curusedreadings' + index + 3\"\r\n              type=\"text\"\r\n              v-model=\"editcurusedreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 3\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].curusedreadings\"\r\n              @click=\"selectCall(row, index, 3, 'curusedreadings')\"\r\n              v-else\r\n              >{{ row.curusedreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.curusedreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--电损-->\r\n        <!--<template slot-scope=\"{ row, index }\" slot=\"transformerullage\">-->\r\n        <!--<div v-if=\"row.total == null\">-->\r\n        <!--<Input :ref=\"'transformerullage'+index+4\" type=\"text\" v-model=\"edittransformerullage\" @on-blur=\"validate\"-->\r\n        <!--v-if=\"editIndex === index && columnsIndex === 4\" />-->\r\n        <!--<span :class=\"myStyle[index].transformerullage\" @click=\"selectCall(row,index,4,'transformerullage')\" v-else>{{ row.transformerullage }}</span>-->\r\n        <!--</div>-->\r\n        <!--<div v-else>-->\r\n        <!--<span>{{ row.transformerullage }}</span>-->\r\n        <!--</div>-->\r\n        <!--</template>-->\r\n        <!--预估电费-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"accountmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'accountmoney' + index + 4\"\r\n              type=\"text\"\r\n              v-model=\"editaccountmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 4\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].accountmoney\"\r\n              @click=\"selectCall(row, index, 4, 'accountmoney')\"\r\n              v-else\r\n              >{{ row.accountmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.accountmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--备注-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              v-model=\"editremark\"\r\n              :ref=\"'remark' + index + 5\"\r\n              type=\"text\"\r\n              @on-blur=\"setremark\"\r\n              v-if=\"editIndex === index && columnsIndex === 5\"\r\n            />\r\n            <Tooltip placement=\"bottom\" max-width=\"200\" v-else>\r\n              <span\r\n                :class=\"myStyle[index].remark\"\r\n                style=\"width: 60px\"\r\n                @click=\"selectCall(row, index, 5, 'remark')\"\r\n                >{{ ellipsis(row.remark) }}</span\r\n              >\r\n              <div slot=\"content\">\r\n                {{ row.remark }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n          <div v-else>\r\n            <Tooltip placement=\"bottom\" max-width=\"200\">\r\n              <span>{{ ellipsis(row.remark) }}</span>\r\n              <div slot=\"content\">\r\n                {{ row.remark }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n        </template>\r\n      </Table>\r\n      <div class=\"table-page\">\r\n        <Page\r\n          size=\"small\"\r\n          :total=\"pageTotal\"\r\n          :current=\"pageNum\"\r\n          :page-size=\"pageSize\"\r\n          show-elevator\r\n          show-sizer\r\n          show-total\r\n          placement=\"top\"\r\n          @on-change=\"handlePage\"\r\n          @on-page-size-change=\"handlePageSize\"\r\n        ></Page>\r\n      </div>\r\n      <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n    </div>\r\n    <select-ammeter\r\n      ref=\"selectAmmeter\"\r\n      v-on:listenToSelectAmmeter=\"setAmmeterData\"\r\n    ></select-ammeter>\r\n    <add-bill-per\r\n      ref=\"addBillPer\"\r\n      v-on:refreshList=\"refresh\"\r\n      @isButtonload=\"isButtonload\"\r\n      @buttonload2=\"buttonload2\"\r\n    ></add-bill-per>\r\n    <completed-pre-modal\r\n      ref=\"completedPre\"\r\n      v-on:refreshList=\"refresh\"\r\n    ></completed-pre-modal>\r\n    <country-modal\r\n      ref=\"countryModal\"\r\n      v-on:getDataFromModal=\"getDataFromModal\"\r\n    ></country-modal>\r\n    <!--    稽核modal-->\r\n    <Modal\r\n      v-model=\"showCheckModel\"\r\n      width=\"80%\"\r\n      title=\"稽核结果及反馈\"\r\n      :mask-closable=\"false\"\r\n    >\r\n      <check-result-and-response ref=\"chekResultAndResponse\"></check-result-and-response>\r\n    </Modal>\r\n    <Modal v-model=\"showJhModel\" width=\"80%\" title=\"稽核结果\" :mask-closable=\"false\">\r\n      <check-result ref=\"checkResult\"></check-result>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button type=\"primary\" @click=\"nextCheck\">已查阅</Button>\r\n        <Button type=\"text\" @click=\"checkCancel\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <!-- @on-cancel=\"alarmClose\" -->\r\n    <Modal\r\n      v-model=\"showAlarmModel\"\r\n      width=\"80%\"\r\n      title=\"台账预警稽核\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n      class=\"yjjh\"\r\n    >\r\n      <alarm-check\r\n        ref=\"showAlarmModel\"\r\n        @save=\"save\"\r\n        @submitChange=\"submitChange\"\r\n        @close=\"alarmClose\"\r\n      ></alarm-check>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button size=\"large\" @click=\"alarmClose\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { mapState } from \"vuex\";\r\nimport { getAuditResult, getAuditResultNew, getAuditResultNew_QXM } from \"@/api/account\";\r\nimport {\r\n  removeAll,\r\n  addPredPowerAccount,\r\n  addAccountEs,\r\n  removeAccountEs,\r\n  getUser,\r\n  getDepartments,\r\n  accountEsTotal,\r\n  selectIdsByEsParams,\r\n} from \"@/api/account\";\r\nimport { getResCenter, getcompany } from \"@/api/alertcontrol/alertcontrol\";\r\nimport { againJoin } from \"@/api/accountBillPer\";\r\nimport {\r\n  getClassification,\r\n  getUserdata,\r\n  getUserByUserRole,\r\n  getCountrysdata,\r\n  getCountryByUserId,\r\n} from \"@/api/basedata/ammeter.js\";\r\nimport { editOwn } from \"@/api/accountSC/accountSC\";\r\nimport { validContractList } from \"@/api/contract\";\r\nimport {\r\n  getDates2,\r\n  testNumber,\r\n  cutDate_yyyymmdd,\r\n  getFirstDateByAccountno_yyyymmdd,\r\n  getLastDateByAccountno_yyyymmdd,\r\n  stringToDate,\r\n  getCurrentDate,\r\n} from \"@/view/account/powerAccountHelper\";\r\nimport {\r\n  _verify_StartDate,\r\n  _verify_EndDate,\r\n  verification,\r\n  unitpirceMin,\r\n  unitpirceMax,\r\n  unitpirceMax1,\r\n} from \"@/view/account/PowerAccountEs\";\r\nimport {\r\n  judge_negate,\r\n  judge_recovery,\r\n  requiredFieldValidator,\r\n} from \"@/view/account/PowerAccountController\";\r\nimport { widthstyle } from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nimport AddBillPer from \"@/view/account/addBillPreModal\";\r\nimport SelectAmmeter from \"@/view/account/selectAmmeter\";\r\nimport CompletedPreModal from \"@/view/account/completedPreModal\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport axios from \"@/libs/api.request\";\r\nimport excel from \"@/libs/excel\";\r\nimport indexData from \"@/config/index\";\r\nimport CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\nimport alarmCheck from \"@/view/account/check/alarmCheck\";\r\nimport checkResult from \"@/view/account/check/checkResult\";\r\nimport checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\nimport QueryPeopleModal from \"@/view/account/queryPeopleModal\";\r\nimport permissionMixin from \"@/mixins/permission\";\r\n\r\nimport pageFun from \"@/mixins/pageFun\";\r\n\r\nlet dates = getDates2();\r\nexport default {\r\n  name: \"addPredPowerAccount\",\r\n  mixins: [permissionMixin, pageFun],\r\n  components: {\r\n    alarmCheck,\r\n    checkResult,\r\n    checkResultAndResponse,\r\n    CompletedPreModal,\r\n    SelectAmmeter,\r\n    AddBillPer,\r\n    CountryModal,\r\n  },\r\n  data() {\r\n    let renderStatus = (h, { row, index }) => {\r\n      var status = \"\";\r\n      let data = this.tbAccount.data[index];\r\n      for (let item of this.accountStatus) {\r\n        if (item.typeCode == row.status) {\r\n          data.statusName = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", data.statusName);\r\n    };\r\n\r\n    let renderCategory = (h, params) => {\r\n      var categoryname = \"\";\r\n      for (let item of this.categorys) {\r\n        if (item.typeCode == params.row.category) {\r\n          categoryname = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", categoryname);\r\n    };\r\n\r\n    return {\r\n      isT: true,\r\n      number2: 0,\r\n      name: \"\",\r\n      dataL: [],\r\n      isQuery: true,\r\n      number: 0,\r\n      cancelTime: \"\",\r\n      ctgKeyList: [],\r\n      submit: [],\r\n      submit2: [],\r\n      ammeterids: [],\r\n      showCheckModel: false,\r\n      showAlarmModel: false,\r\n      showJhModel: false,\r\n      formItemWidth: widthstyle,\r\n      version: \"\",\r\n      dateList: dates,\r\n      filterColl: true, //搜索面板展开\r\n      editIndex: -1, //当前编辑行\r\n      columnsIndex: -1, //当前编辑列\r\n      editStartDate: \"\",\r\n      myStyle: [], //样式\r\n      editEndDate: \"\",\r\n      editcurusedreadings: \"\",\r\n      edittransformerullage: \"\",\r\n      spinShow: false, //遮罩\r\n      categorys: [], //描述类型\r\n      editaccountmoney: \"\",\r\n      editremark: \"\",\r\n      accountStatus: [],\r\n      companies: [],\r\n      departments: [],\r\n      isAdmin: false,\r\n      company: null, //用户默认公司\r\n      country: null, //用户默认所属部门\r\n      countryName: null, //用户默认所属部门\r\n      export: {\r\n        run: false, //是否正在执行导出\r\n        data: \"\", //导出数据\r\n        totalPage: 0, //一共多少页\r\n        currentPage: 0, //当前多少页\r\n        percent: 0,\r\n        size: ********,\r\n      },\r\n      accountObj: {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        company: \"\", //分公司\r\n        projectName: \"\", //项目名称\r\n        country: \"\", //所属部门\r\n        ammeterName: \"\", //电表户号/协议编码\r\n        stationName: \"\",\r\n        accountType: \"2\", //台账类型\r\n        accountestype: 1, //台账类型\r\n        supplybureauammetercode: \"\",\r\n      },\r\n      tbAccount: {\r\n        loading: false,\r\n        columns: [],\r\n        headColumn: [\r\n          { type: \"selection\", width: 60, align: \"center\" },\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n        ],\r\n        headColumn2: [\r\n          { type: \"selection\", width: 60, align: \"center\" },\r\n          {\r\n            title: \"稽核结果及反馈\",\r\n            key: \"action\",\r\n            align: \"center\",\r\n            render: (h, params) => {\r\n              var that = this;\r\n              return h(\r\n                \"Button\",\r\n                {\r\n                  props: {\r\n                    type: \"primary\",\r\n                    size: \"small\",\r\n                  },\r\n                  style: {\r\n                    \"font-size\": \"10px\",\r\n                  },\r\n                  on: {\r\n                    click() {\r\n                      that.$refs.chekResultAndResponse.pcid = params.row.pcid;\r\n                      that.showCheckModel = true;\r\n                    },\r\n                  },\r\n                },\r\n                \"稽核结果及反馈\"\r\n              );\r\n            },\r\n            minWidth: 120,\r\n            maxWidth: 150,\r\n          },\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n        ],\r\n        tailColumn: [\r\n          { title: \"所属分公司\", key: \"companyName\", align: \"center\" },\r\n          { title: \"所属部门\", key: \"countryName\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            key: \"startdate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            key: \"enddate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"用电量(度)\",\r\n            slot: \"curusedreadings\",\r\n            key: \"curusedreadings\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"电价(元)\", key: \"unitpirce\", align: \"center\" },\r\n          { title: \"电费\", slot: \"accountmoney\", key: \"accountmoney\", align: \"center\" },\r\n          { title: \"用电类型\", key: \"electrotypename\", align: \"center\", width: 94 },\r\n          {\r\n            title: \"类型描述\",\r\n            key: \"categoryname\",\r\n            align: \"center\",\r\n            render: renderCategory,\r\n          },\r\n        ],\r\n        lnColumn: [\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n        ],\r\n        scColumn: [\r\n          { title: \"电表户号/协议编码\", key: \"ammeterName\", align: \"center\" },\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n        ],\r\n        lnremark: [{ title: \"备注\", slot: \"remark\", key: \"remark\", align: \"center\" }],\r\n        scremark: [{ title: \"预估原由\", slot: \"remark\", key: \"remark\", align: \"center\" }],\r\n        data: [],\r\n        total: 0,\r\n        exportColumns: [\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n          { title: \"电表户号/协议编码\", key: \"ammeterName\", align: \"center\" },\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n          { title: \"所属分公司\", key: \"companyName\", align: \"center\" },\r\n          { title: \"所属部门\", key: \"countryName\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            key: \"startdate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            key: \"enddate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"用电量(度)\",\r\n            slot: \"curusedreadings\",\r\n            key: \"curusedreadings\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"电价(元)\", key: \"unitpirce\", align: \"center\" },\r\n          {\r\n            title: \"预估电费\",\r\n            slot: \"accountmoney\",\r\n            key: \"accountmoney\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"备注\", slot: \"remark\", key: \"remark\", align: \"center\" },\r\n          { title: \"用电类型\", key: \"electrotypename\", align: \"center\", width: 94 },\r\n          { title: \"类型描述\", key: \"categoryname\", align: \"center\" },\r\n        ],\r\n      },\r\n      pageTotal: 0,\r\n      pageNum: 1,\r\n      pageSize: 10, //当前页\r\n    };\r\n  },\r\n  mounted() {\r\n    this.handleHeight(); //table高度自定义\r\n\r\n    this.version = indexData.version;\r\n\r\n    this.tbAccount.columns = this.tbAccount.headColumn2\r\n      .concat(this.tbAccount.scColumn)\r\n      .concat(this.tbAccount.tailColumn)\r\n      .concat(this.tbAccount.scremark);\r\n\r\n    this.accountStatus = blist(\"accountStatus\");\r\n    this.categorys = blist(\"ammeterCategory\");\r\n    let that = this;\r\n    getUserByUserRole().then((res) => {\r\n      //根据权限获取分公司\r\n      that.companies = res.data.companies;\r\n      if (\r\n        res.data.isCityAdmin == true ||\r\n        res.data.isProAdmin == true ||\r\n        res.data.isSubAdmin == true\r\n      ) {\r\n        that.isAdmin = true;\r\n      }\r\n      getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {\r\n        //根据权限获取所属部门\r\n        that.departments = res.data;\r\n        that.getUserData();\r\n      });\r\n    });\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      loginId: (state) => state.user.loginId,\r\n    }),\r\n  },\r\n  methods: {\r\n    buttonload2(v) {\r\n      this.$refs.showAlarmModel.buttonload2 = v;\r\n    },\r\n    isButtonload(v) {\r\n      this.$refs.showAlarmModel.buttonload = v;\r\n    },\r\n    isShows(t) {\r\n      this.$refs.showAlarmModel.show = t;\r\n      if (this.$refs.showAlarmModel.show == false) {\r\n        this.number2++;\r\n        this.isT = t;\r\n      } else if (this.$refs.showAlarmModel.show == true) {\r\n        this.number2 = 0;\r\n        this.isT = !t;\r\n      }\r\n      if (this.isT && this.number2 < 10) {\r\n        this.isShows(t);\r\n      }\r\n    },\r\n    nextCheck() {\r\n      this.showAlarmModel = true;\r\n      this.isShows(true);\r\n      // this.$refs.showAlarmModel.show=true\r\n      this.$refs.showAlarmModel.tableData = [];\r\n      this.$refs.showAlarmModel.resultList = [];\r\n      this.$refs.showAlarmModel.tableData1 = [];\r\n      this.$refs.showAlarmModel.resultList1 = [];\r\n      this.$refs.showAlarmModel.tableData2 = [];\r\n      this.$refs.showAlarmModel.resultList2 = [];\r\n      this.$refs.showAlarmModel.tableData3 = [];\r\n      this.$refs.showAlarmModel.resultList3 = [];\r\n      this.$refs.showAlarmModel.tableData4 = [];\r\n      this.$refs.showAlarmModel.resultList4 = [];\r\n      this.$refs.showAlarmModel.tableData5 = [];\r\n      this.$refs.showAlarmModel.resultList5 = [];\r\n      // setTimeout(() => {\r\n      this.showJhModel = false;\r\n      // this.showAlarmModel=true;\r\n      this.$refs.showAlarmModel.activeButton = 6;\r\n      // })\r\n    },\r\n    alarmClose() {\r\n      // window.history.go(0);\r\n      this.showAlarmModel = false;\r\n      this.$refs.showAlarmModel.show = false;\r\n    },\r\n    checkCancel() {\r\n      this.showJhModel = false;\r\n    },\r\n    alarmCheck() {},\r\n    selectChange() {\r\n      let that = this;\r\n      if (that.accountObj.company != undefined) {\r\n        if (that.accountObj.company == \"-1\") {\r\n          that.accountObj.country = -1;\r\n          that.accountObj.countryName = null;\r\n        } else {\r\n          getCountryByUserId(that.accountObj.company).then((res) => {\r\n            if (res.data.departments.length != 0) {\r\n              that.accountObj.country = res.data.departments[0].id;\r\n              that.accountObj.countryName = res.data.departments[0].name;\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //选择所属部门开始\r\n    chooseResponseCenter() {\r\n      if (this.accountObj.company == null || this.accountObj.company == \"-1\") {\r\n        this.$Message.info(\"请先选择分公司\");\r\n        return;\r\n      }\r\n      this.$refs.countryModal.choose(this.accountObj.company); //所属部门\r\n    },\r\n    getDataFromModal(data) {\r\n      this.accountObj.country = data.id;\r\n      this.accountObj.countryName = data.name;\r\n      //选择所属部门结束\r\n    },\r\n    getUserData() {\r\n      let that = this;\r\n      getUserdata().then((res) => {\r\n        //当前登录用户所在公司和所属部门\r\n        if (res.data.companies.length != 0) {\r\n          let companies = res.data.companies;\r\n          if (res.data.companies[0].id == \"**********\") {\r\n            companies = that.companies;\r\n          }\r\n          that.company = companies[0].id;\r\n          that.accountObj.company = companies[0].id;\r\n        }\r\n        if (res.data.departments.length != 0) {\r\n          let departments = res.data.departments;\r\n          if (res.data.companies[0].id == \"**********\" && that.departments.length != 0) {\r\n            departments = that.departments;\r\n          }\r\n          that.country = departments[0].id;\r\n          that.countryName = departments[0].name;\r\n          that.accountObj.country = Number(departments[0].id);\r\n          that.accountObj.countryName = departments[0].name;\r\n        }\r\n\r\n        that.pageNum = 1;\r\n        that.getAccountMessages();\r\n      });\r\n    },\r\n    searchList() {\r\n      if (this.accountObj.countryName == \"\") {\r\n        this.accountObj.country = \"-1\";\r\n      }\r\n      this.pageNum = 1;\r\n      this.getAccountMessages();\r\n    },\r\n    accountnoChange() {\r\n      this.searchList();\r\n    },\r\n    setAmmeterData: function (data) {\r\n      let arrayData = [];\r\n      let ctgKeyList = [];\r\n      let no = this.accountObj.accountno;\r\n      if (data != null && data.length > 0) {\r\n        data.forEach(function (item) {\r\n          let obj = {};\r\n          obj.pcid = null;\r\n          obj.ammeterName = item.ammetername;\r\n          obj.projectName = item.projectname;\r\n          obj.substation = item.substation;\r\n          obj.categoryname = item.categoryname;\r\n          obj.category = item.category;\r\n          obj.ammeterid = item.ammeterid;\r\n          obj.company = item.company;\r\n          obj.companyName = item.companyName;\r\n          obj.country = item.country;\r\n          obj.countryName = item.countryName;\r\n          obj.startdate = null;\r\n          obj.enddate = null;\r\n          obj.curusedreadings = 0;\r\n          obj.transformerullage = 0;\r\n          obj.unitpirce = 0;\r\n          obj.accountmoney = 0;\r\n          obj.remark = null;\r\n          obj.electrotype = item.electrotype;\r\n          obj.stationcode5gr = item.stationcode5gr;\r\n          obj.stationname5gr = item.stationname5gr;\r\n          obj.electrotypename = item.electrotypename;\r\n          obj.stationName = item.stationName;\r\n          obj.startdate = getFirstDateByAccountno_yyyymmdd(no);\r\n          obj.enddate = getLastDateByAccountno_yyyymmdd(no);\r\n          obj.accountestype = 1;\r\n          obj.supplybureauammetercode = item.supplybureauammetercode;\r\n          obj.directsupplyflag = item.directsupplyflag;\r\n          obj.stationaddresscode = item.stationaddresscode;\r\n          arrayData.push(obj);\r\n          ctgKeyList.push({ ctgKey: item.ctgKey, ammetername: item.ammetername });\r\n        });\r\n        this.ctgKeyList = ctgKeyList;\r\n      }\r\n\r\n      let version = indexData.version;\r\n      let origin = this.tbAccount.data;\r\n      if (origin.length < 1) {\r\n        this.tbAccount.data = arrayData;\r\n      } else {\r\n        let tem = arrayData;\r\n        if (\"sc\" == version) {\r\n          origin.forEach((item) => {\r\n            for (let j = tem.length - 1; j >= 0; j--) {\r\n              let jj = tem[j];\r\n              if (item.ammeterid === jj.ammeterid) {\r\n                tem.splice(j, 1);\r\n              }\r\n            }\r\n          });\r\n        }\r\n        let total = this.pageTotal;\r\n        this.pageTotal = total + tem.length;\r\n        this.tbAccount.data = tem.concat(this.tbAccount.data);\r\n      }\r\n\r\n      this.setMyStyle(this.tbAccount.data.length);\r\n    },\r\n    //点击保存\r\n    async preserve() {\r\n      let dataL = this.$refs.accountEsTable.getSelection();\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      let version = indexData.version;\r\n      for (let i = 0; i < dataL.length; i++) {\r\n        if (dataL[i].editType == 1) {\r\n          if (\"sc\" == version && dataL[i].electrotype && dataL[i].electrotype > 1400) {\r\n            if (\r\n              dataL[i].stationcode5gr == null ||\r\n              dataL[i].stationcode5gr == undefined ||\r\n              dataL[i].stationcode5gr == \"\"\r\n            ) {\r\n              this.errorTips(\r\n                \"电表/协议编号【\" +\r\n                  dataL[i].ammeterName +\r\n                  \"】，项目名称【\" +\r\n                  dataL[i].projectName +\r\n                  \"】关联局站的5GR站址为空，请完善局站信息，或者5GR有效性清单失效，请联系无线管理员。\"\r\n              );\r\n              return;\r\n            }\r\n          }\r\n          //截止日期校验\r\n          let result = _verify_EndDate(dataL[i], dataL[i].enddate);\r\n          if (result) {\r\n            this.errorTips(result);\r\n            return;\r\n          }\r\n          b = true;\r\n          array.push(dataL[i]);\r\n        }\r\n      }\r\n      // });\r\n      if (b) {\r\n        this.submitData(array);\r\n      } else {\r\n        this.errorTips(\"没有可保存数据\");\r\n      }\r\n    },\r\n    preserveSc() {\r\n      this.$refs.checkResult.ammeterids = this.ammeterids;\r\n      this.showJhModel = true;\r\n    },\r\n    submitChange(indexList) {\r\n      let data = [];\r\n      this.submit2.map((item, index) => {\r\n        indexList.map((item2) => {\r\n          if (index == item2) {\r\n            data.push(item);\r\n          }\r\n        });\r\n      });\r\n      this.submit = data;\r\n    },\r\n    async getContractInfo(stationCode) {\r\n      this.contractCount = 0;\r\n      await validContractList({ stationCode: stationCode }).then((res) => {\r\n        if (res.data) {\r\n          this.contractCount = res.data.length;\r\n        }\r\n      });\r\n    },\r\n    getAuditResultNew(data) {\r\n      let arr = [];\r\n      data.forEach((item) => {\r\n        arr.push(item.pcid);\r\n      });\r\n      let param = {\r\n        pcids: arr,\r\n      };\r\n      getAuditResultNew_QXM(param).then((res2) => {\r\n        this.auditResultList = res2.data;\r\n        this.auditResultList.forEach((item) => {\r\n          this.$refs.showAlarmModel.resultList.push(item.msg);\r\n          this.$refs.showAlarmModel.tableData.push(item.powerAuditEntity);\r\n          if (item.staute == \"失败\") {\r\n            // if(item.powerAuditEntity.mutiJtlteCodes=='是'\r\n            // || item.powerAuditEntity.electricityPrices=='否'\r\n            // || item.powerAuditEntity.addressConsistence=='否'\r\n            // || item.powerAuditEntity.reimbursementCycle=='否' || item.powerAuditEntity.electricityContinuity=='否' ||\r\n            // item.powerAuditEntity.shareAccuracy=='否' ||\r\n            // item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n            // item.powerAuditEntity.paymentConsistence=='否'){\r\n            if (item.powerAuditEntity.mutiJtlteCodes == \"是\") {\r\n              this.$refs.showAlarmModel.tableData4.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList4.push(item.msg);\r\n            }\r\n            if (item.powerAuditEntity.electricityPrices == \"否\") {\r\n              this.$refs.showAlarmModel.tableData5.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList5.push(item.msg);\r\n            }\r\n            if (\r\n              item.powerAuditEntity.addressConsistence == \"否\" ||\r\n              item.powerAuditEntity.reimbursementCycle == \"否\" ||\r\n              item.powerAuditEntity.electricityContinuity == \"否\" ||\r\n              item.powerAuditEntity.shareAccuracy == \"否\" ||\r\n              //   item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n              item.powerAuditEntity.paymentConsistence == \"否\" ||\r\n              item.powerAuditEntity.fluctuateContinuity == \"否\"\r\n            ) {\r\n              this.$refs.showAlarmModel.tableData2.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList2.push(item.msg);\r\n            }\r\n          } else {\r\n            if (\r\n              // item.powerAuditEntity.electricityRationality == \"是\" && //电量合理性(省内大数据)\r\n              // item.powerAuditEntity.exclusiveAccuracy == \"是\" && //局站独享共享设置\r\n              item.powerAuditEntity.periodicAnomaly == \"是\" //台账周期合理性\r\n            ) {\r\n              this.$refs.showAlarmModel.tableData1.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList1.push(item.msg);\r\n            } else {\r\n              this.$refs.showAlarmModel.tableData3.push(item.powerAuditEntity);\r\n              this.$refs.showAlarmModel.resultList3.push(item.msg);\r\n            }\r\n          }\r\n          if (this.auditResultList.length > 0) {\r\n            this.auditResultList[this.auditResultList.length - 1].progress = 1;\r\n          }\r\n          this.$refs.showAlarmModel.processData = Number(item.progress) * 100;\r\n          this.$refs.showAlarmModel.scrollList();\r\n        }, 1000);\r\n      });\r\n    },\r\n    save(value) {\r\n      var that = this;\r\n      //进度条页面链接websoket后调用，传jh字段不走保存流程，走稽核流程\r\n      if (value == 1) {\r\n        that.submit[0].jh = \"1\";\r\n        that.submit[0].ymmc = \"铁塔预估电费台账\";\r\n        that.submit.forEach((item1) => {\r\n          this.ctgKeyList.forEach((item2) => {\r\n            if (item1.ammeterName == item2.ammetername) {\r\n              item1.ctgKey = item2.ctgKey;\r\n            }\r\n          });\r\n        });\r\n        if (this.ctgKeyList.length == 0 && this.dataL.length > 0) {\r\n          this.dataL.forEach((item2) => {\r\n            this.ctgKeyList.push({\r\n              ctgKey: item2.ctgKey,\r\n              ammetername: item2.ammetername,\r\n            });\r\n          });\r\n        }\r\n\r\n        this.getAuditResultNew(this.submit);\r\n      } else {\r\n        if (that.submit[0].hasOwnProperty(\"jh\")) {\r\n          delete that.submit[0].jh;\r\n        }\r\n        if (this.name == \"current\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAccount();\r\n          }\r\n        } else if (this.name == \"all\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAllAccount();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //加入归集单，全部非强控\r\n    selectedFQK() {\r\n      let that = this;\r\n      that.$refs.addBillPer.initAmmeter(\r\n        that.$refs.showAlarmModel.selectIds3,\r\n        7,\r\n        this.accountObj.country\r\n      );\r\n    },\r\n    //提交数据\r\n    submitData(data) {\r\n      let a = [];\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let str = \"\";\r\n        let accountno = this.accountObj.accountno;\r\n        let index = 0;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          if (item.projectName != \"小计\" && item.projectName != \"合计\") {\r\n            let obj = verification(item);\r\n            if (obj.result) {\r\n              if (item.pcid == null) {\r\n                item.accountno = accountno;\r\n              }\r\n              a.push(item.ammeterid);\r\n              submitData.push(item);\r\n              number++;\r\n            } else {\r\n              str +=\r\n                \"电表/协议编号为【\" +\r\n                item.ammeterName +\r\n                \"】的台账验证没有通过：【\" +\r\n                obj.str +\r\n                \"】；\";\r\n            }\r\n          }\r\n        });\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          this.errorTips(str);\r\n        }\r\n        if (submitData.length > 0) {\r\n          this.submit = submitData;\r\n\r\n          this.submit2 = submitData;\r\n          addAccountEs(submitData).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：成功保存 \" + submitData.length + \" 条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    addElectricType() {\r\n      let companyId = this.accountObj.company;\r\n      let country = this.accountObj.country;\r\n      if (companyId != null && country != null) {\r\n        let obj = {\r\n          company: companyId,\r\n          country: country,\r\n          accountno: this.accountObj.accountno,\r\n          accountType: \"2\",\r\n          accountestype: 1,\r\n        };\r\n        this.$refs.selectAmmeter.initAmmeter(obj);\r\n      } else {\r\n        this.errorTips(\"请选择分公司和部门\");\r\n      }\r\n    },\r\n    //验证错误弹出提示框\r\n    errorTips(str) {\r\n      this.$Notice.error({\r\n        title: \"提示\",\r\n        desc: str,\r\n        duration: 10,\r\n      });\r\n    },\r\n    handlePage(value) {\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n\r\n      this.pageNum = value;\r\n      this.getAccountMessages();\r\n    },\r\n    handlePageSize(value) {\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n\r\n      this.pageSize = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //向后台请求数据\r\n    getAccountMessages() {\r\n      let params = this.accountObj;\r\n      params.pageNum = this.pageNum;\r\n      params.pageSize = this.pageSize;\r\n      let req = {\r\n        url: \"/business/accountEs/selectAccountEsList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      this.tbAccount.loading = true;\r\n      axios\r\n        .request(req)\r\n        .then((res) => {\r\n          this.tbAccount.loading = false;\r\n          if (res.data) {\r\n            let data = res.data.rows;\r\n            data.forEach(function (item) {\r\n              item.editType = 0;\r\n            });\r\n            data.push(this.suntotal(data)); //小计\r\n            accountEsTotal(this.accountObj).then((res) => {\r\n              //合计\r\n              let alltotal = res.data;\r\n              alltotal.total = \"合计\";\r\n              alltotal.projectName = \"合计\";\r\n              alltotal._disabled = true;\r\n              data.push(alltotal);\r\n            });\r\n            this.tbAccount.data = data;\r\n            this.pageTotal = res.data.total || 0;\r\n            this.setMyStyle(this.tbAccount.data.length);\r\n\r\n            this.editIndex = -1;\r\n            this.columnsIndex = -1;\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //小计\r\n    suntotal(array) {\r\n      let curusedreadings = 0;\r\n      let transformerullage = 0;\r\n      let accountmoney = 0;\r\n      array.forEach(function (item) {\r\n        if (item.effective === 1) {\r\n          curusedreadings += item.curusedreadings;\r\n          transformerullage += item.transformerullage;\r\n          accountmoney += item.accountmoney;\r\n        }\r\n      });\r\n      return {\r\n        curusedreadings: curusedreadings,\r\n        transformerullage: transformerullage,\r\n        accountmoney: accountmoney,\r\n        total: \"小计\",\r\n        projectName: \"小计\",\r\n        _disabled: true,\r\n      };\r\n    },\r\n    //重置\r\n    onResetHandle() {\r\n      this.accountObj = {\r\n        supplybureauammetercode: \"\",\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        company: this.company,\r\n        projectName: \"\", //项目名称\r\n        country: Number(this.country), //所属部门\r\n        ammeterName: \"\", //电表户号/协议编码\r\n        stationName: \"\",\r\n        accountType: \"1\", //台账类型\r\n        accountestype: 3, //台账类型\r\n        supplybureauammetercode: \"\",\r\n        countryName: this.countryName,\r\n      };\r\n      this.getAccountMessages();\r\n    },\r\n    //计算单价\r\n    unitPrice(row) {\r\n      let accountmoney = row.accountmoney;\r\n      let curusedreadings = row.curusedreadings;\r\n      if (accountmoney != null && curusedreadings != null) {\r\n        let total = null;\r\n        if (curusedreadings == 0) {\r\n          total = 0;\r\n        } else {\r\n          total = accountmoney / curusedreadings;\r\n        }\r\n\r\n        row.unitpirce = total.toFixed(2);\r\n      }\r\n    },\r\n    //验证单价\r\n    validateUnitPrice(data) {\r\n      let category = data.category; //电表描述类型\r\n      let ammeteruse = data.ammeteruse; //电表用途\r\n      let unitpirce = data.unitpirce; //台账单价\r\n      if (!judge_negate(category) && !judge_recovery(ammeteruse)) {\r\n        // if (unitpirce) {\r\n        //   if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n        //     this.errorTips(\r\n        //       \"集团要求单价范围在0.3~2元，此台账单价: \" +\r\n        //         unitpirce +\r\n        //         \" 已超过范围，请确认！\"\r\n        //     );\r\n        //   }\r\n        // }\r\n        if (unitpirce) {\r\n          if (unitpirce != null && unitpirce < unitpirceMax1) {\r\n            // if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n            this.errorTips(\r\n              \"单价范围必须大于0.1元，此台账单价: \" + unitpirce + \"不在范围内，请确认！\"\r\n            );\r\n          }\r\n        }\r\n      }\r\n    },\r\n    remove() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      if (data == null || data.length === 0) {\r\n        this.errorTips(\"请选择要删除的数据\");\r\n        return;\r\n      }\r\n      this.$Modal.confirm({\r\n        title: \"提示\",\r\n        content: \"<p>是否确认删除选中信息？</p>\",\r\n        onOk: () => {\r\n          let b = true;\r\n          let ids = \"\";\r\n          let array = this.tbAccount.data;\r\n          let total = this.pageTotal;\r\n          for (let i = 0; i < data.length; i++) {\r\n            let item = data[i];\r\n            if (item.pcid != null && item.pcid.length > 0) {\r\n              if (item.pabriid) {\r\n                b = false;\r\n              }\r\n              ids += item.pcid + \",\";\r\n            } else {\r\n              for (let j = array.length - 1; j >= 0; j--) {\r\n                let jj = array[j];\r\n                if (jj.ammeterid === item.ammeterid) {\r\n                  array.splice(j, 1);\r\n                  total = total - 1;\r\n                }\r\n              }\r\n            }\r\n          }\r\n          this.pageTotal = total;\r\n          if (b) {\r\n            if (ids.length > 0) {\r\n              removeAccountEs(ids).then((res) => {\r\n                if (res.data.code == 0) {\r\n                  this.$Message.success(\"删除成功\");\r\n                  this.getAccountMessages();\r\n                }\r\n              });\r\n            }\r\n          } else {\r\n            this.errorTips(\"选中信息中有信息还没有跟归集单解除关联，请先解除关联\");\r\n          }\r\n        },\r\n        onCancel: () => {},\r\n      });\r\n    },\r\n    //一键删除数据\r\n    deleteAll() {\r\n      this.$Modal.confirm({\r\n        title: \"提示\",\r\n        content: \"<p>确定一键删除吗？</p>\",\r\n        onOk: () => {\r\n          this.tbAccount.loading = true;\r\n          let params = this.accountObj;\r\n          params.removeAllFlag = true;\r\n          delete params.pageSize;\r\n          delete params.pageNum;\r\n          removeAll(params).then((res) => {\r\n            this.tbAccount.loading = false;\r\n            if (res.data.num > 0) {\r\n              this.$Message.success(\"一键删除成功\");\r\n              this.searchList();\r\n            } else {\r\n              this.$Message.error(\"一键删除失败\");\r\n            }\r\n          });\r\n        },\r\n        onCancel: () => {},\r\n      });\r\n    },\r\n    //加入归集单\r\n    addPreserveGJ() {\r\n      let dataL = this.$refs.accountEsTable.getSelection();\r\n      this.dataL = this.$refs.accountEsTable.getSelection();\r\n      if (dataL == null || dataL.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        this.addSubmitDataGJ(dataL);\r\n      }\r\n    },\r\n    //加入归集单\r\n    addPreserveGJAll() {\r\n      let params = this.accountObj;\r\n      params.pageNum = 1;\r\n      params.pageSize = 20000;\r\n      let req = {\r\n        url: \"/business/accountEs/selectAccountEsList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      let array1 = [];\r\n      this.tbAccount.loading = true;\r\n      axios.request(req).then((res) => {\r\n        this.tbAccount.loading = false;\r\n        let dataL = res.data.rows;\r\n        this.dataL = res.data.rows;\r\n        this.addSubmitDataGJ(dataL);\r\n      });\r\n    },\r\n    //提交归集单数据\r\n    addSubmitDataGJ(data) {\r\n      let a = [];\r\n      let str = \"\";\r\n      let str1 = \"\";\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let b = 1;\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let obj = verification(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n        });\r\n        that.ammeterids = a;\r\n        if (b === 1) {\r\n          // this.$refs.addBillPer.initAmmeter(ids,7,this.accountObj.country);\r\n          if (submitData.length > 0) {\r\n            this.submit = submitData;\r\n            this.submit2 = submitData;\r\n            this.preserveSc();\r\n          }\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n        if (str.length > 0) {\r\n          that.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n      }\r\n    },\r\n    openAddBillPerModal(name) {\r\n      this.name = name;\r\n      if (name === \"current\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJ();\r\n        // } else {\r\n        this.selectedAccount();\r\n        // }\r\n      } else if (name === \"all\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJAll();\r\n        // } else {\r\n        this.selectedAllAccount();\r\n        // }\r\n      }\r\n    },\r\n    //加入归集单，全部有效台账\r\n    selectedAllAccount() {\r\n      let that = this;\r\n      that.spinShow = true;\r\n      selectIdsByEsParams(this.accountObj).then((res) => {\r\n        that.spinShow = false;\r\n        if (res.data.length == 0) {\r\n          that.errorTips(\"无有效数据可加入归集单\");\r\n        } else {\r\n          that.$refs.addBillPer.initAmmeter(res.data, 7, this.accountObj.country);\r\n          // that.$refs.addBillPer.initAmmeter(\r\n          //   this.$refs.showAlarmModel.selectIds1,\r\n          //   7,\r\n          //   this.accountObj.country\r\n          // );\r\n        }\r\n      });\r\n    },\r\n    selectedAccount() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      let b = 1;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        let ids = [];\r\n        data.forEach(function (item) {\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n          ids.push(item.pcid);\r\n        });\r\n        if (b === 1) {\r\n          this.$refs.addBillPer.initAmmeter(ids, 7, this.accountObj.country);\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n      }\r\n    },\r\n    openCompletedPreModal() {\r\n      this.$refs.completedPre.initAmmeter(this.accountObj.country, 7);\r\n    },\r\n    againJoin() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      let b = true;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要重新加入归集单的台账\");\r\n      } else {\r\n        let ids = \"\";\r\n        data.forEach(function (item) {\r\n          let status = item.status;\r\n          if (status != 5) {\r\n            b = false;\r\n          }\r\n          ids += item.pcid + \",\";\r\n        });\r\n        if (b) {\r\n          againJoin(ids).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：操作成功\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        } else {\r\n          this.errorTips(\"只有已退回的台账才能重新加入归集单\");\r\n        }\r\n      }\r\n    },\r\n    submitChange1(data) {\r\n      this.submit = data;\r\n    },\r\n    refresh() {\r\n      if (this.$refs.showAlarmModel.fromGuijidan != 1) {\r\n        let obj = this;\r\n        obj.showAlarmModel = false;\r\n        setTimeout(function () {\r\n          obj.getAccountMessages();\r\n        }, 200);\r\n      } else {\r\n        this.showAlarmModel = true;\r\n      }\r\n    },\r\n    beforeLoadData(data, str) {\r\n      var cols = [],\r\n        keys = [];\r\n      for (var i = 0; i < this.tbAccount.exportColumns.length; i++) {\r\n        cols.push(this.tbAccount.exportColumns[i].title);\r\n        keys.push(this.tbAccount.exportColumns[i].key);\r\n      }\r\n      const params = {\r\n        title: cols,\r\n        key: keys,\r\n        data: data,\r\n        autoWidth: true,\r\n        filename: str,\r\n      };\r\n      excel.export_array_to_excel(params);\r\n      return;\r\n    },\r\n    exportCsv(name) {\r\n      this.export.run = true;\r\n      if (name === \"current\") {\r\n        this.beforeLoadData(this.tbAccount.data, \"预估台账导出数据\");\r\n      } else if (name === \"all\") {\r\n        let params = this.accountObj;\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n        let req = {\r\n          url: \"/business/accountEs/selectAccountEsList\",\r\n          method: \"get\",\r\n          params: params,\r\n        };\r\n        this.tbAccount.loading = true;\r\n        axios\r\n          .request(req)\r\n          .then((res) => {\r\n            this.tbAccount.loading = false;\r\n            if (res.data) {\r\n              let array = res.data.rows;\r\n              accountEsTotal(this.accountObj).then((res) => {\r\n                //合计\r\n                let alltotal = res.data;\r\n                alltotal.total = \"合计\";\r\n                alltotal._disabled = true;\r\n                array.push(alltotal);\r\n                this.beforeLoadData(array, \"预估台账导出数据\");\r\n              });\r\n            }\r\n          })\r\n          .catch((err) => {\r\n            console.log(err);\r\n          });\r\n      }\r\n    },\r\n    validate() {\r\n      if (this.columnsIndex != 5) {\r\n        let val = this.enterOperate(this.columnsIndex).data;\r\n        if (val) {\r\n          if (testNumber(val)) {\r\n            switch (this.columnsIndex) {\r\n              case 1:\r\n                this.validateStartdate();\r\n                break;\r\n              case 2:\r\n                this.validateEnddate();\r\n                break;\r\n              case 3:\r\n                this.validatecurusedreadings();\r\n                break;\r\n              case 4:\r\n                this.validateaccountmoney();\r\n                break;\r\n            }\r\n          } else {\r\n            this.errorTips(\"请输入数字！\");\r\n          }\r\n        }\r\n      }\r\n    },\r\n    validateStartdate() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editStartDate;\r\n      let result = _verify_StartDate(data, val);\r\n      if (result) {\r\n        //失败就弹出提示内容\r\n        this.errorTips(result);\r\n        this.myStyle[this.editIndex].startdate = \"errorStle\";\r\n      } else {\r\n        this.myStyle[this.editIndex].startdate = \"myspan\";\r\n        data.startdate = val;\r\n        data.editType = 1;\r\n      }\r\n    },\r\n    validateEnddate() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editEndDate;\r\n      // 验证截止日期方法\r\n      let result = _verify_EndDate(data, val);\r\n      if (result) {\r\n        //失败就弹出提示内容，并将数据恢复初始化\r\n        this.errorTips(result);\r\n      } else {\r\n        data.enddate = val;\r\n        data.editType = 1;\r\n      }\r\n    },\r\n    validatecurusedreadings() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editcurusedreadings;\r\n      data.curusedreadings = val;\r\n      data.totalusedreadings = val;\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n    },\r\n    validatetransformerullage() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.edittransformerullage;\r\n      data.transformerullage = val;\r\n      data.editType = 1;\r\n    },\r\n    validateaccountmoney() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editaccountmoney;\r\n      data.accountmoney = val; //允许负数 Math.abs(val)\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      this.validateUnitPrice(data);\r\n    },\r\n    setremark() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editremark;\r\n      data.remark = val;\r\n      data.editType = 1;\r\n    },\r\n    setMyStyle(length) {\r\n      this.myStyle = [];\r\n      for (var i = 0; i < length; i++) {\r\n        this.myStyle.push({\r\n          startdate: \"myspan\",\r\n          enddate: \"myspan\",\r\n          curusedreadings: \"myspan\",\r\n          accountmoney: \"myspan\",\r\n          remark: \"myspan\",\r\n        });\r\n      }\r\n    },\r\n    //span点击事件将span换成输入框并且获取焦点\r\n    selectCall(row, index, columns, str) {\r\n      this.editStartDate = row.startdate;\r\n      this.editEndDate = row.enddate;\r\n      this.editcurusedreadings =\r\n        row.curusedreadings == null || row.curusedreadings === 0\r\n          ? null\r\n          : row.curusedreadings;\r\n      this.editaccountmoney =\r\n        row.accountmoney == null || row.accountmoney === 0 ? null : row.accountmoney;\r\n      this.editremark = row.remark;\r\n\r\n      this.editIndex = index;\r\n      this.columnsIndex = columns;\r\n\r\n      let a = this;\r\n      setTimeout(function () {\r\n        if (columns != 8) {\r\n          a.$refs[str + index + columns].focus();\r\n        }\r\n      }, 200);\r\n    },\r\n    //跳转到下一格\r\n    nextCell(data) {\r\n      let index = data.editIndex;\r\n      let columns = data.columnsIndex;\r\n      let row = \"\";\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n      } else if (index > -1 && columns === 5) {\r\n        //当跳转的最后一行最后一格的时候\r\n        if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n          index = 0;\r\n        } else {\r\n          index++;\r\n        }\r\n        columns = 1;\r\n      } else {\r\n        columns += 1;\r\n      }\r\n      data.editIndex = index;\r\n      data.columnsIndex = columns;\r\n      row = data.tbAccount.data[index];\r\n      if (row) {\r\n        data.editStartDate = row.startdate;\r\n        data.editEndDate = row.enddate;\r\n        data.editcurusedreadings =\r\n          row.curusedreadings == null || row.curusedreadings === 0\r\n            ? null\r\n            : row.curusedreadings;\r\n        data.editaccountmoney =\r\n          row.accountmoney == null || row.accountmoney === 0 ? null : row.accountmoney;\r\n        data.editremark = row.remark;\r\n      }\r\n\r\n      setTimeout(function () {\r\n        data.$refs[data.enterOperate(columns).str + index + columns].focus();\r\n      }, 200);\r\n    },\r\n    //根据列号返回对应的列名\r\n    enterOperate(number) {\r\n      let str = \"\";\r\n      let data = null;\r\n      switch (number) {\r\n        case 1:\r\n          str = \"startdate\";\r\n          data = this.editStartDate;\r\n          break;\r\n        case 2:\r\n          str = \"enddate\";\r\n          data = this.editEndDate;\r\n          break;\r\n        case 3:\r\n          str = \"curusedreadings\";\r\n          data = this.editcurusedreadings;\r\n          break;\r\n        case 4:\r\n          str = \"accountmoney\";\r\n          data = this.editaccountmoney;\r\n          break;\r\n        case 5:\r\n          str = \"remark\";\r\n          data = this.editremark;\r\n          break;\r\n      }\r\n      return { str: str, data: data };\r\n    },\r\n    pred() {\r\n      var lett = this;\r\n      let index = lett.editIndex;\r\n      let columns = lett.columnsIndex;\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n        lett.editIndex = index;\r\n        lett.columnsIndex = columns;\r\n        lett.editStartDate = lett.tbAccount.data[index].startdate;\r\n        setTimeout(function () {\r\n          lett.$refs[lett.enterOperate(columns).str + index + columns].focus();\r\n        }, 200);\r\n      } else {\r\n        lett.validate();\r\n        lett.setremark();\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    ellipsis(value) {\r\n      if (!value) return \"\";\r\n      if (value.length > 3) {\r\n        return value.slice(0, 3) + \"...\";\r\n      }\r\n      return value;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.page-class {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  .cl-table {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n  .button-bar {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n.yjjh > .ivu-modal-wrap > .ivu-modal {\r\n  top: 20px !important;\r\n}\r\n.mytable .ivu-table-cell {\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: normal;\r\n  word-break: break-all;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.accountEs .filter-divider {\r\n  margin: 0px;\r\n  text-align: center;\r\n}\r\n.accountEs .header-bar-show {\r\n  max-height: 300px;\r\n  padding-top: 14px;\r\n  overflow: inherit;\r\n  border-bottom: 1px solid #e8eaec;\r\n}\r\n.accountEs .header-bar-hide {\r\n  max-height: 0;\r\n  padding-top: 0;\r\n  overflow: hidden;\r\n  border-bottom: 0;\r\n}\r\n\r\n.mytable .myspan {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n}\r\n.mytable .errorStle {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n  color: red;\r\n}\r\n</style>\r\n"]}]}