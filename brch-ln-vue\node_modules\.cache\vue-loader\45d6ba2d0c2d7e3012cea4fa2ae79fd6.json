{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\listAmmeter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\listAmmeter.vue", "mtime": 1754285403019}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IHdoaXRlTGlzdCwgd2hpdGVJbnNlcnQgfSBmcm9tICdAL2FwaS9hY2NvdW50JzsKaW1wb3J0IHtzZWxlY3RDaGFuZ2VBbW1ldGVyLGxpc3RBbW1ldGVyLCByZW1vdmVBbW1ldGVyLGNoZWNrQWNvdW50QnlVcGRhdGUsZ2V0VXNlckJ5VXNlclJvbGUsZ2V0Q291bnRyeUJ5VXNlcklkLGdldFVzZXJkYXRhLGdldENvdW50cnlzZGF0YSxnZXRDbGFzc2lmaWNhdGlvbixjaGVja1N0YXJ0Rmxvd30gZnJvbSAnQC9hcGkvYmFzZWRhdGEvYW1tZXRlci5qcycKaW1wb3J0IHtpc0luVG9kb0xpc3R9ZnJvbSJAL2FwaS9hbGVydGNvbnRyb2wvYWxlcnRjb250cm9sIjsKaW1wb3J0IHtibGlzdCxidGV4dH0gZnJvbSAiQC9saWJzL3Rvb2xzIjsKaW1wb3J0IHZpZXdBbW1ldGVyUGFnZSBmcm9tICcuL3ZpZXdBbW1ldGVyLnZ1ZScKaW1wb3J0IHZpZXdRdW90YVBhZ2UgZnJvbSAnQC92aWV3L2Jhc2VkYXRhL3F1b3RhL3ZpZXdRdW90YS52dWUnOwppbXBvcnQgdmlld1N0YXRpb25QYWdlIGZyb20gJ0Avdmlldy9iYXNlZGF0YS9zdGF0aW9uL3ZpZXdTdGF0aW9uLnZ1ZScKaW1wb3J0IGNvdW50cnlNb2RhbCBmcm9tICIuL2NvdW50cnlNb2RhbCI7CmltcG9ydCBQcm9jZXNzSW5mbyBmcm9tICdAL3ZpZXcvYmFzaWMvc3lzdGVtL3dvcmtmbG93L3Byb2Nlc3MtaW5mbyc7CmltcG9ydCBXb3JrRmxvd0luZm9Db21wb25ldCBmcm9tICdAL3ZpZXcvYmFzaWMvc3lzdGVtL3dvcmtmbG93L3dvcmtGbG93SW5mb0NvbXBvbmV0JwoKaW1wb3J0IGF0dGFjaEZpbGUgZnJvbSAiQC92aWV3L2Jhc2VkYXRhL3doaXRlbGlzdC9hdHRhY2hGaWxlLnZ1ZSI7CmltcG9ydCB7IGF0dGNoTGlzdCwgcmVtb3ZlQXR0YWNoIH0gZnJvbSAnQC9hcGkvYmFzZWRhdGEvYW1tZXRlci5qcycKaW1wb3J0IGF4aW9zIGZyb20gJ0AvbGlicy9hcGkucmVxdWVzdCcKCmltcG9ydCBleGNlbCBmcm9tICdAL2xpYnMvZXhjZWwnCgppbXBvcnQge21hcE11dGF0aW9uc30gZnJvbSAidnVleCI7CmltcG9ydCByb3V0ZXJzIGZyb20gJ0Avcm91dGVyL3JvdXRlcnMnOwppbXBvcnQge2dldEhvbWVSb3V0ZX0gZnJvbSAnQC9saWJzL3V0aWwnOwpleHBvcnQgZGVmYXVsdCB7CiAgICBuYW1lOiAnYW1tZXRlcicsCiAgICBjb21wb25lbnRzOiB7CiAgICAgICAgUHJvY2Vzc0luZm8sCiAgICAgICAgV29ya0Zsb3dJbmZvQ29tcG9uZXQsCiAgICAgICAgLy8gYWRkQW1tZXRlclBhZ2UsCiAgICAgICAgLy8gZWRpdEFtbWV0ZXJQYWdlLAogICAgICAgIHZpZXdBbW1ldGVyUGFnZSwKICAgICAgICB2aWV3U3RhdGlvblBhZ2UsCiAgICAgICAgdmlld1F1b3RhUGFnZSwKICAgICAgICBjb3VudHJ5TW9kYWwsCiAgICAgICAgYXR0YWNoRmlsZQogICAgfSwKCiAgICBkYXRhKCkgewogICAgICAgIC8v54q25oCBCiAgICAgICAgbGV0IHJlbmRlclN0YXR1cyA9IChoLCBwYXJhbXMpID0+IHsKICAgICAgICAgICAgbGV0IHZhbHVlID0gIiI7CiAgICAgICAgICAgIGZvciAobGV0IGl0ZW0gb2YgdGhpcy5zdGF0dXMpIHsKICAgICAgICAgICAgICAgIGlmIChpdGVtLnR5cGVDb2RlID09IHBhcmFtcy5yb3cuc3RhdHVzKSB7CiAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSBpdGVtLnR5cGVOYW1lOwogICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiBoKCJkaXYiLCB2YWx1ZSk7CiAgICAgICAgfTsKICAgICAgICAvL+eUqOeUteexu+WeiwogICAgICAgIGxldCByZW5kZXJFbGVjdHJvVHlwZSA9IChoLCBwYXJhbXMpID0+IHsKICAgICAgICAgICAgbGV0IHZhbHVlID0gIiI7CiAgICAgICAgICAgIGZvciAobGV0IGl0ZW0gb2YgdGhpcy5lbGVjdHJvVHlwZSkgewogICAgICAgICAgICAgICAgaWYgKGl0ZW0udHlwZUNvZGUgPT0gcGFyYW1zLnJvdy5lbGVjdHJvdHlwZSkgewogICAgICAgICAgICAgICAgICAgIHZhbHVlID0gaXRlbS50eXBlTmFtZTsKICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gaCgiZGl2IiwgdmFsdWUpOwogICAgICAgIH07CiAgICAgICAgLy/nlLXooajnsbvlnosKICAgICAgICBsZXQgcmVuZGVyQW1tZXRlclR5cGUgPSAoaCwgcGFyYW1zKSA9PiB7CiAgICAgICAgICAgIGxldCB2YWx1ZSA9ICIiOwogICAgICAgICAgICBmb3IgKGxldCBpdGVtIG9mIHRoaXMuYW1tZXRlclR5cGUpIHsKICAgICAgICAgICAgICAgIGlmIChpdGVtLnR5cGVDb2RlID09IHBhcmFtcy5yb3cuYW1tZXRlcnR5cGUpIHsKICAgICAgICAgICAgICAgICAgICB2YWx1ZSA9IGl0ZW0udHlwZU5hbWU7CiAgICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIGgoImRpdiIsIHZhbHVlKTsKICAgICAgICB9OwogICAgICAgIC8v55So55S15oCn6LSoCiAgICAgICAgbGV0IHJlbmRlckVsZWN0cm9OYXR1cmUgPSAoaCwgcGFyYW1zKSA9PiB7CiAgICAgICAgICAgIGxldCB2YWx1ZSA9ICIiOwogICAgICAgICAgICBmb3IgKGxldCBpdGVtIG9mIHRoaXMuZWxlY3Ryb05hdXJlKSB7CiAgICAgICAgICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSBwYXJhbXMucm93LmVsZWN0cm9uYXR1cmUpIHsKICAgICAgICAgICAgICAgICAgICB2YWx1ZSA9IGl0ZW0udHlwZU5hbWU7CiAgICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIGgoImRpdiIsIHZhbHVlKTsKICAgICAgICB9OwogICAgICAgIC8v5LuY6LS55pa55byPCiAgICAgICAgbGV0IHJlbmRlclBheVR5cGUgPSAoaCwgcGFyYW1zKSA9PiB7CiAgICAgICAgICAgIGxldCB2YWx1ZSA9ICIiOwogICAgICAgICAgICBmb3IgKGxldCBpdGVtIG9mIHRoaXMucGF5VHlwZSkgewogICAgICAgICAgICAgICAgaWYgKGl0ZW0udHlwZUNvZGUgPT0gcGFyYW1zLnJvdy5wYXl0eXBlKSB7CiAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSBpdGVtLnR5cGVOYW1lOwogICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiBoKCJkaXYiLCB2YWx1ZSk7CiAgICAgICAgfTsKICAgICAgICAvL+eUteS7t+aAp+i0qAogICAgICAgIGxldCByZW5kZXJFbGVjdHJvdmFsZW5jZU5hdHVyZSA9IChoLCBwYXJhbXMpID0+IHsKICAgICAgICAgICAgbGV0IHZhbHVlID0gIiI7CiAgICAgICAgICAgIGZvciAobGV0IGl0ZW0gb2YgdGhpcy5lbGVjdHJvdmFsZW5jZU5hdHVyZSkgewogICAgICAgICAgICAgICAgaWYgKGl0ZW0udHlwZUNvZGUgPT0gcGFyYW1zLnJvdy5lbGVjdHJvdmFsZW5jZW5hdHVyZSkgewogICAgICAgICAgICAgICAgICAgIHZhbHVlID0gaXRlbS50eXBlTmFtZTsKICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gaCgiZGl2IiwgdmFsdWUpOwogICAgICAgIH07CiAgICAgICAgLy/ljZXmja7nirbmgIEKICAgICAgICBsZXQgcmVuZGVyQmlsbFN0YXR1cyA9IChoLCBwYXJhbXMpID0+IHsKICAgICAgICAgICAgbGV0IHZhbHVlID0gIiI7CiAgICAgICAgICAgIGZvciAobGV0IGl0ZW0gb2YgdGhpcy5iaWxsU3RhdHVzKSB7CiAgICAgICAgICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSBwYXJhbXMucm93LmJpbGxTdGF0dXMpIHsKICAgICAgICAgICAgICAgICAgICB2YWx1ZSA9IGl0ZW0udHlwZU5hbWU7CiAgICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIGgoImRpdiIsIHZhbHVlKTsKICAgICAgICB9OwogICAgICAgIC8v5Lqn5p2D5b2S5bGeCiAgICAgICAgbGV0IHJlbmRlclByb3BlcnR5ID0gKGgsIHBhcmFtcykgPT4gewogICAgICAgICAgICBsZXQgdmFsdWUgPSAiIjsKICAgICAgICAgICAgZm9yIChsZXQgaXRlbSBvZiB0aGlzLnByb3BlcnR5KSB7CiAgICAgICAgICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSBwYXJhbXMucm93LnByb3BlcnR5KSB7CiAgICAgICAgICAgICAgICAgICAgdmFsdWUgPSBpdGVtLnR5cGVOYW1lOwogICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiBoKCJkaXYiLCB2YWx1ZSk7CiAgICAgICAgfTsKICAgICAgICAvL+WvueWklue7k+eul+exu+WeiwogICAgICAgIGxldCByZW5kZXJEaXJlY3RzdXBwbHlmbGFnID0gKGgsIHBhcmFtcykgPT4gewogICAgICAgICAgICBsZXQgdmFsdWUgPSAiIjsKICAgICAgICAgICAgZm9yIChsZXQgaXRlbSBvZiB0aGlzLmRpcmVjdHN1cHBseUZsYWcpIHsKICAgICAgICAgICAgICAgIGlmIChpdGVtLnR5cGVDb2RlID09IHBhcmFtcy5yb3cuZGlyZWN0c3VwcGx5ZmxhZykgewogICAgICAgICAgICAgICAgICAgIHZhbHVlID0gaXRlbS50eXBlTmFtZTsKICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICByZXR1cm4gaCgiZGl2IiwgdmFsdWUpOwogICAgICAgIH07CiAgICAgICAgLy/mn6XnnIvor6bmg4UKICAgICAgICBsZXQgcmVudGVyVmlld0FtbWV0ZXIgPSAoaCwgcGFyYW1zKSA9PiB7CiAgICAgICAgICAgIGxldCBjb2x1bW4gPSBwYXJhbXMuY29sdW1uLmtleTsKICAgICAgICAgICAgcmV0dXJuIGgoImRpdiIsIFtoKCJ1IiwgewogICAgICAgICAgICAgICAgc3R5bGU6e2Rpc3BsYXk6ImlubGluZSJ9LAogICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgICBjbGljazogKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnZpZXdBbW1ldGVyKHBhcmFtcy5yb3cuaWQpOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgcGFyYW1zLnJvd1tjb2x1bW5dKV0pOwogICAgICAgIH07CiAgICAgICAgLy/mn6XnnIvlsYDnq5nor6bmg4UKICAgICAgICBsZXQgcmVudGVyVmlld1N0YXRpb24gPSAoaCwgcGFyYW1zKSA9PiB7CiAgICAgICAgICAgIGxldCBjb2x1bW4gPSBwYXJhbXMuY29sdW1uLmtleTsKICAgICAgICAgICAgcmV0dXJuIGgoImRpdiIsIFtoKCJ1IiwgewogICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgICBjbGljazogKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLnZpZXdTdGF0aW9uKHBhcmFtcy5yb3cuc3RhdGlvbmNvZGUpOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgcGFyYW1zLnJvd1tjb2x1bW5dKV0pOwogICAgICAgIH07CiAgICAgIC8v6LWE5rqQ5bGA56uZaWQKICAgICAgbGV0IHRlcm1uYW1lRm9ybWF0ID0gKGgsIHBhcmFtcykgPT4gewogICAgICAgIGlmICghcGFyYW1zLnJvdy5yZXNzdGF0aW9uY29kZSkgewogICAgICAgICAgLy8g6L+U5Zue57qi6Imy5paH5a2X5o+Q56S677ya6K+357u05oqkCiAgICAgICAgICByZXR1cm4gaCgnZGl2JywgewogICAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnI2YwMCcKICAgICAgICAgICAgfSwKICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICBjbGljazogKCkgPT4gewogICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLlnKjkv67mlLnnlLXooajpobXpnaLvvIzlhbPogZTlsYDnq5nkv6Hmga/lpITnu7TmiqQiKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0sICfor7fnu7TmiqQnKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gaCgnZGl2JywgcGFyYW1zLnJvdy5yZXNzdGF0aW9uY29kZSkKICAgICAgICB9CgogICAgICB9OwogICAgICAgIGxldCByZW5kZXJXID0gKGgsIHBhcmFtcykgPT4gewogICAgICAgICAgICBsZXQgdGhhdCA9IHRoaXM7CiAgICAgICAgICAgIGxldCB0ZXh0LCB0eXBlID0gIiI7CiAgICAgICAgICAgIGxldCByb3cgPSBwYXJhbXMucm93OwogICAgICAgICAgICBpZiAocm93LmJpbGxTdGF0dXMgIT0gMCAmJiByb3cuYmlsbFN0YXR1cyAhPSAzICYmIHJvdy5wcm9jZXNzaW5zdElkICE9IG51bGwpIHsKICAgICAgICAgICAgICAgIHRleHQgPSAi5p+l55yLIjsKICAgICAgICAgICAgICAgIHR5cGUgPSAic3VjY2VzcyI7CiAgICAgICAgICAgIH0gZWxzZSBpZiAocGFyYW1zLnJvdy5iaWxsU3RhdHVzID09IDAgfHwgcGFyYW1zLnJvdy5iaWxsU3RhdHVzID09IDMpIHsKICAgICAgICAgICAgICAgIHRleHQgPSAi5o+Q5LqkIjsKICAgICAgICAgICAgICAgIHR5cGUgPSAicHJpbWFyeSI7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYodHlwZSA9PSAiIil7CiAgICAgICAgICAgICAgICByZXR1cm4gaCgiZGl2Iiwge30sIHRleHQpOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiBoKCJCdXR0b24iLAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgICBwcm9wczogewogICAgICAgICAgICAgICAgICAgIHR5cGU6IHR5cGUsIHNpemU6ICJzbWFsbCIKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICBvbjogewogICAgICAgICAgICAgICAgICAgIGNsaWNrKCkgewogICAgICAgICAgICAgICAgICAgICAgICBpZiAocm93LmJpbGxTdGF0dXMhPTAgJiYgcm93LmJpbGxTdGF0dXMgIT0gMyAmJiByb3cucHJvY2Vzc2luc3RJZCAhPSBudWxsKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGF0LnNob3dGbG93KHBhcmFtcy5yb3csIHBhcmFtcy5yb3cucHJvY2Vzc2luc3RJZCk7CiAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocGFyYW1zLnJvdy5iaWxsU3RhdHVzID09IDAgfHwgcGFyYW1zLnJvdy5iaWxsU3RhdHVzID09IDMpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQubG9hZGluZz10cnVlOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5zdGFydEZsb3cocGFyYW1zLnJvdyk7CiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHRleHQKICAgICAgICAgICAgKTsKICAgICAgICB9OwogICAgICAgIGxldCByZW5kZXJRdW90YSA9IChoLCBwYXJhbXMpID0+IHsKICAgICAgICAgICAgbGV0IHRoYXQgPSB0aGlzOwogICAgICAgICAgICByZXR1cm4gaCgiQnV0dG9uIiwgewogICAgICAgICAgICAgICAgcHJvcHM6IHsKICAgICAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsIHNpemU6ICJzbWFsbCIKICAgICAgICAgICAgICAgIH0sYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZDogcGFyYW1zLnJvdy5xdW90YUlkP2ZhbHNlOnRydWUKICAgICAgICAgICAgICAgIH0sc3R5bGU6IHsKICAgICAgICAgICAgICAgICAgICBvcGFjaXR5OnBhcmFtcy5yb3cucXVvdGFJZD8xOjAuNAogICAgICAgICAgICAgICAgfSwgb246IHsKICAgICAgICAgICAgICAgICAgICBjbGljaygpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC52aWV3UXVvdGEocGFyYW1zLnJvdy5xdW90YUlkKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0sICLmn6XnnIsiKTsKICAgICAgICB9OwogICAgICAgIGNvbnN0IHZhbGlzaGVyZWRlcGFydG5hbWUgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgICAgICAgIC8vY29uc29sZS5sb2cocnVsZSwgdmFsdWUsICJydWxlLCB2YWx1ZTU1NTU1NTU1NTU1NTU1NTUiKTsKICAgICAgICAgICAgaWYgKHZhbHVlID09IG51bGwgfHwgdmFsdWUgPT0gJycpIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign5LiN6IO95Li656m6JykpCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBjYWxsYmFjaygpCiAgICAgICAgICAgIH0KICAgICAgICB9OwogICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIG11bHRpRmlsZXM6IG51bGwsCiAgICAgICAgICAgIHJlbW92ZUlkczogW10sCiAgICAgICAgICAgIGF0dGFjaERhdGE6IFtdLAogICAgICAgICAgICBmaWxlUGFyYW06IHsKICAgICAgICAgICAgICAgIGJ1c2lJZDogIiIsCiAgICAgICAgICAgICAgICBidXNpQWxpYXM6ICLpmYTku7Yo5Y2P6K6u566h55CGKSIsCiAgICAgICAgICAgICAgICBjYXRlZ29yeUNvZGU6ICJmaWxlIiwKICAgICAgICAgICAgICAgIGFyZWFDb2RlOiAibG4iCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHJ1bGVWYWxpZGF0ZTogewogICAgICAgICAgICAgICAgYXBwbHlSZWFzb246IFsKICAgICAgICAgICAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogdmFsaXNoZXJlZGVwYXJ0bmFtZSwgdHJpZ2dlcjogJ2NoYW5nZSwgYmx1cid9CiAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgd2hpdGVsaXN0VHlwZTogWwogICAgICAgICAgICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB2YWxpc2hlcmVkZXBhcnRuYW1lLCB0cmlnZ2VyOiAnY2hhbmdlLCBibHVyJ30KICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgfSwKICAgICAgICAgICAgYWRkV2hpdGVMaXN0OiB7CiAgICAgICAgICAgICAgICB3aGl0ZWxpc3RUeXBlOiAiIiwKICAgICAgICAgICAgICAgIGFwcGx5UmVhc29uOiAiIiwKICAgICAgICAgICAgICAgIGlkOiAiIgogICAgICAgICAgICB9LAogICAgICAgICAgICBhdHRhY2g6IHsKICAgICAgICAgICAgICAgIGZpbGVGb3JtOiB7CiAgICAgICAgICAgICAgICAgICAgZmlsZTogbnVsbAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIGZvcm1MYXlvdXQ6IFsKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiAn5LiK5Lyg6ZmE5Lu2JywKICAgICAgICAgICAgICAgICAgICAgICAgcHJvcDogJ2ZpbGUnLAogICAgICAgICAgICAgICAgICAgICAgICBmb3JtSXRlbVR5cGU6ICdmaWxlJywKICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDMwMCwKICAgICAgICAgICAgICAgICAgICAgICAgZm9ybWF0OiB0aGlzLmZvcm1hdAogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAgICAgICAgIGNvbHVtbnM6IFtdLAogICAgICAgICAgICAgICAgZGF0YTpbXSwKICAgICAgICAgICAgfSwKICAgICAgICAgICAgd2hpdGVMaXN0OiBmYWxzZSwKICAgICAgICAgICAgbG9hZGluZzpmYWxzZSwKICAgICAgICAgICAgc2hvd1dvcmtGbG93OiBmYWxzZSwKICAgICAgICAgICAgY29uZmlnVmVyc2lvbjpudWxsLC8v54mI5pysCiAgICAgICAgICAgIGlzRGlzYWJsZTpmYWxzZSwKICAgICAgICAgICAgZmlsdGVyQ29sbDogdHJ1ZSwvL+aQnOe0oumdouadv+WxleW8gAogICAgICAgICAgICBpc0FkbWluOmZhbHNlLAogICAgICAgICAgICBjb21wYW55Om51bGwsLy/nlKjmiLfpu5jorqTlhazlj7gKICAgICAgICAgICAgY291bnRyeTpudWxsLC8v55So5oi36buY6K6k5omA5bGe6YOo6ZeoCiAgICAgICAgICAgIGNvdW50cnlOYW1lOm51bGwsLy/nlKjmiLfpu5jorqTmiYDlsZ7pg6jpl6gKCiAgICAgICAgICAgIGNsYXNzaWZpY2F0aW9uRGF0YTpbXSwvL+eUqOeUteexu+WeiwogICAgICAgICAgICBjbGFzc2lmaWNhdGlvbnM6W10sLy/nlKjnlLXnsbvlnosKCiAgICAgICAgICAgIGRlbW9MaXN0OiBbXSwKICAgICAgICAgICAgaXNlbnRpdHlhbW1ldGVyOnt9LAogICAgICAgICAgICBpc2VudGl0eWFtbWV0ZXJzOltdLAogICAgICAgICAgICBzdGF0dXM6W10sLy/nirbmgIEKICAgICAgICAgICAgYmlsbFN0YXR1czpbXSwvL+WNleaNrueKtuaAgQogICAgICAgICAgICBlbGVjdHJvVHlwZTpbXSwvL+eUqOeUteexu+WeiwogICAgICAgICAgICBhbW1ldGVyVHlwZTpbXSwvL+eUteihqOexu+WeiwogICAgICAgICAgICBlbGVjdHJvTmF0dXJlOltdLC8v55So55S15oCn6LSoCiAgICAgICAgICAgIHBheVR5cGU6W10sLy/ku5jotLnmlrnlvI8KICAgICAgICAgICAgZWxlY3Ryb3ZhbGVuY2VOYXR1cmU6W10sLy/nlLXku7fmgKfotKgKICAgICAgICAgICAgZGlyZWN0c3VwcGx5RmxhZzpbXSwvL+WvueWklue7k+eul+exu+WeiwogICAgICAgICAgICBwcm9wZXJ0eTpbXSwvL+S6p+adg+W9kuWxngogICAgICAgICAgICBxdWVyeVBhcmFtc0xpc3Q6IHsKICAgICAgICAgICAgcHJvamVjdG5hbWU6ICcnLC8v6aG555uu5ZCN56ewCiAgICAgICAgICAgIG1ldGVyQ29kZTogJycsLy/nlLXooajnvJblj7cKICAgICAgICAgICAgc3RhdGlvbk5hbWU6ICcnLC8v5bGA56uZ5ZCN56ewCiAgICAgICAgICAgIGNpdHk6ICcnLC8v5Zyw5biCCiAgICAgICAgICAgIGRpc3RyaWN0OiAnJywvL+WMuuWOvwogICAgICAgICAgICBzdGF0dXM6ICcxJywvL+eKtuaAgQogICAgICAgICAgICBjb21wYW55OiAnJywKICAgICAgICAgICAgY291bnRyeU5hbWU6ICcnLAogICAgICAgICAgICBiaTExc3RhdHVzTmFtZTogJycsCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHF1ZXJ5UGFyYW1zOntjb3VudHJ5Om51bGwsY29tcGFueTpudWxsLGNvdW50cnlOYW1lOm51bGwscmVzc3RhdGlvbmNvZGU6bnVsbCwgc3RhdGlvbmNvZGU1Z3I6IG51bGwsIHN0YXRpb25uYW1lNWdyOiBudWxsfSwKICAgICAgICAgICAgY29tcGFuaWVzOltdLAogICAgICAgICAgICBkZXBhcnRtZW50czpbXSwKICAgICAgICAgICAgbXVsdGlwbGVTZWxlY3Rpb25Sb3c6IFtdLAoKICAgICAgICAgICAgd29ya0Zsb3dQYXJhbXM6IHt9LAogICAgICAgICAgICBoaXNQYXJhbXM6IHt9LAogICAgICAgICAgICBleHBvcnRDb2x1bW5zOlsKICAgICAgICAgICAgICAgIHt0aXRsZTogJ+eUteihqOe8luWPtycsa2V5OiAnYW1tZXRlcm5hbWUnfSwKICAgICAgICAgICAgICAgIHt0aXRsZTogJ+mhueebruWQjeensCcsa2V5OiAncHJvamVjdG5hbWUnfSwKICAgICAgICAgICAgICAgIHt0aXRsZTogJ+WFs+iBlOWxgOermeWQjScsa2V5OiAnc3RhdGlvbk5hbWUnfSwKICAgICAgICAgICAgICAgIHt0aXRsZTogJ+aJgOWxnuWIhuWFrOWPuCcsa2V5OiAnY29tcGFueU5hbWUnfSwKICAgICAgICAgICAgICAgIHt0aXRsZTogJ+aJgOWxnumDqOmXqCcsIGtleTogJ2NvdW50cnlOYW1lJ30sCiAgICAgICAgICAgICAgICB7dGl0bGU6ICfnirbmgIEnLGtleTogJ3N0YXR1c1N0cid9LAogICAgICAgICAgICAgICAge3RpdGxlOiAn5Y2V5o2u54q25oCBJyxrZXk6ICdiaWxsU3RhdHVzU3RyJ30sCiAgICAgICAgICAgICAgICB7dGl0bGU6ICfnlKjnlLXnsbvlnosnLGtleTogJ2VsZWN0cm90eXBlbmFtZSd9LAogICAgICAgICAgICAgICAge3RpdGxlOiAn5a+55aSW57uT566X57G75Z6LJyxrZXk6ICdkaXJlY3RzdXBwbHlmbGFnU3RyJ30sCiAgICAgICAgICAgICAgICB7dGl0bGU6ICfku5jotLnmlrnlvI8nLGtleTogJ3BheXR5cGVTdHInfSwKICAgICAgICAgICAgICAgIHt0aXRsZTogJ+eUteS7t+aAp+i0qCcsa2V5OiAnZWxlY3Ryb3ZhbGVuY2VuYXR1cmVTdHInfSwKICAgICAgICAgICAgICAgIHt0aXRsZTogJ+eUteihqOexu+Weiycsa2V5OiAnYW1tZXRlcnR5cGVTdHInfSwKICAgICAgICAgICAgICAgIHt0aXRsZTogJ+S6p+adg+W9kuWxnicsa2V5OiAncHJvcGVydHlTdHInfSwKICAgICAgICAgICAgICAgIC8vIHt0aXRsZTogJ+aUr+WxgC/liIblsYAnLGtleTogJ3N1YnN0YXRpb24nfSwKICAgICAgICAgICAgICAgIHt0aXRsZTogJ+euoeeQhui0n+i0o+S6uicsa2V5OiAnYW1tZXRlcm1hbmFnZXInfSwKICAgICAgICAgICAgICAgIHt0aXRsZTogJ+WIm+W7uuaXtumXtCcsa2V5OiAnY3JlYXRlVGltZSd9LAogICAgICAgICAgICAgICAge3RpdGxlOiAn5L6b55S15bGA55S16KGo5oi35Y+35oiW57yW5Y+3JyxrZXk6ICdzdXBwbHlidXJlYXVhbW1ldGVyY29kZSd9LAogICAgICAgICAgICAgICAge3RpdGxlOiAn6LWE5rqQ5bGA56uZaWTvvJonLGtleTogJ3Rlcm1uYW1lJ30sCiAgICAgICAgICAgICAgICB7dGl0bGU6ICflgI3njofvvJonLGtleTogJ21hZ25pZmljYXRpb24nfSwKICAgICAgICAgICAgICAgIHt0aXRsZTogJ+WNleS7t++8micsa2V5OiAncHJpY2UnfSwKICAgICAgICAgICAgICAgIHt0aXRsZTogJ+WIhuWJsuavlOS+i++8micsa2V5OiAncGVyY2VudCd9LAogICAgICAgICAgICBdLAogICAgICAgICAgICBleHBvcnQ6IHsKICAgICAgICAgICAgICAgIHJ1bjogZmFsc2UsLy/mmK/lkKbmraPlnKjmiafooYzlr7zlh7oKICAgICAgICAgICAgICAgIGRhdGE6ICIiLC8v5a+85Ye65pWw5o2uCiAgICAgICAgICAgICAgICB0b3RhbFBhZ2U6IDAsLy/kuIDlhbHlpJrlsJHpobUKICAgICAgICAgICAgICAgIGN1cnJlbnRQYWdlOiAwLC8v5b2T5YmN5aSa5bCR6aG1CiAgICAgICAgICAgICAgICBwZXJjZW50OiAwLAogICAgICAgICAgICAgICAgc2l6ZTogMjAwMDAwCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGFtbWV0ZXI6IHsKICAgICAgICAgICAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAgICAgICAgICAgY29sdW1uczogWwogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfnlLXooajnvJblj7cnLAogICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICdhbW1ldGVybmFtZScsCiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lOiAidGQtaWQiLAogICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI6IHJlbnRlclZpZXdBbW1ldGVyLAogICAgICAgICAgICAgICAgICAgICAgICBtaW5XaWR0aDogMTIwLAogICAgICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDoyMDAKICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfpobnnm67lkI3np7AnLAogICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICdwcm9qZWN0bmFtZScsCiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lOiAidGQtaWQiLAogICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI6IHJlbnRlclZpZXdBbW1ldGVyLAogICAgICAgICAgICAgICAgICAgICAgICBtaW5XaWR0aDogMTIwLAogICAgICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDoyMDAKICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICflhbPogZTlsYDnq5nlkI0nLAogICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICdzdGF0aW9uTmFtZScsCiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZTogInRkLWlkIiwKICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI6IHJlbnRlclZpZXdTdGF0aW9uLAogICAgICAgICAgICAgICAgICAgICAgICBtaW5XaWR0aDogMTAwLAogICAgICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDoyMDAKICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn6LWE5rqQ5bGA56uZaWQnLAogICAgICAgICAgICAgICAgICAgICAga2V5OiAndGVybW5hbWUnLAogICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lOiAidGQtaWQiLAogICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgICAgICAgICAgICAgcmVuZGVyOiB0ZXJtbmFtZUZvcm1hdCwKICAgICAgICAgICAgICAgICAgICAgIG1pbldpZHRoOiAxMDAsCiAgICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDoyMDAKICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfmiYDlsZ7liIblhazlj7gnLAogICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICdjb21wYW55TmFtZScsCiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgICAgICAgICAgICAgICAgbWluV2lkdGg6IDEwMCwKICAgICAgICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6MjAwCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn5omA5bGe6YOo6ZeoJywKICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAnY291bnRyeU5hbWUnLAogICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICAgICAgICAgICAgICAgIG1pbldpZHRoOiA4MCwKICAgICAgICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6MjAwCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn54q25oCBJywKICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAnc3RhdHVzJywKICAgICAgICAgICAgICAgICAgICAgICAgcmVuZGVyOiByZW5kZXJTdGF0dXMsCiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgICAgICAgICAgICAgICAgbWluV2lkdGg6IDU1LAogICAgICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDoyMDAKICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfljZXmja7nirbmgIEnLAogICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICAgICAgICAgICAgICAgIGtleTogJ2JpbGxTdGF0dXMnLAogICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI6IHJlbmRlckJpbGxTdGF0dXMsCiAgICAgICAgICAgICAgICAgICAgICAgIG1pbldpZHRoOiA4MCwKICAgICAgICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6MjAwCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn55So55S157G75Z6LJywKICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAnZWxlY3Ryb3R5cGVuYW1lJywKICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgICAgICAgICAgICAgICBtaW5XaWR0aDogOTAsCiAgICAgICAgICAgICAgICAgICAgICAgIG1heFdpZHRoOjIwMAogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogJ+WvueWklue7k+eul+exu+WeiycsCiAgICAgICAgICAgICAgICAgICAgICAgIGtleTogJ2RpcmVjdHN1cHBseWZsYWcnLAogICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI6IHJlbmRlckRpcmVjdHN1cHBseWZsYWcsCiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgICAgICAgICAgICAgICAgbWluV2lkdGg6IDgwLAogICAgICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDoyMDAKICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfku5jotLnmlrnlvI8nLAogICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICdwYXl0eXBlJywKICAgICAgICAgICAgICAgICAgICAgICAgcmVuZGVyOiByZW5kZXJQYXlUeXBlLAogICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICAgICAgICAgICAgICAgIG1pbldpZHRoOiA3MCwKICAgICAgICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6MjAwCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn55S15Lu35oCn6LSoJywKICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAnZWxlY3Ryb3ZhbGVuY2VuYXR1cmUnLAogICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI6IHJlbmRlckVsZWN0cm92YWxlbmNlTmF0dXJlLAogICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICAgICAgICAgICAgICAgIG1pbldpZHRoOiA3MCwKICAgICAgICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6MjAwCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn55S16KGo57G75Z6LJywKICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAnYW1tZXRlcnR5cGUnLAogICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI6IHJlbmRlckFtbWV0ZXJUeXBlLAogICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICAgICAgICAgICAgICAgIG1pbldpZHRoOiA3MCwKICAgICAgICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6MjAwCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn5Lqn5p2D5b2S5bGeJywKICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAncHJvcGVydHknLAogICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI6IHJlbmRlclByb3BlcnR5LAogICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICAgICAgICAgICAgICAgIG1pbldpZHRoOiA3MCwKICAgICAgICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6MjAwCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAvLyB7CiAgICAgICAgICAgICAgICAgICAgLy8gICAgIHRpdGxlOiAn5pSv5bGAL+WIhuWxgCcsCiAgICAgICAgICAgICAgICAgICAgLy8gICAgIGtleTogJ3N1YnN0YXRpb24nLAogICAgICAgICAgICAgICAgICAgIC8vICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICAgICAgICAgICAgLy8gICAgIG1pbldpZHRoOiA4MCwKICAgICAgICAgICAgICAgICAgICAvLyAgICAgbWF4V2lkdGg6MjAwCiAgICAgICAgICAgICAgICAgICAgLy8gfSwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn566h55CG6LSf6LSj5Lq6JywKICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAnYW1tZXRlcm1hbmFnZXInLAogICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICAgICAgICAgICAgICAgIG1pbldpZHRoOiA5MCwKICAgICAgICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6MjAwCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn5Yib5bu65pe26Ze0JywKICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAnY3JlYXRlVGltZScsCiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgICAgICAgICAgICAgICAgbWluV2lkdGg6IDExMCwKICAgICAgICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6MjAwCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn5LiK5Lyg6ZmE5Lu2JywKICAgICAgICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicsCiAgICAgICAgICAgICAgICAgICAgbWluV2lkdGg6IDcwLAogICAgICAgICAgICAgICAgICAgIG1heFdpZHRoOiAyMDAsCiAgICAgICAgICAgICAgICAgICAgcmVuZGVyOiAoaCwgcGFyYW1zKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICBjb25zdCByb3cgPSBwYXJhbXMucm93OwogICAgICAgICAgICAgICAgICAgICAgY29uc3QgdGV4dCA9IHJvdy5pc0F0dGFjaCA9PT0gMCA/ICfmnKrkuIrkvKAnIDogJ+W3suS4iuS8oCc7CiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gaCgnc3BhbicsIHt9LCB0ZXh0KTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIua1geeoiyIsCiAgICAgICAgICAgICAgICAgICAgICAgIGZpeGVkOiAncmlnaHQnLAogICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJhY3Rpb24iLAogICAgICAgICAgICAgICAgICAgICAgICBtaW5XaWR0aDogNjAsCiAgICAgICAgICAgICAgICAgICAgICAgIG1heFdpZHRoOjIwMCwKICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI6IHJlbmRlclcKICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLmn6XnnIvlrprpop0iLAogICAgICAgICAgICAgICAgICAgICAgICBmaXhlZDogJ3JpZ2h0JywKICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAiYWN0aW9uIiwKICAgICAgICAgICAgICAgICAgICAgICAgbWluV2lkdGg6IDY1LAogICAgICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDoyMDAsCiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywKICAgICAgICAgICAgICAgICAgICAgICAgcmVuZGVyOiByZW5kZXJRdW90YQogICAgICAgICAgICAgICAgICAgIH1dLAogICAgICAgICAgICAgICAgZGF0YTogW10sCiAgICAgICAgICAgICAgICBwYWdlU2l6ZToxMAogICAgICAgICAgICB9CiAgICAgICAgfQogICAgfSwKICAgIG1ldGhvZHM6IHsKICAgICAgICAuLi5tYXBNdXRhdGlvbnMoWyJjbG9zZVRhZyIsICJjbG9zZVRhZ0J5TmFtZSJdKSwKICAgICAgICAvL+mqjOivgemUmeivr+W8ueWHuuaPkOekuuahhgogICAgICAgIGVycm9yVGlwcyhzdHIpewogICAgICAgICAgICB0aGlzLiROb3RpY2UuZXJyb3IoewogICAgICAgICAgICAgICAgdGl0bGU6ICfmj5DnpLonLAogICAgICAgICAgICAgICAgZGVzYzogc3RyLAogICAgICAgICAgICAgICAgZHVyYXRpb246IDEwCiAgICAgICAgICAgIH0pOwogICAgICAgIH0sCiAgICAgICAgLy8gdHlwZUxpc3QKICAgICAgICBzdWJtaXRXaGl0ZUxpc3QoKSB7CiAgICAgICAgICAgIGxldCBhcnIgPSBbXTsKICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLiRyZWZzLmFtbWV0ZXJUYWJsZS5nZXRTZWxlY3Rpb24oKTsKICAgICAgICAgICAgY29uc29sZS5sb2coZGF0YVswXS5hbW1ldGVybmFtZSwgImRhdGEuYW1tZXRlcm5hbWUiKTsKICAgICAgICAgICAgY29uc29sZS5sb2codGhpcy5hdHRhY2guZmlsZUZvcm0uZmlsZSwgInRoaXMuYXR0YWNoLmZpbGVGb3JtLmZpbGUiKTsKCiAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgIHRoaXMuYWRkV2hpdGVMaXN0LndoaXRlbGlzdFR5cGUuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgICAgIGFyci5wdXNoKHsKICAgICAgICAgICAgICAgICAgICB3aGl0ZWxpc3RUeXBlOiBpdGVtLAogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIGxldCBwYXJhbXMgPSB7CiAgICAgICAgICAgICAgICB0eXBlTGlzdDogYXJyLAogICAgICAgICAgICAgICAgYXBwbHlBcmd1bWVudDogdGhpcy5hZGRXaGl0ZUxpc3QuYXBwbHlSZWFzb24sCiAgICAgICAgICAgICAgICBtZXRlckNvZGU6IGRhdGFbMF0uYW1tZXRlcm5hbWUsCiAgICAgICAgICAgICAgICAiZmoiOiAi5pyq5LiK5Lyg6ZmE5Lu277ybIiwKICAgICAgICAgICAgICAgIC8vIHR5cGVMaXN0LndoaXRlbGlzdFR5cGU6ICcxJywKICAgICAgICAgICAgICAgIGR3anNseDogZGF0YVswXS5kaXJlY3RzdXBwbHlmbGFnID09IDE/J+ebtOS+myc6ZGF0YVswXS5kaXJlY3RzdXBwbHlmbGFnID09IDI/J+i9rOS+myc6IiIsCgogICAgICAgICAgICAgICAgfQogICAgICAgICAgICBjb25zb2xlLmxvZyhwYXJhbXMsICJwYXJhbXMiKTsKICAgICAgICAgICAgd2hpdGVJbnNlcnQoCiAgICAgICAgICAgICAgICBwYXJhbXMKICAgICAgICAgICAgKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMsICJyZXM2NjY2NjY2NjY2NjYiKTsKICAgICAgICAgICAgICAgIHRoaXMud2hpdGVMaXN0ID0gZmFsc2U7CiAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5jb2RlID09IDUwMCkgewogICAgICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgICAvLyB0aGlzLiRNZXNzYWdlLmVycm9yKHJlcy5kYXRhLm1zZyk7CiAgICAgICAgICAgICAgICB9ZWxzZXsKICAgICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2Uuc3VjY2Vzcygi5L+d5a2Y5oiQ5YqfIik7CiAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICAgIHRoaXMuaWQxID0gcmVzLmRhdGEuaWQ7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pCiAgICAgICAgfSwKICAgICAgcmVtb3ZlQXR0YWNoKCl7CiAgICAgICAgcmVtb3ZlQXR0YWNoKHtpZHM6dGhpcy5yZW1vdmVJZHMuam9pbigpfSkudGhlbigoKSA9PiB7CgogICAgICAgIH0pOwogICAgICB9LAogICAgICAgIHNldEF0dGFjaERhdGEoZGF0YSl7CiAgICAgICAgY29uc29sZS5sb2coZGF0YSwgImRhdGE1NTU1NTU1NTU1NTU1NTUiKTsKICAgICAgICB0aGlzLm11bHRpRmlsZXMgPSBkYXRhLmRhdGE7CiAgICAgICAgdGhpcy5yZW1vdmVJZHMgPSBkYXRhLmlkczsKICAgICAgICBpZih0aGlzLnJlbW92ZUlkcy5sZW5ndGghPSAwICYmIGRhdGEudHlwZSA9PSAncmVtb3ZlJyl7CiAgICAgICAgICB0aGlzLnJlbW92ZUF0dGFjaCgpOwogICAgICAgIH1lbHNlewogICAgICAgICAgdGhpcy51cGxvYWQoKTsKICAgICAgICB9CiAgICAgIH0sCiAgICAgICAgdXBsb2FkKCl7CiAgICAgICAgaWYgKHRoaXMuYXR0YWNoRGF0YS5sZW5ndGggIT0gMCAmJiB0aGlzLm11bHRpRmlsZXMubGVuZ3RoICE9IDApewoKICAgICAgICAgIC8vIHRoaXMuJE1lc3NhZ2UuaW5mbygi5o+Q56S6OuS4iuS8oOaWh+S7tui/h+Wkp+WPr+iDveWvvOiHtOS4iuS8oOWksei0pe+8gSIpOwogICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgIGF4aW9zLnJlcXVlc3QoewogICAgICAgICAgICB1cmw6ICcvY29tbW9uL2F0dGFjaG1lbnRzL3VwbG9hZE11bHRpRmlsZScsCiAgICAgICAgICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgICAgICAgICBkYXRhOiB0aGlzLm11bHRpRmlsZXMKICAgICAgICAgIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICBpZihyZXMuZGF0YS5jb2RlICE9IDApewogICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpczsKICAgICAgICAgICAgLy8gaWYodGhhdC5maWxlUGFyYW0uYnVzaUlkID09ICIiKSB7CiAgICAgICAgICAgIC8vICAgICB0aGlzLiRNZXNzYWdlLmVycm9yKCJjdW93dSIpOwogICAgICAgICAgICAvLyB9ZWxzZSB7CiAgICAgICAgICAgICAgICBhdHRjaExpc3Qoe2J1c2lJZDp0aGF0LmZpbGVQYXJhbS5idXNpSWR9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMsICJhdHRjaExpc3QiKTsKICAgICAgICAgICAgICAgIHRoYXQuYXR0YWNoRGF0YSA9IE9iamVjdC5hc3NpZ24oW10sIHJlcy5kYXRhLnJvd3MpOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgLy8gfQoKICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9LAogICAgICBnZXRBY2NvdW50TWVzc2FnZXMoKSB7CiAgICAgICAgLy8gdGhpcy5saXN0VGIubG9hZGluZyA9IHRydWU7CiAgICAgICAgd2hpdGVMaXN0KHsKICAgICAgICAgICAgLy8gbWV0ZXJDb2RlOiB0aGlzLnF1ZXJ5UGFyYW1zTGlzdC5tZXRlckNvZGUsCiAgICAgICAgICAgIC8vIHByb2plY3RuYW1lOiB0aGlzLnF1ZXJ5UGFyYW1zTGlzdC5wcm9qZWN0bmFtZSwKICAgICAgICAgICAgLy8gc3RhdGlvbk5hbWU6IHRoaXMucXVlcnlQYXJhbXNMaXN0LnN0YXRpb25OYW1lLAogICAgICAgICAgICAvLyBjb21wYW55OiB0aGlzLnF1ZXJ5UGFyYW1zTGlzdC5jb21wYW55LAogICAgICAgICAgICAvLyBjb3VudHJ5TmFtZTogdGhpcy5xdWVyeVBhcmFtc0xpc3QuY291bnRyeU5hbWUsCiAgICAgICAgICAgIC8vIHN0YXR1czogdGhpcy5xdWVyeVBhcmFtc0xpc3Quc3RhdHVzLAogICAgICAgICAgICAvLyBzaXplOiB0aGlzLnBhZ2VTaXplLAogICAgICAgICAgICAvLyBjdXJyZW50OiB0aGlzLnBhZ2VOdW0KICAgICAgICAgICAgfSkudGhlbihyZXMgPT4gewogICAgICAgIC8vIHRoaXMubGlzdFRiLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgY29uc29sZS5sb2cocmVzLCAicXVlcnlQYXJhbXNMaXN0IHJlcyIpOwogICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMuZGF0YS5sZW5ndGgsICJyZXMuZGF0YS5sZW5ndGgiKTsKICAgICAgICAgICAgLy8gdGhpcy5wYWdlVG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgICAgICAgLy8gdGhpcy5pbnNpZGVEYXRhID0gcmVzLmRhdGEucm93czsKICAgICAgICB9KQogICAgICAgIH0sCiAgICAgICAgYXBwbHlXKCkgewogICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMuJHJlZnMuYW1tZXRlclRhYmxlLmdldFNlbGVjdGlvbigpOwogICAgICAgICAgICBjb25zb2xlLmxvZyhkYXRhLCAiZGF0YTU1NTU1NTU1NTU1NTU1Iik7CiAgICAgICAgICAgIHRoaXMuZmlsZVBhcmFtLmJ1c2lJZCA9IGRhdGFbMF0uaWQ7CiAgICAgICAgICAgIGlmKGRhdGEubGVuZ3RoID4gMSkgewogICAgICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoIuWPquiDvemAieaLqeS4gOS4queUteihqOeUs+ivt+WKoOWFpeeZveWQjeWNlSIpOwogICAgICAgICAgICB9ZWxzZSBpZihkYXRhLmxlbmd0aCA9PSAwKSB7CiAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6K+36YCJ5oup5LiA5Liq55S16KGo55Sz6K+35Yqg5YWl55m95ZCN5Y2VIik7CiAgICAgICAgICAgIH1lbHNlIHsKICAgICAgICAgICAgICAgIC8vIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7CiAgICAgICAgICAgICAgICAvLyBkYXRhWzBdLm5hbWUKICAgICAgICAgICAgICAgIHRoaXMuYXR0YWNoRGF0YSA9IFtdOwogICAgICAgICAgICAgICAgdGhpcy5hZGRXaGl0ZUxpc3QgPSB7CiAgICAgICAgICAgICAgICAgICAgd2hpdGVsaXN0VHlwZTogIiIsCiAgICAgICAgICAgICAgICAgICAgYXBwbHlSZWFzb246ICIiLAogICAgICAgICAgICAgICAgICAgIGlkOiAiIgogICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgdGhpcy53aGl0ZUxpc3QgPSB0cnVlOwoKICAgICAgICAgICAgfQogICAgICAgIH0sCiAgICAgICAgb25Nb2RhbE9LKCkgewogICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmVycm9yKCfnoa7lrponKQogICAgICAgIH0sCiAgICAgICAgb25Nb2RhbENhbmNlbCgpIHsKICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5lcnJvcign5Y+W5raIJykKICAgICAgICB9LAoKICAgICAgICAvKuWIoOmZpCovCiAgICAgICAgcmVtb3ZlQW1tZXRlcihpZCkgewogICAgICAgICAgICBsZXQgbXVsdGlwbGVTZWxlY3Rpb24gPSBbXTsKICAgICAgICAgICAgaWYgKHRoaXMubXVsdGlwbGVTZWxlY3Rpb25Sb3cubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgZm9yKGxldCBpdGVtIG9mIHRoaXMubXVsdGlwbGVTZWxlY3Rpb25Sb3cpewogICAgICAgICAgICAgICAgICAgIGlmKGl0ZW0uYmlsbFN0YXR1cyAhPSAwKXsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLmiYDpgInmlbDmja7ljIXlkKvpnZ7ojYnnqL/mlbDmja7vvIzkuI3og73liKDpmaTvvIEiKTsKICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIDsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgbXVsdGlwbGVTZWxlY3Rpb24ucHVzaChpdGVtLmlkKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIGlkID0gbXVsdGlwbGVTZWxlY3Rpb24uam9pbignLCcpOwogICAgICAgICAgICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7CiAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfmuKnppqjmj5DnpLonLAogICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICc8cD7noa7orqTliKDpmaTlkJc/PC9wPicsCiAgICAgICAgICAgICAgICAgICAgb25PazogKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmFtbWV0ZXIubG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgICAgICAgICAgICAgIHJlbW92ZUFtbWV0ZXIoe2lkczogaWR9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLnN1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5fb25TZWFyY2hIYW5kbGUoKTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuYW1tZXRlci5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uUm93ID0gW107CiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLor7foh7PlsJHpgInmi6nkuIDooYwiKTsKICAgICAgICAgICAgfQoKICAgICAgICB9LAoKICAgICAgICAvKue8lui+kSovCiAgICAgICAgZWRpdEFtbWV0ZXIoKSB7CiAgICAgICAgICAgIGlmICh0aGlzLm11bHRpcGxlU2VsZWN0aW9uUm93Lmxlbmd0aCA9PSAxKSB7CiAgICAgICAgICAgICAgICBsZXQgcm93ID0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvblJvd1swXTsKICAgICAgICAgICAgICAgIHNlbGVjdENoYW5nZUFtbWV0ZXIoe2lkOnJvdy5pZH0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgICAgICAvL+WtmOWcqOS6juS7o+WKnuS4reaXtu+8jOaKpeWHuuaPkOekugogICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJE1vZGFsLndhcm5pbmcoe3RpdGxlOiAi5rip6aao5o+Q56S6IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICLor6XnlLXooajlt7Lnu4/lrZjlnKjmjaLooajnlLXooajjgJDnlLXooajnvJblj7fvvJoiICsgcmVzLmRhdGFbMF0uYW1tZXRlcm5hbWUgKyAi77yM6aG555uu5ZCN56ew77yaIiArIHJlcy5kYXRhWzBdLnByb2plY3RuYW1lICsgIuOAkSzkuI3lhYHorrjlho3kv67mlLkiCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGlzSW5Ub2RvTGlzdChyb3cuaWQsIDEpLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8v5a2Y5Zyo5LqO5Luj5Yqe5Lit5pe277yM5oql5Ye65o+Q56S6CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgb3duZXJuYW1lID0gIiI7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcmVzLmRhdGEubGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3duZXJuYW1lICs9IHJlcy5kYXRhW2ldLm93bmVybmFtZSArICcgJzsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7dGl0bGU6ICLmuKnppqjmj5DnpLoiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiAi6K+l5pWw5o2u5a2Y5Zyo5LqOIiArIG93bmVybmFtZSArICLnmoTmtYHnqIvku6Plip7kuK3vvIzlpITnkIblkI7miY3lj6/kv67mlLnmlbDmja4iCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrQWNvdW50QnlVcGRhdGUoe2lkOiByb3cuaWR9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8v5L+u5pS55pWw5o2u5YmN6aqM6K+B5Y+w6LSmCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YSA9PSAtMSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7dGl0bGU6ICLmuKnppqjmj5DnpLoiLCBjb250ZW50OiAi6K+l5pWw5o2u5bey5aGr5YaZ5Y+w6LSm5oiW5q2j5Zyo5oql6LSm5Lit77yM5aSE55CG5ZCO5omN5Y+v5L+u5pS55pWw5o2uIn0pOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jbG9zZVRhZ0J5TmFtZSh7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcm91dGU6IGdldEhvbWVSb3V0ZShyb3V0ZXJzLCAiZWRpdEFtbWV0ZXIiKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICJlZGl0QW1tZXRlciIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcXVlcnk6IHtpZDogcm93LmlkfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXBsYWNlOiB0cnVlCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgfSkuY2F0Y2goZXJyID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGVycik7CiAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLor7fpgInmi6nlhbbkuK3kuIDooYwiKTsKICAgICAgICAgICAgfQoKICAgICAgICB9LAogICAgICAgIC8q5o2i6KGoKi8KICAgICAgICBjaGFuZ2VBbW1ldGVyKCkgewogICAgICAgICAgICBpZiAodGhpcy5tdWx0aXBsZVNlbGVjdGlvblJvdy5sZW5ndGggPT0gMSkgewogICAgICAgICAgICAgICAgbGV0IHJvdyA9IHRoaXMubXVsdGlwbGVTZWxlY3Rpb25Sb3dbMF07CiAgICAgICAgICAgICAgICAvLyBpZihyb3cucHJvcGVydHkhPTIpIHsKICAgICAgICAgICAgICAgICAgICBzZWxlY3RDaGFuZ2VBbW1ldGVyKHtpZDogcm93LmlkfSkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAvL+WtmOWcqOS6juS7o+WKnuS4reaXtu+8jOaKpeWHuuaPkOekugogICAgICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICLmuKnppqjmj5DnpLoiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICLor6XnlLXooajlt7Lnu4/lrZjlnKjmjaLooajnlLXooajjgJDnlLXooajnvJblj7fvvJoiICsgcmVzLmRhdGFbMF0uYW1tZXRlcm5hbWUgKyAi77yM6aG555uu5ZCN56ew77yaIiArIHJlcy5kYXRhWzBdLnByb2plY3RuYW1lICsgIuOAkSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNJblRvZG9MaXN0KHJvdy5pZCwgMSkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8v5a2Y5Zyo5LqO5Luj5Yqe5Lit5pe277yM5oql5Ye65o+Q56S6CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGV0IG93bmVybmFtZSA9ICIiOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcmVzLmRhdGEubGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG93bmVybmFtZSArPSByZXMuZGF0YVtpXS5vd25lcm5hbWUgKyAnICc7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiAi6K+l5pWw5o2u5a2Y5Zyo5LqOIiArIG93bmVybmFtZSArICLnmoTmtYHnqIvku6Plip7kuK3vvIzlpITnkIblkI7miY3lj6/kv67mlLnmlbDmja4iCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY2hlY2tDaGFuZ2VBbW1ldGVyKHJvdyk7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIC8vIH1lbHNlewogICAgICAgICAgICAgICAgLy8gICAgIHRoaXMuJE1lc3NhZ2UuaW5mbygi6K+l6KGo56uZ5Z2A5Lqn5p2D5b2S5bGe5Li66ZOB5aGU77yM5LiN6IO96L+b6KGM5o2i6KGo5pON5L2cIik7CiAgICAgICAgICAgICAgICAvLyB9CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIuivt+mAieaLqeWFtuS4reS4gOihjCIpOwogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBjaGVja0NoYW5nZUFtbWV0ZXIocm93KXsKICAgICAgICAgICAgaWYocm93LmJpbGxTdGF0dXMgIT0wKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRNb2RhbC5jb25maXJtKHsKICAgICAgICAgICAgICAgICAgICB0aXRsZTogJ+a4qemmqOaPkOekuicsCiAgICAgICAgICAgICAgICAgICAgY29udGVudDogJzxwPuaNouihqOa1geeoi+e7k+adn++8jOaXp+ihqOWwhuWBnOeUqO+8jOaWsOihqOWQr+eUqO+8jOivt+ehruiupOaYr+WQpuaNouihqO+8nzwvcD4nLAogICAgICAgICAgICAgICAgICAgIG9uT2s6ICgpID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5jbG9zZVRhZ0J5TmFtZSh7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3V0ZTogZ2V0SG9tZVJvdXRlKHJvdXRlcnMsICJjaGFuZ2VBbW1ldGVyIiksCiAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiAiY2hhbmdlQW1tZXRlciIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBxdWVyeToge2lkOiByb3cuaWR9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVwbGFjZTogdHJ1ZQogICAgICAgICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgIH1lbHNlewogICAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLojYnnqL/nirbmgIHkuI3og73mk43kvZzmjaLooajmlbDmja4iKTsKICAgICAgICAgICAgfQogICAgICAgIH0sCgogICAgICAgIC8q5p+l55yLKi8KICAgICAgICB2aWV3QW1tZXRlcihpZCkgewogICAgICAgICAgICB0aGlzLiRyZWZzLnZpZXdBbW1ldGVyUGFnZS5pbml0QW1tZXRlcihpZCk7CiAgICAgICAgfSwKICAgICAgICAvKuafpeeci+WxgOermSovCiAgICAgICAgdmlld1N0YXRpb24oaWQpIHsKICAgICAgICAgICAgdGhpcy4kcmVmcy52aWV3U3RhdGlvblBhZ2UuaW5pdFN0YXRpb24oaWQpOwogICAgICAgIH0sCiAgICAgICAgLyrmn6XnnIvlrprpop0qLwogICAgICAgIHZpZXdRdW90YShpZCkgewogICAgICAgICAgICB0aGlzLiRyZWZzLnZpZXdRdW90YVBhZ2UuaW5pdFF1b3RhKGlkKTsKICAgICAgICB9LAoKICAgICAgICAvKua3u+WKoCovCiAgICAgICAgYWRkQW1tZXRlcigpIHsKICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goewogICAgICAgICAgICAgICAgbmFtZTogImFkZEFtbWV0ZXIiLAogICAgICAgICAgICAgICAgcXVlcnk6e30sCiAgICAgICAgICAgICAgICByZXBsYWNlOnRydWUKICAgICAgICAgICAgfSkKICAgICAgICAgICAgLy8gdGhpcy4kcmVmcy5hZGRBbW1ldGVyUGFnZS5pbml0QW1tZXRlcigpOwogICAgICAgIH0sCiAgICAgICAgc2VsZWN0Q2hhbmdlKCl7CiAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpczsKICAgICAgICAgICAgaWYgKHRoaXMucXVlcnlQYXJhbXMuY29tcGFueSAhPSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgICAgIGlmKHRoaXMucXVlcnlQYXJhbXMuY29tcGFueSA9PSAiLTEiKXsKICAgICAgICAgICAgICAgICAgICB0aGF0LnF1ZXJ5UGFyYW1zLmNvdW50cnkgPSAtMTsKICAgICAgICAgICAgICAgICAgICB0aGF0LnF1ZXJ5UGFyYW1zLmNvdW50cnlOYW1lID0gbnVsbDsKICAgICAgICAgICAgICAgIH1lbHNlewogICAgICAgICAgICAgICAgICAgIGdldENvdW50cnlCeVVzZXJJZCh0aGF0LnF1ZXJ5UGFyYW1zLmNvbXBhbnkpLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgaWYocmVzLmRhdGEuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5xdWVyeVBhcmFtcy5jb3VudHJ5ID0gcmVzLmRhdGEuZGVwYXJ0bWVudHNbMF0uaWQ7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGF0LnF1ZXJ5UGFyYW1zLmNvdW50cnlOYW1lID0gcmVzLmRhdGEuZGVwYXJ0bWVudHNbMF0ubmFtZTsKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBzZXRFbGVjdHJveVR5cGUoKXsKICAgICAgICAgICAgbGV0IHR5cGVzID0gdGhpcy5jbGFzc2lmaWNhdGlvbnM7CiAgICAgICAgICAgIGlmKHR5cGVzLmxlbmd0aCAhPSAwKXsKICAgICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZWxlY3Ryb3R5cGUgPSB0eXBlc1t0eXBlcy5sZW5ndGgtMV07CiAgICAgICAgICAgIH1lbHNlewogICAgICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbGVjdHJvdHlwZSA9IG51bGw7CiAgICAgICAgICAgIH0KICAgICAgICB9LAoKICAgICAgICBfb25SZXNldEhhbmRsZSgpewogICAgICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uUm93PVtdOwogICAgICAgICAgICB0aGlzLmNsYXNzaWZpY2F0aW9ucyA9IFtdOwogICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0ge3R5cGU6MCxjb21wYW55Om51bGwsY291bnRyeTpudWxsLGNvdW50cnlOYW1lOm51bGwsIHN0YXRpb25jb2RlNWdyOiBudWxsLCBzdGF0aW9ubmFtZTVncjogbnVsbH07CiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuY29tcGFueT0gdGhpcy5jb21wYW55OwogICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvdW50cnk9IE51bWJlcih0aGlzLmNvdW50cnkpOwogICAgICAgICAgICB0aGlzLiRyZWZzLmFtbWV0ZXJUYWJsZS5xdWVyeSh0aGlzLnF1ZXJ5UGFyYW1zKTsKICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb3VudHJ5TmFtZSA9IHRoaXMuY291bnRyeU5hbWU7CiAgICAgICAgfSwKICAgICAgICBfb25TZWFyY2hIYW5kbGUoKXsKICAgICAgICAgICAgdGhpcy5pc0Rpc2FibGU9dHJ1ZQogICAgICAgICAgICBzZXRUaW1lb3V0KCgpPT57CiAgICAgICAgICAgICAgICB0aGlzLmlzRGlzYWJsZT1mYWxzZSAgIC8v54K55Ye75LiA5qyh5pe26ZqU5Lik56eS5ZCO5omN6IO95YaN5qyh54K55Ye7CiAgICAgICAgICAgIH0sMjAwMCkKICAgICAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvblJvdz1bXTsKICAgICAgICAgICAgdGhpcy5zZXRFbGVjdHJveVR5cGUoKTsKICAgICAgICAgICAgaWYodGhpcy5xdWVyeVBhcmFtcy5jb3VudHJ5TmFtZSA9PSAiIil7CiAgICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvdW50cnkgPSAiLTEiOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuJHJlZnMuYW1tZXRlclRhYmxlLnF1ZXJ5KHRoaXMucXVlcnlQYXJhbXMpOwogICAgICAgICAgICAvLyB0aGlzLnF1ZXJ5KHRoaXMucXVlcnlQYXJhbXMpOwogICAgICAgIH0sCiAgICAgICAgc2V0RGlzYWJsZWQoKXsKICAgICAgICAgICAgZm9yKGxldCBpdGVtIG9mIHRoaXMuJHJlZnMuYW1tZXRlclRhYmxlLmluc2lkZURhdGEpewogICAgICAgICAgICAgICAgaWYoaXRlbS5iaWxsU3RhdHVzICE9IDApewogICAgICAgICAgICAgICAgICAgIGl0ZW0uX2Rpc2FibGVkID0gdHJ1ZTsvL+emgeatoumAieaLqQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBoYW5kbGVTZWxlY3RSb3codmFsKXsKICAgICAgICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvblJvdyA9IFtdOwogICAgICAgICAgICB2YWwuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb25Sb3cucHVzaChpdGVtKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKICAgICAgICBzdGFydEZsb3dTdWJtaXQocm93KXsKICAgICAgICAgICAgbGV0IGJ1c2lBbGlhcyA9ICJBRERfQU1NIjsKICAgICAgICAgICAgbGV0IGJ1c2lUaXRsZSA9ICLmlrDlop7nlLXooagoIityb3cucHJvamVjdG5hbWUrIinlrqHmibkiOwogICAgICAgICAgICBpZihyb3cuYmlsbFN0YXR1cyA9PT0gMyl7CiAgICAgICAgICAgICAgICBidXNpQWxpYXMgPSAiTU9ESUZZX0FNTSI7CiAgICAgICAgICAgICAgICBidXNpVGl0bGUgPSAi5L+u5pS555S16KGoKCIrcm93LnByb2plY3RuYW1lKyIp5a6h5om5IjsKICAgICAgICAgICAgfQogICAgICAgICAgICBpZihyb3cuaXNjaGFuZ2VhbW1ldGVyID09IDEgJiYgcm93LmJpbGxTdGF0dXM8Mil7CiAgICAgICAgICAgICAgICBidXNpQWxpYXMgPSAiQU1NX1NXSVRDSF9BTU0iOwogICAgICAgICAgICAgICAgYnVzaVRpdGxlID0gIueUteihqOaNouihqCgiK3Jvdy5wcm9qZWN0bmFtZSsiKeWuoeaJuSI7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy53b3JrRmxvd1BhcmFtcyA9IHsKICAgICAgICAgICAgICAgIGJ1c2lJZDogcm93LmlkLAogICAgICAgICAgICAgICAgYnVzaUFsaWFzOiBidXNpQWxpYXMsCiAgICAgICAgICAgICAgICBidXNpVGl0bGU6IGJ1c2lUaXRsZQogICAgICAgICAgICB9CiAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpczsKICAgICAgICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7CiAgICAgICAgICAgICAgICB0aXRsZTogJ+eUteihqOaPkOS6pOa1geeoiycsCiAgICAgICAgICAgICAgICBjb250ZW50OiAnPHA+5piv5ZCm5o+Q5Lqk55S16KGoICgnICsgcm93LnByb2plY3RuYW1lICsgJykg5Yiw5rWB56iLPC9wPicsCiAgICAgICAgICAgICAgICBvbk9rOiAoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgdGhhdC5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC4kcmVmcy5jbHdmYnRuLm9uQ2xpY2soKTsKICAgICAgICAgICAgICAgICAgICB9LCAzMDApOwogICAgICAgICAgICAgICAgfSxvbkNhbmNlbDogKCkgPT4gewogICAgICAgICAgICAgICAgICAgIHRoYXQubG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIHN0YXJ0Rmxvdyhyb3cpIHsKICAgICAgICAgICAgbGV0IHRoYXQgPSB0aGlzOwogICAgICAgICAgICBpc0luVG9kb0xpc3Qocm93LmlkLDEpLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgIC8v5a2Y5Zyo5LqO5Luj5Yqe5Lit5pe277yM5oql5Ye65o+Q56S6CiAgICAgICAgICAgICAgICBsZXQgb3duZXJuYW1lID0gIiI7CiAgICAgICAgICAgICAgICBpZiAocmVzLmRhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcmVzLmRhdGEubGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgICAgICAgICAgICAgb3duZXJuYW1lICs9IHJlcy5kYXRhW2ldLm93bmVybmFtZSArICcgJzsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgdGhhdC4kTW9kYWwud2FybmluZyh7dGl0bGU6Iua4qemmqOaPkOekuiIsY29udGVudDogIuivpeaVsOaNruWtmOWcqOS6jiIgKyBvd25lcm5hbWUgKyAi55qE5rWB56iL5Luj5Yqe5Lit77yM5aSE55CG5ZCO5omN5Y+v57un57ut5o+Q5Lqk5rWB56iLIn0pOwogICAgICAgICAgICAgICAgICAgIHRoYXQubG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgICAgfWVsc2UgaWYocm93LmJpbGxTdGF0dXMgPT0gMyB8fCByb3cuYmlsbFN0YXR1cyA9PSA0KXsKICAgICAgICAgICAgICAgICAgICBjaGVja1N0YXJ0Rmxvdyh7aWQ6cm93LmlkfSkudGhlbihyZXMxID0+IHsKICAgICAgICAgICAgICAgICAgICAgICAgLyrmj5DkuqTmtYHnqIvpqozor4HnlKjmiLfmmK/lkKbmnInmlbDmja7pnIDopoHmj5DkuqQqLwogICAgICAgICAgICAgICAgICAgICAgICB0aGF0LmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlczEuZGF0YS5pZCA9PSBudWxsIHx8IHJlczEuZGF0YS5pZCA9PSB1bmRlZmluZWQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoYXQuJE1vZGFsLndhcm5pbmcoe3RpdGxlOiLmuKnppqjmj5DnpLoiLGNvbnRlbnQ6ICLmgqjmsqHmnInlj6/mj5DkuqTnmoTmlbDmja4ifSk7CiAgICAgICAgICAgICAgICAgICAgICAgIH1lbHNlewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhhdC5zdGFydEZsb3dTdWJtaXQocm93KTsKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfWVsc2V7CiAgICAgICAgICAgICAgICAgICAgdGhhdC5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgdGhhdC5zdGFydEZsb3dTdWJtaXQocm93KTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgfSwKICAgICAgICBzaG93Rmxvdyhyb3csIHByb2NJbnN0SWQpIHsKICAgICAgICAgICAgdGhpcy5zaG93V29ya0Zsb3cgPSB0cnVlOwogICAgICAgICAgICB0aGlzLmhpc1BhcmFtcyA9IHsKICAgICAgICAgICAgICAgIGJ1c2lJZDogcm93LmlkLAogICAgICAgICAgICAgICAgYnVzaVR5cGU6IHJvdy5idXNpQWxpYXMsCiAgICAgICAgICAgICAgICBwcm9jSW5zdElkOiBwcm9jSW5zdElkCiAgICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGRvV29ya0Zsb3coZGF0YSkgeyAvL+a1geeoi+WbnuiwgwogICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy4kcmVmcy5hbW1ldGVyVGFibGUucXVlcnkoKTsKICAgICAgICAgICAgLy8gdGhpcy5xdWVyeSh0aGlzLnF1ZXJ5UGFyYW1zKTsKICAgICAgICB9LAogICAgICAgIHF1ZXJ5KHBhcmFtcykgewogICAgICAgICAgICB0aGlzLmFtbWV0ZXIubG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgIGxpc3RBbW1ldGVyKHBhcmFtcykudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgdGhpcy5hbW1ldGVyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICAgIHRoaXMuYW1tZXRlci50b3RhbCA9IHJlcy5kYXRhLnRvdGFsCiAgICAgICAgICAgICAgICB0aGlzLmFtbWV0ZXIuZGF0YSA9IE9iamVjdC5hc3NpZ24oW10sIHJlcy5kYXRhLnJvd3MpCiAgICAgICAgICAgIH0pOwogICAgICAgIH0sCiAgICAgICAgYmVmb3JlTG9hZERhdGEoZGF0YSkgewogICAgICAgICAgICBsZXQgY29scz1bXSxrZXlzPVtdCiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5leHBvcnRDb2x1bW5zLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICAgICAgICBjb2xzLnB1c2godGhpcy5leHBvcnRDb2x1bW5zW2ldLnRpdGxlKQogICAgICAgICAgICAgICAga2V5cy5wdXNoKHRoaXMuZXhwb3J0Q29sdW1uc1tpXS5rZXkpCiAgICAgICAgICAgIH0KICAgICAgICAgICAgY29uc3QgcGFyYW1zID0gewogICAgICAgICAgICAgIHRpdGxlOiBjb2xzLAogICAgICAgICAgICAgIGtleToga2V5cywKICAgICAgICAgICAgICBkYXRhOiBkYXRhLAogICAgICAgICAgICAgIGF1dG9XaWR0aDogdHJ1ZSwKICAgICAgICAgICAgICBmaWxlbmFtZTogJ+eUteihqOaVsOaNruWvvOWHuicKICAgICAgICAgICAgfTsKICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlU2l6ZSA9IHRoaXMuYW1tZXRlci5wYWdlU2l6ZTsKICAgICAgICAgICAgZXhjZWwuZXhwb3J0X2FycmF5X3RvX2V4Y2VsKHBhcmFtcyk7CiAgICAgICAgICAgIHRoaXMuJFNwaW4uaGlkZSgpOwogICAgICAgICAgICByZXR1cm4KICAgICAgICB9LAogICAgICAgIGV4cG9ydExvYWRpbmcoKXsKICAgICAgICAgICAgdGhpcy4kU3Bpbi5zaG93KHsKICAgICAgICAgICAgICAgIHJlbmRlcjogKGgpID0+IHsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gaCgnZGl2JywgWwogICAgICAgICAgICAgICAgICAgICAgICBoKCdQcm9ncmVzcycsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICc4MDBweCcKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBoKCdkaXYnLCAn5a+85Ye65Lit77yM6K+35Yu/5Yi35paw6aG16Z2iLi4uLi4uJykKICAgICAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICB9LAogICAgICAgIGV4cG9ydENzdihuYW1lKSB7CiAgICAgICAgICAgIHRoaXMuZXhwb3J0TG9hZGluZygpOwogICAgICAgICAgICB0aGlzLmV4cG9ydC5ydW4gPSB0cnVlOwogICAgICAgICAgICBsZXQgcGFyYW1zID0gdGhpcy5xdWVyeVBhcmFtczsKICAgICAgICAgICAgaWYgKG5hbWUgPT09ICdjdXJyZW50JykgewogICAgICAgICAgICAgICAgdGhpcy5iZWZvcmVMb2FkRGF0YSh0aGlzLnNldFZhbHVlQnlGb3JFYWNoKHRoaXMuYW1tZXRlci5kYXRhKSkKICAgICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfSBlbHNlIGlmIChuYW1lID09PSAnYWxsJykgewogICAgICAgICAgICAgICAgcGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICAgICAgICAgICAgcGFyYW1zLnBhZ2VTaXplID0gdGhpcy5leHBvcnQuc2l6ZTsKICAgICAgICAgICAgfQogICAgICAgICAgICAvLyBsZXQgcmVxID0gewogICAgICAgICAgICAvLyAgICAgdXJsIDogIi9idXNpbmVzcy9hbW1ldGVyb3Jwcm90b2NvbC9saXN0IiwKICAgICAgICAgICAgLy8gICAgIG1ldGhvZCA6ICJnZXQiLAogICAgICAgICAgICAvLyAgICAgcGFyYW1zIDogcGFyYW1zCiAgICAgICAgICAgIC8vIH07CiAgICAgICAgICAgIC8vIHRoaXMuYW1tZXRlci5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgLy8gYXhpb3MucmVxdWVzdChyZXEpLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgLy8gICAgIHRoaXMuYW1tZXRlci5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIC8vICAgICBpZiAocmVzLmRhdGEpIHsKICAgICAgICAgICAgLy8gICAgICAgICBsZXQgYXJyYXkgPSByZXMuZGF0YS5yb3dzOwogICAgICAgICAgICAvLyAgICAgICAgIHRoaXMuYmVmb3JlTG9hZERhdGEodGhpcy5zZXRWYWx1ZUJ5Rm9yRWFjaChhcnJheSkpOwogICAgICAgICAgICAvLyAgICAgfQogICAgICAgICAgICAvLyB9KS5jYXRjaChlcnIgPT4gewogICAgICAgICAgICAvLyAgICAgY29uc29sZS5sb2coZXJyKTsKICAgICAgICAgICAgLy8gfSk7CiAgICAgICAgICAgIGxldCByZXEgPSB7CiAgICAgICAgICAgICAgICB1cmwgOiAiL2J1c2luZXNzL2FtbWV0ZXJvcnByb3RvY29sL2V4cG9ydE1ldGVyQWxsIiwKICAgICAgICAgICAgICAgIG1ldGhvZCA6ICJwb3N0IiwKICAgICAgICAgICAgICAgIHBhcmFtcyA6IHBhcmFtcwogICAgICAgICAgICB9OwogICAgICAgICAgICBheGlvcy5maWxlKHJlcSkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbcmVzXSkKICAgICAgICAgICAgICBjb25zdCBmaWxlTmFtZSA9ICfnlLXooajmlbDmja7lr7zlh7oueGxzJzsKICAgICAgICAgICAgICAvLyDliJvlu7rkuIDkuKrkuIvovb3pk77mjqUKICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5LiL6L295paH5Lu2OicsIGZpbGVOYW1lKTsKICAgICAgICAgICAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOwogICAgICAgICAgICAgIGNvbnN0IGEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7CiAgICAgICAgICAgICAgYS5ocmVmID0gdXJsOwogICAgICAgICAgICAgIGEuZG93bmxvYWQgPSBmaWxlTmFtZTsgLy8g6K6+572u5LiL6L295paH5Lu25ZCNCiAgICAgICAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChhKTsKICAgICAgICAgICAgICBhLmNsaWNrKCk7CgogICAgICAgICAgICAgIC8vIOa4heeQhgogICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChhKTsKICAgICAgICAgICAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwodXJsKTsgLy8g6YeK5pS+5YaF5a2YCiAgICAgICAgICAgICAgfSwgMTAwKTsKICAgICAgICAgICAgICB0aGlzLiRTcGluLmhpZGUoKTsKICAgICAgICAgICAgfSkKICAgICAgICB9LAogICAgICAgIHNldFZhbHVlQnlGb3JFYWNoKGFycmF5KXsKICAgICAgICAgICAgYXJyYXkuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgaXRlbS5jYXRlZ29yeVN0ciA9IGJ0ZXh0KCJhbW1ldGVyQ2F0ZWdvcnkiLCBpdGVtLmNhdGVnb3J5LCd0eXBlQ29kZScsJ3R5cGVOYW1lJyk7CiAgICAgICAgICAgICAgICBpdGVtLnBhY2thZ2V0eXBlU3RyID0gYnRleHQoInBhY2thZ2VUeXBlIiwgaXRlbS5wYWNrYWdldHlwZSwndHlwZUNvZGUnLCd0eXBlTmFtZScpOwogICAgICAgICAgICAgICAgaXRlbS5wYXlwZXJpb2RTdHIgPSBidGV4dCgicGF5UGVyaW9kIiwgaXRlbS5wYXlwZXJpb2QsJ3R5cGVDb2RlJywndHlwZU5hbWUnKTsKICAgICAgICAgICAgICAgIGl0ZW0ucGF5dHlwZVN0ciA9IGJ0ZXh0KCJwYXlUeXBlIiwgaXRlbS5wYXl0eXBlLCd0eXBlQ29kZScsJ3R5cGVOYW1lJyk7CiAgICAgICAgICAgICAgICBpdGVtLmVsZWN0cm9uYXR1cmVTdHIgPSBidGV4dCgiZWxlY3Ryb05hdHVyZSIsIGl0ZW0uZWxlY3Ryb25hdHVyZSwndHlwZUNvZGUnLCd0eXBlTmFtZScpOwogICAgICAgICAgICAgICAgaXRlbS5lbGVjdHJvdmFsZW5jZW5hdHVyZVN0ciA9IGJ0ZXh0KCJlbGVjdHJvdmFsZW5jZU5hdHVyZSIsIGl0ZW0uZWxlY3Ryb3ZhbGVuY2VuYXR1cmUsJ3R5cGVDb2RlJywndHlwZU5hbWUnKTsKICAgICAgICAgICAgICAgIGl0ZW0uZWxlY3Ryb3R5cGVTdHIgPSBidGV4dCgiZWxlY3Ryb1R5cGUiLCBpdGVtLmVsZWN0cm90eXBlLCd0eXBlQ29kZScsJ3R5cGVOYW1lJyk7CiAgICAgICAgICAgICAgICBpdGVtLnN0YXR1c1N0ciA9IGJ0ZXh0KCJzdGF0dXMiLCBpdGVtLnN0YXR1cywndHlwZUNvZGUnLCd0eXBlTmFtZScpOwogICAgICAgICAgICAgICAgaXRlbS5wcm9wZXJ0eVN0ciA9IGJ0ZXh0KCJwcm9wZXJ0eSIsIGl0ZW0ucHJvcGVydHksJ3R5cGVDb2RlJywndHlwZU5hbWUnKTsKICAgICAgICAgICAgICAgIGl0ZW0uYW1tZXRlcnR5cGVTdHIgPSBidGV4dCgiYW1tZXRlclR5cGUiLCBpdGVtLmFtbWV0ZXJ0eXBlLCd0eXBlQ29kZScsJ3R5cGVOYW1lJyk7CiAgICAgICAgICAgICAgICBpdGVtLnN0YXRpb25zdGF0dXNTdHIgPSBidGV4dCgic3RhdGlvblN0YXR1cyIsIGl0ZW0uc3RhdGlvbnN0YXR1cywndHlwZUNvZGUnLCd0eXBlTmFtZScpOwogICAgICAgICAgICAgICAgaXRlbS5zdGF0aW9udHlwZVN0ciA9IGJ0ZXh0KCJCVVJfU1RBTkRfVFlQRSIsIGl0ZW0uc3RhdGlvbnR5cGUsJ3R5cGVDb2RlJywndHlwZU5hbWUnKTsKICAgICAgICAgICAgICAgIGl0ZW0uYW1tZXRlcnVzZVN0ciA9IGJ0ZXh0KCJhbW1ldGVyVXNlIiwgaXRlbS5hbW1ldGVydXNlLCd0eXBlQ29kZScsJ3R5cGVOYW1lJyk7CiAgICAgICAgICAgICAgICBpdGVtLmRpcmVjdHN1cHBseWZsYWdTdHIgPSBidGV4dCgiZGlyZWN0U3VwcGx5RmxhZyIsIGl0ZW0uZGlyZWN0c3VwcGx5ZmxhZywndHlwZUNvZGUnLCd0eXBlTmFtZScpOwogICAgICAgICAgICAgICAgaXRlbS5iaWxsU3RhdHVzU3RyID0gYnRleHQoImJhc2ljQmlsbFN0YXR1cyIsIGl0ZW0uYmlsbFN0YXR1cywndHlwZUNvZGUnLCd0eXBlTmFtZScpOwogICAgICAgICAgICAgICAgaXRlbS5zdXBwbHlidXJlYXVhbW1ldGVyY29kZTsKICAgICAgICAgICAgICAgIGl0ZW0ubWFnbmlmaWNhdGlvbgogICAgICAgICAgICB9KTsKICAgICAgICAgICAgcmV0dXJuIGFycmF5OwogICAgICAgIH0sCiAgICAgICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jlvIDlp4sKICAgICAgICBjaG9vc2VSZXNwb25zZUNlbnRlcigpIHsKICAgICAgICAgICAgaWYodGhpcy5xdWVyeVBhcmFtcy5jb21wYW55ID09IG51bGwgfHwgdGhpcy5xdWVyeVBhcmFtcy5jb21wYW55ID09ICItMSIgKXsKICAgICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbygi6K+35YWI6YCJ5oup5YiG5YWs5Y+4Iik7cmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuJHJlZnMuY291bnRyeU1vZGFsLmNob29zZSh0aGlzLnF1ZXJ5UGFyYW1zLmNvbXBhbnkpOy8v5omA5bGe6YOo6ZeoCiAgICAgICAgfSwKICAgICAgICBnZXREYXRhRnJvbU1vZGFsKGRhdGEpIHsKICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb3VudHJ5ID0gZGF0YS5pZDsKICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb3VudHJ5TmFtZSA9IGRhdGEubmFtZTsKICAgICAgICAgICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jnu5PmnZ8KICAgICAgICB9LAogICAgICAgIGdldFVzZXJEYXRhKCl7CiAgICAgICAgICAgIGxldCB0aGF0ID0gdGhpczsKICAgICAgICAgICAgZ2V0VXNlcmRhdGEoKS50aGVuKHJlcyA9PiB7Ly/lvZPliY3nmbvlvZXnlKjmiLfmiYDlnKjlhazlj7jlkozmiYDlsZ7pg6jpl6gKICAgICAgICAgICAgICAgIGxldCBjb21wYW5pZXMgPSB0aGF0LmNvbXBhbmllczsKICAgICAgICAgICAgICAgIGlmKHJlcy5kYXRhLmNvbXBhbmllcyAhPSBudWxsICYmIHJlcy5kYXRhLmNvbXBhbmllcy5sZW5ndGggIT0gMCl7CiAgICAgICAgICAgICAgICAgICAgaWYocmVzLmRhdGEuY29tcGFuaWVzWzBdLmlkICE9ICIyNjAwMDAwMDAwIil7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbXBhbmllcyA9IHJlcy5kYXRhLmNvbXBhbmllczsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB0aGF0LmNvbXBhbnkgPSBjb21wYW5pZXNbMF0uaWQ7CiAgICAgICAgICAgICAgICB0aGF0LnF1ZXJ5UGFyYW1zLmNvbXBhbnkgPSBjb21wYW5pZXNbMF0uaWQ7CgogICAgICAgICAgICAgICAgbGV0IGRlcGFydG1lbnRzID0gdGhhdC5kZXBhcnRtZW50czsKICAgICAgICAgICAgICAgIGlmKHJlcy5kYXRhLmRlcGFydG1lbnRzICE9IG51bGwgJiYgcmVzLmRhdGEuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApewogICAgICAgICAgICAgICAgICAgIGlmKHJlcy5kYXRhLmNvbXBhbmllc1swXS5pZCAhPSAiMjYwMDAwMDAwMCIpewogICAgICAgICAgICAgICAgICAgICAgICBkZXBhcnRtZW50cyA9IHJlcy5kYXRhLmRlcGFydG1lbnRzOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIHRoYXQuY291bnRyeSA9IGRlcGFydG1lbnRzWzBdLmlkOwogICAgICAgICAgICAgICAgdGhhdC5jb3VudHJ5TmFtZSA9IGRlcGFydG1lbnRzWzBdLm5hbWU7CiAgICAgICAgICAgICAgICB0aGF0LnF1ZXJ5UGFyYW1zLmNvdW50cnkgPSBOdW1iZXIoZGVwYXJ0bWVudHNbMF0uaWQpOwogICAgICAgICAgICAgICAgdGhhdC5xdWVyeVBhcmFtcy5jb3VudHJ5TmFtZSA9IGRlcGFydG1lbnRzWzBdLm5hbWU7CiAgICAgICAgICAgICAgICB0aGlzLl9vblNlYXJjaEhhbmRsZSgpOwogICAgICAgICAgICAgICAvLyB0aGlzLnF1ZXJ5KHtwYWdlTnVtOiAxLHR5cGU6MCxwYWdlU2l6ZTogdGhpcy5hbW1ldGVyLnBhZ2VTaXplLGNvbXBhbnk6dGhpcy5jb21wYW55LGNvdW50cnk6dGhpcy5jb3VudHJ5fSk7CiAgICAgICAgICAgIH0pOwogICAgICAgIH0sCiAgICAgICAgaW5pdCgpewogICAgICAgICAgICB0aGlzLnN0YXR1cyA9IGJsaXN0KCJzdGF0dXMiKTsvL+eKtuaAgQogICAgICAgICAgICB0aGlzLmJpbGxTdGF0dXMgPSBibGlzdCgiYmFzaWNCaWxsU3RhdHVzIik7Ly/ljZXmja7nirbmgIEKICAgICAgICAgICAgdGhpcy5hbW1ldGVyVHlwZT1ibGlzdCgiYW1tZXRlclR5cGUiKS8v55S16KGo57G75Z6LCiAgICAgICAgICAgIHRoaXMuZWxlY3Ryb1R5cGUgPSBibGlzdCgiZWxlY3Ryb1R5cGUiKTsvL+eUqOeUteexu+WeiwogICAgICAgICAgICB0aGlzLmVsZWN0cm9OYXVyZSA9IGJsaXN0KCJlbGVjdHJvTmF0dXJlIik7Ly/nlKjnlLXmgKfotKgKICAgICAgICAgICAgdGhpcy5wYXlUeXBlID0gYmxpc3QoInBheVR5cGUiKTsvL+S7mOi0ueaWueW8jwogICAgICAgICAgICB0aGlzLmVsZWN0cm92YWxlbmNlTmF0dXJlID0gYmxpc3QoImVsZWN0cm92YWxlbmNlTmF0dXJlIik7Ly/nlLXku7fmgKfotKgKICAgICAgICAgICAgdGhpcy5wcm9wZXJ0eSA9IGJsaXN0KCJwcm9wZXJ0eSIpOy8v5Lqn5p2D5b2S5bGeCiAgICAgICAgICAgIHRoaXMuZGlyZWN0c3VwcGx5RmxhZyA9IGJsaXN0KCJkaXJlY3RTdXBwbHlGbGFnIik7Ly/lr7nlpJbnu5PnrpfnsbvlnosKICAgICAgICAgICAgdGhpcy5pc2VudGl0eWFtbWV0ZXJzLnB1c2goe3R5cGVDb2RlOiAwLCB0eXBlTmFtZTogJ+WQpid9KTsKICAgICAgICAgICAgdGhpcy5pc2VudGl0eWFtbWV0ZXJzLnB1c2goe3R5cGVDb2RlOiAxLCB0eXBlTmFtZTogJ+aYryd9KTsKICAgICAgICAgICAgbGV0IHRoYXQgPSB0aGlzOwogICAgICAgICAgICBnZXRVc2VyQnlVc2VyUm9sZSgpLnRoZW4ocmVzID0+IHsvL+agueaNruadg+mZkOiOt+WPluWIhuWFrOWPuAogICAgICAgICAgICAgICAgdGhhdC5jb21wYW5pZXMgPSByZXMuZGF0YS5jb21wYW5pZXM7CiAgICAgICAgICAgICAgICBpZihyZXMuZGF0YS5pc0NpdHlBZG1pbiA9PSB0cnVlIHx8IHJlcy5kYXRhLmlzUHJvQWRtaW4gPT0gdHJ1ZSB8fCByZXMuZGF0YS5pc1N1YkFkbWluID09IHRydWUpewogICAgICAgICAgICAgICAgICAgIHRoYXQuaXNBZG1pbiA9IHRydWU7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBnZXRDb3VudHJ5c2RhdGEoe29yZ0NvZGU6cmVzLmRhdGEuY29tcGFuaWVzWzBdLmlkfSkudGhlbihyZXMgPT4gey8v5qC55o2u5p2D6ZmQ6I635Y+W5omA5bGe6YOo6ZeoCiAgICAgICAgICAgICAgICAgICAgdGhhdC5kZXBhcnRtZW50cyA9IHJlcy5kYXRhOwogICAgICAgICAgICAgICAgICAgIHRoYXQuZ2V0VXNlckRhdGEoKTsKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgZ2V0Q2xhc3NpZmljYXRpb24oKS50aGVuKHJlcyA9PiB7Ly/nlKjnlLXnsbvlnosKICAgICAgICAgICAgICAgIHRoaXMuY2xhc3NpZmljYXRpb25EYXRhID0gcmVzLmRhdGE7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgICAvLyB0aGlzLl9vblNlYXJjaEhhbmRsZSgpOyAKICAgICAgICB9CiAgICB9LAogICAgbW91bnRlZCgpIHsKICAgICAgICB0aGlzLmluaXQoKTsKICAgICAgICB0aGlzLmNvbmZpZ1ZlcnNpb24gPSB0aGlzLiRjb25maWcudmVyc2lvbjsKICAgICAgICBpZih0aGlzLmNvbmZpZ1ZlcnNpb249PSdsbid8fHRoaXMuY29uZmlnVmVyc2lvbj09J0xOJyl7CiAgICAgICAgICAgIHRoaXMuZXhwb3J0Q29sdW1ucy51bnNoaWZ0KAogICAgICAgICAgICAgICAge3RpdGxlOiAn5L6b55S15bGA55S16KGo57yW5Y+3JyxrZXk6ICdzdXBwbHlidXJlYXVhbW1ldGVyY29kZSd9LAogICAgICAgICAgICApOwogICAgICAgICAgICB0aGlzLmFtbWV0ZXIuY29sdW1ucy51bnNoaWZ0KAogICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIHRpdGxlOiAn5L6b55S15bGA55S16KGo57yW5Y+3JywKICAgICAgICAgICAgICAgICAgICBrZXk6ICdzdXBwbHlidXJlYXVhbW1ldGVyY29kZScsCiAgICAgICAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLAogICAgICAgICAgICAgICAgICAgIG1pbldpZHRoOiAxMDAsCiAgICAgICAgICAgICAgICAgICAgbWF4V2lkdGg6MjAwCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICkKICAgICAgICB9CiAgICAgICAgLy8gdGhpcy5maWxlUGFyYW0uYnVzaUlkID0gIjY2NjY2NiI7CiAgICB9LAogICAgLy8gd2F0Y2g6ewogICAgLy8gICAgICckcm91dGUnOiJpbml0IgogICAgLy8gfSwKCn0K"}, {"version": 3, "sources": ["listAmmeter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "listAmmeter.vue", "sourceRoot": "src/view/basedata/ammeter", "sourcesContent": ["<template>\r\n  <!-- *****电表管理  <AUTHOR> -->\r\n    <div>\r\n        <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n<!--        <add-ammeter-page ref=\"addAmmeterPage\"></add-ammeter-page>-->\r\n        <view-ammeter-page ref=\"viewAmmeterPage\"></view-ammeter-page>\r\n        <view-station-page ref=\"viewStationPage\"></view-station-page>\r\n        <view-quota-page ref=\"viewQuotaPage\" ></view-quota-page>\r\n        <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n        <div class=\"noaccount\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"formInline\" :model=\"queryParams\" >\r\n                    <Row>\r\n                        <Col span=\"5\" v-if=\"configVersion=='ln'|| configVersion=='LN'\">\r\n                            <FormItem label=\"供电局电表编号：\" prop=\"supplybureauammetercode\" :label-width=\"110\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.supplybureauammetercode\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\" v-else>\r\n                            <FormItem label=\"电表编号：\" prop=\"ammetername\" :label-width=\"110\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.ammetername\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"项目名称：\" prop=\"projectname\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.projectname\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"电表类型：\" prop=\"ammetertype\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-select clearable v-model=\"queryParams.ammetertype\"\r\n                                           category=\"ammeterType\" style=\"width:30vm\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"7\">\r\n                            <FormItem label=\"用电类型：\" prop=\"classifications\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <Cascader clearable :data=\"classificationData\" :change-on-select=\"true\" v-model=\"classifications\"></Cascader>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" :label-width=\"110\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" :label-width=\"100\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"queryParams.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" :label-width=\"100\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.country\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n<!--                        <Col span=\"5\">-->\r\n<!--                            <FormItem label=\"所属分局或支局\" prop=\"substation\" class=\"form-line-height\">-->\r\n<!--                                <cl-input v-model=\"queryParams.substation\"></cl-input>-->\r\n<!--                            </FormItem>-->\r\n<!--                        </Col>-->\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"单据状态：\" prop=\"billStatus\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-select clearable v-model=\"queryParams.billStatus\"\r\n                                           category=\"basicBillStatus\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n<!--                        <Col span=\"5\">-->\r\n<!--                            <FormItem label=\"管理负责人\" prop=\"ammetermanager\">-->\r\n<!--                                <cl-input v-model=\"queryParams.ammetermanager\"></cl-input>-->\r\n<!--                            </FormItem>-->\r\n<!--                        </Col>-->\r\n                        <Col span=\"4\">\r\n                            <FormItem label=\"电价性质：\" prop=\"electrovalencenature\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-select clearable v-model=\"queryParams.electrovalencenature\"\r\n                                           category=\"electrovalenceNature\" style=\"width:30vm\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"3\">\r\n                          <FormItem label=\"状态：\" prop=\"status\" :label-width=\"100\" class=\"form-line-height\">\r\n                            <cl-select v-model=\"queryParams.status\"\r\n                                       category=\"status\" style=\"width:30vm\"\r\n                                       labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                          </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"对外结算类型：\" prop=\"directsupplyflag\" :label-width=\"110\" class=\"form-line-height\">\r\n                                <cl-select clearable v-model=\"queryParams.directsupplyflag\"\r\n                                           category=\"directSupplyFlag\" style=\"width:30vm\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"局站名称：\" prop=\"stationName\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.stationName\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"资源局站id：\" prop=\"resstationcode\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.resstationcode\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"4\">\r\n                          <FormItem label=\"局站编码：\" prop=\"stationcode\" :label-width=\"100\" class=\"form-line-height\">\r\n                            <cl-input v-model=\"queryParams.stationcode\"></cl-input>\r\n                          </FormItem>\r\n                        </Col>\r\n                        <Col span=\"3\">\r\n                            <FormItem label=\"是否实体：\" prop=\"isentityammeter\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.isentityammeter\">\r\n                                <Option v-for=\"item in isentityammeters\" :value=\"item.typeCode\"\r\n                                        :key=\"item.typeCode\">{{item.typeName}}\r\n                                </Option>\r\n                               </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                  <Row>\r\n                    <Col span=\"5\">\r\n                      <FormItem label=\"5GR站址编码:\" :label-width=\"100\" class=\"form-line-height\">\r\n                        <cl-input v-model=\"queryParams.stationcode5gr\"></cl-input>\r\n                      </FormItem>\r\n                    </Col>\r\n                    <Col span=\"5\">\r\n                      <FormItem label=\"5GR站址名称:\" :label-width=\"100\" class=\"form-line-height\">\r\n                        <cl-input v-model=\"queryParams.stationname5gr\"></cl-input>\r\n                      </FormItem>\r\n                    </Col>\r\n                    <Col span=\"5\">\r\n                      <div class=\"form-line-height\">\r\n                        <Button style=\"margin-left: 5px;width:69px;\" type=\"success\" :disabled=\"isDisable\"  icon=\"ios-search\" @click=\"_onSearchHandle()\" >搜索 </Button>\r\n                        <Button style=\"margin-left: 5px;width:69px;\" type=\"info\" icon=\"ios-redo\" @click=\"_onResetHandle\" >重置</Button>\r\n                      </div>\r\n                    </Col>\r\n                  </Row>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <cl-table ref=\"ammeterTable\"\r\n                  :searchLayout=\"ammeter.filter\"\r\n                  :query-params=\"queryParams\"\r\n                  :columns=\"ammeter.columns\"\r\n                  :data=\"ammeter.data\"\r\n                  :loading=\"ammeter.loading\"\r\n                  select-enabled\r\n                  select-multiple\r\n                  @on-selection-change=\"handleSelectRow\"\r\n                  :total=\"ammeter.total\"\r\n                  :pageSize=\"ammeter.pageSize\"\r\n                  @on-query=\"query\"\r\n                  :searchable=\"false\"\r\n                  :exportable=\"false\">\r\n            <div slot=\"buttons\">\r\n                <!-- <Button type=\"primary\" @click=\"applyW\">加入白名单</Button> -->\r\n                <Button type=\"primary\" @click=\"addAmmeter\">添加</Button>\r\n                <Button type=\"success\" @click=\"editAmmeter\">修改</Button>\r\n                <Button type=\"warning\" @click=\"changeAmmeter\">换表</Button>\r\n                <Button type=\"error\" @click=\"removeAmmeter\">删除</Button>\r\n                <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n                    <Button type='default' style=\"margin-left: 5px\" >导出\r\n                        <Icon type='ios-arrow-down'></Icon>\r\n                    </Button>\r\n                    <DropdownMenu slot='list'>\r\n                        <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                        <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n                    </DropdownMenu>\r\n                </Dropdown>\r\n            </div>\r\n        </cl-table>\r\n        <cl-wf-btn ref=\"clwfbtn\" :isStart=\"true\" :params=\"workFlowParams\" @on-ok=\"doWorkFlow\" v-show=\"false\"></cl-wf-btn>\r\n        <!-- 查看流程 -->\r\n        <Modal v-model=\"showWorkFlow\" title=\"电表流程及审批意见跟踪表\" :width=\"800\">\r\n            <WorkFlowInfoComponet :wfHisParams=\"hisParams\" v-if=\"showWorkFlow\"></WorkFlowInfoComponet>\r\n        </Modal>\r\n        <!-- 加入白名单 -->\r\n        <Modal v-model=\"whiteList\" title=\"加入白名单\" @on-ok=\"submitWhiteList\" :width=\"800\">\r\n\r\n            <Form :model=\"addWhiteList\" ref=\"addWhiteList\" :rules=\"ruleValidate\" :label-width=\"80\"\r\n                class=\"margin-right-width\">\r\n                <Row>\r\n                    <Col span=\"12\">\r\n                        <FormItem label=\"白名单类型：\" :label-width=\"120\" prop=\"whitelistType\">\r\n\r\n                        <Select\r\n                                ref=\"selects\"\r\n                                :multiple=\"true\"\r\n                                :clearable=\"true\"\r\n                                v-model=\"addWhiteList.whitelistType\">\r\n                                <Option value=\"1\">一站多表</Option>\r\n                                <Option value=\"2\">一表多站</Option>\r\n                                <Option value=\"3\">单价</Option>\r\n                        </Select>\r\n                    </FormItem>\r\n                    </Col>\r\n                </Row>\r\n                <Row>\r\n                    <Col span=\"24\">\r\n                        <FormItem label=\"申请理由：\" :label-width=\"120\" prop=\"applyReason\">\r\n                            <cl-input type=\"textarea\" :rows=\"3\" v-model=\"addWhiteList.applyReason\"></cl-input>\r\n                            <!-- <label v-if=\"oldData.memo != null &&oldData.memo != ammeter.memo\"\r\n                                    style=\"color: red;\">历史数据：{{oldData.memo}}</label> -->\r\n                        </FormItem>\r\n                    </Col>\r\n                </Row>\r\n                <Row style=\"margin-left: 20.8px;\">\r\n                    <Col span=\"24\" style=\"position: relative;\">\r\n                        <!-- <cl-form v-model=\"attach.fileForm\" :label-width=\"120\" :layout=\"attach.formLayout\"></cl-form> -->\r\n                        <attach-file :param=\"fileParam\" :attachData=\"attachData\"\r\n                                         v-on:setAttachData=\"setAttachData\"/>\r\n                        <span style=\"position: absolute; top: 28px; left: 417px;\">支持pdf/word/jpg\\png文件上传</span>\r\n                    </Col>\r\n                </Row>\r\n            </Form>\r\n        </Modal>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import { whiteList, whiteInsert } from '@/api/account';\r\n    import {selectChangeAmmeter,listAmmeter, removeAmmeter,checkAcountByUpdate,getUserByUserRole,getCountryByUserId,getUserdata,getCountrysdata,getClassification,checkStartFlow} from '@/api/basedata/ammeter.js'\r\n    import {isInTodoList}from\"@/api/alertcontrol/alertcontrol\";\r\n    import {blist,btext} from \"@/libs/tools\";\r\n    import viewAmmeterPage from './viewAmmeter.vue'\r\n    import viewQuotaPage from '@/view/basedata/quota/viewQuota.vue';\r\n    import viewStationPage from '@/view/basedata/station/viewStation.vue'\r\n    import countryModal from \"./countryModal\";\r\n    import ProcessInfo from '@/view/basic/system/workflow/process-info';\r\n    import WorkFlowInfoComponet from '@/view/basic/system/workflow/workFlowInfoComponet'\r\n\r\n    import attachFile from \"@/view/basedata/whitelist/attachFile.vue\";\r\n    import { attchList, removeAttach } from '@/api/basedata/ammeter.js'\r\n    import axios from '@/libs/api.request'\r\n\r\n    import excel from '@/libs/excel'\r\n\r\n    import {mapMutations} from \"vuex\";\r\n    import routers from '@/router/routers';\r\n    import {getHomeRoute} from '@/libs/util';\r\n    export default {\r\n        name: 'ammeter',\r\n        components: {\r\n            ProcessInfo,\r\n            WorkFlowInfoComponet,\r\n            // addAmmeterPage,\r\n            // editAmmeterPage,\r\n            viewAmmeterPage,\r\n            viewStationPage,\r\n            viewQuotaPage,\r\n            countryModal,\r\n            attachFile\r\n        },\r\n\r\n        data() {\r\n            //状态\r\n            let renderStatus = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.status) {\r\n                    if (item.typeCode == params.row.status) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //用电类型\r\n            let renderElectroType = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.electroType) {\r\n                    if (item.typeCode == params.row.electrotype) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //电表类型\r\n            let renderAmmeterType = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.ammeterType) {\r\n                    if (item.typeCode == params.row.ammetertype) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //用电性质\r\n            let renderElectroNature = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.electroNaure) {\r\n                    if (item.typeCode == params.row.electronature) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //付费方式\r\n            let renderPayType = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.payType) {\r\n                    if (item.typeCode == params.row.paytype) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //电价性质\r\n            let renderElectrovalenceNature = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.electrovalenceNature) {\r\n                    if (item.typeCode == params.row.electrovalencenature) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //单据状态\r\n            let renderBillStatus = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.billStatus) {\r\n                    if (item.typeCode == params.row.billStatus) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //产权归属\r\n            let renderProperty = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.property) {\r\n                    if (item.typeCode == params.row.property) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //对外结算类型\r\n            let renderDirectsupplyflag = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.directsupplyFlag) {\r\n                    if (item.typeCode == params.row.directsupplyflag) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //查看详情\r\n            let renterViewAmmeter = (h, params) => {\r\n                let column = params.column.key;\r\n                return h(\"div\", [h(\"u\", {\r\n                    style:{display:\"inline\"},\r\n                    on: {\r\n                        click: () => {\r\n                            this.viewAmmeter(params.row.id);\r\n                        }\r\n                    }\r\n                }, params.row[column])]);\r\n            };\r\n            //查看局站详情\r\n            let renterViewStation = (h, params) => {\r\n                let column = params.column.key;\r\n                return h(\"div\", [h(\"u\", {\r\n                    on: {\r\n                        click: () => {\r\n                            this.viewStation(params.row.stationcode);\r\n                        }\r\n                    }\r\n                }, params.row[column])]);\r\n            };\r\n          //资源局站id\r\n          let termnameFormat = (h, params) => {\r\n            if (!params.row.resstationcode) {\r\n              // 返回红色文字提示：请维护\r\n              return h('div', {\r\n                style: {\r\n                  color: '#f00'\r\n                },\r\n                on: {\r\n                  click: () => {\r\n                    this.$Message.info(\"在修改电表页面，关联局站信息处维护\");\r\n                  }\r\n                }\r\n              }, '请维护')\r\n            } else {\r\n              return h('div', params.row.resstationcode)\r\n            }\r\n\r\n          };\r\n            let renderW = (h, params) => {\r\n                let that = this;\r\n                let text, type = \"\";\r\n                let row = params.row;\r\n                if (row.billStatus != 0 && row.billStatus != 3 && row.processinstId != null) {\r\n                    text = \"查看\";\r\n                    type = \"success\";\r\n                } else if (params.row.billStatus == 0 || params.row.billStatus == 3) {\r\n                    text = \"提交\";\r\n                    type = \"primary\";\r\n                }\r\n                if(type == \"\"){\r\n                    return h(\"div\", {}, text);\r\n                }\r\n                return h(\"Button\",\r\n                {\r\n                    props: {\r\n                        type: type, size: \"small\"\r\n                    },\r\n                    on: {\r\n                        click() {\r\n                            if (row.billStatus!=0 && row.billStatus != 3 && row.processinstId != null) {\r\n                                that.showFlow(params.row, params.row.processinstId);\r\n                            } else if (params.row.billStatus == 0 || params.row.billStatus == 3) {\r\n                                that.loading=true;\r\n                                that.startFlow(params.row);\r\n                            }\r\n                        }\r\n                    }\r\n                },\r\n                text\r\n                );\r\n            };\r\n            let renderQuota = (h, params) => {\r\n                let that = this;\r\n                return h(\"Button\", {\r\n                    props: {\r\n                        type: \"success\", size: \"small\"\r\n                    },attrs: {\r\n                        disabled: params.row.quotaId?false:true\r\n                    },style: {\r\n                        opacity:params.row.quotaId?1:0.4\r\n                    }, on: {\r\n                        click() {\r\n                            that.viewQuota(params.row.quotaId);\r\n                        }\r\n                    }\r\n                }, \"查看\");\r\n            };\r\n            const valisheredepartname = (rule, value, callback) => {\r\n                //console.log(rule, value, \"rule, value5555555555555555\");\r\n                if (value == null || value == '') {\r\n                    callback(new Error('不能为空'))\r\n                } else {\r\n                    callback()\r\n                }\r\n            };\r\n            return {\r\n                multiFiles: null,\r\n                removeIds: [],\r\n                attachData: [],\r\n                fileParam: {\r\n                    busiId: \"\",\r\n                    busiAlias: \"附件(协议管理)\",\r\n                    categoryCode: \"file\",\r\n                    areaCode: \"ln\"\r\n                },\r\n                ruleValidate: {\r\n                    applyReason: [\r\n                        {required: true, validator: valisheredepartname, trigger: 'change, blur'}\r\n                    ],\r\n                    whitelistType: [\r\n                        {required: true, validator: valisheredepartname, trigger: 'change, blur'}\r\n                    ]\r\n                },\r\n                addWhiteList: {\r\n                    whitelistType: \"\",\r\n                    applyReason: \"\",\r\n                    id: \"\"\r\n                },\r\n                attach: {\r\n                    fileForm: {\r\n                        file: null\r\n                    },\r\n                    formLayout: [\r\n                        {\r\n                            label: '上传附件',\r\n                            prop: 'file',\r\n                            formItemType: 'file',\r\n                            width: 300,\r\n                            format: this.format\r\n                        }\r\n                    ],\r\n                    loading: false,\r\n                    columns: [],\r\n                    data:[],\r\n                },\r\n                whiteList: false,\r\n                loading:false,\r\n                showWorkFlow: false,\r\n                configVersion:null,//版本\r\n                isDisable:false,\r\n                filterColl: true,//搜索面板展开\r\n                isAdmin:false,\r\n                company:null,//用户默认公司\r\n                country:null,//用户默认所属部门\r\n                countryName:null,//用户默认所属部门\r\n\r\n                classificationData:[],//用电类型\r\n                classifications:[],//用电类型\r\n\r\n                demoList: [],\r\n                isentityammeter:{},\r\n                isentityammeters:[],\r\n                status:[],//状态\r\n                billStatus:[],//单据状态\r\n                electroType:[],//用电类型\r\n                ammeterType:[],//电表类型\r\n                electroNature:[],//用电性质\r\n                payType:[],//付费方式\r\n                electrovalenceNature:[],//电价性质\r\n                directsupplyFlag:[],//对外结算类型\r\n                property:[],//产权归属\r\n                queryParamsList: {\r\n                projectname: '',//项目名称\r\n                meterCode: '',//电表编号\r\n                stationName: '',//局站名称\r\n                city: '',//地市\r\n                district: '',//区县\r\n                status: '1',//状态\r\n                company: '',\r\n                countryName: '',\r\n                bi11statusName: '',\r\n                },\r\n                queryParams:{country:null,company:null,countryName:null,resstationcode:null, stationcode5gr: null, stationname5gr: null},\r\n                companies:[],\r\n                departments:[],\r\n                multipleSelectionRow: [],\r\n\r\n                workFlowParams: {},\r\n                hisParams: {},\r\n                exportColumns:[\r\n                    {title: '电表编号',key: 'ammetername'},\r\n                    {title: '项目名称',key: 'projectname'},\r\n                    {title: '关联局站名',key: 'stationName'},\r\n                    {title: '所属分公司',key: 'companyName'},\r\n                    {title: '所属部门', key: 'countryName'},\r\n                    {title: '状态',key: 'statusStr'},\r\n                    {title: '单据状态',key: 'billStatusStr'},\r\n                    {title: '用电类型',key: 'electrotypename'},\r\n                    {title: '对外结算类型',key: 'directsupplyflagStr'},\r\n                    {title: '付费方式',key: 'paytypeStr'},\r\n                    {title: '电价性质',key: 'electrovalencenatureStr'},\r\n                    {title: '电表类型',key: 'ammetertypeStr'},\r\n                    {title: '产权归属',key: 'propertyStr'},\r\n                    // {title: '支局/分局',key: 'substation'},\r\n                    {title: '管理负责人',key: 'ammetermanager'},\r\n                    {title: '创建时间',key: 'createTime'},\r\n                    {title: '供电局电表户号或编号',key: 'supplybureauammetercode'},\r\n                    {title: '资源局站id：',key: 'termname'},\r\n                    {title: '倍率：',key: 'magnification'},\r\n                    {title: '单价：',key: 'price'},\r\n                    {title: '分割比例：',key: 'percent'},\r\n                ],\r\n                export: {\r\n                    run: false,//是否正在执行导出\r\n                    data: \"\",//导出数据\r\n                    totalPage: 0,//一共多少页\r\n                    currentPage: 0,//当前多少页\r\n                    percent: 0,\r\n                    size: 200000\r\n                },\r\n                ammeter: {\r\n                    loading: false,\r\n                    columns: [\r\n                        {\r\n                            title: '电表编号',\r\n                            key: 'ammetername',\r\n                            align: 'center',\r\n                            className: \"td-id\",\r\n                            render: renterViewAmmeter,\r\n                            minWidth: 120,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '项目名称',\r\n                            key: 'projectname',\r\n                            align: 'center',\r\n                            className: \"td-id\",\r\n                            render: renterViewAmmeter,\r\n                            minWidth: 120,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '关联局站名',\r\n                            key: 'stationName',\r\n                            className: \"td-id\",\r\n                            align: 'center',\r\n                            render: renterViewStation,\r\n                            minWidth: 100,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                          title: '资源局站id',\r\n                          key: 'termname',\r\n                          className: \"td-id\",\r\n                          align: 'center',\r\n                          render: termnameFormat,\r\n                          minWidth: 100,\r\n                          maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '所属分公司',\r\n                            key: 'companyName',\r\n                            align: 'center',\r\n                            minWidth: 100,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '所属部门',\r\n                            key: 'countryName',\r\n                            align: 'center',\r\n                            minWidth: 80,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '状态',\r\n                            key: 'status',\r\n                            render: renderStatus,\r\n                            align: 'center',\r\n                            minWidth: 55,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '单据状态',\r\n                            align: 'center',\r\n                            key: 'billStatus',\r\n                            render: renderBillStatus,\r\n                            minWidth: 80,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '用电类型',\r\n                            key: 'electrotypename',\r\n                            align: 'center',\r\n                            minWidth: 90,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '对外结算类型',\r\n                            key: 'directsupplyflag',\r\n                            render: renderDirectsupplyflag,\r\n                            align: 'center',\r\n                            minWidth: 80,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '付费方式',\r\n                            key: 'paytype',\r\n                            render: renderPayType,\r\n                            align: 'center',\r\n                            minWidth: 70,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '电价性质',\r\n                            key: 'electrovalencenature',\r\n                            render: renderElectrovalenceNature,\r\n                            align: 'center',\r\n                            minWidth: 70,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '电表类型',\r\n                            key: 'ammetertype',\r\n                            render: renderAmmeterType,\r\n                            align: 'center',\r\n                            minWidth: 70,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '产权归属',\r\n                            key: 'property',\r\n                            render: renderProperty,\r\n                            align: 'center',\r\n                            minWidth: 70,\r\n                            maxWidth:200\r\n                        },\r\n                        // {\r\n                        //     title: '支局/分局',\r\n                        //     key: 'substation',\r\n                        //     align: 'center',\r\n                        //     minWidth: 80,\r\n                        //     maxWidth:200\r\n                        // },\r\n                        {\r\n                            title: '管理负责人',\r\n                            key: 'ammetermanager',\r\n                            align: 'center',\r\n                            minWidth: 90,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '创建时间',\r\n                            key: 'createTime',\r\n                            align: 'center',\r\n                            minWidth: 110,\r\n                            maxWidth:200\r\n                        },\r\n                      {\r\n                        title: '上传附件',\r\n                        align: 'center',\r\n                        minWidth: 70,\r\n                        maxWidth: 200,\r\n                        render: (h, params) => {\r\n                          const row = params.row;\r\n                          const text = row.isAttach === 0 ? '未上传' : '已上传';\r\n                          return h('span', {}, text);\r\n                        }\r\n                      },\r\n                        {\r\n                            title: \"流程\",\r\n                            fixed: 'right',\r\n                            key: \"action\",\r\n                            minWidth: 60,\r\n                            maxWidth:200,\r\n                            align: 'center',\r\n                            render: renderW\r\n                        },\r\n                        {\r\n                            title: \"查看定额\",\r\n                            fixed: 'right',\r\n                            key: \"action\",\r\n                            minWidth: 65,\r\n                            maxWidth:200,\r\n                            align: 'center',\r\n                            render: renderQuota\r\n                        }],\r\n                    data: [],\r\n                    pageSize:10\r\n                }\r\n            }\r\n        },\r\n        methods: {\r\n            ...mapMutations([\"closeTag\", \"closeTagByName\"]),\r\n            //验证错误弹出提示框\r\n            errorTips(str){\r\n                this.$Notice.error({\r\n                    title: '提示',\r\n                    desc: str,\r\n                    duration: 10\r\n                });\r\n            },\r\n            // typeList\r\n            submitWhiteList() {\r\n                let arr = [];\r\n                let data = this.$refs.ammeterTable.getSelection();\r\n                console.log(data[0].ammetername, \"data.ammetername\");\r\n                console.log(this.attach.fileForm.file, \"this.attach.fileForm.file\");\r\n\r\n                this.loading = true;\r\n                this.addWhiteList.whitelistType.forEach(item => {\r\n                    arr.push({\r\n                        whitelistType: item,\r\n                    });\r\n                })\r\n                let params = {\r\n                    typeList: arr,\r\n                    applyArgument: this.addWhiteList.applyReason,\r\n                    meterCode: data[0].ammetername,\r\n                    \"fj\": \"未上传附件；\",\r\n                    // typeList.whitelistType: '1',\r\n                    dwjslx: data[0].directsupplyflag == 1?'直供':data[0].directsupplyflag == 2?'转供':\"\",\r\n\r\n                    }\r\n                console.log(params, \"params\");\r\n                whiteInsert(\r\n                    params\r\n                ).then(res => {\r\n                    console.log(res, \"res666666666666\");\r\n                    this.whiteList = false;\r\n                    if(res.data.code == 500) {\r\n                    this.loading = false;\r\n                    // this.$Message.error(res.data.msg);\r\n                    }else{\r\n                    this.$Message.success(\"保存成功\");\r\n                    this.loading = false;\r\n                    this.id1 = res.data.id;\r\n                    }\r\n                })\r\n            },\r\n          removeAttach(){\r\n            removeAttach({ids:this.removeIds.join()}).then(() => {\r\n\r\n            });\r\n          },\r\n            setAttachData(data){\r\n            console.log(data, \"data555555555555555\");\r\n            this.multiFiles = data.data;\r\n            this.removeIds = data.ids;\r\n            if(this.removeIds.length!= 0 && data.type == 'remove'){\r\n              this.removeAttach();\r\n            }else{\r\n              this.upload();\r\n            }\r\n          },\r\n            upload(){\r\n            if (this.attachData.length != 0 && this.multiFiles.length != 0){\r\n\r\n              // this.$Message.info(\"提示:上传文件过大可能导致上传失败！\");\r\n              this.loading = true;\r\n              axios.request({\r\n                url: '/common/attachments/uploadMultiFile',\r\n                method: 'post',\r\n                data: this.multiFiles\r\n              }).then((res) => {\r\n                if(res.data.code != 0){\r\n                  this.loading = false;\r\n                }\r\n                let that = this;\r\n                // if(that.fileParam.busiId == \"\") {\r\n                //     this.$Message.error(\"cuowu\");\r\n                // }else {\r\n                    attchList({busiId:that.fileParam.busiId}).then(res => {\r\n                    console.log(res, \"attchList\");\r\n                    that.attachData = Object.assign([], res.data.rows);\r\n                });\r\n                // }\r\n\r\n              })\r\n            }\r\n          },\r\n          getAccountMessages() {\r\n            // this.listTb.loading = true;\r\n            whiteList({\r\n                // meterCode: this.queryParamsList.meterCode,\r\n                // projectname: this.queryParamsList.projectname,\r\n                // stationName: this.queryParamsList.stationName,\r\n                // company: this.queryParamsList.company,\r\n                // countryName: this.queryParamsList.countryName,\r\n                // status: this.queryParamsList.status,\r\n                // size: this.pageSize,\r\n                // current: this.pageNum\r\n                }).then(res => {\r\n            // this.listTb.loading = false;\r\n                console.log(res, \"queryParamsList res\");\r\n                console.log(res.data.length, \"res.data.length\");\r\n                // this.pageTotal = res.data.total;\r\n                // this.insideData = res.data.rows;\r\n            })\r\n            },\r\n            applyW() {\r\n                let data = this.$refs.ammeterTable.getSelection();\r\n                console.log(data, \"data55555555555555\");\r\n                this.fileParam.busiId = data[0].id;\r\n                if(data.length > 1) {\r\n                    this.errorTips(\"只能选择一个电表申请加入白名单\");\r\n                }else if(data.length == 0) {\r\n                    this.errorTips(\"请选择一个电表申请加入白名单\");\r\n                }else {\r\n                    // this.getAccountMessages();\r\n                    // data[0].name\r\n                    this.attachData = [];\r\n                    this.addWhiteList = {\r\n                        whitelistType: \"\",\r\n                        applyReason: \"\",\r\n                        id: \"\"\r\n                };\r\n                    this.whiteList = true;\r\n\r\n                }\r\n            },\r\n            onModalOK() {\r\n                this.$Message.error('确定')\r\n            },\r\n            onModalCancel() {\r\n                this.$Message.error('取消')\r\n            },\r\n\r\n            /*删除*/\r\n            removeAmmeter(id) {\r\n                let multipleSelection = [];\r\n                if (this.multipleSelectionRow.length > 0) {\r\n                    for(let item of this.multipleSelectionRow){\r\n                        if(item.billStatus != 0){\r\n                            this.$Message.info(\"所选数据包含非草稿数据，不能删除！\");\r\n                            return ;\r\n                        }\r\n                        multipleSelection.push(item.id);\r\n                    }\r\n                    id = multipleSelection.join(',');\r\n                    this.$Modal.confirm({\r\n                        title: '温馨提示',\r\n                        content: '<p>确认删除吗?</p>',\r\n                        onOk: () => {\r\n                            this.ammeter.loading = true;\r\n                            removeAmmeter({ids: id}).then(res => {\r\n                                this.$Message.success(\"删除成功\");\r\n                                this._onSearchHandle();\r\n                                this.ammeter.loading = false;\r\n                            });\r\n                            this.multipleSelectionRow = [];\r\n                        },\r\n                    });\r\n                } else {\r\n                    this.$Message.info(\"请至少选择一行\");\r\n                }\r\n\r\n            },\r\n\r\n            /*编辑*/\r\n            editAmmeter() {\r\n                if (this.multipleSelectionRow.length == 1) {\r\n                    let row = this.multipleSelectionRow[0];\r\n                    selectChangeAmmeter({id:row.id}).then(res => {\r\n                        //存在于代办中时，报出提示\r\n                        if (res.data.length > 0) {\r\n                            this.$Modal.warning({title: \"温馨提示\",\r\n                                content: \"该电表已经存在换表电表【电表编号：\" + res.data[0].ammetername + \"，项目名称：\" + res.data[0].projectname + \"】,不允许再修改\"\r\n                            });\r\n                        } else {\r\n                            isInTodoList(row.id, 1).then(res => {\r\n                                //存在于代办中时，报出提示\r\n                                let ownername = \"\";\r\n                                if (res.data.length > 0) {\r\n                                    for (let i = 0; i < res.data.length; i++) {\r\n                                        ownername += res.data[i].ownername + ' ';\r\n                                    }\r\n                                    this.$Modal.warning({title: \"温馨提示\",\r\n                                        content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可修改数据\"\r\n                                    });\r\n                                } else {\r\n                                    checkAcountByUpdate({id: row.id}).then(res => {\r\n                                        //修改数据前验证台账\r\n                                        if (res.data == -1) {\r\n                                            this.$Modal.warning({title: \"温馨提示\", content: \"该数据已填写台账或正在报账中，处理后才可修改数据\"});\r\n                                        } else {\r\n                                            this.closeTagByName({\r\n                                                route: getHomeRoute(routers, \"editAmmeter\"),\r\n                                            });\r\n                                            this.$router.push({\r\n                                                name: \"editAmmeter\",\r\n                                                query: {id: row.id},\r\n                                                replace: true\r\n                                            })\r\n                                        }\r\n                                    })\r\n                                }\r\n                            }).catch(err => {\r\n                                console.log(err);\r\n                            });\r\n                        }\r\n                    });\r\n                } else {\r\n                    this.$Message.info(\"请选择其中一行\");\r\n                }\r\n\r\n            },\r\n            /*换表*/\r\n            changeAmmeter() {\r\n                if (this.multipleSelectionRow.length == 1) {\r\n                    let row = this.multipleSelectionRow[0];\r\n                    // if(row.property!=2) {\r\n                        selectChangeAmmeter({id: row.id}).then(res => {\r\n                            //存在于代办中时，报出提示\r\n                            if (res.data.length > 0) {\r\n                                this.$Modal.warning({\r\n                                    title: \"温馨提示\",\r\n                                    content: \"该电表已经存在换表电表【电表编号：\" + res.data[0].ammetername + \"，项目名称：\" + res.data[0].projectname + \"】\"\r\n                                });\r\n                            } else {\r\n                                isInTodoList(row.id, 1).then(res => {\r\n                                    //存在于代办中时，报出提示\r\n                                    let ownername = \"\";\r\n                                    if (res.data.length > 0) {\r\n                                        for (let i = 0; i < res.data.length; i++) {\r\n                                            ownername += res.data[i].ownername + ' ';\r\n                                        }\r\n                                        this.$Modal.warning({\r\n                                            title: \"温馨提示\",\r\n                                            content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可修改数据\"\r\n                                        });\r\n                                    } else {\r\n                                        this.checkChangeAmmeter(row);\r\n                                    }\r\n                                });\r\n                            }\r\n                        });\r\n                    // }else{\r\n                    //     this.$Message.info(\"该表站址产权归属为铁塔，不能进行换表操作\");\r\n                    // }\r\n                } else {\r\n                    this.$Message.info(\"请选择其中一行\");\r\n                }\r\n            },\r\n            checkChangeAmmeter(row){\r\n                if(row.billStatus !=0) {\r\n                    this.$Modal.confirm({\r\n                        title: '温馨提示',\r\n                        content: '<p>换表流程结束，旧表将停用，新表启用，请确认是否换表？</p>',\r\n                        onOk: () => {\r\n                            this.closeTagByName({\r\n                                route: getHomeRoute(routers, \"changeAmmeter\"),\r\n                            });\r\n                            this.$router.push({\r\n                                name: \"changeAmmeter\",\r\n                                query: {id: row.id},\r\n                                replace: true\r\n                            })\r\n                        },\r\n                    });\r\n\r\n                }else{\r\n                    this.$Message.info(\"草稿状态不能操作换表数据\");\r\n                }\r\n            },\r\n\r\n            /*查看*/\r\n            viewAmmeter(id) {\r\n                this.$refs.viewAmmeterPage.initAmmeter(id);\r\n            },\r\n            /*查看局站*/\r\n            viewStation(id) {\r\n                this.$refs.viewStationPage.initStation(id);\r\n            },\r\n            /*查看定额*/\r\n            viewQuota(id) {\r\n                this.$refs.viewQuotaPage.initQuota(id);\r\n            },\r\n\r\n            /*添加*/\r\n            addAmmeter() {\r\n                this.$router.push({\r\n                    name: \"addAmmeter\",\r\n                    query:{},\r\n                    replace:true\r\n                })\r\n                // this.$refs.addAmmeterPage.initAmmeter();\r\n            },\r\n            selectChange(){\r\n                let that = this;\r\n                if (this.queryParams.company != undefined) {\r\n                    if(this.queryParams.company == \"-1\"){\r\n                        that.queryParams.country = -1;\r\n                        that.queryParams.countryName = null;\r\n                    }else{\r\n                        getCountryByUserId(that.queryParams.company).then(res => {\r\n                            if(res.data.departments.length != 0){\r\n                                that.queryParams.country = res.data.departments[0].id;\r\n                                that.queryParams.countryName = res.data.departments[0].name;\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            },\r\n            setElectroyType(){\r\n                let types = this.classifications;\r\n                if(types.length != 0){\r\n                    this.queryParams.electrotype = types[types.length-1];\r\n                }else{\r\n                    this.queryParams.electrotype = null;\r\n                }\r\n            },\r\n\r\n            _onResetHandle(){\r\n                this.multipleSelectionRow=[];\r\n                this.classifications = [];\r\n                this.queryParams = {type:0,company:null,country:null,countryName:null, stationcode5gr: null, stationname5gr: null};\r\n                this.queryParams.company= this.company;\r\n                this.queryParams.country= Number(this.country);\r\n                this.$refs.ammeterTable.query(this.queryParams);\r\n                this.queryParams.countryName = this.countryName;\r\n            },\r\n            _onSearchHandle(){\r\n                this.isDisable=true\r\n                setTimeout(()=>{\r\n                    this.isDisable=false   //点击一次时隔两秒后才能再次点击\r\n                },2000)\r\n                this.multipleSelectionRow=[];\r\n                this.setElectroyType();\r\n                if(this.queryParams.countryName == \"\"){\r\n                    this.queryParams.country = \"-1\";\r\n                }\r\n                this.$refs.ammeterTable.query(this.queryParams);\r\n                // this.query(this.queryParams);\r\n            },\r\n            setDisabled(){\r\n                for(let item of this.$refs.ammeterTable.insideData){\r\n                    if(item.billStatus != 0){\r\n                        item._disabled = true;//禁止选择\r\n                    }\r\n                }\r\n            },\r\n            handleSelectRow(val){\r\n                this.multipleSelectionRow = [];\r\n                val.forEach(item => {\r\n                    this.multipleSelectionRow.push(item);\r\n                });\r\n            },\r\n            startFlowSubmit(row){\r\n                let busiAlias = \"ADD_AMM\";\r\n                let busiTitle = \"新增电表(\"+row.projectname+\")审批\";\r\n                if(row.billStatus === 3){\r\n                    busiAlias = \"MODIFY_AMM\";\r\n                    busiTitle = \"修改电表(\"+row.projectname+\")审批\";\r\n                }\r\n                if(row.ischangeammeter == 1 && row.billStatus<2){\r\n                    busiAlias = \"AMM_SWITCH_AMM\";\r\n                    busiTitle = \"电表换表(\"+row.projectname+\")审批\";\r\n                }\r\n                this.workFlowParams = {\r\n                    busiId: row.id,\r\n                    busiAlias: busiAlias,\r\n                    busiTitle: busiTitle\r\n                }\r\n                let that = this;\r\n                this.$Modal.confirm({\r\n                    title: '电表提交流程',\r\n                    content: '<p>是否提交电表 (' + row.projectname + ') 到流程</p>',\r\n                    onOk: () => {\r\n                        that.loading = true;\r\n                        setTimeout(function () {\r\n                            that.$refs.clwfbtn.onClick();\r\n                        }, 300);\r\n                    },onCancel: () => {\r\n                        that.loading = false;\r\n                    }\r\n                });\r\n            },\r\n            startFlow(row) {\r\n                let that = this;\r\n                isInTodoList(row.id,1).then(res => {\r\n                    //存在于代办中时，报出提示\r\n                    let ownername = \"\";\r\n                    if (res.data.length > 0) {\r\n                        for (let i = 0; i < res.data.length; i++) {\r\n                            ownername += res.data[i].ownername + ' ';\r\n                        }\r\n                        that.$Modal.warning({title:\"温馨提示\",content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可继续提交流程\"});\r\n                        that.loading = false;\r\n                    }else if(row.billStatus == 3 || row.billStatus == 4){\r\n                        checkStartFlow({id:row.id}).then(res1 => {\r\n                            /*提交流程验证用户是否有数据需要提交*/\r\n                            that.loading = false;\r\n                            if (res1.data.id == null || res1.data.id == undefined) {\r\n                                that.$Modal.warning({title:\"温馨提示\",content: \"您没有可提交的数据\"});\r\n                            }else{\r\n                                that.startFlowSubmit(row);\r\n                            }\r\n                        });\r\n                    }else{\r\n                        that.loading = false;\r\n                        that.startFlowSubmit(row);\r\n                    }\r\n                });\r\n            },\r\n            showFlow(row, procInstId) {\r\n                this.showWorkFlow = true;\r\n                this.hisParams = {\r\n                    busiId: row.id,\r\n                    busiType: row.busiAlias,\r\n                    procInstId: procInstId\r\n                }\r\n            },\r\n            doWorkFlow(data) { //流程回调\r\n                this.loading = false;\r\n                this.$refs.ammeterTable.query();\r\n                // this.query(this.queryParams);\r\n            },\r\n            query(params) {\r\n                this.ammeter.loading = true;\r\n                listAmmeter(params).then(res => {\r\n                    this.ammeter.loading = false;\r\n                    this.ammeter.total = res.data.total\r\n                    this.ammeter.data = Object.assign([], res.data.rows)\r\n                });\r\n            },\r\n            beforeLoadData(data) {\r\n                let cols=[],keys=[]\r\n                for (let i = 0; i < this.exportColumns.length; i++) {\r\n                    cols.push(this.exportColumns[i].title)\r\n                    keys.push(this.exportColumns[i].key)\r\n                }\r\n                const params = {\r\n                  title: cols,\r\n                  key: keys,\r\n                  data: data,\r\n                  autoWidth: true,\r\n                  filename: '电表数据导出'\r\n                };\r\n                this.queryParams.pageSize = this.ammeter.pageSize;\r\n                excel.export_array_to_excel(params);\r\n                this.$Spin.hide();\r\n                return\r\n            },\r\n            exportLoading(){\r\n                this.$Spin.show({\r\n                    render: (h) => {\r\n                        return h('div', [\r\n                            h('Progress', {\r\n                                style: {\r\n                                    width: '800px'\r\n                                },\r\n                            }),\r\n                            h('div', '导出中，请勿刷新页面......')\r\n                        ])\r\n                    }\r\n                });\r\n            },\r\n            exportCsv(name) {\r\n                this.exportLoading();\r\n                this.export.run = true;\r\n                let params = this.queryParams;\r\n                if (name === 'current') {\r\n                    this.beforeLoadData(this.setValueByForEach(this.ammeter.data))\r\n                    return;\r\n                } else if (name === 'all') {\r\n                    params.pageNum = 1;\r\n                    params.pageSize = this.export.size;\r\n                }\r\n                // let req = {\r\n                //     url : \"/business/ammeterorprotocol/list\",\r\n                //     method : \"get\",\r\n                //     params : params\r\n                // };\r\n                // this.ammeter.loading = true;\r\n                // axios.request(req).then(res => {\r\n                //     this.ammeter.loading = false;\r\n                //     if (res.data) {\r\n                //         let array = res.data.rows;\r\n                //         this.beforeLoadData(this.setValueByForEach(array));\r\n                //     }\r\n                // }).catch(err => {\r\n                //     console.log(err);\r\n                // });\r\n                let req = {\r\n                    url : \"/business/ammeterorprotocol/exportMeterAll\",\r\n                    method : \"post\",\r\n                    params : params\r\n                };\r\n                axios.file(req).then(res => {\r\n                  const blob = new Blob([res])\r\n                  const fileName = '电表数据导出.xls';\r\n                  // 创建一个下载链接\r\n                  console.log('下载文件:', fileName);\r\n                  const url = URL.createObjectURL(blob);\r\n                  const a = document.createElement('a');\r\n                  a.href = url;\r\n                  a.download = fileName; // 设置下载文件名\r\n                  document.body.appendChild(a);\r\n                  a.click();\r\n\r\n                  // 清理\r\n                  setTimeout(() => {\r\n                    document.body.removeChild(a);\r\n                    URL.revokeObjectURL(url); // 释放内存\r\n                  }, 100);\r\n                  this.$Spin.hide();\r\n                })\r\n            },\r\n            setValueByForEach(array){\r\n                array.forEach(function (item) {\r\n                    item.categoryStr = btext(\"ammeterCategory\", item.category,'typeCode','typeName');\r\n                    item.packagetypeStr = btext(\"packageType\", item.packagetype,'typeCode','typeName');\r\n                    item.payperiodStr = btext(\"payPeriod\", item.payperiod,'typeCode','typeName');\r\n                    item.paytypeStr = btext(\"payType\", item.paytype,'typeCode','typeName');\r\n                    item.electronatureStr = btext(\"electroNature\", item.electronature,'typeCode','typeName');\r\n                    item.electrovalencenatureStr = btext(\"electrovalenceNature\", item.electrovalencenature,'typeCode','typeName');\r\n                    item.electrotypeStr = btext(\"electroType\", item.electrotype,'typeCode','typeName');\r\n                    item.statusStr = btext(\"status\", item.status,'typeCode','typeName');\r\n                    item.propertyStr = btext(\"property\", item.property,'typeCode','typeName');\r\n                    item.ammetertypeStr = btext(\"ammeterType\", item.ammetertype,'typeCode','typeName');\r\n                    item.stationstatusStr = btext(\"stationStatus\", item.stationstatus,'typeCode','typeName');\r\n                    item.stationtypeStr = btext(\"BUR_STAND_TYPE\", item.stationtype,'typeCode','typeName');\r\n                    item.ammeteruseStr = btext(\"ammeterUse\", item.ammeteruse,'typeCode','typeName');\r\n                    item.directsupplyflagStr = btext(\"directSupplyFlag\", item.directsupplyflag,'typeCode','typeName');\r\n                    item.billStatusStr = btext(\"basicBillStatus\", item.billStatus,'typeCode','typeName');\r\n                    item.supplybureauammetercode;\r\n                    item.magnification\r\n                });\r\n                return array;\r\n            },\r\n            //选择所属部门开始\r\n            chooseResponseCenter() {\r\n                if(this.queryParams.company == null || this.queryParams.company == \"-1\" ){\r\n                    this.$Message.info(\"请先选择分公司\");return;\r\n                }\r\n                this.$refs.countryModal.choose(this.queryParams.company);//所属部门\r\n            },\r\n            getDataFromModal(data) {\r\n                this.queryParams.country = data.id;\r\n                this.queryParams.countryName = data.name;\r\n                //选择所属部门结束\r\n            },\r\n            getUserData(){\r\n                let that = this;\r\n                getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                    let companies = that.companies;\r\n                    if(res.data.companies != null && res.data.companies.length != 0){\r\n                        if(res.data.companies[0].id != \"2600000000\"){\r\n                            companies = res.data.companies;\r\n                        }\r\n                    }\r\n                    that.company = companies[0].id;\r\n                    that.queryParams.company = companies[0].id;\r\n\r\n                    let departments = that.departments;\r\n                    if(res.data.departments != null && res.data.departments.length != 0){\r\n                        if(res.data.companies[0].id != \"2600000000\"){\r\n                            departments = res.data.departments;\r\n                        }\r\n                    }\r\n                    that.country = departments[0].id;\r\n                    that.countryName = departments[0].name;\r\n                    that.queryParams.country = Number(departments[0].id);\r\n                    that.queryParams.countryName = departments[0].name;\r\n                    this._onSearchHandle();\r\n                   // this.query({pageNum: 1,type:0,pageSize: this.ammeter.pageSize,company:this.company,country:this.country});\r\n                });\r\n            },\r\n            init(){\r\n                this.status = blist(\"status\");//状态\r\n                this.billStatus = blist(\"basicBillStatus\");//单据状态\r\n                this.ammeterType=blist(\"ammeterType\")//电表类型\r\n                this.electroType = blist(\"electroType\");//用电类型\r\n                this.electroNaure = blist(\"electroNature\");//用电性质\r\n                this.payType = blist(\"payType\");//付费方式\r\n                this.electrovalenceNature = blist(\"electrovalenceNature\");//电价性质\r\n                this.property = blist(\"property\");//产权归属\r\n                this.directsupplyFlag = blist(\"directSupplyFlag\");//对外结算类型\r\n                this.isentityammeters.push({typeCode: 0, typeName: '否'});\r\n                this.isentityammeters.push({typeCode: 1, typeName: '是'});\r\n                let that = this;\r\n                getUserByUserRole().then(res => {//根据权限获取分公司\r\n                    that.companies = res.data.companies;\r\n                    if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                        that.isAdmin = true;\r\n                    }\r\n                    getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                        that.departments = res.data;\r\n                        that.getUserData();\r\n                    });\r\n                });\r\n                getClassification().then(res => {//用电类型\r\n                    this.classificationData = res.data;\r\n                });\r\n                // this._onSearchHandle(); \r\n            }\r\n        },\r\n        mounted() {\r\n            this.init();\r\n            this.configVersion = this.$config.version;\r\n            if(this.configVersion=='ln'||this.configVersion=='LN'){\r\n                this.exportColumns.unshift(\r\n                    {title: '供电局电表编号',key: 'supplybureauammetercode'},\r\n                );\r\n                this.ammeter.columns.unshift(\r\n                    {\r\n                        title: '供电局电表编号',\r\n                        key: 'supplybureauammetercode',\r\n                        align: 'center',\r\n                        minWidth: 100,\r\n                        maxWidth:200\r\n                    }\r\n                )\r\n            }\r\n            // this.fileParam.busiId = \"666666\";\r\n        },\r\n        // watch:{\r\n        //     '$route':\"init\"\r\n        // },\r\n\r\n    }\r\n</script>\r\n\r\n<style lang=\"less\">\r\n    td.td-id {\r\n        font-weight: bold;\r\n        color: green;\r\n        cursor: pointer;\r\n    }\r\n    .noaccount .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .noaccount .header-bar-show {\r\n        max-height: 300px;\r\n        /*padding-top: 14px;*/\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .noaccount .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n    .noaccount .row{\r\n        height:30px;\r\n        margin-bottom: -50px;\r\n    }\r\n    .form-line-height{\r\n        margin-bottom:10px;\r\n    }\r\n</style>\r\n"]}]}