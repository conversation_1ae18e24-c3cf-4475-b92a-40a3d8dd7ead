{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\effectAnalysis.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\effectAnalysis.vue", "mtime": 1754285403029}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["effectAnalysis.vue"], "names": [], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "effectAnalysis.vue", "sourceRoot": "src/view/carbon/assess/assessReport/components", "sourcesContent": ["<template>\r\n  <div class=\"water-eval-container\" style=\"position: relative\">\r\n    <div class=\"effectiveness-charts\" id=\"effectiveness-charts\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport \"echarts-gl\";\r\nexport default {\r\n  name: \"cityGreenLand\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      electrictTotal: \"\",\r\n      isFullscreen: false,\r\n      optionData: [\r\n        {\r\n          name: \"外购绿电\",\r\n          value: 30000,\r\n        },\r\n        {\r\n          name: \"外购火电\",\r\n          value: 22116,\r\n        },\r\n        {\r\n          name: \"自有新能源发电\",\r\n          value: 16616,\r\n        },\r\n      ],\r\n      nowStep: 0,\r\n      maxStep: 35,\r\n      height: 95,\r\n    };\r\n  },\r\n  props: {\r\n    examineList: {\r\n      type: Array,\r\n    },\r\n  },\r\n\r\n  watch: {\r\n    examineList: {\r\n      immediate: true,\r\n      handler(newVal, oldVal) {\r\n        if (newVal == undefined || newVal == null || newVal == \"\") return;\r\n        this.$nextTick(() => {\r\n          this.init();\r\n        });\r\n      },\r\n      deep: true, // 深度监听\r\n    },\r\n  },\r\n\r\n  methods: {\r\n    init() {\r\n      // 原始数据\r\n      let data = this.examineList;\r\n      // 将数据根据从小到大排序\r\n      // let newdata = sortObject(data);\r\n      // 图例数据\r\n      let lengthData = [];\r\n      // 返回数据\r\n      let resultData = data.map((item, index) => {\r\n        let len = item.num.toString().length;\r\n        let graw;\r\n        if (len * 1 < 2) {\r\n          graw = 100;\r\n        } else if (len * 1 < 3) {\r\n          graw = 114;\r\n        } else if (len * 1 < 4) {\r\n          graw = 124;\r\n        }\r\n        lengthData.push({\r\n          type: \"group\",\r\n          top: index * 35,\r\n          scale: [1, 1],\r\n          children: [\r\n            {\r\n              type: \"circle\",\r\n              shape: {\r\n                cx: 0,\r\n                cy: 9,\r\n                r: 5,\r\n              },\r\n              style: {\r\n                fill:\r\n                  index === 0\r\n                    ? \"#ACFFF0\"\r\n                    : index === 1\r\n                    ? \"#00ECC0\"\r\n                    : index === 2\r\n                    ? \"#00D96C\"\r\n                    : \"#006452\",\r\n              },\r\n            },\r\n            {\r\n              type: \"text\",\r\n              style: {\r\n                text: item.name,\r\n                fontFamily: \"PingFangSC-Regular\",\r\n                fill: \"#fff\",\r\n                fontSize: 14,\r\n                x: 14,\r\n                y: 2,\r\n              },\r\n            },\r\n            {\r\n              type: \"text\",\r\n              name: item.name,\r\n              style: {\r\n                text: item.num,\r\n                fill: \"#fff\",\r\n                fontFamily: \"Alibaba-PuHuiTi-Bold\",\r\n                fontSize: 18,\r\n                x: 85,\r\n                y: -2,\r\n              },\r\n            },\r\n            {\r\n              type: \"text\",\r\n              style: {\r\n                text: \"个\",\r\n                fontFamily: \"PingFangSC-Regular\",\r\n                fill: \"#fff\",\r\n                fontSize: 14,\r\n                x: graw,\r\n                y: 2,\r\n              },\r\n            },\r\n          ],\r\n        });\r\n\r\n        if (index === 0) {\r\n          return {\r\n            style: {\r\n              fill: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [\r\n                { offset: 0, color: \"#ACFFF0\" },\r\n                { offset: 1, color: \"#00ECC0\" },\r\n              ]),\r\n            },\r\n            ...item,\r\n          };\r\n        } else if (index === 1) {\r\n          return {\r\n            style: {\r\n              fill: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [\r\n                { offset: 0, color: \"#00D96C\" },\r\n                { offset: 1, color: \"#00ECC0\" },\r\n              ]),\r\n            },\r\n            ...item,\r\n          };\r\n        } else if (index === 2) {\r\n          return {\r\n            style: {\r\n              fill: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [\r\n                { offset: 0, color: \"#00D96C\" },\r\n                { offset: 1, color: \"#006452\" },\r\n              ]),\r\n            },\r\n            ...item,\r\n          };\r\n        } else if (index === 3) {\r\n          return {\r\n            style: {\r\n              fill: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [\r\n                { offset: 0, color: \"#006452\" },\r\n                { offset: 1, color: \"#006452\" },\r\n              ]),\r\n            },\r\n            ...item,\r\n          };\r\n        }\r\n      });\r\n      // 获取计算的数据\r\n      let getData = this.pyramidChart(\r\n        resultData,\r\n        document.getElementById(\"effectiveness-charts\")\r\n      );\r\n      let myChart = this.$echarts.init(document.getElementById(\"effectiveness-charts\"));\r\n      let option = {\r\n        graphic: [\r\n          {\r\n            type: \"group\",\r\n            left: \"8%\",\r\n            top: \"16%\",\r\n            scale: [0.9, 0.9],\r\n            onclick: function (params) {},\r\n            children: getData,\r\n          },\r\n          {\r\n            type: \"group\",\r\n            left: \"60%\",\r\n            top: \"20%\",\r\n            scale: [1, 1],\r\n            onclick: function (params) {},\r\n            children: lengthData,\r\n          },\r\n        ],\r\n        series: [],\r\n      };\r\n      myChart.setOption(option);\r\n      window.addEventListener(\"resize\", function () {\r\n        myChart.resize();\r\n      });\r\n    },\r\n    pyramidChart(data = [], dom, option = {}) {\r\n      let domHeight = dom ? dom.clientHeight : 0;\r\n      let domWidth = dom ? dom.clientWidth : 0;\r\n      // 默认获取一个正方形空间\r\n      let maxDistance = domHeight > domWidth / 2.3 ? domWidth / 2.3 : domHeight;\r\n      // 合并设置\r\n      let resultOption = Object.assign(\r\n        {\r\n          slanted: 1, // 每层底部的倾斜度\r\n          maxWidth: maxDistance, // 金字塔最大宽度\r\n          maxHeight: maxDistance, // 金字塔最大高度\r\n          offset: 35, //偏差\r\n        },\r\n        option\r\n      );\r\n      if (data.length === 1) {\r\n        resultOption.slanted = 50;\r\n      }\r\n      if (data.length === 2) {\r\n        resultOption.slanted = 25;\r\n      }\r\n      if (data.length === 3) {\r\n        resultOption.slanted = 10;\r\n      }\r\n      // 减去多余的误差边距\r\n      resultOption.maxHeight = resultOption.maxHeight - resultOption.offset;\r\n      // 一半最大宽度,用于计算左右边距\r\n      let halfMaxWidth = resultOption.maxWidth / 2;\r\n      // 数据最终\r\n      let resultData = [];\r\n      // 数据值 数组\r\n      let dataNums = data.map((item) => item.value || 0);\r\n      // 计算数据总和\r\n      let dataNumSum =\r\n        dataNums.length > 0 &&\r\n        dataNums.reduce(function (prev, curr) {\r\n          return Number(prev || 0) + Number(curr || 0);\r\n        });\r\n      // 中间数据点坐标数组 根据长度比值算出\r\n      let midlinePoint = [];\r\n      let multipleLayer = [0.6];\r\n      // 计算倍数等基础数据\r\n      dataNums.forEach((item, index, arr) => {\r\n        let itemNext = arr[index + 1];\r\n        if (itemNext) {\r\n          multipleLayer.push(itemNext / dataNums[0]); // 计算倍数\r\n        }\r\n        // 计算点坐标 长度\r\n        let point =\r\n          Math.round((item / dataNumSum) * resultOption.maxHeight * 1000) / 1000;\r\n        midlinePoint.push(point);\r\n      });\r\n      // 三角形的高度\r\n      let triangleHeight = 0;\r\n      let triangleHeightLayer = [];\r\n      // 三角形tan角度\r\n      let triangleRatio = halfMaxWidth / resultOption.maxHeight;\r\n      midlinePoint.forEach((item) => {\r\n        triangleHeight = triangleHeight + item;\r\n        triangleHeightLayer.push(triangleHeight);\r\n      });\r\n      // 中间数据点 最后的数据长度\r\n      let midlinePointFinally = triangleHeightLayer[triangleHeightLayer.length - 1] || 0;\r\n      // 开始拼接数据\r\n      data.forEach((item, index) => {\r\n        let arrObj = [];\r\n        let triangleHeightLayerOne = triangleHeightLayer[index];\r\n        let triangleHeightLayerOneLast = triangleHeightLayer[index - 1] || 0;\r\n        let multipleLayerOne = multipleLayer[index];\r\n        let multipleLayerOneLast = multipleLayer[index - 1] || 0;\r\n        // 第一层数据单独处理\r\n        if (index === 0) {\r\n          arrObj.push(\r\n            [0, 0],\r\n            [\r\n              -triangleRatio *\r\n                (triangleHeightLayerOne - resultOption.slanted * multipleLayerOne),\r\n              triangleHeightLayerOne - resultOption.slanted * multipleLayerOne,\r\n            ],\r\n            [0, triangleHeightLayerOne],\r\n            [\r\n              triangleRatio *\r\n                (triangleHeightLayerOne - resultOption.slanted * multipleLayerOne),\r\n              triangleHeightLayerOne - resultOption.slanted * multipleLayerOne,\r\n            ]\r\n          );\r\n        } else {\r\n          arrObj.push(\r\n            [0, triangleHeightLayerOneLast],\r\n            [\r\n              -triangleRatio *\r\n                (triangleHeightLayerOneLast -\r\n                  resultOption.slanted * multipleLayerOneLast),\r\n              triangleHeightLayerOneLast - resultOption.slanted * multipleLayerOneLast,\r\n            ],\r\n            [\r\n              -triangleRatio *\r\n                (triangleHeightLayerOne - resultOption.slanted * multipleLayerOne),\r\n              triangleHeightLayerOne - resultOption.slanted * multipleLayerOne,\r\n            ],\r\n            [0, triangleHeightLayerOne],\r\n            [\r\n              triangleRatio *\r\n                (triangleHeightLayerOne - resultOption.slanted * multipleLayerOne),\r\n              triangleHeightLayerOne - resultOption.slanted * multipleLayerOne,\r\n            ],\r\n            [\r\n              triangleRatio *\r\n                (triangleHeightLayerOneLast -\r\n                  resultOption.slanted * multipleLayerOneLast),\r\n              triangleHeightLayerOneLast - resultOption.slanted * multipleLayerOneLast,\r\n            ]\r\n          );\r\n        }\r\n        resultData.push({\r\n          type: \"polygon\",\r\n          z: 1,\r\n          shape: {\r\n            points: arrObj,\r\n          },\r\n          name: item.name,\r\n          style: item.style,\r\n        });\r\n      });\r\n      // 添加线\r\n      resultData.push({\r\n        type: \"polyline\",\r\n        shape: {\r\n          points: [\r\n            [0, 0],\r\n            [0, midlinePointFinally],\r\n          ],\r\n        },\r\n        style: {\r\n          stroke: \"#f2f2f2\",\r\n          opacity: 0.2,\r\n          lineWidth: 1,\r\n        },\r\n        z: 2,\r\n      });\r\n      // 返回\r\n      return resultData;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n#effectiveness-charts {\r\n  height: 300px;\r\n  width: 53rem;\r\n  letter-spacing: 0.1rem;\r\n}\r\n/deep/ canvas {\r\n  z-index: 9 !important;\r\n}\r\n</style>\r\n"]}]}