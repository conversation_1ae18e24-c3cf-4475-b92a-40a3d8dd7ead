{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\quota\\listQuota.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\quota\\listQuota.vue", "mtime": 1754285403021}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["listQuota.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm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file": "listQuota.vue", "sourceRoot": "src/view/basedata/quota", "sourcesContent": ["import {addQuota,viewQuota} from \"../../../api/basedata/quota/quota\";\r\n<template>\r\n    <div>\r\n        <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n<!--        <add-quota-page ref=\"addQuotaPage\" ></add-quota-page>-->\r\n        <view-quota-page ref=\"viewQuotaPage\" ></view-quota-page>\r\n        <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n        <div class=\"noaccount\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"formInline\" :model=\"queryParams\">\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"电表/协议编号：\" :label-width=\"140\" prop=\"ammeterCode\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.ammeterCode\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"项目名称：\" prop=\"projectName\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.projectName\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"单据状态：\" prop=\"billStatus\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-select v-model=\"queryParams.billStatus\"\r\n                                           category=\"basicBillStatus\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n<!--                        <Col span=\"5\" v-if=\"isProAdmin\">-->\r\n<!--                            <FormItem label=\"所属分公司：\" prop=\"company\" :label-width=\"100\" class=\"form-line-height\">-->\r\n<!--                                <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\">-->\r\n<!--                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>-->\r\n<!--                                </Select>-->\r\n<!--                            </FormItem>-->\r\n<!--                        </Col>-->\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n<!--                            <FormItem label=\"所属部门\" prop=\"country\">-->\r\n<!--                                <Select v-model=\"queryParams.country\">-->\r\n<!--                                    <Option value=\"-1\">全部</Option>-->\r\n<!--                                    <Option v-for=\"item in departments\" :value=\"item.id\" >{{item.name}}</Option>-->\r\n<!--                                </Select>-->\r\n<!--                            </FormItem>-->\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" :label-width=\"140\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"queryParams.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\" :label-width=\"140\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.country\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <div class=\"form-line-height\">\r\n                                <Button style=\"margin-left: 5px;width:69px;\" type=\"success\"  icon=\"ios-search\" @click=\"_onSearchHandle()\" >搜索 </Button>\r\n                                <Button style=\"margin-left: 5px;width:69px;\" type=\"info\" icon=\"ios-redo\" @click=\"_onResetHandle\" >重置</Button>\r\n                            </div>\r\n                        </Col>\r\n                    </Row>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <cl-table ref=\"quotaTable\"\r\n                  :searchLayout=\"quota.filter\"\r\n                  :query-params=\"queryParams\"\r\n                  :columns=\"quota.columns\"\r\n                  :data=\"quota.data\"\r\n                  select-enabled\r\n                  select-multiple\r\n                  @on-selection-change=\"handleSelectRow\"\r\n                  :loading=\"quota.loading\"\r\n                  :total=\"quota.total\"\r\n                  :pageSize=\"quota.pageSize\"\r\n                  :searchable=\"false\"\r\n                  :exportable=\"false\"\r\n                  @on-query=\"query\">\r\n            <div slot=\"buttons\">\r\n                <Button type=\"primary\" @click=\"addQuota\">添加</Button>\r\n                <Button type=\"success\" v-if=\"'sc' == version\" @click=\"editQuota\">修改</Button>\r\n                <Button type=\"error\"   v-if=\"'sc' == version\" @click=\"removeQuota\">删除</Button>\r\n                <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n                    <Button type='default' style=\"margin-left: 5px\" >导出\r\n                        <Icon type='ios-arrow-down'></Icon>\r\n                    </Button>\r\n                    <DropdownMenu slot='list'>\r\n                        <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                        <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n                    </DropdownMenu>\r\n                </Dropdown>\r\n            </div>\r\n        </cl-table>\r\n        <cl-wf-btn ref=\"clwfbtn\" :isStart=\"true\" :params=\"workFlowParams\" @on-ok=\"doWorkFlow\" v-show=\"false\"></cl-wf-btn>\r\n        <!-- 查看流程 -->\r\n        <Modal v-model=\"showWorkFlow\" title=\"定额流程及审批意见跟踪表\" :width=\"800\">\r\n            <WorkFlowInfoComponet :wfHisParams=\"hisParams\" v-if=\"showWorkFlow\"></WorkFlowInfoComponet>\r\n        </Modal>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import {getUserByUserRole,getCountryByUserId,getUserdata,getCountrysdata} from '@/api/basedata/ammeter.js'\r\n    import {isInTodoList}from\"@/api/alertcontrol/alertcontrol\";\r\n    import {removeQuota,listQuota,checkStartFlow} from '@/api/basedata/quota/quota.js';\r\n    import {blist,btext} from \"@/libs/tools\";\r\n    import viewQuotaPage from './viewQuota.vue';\r\n    import countryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import ProcessInfo from '@/view/basic/system/workflow/process-info';\r\n    import WorkFlowInfoComponet from '@/view/basic/system/workflow/workFlowInfoComponet'\r\n    import axios from '@/libs/api.request';\r\n    import excel from '@/libs/excel'\r\n    import {mapMutations} from \"vuex\";\r\n    import routers from '@/router/routers';\r\n    import {getHomeRoute} from '@/libs/util';\r\n    import indexData from '@/config/index'\r\n\r\nexport default {\r\n    name: 'quota',\r\n    components: {\r\n        ProcessInfo,\r\n        WorkFlowInfoComponet,\r\n        viewQuotaPage,\r\n        countryModal\r\n    },\r\n\r\n    data() {\r\n        //查看详情\r\n        let renterViewQuota = (h, params) => {\r\n            let column = params.column.key;\r\n            return h(\"div\", [h(\"u\", {\r\n                on: {\r\n                    click: () => {\r\n                        this.viewQuota(params.row);\r\n                    }\r\n                }\r\n            }, params.row[column])]);\r\n        };\r\n        let renderW = (h, params) => {\r\n            let that = this;\r\n            let text, type = \"\";\r\n            if (params.row.billStatus != 0 && params.row.billStatus != 3 && params.row.processinstId != null) {\r\n                text = \"查看\";\r\n                type = \"success\";\r\n            } else if (params.row.billStatus == 0 || params.row.billStatus == 3) {\r\n                text = \"提交\";\r\n                type = \"primary\";\r\n            }\r\n            if(type == \"\"){\r\n                return h(\"div\", {}, text);\r\n            }\r\n            return h(\"Button\", {\r\n                props: {\r\n                    type: type, size: \"small\"\r\n                }, on: {\r\n                    click() {\r\n                        if (params.row.billStatus!=0 && params.row.billStatus != 3 && params.row.processinstId != null) {\r\n                            that.showFlow(params.row, params.row.processinstId);\r\n                        } else if (params.row.billStatus == 0 || params.row.billStatus == 3) {\r\n                            that.loading = true;\r\n                            that.startFlow(params.row);\r\n                        }\r\n                    }\r\n                }\r\n            }, text);\r\n        };\r\n        //单据状态\r\n        let renderBillStatus = (h, params) => {\r\n            let value = \"\";\r\n            for (let item of this.billStatus) {\r\n                if (item.typeCode == params.row.billStatus) {\r\n                    value = item.typeName;\r\n                    break;\r\n                }\r\n            }\r\n            return h(\"div\", value);\r\n        };\r\n        return {\r\n            loading:false,//提交按钮重复提交\r\n            filterColl: true,//搜索面板\r\n            isAdmin:false,\r\n            isProAdmin:false,\r\n            company:null,//用户默认公司\r\n            country:null,//用户默认所属部门\r\n            countryName:null,//用户默认所属部门\r\n            version:'',\r\n            billStatus:[],//单据状态\r\n            demoList: [],\r\n            companies:[],\r\n            departments:[],\r\n            queryParams:{country:null,company:null,countryName:null},\r\n            multipleSelectionRow: [],\r\n\r\n            workFlowParams: {},\r\n            hisParams: {},\r\n            showWorkFlow: false,\r\n            exportColumns:[{title: '电表/协议编号',key: 'ammeterCode'},\r\n                {title: '项目名称',key: 'projectName'},\r\n                {title: '单据状态',key:'billStatusStr'},\r\n                {title: '1月定额值（度）',key: 'janQuotaValue'},\r\n                {title: '2月定额值（度）',key: 'febQuotaValue'},\r\n                {title: '3月定额值（度）',key: 'marQuotaValue'},\r\n                {title: '4月定额值（度）',key: 'aprQuotaValue'},\r\n                {title: '5月定额值（度）',key: 'mayQuotaValue'},\r\n                {title: '6月定额值（度）',key: 'junQuotaValue'},\r\n                {title: '7月定额值（度）',key: 'julQuotaValue'},\r\n                {title: '8月定额值（度）',key: 'augQuotaValue'},\r\n                {title: '9月定额值（度）',key: 'sepQuotaValue'},\r\n                {title: '10月定额值（度）',key: 'octQuotaValue'},\r\n                {title: '11月定额值（度）',key: 'novQuotaValue'},\r\n                {title: '12月定额值（度）',key: 'decQuotaValue'}],\r\n            export: {\r\n                run: false,//是否正在执行导出\r\n                data: \"\",//导出数据\r\n                totalPage: 0,//一共多少页\r\n                currentPage: 0,//当前多少页\r\n                percent: 0,\r\n                size: 200000\r\n            },\r\n            quota: {\r\n                loading: false,\r\n                columns: [\r\n                    {\r\n                        title: '电表/协议编号',\r\n                        key: 'ammeterCode',\r\n                        className: \"td-id\",\r\n                        render: renterViewQuota,\r\n                        align: 'center',\r\n                        minWidth:130,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '项目名称',\r\n                        key: 'projectName',\r\n                        align: 'center',\r\n                        minWidth:130,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '单据状态',\r\n                        align: 'center',\r\n                        render: renderBillStatus,\r\n                        minWidth:80,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '1月定额值（度）',\r\n                        key: 'janQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '2月定额值（度）',\r\n                        key: 'febQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '3月定额值（度）',\r\n                        key: 'marQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '4月定额值（度）',\r\n                        key: 'aprQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '5月定额值（度）',\r\n                        key: 'mayQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '6月定额值（度）',\r\n                        key: 'junQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '7月定额值（度）',\r\n                        key: 'julQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '8月定额值（度）',\r\n                        key: 'augQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '9月定额值（度）',\r\n                        key: 'sepQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '10月定额值（度）',\r\n                        key: 'octQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '11月定额值（度）',\r\n                        key: 'novQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: '12月定额值（度）',\r\n                        key: 'decQuotaValue',\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200\r\n                    },\r\n                    {\r\n                        title: \"流程\",\r\n                        fixed: 'right',\r\n                        key: \"action\",\r\n                        align: 'center',\r\n                        minWidth:70,\r\n                        maxWidth:200,\r\n                        render: renderW\r\n                    }\r\n                ],\r\n                data: [],\r\n                total: 0,\r\n                pageSize: 10\r\n            }\r\n        }\r\n    },\r\n    methods: {\r\n        ...mapMutations([\"closeTag\", \"closeTagByName\"]),\r\n        onModalOK() {\r\n            this.$Message.error('确定')\r\n        },\r\n        onModalCancel() {\r\n            this.$Message.error('取消')\r\n        },\r\n        selectChange(){\r\n            let that = this;\r\n            if (this.queryParams.company != undefined) {\r\n                if(this.queryParams.company == \"-1\"){\r\n                    that.queryParams.country = -1;\r\n                    that.queryParams.countryName = null;\r\n                }else{\r\n                    getCountryByUserId(that.queryParams.company).then(res => {\r\n                        if(res.data.departments.length != 0){\r\n                            that.queryParams.country = res.data.departments[0].id;\r\n                            that.queryParams.countryName = res.data.departments[0].name;\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n\r\n        /*删除*/\r\n        removeQuota(id){\r\n            let multipleSelection = [];\r\n            if (this.multipleSelectionRow.length > 0) {\r\n                for(let item of this.multipleSelectionRow){\r\n                    if(item.billStatus != 0){\r\n                        this.$Message.info(\"所选数据包含非草稿数据，不能删除！\");\r\n                        return ;\r\n                    }\r\n                    multipleSelection.push(item.id);\r\n                }\r\n                id = multipleSelection.join(',');\r\n                this.$Modal.confirm({\r\n                    title: '温馨提示',\r\n                    content: '<p>确认删除吗?</p>',\r\n                    onOk: () => {\r\n                        this.quota.loading = true;\r\n                        removeQuota({ids: id}).then(res => {\r\n                            this.quota.loading = false;\r\n                            this.$Message.success(\"删除成功\");\r\n                            this._onSearchHandle();\r\n                        });\r\n                        this.multipleSelectionRow = [];\r\n                    },\r\n                });\r\n            } else {\r\n                this.$Message.info(\"请至少选择一行\");\r\n            }\r\n\r\n        },\r\n\r\n        /*编辑*/\r\n        editQuota() {\r\n            if (this.multipleSelectionRow.length == 1) {\r\n                let row = this.multipleSelectionRow[0];\r\n                isInTodoList(row.id,3).then(res=>{\r\n                    //存在于代办中时，报出提示\r\n                    let ownername=\"\";\r\n                    if(res.data.length>0) {\r\n                        for (let i = 0; i < res.data.length; i++) {\r\n                            ownername += res.data[i].ownername + ' ';\r\n                        }\r\n                        this.$Modal.warning({title:\"温馨提示\",content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可修改数据\"});\r\n                    }else{\r\n                        this.closeTagByName({\r\n                            route: getHomeRoute(routers, \"editQuota\"),\r\n                        });\r\n                        this.$router.push({\r\n                            name: \"editQuota\",\r\n                            query:{id:row.id},\r\n                            replace:true\r\n                        })\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            } else {\r\n                this.$Message.info(\"请选择其中一行\");\r\n            }\r\n        },\r\n\r\n        /*添加*/\r\n        addQuota() {\r\n\r\n            this.$router.push({\r\n                name: \"addQuota\",\r\n                query:{},\r\n                replace:true\r\n            })\r\n        },\r\n        /*查看*/\r\n        viewQuota(row) {\r\n            this.$refs.viewQuotaPage.initQuota(row.id);\r\n        },\r\n        _onResetHandle(){\r\n            this.multipleSelectionRow=[];\r\n            this.queryParams.company= this.company;\r\n            this.queryParams.country= this.country;\r\n            this.$refs.quotaTable.query(this.queryParams);\r\n            this.queryParams.countryName = this.countryName;\r\n        },\r\n        _onSearchHandle(){\r\n            this.multipleSelectionRow=[];\r\n            if(this.queryParams.countryName == \"\"){\r\n                this.queryParams.country = \"-1\";\r\n            }\r\n            this.$refs.quotaTable.query(this.queryParams);\r\n        },\r\n        setDisabled(){\r\n            for(let item of this.$refs.quotaTable.insideData){\r\n                if(item.billStatus != 0){\r\n                    item._disabled = true;//禁止选择\r\n                }\r\n            }\r\n        },\r\n        handleSelectRow(val){\r\n            this.multipleSelectionRow = [];\r\n            val.forEach(item => {\r\n                this.multipleSelectionRow.push(item);\r\n            });\r\n        },\r\n        startFlowSubmit(row){\r\n            let busiAlias = \"ADD_QUOTA\";\r\n            let busiTitle = \"新增定额(\" + row.projectName + \")审批\";\r\n            if (row.billStatus === 3) {\r\n                busiAlias = \"MODIFY_QUOTA\";\r\n                busiTitle = \"修改定额(\" + row.projectName + \")审批\";\r\n            }\r\n            this.workFlowParams = {\r\n                busiId: row.id,\r\n                busiAlias: busiAlias,\r\n                busiTitle: busiTitle\r\n            };\r\n            let that = this;\r\n            this.$Modal.confirm({\r\n                title: '定额提交流程',\r\n                content: '<p>是否提交定额 (' + row.projectName + ') 到流程</p>',\r\n                onOk: () => {\r\n                    that.loading = true;\r\n                    setTimeout(function () {\r\n                        that.$refs.clwfbtn.onClick();\r\n                    }, 300);\r\n                },onCancel: () => {\r\n                    that.loading = false;\r\n                }\r\n            });\r\n        },\r\n        startFlow(row) {\r\n            let that = this;\r\n            isInTodoList(row.id,3).then(res => {\r\n                //存在于代办中时，报出提示\r\n                let ownername = \"\";\r\n                if (res.data.length > 0) {\r\n                    for (let i = 0; i < res.data.length; i++) {\r\n                        ownername += res.data[i].ownername + ' ';\r\n                    }\r\n                    that.$Modal.warning({title:\"温馨提示\",content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可继续提交流程\"});\r\n                    that.loading = false;\r\n                }else if(row.billStatus == 3 || row.billStatus == 4){\r\n                    checkStartFlow({id:row.id}).then(res1 => {\r\n                        /*提交流程验证用户是否有数据需要提交*/\r\n                        that.loading = false;\r\n                        if (res1.data.id == null || res1.data.id == undefined) {\r\n                            that.$Modal.warning({title:\"温馨提示\",content: \"您没有可提交的数据\"});\r\n                        }else{\r\n                            that.startFlowSubmit(row);\r\n                        }\r\n                    });\r\n                }else{\r\n                    that.loading = false;\r\n                    that.startFlowSubmit(row);\r\n                }\r\n            });\r\n        },\r\n        showFlow(row, procInstId) {\r\n            this.showWorkFlow = true;\r\n            this.hisParams = {\r\n                busiId: row.id,\r\n                busiType: row.busiAlias,\r\n                procInstId: procInstId\r\n            }\r\n        },\r\n        doWorkFlow(data) { //流程回调\r\n            this.loading = false;\r\n            this.$refs.quotaTable.query();\r\n        },\r\n        query(params) {\r\n            this.quota.loading = true;\r\n            listQuota(params).then(res => {\r\n                this.quota.loading = false;\r\n                this.quota.total = res.data.total\r\n                this.quota.data = Object.assign([], res.data.rows)\r\n            });\r\n        },\r\n        beforeLoadData(data) {\r\n            let cols=[],keys=[]\r\n            for (let i = 0; i < this.exportColumns.length; i++) {\r\n                cols.push(this.exportColumns[i].title)\r\n                keys.push(this.exportColumns[i].key)\r\n            }\r\n            const params = {\r\n                title: cols,\r\n                key: keys,\r\n                data: data,\r\n                autoWidth: true,\r\n                filename: '定额数据导出'\r\n            };\r\n            this.queryParams.pageSize = this.quota.pageSize;\r\n            excel.export_array_to_excel(params);\r\n            this.$Spin.hide();\r\n            return\r\n        },\r\n        exportLoading(){\r\n            this.$Spin.show({\r\n                render: (h) => {\r\n                    return h('div', [\r\n                        h('Progress', {\r\n                            style: {\r\n                                width: '800px'\r\n                            },\r\n                        }),\r\n                        h('div', '导出中，请勿刷新页面......')\r\n                    ])\r\n                }\r\n            });\r\n        },\r\n        exportCsv(name) {\r\n            this.exportLoading();\r\n            this.export.run = true;\r\n            let params = this.queryParams;\r\n            if (name === 'current') {\r\n                this.beforeLoadData(this.setValueByForEach(this.quota.data));\r\n                return;\r\n            } else if (name === 'all') {\r\n                params.pageNum = 1;\r\n                params.pageSize = this.export.size\r\n            }\r\n            let req = {\r\n                url : \"/business/quota/list\",\r\n                method : \"get\",\r\n                params : params\r\n            };\r\n            axios.request(req).then(res => {\r\n                if (res.data) {\r\n                    let array = res.data.rows;\r\n                    this.beforeLoadData(this.setValueByForEach(array))\r\n                }\r\n            }).catch(err => {\r\n                console.log(err);\r\n            });\r\n        },\r\n        setValueByForEach(array){\r\n            array.forEach(function (item) {\r\n                item.billStatusStr = btext(\"basicBillStatus\", item.billStatus,'typeCode','typeName');\r\n            });\r\n            return array;\r\n        },\r\n        //选择所属部门开始\r\n        chooseResponseCenter() {\r\n            if(this.queryParams.company == null || this.queryParams.company == \"-1\"){\r\n                this.$Message.info(\"请先选择分公司\");return;\r\n            }\r\n            this.$refs.countryModal.choose(this.queryParams.company);//所属部门\r\n        },\r\n        getDataFromModal(data) {\r\n            this.queryParams.country = data.id;\r\n            this.queryParams.countryName = data.name;\r\n            //选择所属部门结束\r\n        },\r\n        getUserData(){\r\n            let that = this;\r\n            getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                let companies = that.companies;\r\n                if(res.data.companies != null && res.data.companies.length != 0){\r\n                    if(res.data.companies[0].id != \"2600000000\"){\r\n                        companies = res.data.companies;;\r\n                    }else{\r\n                        that.isProAdmin = true;\r\n                    }\r\n                }\r\n                that.company = companies[0].id;\r\n                that.queryParams.company = companies[0].id;\r\n\r\n                let departments = that.departments;\r\n                if(res.data.departments != null && res.data.departments.length != 0){\r\n                    if(res.data.companies[0].id != \"2600000000\"){\r\n                        departments = res.data.departments;\r\n                    }\r\n                }\r\n                that.country = departments[0].id;\r\n                that.countryName = departments[0].name;\r\n                that.queryParams.country = Number(departments[0].id);\r\n                that.queryParams.countryName = departments[0].name;\r\n                this.query({pageNum: 1,type:0,pageSize: this.quota.pageSize,company:this.company,country:this.country});\r\n            });\r\n        },\r\n        init() {\r\n            this.version = indexData.version\r\n            this.billStatus = blist(\"basicBillStatus\");//单据状态\r\n            let that = this;\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                    that.getUserData();\r\n                });\r\n            });\r\n\r\n        },\r\n    },\r\n\r\n    mounted() {\r\n        this.init();\r\n    },\r\n    // watch:{\r\n    //     '$route':\"init\"\r\n    // },\r\n\r\n}\r\n</script>\r\n\r\n\r\n<style lang=\"less\">\r\n    td.td-id {\r\n        font-weight: bold;\r\n        color: green;\r\n        cursor: pointer;\r\n    }\r\n    .noaccount .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .noaccount .header-bar-show {\r\n        max-height: 300px;\r\n        /*padding-top: 14px;*/\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .noaccount .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n    .form-line-height{\r\n        margin-bottom:10px;\r\n    }\r\n</style>"]}]}