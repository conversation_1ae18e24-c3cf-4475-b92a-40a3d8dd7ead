{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addCreditAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addCreditAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRQcmVkUG93ZXJBY2NvdW50LA0KICBhZGRBY2NvdW50RXMsDQogIHJlbW92ZUFjY291bnRFcywNCiAgZ2V0VXNlciwNCiAgZ2V0RGVwYXJ0bWVudHMsDQogIGFjY291bnRFc1RvdGFsLA0KICBzZWxlY3RJZHNCeUVzUGFyYW1zLA0KICByZW1vdmVBbGwsDQp9IGZyb20gIkAvYXBpL2FjY291bnQiOw0KaW1wb3J0IHsgZ2V0UmVzQ2VudGVyLCBnZXRjb21wYW55IH0gZnJvbSAiQC9hcGkvYWxlcnRjb250cm9sL2FsZXJ0Y29udHJvbCI7DQppbXBvcnQgeyBhZ2FpbkpvaW4gfSBmcm9tICJAL2FwaS9hY2NvdW50QmlsbFBlciI7DQppbXBvcnQgew0KICBnZXRDbGFzc2lmaWNhdGlvbiwNCiAgZ2V0VXNlcmRhdGEsDQogIGdldFVzZXJCeVVzZXJSb2xlLA0KICBnZXRDb3VudHJ5c2RhdGEsDQogIGdldENvdW50cnlCeVVzZXJJZCwNCn0gZnJvbSAiQC9hcGkvYmFzZWRhdGEvYW1tZXRlci5qcyI7DQppbXBvcnQgew0KICBnZXREYXRlcywNCiAgdGVzdE51bWJlciwNCiAgZ2V0Rmlyc3REYXRlQnlBY2NvdW50bm9feXl5eW1tZGQsDQogIGdldExhc3REYXRlQnlBY2NvdW50bm9feXl5eW1tZGQsDQogIGdldERhdGVzMiwNCiAgY3V0RGF0ZV95eXl5bW1kZCwNCiAgc3RyaW5nVG9EYXRlLA0KICBnZXRDdXJyZW50RGF0ZSwNCn0gZnJvbSAiQC92aWV3L2FjY291bnQvcG93ZXJBY2NvdW50SGVscGVyIjsNCmltcG9ydCB7DQogIF92ZXJpZnlfU3RhcnREYXRlLA0KICBfdmVyaWZ5X0VuZERhdGUsDQogIHZlcmlmaWNhdGlvbiwNCiAgdW5pdHBpcmNlTWluLA0KICB1bml0cGlyY2VNYXgsDQogIHVuaXRwaXJjZU1heDEsDQp9IGZyb20gIkAvdmlldy9hY2NvdW50L1Bvd2VyQWNjb3VudEVzIjsNCmltcG9ydCB7DQogIF92ZXJpZnlfU3RhcnREYXRlMSwNCiAganVkZ2VOdW1iZXIsDQogIF92ZXJpZnlfRW5kRGF0ZTEsDQogIF92ZXJpZnlfUHJldlRvdGFsUmVhZGluZ3MsDQogIF92ZXJpZnlfQ3VyVG90YWxSZWFkaW5ncywNCiAgb3RoZXJfbm9fYW1tZXRlcm9yX3Byb3RvY29sLA0KICBzZWxmX25vX2FtbWV0ZXJvcl9wcm90b2NvbCwNCiAgSEZMX2FtbWV0ZXJvciwNCiAganVkZ2luZ19lZGl0YWJpbGl0eSwNCiAganVkZ2luZ19lZGl0YWJpbGl0eTEsDQogIF92ZXJpZnlfTW9uZXksDQogIF9jYWxjdWxhdGVVc2VkUmVhZGluZ3MsDQogIF9jYWxjdWxhdGVUb3RhbFJlYWRpbmdzLA0KICBfY2FsY3VsYXRlVW5pdFByaWNlQnlVc2VkTW9uZXksDQogIF9jYWxjdWxhdGVBY2NvdW50TW9uZXksDQogIF9jYWxjdWxhdGVRdW90ZXJlYWRpbmdzcmF0aW8sDQogIHJlcXVpcmVkRmllbGRWYWxpZGF0b3IsDQogIGNvdW50VGF4YW1vdW50c3IsDQogIGNvdW50VGF4YW1vdW50LA0KICBjYWxjdWxhdGVBY3R1YWxNb25leSwNCiAganVkZ2VfbmVnYXRlLA0KICBqdWRnZV9yZWNvdmVyeSwNCiAganVkZ2VfeWIsDQp9IGZyb20gIkAvdmlldy9hY2NvdW50L1Bvd2VyQWNjb3VudENvbnRyb2xsZXIiOw0KaW1wb3J0IHsgd2lkdGhzdHlsZSB9IGZyb20gIkAvdmlldy9idXNpbmVzcy9tc3NBY2NvdW50YmlsbC9tc3NBY2NvdW50YmlsbGRhdGEiOw0KaW1wb3J0IENvdW50cnlNb2RhbCBmcm9tICJAL3ZpZXcvYmFzZWRhdGEvYW1tZXRlci9jb3VudHJ5TW9kYWwiOw0KaW1wb3J0IGV4Y2VsIGZyb20gIkAvbGlicy9leGNlbCI7DQppbXBvcnQgYXhpb3MgZnJvbSAiQC9saWJzL2FwaS5yZXF1ZXN0IjsNCmltcG9ydCB7IGJsaXN0IH0gZnJvbSAiQC9saWJzL3Rvb2xzIjsNCmltcG9ydCBBZGRCaWxsUGVyIGZyb20gIkAvdmlldy9hY2NvdW50L2FkZEJpbGxQcmVNb2RhbCI7DQppbXBvcnQgQ29tcGxldGVkUHJlTW9kYWwgZnJvbSAiQC92aWV3L2FjY291bnQvY29tcGxldGVkUHJlTW9kYWwiOw0KaW1wb3J0IFNlbGVjdEFtbWV0ZXIgZnJvbSAiQC92aWV3L2FjY291bnQvc2VsZWN0QW1tZXRlciI7DQppbXBvcnQgaW5kZXhEYXRhIGZyb20gIkAvY29uZmlnL2luZGV4IjsNCg0KaW1wb3J0IHBlcm1pc3Npb25NaXhpbiBmcm9tICJAL21peGlucy9wZXJtaXNzaW9uIjsNCg0KaW1wb3J0IHBhZ2VGdW4gZnJvbSAiQC9taXhpbnMvcGFnZUZ1biI7DQoNCmxldCBkYXRlcyA9IGdldERhdGVzKCk7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJhZGRQcmVkUG93ZXJBY2NvdW50IiwNCiAgbWl4aW5zOiBbcGVybWlzc2lvbk1peGluLCBwYWdlRnVuXSwNCiAgY29tcG9uZW50czogeyBDb21wbGV0ZWRQcmVNb2RhbCwgU2VsZWN0QW1tZXRlciwgQWRkQmlsbFBlciwgQ291bnRyeU1vZGFsIH0sDQogIGRhdGEoKSB7DQogICAgbGV0IHJlbmRlclN0YXR1cyA9IChoLCB7IHJvdywgaW5kZXggfSkgPT4gew0KICAgICAgdmFyIHN0YXR1cyA9ICIiOw0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW2luZGV4XTsNCiAgICAgIGZvciAobGV0IGl0ZW0gb2YgdGhpcy5hY2NvdW50U3RhdHVzKSB7DQogICAgICAgIGlmIChpdGVtLnR5cGVDb2RlID09IHJvdy5zdGF0dXMpIHsNCiAgICAgICAgICBkYXRhLnN0YXR1c05hbWUgPSBpdGVtLnR5cGVOYW1lOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gaCgiZGl2IiwgZGF0YS5zdGF0dXNOYW1lKTsNCiAgICB9Ow0KDQogICAgbGV0IHJlbmRlckNhdGVnb3J5ID0gKGgsIHBhcmFtcykgPT4gew0KICAgICAgdmFyIGNhdGVnb3J5bmFtZSA9ICIiOw0KICAgICAgZm9yIChsZXQgaXRlbSBvZiB0aGlzLmNhdGVnb3J5cykgew0KICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSBwYXJhbXMucm93LmNhdGVnb3J5KSB7DQogICAgICAgICAgY2F0ZWdvcnluYW1lID0gaXRlbS50eXBlTmFtZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGgoImRpdiIsIGNhdGVnb3J5bmFtZSk7DQogICAgfTsNCg0KICAgIHJldHVybiB7DQogICAgICBmb3JtSXRlbVdpZHRoOiB3aWR0aHN0eWxlLA0KICAgICAgdmVyc2lvbjogIiIsDQogICAgICBkYXRlTGlzdDogZGF0ZXMsDQogICAgICBmaWx0ZXJDb2xsOiB0cnVlLCAvL+aQnOe0oumdouadv+WxleW8gA0KICAgICAgZWRpdEluZGV4OiAtMSwgLy/lvZPliY3nvJbovpHooYwNCiAgICAgIGNvbHVtbnNJbmRleDogLTEsIC8v5b2T5YmN57yW6L6R5YiXDQogICAgICBlZGl0U3RhcnREYXRlOiAiIiwNCiAgICAgIG15U3R5bGU6IFtdLCAvL+agt+W8jw0KICAgICAgZWRpdEVuZERhdGU6ICIiLA0KICAgICAgZWRpdGN1cnVzZWRyZWFkaW5nczogIiIsDQogICAgICBlZGl0dHJhbnNmb3JtZXJ1bGxhZ2U6ICIiLA0KICAgICAgZWRpdHRheHJhdGU6ICIiLA0KICAgICAgZWRpdGlucHV0dGlja2V0bW9uZXk6ICIiLA0KICAgICAgZWRpdGlucHV0dGF4dGlja2V0bW9uZXk6ICIiLA0KICAgICAgc3BpblNob3c6IGZhbHNlLCAvL+mBrue9qQ0KICAgICAgY2F0ZWdvcnlzOiBbXSwgLy/mj4/ov7DnsbvlnosNCiAgICAgIGVkaXRhY2NvdW50bW9uZXk6ICIiLA0KICAgICAgZWRpdHJlbWFyazogIiIsDQogICAgICBhY2NvdW50U3RhdHVzOiBbXSwNCiAgICAgIGNvbXBhbmllczogW10sDQogICAgICBkZXBhcnRtZW50czogW10sDQogICAgICBpc0FkbWluOiBmYWxzZSwNCiAgICAgIGNvbXBhbnk6IG51bGwsIC8v55So5oi36buY6K6k5YWs5Y+4DQogICAgICBjb3VudHJ5OiBudWxsLCAvL+eUqOaIt+m7mOiupOaJgOWxnumDqOmXqA0KICAgICAgY291bnRyeU5hbWU6IG51bGwsIC8v55So5oi36buY6K6k5omA5bGe6YOo6ZeoDQogICAgICBleHBvcnQ6IHsNCiAgICAgICAgcnVuOiBmYWxzZSwgLy/mmK/lkKbmraPlnKjmiafooYzlr7zlh7oNCiAgICAgICAgZGF0YTogIiIsIC8v5a+85Ye65pWw5o2uDQogICAgICAgIHRvdGFsUGFnZTogMCwgLy/kuIDlhbHlpJrlsJHpobUNCiAgICAgICAgY3VycmVudFBhZ2U6IDAsIC8v5b2T5YmN5aSa5bCR6aG1DQogICAgICAgIHBlcmNlbnQ6IDAsDQogICAgICAgIHNpemU6IDEwMDAwMDAwLA0KICAgICAgfSwNCiAgICAgIGFjY291bnRPYmo6IHsNCiAgICAgICAgYWNjb3VudG5vOiBkYXRlc1swXS5jb2RlLCAvL+acn+WPtyzpu5jorqTlvZPliY3mnIgNCiAgICAgICAgY29tcGFueTogIiIsIC8v5YiG5YWs5Y+4DQogICAgICAgIHByb2plY3ROYW1lOiAiIiwgLy/pobnnm67lkI3np7ANCiAgICAgICAgY291bnRyeTogIiIsIC8v5omA5bGe6YOo6ZeoDQogICAgICAgIGFtbWV0ZXJOYW1lOiAiIiwgLy/nlLXooajmiLflj7cv5Y2P6K6u57yW56CBDQogICAgICAgIHN0YXRpb25OYW1lOiAiIiwNCiAgICAgICAgYWNjb3VudFR5cGU6ICIxIiwgLy/lj7DotKbnsbvlnosNCiAgICAgICAgYWNjb3VudGVzdHlwZTogMiwgLy/lj7DotKbnsbvlnosNCiAgICAgICAgc3VwcGx5YnVyZWF1YW1tZXRlcmNvZGU6ICIiLA0KICAgICAgfSwNCiAgICAgIHRiQWNjb3VudDogew0KICAgICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgICAgY29sdW1uczogW10sDQogICAgICAgIGhlYWRDb2x1bW46IFsNCiAgICAgICAgICB7IHR5cGU6ICJzZWxlY3Rpb24iLCB3aWR0aDogNjAsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLpobnnm67lkI3np7AiLCBrZXk6ICJwcm9qZWN0TmFtZSIsIHNsb3Q6ICJwcm9qZWN0TmFtZSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICB0YWlsQ29sdW1uOiBbDQogICAgICAgICAgeyB0aXRsZTogIuaJgOWxnuWIhuWFrOWPuCIsIGtleTogImNvbXBhbnlOYW1lIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuaJgOWxnumDqOmXqCIsIGtleTogImNvdW50cnlOYW1lIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuWxgOermSIsIGtleTogInN0YXRpb25OYW1lIiwgYWxpZ246ICJjZW50ZXIiLCB3aWR0aDogNjAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIui1t+Wni+aXpeacnyIsDQogICAgICAgICAgICBzbG90OiAic3RhcnRkYXRlIiwNCiAgICAgICAgICAgIGtleTogInN0YXJ0ZGF0ZSIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB3aWR0aDogOTAsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuaIquatouaXpeacnyIsDQogICAgICAgICAgICBzbG90OiAiZW5kZGF0ZSIsDQogICAgICAgICAgICBrZXk6ICJlbmRkYXRlIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA5MCwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi55So55S16YePKOW6pikiLA0KICAgICAgICAgICAgc2xvdDogImN1cnVzZWRyZWFkaW5ncyIsDQogICAgICAgICAgICBrZXk6ICJjdXJ1c2VkcmVhZGluZ3MiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgeyB0aXRsZTogIueUteS7tyjlhYMpIiwga2V5OiAidW5pdHBpcmNlIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuaZruelqOWQq+eojumHkeminSjlhYMpIiwgc2xvdDogImlucHV0dGlja2V0bW9uZXkiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5LiT56Wo5ZCr56iO6YeR6aKdKOWFgykiLCBzbG90OiAiaW5wdXR0YXh0aWNrZXRtb25leSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLkuJPnpajnqI7njofvvIgl77yJIiwgc2xvdDogInRheHJhdGUiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5LiT56Wo56iO6aKdIiwga2V5OiAidGF4YW1vdW50IiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIueUtei0uSIsIGtleTogImFjY291bnRtb25leSIsIGFsaWduOiAiY2VudGVyIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLlpIfms6giLCBzbG90OiAicmVtYXJrIiwga2V5OiAicmVtYXJrIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIueUqOeUteexu+WeiyIsIGtleTogImVsZWN0cm90eXBlbmFtZSIsIGFsaWduOiAiY2VudGVyIiwgd2lkdGg6IDk0IH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLnsbvlnovmj4/ov7AiLA0KICAgICAgICAgICAga2V5OiAiY2F0ZWdvcnluYW1lIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHJlbmRlcjogcmVuZGVyQ2F0ZWdvcnksDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgbG5Db2x1bW46IFsNCiAgICAgICAgICB7IHRpdGxlOiAi5L6b55S15bGA55S16KGo57yW5Y+3Iiwga2V5OiAic3VwcGx5YnVyZWF1YW1tZXRlcmNvZGUiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgc2NDb2x1bW46IFsNCiAgICAgICAgICB7IHRpdGxlOiAi55S16KGo5oi35Y+3L+WNj+iurue8lueggSIsIGtleTogImFtbWV0ZXJOYW1lIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuS+m+eUteWxgOeUteihqOe8luWPtyIsIGtleTogInN1cHBseWJ1cmVhdWFtbWV0ZXJjb2RlIiwgYWxpZ246ICJjZW50ZXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGRhdGE6IFtdLA0KICAgICAgICB0b3RhbDogMCwNCiAgICAgICAgZXhwb3J0Q29sdW1uczogWw0KICAgICAgICAgIHsgdGl0bGU6ICLpobnnm67lkI3np7AiLCBrZXk6ICJwcm9qZWN0TmFtZSIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi55S16KGo5oi35Y+3L+WNj+iurue8lueggSIsIGtleTogImFtbWV0ZXJOYW1lIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLkvpvnlLXlsYDnlLXooajnvJblj7ciLCBrZXk6ICJzdXBwbHlidXJlYXVhbW1ldGVyY29kZSIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5omA5bGe5YiG5YWs5Y+4Iiwga2V5OiAiY29tcGFueU5hbWUiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuaJgOWxnumDqOmXqCIsIGtleTogImNvdW50cnlOYW1lIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLlsYDnq5kiLCBrZXk6ICJzdGF0aW9uTmFtZSIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi6LW35aeL5pel5pyfIiwga2V5OiAic3RhcnRkYXRlIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLmiKrmraLml6XmnJ8iLCBrZXk6ICJlbmRkYXRlIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLnlKjnlLXph48o5bqmKSIsIGtleTogImN1cnVzZWRyZWFkaW5ncyIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi55S15Lu3KOWFgykiLCBrZXk6ICJ1bml0cGlyY2UiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuaZruelqOWQq+eojumHkeminSjlhYMpIiwga2V5OiAiaW5wdXR0aWNrZXRtb25leSIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5LiT56Wo5ZCr56iO6YeR6aKdKOWFgykiLCBrZXk6ICJpbnB1dHRheHRpY2tldG1vbmV5IiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLkuJPnpajnqI7njofvvIgl77yJIiwga2V5OiAidGF4cmF0ZSIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5LiT56Wo56iO6aKdIiwga2V5OiAidGF4YW1vdW50IiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLnlLXotLkiLCBrZXk6ICJhY2NvdW50bW9uZXkiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuWkh+azqCIsIGtleTogInJlbWFyayIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi55So55S157G75Z6LIiwga2V5OiAiZWxlY3Ryb3R5cGVuYW1lIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLnsbvlnovmj4/ov7AiLCBrZXk6ICJjYXRlZ29yeW5hbWUiIH0sDQogICAgICAgIF0sDQogICAgICB9LA0KICAgICAgcGFnZVRvdGFsOiAwLA0KICAgICAgcGFnZU51bTogMSwNCiAgICAgIHBhZ2VTaXplOiAxMCwgLy/lvZPliY3pobUNCiAgICB9Ow0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIHRoaXMuaGFuZGxlSGVpZ2h0KCk7IC8vdGFibGXpq5jluqboh6rlrprkuYkNCg0KICAgIHRoaXMudmVyc2lvbiA9IGluZGV4RGF0YS52ZXJzaW9uOw0KDQogICAgLy8gc3dpdGNoIChpbmRleERhdGEudmVyc2lvbikgew0KICAgIC8vICAgY2FzZSAic2MiOg0KICAgIHRoaXMudGJBY2NvdW50LmNvbHVtbnMgPSB0aGlzLnRiQWNjb3VudC5oZWFkQ29sdW1uDQogICAgICAuY29uY2F0KHRoaXMudGJBY2NvdW50LnNjQ29sdW1uKQ0KICAgICAgLmNvbmNhdCh0aGlzLnRiQWNjb3VudC50YWlsQ29sdW1uKTsNCiAgICAvLyAgICAgYnJlYWs7DQogICAgLy8gICBjYXNlICJsbiI6DQogICAgLy8gICAgIHRoaXMudGJBY2NvdW50LmNvbHVtbnMgPSB0aGlzLnRiQWNjb3VudC5oZWFkQ29sdW1uDQogICAgLy8gICAgICAgLmNvbmNhdCh0aGlzLnRiQWNjb3VudC5sbkNvbHVtbikNCiAgICAvLyAgICAgICAuY29uY2F0KHRoaXMudGJBY2NvdW50LnRhaWxDb2x1bW4pOw0KICAgIC8vICAgICBicmVhazsNCiAgICAvLyB9DQoNCiAgICB0aGlzLmFjY291bnRTdGF0dXMgPSBibGlzdCgiYWNjb3VudFN0YXR1cyIpOw0KICAgIHRoaXMuY2F0ZWdvcnlzID0gYmxpc3QoImFtbWV0ZXJDYXRlZ29yeSIpOw0KICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICBnZXRVc2VyQnlVc2VyUm9sZSgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgLy/moLnmja7mnYPpmZDojrflj5bliIblhazlj7gNCiAgICAgIHRoYXQuY29tcGFuaWVzID0gcmVzLmRhdGEuY29tcGFuaWVzOw0KICAgICAgaWYgKA0KICAgICAgICByZXMuZGF0YS5pc0NpdHlBZG1pbiA9PSB0cnVlIHx8DQogICAgICAgIHJlcy5kYXRhLmlzUHJvQWRtaW4gPT0gdHJ1ZSB8fA0KICAgICAgICByZXMuZGF0YS5pc1N1YkFkbWluID09IHRydWUNCiAgICAgICkgew0KICAgICAgICB0aGF0LmlzQWRtaW4gPSB0cnVlOw0KICAgICAgfQ0KICAgICAgZ2V0Q291bnRyeXNkYXRhKHsgb3JnQ29kZTogcmVzLmRhdGEuY29tcGFuaWVzWzBdLmlkIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAvL+agueaNruadg+mZkOiOt+WPluaJgOWxnumDqOmXqA0KICAgICAgICB0aGF0LmRlcGFydG1lbnRzID0gcmVzLmRhdGE7DQogICAgICAgIHRoYXQuZ2V0VXNlckRhdGEoKTsNCiAgICAgIH0pOw0KICAgIH0pOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgcHJlc2VydmVTYygpIHsNCiAgICAgIHRoaXMuJHJlZnMuY2hlY2tSZXN1bHQuYW1tZXRlcmlkcyA9IHRoaXMuYW1tZXRlcmlkczsNCiAgICAgIHRoaXMuc2hvd0poTW9kZWwgPSB0cnVlOw0KICAgIH0sDQogICAgaXNCdXR0b25sb2FkKHYpIHsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwuYnV0dG9ubG9hZCA9IHY7DQogICAgfSwNCiAgICAvL+S4gOmUruWIoOmZpOaVsOaNrg0KICAgIGRlbGV0ZUFsbCgpIHsNCiAgICAgIHRoaXMuJE1vZGFsLmNvbmZpcm0oew0KICAgICAgICB0aXRsZTogIuaPkOekuiIsDQogICAgICAgIGNvbnRlbnQ6ICI8cD7noa7lrprkuIDplK7liKDpmaTlkJfvvJ88L3A+IiwNCiAgICAgICAgb25PazogKCkgPT4gew0KICAgICAgICAgIHRoaXMudGJBY2NvdW50LmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgIGxldCBwYXJhbXMgPSB0aGlzLmFjY291bnRPYmo7DQogICAgICAgICAgcGFyYW1zLnJlbW92ZUFsbEZsYWcgPSB0cnVlOw0KICAgICAgICAgIGRlbGV0ZSBwYXJhbXMucGFnZVNpemU7DQogICAgICAgICAgZGVsZXRlIHBhcmFtcy5wYWdlTnVtOw0KICAgICAgICAgIHJlbW92ZUFsbChwYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgdGhpcy50YkFjY291bnQubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgaWYgKHJlcy5kYXRhLm51bSA+IDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5zdWNjZXNzKCLkuIDplK7liKDpmaTmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5zZWFyY2hMaXN0KCk7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmVycm9yKCLkuIDplK7liKDpmaTlpLHotKUiKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgb25DYW5jZWw6ICgpID0+IHt9LA0KICAgICAgfSk7DQogICAgfSwNCiAgICBzZWxlY3RDaGFuZ2UoKSB7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICBpZiAodGhhdC5hY2NvdW50T2JqLmNvbXBhbnkgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgIGlmICh0aGF0LmFjY291bnRPYmouY29tcGFueSA9PSAiLTEiKSB7DQogICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnkgPSAtMTsNCiAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeU5hbWUgPSBudWxsOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGdldENvdW50cnlCeVVzZXJJZCh0aGF0LmFjY291bnRPYmouY29tcGFueSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLmRhdGEuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApIHsNCiAgICAgICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnkgPSByZXMuZGF0YS5kZXBhcnRtZW50c1swXS5pZDsNCiAgICAgICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnlOYW1lID0gcmVzLmRhdGEuZGVwYXJ0bWVudHNbMF0ubmFtZTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jlvIDlp4sNCiAgICBjaG9vc2VSZXNwb25zZUNlbnRlcigpIHsNCiAgICAgIGlmICh0aGlzLmFjY291bnRPYmouY29tcGFueSA9PSBudWxsIHx8IHRoaXMuYWNjb3VudE9iai5jb21wYW55ID09ICItMSIpIHsNCiAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLor7flhYjpgInmi6nliIblhazlj7giKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy4kcmVmcy5jb3VudHJ5TW9kYWwuY2hvb3NlKHRoaXMuYWNjb3VudE9iai5jb21wYW55KTsgLy/miYDlsZ7pg6jpl6gNCiAgICB9LA0KICAgIGdldERhdGFGcm9tTW9kYWwoZGF0YSkgew0KICAgICAgdGhpcy5hY2NvdW50T2JqLmNvdW50cnkgPSBkYXRhLmlkOw0KICAgICAgdGhpcy5hY2NvdW50T2JqLmNvdW50cnlOYW1lID0gZGF0YS5uYW1lOw0KICAgICAgLy/pgInmi6nmiYDlsZ7pg6jpl6jnu5PmnZ8NCiAgICB9LA0KICAgIGdldFVzZXJEYXRhKCkgew0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgZ2V0VXNlcmRhdGEoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgLy/lvZPliY3nmbvlvZXnlKjmiLfmiYDlnKjlhazlj7jlkozmiYDlsZ7pg6jpl6gNCiAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllcy5sZW5ndGggIT0gMCkgew0KICAgICAgICAgIGxldCBjb21wYW5pZXMgPSByZXMuZGF0YS5jb21wYW5pZXM7DQogICAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllc1swXS5pZCA9PSAiMjYwMDAwMDAwMCIpIHsNCiAgICAgICAgICAgIGNvbXBhbmllcyA9IHRoYXQuY29tcGFuaWVzOw0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGF0LmNvbXBhbnkgPSBjb21wYW5pZXNbMF0uaWQ7DQogICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvbXBhbnkgPSBjb21wYW5pZXNbMF0uaWQ7DQogICAgICAgIH0NCiAgICAgICAgaWYgKHJlcy5kYXRhLmRlcGFydG1lbnRzLmxlbmd0aCAhPSAwKSB7DQogICAgICAgICAgbGV0IGRlcGFydG1lbnRzID0gcmVzLmRhdGEuZGVwYXJ0bWVudHM7DQogICAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllc1swXS5pZCA9PSAiMjYwMDAwMDAwMCIgJiYgdGhhdC5kZXBhcnRtZW50cy5sZW5ndGggIT0gMCkgew0KICAgICAgICAgICAgZGVwYXJ0bWVudHMgPSB0aGF0LmRlcGFydG1lbnRzOw0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGF0LmNvdW50cnkgPSBkZXBhcnRtZW50c1swXS5pZDsNCiAgICAgICAgICB0aGF0LmNvdW50cnlOYW1lID0gZGVwYXJ0bWVudHNbMF0ubmFtZTsNCiAgICAgICAgICB0aGF0LmFjY291bnRPYmouY291bnRyeSA9IE51bWJlcihkZXBhcnRtZW50c1swXS5pZCk7DQogICAgICAgICAgdGhhdC5hY2NvdW50T2JqLmNvdW50cnlOYW1lID0gZGVwYXJ0bWVudHNbMF0ubmFtZTsNCiAgICAgICAgfQ0KDQogICAgICAgIHRoYXQucGFnZU51bSA9IDE7DQogICAgICAgIHRoYXQuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIHNlYXJjaExpc3QoKSB7DQogICAgICBpZiAodGhpcy5hY2NvdW50T2JqLmNvdW50cnlOYW1lID09ICIiKSB7DQogICAgICAgIHRoaXMuYWNjb3VudE9iai5jb3VudHJ5ID0gIi0xIjsNCiAgICAgIH0NCiAgICAgIHRoaXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgIH0sDQogICAgYWNjb3VudG5vQ2hhbmdlKCkgew0KICAgICAgdGhpcy5zZWFyY2hMaXN0KCk7DQogICAgfSwNCiAgICBzZXRBbW1ldGVyRGF0YTogZnVuY3Rpb24gKGRhdGEpIHsNCiAgICAgIGxldCBhcnJheURhdGEgPSBbXTsNCiAgICAgIGxldCBubyA9IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm87DQogICAgICBpZiAoZGF0YSAhPSBudWxsICYmIGRhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICBsZXQgb2JqID0ge307DQogICAgICAgICAgb2JqLnBjaWQgPSBudWxsOw0KICAgICAgICAgIG9iai5hbW1ldGVyTmFtZSA9IGl0ZW0uYW1tZXRlcm5hbWU7DQogICAgICAgICAgb2JqLnByb2plY3ROYW1lID0gaXRlbS5wcm9qZWN0bmFtZTsNCiAgICAgICAgICBvYmouc3Vic3RhdGlvbiA9IGl0ZW0uc3Vic3RhdGlvbjsNCiAgICAgICAgICBvYmouY2F0ZWdvcnluYW1lID0gaXRlbS5jYXRlZ29yeW5hbWU7DQogICAgICAgICAgb2JqLmNhdGVnb3J5ID0gaXRlbS5jYXRlZ29yeTsNCiAgICAgICAgICBvYmouYW1tZXRlcmlkID0gaXRlbS5hbW1ldGVyaWQ7DQogICAgICAgICAgb2JqLmNvbXBhbnkgPSBpdGVtLmNvbXBhbnk7DQogICAgICAgICAgb2JqLmNvbXBhbnlOYW1lID0gaXRlbS5jb21wYW55TmFtZTsNCiAgICAgICAgICBvYmouY291bnRyeSA9IGl0ZW0uY291bnRyeTsNCiAgICAgICAgICBvYmouY291bnRyeU5hbWUgPSBpdGVtLmNvdW50cnlOYW1lOw0KICAgICAgICAgIG9iai5zdGFydGRhdGUgPSBudWxsOw0KICAgICAgICAgIG9iai5lbmRkYXRlID0gbnVsbDsNCiAgICAgICAgICBvYmouY3VydXNlZHJlYWRpbmdzID0gMDsNCiAgICAgICAgICBvYmoudHJhbnNmb3JtZXJ1bGxhZ2UgPSAwOw0KICAgICAgICAgIG9iai51bml0cGlyY2UgPSAwOw0KICAgICAgICAgIG9iai5pbnB1dHRpY2tldG1vbmV5ID0gMDsNCiAgICAgICAgICBvYmouaW5wdXR0YXh0aWNrZXRtb25leSA9IDA7DQogICAgICAgICAgb2JqLnRheHJhdGUgPSAiMTMiOw0KICAgICAgICAgIG9iai50YXhhbW91bnQgPSAwOw0KICAgICAgICAgIG9iai5hY2NvdW50bW9uZXkgPSAwOw0KICAgICAgICAgIG9iai5yZW1hcmsgPSBudWxsOw0KICAgICAgICAgIG9iai5lbGVjdHJvdHlwZW5hbWUgPSBpdGVtLmVsZWN0cm90eXBlbmFtZTsNCiAgICAgICAgICBvYmouc3RhdGlvbk5hbWUgPSBpdGVtLnN0YXRpb25OYW1lOw0KICAgICAgICAgIG9iai5zdGFydGRhdGUgPSBnZXRGaXJzdERhdGVCeUFjY291bnRub195eXl5bW1kZChubyk7DQogICAgICAgICAgb2JqLmVuZGRhdGUgPSBnZXRMYXN0RGF0ZUJ5QWNjb3VudG5vX3l5eXltbWRkKG5vKTsNCiAgICAgICAgICBvYmouYWNjb3VudGVzdHlwZSA9IDI7DQogICAgICAgICAgb2JqLnN1cHBseWJ1cmVhdWFtbWV0ZXJjb2RlID0gaXRlbS5zdXBwbHlidXJlYXVhbW1ldGVyY29kZTsNCiAgICAgICAgICBvYmouZWxlY3Ryb3R5cGUgPSBpdGVtLmVsZWN0cm90eXBlOw0KICAgICAgICAgIG9iai5zdGF0aW9uY29kZTVnciA9IGl0ZW0uc3RhdGlvbmNvZGU1Z3I7DQogICAgICAgICAgb2JqLnN0YXRpb25uYW1lNWdyID0gaXRlbS5zdGF0aW9ubmFtZTVncjsNCiAgICAgICAgICBhcnJheURhdGEucHVzaChvYmopOw0KICAgICAgICB9KTsNCiAgICAgIH0NCg0KICAgICAgbGV0IHZlcnNpb24gPSBpbmRleERhdGEudmVyc2lvbjsNCiAgICAgIGxldCBvcmlnaW4gPSB0aGlzLnRiQWNjb3VudC5kYXRhOw0KICAgICAgaWYgKG9yaWdpbi5sZW5ndGggPCAxKSB7DQogICAgICAgIHRoaXMudGJBY2NvdW50LmRhdGEgPSBhcnJheURhdGE7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgdGVtID0gYXJyYXlEYXRhOw0KICAgICAgICBpZiAoInNjIiA9PSB2ZXJzaW9uKSB7DQogICAgICAgICAgb3JpZ2luLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgIGZvciAobGV0IGogPSB0ZW0ubGVuZ3RoIC0gMTsgaiA+PSAwOyBqLS0pIHsNCiAgICAgICAgICAgICAgbGV0IGpqID0gdGVtW2pdOw0KICAgICAgICAgICAgICBpZiAoaXRlbS5hbW1ldGVyaWQgPT09IGpqLmFtbWV0ZXJpZCkgew0KICAgICAgICAgICAgICAgIHRlbS5zcGxpY2UoaiwgMSk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgICBsZXQgdG90YWwgPSB0aGlzLnBhZ2VUb3RhbDsNCiAgICAgICAgdGhpcy5wYWdlVG90YWwgPSB0b3RhbCArIHRlbS5sZW5ndGg7DQogICAgICAgIHRoaXMudGJBY2NvdW50LmRhdGEgPSB0ZW0uY29uY2F0KHRoaXMudGJBY2NvdW50LmRhdGEpOw0KICAgICAgfQ0KICAgICAgdGhpcy5zZXRNeVN0eWxlKHRoaXMudGJBY2NvdW50LmRhdGEubGVuZ3RoKTsNCiAgICB9LA0KICAgIC8v54K55Ye75L+d5a2YDQogICAgYXN5bmMgcHJlc2VydmUoKSB7DQogICAgICBsZXQgZGF0YUwgPSB0aGlzLiRyZWZzLmFjY291bnRFc1RhYmxlLmdldFNlbGVjdGlvbigpOw0KDQogICAgICBsZXQgYiA9IGZhbHNlOw0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhOw0KICAgICAgbGV0IGFycmF5ID0gW107DQogICAgICBsZXQgdmVyc2lvbiA9IGluZGV4RGF0YS52ZXJzaW9uOw0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhTC5sZW5ndGg7IGkrKykgew0KICAgICAgICBpZiAoZGF0YUxbaV0uZWRpdFR5cGUgPT0gMSkgew0KICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICJzYyIgPT0gdmVyc2lvbiAmJg0KICAgICAgICAgICAgZGF0YUxbaV0uZWxlY3Ryb3R5cGUgJiYNCiAgICAgICAgICAgIGRhdGFMW2ldLmVsZWN0cm90eXBlID4gMTQwMCAmJg0KICAgICAgICAgICAgKGRhdGFMW2ldLnN0YXRpb25jb2RlNWdyID09IG51bGwgfHwNCiAgICAgICAgICAgICAgZGF0YUxbaV0uc3RhdGlvbmNvZGU1Z3IgPT0gdW5kZWZpbmVkIHx8DQogICAgICAgICAgICAgIGRhdGFMW2ldLnN0YXRpb25jb2RlNWdyID09ICIiKQ0KICAgICAgICAgICkgew0KICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoDQogICAgICAgICAgICAgICLnlLXooagv5Y2P6K6u57yW5Y+344CQIiArDQogICAgICAgICAgICAgICAgZGF0YUxbaV0uYW1tZXRlck5hbWUgKw0KICAgICAgICAgICAgICAgICLjgJHvvIzpobnnm67lkI3np7DjgJAiICsNCiAgICAgICAgICAgICAgICBkYXRhTFtpXS5wcm9qZWN0TmFtZSArDQogICAgICAgICAgICAgICAgIuOAkeWFs+iBlOWxgOermeeahDVHUuermeWdgOS4uuepuu+8jOivt+WujOWWhOWxgOermeS/oeaBr++8jOaIluiAhTVHUuacieaViOaAp+a4heWNleWkseaViO+8jOivt+iBlOezu+aXoOe6v+euoeeQhuWRmOOAgiINCiAgICAgICAgICAgICk7DQogICAgICAgICAgfQ0KICAgICAgICAgIC8v5oiq5q2i5pel5pyf5qCh6aqMDQogICAgICAgICAgbGV0IHJlc3VsdCA9IF92ZXJpZnlfRW5kRGF0ZShkYXRhTFtpXSwgZGF0YUxbaV0uZW5kZGF0ZSk7DQogICAgICAgICAgaWYgKHJlc3VsdCkgew0KICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMocmVzdWx0KTsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQogICAgICAgICAgYiA9IHRydWU7DQogICAgICAgICAgYXJyYXkucHVzaChkYXRhTFtpXSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC8vIH0pOw0KICAgICAgaWYgKGIpIHsNCiAgICAgICAgdGhpcy5zdWJtaXREYXRhKGFycmF5KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLmsqHmnInlj6/kv53lrZjmlbDmja4iKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8v5o+Q5Lqk5pWw5o2uDQogICAgc3VibWl0RGF0YShkYXRhKSB7DQogICAgICBsZXQgdmVyc2lvbiA9IGluZGV4RGF0YS52ZXJzaW9uOw0KICAgICAgaWYgKGRhdGEgIT0gbnVsbCAmJiBkYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbGV0IG51bWJlciA9IDA7DQogICAgICAgIGxldCBzdWJtaXREYXRhID0gW107DQogICAgICAgIGxldCBzdHIgPSAiIjsNCiAgICAgICAgbGV0IGFjY291bnRubyA9IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm87DQogICAgICAgIGxldCBpbmRleCA9IDA7DQogICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgIGlmIChpdGVtLnByb2plY3ROYW1lICE9ICLlsI/orqEiICYmIGl0ZW0ucHJvamVjdE5hbWUgIT0gIuWQiOiuoSIpIHsNCiAgICAgICAgICAgIGxldCBvYmogPSB2ZXJpZmljYXRpb24oaXRlbSk7DQogICAgICAgICAgICBpZiAob2JqLnJlc3VsdCkgew0KICAgICAgICAgICAgICBpZiAoaXRlbS5wY2lkID09IG51bGwpIHsNCiAgICAgICAgICAgICAgICBpdGVtLmFjY291bnRubyA9IGFjY291bnRubzsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBzdWJtaXREYXRhLnB1c2goaXRlbSk7DQogICAgICAgICAgICAgIG51bWJlcisrOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgc3RyICs9DQogICAgICAgICAgICAgICAgIueUteihqC/ljY/orq7nvJblj7fkuLrjgJAiICsNCiAgICAgICAgICAgICAgICBpdGVtLmFtbWV0ZXJOYW1lICsNCiAgICAgICAgICAgICAgICAi44CR55qE5Y+w6LSm6aqM6K+B5rKh5pyJ6YCa6L+H77ya44CQIiArDQogICAgICAgICAgICAgICAgb2JqLnN0ciArDQogICAgICAgICAgICAgICAgIuOAke+8myI7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgICAgaWYgKHN0ci5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoc3RyKTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoc3VibWl0RGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgYWRkQWNjb3VudEVzKHN1Ym1pdERhdGEpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgaWYgKHJlcy5kYXRhLmNvZGUgPT0gMCkgew0KICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oew0KICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICLmj5DnpLrvvJrmiJDlip/kv53lrZggIiArIHN1Ym1pdERhdGEubGVuZ3RoICsgIiDmnaHmlbDmja4iLA0KICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxMCwNCiAgICAgICAgICAgICAgICBjbG9zYWJsZTogdHJ1ZSwNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIGFkZEVsZWN0cmljVHlwZSgpIHsNCiAgICAgIGxldCBjb21wYW55SWQgPSB0aGlzLmFjY291bnRPYmouY29tcGFueTsNCiAgICAgIGxldCBjb3VudHJ5ID0gdGhpcy5hY2NvdW50T2JqLmNvdW50cnk7DQogICAgICBpZiAoY29tcGFueUlkICE9IG51bGwgJiYgY291bnRyeSAhPSBudWxsKSB7DQogICAgICAgIGxldCBvYmogPSB7DQogICAgICAgICAgY29tcGFueTogY29tcGFueUlkLA0KICAgICAgICAgIGNvdW50cnk6IGNvdW50cnksDQogICAgICAgICAgYWNjb3VudG5vOiB0aGlzLmFjY291bnRPYmouYWNjb3VudG5vLA0KICAgICAgICAgIGFjY291bnRUeXBlOiAiMSIsDQogICAgICAgICAgYWNjb3VudGVzdHlwZTogMiwNCiAgICAgICAgfTsNCiAgICAgICAgdGhpcy4kcmVmcy5zZWxlY3RBbW1ldGVyLmluaXRBbW1ldGVyKG9iaik7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmVycm9yVGlwcygi6K+36YCJ5oup5YiG5YWs5Y+45ZKM6YOo6ZeoIik7DQogICAgICB9DQogICAgfSwNCiAgICAvL+mqjOivgemUmeivr+W8ueWHuuaPkOekuuahhg0KICAgIGVycm9yVGlwcyhzdHIpIHsNCiAgICAgIHRoaXMuJE5vdGljZS5lcnJvcih7DQogICAgICAgIHRpdGxlOiAi5o+Q56S6IiwNCiAgICAgICAgZGVzYzogc3RyLA0KICAgICAgICBkdXJhdGlvbjogMTAsDQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZVBhZ2UodmFsdWUpIHsNCiAgICAgIGxldCBiID0gZmFsc2U7DQogICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGE7DQogICAgICBsZXQgYXJyYXkgPSBbXTsNCiAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICBpZiAoaXRlbS5lZGl0VHlwZSA9PSAxKSB7DQogICAgICAgICAgYiA9IHRydWU7DQogICAgICAgICAgYXJyYXkucHVzaChpdGVtKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICBpZiAoYikgew0KICAgICAgICB0aGlzLiRNb2RhbC5jb25maXJtKHsNCiAgICAgICAgICB0aXRsZTogIuaPkOekuiIsDQogICAgICAgICAgY29udGVudDogIjxwPuaCqOacieW3sue8lui+keS/oeaBr+i/mOayoeacieS/neWtmO+8jOaYr+WQpuS/neWtmO+8nzwvcD4iLA0KICAgICAgICAgIG9uT2s6ICgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuc3VibWl0RGF0YShhcnJheSk7DQogICAgICAgICAgfSwNCiAgICAgICAgICBvbkNhbmNlbDogKCkgPT4ge30sDQogICAgICAgIH0pOw0KICAgICAgfQ0KDQogICAgICB0aGlzLnBhZ2VOdW0gPSB2YWx1ZTsNCiAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgfSwNCiAgICBoYW5kbGVQYWdlU2l6ZSh2YWx1ZSkgew0KICAgICAgbGV0IGIgPSBmYWxzZTsNCiAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YTsNCiAgICAgIGxldCBhcnJheSA9IFtdOw0KICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgIGlmIChpdGVtLmVkaXRUeXBlID09IDEpIHsNCiAgICAgICAgICBiID0gdHJ1ZTsNCiAgICAgICAgICBhcnJheS5wdXNoKGl0ZW0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIGlmIChiKSB7DQogICAgICAgIHRoaXMuJE1vZGFsLmNvbmZpcm0oew0KICAgICAgICAgIHRpdGxlOiAi5o+Q56S6IiwNCiAgICAgICAgICBjb250ZW50OiAiPHA+5oKo5pyJ5bey57yW6L6R5L+h5oGv6L+Y5rKh5pyJ5L+d5a2Y77yM5piv5ZCm5L+d5a2Y77yfPC9wPiIsDQogICAgICAgICAgb25PazogKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5zdWJtaXREYXRhKGFycmF5KTsNCiAgICAgICAgICB9LA0KICAgICAgICAgIG9uQ2FuY2VsOiAoKSA9PiB7fSwNCiAgICAgICAgfSk7DQogICAgICB9DQoNCiAgICAgIHRoaXMucGFnZVNpemUgPSB2YWx1ZTsNCiAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgfSwNCiAgICAvL+WQkeWQjuWPsOivt+axguaVsOaNrg0KICAgIGdldEFjY291bnRNZXNzYWdlcygpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB0aGlzLmFjY291bnRPYmo7DQogICAgICBwYXJhbXMucGFnZU51bSA9IHRoaXMucGFnZU51bTsNCiAgICAgIHBhcmFtcy5wYWdlU2l6ZSA9IHRoaXMucGFnZVNpemU7DQogICAgICBsZXQgcmVxID0gew0KICAgICAgICB1cmw6ICIvYnVzaW5lc3MvYWNjb3VudEVzL3NlbGVjdEFjY291bnRFc0xpc3QiLA0KICAgICAgICBtZXRob2Q6ICJnZXQiLA0KICAgICAgICBwYXJhbXM6IHBhcmFtcywNCiAgICAgIH07DQogICAgICB0aGlzLnRiQWNjb3VudC5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGF4aW9zDQogICAgICAgIC5yZXF1ZXN0KHJlcSkNCiAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIHRoaXMudGJBY2NvdW50LmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICBpZiAocmVzLmRhdGEpIHsNCiAgICAgICAgICAgIGxldCBkYXRhID0gcmVzLmRhdGEucm93czsNCiAgICAgICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgICAgICBpdGVtLmVkaXRUeXBlID0gMDsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgZGF0YS5wdXNoKHRoaXMuc3VudG90YWwoZGF0YSkpOyAvL+Wwj+iuoQ0KICAgICAgICAgICAgYWNjb3VudEVzVG90YWwodGhpcy5hY2NvdW50T2JqKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgLy/lkIjorqENCiAgICAgICAgICAgICAgbGV0IGFsbHRvdGFsID0gcmVzLmRhdGE7DQogICAgICAgICAgICAgIGFsbHRvdGFsLnRvdGFsID0gIuWQiOiuoSI7DQogICAgICAgICAgICAgIGFsbHRvdGFsLnByb2plY3ROYW1lID0gIuWQiOiuoSI7DQogICAgICAgICAgICAgIGFsbHRvdGFsLl9kaXNhYmxlZCA9IHRydWU7DQogICAgICAgICAgICAgIGRhdGEucHVzaChhbGx0b3RhbCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHRoaXMudGJBY2NvdW50LmRhdGEgPSBkYXRhOw0KICAgICAgICAgICAgdGhpcy5wYWdlVG90YWwgPSByZXMuZGF0YS50b3RhbCB8fCAwOw0KICAgICAgICAgICAgdGhpcy5zZXRNeVN0eWxlKHRoaXMudGJBY2NvdW50LmRhdGEubGVuZ3RoKTsNCg0KICAgICAgICAgICAgdGhpcy5lZGl0SW5kZXggPSAtMTsNCiAgICAgICAgICAgIHRoaXMuY29sdW1uc0luZGV4ID0gLTE7DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKGVycikgPT4gew0KICAgICAgICAgIGNvbnNvbGUubG9nKGVycik7DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgLy/lsI/orqENCiAgICBzdW50b3RhbChhcnJheSkgew0KICAgICAgbGV0IGN1cnVzZWRyZWFkaW5ncyA9IDA7DQogICAgICBsZXQgdHJhbnNmb3JtZXJ1bGxhZ2UgPSAwOw0KICAgICAgbGV0IGFjY291bnRtb25leSA9IDA7DQogICAgICBhcnJheS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgIGlmIChpdGVtLmVmZmVjdGl2ZSA9PT0gMSkgew0KICAgICAgICAgIGN1cnVzZWRyZWFkaW5ncyArPSBpdGVtLmN1cnVzZWRyZWFkaW5nczsNCiAgICAgICAgICB0cmFuc2Zvcm1lcnVsbGFnZSArPSBpdGVtLnRyYW5zZm9ybWVydWxsYWdlOw0KICAgICAgICAgIGFjY291bnRtb25leSArPSBpdGVtLmFjY291bnRtb25leTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICByZXR1cm4gew0KICAgICAgICBjdXJ1c2VkcmVhZGluZ3M6IGN1cnVzZWRyZWFkaW5ncywNCiAgICAgICAgdHJhbnNmb3JtZXJ1bGxhZ2U6IHRyYW5zZm9ybWVydWxsYWdlLA0KICAgICAgICBhY2NvdW50bW9uZXk6IGFjY291bnRtb25leSwNCiAgICAgICAgdG90YWw6ICLlsI/orqEiLA0KICAgICAgICBwcm9qZWN0TmFtZTogIuWwj+iuoSIsDQogICAgICAgIF9kaXNhYmxlZDogdHJ1ZSwNCiAgICAgIH07DQogICAgfSwNCiAgICAvL+mHjee9rg0KICAgIG9uUmVzZXRIYW5kbGUoKSB7DQogICAgICB0aGlzLmFjY291bnRPYmogPSB7DQogICAgICAgIGFjY291bnRubzogZGF0ZXNbMF0uY29kZSwgLy/mnJ/lj7cs6buY6K6k5b2T5YmN5pyIDQogICAgICAgIGNvbXBhbnk6IHRoaXMuY29tcGFueSwNCiAgICAgICAgcHJvamVjdE5hbWU6ICIiLCAvL+mhueebruWQjeensA0KICAgICAgICBjb3VudHJ5OiBOdW1iZXIodGhpcy5jb3VudHJ5KSwgLy/miYDlsZ7pg6jpl6gNCiAgICAgICAgYW1tZXRlck5hbWU6ICIiLCAvL+eUteihqOaIt+WPty/ljY/orq7nvJbnoIENCiAgICAgICAgc3RhdGlvbk5hbWU6ICIiLA0KICAgICAgICBhY2NvdW50VHlwZTogIjEiLCAvL+WPsOi0puexu+Weiw0KICAgICAgICBhY2NvdW50ZXN0eXBlOiAzLCAvL+WPsOi0puexu+Weiw0KICAgICAgICBzdXBwbHlidXJlYXVhbW1ldGVyY29kZTogIiIsDQogICAgICAgIGNvdW50cnlOYW1lOiB0aGlzLmNvdW50cnlOYW1lLA0KICAgICAgfTsNCiAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgfSwNCiAgICAvL+iuoeeul+WNleS7tw0KICAgIHVuaXRQcmljZShyb3cpIHsNCiAgICAgIGxldCB2ZXJzaW9uID0gaW5kZXhEYXRhLnZlcnNpb247DQogICAgICBsZXQgYWNjb3VudG1vbmV5ID0gcm93LmFjY291bnRtb25leTsNCiAgICAgIGxldCBjdXJ1c2VkcmVhZGluZ3MgPSByb3cuY3VydXNlZHJlYWRpbmdzOw0KICAgICAgbGV0IHRheGFtb3VudCA9IHJvdy50YXhhbW91bnQ7DQogICAgICBpZiAoYWNjb3VudG1vbmV5ICE9IG51bGwgJiYgY3VydXNlZHJlYWRpbmdzICE9IG51bGwpIHsNCiAgICAgICAgbGV0IHRvdGFsID0gbnVsbDsNCiAgICAgICAgaWYgKGN1cnVzZWRyZWFkaW5ncyA9PSAwKSB7DQogICAgICAgICAgdG90YWwgPSAwOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIGlmICgibG4iID09IHRoaXMudmVyc2lvbikgdG90YWwgPSAoYWNjb3VudG1vbmV5IC0gdGF4YW1vdW50KSAvIGN1cnVzZWRyZWFkaW5nczsNCiAgICAgICAgICAvLyBlbHNlDQogICAgICAgICAgdG90YWwgPSBhY2NvdW50bW9uZXkgLyBjdXJ1c2VkcmVhZGluZ3M7DQogICAgICAgIH0NCg0KICAgICAgICByb3cudW5pdHBpcmNlID0gdG90YWwudG9GaXhlZCgyKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8v6aqM6K+B5Y2V5Lu3DQogICAgdmFsaWRhdGVVbml0UHJpY2UoZGF0YSkgew0KICAgICAgbGV0IGNhdGVnb3J5ID0gZGF0YS5jYXRlZ29yeTsgLy/nlLXooajmj4/ov7DnsbvlnosNCiAgICAgIGxldCBhbW1ldGVydXNlID0gZGF0YS5hbW1ldGVydXNlOyAvL+eUteihqOeUqOmAlA0KICAgICAgbGV0IHVuaXRwaXJjZSA9IGRhdGEudW5pdHBpcmNlOyAvL+WPsOi0puWNleS7tw0KICAgICAgLy9pZighanVkZ2VfbmVnYXRlKGNhdGVnb3J5KSAmJiAhanVkZ2VfcmVjb3ZlcnkoYW1tZXRlcnVzZSkpDQogICAgICB7DQogICAgICAgIC8vIGlmICh1bml0cGlyY2UpIHsNCiAgICAgICAgLy8gICBpZiAodW5pdHBpcmNlIDwgdW5pdHBpcmNlTWluIHx8IHVuaXRwaXJjZSA+IHVuaXRwaXJjZU1heCkgew0KICAgICAgICAvLyAgICAgdGhpcy5lcnJvclRpcHMoDQogICAgICAgIC8vICAgICAgICLpm4blm6LopoHmsYLljZXku7fojIPlm7TlnKgwLjN+MuWFg++8jOatpOWPsOi0puWNleS7tzogIiArDQogICAgICAgIC8vICAgICAgICAgdW5pdHBpcmNlICsNCiAgICAgICAgLy8gICAgICAgICAiIOW3sui2hei/h+iMg+WbtO+8jOivt+ehruiupO+8gSINCiAgICAgICAgLy8gICAgICk7DQogICAgICAgIC8vICAgfQ0KICAgICAgICAvLyB9DQogICAgICAgIGlmICh1bml0cGlyY2UpIHsNCiAgICAgICAgICBpZiAodW5pdHBpcmNlICE9IG51bGwgJiYgdW5pdHBpcmNlIDwgdW5pdHBpcmNlTWF4MSkgew0KICAgICAgICAgICAgLy8gaWYgKHVuaXRwaXJjZSA8IHVuaXRwaXJjZU1pbiB8fCB1bml0cGlyY2UgPiB1bml0cGlyY2VNYXgpIHsNCiAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKA0KICAgICAgICAgICAgICAi5Y2V5Lu36IyD5Zu05b+F6aG75aSn5LqOMC4x5YWD77yM5q2k5Y+w6LSm5Y2V5Lu3OiAiICsgdW5pdHBpcmNlICsgIuS4jeWcqOiMg+WbtOWGhe+8jOivt+ehruiupO+8gSINCiAgICAgICAgICAgICk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICByZW1vdmUoKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMuJHJlZnMuYWNjb3VudEVzVGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICBpZiAoZGF0YSA9PSBudWxsIHx8IGRhdGEubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fpgInmi6nopoHliKDpmaTnmoTmlbDmja4iKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7DQogICAgICAgIHRpdGxlOiAi5o+Q56S6IiwNCiAgICAgICAgY29udGVudDogIjxwPuaYr+WQpuehruiupOWIoOmZpOmAieS4reS/oeaBr++8nzwvcD4iLA0KICAgICAgICBvbk9rOiAoKSA9PiB7DQogICAgICAgICAgbGV0IGIgPSB0cnVlOw0KICAgICAgICAgIGxldCBpZHMgPSAiIjsNCiAgICAgICAgICBsZXQgYXJyYXkgPSB0aGlzLnRiQWNjb3VudC5kYXRhOw0KICAgICAgICAgIGxldCB0b3RhbCA9IHRoaXMucGFnZVRvdGFsOw0KICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGF0YS5sZW5ndGg7IGkrKykgew0KICAgICAgICAgICAgbGV0IGl0ZW0gPSBkYXRhW2ldOw0KICAgICAgICAgICAgaWYgKGl0ZW0ucGNpZCAhPSBudWxsICYmIGl0ZW0ucGNpZC5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIGlmIChpdGVtLnBhYnJpaWQpIHsNCiAgICAgICAgICAgICAgICBiID0gZmFsc2U7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgaWRzICs9IGl0ZW0ucGNpZCArICIsIjsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIGZvciAobGV0IGogPSBhcnJheS5sZW5ndGggLSAxOyBqID49IDA7IGotLSkgew0KICAgICAgICAgICAgICAgIGxldCBqaiA9IGFycmF5W2pdOw0KICAgICAgICAgICAgICAgIGlmIChqai5hbW1ldGVyaWQgPT09IGl0ZW0uYW1tZXRlcmlkKSB7DQogICAgICAgICAgICAgICAgICBhcnJheS5zcGxpY2UoaiwgMSk7DQogICAgICAgICAgICAgICAgICB0b3RhbCA9IHRvdGFsIC0gMTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5wYWdlVG90YWwgPSB0b3RhbDsNCiAgICAgICAgICBpZiAoYikgew0KICAgICAgICAgICAgaWYgKGlkcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIHJlbW92ZUFjY291bnRFcyhpZHMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YS5jb2RlID09IDApIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2Uuc3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCLpgInkuK3kv6Hmga/kuK3mnInkv6Hmga/ov5jmsqHmnInot5/lvZLpm4bljZXop6PpmaTlhbPogZTvvIzor7flhYjop6PpmaTlhbPogZQiKTsNCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIG9uQ2FuY2VsOiAoKSA9PiB7fSwNCiAgICAgIH0pOw0KICAgIH0sDQogICAgb3BlbkFkZEJpbGxQZXJNb2RhbChuYW1lKSB7DQogICAgICBpZiAobmFtZSA9PT0gImN1cnJlbnQiKSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRBY2NvdW50KCk7DQogICAgICB9IGVsc2UgaWYgKG5hbWUgPT09ICJhbGwiKSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRBbGxBY2NvdW50KCk7DQogICAgICB9DQogICAgfSwNCiAgICAvL+WKoOWFpeW9kumbhuWNle+8jOWFqOmDqOacieaViOWPsOi0pg0KICAgIHNlbGVjdGVkQWxsQWNjb3VudCgpIHsNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIHRoYXQuc3BpblNob3cgPSB0cnVlOw0KICAgICAgc2VsZWN0SWRzQnlFc1BhcmFtcyh0aGlzLmFjY291bnRPYmopLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGF0LnNwaW5TaG93ID0gZmFsc2U7DQogICAgICAgIGlmIChyZXMuZGF0YS5sZW5ndGggPT0gMCkgew0KICAgICAgICAgIHRoYXQuZXJyb3JUaXBzKCLml6DmnInmlYjmlbDmja7lj6/liqDlhaXlvZLpm4bljZUiKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGF0LiRyZWZzLmFkZEJpbGxQZXIuaW5pdEFtbWV0ZXIocmVzLmRhdGEsIDExLCB0aGlzLmFjY291bnRPYmouY291bnRyeSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgc2VsZWN0ZWRBY2NvdW50KCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLiRyZWZzLmFjY291bnRFc1RhYmxlLmdldFNlbGVjdGlvbigpOw0KICAgICAgbGV0IGIgPSAxOw0KICAgICAgaWYgKGRhdGEgPT0gbnVsbCB8fCBkYXRhLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fpgInmi6nopoHliqDlhaXlvZLpm4bljZXnmoTlj7DotKYiKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldCBpZHMgPSBbXTsNCiAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgaWYgKGl0ZW0uZWZmZWN0aXZlICE9IDEpIHsNCiAgICAgICAgICAgIGIgPSAyOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoaXRlbS5zdGF0dXMgPT09IDUpIHsNCiAgICAgICAgICAgIGIgPSAzOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoaXRlbS5zdGF0dXMgPT09IDQpIHsNCiAgICAgICAgICAgIGIgPSA0Ow0KICAgICAgICAgIH0NCiAgICAgICAgICBpZHMucHVzaChpdGVtLnBjaWQpOw0KICAgICAgICB9KTsNCiAgICAgICAgaWYgKGIgPT09IDEpIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLmFkZEJpbGxQZXIuaW5pdEFtbWV0ZXIoaWRzLCAxMSwgdGhpcy5hY2NvdW50T2JqLmNvdW50cnkpOw0KICAgICAgICB9IGVsc2UgaWYgKGIgPT09IDIpIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6YCJ5Lit55qE5Y+w6LSm5Lit5a2Y5Zyo5Li05pe25pWw5o2u77yM6K+35YWI5L+d5a2Y5YaN5Yqg5YWl5b2S6ZuG5Y2V77yBIik7DQogICAgICAgIH0gZWxzZSBpZiAoYiA9PT0gMykgew0KICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCLpgIDlm57nmoTlj7DotKbkuI3og73liqDlhaXlhbblroPlvZLpm4bljZXvvIzor7fngrnlh7tb6YeN5paw5Yqg5YWl5b2S6ZuG5Y2VXeaMiemSriIpOw0KICAgICAgICB9IGVsc2UgaWYgKGIgPT09IDQpIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6YCJ5oup55qE5Y+w6LSm5pyJ5bey5Yqg5YWl5b2S6ZuG5Y2V55qE5Y+w6LSm77yM5LiN6IO95Yqg5YWl5YW25LuW5b2S6ZuG5Y2VIik7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqDQogICAgICog6ZyA6KaB56i95qC4DQogICAgICovDQogICAgYWRkUHJlc2VydmVHSigpIHsNCiAgICAgIGxldCBkYXRhTCA9IHRoaXMuJHJlZnMuYWNjb3VudEVzVGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICBpZiAoZGF0YUwgPT0gbnVsbCB8fCBkYXRhTC5sZW5ndGggPT0gMCkgew0KICAgICAgICB0aGlzLmVycm9yVGlwcygi6K+36YCJ5oup6KaB5Yqg5YWl5b2S6ZuG5Y2V55qE5Y+w6LSmIik7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmFkZFN1Ym1pdERhdGFHSihkYXRhTCk7DQogICAgICB9DQogICAgfSwNCiAgICBhZGRQcmVzZXJ2ZUdKQWxsKCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHRoaXMuYWNjb3VudE9iajsNCiAgICAgIHBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHBhcmFtcy5wYWdlU2l6ZSA9IDIwMDAwOw0KICAgICAgbGV0IHJlcSA9IHsNCiAgICAgICAgdXJsOiAiL2J1c2luZXNzL2FjY291bnQvc2VsZkFjY291bnRMaXN0IiwNCiAgICAgICAgbWV0aG9kOiAiZ2V0IiwNCiAgICAgICAgcGFyYW1zOiBwYXJhbXMsDQogICAgICB9Ow0KICAgICAgbGV0IGFycmF5ID0gW107DQogICAgICB0aGlzLmFjY291bnRUYi5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGF4aW9zLnJlcXVlc3QocmVxKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgbGV0IGRhdGFMID0gcmVzLmRhdGEucm93czsNCiAgICAgICAgdGhpcy5hZGRTdWJtaXREYXRhR0ooZGF0YUwpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvL+aPkOS6pOW9kumbhuWNleaVsOaNrg0KICAgIGFkZFN1Ym1pdERhdGFHSihkYXRhMSkgew0KICAgICAgbGV0IGEgPSBbXTsNCiAgICAgIGxldCBiID0gMTsNCiAgICAgIGxldCBkYXRhID0gZGF0YTEuZmlsdGVyKChpdGVtKSA9PiBpdGVtLmVmZmVjdGl2ZSA9PSAxKTsNCiAgICAgIGxldCBzdHIgPSAiIjsNCiAgICAgIGxldCBzdHIxID0gIiI7DQogICAgICBsZXQgdmVyc2lvbiA9IGluZGV4RGF0YS52ZXJzaW9uOw0KICAgICAgaWYgKGRhdGEgIT0gbnVsbCAmJiBkYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbGV0IG51bWJlciA9IDA7DQogICAgICAgIGxldCBzdWJtaXREYXRhID0gW107DQogICAgICAgIGxldCBubyA9IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm87DQogICAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgbGV0IG9iaiA9IHJlcXVpcmVkRmllbGRWYWxpZGF0b3IoaXRlbSk7DQogICAgICAgICAgaWYgKG9iai5yZXN1bHQpIHsNCiAgICAgICAgICAgIGxldCB5eXl5bW1kZCA9IGN1dERhdGVfeXl5eW1tZGQoaXRlbS5zdGFydGRhdGUpOw0KICAgICAgICAgICAgaXRlbS5zdGFydHllYXIgPSB5eXl5bW1kZC55eXl5Ow0KICAgICAgICAgICAgaXRlbS5zdGFydG1vbnRoID0geXl5eW1tZGQubW07DQogICAgICAgICAgICB5eXl5bW1kZCA9IGN1dERhdGVfeXl5eW1tZGQoaXRlbS5lbmRkYXRlKTsNCiAgICAgICAgICAgIGl0ZW0uZW5keWVhciA9IHl5eXltbWRkLnl5eXk7DQogICAgICAgICAgICBpdGVtLmVuZG1vbnRoID0geXl5eW1tZGQubW07DQogICAgICAgICAgICBhLnB1c2goaXRlbS5hbW1ldGVyaWQpOw0KICAgICAgICAgICAgc3VibWl0RGF0YS5wdXNoKGl0ZW0pOw0KICAgICAgICAgICAgbnVtYmVyKys7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHN0ciArPQ0KICAgICAgICAgICAgICAi55S16KGoL+WNj+iurue8luWPt+S4uuOAkCIgKw0KICAgICAgICAgICAgICBpdGVtLmFtbWV0ZXJjb2RlICsNCiAgICAgICAgICAgICAgIuOAkeeahOWPsOi0pumqjOivgeayoeaciemAmui/h++8muOAkCIgKw0KICAgICAgICAgICAgICBvYmouc3RyICsNCiAgICAgICAgICAgICAgIuOAke+8myI7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgaWYgKGl0ZW0ubWFnbmlmaWNhdGlvbmVyciA9PSAyKSB7DQogICAgICAgICAgICBzdHIxICs9DQogICAgICAgICAgICAgICLnlLXooagv5Y2P6K6u57yW5Y+35Li644CQIiArDQogICAgICAgICAgICAgIGl0ZW0uYW1tZXRlcmNvZGUgKw0KICAgICAgICAgICAgICAi44CR55qE5Y+w6LSm5YCN546H44CQIiArDQogICAgICAgICAgICAgIGl0ZW0ubWFnbmlmaWNhdGlvbiArDQogICAgICAgICAgICAgICLjgJHkuI7nlLXooajlgI3njofjgJAiICsNCiAgICAgICAgICAgICAgaXRlbS5hbW1tdWx0dGltZXMgKw0KICAgICAgICAgICAgICAi44CR5LiN5LiA6Ie077yBICA8YnIgLz4gIjsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBpZiAoaXRlbS5wZXJjZW50ZXJyID09IDIpIHsNCiAgICAgICAgICAgIHN0cjEgKz0NCiAgICAgICAgICAgICAgIueUteihqC/ljY/orq7nvJblj7fkuLrjgJAiICsNCiAgICAgICAgICAgICAgaXRlbS5hbW1ldGVyY29kZSArDQogICAgICAgICAgICAgICLjgJHnmoTlj7DotKbliIblibLmr5TkvovjgJAiICsNCiAgICAgICAgICAgICAgaXRlbS5wZXJjZW50ICsNCiAgICAgICAgICAgICAgIuOAkeS4jueUteihqOWIhuWJsuavlOS+i+OAkCIgKw0KICAgICAgICAgICAgICBpdGVtLmFtbXBlcmNlbnQgKw0KICAgICAgICAgICAgICAi44CR5LiN5LiA6Ie077yBIDxiciAvPiAiOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoaXRlbS5lZmZlY3RpdmUgIT0gMSkgew0KICAgICAgICAgICAgYiA9IDI7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChpdGVtLnN0YXR1cyAhPSAxKSB7DQogICAgICAgICAgICBiID0gMzsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKA0KICAgICAgICAgICAgInNjIiA9PSB2ZXJzaW9uICYmDQogICAgICAgICAgICBpdGVtLnVuaXRwaXJjZSA+IDIgJiYNCiAgICAgICAgICAgIChpdGVtLnVuaXRwaXJjZW9sZCA9PSBudWxsIHx8IGl0ZW0udW5pdHBpcmNlb2xkIDwgMikgJiYNCiAgICAgICAgICAgIHRoYXQudmFsaXByaWNlDQogICAgICAgICAgKSB7DQogICAgICAgICAgICBiID0gNDsNCiAgICAgICAgICAgIHN0ciArPSBpdGVtLmFtbWV0ZXJjb2RlICsgIiwiOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICAgIGlmIChiID09IDEpIHsNCiAgICAgICAgICBpZiAoc3RyMS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICB0aGlzLiROb3RpY2Uud2FybmluZyh7DQogICAgICAgICAgICAgIHRpdGxlOiAi5rOo5oSPIiwNCiAgICAgICAgICAgICAgZGVzYzogc3RyMSwNCiAgICAgICAgICAgICAgZHVyYXRpb246IDAsDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHN1Ym1pdERhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy5zdWJtaXQgPSBzdWJtaXREYXRhOw0KICAgICAgICAgICAgdGhpcy5zdWJtaXQyID0gc3VibWl0RGF0YTsNCiAgICAgICAgICAgIHRoaXMucHJlc2VydmVTYygpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIGlmIChiID09PSAyKSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoIumAieS4reeahOWPsOi0puS4reWtmOWcqOS4tOaXtuaVsOaNru+8jOivt+WFiOS/neWtmOWGjeWKoOWFpeW9kumbhuWNle+8gSIpOw0KICAgICAgICB9IGVsc2UgaWYgKGIgPT09IDMpIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6YCA5Zue55qE5Y+w6LSm5LiN6IO95Yqg5YWl5YW25a6D5b2S6ZuG5Y2V77yM6K+354K55Ye7W+mHjeaWsOWKoOWFpeW9kumbhuWNlV3mjInpkq4iKTsNCiAgICAgICAgfSBlbHNlIGlmIChiID09PSA0KSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoDQogICAgICAgICAgICAi55S16KGoL+WNj+iurue8luWPt+S4uuOAkCIgKw0KICAgICAgICAgICAgICBzdHIgKw0KICAgICAgICAgICAgICAi44CR55qE5Y+w6LSm5Y2V5Lu35bey57uP6LaF6L+HMuWFg++8jOivt+WPkU9B6YKu5Lu257uZ55yB5YWs5Y+45a6h5qC477yM6YCa6L+H5ZCO5omN5Y+v5Yqg5YWl5b2S6ZuG5Y2V77yBIg0KICAgICAgICAgICk7DQogICAgICAgIH0NCiAgICAgICAgdGhhdC5hbW1ldGVyaWRzID0gYTsNCiAgICAgICAgaWYgKHN0ci5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhhdC5lcnJvclRpcHMoc3RyKTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoc3RyMS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhhdC4kTm90aWNlLndhcm5pbmcoew0KICAgICAgICAgICAgdGl0bGU6ICLms6jmhI8iLA0KICAgICAgICAgICAgZGVzYzogc3RyMSwNCiAgICAgICAgICAgIGR1cmF0aW9uOiAwLA0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvKioNCiAgICAgKiAtLS0NCiAgICAgKi8NCiAgICBvcGVuQ29tcGxldGVkUHJlTW9kYWwoKSB7DQogICAgICB0aGlzLiRyZWZzLmNvbXBsZXRlZFByZS5pbml0QW1tZXRlcih0aGlzLmFjY291bnRPYmouY291bnRyeSwgMTEpOw0KICAgIH0sDQogICAgYWdhaW5Kb2luKCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLiRyZWZzLmFjY291bnRFc1RhYmxlLmdldFNlbGVjdGlvbigpOw0KICAgICAgbGV0IGIgPSB0cnVlOw0KICAgICAgaWYgKGRhdGEgPT0gbnVsbCB8fCBkYXRhLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fpgInmi6nopoHph43mlrDliqDlhaXlvZLpm4bljZXnmoTlj7DotKYiKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldCBpZHMgPSAiIjsNCiAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgbGV0IHN0YXR1cyA9IGl0ZW0uc3RhdHVzOw0KICAgICAgICAgIGlmIChzdGF0dXMgIT0gNSkgew0KICAgICAgICAgICAgYiA9IGZhbHNlOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZHMgKz0gaXRlbS5wY2lkICsgIiwiOw0KICAgICAgICB9KTsNCiAgICAgICAgaWYgKGIpIHsNCiAgICAgICAgICBhZ2FpbkpvaW4oaWRzKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuZGF0YS5jb2RlID09IDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKHsNCiAgICAgICAgICAgICAgICBjb250ZW50OiAi5o+Q56S677ya5pON5L2c5oiQ5YqfIiwNCiAgICAgICAgICAgICAgICBkdXJhdGlvbjogMTAsDQogICAgICAgICAgICAgICAgY2xvc2FibGU6IHRydWUsDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCLlj6rmnInlt7LpgIDlm57nmoTlj7DotKbmiY3og73ph43mlrDliqDlhaXlvZLpm4bljZUiKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgcmVmcmVzaCgpIHsNCiAgICAgIGxldCBvYmogPSB0aGlzOw0KICAgICAgb2JqLnNob3dBbGFybU1vZGVsID0gZmFsc2U7DQogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgb2JqLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgfSwgMjAwKTsNCiAgICB9LA0KICAgIGJlZm9yZUxvYWREYXRhKGRhdGEsIHN0cikgew0KICAgICAgdmFyIGNvbHMgPSBbXSwNCiAgICAgICAga2V5cyA9IFtdOw0KICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCB0aGlzLnRiQWNjb3VudC5leHBvcnRDb2x1bW5zLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbHMucHVzaCh0aGlzLnRiQWNjb3VudC5leHBvcnRDb2x1bW5zW2ldLnRpdGxlKTsNCiAgICAgICAga2V5cy5wdXNoKHRoaXMudGJBY2NvdW50LmV4cG9ydENvbHVtbnNbaV0ua2V5KTsNCiAgICAgIH0NCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgdGl0bGU6IGNvbHMsDQogICAgICAgIGtleToga2V5cywNCiAgICAgICAgZGF0YTogZGF0YSwNCiAgICAgICAgYXV0b1dpZHRoOiB0cnVlLA0KICAgICAgICBmaWxlbmFtZTogc3RyLA0KICAgICAgfTsNCiAgICAgIGV4Y2VsLmV4cG9ydF9hcnJheV90b19leGNlbChwYXJhbXMpOw0KICAgICAgcmV0dXJuOw0KICAgIH0sDQogICAgZXhwb3J0Q3N2KG5hbWUpIHsNCiAgICAgIHRoaXMuZXhwb3J0LnJ1biA9IHRydWU7DQogICAgICBpZiAobmFtZSA9PT0gImN1cnJlbnQiKSB7DQogICAgICAgIHRoaXMuYmVmb3JlTG9hZERhdGEodGhpcy50YkFjY291bnQuZGF0YSwgIuiHquacieaMgui0puWPsOi0puWvvOWHuuaVsOaNriIpOw0KICAgICAgfSBlbHNlIGlmIChuYW1lID09PSAiYWxsIikgew0KICAgICAgICBsZXQgcGFyYW1zID0gdGhpcy5hY2NvdW50T2JqOw0KICAgICAgICBwYXJhbXMucGFnZU51bSA9IDE7DQogICAgICAgIHBhcmFtcy5wYWdlU2l6ZSA9IHRoaXMuZXhwb3J0LnNpemU7DQogICAgICAgIGxldCByZXEgPSB7DQogICAgICAgICAgdXJsOiAiL2J1c2luZXNzL2FjY291bnRFcy9zZWxlY3RBY2NvdW50RXNMaXN0IiwNCiAgICAgICAgICBtZXRob2Q6ICJnZXQiLA0KICAgICAgICAgIHBhcmFtczogcGFyYW1zLA0KICAgICAgICB9Ow0KICAgICAgICB0aGlzLnRiQWNjb3VudC5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgYXhpb3MNCiAgICAgICAgICAucmVxdWVzdChyZXEpDQogICAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgdGhpcy50YkFjY291bnQubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgaWYgKHJlcy5kYXRhKSB7DQogICAgICAgICAgICAgIGxldCBhcnJheSA9IHJlcy5kYXRhLnJvd3M7DQogICAgICAgICAgICAgIGFjY291bnRFc1RvdGFsKHRoaXMuYWNjb3VudE9iaikudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgLy/lkIjorqENCiAgICAgICAgICAgICAgICBsZXQgYWxsdG90YWwgPSByZXMuZGF0YTsNCiAgICAgICAgICAgICAgICBhbGx0b3RhbC50b3RhbCA9ICLlkIjorqEiOw0KICAgICAgICAgICAgICAgIGFsbHRvdGFsLl9kaXNhYmxlZCA9IHRydWU7DQogICAgICAgICAgICAgICAgYXJyYXkucHVzaChhbGx0b3RhbCk7DQogICAgICAgICAgICAgICAgdGhpcy5iZWZvcmVMb2FkRGF0YShhcnJheSwgIuiHquacieaMgui0puWPsOi0puWvvOWHuuaVsOaNriIpOw0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5jYXRjaCgoZXJyKSA9PiB7DQogICAgICAgICAgICBjb25zb2xlLmxvZyhlcnIpOw0KICAgICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgdmFsaWRhdGUoKSB7DQogICAgICBpZiAodGhpcy5jb2x1bW5zSW5kZXggIT0gNiAmJiB0aGlzLmNvbHVtbnNJbmRleCAhPSA3KSB7DQogICAgICAgIGxldCB2YWwgPSB0aGlzLmVudGVyT3BlcmF0ZSh0aGlzLmNvbHVtbnNJbmRleCkuZGF0YTsNCiAgICAgICAgaWYgKHZhbCkgew0KICAgICAgICAgIGlmICh0ZXN0TnVtYmVyKHZhbCkpIHsNCiAgICAgICAgICAgIHN3aXRjaCAodGhpcy5jb2x1bW5zSW5kZXgpIHsNCiAgICAgICAgICAgICAgY2FzZSAxOg0KICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVTdGFydGRhdGUoKTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgY2FzZSAyOg0KICAgICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVFbmRkYXRlKCk7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgMzoNCiAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlY3VydXNlZHJlYWRpbmdzKCk7DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIGNhc2UgNDoNCiAgICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlaW5wdXR0aWNrZXRtb25leSgpOw0KICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICBjYXNlIDU6DQogICAgICAgICAgICAgICAgdGhpcy52YWxpZGF0ZWlucHV0dGF4dGlja2V0bW9uZXkoKTsNCiAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoIuivt+i+k+WFpeaVsOWtl++8gSIpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgdmFsaWRhdGVTdGFydGRhdGUoKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdFN0YXJ0RGF0ZTsNCiAgICAgIGxldCByZXN1bHQgPSBfdmVyaWZ5X1N0YXJ0RGF0ZShkYXRhLCB2YWwpOw0KICAgICAgaWYgKHJlc3VsdCkgew0KICAgICAgICAvL+Wksei0peWwseW8ueWHuuaPkOekuuWGheWuuQ0KICAgICAgICB0aGlzLmVycm9yVGlwcyhyZXN1bHQpOw0KICAgICAgICB0aGlzLm15U3R5bGVbdGhpcy5lZGl0SW5kZXhdLnN0YXJ0ZGF0ZSA9ICJlcnJvclN0bGUiOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5teVN0eWxlW3RoaXMuZWRpdEluZGV4XS5zdGFydGRhdGUgPSAibXlzcGFuIjsNCiAgICAgICAgZGF0YS5zdGFydGRhdGUgPSB2YWw7DQogICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgfQ0KICAgIH0sDQogICAgdmFsaWRhdGVFbmRkYXRlKCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRFbmREYXRlOw0KICAgICAgLy8g6aqM6K+B5oiq5q2i5pel5pyf5pa55rOVDQogICAgICBsZXQgcmVzdWx0ID0gX3ZlcmlmeV9FbmREYXRlKGRhdGEsIHZhbCk7DQogICAgICBpZiAocmVzdWx0KSB7DQogICAgICAgIC8v5aSx6LSl5bCx5by55Ye65o+Q56S65YaF5a6577yM5bm25bCG5pWw5o2u5oGi5aSN5Yid5aeL5YyWDQogICAgICAgIHRoaXMuZXJyb3JUaXBzKHJlc3VsdCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBkYXRhLmVuZGRhdGUgPSB2YWw7DQogICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgfQ0KICAgIH0sDQogICAgdmFsaWRhdGVjdXJ1c2VkcmVhZGluZ3MoKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdGN1cnVzZWRyZWFkaW5nczsNCiAgICAgIGRhdGEuY3VydXNlZHJlYWRpbmdzID0gdmFsOw0KICAgICAgZGF0YS50b3RhbHVzZWRyZWFkaW5ncyA9IHZhbDsNCiAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgdGhpcy51bml0UHJpY2UoZGF0YSk7DQogICAgfSwNCiAgICB2YWxpZGF0ZXRyYW5zZm9ybWVydWxsYWdlKCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXR0cmFuc2Zvcm1lcnVsbGFnZTsNCiAgICAgIGRhdGEudHJhbnNmb3JtZXJ1bGxhZ2UgPSB2YWw7DQogICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICB9LA0KICAgIC8v5pmu56WoDQogICAgdmFsaWRhdGVpbnB1dHRpY2tldG1vbmV5KCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLnRiQWNjb3VudC5kYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRpbnB1dHRpY2tldG1vbmV5Ow0KICAgICAgLy92YWwgPSBNYXRoLmFicyh2YWwpOyAvLy8v5YWB6K646LSf5pWwIE1hdGguYWJzKHZhbCkNCiAgICAgIGRhdGEuaW5wdXR0aWNrZXRtb25leSA9IHBhcnNlRmxvYXQodmFsKTsNCiAgICAgIGRhdGEudGlja2V0bW9uZXkgPSBwYXJzZUZsb2F0KHZhbCk7DQogICAgICBkYXRhLmFjY291bnRtb25leSA9IGRhdGEuaW5wdXR0aWNrZXRtb25leSArIGRhdGEuaW5wdXR0YXh0aWNrZXRtb25leTsNCiAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgdGhpcy51bml0UHJpY2UoZGF0YSk7DQogICAgICB0aGlzLnZhbGlkYXRlVW5pdFByaWNlKGRhdGEpOw0KICAgIH0sDQogICAgLy/kuJPnpagNCiAgICB2YWxpZGF0ZWlucHV0dGF4dGlja2V0bW9uZXkoKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdGlucHV0dGF4dGlja2V0bW9uZXk7DQogICAgICAvL3ZhbCA9IE1hdGguYWJzKHZhbCk7DQogICAgICBkYXRhLmlucHV0dGF4dGlja2V0bW9uZXkgPSBwYXJzZUZsb2F0KHZhbCk7DQogICAgICBkYXRhLnRheHRpY2tldG1vbmV5ID0gcGFyc2VGbG9hdCh2YWwpOw0KICAgICAgZGF0YS5hY2NvdW50bW9uZXkgPSBkYXRhLmlucHV0dGlja2V0bW9uZXkgKyBkYXRhLmlucHV0dGF4dGlja2V0bW9uZXk7DQogICAgICAvL2RhdGEudGF4YW1vdW50ID0gTWF0aC5hYnMoY291bnRUYXhhbW91bnQoZGF0YSkpOw0KICAgICAgZGF0YS50YXhhbW91bnQgPSBjb3VudFRheGFtb3VudHNyKGRhdGEpOw0KICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICB0aGlzLnVuaXRQcmljZShkYXRhKTsNCiAgICAgIHRoaXMudmFsaWRhdGVVbml0UHJpY2UoZGF0YSk7DQogICAgfSwNCiAgICAvL+iuoeeul+eojuminQ0KICAgIHNldHRheHJhdGUoKSB7DQogICAgICBsZXQgdmFsID0gdGhpcy5lZGl0dGF4cmF0ZTsNCiAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICBkYXRhLnRheHJhdGUgPSB2YWw7DQogICAgICBkYXRhLnRheGFtb3VudCA9IE1hdGguYWJzKGNvdW50VGF4YW1vdW50KGRhdGEpKTsNCiAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgIH0sDQogICAgdmFsaWRhdGVhY2NvdW50bW9uZXkoKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMudGJBY2NvdW50LmRhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdGFjY291bnRtb25leTsNCiAgICAgIGRhdGEuYWNjb3VudG1vbmV5ID0gdmFsOyAvL+WFgeiuuOi0n+aVsCBNYXRoLmFicyh2YWwpDQogICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgIHRoaXMudW5pdFByaWNlKGRhdGEpOw0KICAgICAgdGhpcy52YWxpZGF0ZVVuaXRQcmljZShkYXRhKTsNCiAgICB9LA0KICAgIHNldHJlbWFyaygpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy50YkFjY291bnQuZGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICBsZXQgdmFsID0gdGhpcy5lZGl0cmVtYXJrOw0KICAgICAgZGF0YS5yZW1hcmsgPSB2YWw7DQogICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICB9LA0KICAgIHNldE15U3R5bGUobGVuZ3RoKSB7DQogICAgICB0aGlzLm15U3R5bGUgPSBbXTsNCiAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbGVuZ3RoOyBpKyspIHsNCiAgICAgICAgdGhpcy5teVN0eWxlLnB1c2goew0KICAgICAgICAgIHN0YXJ0ZGF0ZTogIm15c3BhbiIsDQogICAgICAgICAgZW5kZGF0ZTogIm15c3BhbiIsDQogICAgICAgICAgY3VydXNlZHJlYWRpbmdzOiAibXlzcGFuIiwNCiAgICAgICAgICBpbnB1dHRpY2tldG1vbmV5OiAibXlzcGFuIiwNCiAgICAgICAgICBpbnB1dHRheHRpY2tldG1vbmV5OiAibXlzcGFuIiwNCiAgICAgICAgICB0YXhyYXRlOiAibXlzcGFuIiwNCiAgICAgICAgICByZW1hcms6ICJteXNwYW4iLA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vc3BhbueCueWHu+S6i+S7tuWwhnNwYW7mjaLmiJDovpPlhaXmoYblubbkuJTojrflj5bnhKbngrkNCiAgICBzZWxlY3RDYWxsKHJvdywgaW5kZXgsIGNvbHVtbnMsIHN0cikgew0KICAgICAgdGhpcy5lZGl0U3RhcnREYXRlID0gcm93LnN0YXJ0ZGF0ZTsNCiAgICAgIHRoaXMuZWRpdEVuZERhdGUgPSByb3cuZW5kZGF0ZTsNCiAgICAgIHRoaXMuZWRpdGN1cnVzZWRyZWFkaW5ncyA9DQogICAgICAgIHJvdy5jdXJ1c2VkcmVhZGluZ3MgPT0gbnVsbCB8fCByb3cuY3VydXNlZHJlYWRpbmdzID09PSAwDQogICAgICAgICAgPyBudWxsDQogICAgICAgICAgOiByb3cuY3VydXNlZHJlYWRpbmdzOw0KICAgICAgdGhpcy5lZGl0aW5wdXR0aWNrZXRtb25leSA9DQogICAgICAgIHJvdy5pbnB1dHRpY2tldG1vbmV5ID09IG51bGwgfHwgcm93LmlucHV0dGlja2V0bW9uZXkgPT09IDANCiAgICAgICAgICA/IG51bGwNCiAgICAgICAgICA6IHJvdy5pbnB1dHRpY2tldG1vbmV5Ow0KICAgICAgdGhpcy5lZGl0aW5wdXR0YXh0aWNrZXRtb25leSA9DQogICAgICAgIHJvdy5pbnB1dHRheHRpY2tldG1vbmV5ID09IG51bGwgfHwgcm93LmlucHV0dGF4dGlja2V0bW9uZXkgPT09IDANCiAgICAgICAgICA/IG51bGwNCiAgICAgICAgICA6IHJvdy5pbnB1dHRheHRpY2tldG1vbmV5Ow0KICAgICAgdGhpcy5lZGl0dGF4cmF0ZSA9DQogICAgICAgIHJvdy50YXhyYXRlID09IG51bGwgfHwgcm93LnRheHJhdGUgPT09IDAgPyBudWxsIDogcGFyc2VJbnQocm93LnRheHJhdGUpICsgIiI7DQogICAgICB0aGlzLmVkaXRyZW1hcmsgPSByb3cucmVtYXJrOw0KDQogICAgICB0aGlzLmVkaXRJbmRleCA9IGluZGV4Ow0KICAgICAgdGhpcy5jb2x1bW5zSW5kZXggPSBjb2x1bW5zOw0KDQogICAgICBsZXQgYSA9IHRoaXM7DQogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgaWYgKGNvbHVtbnMgIT0gNykgew0KICAgICAgICAgIGEuJHJlZnNbc3RyICsgaW5kZXggKyBjb2x1bW5zXS5mb2N1cygpOw0KICAgICAgICB9DQogICAgICB9LCAyMDApOw0KICAgIH0sDQogICAgLy/ot7PovazliLDkuIvkuIDmoLwNCiAgICBuZXh0Q2VsbChkYXRhKSB7DQogICAgICBsZXQgaW5kZXggPSBkYXRhLmVkaXRJbmRleDsNCiAgICAgIGxldCBjb2x1bW5zID0gZGF0YS5jb2x1bW5zSW5kZXg7DQogICAgICBsZXQgcm93ID0gIiI7DQogICAgICBpZiAoaW5kZXggPT09IC0xICYmIGNvbHVtbnMgPT09IC0xKSB7DQogICAgICAgIGluZGV4ID0gMDsNCiAgICAgICAgY29sdW1ucyA9IDE7DQogICAgICB9IGVsc2UgaWYgKGluZGV4ID4gLTEgJiYgY29sdW1ucyA9PT0gNykgew0KICAgICAgICAvL+W9k+i3s+i9rOeahOacgOWQjuS4gOihjOacgOWQjuS4gOagvOeahOaXtuWAmQ0KICAgICAgICBpZiAoaW5kZXggPj0gZGF0YS5wYWdlU2l6ZSAtIDEgfHwgaW5kZXggPj0gZGF0YS5wYWdlVG90YWwgLSAxKSB7DQogICAgICAgICAgaW5kZXggPSAwOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGluZGV4Kys7DQogICAgICAgIH0NCiAgICAgICAgY29sdW1ucyA9IDE7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb2x1bW5zICs9IDE7DQogICAgICB9DQogICAgICBkYXRhLmVkaXRJbmRleCA9IGluZGV4Ow0KICAgICAgZGF0YS5jb2x1bW5zSW5kZXggPSBjb2x1bW5zOw0KICAgICAgcm93ID0gZGF0YS50YkFjY291bnQuZGF0YVtpbmRleF07DQogICAgICBpZiAocm93KSB7DQogICAgICAgIGRhdGEuZWRpdFN0YXJ0RGF0ZSA9IHJvdy5zdGFydGRhdGU7DQogICAgICAgIGRhdGEuZWRpdEVuZERhdGUgPSByb3cuZW5kZGF0ZTsNCiAgICAgICAgZGF0YS5lZGl0Y3VydXNlZHJlYWRpbmdzID0NCiAgICAgICAgICByb3cuY3VydXNlZHJlYWRpbmdzID09IG51bGwgfHwgcm93LmN1cnVzZWRyZWFkaW5ncyA9PT0gMA0KICAgICAgICAgICAgPyBudWxsDQogICAgICAgICAgICA6IHJvdy5jdXJ1c2VkcmVhZGluZ3M7DQogICAgICAgIGRhdGEuZWRpdGlucHV0dGlja2V0bW9uZXkgPQ0KICAgICAgICAgIHJvdy5pbnB1dHRpY2tldG1vbmV5ID09IG51bGwgfHwgcm93LmlucHV0dGlja2V0bW9uZXkgPT09IDANCiAgICAgICAgICAgID8gbnVsbA0KICAgICAgICAgICAgOiByb3cuaW5wdXR0aWNrZXRtb25leTsNCiAgICAgICAgZGF0YS5lZGl0aW5wdXR0YXh0aWNrZXRtb25leSA9DQogICAgICAgICAgcm93LmlucHV0dGF4dGlja2V0bW9uZXkgPT0gbnVsbCB8fCByb3cuaW5wdXR0YXh0aWNrZXRtb25leSA9PT0gMA0KICAgICAgICAgICAgPyBudWxsDQogICAgICAgICAgICA6IHJvdy5pbnB1dHRheHRpY2tldG1vbmV5Ow0KICAgICAgICBkYXRhLmVkaXR0YXhyYXRlID0NCiAgICAgICAgICByb3cudGF4cmF0ZSA9PSBudWxsIHx8IHJvdy50YXhyYXRlID09PSAwID8gbnVsbCA6IHBhcnNlSW50KHJvdy50YXhyYXRlKSArICIiOw0KICAgICAgICBkYXRhLmVkaXRyZW1hcmsgPSByb3cucmVtYXJrOw0KICAgICAgfQ0KDQogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgZGF0YS4kcmVmc1tkYXRhLmVudGVyT3BlcmF0ZShjb2x1bW5zKS5zdHIgKyBpbmRleCArIGNvbHVtbnNdLmZvY3VzKCk7DQogICAgICB9LCAyMDApOw0KICAgIH0sDQogICAgLy/moLnmja7liJflj7fov5Tlm57lr7nlupTnmoTliJflkI0NCiAgICBlbnRlck9wZXJhdGUobnVtYmVyKSB7DQogICAgICBsZXQgc3RyID0gIiI7DQogICAgICBsZXQgZGF0YSA9IG51bGw7DQogICAgICBzd2l0Y2ggKG51bWJlcikgew0KICAgICAgICBjYXNlIDE6DQogICAgICAgICAgc3RyID0gInN0YXJ0ZGF0ZSI7DQogICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdFN0YXJ0RGF0ZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAyOg0KICAgICAgICAgIHN0ciA9ICJlbmRkYXRlIjsNCiAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0RW5kRGF0ZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAzOg0KICAgICAgICAgIHN0ciA9ICJjdXJ1c2VkcmVhZGluZ3MiOw0KICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRjdXJ1c2VkcmVhZGluZ3M7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgNDoNCiAgICAgICAgICBzdHIgPSAiaW5wdXR0aWNrZXRtb25leSI7DQogICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdGlucHV0dGlja2V0bW9uZXk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgNToNCiAgICAgICAgICBzdHIgPSAiaW5wdXR0YXh0aWNrZXRtb25leSI7DQogICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdGlucHV0dGF4dGlja2V0bW9uZXk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgNjoNCiAgICAgICAgICBzdHIgPSAidGF4cmF0ZSI7DQogICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdHRheHJhdGU7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgNzoNCiAgICAgICAgICBzdHIgPSAicmVtYXJrIjsNCiAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0cmVtYXJrOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHsgc3RyOiBzdHIsIGRhdGE6IGRhdGEgfTsNCiAgICB9LA0KICAgIHByZWQoKSB7DQogICAgICB2YXIgbGV0dCA9IHRoaXM7DQogICAgICBsZXQgaW5kZXggPSBsZXR0LmVkaXRJbmRleDsNCiAgICAgIGxldCBjb2x1bW5zID0gbGV0dC5jb2x1bW5zSW5kZXg7DQogICAgICBpZiAoaW5kZXggPT09IC0xICYmIGNvbHVtbnMgPT09IC0xKSB7DQogICAgICAgIGluZGV4ID0gMDsNCiAgICAgICAgY29sdW1ucyA9IDE7DQogICAgICAgIGxldHQuZWRpdEluZGV4ID0gaW5kZXg7DQogICAgICAgIGxldHQuY29sdW1uc0luZGV4ID0gY29sdW1uczsNCiAgICAgICAgbGV0dC5lZGl0U3RhcnREYXRlID0gbGV0dC50YkFjY291bnQuZGF0YVtpbmRleF0uc3RhcnRkYXRlOw0KICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICBsZXR0LiRyZWZzW2xldHQuZW50ZXJPcGVyYXRlKGNvbHVtbnMpLnN0ciArIGluZGV4ICsgY29sdW1uc10uZm9jdXMoKTsNCiAgICAgICAgfSwgMjAwKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldHQudmFsaWRhdGUoKTsNCiAgICAgICAgbGV0dC5zZXRyZW1hcmsoKTsNCiAgICAgICAgbGV0dC5uZXh0Q2VsbChsZXR0KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGVsbGlwc2lzKHZhbHVlKSB7DQogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gIiI7DQogICAgICBpZiAodmFsdWUubGVuZ3RoID4gMykgew0KICAgICAgICByZXR1cm4gdmFsdWUuc2xpY2UoMCwgMykgKyAiLi4uIjsNCiAgICAgIH0NCiAgICAgIHJldHVybiB2YWx1ZTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["addCreditAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8WA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addCreditAccount.vue", "sourceRoot": "src/view/account/homePageAccount", "sourcesContent": ["<!--自有挂账电费台账-->\r\n\r\n<template>\r\n  <div class=\"page-class page-card\">\r\n    <div class=\"query-box\">\r\n      <Row :class=\"filterColl ? 'header-bar-show' : 'header-bar-hide'\">\r\n        <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.accountno\"\r\n                  :style=\"formItemWidth\"\r\n                  @on-change=\"accountnoChange\"\r\n                >\r\n                  <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"项目名称:\" prop=\"projectName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.projectName\"\r\n                  placeholder=\"请输入项目名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"'sc' == version\">\r\n              <FormItem\r\n                label=\"电表/协议编码:\"\r\n                prop=\"ammeterName\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.ammeterName\"\r\n                  placeholder=\"请输入电表户号/协议编码\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"供电局电表编号:\"\r\n                prop=\"supplybureauammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.supplybureauammetercode\"\r\n                  placeholder=\"请输入供电局电表编号\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"局站名称:\" prop=\"stationName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.stationName\"\r\n                  placeholder=\"请输入局站名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.company\"\r\n                  @on-change=\"selectChange(accountObj.company)\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                  <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"所属部门：\"\r\n                prop=\"countryName\"\r\n                v-if=\"isAdmin == true\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Input\r\n                  :clearable=\"true\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"accountObj.countryName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n              <FormItem\r\n                label=\"所属部门：\"\r\n                prop=\"country\"\r\n                v-if=\"isAdmin == false\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option value=\"-1\">全部</Option>\r\n                  <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{\r\n                    item.name\r\n                  }}</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\"></Col>\r\n          </Row>\r\n          <div align=\"right\">\r\n            <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n            <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n            <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n              <Button type=\"default\" style=\"margin-left: 5px\"\r\n                >导出\r\n                <Icon type=\"ios-arrow-down\"></Icon>\r\n              </Button>\r\n              <DropdownMenu slot=\"list\">\r\n                <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n          </div>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon\r\n          :type=\"filterColl ? 'md-arrow-dropup' : 'md-arrow-dropdown'\"\r\n          size=\"20\"\r\n          @click=\"filterColl = !filterColl\"\r\n          :color=\"filterColl ? '#000' : '#1ab394'\"\r\n        ></icon>\r\n      </div>\r\n    </div>\r\n    <div class=\"cl-table\">\r\n      <Row class=\"button-bar\">\r\n        <div class=\"table-button\">\r\n          <Button type=\"primary\" @click=\"addElectricType\">新增</Button>\r\n          <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n          <Button type=\"error\" @click=\"remove\">删除</Button>\r\n          <Button type=\"error\" @click=\"deleteAll()\">一键删除</Button>\r\n          <Button type=\"primary\" @click=\"openCompletedPreModal\">复制归集单台账</Button>\r\n          <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n            <Button type=\"info\" style=\"margin-left: 5px\"\r\n              >加入归集单\r\n              <Icon type=\"ios-arrow-down\"></Icon>\r\n            </Button>\r\n            <DropdownMenu slot=\"list\">\r\n              <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n              <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n          <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n        </div>\r\n      </Row>\r\n      <Table\r\n        ref=\"accountEsTable\"\r\n        border\r\n        :columns=\"tbAccount.columns\"\r\n        :data=\"tbAccount.data\"\r\n        class=\"mytable\"\r\n        :height=\"tableHeight\"\r\n      >\r\n        <!--项目名称-->\r\n        <template slot-scope=\"{ row }\" slot=\"projectName\">\r\n          <div v-if=\"row.status === 5\">\r\n            <span>{{ row.projectName }}</span\r\n            ><span style=\"color: red\">[退回]</span>\r\n          </div>\r\n          <span v-else>{{ row.projectName }}</span>\r\n        </template>\r\n        <!--起始时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"startdate\" v-if=\"row.total == null\">\r\n          <Input\r\n            :ref=\"'startdate' + index + 1\"\r\n            type=\"text\"\r\n            @on-blur=\"validate\"\r\n            v-model=\"editStartDate\"\r\n            v-if=\"editIndex === index && columnsIndex === 1\"\r\n          />\r\n          <span\r\n            :class=\"myStyle[index].startdate\"\r\n            @click=\"selectCall(row, index, 1, 'startdate')\"\r\n            v-else\r\n            >{{ row.startdate }}</span\r\n          >\r\n        </template>\r\n        <!--截止时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"enddate\" v-if=\"row.total == null\">\r\n          <Input\r\n            :ref=\"'enddate' + index + 2\"\r\n            type=\"text\"\r\n            v-model=\"editEndDate\"\r\n            @on-blur=\"validate\"\r\n            v-if=\"editIndex === index && columnsIndex === 2\"\r\n          />\r\n          <span\r\n            :class=\"myStyle[index].enddate\"\r\n            @click=\"selectCall(row, index, 2, 'enddate')\"\r\n            v-else\r\n            >{{ row.enddate }}</span\r\n          >\r\n        </template>\r\n        <!--用电量-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"curusedreadings\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'curusedreadings' + index + 3\"\r\n              type=\"text\"\r\n              v-model=\"editcurusedreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 3\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].curusedreadings\"\r\n              @click=\"selectCall(row, index, 3, 'curusedreadings')\"\r\n              v-else\r\n              >{{ row.curusedreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.curusedreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--输入的普票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputticketmoney' + index + 4\"\r\n              type=\"text\"\r\n              v-model=\"editinputticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 4\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputticketmoney\"\r\n              @click=\"selectCall(row, index, 4, 'inputticketmoney')\"\r\n              v-else\r\n              >{{ row.inputticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--输入的专票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputtaxticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputtaxticketmoney' + index + 5\"\r\n              type=\"text\"\r\n              v-model=\"editinputtaxticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 5\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputtaxticketmoney\"\r\n              @click=\"selectCall(row, index, 5, 'inputtaxticketmoney')\"\r\n              v-else\r\n              >{{ row.inputtaxticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputtaxticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--专票税率-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"taxrate\">\r\n          <div v-if=\"row.total == null\">\r\n            <Select\r\n              :ref=\"'taxrate' + index + 6\"\r\n              type=\"text\"\r\n              v-model=\"edittaxrate\"\r\n              @on-change=\"settaxrate\"\r\n              v-if=\"editIndex === index && columnsIndex === 6\"\r\n              transfer=\"true\"\r\n            >\r\n              <Option selected value=\"13\">13</Option>\r\n              <Option value=\"1\">1</Option>\r\n              <Option value=\"3\">3</Option>\r\n              <Option value=\"6\">6</Option>\r\n              <Option value=\"16\">16</Option>\r\n              <Option value=\"17\">17</Option>\r\n            </Select>\r\n            <span\r\n              :class=\"myStyle[index].taxrate\"\r\n              @click=\"selectCall(row, index, 6, 'taxrate')\"\r\n              v-else\r\n              >{{ row.taxrate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.taxrate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--备注-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              v-model=\"editremark\"\r\n              :ref=\"'remark' + index + 7\"\r\n              type=\"text\"\r\n              @on-blur=\"setremark\"\r\n              v-if=\"editIndex === index && columnsIndex === 7\"\r\n            />\r\n            <Tooltip placement=\"bottom\" max-width=\"200\" v-else>\r\n              <span\r\n                :class=\"myStyle[index].remark\"\r\n                style=\"width: 60px\"\r\n                @click=\"selectCall(row, index, 7, 'remark')\"\r\n                >{{ ellipsis(row.remark) }}</span\r\n              >\r\n              <div slot=\"content\">\r\n                {{ row.remark }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n          <div v-else>\r\n            <Tooltip placement=\"bottom\" max-width=\"200\">\r\n              <span>{{ ellipsis(row.remark) }}</span>\r\n              <div slot=\"content\">\r\n                {{ row.remark }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n        </template>\r\n      </Table>\r\n      <div class=\"table-page\">\r\n        <Page\r\n          size=\"small\"\r\n          :total=\"pageTotal\"\r\n          :current=\"pageNum\"\r\n          :page-size=\"pageSize\"\r\n          show-elevator\r\n          show-sizer\r\n          show-total\r\n          placement=\"top\"\r\n          @on-change=\"handlePage\"\r\n          @on-page-size-change=\"handlePageSize\"\r\n        ></Page>\r\n      </div>\r\n      <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n    </div>\r\n    <div>\r\n      <select-ammeter\r\n        ref=\"selectAmmeter\"\r\n        v-on:listenToSelectAmmeter=\"setAmmeterData\"\r\n      ></select-ammeter>\r\n      <add-bill-per ref=\"addBillPer\" v-on:refreshList=\"refresh\"></add-bill-per>\r\n      <completed-pre-modal\r\n        ref=\"completedPre\"\r\n        v-on:refreshList=\"refresh\"\r\n      ></completed-pre-modal>\r\n      <country-modal\r\n        ref=\"countryModal\"\r\n        v-on:getDataFromModal=\"getDataFromModal\"\r\n      ></country-modal>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  addPredPowerAccount,\r\n  addAccountEs,\r\n  removeAccountEs,\r\n  getUser,\r\n  getDepartments,\r\n  accountEsTotal,\r\n  selectIdsByEsParams,\r\n  removeAll,\r\n} from \"@/api/account\";\r\nimport { getResCenter, getcompany } from \"@/api/alertcontrol/alertcontrol\";\r\nimport { againJoin } from \"@/api/accountBillPer\";\r\nimport {\r\n  getClassification,\r\n  getUserdata,\r\n  getUserByUserRole,\r\n  getCountrysdata,\r\n  getCountryByUserId,\r\n} from \"@/api/basedata/ammeter.js\";\r\nimport {\r\n  getDates,\r\n  testNumber,\r\n  getFirstDateByAccountno_yyyymmdd,\r\n  getLastDateByAccountno_yyyymmdd,\r\n  getDates2,\r\n  cutDate_yyyymmdd,\r\n  stringToDate,\r\n  getCurrentDate,\r\n} from \"@/view/account/powerAccountHelper\";\r\nimport {\r\n  _verify_StartDate,\r\n  _verify_EndDate,\r\n  verification,\r\n  unitpirceMin,\r\n  unitpirceMax,\r\n  unitpirceMax1,\r\n} from \"@/view/account/PowerAccountEs\";\r\nimport {\r\n  _verify_StartDate1,\r\n  judgeNumber,\r\n  _verify_EndDate1,\r\n  _verify_PrevTotalReadings,\r\n  _verify_CurTotalReadings,\r\n  other_no_ammeteror_protocol,\r\n  self_no_ammeteror_protocol,\r\n  HFL_ammeteror,\r\n  judging_editability,\r\n  judging_editability1,\r\n  _verify_Money,\r\n  _calculateUsedReadings,\r\n  _calculateTotalReadings,\r\n  _calculateUnitPriceByUsedMoney,\r\n  _calculateAccountMoney,\r\n  _calculateQuotereadingsratio,\r\n  requiredFieldValidator,\r\n  countTaxamountsr,\r\n  countTaxamount,\r\n  calculateActualMoney,\r\n  judge_negate,\r\n  judge_recovery,\r\n  judge_yb,\r\n} from \"@/view/account/PowerAccountController\";\r\nimport { widthstyle } from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nimport CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\nimport excel from \"@/libs/excel\";\r\nimport axios from \"@/libs/api.request\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport AddBillPer from \"@/view/account/addBillPreModal\";\r\nimport CompletedPreModal from \"@/view/account/completedPreModal\";\r\nimport SelectAmmeter from \"@/view/account/selectAmmeter\";\r\nimport indexData from \"@/config/index\";\r\n\r\nimport permissionMixin from \"@/mixins/permission\";\r\n\r\nimport pageFun from \"@/mixins/pageFun\";\r\n\r\nlet dates = getDates();\r\nexport default {\r\n  name: \"addPredPowerAccount\",\r\n  mixins: [permissionMixin, pageFun],\r\n  components: { CompletedPreModal, SelectAmmeter, AddBillPer, CountryModal },\r\n  data() {\r\n    let renderStatus = (h, { row, index }) => {\r\n      var status = \"\";\r\n      let data = this.tbAccount.data[index];\r\n      for (let item of this.accountStatus) {\r\n        if (item.typeCode == row.status) {\r\n          data.statusName = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", data.statusName);\r\n    };\r\n\r\n    let renderCategory = (h, params) => {\r\n      var categoryname = \"\";\r\n      for (let item of this.categorys) {\r\n        if (item.typeCode == params.row.category) {\r\n          categoryname = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", categoryname);\r\n    };\r\n\r\n    return {\r\n      formItemWidth: widthstyle,\r\n      version: \"\",\r\n      dateList: dates,\r\n      filterColl: true, //搜索面板展开\r\n      editIndex: -1, //当前编辑行\r\n      columnsIndex: -1, //当前编辑列\r\n      editStartDate: \"\",\r\n      myStyle: [], //样式\r\n      editEndDate: \"\",\r\n      editcurusedreadings: \"\",\r\n      edittransformerullage: \"\",\r\n      edittaxrate: \"\",\r\n      editinputticketmoney: \"\",\r\n      editinputtaxticketmoney: \"\",\r\n      spinShow: false, //遮罩\r\n      categorys: [], //描述类型\r\n      editaccountmoney: \"\",\r\n      editremark: \"\",\r\n      accountStatus: [],\r\n      companies: [],\r\n      departments: [],\r\n      isAdmin: false,\r\n      company: null, //用户默认公司\r\n      country: null, //用户默认所属部门\r\n      countryName: null, //用户默认所属部门\r\n      export: {\r\n        run: false, //是否正在执行导出\r\n        data: \"\", //导出数据\r\n        totalPage: 0, //一共多少页\r\n        currentPage: 0, //当前多少页\r\n        percent: 0,\r\n        size: ********,\r\n      },\r\n      accountObj: {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        company: \"\", //分公司\r\n        projectName: \"\", //项目名称\r\n        country: \"\", //所属部门\r\n        ammeterName: \"\", //电表户号/协议编码\r\n        stationName: \"\",\r\n        accountType: \"1\", //台账类型\r\n        accountestype: 2, //台账类型\r\n        supplybureauammetercode: \"\",\r\n      },\r\n      tbAccount: {\r\n        loading: false,\r\n        columns: [],\r\n        headColumn: [\r\n          { type: \"selection\", width: 60, align: \"center\" },\r\n          { title: \"项目名称\", key: \"projectName\", slot: \"projectName\", align: \"center\" },\r\n        ],\r\n        tailColumn: [\r\n          { title: \"所属分公司\", key: \"companyName\", align: \"center\" },\r\n          { title: \"所属部门\", key: \"countryName\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            key: \"startdate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            key: \"enddate\",\r\n            align: \"center\",\r\n            width: 90,\r\n          },\r\n          {\r\n            title: \"用电量(度)\",\r\n            slot: \"curusedreadings\",\r\n            key: \"curusedreadings\",\r\n            align: \"center\",\r\n          },\r\n          { title: \"电价(元)\", key: \"unitpirce\", align: \"center\" },\r\n          { title: \"普票含税金额(元)\", slot: \"inputticketmoney\", align: \"center\" },\r\n          { title: \"专票含税金额(元)\", slot: \"inputtaxticketmoney\", align: \"center\" },\r\n          { title: \"专票税率（%）\", slot: \"taxrate\", align: \"center\" },\r\n          { title: \"专票税额\", key: \"taxamount\", align: \"center\" },\r\n          { title: \"电费\", key: \"accountmoney\", align: \"center\" },\r\n          { title: \"备注\", slot: \"remark\", key: \"remark\", align: \"center\" },\r\n          { title: \"用电类型\", key: \"electrotypename\", align: \"center\", width: 94 },\r\n          {\r\n            title: \"类型描述\",\r\n            key: \"categoryname\",\r\n            align: \"center\",\r\n            render: renderCategory,\r\n          },\r\n        ],\r\n        lnColumn: [\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n        ],\r\n        scColumn: [\r\n          { title: \"电表户号/协议编码\", key: \"ammeterName\", align: \"center\" },\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n        ],\r\n        data: [],\r\n        total: 0,\r\n        exportColumns: [\r\n          { title: \"项目名称\", key: \"projectName\" },\r\n          { title: \"电表户号/协议编码\", key: \"ammeterName\" },\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\" },\r\n          { title: \"所属分公司\", key: \"companyName\" },\r\n          { title: \"所属部门\", key: \"countryName\" },\r\n          { title: \"局站\", key: \"stationName\" },\r\n          { title: \"起始日期\", key: \"startdate\" },\r\n          { title: \"截止日期\", key: \"enddate\" },\r\n          { title: \"用电量(度)\", key: \"curusedreadings\" },\r\n          { title: \"电价(元)\", key: \"unitpirce\" },\r\n          { title: \"普票含税金额(元)\", key: \"inputticketmoney\" },\r\n          { title: \"专票含税金额(元)\", key: \"inputtaxticketmoney\" },\r\n          { title: \"专票税率（%）\", key: \"taxrate\" },\r\n          { title: \"专票税额\", key: \"taxamount\" },\r\n          { title: \"电费\", key: \"accountmoney\" },\r\n          { title: \"备注\", key: \"remark\" },\r\n          { title: \"用电类型\", key: \"electrotypename\" },\r\n          { title: \"类型描述\", key: \"categoryname\" },\r\n        ],\r\n      },\r\n      pageTotal: 0,\r\n      pageNum: 1,\r\n      pageSize: 10, //当前页\r\n    };\r\n  },\r\n  mounted() {\r\n    this.handleHeight(); //table高度自定义\r\n\r\n    this.version = indexData.version;\r\n\r\n    // switch (indexData.version) {\r\n    //   case \"sc\":\r\n    this.tbAccount.columns = this.tbAccount.headColumn\r\n      .concat(this.tbAccount.scColumn)\r\n      .concat(this.tbAccount.tailColumn);\r\n    //     break;\r\n    //   case \"ln\":\r\n    //     this.tbAccount.columns = this.tbAccount.headColumn\r\n    //       .concat(this.tbAccount.lnColumn)\r\n    //       .concat(this.tbAccount.tailColumn);\r\n    //     break;\r\n    // }\r\n\r\n    this.accountStatus = blist(\"accountStatus\");\r\n    this.categorys = blist(\"ammeterCategory\");\r\n    let that = this;\r\n    getUserByUserRole().then((res) => {\r\n      //根据权限获取分公司\r\n      that.companies = res.data.companies;\r\n      if (\r\n        res.data.isCityAdmin == true ||\r\n        res.data.isProAdmin == true ||\r\n        res.data.isSubAdmin == true\r\n      ) {\r\n        that.isAdmin = true;\r\n      }\r\n      getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {\r\n        //根据权限获取所属部门\r\n        that.departments = res.data;\r\n        that.getUserData();\r\n      });\r\n    });\r\n  },\r\n  methods: {\r\n    preserveSc() {\r\n      this.$refs.checkResult.ammeterids = this.ammeterids;\r\n      this.showJhModel = true;\r\n    },\r\n    isButtonload(v) {\r\n      this.$refs.showAlarmModel.buttonload = v;\r\n    },\r\n    //一键删除数据\r\n    deleteAll() {\r\n      this.$Modal.confirm({\r\n        title: \"提示\",\r\n        content: \"<p>确定一键删除吗？</p>\",\r\n        onOk: () => {\r\n          this.tbAccount.loading = true;\r\n          let params = this.accountObj;\r\n          params.removeAllFlag = true;\r\n          delete params.pageSize;\r\n          delete params.pageNum;\r\n          removeAll(params).then((res) => {\r\n            this.tbAccount.loading = false;\r\n            if (res.data.num > 0) {\r\n              this.$Message.success(\"一键删除成功\");\r\n              this.searchList();\r\n            } else {\r\n              this.$Message.error(\"一键删除失败\");\r\n            }\r\n          });\r\n        },\r\n        onCancel: () => {},\r\n      });\r\n    },\r\n    selectChange() {\r\n      let that = this;\r\n      if (that.accountObj.company != undefined) {\r\n        if (that.accountObj.company == \"-1\") {\r\n          that.accountObj.country = -1;\r\n          that.accountObj.countryName = null;\r\n        } else {\r\n          getCountryByUserId(that.accountObj.company).then((res) => {\r\n            if (res.data.departments.length != 0) {\r\n              that.accountObj.country = res.data.departments[0].id;\r\n              that.accountObj.countryName = res.data.departments[0].name;\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //选择所属部门开始\r\n    chooseResponseCenter() {\r\n      if (this.accountObj.company == null || this.accountObj.company == \"-1\") {\r\n        this.$Message.info(\"请先选择分公司\");\r\n        return;\r\n      }\r\n      this.$refs.countryModal.choose(this.accountObj.company); //所属部门\r\n    },\r\n    getDataFromModal(data) {\r\n      this.accountObj.country = data.id;\r\n      this.accountObj.countryName = data.name;\r\n      //选择所属部门结束\r\n    },\r\n    getUserData() {\r\n      let that = this;\r\n      getUserdata().then((res) => {\r\n        //当前登录用户所在公司和所属部门\r\n        if (res.data.companies.length != 0) {\r\n          let companies = res.data.companies;\r\n          if (res.data.companies[0].id == \"**********\") {\r\n            companies = that.companies;\r\n          }\r\n          that.company = companies[0].id;\r\n          that.accountObj.company = companies[0].id;\r\n        }\r\n        if (res.data.departments.length != 0) {\r\n          let departments = res.data.departments;\r\n          if (res.data.companies[0].id == \"**********\" && that.departments.length != 0) {\r\n            departments = that.departments;\r\n          }\r\n          that.country = departments[0].id;\r\n          that.countryName = departments[0].name;\r\n          that.accountObj.country = Number(departments[0].id);\r\n          that.accountObj.countryName = departments[0].name;\r\n        }\r\n\r\n        that.pageNum = 1;\r\n        that.getAccountMessages();\r\n      });\r\n    },\r\n    searchList() {\r\n      if (this.accountObj.countryName == \"\") {\r\n        this.accountObj.country = \"-1\";\r\n      }\r\n      this.pageNum = 1;\r\n      this.getAccountMessages();\r\n    },\r\n    accountnoChange() {\r\n      this.searchList();\r\n    },\r\n    setAmmeterData: function (data) {\r\n      let arrayData = [];\r\n      let no = this.accountObj.accountno;\r\n      if (data != null && data.length > 0) {\r\n        data.forEach(function (item) {\r\n          let obj = {};\r\n          obj.pcid = null;\r\n          obj.ammeterName = item.ammetername;\r\n          obj.projectName = item.projectname;\r\n          obj.substation = item.substation;\r\n          obj.categoryname = item.categoryname;\r\n          obj.category = item.category;\r\n          obj.ammeterid = item.ammeterid;\r\n          obj.company = item.company;\r\n          obj.companyName = item.companyName;\r\n          obj.country = item.country;\r\n          obj.countryName = item.countryName;\r\n          obj.startdate = null;\r\n          obj.enddate = null;\r\n          obj.curusedreadings = 0;\r\n          obj.transformerullage = 0;\r\n          obj.unitpirce = 0;\r\n          obj.inputticketmoney = 0;\r\n          obj.inputtaxticketmoney = 0;\r\n          obj.taxrate = \"13\";\r\n          obj.taxamount = 0;\r\n          obj.accountmoney = 0;\r\n          obj.remark = null;\r\n          obj.electrotypename = item.electrotypename;\r\n          obj.stationName = item.stationName;\r\n          obj.startdate = getFirstDateByAccountno_yyyymmdd(no);\r\n          obj.enddate = getLastDateByAccountno_yyyymmdd(no);\r\n          obj.accountestype = 2;\r\n          obj.supplybureauammetercode = item.supplybureauammetercode;\r\n          obj.electrotype = item.electrotype;\r\n          obj.stationcode5gr = item.stationcode5gr;\r\n          obj.stationname5gr = item.stationname5gr;\r\n          arrayData.push(obj);\r\n        });\r\n      }\r\n\r\n      let version = indexData.version;\r\n      let origin = this.tbAccount.data;\r\n      if (origin.length < 1) {\r\n        this.tbAccount.data = arrayData;\r\n      } else {\r\n        let tem = arrayData;\r\n        if (\"sc\" == version) {\r\n          origin.forEach((item) => {\r\n            for (let j = tem.length - 1; j >= 0; j--) {\r\n              let jj = tem[j];\r\n              if (item.ammeterid === jj.ammeterid) {\r\n                tem.splice(j, 1);\r\n              }\r\n            }\r\n          });\r\n        }\r\n        let total = this.pageTotal;\r\n        this.pageTotal = total + tem.length;\r\n        this.tbAccount.data = tem.concat(this.tbAccount.data);\r\n      }\r\n      this.setMyStyle(this.tbAccount.data.length);\r\n    },\r\n    //点击保存\r\n    async preserve() {\r\n      let dataL = this.$refs.accountEsTable.getSelection();\r\n\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      let version = indexData.version;\r\n      for (let i = 0; i < dataL.length; i++) {\r\n        if (dataL[i].editType == 1) {\r\n          if (\r\n            \"sc\" == version &&\r\n            dataL[i].electrotype &&\r\n            dataL[i].electrotype > 1400 &&\r\n            (dataL[i].stationcode5gr == null ||\r\n              dataL[i].stationcode5gr == undefined ||\r\n              dataL[i].stationcode5gr == \"\")\r\n          ) {\r\n            this.errorTips(\r\n              \"电表/协议编号【\" +\r\n                dataL[i].ammeterName +\r\n                \"】，项目名称【\" +\r\n                dataL[i].projectName +\r\n                \"】关联局站的5GR站址为空，请完善局站信息，或者5GR有效性清单失效，请联系无线管理员。\"\r\n            );\r\n          }\r\n          //截止日期校验\r\n          let result = _verify_EndDate(dataL[i], dataL[i].enddate);\r\n          if (result) {\r\n            this.errorTips(result);\r\n            return;\r\n          }\r\n          b = true;\r\n          array.push(dataL[i]);\r\n        }\r\n      }\r\n      // });\r\n      if (b) {\r\n        this.submitData(array);\r\n      } else {\r\n        this.errorTips(\"没有可保存数据\");\r\n      }\r\n    },\r\n    //提交数据\r\n    submitData(data) {\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let str = \"\";\r\n        let accountno = this.accountObj.accountno;\r\n        let index = 0;\r\n        data.forEach(function (item) {\r\n          if (item.projectName != \"小计\" && item.projectName != \"合计\") {\r\n            let obj = verification(item);\r\n            if (obj.result) {\r\n              if (item.pcid == null) {\r\n                item.accountno = accountno;\r\n              }\r\n              submitData.push(item);\r\n              number++;\r\n            } else {\r\n              str +=\r\n                \"电表/协议编号为【\" +\r\n                item.ammeterName +\r\n                \"】的台账验证没有通过：【\" +\r\n                obj.str +\r\n                \"】；\";\r\n            }\r\n          }\r\n        });\r\n        if (str.length > 0) {\r\n          this.errorTips(str);\r\n        }\r\n        if (submitData.length > 0) {\r\n          addAccountEs(submitData).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：成功保存 \" + submitData.length + \" 条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    addElectricType() {\r\n      let companyId = this.accountObj.company;\r\n      let country = this.accountObj.country;\r\n      if (companyId != null && country != null) {\r\n        let obj = {\r\n          company: companyId,\r\n          country: country,\r\n          accountno: this.accountObj.accountno,\r\n          accountType: \"1\",\r\n          accountestype: 2,\r\n        };\r\n        this.$refs.selectAmmeter.initAmmeter(obj);\r\n      } else {\r\n        this.errorTips(\"请选择分公司和部门\");\r\n      }\r\n    },\r\n    //验证错误弹出提示框\r\n    errorTips(str) {\r\n      this.$Notice.error({\r\n        title: \"提示\",\r\n        desc: str,\r\n        duration: 10,\r\n      });\r\n    },\r\n    handlePage(value) {\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n\r\n      this.pageNum = value;\r\n      this.getAccountMessages();\r\n    },\r\n    handlePageSize(value) {\r\n      let b = false;\r\n      let data = this.tbAccount.data;\r\n      let array = [];\r\n      data.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n\r\n      this.pageSize = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //向后台请求数据\r\n    getAccountMessages() {\r\n      let params = this.accountObj;\r\n      params.pageNum = this.pageNum;\r\n      params.pageSize = this.pageSize;\r\n      let req = {\r\n        url: \"/business/accountEs/selectAccountEsList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      this.tbAccount.loading = true;\r\n      axios\r\n        .request(req)\r\n        .then((res) => {\r\n          this.tbAccount.loading = false;\r\n          if (res.data) {\r\n            let data = res.data.rows;\r\n            data.forEach(function (item) {\r\n              item.editType = 0;\r\n            });\r\n            data.push(this.suntotal(data)); //小计\r\n            accountEsTotal(this.accountObj).then((res) => {\r\n              //合计\r\n              let alltotal = res.data;\r\n              alltotal.total = \"合计\";\r\n              alltotal.projectName = \"合计\";\r\n              alltotal._disabled = true;\r\n              data.push(alltotal);\r\n            });\r\n            this.tbAccount.data = data;\r\n            this.pageTotal = res.data.total || 0;\r\n            this.setMyStyle(this.tbAccount.data.length);\r\n\r\n            this.editIndex = -1;\r\n            this.columnsIndex = -1;\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //小计\r\n    suntotal(array) {\r\n      let curusedreadings = 0;\r\n      let transformerullage = 0;\r\n      let accountmoney = 0;\r\n      array.forEach(function (item) {\r\n        if (item.effective === 1) {\r\n          curusedreadings += item.curusedreadings;\r\n          transformerullage += item.transformerullage;\r\n          accountmoney += item.accountmoney;\r\n        }\r\n      });\r\n      return {\r\n        curusedreadings: curusedreadings,\r\n        transformerullage: transformerullage,\r\n        accountmoney: accountmoney,\r\n        total: \"小计\",\r\n        projectName: \"小计\",\r\n        _disabled: true,\r\n      };\r\n    },\r\n    //重置\r\n    onResetHandle() {\r\n      this.accountObj = {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        company: this.company,\r\n        projectName: \"\", //项目名称\r\n        country: Number(this.country), //所属部门\r\n        ammeterName: \"\", //电表户号/协议编码\r\n        stationName: \"\",\r\n        accountType: \"1\", //台账类型\r\n        accountestype: 3, //台账类型\r\n        supplybureauammetercode: \"\",\r\n        countryName: this.countryName,\r\n      };\r\n      this.getAccountMessages();\r\n    },\r\n    //计算单价\r\n    unitPrice(row) {\r\n      let version = indexData.version;\r\n      let accountmoney = row.accountmoney;\r\n      let curusedreadings = row.curusedreadings;\r\n      let taxamount = row.taxamount;\r\n      if (accountmoney != null && curusedreadings != null) {\r\n        let total = null;\r\n        if (curusedreadings == 0) {\r\n          total = 0;\r\n        } else {\r\n          // if (\"ln\" == this.version) total = (accountmoney - taxamount) / curusedreadings;\r\n          // else\r\n          total = accountmoney / curusedreadings;\r\n        }\r\n\r\n        row.unitpirce = total.toFixed(2);\r\n      }\r\n    },\r\n    //验证单价\r\n    validateUnitPrice(data) {\r\n      let category = data.category; //电表描述类型\r\n      let ammeteruse = data.ammeteruse; //电表用途\r\n      let unitpirce = data.unitpirce; //台账单价\r\n      //if(!judge_negate(category) && !judge_recovery(ammeteruse))\r\n      {\r\n        // if (unitpirce) {\r\n        //   if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n        //     this.errorTips(\r\n        //       \"集团要求单价范围在0.3~2元，此台账单价: \" +\r\n        //         unitpirce +\r\n        //         \" 已超过范围，请确认！\"\r\n        //     );\r\n        //   }\r\n        // }\r\n        if (unitpirce) {\r\n          if (unitpirce != null && unitpirce < unitpirceMax1) {\r\n            // if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n            this.errorTips(\r\n              \"单价范围必须大于0.1元，此台账单价: \" + unitpirce + \"不在范围内，请确认！\"\r\n            );\r\n          }\r\n        }\r\n      }\r\n    },\r\n    remove() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      if (data == null || data.length === 0) {\r\n        this.errorTips(\"请选择要删除的数据\");\r\n        return;\r\n      }\r\n      this.$Modal.confirm({\r\n        title: \"提示\",\r\n        content: \"<p>是否确认删除选中信息？</p>\",\r\n        onOk: () => {\r\n          let b = true;\r\n          let ids = \"\";\r\n          let array = this.tbAccount.data;\r\n          let total = this.pageTotal;\r\n          for (let i = 0; i < data.length; i++) {\r\n            let item = data[i];\r\n            if (item.pcid != null && item.pcid.length > 0) {\r\n              if (item.pabriid) {\r\n                b = false;\r\n              }\r\n              ids += item.pcid + \",\";\r\n            } else {\r\n              for (let j = array.length - 1; j >= 0; j--) {\r\n                let jj = array[j];\r\n                if (jj.ammeterid === item.ammeterid) {\r\n                  array.splice(j, 1);\r\n                  total = total - 1;\r\n                }\r\n              }\r\n            }\r\n          }\r\n          this.pageTotal = total;\r\n          if (b) {\r\n            if (ids.length > 0) {\r\n              removeAccountEs(ids).then((res) => {\r\n                if (res.data.code == 0) {\r\n                  this.$Message.success(\"删除成功\");\r\n                  this.getAccountMessages();\r\n                }\r\n              });\r\n            }\r\n          } else {\r\n            this.errorTips(\"选中信息中有信息还没有跟归集单解除关联，请先解除关联\");\r\n          }\r\n        },\r\n        onCancel: () => {},\r\n      });\r\n    },\r\n    openAddBillPerModal(name) {\r\n      if (name === \"current\") {\r\n        this.selectedAccount();\r\n      } else if (name === \"all\") {\r\n        this.selectedAllAccount();\r\n      }\r\n    },\r\n    //加入归集单，全部有效台账\r\n    selectedAllAccount() {\r\n      let that = this;\r\n      that.spinShow = true;\r\n      selectIdsByEsParams(this.accountObj).then((res) => {\r\n        that.spinShow = false;\r\n        if (res.data.length == 0) {\r\n          that.errorTips(\"无有效数据可加入归集单\");\r\n        } else {\r\n          that.$refs.addBillPer.initAmmeter(res.data, 11, this.accountObj.country);\r\n        }\r\n      });\r\n    },\r\n    selectedAccount() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      let b = 1;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        let ids = [];\r\n        data.forEach(function (item) {\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n          ids.push(item.pcid);\r\n        });\r\n        if (b === 1) {\r\n          this.$refs.addBillPer.initAmmeter(ids, 11, this.accountObj.country);\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 需要稽核\r\n     */\r\n    addPreserveGJ() {\r\n      let dataL = this.$refs.accountEsTable.getSelection();\r\n      if (dataL == null || dataL.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        this.addSubmitDataGJ(dataL);\r\n      }\r\n    },\r\n    addPreserveGJAll() {\r\n      let params = this.accountObj;\r\n      params.pageNum = 1;\r\n      params.pageSize = 20000;\r\n      let req = {\r\n        url: \"/business/account/selfAccountList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      this.accountTb.loading = true;\r\n      axios.request(req).then((res) => {\r\n        let dataL = res.data.rows;\r\n        this.addSubmitDataGJ(dataL);\r\n      });\r\n    },\r\n    //提交归集单数据\r\n    addSubmitDataGJ(data1) {\r\n      let a = [];\r\n      let b = 1;\r\n      let data = data1.filter((item) => item.effective == 1);\r\n      let str = \"\";\r\n      let str1 = \"\";\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let obj = requiredFieldValidator(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status != 1) {\r\n            b = 3;\r\n          }\r\n          if (\r\n            \"sc\" == version &&\r\n            item.unitpirce > 2 &&\r\n            (item.unitpirceold == null || item.unitpirceold < 2) &&\r\n            that.valiprice\r\n          ) {\r\n            b = 4;\r\n            str += item.ammetercode + \",\";\r\n          }\r\n        });\r\n        if (b == 1) {\r\n          if (str1.length > 0) {\r\n            this.$Notice.warning({\r\n              title: \"注意\",\r\n              desc: str1,\r\n              duration: 0,\r\n            });\r\n          }\r\n          if (submitData.length > 0) {\r\n            this.submit = submitData;\r\n            this.submit2 = submitData;\r\n            this.preserveSc();\r\n          }\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\r\n            \"电表/协议编号为【\" +\r\n              str +\r\n              \"】的台账单价已经超过2元，请发OA邮件给省公司审核，通过后才可加入归集单！\"\r\n          );\r\n        }\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          that.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n      }\r\n    },\r\n    /**\r\n     * ---\r\n     */\r\n    openCompletedPreModal() {\r\n      this.$refs.completedPre.initAmmeter(this.accountObj.country, 11);\r\n    },\r\n    againJoin() {\r\n      let data = this.$refs.accountEsTable.getSelection();\r\n      let b = true;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要重新加入归集单的台账\");\r\n      } else {\r\n        let ids = \"\";\r\n        data.forEach(function (item) {\r\n          let status = item.status;\r\n          if (status != 5) {\r\n            b = false;\r\n          }\r\n          ids += item.pcid + \",\";\r\n        });\r\n        if (b) {\r\n          againJoin(ids).then((res) => {\r\n            if (res.data.code == 0) {\r\n              this.$Message.info({\r\n                content: \"提示：操作成功\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              this.getAccountMessages();\r\n            }\r\n          });\r\n        } else {\r\n          this.errorTips(\"只有已退回的台账才能重新加入归集单\");\r\n        }\r\n      }\r\n    },\r\n    refresh() {\r\n      let obj = this;\r\n      obj.showAlarmModel = false;\r\n      setTimeout(function () {\r\n        obj.getAccountMessages();\r\n      }, 200);\r\n    },\r\n    beforeLoadData(data, str) {\r\n      var cols = [],\r\n        keys = [];\r\n      for (var i = 0; i < this.tbAccount.exportColumns.length; i++) {\r\n        cols.push(this.tbAccount.exportColumns[i].title);\r\n        keys.push(this.tbAccount.exportColumns[i].key);\r\n      }\r\n      const params = {\r\n        title: cols,\r\n        key: keys,\r\n        data: data,\r\n        autoWidth: true,\r\n        filename: str,\r\n      };\r\n      excel.export_array_to_excel(params);\r\n      return;\r\n    },\r\n    exportCsv(name) {\r\n      this.export.run = true;\r\n      if (name === \"current\") {\r\n        this.beforeLoadData(this.tbAccount.data, \"自有挂账台账导出数据\");\r\n      } else if (name === \"all\") {\r\n        let params = this.accountObj;\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n        let req = {\r\n          url: \"/business/accountEs/selectAccountEsList\",\r\n          method: \"get\",\r\n          params: params,\r\n        };\r\n        this.tbAccount.loading = true;\r\n        axios\r\n          .request(req)\r\n          .then((res) => {\r\n            this.tbAccount.loading = false;\r\n            if (res.data) {\r\n              let array = res.data.rows;\r\n              accountEsTotal(this.accountObj).then((res) => {\r\n                //合计\r\n                let alltotal = res.data;\r\n                alltotal.total = \"合计\";\r\n                alltotal._disabled = true;\r\n                array.push(alltotal);\r\n                this.beforeLoadData(array, \"自有挂账台账导出数据\");\r\n              });\r\n            }\r\n          })\r\n          .catch((err) => {\r\n            console.log(err);\r\n          });\r\n      }\r\n    },\r\n    validate() {\r\n      if (this.columnsIndex != 6 && this.columnsIndex != 7) {\r\n        let val = this.enterOperate(this.columnsIndex).data;\r\n        if (val) {\r\n          if (testNumber(val)) {\r\n            switch (this.columnsIndex) {\r\n              case 1:\r\n                this.validateStartdate();\r\n                break;\r\n              case 2:\r\n                this.validateEnddate();\r\n                break;\r\n              case 3:\r\n                this.validatecurusedreadings();\r\n                break;\r\n              case 4:\r\n                this.validateinputticketmoney();\r\n                break;\r\n              case 5:\r\n                this.validateinputtaxticketmoney();\r\n                break;\r\n            }\r\n          } else {\r\n            this.errorTips(\"请输入数字！\");\r\n          }\r\n        }\r\n      }\r\n    },\r\n    validateStartdate() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editStartDate;\r\n      let result = _verify_StartDate(data, val);\r\n      if (result) {\r\n        //失败就弹出提示内容\r\n        this.errorTips(result);\r\n        this.myStyle[this.editIndex].startdate = \"errorStle\";\r\n      } else {\r\n        this.myStyle[this.editIndex].startdate = \"myspan\";\r\n        data.startdate = val;\r\n        data.editType = 1;\r\n      }\r\n    },\r\n    validateEnddate() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editEndDate;\r\n      // 验证截止日期方法\r\n      let result = _verify_EndDate(data, val);\r\n      if (result) {\r\n        //失败就弹出提示内容，并将数据恢复初始化\r\n        this.errorTips(result);\r\n      } else {\r\n        data.enddate = val;\r\n        data.editType = 1;\r\n      }\r\n    },\r\n    validatecurusedreadings() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editcurusedreadings;\r\n      data.curusedreadings = val;\r\n      data.totalusedreadings = val;\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n    },\r\n    validatetransformerullage() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.edittransformerullage;\r\n      data.transformerullage = val;\r\n      data.editType = 1;\r\n    },\r\n    //普票\r\n    validateinputticketmoney() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editinputticketmoney;\r\n      //val = Math.abs(val); ////允许负数 Math.abs(val)\r\n      data.inputticketmoney = parseFloat(val);\r\n      data.ticketmoney = parseFloat(val);\r\n      data.accountmoney = data.inputticketmoney + data.inputtaxticketmoney;\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      this.validateUnitPrice(data);\r\n    },\r\n    //专票\r\n    validateinputtaxticketmoney() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editinputtaxticketmoney;\r\n      //val = Math.abs(val);\r\n      data.inputtaxticketmoney = parseFloat(val);\r\n      data.taxticketmoney = parseFloat(val);\r\n      data.accountmoney = data.inputticketmoney + data.inputtaxticketmoney;\r\n      //data.taxamount = Math.abs(countTaxamount(data));\r\n      data.taxamount = countTaxamountsr(data);\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      this.validateUnitPrice(data);\r\n    },\r\n    //计算税额\r\n    settaxrate() {\r\n      let val = this.edittaxrate;\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      data.taxrate = val;\r\n      data.taxamount = Math.abs(countTaxamount(data));\r\n      data.editType = 1;\r\n    },\r\n    validateaccountmoney() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editaccountmoney;\r\n      data.accountmoney = val; //允许负数 Math.abs(val)\r\n      data.editType = 1;\r\n      this.unitPrice(data);\r\n      this.validateUnitPrice(data);\r\n    },\r\n    setremark() {\r\n      let data = this.tbAccount.data[this.editIndex];\r\n      let val = this.editremark;\r\n      data.remark = val;\r\n      data.editType = 1;\r\n    },\r\n    setMyStyle(length) {\r\n      this.myStyle = [];\r\n      for (var i = 0; i < length; i++) {\r\n        this.myStyle.push({\r\n          startdate: \"myspan\",\r\n          enddate: \"myspan\",\r\n          curusedreadings: \"myspan\",\r\n          inputticketmoney: \"myspan\",\r\n          inputtaxticketmoney: \"myspan\",\r\n          taxrate: \"myspan\",\r\n          remark: \"myspan\",\r\n        });\r\n      }\r\n    },\r\n    //span点击事件将span换成输入框并且获取焦点\r\n    selectCall(row, index, columns, str) {\r\n      this.editStartDate = row.startdate;\r\n      this.editEndDate = row.enddate;\r\n      this.editcurusedreadings =\r\n        row.curusedreadings == null || row.curusedreadings === 0\r\n          ? null\r\n          : row.curusedreadings;\r\n      this.editinputticketmoney =\r\n        row.inputticketmoney == null || row.inputticketmoney === 0\r\n          ? null\r\n          : row.inputticketmoney;\r\n      this.editinputtaxticketmoney =\r\n        row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n          ? null\r\n          : row.inputtaxticketmoney;\r\n      this.edittaxrate =\r\n        row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n      this.editremark = row.remark;\r\n\r\n      this.editIndex = index;\r\n      this.columnsIndex = columns;\r\n\r\n      let a = this;\r\n      setTimeout(function () {\r\n        if (columns != 7) {\r\n          a.$refs[str + index + columns].focus();\r\n        }\r\n      }, 200);\r\n    },\r\n    //跳转到下一格\r\n    nextCell(data) {\r\n      let index = data.editIndex;\r\n      let columns = data.columnsIndex;\r\n      let row = \"\";\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n      } else if (index > -1 && columns === 7) {\r\n        //当跳转的最后一行最后一格的时候\r\n        if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n          index = 0;\r\n        } else {\r\n          index++;\r\n        }\r\n        columns = 1;\r\n      } else {\r\n        columns += 1;\r\n      }\r\n      data.editIndex = index;\r\n      data.columnsIndex = columns;\r\n      row = data.tbAccount.data[index];\r\n      if (row) {\r\n        data.editStartDate = row.startdate;\r\n        data.editEndDate = row.enddate;\r\n        data.editcurusedreadings =\r\n          row.curusedreadings == null || row.curusedreadings === 0\r\n            ? null\r\n            : row.curusedreadings;\r\n        data.editinputticketmoney =\r\n          row.inputticketmoney == null || row.inputticketmoney === 0\r\n            ? null\r\n            : row.inputticketmoney;\r\n        data.editinputtaxticketmoney =\r\n          row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n            ? null\r\n            : row.inputtaxticketmoney;\r\n        data.edittaxrate =\r\n          row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n        data.editremark = row.remark;\r\n      }\r\n\r\n      setTimeout(function () {\r\n        data.$refs[data.enterOperate(columns).str + index + columns].focus();\r\n      }, 200);\r\n    },\r\n    //根据列号返回对应的列名\r\n    enterOperate(number) {\r\n      let str = \"\";\r\n      let data = null;\r\n      switch (number) {\r\n        case 1:\r\n          str = \"startdate\";\r\n          data = this.editStartDate;\r\n          break;\r\n        case 2:\r\n          str = \"enddate\";\r\n          data = this.editEndDate;\r\n          break;\r\n        case 3:\r\n          str = \"curusedreadings\";\r\n          data = this.editcurusedreadings;\r\n          break;\r\n        case 4:\r\n          str = \"inputticketmoney\";\r\n          data = this.editinputticketmoney;\r\n          break;\r\n        case 5:\r\n          str = \"inputtaxticketmoney\";\r\n          data = this.editinputtaxticketmoney;\r\n          break;\r\n        case 6:\r\n          str = \"taxrate\";\r\n          data = this.edittaxrate;\r\n          break;\r\n        case 7:\r\n          str = \"remark\";\r\n          data = this.editremark;\r\n          break;\r\n      }\r\n      return { str: str, data: data };\r\n    },\r\n    pred() {\r\n      var lett = this;\r\n      let index = lett.editIndex;\r\n      let columns = lett.columnsIndex;\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n        lett.editIndex = index;\r\n        lett.columnsIndex = columns;\r\n        lett.editStartDate = lett.tbAccount.data[index].startdate;\r\n        setTimeout(function () {\r\n          lett.$refs[lett.enterOperate(columns).str + index + columns].focus();\r\n        }, 200);\r\n      } else {\r\n        lett.validate();\r\n        lett.setremark();\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    ellipsis(value) {\r\n      if (!value) return \"\";\r\n      if (value.length > 3) {\r\n        return value.slice(0, 3) + \"...\";\r\n      }\r\n      return value;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.page-class {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  .cl-table {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n  .button-bar {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n.mytable .ivu-table-cell {\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: normal;\r\n  word-break: break-all;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.accountEs .filter-divider {\r\n  margin: 0px;\r\n  text-align: center;\r\n}\r\n.accountEs .header-bar-show {\r\n  max-height: 300px;\r\n  padding-top: 14px;\r\n  overflow: inherit;\r\n  border-bottom: 1px solid #e8eaec;\r\n}\r\n.accountEs .header-bar-hide {\r\n  max-height: 0;\r\n  padding-top: 0;\r\n  overflow: hidden;\r\n  border-bottom: 0;\r\n}\r\n\r\n.mytable .myspan {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n}\r\n.mytable .errorStle {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n  color: red;\r\n}\r\n</style>\r\n"]}]}