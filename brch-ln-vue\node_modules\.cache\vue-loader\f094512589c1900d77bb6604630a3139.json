{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\budget\\budgetmanage\\modal-mon.vue?vue&type=style&index=0&id=2091d180&lang=less&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\budget\\budgetmanage\\modal-mon.vue", "mtime": 1754285403023}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749178874080}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoubGlzdC12aWV3IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAubGlzdC1pdGVtIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgd2lkdGg6IDMzLjMlOw0KICAgIC5sYWJlbCB7DQogICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIH0NCiAgICAubGFiZWwsDQogICAgLmlucHV0IHsNCiAgICAgIHdpZHRoOiA1MCU7DQogICAgICBoZWlnaHQ6IDQycHg7DQogICAgICBib3JkZXI6IDFweCBzb2xpZCAjZWVlOw0KICAgICAgcGFkZGluZzogNHB4IDEwcHg7DQogICAgfQ0KICB9DQp9DQo6OnYtZGVlcCAuaXZ1LXJhZGlvLXdyYXBwZXIgew0KICBmb250LXdlaWdodDogbm9ybWFsOw0KICBmb250LXNpemU6IDEzcHg7DQogIG1hcmdpbjogMTBweCAwIDIwcHggMTBweDsNCiAgJjpmaXJzdC1jaGlsZCB7DQogICAgbWFyZ2luLXJpZ2h0OiA1MHB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["modal-mon.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "modal-mon.vue", "sourceRoot": "src/view/budget/budgetmanage", "sourcesContent": ["<template>\r\n  <Modal v-model=\"showModal\" title=\"调整每月预算\" :width=\"1000\">\r\n    <div class=\"page-class common-wh\">\r\n      <Form ref=\"myform\" :model=\"formData\" :label-width=\"120\" :rules=\"ruleValidate\">\r\n        <div class=\"form-title\">全年预算总额</div>\r\n        <Row>\r\n          <Col span=\"8\">\r\n            <FormItem label=\"年份:\" prop=\"year\">\r\n              <Input :clearable=\"true\" v-model=\"formData.year\" disabled>\r\n                <span slot=\"append\">元</span>\r\n              </Input>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"8\">\r\n            <FormItem label=\"年度预算:\" prop=\"budgetAmount\">\r\n              <Input :clearable=\"true\" v-model=\"formData.budgetAmount\" disabled>\r\n                <span slot=\"append\">元</span>\r\n              </Input>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"8\">\r\n            <FormItem label=\"剩余预算:\" prop=\"subBudgetAmount\">\r\n              <Input :clearable=\"true\" v-model=\"formData.subBudgetAmount\" disabled>\r\n                <span slot=\"append\">元</span>\r\n              </Input>\r\n            </FormItem>\r\n          </Col>\r\n        </Row>\r\n        <div class=\"form-title\">每月预算设置</div>\r\n        <RadioGroup v-model=\"formData.single\" @on-change=\"formInputChange\">\r\n          <Radio label=\"average\">年度预算均值</Radio>\r\n          <Radio label=\"auto\">自定义</Radio>\r\n        </RadioGroup>\r\n        <div class=\"list-view\">\r\n          <div class=\"list-item\" v-for=\"(item, index) in 3\" :key=\"'a' + index\">\r\n            <div class=\"label\">月份</div>\r\n            <div class=\"label\">预算</div>\r\n          </div>\r\n          <template v-for=\"(item, index) in listForm\">\r\n            <div class=\"list-item\" :key=\"index\">\r\n              <div class=\"label\">{{ item.name }}</div>\r\n              <div class=\"input\">\r\n                <InputNumber\r\n                  :min=\"0\"\r\n                  v-model=\"formData[item.key]\"\r\n                  :disabled=\"formData.budgetAmount && formData.single == 'average'\"\r\n                  @on-change=\"inputChange\"\r\n                >\r\n                </InputNumber>\r\n              </div>\r\n              <!-- <Input\r\n                class=\"input\"\r\n                type=\"number\"\r\n                :min=\"1\"\r\n                v-model=\"formData[item.key]\"\r\n                :disabled=\"formData.budgetAmount && formData.single == 'average'\"\r\n                @on-change=\"inputChange\"\r\n              > -->\r\n            </div>\r\n          </template>\r\n        </div>\r\n      </Form>\r\n    </div>\r\n    <div slot=\"footer\">\r\n      <Button type=\"default\" class=\"cancelBtn\" @click=\"showModal = false\">取消</Button>\r\n      <Button type=\"primary\" class=\"okBtn\" @click=\"onModalOK\">确认</Button>\r\n    </div>\r\n  </Modal>\r\n</template>\r\n<script>\r\nimport { getBudgetManaMonInfo, getBudgetManaMonEdit } from \"@/api/budget/index\";\r\nimport { isEmpty } from \"@/libs/validate.js\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      showModal: false,\r\n      ruleValidate: {},\r\n      formData: {\r\n        city: \"\", //用于提交\r\n        budgetId: \"\", //用于提交\r\n        year: \"\",\r\n        budgetAmount: \"\",\r\n        subBudgetAmount: \"\",\r\n        single: \"average\",\r\n      },\r\n      listForm: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    for (let i = 0; i < 12; i++) {\r\n      this.formData[`budgetAmount${i + 1}`] = null;\r\n      this.listForm.push({\r\n        name: `${i + 1}月`,\r\n        key: `budgetAmount${i + 1}`,\r\n      });\r\n    }\r\n  },\r\n  computed: {\r\n    test() {\r\n      let data = null;\r\n      Object.assign(this.formData).forEach((key) => {\r\n        if (/budgetAmount/.test(key)) {\r\n          console.log(\"ddd\", this.formData[key]);\r\n        }\r\n      });\r\n      return data;\r\n    },\r\n  },\r\n  methods: {\r\n    //弹窗:打开\r\n    openModal() {\r\n      this.showModal = true;\r\n      this.getData();\r\n    },\r\n    formInputChange() {\r\n      if (this.formData.single == \"average\") {\r\n        let data = Number((this.formData.budgetAmount / 12).toFixed(2));\r\n        for (let key in this.formData) {\r\n          if (/budgetAmount/.test(key) && /[0-9]/.test(key)) {\r\n            this.formData[key] = data;\r\n          }\r\n        }\r\n      }\r\n      this.inputChange();\r\n    },\r\n    inputChange() {\r\n      let sum = null;\r\n      for (let key in this.formData) {\r\n        if (/budgetAmount/.test(key) && /[0-9]/.test(key)) {\r\n          sum += Number(this.formData[key]);\r\n        }\r\n      }\r\n      if (this.formData.budgetAmount) {\r\n        this.formData.subBudgetAmount = (\r\n          Number(this.formData.budgetAmount) - sum\r\n        ).toFixed(2);\r\n      } else {\r\n        this.formData.subBudgetAmount = 0;\r\n      }\r\n    },\r\n    //获取数据\r\n    getData() {\r\n      this.clearForm();\r\n      let year = new Date().getFullYear();\r\n      getBudgetManaMonInfo(year).then((res) => {\r\n        let data = res.data;\r\n        Object.keys(this.formData).forEach((key) => {\r\n          this.formData[key] = data[key];\r\n        });\r\n        let mons = [];\r\n        for (let key in this.formData) {\r\n          if (/budgetAmount/.test(key) && /[0-9]/.test(key)) {\r\n            mons.push(this.formData[key]);\r\n          }\r\n        }\r\n        //判断是否均值、自定义\r\n        this.formData.single = mons.every((item) => item == mons[0]) ? \"average\" : \"auto\";\r\n\r\n        this.inputChange();\r\n        this.$forceUpdate();\r\n      });\r\n    },\r\n    onModalOK() {\r\n      this.$refs.myform.validate((valid) => {\r\n        if (valid) {\r\n          //预算总和> 年度预算\r\n          if (this.formData.subBudgetAmount < 0) {\r\n            this.$Message.warning(\"各月预算总和>年度预算,请调整\");\r\n            return;\r\n          }\r\n          this.btnloading = true;\r\n          let year = new Date().getFullYear();\r\n          Object.assign(this.formData, {\r\n            year,\r\n          });\r\n          getBudgetManaMonEdit(this.formData).then((res) => {\r\n            this.btnloading = false;\r\n            this.$emit(\"refresh\");\r\n            this.clearForm();\r\n            this.$Message.success(\"调整成功\");\r\n            this.showModal = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    clearForm() {\r\n      this.$refs[\"myform\"].resetFields();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.list-view {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-bottom: 10px;\r\n  .list-item {\r\n    display: flex;\r\n    align-items: center;\r\n    width: 33.3%;\r\n    .label {\r\n      height: 100%;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n    }\r\n    .label,\r\n    .input {\r\n      width: 50%;\r\n      height: 42px;\r\n      border: 1px solid #eee;\r\n      padding: 4px 10px;\r\n    }\r\n  }\r\n}\r\n::v-deep .ivu-radio-wrapper {\r\n  font-weight: normal;\r\n  font-size: 13px;\r\n  margin: 10px 0 20px 10px;\r\n  &:first-child {\r\n    margin-right: 50px;\r\n  }\r\n}\r\n</style>\r\n"]}]}