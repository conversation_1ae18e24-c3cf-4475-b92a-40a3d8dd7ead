{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addSelfPowerAccount.vue?vue&type=template&id=25549dd2&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addSelfPowerAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}