{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\ln\\accountQuerylnList.vue?vue&type=style&index=0&id=01aabc95&lang=less&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\ln\\accountQuerylnList.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5hY2NvdW50YmlsbCAuZmlsdGVyLWRpdmlkZXIgewogICAgbWFyZ2luOiAwcHg7CiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KLmFjY291bnRiaWxsIC5oZWFkZXItYmFyLXNob3cgewogICAgbWF4LWhlaWdodDogMzAwcHg7CiAgICBwYWRkaW5nLXRvcDogMTRweDsKICAgIG92ZXJmbG93OiBpbmhlcml0OwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOGVhZWM7Cn0KLmFjY291bnRiaWxsIC5oZWFkZXItYmFyLWhpZGUgewogICAgbWF4LWhlaWdodDogMDsKICAgIHBhZGRpbmctdG9wOiAwOwogICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIGJvcmRlci1ib3R0b206IDA7Cn0KLm15dGFibGUgLml2dS10YWJsZS1jZWxsewogICAgcGFkZGluZy1sZWZ0OiAxcHg7CiAgICBwYWRkaW5nLXJpZ2h0OiAxcHg7CiAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgICB3aGl0ZS1zcGFjZTogbm9ybWFsOwogICAgd29yZC1icmVhazogYnJlYWstYWxsOwogICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKfQo="}, {"version": 3, "sources": ["accountQuerylnList.vue"], "names": [], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "accountQuerylnList.vue", "sourceRoot": "src/view/account/ln", "sourcesContent": ["<style lang=\"less\">\r\n    .accountbill .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .accountbill .header-bar-show {\r\n        max-height: 300px;\r\n        padding-top: 14px;\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .accountbill .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n    .mytable .ivu-table-cell{\r\n        padding-left: 1px;\r\n        padding-right: 1px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: normal;\r\n        word-break: break-all;\r\n        box-sizing: border-box;\r\n    }\r\n</style>\r\n<template>\r\n    <div>\r\n        <div class=\"accountbill\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"listForm\" :model=\"queryParams\" :label-width=\"80\" inline>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"起始期号：\" prop=\"startAccountno\" class=\"form-line-height\">\r\n                                <DatePicker v-model=\"startDatePicker\" type=\"month\" @on-change='startChange' placeholder=\"起始期号\" format=\"yyyyMM\" :style=\"formItemWidth\"></DatePicker>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"截止期号：\" prop=\"endAccountno\" class=\"form-line-height\">\r\n                                <DatePicker v-model=\"endDatePicker\" type=\"month\" @on-change='endChange' placeholder=\"截止期号\" format=\"yyyyMM\" :style=\"formItemWidth\"></DatePicker>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"局站名称:\" prop=\"stationName\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.stationName\" placeholder=\"请输入局站名称\" :style=\"formItemWidth\" />\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"项目名称:\" prop=\"projectname\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.projectname\" placeholder=\"请输入项目名称\" :style=\"formItemWidth\" />\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"供电局电表编号:\" prop=\"supplybureauammetercode\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.supplybureauammetercode\" placeholder=\"请输入供电局电表编号\"\r\n                                          :style=\"formItemWidth\"/>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"用电类型:\" prop=\"classifications\" class=\"form-line-height\">\r\n                                <Cascader :style=\"formItemWidth\" :data=\"classificationData\" v-model=\"classifications\"></Cascader>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"queryParams.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly :style=\"formItemWidth\"/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.country\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"站址编码:\" prop=\"stationaddresscode\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.stationaddresscode\" placeholder=\"请输入站址编码\"\r\n                                          :style=\"formItemWidth\"/>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <div align=\"right\">\r\n                        <Button style=\"margin-left: 5px;width:69px;\" type=\"success\" icon=\"ios-search\" @click=\"_onSearchHandle()\">搜索</Button>\r\n                        <Button style=\"margin-left: 5px;width:69px;\" type=\"info\" icon=\"ios-redo\" @click=\"_onResetHandle\">重置</Button>\r\n                        <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n                            <Button type='default'\r\n                                    style=\"margin-left: 5px\"\r\n                            >导出\r\n                                <Icon type='ios-arrow-down'></Icon>\r\n                            </Button>\r\n                            <DropdownMenu slot='list'>\r\n                                <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                                <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n                            </DropdownMenu>\r\n                        </Dropdown>\r\n                    </div>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n        </div>\r\n        <div>\r\n            <Page size=\"small\" :total=\"pageTotal\" :current=\"pageNum\" :page-size=\"pageSize\" show-elevator show-sizer show-total\r\n                  placement=\"top\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n            <Table ref=\"listTable\"\r\n                   border :loading=\"listTb.loading\"\r\n                   :columns=\"listTb.columns\"\r\n                   :data=\"insideData\"\r\n                   class=\"mytable\"></Table>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import {getcompany,getResCenter}from\"@/api/alertcontrol/alertcontrol\"\r\n    import {blist} from \"@/libs/tools\";\r\n    import axios from '@/libs/api.request';\r\n    import {getClassification,getUserdata,getUserByUserRole,getCountrysdata,getCountryByUserId} from '@/api/basedata/ammeter.js'\r\n    import {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\n    import CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import excel from '@/libs/excel'\r\n    import indexData from '@/config/index'\r\n\r\n    export default {\r\n        components: {CountryModal},\r\n        name:'pylonlnbgquery',\r\n        data () {\r\n            let renderCategory = (h, params) => {\r\n                var categoryname = \"\";\r\n                for (let item of this.categorys) {\r\n                    if (item.typeCode == params.row.category) {\r\n                        categoryname = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", categoryname);\r\n            };\r\n\r\n            return {\r\n                version:null,\r\n                formItemWidth: widthstyle,\r\n                categorys:[],//描述类型\r\n                queryParams:{\r\n                    startAccountno:'',//起始局站电费月台帐\r\n                    endAccountno:'',//截止局站电费月台帐\r\n                    projectname:'',//项目名称\r\n                    ammetercode:'',//电表户号/协议编码\r\n                    company:'',//分公司\r\n                    country:'',//所属部门\r\n                    ammeteruse:'',\r\n                    countryName:'',\r\n                    supplybureauammetercode:'',\r\n                    stationaddresscode:'',\r\n                },\r\n                startDatePicker:'',\r\n                endDatePicker:'',\r\n                companies:[],\r\n                departments:[],\r\n                isAdmin:false,\r\n                company:null,//用户默认公司\r\n                country:null,//用户默认所属部门\r\n                countryName:null,//用户默认所属部门\r\n                classificationData:[],//用电类型树\r\n                classifications:[],//选择的用电类型树\r\n                filterColl: true,//搜索面板展开\r\n                exportable:false,\r\n                accountStatus:[],\r\n                ammeteruseList:[],\r\n                export: {\r\n                    run: false,//是否正在执行导出\r\n                    data: \"\",//导出数据\r\n                    totalPage: 0,//一共多少页\r\n                    currentPage: 0,//当前多少页\r\n                    percent: 0,\r\n                    size: ********\r\n                },\r\n                subtotal:'',\r\n                alltotal:'',\r\n                url:'business/pylonlnbg/selectQuery',\r\n                listTb:{\r\n                    loading: false,\r\n                    columns:[\r\n                        {title: '项目名称', key: 'projectname', align: 'center',},\r\n                        {title: '供电局电表编号', key: 'supplybureauammetercode', align: 'center', width: 60,},\r\n                        {title: '局站', key: 'stationName', align: 'center',},\r\n                        {title: '用电类型', key: 'electrotypename', align: 'center',},\r\n                        {title: '站址编码', key: 'stationaddresscode', align: 'center',},\r\n                        {title: '期号', key: 'accountno', align: 'center',},\r\n                        {title: '起始日期',  key: 'startdate', align: 'center',},\r\n                        {title: '截止日期', key: 'enddate', align: 'center',},\r\n                        {title: '用电量', key: 'curusedreadings', align: 'center',},\r\n                        {title: '电价（元）', key: 'unitpirce', align: 'center',},\r\n                        {title: '包干电费（元）',key: 'taxticketmoney', align: 'center',},\r\n                        {title: '其他(元)', key: 'ullagemoney', align: 'center',},\r\n                        {title: '税率（%）', key: 'taxrate', align: 'center', },\r\n                        {title: '税额', key: 'taxamount', align: 'center', },\r\n                        {title: '实缴费用(元)', key: 'accountmoney', align: 'center', },\r\n                        {title: '总金额（不含税）', key: 'totalBHS', align: 'center', width: 90,},\r\n                        {title: '状态', key: 'status', align: 'center', width: 90,},\r\n                        {title: '类型描述', key: 'categoryname', align: 'center',render:renderCategory},\r\n                        {title: '录入人', key: 'inputname', align: 'center', width: 90,},\r\n                        {title: '归集单事项名称', key: 'note', align: 'center', width: 90,},\r\n                        {title: '备注', key: 'remark', align: 'center',\r\n                            render: (h, params) => {\r\n                                let str = ''\r\n                                let index = params.index;\r\n                                if (index < this.pageSize / 2) {\r\n                                    str = 'bottom'\r\n                                } else {\r\n                                    str = 'top'\r\n                                }\r\n\r\n                                return h('div', [\r\n                                    h('Tooltip', {\r\n                                        props: {placement: str}\r\n                                    }, [\r\n                                        h('span', {\r\n                                            style: {\r\n                                                display: 'inline-block',\r\n                                                width: params.column._width * 0.9 + 'px',\r\n                                                overflow: 'hidden',\r\n                                                textOverflow: 'ellipsis',\r\n                                                whiteSpace: 'nowrap',\r\n                                            },\r\n                                        }, params.row.remark),\r\n                                        h('span', {\r\n                                            slot: 'content',\r\n                                            style: {whiteSpace: 'normal', wordBreak: 'break-all'}\r\n                                        }, params.row.remark)\r\n                                    ])\r\n                                ])\r\n                            }\r\n                        },],\r\n                    data:[]\r\n                },\r\n                insideData:[],\r\n                pageTotal: 0,\r\n                pageNum: 1,\r\n                pageSize: 10,//当前页\r\n            }\r\n        },\r\n        methods: {\r\n            selectChange() {\r\n                let that = this;\r\n                if (this.queryParams.company != undefined) {\r\n                    if(this.queryParams.company == \"-1\"){\r\n                        that.queryParams.country = -1;\r\n                        that.queryParams.countryName = null;\r\n                    }else{\r\n                        getCountryByUserId(that.queryParams.company).then(res => {\r\n                            if(res.data.departments.length != 0){\r\n                                that.queryParams.country = res.data.departments[0].id;\r\n                                that.queryParams.countryName = res.data.departments[0].name;\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            },\r\n            //选择所属部门开始\r\n            chooseResponseCenter() {\r\n                if(this.queryParams.company == null || this.queryParams.company == \"-1\" ){\r\n                    this.$Message.info(\"请先选择分公司\");return;\r\n                }\r\n                this.$refs.countryModal.choose(this.queryParams.company);//所属部门\r\n            },\r\n            getDataFromModal(data) {\r\n                this.queryParams.country = data.id;\r\n                this.queryParams.countryName = data.name;\r\n                //选择所属部门结束\r\n            },\r\n            //翻页\r\n            handlePage(value) {\r\n                this.pageNum = value;\r\n                this.getAccountMessages();\r\n            },\r\n            //改变表格可显示数据数量\r\n            handlePageSize(value) {\r\n                this.pageSize = value;\r\n                this.getAccountMessages();\r\n            },\r\n            //向后台请求数据\r\n            getAccountMessages() {\r\n                this.setElectroyType();\r\n                let params = this.queryParams;\r\n                params.pageNum = this.pageNum;\r\n                params.pageSize = this.pageSize;\r\n                let req = {\r\n                    url : this.url,\r\n                    method : \"get\",\r\n                    params : params\r\n                };\r\n                let array = [];\r\n                this.listTb.loading = true;\r\n                axios.request(req).then(res => {\r\n                    this.listTb.loading = false;\r\n                    if (res.data) {\r\n                        array = res.data.rows;\r\n                        this.totalBHS(array)\r\n                        array.push(this.suntotal(array))//小计\r\n                        this.pageTotal = res.data.total || 0\r\n                        this.queryTotal(this.queryParams).then(res => {//合计\r\n                            let alltotal = res.data\r\n                            alltotal.total = '合计'\r\n                            alltotal.projectname= '合计',\r\n                                alltotal._disabled = true\r\n                            array.push(alltotal)\r\n                        });\r\n                        this.insideData = array\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            },\r\n            queryTotal(obj){\r\n                return axios.request({\r\n                    url: '/business/pylonlnbg/queryTotal',\r\n                    data:obj,\r\n                    method: 'post'\r\n                })\r\n            },\r\n            totalBHS(array){\r\n                let categorys = this.categorys\r\n                let accountStatus = this.accountStatus\r\n                array.forEach(function (item) {\r\n                    let taxticketmoney = item.taxticketmoney;//专票含税金额\r\n                    let ticketmoney = item.ticketmoney;//普票含税金额\r\n                    let ullagemoney = item.ullagemoney;//其他费用\r\n                    let taxamount = item.taxamount;//税额\r\n                    let total = ticketmoney +(taxticketmoney - taxamount) +ullagemoney\r\n                    total = total.toFixed(2)\r\n                    item.totalBHS = total\r\n\r\n                    var categoryname = \"\";\r\n                    for (let it of categorys) {\r\n                        if (it.typeCode == item.category) {\r\n                            categoryname = it.typeName;\r\n                            break;\r\n                        }\r\n                    }\r\n                    item.categoryname = categoryname\r\n\r\n                    var status = \"\";\r\n                    for (let i of accountStatus) {\r\n                        if (i.typeCode == item.status) {\r\n                            status = i.typeName;\r\n                            break;\r\n                        }\r\n                    }\r\n                    item.status = status\r\n                })\r\n            },\r\n            //小计\r\n            suntotal(array) {\r\n                let taxamount = 0\r\n                let ullagemoney = 0\r\n                let accountmoney = 0\r\n                let taxticketmoney = 0\r\n                let curusedreadings = 0\r\n                let total = 0\r\n                array.forEach(function (item) {\r\n                    if (item.effective == 1) {\r\n                        taxamount += item.taxamount\r\n                        ullagemoney += item.ullagemoney\r\n                        accountmoney += item.accountmoney\r\n                        taxticketmoney += item.taxticketmoney\r\n                        curusedreadings += item.curusedreadings\r\n                        total += parseFloat(item.totalBHS)\r\n                    }\r\n                })\r\n                return {\r\n                    taxamount: taxamount.toFixed(2),\r\n                    ullagemoney: ullagemoney.toFixed(2),\r\n                    accountmoney: accountmoney.toFixed(2),\r\n                    taxticketmoney: taxticketmoney.toFixed(2),\r\n                    totalBHS:total.toFixed(2),\r\n                    curusedreadings: curusedreadings.toFixed(2),\r\n                    total: '小计',\r\n                    projectname: '小计',\r\n                    _disabled: true\r\n                }\r\n            },\r\n            _onSearchHandle(){\r\n                if(this.queryParams.countryName == \"\"){\r\n                    this.queryParams.country = \"-1\";\r\n                }\r\n                this.pageNum = 1\r\n                this.getAccountMessages()\r\n            },\r\n            _onResetHandle(){\r\n                this.queryParams = {company:null,country:null,countryName:null}\r\n                this.startDatePicker = ''\r\n                this.endDatePicker = ''\r\n                this.classifications = []\r\n                this.queryParams.company= this.company;\r\n                this.queryParams.country= Number(this.country);\r\n                this.queryParams.countryName = this.countryName;\r\n                this.getAccountMessages()\r\n            },\r\n            startChange(year){\r\n                this.queryParams.startAccountno = year\r\n            },\r\n            endChange(year){\r\n                this.queryParams.endAccountno = year\r\n            },\r\n            setElectroyType(){\r\n                let types = this.classifications;\r\n                this.queryParams.electrotype = types[types.length-1]\r\n            },\r\n            beforeLoadData(data) {\r\n                var cols=[],keys=[]\r\n                for (var i = 0; i < this.listTb.columns.length; i++) {\r\n                    cols.push(this.listTb.columns[i].title)\r\n                    keys.push(this.listTb.columns[i].key)\r\n                }\r\n                const params = {\r\n                    title: cols,\r\n                    key: keys,\r\n                    data: data,\r\n                    autoWidth: true,\r\n                    filename: '铁塔包干台账导出数据'\r\n                }\r\n                excel.export_array_to_excel(params)\r\n                return\r\n            },\r\n            exportCsv(name) {\r\n                this.export.run = true\r\n                if (name === 'current') {\r\n                    this.beforeLoadData(this.insideData)\r\n                } else if (name === 'all') {\r\n                    this.setElectroyType();\r\n                    let params = this.queryParams;\r\n                    params.pageNum = 1;\r\n                    params.pageSize = this.export.size;\r\n                    let req = {\r\n                        url : this.url,\r\n                        method : \"get\",\r\n                        params : params\r\n                    };\r\n                    axios.request(req).then(res => {\r\n                        if (res.data) {\r\n                            let array = res.data.rows\r\n                            this.queryTotal(this.queryParams).then(res => {//合计\r\n                                let alltotal = res.data\r\n                                alltotal.total = '合计'\r\n                                alltotal._disabled=true\r\n                                array.push(alltotal)\r\n                                this.beforeLoadData(array)\r\n                            });\r\n                        }\r\n                    }).catch(err => {\r\n                        console.log(err);\r\n                    });\r\n                }\r\n            },\r\n            getUserData(){\r\n                let that = this;\r\n                getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                    if(res.data.companies.length != 0){\r\n                        let companies = res.data.companies;\r\n                        if(res.data.companies[0].id == \"2600000000\"){\r\n                            companies = that.companies;\r\n                        }\r\n                        that.company = companies[0].id;\r\n                        that.queryParams.company = companies[0].id;\r\n                    }\r\n                    if(res.data.departments.length != 0){\r\n                        let departments = res.data.departments;\r\n                        if(res.data.companies[0].id == \"2600000000\" && that.departments.length != 0){\r\n                            departments = that.departments\r\n                        }\r\n                        that.country = departments[0].id;\r\n                        that.countryName = departments[0].name;\r\n                        that.queryParams.country = Number(departments[0].id);\r\n                        that.queryParams.countryName = departments[0].name;\r\n                    }\r\n\r\n                    that.pageNum = 1\r\n                    that.getAccountMessages();\r\n                });\r\n            },\r\n        },\r\n        mounted() {\r\n            this.version = indexData.version\r\n            this.accountStatus = blist(\"accountStatus\");\r\n            this.ammeteruseList = blist('ammeterUse');\r\n            this.categorys = blist(\"ammeterCategory\");\r\n            let that = this;\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                    that.getUserData();\r\n                });\r\n            });\r\n\r\n            getClassification().then(res => {//用电类型\r\n                this.classificationData = res.data;\r\n            });\r\n\r\n        }\r\n    }\r\n</script>"]}]}