{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\changeAmmeter.vue?vue&type=template&id=3e493c28&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\changeAmmeter.vue", "mtime": 1754285403017}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}