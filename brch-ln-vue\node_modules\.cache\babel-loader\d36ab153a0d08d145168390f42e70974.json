{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\statistics\\energymeter\\modal-list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\statistics\\energymeter\\modal-list.vue", "mtime": 1754285403055}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": 1741317456427}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["modal-list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,SAAA,iBAAA,EAAA,kBAAA,QAAA,wBAAA;AACA,SAAA,cAAA,QAAA,wBAAA;AACA,SAAA,KAAA,QAAA,cAAA;AACA,SAAA,OAAA,QAAA,aAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AAEA,eAAA;AACA,EAAA,KAAA,EAAA,CAAA,OAAA,CADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,SAAA,EAAA,KADA;AAEA,MAAA,UAAA,EAAA,KAFA;AAEA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AADA,OAHA;AAMA,MAAA,WAAA,EAAA,CACA;AADA,OANA;AASA,MAAA,aAAA,EAAA,EATA;AAUA,MAAA,KAAA,EAAA;AACA,QAAA,WAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAEA;AACA,QAAA,OAAA,EAAA,EAHA;AAGA;AACA,QAAA,OAAA,EAAA;AAJA,OAVA;AAgBA,MAAA,QAAA,EAAA,EAhBA;AAiBA,MAAA,QAAA,EAAA;AACA,QAAA,OAAA,EAAA,KADA;AAEA,QAAA,SAAA,EAAA,CAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,QAAA,EAAA,EAJA;AAIA;AACA,QAAA,OAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,SAFA;AAGA,UAAA,KAAA,EAAA;AAHA,SADA,EAMA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA;AAHA,SANA,EAWA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA;AAHA,SAXA,EAgBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA;AAHA,SAhBA,EAqBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA;AAHA,SArBA;AALA,OAjBA;AAkDA,MAAA,SAAA,EAAA,EAlDA;AAmDA,MAAA,QAAA,EAAA,EAnDA;AAoDA,MAAA,YAAA,EAAA;AApDA,KAAA;AAsDA,GAzDA;AA0DA,EAAA,OA1DA,qBA0DA;AACA,SAAA,KAAA,CAAA,KAAA,GAAA,KAAA,CAAA,mBAAA,CAAA;AACA,QAAA,IAAA,GAAA,EAAA;;AACA,SAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA,MAAA,IAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,CAAA,GAAA,CADA;AAEA,QAAA,IAAA,EAAA,IAFA;AAGA,QAAA,GAAA,EAAA,IAHA;AAIA,QAAA,OAAA,EAAA,IAJA;AAKA,QAAA,OAAA,EAAA;AALA,OAAA;AAOA;;AACA,SAAA,QAAA,GAAA,IAAA;AACA,GAvEA;AAwEA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,SAFA,qBAEA,GAFA,EAEA;AACA,WAAA,SAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,GAAA;;AACA,WAAA,cAAA,GAHA,CAGA;;AACA,KANA;AAOA;AACA,IAAA,cARA,4BAQA;AACA,WAAA,KAAA,CAAA,WAAA,EAAA,WAAA;;AACA,WAAA,eAAA,GAFA,CAEA;;AACA,KAXA;AAYA;AACA,IAAA,eAbA,6BAaA;AACA,WAAA,aAAA,qBAAA,KAAA,WAAA;AACA,WAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,KAAA,aAAA;AACA,KAhBA;AAiBA;AACA,IAAA,UAlBA,sBAkBA,MAlBA,EAkBA;AAAA;;AACA,MAAA,MAAA,CAAA,MAAA,CAAA,MAAA,EAAA,KAAA,UAAA,EAAA;AACA,QAAA,UAAA,EAAA,MAAA,CAAA;AADA,OAAA;AAGA,aAAA,MAAA,CAAA,OAAA;AACA,WAAA,QAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,cAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,OAAA,GAAA,KAAA;AACA,YAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,QAAA,KAAA,CAAA,QAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,SAAA,GAAA,IAAA;AACA,OALA;AAMA;AA9BA;AAxEA,CAAA", "sourcesContent": ["<template>\r\n  <Modal v-model=\"showModal\" :title=\"title\" width=\"70%\">\r\n    <div class=\"charge-info common-wh\">\r\n      <div class=\"query-box\">\r\n        <Form ref=\"queryform\" :model=\"queryParams\" :label-width=\"100\">\r\n          <Row class=\"form-row\">\r\n            <div class=\"query-btns\">\r\n              <Button\r\n                type=\"success\"\r\n                class=\"queryBtn\"\r\n                icon=\"ios-search\"\r\n                @click=\"_onSearchHandle\"\r\n                >搜索\r\n              </Button>\r\n              <Button type=\"info\" class=\"queryBtn\" icon=\"ios-redo\" @click=\"_onResetHandle\"\r\n                >重置</Button\r\n              >\r\n            </div>\r\n          </Row>\r\n        </Form>\r\n      </div>\r\n\r\n      <cl-table\r\n        ref=\"clTable\"\r\n        :height=\"400\"\r\n        :query-params=\"queryParams\"\r\n        :columns=\"tableSet.columns\"\r\n        :loading=\"tableSet.loading\"\r\n        :total=\"tableSet.total\"\r\n        :pageSize=\"tableSet.pageSize\"\r\n        :data=\"tableList\"\r\n        :sum-columns=\"[]\"\r\n        @on-query=\"tableQuery\"\r\n        :searchable=\"false\"\r\n        :exportable=\"false\"\r\n      >\r\n      </cl-table>\r\n    </div>\r\n    <div slot=\"footer\">\r\n      <Button type=\"default\" class=\"cancelBtn\" @click=\"showModal = false\">取消</Button>\r\n    </div>\r\n  </Modal>\r\n</template>\r\n<script>\r\nimport { getUserByUserRole, getCountryByUserId } from \"@/api/basedata/ammeter\";\r\nimport { getAmmeterInfo } from \"@/api/statistics/index\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport { noEmpty } from \"@/libs/util\";\r\nimport axios from \"@/libs/api.request\";\r\n\r\nexport default {\r\n  props: [\"title\"],\r\n  data() {\r\n    return {\r\n      showModal: false,\r\n      btnloading: false, //确认提交\r\n      pageParams: {\r\n        type: \"\",\r\n      },\r\n      queryParams: {\r\n        //查询参数\r\n      },\r\n      queryedParams: {},\r\n      dicts: {\r\n        stationType: [],\r\n        company: [], //所属分公司\r\n        country: [], //所属部门\r\n        isAdmin: false,\r\n      },\r\n      listData: [],\r\n      tableSet: {\r\n        loading: false,\r\n        pageTotal: 0,\r\n        pageNum: 1,\r\n        pageSize: 10, //当前页\r\n        columns: [\r\n          {\r\n            title: \"部门名称\",\r\n            key: \"orgName\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"局站编码\",\r\n            key: \"stationcode\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"局站名称\",\r\n            key: \"stationname\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"电表编号\",\r\n            key: \"meterCode\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"项目名称\",\r\n            key: \"projectname\",\r\n            align: \"center\",\r\n          },\r\n        ],\r\n      },\r\n      tableList: [],\r\n      curMonth: {},\r\n      list_loading: true,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.dicts.state = blist(\"budget_audit_type\");\r\n    let arr2 = [];\r\n    for (let i = 0; i < 12; i++) {\r\n      arr2.push({\r\n        name: i + 1,\r\n        cons: null,\r\n        cnt: null,\r\n        consCnt: null,\r\n        noReach: false,\r\n      });\r\n    }\r\n    this.listData = arr2;\r\n  },\r\n  methods: {\r\n    //弹窗:打开\r\n    openModal(row) {\r\n      this.showModal = true;\r\n      this.pageParams = row;\r\n      this._onResetHandle(); //下拉选项\r\n    },\r\n    //表格-筛选-重置\r\n    _onResetHandle() {\r\n      this.$refs[\"queryform\"].resetFields();\r\n      this._onSearchHandle(); //搜索列表\r\n    },\r\n    //表格-筛选-搜索\r\n    _onSearchHandle() {\r\n      this.queryedParams = { ...this.queryParams };\r\n      this.$refs.clTable.query(this.queryedParams);\r\n    },\r\n    //查询\r\n    tableQuery(params) {\r\n      Object.assign(params, this.pageParams, {\r\n        pageNumber: params.pageNum,\r\n      });\r\n      delete params.pageNum;\r\n      this.tableSet.loading = true;\r\n      getAmmeterInfo(params).then((res) => {\r\n        this.tableSet.loading = false;\r\n        let data = res.data.rows;\r\n        this.tableSet.total = res.data.total;\r\n        this.tableList = data;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.charge-info {\r\n  font-weight: 400;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .list-box {\r\n    margin: 0 20px 10px 20px;\r\n    padding: 10px 10px 0 10px;\r\n    background: #f6f8fa;\r\n    .list-title {\r\n      font-size: 14px;\r\n      margin-bottom: 10px;\r\n      .title {\r\n        font-weight: bold;\r\n      }\r\n    }\r\n    .list-con {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      position: relative;\r\n      .top {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        .title {\r\n          font-size: 14px;\r\n          margin-bottom: 10px;\r\n        }\r\n      }\r\n      .list-item {\r\n        width: ~\"calc(25% - 10px)\";\r\n        height: 87px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        margin: 0 5px;\r\n        margin-bottom: 10px;\r\n        padding: 10px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        cursor: pointer;\r\n\r\n        .value {\r\n          text-align: center;\r\n          // margin: 6px 0;\r\n          font-size: 14px;\r\n          font-weight: bold;\r\n        }\r\n        .value-red {\r\n          color: red;\r\n        }\r\n        .rate {\r\n          display: flex;\r\n          .rate-1 {\r\n            width: 50%;\r\n            text-align: left;\r\n          }\r\n        }\r\n        .list-no {\r\n          text-align: center;\r\n          margin: auto;\r\n        }\r\n      }\r\n      .list-active {\r\n        background: #3581f442;\r\n      }\r\n    }\r\n  }\r\n  .list-title2 {\r\n    font-size: 14px;\r\n    margin: 0 20px;\r\n    font-weight: bold;\r\n  }\r\n  .query-btns {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n::v-deep .ivu-modal {\r\n  top: 50px !important;\r\n}\r\n::v-deep .ivu-modal-content .ivu-modal-body form {\r\n  padding: 10px 10px 0px 10px !important;\r\n}\r\n::v-deep .two-input .ivu-form-item-content {\r\n  display: flex;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/view/statistics/energymeter"}]}