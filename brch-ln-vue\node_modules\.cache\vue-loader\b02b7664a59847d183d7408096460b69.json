{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\warnAnalysis.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\warnAnalysis.vue", "mtime": 1754285403030}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgImVjaGFydHMtZ2wiOw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiY2l0eUdyZWVuTGFuZCIsDQogIGNvbXBvbmVudHM6IHt9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBlbGVjdHJpY3RUb3RhbDogIiIsDQogICAgICBpc0Z1bGxzY3JlZW46IGZhbHNlLA0KICAgICAgb3B0aW9uRGF0YTogWw0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogIuiAg+aguOaIkOe7qemrmOS6jumihOitpuWAvCIsDQogICAgICAgICAgdmFsdWU6IDIyMTE2LA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogIuiAg+aguOaIkOe7qeS9juS6jumihOitpuWAvCIsDQogICAgICAgICAgdmFsdWU6IDE2NjE2LA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICAgIG5vd1N0ZXA6IDAsDQogICAgICBtYXhTdGVwOiAzNSwNCiAgICAgIGhlaWdodDogOTUsDQogICAgfTsNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICBlbGVjdHJpY1N0cmN1Y3RPYmo6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICB9LA0KICAgIHdhcm5WYWx1ZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgIH0sDQogIH0sDQoNCiAgd2F0Y2g6IHsNCiAgICBlbGVjdHJpY1N0cmN1Y3RPYmo6IHsNCiAgICAgIGltbWVkaWF0ZTogdHJ1ZSwNCiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsNCiAgICAgICAgaWYgKG5ld1ZhbCA9PSB1bmRlZmluZWQgfHwgbmV3VmFsID09IG51bGwgfHwgbmV3VmFsID09ICIiKSByZXR1cm47DQogICAgICAgIC8vIOaAu+aVsOmHjw0KICAgICAgICB0aGlzLmVsZWN0cmljdFRvdGFsID0gbmV3VmFsLmVsZWN0cmljdFRvdGFsOw0KICAgICAgICAvLyAvLyDlpJbotK3nu7/nlLUNCiAgICAgICAgLy8gdGhpcy4kc2V0KHRoaXMub3B0aW9uRGF0YVswXSwgInZhbHVlIiwgbmV3VmFsLm91dHNvdXJjaW5nR3JlZW5Qb3dlcik7DQogICAgICAgIC8vIOWklui0reeBq+eUtQ0KICAgICAgICB0aGlzLiRzZXQodGhpcy5vcHRpb25EYXRhWzBdLCAidmFsdWUiLCBuZXdWYWwudGhhbldhcm5OdW0pOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5vcHRpb25EYXRhWzFdLCAidmFsdWUiLCBuZXdWYWwubG93V2Fybk51bSk7DQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmluaXQoKTsNCiAgICAgICAgfSk7DQogICAgICB9LA0KICAgICAgZGVlcDogdHJ1ZSwgLy8g5rex5bqm55uR5ZCsDQogICAgfSwNCiAgfSwNCg0KICBtZXRob2RzOiB7DQogICAgaW5pdCgpIHsNCiAgICAgIGxldCBfdGhpcyA9IHRoaXM7DQogICAgICAvL+aehOW7ujNk6aW854q25Zu+DQogICAgICBsZXQgbXlDaGFydCA9IHRoaXMuJGVjaGFydHMuaW5pdChkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgic3RydWN0dXJlM0QtY2hhcnRzIikpOw0KICAgICAgLy8g5Lyg5YWl5pWw5o2u55Sf5oiQIG9wdGlvbg0KICAgICAgdGhpcy5vcHRpb24gPSB0aGlzLmdldFBpZTNEKF90aGlzLm9wdGlvbkRhdGEsIDAuNzUpOw0KICAgICAgbXlDaGFydC5zZXRPcHRpb24odGhpcy5vcHRpb24pOw0KICAgICAgLy/mmK/lkKbpnIDopoFsYWJlbOaMh+W8lee6v++8jOWmguaenOimgeWwsea3u+WKoOS4gOS4qumAj+aYjueahDJk6aW854q25Zu+5bm26LCD5pW06KeS5bqm5L2/5b6XbGFiZWxMaW5l5ZKMM2TnmoTppbznirblm77lr7npvZDvvIzlubblho3mrKFzZXRPcHRpb24NCiAgICAgIHRoaXMub3B0aW9uLnNlcmllcy5wdXNoKHsNCiAgICAgICAgbmFtZTogInBpZTJkIiwNCiAgICAgICAgdHlwZTogInBpZSIsDQogICAgICAgIGxhYmVsOiB7DQogICAgICAgICAgbm9ybWFsOiB7DQogICAgICAgICAgICBwb3NpdGlvbjogImlubmVyIiwNCiAgICAgICAgICAgIHNob3c6IGZhbHNlLA0KICAgICAgICAgIH0sDQogICAgICAgIH0sDQogICAgICAgIGxhYmVsTGluZTogeyBzaG93OiBmYWxzZSwgbGVuZ3RoOiAxNSwgbGVuZ3RoMjogNDAgfSwNCiAgICAgICAgc3RhcnRBbmdsZTogMCwgLy/otbflp4vop5LluqbvvIzmlK/mjIHojIPlm7RbMCwgMzYwXeOAgg0KICAgICAgICBjbG9ja3dpc2U6IGZhbHNlLCAvL+mlvOWbvueahOaJh+WMuuaYr+WQpuaYr+mhuuaXtumSiOaOkuW4g+OAguS4iui/sOi/meS4pOmhuemFjee9ruS4u+imgeaYr+S4uuS6huWvuem9kDNk55qE5qC35byPDQogICAgICAgIHJhZGl1czogWyI3OCUiLCAiNzQlIl0sDQogICAgICAgIGNlbnRlcjogWyI1MCUiLCAiNTAlIl0sDQogICAgICAgIGRhdGE6IF90aGlzLm9wdGlvbkRhdGEsDQogICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgIG9wYWNpdHk6IDAsDQogICAgICAgIH0sDQogICAgICB9KTsNCg0KICAgICAgbXlDaGFydC5zZXRPcHRpb24odGhpcy5vcHRpb24pOw0KICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoInJlc2l6ZSIsIGZ1bmN0aW9uICgpIHsNCiAgICAgICAgbXlDaGFydC5yZXNpemUoKTsNCiAgICAgIH0pOw0KICAgICAgLy8gdGhpcy5iaW5kTGlzdGVuKG15Q2hhcnQpOw0KICAgIH0sDQoNCiAgICBnZXRQaWUzRChwaWVEYXRhLCBpbnRlcm5hbERpYW1ldGVyUmF0aW8pIHsNCiAgICAgIC8vaW50ZXJuYWxEaWFtZXRlclJhdGlvOumAj+aYjueahOepuuW/g+WNoOavlA0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgbGV0IHNlcmllcyA9IFtdOw0KICAgICAgbGV0IHN1bVZhbHVlID0gMTsNCiAgICAgIGxldCBzdGFydFZhbHVlID0gMTsNCiAgICAgIGxldCBlbmRWYWx1ZSA9IDE7DQogICAgICBsZXQgbGVnZW5kRGF0YSA9IFtdOw0KICAgICAgbGV0IGxlZ2VuZEJmYiA9IFtdOw0KICAgICAgbGV0IGsgPSAxIC0gaW50ZXJuYWxEaWFtZXRlclJhdGlvOw0KICAgICAgcGllRGF0YS5zb3J0KChhLCBiKSA9PiB7DQogICAgICAgIHJldHVybiBiLnZhbHVlIC0gYS52YWx1ZTsNCiAgICAgIH0pOw0KICAgICAgLy8g5Li65q+P5LiA5Liq6aW85Zu+5pWw5o2u77yM55Sf5oiQ5LiA5LiqIHNlcmllcy1zdXJmYWNlIOmFjee9rg0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBwaWVEYXRhLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIHN1bVZhbHVlICs9IHBpZURhdGFbaV0udmFsdWU7DQogICAgICAgIGxldCBzZXJpZXNJdGVtID0gew0KICAgICAgICAgIG5hbWU6IHR5cGVvZiBwaWVEYXRhW2ldLm5hbWUgPT09ICJ1bmRlZmluZWQiID8gYHNlcmllcyR7aX1gIDogcGllRGF0YVtpXS5uYW1lLA0KICAgICAgICAgIHR5cGU6ICJzdXJmYWNlIiwNCiAgICAgICAgICBwYXJhbWV0cmljOiB0cnVlLA0KICAgICAgICAgIHdpcmVmcmFtZTogew0KICAgICAgICAgICAgc2hvdzogZmFsc2UsDQogICAgICAgICAgfSwNCiAgICAgICAgICBwaWVEYXRhOiBwaWVEYXRhW2ldLA0KICAgICAgICAgIHBpZVN0YXR1czogew0KICAgICAgICAgICAgc2VsZWN0ZWQ6IGZhbHNlLA0KICAgICAgICAgICAgaG92ZXJlZDogZmFsc2UsDQogICAgICAgICAgICBrOiBrLA0KICAgICAgICAgIH0sDQogICAgICAgICAgLy8gY2VudGVyOiBbIjEwJSIsICI1MCUiXSwNCiAgICAgICAgfTsNCg0KICAgICAgICBpZiAodHlwZW9mIHBpZURhdGFbaV0uaXRlbVN0eWxlICE9ICJ1bmRlZmluZWQiKSB7DQogICAgICAgICAgbGV0IGl0ZW1TdHlsZSA9IHt9Ow0KDQogICAgICAgICAgdHlwZW9mIHBpZURhdGFbaV0uaXRlbVN0eWxlLm9wYWNpdHkgIT0gInVuZGVmaW5lZCINCiAgICAgICAgICAgID8gKGl0ZW1TdHlsZS5vcGFjaXR5ID0gcGllRGF0YVtpXS5pdGVtU3R5bGUub3BhY2l0eSkNCiAgICAgICAgICAgIDogbnVsbDsNCiAgICAgICAgICBzZXJpZXNJdGVtLml0ZW1TdHlsZSA9IGl0ZW1TdHlsZTsNCiAgICAgICAgfQ0KICAgICAgICBzZXJpZXMucHVzaChzZXJpZXNJdGVtKTsNCiAgICAgIH0NCg0KICAgICAgLy8g5L2/55So5LiK5LiA5qyh6YGN5Y6G5pe277yM6K6h566X5Ye655qE5pWw5o2u5ZKMIHN1bVZhbHVl77yM6LCD55SoIGdldFBhcmFtZXRyaWNFcXVhdGlvbiDlh73mlbDvvIwNCiAgICAgIC8vIOWQkeavj+S4qiBzZXJpZXMtc3VyZmFjZSDkvKDlhaXkuI3lkIznmoTlj4LmlbDmlrnnqIsgc2VyaWVzLXN1cmZhY2UucGFyYW1ldHJpY0VxdWF0aW9u77yM5Lmf5bCx5piv5a6e546w5q+P5LiA5Liq5omH5b2i44CCDQogICAgICBsZWdlbmREYXRhID0gW107DQogICAgICBsZWdlbmRCZmIgPSBbXTsNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc2VyaWVzLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGVuZFZhbHVlID0gc3RhcnRWYWx1ZSArIHNlcmllc1tpXS5waWVEYXRhLnZhbHVlOw0KICAgICAgICBzZXJpZXNbaV0ucGllRGF0YS5zdGFydFJhdGlvID0gc3RhcnRWYWx1ZSAvIHN1bVZhbHVlOw0KICAgICAgICBzZXJpZXNbaV0ucGllRGF0YS5lbmRSYXRpbyA9IGVuZFZhbHVlIC8gc3VtVmFsdWU7DQogICAgICAgIHNlcmllc1tpXS5wYXJhbWV0cmljRXF1YXRpb24gPSB0aGlzLmdldFBhcmFtZXRyaWNFcXVhdGlvbigNCiAgICAgICAgICBzZXJpZXNbaV0ucGllRGF0YS5zdGFydFJhdGlvLA0KICAgICAgICAgIHNlcmllc1tpXS5waWVEYXRhLmVuZFJhdGlvLA0KICAgICAgICAgIGZhbHNlLA0KICAgICAgICAgIGZhbHNlLA0KICAgICAgICAgIGssDQogICAgICAgICAgc2VyaWVzW2ldLnBpZURhdGEudmFsdWUNCiAgICAgICAgKTsNCiAgICAgICAgc3RhcnRWYWx1ZSA9IGVuZFZhbHVlOw0KICAgICAgICBsZXQgYmZiID0gdGhhdC5mb21hdEZsb2F0KHNlcmllc1tpXS5waWVEYXRhLnZhbHVlIC8gc3VtVmFsdWUsIDQpOw0KICAgICAgICBsZWdlbmREYXRhLnB1c2goew0KICAgICAgICAgIG5hbWU6IHNlcmllc1tpXS5uYW1lLA0KICAgICAgICAgIHZhbHVlOiBiZmIsDQogICAgICAgIH0pOw0KICAgICAgICBsZWdlbmRCZmIucHVzaCh7DQogICAgICAgICAgbmFtZTogc2VyaWVzW2ldLm5hbWUsDQogICAgICAgICAgdmFsdWU6IHNlcmllc1tpXS5waWVEYXRhLnZhbHVlLA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGxldCBib3hIZWlnaHQgPSB0aGlzLmdldEhlaWdodDNEKHNlcmllcywgMTUpOyAvL+mAmui/h+S8oOWPguiuvuWumjNk6aW8L+eOr+eahOmrmOW6pu+8jDI25Luj6KGoMjZweA0KICAgICAgLy8g5YeG5aSH5b6F6L+U5Zue55qE6YWN572u6aG577yM5oqK5YeG5aSH5aW955qEIGxlZ2VuZERhdGHjgIFzZXJpZXMg5Lyg5YWl44CCDQogICAgICBsZXQgb3B0aW9uID0gew0KICAgICAgICBjb2xvcjogWyIjMDBGRUVCIiwgIiNGRjhEMzMiLCAiICM5NEIxQTgiXSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgb3JpZW50OiAidmVydGljYWwiLA0KICAgICAgICAgIGRhdGE6IGxlZ2VuZERhdGEsDQogICAgICAgICAgdHlwZTogInBsYWluIiwNCiAgICAgICAgICByaWdodDogIjclIiwNCiAgICAgICAgICB0b3A6ICIxJSIsDQogICAgICAgICAgaXRlbUdhcDogMTAsDQogICAgICAgICAgaXRlbVdpZHRoOiAxMCwNCiAgICAgICAgICBpdGVtSGVpZ2h0OiAxMCwNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgIGNvbG9yOiAiI0ExRTJGRiIsDQogICAgICAgICAgICByaWNoOiB7DQogICAgICAgICAgICAgIG5hbWU6IHsNCiAgICAgICAgICAgICAgICBhbGlnbjogImxlZnQiLA0KICAgICAgICAgICAgICAgIHdpZHRoOiA5MCwNCiAgICAgICAgICAgICAgICBwYWRkaW5nOiBbMCwgMCwgMCwgMF0sDQogICAgICAgICAgICAgICAgZm9udFNpemU6IDEzLA0KICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6ICJQaW5nRmFuZ1NDLVJlZ3VsYXIiLA0KICAgICAgICAgICAgICAgIGNvbG9yOiAiIzMwM2I1MCIsDQogICAgICAgICAgICAgIH0sDQoNCiAgICAgICAgICAgICAgYmZzOiB7DQogICAgICAgICAgICAgICAgZm9udFNpemU6IDEzLA0KICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6ICJQaW5nRmFuZ1NDLVJlZ3VsYXIiLA0KICAgICAgICAgICAgICAgIGNvbG9yOiAiIzMwM2I1MCIsDQogICAgICAgICAgICAgICAgLy8gYWxpZ246ICJyaWdodCIsDQogICAgICAgICAgICAgICAgcGFkZGluZzogWzAsIDAsIDAsIDMwXSwNCiAgICAgICAgICAgICAgfSwNCg0KICAgICAgICAgICAgICB3YW46IHsNCiAgICAgICAgICAgICAgICBmb250U2l6ZTogMTIsDQogICAgICAgICAgICAgICAgY29sb3I6ICIjNEFFNUUzIiwNCiAgICAgICAgICAgICAgICBwYWRkaW5nOiBbMCwgMCwgMCwgMV0sDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICB9LA0KICAgICAgICAgIH0sDQogICAgICAgICAgLy8gZGF0YTogWw0KICAgICAgICAgIC8vICAgew0KICAgICAgICAgIC8vICAgICBuYW1lOiAi5aSW6LSt57u/55S1IiwNCiAgICAgICAgICAvLyAgICAgaWNvbjogYGltYWdlOi8vJHtsaWdodG5pbmd9YCwNCiAgICAgICAgICAvLyAgIH0sDQogICAgICAgICAgLy8gICB7IG5hbWU6ICLlpJbotK3ngavnlLUiLCBpY29uOiBgaW1hZ2U6Ly8ke291dHNvdXJjaW5nfWAgfSwNCiAgICAgICAgICAvLyAgIHsgbmFtZTogIuiHquacieaWsOiDvea6kOWPkeeUtSIsIGljb246IGBpbWFnZTovLyR7bmV3ZW5lcmd5fWAgfSwNCiAgICAgICAgICAvLyBdLA0KICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbiAocGFyYW0pIHsNCiAgICAgICAgICAgIGxldCBpdGVtID0gbGVnZW5kQmZiLmZpbHRlcigoaXRlbSkgPT4gaXRlbS5uYW1lID09IHBhcmFtKVswXTsNCg0KICAgICAgICAgICAgcmV0dXJuIGB7bmFtZXwke2l0ZW0ubmFtZX0gfSB7YmZzfCAgJHtpdGVtLnZhbHVlfSB9YDsNCiAgICAgICAgICB9LA0KICAgICAgICB9LA0KDQogICAgICAgIGxhYmVsOiB7DQogICAgICAgICAgc2hvdzogZmFsc2UsDQogICAgICAgICAgdGV4dFN0eWxlOiB7DQogICAgICAgICAgICBjb2xvcjogIiMwMDAiLA0KICAgICAgICAgIH0sDQogICAgICAgIH0sDQoNCiAgICAgICAgeEF4aXMzRDogew0KICAgICAgICAgIG1pbjogLTEsDQogICAgICAgICAgbWF4OiAxLA0KICAgICAgICB9LA0KICAgICAgICB5QXhpczNEOiB7DQogICAgICAgICAgbWluOiAtMSwNCiAgICAgICAgICBtYXg6IDEsDQogICAgICAgIH0sDQogICAgICAgIHpBeGlzM0Q6IHsNCiAgICAgICAgICBtaW46IC0xLA0KICAgICAgICAgIG1heDogMSwNCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDNEOiB7DQogICAgICAgICAgc2hvdzogZmFsc2UsDQogICAgICAgICAgbGVmdDogIi0yNSUiLA0KICAgICAgICAgIHRvcDogIi0xMCUiLA0KICAgICAgICAgIGJveEhlaWdodDogYm94SGVpZ2h0LCAvL+WchueOr+eahOmrmOW6pg0KICAgICAgICAgIHZpZXdDb250cm9sOiB7DQogICAgICAgICAgICAvLzNk5pWI5p6c5Y+v5Lul5pS+5aSn44CB5peL6L2s562J77yM6K+36Ieq5bex5Y675p+l55yL5a6Y5pa56YWN572uDQogICAgICAgICAgICBhbHBoYTogMjcsIC8v6KeS5bqmDQogICAgICAgICAgICBiZXRhOiAwLA0KICAgICAgICAgICAgZGlzdGFuY2U6IDIyMCwgLy/osIPmlbTop4bop5LliLDkuLvkvZPnmoTot53nprvvvIznsbvkvLzosIPmlbR6b29tDQogICAgICAgICAgICByb3RhdGVTZW5zaXRpdml0eTogMCwgLy/orr7nva7kuLow5peg5rOV5peL6L2sDQogICAgICAgICAgICB6b29tU2Vuc2l0aXZpdHk6IDAsIC8v6K6+572u5Li6MOaXoOazlee8qeaUvg0KICAgICAgICAgICAgcGFuU2Vuc2l0aXZpdHk6IDAsIC8v6K6+572u5Li6MOaXoOazleW5s+enuw0KICAgICAgICAgICAgYXV0b1JvdGF0ZTogdHJ1ZSwgLy/oh6rliqjml4vovawNCiAgICAgICAgICB9LA0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IHNlcmllcywNCiAgICAgIH07DQogICAgICByZXR1cm4gb3B0aW9uOw0KICAgIH0sDQoNCiAgICAvL+iOt+WPljNk5LiZ5Zu+55qE5pyA6auY5omH5Yy655qE6auY5bqmDQogICAgZ2V0SGVpZ2h0M0Qoc2VyaWVzLCBoZWlnaHQpIHsNCiAgICAgIHNlcmllcy5zb3J0KChhLCBiKSA9PiB7DQogICAgICAgIHJldHVybiBiLnBpZURhdGEudmFsdWUgLSBhLnBpZURhdGEudmFsdWU7DQogICAgICB9KTsNCiAgICAgIHJldHVybiAoaGVpZ2h0ICogMjApIC8gc2VyaWVzWzBdLnBpZURhdGEudmFsdWU7DQogICAgfSwNCg0KICAgIC8vIOeUn+aIkOaJh+W9oueahOabsumdouWPguaVsOaWueeoi++8jOeUqOS6jiBzZXJpZXMtc3VyZmFjZS5wYXJhbWV0cmljRXF1YXRpb24NCiAgICBnZXRQYXJhbWV0cmljRXF1YXRpb24oc3RhcnRSYXRpbywgZW5kUmF0aW8sIGlzU2VsZWN0ZWQsIGlzSG92ZXJlZCwgaywgaCkgew0KICAgICAgLy8g6K6h566XDQogICAgICBsZXQgbWlkUmF0aW8gPSAoc3RhcnRSYXRpbyArIGVuZFJhdGlvKSAvIDI7DQogICAgICBsZXQgc3RhcnRSYWRpYW4gPSBzdGFydFJhdGlvICogTWF0aC5QSSAqIDI7DQogICAgICBsZXQgZW5kUmFkaWFuID0gZW5kUmF0aW8gKiBNYXRoLlBJICogMjsNCiAgICAgIGxldCBtaWRSYWRpYW4gPSBtaWRSYXRpbyAqIE1hdGguUEkgKiAyOw0KICAgICAgLy8g5aaC5p6c5Y+q5pyJ5LiA5Liq5omH5b2i77yM5YiZ5LiN5a6e546w6YCJ5Lit5pWI5p6c44CCDQogICAgICAvLyBpZiAoc3RhcnRSYXRpbyA9PT0gMCAmJiBlbmRSYXRpbyA9PT0gMSkgew0KICAgICAgLy8gICBpc1NlbGVjdGVkID0gZmFsc2U7DQogICAgICAvLyB9DQogICAgICAvLyDpgJrov4fmiYflvaLlhoXlvoQv5aSW5b6E55qE5YC877yM5o2i566X5Ye66L6F5Yqp5Y+C5pWwIGvvvIjpu5jorqTlgLwgMS8z77yJDQogICAgICBrID0gdHlwZW9mIGsgIT09ICJ1bmRlZmluZWQiID8gayA6IDEgLyAzOw0KICAgICAgLy8g6K6h566X6YCJ5Lit5pWI5p6c5YiG5Yir5ZyoIHgg6L2044CBeSDovbTmlrnlkJHkuIrnmoTkvY3np7vvvIjmnKrpgInkuK3vvIzliJnkvY3np7vlnYfkuLogMO+8iQ0KICAgICAgbGV0IG9mZnNldFggPSBpc1NlbGVjdGVkID8gTWF0aC5jb3MobWlkUmFkaWFuKSAqIDAuMSA6IDA7DQogICAgICBsZXQgb2Zmc2V0WSA9IGlzU2VsZWN0ZWQgPyBNYXRoLnNpbihtaWRSYWRpYW4pICogMC4xIDogMDsNCiAgICAgIC8vIOiuoeeul+mrmOS6ruaViOaenOeahOaUvuWkp+avlOS+i++8iOacqumrmOS6ru+8jOWImeavlOS+i+S4uiAx77yJDQogICAgICBsZXQgaG92ZXJSYXRlID0gaXNIb3ZlcmVkID8gMS4wNSA6IDE7DQogICAgICAvLyDov5Tlm57mm7LpnaLlj4LmlbDmlrnnqIsNCiAgICAgIHJldHVybiB7DQogICAgICAgIHU6IHsNCiAgICAgICAgICBtaW46IC1NYXRoLlBJLA0KICAgICAgICAgIG1heDogTWF0aC5QSSAqIDMsDQogICAgICAgICAgc3RlcDogTWF0aC5QSSAvIDMyLA0KICAgICAgICB9LA0KICAgICAgICB2OiB7DQogICAgICAgICAgbWluOiAwLA0KICAgICAgICAgIG1heDogTWF0aC5QSSAqIDIsDQogICAgICAgICAgc3RlcDogTWF0aC5QSSAvIDIwLA0KICAgICAgICB9LA0KICAgICAgICB4OiBmdW5jdGlvbiAodSwgdikgew0KICAgICAgICAgIGlmICh1IDwgc3RhcnRSYWRpYW4pIHsNCiAgICAgICAgICAgIHJldHVybiBvZmZzZXRYICsgTWF0aC5jb3Moc3RhcnRSYWRpYW4pICogKDEgKyBNYXRoLmNvcyh2KSAqIGspICogaG92ZXJSYXRlOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAodSA+IGVuZFJhZGlhbikgew0KICAgICAgICAgICAgcmV0dXJuIG9mZnNldFggKyBNYXRoLmNvcyhlbmRSYWRpYW4pICogKDEgKyBNYXRoLmNvcyh2KSAqIGspICogaG92ZXJSYXRlOw0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gb2Zmc2V0WCArIE1hdGguY29zKHUpICogKDEgKyBNYXRoLmNvcyh2KSAqIGspICogaG92ZXJSYXRlOw0KICAgICAgICB9LA0KICAgICAgICB5OiBmdW5jdGlvbiAodSwgdikgew0KICAgICAgICAgIGlmICh1IDwgc3RhcnRSYWRpYW4pIHsNCiAgICAgICAgICAgIHJldHVybiBvZmZzZXRZICsgTWF0aC5zaW4oc3RhcnRSYWRpYW4pICogKDEgKyBNYXRoLmNvcyh2KSAqIGspICogaG92ZXJSYXRlOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAodSA+IGVuZFJhZGlhbikgew0KICAgICAgICAgICAgcmV0dXJuIG9mZnNldFkgKyBNYXRoLnNpbihlbmRSYWRpYW4pICogKDEgKyBNYXRoLmNvcyh2KSAqIGspICogaG92ZXJSYXRlOw0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gb2Zmc2V0WSArIE1hdGguc2luKHUpICogKDEgKyBNYXRoLmNvcyh2KSAqIGspICogaG92ZXJSYXRlOw0KICAgICAgICB9LA0KICAgICAgICB6OiBmdW5jdGlvbiAodSwgdikgew0KICAgICAgICAgIGlmICh1IDwgLU1hdGguUEkgKiAwLjUpIHsNCiAgICAgICAgICAgIHJldHVybiBNYXRoLnNpbih1KTsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHUgPiBNYXRoLlBJICogMi41KSB7DQogICAgICAgICAgICByZXR1cm4gTWF0aC5zaW4odSkgKiBoICogMC4xOw0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gTWF0aC5zaW4odikgPiAwID8gMSAqIGggKiAwLjEgOiAtMTsNCiAgICAgICAgfSwNCiAgICAgIH07DQogICAgfSwNCg0KICAgIGZvbWF0RmxvYXQobnVtLCBuKSB7DQogICAgICB2YXIgZiA9IHBhcnNlRmxvYXQobnVtKTsNCiAgICAgIGlmIChpc05hTihmKSkgew0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQogICAgICBmID0gTWF0aC5yb3VuZChudW0gKiBNYXRoLnBvdygxMCwgbikpIC8gTWF0aC5wb3coMTAsIG4pOyAvLyBuIOW5gg0KICAgICAgdmFyIHMgPSBmLnRvU3RyaW5nKCk7DQogICAgICB2YXIgcnMgPSBzLmluZGV4T2YoIi4iKTsNCiAgICAgIC8v5Yik5a6a5aaC5p6c5piv5pW05pWw77yM5aKe5Yqg5bCP5pWw54K55YaN6KGlMA0KICAgICAgaWYgKHJzIDwgMCkgew0KICAgICAgICBycyA9IHMubGVuZ3RoOw0KICAgICAgICBzICs9ICIuIjsNCiAgICAgIH0NCiAgICAgIHdoaWxlIChzLmxlbmd0aCA8PSBycyArIG4pIHsNCiAgICAgICAgcyArPSAiMCI7DQogICAgICB9DQogICAgICByZXR1cm4gczsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["warnAnalysis.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "warnAnalysis.vue", "sourceRoot": "src/view/carbon/assess/assessReport/components", "sourcesContent": ["<template>\r\n  <div class=\"water-eval-container\" style=\"position: relative\">\r\n    <div class=\"cityGreenLand-charts\" id=\"structure3D-charts\"></div>\r\n    <div class=\"total_power\">\r\n      <p>\r\n        考核预警值<span>{{ warnValue }}%</span>\r\n      </p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport \"echarts-gl\";\r\nexport default {\r\n  name: \"cityGreenLand\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      electrictTotal: \"\",\r\n      isFullscreen: false,\r\n      optionData: [\r\n        {\r\n          name: \"考核成绩高于预警值\",\r\n          value: 22116,\r\n        },\r\n        {\r\n          name: \"考核成绩低于预警值\",\r\n          value: 16616,\r\n        },\r\n      ],\r\n      nowStep: 0,\r\n      maxStep: 35,\r\n      height: 95,\r\n    };\r\n  },\r\n  props: {\r\n    electricStrcuctObj: {\r\n      type: Object,\r\n    },\r\n    warnValue: {\r\n      type: String,\r\n    },\r\n  },\r\n\r\n  watch: {\r\n    electricStrcuctObj: {\r\n      immediate: true,\r\n      handler(newVal, oldVal) {\r\n        if (newVal == undefined || newVal == null || newVal == \"\") return;\r\n        // 总数量\r\n        this.electrictTotal = newVal.electrictTotal;\r\n        // // 外购绿电\r\n        // this.$set(this.optionData[0], \"value\", newVal.outsourcingGreenPower);\r\n        // 外购火电\r\n        this.$set(this.optionData[0], \"value\", newVal.thanWarnNum);\r\n        this.$set(this.optionData[1], \"value\", newVal.lowWarnNum);\r\n        this.$nextTick(() => {\r\n          this.init();\r\n        });\r\n      },\r\n      deep: true, // 深度监听\r\n    },\r\n  },\r\n\r\n  methods: {\r\n    init() {\r\n      let _this = this;\r\n      //构建3d饼状图\r\n      let myChart = this.$echarts.init(document.getElementById(\"structure3D-charts\"));\r\n      // 传入数据生成 option\r\n      this.option = this.getPie3D(_this.optionData, 0.75);\r\n      myChart.setOption(this.option);\r\n      //是否需要label指引线，如果要就添加一个透明的2d饼状图并调整角度使得labelLine和3d的饼状图对齐，并再次setOption\r\n      this.option.series.push({\r\n        name: \"pie2d\",\r\n        type: \"pie\",\r\n        label: {\r\n          normal: {\r\n            position: \"inner\",\r\n            show: false,\r\n          },\r\n        },\r\n        labelLine: { show: false, length: 15, length2: 40 },\r\n        startAngle: 0, //起始角度，支持范围[0, 360]。\r\n        clockwise: false, //饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式\r\n        radius: [\"78%\", \"74%\"],\r\n        center: [\"50%\", \"50%\"],\r\n        data: _this.optionData,\r\n        itemStyle: {\r\n          opacity: 0,\r\n        },\r\n      });\r\n\r\n      myChart.setOption(this.option);\r\n      window.addEventListener(\"resize\", function () {\r\n        myChart.resize();\r\n      });\r\n      // this.bindListen(myChart);\r\n    },\r\n\r\n    getPie3D(pieData, internalDiameterRatio) {\r\n      //internalDiameterRatio:透明的空心占比\r\n      let that = this;\r\n      let series = [];\r\n      let sumValue = 1;\r\n      let startValue = 1;\r\n      let endValue = 1;\r\n      let legendData = [];\r\n      let legendBfb = [];\r\n      let k = 1 - internalDiameterRatio;\r\n      pieData.sort((a, b) => {\r\n        return b.value - a.value;\r\n      });\r\n      // 为每一个饼图数据，生成一个 series-surface 配置\r\n      for (let i = 0; i < pieData.length; i++) {\r\n        sumValue += pieData[i].value;\r\n        let seriesItem = {\r\n          name: typeof pieData[i].name === \"undefined\" ? `series${i}` : pieData[i].name,\r\n          type: \"surface\",\r\n          parametric: true,\r\n          wireframe: {\r\n            show: false,\r\n          },\r\n          pieData: pieData[i],\r\n          pieStatus: {\r\n            selected: false,\r\n            hovered: false,\r\n            k: k,\r\n          },\r\n          // center: [\"10%\", \"50%\"],\r\n        };\r\n\r\n        if (typeof pieData[i].itemStyle != \"undefined\") {\r\n          let itemStyle = {};\r\n\r\n          typeof pieData[i].itemStyle.opacity != \"undefined\"\r\n            ? (itemStyle.opacity = pieData[i].itemStyle.opacity)\r\n            : null;\r\n          seriesItem.itemStyle = itemStyle;\r\n        }\r\n        series.push(seriesItem);\r\n      }\r\n\r\n      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，\r\n      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。\r\n      legendData = [];\r\n      legendBfb = [];\r\n      for (let i = 0; i < series.length; i++) {\r\n        endValue = startValue + series[i].pieData.value;\r\n        series[i].pieData.startRatio = startValue / sumValue;\r\n        series[i].pieData.endRatio = endValue / sumValue;\r\n        series[i].parametricEquation = this.getParametricEquation(\r\n          series[i].pieData.startRatio,\r\n          series[i].pieData.endRatio,\r\n          false,\r\n          false,\r\n          k,\r\n          series[i].pieData.value\r\n        );\r\n        startValue = endValue;\r\n        let bfb = that.fomatFloat(series[i].pieData.value / sumValue, 4);\r\n        legendData.push({\r\n          name: series[i].name,\r\n          value: bfb,\r\n        });\r\n        legendBfb.push({\r\n          name: series[i].name,\r\n          value: series[i].pieData.value,\r\n        });\r\n      }\r\n      let boxHeight = this.getHeight3D(series, 15); //通过传参设定3d饼/环的高度，26代表26px\r\n      // 准备待返回的配置项，把准备好的 legendData、series 传入。\r\n      let option = {\r\n        color: [\"#00FEEB\", \"#FF8D33\", \" #94B1A8\"],\r\n        legend: {\r\n          orient: \"vertical\",\r\n          data: legendData,\r\n          type: \"plain\",\r\n          right: \"7%\",\r\n          top: \"1%\",\r\n          itemGap: 10,\r\n          itemWidth: 10,\r\n          itemHeight: 10,\r\n          textStyle: {\r\n            color: \"#A1E2FF\",\r\n            rich: {\r\n              name: {\r\n                align: \"left\",\r\n                width: 90,\r\n                padding: [0, 0, 0, 0],\r\n                fontSize: 13,\r\n                fontFamily: \"PingFangSC-Regular\",\r\n                color: \"#303b50\",\r\n              },\r\n\r\n              bfs: {\r\n                fontSize: 13,\r\n                fontFamily: \"PingFangSC-Regular\",\r\n                color: \"#303b50\",\r\n                // align: \"right\",\r\n                padding: [0, 0, 0, 30],\r\n              },\r\n\r\n              wan: {\r\n                fontSize: 12,\r\n                color: \"#4AE5E3\",\r\n                padding: [0, 0, 0, 1],\r\n              },\r\n            },\r\n          },\r\n          // data: [\r\n          //   {\r\n          //     name: \"外购绿电\",\r\n          //     icon: `image://${lightning}`,\r\n          //   },\r\n          //   { name: \"外购火电\", icon: `image://${outsourcing}` },\r\n          //   { name: \"自有新能源发电\", icon: `image://${newenergy}` },\r\n          // ],\r\n          show: true,\r\n          formatter: function (param) {\r\n            let item = legendBfb.filter((item) => item.name == param)[0];\r\n\r\n            return `{name|${item.name} } {bfs|  ${item.value} }`;\r\n          },\r\n        },\r\n\r\n        label: {\r\n          show: false,\r\n          textStyle: {\r\n            color: \"#000\",\r\n          },\r\n        },\r\n\r\n        xAxis3D: {\r\n          min: -1,\r\n          max: 1,\r\n        },\r\n        yAxis3D: {\r\n          min: -1,\r\n          max: 1,\r\n        },\r\n        zAxis3D: {\r\n          min: -1,\r\n          max: 1,\r\n        },\r\n        grid3D: {\r\n          show: false,\r\n          left: \"-25%\",\r\n          top: \"-10%\",\r\n          boxHeight: boxHeight, //圆环的高度\r\n          viewControl: {\r\n            //3d效果可以放大、旋转等，请自己去查看官方配置\r\n            alpha: 27, //角度\r\n            beta: 0,\r\n            distance: 220, //调整视角到主体的距离，类似调整zoom\r\n            rotateSensitivity: 0, //设置为0无法旋转\r\n            zoomSensitivity: 0, //设置为0无法缩放\r\n            panSensitivity: 0, //设置为0无法平移\r\n            autoRotate: true, //自动旋转\r\n          },\r\n        },\r\n        series: series,\r\n      };\r\n      return option;\r\n    },\r\n\r\n    //获取3d丙图的最高扇区的高度\r\n    getHeight3D(series, height) {\r\n      series.sort((a, b) => {\r\n        return b.pieData.value - a.pieData.value;\r\n      });\r\n      return (height * 20) / series[0].pieData.value;\r\n    },\r\n\r\n    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation\r\n    getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {\r\n      // 计算\r\n      let midRatio = (startRatio + endRatio) / 2;\r\n      let startRadian = startRatio * Math.PI * 2;\r\n      let endRadian = endRatio * Math.PI * 2;\r\n      let midRadian = midRatio * Math.PI * 2;\r\n      // 如果只有一个扇形，则不实现选中效果。\r\n      // if (startRatio === 0 && endRatio === 1) {\r\n      //   isSelected = false;\r\n      // }\r\n      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）\r\n      k = typeof k !== \"undefined\" ? k : 1 / 3;\r\n      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）\r\n      let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;\r\n      let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;\r\n      // 计算高亮效果的放大比例（未高亮，则比例为 1）\r\n      let hoverRate = isHovered ? 1.05 : 1;\r\n      // 返回曲面参数方程\r\n      return {\r\n        u: {\r\n          min: -Math.PI,\r\n          max: Math.PI * 3,\r\n          step: Math.PI / 32,\r\n        },\r\n        v: {\r\n          min: 0,\r\n          max: Math.PI * 2,\r\n          step: Math.PI / 20,\r\n        },\r\n        x: function (u, v) {\r\n          if (u < startRadian) {\r\n            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n          }\r\n          if (u > endRadian) {\r\n            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n          }\r\n          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;\r\n        },\r\n        y: function (u, v) {\r\n          if (u < startRadian) {\r\n            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n          }\r\n          if (u > endRadian) {\r\n            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n          }\r\n          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;\r\n        },\r\n        z: function (u, v) {\r\n          if (u < -Math.PI * 0.5) {\r\n            return Math.sin(u);\r\n          }\r\n          if (u > Math.PI * 2.5) {\r\n            return Math.sin(u) * h * 0.1;\r\n          }\r\n          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;\r\n        },\r\n      };\r\n    },\r\n\r\n    fomatFloat(num, n) {\r\n      var f = parseFloat(num);\r\n      if (isNaN(f)) {\r\n        return false;\r\n      }\r\n      f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n); // n 幂\r\n      var s = f.toString();\r\n      var rs = s.indexOf(\".\");\r\n      //判定如果是整数，增加小数点再补0\r\n      if (rs < 0) {\r\n        rs = s.length;\r\n        s += \".\";\r\n      }\r\n      while (s.length <= rs + n) {\r\n        s += \"0\";\r\n      }\r\n      return s;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n#structure3D-charts {\r\n  height: 300px;\r\n  width: 53rem;\r\n  letter-spacing: 0.1rem;\r\n}\r\n.total_power {\r\n  width: 16rem;\r\n  height: 2.5vh;\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 5rem;\r\n  display: flex;\r\n\r\n  & > p:nth-of-type(1) {\r\n    font-size: 15px;\r\n    font-family: PingFangSC-Medium;\r\n    font-weight: 400;\r\n    color: #303b50;\r\n    span {\r\n      color: #00ecc0;\r\n      font-size: 16px;\r\n      margin-left: 0.3rem;\r\n      font-family: Alibaba-PuHuiTi-Bold;\r\n    }\r\n  }\r\n}\r\n\r\n/deep/ canvas {\r\n  z-index: 9 !important;\r\n}\r\n</style>\r\n"]}]}