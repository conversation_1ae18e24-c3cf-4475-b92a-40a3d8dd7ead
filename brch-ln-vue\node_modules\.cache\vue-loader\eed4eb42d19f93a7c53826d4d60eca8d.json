{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessTemplate\\componets\\choiceTree.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessTemplate\\componets\\choiceTree.vue", "mtime": 1754285403034}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["choiceTree.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "choiceTree.vue", "sourceRoot": "src/view/carbon/assess/assessTemplate/componets", "sourcesContent": ["<template>\r\n  <div>\r\n    <ul class=\"tree_box\">\r\n      <li>\r\n        <el-radio-group\r\n          :disabled=\"openMode == 2\"\r\n          v-model=\"objectType\"\r\n          @change=\"handleObjectTypeChange\"\r\n        >\r\n          <el-radio :label=\"1\">公司</el-radio>\r\n          <el-radio :label=\"2\">指定部门</el-radio>\r\n        </el-radio-group>\r\n        <el-input\r\n          v-if=\"openMode == 1\"\r\n          v-model=\"objectDialog.name\"\r\n          placeholder=\"关键字模糊查询\"\r\n          @change=\"handleseachText\"\r\n        >\r\n          <i slot=\"suffix\">\r\n            <img\r\n              src=\"@/assets/carbon/assess/searchiconImg.png\"\r\n              alt=\"searchiconImg.png\"\r\n              class=\"searchiconImg\"\r\n              @click=\"handleseachText\"\r\n              style=\"cursor: pointer\"\r\n            />\r\n          </i>\r\n        </el-input>\r\n        <el-tree\r\n          :disabled=\"openMode == 2\"\r\n          :data=\"treeData\"\r\n          show-checkbox\r\n          node-key=\"id\"\r\n          :checkedKeys=\"checkedKeys\"\r\n          ref=\"subRoleTree\"\r\n          :default-expand-all=\"true\"\r\n          :props=\"defaultProps\"\r\n          @check=\"treeCheck\"\r\n          :default-checked-keys=\"checkedKeys\"\r\n          :filter-node-method=\"filterNode\"\r\n        >\r\n        </el-tree>\r\n      </li>\r\n      <li>\r\n        <div class=\"company_box\" v-for=\"(item, index) in selectData\" :key=\"item.deptId\">\r\n          <div>\r\n            <p>\r\n              {{ item.name }}<span v-if=\"objectType == 2\"> · {{ item.dept }}</span>\r\n            </p>\r\n          </div>\r\n          <img\r\n            v-if=\"openMode == 1\"\r\n            src=\"@/assets/carbon/assess/treeDelect.png\"\r\n            alt\r\n            @click=\"delectTree(item, index)\"\r\n          />\r\n        </div>\r\n      </li>\r\n    </ul>\r\n    <div class=\"foot_btn\">\r\n      <el-button type=\"success\" @click=\"treeCancel\"\r\n        ><span v-if=\"openMode == 2\">关闭</span><span v-else>取消</span></el-button\r\n      >\r\n      <el-button type=\"info\" @click=\"treeDetermine\" v-if=\"openMode == 1\">确定</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    treeData: {\r\n      type: Array,\r\n      default: function () {\r\n        return [];\r\n      },\r\n    },\r\n    checkedKeys: {\r\n      type: Array,\r\n      default: function () {\r\n        return [];\r\n      },\r\n    },\r\n    objectType: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n    openMode: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"name\",\r\n      },\r\n      data: [],\r\n      father: [],\r\n      selectData: [],\r\n      objectDialog: {\r\n        objectType: 1,\r\n        name: undefined,\r\n        isSearch: false,\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    // 搜索\r\n    handleseachText(val) {\r\n      this.objectDialog.objectType = this.objectType;\r\n      this.objectDialog.isSearch = true;\r\n      this.$emit(\"handleseachText\", this.objectDialog);\r\n    },\r\n    // 确定\r\n    treeDetermine() {\r\n      this.objectDialog.name = \"\";\r\n      this.$emit(\"treeDetermine\", this.selectData);\r\n    },\r\n    // 取消\r\n    treeCancel() {\r\n      this.objectDialog.name = \"\";\r\n      this.selectData = [];\r\n      this.checkedKeys = [];\r\n      this.$emit(\"treeCancel\");\r\n    },\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.name.indexOf(value) !== -1;\r\n    },\r\n    handleObjectTypeChange(val) {\r\n      this.objectDialog.name = undefined;\r\n      this.objectDialog.objectType = val;\r\n      // this.objectType = val;\r\n      // this.checkedKeys = [];\r\n      this.$emit(\"handleObjectTypeChange\", this.objectDialog);\r\n    },\r\n    delectTree(item, index) {\r\n      this.selectData.splice(index, 1);\r\n      this.$refs.subRoleTree.setChecked(item.id, false, true);\r\n    },\r\n    addSelectData(data) {\r\n      if (this.selectData == null && this.selectData == undefined) {\r\n        this.selectData = [].concat([data]);\r\n      } else {\r\n        if (this.isDataInList(data, this.selectData) == false) {\r\n          this.selectData.push(data);\r\n        }\r\n      }\r\n    },\r\n    isDataInList(data, list) {\r\n      let ret = false;\r\n      for (let i = 0; i < list.length; i++) {\r\n        if (data.deptId == list[i].deptId) {\r\n          ret = true;\r\n          break;\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n    // 每次选中触发的事件\r\n    treeCheck(data) {\r\n      if (this.objectType == 1) {\r\n        this.company(data);\r\n      } else {\r\n        // 部门\r\n        this.department(data);\r\n      }\r\n    },\r\n    //选中公司\r\n    company(data) {\r\n      let thisNode = this.$refs.subRoleTree.getNode(data.id);\r\n      if (thisNode.level == \"2\") {\r\n        let index = this.selectData.findIndex((i) => {\r\n          return i.id == thisNode.data.id;\r\n        });\r\n        if (index == -1 && thisNode.checked == true) {\r\n          this.addSelectData({\r\n            name: thisNode.data.name,\r\n            id: thisNode.data.id,\r\n            dept: \"公司\",\r\n            deptId: thisNode.data.id,\r\n          });\r\n        } else if (index != -1 && thisNode.checked == false) {\r\n          this.selectData.splice(index, 1);\r\n        }\r\n      } else if (thisNode.level == \"1\") {\r\n        [...thisNode.childNodes].forEach((item) => {\r\n          let indexNum = this.selectData.findIndex((i) => {\r\n            return i.id == item.data.id;\r\n          });\r\n          if (indexNum == -1 && thisNode.checked == true) {\r\n            this.addSelectData({\r\n              name: item.data.name,\r\n              id: item.data.id,\r\n              dept: \"公司\",\r\n              deptId: item.data.id,\r\n            });\r\n          } else if (indexNum != -1 && thisNode.checked == false) {\r\n            this.selectData.splice(indexNum, 1);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 选中部门\r\n    department(data) {\r\n      let thisNode = this.$refs.subRoleTree.getNode(data.id);\r\n      // 三级\r\n      if (thisNode.level == \"3\") {\r\n        if (thisNode.checked == true) {\r\n          this.addSelectData({\r\n            name: thisNode.parent.data.name,\r\n            id: thisNode.parent.data.id,\r\n            dept: thisNode.data.name,\r\n            deptId: thisNode.data.id,\r\n          });\r\n        } else {\r\n          let index = this.selectData.findIndex((i) => {\r\n            return i.deptId == thisNode.data.id;\r\n          });\r\n          if (index != -1) {\r\n            this.selectData.splice(index, 1);\r\n          }\r\n        }\r\n        // 二级\r\n      } else if (thisNode.level == \"2\") {\r\n        if (thisNode.checked == true && thisNode.childNodes.length > 0) {\r\n          let name = thisNode.data.name;\r\n          let id = thisNode.data.id;\r\n          thisNode.childNodes.forEach((item) => {\r\n            this.addSelectData({\r\n              name: name,\r\n              id: id,\r\n              dept: item.data.name,\r\n              deptId: item.data.id,\r\n            });\r\n          });\r\n        } else if (thisNode.checked == false) {\r\n          thisNode.childNodes.forEach((item) => {\r\n            let index = this.selectData.findIndex((f) => {\r\n              return f.deptId == item.data.id;\r\n            });\r\n            if (index != -1) {\r\n              this.selectData.splice(index, 1);\r\n            }\r\n          });\r\n        }\r\n      } else if (thisNode.level == \"1\") {\r\n        if (thisNode.childNodes.length > 0) {\r\n          thisNode.childNodes.forEach((item) => {\r\n            item.childNodes.forEach((child) => {\r\n              let t = this.selectData.findIndex((x) => {\r\n                return x.deptId == child.data.id;\r\n              });\r\n              if (t == -1 && thisNode.checked == true) {\r\n                this.addSelectData({\r\n                  name: item.data.name,\r\n                  id: item.data.id,\r\n                  dept: child.data.name,\r\n                  deptId: child.data.id,\r\n                });\r\n              } else if (t != -1 && thisNode.checked == false) {\r\n                this.selectData.splice(t, 1);\r\n              }\r\n            });\r\n          });\r\n        }\r\n      }\r\n    },\r\n  },\r\n  watch: {\r\n    checkedKeys: {\r\n      handler(newValue, oldValue) {\r\n        this.selectData = [];\r\n        this.$nextTick(() => {\r\n          if (newValue != null && newValue != undefined && newValue.length > 0) {\r\n            newValue.forEach((deptId) => {\r\n              this.treeCheck({ id: deptId });\r\n            });\r\n          }\r\n        });\r\n      },\r\n      deep: true,\r\n    },\r\n  },\r\n  // watch: {\r\n  //   filterText(val) {\r\n  //     this.$refs.subRoleTree.filter(val);\r\n  //   },\r\n  // },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\nli {\r\n  list-style: none;\r\n}\r\np {\r\n  margin: 0;\r\n}\r\n.tree_box {\r\n  width: 100%;\r\n  height: 49vh;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  & > li {\r\n    height: 100%;\r\n    padding: 1vh 1rem;\r\n    /deep/.el-input {\r\n      width: 100%;\r\n      margin-top: 2vh;\r\n      height: 3.4vh;\r\n    }\r\n  }\r\n  & > li:nth-of-type(1) {\r\n    width: 48%;\r\n    background: #f6f8fa;\r\n  }\r\n  & > li:nth-of-type(2)::-webkit-scrollbar {\r\n    display: none;\r\n  }\r\n  & > li:nth-of-type(2) {\r\n    width: 48%;\r\n    overflow: auto;\r\n    background: #f6f8fa;\r\n    & > .company_box {\r\n      cursor: pointer;\r\n      padding: 1vh 1.5rem;\r\n      margin-bottom: 1vh;\r\n      margin-right: 1rem;\r\n      float: left;\r\n      display: flex;\r\n      align-items: center;\r\n      background: #f6f8fa;\r\n      div {\r\n        display: flex;\r\n        p {\r\n          font-size: 14px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          font-weight: 400;\r\n          color: #409eff;\r\n        }\r\n      }\r\n      & > img {\r\n        width: 1.5rem;\r\n        margin-left: 1rem;\r\n      }\r\n    }\r\n  }\r\n  /deep/.el-radio {\r\n    margin-right: 8rem;\r\n  }\r\n\r\n  /deep/.el-radio__label {\r\n    color: #303b50 !important;\r\n    font-family: PingFangSC-Regular, sans-serif;\r\n    letter-spacing: 1px;\r\n  }\r\n  /deep/.el-radio__inner {\r\n    border: 0.1rem solid #409eff !important;\r\n    background-color: transparent;\r\n  }\r\n  /deep/.el-radio__input.is-checked .el-radio__inner {\r\n    background: transparent;\r\n  }\r\n  /deep/.el-radio__input.is-checked .el-radio__inner::after {\r\n    background: #409eff !important;\r\n  }\r\n  .el-tree {\r\n    background: transparent;\r\n    color: #606266;\r\n    margin-top: 2vh;\r\n    height: 38vh;\r\n    overflow: auto;\r\n  }\r\n  .el-tree::-webkit-scrollbar {\r\n    display: none;\r\n  }\r\n  /deep/.el-tree-node__content:hover {\r\n    background-color: transparent !important;\r\n  }\r\n  /deep/.el-tree-node__content {\r\n    height: 4.2vh;\r\n  }\r\n  /deep/.el-tree-node.is-current > .el-tree-node__content {\r\n    height: 4vh;\r\n    background-color: #c9e5f4 !important;\r\n  }\r\n  /deep/ .el-tree-node__label {\r\n    font-size: 14px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    font-weight: 400;\r\n    color: #303b50;\r\n    letter-spacing: 0.05rem;\r\n    margin-top: 0.3vh;\r\n  }\r\n  /deep/.el-tree-node__expand-icon {\r\n    color: #999;\r\n  }\r\n  /deep/.el-checkbox__input.is-checked .el-checkbox__inner {\r\n    background-color: #409eff;\r\n    border: none;\r\n  }\r\n  /deep/ .el-checkbox__inner {\r\n    background: #fff; //409eff\r\n    border: 1px solid #999 !important;\r\n  }\r\n  /deep/ .el-checkbox__input.is-indeterminate .el-checkbox__inner {\r\n    background-color: #000;\r\n    border-color: #999 !important;\r\n  }\r\n  /deep/.el-checkbox__inner::after {\r\n    border: 0.2rem solid #000;\r\n    border-left: 0;\r\n    border-top: 0;\r\n    left: 0.3rem;\r\n    top: 0.1vh;\r\n  }\r\n}\r\n.foot_btn {\r\n  width: 50%;\r\n  height: 5vh;\r\n  margin: 2vh auto 0;\r\n  alignment: center;\r\n}\r\n.searchiconImg {\r\n  width: 20px;\r\n  height: 20px;\r\n  margin: 8px 5px 0 0;\r\n}\r\n</style>\r\n"]}]}