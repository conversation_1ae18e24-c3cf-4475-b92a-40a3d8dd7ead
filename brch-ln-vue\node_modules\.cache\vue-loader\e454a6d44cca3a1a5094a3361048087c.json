{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\costdigit\\station-electric\\modal-info.vue?vue&type=style&index=0&id=454ccb87&lang=less&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\costdigit\\station-electric\\modal-info.vue", "mtime": 1754285269231}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749178874080}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoubGlzdC10aXRsZSB7DQogIHBhZGRpbmc6IDIwcHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KfQ0K"}, {"version": 3, "sources": ["modal-info.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8OA;AACA;AACA;AACA;AACA;AACA", "file": "modal-info.vue", "sourceRoot": "src/view/costdigit/station-electric", "sourcesContent": ["<template>\r\n  <Modal class=\"common-wh\" v-model=\"showModal\" title=\"详情\" width=\"70%\">\r\n    <Form ref=\"myform\" :model=\"formData\" :label-width=\"140\">\r\n      <Row>\r\n        <Col span=\"8\">\r\n          <FormItem label=\"局站名称:\" prop=\"station\">\r\n            <Input v-model=\"formData.station\" disabled> </Input>\r\n          </FormItem>\r\n        </Col>\r\n        <!-- <Col span=\"8\">\r\n          <FormItem label=\"站址类型:\" prop=\"stationType\">\r\n            <Input v-model=\"formData.stationType\" disabled> </Input>\r\n          </FormItem>\r\n        </Col> -->\r\n        <Col span=\"8\">\r\n          <FormItem label=\"局站编码:\" prop=\"stationCode\">\r\n            <Input v-model=\"formData.stationCode\" disabled> </Input>\r\n          </FormItem>\r\n        </Col>\r\n        <!-- <Col span=\"8\">\r\n          <FormItem label=\"资源编码:\" prop=\"pueCode\">\r\n            <Input v-model=\"formData.pueCode\" disabled> </Input>\r\n          </FormItem>\r\n        </Col>\r\n        <Col span=\"8\">\r\n          <FormItem label=\"5gr站址编码:\" prop=\"stationcode5gr\">\r\n            <Input v-model=\"formData.stationcode5gr\" disabled> </Input>\r\n          </FormItem>\r\n        </Col> -->\r\n        <!-- <Col span=\"8\">\r\n          <FormItem label=\"数据来源:\" prop=\"sourceName\">\r\n            <Input v-model=\"formData.sourceName\" disabled> </Input>\r\n          </FormItem>\r\n        </Col> -->\r\n        <Col span=\"8\">\r\n          <FormItem label=\"月份:\" prop=\"yf\">\r\n            <Input v-model=\"formData.yf\" disabled> </Input>\r\n          </FormItem>\r\n        </Col>\r\n        <Col span=\"8\">\r\n          <FormItem label=\"月总电量:\" prop=\"ywdl\">\r\n            <Input v-model=\"formData.ywdl\" disabled> </Input>\r\n          </FormItem>\r\n        </Col>\r\n        <Col span=\"8\">\r\n          <FormItem label=\"日均电量:\" prop=\"rjdl\">\r\n            <Input v-model=\"formData.rjdl\" disabled> </Input>\r\n          </FormItem>\r\n        </Col>\r\n      </Row>\r\n    </Form>\r\n    <div class=\"list-title\">\r\n      <div>用电详情</div>\r\n      <!-- <Button\r\n        type=\"primary\"\r\n        style=\"margin-left: 5px\"\r\n        @click=\"exportCsv\"\r\n        :disabled=\"tableList.length == 0\"\r\n        >导出\r\n      </Button> -->\r\n      <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n        <Button type=\"default\" style=\"margin-left: 5px\"\r\n          >导出\r\n          <Icon type=\"ios-arrow-down\"></Icon>\r\n        </Button>\r\n        <DropdownMenu slot=\"list\">\r\n          <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n          <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n        </DropdownMenu>\r\n      </Dropdown>\r\n    </div>\r\n    <cl-table\r\n      ref=\"clTable\"\r\n      height=\"300\"\r\n      :searchable=\"false\"\r\n      :exportable=\"false\"\r\n      :columns=\"tableSet.columns\"\r\n      :loading=\"tableSet.loading\"\r\n      :total=\"tableSet.total\"\r\n      :pageSize=\"tableSet.pageSize\"\r\n      :showPage=\"tableSet.showPage\"\r\n      @on-query=\"tableQuery\"\r\n      :data=\"tableList\"\r\n      v-if=\"showModal\"\r\n    >\r\n    </cl-table>\r\n    <div slot=\"footer\">\r\n      <Button type=\"default\" class=\"cancelBtn\" @click=\"showModal = false\">取消</Button>\r\n    </div>\r\n  </Modal>\r\n</template>\r\n<script>\r\nimport axios from \"@/libs/api.request\";\r\nimport { getCostStaElectricInfo } from \"@/api/costdigit/index\";\r\nimport { deepClone } from \"@/libs/util\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      showModal: false,\r\n      btnloading: false, //确认提交\r\n      pageParams: {},\r\n      dicts: {},\r\n      formData: {},\r\n      tableList: [],\r\n      tableSet: {\r\n        loading: false,\r\n        showPage: true,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        total: 0,\r\n        columns: [\r\n          {\r\n            title: \"局站编码\",\r\n            key: \"stationCode\",\r\n            align: \"center\",\r\n          },\r\n          // {\r\n          //   title: \"资源编码\",\r\n          //   key: \"pueCode\",\r\n          //   align: \"center\",\r\n          // },\r\n          {\r\n            title: \"局站名称\",\r\n            key: \"station\",\r\n            align: \"center\",\r\n          },\r\n          // {\r\n          //   title: \"5GR站址编码\",\r\n          //   key: \"stationcode5gr\",\r\n          //   align: \"center\",\r\n          // },\r\n          {\r\n            title: \"日期\",\r\n            key: \"rq\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"用电量\",\r\n            key: \"ywdl\",\r\n            align: \"center\",\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  mounted() {},\r\n  methods: {\r\n    //弹窗:打开\r\n    openModal(val) {\r\n      this.showModal = true;\r\n      // 数据来源 1 无线大数据 2 智慧机房\r\n      this.pageParams = {\r\n        stationCode: val.stationCode,\r\n        source: val.source,\r\n        tjyf: val.tjyf,\r\n        cityCode: val.cityCode,\r\n        countyCode: val.countyCode,\r\n      };\r\n      Object.assign(this.formData, val);\r\n      this.$nextTick(() => {\r\n        this.$refs.clTable.query(this.pageParams);\r\n      });\r\n    },\r\n    //查询\r\n    tableQuery(params) {\r\n      this.tableSet.loading = true;\r\n      getCostStaElectricInfo(params).then((res) => {\r\n        this.tableSet.loading = false;\r\n        let data = res.data.rows;\r\n        this.tableSet.total = res.data.total;\r\n        this.tableList = data;\r\n      });\r\n    },\r\n    //导出\r\n    exportCsv(name) {\r\n      if (this.tableSet.total == 0) {\r\n        this.$Message.warning(\"暂无数据可导出\");\r\n        return;\r\n      }\r\n      this.exportLoading();\r\n      let params = this.deepClone(this.$refs.clTable.insideQueryParams);\r\n      if (name == \"all\") {\r\n        params.pageNum = 1;\r\n        params.pageSize = this.tableSet.total;\r\n      }\r\n      axios\r\n        .file({\r\n          url: \"/business/cost/stationElectric/xq/export\",\r\n          method: \"post\",\r\n          data: params,\r\n        })\r\n        .then((res) => {\r\n          const content = res;\r\n          const blob = new Blob([content]);\r\n\r\n          let fileName = `${this.formData.yf}-电量查询详情.xlsx`;\r\n          if (\"download\" in document.createElement(\"a\")) {\r\n            // 非IE下载\r\n            const elink = document.createElement(\"a\");\r\n            elink.download = fileName;\r\n            elink.style.display = \"none\";\r\n            elink.href = URL.createObjectURL(blob);\r\n            document.body.appendChild(elink);\r\n            elink.click();\r\n            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n            document.body.removeChild(elink);\r\n          } else {\r\n            // IE10+下载\r\n            navigator.msSaveBlob(blob, fileName);\r\n          }\r\n          this.$Spin.hide();\r\n        });\r\n    },\r\n    exportLoading() {\r\n      this.$Spin.show({\r\n        render: (h) => {\r\n          return h(\"div\", [\r\n            h(\"Progress\", {\r\n              style: {\r\n                width: \"800px\",\r\n              },\r\n            }),\r\n            h(\"div\", \"导出中，请勿刷新页面......\"),\r\n          ]);\r\n        },\r\n      });\r\n    },\r\n    clearForm() {\r\n      for (let key in this.formData) {\r\n        this.formData[key] = null;\r\n      }\r\n      this.$refs[\"myform\"] && this.$refs[\"myform\"].resetFields();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.list-title {\r\n  padding: 20px;\r\n  font-size: 16px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n</style>\r\n"]}]}