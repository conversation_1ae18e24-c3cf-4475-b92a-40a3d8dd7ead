{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\check\\queryAbnormal.vue?vue&type=style&index=0&id=********&scoped=true&lang=css&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\check\\queryAbnormal.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoudGFibGVDYXJkew0KICB3aWR0aDogMTAwJTsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgcGFkZGluZzoxMHB4Ow0KICBoZWlnaHQ6YXV0bzsNCiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGUNCn0NCi50YWJsZVRpdGxlew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGxlZnQ6IDVweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBtYXJnaW4tYm90dG9tOjdweDsNCiAgcGFkZGluZy1yaWdodDogNXB4Ow0KICBmb250LXdlaWdodDogYm9sZGVyOw0KfQ0KLnRhYmxlVGl0bGUyew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGxlZnQ6IDBweDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBtYXJnaW4tYm90dG9tOjdweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCi50YWJsZVRpdGxlOjpiZWZvcmV7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgdG9wOjNweDsNCiAgbGVmdDogLTVweDsNCiAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICBjb250ZW50OiAiIjsNCiAgd2lkdGg6IDJweDsNCiAgaGVpZ2h0OiAxNnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjMWU4OGU1Ow0KfQ0K"}, {"version": 3, "sources": ["queryAbnormal.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "queryAbnormal.vue", "sourceRoot": "src/view/account/check", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"tableCard\">\r\n      <div class=\"tableTitle\">\r\n        <div>{{titleName}}</div>\r\n        <Button type=\"text\" @click=\"exportCsv\">导出</Button>\r\n      </div>\r\n      <Table\r\n          border\r\n          height=\"500\"\r\n          :loading=\"loading\"\r\n          :columns=\"columns\"\r\n          :data=\"tableData\"\r\n      ></Table>\r\n      <Page\r\n          size=\"small\"\r\n          :total=\"pageTotal\"\r\n          :current=\"pageNum\"\r\n          :page-size=\"pageSize\"\r\n          show-elevator\r\n          show-sizer\r\n          show-total\r\n          placement=\"top\"\r\n          @on-change=\"handlePage\"\r\n          @on-page-size-change=\"handlePageSize\"\r\n      ></Page>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getPowerError2} from \"@/api/account\";\r\nimport axios from \"@/libs/api.request\";\r\n\r\nexport default {\r\n  name: \"query\",\r\n  activeName:'局站与电表关联异常项',\r\n  data(){\r\n    return{\r\n      pageTotal:0,\r\n      pageNum:1,\r\n      pageSize:10,\r\n      exportName:'',\r\n      operationsBranch:'',\r\n      cityName:'',\r\n      cityCode:\"1000373\",\r\n      countyCompanies:\"1000406\",\r\n      siteType:\"1\",\r\n      month:\"\",\r\n      titleName:'局站与电表关联异常项',\r\n      activeName:'电价合理性',\r\n      loading:false,\r\n      columns:[],\r\n      //局站与电表关联异常表\r\n      stationError:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        { title: \"期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        { title: \"类型\", minWidth:150,key: \"errorType\", align: \"center\" ,},\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n      ],\r\n      //电价合理性表\r\n      priceValid:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        { title: \"台账期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        // { title: \"类型\", minWidth:150,key: \"errorType\", align: \"center\" ,},\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n        { title: \"台账单价\", minWidth:150,key: \"accountPrice\", align: \"center\" ,},\r\n        { title: \"基础信息单价(合同单价)\", minWidth:150,key: \"meterPrice\", align: \"center\" ,},\r\n      ],\r\n      //电表站址一致性\r\n      stationSame:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        { title: \"期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n      ],\r\n      //台账周期连续性\r\n      accountZQ:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        // { title: \"类型\", minWidth:150,key: \"errorType\", align: \"center\" ,},\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        // { title: \"期号\", minWidth:150,key: 'ledgerPeriod', align: \"center\"},\r\n        { title: \"期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n        { title: \"上次起始日期\", minWidth:150,key: \"lastStartTime\", align: \"center\"},\r\n        { title: \"上次截止日期\", minWidth:150,key: \"lastStopTime\", align: \"center\"},\r\n        { title: \"本次起始日期\", minWidth:150,key: \"startTime\", align: \"center\"},\r\n        { title: \"本次截止日期\", minWidth:150,key: \"stopTime\", align: \"center\"},\r\n        { title: \"本次台账录入时间\", minWidth:150,key: \"auditTimeNow\", align: \"center\"},\r\n        // { title: \"上次台账录入时间\", minWidth:150,key: \"auditTimeLast\", align: \"center\"},\r\n        // { title: \"报账周期差异（天）\", minWidth:150,key: \"differencesDay\", align: \"center\"}, \r\n      ],\r\n      //电表度数连续性\r\n      dbds:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        { title: \"类型\", minWidth:150,key: \"errorType\", align: \"center\" ,},\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        { title: \"台账期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n        { title: \"上次起始度数\", minWidth:150,key: \"lastStartDegrees\", align: \"center\"},\r\n        { title: \"上次截止度数\", minWidth:150,key: \"lastStopDegrees\", align: \"center\"},\r\n        { title: \"本次起始度数\", minWidth:150,key: \"startDegrees\", align: \"center\"},\r\n        { title: \"本次截止度数\", minWidth:150,key: \"stopDegrees\", align: \"center\"},\r\n        { title: \"本次台账录入时间\", minWidth:150,key: \"auditTime\", align: \"center\"},\r\n      ],\r\n      //日均电量及台账波动性异常查看\r\n      tzdl:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        { title: \"类型\", minWidth:150,key: \"errorType\", align: \"center\" ,},\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        { title: \"台账期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n        { title: \"台账电量\", minWidth:150,key: \"towerDegrees\", align: \"center\"},\r\n        { title: \"标准日均电量\", minWidth:150,key: \"useDay\", align: \"center\"},\r\n        { title: \"波动幅度\", minWidth:150,key: \"degreesFluctuate\", align: \"center\"},\r\n      ],\r\n      //日均电量及台账波动性异常查看 \r\n      tzdl2:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        { title: \"台账期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n        { title: \"报账开始时间\", minWidth:150,key: 'ledgerPeriod', align: \"center\"},\r\n        { title: \"报账结束时间\", minWidth:150,key: 'ledgerPeriod', align: \"center\"},\r\n        { title: \"台账电量\", minWidth:150,key: \"towerDegrees\", align: \"center\"},\r\n        { title: \"台账日均电量\", minWidth:150,key: \"towerDegrees\", align: \"center\"},\r\n        { title: \"集团5gr日均电量(标准电量)\", minWidth:150,key: \"degreesDay\", align: \"center\"},\r\n        // { title: \"集团5gr日均电量(标准电量)\", minWidth:150,key: \"useDay\", align: \"center\" },\r\n        { title: \"波动幅度\", minWidth:150,key: \"degreesFluctuate\", align: \"center\"},\r\n      ],\r\n      //日均电量及台账波动性异常查看 \r\n      tzdl22:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        { title: \"台账期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n        { title: \"报账开始时间\", minWidth:150,key: 'startTime', align: \"center\"},\r\n        { title: \"报账结束时间\", minWidth:150,key: 'stopTime', align: \"center\"},\r\n        { title: \"本期总电量\", minWidth:150,key: \"degrees\", align: \"center\"},\r\n        { title: \"台账日均电量\", minWidth:150,key: \"useDay\", align: \"center\"},\r\n        { title: \"集团5gr日均电量(标准电量)\", minWidth:150,key: \"degreesDay\", align: \"center\"},\r\n        // { title: \"集团5gr日均电量(标准电量)\", minWidth:150,key: \"useDay\", align: \"center\" },\r\n        { title: \"波动幅度\", minWidth:150,key: \"degreesFluctuate\", align: \"center\"},\r\n      ],\r\n      //日均耗电量\r\n      tzdl3:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        { title: \"台账期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        // { title: \"台账期号\", minWidth:150,key: 'ledgerPeriod', align: \"center\"},\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n        { title: \"台账电量\", minWidth:150,key: \"towerDegrees\", align: \"center\"},\r\n        { title: \"台账日均耗电量\", minWidth:150,key: \"degreesDay\", align: \"center\"},\r\n        { title: \"标准日均电量\", minWidth:150,key: \"useDay\", align: \"center\"},\r\n      ],\r\n      //共享站分摊比例异常日均电量异常查看 \r\n      gxzyc:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        // { title: \"类型\", minWidth:150,key: \"errorType\", align: \"center\" ,},\r\n        { title: \"类型\", minWidth:150,key: \"ratioErrorType\", align: \"center\" ,},\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        { title: \"期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n        // { title: \"维护共享家数\", minWidth:150,key: \"shareNumber\", align: \"center\" },\r\n        { title: \"维护共享家数\", minWidth:150,key: \"shareNum\", align: \"center\" },\r\n        { title: \"协议管理能耗比例\", minWidth:150,key: \"meterPercent\", align: \"center\" },\r\n        // { title: \"电信\", minWidth:150,key: \"percent\", align: \"center\" },\r\n        { title: \"电信\", minWidth:150,key: \"dxApportionmentratio\", align: \"center\" },\r\n        { title: \"移动\", minWidth:150,key: \"mobileApportionmentratio\", align: \"center\" },\r\n        { title: \"联通\", minWidth:150,key: \"unicomApportionmentratio\", align: \"center\" },\r\n        { title: \"拓展\", minWidth:150,key: \"expandApportionmentratio\", align: \"center\" },\r\n        { title: \"能源\", minWidth:150,key: \"energyApportionmentratio\", align: \"center\" },\r\n        // { title: \"合计\", minWidth:150,key: \"\", align: \"center\" },\r\n        { title: \"合计\", minWidth:150,key: \"totalApportionmentratio\", align: \"center\" },\r\n      ],\r\n      //独享站分摊比例异常日均电量异常查看\r\n      dxzft:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        // { title: \"类型\", minWidth:150,key: \"errorType\", align: \"center\" ,},\r\n        { title: \"类型\", minWidth:150,key: \"ratioErrorType\", align: \"center\" ,},\r\n        // { title: \"期号\", minWidth:150,key: 'ledgerPeriod', align: \"center\"}, \r\n        { title: \"期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n        // { title: \"维护共享家数\", minWidth:150,key: \"shareNumber\", align: \"center\" },\r\n        { title: \"维护共享家数\", minWidth:150,key: \"shareNum\", align: \"center\" },\r\n        // { title: \"电信\", minWidth:150,key: \"percent\", align: \"center\" },\r\n        { title: \"电信\", minWidth:150,key: \"dxApportionmentratio\", align: \"center\" },\r\n        { title: \"移动\", minWidth:150,key: \"mobileApportionmentratio\", align: \"center\" },\r\n        { title: \"联通\", minWidth:150,key: \"unicomApportionmentratio\", align: \"center\" },\r\n        { title: \"拓展\", minWidth:150,key: \"expandApportionmentratio\", align: \"center\" },\r\n        { title: \"能源\", minWidth:150,key: \"energyApportionmentratio\", align: \"center\" },\r\n        // { title: \"合计\", minWidth:150,key: \"\", align: \"center\" },\r\n        { title: \"合计\", minWidth:150,key: \"totalApportionmentratio\", align: \"center\" },\r\n      ],\r\n      //台账周期异常查看\r\n      tzyczq:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        { title: \"台账期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        // { title: \"台账期号\", minWidth:150,key: 'ledgerPeriod', align: \"center\"},\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n        { title: \"本次台账录入时间\", minWidth:150,key: \"auditTime\", align: \"center\"},\r\n        { title: \"上次台账录入时间\", minWidth:150,key: \"auditTimeLast\", align: \"center\"},\r\n        { title: \"报账周期差异\", minWidth:150,key: \"differencesDay\", align: \"center\"},\r\n      ],\r\n      //总数\r\n      zs:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n        { title: \"状态\", minWidth:150,key: \"status\", align: \"center\" },\r\n        { title: \"异常项\", minWidth:150,key: \"abnormal\", align: \"center\" },\r\n        { title: \"电表负责人\", minWidth:150,key: \"headPeople\", align: \"center\" },\r\n\r\n      ],\r\n      //电量合理性\r\n      dlhlx:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        { title: \"类型\", minWidth:150,key: \"errorType\", align: \"center\" ,},\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        // { title: \"期号\", minWidth:150,key: 'ledgerPeriod', align: \"center\"},\r\n        { title: \"期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n        { title: \"上期报账台账电量\", minWidth:150,key: \"lastDegrees\", align: \"center\" },\r\n        { title: \"本期报账台账电量\", minWidth:150,key: \"degrees\", align: \"center\" },\r\n      ],\r\n      //电量合理性(省内大数据)\r\n      dlhlxda:[\r\n        { title: \"序号\", type: 'index', minWidth:70,align: \"center\"},\r\n        { title: \"地市\", minWidth:150,key: 'city', align: \"center\"},\r\n        { title: \"运营分局\", minWidth:150,key: 'operationsBranch', align: \"center\"},\r\n        // { title: \"类型\", minWidth:150,key: \"type\", align: \"center\" ,},\r\n        // { title: \"类型\", minWidth:150,key: \"errorType\", align: \"center\" ,},\r\n        { title: \"台账期号\", minWidth:150,key: 'accountNo', align: \"center\"},\r\n        { title: \"集团站址编码\", minWidth:150,key: \"stationcode\", align: \"center\" },\r\n        { title: \"电表户名/协议号码\",minWidth:150, key: \"ammeterid\", align: \"center\" },\r\n        { title: \"铁塔站址编码\", minWidth:150,key: \"towerSiteCode\", align: \"center\" },\r\n        // { title: \"上期报账台账电量\", minWidth:150,key: \"lastDegrees\", align: \"center\" },\r\n        // { title: \"本期报账台账电量\", minWidth:150,key: \"degrees\", align: \"center\" },\r\n        { title: \"报账开始时间\", minWidth:150,key: \"startTime\", align: \"center\" },\r\n        { title: \"报账结束时间\", minWidth:150,key: \"stopTime\", align: \"center\" },\r\n        { title: \"台账电量\", minWidth:150,key: \"degrees\", align: \"center\" },\r\n        // { title: \"省内大数据平台电量\", minWidth:150,key: \"lastDegrees\", align: \"center\" },\r\n      ],\r\n      tableData:[\r\n      ]\r\n    }\r\n  },\r\n  methods:{\r\n    handlePage(value){\r\n      console.log(value);\r\n      this.pageNum=value;\r\n      this.query();\r\n    },\r\n    handlePageSize(value){\r\n      console.log(value);\r\n      this.pageSize=value;\r\n      this.query();\r\n    },\r\n    exportCsv() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1;\r\n      const day = now.getDate();\r\n      let params = {\r\n        city: this.cityName,\r\n        cityCode:this.cityCode,\r\n        countyCompaniesCode:this.countyCompanies,\r\n        operationsBranch:this.operationsBranch,\r\n        siteType:this.siteType,\r\n        month:this.month,\r\n        exportName:this.exportName,\r\n        fileName:this.exportName\r\n      };\r\n      let req = {\r\n        url: \"/business/poweraudit/exportAuditDetails\",\r\n        method: \"post\",\r\n        data: params,\r\n      };\r\n      axios.file(req).then((res) => {\r\n        const content = res;\r\n        const blob = new Blob([content]);\r\n        const fileName = this.exportName+`导出.xlsx`;\r\n        if (\"download\" in document.createElement(\"a\")) {\r\n          // 非IE下载\r\n          const elink = document.createElement(\"a\");\r\n          elink.download = fileName;\r\n          elink.style.display = \"none\";\r\n          elink.href = URL.createObjectURL(blob);\r\n          document.body.appendChild(elink);\r\n          elink.click();\r\n          URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n          document.body.removeChild(elink);\r\n        } else {\r\n          // IE10+下载\r\n          navigator.msSaveBlob(blob, fileName);\r\n        }\r\n      })\r\n      .catch((err) => {\r\n        console.log(err);\r\n      });\r\n    },\r\n    checktable(){\r\n      // this.priceValid[2].title=this.type==='tz'?\"台账期号\":\"报账期号\";\r\n      // this.priceValid[2].key=this.type==='tz'?\"tzqh\":'bzdqh';\r\n      // this.accountZQ[2].key=this.type==='tz'?\"tzqh\":'bzdqh';\r\n      // this.dbds[2].key=this.type==='tz'?\"tzqh\":'bzdqh';\r\n      // this.tzdl[2].key=this.type==='tz'?\"tzqh\":'bzdqh';\r\n      // this.gxzyc[2].key=this.type==='tz'?\"tzqh\":'bzdqh';\r\n      // this.dxzft[2].key=this.type==='tz'?\"tzqh\":'bzdqh';\r\n      // this.tzyczq[2].key=this.type==='tz'?\"tzqh\":'bzdqh';\r\n      switch (this.activeName){\r\n        case \"一表多站\":\r\n          this.titleName=\"一表多站\";\r\n          this.columns=this.stationError;\r\n          this.menu='A';\r\n          break;\r\n        case \"一站多表/多站多表\":\r\n          this.titleName=\"一站多表/多站多表\";\r\n          this.columns=this.stationError;\r\n          this.menu='A';\r\n          break;\r\n        case \"电价合理性\":\r\n          this.titleName=\"电价合理性\"\r\n          this.columns=this.priceValid\r\n          this.menu='B';\r\n          break;\r\n        case \"电表站址一致性\":\r\n          this.titleName=\"电表站址一致性\"\r\n          this.columns=this.stationSame\r\n          this.menu='C';\r\n          break;\r\n        case \"台账周期连续性\":\r\n          this.titleName=\"台账周期异常\"\r\n          this.columns=this.accountZQ\r\n          this.menu='D';\r\n          break;\r\n        case \"电表度数连续性\":\r\n          this.titleName=\"电表度数连续性\"\r\n          this.columns=this.dbds\r\n          this.menu='E';\r\n          break;\r\n        case \"日均电量的波动合理性(集团5gr)\":\r\n          this.titleName=\"日均电量的波动合理性(集团5gr)\"\r\n          this.columns=this.tzdl22\r\n          this.menu='F';\r\n          break;\r\n        case \"日均电量的波动合理性\":\r\n          this.titleName=\"日均电量的波动合理性\"\r\n          this.columns=this.tzdl2\r\n          this.menu='F';\r\n          break;\r\n        case \"日均耗电量合理性\":\r\n          this.titleName=\"日均耗电量合理性\"\r\n          this.columns=this.tzdl3\r\n          this.menu='F';\r\n          break;\r\n        case \"分摊比例准确性\":\r\n          this.titleName=\"分摊比例准确性\"\r\n          this.columns=this.gxzyc\r\n          this.menu='G';\r\n          break;\r\n        // case \"局站独享共享设置\":\r\n        //   this.titleName=\"局站独享共享设置\"\r\n        //   this.columns=this.dxzft\r\n        //   this.menu='H';\r\n        //   break;\r\n        case \"台账周期合理性\":\r\n          this.titleName=\"台账周期合理性\"\r\n          this.columns=this.tzyczq\r\n          this.menu='I';\r\n          break;\r\n        case \"电量合理性\":\r\n          this.titleName=\"电量合理性\"\r\n          this.columns=this.dlhlx\r\n          this.menu='I';\r\n          break;\r\n        // case \"电量合理性(省内大数据)\":\r\n        //   this.titleName=\"电量合理性(省内大数据)\"\r\n        //   this.columns=this.dlhlxda\r\n        //   this.menu='I';\r\n        //   break;\r\n        case \"地市和运营分局\":\r\n          this.titleName=\"总数详表\"\r\n          this.columns=this.zs\r\n          this.menu='I';\r\n          break;\r\n      }\r\n      this.query();\r\n    },\r\n    query(){\r\n      this.loading=true;\r\n      let data={\r\n        cityCode:this.cityCode,\r\n        countyCompaniesCode:this.countyCompanies,\r\n        operationsBranch:this.operationsBranch,\r\n        city: this.cityName,\r\n        siteType:this.siteType,\r\n        month:this.month,\r\n        exportName:this.exportName,\r\n        pageNum:this.pageNum,\r\n        pageSize:this.pageSize\r\n      }\r\n      getPowerError2(data).then((res) => {\r\n        this.loading=false;\r\n        if(res.data){\r\n          this.tableData=res.data.list\r\n          this.pageTotal=res.data.total\r\n        }\r\n      })\r\n    }\r\n  },\r\n  mounted() {\r\n  },\r\n  // watch:{\r\n  //   activeName(val) {\r\n  //     if (val) {\r\n  //       this.checktable();\r\n  //     }\r\n  //   },\r\n  // }\r\n}\r\n</script>\r\n<style scoped>\r\n.tableCard{\r\n  width: 100%;\r\n  margin-bottom: 20px;\r\n  padding:10px;\r\n  height:auto;\r\n  background-color: white\r\n}\r\n.tableTitle{\r\n  position: relative;\r\n  left: 5px;\r\n  display: flex;\r\n  align-items: stretch;\r\n  justify-content: space-between;\r\n  font-size: 14px;\r\n  margin-bottom:7px;\r\n  padding-right: 5px;\r\n  font-weight: bolder;\r\n}\r\n.tableTitle2{\r\n  position: relative;\r\n  left: 0px;\r\n  font-size: 14px;\r\n  margin-bottom:7px;\r\n  font-weight: 500;\r\n}\r\n.tableTitle::before{\r\n  position: absolute;\r\n  top:3px;\r\n  left: -5px;\r\n  display: inline-block;\r\n  content: \"\";\r\n  width: 2px;\r\n  height: 16px;\r\n  background-color: #1e88e5;\r\n}\r\n</style>\r\n"]}]}