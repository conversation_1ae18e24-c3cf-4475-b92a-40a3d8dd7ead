{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addOilAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addOilAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["addOilAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgQA,OAAA,eAAA,MAAA,gCAAA;AACA,SACA,iBADA,EAEA,WAFA,EAGA,eAHA,EAIA,yBAJA,EAKA,wBALA,EAMA,2BANA,EAOA,0BAPA,EAQA,aARA,EASA,mBATA,EAUA,oBAVA,EAWA,aAXA,EAYA,sBAZA,EAaA,uBAbA,EAcA,8BAdA,EAeA,sBAfA,EAgBA,4BAhBA,EAiBA,sBAjBA,EAkBA,eAlBA,EAmBA,cAnBA,EAoBA,oBApBA,EAqBA,YArBA,EAsBA,cAtBA,EAuBA,QAvBA,EAwBA,YAxBA,EAyBA,YAzBA,QA0BA,uCA1BA;AA2BA,SACA,cADA,EAEA,gBAFA,EAGA,YAHA,QAIA,0BAJA;AAKA,OAAA,sBAAA,MAAA,6CAAA;AACA,OAAA,WAAA,MAAA,kCAAA;AACA,OAAA,UAAA,MAAA,iCAAA;AACA,SAAA,QAAA,EAAA,UAAA,QAAA,mCAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AACA,OAAA,aAAA,MAAA,iBAAA;AACA,SAAA,oBAAA,QAAA,+BAAA;AACA,OAAA,UAAA,MAAA,sBAAA;AACA,SAAA,aAAA,QAAA,sBAAA;AACA,SAAA,MAAA,QAAA,cAAA;AACA,SAAA,UAAA,QAAA,mDAAA;AACA,OAAA,iBAAA,MAAA,qBAAA;AACA,OAAA,SAAA,MAAA,gBAAA;AACA,OAAA,YAAA,MAAA,sCAAA;AACA,SAAA,WAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,kBAAA,QAAA,2BAAA;AACA,IAAA,KAAA,GAAA,QAAA,EAAA;AACA,eAAA;AACA,EAAA,IAAA,EAAA,gBADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,eAAA,EAAA,eAAA;AAAA,IAAA,UAAA,EAAA,UAAA;AAAA,IAAA,WAAA,EAAA,WAAA;AAAA,IAAA,sBAAA,EAAA,sBAAA;AAAA,IAAA,iBAAA,EAAA,iBAAA;AAAA,IAAA,aAAA,EAAA,aAAA;AAAA,IAAA,UAAA,EAAA,UAAA;AAAA,IAAA,YAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AAAA;;AACA,QAAA,KAAA,GAAA,SAAA,KAAA,CAAA,CAAA,QAAA;AAAA,UAAA,GAAA,QAAA,GAAA;AAAA,UAAA,KAAA,QAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA;AACA,UAAA,GAAA,GAAA,EAAA;;AACA,UAAA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,IAAA,IAAA,EAAA;AACA,QAAA,GAAA,GAAA,MAAA;AACA;;AACA,aAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,GAAA,EAAA;AACA,QAAA,EAAA,EAAA;AACA,UAAA,KADA,mBACA;AACA;AACA,gBAAA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,IAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,GAAA;AACA;AACA;AANA;AADA,OAAA,EASA,GATA,CAAA,CAAA,CAAA;AAUA,KAhBA;;AAiBA,WAAA;AACA,MAAA,MAAA,EAAA,EADA;AAEA,MAAA,OAAA,EAAA,EAFA;AAGA,MAAA,cAAA,EAAA,KAHA;AAIA,MAAA,WAAA,EAAA,KAJA;AAKA,MAAA,cAAA,EAAA,KALA;AAMA,MAAA,aAAA,EAAA,UANA;AAOA,MAAA,OAAA,EAAA,EAPA;AAQA,MAAA,QAAA,EAAA,KARA;AASA,MAAA,UAAA,EAAA,IATA;AASA;AACA,MAAA,SAAA,EAAA,CAAA,CAVA;AAUA;AACA,MAAA,YAAA,EAAA,CAAA,CAXA;AAWA;AACA,MAAA,OAAA,EAAA,EAZA;AAYA;AACA,MAAA,cAAA,EAAA,EAbA;AAcA,MAAA,gBAAA,EAAA,EAdA;AAeA,MAAA,WAAA,EAAA,EAfA;AAgBA,MAAA,aAAA,EAAA,EAhBA;AAiBA,MAAA,eAAA,EAAA,EAjBA;AAkBA,MAAA,kBAAA,EAAA,EAlBA;AAmBA,MAAA,WAAA,EAAA,EAnBA;AAoBA,MAAA,cAAA,EAAA,EApBA;AAqBA,MAAA,eAAA,EAAA,EArBA;AAsBA,MAAA,QAAA,EAAA,KAtBA;AAsBA;AACA,MAAA,SAAA,EAAA,EAvBA;AAuBA;AACA,MAAA,UAAA,EAAA,EAxBA;AAyBA,MAAA,aAAA,EAAA,EAzBA;AA0BA,MAAA,SAAA,EAAA,EA1BA;AA2BA,MAAA,QAAA,EAAA,EA3BA;AA4BA,MAAA,YAAA,EAAA,EA5BA;AA6BA,MAAA,WAAA,EAAA,EA7BA;AA8BA,MAAA,OAAA,EAAA,KA9BA;AA+BA,MAAA,OAAA,EAAA,IA/BA;AA+BA;AACA,MAAA,OAAA,EAAA,IAhCA;AAgCA;AACA,MAAA,WAAA,EAAA,IAjCA;AAiCA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IADA;AACA;AACA,QAAA,OAAA,EAAA,EAFA;AAEA;AACA,QAAA,OAAA,EAAA,EAHA;AAGA;AACA,QAAA,UAAA,EAAA,IAJA;AAIA;AACA,QAAA,WAAA,EAAA,CALA;AAMA,QAAA,OAAA,EAAA,CANA;AAOA,QAAA,cAAA,EAAA,CAPA;AAQA,QAAA,WAAA,EAAA;AARA,OAlCA;AA6CA,MAAA,SAAA,EAAA;AACA,QAAA,OAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAFA,EAQA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,YAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SARA,EAcA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,cAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAdA,EAoBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SApBA,EA0BA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA1BA,EAgCA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAhCA,EAsCA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,IAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAtCA,EA4CA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,IAAA,EAAA,gBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA5CA,EAkDA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,IAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAlDA,EAwDA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAxDA,EA8DA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA9DA,EAoEA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SApEA,EA0EA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,mBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA1EA,EAgFA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,MAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAhFA,EAiFA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAjFA,CAHA;AAsFA,QAAA,IAAA,EAAA;AAtFA,OA7CA;AAqIA,MAAA,SAAA,EAAA,CArIA;AAsIA,MAAA,OAAA,EAAA,CAtIA;AAuIA,MAAA,QAAA,EAAA,EAvIA,CAuIA;;AAvIA,KAAA;AAyIA,GA9JA;AA+JA,EAAA,OAAA,EAAA;AACA,IAAA,MADA,oBACA,CAEA,CAHA;AAIA,IAAA,UAJA,sBAIA,GAJA,EAIA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA,EAAA,KAAA,EADA,CAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,UAAA,GAAA,CAAA,EAAA,EAAA;AACA,aAAA,KAAA,CAAA,eAAA,CAAA,MAAA,CAAA,GAAA,CAAA,EAAA,GAAA,EAAA;AACA,OAFA,MAEA;AACA,aAAA,SAAA,CAAA,aAAA;AACA,OApBA,CAqBA;AACA;;AACA,KA3BA;AA4BA,IAAA,UA5BA,wBA4BA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,WAAA;AACA,MAAA,IAAA,CAAA,WAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAA,cAAA,GAAA,GAAA,CALA,CAMA;AACA;AACA,KApCA;AAqCA,IAAA,YArCA,0BAqCA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,YAAA,IAAA,CAAA,UAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,CAAA,CAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,IAAA;AACA,SAHA,MAGA;AACA,UAAA,kBAAA,CAAA,IAAA,CAAA,UAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA;AACA;AACA,WALA;AAMA;AACA;AACA,KApDA;AAqDA;AACA,IAAA,oBAtDA,kCAsDA;AACA,UAAA,KAAA,UAAA,CAAA,OAAA,IAAA,IAAA,IAAA,KAAA,UAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AAAA;AACA;;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAJA,CAIA;AACA,KA3DA;AA4DA,IAAA,gBA5DA,4BA4DA,IA5DA,EA4DA;AACA,WAAA,UAAA,CAAA,OAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,UAAA,CAAA,WAAA,GAAA,IAAA,CAAA,IAAA,CAFA,CAGA;AACA,KAhEA;AAiEA,IAAA,WAjEA,yBAiEA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,WAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,EAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA;;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,IAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA;;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,MAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA;;AAEA,QAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,kBAAA;AACA,OAtBA;AAuBA,KA1FA;AA2FA,IAAA,UA3FA,wBA2FA;AACA,UAAA,KAAA,UAAA,CAAA,WAAA,IAAA,EAAA,EAAA;AACA,aAAA,UAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,WAAA,OAAA,GAAA,CAAA;AACA,WAAA,kBAAA;AACA,KAjGA;AAkGA,IAAA,eAlGA,6BAkGA;AACA,WAAA,UAAA;AACA,KApGA;AAqGA;AACA,IAAA,QAtGA,sBAsGA;AACA,UAAA,KAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,KAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,CAAA,GAAA,IAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA;;AACA,UAAA,CAAA,EAAA;AACA,aAAA,UAAA,CAAA,KAAA;AACA,OAFA,MAEA;AACA,aAAA,SAAA,CAAA,SAAA;AACA;AACA,KAnHA;AAoHA,IAAA,YApHA,wBAoHA,SApHA,EAoHA;AACA,UAAA,IAAA,GAAA,EAAA;AACA,WAAA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA;AACA,QAAA,SAAA,CAAA,GAAA,CAAA,UAAA,KAAA,EAAA;AACA,cAAA,KAAA,IAAA,KAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,SAJA;AAKA,OANA;AAOA,WAAA,MAAA,GAAA,IAAA;AACA,KA9HA;AAgIA;AACA,IAAA,UAjIA,sBAiIA,IAjIA,EAiIA;AAAA;;AACA,UAAA,CAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,GAAA,CAAA;AACA,YAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,YAAA,SAAA,GAAA,KAAA,UAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA;AACA,cAAA,IAAA,CAAA,EAAA,IAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,SAAA;AACA;;AACA,UAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,cAAA,GAAA,CAAA;AACA,UAAA,UAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,MAAA;AACA,SATA;AAUA,QAAA,IAAA,CAAA,GAAA,GAAA,CAAA;;AACA,YAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,GAAA;AACA;;AACA,YAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,cAAA,CAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,aAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,MADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;;AAKA,cAAA,MAAA,CAAA,kBAAA;AACA;AACA,WATA;AAUA;AACA;AACA,KApKA;AAqKA,IAAA,iBArKA,+BAqKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAA,WAAA,GAAA,IAAA,IAAA,EAAA;AACA,UAAA,WAAA,GAAA,WAAA,CAAA,WAAA,EAAA;AACA,UAAA,YAAA,GAAA,WAAA,CAAA,QAAA,KAAA,CAAA;;AACA,UAAA,QAAA,KAAA,SAAA,CAAA,IAAA,EAAA;AACA,aAAA,SAAA,CAAA,IAAA,GAAA,EAAA;AACA;;AACA,WAAA,SAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AACA;AACA;AACA,QAAA,SAAA,EAAA,KAAA,UAAA,CAAA,SAAA,IAAA,CAAA,CAAA,IAAA,KAAA,UAAA,CAAA,SAAA,IAAA,SAAA,GAAA,WAAA,GAAA,EAAA,GAAA,YAAA,GAAA,KAAA,UAAA,CAAA,SAHA;AAIA,QAAA,UAAA,EAAA,EAJA;AAKA,QAAA,YAAA,EAAA,EALA;AAMA,QAAA,aAAA,EAAA,EANA;AAOA,QAAA,SAAA,EAAA,GAPA;AAQA,QAAA,SAAA,EAAA,GARA;AASA,QAAA,WAAA,EAAA,GATA;AAUA,QAAA,cAAA,EAAA,GAVA;AAWA,QAAA,WAAA,EAAA,EAXA;AAYA,QAAA,SAAA,EAAA,GAZA;AAaA,QAAA,QAAA,EAAA,GAbA;AAcA,QAAA,SAAA,EAAA,GAdA;AAeA,QAAA,iBAAA,EAAA,EAfA;AAgBA,QAAA,MAAA,EAAA;AAhBA,OAAA;AAkBA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,UAAA,EAAA,QADA;AAEA,QAAA,YAAA,EAAA,QAFA;AAGA;AACA,QAAA,aAAA,EAAA,QAJA;AAKA,QAAA,SAAA,EAAA,QALA;AAMA,QAAA,SAAA,EAAA,QANA;AAOA,QAAA,WAAA,EAAA,QAPA;AAQA,QAAA,cAAA,EAAA,QARA;AASA,QAAA,WAAA,EAAA,QATA;AAUA,QAAA,SAAA,EAAA,QAVA;AAWA,QAAA,QAAA,EAAA,QAXA;AAYA,QAAA,SAAA,EAAA,QAZA;AAaA,QAAA,iBAAA,EAAA,QAbA;AAcA,QAAA,MAAA,EAAA;AAdA,OAAA;AAiBA,KA5NA;AA6NA;AACA,IAAA,SA9NA,qBA8NA,GA9NA,EA8NA;AACA,WAAA,OAAA,CAAA,KAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,GAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA;AAKA,KApOA;AAqOA,IAAA,UArOA,sBAqOA,KArOA,EAqOA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAEA;AARA,SAAA;AAUA;;AAEA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KA9PA;AA+PA,IAAA,cA/PA,0BA+PA,KA/PA,EA+PA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAEA;AARA,SAAA;AAUA;;AACA,WAAA,QAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KAvRA;AAwRA;AACA,IAAA,kBAzRA,gCAyRA;AAAA;;AACA,UAAA,QAAA,GAAA,KAAA,UAAA;AACA,MAAA,QAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA,MAAA,QAAA,CAAA,QAAA,GAAA,KAAA,QAAA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,4BADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,WAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,EAAA;AACA,cAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,WAFA;AAGA,UAAA,MAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,IAAA,CAAA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,SAAA,CAAA,IAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,CAAA,CAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,CAAA,CAAA;AACA;AACA,OAbA,EAaA,KAbA,CAaA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OAfA;AAgBA,KAnTA;AAoTA;AACA,IAAA,aArTA,2BAqTA;AACA,WAAA,UAAA,GAAA;AACA,QAAA,SAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,KAAA,OAFA;AAGA,QAAA,UAAA,EAAA,IAHA;AAIA,QAAA,OAAA,EAAA,MAAA,CAAA,KAAA,OAAA,CAJA;AAKA,QAAA,WAAA,EAAA,IALA;AAMA,QAAA,OAAA,EAAA,IANA;AAOA,QAAA,cAAA,EAAA;AAPA,OAAA;AASA,WAAA,kBAAA;AACA,KAhUA;AAiUA;AACA,IAAA,SAlUA,qBAkUA,GAlUA,EAkUA;AACA,UAAA,WAAA,GAAA,GAAA,CAAA,WAAA;AACA,UAAA,cAAA,GAAA,GAAA,CAAA,cAAA;AACA,UAAA,SAAA,GAAA,GAAA,CAAA,SAAA;;AACA,UAAA,WAAA,IAAA,IAAA,IAAA,cAAA,IAAA,IAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA;AACA,QAAA,KAAA,GAAA,WAAA,GAAA,cAAA;AACA,QAAA,GAAA,CAAA,SAAA,GAAA,KAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA;AACA,KA3UA;AA4UA,IAAA,MA5UA,oBA4UA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,WAAA;AACA;AACA;;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,oBAFA;AAGA,QAAA,IAAA,EAAA,gBAAA;AACA,cAAA,CAAA,GAAA,IAAA;AACA,cAAA,GAAA,GAAA,EAAA;AACA,cAAA,KAAA,GAAA,MAAA,CAAA,SAAA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,IAAA,GAAA,IAAA,CAAA,CAAA,CAAA;;AACA,gBAAA,IAAA,CAAA,EAAA,IAAA,IAAA,IAAA,IAAA,CAAA,EAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,kBAAA,IAAA,CAAA,OAAA,EAAA;AACA,gBAAA,CAAA,GAAA,KAAA;AACA;;AACA,cAAA,GAAA,IAAA,IAAA,CAAA,EAAA,GAAA,GAAA;AACA;AACA;;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,KAAA;;AACA,cAAA,CAAA,EAAA;AACA,gBAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,cAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,oBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,kBAAA,MAAA,CAAA,kBAAA;AACA;AACA,eALA;AAMA;AACA,WATA,MASA;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,4BAAA;AACA;AACA,SA7BA;AA8BA,QAAA,QAAA,EAAA,oBAAA,CACA;AA/BA,OAAA;AAiCA,KAnXA;AAoXA,IAAA,mBApXA,+BAoXA,IApXA,EAoXA;AACA,UAAA,IAAA,KAAA,SAAA,EAAA;AACA,aAAA,eAAA;AACA,OAFA,MAEA,IAAA,IAAA,KAAA,KAAA,EAAA;AACA,aAAA,kBAAA;AACA;AACA,KA1XA;AA2XA;AACA,IAAA,kBA5XA,gCA4XA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,MAAA,YAAA,CAAA,KAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,aAAA;AACA,SAFA,MAEA;AACA,cAAA,GAAA,GAAA,EAAA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA;;AACA,UAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,EAAA,EAAA,EAAA,MAAA,CAAA,UAAA,CAAA,OAAA;AACA;AACA,OAZA;AAaA,KA5YA;AA6YA,IAAA,eA7YA,6BA6YA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,CAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,cAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SARA;;AASA,YAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,EAAA,EAAA,EAAA,KAAA,UAAA,CAAA,OAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,0BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,+BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,2BAAA;AACA;AACA;AACA,KAvaA;AAwaA,IAAA,qBAxaA,mCAwaA;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,WAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAAA,CAAA;AACA,KA1aA;AA2aA,IAAA,SA3aA,uBA2aA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,gBAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,MAAA,GAAA,IAAA,CAAA,MAAA;;AACA,cAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,KAAA;AACA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,EAAA,GAAA,GAAA;AACA,SANA;;AAOA,YAAA,CAAA,EAAA;AACA,UAAA,aAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,SADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;;AAKA,cAAA,MAAA,CAAA,kBAAA;AACA;AACA,WATA;AAUA,SAXA,MAWA;AACA,eAAA,SAAA,CAAA,mBAAA;AACA;AACA;AACA,KAxcA;AAycA,IAAA,OAzcA,qBAycA;AACA,UAAA,GAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,GAAA,CAAA,kBAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA,KA9cA;AA+cA,IAAA,QA/cA,sBA+cA;AACA,UAAA,KAAA,YAAA,IAAA,CAAA,EAAA;AACA,YAAA,GAAA,GAAA,KAAA,YAAA,CAAA,KAAA,YAAA,EAAA,IAAA;;AACA,YAAA,GAAA,EAAA;AACA;AACA,kBAAA,KAAA,YAAA;AACA,iBAAA,CAAA;AACA,mBAAA,kBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,oBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,qBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,iBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,oBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,sBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,mBAAA;AACA;AACA;AACA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,kBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,yBAAA;AACA;AA9BA,WAFA,CAkCA;AACA;AACA;;AACA;AACA;AACA,KAzfA;AA0fA;AACA,IAAA,kBA3fA,gCA2fA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,cAAA;AACA,MAAA,IAAA,CAAA,UAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CAJA,CAKA;AAEA,KAlgBA;AAmgBA;AACA,IAAA,eApgBA,6BAogBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,WAAA;AACA,UAAA,MAAA,GAAA,GAAA,CAAA,CAAA,CAAA,IAAA,GAAA,IAAA,GAAA,CAAA,CAAA,CAAA,IAAA,GAAA,IAAA,GAAA,CAAA,CAAA,CAAA,IAAA,GAAA,IAAA,GAAA,CAAA,CAAA,CAAA,IAAA,GAAA;;AACA,UAAA,CAAA,MAAA,EAAA;AACA,aAAA,SAAA,CAAA,YAAA;AACA,QAAA,GAAA,GAAA,EAAA;AACA,OAPA,CAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;;;AACA,MAAA,IAAA,CAAA,OAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CAtBA,CAuBA;AACA,KA5hBA;AA6hBA,IAAA,iBA7hBA,+BA6hBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,aAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,SAAA,EAAA,gBAAA;AAGA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA;;AACA,UAAA,IAAA,CAAA,SAAA,IAAA,GAAA,IAAA,EAAA,EAAA;AACA,SAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,EAAA,OAAA,CAAA,CAAA;AAAA;AACA,OAFA,MAEA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,EAAA;AACA;;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CAZA,CAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KArjBA;AAsjBA,IAAA,kBAtjBA,8BAsjBA,CAtjBA,EAsjBA;AACA,UAAA,CAAA;;AACA,UAAA,CAAA,IAAA,CAAA,EAAA;AACA,QAAA,CAAA,GAAA,MAAA;AACA,OAFA,MAEA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,QAAA,CAAA,GAAA,MAAA;AACA,OAFA,MAEA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,QAAA,CAAA,GAAA,MAAA;AACA,OAFA,MAEA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,QAAA,CAAA,GAAA,MAAA;AACA;;AACA,aAAA,CAAA;AACA,KAlkBA;AAmkBA,IAAA,qBAnkBA,mCAmkBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,kBAAA,CAAA,KAAA,WAAA,CAAA,CAFA,CAIA;AACA;AACA;AACA;;AACA,MAAA,IAAA,CAAA,aAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KA7kBA;AA8kBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,oBA7mBA,kCA6mBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,eAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,UAAA,EAAA,iCAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,eAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA,CAFA,CAGA;AACA;;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,UAAA,GAAA,CAAA,GAAA,CAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,WAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CATA,CAUA;;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CAXA,CAYA;AACA,OAbA,MAaA,IAAA,GAAA,IAAA,IAAA,CAAA,eAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CADA,CAEA;AACA;AACA;AACA,OAtBA,CAuBA;;AACA,KAroBA;AAsoBA;AACA,IAAA,sBAvoBA,oCAuoBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,kBAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,kBAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CALA,CAMA;;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CARA,CASA;AACA;;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,eAAA,CAAA,IAAA,CAAA,CAXA,CAYA;AACA,OAbA,MAaA,IAAA,GAAA,IAAA,IAAA,CAAA,kBAAA,EAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,GAAA,CADA,CAEA;AACA;;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,eAAA,CAAA,IAAA,CAAA,CAJA,CAKA;AACA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,WAAA,EAAA,4BAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,cAAA,EAAA,iBAAA,EAxBA,CAyBA;AACA,KAjqBA;AAkqBA,IAAA,mBAlqBA,iCAkqBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,WAAA;AACA,MAAA,IAAA,CAAA,WAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KAvqBA;AAwqBA,IAAA,oBAxqBA,kCAwqBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,gBAAA;;AACA,UAAA,MAAA,GAAA,oBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;;AACA,UAAA,MAAA,EAAA;AAAA;AACA,aAAA,SAAA,CAAA,MAAA;AACA,OAFA,MAEA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA;AACA,KAlrBA;AAmrBA,IAAA,iBAnrBA,+BAmrBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,aAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KAxrBA;AAyrBA,IAAA,kBAzrBA,gCAyrBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,cAAA;;AACA,UAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,QAAA;AACA;;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,UAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,CAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAVA,CAWA;AACA,KArsBA;AAssBA,IAAA,yBAtsBA,uCAssBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,eAAA;AACA,MAAA,IAAA,CAAA,iBAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KA3sBA;AA4sBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,SAntBA,uBAmtBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,UAAA;AACA,MAAA,IAAA,CAAA,MAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KAxtBA;AAytBA,IAAA,aAztBA,2BAytBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,cAAA;AACA,MAAA,IAAA,CAAA,UAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KA9tBA;AA+tBA,IAAA,UA/tBA,sBA+tBA,MA/tBA,EA+tBA;AACA,WAAA,OAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA;AACA,UAAA,WAAA,EAAA,QADA;AAEA,UAAA,YAAA,EAAA,QAFA;AAGA,UAAA,SAAA,EAAA,QAHA;AAIA,UAAA,cAAA,EAAA,QAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAAA;AAOA;AACA,KA1uBA;AA2uBA;AACA,IAAA,UA5uBA,sBA4uBA,GA5uBA,EA4uBA,KA5uBA,EA4uBA,OA5uBA,EA4uBA,GA5uBA,EA4uBA;AACA,WAAA,gBAAA,GAAA,GAAA,CAAA,YAAA;AACA,WAAA,cAAA,GAAA,GAAA,CAAA,UAAA;AACA,WAAA,aAAA,GAAA,GAAA,CAAA,SAAA,IAAA,IAAA,IAAA,GAAA,CAAA,SAAA,KAAA,CAAA,GAAA,IAAA,GAAA,GAAA,CAAA,SAAA;AACA,WAAA,WAAA,GAAA,GAAA,CAAA,aAAA;AACA,WAAA,eAAA,GAAA,GAAA,CAAA,iBAAA;AACA,WAAA,eAAA,GAAA,GAAA,CAAA,WAAA;AACA,WAAA,kBAAA,GAAA,GAAA,CAAA,cAAA;AACA,WAAA,WAAA,GAAA,GAAA,CAAA,WAAA;AACA,WAAA,cAAA,GAAA,GAAA,CAAA,QAAA;AACA,WAAA,UAAA,GAAA,GAAA,CAAA,MAAA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,WAAA,YAAA,GAAA,OAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,CAAA,CAAA,KAAA,CAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAIA,KA9vBA;AA+vBA;AACA,IAAA,QAhwBA,oBAgwBA,IAhwBA,EAgwBA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,YAAA;AACA,UAAA,GAAA,GAAA,EAAA;;AACA,UAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAA,KAAA,GAAA,CAAA;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OAHA,MAGA,IAAA,KAAA,GAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,EAAA;AACA;AACA,YAAA,KAAA,IAAA,IAAA,CAAA,QAAA,GAAA,CAAA,IAAA,KAAA,IAAA,IAAA,CAAA,SAAA,GAAA,CAAA,EAAA;AACA,UAAA,KAAA,GAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,KAAA;AACA;;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OARA,MAQA;AACA,QAAA,OAAA,IAAA,CAAA;AACA;;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,MAAA,GAAA,GAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,KAAA,CAAA;;AACA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,GAAA,CAAA,YAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,GAAA,CAAA,UAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,GAAA,CAAA,SAAA,IAAA,IAAA,IAAA,GAAA,CAAA,SAAA,KAAA,CAAA,GAAA,IAAA,GAAA,GAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,aAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,GAAA,CAAA,iBAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,GAAA,CAAA,WAAA;AACA,QAAA,IAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,cAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,WAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,GAAA,CAAA,QAAA;AACA,QAAA,IAAA,CAAA,UAAA,GAAA,GAAA,CAAA,MAAA;AAEA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA,KAryBA;AAsyBA;AACA,IAAA,YAvyBA,wBAuyBA,MAvyBA,EAuyBA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,cAAA,MAAA;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,YAAA;AACA,UAAA,IAAA,GAAA,KAAA,cAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,cAAA;AACA,UAAA,IAAA,GAAA,KAAA,gBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,eAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,WAAA;AACA,UAAA,IAAA,GAAA,KAAA,aAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,aAAA;AACA,UAAA,IAAA,GAAA,KAAA,eAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,gBAAA;AACA,UAAA,IAAA,GAAA,KAAA,kBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,aAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,UAAA;AACA,UAAA,IAAA,GAAA,KAAA,cAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,mBAAA;AACA,UAAA,IAAA,GAAA,KAAA,eAAA;AACA;;AACA,aAAA,EAAA;AACA,UAAA,GAAA,GAAA,QAAA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA;AACA;AAxCA;;AA0CA,aAAA;AAAA,QAAA,GAAA,EAAA,GAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA;AACA,KAr1BA;AAs1BA,IAAA,IAt1BA,kBAs1BA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,YAAA;;AACA,UAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAA,KAAA,GAAA,CAAA;AACA,QAAA,OAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OARA,MAQA;AACA,QAAA,IAAA,CAAA,QAAA;AACA,QAAA,IAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA,KAv2BA;AAw2BA,IAAA,QAx2BA,oBAw2BA,KAx2BA,EAw2BA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,EAAA;;AACA,UAAA,KAAA,CAAA,MAAA,GAAA,GAAA,EAAA;AACA,eAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,GAAA,IAAA,KAAA;AACA;;AACA,aAAA,KAAA;AACA,KA92BA;AA+2BA,IAAA,mBA/2BA,iCA+2BA,CAEA,CAj3BA;AAk3BA,IAAA,iBAl3BA,6BAk3BA,IAl3BA,EAk3BA;AACA,WAAA,SAAA,CACA,IAAA,CAAA,IAAA,GAAA,gCADA;AAGA,KAt3BA;AAu3BA,IAAA,cAv3BA,0BAu3BA,KAv3BA,EAu3BA,IAv3BA,EAu3BA;AACA,WAAA,QAAA,CAAA,IAAA,CAAA;AACA,QAAA,OAAA,EAAA,IAAA,CAAA,IAAA,GAAA;AADA,OAAA;AAGA,KA33BA;AA43BA;AACA,IAAA,aA73BA,yBA63BA,IA73BA,EA63BA;AAAA;;AACA,UAAA,CAAA,IAAA,EAAA;AACA,aAAA,OAAA,CAAA,KAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,YAFA;AAGA,UAAA,QAAA,EAAA;AAHA,SAAA;AAKA;AACA;;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CATA,CASA;;AACA,UAAA,cAAA,GAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAVA,CAUA;;AACA,UAAA,UAAA,GAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA,QAAA,GAAA,CAAA,EAAA,cAAA,CAAA,CAXA,CAWA;;AACA,UAAA,SAAA,UAAA,IAAA,UAAA,UAAA,EAAA;AACA,aAAA,OAAA,CAAA,KAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,IAAA,CAAA,IAAA,GAAA,gCAFA;AAGA,UAAA,QAAA,EAAA;AAHA,SAAA;AAKA;AACA;;AACA,UAAA,KAAA,GAAA,EAAA;AACA,UAAA,KAAA,GAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA;AACA,QAAA,GAAA,EAAA,8BADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAA,EAAA,KAAA;AAHA,OAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA;AACA,UAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,YAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GADA;AAEA,YAAA,QAAA,EAAA,CAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA;AAKA,UAAA,IAAA,CAAA,IAAA,GAAA,KAAA;AACA;;AACA,QAAA,MAAA,CAAA,kBAAA;AACA,OAfA,EAeA,KAfA,CAeA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,KAAA;AACA,QAAA,IAAA,CAAA,IAAA,GAAA,KAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OAnBA;AAoBA,aAAA,KAAA;AACA,KA16BA;AA46BA;AACA,IAAA,YA76BA,0BA66BA;AAAA;;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,qCADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,YAAA,EAAA;AAHA,OAAA;AAKA,MAAA,KAAA,CAAA,IAAA,CAAA,GAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,QAAA,GAAA,KAAA;AACA,YAAA,OAAA,GAAA,GAAA;AACA,YAAA,IAAA,GAAA,IAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AACA,YAAA,QAAA,GAAA,eAAA;;AACA,YAAA,cAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA,EAAA;AACA;AACA,cAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA,QAAA;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,UAAA,KAAA,CAAA,IAAA,GAAA,GAAA,CAAA,eAAA,CAAA,IAAA,CAAA;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,UAAA,KAAA,CAAA,KAAA;AACA,UAAA,GAAA,CAAA,eAAA,CAAA,KAAA,CAAA,IAAA,EARA,CAQA;;AACA,UAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,SAVA,MAUA;AACA;AACA,UAAA,SAAA,CAAA,UAAA,CAAA,IAAA,EAAA,QAAA;AACA;AACA,OApBA;AAqBA;AAx8BA,GA/JA;AAymCA,EAAA,OAzmCA,qBAymCA;AACA,SAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,SAAA,SAAA,CAAA,OAAA,GAAA,KAAA,SAAA,CAAA,UAAA;AACA,SAAA,QAAA,GAAA,MAAA,CAAA,SAAA,CAAA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,KAAA,QAAA,EAAA,eAAA,EAJA,CAKA;;AACA,SAAA,YAAA,GAAA,MAAA,CAAA,aAAA,CAAA;AACA,SAAA,UAAA,CAAA,WAAA,GAAA,KAAA,YAAA,CAAA,CAAA,EAAA,QAAA;AACA,SAAA,UAAA,CAAA,OAAA,GAAA,KAAA,QAAA,CAAA,CAAA,EAAA,QAAA;AACA,QAAA,IAAA,GAAA,IAAA;AACA,IAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,MAAA,eAAA,CAAA;AAAA,QAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA;AAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,IAAA,CAAA,WAAA;AACA,OAHA;AAIA,KATA;AAUA;AA7nCA,CAAA", "sourcesContent": ["<style lang=\"less\">\r\n    .mytable .ivu-table-cell{\r\n        padding-left: 5px;\r\n        padding-right: 5px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: normal;\r\n        word-break: break-all;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .accountEs .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .accountEs .header-bar-show {\r\n        max-height: 300px;\r\n        padding-top: 14px;\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .accountEs .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n\r\n\r\n    .mytable .myspan{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block\r\n    }\r\n    .mytable .errorStle{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block;\r\n        color:red;\r\n    }\r\n</style>\r\n<template>\r\n    <div>\r\n        <div class=\"accountEs\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.accountno\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{ item.name }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"关键字:\" prop=\"oilUseBody\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"accountObj.oilUseBody\" placeholder=\"用能主体关键字模糊查询\" :style=\"formItemWidth\" />\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"用油类型:\" prop=\"oilType\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.oilType\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in oilTypes\" :value=\"item.typeCode\" :key=\"item.typeCode\">{{ item.typeName }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"用油类别:\" prop=\"oilCategory\" class=\"form-line-height\">\r\n                                <Select clearable v-model=\"accountObj.oilCategory\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in oilCategorys\" :value=\"item.typeCode\" :key=\"item.typeCode\">{{ item.typeName }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.company\" @on-change=\"selectChange(accountObj.company)\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"accountObj.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly :style=\"formItemWidth\"/>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <div  align=\"right\">\r\n                        <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n                        <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n                    </div>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <div>\r\n                <Row>\r\n                    <Col span=\"12\">\r\n                        <Page size=\"small\" :total=\"pageTotal\" :current=\"pageNum\" :page-size=\"pageSize\" show-elevator show-sizer show-total\r\n                              placement=\"top\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n                    </Col>\r\n                    <Col span=\"12\">\r\n                        <div align=\"right\" class=\"account\">\r\n                            <Button type=\"primary\" @click=\"addNewCoalAccount\">新增</Button>\r\n                            <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n                            <Button type=\"error\" @click=\"remove\">删除</Button>\r\n                            <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n                                <Button type='info' style=\"margin-left: 5px\">加入归集单\r\n                                    <Icon type='ios-arrow-down'></Icon>\r\n                                </Button>\r\n                                <DropdownMenu slot='list'>\r\n                                    <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n                                    <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n                                </DropdownMenu>\r\n                            </Dropdown>\r\n                            <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n                            <Button type=\"primary\" @click=\"loadTemplate\">导入模板下载</Button>\r\n                            <Upload style=\"float:right;\" :on-format-error=\"handleFormatError\"\r\n                                    :before-upload='onExcelUpload' :on-progress=\"handleProgress\"\r\n                                    :on-success=\"handleUploadSuccess\" :max-size=\"10240\" action=\"_blank\"\r\n                                    accept=\".csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\r\n                                    :format=\"['xls','xlsx']\">\r\n                                <Button icon='ios-cloud-upload'>导入</Button>\r\n                            </Upload>\r\n                        </div>\r\n                    </Col>\r\n                </Row>\r\n            </div>\r\n            <Table ref=\"accountEsTable\"\r\n                   border\r\n                   :columns=\"tbAccount.tailColumn\"\r\n                   :data=\"tbAccount.data\"\r\n                   class=\"mytable\">\r\n                <!--备注-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=500 v-model=\"editremark\" :ref=\"'remark'+index+10\" type=\"text\" @on-blur=\"setremark\"\r\n                               v-if=\"editIndex === index && columnsIndex === 10\"/>\r\n                        <Tooltip placement=\"left\" max-width=\"600\" v-else>\r\n                            <span :class=\"myStyle[index].remark\" style=\"width: 60px\" @click=\"selectCall(row,index,10,'remark')\">\r\n                                {{ ellipsis(row.remark) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                    <div v-else>\r\n                        <Tooltip placement=\"bottom\" max-width=\"200\">\r\n                            <span>{{ ellipsis(row.remark)}}</span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--用油主体-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"oilUseBody\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=100 v-model=\"editOilUseBody\" :ref=\"'oilUseBody'+index+1\" type=\"text\" @on-blur=\"validate\"\r\n                               v-if=\"editIndex === index && columnsIndex === 1\"/>\r\n                        <Tooltip placement=\"right\" max-width=\"200\" v-else>\r\n                            <span :class=\"myStyle[index].oilUseBody\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,1,'oilUseBody')\">\r\n                                {{ ellipsis(row.oilUseBody) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.oilUseBody }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--费用发生日-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"feeStartDate\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'feeStartDate'+index+2\" type=\"text\" v-model=\"editFeeStartDate\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 2\" />\r\n                    <span :class=\"myStyle[index].feeStartDate\" @click=\"selectCall(row,index,2,'feeStartDate')\" v-else>{{ row.feeStartDate }}</span>\r\n                </template>\r\n                <!--用油类型-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"oilImportType\" v-if=\"row.total == null\">\r\n                    <Select :ref=\"'oilImportType'+index+3\" type=\"text\" v-model=\"editOilType\" @on-change=\"validate\"\r\n                            v-if=\"editIndex === index && columnsIndex === 3\" transfer=\"true\">\r\n                        <Option value=\"1\" label=\"汽油92\"></Option>\r\n                        <Option value=\"2\" label=\"汽油95\"></Option>\r\n                        <Option value=\"3\" label=\"汽油98\"></Option>\r\n                        <Option value=\"4\" label=\"柴油0号\"></Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].oilImportType\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,3,'oilImportType')\" v-else>{{ row.oilImportType }}</span>\r\n                </template>\r\n                <!--加油量-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"oilAmount\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'oilAmount'+index+4\" type=\"text\" v-model=\"editOilAmount\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 4\"/>\r\n                    <span :class=\"myStyle[index].oilAmount\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,4,'oilAmount')\" v-else>{{ row.oilAmount }}</span>\r\n                </template>\r\n                <!--普票含税金额-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"ticketMoney\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'ticketMoney'+index+5\" type=\"text\" v-model=\"editTicketMoney\" @on-change=\"validateTicketMoney2\"\r\n                           v-if=\"editIndex === index && columnsIndex === 5\"/>\r\n                    <span :class=\"myStyle[index].ticketMoney\"\r\n                     style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\"\r\n                     @click=\"selectCall(row,index,5,'ticketMoney')\" v-else>{{ row.ticketMoney }}</span>\r\n                </template>\r\n                <!--专票含税金额-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"taxTicketMoney\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'taxTicketMoney'+index+6\" type=\"text\" v-model=\"editTaxTicketMoney\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 6\" />\r\n                    <span :class=\"myStyle[index].taxTicketMoney\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,6,'taxTicketMoney')\" v-else>{{ row.taxTicketMoney }}</span>\r\n                </template>\r\n                <!--专票税率-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"taxRateShow\" v-if=\"row.total == null\">\r\n                    <Select :ref=\"'taxRateShow'+index+7\" type=\"text\" v-model=\"editTaxRate\" @on-change=\"settaxrate\"\r\n                            v-if=\"editIndex === index && columnsIndex === 7\" transfer=\"true\">\r\n                        <Option value=\"1\">1</Option>\r\n                        <Option value=\"3\">3</Option>\r\n                        <Option value=\"6\">6</Option>\r\n                        <Option value=\"13\">13</Option>\r\n                        <Option value=\"16\">16</Option>\r\n                        <Option value=\"17\">17</Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].taxRateShow\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,7,'taxRateShow')\" v-else>{{ row.taxRateShow }}</span>\r\n                </template>\r\n                <!--其他费用-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"otherFee\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'otherFee'+index+8\" type=\"text\" v-model=\"editOtherMoney\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 8\" />\r\n                    <span v-else :class=\"myStyle[index].otherFee\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,8,'otherFee')\">{{ row.otherFee }}</span>\r\n                </template>\r\n                <!--用油类别-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"oilImportCategory\" v-if=\"row.total == null\">\r\n                    <Select :ref=\"'oilImportCategory'+index+9\" type=\"text\" v-model=\"editOilCategory\" @on-change=\"validate\"\r\n                            v-if=\"editIndex === index && columnsIndex === 9\" transfer=\"true\">\r\n                        <Option value=\"固定源\" label=\"固定源\"></Option>\r\n                        <Option value=\"移动源\" label=\"移动源\"></Option>\r\n                    </Select>\r\n                    <span v-else :class=\"myStyle[index].oilImportCategory\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,9,'oilImportCategory')\">{{ row.oilImportCategory }}</span>\r\n                </template>\r\n            </Table>\r\n            <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n        </div>\r\n        <div>\r\n            <add-bill-per ref=\"addBillPer\"\r\n                          v-on:refreshList=\"refresh\"></add-bill-per >\r\n            <completed-pre-modal ref=\"completedPre\" v-on:refreshList=\"refresh\"></completed-pre-modal>\r\n            <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n            <upload-file-modal ref=\"uploadFileModal\" v-on:onchange=\"change\"></upload-file-modal>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\nimport UploadFileModal from \"@/view/account/uploadFileModal\";\r\nimport {\r\n    _verify_StartDate,\r\n    judgeNumber,\r\n    _verify_EndDate,\r\n    _verify_PrevTotalReadings,\r\n    _verify_CurTotalReadings,\r\n    other_no_ammeteror_protocol,\r\n    self_no_ammeteror_protocol,\r\n    HFL_ammeteror,\r\n    judging_editability,\r\n    judging_editability1,\r\n    _verify_Money,\r\n    _calculateUsedReadings,\r\n    _calculateTotalReadings,\r\n    _calculateUnitPriceByUsedMoney,\r\n    _calculateAccountMoney,\r\n    _calculateQuotereadingsratio,\r\n    requiredFieldValidator,\r\n    countTaxamount1,\r\n    countTaxamount,\r\n    calculateActualMoney,\r\n    judge_negate,\r\n    judge_recovery,\r\n    judge_yb,\r\n    unitpirceMin,\r\n    unitpirceMax\r\n} from '@/view/account/PowerAccountController';\r\n    import {\r\n        saveOilAccount,\r\n        removeOilAccount,\r\n        selectOilIds\r\n    } from '@/api/coalHeatOilAccount';\r\n    import checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\n    import checkResult from \"@/view/account/check/checkResult\";\r\n    import alarmCheck from \"@/view/account/check/alarmCheck\";\r\n    import {getDates,testNumber,} from '@/view/account/powerAccountHelper';\r\n    import axios from '@/libs/api.request';\r\n    import SelectAmmeter from \"./selectAmmeter\";\r\n    import {_verify_FeeStartDate} from '@/view/account/PowerAccountEs';\r\n    import AddBillPer from \"./addOilBillPreModal\";\r\n    import {reJoinBillpre} from '@/api/accountBillPer';\r\n    import {blist1} from \"@/libs/tools\";\r\n    import {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\n    import CompletedPreModal from \"./completedPreModal\";\r\n    import indexData from '@/config/index'\r\n    import CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import {getUserdata,getUserByUserRole,getCountrysdata,getCountryByUserId} from '@/api/basedata/ammeter.js'\r\n    let dates=getDates();\r\n    export default {\r\n        name: 'addCoalAccount',\r\n        components: {UploadFileModal, alarmCheck, checkResult, checkResultAndResponse,CompletedPreModal, SelectAmmeter,AddBillPer,CountryModal},\r\n        data() {\r\n            let photo = (h, {row, index}) => {\r\n                let that = this\r\n                let str = ''\r\n                if (row.projectname != '小计' && row.projectname != '合计') {\r\n                    str = '上传附件'\r\n                }\r\n                return h(\"div\", [h(\"u\", {\r\n                    on: {\r\n                        click() {\r\n                            //打开弹出框\r\n                            if (row.projectname != '小计' && row.projectname != '合计') {\r\n                                that.uploadFile(row)\r\n                            }\r\n                        }\r\n                    }\r\n                }, str)]);\r\n            };\r\n            return {\r\n                submit:[],\r\n                submit2:[],\r\n                showCheckModel:false,\r\n                showJhModel:false,\r\n                showAlarmModel:false,\r\n                formItemWidth: widthstyle,\r\n                version:'',\r\n                dateList:dates,\r\n                filterColl: true,//搜索面板展开\r\n                editIndex: -1,//当前编辑行\r\n                columnsIndex:-1,//当前编辑列\r\n                myStyle:[],//样式\r\n                editOilUseBody:'',\r\n                editFeeStartDate:'',\r\n                editOilType:'',\r\n                editOilAmount:'',\r\n                editTicketMoney:'',\r\n                editTaxTicketMoney:'',\r\n                editTaxRate:'',\r\n                editOtherMoney:'',\r\n                editOilCategory:'',\r\n                spinShow:false,//遮罩\r\n                categorys:[],//描述类型\r\n                editremark:'',\r\n                accountStatus:[],\r\n                companies:[],\r\n                oilTypes: [],\r\n                oilCategorys: [],\r\n                departments:[],\r\n                isAdmin:false,\r\n                company:null,//用户默认公司\r\n                country:null,//用户默认所属部门\r\n                countryName:null,//用户默认所属部门\r\n                accountObj:{\r\n                    accountno:dates[1].code,//期号,默认当前月\r\n                    company:\"\",//分公司\r\n                    country:\"\",//所属部门\r\n                    oilUseBody:null,//用油主体\r\n                    oilCategory:1,\r\n                    oilType:1,\r\n                    oilAccountType: 1,\r\n                    countryName: \"\",\r\n\r\n                },\r\n                tbAccount: {\r\n                    loading: true,\r\n                    columns: [],\r\n                    tailColumn: [\r\n                        {type: 'selection', width: 60, align: 'center',},\r\n                        {\r\n                            title: \"期号\",\r\n                            key: \"accountNo\",\r\n                            align: \"center\",\r\n                            width: 90,\r\n                        },\r\n                        {\r\n                            title: \"用油主体\",\r\n                            slot: \"oilUseBody\",\r\n                            align: \"center\",\r\n                            width: 150,\r\n                        },\r\n                        {\r\n                            title: \"费用发生日\",\r\n                            slot: \"feeStartDate\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"用油类型\",\r\n                            slot: \"oilImportType\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"加油量(L)\",\r\n                            slot: \"oilAmount\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"单价(元)\",\r\n                            key: \"unitPrice\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"普票含税金额(元)\",\r\n                            slot: \"ticketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票含税金额(元)\",\r\n                            slot: \"taxTicketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票税率（%）\",\r\n                            slot: \"taxRateShow\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"专票税额\",\r\n                            key: \"taxAmount\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"其他(元)\",\r\n                            slot: \"otherFee\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"实缴费用(元)含税\",\r\n                            key: \"paidMoney\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {\r\n                            title: \"用油类别\",\r\n                            slot: \"oilImportCategory\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {title: \"附件\", align: \"center\", render: photo, width: 100},\r\n                        {title: \"备注\", slot: \"remark\",align: \"center\", width: 150},\r\n                    ],\r\n                    data: [],\r\n                },\r\n                pageTotal: 0,\r\n                pageNum: 1,\r\n                pageSize: 10,//当前页\r\n            }\r\n        },\r\n        methods: {\r\n            change() {\r\n\r\n            },\r\n            uploadFile(row) {\r\n                console.log(row, \"row\");\r\n                // let id;\r\n                // if(!row.id2) {\r\n                //     editAmmeter('', 0).then(res => {\r\n                //         debugger\r\n                //         console.log(res, \"res\");\r\n                //         row.id2 = res.data.id;\r\n\r\n                //         this.id2 = res.data.id\r\n                //         // debugger\r\n                //         // this.fileParam.busiId = ;\r\n                //         this.$refs.uploadFileModal.choose(row.id2 + '');\r\n                //     })\r\n                // }else {\r\n\r\n                if(row.id) {\r\n                    this.$refs.uploadFileModal.choose(row.id + '');\r\n                }else {\r\n                    this.errorTips(\"请先保存后再上传文件！\");\r\n                }\r\n                // }\r\n                // console.log(row, \"row\");\r\n            },\r\n            settaxrate() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxRate;\r\n                data.taxRateShow = val;\r\n                data.editType = 1;\r\n                data.taxAmount = data.taxRateShow*data.taxTicketMoney/100;\r\n                // let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                // data.paidMoney = paidMoney.toFixed(2);\r\n            },\r\n            selectChange(){\r\n                let that = this;\r\n                if (that.accountObj.company != undefined) {\r\n                    if(that.accountObj.company == \"-1\"){\r\n                        that.accountObj.country = -1;\r\n                        that.accountObj.countryName = null;\r\n                    }else{\r\n                        getCountryByUserId(that.accountObj.company).then(res => {\r\n                            if(res.data.departments.length != 0){\r\n                                that.accountObj.country = res.data.departments[0].id;\r\n                                that.accountObj.countryName = res.data.departments[0].name;\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            },\r\n            //选择所属部门开始\r\n            chooseResponseCenter() {\r\n                if(this.accountObj.company == null || this.accountObj.company == \"-1\" ){\r\n                    this.$Message.info(\"请先选择分公司\");return;\r\n                }\r\n                this.$refs.countryModal.choose(this.accountObj.company);//所属部门\r\n            },\r\n            getDataFromModal(data) {\r\n                this.accountObj.country = data.id;\r\n                this.accountObj.countryName = data.name;\r\n                //选择所属部门结束\r\n            },\r\n            getUserData(){\r\n                let that = this;\r\n                getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                    if(res.data.companies.length != 0){\r\n                        let companies = res.data.companies;\r\n                        if(res.data.companies[0].id == \"**********\"){\r\n                            companies = that.companies;\r\n                        }\r\n                        that.company = companies[0].id;\r\n                        that.accountObj.company = companies[0].id;\r\n                    }\r\n                    if(res.data.departments.length != 0){\r\n                        let departments = res.data.departments;\r\n                        if(res.data.companies[0].id == \"**********\" && that.departments.length != 0){\r\n                            departments = that.departments\r\n                        }\r\n                        that.country = departments[0].id;\r\n                        that.countryName = departments[0].name;\r\n                        that.accountObj.country = Number(departments[0].id);\r\n                        that.accountObj.countryName = departments[0].name;\r\n                    }\r\n\r\n                    that.pageNum = 1\r\n                    that.getAccountMessages();\r\n                });\r\n            },\r\n            searchList(){\r\n                if(this.accountObj.countryName == \"\"){\r\n                    this.accountObj.country = \"-1\";\r\n                }\r\n                this.pageNum = 1;\r\n                this.getAccountMessages()\r\n            },\r\n            accountnoChange(){\r\n                this.searchList()\r\n            },\r\n            //点击保存\r\n            preserve() {\r\n                let dataL = this.$refs.accountEsTable.getSelection();\r\n                let b = false;\r\n                let array = [];\r\n                for (let i = 0; i < dataL.length; i ++) {\r\n                    b = true;\r\n                    array.push(dataL[i])\r\n                }\r\n                if(b){\r\n                    this.submitData(array);\r\n                }else {\r\n                    this.errorTips('没有可保存数据')\r\n                }\r\n            },\r\n            submitChange(indexList){\r\n                let data=[];\r\n                this.submit2.map((item,index)=>{\r\n                    indexList.map((item2)=>{\r\n                        if(index==item2){\r\n                            data.push(item)\r\n                        }\r\n                    })\r\n                });\r\n                this.submit=data\r\n            },\r\n\r\n            //提交数据\r\n            submitData(data){\r\n                let a = [];\r\n                let that=this;\r\n                if(data != null && data.length > 0){\r\n                    let number = 0;\r\n                    let submitData = [];\r\n                    let str = '';\r\n                    let accountno = this.accountObj.accountno;\r\n                    data.forEach(function (item) {\r\n                        // 校验数据\r\n                        if(item.id == null){\r\n                            item.accountno = accountno\r\n                        }\r\n                        a.push(item.id);\r\n                        item.oilAccountType = 1;\r\n                        submitData.push(item);\r\n                        number ++;\r\n                    });\r\n                    that.ids=a;\r\n                    if(str.length > 0){\r\n                        this.errorTips(str)\r\n                    }\r\n                    if(submitData.length > 0){\r\n                        saveOilAccount(submitData).then((res) => {\r\n                            if (res.data.code == 0) {\r\n                                this.$Message.info({\r\n                                    content: '提示：成功保存 ' + res.data.num + ' 条数据',\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }\r\n                }\r\n            },\r\n            addNewCoalAccount() {\r\n                // let companyId = this.accountObj.company;\r\n                // let country = this.accountObj.country;\r\n                // if(companyId != null && country != null){\r\n                //     let obj = {\r\n                //         company:companyId,\r\n                //         country:country,\r\n                //         accountno:this.accountObj.accountno,\r\n                //         accountType:'1',\r\n                //         accountestype:1\r\n                //     }\r\n                // }else{\r\n                //     this.errorTips('请选择分公司和部门')\r\n                // }\r\n                const currentDate = new Date();\r\n                const currentYear = currentDate.getFullYear();\r\n                const currentMonth = currentDate.getMonth() + 1;\r\n                if (null == this.tbAccount.data) {\r\n                    this.tbAccount.data = [];\r\n                }\r\n                this.tbAccount.data.unshift({\r\n                    // accountNo:dates[1].code,\r\n                    // accountNo: this.accountObj.accountno,\r\n                    accountNo: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined) ? currentYear+\"\"+currentMonth: this.accountObj.accountno,\r\n                    oilUseBody: \"\",\r\n                    feeStartDate:\"\",\r\n                    oilImportType: \"\",\r\n                    oilAmount:\"0\",\r\n                    unitPrice:\"0\",\r\n                    ticketMoney:\"0\",\r\n                    taxTicketMoney:\"0\",\r\n                    taxRateShow:\"\",\r\n                    taxAmount:\"0\",\r\n                    otherFee:\"0\",\r\n                    paidMoney:\"0\",\r\n                    oilImportCategory:\"\",\r\n                    remark:\"\",\r\n                });\r\n                this.myStyle.push({\r\n                    oilUseBody: 'myspan',\r\n                    feeStartDate: 'myspan',\r\n                    // curtotalreadings: 'myspan',\r\n                    oilImportType: 'myspan',\r\n                    oilAmount: 'myspan',\r\n                    unitPrice: 'myspan',\r\n                    ticketMoney:\"myspan\",\r\n                    taxTicketMoney:\"myspan\",\r\n                    taxRateShow: 'myspan',\r\n                    taxAmount: 'myspan',\r\n                    otherFee: 'myspan',\r\n                    paidMoney: 'myspan',\r\n                    oilImportCategory: 'myspan',\r\n                    remark: 'myspan',\r\n\r\n                    });\r\n            },\r\n            //验证错误弹出提示框\r\n            errorTips(str){\r\n                this.$Notice.error({\r\n                    title: '提示',\r\n                    desc: str,\r\n                    duration: 10\r\n                });\r\n            },\r\n            handlePage(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageNum = value;\r\n                this.getAccountMessages();\r\n            },\r\n            handlePageSize(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n                this.pageSize = value;\r\n                this.getAccountMessages();\r\n            },\r\n            //向后台请求数据\r\n            getAccountMessages() {\r\n                 let postData = this.accountObj;\r\n                postData.pageNum = this.pageNum;\r\n                postData.pageSize = this.pageSize;\r\n                let req = {\r\n                    url : \"/business/oil/account/list\",\r\n                    method : \"get\",\r\n                    params : postData\r\n                };\r\n                this.tbAccount.loading = true;\r\n                axios.request(req).then(res => {\r\n                    this.tbAccount.loading = false;\r\n                    if (res.data) {\r\n                        let data = res.data.rows;\r\n                        data.forEach(function (item) {\r\n                            item.editType = 0;\r\n                        });\r\n                        this.tbAccount.data = data;\r\n                        this.pageTotal = res.data.total || 0;\r\n                        this.setMyStyle(this.tbAccount.data.length);\r\n                        this.editIndex = -1;\r\n                        this.columnsIndex = -1;\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            },\r\n            //重置\r\n            onResetHandle(){\r\n                this.accountObj = {\r\n                    accountno:null,\r\n                    company:this.company,\r\n                    oilUseBody:null,\r\n                    country:Number(this.country),\r\n                    oilCategory:null,\r\n                    oilType:null,\r\n                    oilAccountType:1,\r\n                }\r\n                this.getAccountMessages()\r\n            },\r\n            //计算单价\r\n            unitPrice(row){\r\n                let ticketMoney = row.ticketMoney;\r\n                let taxTicketMoney = row.taxTicketMoney;\r\n                let oilAmount = row.oilAmount;\r\n                if(ticketMoney != null || taxTicketMoney != null){\r\n                    let total = null;\r\n                    total = ticketMoney + taxTicketMoney;\r\n                    row.unitpirce = total/oilAmount.toFixed(2);\r\n                }\r\n            },\r\n            remove(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                if(data == null || data.length === 0){\r\n                    this.errorTips(\"请选择要删除的数据\")\r\n                    return;\r\n                }\r\n                this.$Modal.confirm({\r\n                    title: '提示',\r\n                    content: '<p>是否确认删除选中信息？</p>',\r\n                    onOk: () => {\r\n                        let b = true;\r\n                        let ids = '';\r\n                        let total = this.pageTotal\r\n                        for(let i=0;i<data.length;i++){\r\n                            let item = data[i];\r\n                            if(item.id != null && item.id.length > 0){\r\n                                if(item.pabriid){\r\n                                    b = false;\r\n                                }\r\n                                ids += item.id + ',';\r\n                            }\r\n                        }\r\n                        this.pageTotal = total\r\n                        if(b){\r\n                            if(ids.length > 0){\r\n                                removeOilAccount(ids).then((res) => {\r\n                                    if (res.data.code == 0) {\r\n                                        this.$Message.success('删除成功');\r\n                                        this.getAccountMessages();\r\n                                    }\r\n                                });\r\n                            }\r\n                        }else {\r\n                            this.errorTips('选中信息中有信息还没有跟归集单解除关联，请先解除关联')\r\n                        }\r\n                    },\r\n                    onCancel: () => {\r\n                    }\r\n                });\r\n            },\r\n            openAddBillPerModal(name) {\r\n                if (name === 'current') {\r\n                    this.selectedAccount()\r\n                } else if (name === 'all') {\r\n                    this.selectedAllAccount()\r\n                }\r\n            },\r\n            //加入归集单，全部有效台账\r\n            selectedAllAccount(){\r\n                let that = this\r\n                that.spinShow = true;\r\n                selectOilIds(this.accountObj).then(res => {\r\n                    that.spinShow = false;\r\n                    if(res.data.length == 0){\r\n                        that.errorTips('无有效数据可加入归集单')\r\n                    }else {\r\n                        let ids = [];\r\n                        for(let i=0;i<res.data.rows.length;i++){\r\n                            let item = res.data.rows[i];\r\n                            ids.push(item.id)\r\n                        }\r\n                        that.$refs.addBillPer.initAmmeter(ids, 21,this.accountObj.country);\r\n                    }\r\n                });\r\n            },\r\n            selectedAccount(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = 1;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要加入归集单的台账')\r\n                } else {\r\n                    let ids = [];\r\n                    data.forEach(function (item) {\r\n                        if(item.status === 5){\r\n                            b = 3\r\n                        }\r\n                        if(item.status === 4){\r\n                            b=4;\r\n                        }\r\n                        ids.push(item.id)\r\n                    });\r\n                    if(b === 1){\r\n                        this.$refs.addBillPer.initAmmeter(ids,21,this.accountObj.country);\r\n                    }else if(b === 2) {\r\n                        this.errorTips('选中的台账中存在临时数据，请先保存再加入归集单！')\r\n                    }else if(b===3){\r\n                        this.errorTips('退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮')\r\n                    }else if(b===4){\r\n                        this.errorTips('选择的台账有已加入归集单的台账，不能加入其他归集单')\r\n                    }\r\n                }\r\n            },\r\n            openCompletedPreModal(){\r\n                this.$refs.completedPre.initAmmeter(this.accountObj.country,2);\r\n            },\r\n            againJoin(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = true;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要重新加入归集单的台账')\r\n                } else {\r\n                    let ids = '';\r\n                    data.forEach(function (item) {\r\n                        let status = item.status;\r\n                        if(status != 5){\r\n                            b = false;\r\n                        }\r\n                        ids+= item.id +','\r\n                    });\r\n                    if(b){\r\n                        reJoinBillpre(ids).then((res) =>{\r\n                            if(res.data.code==0){\r\n                                this.$Message.info({\r\n                                    content:'提示：操作成功' ,\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }else {\r\n                        this.errorTips('只有已退回的台账才能重新加入归集单')\r\n                    }\r\n                }\r\n            },\r\n            refresh(){\r\n                let obj = this\r\n                setTimeout(function () {\r\n                    obj.getAccountMessages()\r\n                },200);\r\n            },\r\n            validate(){\r\n                if(this.columnsIndex != 5){\r\n                    let val = this.enterOperate(this.columnsIndex).data;\r\n                    if(val) {\r\n                        // if (testNumber(val)) {\r\n                            switch (this.columnsIndex) {\r\n                                case 1:\r\n                                this.validateOilUseBody();\r\n                                break;\r\n                            case 2:\r\n                                this.validateFeeStartDate();\r\n                                break;\r\n                            case 3:\r\n                                this.validateOilImportType();\r\n                                break;\r\n                            case 4:\r\n                                this.validateOilAmount();\r\n                                break;\r\n                            case 5:\r\n                                this.validateTicketMoney2();\r\n                                break;\r\n                            case 6:\r\n                                this.validateTaxTicketMoney();\r\n                                break;\r\n                            case 7:\r\n                                this.validateTaxRateShow();\r\n                                break;\r\n                            // case 3:\r\n                            //     this.validateFeeStartDate();\r\n                            //     break;\r\n                            case 8:\r\n                                this.validateOtherMoney();\r\n                                break;\r\n                            case 9:\r\n                                this.validateOilImportCategory();\r\n                                break;\r\n                            }\r\n                        // }else{\r\n                        //     this.errorTips('请输入数字！');\r\n                        // }\r\n                    }\r\n                }\r\n            },\r\n            //用油主体\r\n            validateOilUseBody() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOilUseBody;\r\n                data.oilUseBody = val;\r\n                data.editType = 1;\r\n                // }\r\n\r\n            },\r\n            //费用发生日\r\n            validateEnddate() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editEndDate;\r\n                let isDian = val[4]=='.' && val[7]=='.' || val[4]=='.' && val[6]=='.' ;\r\n                if(!isDian) {\r\n                    this.errorTips(\"结束时间格式不正确！\");\r\n                    val = \"\";\r\n                }\r\n                // if (val != data.old_enddate) {\r\n                //     // 验证截止日期方法\r\n                //     let result = _verify_EndDate1(data, val);\r\n                //     if (result) {\r\n                //         this.errorTips(result);\r\n                //         this.myStyle[this.editIndex].endDate = \"errorStle\";\r\n                //     } else {\r\n                //         this.myStyle[this.editIndex].endDate = \"myspan\";\r\n\r\n                //         this.updateenddate(data, val)\r\n\r\n                //     }\r\n                // } else if (val == data.old_enddate) {\r\n                    data.endDate = val;\r\n                    data.editType = 1;\r\n                // }\r\n            },\r\n            validateOilAmount() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOilAmount;\r\n                console.log(data.startDate, \"data.startDate\")\r\n\r\n\r\n                data.oilAmount = val;\r\n                if(data.paidMoney && val!=\"\") {\r\n                    (data.paidMoney/data.oilAmount).toFixed(4);;\r\n                }else {\r\n                    data.unitPrice = \"\";\r\n                }\r\n                data.editType = 1;\r\n                // console.log(data.startDate.split(\".\")[2]*1, \"data.startDate.split(\")\r\n                // console.log(data.endDate.split(\".\")[2]*1, \"data.endDate.split(\")\r\n                // if(data.startDate !=\"\" && data.endDate !=\"\") {\r\n                //     let riqiLengh = data.endDate.split(\".\")[2]*1 - data.startDate.split(\".\")[2]*1;\r\n                //     data.heatAmount = val*riqiLengh*60*0.7*3.6/1000000;\r\n                //     console.log(data.heatAmount, \"data.heatAmount\")\r\n                // }\r\n                // else {\r\n                //     this.errorTips(\"开始或者结束时间不能为空！\");\r\n                //     data.heatAmount = \"\";\r\n                // }\r\n            },\r\n            getEditOilTypeName(v) {\r\n                let a;\r\n                if(v == 1) {\r\n                    a = '汽油92';\r\n                }else if(v == 2) {\r\n                    a = '汽油95';\r\n                }else if(v == 3) {\r\n                    a = '汽油98';\r\n                }else if(v == 4) {\r\n                    a = '柴油0号';\r\n                }\r\n                return a;\r\n            },\r\n            validateOilImportType() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.getEditOilTypeName(this.editOilType);\r\n\r\n                // <Option value=\"1\" label=\"汽油92\"></Option>\r\n                //         <Option value=\"2\" label=\"汽油95\"></Option>\r\n                //         <Option value=\"3\" label=\"汽油98\"></Option>\r\n                //         <Option value=\"4\" label=\"柴油0号\"></Option>\r\n                data.oilImportType = val;\r\n                data.editType = 1;\r\n            },\r\n            //验证普票\r\n            // validateTicketMoney2() {\r\n            //     // debugger\r\n            //     let data = this.tbAccount.data[this.editIndex];\r\n            //     let val = this.editTicketMoney;\r\n            //     // console.log(data.paidMoney, data.oilAmount, \"data.paidMoney, data.oilAmount\");\r\n            //     // console.log(val, \"val\");\r\n            //     // if (val != data.old_ticketmoney) {\r\n            //         // val = parseFloat(val);\r\n            //         // data.ticketMoney = _verify_Money(data, val);\r\n            //         data.ticketMoney = val;\r\n            //         data.editType = 1;\r\n            //         // data.inputticketmoney = _verify_Money(data, val)\r\n            //         // data.ticketmoney = calculateActualMoney(data,val)\r\n            //         let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n            //          data.paidMoney = paidMoney.toFixed(2);\r\n            //         let unitPrice = data.paidMoney/data.oilAmount*1;\r\n            //         console.log(data.ticketMoney, \"data.ticketMoney\")\r\n            //         data.unitPrice = unitPrice.toFixed(4);\r\n            //         // let unitpirce = data.oilAmount?(data.paidMoney/data.oilAmount*1):0;\r\n\r\n            //         // this.calculateAll(data);\r\n            //     // } else if (val == data.old_ticketmoney) {\r\n            //     //     data.ticketMoney = val;\r\n            //     //     data.editType = 1;\r\n            //     //     // data.inputticketmoney = val;\r\n            //     //     // data.ticketmoney = calculateActualMoney(data,val)\r\n            //     //     // this.calculateAll(data);\r\n            //     // }\r\n            //     // this.validateUnitPrice(data)\r\n            // },\r\n            validateTicketMoney2() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTicketMoney;\r\n                console.log(data.paidMoney, data.coalAmount, \"data.paidMoney, data.coalAmount\");\r\n                if (val != data.old_ticketmoney) {\r\n                    val = parseFloat(val);\r\n                    data.ticketMoney = _verify_Money(data, val);\r\n                    // data.inputticketmoney = _verify_Money(data, val)\r\n                    // data.ticketmoney = calculateActualMoney(data,val)\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                    let unitPrice = data.coalAmount?(data.paidMoney/data.coalAmount*1):0;\r\n                    console.log(unitPrice, \"unitPrice\")\r\n                    data.unitPrice = unitPrice.toFixed(4);\r\n                    // let unitpirce = data.coalAmount?(data.paidMoney/data.coalAmount*1):0;\r\n                    data.editType = 1;\r\n                    // this.calculateAll(data);\r\n                } else if (val == data.old_ticketmoney) {\r\n                    data.ticketMoney = val;\r\n                    // data.inputticketmoney = val;\r\n                    // data.ticketmoney = calculateActualMoney(data,val)\r\n                    // this.calculateAll(data);\r\n                }\r\n                // this.validateUnitPrice(data)\r\n            },\r\n            //验证专票\r\n            validateTaxTicketMoney() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxTicketMoney;\r\n                if (val != data.old_taxticketmoney) {\r\n                    val = parseFloat(val);\r\n                    data.taxTicketMoney = _verify_Money(data, val)\r\n                    data.editType = 1;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                    // let unitPrice = data.oilAmount?(data.paidMoney/data.oilAmount):0;\r\n                    let unitPrice = data.paidMoney/data.oilAmount;\r\n                    data.unitPrice = unitPrice.toFixed(4);\r\n                    // data.taxticketmoney = calculateActualMoney(data,val)\r\n                    // data.taxAmount = data.taxTicketMoney*data.taxRateShow;\r\n                    data.taxAmount = countTaxamount1(data);\r\n                    // this.calculateAll(data);\r\n                } else if (val == data.old_taxticketmoney) {\r\n                    data.taxTicketMoney = val;\r\n                    // data.taxticketmoney = calculateActualMoney(data,val)\r\n                    // data.taxAmount = (data.taxTicketMoney*1)*(data.taxRateShow*1);\r\n                    data.taxAmount = countTaxamount1(data);\r\n                    // this.calculateAll(data);\r\n                }\r\n                console.log(data.taxRateShow, \".taxRateShowdata5555555555\");\r\n                console.log(data.taxTicketMoney, \".taxTicketMoney\");\r\n                // this.validateUnitPrice(data)\r\n            },\r\n            validateTaxRateShow() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxRate;\r\n                data.taxRateShow = val;\r\n                data.editType = 1;\r\n            },\r\n            validateFeeStartDate(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editFeeStartDate;\r\n                let result = _verify_FeeStartDate(data,val);\r\n                if(result){//失败就弹出提示内容，并将数据恢复初始化\r\n                    this.errorTips(result)\r\n                }else{\r\n                    data.feeStartDate = val;\r\n                    data.editType = 1;\r\n                }\r\n            },\r\n            validateUnitPrice() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editUnitPrice;\r\n                data.unitPrice = val;\r\n                data.editType = 1;\r\n            },\r\n            validateOtherMoney(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOtherMoney;\r\n                if (!testNumber(val)) {\r\n                    this.errorTips('请输入数字！');\r\n                }\r\n                data.otherFee = val;\r\n                data.editType = 1;\r\n                let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                    data.paidMoney = paidMoney.toFixed(2);\r\n                    data.unitPrice = (data.paidMoney/data.oilAmount).toFixed(4);\r\n                // debugger)\r\n            },\r\n            validateOilImportCategory(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOilCategory;\r\n                data.oilImportCategory = val;\r\n                data.editType = 1;\r\n            },\r\n            // validateOtherMoney(){\r\n            //     let data = this.tbAccount.data[this.editIndex];\r\n            //     let val = this.editOtherMoney;\r\n            //     data.otherMoney = val;\r\n            //     data.editType = 1;\r\n            //     debugger\r\n            // },\r\n            setremark(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editremark;\r\n                data.remark = val;\r\n                data.editType = 1;\r\n            },\r\n            setOilUseBody(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOilUseBody;\r\n                data.oilUseBody = val;\r\n                data.editType = 1;\r\n            },\r\n            setMyStyle(length){\r\n                this.myStyle=[];\r\n                for(var i=0;i<length;i++){\r\n                    this.myStyle.push({\r\n                        coalUseBody:'myspan',\r\n                        feeStartDate:'myspan',\r\n                        oilAmount:'myspan',\r\n                        taxTicketMoney:'myspan',\r\n                        remark:'myspan',\r\n                    });\r\n                }\r\n            },\r\n            //span点击事件将span换成输入框并且获取焦点\r\n            selectCall(row,index,columns,str){\r\n                this.editFeeStartDate = row.feeStartDate;\r\n                this.editOilUseBody = row.oilUseBody;\r\n                this.editOilAmount = row.oilAmount == null || row.oilAmount===0?null:row.oilAmount;\r\n                this.editOilType = row.oilImportType;\r\n                this.editOilCategory = row.oilImportCategory;\r\n                this.editTicketMoney = row.ticketMoney;\r\n                this.editTaxTicketMoney = row.taxTicketMoney;\r\n                this.editTaxRate = row.taxRateShow;\r\n                this.editOtherMoney = row.otherFee;\r\n                this.editremark = row.remark;\r\n                this.editIndex = index;\r\n                this.columnsIndex = columns;\r\n                let a=this;\r\n                setTimeout(function () {\r\n                    a.$refs[str+index+columns].focus();\r\n                },200);\r\n\r\n            },\r\n            //跳转到下一格\r\n            nextCell(data){\r\n                let index = data.editIndex;\r\n                let columns = data.columnsIndex;\r\n                let row = '';\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                }else if(index > -1 && columns === 5){\r\n                    //当跳转的最后一行最后一格的时候\r\n                    if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n                        index = 0;\r\n                    }else{\r\n                        index ++;\r\n                    }\r\n                    columns = 1;\r\n                }else{\r\n                    columns += 1;\r\n                }\r\n                data.editIndex = index;\r\n                data.columnsIndex = columns;\r\n                row = data.tbAccount.data[index];\r\n                if(row){\r\n                    data.editFeeStartDate = row.feeStartDate;\r\n                    data.editOilUseBody = row.oilUseBody;\r\n                    data.editOilAmount = row.oilAmount == null || row.oilAmount===0?null:row.oilAmount;\r\n                    data.editOilType = row.oilImportType;\r\n                    data.editOilCategory = row.oilImportCategory;\r\n                    data.editTicketMoney = row.ticketMoney;\r\n                    data.editTaxTicketMoney = row.taxTicketMoney;\r\n                    data.editTaxRate = row.taxRateShow;\r\n                    data.editOtherMoney = row.otherFee;\r\n                    data.editremark = row.remark;\r\n\r\n                }\r\n                setTimeout(function () {\r\n                    data.$refs[data.enterOperate(columns).str+index+columns].focus();\r\n                },200);\r\n            },\r\n            //根据列号返回对应的列名\r\n            enterOperate(number){\r\n                let str = '';\r\n                let data = null;\r\n                switch (number) {\r\n                    case 1:\r\n                        str = 'oilUseBody';\r\n                        data = this.editOilUseBody;\r\n                        break;\r\n                    case 2:\r\n                        str = 'feeStartDate';\r\n                        data = this.editFeeStartDate;\r\n                        break;\r\n                    case 3:\r\n                        str = 'oilImportType'\r\n                        data = this.editOilType;\r\n                        break;\r\n                    case 4:\r\n                        str = 'oilAmount';\r\n                        data = this.editOilAmount;\r\n                        break;\r\n                    case 5:\r\n                        str = 'ticketMoney';\r\n                        data = this.editTicketMoney;\r\n                        break;\r\n                    case 6:\r\n                        str = 'taxTicketMoney';\r\n                        data = this.editTaxTicketMoney;\r\n                        break;\r\n                    case 7:\r\n                        str = 'taxRateShow';\r\n                        data = this.editTaxRate;\r\n                        break;\r\n                    case 8:\r\n                        str = 'otherFee';\r\n                        data = this.editOtherMoney;\r\n                        break;\r\n                    case 9:\r\n                        str = 'oilImportCategory';\r\n                        data = this.editOilCategory;\r\n                        break;\r\n                    case 10:\r\n                        str = 'remark';\r\n                        data = this.editremark;\r\n                        break;\r\n                }\r\n                return {str:str,data:data};\r\n            },\r\n            pred(){\r\n                var lett = this;\r\n                let index = lett.editIndex;\r\n                let columns = lett.columnsIndex;\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                    lett.editIndex = index;\r\n                    lett.columnsIndex = columns;\r\n                    setTimeout(function () {\r\n                        lett.$refs[lett.enterOperate(columns).str+index+columns].focus();\r\n                    },200);\r\n                }else{\r\n                    lett.validate()\r\n                    lett.setremark()\r\n                    lett.nextCell(lett)\r\n                }\r\n            },\r\n            ellipsis (value) {\r\n                if (!value) return ''\r\n                if (value.length > 100) {\r\n                    return value.slice(0,100) + '...'\r\n                }\r\n                return value\r\n            },\r\n            handleUploadSuccess() {\r\n\r\n            },\r\n            handleFormatError(file) {\r\n                this.errorTips(\r\n                    file.name + \" 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件\"\r\n                );\r\n            },\r\n            handleProgress(event, file) {\r\n                this.$Message.info({\r\n                    content: file.name + \" 正在上传。\",\r\n                });\r\n            },\r\n            // 导入\r\n            onExcelUpload(file) {\r\n                if (!file) {\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: '请选择要上传的文件！',\r\n                        duration: 10\r\n                    });\r\n                    return\r\n                }\r\n                let fileName = file.name.lastIndexOf(\".\");//取到文件名开始到最后一个点的长度\r\n                let fileNameLength = file.name.length;//取到文件名长度\r\n                let fileFormat = file.name.substring(fileName + 1, fileNameLength);//截\r\n                if('xls' != fileFormat && 'xlsx' != fileFormat){\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: file.name + ' 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件',\r\n                        duration: 10\r\n                    });\r\n                    return;\r\n                }\r\n                let param = {}\r\n                let excel = {file: file}\r\n                let that = this\r\n                that.spinShow = true\r\n                axios.request({\r\n                    url: '/business/oil/account/import',\r\n                    method: 'post',\r\n                    data: Object.assign({}, param, excel)\r\n                }).then((res) => {\r\n                    that.spinShow = false\r\n                    if (res.data.str) {\r\n                        that.$Message.info({\r\n                            content: res.data.str,\r\n                            duration: 0,\r\n                            closable: true\r\n                        });\r\n                        that.show = false;\r\n                    }\r\n                    this.getAccountMessages();\r\n                }).catch(err => {\r\n                    that.spinShow = false\r\n                    that.show = false;\r\n                    console.log(err);\r\n                });\r\n                return false\r\n            },\r\n\r\n            // 导入模板下载\r\n            loadTemplate() {\r\n                let req = {\r\n                    url : \"/business/oil/account/template/load\",\r\n                    method : \"get\",\r\n                    responseType: 'blob',\r\n                };\r\n                axios.file(req)\r\n                    .then(res => {\r\n                        this.spinShow = false;\r\n                        const content = res;\r\n                        const blob = new Blob([content]);\r\n                        const fileName = \"用油台账导入模板.xlsx\";\r\n                        if (\"download\" in document.createElement(\"a\")) {\r\n                            // 非IE下载\r\n                            const elink = document.createElement(\"a\");\r\n                            elink.download = fileName;\r\n                            elink.style.display = \"none\";\r\n                            elink.href = URL.createObjectURL(blob);\r\n                            document.body.appendChild(elink);\r\n                            elink.click();\r\n                            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n                            document.body.removeChild(elink);\r\n                        } else {\r\n                            // IE10+下载\r\n                            navigator.msSaveBlob(blob, fileName);\r\n                        }\r\n                    });\r\n            }\r\n        },\r\n        mounted() {\r\n            this.version = indexData.version;\r\n            this.tbAccount.columns = this.tbAccount.tailColumn;\r\n            this.oilTypes = blist1(\"oilType\");\r\n            console.log(this.oilTypes, \"this.oilTypes\");\r\n            // debugger\r\n            this.oilCategorys = blist1(\"oilCategory\");\r\n            this.accountObj.oilCategory = this.oilCategorys[0].typeCode;\r\n            this.accountObj.oilType = this.oilTypes[0].typeCode;\r\n            let that = this\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                    that.getUserData();\r\n                });\r\n            });\r\n        }\r\n    }\r\n</script>\r\n"], "sourceRoot": "src/view/account"}]}