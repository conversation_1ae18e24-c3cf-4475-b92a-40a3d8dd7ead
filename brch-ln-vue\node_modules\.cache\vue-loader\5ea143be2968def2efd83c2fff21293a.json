{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\listAmmeter.vue?vue&type=style&index=0&id=2e85c33a&lang=less&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\listAmmeter.vue", "mtime": 1754285403019}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749178874080}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCnRkLnRkLWlkIHsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgY29sb3I6IGdyZWVuOwogICAgY3Vyc29yOiBwb2ludGVyOwp9Ci5ub2FjY291bnQgLmZpbHRlci1kaXZpZGVyIHsKICAgIG1hcmdpbjogMHB4OwogICAgdGV4dC1hbGlnbjogY2VudGVyOwp9Ci5ub2FjY291bnQgLmhlYWRlci1iYXItc2hvdyB7CiAgICBtYXgtaGVpZ2h0OiAzMDBweDsKICAgIC8qcGFkZGluZy10b3A6IDE0cHg7Ki8KICAgIG92ZXJmbG93OiBpbmhlcml0OwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOGVhZWM7Cn0KLm5vYWNjb3VudCAuaGVhZGVyLWJhci1oaWRlIHsKICAgIG1heC1oZWlnaHQ6IDA7CiAgICBwYWRkaW5nLXRvcDogMDsKICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICBib3JkZXItYm90dG9tOiAwOwp9Ci5ub2FjY291bnQgLnJvd3sKICAgIGhlaWdodDozMHB4OwogICAgbWFyZ2luLWJvdHRvbTogLTUwcHg7Cn0KLmZvcm0tbGluZS1oZWlnaHR7CiAgICBtYXJnaW4tYm90dG9tOjEwcHg7Cn0K"}, {"version": 3, "sources": ["listAmmeter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw2CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "listAmmeter.vue", "sourceRoot": "src/view/basedata/ammeter", "sourcesContent": ["<template>\r\n  <!-- *****电表管理  <AUTHOR> -->\r\n    <div>\r\n        <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n<!--        <add-ammeter-page ref=\"addAmmeterPage\"></add-ammeter-page>-->\r\n        <view-ammeter-page ref=\"viewAmmeterPage\"></view-ammeter-page>\r\n        <view-station-page ref=\"viewStationPage\"></view-station-page>\r\n        <view-quota-page ref=\"viewQuotaPage\" ></view-quota-page>\r\n        <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n        <div class=\"noaccount\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"formInline\" :model=\"queryParams\" >\r\n                    <Row>\r\n                        <Col span=\"5\" v-if=\"configVersion=='ln'|| configVersion=='LN'\">\r\n                            <FormItem label=\"供电局电表编号：\" prop=\"supplybureauammetercode\" :label-width=\"110\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.supplybureauammetercode\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\" v-else>\r\n                            <FormItem label=\"电表编号：\" prop=\"ammetername\" :label-width=\"110\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.ammetername\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"项目名称：\" prop=\"projectname\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.projectname\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"电表类型：\" prop=\"ammetertype\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-select clearable v-model=\"queryParams.ammetertype\"\r\n                                           category=\"ammeterType\" style=\"width:30vm\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"7\">\r\n                            <FormItem label=\"用电类型：\" prop=\"classifications\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <Cascader clearable :data=\"classificationData\" :change-on-select=\"true\" v-model=\"classifications\"></Cascader>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" :label-width=\"110\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" :label-width=\"100\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"queryParams.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" :label-width=\"100\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.country\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n<!--                        <Col span=\"5\">-->\r\n<!--                            <FormItem label=\"所属分局或支局\" prop=\"substation\" class=\"form-line-height\">-->\r\n<!--                                <cl-input v-model=\"queryParams.substation\"></cl-input>-->\r\n<!--                            </FormItem>-->\r\n<!--                        </Col>-->\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"单据状态：\" prop=\"billStatus\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-select clearable v-model=\"queryParams.billStatus\"\r\n                                           category=\"basicBillStatus\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n<!--                        <Col span=\"5\">-->\r\n<!--                            <FormItem label=\"管理负责人\" prop=\"ammetermanager\">-->\r\n<!--                                <cl-input v-model=\"queryParams.ammetermanager\"></cl-input>-->\r\n<!--                            </FormItem>-->\r\n<!--                        </Col>-->\r\n                        <Col span=\"4\">\r\n                            <FormItem label=\"电价性质：\" prop=\"electrovalencenature\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-select clearable v-model=\"queryParams.electrovalencenature\"\r\n                                           category=\"electrovalenceNature\" style=\"width:30vm\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"3\">\r\n                          <FormItem label=\"状态：\" prop=\"status\" :label-width=\"100\" class=\"form-line-height\">\r\n                            <cl-select v-model=\"queryParams.status\"\r\n                                       category=\"status\" style=\"width:30vm\"\r\n                                       labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                          </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"对外结算类型：\" prop=\"directsupplyflag\" :label-width=\"110\" class=\"form-line-height\">\r\n                                <cl-select clearable v-model=\"queryParams.directsupplyflag\"\r\n                                           category=\"directSupplyFlag\" style=\"width:30vm\"\r\n                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"局站名称：\" prop=\"stationName\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.stationName\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"5\">\r\n                            <FormItem label=\"资源局站id：\" prop=\"resstationcode\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"queryParams.resstationcode\"></cl-input>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"4\">\r\n                          <FormItem label=\"局站编码：\" prop=\"stationcode\" :label-width=\"100\" class=\"form-line-height\">\r\n                            <cl-input v-model=\"queryParams.stationcode\"></cl-input>\r\n                          </FormItem>\r\n                        </Col>\r\n                        <Col span=\"3\">\r\n                            <FormItem label=\"是否实体：\" prop=\"isentityammeter\" :label-width=\"100\" class=\"form-line-height\">\r\n                                <Select v-model=\"queryParams.isentityammeter\">\r\n                                <Option v-for=\"item in isentityammeters\" :value=\"item.typeCode\"\r\n                                        :key=\"item.typeCode\">{{item.typeName}}\r\n                                </Option>\r\n                               </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                  <Row>\r\n                    <Col span=\"5\">\r\n                      <FormItem label=\"5GR站址编码:\" :label-width=\"100\" class=\"form-line-height\">\r\n                        <cl-input v-model=\"queryParams.stationcode5gr\"></cl-input>\r\n                      </FormItem>\r\n                    </Col>\r\n                    <Col span=\"5\">\r\n                      <FormItem label=\"5GR站址名称:\" :label-width=\"100\" class=\"form-line-height\">\r\n                        <cl-input v-model=\"queryParams.stationname5gr\"></cl-input>\r\n                      </FormItem>\r\n                    </Col>\r\n                    <Col span=\"5\">\r\n                      <div class=\"form-line-height\">\r\n                        <Button style=\"margin-left: 5px;width:69px;\" type=\"success\" :disabled=\"isDisable\"  icon=\"ios-search\" @click=\"_onSearchHandle()\" >搜索 </Button>\r\n                        <Button style=\"margin-left: 5px;width:69px;\" type=\"info\" icon=\"ios-redo\" @click=\"_onResetHandle\" >重置</Button>\r\n                      </div>\r\n                    </Col>\r\n                  </Row>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <cl-table ref=\"ammeterTable\"\r\n                  :searchLayout=\"ammeter.filter\"\r\n                  :query-params=\"queryParams\"\r\n                  :columns=\"ammeter.columns\"\r\n                  :data=\"ammeter.data\"\r\n                  :loading=\"ammeter.loading\"\r\n                  select-enabled\r\n                  select-multiple\r\n                  @on-selection-change=\"handleSelectRow\"\r\n                  :total=\"ammeter.total\"\r\n                  :pageSize=\"ammeter.pageSize\"\r\n                  @on-query=\"query\"\r\n                  :searchable=\"false\"\r\n                  :exportable=\"false\">\r\n            <div slot=\"buttons\">\r\n                <!-- <Button type=\"primary\" @click=\"applyW\">加入白名单</Button> -->\r\n                <Button type=\"primary\" @click=\"addAmmeter\">添加</Button>\r\n                <Button type=\"success\" @click=\"editAmmeter\">修改</Button>\r\n                <Button type=\"warning\" @click=\"changeAmmeter\">换表</Button>\r\n                <Button type=\"error\" @click=\"removeAmmeter\">删除</Button>\r\n                <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n                    <Button type='default' style=\"margin-left: 5px\" >导出\r\n                        <Icon type='ios-arrow-down'></Icon>\r\n                    </Button>\r\n                    <DropdownMenu slot='list'>\r\n                        <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                        <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n                    </DropdownMenu>\r\n                </Dropdown>\r\n            </div>\r\n        </cl-table>\r\n        <cl-wf-btn ref=\"clwfbtn\" :isStart=\"true\" :params=\"workFlowParams\" @on-ok=\"doWorkFlow\" v-show=\"false\"></cl-wf-btn>\r\n        <!-- 查看流程 -->\r\n        <Modal v-model=\"showWorkFlow\" title=\"电表流程及审批意见跟踪表\" :width=\"800\">\r\n            <WorkFlowInfoComponet :wfHisParams=\"hisParams\" v-if=\"showWorkFlow\"></WorkFlowInfoComponet>\r\n        </Modal>\r\n        <!-- 加入白名单 -->\r\n        <Modal v-model=\"whiteList\" title=\"加入白名单\" @on-ok=\"submitWhiteList\" :width=\"800\">\r\n\r\n            <Form :model=\"addWhiteList\" ref=\"addWhiteList\" :rules=\"ruleValidate\" :label-width=\"80\"\r\n                class=\"margin-right-width\">\r\n                <Row>\r\n                    <Col span=\"12\">\r\n                        <FormItem label=\"白名单类型：\" :label-width=\"120\" prop=\"whitelistType\">\r\n\r\n                        <Select\r\n                                ref=\"selects\"\r\n                                :multiple=\"true\"\r\n                                :clearable=\"true\"\r\n                                v-model=\"addWhiteList.whitelistType\">\r\n                                <Option value=\"1\">一站多表</Option>\r\n                                <Option value=\"2\">一表多站</Option>\r\n                                <Option value=\"3\">单价</Option>\r\n                        </Select>\r\n                    </FormItem>\r\n                    </Col>\r\n                </Row>\r\n                <Row>\r\n                    <Col span=\"24\">\r\n                        <FormItem label=\"申请理由：\" :label-width=\"120\" prop=\"applyReason\">\r\n                            <cl-input type=\"textarea\" :rows=\"3\" v-model=\"addWhiteList.applyReason\"></cl-input>\r\n                            <!-- <label v-if=\"oldData.memo != null &&oldData.memo != ammeter.memo\"\r\n                                    style=\"color: red;\">历史数据：{{oldData.memo}}</label> -->\r\n                        </FormItem>\r\n                    </Col>\r\n                </Row>\r\n                <Row style=\"margin-left: 20.8px;\">\r\n                    <Col span=\"24\" style=\"position: relative;\">\r\n                        <!-- <cl-form v-model=\"attach.fileForm\" :label-width=\"120\" :layout=\"attach.formLayout\"></cl-form> -->\r\n                        <attach-file :param=\"fileParam\" :attachData=\"attachData\"\r\n                                         v-on:setAttachData=\"setAttachData\"/>\r\n                        <span style=\"position: absolute; top: 28px; left: 417px;\">支持pdf/word/jpg\\png文件上传</span>\r\n                    </Col>\r\n                </Row>\r\n            </Form>\r\n        </Modal>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    import { whiteList, whiteInsert } from '@/api/account';\r\n    import {selectChangeAmmeter,listAmmeter, removeAmmeter,checkAcountByUpdate,getUserByUserRole,getCountryByUserId,getUserdata,getCountrysdata,getClassification,checkStartFlow} from '@/api/basedata/ammeter.js'\r\n    import {isInTodoList}from\"@/api/alertcontrol/alertcontrol\";\r\n    import {blist,btext} from \"@/libs/tools\";\r\n    import viewAmmeterPage from './viewAmmeter.vue'\r\n    import viewQuotaPage from '@/view/basedata/quota/viewQuota.vue';\r\n    import viewStationPage from '@/view/basedata/station/viewStation.vue'\r\n    import countryModal from \"./countryModal\";\r\n    import ProcessInfo from '@/view/basic/system/workflow/process-info';\r\n    import WorkFlowInfoComponet from '@/view/basic/system/workflow/workFlowInfoComponet'\r\n\r\n    import attachFile from \"@/view/basedata/whitelist/attachFile.vue\";\r\n    import { attchList, removeAttach } from '@/api/basedata/ammeter.js'\r\n    import axios from '@/libs/api.request'\r\n\r\n    import excel from '@/libs/excel'\r\n\r\n    import {mapMutations} from \"vuex\";\r\n    import routers from '@/router/routers';\r\n    import {getHomeRoute} from '@/libs/util';\r\n    export default {\r\n        name: 'ammeter',\r\n        components: {\r\n            ProcessInfo,\r\n            WorkFlowInfoComponet,\r\n            // addAmmeterPage,\r\n            // editAmmeterPage,\r\n            viewAmmeterPage,\r\n            viewStationPage,\r\n            viewQuotaPage,\r\n            countryModal,\r\n            attachFile\r\n        },\r\n\r\n        data() {\r\n            //状态\r\n            let renderStatus = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.status) {\r\n                    if (item.typeCode == params.row.status) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //用电类型\r\n            let renderElectroType = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.electroType) {\r\n                    if (item.typeCode == params.row.electrotype) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //电表类型\r\n            let renderAmmeterType = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.ammeterType) {\r\n                    if (item.typeCode == params.row.ammetertype) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //用电性质\r\n            let renderElectroNature = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.electroNaure) {\r\n                    if (item.typeCode == params.row.electronature) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //付费方式\r\n            let renderPayType = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.payType) {\r\n                    if (item.typeCode == params.row.paytype) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //电价性质\r\n            let renderElectrovalenceNature = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.electrovalenceNature) {\r\n                    if (item.typeCode == params.row.electrovalencenature) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //单据状态\r\n            let renderBillStatus = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.billStatus) {\r\n                    if (item.typeCode == params.row.billStatus) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //产权归属\r\n            let renderProperty = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.property) {\r\n                    if (item.typeCode == params.row.property) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //对外结算类型\r\n            let renderDirectsupplyflag = (h, params) => {\r\n                let value = \"\";\r\n                for (let item of this.directsupplyFlag) {\r\n                    if (item.typeCode == params.row.directsupplyflag) {\r\n                        value = item.typeName;\r\n                        break;\r\n                    }\r\n                }\r\n                return h(\"div\", value);\r\n            };\r\n            //查看详情\r\n            let renterViewAmmeter = (h, params) => {\r\n                let column = params.column.key;\r\n                return h(\"div\", [h(\"u\", {\r\n                    style:{display:\"inline\"},\r\n                    on: {\r\n                        click: () => {\r\n                            this.viewAmmeter(params.row.id);\r\n                        }\r\n                    }\r\n                }, params.row[column])]);\r\n            };\r\n            //查看局站详情\r\n            let renterViewStation = (h, params) => {\r\n                let column = params.column.key;\r\n                return h(\"div\", [h(\"u\", {\r\n                    on: {\r\n                        click: () => {\r\n                            this.viewStation(params.row.stationcode);\r\n                        }\r\n                    }\r\n                }, params.row[column])]);\r\n            };\r\n          //资源局站id\r\n          let termnameFormat = (h, params) => {\r\n            if (!params.row.resstationcode) {\r\n              // 返回红色文字提示：请维护\r\n              return h('div', {\r\n                style: {\r\n                  color: '#f00'\r\n                },\r\n                on: {\r\n                  click: () => {\r\n                    this.$Message.info(\"在修改电表页面，关联局站信息处维护\");\r\n                  }\r\n                }\r\n              }, '请维护')\r\n            } else {\r\n              return h('div', params.row.resstationcode)\r\n            }\r\n\r\n          };\r\n            let renderW = (h, params) => {\r\n                let that = this;\r\n                let text, type = \"\";\r\n                let row = params.row;\r\n                if (row.billStatus != 0 && row.billStatus != 3 && row.processinstId != null) {\r\n                    text = \"查看\";\r\n                    type = \"success\";\r\n                } else if (params.row.billStatus == 0 || params.row.billStatus == 3) {\r\n                    text = \"提交\";\r\n                    type = \"primary\";\r\n                }\r\n                if(type == \"\"){\r\n                    return h(\"div\", {}, text);\r\n                }\r\n                return h(\"Button\",\r\n                {\r\n                    props: {\r\n                        type: type, size: \"small\"\r\n                    },\r\n                    on: {\r\n                        click() {\r\n                            if (row.billStatus!=0 && row.billStatus != 3 && row.processinstId != null) {\r\n                                that.showFlow(params.row, params.row.processinstId);\r\n                            } else if (params.row.billStatus == 0 || params.row.billStatus == 3) {\r\n                                that.loading=true;\r\n                                that.startFlow(params.row);\r\n                            }\r\n                        }\r\n                    }\r\n                },\r\n                text\r\n                );\r\n            };\r\n            let renderQuota = (h, params) => {\r\n                let that = this;\r\n                return h(\"Button\", {\r\n                    props: {\r\n                        type: \"success\", size: \"small\"\r\n                    },attrs: {\r\n                        disabled: params.row.quotaId?false:true\r\n                    },style: {\r\n                        opacity:params.row.quotaId?1:0.4\r\n                    }, on: {\r\n                        click() {\r\n                            that.viewQuota(params.row.quotaId);\r\n                        }\r\n                    }\r\n                }, \"查看\");\r\n            };\r\n            const valisheredepartname = (rule, value, callback) => {\r\n                //console.log(rule, value, \"rule, value5555555555555555\");\r\n                if (value == null || value == '') {\r\n                    callback(new Error('不能为空'))\r\n                } else {\r\n                    callback()\r\n                }\r\n            };\r\n            return {\r\n                multiFiles: null,\r\n                removeIds: [],\r\n                attachData: [],\r\n                fileParam: {\r\n                    busiId: \"\",\r\n                    busiAlias: \"附件(协议管理)\",\r\n                    categoryCode: \"file\",\r\n                    areaCode: \"ln\"\r\n                },\r\n                ruleValidate: {\r\n                    applyReason: [\r\n                        {required: true, validator: valisheredepartname, trigger: 'change, blur'}\r\n                    ],\r\n                    whitelistType: [\r\n                        {required: true, validator: valisheredepartname, trigger: 'change, blur'}\r\n                    ]\r\n                },\r\n                addWhiteList: {\r\n                    whitelistType: \"\",\r\n                    applyReason: \"\",\r\n                    id: \"\"\r\n                },\r\n                attach: {\r\n                    fileForm: {\r\n                        file: null\r\n                    },\r\n                    formLayout: [\r\n                        {\r\n                            label: '上传附件',\r\n                            prop: 'file',\r\n                            formItemType: 'file',\r\n                            width: 300,\r\n                            format: this.format\r\n                        }\r\n                    ],\r\n                    loading: false,\r\n                    columns: [],\r\n                    data:[],\r\n                },\r\n                whiteList: false,\r\n                loading:false,\r\n                showWorkFlow: false,\r\n                configVersion:null,//版本\r\n                isDisable:false,\r\n                filterColl: true,//搜索面板展开\r\n                isAdmin:false,\r\n                company:null,//用户默认公司\r\n                country:null,//用户默认所属部门\r\n                countryName:null,//用户默认所属部门\r\n\r\n                classificationData:[],//用电类型\r\n                classifications:[],//用电类型\r\n\r\n                demoList: [],\r\n                isentityammeter:{},\r\n                isentityammeters:[],\r\n                status:[],//状态\r\n                billStatus:[],//单据状态\r\n                electroType:[],//用电类型\r\n                ammeterType:[],//电表类型\r\n                electroNature:[],//用电性质\r\n                payType:[],//付费方式\r\n                electrovalenceNature:[],//电价性质\r\n                directsupplyFlag:[],//对外结算类型\r\n                property:[],//产权归属\r\n                queryParamsList: {\r\n                projectname: '',//项目名称\r\n                meterCode: '',//电表编号\r\n                stationName: '',//局站名称\r\n                city: '',//地市\r\n                district: '',//区县\r\n                status: '1',//状态\r\n                company: '',\r\n                countryName: '',\r\n                bi11statusName: '',\r\n                },\r\n                queryParams:{country:null,company:null,countryName:null,resstationcode:null, stationcode5gr: null, stationname5gr: null},\r\n                companies:[],\r\n                departments:[],\r\n                multipleSelectionRow: [],\r\n\r\n                workFlowParams: {},\r\n                hisParams: {},\r\n                exportColumns:[\r\n                    {title: '电表编号',key: 'ammetername'},\r\n                    {title: '项目名称',key: 'projectname'},\r\n                    {title: '关联局站名',key: 'stationName'},\r\n                    {title: '所属分公司',key: 'companyName'},\r\n                    {title: '所属部门', key: 'countryName'},\r\n                    {title: '状态',key: 'statusStr'},\r\n                    {title: '单据状态',key: 'billStatusStr'},\r\n                    {title: '用电类型',key: 'electrotypename'},\r\n                    {title: '对外结算类型',key: 'directsupplyflagStr'},\r\n                    {title: '付费方式',key: 'paytypeStr'},\r\n                    {title: '电价性质',key: 'electrovalencenatureStr'},\r\n                    {title: '电表类型',key: 'ammetertypeStr'},\r\n                    {title: '产权归属',key: 'propertyStr'},\r\n                    // {title: '支局/分局',key: 'substation'},\r\n                    {title: '管理负责人',key: 'ammetermanager'},\r\n                    {title: '创建时间',key: 'createTime'},\r\n                    {title: '供电局电表户号或编号',key: 'supplybureauammetercode'},\r\n                    {title: '资源局站id：',key: 'termname'},\r\n                    {title: '倍率：',key: 'magnification'},\r\n                    {title: '单价：',key: 'price'},\r\n                    {title: '分割比例：',key: 'percent'},\r\n                ],\r\n                export: {\r\n                    run: false,//是否正在执行导出\r\n                    data: \"\",//导出数据\r\n                    totalPage: 0,//一共多少页\r\n                    currentPage: 0,//当前多少页\r\n                    percent: 0,\r\n                    size: 200000\r\n                },\r\n                ammeter: {\r\n                    loading: false,\r\n                    columns: [\r\n                        {\r\n                            title: '电表编号',\r\n                            key: 'ammetername',\r\n                            align: 'center',\r\n                            className: \"td-id\",\r\n                            render: renterViewAmmeter,\r\n                            minWidth: 120,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '项目名称',\r\n                            key: 'projectname',\r\n                            align: 'center',\r\n                            className: \"td-id\",\r\n                            render: renterViewAmmeter,\r\n                            minWidth: 120,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '关联局站名',\r\n                            key: 'stationName',\r\n                            className: \"td-id\",\r\n                            align: 'center',\r\n                            render: renterViewStation,\r\n                            minWidth: 100,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                          title: '资源局站id',\r\n                          key: 'termname',\r\n                          className: \"td-id\",\r\n                          align: 'center',\r\n                          render: termnameFormat,\r\n                          minWidth: 100,\r\n                          maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '所属分公司',\r\n                            key: 'companyName',\r\n                            align: 'center',\r\n                            minWidth: 100,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '所属部门',\r\n                            key: 'countryName',\r\n                            align: 'center',\r\n                            minWidth: 80,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '状态',\r\n                            key: 'status',\r\n                            render: renderStatus,\r\n                            align: 'center',\r\n                            minWidth: 55,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '单据状态',\r\n                            align: 'center',\r\n                            key: 'billStatus',\r\n                            render: renderBillStatus,\r\n                            minWidth: 80,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '用电类型',\r\n                            key: 'electrotypename',\r\n                            align: 'center',\r\n                            minWidth: 90,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '对外结算类型',\r\n                            key: 'directsupplyflag',\r\n                            render: renderDirectsupplyflag,\r\n                            align: 'center',\r\n                            minWidth: 80,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '付费方式',\r\n                            key: 'paytype',\r\n                            render: renderPayType,\r\n                            align: 'center',\r\n                            minWidth: 70,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '电价性质',\r\n                            key: 'electrovalencenature',\r\n                            render: renderElectrovalenceNature,\r\n                            align: 'center',\r\n                            minWidth: 70,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '电表类型',\r\n                            key: 'ammetertype',\r\n                            render: renderAmmeterType,\r\n                            align: 'center',\r\n                            minWidth: 70,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '产权归属',\r\n                            key: 'property',\r\n                            render: renderProperty,\r\n                            align: 'center',\r\n                            minWidth: 70,\r\n                            maxWidth:200\r\n                        },\r\n                        // {\r\n                        //     title: '支局/分局',\r\n                        //     key: 'substation',\r\n                        //     align: 'center',\r\n                        //     minWidth: 80,\r\n                        //     maxWidth:200\r\n                        // },\r\n                        {\r\n                            title: '管理负责人',\r\n                            key: 'ammetermanager',\r\n                            align: 'center',\r\n                            minWidth: 90,\r\n                            maxWidth:200\r\n                        },\r\n                        {\r\n                            title: '创建时间',\r\n                            key: 'createTime',\r\n                            align: 'center',\r\n                            minWidth: 110,\r\n                            maxWidth:200\r\n                        },\r\n                      {\r\n                        title: '上传附件',\r\n                        align: 'center',\r\n                        minWidth: 70,\r\n                        maxWidth: 200,\r\n                        render: (h, params) => {\r\n                          const row = params.row;\r\n                          const text = row.isAttach === 0 ? '未上传' : '已上传';\r\n                          return h('span', {}, text);\r\n                        }\r\n                      },\r\n                        {\r\n                            title: \"流程\",\r\n                            fixed: 'right',\r\n                            key: \"action\",\r\n                            minWidth: 60,\r\n                            maxWidth:200,\r\n                            align: 'center',\r\n                            render: renderW\r\n                        },\r\n                        {\r\n                            title: \"查看定额\",\r\n                            fixed: 'right',\r\n                            key: \"action\",\r\n                            minWidth: 65,\r\n                            maxWidth:200,\r\n                            align: 'center',\r\n                            render: renderQuota\r\n                        }],\r\n                    data: [],\r\n                    pageSize:10\r\n                }\r\n            }\r\n        },\r\n        methods: {\r\n            ...mapMutations([\"closeTag\", \"closeTagByName\"]),\r\n            //验证错误弹出提示框\r\n            errorTips(str){\r\n                this.$Notice.error({\r\n                    title: '提示',\r\n                    desc: str,\r\n                    duration: 10\r\n                });\r\n            },\r\n            // typeList\r\n            submitWhiteList() {\r\n                let arr = [];\r\n                let data = this.$refs.ammeterTable.getSelection();\r\n                console.log(data[0].ammetername, \"data.ammetername\");\r\n                console.log(this.attach.fileForm.file, \"this.attach.fileForm.file\");\r\n\r\n                this.loading = true;\r\n                this.addWhiteList.whitelistType.forEach(item => {\r\n                    arr.push({\r\n                        whitelistType: item,\r\n                    });\r\n                })\r\n                let params = {\r\n                    typeList: arr,\r\n                    applyArgument: this.addWhiteList.applyReason,\r\n                    meterCode: data[0].ammetername,\r\n                    \"fj\": \"未上传附件；\",\r\n                    // typeList.whitelistType: '1',\r\n                    dwjslx: data[0].directsupplyflag == 1?'直供':data[0].directsupplyflag == 2?'转供':\"\",\r\n\r\n                    }\r\n                console.log(params, \"params\");\r\n                whiteInsert(\r\n                    params\r\n                ).then(res => {\r\n                    console.log(res, \"res666666666666\");\r\n                    this.whiteList = false;\r\n                    if(res.data.code == 500) {\r\n                    this.loading = false;\r\n                    // this.$Message.error(res.data.msg);\r\n                    }else{\r\n                    this.$Message.success(\"保存成功\");\r\n                    this.loading = false;\r\n                    this.id1 = res.data.id;\r\n                    }\r\n                })\r\n            },\r\n          removeAttach(){\r\n            removeAttach({ids:this.removeIds.join()}).then(() => {\r\n\r\n            });\r\n          },\r\n            setAttachData(data){\r\n            console.log(data, \"data555555555555555\");\r\n            this.multiFiles = data.data;\r\n            this.removeIds = data.ids;\r\n            if(this.removeIds.length!= 0 && data.type == 'remove'){\r\n              this.removeAttach();\r\n            }else{\r\n              this.upload();\r\n            }\r\n          },\r\n            upload(){\r\n            if (this.attachData.length != 0 && this.multiFiles.length != 0){\r\n\r\n              // this.$Message.info(\"提示:上传文件过大可能导致上传失败！\");\r\n              this.loading = true;\r\n              axios.request({\r\n                url: '/common/attachments/uploadMultiFile',\r\n                method: 'post',\r\n                data: this.multiFiles\r\n              }).then((res) => {\r\n                if(res.data.code != 0){\r\n                  this.loading = false;\r\n                }\r\n                let that = this;\r\n                // if(that.fileParam.busiId == \"\") {\r\n                //     this.$Message.error(\"cuowu\");\r\n                // }else {\r\n                    attchList({busiId:that.fileParam.busiId}).then(res => {\r\n                    console.log(res, \"attchList\");\r\n                    that.attachData = Object.assign([], res.data.rows);\r\n                });\r\n                // }\r\n\r\n              })\r\n            }\r\n          },\r\n          getAccountMessages() {\r\n            // this.listTb.loading = true;\r\n            whiteList({\r\n                // meterCode: this.queryParamsList.meterCode,\r\n                // projectname: this.queryParamsList.projectname,\r\n                // stationName: this.queryParamsList.stationName,\r\n                // company: this.queryParamsList.company,\r\n                // countryName: this.queryParamsList.countryName,\r\n                // status: this.queryParamsList.status,\r\n                // size: this.pageSize,\r\n                // current: this.pageNum\r\n                }).then(res => {\r\n            // this.listTb.loading = false;\r\n                console.log(res, \"queryParamsList res\");\r\n                console.log(res.data.length, \"res.data.length\");\r\n                // this.pageTotal = res.data.total;\r\n                // this.insideData = res.data.rows;\r\n            })\r\n            },\r\n            applyW() {\r\n                let data = this.$refs.ammeterTable.getSelection();\r\n                console.log(data, \"data55555555555555\");\r\n                this.fileParam.busiId = data[0].id;\r\n                if(data.length > 1) {\r\n                    this.errorTips(\"只能选择一个电表申请加入白名单\");\r\n                }else if(data.length == 0) {\r\n                    this.errorTips(\"请选择一个电表申请加入白名单\");\r\n                }else {\r\n                    // this.getAccountMessages();\r\n                    // data[0].name\r\n                    this.attachData = [];\r\n                    this.addWhiteList = {\r\n                        whitelistType: \"\",\r\n                        applyReason: \"\",\r\n                        id: \"\"\r\n                };\r\n                    this.whiteList = true;\r\n\r\n                }\r\n            },\r\n            onModalOK() {\r\n                this.$Message.error('确定')\r\n            },\r\n            onModalCancel() {\r\n                this.$Message.error('取消')\r\n            },\r\n\r\n            /*删除*/\r\n            removeAmmeter(id) {\r\n                let multipleSelection = [];\r\n                if (this.multipleSelectionRow.length > 0) {\r\n                    for(let item of this.multipleSelectionRow){\r\n                        if(item.billStatus != 0){\r\n                            this.$Message.info(\"所选数据包含非草稿数据，不能删除！\");\r\n                            return ;\r\n                        }\r\n                        multipleSelection.push(item.id);\r\n                    }\r\n                    id = multipleSelection.join(',');\r\n                    this.$Modal.confirm({\r\n                        title: '温馨提示',\r\n                        content: '<p>确认删除吗?</p>',\r\n                        onOk: () => {\r\n                            this.ammeter.loading = true;\r\n                            removeAmmeter({ids: id}).then(res => {\r\n                                this.$Message.success(\"删除成功\");\r\n                                this._onSearchHandle();\r\n                                this.ammeter.loading = false;\r\n                            });\r\n                            this.multipleSelectionRow = [];\r\n                        },\r\n                    });\r\n                } else {\r\n                    this.$Message.info(\"请至少选择一行\");\r\n                }\r\n\r\n            },\r\n\r\n            /*编辑*/\r\n            editAmmeter() {\r\n                if (this.multipleSelectionRow.length == 1) {\r\n                    let row = this.multipleSelectionRow[0];\r\n                    selectChangeAmmeter({id:row.id}).then(res => {\r\n                        //存在于代办中时，报出提示\r\n                        if (res.data.length > 0) {\r\n                            this.$Modal.warning({title: \"温馨提示\",\r\n                                content: \"该电表已经存在换表电表【电表编号：\" + res.data[0].ammetername + \"，项目名称：\" + res.data[0].projectname + \"】,不允许再修改\"\r\n                            });\r\n                        } else {\r\n                            isInTodoList(row.id, 1).then(res => {\r\n                                //存在于代办中时，报出提示\r\n                                let ownername = \"\";\r\n                                if (res.data.length > 0) {\r\n                                    for (let i = 0; i < res.data.length; i++) {\r\n                                        ownername += res.data[i].ownername + ' ';\r\n                                    }\r\n                                    this.$Modal.warning({title: \"温馨提示\",\r\n                                        content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可修改数据\"\r\n                                    });\r\n                                } else {\r\n                                    checkAcountByUpdate({id: row.id}).then(res => {\r\n                                        //修改数据前验证台账\r\n                                        if (res.data == -1) {\r\n                                            this.$Modal.warning({title: \"温馨提示\", content: \"该数据已填写台账或正在报账中，处理后才可修改数据\"});\r\n                                        } else {\r\n                                            this.closeTagByName({\r\n                                                route: getHomeRoute(routers, \"editAmmeter\"),\r\n                                            });\r\n                                            this.$router.push({\r\n                                                name: \"editAmmeter\",\r\n                                                query: {id: row.id},\r\n                                                replace: true\r\n                                            })\r\n                                        }\r\n                                    })\r\n                                }\r\n                            }).catch(err => {\r\n                                console.log(err);\r\n                            });\r\n                        }\r\n                    });\r\n                } else {\r\n                    this.$Message.info(\"请选择其中一行\");\r\n                }\r\n\r\n            },\r\n            /*换表*/\r\n            changeAmmeter() {\r\n                if (this.multipleSelectionRow.length == 1) {\r\n                    let row = this.multipleSelectionRow[0];\r\n                    // if(row.property!=2) {\r\n                        selectChangeAmmeter({id: row.id}).then(res => {\r\n                            //存在于代办中时，报出提示\r\n                            if (res.data.length > 0) {\r\n                                this.$Modal.warning({\r\n                                    title: \"温馨提示\",\r\n                                    content: \"该电表已经存在换表电表【电表编号：\" + res.data[0].ammetername + \"，项目名称：\" + res.data[0].projectname + \"】\"\r\n                                });\r\n                            } else {\r\n                                isInTodoList(row.id, 1).then(res => {\r\n                                    //存在于代办中时，报出提示\r\n                                    let ownername = \"\";\r\n                                    if (res.data.length > 0) {\r\n                                        for (let i = 0; i < res.data.length; i++) {\r\n                                            ownername += res.data[i].ownername + ' ';\r\n                                        }\r\n                                        this.$Modal.warning({\r\n                                            title: \"温馨提示\",\r\n                                            content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可修改数据\"\r\n                                        });\r\n                                    } else {\r\n                                        this.checkChangeAmmeter(row);\r\n                                    }\r\n                                });\r\n                            }\r\n                        });\r\n                    // }else{\r\n                    //     this.$Message.info(\"该表站址产权归属为铁塔，不能进行换表操作\");\r\n                    // }\r\n                } else {\r\n                    this.$Message.info(\"请选择其中一行\");\r\n                }\r\n            },\r\n            checkChangeAmmeter(row){\r\n                if(row.billStatus !=0) {\r\n                    this.$Modal.confirm({\r\n                        title: '温馨提示',\r\n                        content: '<p>换表流程结束，旧表将停用，新表启用，请确认是否换表？</p>',\r\n                        onOk: () => {\r\n                            this.closeTagByName({\r\n                                route: getHomeRoute(routers, \"changeAmmeter\"),\r\n                            });\r\n                            this.$router.push({\r\n                                name: \"changeAmmeter\",\r\n                                query: {id: row.id},\r\n                                replace: true\r\n                            })\r\n                        },\r\n                    });\r\n\r\n                }else{\r\n                    this.$Message.info(\"草稿状态不能操作换表数据\");\r\n                }\r\n            },\r\n\r\n            /*查看*/\r\n            viewAmmeter(id) {\r\n                this.$refs.viewAmmeterPage.initAmmeter(id);\r\n            },\r\n            /*查看局站*/\r\n            viewStation(id) {\r\n                this.$refs.viewStationPage.initStation(id);\r\n            },\r\n            /*查看定额*/\r\n            viewQuota(id) {\r\n                this.$refs.viewQuotaPage.initQuota(id);\r\n            },\r\n\r\n            /*添加*/\r\n            addAmmeter() {\r\n                this.$router.push({\r\n                    name: \"addAmmeter\",\r\n                    query:{},\r\n                    replace:true\r\n                })\r\n                // this.$refs.addAmmeterPage.initAmmeter();\r\n            },\r\n            selectChange(){\r\n                let that = this;\r\n                if (this.queryParams.company != undefined) {\r\n                    if(this.queryParams.company == \"-1\"){\r\n                        that.queryParams.country = -1;\r\n                        that.queryParams.countryName = null;\r\n                    }else{\r\n                        getCountryByUserId(that.queryParams.company).then(res => {\r\n                            if(res.data.departments.length != 0){\r\n                                that.queryParams.country = res.data.departments[0].id;\r\n                                that.queryParams.countryName = res.data.departments[0].name;\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            },\r\n            setElectroyType(){\r\n                let types = this.classifications;\r\n                if(types.length != 0){\r\n                    this.queryParams.electrotype = types[types.length-1];\r\n                }else{\r\n                    this.queryParams.electrotype = null;\r\n                }\r\n            },\r\n\r\n            _onResetHandle(){\r\n                this.multipleSelectionRow=[];\r\n                this.classifications = [];\r\n                this.queryParams = {type:0,company:null,country:null,countryName:null, stationcode5gr: null, stationname5gr: null};\r\n                this.queryParams.company= this.company;\r\n                this.queryParams.country= Number(this.country);\r\n                this.$refs.ammeterTable.query(this.queryParams);\r\n                this.queryParams.countryName = this.countryName;\r\n            },\r\n            _onSearchHandle(){\r\n                this.isDisable=true\r\n                setTimeout(()=>{\r\n                    this.isDisable=false   //点击一次时隔两秒后才能再次点击\r\n                },2000)\r\n                this.multipleSelectionRow=[];\r\n                this.setElectroyType();\r\n                if(this.queryParams.countryName == \"\"){\r\n                    this.queryParams.country = \"-1\";\r\n                }\r\n                this.$refs.ammeterTable.query(this.queryParams);\r\n                // this.query(this.queryParams);\r\n            },\r\n            setDisabled(){\r\n                for(let item of this.$refs.ammeterTable.insideData){\r\n                    if(item.billStatus != 0){\r\n                        item._disabled = true;//禁止选择\r\n                    }\r\n                }\r\n            },\r\n            handleSelectRow(val){\r\n                this.multipleSelectionRow = [];\r\n                val.forEach(item => {\r\n                    this.multipleSelectionRow.push(item);\r\n                });\r\n            },\r\n            startFlowSubmit(row){\r\n                let busiAlias = \"ADD_AMM\";\r\n                let busiTitle = \"新增电表(\"+row.projectname+\")审批\";\r\n                if(row.billStatus === 3){\r\n                    busiAlias = \"MODIFY_AMM\";\r\n                    busiTitle = \"修改电表(\"+row.projectname+\")审批\";\r\n                }\r\n                if(row.ischangeammeter == 1 && row.billStatus<2){\r\n                    busiAlias = \"AMM_SWITCH_AMM\";\r\n                    busiTitle = \"电表换表(\"+row.projectname+\")审批\";\r\n                }\r\n                this.workFlowParams = {\r\n                    busiId: row.id,\r\n                    busiAlias: busiAlias,\r\n                    busiTitle: busiTitle\r\n                }\r\n                let that = this;\r\n                this.$Modal.confirm({\r\n                    title: '电表提交流程',\r\n                    content: '<p>是否提交电表 (' + row.projectname + ') 到流程</p>',\r\n                    onOk: () => {\r\n                        that.loading = true;\r\n                        setTimeout(function () {\r\n                            that.$refs.clwfbtn.onClick();\r\n                        }, 300);\r\n                    },onCancel: () => {\r\n                        that.loading = false;\r\n                    }\r\n                });\r\n            },\r\n            startFlow(row) {\r\n                let that = this;\r\n                isInTodoList(row.id,1).then(res => {\r\n                    //存在于代办中时，报出提示\r\n                    let ownername = \"\";\r\n                    if (res.data.length > 0) {\r\n                        for (let i = 0; i < res.data.length; i++) {\r\n                            ownername += res.data[i].ownername + ' ';\r\n                        }\r\n                        that.$Modal.warning({title:\"温馨提示\",content: \"该数据存在于\" + ownername + \"的流程代办中，处理后才可继续提交流程\"});\r\n                        that.loading = false;\r\n                    }else if(row.billStatus == 3 || row.billStatus == 4){\r\n                        checkStartFlow({id:row.id}).then(res1 => {\r\n                            /*提交流程验证用户是否有数据需要提交*/\r\n                            that.loading = false;\r\n                            if (res1.data.id == null || res1.data.id == undefined) {\r\n                                that.$Modal.warning({title:\"温馨提示\",content: \"您没有可提交的数据\"});\r\n                            }else{\r\n                                that.startFlowSubmit(row);\r\n                            }\r\n                        });\r\n                    }else{\r\n                        that.loading = false;\r\n                        that.startFlowSubmit(row);\r\n                    }\r\n                });\r\n            },\r\n            showFlow(row, procInstId) {\r\n                this.showWorkFlow = true;\r\n                this.hisParams = {\r\n                    busiId: row.id,\r\n                    busiType: row.busiAlias,\r\n                    procInstId: procInstId\r\n                }\r\n            },\r\n            doWorkFlow(data) { //流程回调\r\n                this.loading = false;\r\n                this.$refs.ammeterTable.query();\r\n                // this.query(this.queryParams);\r\n            },\r\n            query(params) {\r\n                this.ammeter.loading = true;\r\n                listAmmeter(params).then(res => {\r\n                    this.ammeter.loading = false;\r\n                    this.ammeter.total = res.data.total\r\n                    this.ammeter.data = Object.assign([], res.data.rows)\r\n                });\r\n            },\r\n            beforeLoadData(data) {\r\n                let cols=[],keys=[]\r\n                for (let i = 0; i < this.exportColumns.length; i++) {\r\n                    cols.push(this.exportColumns[i].title)\r\n                    keys.push(this.exportColumns[i].key)\r\n                }\r\n                const params = {\r\n                  title: cols,\r\n                  key: keys,\r\n                  data: data,\r\n                  autoWidth: true,\r\n                  filename: '电表数据导出'\r\n                };\r\n                this.queryParams.pageSize = this.ammeter.pageSize;\r\n                excel.export_array_to_excel(params);\r\n                this.$Spin.hide();\r\n                return\r\n            },\r\n            exportLoading(){\r\n                this.$Spin.show({\r\n                    render: (h) => {\r\n                        return h('div', [\r\n                            h('Progress', {\r\n                                style: {\r\n                                    width: '800px'\r\n                                },\r\n                            }),\r\n                            h('div', '导出中，请勿刷新页面......')\r\n                        ])\r\n                    }\r\n                });\r\n            },\r\n            exportCsv(name) {\r\n                this.exportLoading();\r\n                this.export.run = true;\r\n                let params = this.queryParams;\r\n                if (name === 'current') {\r\n                    this.beforeLoadData(this.setValueByForEach(this.ammeter.data))\r\n                    return;\r\n                } else if (name === 'all') {\r\n                    params.pageNum = 1;\r\n                    params.pageSize = this.export.size;\r\n                }\r\n                // let req = {\r\n                //     url : \"/business/ammeterorprotocol/list\",\r\n                //     method : \"get\",\r\n                //     params : params\r\n                // };\r\n                // this.ammeter.loading = true;\r\n                // axios.request(req).then(res => {\r\n                //     this.ammeter.loading = false;\r\n                //     if (res.data) {\r\n                //         let array = res.data.rows;\r\n                //         this.beforeLoadData(this.setValueByForEach(array));\r\n                //     }\r\n                // }).catch(err => {\r\n                //     console.log(err);\r\n                // });\r\n                let req = {\r\n                    url : \"/business/ammeterorprotocol/exportMeterAll\",\r\n                    method : \"post\",\r\n                    params : params\r\n                };\r\n                axios.file(req).then(res => {\r\n                  const blob = new Blob([res])\r\n                  const fileName = '电表数据导出.xls';\r\n                  // 创建一个下载链接\r\n                  console.log('下载文件:', fileName);\r\n                  const url = URL.createObjectURL(blob);\r\n                  const a = document.createElement('a');\r\n                  a.href = url;\r\n                  a.download = fileName; // 设置下载文件名\r\n                  document.body.appendChild(a);\r\n                  a.click();\r\n\r\n                  // 清理\r\n                  setTimeout(() => {\r\n                    document.body.removeChild(a);\r\n                    URL.revokeObjectURL(url); // 释放内存\r\n                  }, 100);\r\n                  this.$Spin.hide();\r\n                })\r\n            },\r\n            setValueByForEach(array){\r\n                array.forEach(function (item) {\r\n                    item.categoryStr = btext(\"ammeterCategory\", item.category,'typeCode','typeName');\r\n                    item.packagetypeStr = btext(\"packageType\", item.packagetype,'typeCode','typeName');\r\n                    item.payperiodStr = btext(\"payPeriod\", item.payperiod,'typeCode','typeName');\r\n                    item.paytypeStr = btext(\"payType\", item.paytype,'typeCode','typeName');\r\n                    item.electronatureStr = btext(\"electroNature\", item.electronature,'typeCode','typeName');\r\n                    item.electrovalencenatureStr = btext(\"electrovalenceNature\", item.electrovalencenature,'typeCode','typeName');\r\n                    item.electrotypeStr = btext(\"electroType\", item.electrotype,'typeCode','typeName');\r\n                    item.statusStr = btext(\"status\", item.status,'typeCode','typeName');\r\n                    item.propertyStr = btext(\"property\", item.property,'typeCode','typeName');\r\n                    item.ammetertypeStr = btext(\"ammeterType\", item.ammetertype,'typeCode','typeName');\r\n                    item.stationstatusStr = btext(\"stationStatus\", item.stationstatus,'typeCode','typeName');\r\n                    item.stationtypeStr = btext(\"BUR_STAND_TYPE\", item.stationtype,'typeCode','typeName');\r\n                    item.ammeteruseStr = btext(\"ammeterUse\", item.ammeteruse,'typeCode','typeName');\r\n                    item.directsupplyflagStr = btext(\"directSupplyFlag\", item.directsupplyflag,'typeCode','typeName');\r\n                    item.billStatusStr = btext(\"basicBillStatus\", item.billStatus,'typeCode','typeName');\r\n                    item.supplybureauammetercode;\r\n                    item.magnification\r\n                });\r\n                return array;\r\n            },\r\n            //选择所属部门开始\r\n            chooseResponseCenter() {\r\n                if(this.queryParams.company == null || this.queryParams.company == \"-1\" ){\r\n                    this.$Message.info(\"请先选择分公司\");return;\r\n                }\r\n                this.$refs.countryModal.choose(this.queryParams.company);//所属部门\r\n            },\r\n            getDataFromModal(data) {\r\n                this.queryParams.country = data.id;\r\n                this.queryParams.countryName = data.name;\r\n                //选择所属部门结束\r\n            },\r\n            getUserData(){\r\n                let that = this;\r\n                getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                    let companies = that.companies;\r\n                    if(res.data.companies != null && res.data.companies.length != 0){\r\n                        if(res.data.companies[0].id != \"2600000000\"){\r\n                            companies = res.data.companies;\r\n                        }\r\n                    }\r\n                    that.company = companies[0].id;\r\n                    that.queryParams.company = companies[0].id;\r\n\r\n                    let departments = that.departments;\r\n                    if(res.data.departments != null && res.data.departments.length != 0){\r\n                        if(res.data.companies[0].id != \"2600000000\"){\r\n                            departments = res.data.departments;\r\n                        }\r\n                    }\r\n                    that.country = departments[0].id;\r\n                    that.countryName = departments[0].name;\r\n                    that.queryParams.country = Number(departments[0].id);\r\n                    that.queryParams.countryName = departments[0].name;\r\n                    this._onSearchHandle();\r\n                   // this.query({pageNum: 1,type:0,pageSize: this.ammeter.pageSize,company:this.company,country:this.country});\r\n                });\r\n            },\r\n            init(){\r\n                this.status = blist(\"status\");//状态\r\n                this.billStatus = blist(\"basicBillStatus\");//单据状态\r\n                this.ammeterType=blist(\"ammeterType\")//电表类型\r\n                this.electroType = blist(\"electroType\");//用电类型\r\n                this.electroNaure = blist(\"electroNature\");//用电性质\r\n                this.payType = blist(\"payType\");//付费方式\r\n                this.electrovalenceNature = blist(\"electrovalenceNature\");//电价性质\r\n                this.property = blist(\"property\");//产权归属\r\n                this.directsupplyFlag = blist(\"directSupplyFlag\");//对外结算类型\r\n                this.isentityammeters.push({typeCode: 0, typeName: '否'});\r\n                this.isentityammeters.push({typeCode: 1, typeName: '是'});\r\n                let that = this;\r\n                getUserByUserRole().then(res => {//根据权限获取分公司\r\n                    that.companies = res.data.companies;\r\n                    if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                        that.isAdmin = true;\r\n                    }\r\n                    getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                        that.departments = res.data;\r\n                        that.getUserData();\r\n                    });\r\n                });\r\n                getClassification().then(res => {//用电类型\r\n                    this.classificationData = res.data;\r\n                });\r\n                // this._onSearchHandle(); \r\n            }\r\n        },\r\n        mounted() {\r\n            this.init();\r\n            this.configVersion = this.$config.version;\r\n            if(this.configVersion=='ln'||this.configVersion=='LN'){\r\n                this.exportColumns.unshift(\r\n                    {title: '供电局电表编号',key: 'supplybureauammetercode'},\r\n                );\r\n                this.ammeter.columns.unshift(\r\n                    {\r\n                        title: '供电局电表编号',\r\n                        key: 'supplybureauammetercode',\r\n                        align: 'center',\r\n                        minWidth: 100,\r\n                        maxWidth:200\r\n                    }\r\n                )\r\n            }\r\n            // this.fileParam.busiId = \"666666\";\r\n        },\r\n        // watch:{\r\n        //     '$route':\"init\"\r\n        // },\r\n\r\n    }\r\n</script>\r\n\r\n<style lang=\"less\">\r\n    td.td-id {\r\n        font-weight: bold;\r\n        color: green;\r\n        cursor: pointer;\r\n    }\r\n    .noaccount .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .noaccount .header-bar-show {\r\n        max-height: 300px;\r\n        /*padding-top: 14px;*/\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .noaccount .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n    .noaccount .row{\r\n        height:30px;\r\n        margin-bottom: -50px;\r\n    }\r\n    .form-line-height{\r\n        margin-bottom:10px;\r\n    }\r\n</style>\r\n"]}]}