{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\energyview\\modifyReport.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\energyview\\modifyReport.vue", "mtime": 1754285403042}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": 1741317456427}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXM2Lm9iamVjdC5rZXlzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXM2LnJlZ2V4cC50by1zdHJpbmciOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lczYuYXJyYXkuZmluZC1pbmRleCI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20uaXRlcmFibGUiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lczYub2JqZWN0LmFzc2lnbiI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vIGltcG9ydCBlbXB0eVN0cmF0ZSBmcm9tICJAL2NvbXBvbmVudHMvZW1wdHkiOwppbXBvcnQgZGVsZWN0cyBmcm9tICJAL2NvbXBvbmVudHMvZGVsZWN0cy9pbmRleCI7IC8vIGltcG9ydCB7bWFwR2V0dGVyc30gZnJvbSAidnVleCI7Ci8vIGltcG9ydCB7ZGF0ZUZvcm1hdH0gZnJvbSAiQC91dGlsL2RhdGUiOwovLyBpbXBvcnQgeyBkb3dubG9hZEZpbGUgfSBmcm9tICJAL3V0aWwvcnVveWkiOwoKaW1wb3J0IHsgZW5lcmd5TGlzdCwgZW5lcmd5VXBkYXRlTGlzdCwgZ2V0RW5lcmd5VXBkYXRlUmVjb3JkTGlzdCwgZW5lcmd5RGF0YVVwZGF0ZUxpc3QgfSBmcm9tICJAL2FwaS9jYXJib24vZGlzY2hhcmdlL2VuZXJneSI7CmltcG9ydCBqaXR1YW5FbmVyZ3lSZXBvcnRMb2cgZnJvbSAiLi9qaXR1YW5FbmVyZ3lSZXBvcnRMb2ciOwpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogewogICAgLy8gZW1wdHlTdHJhdGUsCiAgICBkZWxlY3RzOiBkZWxlY3RzLAogICAgaml0dWFuRW5lcmd5UmVwb3J0TG9nOiBqaXR1YW5FbmVyZ3lSZXBvcnRMb2cKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwoKICAgIHJldHVybiB7CiAgICAgIG9iak5hbWU6ICfmn6XnnIvmlbDmja4nLAogICAgICB0YWJsZUxvYWRpbmc6IGZhbHNlLAogICAgICBpc1Nob3c6IGZhbHNlLAogICAgICBsaXN0OiBbXSwKICAgICAgdGFibGVPcHRpb246IHsKICAgICAgICBib3JkZXI6IGZhbHNlLAogICAgICAgIGluZGV4OiBmYWxzZSwKICAgICAgICBoZWlnaHQ6ICJhdXRvIiwKICAgICAgICBjYWxjSGVpZ2h0OiAzNSwKICAgICAgICBzdHJpcGU6IHRydWUsCiAgICAgICAgbWVudUFsaWduOiAiY2VudGVyIiwKICAgICAgICBhbGlnbjogImNlbnRlciIsCiAgICAgICAgcmVmcmVzaEJ0bjogZmFsc2UsCiAgICAgICAgc2hvd0Nsb21udUJ0bjogZmFsc2UsCiAgICAgICAgc2VhcmNoTWVudVNwYW46IDQsCiAgICAgICAgc2VhcmNoU2l6ZTogIm1pbmkiLAogICAgICAgIGNhcmQ6IHRydWUsCiAgICAgICAgYWRkQnRuOiBmYWxzZSwKICAgICAgICBlZGl0QnRuOiBmYWxzZSwKICAgICAgICBkZWxCdG46IGZhbHNlLAogICAgICAgIGNvbHVtbkJ0bjogZmFsc2UsCiAgICAgICAgc2VhcmNoQnRuOiBmYWxzZSwKICAgICAgICBlbXB0eUJ0bjogZmFsc2UsCiAgICAgICAgbWVudTogZmFsc2UsCiAgICAgICAgZGlhbG9nV2lkdGg6IDUwMCwKICAgICAgICBkaWFsb2dNZW51UG9zaXRpb246ICJjZW50ZXIiLAogICAgICAgIGRpYWxvZ0N1c3RvbUNsYXNzOiAic2luZ2xlUm93RGlhbG9nIiwKICAgICAgICBsYWJlbFdpZHRoOiAxMDAsCiAgICAgICAgY29sdW1uOiBbewogICAgICAgICAgbGFiZWw6ICLmjIfmoIciLAogICAgICAgICAgcHJvcDogImluZGljYXRvck5hbWUiLAogICAgICAgICAgb3ZlckhpZGRlbjogdHJ1ZSwKICAgICAgICAgIHdpZHRoOiA3NTAsCiAgICAgICAgICBhbGlnbjogImxlZnQiCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICLpm4blm6IiLAogICAgICAgICAgcHJvcDogImdyb3VwRGF0YSIsCiAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uIGZvcm1hdHRlcih2YWwsIHZhbHVlLCBsYWJlbCkgewogICAgICAgICAgICByZXR1cm4gX3RoaXMuZm9ybWF0RGlzcGxheURhdGEodmFsdWUpOwogICAgICAgICAgfSwKICAgICAgICAgIGNlbGw6IHRydWUsCiAgICAgICAgICBzbG90OiB0cnVlLAogICAgICAgICAgb3ZlckhpZGRlbjogZmFsc2UKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogIuiCoeS7vSIsCiAgICAgICAgICBwcm9wOiAic3RvY2tEYXRhIiwKICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gZm9ybWF0dGVyKHZhbCwgdmFsdWUsIGxhYmVsKSB7CiAgICAgICAgICAgIHJldHVybiBfdGhpcy5mb3JtYXREaXNwbGF5RGF0YSh2YWx1ZSk7CiAgICAgICAgICB9LAogICAgICAgICAgY2VsbDogdHJ1ZSwKICAgICAgICAgIHNsb3Q6IHRydWUsCiAgICAgICAgICBvdmVySGlkZGVuOiBmYWxzZQogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAn5pWw5o2u5Lit5b+DJywKICAgICAgICAgIGNoaWxkcmVuOiBbewogICAgICAgICAgICBsYWJlbDogJ+Wkp+WeiycsCiAgICAgICAgICAgIHByb3A6ICdsYXJnZURhdGEnLAogICAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uIGZvcm1hdHRlcih2YWwsIHZhbHVlLCBsYWJlbCkgewogICAgICAgICAgICAgIHJldHVybiBfdGhpcy5mb3JtYXREaXNwbGF5RGF0YSh2YWx1ZSk7CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIG92ZXJIaWRkZW46IHRydWUsCiAgICAgICAgICAgIGNlbGw6IHRydWUsCiAgICAgICAgICAgIHNsb3Q6IHRydWUKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgbGFiZWw6ICfkuK3lsI/lnosnLAogICAgICAgICAgICBwcm9wOiAnbWVkaXVtRGF0YScsCiAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gZm9ybWF0dGVyKHZhbCwgdmFsdWUsIGxhYmVsKSB7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzLmZvcm1hdERpc3BsYXlEYXRhKHZhbHVlKTsKICAgICAgICAgICAgfSwKICAgICAgICAgICAgY2VsbDogdHJ1ZSwKICAgICAgICAgICAgc2xvdDogdHJ1ZSwKICAgICAgICAgICAgb3ZlckhpZGRlbjogdHJ1ZQogICAgICAgICAgfV0KICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogIuenu+WKqOS4muWKoSIsCiAgICAgICAgICBwcm9wOiAibW9iaWxlRGF0YSIsCiAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uIGZvcm1hdHRlcih2YWwsIHZhbHVlLCBsYWJlbCkgewogICAgICAgICAgICByZXR1cm4gX3RoaXMuZm9ybWF0RGlzcGxheURhdGEodmFsdWUpOwogICAgICAgICAgfSwKICAgICAgICAgIGNlbGw6IHRydWUsCiAgICAgICAgICBzbG90OiB0cnVlLAogICAgICAgICAgb3ZlckhpZGRlbjogZmFsc2UKICAgICAgICB9XQogICAgICB9LAogICAgICBjb21wYW55TmFtZTogdW5kZWZpbmVkLAogICAgICB0YWJsZURhdGE6IFtdLAogICAgICAvLyDljp/ooajmoLzmlbDmja4KICAgICAgb3JpZ2luYWxEYXRhOiBbXSwKICAgICAgLy8g6ZyA5pS555qE5LiL5qCH5pWw57uECiAgICAgIHVwZGF0ZUxpc3Q6IFtdLAogICAgICBlbmVyZ3lEYXRhOiBbXSwKICAgICAgZGlzYWJsZWRMaXN0OiB7CiAgICAgICAgZ3JvdXBMaXN0OiBbMCwgNCwgNSwgNywgMTgsIDIzLCAyNywgNTddLAogICAgICAgIHN0b2NrTGlzdDogWzAsIDQsIDUsIDcsIDE4LCAyMywgMjcsIDU3XSwKICAgICAgICBsYXJnZUxpc3Q6IFswLCA0LCA1LCA2LCA3LCA4LCA5LCAxMCwgMTcsIDE4LCAxOSwgMjAsIDIzLCAyNywgMzgsIDM5LCA0MCwgNDEsIDQyLCA0MywgNDQsIDQ1LCA0NiwgNDcsIDQ4LCA0OSwgNTAsIDUxLCA1MiwgNTMsIDU0LCA1NSwgNTZdLAogICAgICAgIG1lZGl1bUxpc3Q6IFswLCA0LCA1LCA2LCA3LCA4LCA5LCAxMCwgMTEsIDEyLCAxMywgMTQsIDE1LCAxNiwgMTcsIDE4LCAxOSwgMjAsIDIzLCAyNywgMzgsIDM5LCA0MCwgNDEsIDQyLCA0MywgNDQsIDQ1LCA0NiwgNDcsIDQ4LCA0OSwgNTAsIDUxLCA1MiwgNTMsIDU0LCA1NSwgNTZdLAogICAgICAgIG1vYmlsZUxpc3Q6IFswLCAyLCA0LCA1LCA2LCA3LCA4LCA5LCAxMCwgMTEsIDEyLCAxMywgMTQsIDE1LCAxNiwgMTcsIDE4LCAxOSwgMjAsIDI0LCAyNSwgMjgsIDI5LCAzOCwgMzksIDQwLCA0MSwgNDIsIDQzLCA0NCwgNDUsIDQ2LCA0NywgNDgsIDQ5LCA1MCwgNTEsIDUyLCA1MywgNTQsIDU1LCA1NiwgNTddCiAgICAgIH0sCiAgICAgIGZvcm11bGFMaXN0OiB7CiAgICAgICAgZ3JvdXBMaXN0OiBbMCwgNCwgNSwgNywgMTgsIDIzLCAyN10sCiAgICAgICAgc3RvY2tMaXN0OiBbMCwgNCwgNSwgNywgMTgsIDIzLCAyN10sCiAgICAgICAgbGFyZ2VMaXN0OiBbMCwgNCwgMjMsIDI3XSwKICAgICAgICBtZWRpdW1MaXN0OiBbMCwgNCwgMTEsIDEyLCAxMywgMTQsIDE1LCAxNiwgMjMsIDI3XSwKICAgICAgICBtb2JpbGVMaXN0OiBbMCwgNCwgNywgOCwgOSwgMTBdCiAgICAgIH0sCiAgICAgIG5vSW5wdXRMaXN0OiB7CiAgICAgICAgZ3JvdXBMaXN0OiBbNTddLAogICAgICAgIHN0b2NrTGlzdDogWzU3XSwKICAgICAgICBsYXJnZUxpc3Q6IFs1LCA2LCA3LCA4LCA5LCAxMCwgMTcsIDE4LCAxOSwgMjAsIDM4LCAzOSwgNDAsIDQxLCA0MiwgNDMsIDQ0LCA0NSwgNDYsIDQ3LCA0OCwgNDksIDUwLCA1MSwgNTIsIDUzLCA1NCwgNTUsIDU2XSwKICAgICAgICBtZWRpdW1MaXN0OiBbNSwgNiwgNywgOCwgOSwgMTAsIDE3LCAxOCwgMTksIDIwLCAzOCwgMzksIDQwLCA0MSwgNDIsIDQzLCA0NCwgNDUsIDQ2LCA0NywgNDgsIDQ5LCA1MCwgNTEsIDUyLCA1MywgNTQsIDU1LCA1Nl0sCiAgICAgICAgbW9iaWxlTGlzdDogWzIsIDUsIDYsIDExLCAxMiwgMTMsIDE0LCAxNSwgMTYsIDE3LCAxOCwgMTksIDIwLCAyNCwgMjUsIDI4LCAyOSwgMzgsIDM5LCA0MCwgNDEsIDQyLCA0MywgNDQsIDQ1LCA0NiwgNDcsIDQ4LCA0OSwgNTAsIDUxLCA1MiwgNTMsIDU0LCA1NSwgNTYsIDU3XQogICAgICB9LAogICAgICByZXF1aXJlSW5wdXRMaXN0OiB7CiAgICAgICAgZ3JvdXBMaXN0OiBbMSwgMiwgMywgNiwgOCwgOSwgMTAsIDExLCAxMiwgMTMsIDE0LCAxNSwgMTYsIDE3LCAxOSwgMjAsIDIxLCAyMiwgMjQsIDI1LCAyNiwgMjgsIDI5LCAzMCwgMzEsIDMyLCAzMywgMzQsIDM1LCAzNiwgMzcsIDM4XSwKICAgICAgICBzdG9ja0xpc3Q6IFsxLCAyLCAzLCA2LCA4LCA5LCAxMCwgMTEsIDEyLCAxMywgMTQsIDE1LCAxNiwgMTcsIDE5LCAyMCwgMjEsIDIyLCAyNCwgMjUsIDI2LCAyOCwgMjksIDMwLCAzMSwgMzIsIDMzLCAzNCwgMzUsIDM2LCAzNywgMzhdLAogICAgICAgIGxhcmdlTGlzdDogWzEsIDIsIDMsIDYsIDExLCAxMiwgMTMsIDE0LCAxNSwgMTYsIDIxLCAyMiwgMjQsIDI1LCAyNiwgMjgsIDI5LCAzMCwgMzEsIDMyLCAzMywgMzQsIDM1LCAzNiwgMzcsIDU3XSwKICAgICAgICBtZWRpdW1MaXN0OiBbMSwgMiwgMywgNiwgMjEsIDIyLCAyNCwgMjUsIDI2LCAyOCwgMjksIDMwLCAzMSwgMzIsIDMzLCAzNCwgMzUsIDM2LCAzNywgNTddLAogICAgICAgIG1vYmlsZUxpc3Q6IFsxLCAzLCAyMSwgMjIsIDIzLCAyNiwgMjcsIDMwLCAzMSwgMzIsIDMzLCAzNCwgMzUsIDM2LCAzN10KICAgICAgfSwKICAgICAgbm9SZXF1aXJlSW5wdXRMaXN0OiB7CiAgICAgICAgZ3JvdXBMaXN0OiBbMzksIDQwLCA0MSwgNDIsIDQzLCA0NCwgNDUsIDQ2LCA0NywgNDgsIDQ5LCA1MCwgNTEsIDUyLCA1MywgNTQsIDU1LCA1Nl0sCiAgICAgICAgc3RvY2tMaXN0OiBbMzksIDQwLCA0MSwgNDIsIDQzLCA0NCwgNDUsIDQ2LCA0NywgNDgsIDQ5LCA1MCwgNTEsIDUyLCA1MywgNTQsIDU1LCA1Nl0KICAgICAgfSwKICAgICAgYmFja2dyb3VuZExpc3Q6IHsKICAgICAgICBncm91cExpc3Q6IFtdLAogICAgICAgIHN0b2NrTGlzdDogW10sCiAgICAgICAgbGFyZ2VMaXN0OiBbXSwKICAgICAgICBtZWRpdW1MaXN0OiBbXSwKICAgICAgICBtb2JpbGVMaXN0OiBbXQogICAgICB9LAogICAgICBmb3JtRGF0YTogewogICAgICAgIHJlcG9ydFRpbWU6IHVuZGVmaW5lZCwKICAgICAgICBjb21wYW55SWQ6IHVuZGVmaW5lZAogICAgICB9CiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHt9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICBpZiAodGhpcy4kcm91dGUucXVlcnkpIHsKICAgICAgdGhpcy5mb3JtRGF0YS5yZXBvcnRUaW1lID0gdGhpcy4kcm91dGUucXVlcnkucmVwb3J0VGltZTsKICAgICAgdGhpcy5jb21wYW55TmFtZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmNvbXBhbnlOYW1lOwogICAgICB0aGlzLmZvcm1EYXRhLmNvbXBhbnlJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmNvbXBhbnlJZDsgLy8gdGhpcy5nZXRMaXN0KCk7CgogICAgICB0aGlzLmdldFVwZFJlY29yZExpc3QoKTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGRhdGFTdHlsZTogZnVuY3Rpb24gZGF0YVN0eWxlKF9yZWYpIHsKICAgICAgdmFyIHJvdyA9IF9yZWYucm93LAogICAgICAgICAgY29sdW1uID0gX3JlZi5jb2x1bW4sCiAgICAgICAgICByb3dJbmRleCA9IF9yZWYucm93SW5kZXgsCiAgICAgICAgICBjb2x1bW5JbmRleCA9IF9yZWYuY29sdW1uSW5kZXg7CgogICAgICBpZiAoY29sdW1uSW5kZXggPT09IDEgJiYgcm93Lmdyb3VwRWRpdCkgewogICAgICAgIHJldHVybiAnY2VsbC1jb2xvcic7CiAgICAgIH0KCiAgICAgIGlmIChjb2x1bW5JbmRleCA9PT0gMiAmJiByb3cuc3RvY2tFZGl0KSB7CiAgICAgICAgcmV0dXJuICdjZWxsLWNvbG9yJzsKICAgICAgfQoKICAgICAgaWYgKGNvbHVtbkluZGV4ID09PSAzICYmIHJvdy5sYXJnZUVkaXQpIHsKICAgICAgICByZXR1cm4gJ2NlbGwtY29sb3InOwogICAgICB9CgogICAgICBpZiAoY29sdW1uSW5kZXggPT09IDQgJiYgcm93Lm1lZGl1bUVkaXQpIHsKICAgICAgICByZXR1cm4gJ2NlbGwtY29sb3InOwogICAgICB9CgogICAgICBpZiAoY29sdW1uSW5kZXggPT09IDUgJiYgcm93Lm1vYmlsZUVkaXQpIHsKICAgICAgICByZXR1cm4gJ2NlbGwtY29sb3InOwogICAgICB9CiAgICB9LAogICAgZ2V0VXBkUmVjb3JkTGlzdDogZnVuY3Rpb24gZ2V0VXBkUmVjb3JkTGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CgogICAgICBnZXRFbmVyZ3lVcGRhdGVSZWNvcmRMaXN0KE9iamVjdC5hc3NpZ24oewogICAgICAgIGNvbXBhbnlJZDogdGhpcy5mb3JtRGF0YS5jb21wYW55SWQsCiAgICAgICAgcmVwb3J0VGltZTogdGhpcy5mb3JtRGF0YS5yZXBvcnRUaW1lCiAgICAgIH0pKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLmRhdGEuY29kZSA9PSAyMDApIHsKICAgICAgICAgIF90aGlzMi5saXN0ID0gcmVzLmRhdGEuZGF0YTsKCiAgICAgICAgICBfdGhpczIubGlzdC5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtcykgewogICAgICAgICAgICBpdGVtcy5uYW1lcyA9ICfmn6XnnIvmlbDmja4nOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+aVsOaNruemgeatoui+k+WFpWUKICAgIGlucHV0TGltaXQ6IGZ1bmN0aW9uIGlucHV0TGltaXQoZXZlbnQpIHsKICAgICAgaWYgKGV2ZW50LmtleSA9PT0gJ2UnIHx8IGV2ZW50LmtleSA9PT0gJy0nKSB7CiAgICAgICAgZXZlbnQucmV0dXJuVmFsdWUgPSBmYWxzZTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KCiAgICAgIHJldHVybiB0cnVlOwogICAgfSwKICAgIGdvQmFjazogZnVuY3Rpb24gZ29CYWNrKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogIi9kYXRhVmlldy9lbmVyZ3kvaW5kZXgiLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICByZXBvcnRUaW1lOiB0aGlzLmZvcm1EYXRhLnJlcG9ydFRpbWUKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOafpeivoum7mOiupOWIl+ihqAogICAgZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdChpdGVtKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwoKICAgICAgdGhpcy50YWJsZUxvYWRpbmcgPSB0cnVlOwogICAgICBlbmVyZ3lMaXN0KHRoaXMuZm9ybURhdGEpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuZGF0YS5jb2RlID09IDIwMCkgewogICAgICAgICAgX3RoaXMzLnRhYmxlTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgaXRlbS50YWJsZURhdGEgPSByZXMuZGF0YS5kYXRhOwoKICAgICAgICAgIF90aGlzMy5jaGVja0luZGljYXRvcklucHV0KCk7CgogICAgICAgICAgX3RoaXMzLnRhYmxlRGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgIGl0ZW0udW5pdCA9IGl0ZW0udW5pdERlc2NyaXB0aW9uICsgIigiICsgaXRlbS51bml0TmFtZSArICIpIjsKICAgICAgICAgICAgaXRlbS4kY2VsbEVkaXQgPSB0cnVlOwogICAgICAgICAgICBpdGVtLmdyb3VwRGF0YSA9IGl0ZW0uZ3JvdXBEYXRhID09IG51bGwgPyAiXFwiIDogaXRlbS5ncm91cERhdGE7CiAgICAgICAgICAgIGl0ZW0uc3RvY2tEYXRhID0gaXRlbS5zdG9ja0RhdGEgPT0gbnVsbCA/ICJcXCIgOiBpdGVtLnN0b2NrRGF0YTsKICAgICAgICAgICAgaXRlbS5sYXJnZURhdGEgPSBpdGVtLmxhcmdlRGF0YSA9PSBudWxsID8gIlxcIiA6IGl0ZW0ubGFyZ2VEYXRhOwogICAgICAgICAgICBpdGVtLm1lZGl1bURhdGEgPSBpdGVtLm1lZGl1bURhdGEgPT0gbnVsbCA/ICJcXCIgOiBpdGVtLm1lZGl1bURhdGE7CiAgICAgICAgICAgIGl0ZW0ubW9iaWxlRGF0YSA9IGl0ZW0ubW9iaWxlRGF0YSA9PSBudWxsID8gIlxcIiA6IGl0ZW0ubW9iaWxlRGF0YTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g5p+l6K+i5L+u5pS555qE5YiX6KGoCiAgICBnZXRFbmVyZ3lVcGRhdGVSZWNvcmRMaXN0OiBmdW5jdGlvbiBnZXRFbmVyZ3lVcGRhdGVSZWNvcmRMaXN0KGl0ZW0pIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CgogICAgICB0aGlzLnRhYmxlTG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMuZm9ybURhdGEudXBkYXRlUmVjb3JkSWQgPSBpdGVtLmlkOwogICAgICBlbmVyZ3lEYXRhVXBkYXRlTGlzdCh0aGlzLmZvcm1EYXRhKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLmRhdGEuY29kZSA9PSAyMDApIHsKICAgICAgICAgIF90aGlzNC50YWJsZUxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIGl0ZW0udGFibGVEYXRhID0gcmVzLmRhdGEuZGF0YTsKCiAgICAgICAgICBfdGhpczQuY2hlY2tJbmRpY2F0b3JJbnB1dCgpOwoKICAgICAgICAgIF90aGlzNC50YWJsZURhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICBpdGVtLnVuaXQgPSBpdGVtLnVuaXREZXNjcmlwdGlvbiArICIoIiArIGl0ZW0udW5pdE5hbWUgKyAiKSI7CiAgICAgICAgICAgIGl0ZW0uJGNlbGxFZGl0ID0gdHJ1ZTsKICAgICAgICAgICAgaXRlbS5ncm91cERhdGEgPSBpdGVtLmdyb3VwRGF0YSA9PSBudWxsID8gIlxcIiA6IGl0ZW0uZ3JvdXBEYXRhOwogICAgICAgICAgICBpdGVtLnN0b2NrRGF0YSA9IGl0ZW0uc3RvY2tEYXRhID09IG51bGwgPyAiXFwiIDogaXRlbS5zdG9ja0RhdGE7CiAgICAgICAgICAgIGl0ZW0ubGFyZ2VEYXRhID0gaXRlbS5sYXJnZURhdGEgPT0gbnVsbCA/ICJcXCIgOiBpdGVtLmxhcmdlRGF0YTsKICAgICAgICAgICAgaXRlbS5tZWRpdW1EYXRhID0gaXRlbS5tZWRpdW1EYXRhID09IG51bGwgPyAiXFwiIDogaXRlbS5tZWRpdW1EYXRhOwogICAgICAgICAgICBpdGVtLm1vYmlsZURhdGEgPSBpdGVtLm1vYmlsZURhdGEgPT0gbnVsbCA/ICJcXCIgOiBpdGVtLm1vYmlsZURhdGE7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIHNhdmVSZXBvcnREYXRhOiBmdW5jdGlvbiBzYXZlUmVwb3J0RGF0YSgpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CgogICAgICBpZiAodGhpcy52YWxpZFJlcG9ydERhdGEoKSA9PSBmYWxzZSkgewogICAgICAgIHRoaXMuYWxlcnRFcnJvcigi6K+36L6T5YWl5YWo6YOo5pWw5o2uIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB2YXIgZGF0YSA9IHt9OwogICAgICB2YXIgc2F2ZUxpc3QgPSBbXTsKICAgICAgdGhpcy50YWJsZURhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHZhciBzYXZlSXRlbSA9IHt9OwogICAgICAgIHNhdmVJdGVtLnJlcG9ydFRpbWUgPSBfdGhpczUuZm9ybURhdGEucmVwb3J0VGltZTsKICAgICAgICBzYXZlSXRlbS5jb21wYW55SWQgPSBfdGhpczUuZm9ybURhdGEuY29tcGFueUlkOwogICAgICAgIHNhdmVJdGVtLnJlcG9ydEZsYWcgPSAiMiI7CiAgICAgICAgc2F2ZUl0ZW0uaWQgPSBpdGVtLmlkOwogICAgICAgIHNhdmVJdGVtLmluZGljYXRvck5hbWUgPSBpdGVtLmluZGljYXRvck5hbWU7CiAgICAgICAgc2F2ZUl0ZW0uZ3JvdXBEYXRhID0gaXRlbS5ncm91cERhdGEgPT0gJ1xcJyB8fCBpdGVtLmdyb3VwRGF0YSA9PSAnLycgPyBudWxsIDogaXRlbS5ncm91cERhdGE7CiAgICAgICAgc2F2ZUl0ZW0uc3RvY2tEYXRhID0gaXRlbS5zdG9ja0RhdGEgPT0gJ1xcJyB8fCBpdGVtLnN0b2NrRGF0YSA9PSAnLycgPyBudWxsIDogaXRlbS5zdG9ja0RhdGE7CiAgICAgICAgc2F2ZUl0ZW0ubGFyZ2VEYXRhID0gaXRlbS5sYXJnZURhdGEgPT0gJ1xcJyB8fCBpdGVtLmxhcmdlRGF0YSA9PSAnLycgPyBudWxsIDogaXRlbS5sYXJnZURhdGE7CiAgICAgICAgc2F2ZUl0ZW0ubWVkaXVtRGF0YSA9IGl0ZW0ubWVkaXVtRGF0YSA9PSAnXFwnIHx8IGl0ZW0ubWVkaXVtRGF0YSA9PSAnLycgPyBudWxsIDogaXRlbS5tZWRpdW1EYXRhOwogICAgICAgIHNhdmVJdGVtLm1vYmlsZURhdGEgPSBpdGVtLm1vYmlsZURhdGEgPT0gJ1xcJyB8fCBpdGVtLm1vYmlsZURhdGEgPT0gJy8nID8gbnVsbCA6IGl0ZW0ubW9iaWxlRGF0YTsKICAgICAgICBzYXZlTGlzdC5wdXNoKHNhdmVJdGVtKTsKICAgICAgfSk7CiAgICAgIGRhdGEuY29tcGFueUlkID0gdGhpcy5mb3JtRGF0YS5jb21wYW55SWQ7CiAgICAgIGRhdGEuZGlzY2hhcmdlRW5lcmd5SW5kaWNhdG9yVm9zID0gc2F2ZUxpc3Q7CiAgICAgIGRhdGEuZGlzY2hhcmdlRGF0YUVuZXJneVVwZGF0ZUxvZ0xpc3QgPSB0aGlzLnVwZGF0ZUxpc3Q7CiAgICAgIGVuZXJneVVwZGF0ZUxpc3QoZGF0YSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5kYXRhLmNvZGUgPT0gMjAwKSB7CiAgICAgICAgICBfdGhpczUuYWxlcnRTdWNjZXNzKCLmk43kvZzmiJDlip8iKTsKCiAgICAgICAgICBfdGhpczUuZ29CYWNrKCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+agoemqjOS4iuaKpeaVsOaNrgogICAgdmFsaWRSZXBvcnREYXRhOiBmdW5jdGlvbiB2YWxpZFJlcG9ydERhdGEoKSB7CiAgICAgIHZhciByZXQgPSB0cnVlOwogICAgICB0aGlzLmJhY2tncm91bmRMaXN0Lmdyb3VwTGlzdCA9IFtdOwogICAgICB0aGlzLmJhY2tncm91bmRMaXN0LnN0b2NrTGlzdCA9IFtdOwogICAgICB0aGlzLmJhY2tncm91bmRMaXN0LmxhcmdlTGlzdCA9IFtdOwogICAgICB0aGlzLmJhY2tncm91bmRMaXN0Lm1lZGl1bUxpc3QgPSBbXTsKICAgICAgdGhpcy5iYWNrZ3JvdW5kTGlzdC5tb2JpbGVMaXN0ID0gW107CgogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRoaXMudGFibGVEYXRhLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgaWYgKHRoaXMudGFibGVEYXRhW2ldLmdyb3VwRGF0YSA9PSB1bmRlZmluZWQgfHwgdGhpcy50YWJsZURhdGFbaV0uZ3JvdXBEYXRhID09IG51bGwgfHwgdGhpcy50YWJsZURhdGFbaV0uZ3JvdXBEYXRhLmxlbmd0aCA9PSAwKSB7CiAgICAgICAgICBpZiAodGhpcy5yZXF1aXJlSW5wdXRMaXN0Lmdyb3VwTGlzdC5pbmRleE9mKGkpID4gLTEpIHsKICAgICAgICAgICAgdGhpcy5iYWNrZ3JvdW5kTGlzdC5ncm91cExpc3QucHVzaChpKTsKICAgICAgICAgICAgcmV0ID0gZmFsc2U7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLnRhYmxlRGF0YVtpXS5ncm91cERhdGEgPSAiMCI7CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICBpZiAodGhpcy50YWJsZURhdGFbaV0uc3RvY2tEYXRhID09IHVuZGVmaW5lZCB8fCB0aGlzLnRhYmxlRGF0YVtpXS5zdG9ja0RhdGEgPT0gbnVsbCB8fCB0aGlzLnRhYmxlRGF0YVtpXS5zdG9ja0RhdGEubGVuZ3RoID09IDApIHsKICAgICAgICAgIGlmICh0aGlzLnJlcXVpcmVJbnB1dExpc3Quc3RvY2tMaXN0LmluZGV4T2YoaSkgPiAtMSkgewogICAgICAgICAgICB0aGlzLmJhY2tncm91bmRMaXN0LnN0b2NrTGlzdC5wdXNoKGkpOwogICAgICAgICAgICByZXQgPSBmYWxzZTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMudGFibGVEYXRhW2ldLnN0b2NrRGF0YSA9ICIwIjsKICAgICAgICAgIH0KCiAgICAgICAgICByZXQgPSBmYWxzZTsKICAgICAgICB9CgogICAgICAgIGlmICh0aGlzLnRhYmxlRGF0YVtpXS5sYXJnZURhdGEgPT0gdW5kZWZpbmVkIHx8IHRoaXMudGFibGVEYXRhW2ldLmxhcmdlRGF0YSA9PSBudWxsIHx8IHRoaXMudGFibGVEYXRhW2ldLmxhcmdlRGF0YS5sZW5ndGggPT0gMCkgewogICAgICAgICAgdGhpcy5iYWNrZ3JvdW5kTGlzdC5sYXJnZUxpc3QucHVzaChpKTsKICAgICAgICAgIHJldCA9IGZhbHNlOwogICAgICAgIH0KCiAgICAgICAgaWYgKHRoaXMudGFibGVEYXRhW2ldLm1lZGl1bURhdGEgPT0gdW5kZWZpbmVkIHx8IHRoaXMudGFibGVEYXRhW2ldLm1lZGl1bURhdGEgPT0gbnVsbCB8fCB0aGlzLnRhYmxlRGF0YVtpXS5tZWRpdW1EYXRhLmxlbmd0aCA9PSAwKSB7CiAgICAgICAgICB0aGlzLmJhY2tncm91bmRMaXN0Lm1lZGl1bUxpc3QucHVzaChpKTsKICAgICAgICAgIHJldCA9IGZhbHNlOwogICAgICAgIH0KCiAgICAgICAgaWYgKHRoaXMudGFibGVEYXRhW2ldLm1vYmlsZURhdGEgPT0gdW5kZWZpbmVkIHx8IHRoaXMudGFibGVEYXRhW2ldLm1vYmlsZURhdGEgPT0gbnVsbCB8fCB0aGlzLnRhYmxlRGF0YVtpXS5tb2JpbGVEYXRhLmxlbmd0aCA9PSAwKSB7CiAgICAgICAgICB0aGlzLmJhY2tncm91bmRMaXN0Lm1vYmlsZUxpc3QucHVzaChpKTsKICAgICAgICAgIHJldCA9IGZhbHNlOwogICAgICAgIH0KICAgICAgfQoKICAgICAgcmV0dXJuIHJldDsKICAgIH0sCiAgICAvLyDkv53lrZjkv67mlLnorrDlvZXnmoTmlrnms5UKICAgIHNhdmVDaGFuZ2VJbmRleDogZnVuY3Rpb24gc2F2ZUNoYW5nZUluZGV4KGRhdGEsIGluZGV4LCBpbmRleE5hbWUsIG51bSwgaWQpIHsKICAgICAgdmFyIHByZWRhdGEgPSB0aGlzLm9yaWdpbmFsRGF0YVtpbmRleF1baW5kZXhOYW1lXTsgLy8g6Iul5piv5L+u5pS55LqG5pWw5o2u77yM5YiZ6K6w5b2V5LiL5qCHCgogICAgICBpZiAodGhpcy5hcmVGbG9hdHNFcXVhbChkYXRhLCB0aGlzLm9yaWdpbmFsRGF0YVtpbmRleF1baW5kZXhOYW1lXSkpIHsKICAgICAgICB2YXIgZmlsdGVyZWRBcnJheSA9IHRoaXMudXBkYXRlTGlzdC5maWx0ZXIoZnVuY3Rpb24gKG9iaikgewogICAgICAgICAgcmV0dXJuIG9iai5pZCAhPT0gaWQ7CiAgICAgICAgfSk7CiAgICAgICAgdGhpcy51cGRhdGVMaXN0ID0gZmlsdGVyZWRBcnJheTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDkvb/nlKhmaW5kSW5kZXgoKeaWueazleafpeaJvuaVsOe7hOS4reWFt+acieeJueWummlk55qE5a+56LGh55qE57Si5byVCiAgICAgICAgdmFyIF9pbmRleCA9IHRoaXMudXBkYXRlTGlzdC5maW5kSW5kZXgoZnVuY3Rpb24gKG9iaikgewogICAgICAgICAgcmV0dXJuIG9iai5pZCA9PT0gaWQ7CiAgICAgICAgfSk7IC8vIOWmguaenOe0ouW8leWkp+S6juetieS6jjDvvIzliJnooajnpLrmib7liLDkuoblr7nosaHvvIzlj6/ku6XliKDpmaQKCgogICAgICAgIGlmIChfaW5kZXggPj0gMCkgewogICAgICAgICAgLy8g5L2/55Soc3BsaWNlKCnmlrnms5XliKDpmaTmlbDnu4TkuK3mjIflrprntKLlvJXnmoTlr7nosaEKICAgICAgICAgIHRoaXMudXBkYXRlTGlzdC5zcGxpY2UoX2luZGV4LCAxKTsKICAgICAgICB9CgogICAgICAgIHRoaXMudXBkYXRlTGlzdC5wdXNoKHsKICAgICAgICAgIGVuZXJneUlkOiBpZCwKICAgICAgICAgIG51bTogbnVtLAogICAgICAgICAgcHJldmlvdXNEYXRhOiBwcmVkYXRhLAogICAgICAgICAgbm93RGF0YTogZGF0YQogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLy8gaGFuZGxlR3JvdXBDaGFuZ2Uocm93LGluZGV4KSB7CiAgICAvLyAgIHRoaXMuc2F2ZUNoYW5nZUluZGV4KHJvdy5ncm91cERhdGEsaW5kZXgsJ2dyb3VwRGF0YScsMSxyb3cuaWQpCiAgICAvLyAgIGlmICh0aGlzLm5vSW5wdXRMaXN0Lmdyb3VwTGlzdC5pbmRleE9mKGluZGV4KSA9PSAtMSAmJiB0aGlzLmZvcm11bGFMaXN0Lmdyb3VwTGlzdC5pbmRleE9mKGluZGV4KSA9PSAtMSkgewogICAgLy8gICAgIGlmIChwYXJzZUZsb2F0KHRoaXMudGFibGVEYXRhW2luZGV4XVsnZ3JvdXBEYXRhJ10udG9TdHJpbmcoKSkgPCAwKSB7CiAgICAvLyAgICAgICB0aGlzLnRhYmxlRGF0YVtpbmRleF1bJ2dyb3VwRGF0YSddID0gIjAiOwogICAgLy8gICAgIH0KICAgIC8vICAgICBpZiAodGhpcy50YWJsZURhdGFbaW5kZXhdWydncm91cERhdGEnXSAhPSB1bmRlZmluZWQgJiYgdGhpcy50YWJsZURhdGFbaW5kZXhdWydncm91cERhdGEnXSAhPSAnJykgewogICAgLy8gICAgICAgbGV0IGogPSB0aGlzLmJhY2tncm91bmRMaXN0Lmdyb3VwTGlzdC5pbmRleE9mKGluZGV4KTsKICAgIC8vICAgICAgIGlmIChqID4gLTEpIHsKICAgIC8vICAgICAgICAgdGhpcy5iYWNrZ3JvdW5kTGlzdC5ncm91cExpc3Quc3BsaWNlKGosIDEpOwogICAgLy8gICAgICAgfQogICAgLy8gICAgIH0KICAgIC8vICAgICBpZiAoaW5kZXggPT0gNiB8fCBpbmRleCA9PSAxMSB8fCBpbmRleCA9PSAxNykgewogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbNV1bJ2dyb3VwRGF0YSddID0gKHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzZdWydncm91cERhdGEnXSkgKwogICAgLy8gICAgICAgICB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVs3XVsnZ3JvdXBEYXRhJ10pICsgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMTFdWydncm91cERhdGEnXSkgKwogICAgLy8gICAgICAgICB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsxN11bJ2dyb3VwRGF0YSddKSkudG9TdHJpbmcoKTsKICAgIC8vICAgICAgIHRoaXMudGFibGVEYXRhWzRdWydncm91cERhdGEnXSA9ICh0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVs1XVsnZ3JvdXBEYXRhJ10pICsKICAgIC8vICAgICAgICAgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMThdWydncm91cERhdGEnXSkgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyMV1bJ2dyb3VwRGF0YSddKSkudG9TdHJpbmcoKTsKICAgIC8vICAgICB9IGVsc2UgaWYgKGluZGV4ID09IDggfHwgaW5kZXggPT0gOSB8fCBpbmRleCA9PSAxMCkgewogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbN11bJ2dyb3VwRGF0YSddID0gKHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzhdWydncm91cERhdGEnXSkgKwogICAgLy8gICAgICAgICB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVs5XVsnZ3JvdXBEYXRhJ10pICsgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMTBdWydncm91cERhdGEnXSkpLnRvU3RyaW5nKCk7CiAgICAvLyAgICAgICB0aGlzLnRhYmxlRGF0YVs1XVsnZ3JvdXBEYXRhJ10gPSAodGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbNl1bJ2dyb3VwRGF0YSddKSArCiAgICAvLyAgICAgICAgIHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzddWydncm91cERhdGEnXSkgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsxMV1bJ2dyb3VwRGF0YSddKSArCiAgICAvLyAgICAgICAgIHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzE3XVsnZ3JvdXBEYXRhJ10pKS50b1N0cmluZygpOwogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbNF1bJ2dyb3VwRGF0YSddID0gKHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzVdWydncm91cERhdGEnXSkgKwogICAgLy8gICAgICAgICB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsxOF1bJ2dyb3VwRGF0YSddKSArIHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzIxXVsnZ3JvdXBEYXRhJ10pKS50b1N0cmluZygpOwogICAgLy8gICAgIH0gZWxzZSBpZiAoaW5kZXggPT0gMTkgfHwgaW5kZXggPT0gMjApIHsKICAgIC8vICAgICAgIHRoaXMudGFibGVEYXRhWzE4XVsnZ3JvdXBEYXRhJ10gPSAodGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMTldWydncm91cERhdGEnXSkgKwogICAgLy8gICAgICAgICB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyMF1bJ2dyb3VwRGF0YSddKSkudG9TdHJpbmcoKTsKICAgIC8vICAgICAgIHRoaXMudGFibGVEYXRhWzRdWydncm91cERhdGEnXSA9ICh0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVs1XVsnZ3JvdXBEYXRhJ10pICsKICAgIC8vICAgICAgICAgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMThdWydncm91cERhdGEnXSkgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyMV1bJ2dyb3VwRGF0YSddKSkudG9TdHJpbmcoKTsKICAgIC8vICAgICB9IGVsc2UgaWYgKGluZGV4ID09IDIxKSB7CiAgICAvLyAgICAgICB0aGlzLnRhYmxlRGF0YVs0XVsnZ3JvdXBEYXRhJ10gPSAodGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbNV1bJ2dyb3VwRGF0YSddKSArCiAgICAvLyAgICAgICAgIHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzE4XVsnZ3JvdXBEYXRhJ10pICsgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMjFdWydncm91cERhdGEnXSkpLnRvU3RyaW5nKCk7CiAgICAvLyAgICAgfSBlbHNlIGlmIChpbmRleCA9PSAyNCB8fCBpbmRleCA9PSAyNSkgewogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbMjNdWydncm91cERhdGEnXSA9ICh0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyNF1bJ2dyb3VwRGF0YSddKSArCiAgICAvLyAgICAgICAgIHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzI1XVsnZ3JvdXBEYXRhJ10pKS50b1N0cmluZygpOwogICAgLy8gICAgIH0gZWxzZSBpZiAoaW5kZXggPT0gMjggfHwgaW5kZXggPT0gMjkpIHsKICAgIC8vICAgICAgIHRoaXMudGFibGVEYXRhWzI3XVsnZ3JvdXBEYXRhJ10gPSAodGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMjhdWydncm91cERhdGEnXSkgKwogICAgLy8gICAgICAgICB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyOV1bJ2dyb3VwRGF0YSddKSkudG9TdHJpbmcoKTsKICAgIC8vICAgICB9CiAgICAvLyAgICAgdGhpcy5jb3VudERhdGEoJ2dyb3VwRGF0YScpOwogICAgLy8gICB9CiAgICAvLyB9LAogICAgLy8gaGFuZGxlU3RvY2tDaGFuZ2Uocm93LGluZGV4KSB7CiAgICAvLyAgIHRoaXMuc2F2ZUNoYW5nZUluZGV4KHJvdy5zdG9ja0RhdGEsaW5kZXgsJ3N0b2NrRGF0YScsMixyb3cuaWQpCiAgICAvLyAgIGlmICh0aGlzLm5vSW5wdXRMaXN0LnN0b2NrTGlzdC5pbmRleE9mKGluZGV4KSA9PSAtMSAmJiB0aGlzLmZvcm11bGFMaXN0LnN0b2NrTGlzdC5pbmRleE9mKGluZGV4KSA9PSAtMSkgewogICAgLy8gICAgIGlmIChwYXJzZUZsb2F0KHRoaXMudGFibGVEYXRhW2luZGV4XVsnc3RvY2tEYXRhJ10udG9TdHJpbmcoKSkgPCAwKSB7CiAgICAvLyAgICAgICB0aGlzLnRhYmxlRGF0YVtpbmRleF1bJ3N0b2NrRGF0YSddID0gIjAiOwogICAgLy8gICAgIH0KICAgIC8vICAgICBpZiAodGhpcy50YWJsZURhdGFbaW5kZXhdWydzdG9ja0RhdGEnXSAhPSB1bmRlZmluZWQgJiYgdGhpcy50YWJsZURhdGFbaW5kZXhdWydzdG9ja0RhdGEnXSAhPSAnJykgewogICAgLy8gICAgICAgbGV0IGogPSB0aGlzLmJhY2tncm91bmRMaXN0LnN0b2NrTGlzdC5pbmRleE9mKGluZGV4KTsKICAgIC8vICAgICAgIGlmIChqID4gLTEpIHsKICAgIC8vICAgICAgICAgdGhpcy5iYWNrZ3JvdW5kTGlzdC5zdG9ja0xpc3Quc3BsaWNlKGosIDEpOwogICAgLy8gICAgICAgfQogICAgLy8gICAgIH0KICAgIC8vICAgICBpZiAoaW5kZXggPT0gNiB8fCBpbmRleCA9PSAxMSB8fCBpbmRleCA9PSAxNykgewogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbNV1bJ3N0b2NrRGF0YSddID0gKHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzZdWydzdG9ja0RhdGEnXSkgKwogICAgLy8gICAgICAgICB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVs3XVsnc3RvY2tEYXRhJ10pICsgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMTFdWydzdG9ja0RhdGEnXSkgKwogICAgLy8gICAgICAgICB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsxN11bJ3N0b2NrRGF0YSddKSkudG9TdHJpbmcoKTsKICAgIC8vICAgICAgIHRoaXMudGFibGVEYXRhWzRdWydzdG9ja0RhdGEnXSA9ICh0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVs1XVsnc3RvY2tEYXRhJ10pICsKICAgIC8vICAgICAgICAgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMThdWydzdG9ja0RhdGEnXSkgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyMV1bJ3N0b2NrRGF0YSddKSkudG9TdHJpbmcoKTsKICAgIC8vICAgICB9IGVsc2UgaWYgKGluZGV4ID09IDggfHwgaW5kZXggPT0gOSB8fCBpbmRleCA9PSAxMCkgewogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbaW5kZXhdWydtZWRpdW1EYXRhJ10gPSB0aGlzLnRhYmxlRGF0YVtpbmRleF1bJ3N0b2NrRGF0YSddOwogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbaW5kZXhdWydtb2JpbGVEYXRhJ10gPSB0aGlzLnRhYmxlRGF0YVtpbmRleF1bJ3N0b2NrRGF0YSddOwogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbN11bJ3N0b2NrRGF0YSddID0gKHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzhdWydzdG9ja0RhdGEnXSkgKwogICAgLy8gICAgICAgICB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVs5XVsnc3RvY2tEYXRhJ10pICsgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMTBdWydzdG9ja0RhdGEnXSkpLnRvU3RyaW5nKCk7CiAgICAvLyAgICAgICB0aGlzLnRhYmxlRGF0YVs3XVsnbWVkaXVtRGF0YSddID0gdGhpcy50YWJsZURhdGFbN11bJ3N0b2NrRGF0YSddOwogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbNF1bJ21lZGl1bURhdGEnXSA9IHRoaXMudGFibGVEYXRhWzddWydzdG9ja0RhdGEnXTsKICAgIC8vICAgICAgIHRoaXMudGFibGVEYXRhWzddWydtb2JpbGVEYXRhJ10gPSB0aGlzLnRhYmxlRGF0YVs3XVsnc3RvY2tEYXRhJ107CiAgICAvLyAgICAgICB0aGlzLnRhYmxlRGF0YVs0XVsnbW9iaWxlRGF0YSddID0gdGhpcy50YWJsZURhdGFbN11bJ3N0b2NrRGF0YSddOwogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbNV1bJ3N0b2NrRGF0YSddID0gKHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzZdWydzdG9ja0RhdGEnXSkgKwogICAgLy8gICAgICAgICB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVs3XVsnc3RvY2tEYXRhJ10pICsgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMTFdWydzdG9ja0RhdGEnXSkgKwogICAgLy8gICAgICAgICB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsxN11bJ3N0b2NrRGF0YSddKSkudG9TdHJpbmcoKTsKICAgIC8vICAgICAgIHRoaXMudGFibGVEYXRhWzRdWydzdG9ja0RhdGEnXSA9ICh0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVs1XVsnc3RvY2tEYXRhJ10pICsKICAgIC8vICAgICAgICAgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMThdWydzdG9ja0RhdGEnXSkgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyMV1bJ3N0b2NrRGF0YSddKSkudG9TdHJpbmcoKTsKICAgIC8vICAgICAgIHRoaXMuY291bnREYXRhKCdtZWRpdW1EYXRhJyk7CiAgICAvLyAgICAgICB0aGlzLmNvdW50RGF0YSgnbW9iaWxlRGF0YScpOwogICAgLy8gICAgIH0gZWxzZSBpZiAoaW5kZXggPiAxMiAmJiBpbmRleCA8IDE3KSB7CiAgICAvLyAgICAgICB0aGlzLnRhYmxlRGF0YVtpbmRleF1bJ21lZGl1bURhdGEnXSA9IHRoaXMudGFibGVEYXRhW2luZGV4XVsnc3RvY2tEYXRhJ107CiAgICAvLyAgICAgICBpZiAoaW5kZXggPT0gMTMgfHwgaW5kZXggPT0gMTUpIHsKICAgIC8vICAgICAgICAgdGhpcy50YWJsZURhdGFbMTFdWydtZWRpdW1EYXRhJ10gPSAodGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMTNdWydzdG9ja0RhdGEnXSkgKwogICAgLy8gICAgICAgICAgIHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzE1XVsnc3RvY2tEYXRhJ10pKS50b1N0cmluZygpOwogICAgLy8gICAgICAgICB0aGlzLnRhYmxlRGF0YVs0XVsnbWVkaXVtRGF0YSddID0gdGhpcy50YWJsZURhdGFbMTFdWydtZWRpdW1EYXRhJ107CiAgICAvLyAgICAgICAgIHRoaXMuY291bnREYXRhKCdtZWRpdW1EYXRhJyk7CiAgICAvLyAgICAgICB9IGVsc2UgewogICAgLy8gICAgICAgICB0aGlzLnRhYmxlRGF0YVsxMl1bJ21lZGl1bURhdGEnXSA9ICh0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsxNF1bJ3N0b2NrRGF0YSddKSArCiAgICAvLyAgICAgICAgICAgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMTZdWydzdG9ja0RhdGEnXSkpLnRvU3RyaW5nKCk7CiAgICAvLyAgICAgICB9CiAgICAvLyAgICAgfSBlbHNlIGlmIChpbmRleCA9PSAxOSB8fCBpbmRleCA9PSAyMCkgewogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbMThdWydzdG9ja0RhdGEnXSA9ICh0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsxOV1bJ3N0b2NrRGF0YSddKSArCiAgICAvLyAgICAgICAgIHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzIwXVsnc3RvY2tEYXRhJ10pKS50b1N0cmluZygpOwogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbNF1bJ3N0b2NrRGF0YSddID0gKHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzVdWydzdG9ja0RhdGEnXSkgKwogICAgLy8gICAgICAgICB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsxOF1bJ3N0b2NrRGF0YSddKSArIHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzIxXVsnc3RvY2tEYXRhJ10pKS50b1N0cmluZygpOwogICAgLy8gICAgIH0gZWxzZSBpZiAoaW5kZXggPT0gMjEpIHsKICAgIC8vICAgICAgIHRoaXMudGFibGVEYXRhWzRdWydzdG9ja0RhdGEnXSA9ICh0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVs1XVsnc3RvY2tEYXRhJ10pICsKICAgIC8vICAgICAgICAgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMThdWydzdG9ja0RhdGEnXSkgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyMV1bJ3N0b2NrRGF0YSddKSkudG9TdHJpbmcoKTsKICAgIC8vICAgICB9IGVsc2UgaWYgKGluZGV4ID09IDI0IHx8IGluZGV4ID09IDI1KSB7CiAgICAvLyAgICAgICB0aGlzLnRhYmxlRGF0YVsyM11bJ3N0b2NrRGF0YSddID0gKHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzI0XVsnc3RvY2tEYXRhJ10pICsKICAgIC8vICAgICAgICAgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMjVdWydzdG9ja0RhdGEnXSkpLnRvU3RyaW5nKCk7CiAgICAvLyAgICAgfSBlbHNlIGlmIChpbmRleCA9PSAyOCB8fCBpbmRleCA9PSAyOSkgewogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbMjddWydzdG9ja0RhdGEnXSA9ICh0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyOF1bJ3N0b2NrRGF0YSddKSArCiAgICAvLyAgICAgICAgIHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzI5XVsnc3RvY2tEYXRhJ10pKS50b1N0cmluZygpOwogICAgLy8gICAgIH0KICAgIC8vICAgICB0aGlzLmNvdW50RGF0YSgnc3RvY2tEYXRhJyk7CiAgICAvLyAgIH0KICAgIC8vIH0sCiAgICAvLyBoYW5kbGVMYXJnZUNoYW5nZShyb3csaW5kZXgpIHsKICAgIC8vICAgdGhpcy5zYXZlQ2hhbmdlSW5kZXgocm93LmxhcmdlRGF0YSxpbmRleCwnbGFyZ2VEYXRhJywzLHJvdy5pZCkKICAgIC8vICAgaWYgKHRoaXMubm9JbnB1dExpc3QubGFyZ2VMaXN0LmluZGV4T2YoaW5kZXgpID09IC0xICYmIHRoaXMuZm9ybXVsYUxpc3QubGFyZ2VMaXN0LmluZGV4T2YoaW5kZXgpID09IC0xKSB7CiAgICAvLyAgICAgaWYgKHBhcnNlRmxvYXQodGhpcy50YWJsZURhdGFbaW5kZXhdWydsYXJnZURhdGEnXS50b1N0cmluZygpKSA8IDApIHsKICAgIC8vICAgICAgIHRoaXMudGFibGVEYXRhW2luZGV4XVsnbGFyZ2VEYXRhJ10gPSAiMCI7CiAgICAvLyAgICAgfQogICAgLy8gICAgIGlmICh0aGlzLnRhYmxlRGF0YVtpbmRleF1bJ2xhcmdlRGF0YSddICE9IHVuZGVmaW5lZCAmJiB0aGlzLnRhYmxlRGF0YVtpbmRleF1bJ2xhcmdlRGF0YSddICE9ICcnKSB7CiAgICAvLyAgICAgICBsZXQgaiA9IHRoaXMuYmFja2dyb3VuZExpc3QubGFyZ2VMaXN0LmluZGV4T2YoaW5kZXgpOwogICAgLy8gICAgICAgaWYgKGogPiAtMSkgewogICAgLy8gICAgICAgICB0aGlzLmJhY2tncm91bmRMaXN0LmxhcmdlTGlzdC5zcGxpY2UoaiwgMSk7CiAgICAvLyAgICAgICB9CiAgICAvLyAgICAgfQogICAgLy8gICAgIGlmIChpbmRleCA9PSAxMSkgewogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbNF1bJ2xhcmdlRGF0YSddID0gdGhpcy50YWJsZURhdGFbMTFdWydsYXJnZURhdGEnXTsKICAgIC8vICAgICB9IGVsc2UgaWYgKGluZGV4ID09IDI0IHx8IGluZGV4ID09IDI1KSB7CiAgICAvLyAgICAgICB0aGlzLnRhYmxlRGF0YVsyM11bJ2xhcmdlRGF0YSddID0gKHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzI0XVsnbGFyZ2VEYXRhJ10pICsKICAgIC8vICAgICAgICAgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMjVdWydsYXJnZURhdGEnXSkpLnRvU3RyaW5nKCk7CiAgICAvLyAgICAgfSBlbHNlIGlmIChpbmRleCA9PSAyOCB8fCBpbmRleCA9PSAyOSkgewogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbMjddWydsYXJnZURhdGEnXSA9ICh0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyOF1bJ2xhcmdlRGF0YSddKSArCiAgICAvLyAgICAgICAgIHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzI5XVsnbGFyZ2VEYXRhJ10pKS50b1N0cmluZygpOwogICAgLy8gICAgIH0KICAgIC8vICAgICB0aGlzLmNvdW50RGF0YSgnbGFyZ2VEYXRhJyk7CiAgICAvLyAgIH0KICAgIC8vIH0sCiAgICAvLyBoYW5kbGVNZWRpdW1DaGFuZ2Uocm93LGluZGV4KSB7CiAgICAvLyAgIHRoaXMuc2F2ZUNoYW5nZUluZGV4KHJvdy5tZWRpdW1EYXRhLGluZGV4LCdtZWRpdW1EYXRhJyw0LHJvdy5pZCkKICAgIC8vICAgaWYgKHRoaXMubm9JbnB1dExpc3QubWVkaXVtTGlzdC5pbmRleE9mKGluZGV4KSA9PSAtMSAmJiB0aGlzLmZvcm11bGFMaXN0Lm1lZGl1bUxpc3QuaW5kZXhPZihpbmRleCkgPT0gLTEpIHsKICAgIC8vICAgICBpZiAocGFyc2VGbG9hdCh0aGlzLnRhYmxlRGF0YVtpbmRleF1bJ21lZGl1bURhdGEnXS50b1N0cmluZygpKSA8IDApIHsKICAgIC8vICAgICAgIHRoaXMudGFibGVEYXRhW2luZGV4XVsnbWVkaXVtRGF0YSddID0gIjAiOwogICAgLy8gICAgIH0KICAgIC8vICAgICBpZiAodGhpcy50YWJsZURhdGFbaW5kZXhdWydtZWRpdW1EYXRhJ10gIT0gdW5kZWZpbmVkICYmIHRoaXMudGFibGVEYXRhW2luZGV4XVsnbWVkaXVtRGF0YSddICE9ICcnKSB7CiAgICAvLyAgICAgICBsZXQgaiA9IHRoaXMuYmFja2dyb3VuZExpc3QubWVkaXVtTGlzdC5pbmRleE9mKGluZGV4KTsKICAgIC8vICAgICAgIGlmIChqID4gLTEpIHsKICAgIC8vICAgICAgICAgdGhpcy5iYWNrZ3JvdW5kTGlzdC5tZWRpdW1MaXN0LnNwbGljZShqLCAxKTsKICAgIC8vICAgICAgIH0KICAgIC8vICAgICB9CiAgICAvLyAgICAgaWYgKGluZGV4ID09IDI0IHx8IGluZGV4ID09IDI1KSB7CiAgICAvLyAgICAgICB0aGlzLnRhYmxlRGF0YVsyM11bJ21lZGl1bURhdGEnXSA9ICh0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyNF1bJ21lZGl1bURhdGEnXSkgKwogICAgLy8gICAgICAgICB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyNV1bJ21lZGl1bURhdGEnXSkpLnRvU3RyaW5nKCk7CiAgICAvLyAgICAgfSBlbHNlIGlmIChpbmRleCA9PSAyOCB8fCBpbmRleCA9PSAyOSkgewogICAgLy8gICAgICAgdGhpcy50YWJsZURhdGFbMjddWydtZWRpdW1EYXRhJ10gPSAodGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMjhdWydtZWRpdW1EYXRhJ10pICsKICAgIC8vICAgICAgICAgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMjldWydtZWRpdW1EYXRhJ10pKS50b1N0cmluZygpOwogICAgLy8gICAgIH0KICAgIC8vICAgICB0aGlzLmNvdW50RGF0YSgnbWVkaXVtRGF0YScpOwogICAgLy8gICB9CiAgICAvLyB9LAogICAgLy8gaGFuZGxlTW9iaWxlQ2hhbmdlKHJvdyxpbmRleCkgewogICAgLy8gICB0aGlzLnNhdmVDaGFuZ2VJbmRleChyb3cubW9iaWxlRGF0YSxpbmRleCwnbWVkaXVtRGF0YScsNSxyb3cuaWQpCiAgICAvLyAgIGlmICh0aGlzLm5vSW5wdXRMaXN0Lm1vYmlsZUxpc3QuaW5kZXhPZihpbmRleCkgPT0gLTEgJiYgdGhpcy5mb3JtdWxhTGlzdC5tb2JpbGVMaXN0LmluZGV4T2YoaW5kZXgpID09IC0xKSB7CiAgICAvLyAgICAgaWYgKHBhcnNlRmxvYXQodGhpcy50YWJsZURhdGFbaW5kZXhdWydtb2JpbGVEYXRhJ10udG9TdHJpbmcoKSkgPCAwKSB7CiAgICAvLyAgICAgICB0aGlzLnRhYmxlRGF0YVtpbmRleF1bJ21vYmlsZURhdGEnXSA9ICIwIjsKICAgIC8vICAgICB9CiAgICAvLyAgICAgaWYgKHRoaXMudGFibGVEYXRhW2luZGV4XVsnbW9iaWxlRGF0YSddICE9IHVuZGVmaW5lZCAmJiB0aGlzLnRhYmxlRGF0YVtpbmRleF1bJ21vYmlsZURhdGEnXSAhPSAnJykgewogICAgLy8gICAgICAgbGV0IGogPSB0aGlzLmJhY2tncm91bmRMaXN0Lm1vYmlsZUxpc3QuaW5kZXhPZihpbmRleCk7CiAgICAvLyAgICAgICBpZiAoaiA+IC0xKSB7CiAgICAvLyAgICAgICAgIHRoaXMuYmFja2dyb3VuZExpc3QubW9iaWxlTGlzdC5zcGxpY2UoaiwgMSk7CiAgICAvLyAgICAgICB9CiAgICAvLyAgICAgfQogICAgLy8gICAgIHRoaXMuY291bnREYXRhKCdtb2JpbGVEYXRhJykKICAgIC8vICAgfQogICAgLy8gfSwKICAgIGNvdW50RGF0YTogZnVuY3Rpb24gY291bnREYXRhKGxhYmVsKSB7CiAgICAgIHRoaXMudGFibGVEYXRhWzBdW2xhYmVsXSA9IChNYXRoLnJvdW5kKCh0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsxXVtsYWJlbF0pICogdGhpcy50YWJsZURhdGFbMV0uY29lZmZpY2llbnQgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVszXVtsYWJlbF0pICogdGhpcy50YWJsZURhdGFbM10uY29lZmZpY2llbnQgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVs0XVtsYWJlbF0pICogdGhpcy50YWJsZURhdGFbNF0uY29lZmZpY2llbnQgLyAxMDAwICsgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMjFdW2xhYmVsXSkgKiB0aGlzLnRhYmxlRGF0YVsyMV0uY29lZmZpY2llbnQgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyMl1bbGFiZWxdKSAqIHRoaXMudGFibGVEYXRhWzIyXS5jb2VmZmljaWVudCAvIDEwMDAgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyNV1bbGFiZWxdKSAqIHRoaXMudGFibGVEYXRhWzI1XS5jb2VmZmljaWVudCAvIDEwMDAgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyNl1bbGFiZWxdKSAqIHRoaXMudGFibGVEYXRhWzI2XS5jb2VmZmljaWVudCAvIDEwMDAgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVsyOV1bbGFiZWxdKSAqIHRoaXMudGFibGVEYXRhWzI5XS5jb2VmZmljaWVudCAvIDEwMDAgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVszMF1bbGFiZWxdKSAqIHRoaXMudGFibGVEYXRhWzMwXS5jb2VmZmljaWVudCArIHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzMxXVtsYWJlbF0pICogdGhpcy50YWJsZURhdGFbMzFdLmNvZWZmaWNpZW50IC8gMTAwMCArIHRoaXMuY2VsbFRvTnVtYmVyKHRoaXMudGFibGVEYXRhWzMyXVtsYWJlbF0pICogdGhpcy50YWJsZURhdGFbMzJdLmNvZWZmaWNpZW50ICsgdGhpcy5jZWxsVG9OdW1iZXIodGhpcy50YWJsZURhdGFbMzNdW2xhYmVsXSkgKyB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVszNF1bbGFiZWxdKSAqIHRoaXMudGFibGVEYXRhWzM0XS5jb2VmZmljaWVudCAvIDEwMDAgLSB0aGlzLmNlbGxUb051bWJlcih0aGlzLnRhYmxlRGF0YVszNV1bbGFiZWxdKSkgKiAxMDAwMDAwKSAvIDEwMDAwMDApLnRvU3RyaW5nKCk7CiAgICB9LAogICAgLy/moLnmja7mjIfmoIflkI3np7Dnoa7lrprovpPlhaXmoYbnirbmgIEKICAgIGNoZWNrSW5kaWNhdG9ySW5wdXQ6IGZ1bmN0aW9uIGNoZWNrSW5kaWNhdG9ySW5wdXQoKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwoKICAgICAgT2JqZWN0LmtleXModGhpcy5kaXNhYmxlZExpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgIHJldHVybiBfdGhpczYuZGlzYWJsZWRMaXN0W2tleV0gPSBbXTsKICAgICAgfSk7CiAgICAgIE9iamVjdC5rZXlzKHRoaXMuZm9ybXVsYUxpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgIHJldHVybiBfdGhpczYuZm9ybXVsYUxpc3Rba2V5XSA9IFtdOwogICAgICB9KTsKICAgICAgT2JqZWN0LmtleXModGhpcy5ub0lucHV0TGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgcmV0dXJuIF90aGlzNi5ub0lucHV0TGlzdFtrZXldID0gW107CiAgICAgIH0pOwogICAgICBPYmplY3Qua2V5cyh0aGlzLnJlcXVpcmVJbnB1dExpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgIHJldHVybiBfdGhpczYucmVxdWlyZUlucHV0TGlzdFtrZXldID0gW107CiAgICAgIH0pOwogICAgICBPYmplY3Qua2V5cyh0aGlzLm5vUmVxdWlyZUlucHV0TGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgcmV0dXJuIF90aGlzNi5ub1JlcXVpcmVJbnB1dExpc3Rba2V5XSA9IFtdOwogICAgICB9KTsKCiAgICAgIHZhciBfbG9vcCA9IGZ1bmN0aW9uIF9sb29wKGkpIHsKICAgICAgICBpZiAoJzHjgIHog73mupDmtojotLnmgLvph48o5ZCo5qCH54WkKScgPT0gX3RoaXM2LnRhYmxlRGF0YVtpXS5pbmRpY2F0b3JOYW1lKSB7CiAgICAgICAgICBPYmplY3Qua2V5cyhfdGhpczYuZGlzYWJsZWRMaXN0KS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICAgICAgcmV0dXJuIF90aGlzNi5kaXNhYmxlZExpc3Rba2V5XS5wdXNoKGkpOwogICAgICAgICAgfSk7CiAgICAgICAgICBPYmplY3Qua2V5cyhfdGhpczYuZm9ybXVsYUxpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgICAgICByZXR1cm4gX3RoaXM2LmZvcm11bGFMaXN0W2tleV0ucHVzaChpKTsKICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSBpZiAoJzEuMeOAgSDnhaTngq0o5ZCoKScgPT0gX3RoaXM2LnRhYmxlRGF0YVtpXS5pbmRpY2F0b3JOYW1lKSB7CiAgICAgICAgICBPYmplY3Qua2V5cyhfdGhpczYucmVxdWlyZUlucHV0TGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgIHJldHVybiBfdGhpczYucmVxdWlyZUlucHV0TGlzdFtrZXldLnB1c2goaSk7CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgaWYgKCcxLjEuMeOAgeWFtuS4reWPkeeUteeUqOeFpCjlkKgpJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUpIHsKICAgICAgICAgIF90aGlzNi5kaXNhYmxlZExpc3QubW9iaWxlTGlzdC5wdXNoKGkpOwoKICAgICAgICAgIF90aGlzNi5ub0lucHV0TGlzdC5tb2JpbGVMaXN0LnB1c2goaSk7CgogICAgICAgICAgT2JqZWN0LmtleXMoX3RoaXM2LnJlcXVpcmVJbnB1dExpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgICAgICBpZiAoa2V5ICE9ICdtb2JpbGVMaXN0JykgewogICAgICAgICAgICAgIF90aGlzNi5yZXF1aXJlSW5wdXRMaXN0W2tleV0ucHVzaChpKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIGlmICgnMS4y44CB54Sm54KtKOWQqCknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSkgewogICAgICAgICAgT2JqZWN0LmtleXMoX3RoaXM2LnJlcXVpcmVJbnB1dExpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgICAgICByZXR1cm4gX3RoaXM2LnJlcXVpcmVJbnB1dExpc3Rba2V5XS5wdXNoKGkpOwogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIGlmICgnMS4z44CB6ICX55S16YeP77yI5oC777yJKOWNg+eTpuaXtiknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSkgewogICAgICAgICAgT2JqZWN0LmtleXMoX3RoaXM2LmRpc2FibGVkTGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgIHJldHVybiBfdGhpczYuZGlzYWJsZWRMaXN0W2tleV0ucHVzaChpKTsKICAgICAgICAgIH0pOwogICAgICAgICAgT2JqZWN0LmtleXMoX3RoaXM2LmZvcm11bGFMaXN0KS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICAgICAgcmV0dXJuIF90aGlzNi5mb3JtdWxhTGlzdFtrZXldLnB1c2goaSk7CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgaWYgKCcxLjMuMeOAgeeUn+S6p+eUqOaIv+iAl+eUtemHjyjljYPnk6bml7YpJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUpIHsKICAgICAgICAgIE9iamVjdC5rZXlzKF90aGlzNi5kaXNhYmxlZExpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgICAgICByZXR1cm4gX3RoaXM2LmRpc2FibGVkTGlzdFtrZXldLnB1c2goaSk7CiAgICAgICAgICB9KTsKCiAgICAgICAgICBfdGhpczYuZm9ybXVsYUxpc3QuZ3JvdXBMaXN0LnB1c2goaSk7CgogICAgICAgICAgX3RoaXM2LmZvcm11bGFMaXN0LnN0b2NrTGlzdC5wdXNoKGkpOwoKICAgICAgICAgIF90aGlzNi5ub0lucHV0TGlzdC5sYXJnZUxpc3QucHVzaChpKTsKCiAgICAgICAgICBfdGhpczYubm9JbnB1dExpc3QubWVkaXVtTGlzdC5wdXNoKGkpOwoKICAgICAgICAgIF90aGlzNi5ub0lucHV0TGlzdC5tb2JpbGVMaXN0LnB1c2goaSk7CiAgICAgICAgfSBlbHNlIGlmICgnMS4zLjEuMeOAgeWFtuS4re+8mumAmuS/oeacuuaIv+iAl+eUtemHjyjljYPnk6bml7YpJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUpIHsKICAgICAgICAgIF90aGlzNi5kaXNhYmxlZExpc3QubGFyZ2VMaXN0LnB1c2goaSk7CgogICAgICAgICAgX3RoaXM2LmRpc2FibGVkTGlzdC5tZWRpdW1MaXN0LnB1c2goaSk7CgogICAgICAgICAgX3RoaXM2LmRpc2FibGVkTGlzdC5tb2JpbGVMaXN0LnB1c2goaSk7CgogICAgICAgICAgX3RoaXM2Lm5vSW5wdXRMaXN0LmxhcmdlTGlzdC5wdXNoKGkpOwoKICAgICAgICAgIF90aGlzNi5ub0lucHV0TGlzdC5tZWRpdW1MaXN0LnB1c2goaSk7CgogICAgICAgICAgX3RoaXM2Lm5vSW5wdXRMaXN0Lm1vYmlsZUxpc3QucHVzaChpKTsKCiAgICAgICAgICBPYmplY3Qua2V5cyhfdGhpczYucmVxdWlyZUlucHV0TGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgIGlmIChrZXkgIT0gJ21vYmlsZUxpc3QnKSB7CiAgICAgICAgICAgICAgX3RoaXM2LnJlcXVpcmVJbnB1dExpc3Rba2V5XS5wdXNoKGkpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgaWYgKCcxLjMuMS4y44CB5YW25Lit77ya5Z+656uZ6ICX55S16YePKOWNg+eTpuaXtiknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSkgewogICAgICAgICAgLy83CiAgICAgICAgICBPYmplY3Qua2V5cyhfdGhpczYuZGlzYWJsZWRMaXN0KS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICAgICAgcmV0dXJuIF90aGlzNi5kaXNhYmxlZExpc3Rba2V5XS5wdXNoKGkpOwogICAgICAgICAgfSk7CiAgICAgICAgICBPYmplY3Qua2V5cyhfdGhpczYuZm9ybXVsYUxpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgICAgICBpZiAoa2V5ICE9ICdsYXJnZUxpc3QnICYmIGtleSAhPSAnbWVkaXVtTGlzdCcpIHsKICAgICAgICAgICAgICBfdGhpczYuZm9ybXVsYUxpc3Rba2V5XS5wdXNoKGkpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKCiAgICAgICAgICBfdGhpczYubm9JbnB1dExpc3QubGFyZ2VMaXN0LnB1c2goaSk7CgogICAgICAgICAgX3RoaXM2Lm5vSW5wdXRMaXN0Lm1lZGl1bUxpc3QucHVzaChpKTsKICAgICAgICB9IGVsc2UgaWYgKCcxLjMuMS4yLjHjgIHlhbbkuK3vvJrpk4HloZTlhazlj7jln7rnq5nogJfnlLXph4/vvIjljIXmi6zlrqTlhoXliIbluIPjgIHlrqTlpJbnq5nnrYnvvIkgKOWNg+eTpuaXtiknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMS4zLjEuMi4y44CB5YW25Lit77ya56ys5LiJ5pa556ef6LWB5Z+656uZ6ICX55S16YeP77yI5YyF5ous5a6k5YaF5YiG5biD44CB5a6k5aSW56uZ562J77yJICjljYPnk6bml7YpJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUgfHwgJzEuMy4xLjIuM+OAgeWFtuS4re+8muiHquacieS6p+adg+WfuuermeiAl+eUtemHj++8iOWMheaLrOWupOWGheWIhuW4g+OAgeWupOWkluermeetie+8iSAo5Y2D55Om5pe2KScgPT0gX3RoaXM2LnRhYmxlRGF0YVtpXS5pbmRpY2F0b3JOYW1lKSB7CiAgICAgICAgICAvLzggLSAxMAogICAgICAgICAgT2JqZWN0LmtleXMoX3RoaXM2LmRpc2FibGVkTGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgIGlmIChrZXkgIT0gJ2dyb3VwTGlzdCcgJiYga2V5ICE9ICdzdG9ja0xpc3QnKSB7CiAgICAgICAgICAgICAgX3RoaXM2LmRpc2FibGVkTGlzdFtrZXldLnB1c2goaSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwoKICAgICAgICAgIF90aGlzNi5mb3JtdWxhTGlzdC5tb2JpbGVMaXN0LnB1c2goaSk7CgogICAgICAgICAgX3RoaXM2Lm5vSW5wdXRMaXN0LmxhcmdlTGlzdC5wdXNoKGkpOwoKICAgICAgICAgIF90aGlzNi5ub0lucHV0TGlzdC5tZWRpdW1MaXN0LnB1c2goaSk7CgogICAgICAgICAgX3RoaXM2LnJlcXVpcmVJbnB1dExpc3QuZ3JvdXBMaXN0LnB1c2goaSk7CgogICAgICAgICAgX3RoaXM2LnJlcXVpcmVJbnB1dExpc3Quc3RvY2tMaXN0LnB1c2goaSk7CiAgICAgICAgfSBlbHNlIGlmICgnMS4zLjEuM+OAgeWFtuS4re+8muaVsOaNruS4reW/g+iAl+eUtemHjyjljYPnk6bml7YpJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUgfHwgJzEuMy4xLjMuMeOAgeWFtuS4re+8muaVsOaNruS4reW/g0lU6K6+5aSH5oC76ICX55S16YeP77yI5Y2D55Om5pe277yJJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUgfHwgJzEuMy4xLjMuMuOAgeWFtuS4re+8muWvueWklklEQ+acuuaIv+iAl+eUtemHjyjljYPnk6bml7YpJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUgfHwgJzEuMy4xLjMuMi4x44CB5YW25Lit77ya5a+55aSWSURD5py65oi/SVTorr7lpIfogJfnlLXph4/vvIjljYPnk6bml7YpJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUgfHwgJzEuMy4xLjMuM+OAgeWFtuS4re+8muiHqueUqOS4muWKoeW5s+WPsOWSjElU5pSv5pKR55So5oi/6ICX55S16YePKOWNg+eTpuaXtiknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMS4zLjEuMy4zLjHjgIHlhbbkuK3vvJroh6rnlKjkuJrliqHlubPlj7BJVOiuvuWkh+iAl+eUtemHj++8iOWNg+eTpuaXtu+8iScgPT0gX3RoaXM2LnRhYmxlRGF0YVtpXS5pbmRpY2F0b3JOYW1lKSB7CiAgICAgICAgICAvLzExIC0gMTYKICAgICAgICAgIF90aGlzNi5kaXNhYmxlZExpc3QubWVkaXVtTGlzdC5wdXNoKGkpOwoKICAgICAgICAgIF90aGlzNi5kaXNhYmxlZExpc3QubW9iaWxlTGlzdC5wdXNoKGkpOwoKICAgICAgICAgIF90aGlzNi5mb3JtdWxhTGlzdC5tZWRpdW1MaXN0LnB1c2goaSk7CgogICAgICAgICAgX3RoaXM2Lm5vSW5wdXRMaXN0Lm1vYmlsZUxpc3QucHVzaChpKTsKCiAgICAgICAgICBPYmplY3Qua2V5cyhfdGhpczYucmVxdWlyZUlucHV0TGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgIGlmIChrZXkgIT0gJ21lZGl1bUxpc3QnICYmIGtleSAhPSAnbW9iaWxlTGlzdCcpIHsKICAgICAgICAgICAgICBfdGhpczYucmVxdWlyZUlucHV0TGlzdFtrZXldLnB1c2goaSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSBpZiAoJzEuMy4xLjTjgIHlhbbkuK3vvJrmjqXlhaXlsYDmiYDlj4rlrqTlpJbmnLrmn5zogJfnlLXph48o5Y2D55Om5pe2KScgPT0gX3RoaXM2LnRhYmxlRGF0YVtpXS5pbmRpY2F0b3JOYW1lIHx8ICcxLjMuMi4x44CB5YW25Lit77ya566h55CG55So5oi/6ICX55S16YePKOWNg+eTpuaXtiknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMS4zLjIuMuOAgeWFtuS4re+8mua4oOmBk+eUqOaIv+iAl+eUtemHjyjljYPnk6bml7YpJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUgfHwgJzIuNeOAgeW6n+awtOaOkuaUvumHjyjlkKgpJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUpIHsKICAgICAgICAgIC8vMTcgMTktMjAgMzgKICAgICAgICAgIE9iamVjdC5rZXlzKF90aGlzNi5kaXNhYmxlZExpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgICAgICBpZiAoa2V5ICE9ICdncm91cExpc3QnICYmIGtleSAhPSAnc3RvY2tMaXN0JykgewogICAgICAgICAgICAgIF90aGlzNi5kaXNhYmxlZExpc3Rba2V5XS5wdXNoKGkpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICAgIE9iamVjdC5rZXlzKF90aGlzNi5ub0lucHV0TGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgIGlmIChrZXkgIT0gJ2dyb3VwTGlzdCcgJiYga2V5ICE9ICdzdG9ja0xpc3QnKSB7CiAgICAgICAgICAgICAgX3RoaXM2Lm5vSW5wdXRMaXN0W2tleV0ucHVzaChpKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CgogICAgICAgICAgX3RoaXM2LnJlcXVpcmVJbnB1dExpc3QuZ3JvdXBMaXN0LnB1c2goaSk7CgogICAgICAgICAgX3RoaXM2LnJlcXVpcmVJbnB1dExpc3Quc3RvY2tMaXN0LnB1c2goaSk7CiAgICAgICAgfSBlbHNlIGlmICgnMS4zLjLjgIHpnZ7nlJ/kuqfnlKjmiL/ogJfnlLXph48o5Y2D55Om5pe2KScgPT0gX3RoaXM2LnRhYmxlRGF0YVtpXS5pbmRpY2F0b3JOYW1lKSB7CiAgICAgICAgICAvLzE4CiAgICAgICAgICBPYmplY3Qua2V5cyhfdGhpczYuZGlzYWJsZWRMaXN0KS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICAgICAgcmV0dXJuIF90aGlzNi5kaXNhYmxlZExpc3Rba2V5XS5wdXNoKGkpOwogICAgICAgICAgfSk7CgogICAgICAgICAgX3RoaXM2LmZvcm11bGFMaXN0Lmdyb3VwTGlzdC5wdXNoKGkpOwoKICAgICAgICAgIF90aGlzNi5mb3JtdWxhTGlzdC5zdG9ja0xpc3QucHVzaChpKTsKCiAgICAgICAgICBPYmplY3Qua2V5cyhfdGhpczYubm9JbnB1dExpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgICAgICBpZiAoa2V5ICE9ICdncm91cExpc3QnICYmIGtleSAhPSAnc3RvY2tMaXN0JykgewogICAgICAgICAgICAgIF90aGlzNi5ub0lucHV0TGlzdFtrZXldLnB1c2goaSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSBpZiAoJzEuMy4z44CBIOWPr+WGjeeUn+iDvea6kOS9v+eUqOmHjyjljYPnk6bml7Yp77yI5LuF6ZmQ5aGr5YaZ6Ieq5Y+R6Ieq55So57u/55S177yJJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUgfHwgJzEuNOOAgeWOn+ayuSjlkKgpJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUpIHsKICAgICAgICAgIC8vMjEgMjIKICAgICAgICAgIE9iamVjdC5rZXlzKF90aGlzNi5yZXF1aXJlSW5wdXRMaXN0KS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICAgICAgcmV0dXJuIF90aGlzNi5yZXF1aXJlSW5wdXRMaXN0W2tleV0ucHVzaChpKTsKICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSBpZiAoJzEuNeOAgeaxveayuea2iOiAl+mHjyjljYcpJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUpIHsKICAgICAgICAgIC8vMjMKICAgICAgICAgIE9iamVjdC5rZXlzKF90aGlzNi5kaXNhYmxlZExpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgICAgICBpZiAoa2V5ICE9ICdtb2JpbGVMaXN0JykgewogICAgICAgICAgICAgIF90aGlzNi5kaXNhYmxlZExpc3Rba2V5XS5wdXNoKGkpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICAgIE9iamVjdC5rZXlzKF90aGlzNi5mb3JtdWxhTGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgIGlmIChrZXkgIT0gJ21vYmlsZUxpc3QnKSB7CiAgICAgICAgICAgICAgX3RoaXM2LmZvcm11bGFMaXN0W2tleV0ucHVzaChpKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CgogICAgICAgICAgX3RoaXM2LnJlcXVpcmVJbnB1dExpc3QubW9iaWxlTGlzdC5wdXNoKGkpOwogICAgICAgIH0gZWxzZSBpZiAoJzEuNS4x44CB5YW25Lit77ya56e75Yqo5rqQ77yI5Y2H77yJJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUgfHwgJzEuNS4y44CB5YW25Lit77ya5Zu65a6a5rqQ77yI5Y2H77yJJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUgfHwgJzEuNy4x44CB5YW25Lit77ya56e75Yqo5rqQ77yI5Y2H77yJJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUgfHwgJzEuNy4y44CB5YW25Lit77ya5Zu65a6a5rqQ77yI5Y2H77yJJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUpIHsKICAgICAgICAgIC8vMjQgMjUgMjggMjkKICAgICAgICAgIF90aGlzNi5kaXNhYmxlZExpc3QubW9iaWxlTGlzdC5wdXNoKGkpOwoKICAgICAgICAgIF90aGlzNi5ub0lucHV0TGlzdC5tb2JpbGVMaXN0LnB1c2goaSk7CgogICAgICAgICAgT2JqZWN0LmtleXMoX3RoaXM2LnJlcXVpcmVJbnB1dExpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgICAgICBpZiAoa2V5ICE9ICdtb2JpbGVMaXN0JykgewogICAgICAgICAgICAgIF90aGlzNi5yZXF1aXJlSW5wdXRMaXN0W2tleV0ucHVzaChpKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIGlmICgnMS4244CB54Wk5rK5KOWNhyknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMS4444CB54eD5paZ5rK5KOWNhyknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMS4544CB5ray5YyW55+z5rK55rCUKOWQqO+8iScgPT0gX3RoaXM2LnRhYmxlRGF0YVtpXS5pbmRpY2F0b3JOYW1lIHx8ICcyLjDjgIHlpKnnhLbmsJTmtojogJfph48o56uL5pa557GzKScgPT0gX3RoaXM2LnRhYmxlRGF0YVtpXS5pbmRpY2F0b3JOYW1lIHx8ICcyLjHjgIHng63lipso5Y2B5Lq/54SmKScgPT0gX3RoaXM2LnRhYmxlRGF0YVtpXS5pbmRpY2F0b3JOYW1lIHx8ICcyLjLjgIEg5YW25LuW6IO95rqQKOWQqOagh+WHhueFpCknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMi4z44CB5paw5rC055So6YePKOWQqCknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMi4044CB6IqC6IO96YePKOWQqOagh+WHhueFpCknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMi40LjHjgIHlhbbkuK3vvJroioLnlLXph48o5Y2D55Om5pe2KScgPT0gX3RoaXM2LnRhYmxlRGF0YVtpXS5pbmRpY2F0b3JOYW1lKSB7CiAgICAgICAgICAvLzI2ICAzMC0zNwogICAgICAgICAgT2JqZWN0LmtleXMoX3RoaXM2LnJlcXVpcmVJbnB1dExpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgICAgICByZXR1cm4gX3RoaXM2LnJlcXVpcmVJbnB1dExpc3Rba2V5XS5wdXNoKGkpOwogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIGlmICgnMS4344CB5p+05rK55raI6ICX6YePKOWNhyknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSkgewogICAgICAgICAgLy8yNwogICAgICAgICAgT2JqZWN0LmtleXMoX3RoaXM2LmRpc2FibGVkTGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgIGlmIChrZXkgIT0gJ21vYmlsZUxpc3QnKSB7CiAgICAgICAgICAgICAgX3RoaXM2LmRpc2FibGVkTGlzdFtrZXldLnB1c2goaSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgICAgT2JqZWN0LmtleXMoX3RoaXM2LmZvcm11bGFMaXN0KS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICAgICAgaWYgKGtleSAhPSAnbW9iaWxlTGlzdCcpIHsKICAgICAgICAgICAgICBfdGhpczYuZm9ybXVsYUxpc3Rba2V5XS5wdXNoKGkpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKCiAgICAgICAgICBfdGhpczYucmVxdWlyZUlucHV0TGlzdC5tb2JpbGVMaXN0LnB1c2goaSk7CiAgICAgICAgfSBlbHNlIGlmICgnMi4244CB5LiA6Iis5Zu65L2T5bqf54mp5Lqn55Sf6YePKOWQqCknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMi4344CB5LiA6Iis5Zu65L2T5bqf54mp57u85ZCI5Yip55So6YePKOWQqCknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMi4444CB57u85ZCI5Yip55So5b6A5bm06LSu5a2Y6YePKOWQqCknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMi4544CB5Y2x6Zmp5bqf54mp5Lqn55Sf6YePKOWQqCknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMy4w44CB5Y2x6Zmp5bqf54mp5aSE572u6YePKOWQqCknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMy4x44CB5aSE572u5b6A5bm06LSu5a2Y6YePKOWQqCknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMy4y44CB5Zyf5aOk5rGh5p+T5rK755CG6Z2i56evKOWFrOmhtyknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMy4z44CB5Zyf5aOk5rGh5p+T6ZyA6KaB5rK755CG6Z2i56evKOWFrOmhtyknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMy4044CB55+/5bGx77yI5oiW55Sf5oCB77yJ5L+u5aSN5rK755CG6Z2i56evKOWFrOmhtyknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMy4144CB55+/5bGx77yI5oiW55Sf5oCB77yJ6ZyA6KaB5L+u5aSN5rK755CG6Z2i56evKOWFrOmhtyknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMy4244CB5bqf5rCU5rK755CG6K6+5pa95pWwKOWllyknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnMy4344CB5bqf5rCU5rK755CG6K6+5pa95aSE55CG6IO95YqbKOeri+aWueexsy/mnIgpJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUgfHwgJzMuOOOAgeW6n+awtOayu+eQhuiuvuaWveaVsCjlpZcpJyA9PSBfdGhpczYudGFibGVEYXRhW2ldLmluZGljYXRvck5hbWUgfHwgJzMuOeOAgeW6n+awtOayu+eQhuiuvuaWveWkhOeQhuiDveWKmyjlkKgv5pyIKScgPT0gX3RoaXM2LnRhYmxlRGF0YVtpXS5pbmRpY2F0b3JOYW1lIHx8ICc0LjDjgIHnlJ/mgIHnjq/looPmsaHmn5PmupAo5LiqKScgPT0gX3RoaXM2LnRhYmxlRGF0YVtpXS5pbmRpY2F0b3JOYW1lIHx8ICc0LjHjgIHnlJ/mgIHnjq/looPpo47pmanngrnvvIjkuKrvvIknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnNC4y44CB6IqC6IO95oqV5YWlKOWFgyknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSB8fCAnNC4z44CB546v5L+d5oqV5YWlKOWFgyknID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSkgewogICAgICAgICAgLy8zOS01NgogICAgICAgICAgT2JqZWN0LmtleXMoX3RoaXM2LmRpc2FibGVkTGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgIGlmIChrZXkgIT0gJ2dyb3VwTGlzdCcgJiYga2V5ICE9ICdzdG9ja0xpc3QnKSB7CiAgICAgICAgICAgICAgX3RoaXM2LmRpc2FibGVkTGlzdFtrZXldLnB1c2goaSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgICAgT2JqZWN0LmtleXMoX3RoaXM2Lm5vSW5wdXRMaXN0KS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICAgICAgaWYgKGtleSAhPSAnZ3JvdXBMaXN0JyAmJiBrZXkgIT0gJ3N0b2NrTGlzdCcpIHsKICAgICAgICAgICAgICBfdGhpczYubm9JbnB1dExpc3Rba2V5XS5wdXNoKGkpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICAgIE9iamVjdC5rZXlzKF90aGlzNi5ub1JlcXVpcmVJbnB1dExpc3QpLmZvckVhY2goZnVuY3Rpb24gKGtleSkgewogICAgICAgICAgICByZXR1cm4gX3RoaXM2Lm5vUmVxdWlyZUlucHV0TGlzdFtrZXldLnB1c2goaSk7CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgaWYgKCc0LjTjgIHmlbDmja7kuK3lv4PmoIflh4bmnLrmnrbmlbDph48nID09IF90aGlzNi50YWJsZURhdGFbaV0uaW5kaWNhdG9yTmFtZSkgewogICAgICAgICAgLy81NwogICAgICAgICAgT2JqZWN0LmtleXMoX3RoaXM2LmRpc2FibGVkTGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgIGlmIChrZXkgIT0gJ2xhcmdlTGlzdCcgJiYga2V5ICE9ICdtZWRpdW1MaXN0JykgewogICAgICAgICAgICAgIF90aGlzNi5kaXNhYmxlZExpc3Rba2V5XS5wdXNoKGkpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICAgIE9iamVjdC5rZXlzKF90aGlzNi5ub0lucHV0TGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgIGlmIChrZXkgIT0gJ2xhcmdlTGlzdCcgJiYga2V5ICE9ICdtZWRpdW1MaXN0JykgewogICAgICAgICAgICAgIF90aGlzNi5ub0lucHV0TGlzdFtrZXldLnB1c2goaSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgICAgT2JqZWN0LmtleXMoX3RoaXM2Lm5vUmVxdWlyZUlucHV0TGlzdCkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgICAgIHJldHVybiBfdGhpczYubm9SZXF1aXJlSW5wdXRMaXN0W2tleV0ucHVzaChpKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfTsKCiAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgdGhpcy50YWJsZURhdGEubGVuZ3RoOyBpKyspIHsKICAgICAgICBfbG9vcChpKTsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZUltcG9ydEV4Y2VsOiBmdW5jdGlvbiBoYW5kbGVJbXBvcnRFeGNlbChmaWxlLCBmaWxlTGlzdCkgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKCiAgICAgIHRyeSB7CiAgICAgICAgdmFyIHRhYmxlRmllbGQgPSBbJ+S4iuaKpeaMh+aghycsICfpm4blm6InLCAn6IKh5Lu9JywgJ+Wkp+WeiycsICfkuK3lsI/lnosnLCAn56e75Yqo5Lia5YqhJ107CiAgICAgICAgdmFyIG0gPSB0aGlzOwogICAgICAgIGltcG9ydEV4Y2VsKGZpbGUsIHRhYmxlRmllbGQpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgaWYgKHJlcy5sZW5ndGggPiBfdGhpczcudGFibGVEYXRhLmxlbmd0aCkgewogICAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IF90aGlzNy50YWJsZURhdGEubGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgICBpZiAoX3RoaXM3LnRhYmxlRGF0YVtpXS5pbmRpY2F0b3JOYW1lID09IHJlc1tpICsgMV1bIuS4iuaKpeaMh+aghyJdLnRyaW0oKSkgewogICAgICAgICAgICAgICAgaWYgKF90aGlzNy5jaGVja0V4Y2VsTGluZURhdGEocmVzW2kgKyAxXSwgaSkgPT0gdHJ1ZSkgewogICAgICAgICAgICAgICAgICBfdGhpczcudGFibGVEYXRhW2ldLmdyb3VwRGF0YSA9IHJlc1tpICsgMV1bJ+mbhuWboiddLnRvU3RyaW5nKCk7CiAgICAgICAgICAgICAgICAgIF90aGlzNy50YWJsZURhdGFbaV0uc3RvY2tEYXRhID0gcmVzW2kgKyAxXVsn6IKh5Lu9J10udG9TdHJpbmcoKTsKICAgICAgICAgICAgICAgICAgX3RoaXM3LnRhYmxlRGF0YVtpXS5sYXJnZURhdGEgPSByZXNbaSArIDFdWyflpKflnosnXS50b1N0cmluZygpOwogICAgICAgICAgICAgICAgICBfdGhpczcudGFibGVEYXRhW2ldLm1lZGl1bURhdGEgPSByZXNbaSArIDFdWyfkuK3lsI/lnosnXS50b1N0cmluZygpOwogICAgICAgICAgICAgICAgICBfdGhpczcudGFibGVEYXRhW2ldLm1vYmlsZURhdGEgPSByZXNbaSArIDFdWyfnp7vliqjkuJrliqEnXS50b1N0cmluZygpOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgbS5hbGVydEVycm9yKCLnrKwiICsgTWF0aC5mbG9vcihpICsgMykgKyAi6KGM5pyJ6Z2e5rOV5pWw5o2u5pWw5o2u77yM6K+35qOA5p+l5paH5qGj77yBIik7CiAgICAgICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgbS5hbGVydEVycm9yKCLnrKwiICsgTWF0aC5mbG9vcihpICsgMykgKyAi6KGM5LiK5oql5oyH5qCH5ZCN56ew6ZSZ6K+v77yM6K+35Y+C6ICD5aGr5oql5qih5p2/77yBIik7CiAgICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBtLmFsZXJ0RXJyb3IoIuaVsOaNruihjOaVsOi/h+Wwke+8jOivt+WPguiAg+Whq+aKpeaooeadv++8gSIpOwogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0gY2F0Y2ggKGV4Y2VwdGlvbikgewogICAgICAgIC8v5oqT5L2PdGhyb3fmipvlh7rnmoTplJnor68KICAgICAgICB0aGlzLmFsZXJ0RXJyb3IoIuaVsOaNruagvOW8j+mUmeivryIpOwogICAgICB9CiAgICB9LAogICAgY2hlY2tFeGNlbExpbmVEYXRhOiBmdW5jdGlvbiBjaGVja0V4Y2VsTGluZURhdGEoZGF0YSwgbGluZSkgewogICAgICB2YXIgcmV0ID0gdHJ1ZTsKCiAgICAgIGlmIChkYXRhWyfpm4blm6InXSA9PSB1bmRlZmluZWQgfHwgZGF0YVsn6ZuG5ZuiJ10gPT0gbnVsbCB8fCBkYXRhWyfogqHku70nXSA9PSB1bmRlZmluZWQgfHwgZGF0YVsn6IKh5Lu9J10gPT0gbnVsbCB8fCBkYXRhWyflpKflnosnXSA9PSB1bmRlZmluZWQgfHwgZGF0YVsn5aSn5Z6LJ10gPT0gbnVsbCB8fCBkYXRhWyfkuK3lsI/lnosnXSA9PSB1bmRlZmluZWQgfHwgZGF0YVsn5Lit5bCP5Z6LJ10gPT0gbnVsbCB8fCBkYXRhWyfnp7vliqjkuJrliqEnXSA9PSB1bmRlZmluZWQgfHwgZGF0YVsn56e75Yqo5Lia5YqhJ10gPT0gbnVsbCkgewogICAgICAgIHJldCA9IGZhbHNlOwogICAgICAgIHJldHVybiByZXQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKHRoaXMubm9JbnB1dExpc3QuZ3JvdXBMaXN0LmluZGV4T2YobGluZSkgPT0gLTEpIHsKICAgICAgICAgIHZhciBncm91cERhdGEgPSBwYXJzZUZsb2F0KGRhdGFbJ+mbhuWboiddKTsKCiAgICAgICAgICBpZiAoaXNOYU4oZ3JvdXBEYXRhKSB8fCBncm91cERhdGEgPT0gbnVsbCB8fCBncm91cERhdGEgPCAwKSB7CiAgICAgICAgICAgIC8vIGlmIChpc05hTihncm91cERhdGEpIHx8IGdyb3VwRGF0YSA9PSBudWxsKSB7CiAgICAgICAgICAgIHJldCA9IGZhbHNlOwogICAgICAgICAgICByZXR1cm4gcmV0OwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgaWYgKHRoaXMubm9JbnB1dExpc3Quc3RvY2tMaXN0LmluZGV4T2YobGluZSkgPT0gLTEpIHsKICAgICAgICAgIHZhciBzdG9ja0RhdGEgPSBwYXJzZUZsb2F0KGRhdGFbJ+iCoeS7vSddKTsKCiAgICAgICAgICBpZiAoaXNOYU4oc3RvY2tEYXRhKSB8fCBzdG9ja0RhdGEgPT0gbnVsbCB8fCBzdG9ja0RhdGEgPCAwKSB7CiAgICAgICAgICAgIC8vIGlmIChpc05hTihzdG9ja0RhdGEpIHx8IHN0b2NrRGF0YSA9PSBudWxsKSB7CiAgICAgICAgICAgIHJldCA9IGZhbHNlOwogICAgICAgICAgICByZXR1cm4gcmV0OwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgaWYgKHRoaXMubm9JbnB1dExpc3QubGFyZ2VMaXN0LmluZGV4T2YobGluZSkgPT0gLTEpIHsKICAgICAgICAgIHZhciBsYXJnZURhdGEgPSBwYXJzZUZsb2F0KGRhdGFbJ+Wkp+WeiyddKTsKCiAgICAgICAgICBpZiAoaXNOYU4obGFyZ2VEYXRhKSB8fCBsYXJnZURhdGEgPT0gbnVsbCB8fCBsYXJnZURhdGEgPCAwKSB7CiAgICAgICAgICAgIC8vIGlmIChpc05hTihsYXJnZURhdGEpIHx8IGxhcmdlRGF0YSA9PSBudWxsKSB7CiAgICAgICAgICAgIHJldCA9IGZhbHNlOwogICAgICAgICAgICByZXR1cm4gcmV0OwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgaWYgKHRoaXMubm9JbnB1dExpc3QubWVkaXVtTGlzdC5pbmRleE9mKGxpbmUpID09IC0xKSB7CiAgICAgICAgICB2YXIgbWVkaXVtRGF0YSA9IHBhcnNlRmxvYXQoZGF0YVsn5Lit5bCP5Z6LJ10pOwoKICAgICAgICAgIGlmIChpc05hTihtZWRpdW1EYXRhKSB8fCBtZWRpdW1EYXRhID09IG51bGwgfHwgbWVkaXVtRGF0YSA8IDApIHsKICAgICAgICAgICAgLy8gaWYgKGlzTmFOKG1lZGl1bURhdGEpIHx8IG1lZGl1bURhdGEgPT0gbnVsbCkgewogICAgICAgICAgICByZXQgPSBmYWxzZTsKICAgICAgICAgICAgcmV0dXJuIHJldDsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIGlmICh0aGlzLm5vSW5wdXRMaXN0Lm1vYmlsZUxpc3QuaW5kZXhPZihsaW5lKSA9PSAtMSkgewogICAgICAgICAgdmFyIG1vYmlsZURhdGEgPSBwYXJzZUZsb2F0KGRhdGFbJ+enu+WKqOS4muWKoSddKTsKCiAgICAgICAgICBpZiAoaXNOYU4obW9iaWxlRGF0YSkgfHwgbW9iaWxlRGF0YSA9PSBudWxsIHx8IG1vYmlsZURhdGEgPCAwKSB7CiAgICAgICAgICAgIC8vIGlmIChpc05hTihtb2JpbGVEYXRhKSB8fCBtb2JpbGVEYXRhID09IG51bGwpIHsKICAgICAgICAgICAgcmV0ID0gZmFsc2U7CiAgICAgICAgICAgIHJldHVybiByZXQ7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICByZXR1cm4gcmV0OwogICAgfSwKICAgIGNlbGxUb051bWJlcjogZnVuY3Rpb24gY2VsbFRvTnVtYmVyKGNlbGwpIHsKICAgICAgdmFyIG51bSA9IDA7CgogICAgICBpZiAoY2VsbCA9PSB1bmRlZmluZWQgfHwgY2VsbCA9PSBudWxsKSB7CiAgICAgICAgbnVtID0gMDsKICAgICAgfSBlbHNlIHsKICAgICAgICBudW0gPSBNYXRoLnJvdW5kKHBhcnNlRmxvYXQoY2VsbCkgKiAxMDAwMDAwKSAvIDEwMDAwMDA7CgogICAgICAgIGlmIChpc05hTihudW0pIHx8IG51bSA9PSBudWxsKSB7CiAgICAgICAgICBudW0gPSAwOwogICAgICAgIH0KICAgICAgfQoKICAgICAgcmV0dXJuIG51bTsKICAgIH0sCiAgICAvLyDliKTmlq3kuKTkuKrlgLzmmK/lkKbnm7jnrYkKICAgIGFyZUZsb2F0c0VxdWFsOiBmdW5jdGlvbiBhcmVGbG9hdHNFcXVhbChmbG9hdDEsIGZsb2F0MikgewogICAgICAvLyDlrprkuYnkuIDkuKrpnZ7luLjlsI/nmoTor6/lt67pmIjlgLwKICAgICAgdmFyIGVwc2lsb24gPSAwLjAwMDAwMTsgLy8g6YCC5b2T6LCD5pW05q2k5YC85Lul5ruh6Laz5L2g55qE6ZyA5rGCCiAgICAgIC8vIOiuoeeul+S4pOS4qua1rueCueaVsOeahOW3ruWAvAoKICAgICAgdmFyIGRpZmZlcmVuY2UgPSBNYXRoLmFicyhmbG9hdDEgLSBmbG9hdDIpOyAvLyDlpoLmnpzlt67lgLzlsI/kuo7or6/lt67pmIjlgLzvvIzlsLHorqTkuLrkuKTkuKrmta7ngrnmlbDnm7jnrYkKCiAgICAgIHJldHVybiBkaWZmZXJlbmNlIDwgZXBzaWxvbjsKICAgIH0sCiAgICBnZXRPYmo6IGZ1bmN0aW9uIGdldE9iaihpdGVtKSB7CiAgICAgIGl0ZW0uc2hvdyA9ICFpdGVtLnNob3c7CgogICAgICBpZiAoaXRlbS5vcGVyYXRlVHlwZSA9PSAxKSB7CiAgICAgICAgdGhpcy5nZXRMaXN0KGl0ZW0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZ2V0RW5lcmd5VXBkYXRlUmVjb3JkTGlzdChpdGVtKTsKICAgICAgfSAvLyBpZihpdGVtLmlzU2hvdykgewogICAgICAvLyAgIGl0ZW0ubmFtZXMgPSAn5pS26LW35pWw5o2uJzsKICAgICAgLy8gfWVsc2UgewogICAgICAvLyAgIGl0ZW0ubmFtZXMgPSAn5p+l55yL5pWw5o2uJzsKICAgICAgLy8gfQoKICAgIH0sCiAgICBmb3JtYXREaXNwbGF5RGF0YTogZnVuY3Rpb24gZm9ybWF0RGlzcGxheURhdGEodmFsdWUpIHsKICAgICAgdmFyIGRhdGEgPSBwYXJzZUZsb2F0KHZhbHVlKTsKCiAgICAgIGlmIChpc05hTih2YWx1ZSkpIHsKICAgICAgICByZXR1cm4gdmFsdWU7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuIGRhdGEudG9GaXhlZCgyKTsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "sources": ["modifyReport.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA;AACA,OAAA,OAAA,MAAA,4BAAA,C,CACA;AACA;AACA;;AACA,SAAA,UAAA,EAAA,gBAAA,EAAA,yBAAA,EAAA,oBAAA,QAAA,+BAAA;AACA,OAAA,qBAAA,MAAA,yBAAA;AACA,eAAA;AACA,EAAA,UAAA,EAAA;AACA;AACA,IAAA,OAAA,EAAA,OAFA;AAGA,IAAA,qBAAA,EAAA;AAHA,GADA;AAMA,EAAA,IANA,kBAMA;AAAA;;AACA,WAAA;AACA,MAAA,OAAA,EAAA,MADA;AAEA,MAAA,YAAA,EAAA,KAFA;AAGA,MAAA,MAAA,EAAA,KAHA;AAIA,MAAA,IAAA,EAAA,EAJA;AAKA,MAAA,WAAA,EAAA;AACA,QAAA,MAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA,MAHA;AAIA,QAAA,UAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA,IALA;AAMA,QAAA,SAAA,EAAA,QANA;AAOA,QAAA,KAAA,EAAA,QAPA;AAQA,QAAA,UAAA,EAAA,KARA;AASA,QAAA,aAAA,EAAA,KATA;AAUA,QAAA,cAAA,EAAA,CAVA;AAWA,QAAA,UAAA,EAAA,MAXA;AAYA,QAAA,IAAA,EAAA,IAZA;AAaA,QAAA,MAAA,EAAA,KAbA;AAcA,QAAA,OAAA,EAAA,KAdA;AAeA,QAAA,MAAA,EAAA,KAfA;AAgBA,QAAA,SAAA,EAAA,KAhBA;AAiBA,QAAA,SAAA,EAAA,KAjBA;AAkBA,QAAA,QAAA,EAAA,KAlBA;AAmBA,QAAA,IAAA,EAAA,KAnBA;AAoBA,QAAA,WAAA,EAAA,GApBA;AAqBA,QAAA,kBAAA,EAAA,QArBA;AAsBA,QAAA,iBAAA,EAAA,iBAtBA;AAuBA,QAAA,UAAA,EAAA,GAvBA;AAwBA,QAAA,MAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,eAFA;AAGA,UAAA,UAAA,EAAA,IAHA;AAIA,UAAA,KAAA,EAAA,GAJA;AAKA,UAAA,KAAA,EAAA;AALA,SADA,EAQA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,SAAA,EAAA,mBAAA,GAAA,EAAA,KAAA,EAAA,KAAA,EAAA;AACA,mBAAA,KAAA,CAAA,iBAAA,CAAA,KAAA,CAAA;AACA,WALA;AAMA,UAAA,IAAA,EAAA,IANA;AAOA,UAAA,IAAA,EAAA,IAPA;AAQA,UAAA,UAAA,EAAA;AARA,SARA,EAkBA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,SAAA,EAAA,mBAAA,GAAA,EAAA,KAAA,EAAA,KAAA,EAAA;AACA,mBAAA,KAAA,CAAA,iBAAA,CAAA,KAAA,CAAA;AACA,WALA;AAMA,UAAA,IAAA,EAAA,IANA;AAOA,UAAA,IAAA,EAAA,IAPA;AAQA,UAAA,UAAA,EAAA;AARA,SAlBA,EA4BA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,QAAA,EAAA,CAAA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,IAAA,EAAA,WAFA;AAGA,YAAA,SAAA,EAAA,mBAAA,GAAA,EAAA,KAAA,EAAA,KAAA,EAAA;AACA,qBAAA,KAAA,CAAA,iBAAA,CAAA,KAAA,CAAA;AACA,aALA;AAMA,YAAA,UAAA,EAAA,IANA;AAOA,YAAA,IAAA,EAAA,IAPA;AAQA,YAAA,IAAA,EAAA;AARA,WAAA,EASA;AACA,YAAA,KAAA,EAAA,KADA;AAEA,YAAA,IAAA,EAAA,YAFA;AAGA,YAAA,SAAA,EAAA,mBAAA,GAAA,EAAA,KAAA,EAAA,KAAA,EAAA;AACA,qBAAA,KAAA,CAAA,iBAAA,CAAA,KAAA,CAAA;AACA,aALA;AAMA,YAAA,IAAA,EAAA,IANA;AAOA,YAAA,IAAA,EAAA,IAPA;AAQA,YAAA,UAAA,EAAA;AARA,WATA;AAFA,SA5BA,EAkDA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,YAFA;AAGA,UAAA,SAAA,EAAA,mBAAA,GAAA,EAAA,KAAA,EAAA,KAAA,EAAA;AACA,mBAAA,KAAA,CAAA,iBAAA,CAAA,KAAA,CAAA;AACA,WALA;AAMA,UAAA,IAAA,EAAA,IANA;AAOA,UAAA,IAAA,EAAA,IAPA;AAQA,UAAA,UAAA,EAAA;AARA,SAlDA;AAxBA,OALA;AA2FA,MAAA,WAAA,EAAA,SA3FA;AA4FA,MAAA,SAAA,EAAA,EA5FA;AA6FA;AACA,MAAA,YAAA,EAAA,EA9FA;AA+FA;AACA,MAAA,UAAA,EAAA,EAhGA;AAiGA,MAAA,UAAA,EAAA,EAjGA;AAkGA,MAAA,YAAA,EAAA;AACA,QAAA,SAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CADA;AAEA,QAAA,SAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAFA;AAGA,QAAA,SAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAHA;AAIA,QAAA,UAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAJA;AAKA,QAAA,UAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AALA,OAlGA;AAyGA,MAAA,WAAA,EAAA;AACA,QAAA,SAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CADA;AAEA,QAAA,SAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAFA;AAGA,QAAA,SAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,CAHA;AAIA,QAAA,UAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAJA;AAKA,QAAA,UAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA;AALA,OAzGA;AAgHA,MAAA,WAAA,EAAA;AACA,QAAA,SAAA,EAAA,CAAA,EAAA,CADA;AAEA,QAAA,SAAA,EAAA,CAAA,EAAA,CAFA;AAGA,QAAA,SAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAHA;AAIA,QAAA,UAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAJA;AAKA,QAAA,UAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AALA,OAhHA;AAuHA,MAAA,gBAAA,EAAA;AACA,QAAA,SAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CADA;AAEA,QAAA,SAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAFA;AAGA,QAAA,SAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAHA;AAIA,QAAA,UAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAJA;AAKA,QAAA,UAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AALA,OAvHA;AA8HA,MAAA,kBAAA,EAAA;AACA,QAAA,SAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CADA;AAEA,QAAA,SAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AAFA,OA9HA;AAkIA,MAAA,cAAA,EAAA;AACA,QAAA,SAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,EAFA;AAGA,QAAA,SAAA,EAAA,EAHA;AAIA,QAAA,UAAA,EAAA,EAJA;AAKA,QAAA,UAAA,EAAA;AALA,OAlIA;AAyIA,MAAA,QAAA,EAAA;AACA,QAAA,UAAA,EAAA,SADA;AAEA,QAAA,SAAA,EAAA;AAFA;AAzIA,KAAA;AA8IA,GArJA;AAsJA,EAAA,QAAA,EAAA,EAtJA;AAyJA,EAAA,OAzJA,qBAyJA;AACA,QAAA,KAAA,MAAA,CAAA,KAAA,EAAA;AAEA,WAAA,QAAA,CAAA,UAAA,GAAA,KAAA,MAAA,CAAA,KAAA,CAAA,UAAA;AACA,WAAA,WAAA,GAAA,KAAA,MAAA,CAAA,KAAA,CAAA,WAAA;AACA,WAAA,QAAA,CAAA,SAAA,GAAA,KAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAJA,CAKA;;AACA,WAAA,gBAAA;AACA;AACA,GAlKA;AAmKA,EAAA,OAAA,EAAA;AACA,IAAA,SADA,2BACA;AAAA,UAAA,GAAA,QAAA,GAAA;AAAA,UAAA,MAAA,QAAA,MAAA;AAAA,UAAA,QAAA,QAAA,QAAA;AAAA,UAAA,WAAA,QAAA,WAAA;;AACA,UAAA,WAAA,KAAA,CAAA,IAAA,GAAA,CAAA,SAAA,EAAA;AACA,eAAA,YAAA;AACA;;AACA,UAAA,WAAA,KAAA,CAAA,IAAA,GAAA,CAAA,SAAA,EAAA;AACA,eAAA,YAAA;AACA;;AACA,UAAA,WAAA,KAAA,CAAA,IAAA,GAAA,CAAA,SAAA,EAAA;AACA,eAAA,YAAA;AACA;;AACA,UAAA,WAAA,KAAA,CAAA,IAAA,GAAA,CAAA,UAAA,EAAA;AACA,eAAA,YAAA;AACA;;AACA,UAAA,WAAA,KAAA,CAAA,IAAA,GAAA,CAAA,UAAA,EAAA;AACA,eAAA,YAAA;AACA;AACA,KAjBA;AAkBA,IAAA,gBAlBA,8BAkBA;AAAA;;AACA,MAAA,yBAAA,CAAA,MAAA,CAAA,MAAA,CAAA;AAAA,QAAA,SAAA,EAAA,KAAA,QAAA,CAAA,SAAA;AAAA,QAAA,UAAA,EAAA,KAAA,QAAA,CAAA;AAAA,OAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,CAAA,KAAA,GAAA,MAAA;AACA,WAFA;AAGA;AACA,OAPA;AAQA,KA3BA;AA6BA;AACA,IAAA,UA9BA,sBA8BA,KA9BA,EA8BA;AACA,UAAA,KAAA,CAAA,GAAA,KAAA,GAAA,IAAA,KAAA,CAAA,GAAA,KAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,WAAA,GAAA,KAAA;AACA,eAAA,KAAA;AACA;;AACA,aAAA,IAAA;AACA,KApCA;AAqCA,IAAA,MArCA,oBAqCA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,wBADA;AAEA,QAAA,KAAA,EAAA;AACA,UAAA,UAAA,EAAA,KAAA,QAAA,CAAA;AADA;AAFA,OAAA;AAMA,KA5CA;AA6CA;AACA,IAAA,OA9CA,mBA8CA,IA9CA,EA8CA;AAAA;;AACA,WAAA,YAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,KAAA,QAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,KAAA;AACA,UAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,UAAA,MAAA,CAAA,mBAAA;;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,eAAA,GAAA,GAAA,GAAA,IAAA,CAAA,QAAA,GAAA,GAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA,SAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA,SAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA,SAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA,UAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA,UAAA;AACA,WARA;AASA;AACA,OAfA;AAgBA,KAhEA;AAiEA;AACA,IAAA,yBAlEA,qCAkEA,IAlEA,EAkEA;AAAA;;AACA,WAAA,YAAA,GAAA,IAAA;AACA,WAAA,QAAA,CAAA,cAAA,GAAA,IAAA,CAAA,EAAA;AACA,MAAA,oBAAA,CAAA,KAAA,QAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,KAAA;AACA,UAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,UAAA,MAAA,CAAA,mBAAA;;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,eAAA,GAAA,GAAA,GAAA,IAAA,CAAA,QAAA,GAAA,GAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA,SAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA,SAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA,SAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA,UAAA;AACA,YAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,CAAA,UAAA;AACA,WARA;AASA;AACA,OAfA;AAgBA,KArFA;AAsFA,IAAA,cAtFA,4BAsFA;AAAA;;AACA,UAAA,KAAA,eAAA,MAAA,KAAA,EAAA;AACA,aAAA,UAAA,CAAA,SAAA;AACA;AACA;;AACA,UAAA,IAAA,GAAA,EAAA;AACA,UAAA,QAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,QAAA,GAAA,EAAA;AACA,QAAA,QAAA,CAAA,UAAA,GAAA,MAAA,CAAA,QAAA,CAAA,UAAA;AACA,QAAA,QAAA,CAAA,SAAA,GAAA,MAAA,CAAA,QAAA,CAAA,SAAA;AACA,QAAA,QAAA,CAAA,UAAA,GAAA,GAAA;AACA,QAAA,QAAA,CAAA,EAAA,GAAA,IAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,aAAA,GAAA,IAAA,CAAA,aAAA;AACA,QAAA,QAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,IAAA,IAAA,IAAA,IAAA,CAAA,SAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,CAAA,SAAA;AACA,QAAA,QAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,IAAA,IAAA,IAAA,IAAA,CAAA,SAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,CAAA,SAAA;AACA,QAAA,QAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,IAAA,IAAA,IAAA,IAAA,CAAA,SAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,CAAA,SAAA;AACA,QAAA,QAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA,IAAA,IAAA,IAAA,IAAA,CAAA,UAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,CAAA,UAAA;AACA,QAAA,QAAA,CAAA,UAAA,GAAA,IAAA,CAAA,UAAA,IAAA,IAAA,IAAA,IAAA,CAAA,UAAA,IAAA,GAAA,GAAA,IAAA,GAAA,IAAA,CAAA,UAAA;AACA,QAAA,QAAA,CAAA,IAAA,CAAA,QAAA;AACA,OAbA;AAcA,MAAA,IAAA,CAAA,SAAA,GAAA,KAAA,QAAA,CAAA,SAAA;AACA,MAAA,IAAA,CAAA,2BAAA,GAAA,QAAA;AACA,MAAA,IAAA,CAAA,gCAAA,GAAA,KAAA,UAAA;AACA,MAAA,gBAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,YAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,MAAA;AACA;AACA,OALA;AAMA,KApHA;AAqHA;AACA,IAAA,eAtHA,6BAsHA;AACA,UAAA,GAAA,GAAA,IAAA;AACA,WAAA,cAAA,CAAA,SAAA,GAAA,EAAA;AACA,WAAA,cAAA,CAAA,SAAA,GAAA,EAAA;AACA,WAAA,cAAA,CAAA,SAAA,GAAA,EAAA;AACA,WAAA,cAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,cAAA,CAAA,UAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,SAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,KAAA,SAAA,CAAA,CAAA,EAAA,SAAA,IAAA,SAAA,IACA,KAAA,SAAA,CAAA,CAAA,EAAA,SAAA,IAAA,IADA,IAEA,KAAA,SAAA,CAAA,CAAA,EAAA,SAAA,CAAA,MAAA,IAAA,CAFA,EAEA;AACA,cAAA,KAAA,gBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,CAAA,EAAA;AACA,iBAAA,cAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AACA,YAAA,GAAA,GAAA,KAAA;AACA,WAHA,MAGA;AACA,iBAAA,SAAA,CAAA,CAAA,EAAA,SAAA,GAAA,GAAA;AACA;AACA;;AACA,YAAA,KAAA,SAAA,CAAA,CAAA,EAAA,SAAA,IAAA,SAAA,IACA,KAAA,SAAA,CAAA,CAAA,EAAA,SAAA,IAAA,IADA,IAEA,KAAA,SAAA,CAAA,CAAA,EAAA,SAAA,CAAA,MAAA,IAAA,CAFA,EAEA;AACA,cAAA,KAAA,gBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,CAAA,EAAA;AACA,iBAAA,cAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AACA,YAAA,GAAA,GAAA,KAAA;AACA,WAHA,MAGA;AACA,iBAAA,SAAA,CAAA,CAAA,EAAA,SAAA,GAAA,GAAA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA;AACA;;AACA,YAAA,KAAA,SAAA,CAAA,CAAA,EAAA,SAAA,IAAA,SAAA,IACA,KAAA,SAAA,CAAA,CAAA,EAAA,SAAA,IAAA,IADA,IAEA,KAAA,SAAA,CAAA,CAAA,EAAA,SAAA,CAAA,MAAA,IAAA,CAFA,EAEA;AACA,eAAA,cAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA;AACA;;AACA,YAAA,KAAA,SAAA,CAAA,CAAA,EAAA,UAAA,IAAA,SAAA,IACA,KAAA,SAAA,CAAA,CAAA,EAAA,UAAA,IAAA,IADA,IAEA,KAAA,SAAA,CAAA,CAAA,EAAA,UAAA,CAAA,MAAA,IAAA,CAFA,EAEA;AACA,eAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA;AACA;;AACA,YAAA,KAAA,SAAA,CAAA,CAAA,EAAA,UAAA,IAAA,SAAA,IACA,KAAA,SAAA,CAAA,CAAA,EAAA,UAAA,IAAA,IADA,IAEA,KAAA,SAAA,CAAA,CAAA,EAAA,UAAA,CAAA,MAAA,IAAA,CAFA,EAEA;AACA,eAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA;AACA;AACA;;AACA,aAAA,GAAA;AACA,KAvKA;AAwKA;AACA,IAAA,eAzKA,2BAyKA,IAzKA,EAyKA,KAzKA,EAyKA,SAzKA,EAyKA,GAzKA,EAyKA,EAzKA,EAyKA;AACA,UAAA,OAAA,GAAA,KAAA,YAAA,CAAA,KAAA,EAAA,SAAA,CAAA,CADA,CAEA;;AACA,UAAA,KAAA,cAAA,CAAA,IAAA,EAAA,KAAA,YAAA,CAAA,KAAA,EAAA,SAAA,CAAA,CAAA,EAAA;AACA,YAAA,aAAA,GAAA,KAAA,UAAA,CAAA,MAAA,CAAA,UAAA,GAAA;AAAA,iBAAA,GAAA,CAAA,EAAA,KAAA,EAAA;AAAA,SAAA,CAAA;AACA,aAAA,UAAA,GAAA,aAAA;AACA,OAHA,MAGA;AACA;AACA,YAAA,MAAA,GAAA,KAAA,UAAA,CAAA,SAAA,CAAA,UAAA,GAAA;AAAA,iBAAA,GAAA,CAAA,EAAA,KAAA,EAAA;AAAA,SAAA,CAAA,CAFA,CAGA;;;AACA,YAAA,MAAA,IAAA,CAAA,EAAA;AACA;AACA,eAAA,UAAA,CAAA,MAAA,CAAA,MAAA,EAAA,CAAA;AACA;;AACA,aAAA,UAAA,CAAA,IAAA,CAAA;AAAA,UAAA,QAAA,EAAA,EAAA;AAAA,UAAA,GAAA,EAAA,GAAA;AAAA,UAAA,YAAA,EAAA,OAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AACA;AACA,KAzLA;AA2LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,SArWA,qBAqWA,KArWA,EAqWA;AACA,WAAA,SAAA,CAAA,CAAA,EAAA,KAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CACA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,CAAA,EAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA,CAAA,EAAA,WAAA,GACA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,CAAA,EAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA,CAAA,EAAA,WADA,GAEA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,CAAA,EAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA,CAAA,EAAA,WAAA,GAAA,IAFA,GAGA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA,EAAA,EAAA,WAHA,GAIA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA,EAAA,EAAA,WAAA,GAAA,IAJA,GAKA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA,EAAA,EAAA,WAAA,GAAA,IALA,GAMA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA,EAAA,EAAA,WAAA,GAAA,IANA,GAOA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA,EAAA,EAAA,WAAA,GAAA,IAPA,GAQA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA,EAAA,EAAA,WARA,GASA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA,EAAA,EAAA,WAAA,GAAA,IATA,GAUA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA,EAAA,EAAA,WAVA,GAWA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,EAAA,EAAA,KAAA,CAAA,CAXA,GAYA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA,EAAA,EAAA,WAAA,GAAA,IAZA,GAaA,KAAA,YAAA,CAAA,KAAA,SAAA,CAAA,EAAA,EAAA,KAAA,CAAA,CAdA,IAeA,OAfA,IAeA,OAfA,EAeA,QAfA,EAAA;AAgBA,KAtXA;AAuXA;AACA,IAAA,mBAxXA,iCAwXA;AAAA;;AACA,MAAA,MAAA,CAAA,IAAA,CAAA,KAAA,YAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,eAAA,MAAA,CAAA,YAAA,CAAA,GAAA,IAAA,EAAA;AAAA,OAAA;AACA,MAAA,MAAA,CAAA,IAAA,CAAA,KAAA,WAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,eAAA,MAAA,CAAA,WAAA,CAAA,GAAA,IAAA,EAAA;AAAA,OAAA;AACA,MAAA,MAAA,CAAA,IAAA,CAAA,KAAA,WAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,eAAA,MAAA,CAAA,WAAA,CAAA,GAAA,IAAA,EAAA;AAAA,OAAA;AACA,MAAA,MAAA,CAAA,IAAA,CAAA,KAAA,gBAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,eAAA,MAAA,CAAA,gBAAA,CAAA,GAAA,IAAA,EAAA;AAAA,OAAA;AACA,MAAA,MAAA,CAAA,IAAA,CAAA,KAAA,kBAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,eAAA,MAAA,CAAA,kBAAA,CAAA,GAAA,IAAA,EAAA;AAAA,OAAA;;AALA,iCAMA,CANA;AAOA,YAAA,mBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,mBAAA,MAAA,CAAA,YAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,WAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,mBAAA,MAAA,CAAA,WAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA;AACA,SAHA,MAGA,IAAA,gBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,mBAAA,MAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA;AACA,SAFA,MAEA,IAAA,qBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,EAAA;AACA,UAAA,MAAA,CAAA,YAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,YAAA,EAAA;AACA,cAAA,MAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;AAKA,SARA,MAQA,IAAA,eAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,mBAAA,MAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA;AACA,SAFA,MAEA,IAAA,qBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,mBAAA,MAAA,CAAA,YAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,WAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,mBAAA,MAAA,CAAA,WAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA;AACA,SAHA,MAGA,IAAA,wBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,mBAAA,MAAA,CAAA,YAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AACA,SAPA,MAOA,IAAA,6BAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,EAAA;AACA,UAAA,MAAA,CAAA,YAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,YAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,YAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,YAAA,EAAA;AACA,cAAA,MAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;AAKA,SAZA,MAYA,IAAA,2BAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,EAAA;AAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,mBAAA,MAAA,CAAA,YAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,WAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,WAAA,IAAA,GAAA,IAAA,YAAA,EAAA;AACA,cAAA,MAAA,CAAA,WAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;;AAKA,UAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AACA,SATA,MASA,IAAA,+CAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,IACA,gDAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aADA,IAEA,+CAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAFA,EAEA;AAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,WAAA,IAAA,GAAA,IAAA,WAAA,EAAA;AACA,cAAA,MAAA,CAAA,YAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;;AAKA,UAAA,MAAA,CAAA,WAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AACA,SAbA,MAaA,IAAA,6BAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,IACA,oCAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aADA,IAEA,kCAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAFA,IAGA,wCAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAHA,IAIA,wCAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAJA,IAKA,uCAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aALA,EAKA;AAAA;AACA,UAAA,MAAA,CAAA,YAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,YAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,YAAA,IAAA,GAAA,IAAA,YAAA,EAAA;AACA,cAAA,MAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;AAKA,SAfA,MAeA,IAAA,kCAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,IACA,6BAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aADA,IAEA,6BAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAFA,IAGA,kBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAHA,EAGA;AAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,WAAA,IAAA,GAAA,IAAA,WAAA,EAAA;AACA,cAAA,MAAA,CAAA,YAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;AAKA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,WAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,WAAA,IAAA,GAAA,IAAA,WAAA,EAAA;AACA,cAAA,MAAA,CAAA,WAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;;AAKA,UAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AACA,SAhBA,MAgBA,IAAA,yBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,EAAA;AAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,mBAAA,MAAA,CAAA,YAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,WAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,WAAA,IAAA,GAAA,IAAA,WAAA,EAAA;AACA,cAAA,MAAA,CAAA,WAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;AAKA,SATA,MASA,IAAA,sCAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,IACA,eAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aADA,EACA;AAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,mBAAA,MAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA;AACA,SAHA,MAGA,IAAA,kBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,EAAA;AAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,YAAA,EAAA;AACA,cAAA,MAAA,CAAA,YAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;AAKA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,WAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,YAAA,EAAA;AACA,cAAA,MAAA,CAAA,WAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;;AAKA,UAAA,MAAA,CAAA,gBAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AACA,SAZA,MAYA,IAAA,qBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,IACA,qBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aADA,IAEA,qBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAFA,IAGA,qBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAHA,EAGA;AAAA;AACA,UAAA,MAAA,CAAA,YAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,YAAA,EAAA;AACA,cAAA,MAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;AAKA,SAXA,MAWA,IAAA,eAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,IACA,gBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aADA,IAEA,kBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAFA,IAGA,qBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAHA,IAIA,iBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAJA,IAKA,qBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aALA,IAMA,iBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aANA,IAOA,mBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAPA,IAQA,uBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aARA,EAQA;AAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,mBAAA,MAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA;AACA,SAVA,MAUA,IAAA,kBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,EAAA;AAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,YAAA,EAAA;AACA,cAAA,MAAA,CAAA,YAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;AAKA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,WAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,YAAA,EAAA;AACA,cAAA,MAAA,CAAA,WAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;;AAKA,UAAA,MAAA,CAAA,gBAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AACA,SAZA,MAYA,IAAA,sBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,IACA,wBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aADA,IAEA,sBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAFA,IAGA,oBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAHA,IAIA,oBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAJA,IAKA,oBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aALA,IAMA,sBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aANA,IAOA,wBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAPA,IAQA,2BAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aARA,IASA,6BAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aATA,IAUA,oBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAVA,IAWA,2BAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAXA,IAYA,oBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAZA,IAaA,yBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAbA,IAcA,oBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAdA,IAeA,oBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAfA,IAgBA,iBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAhBA,IAiBA,iBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAjBA,EAiBA;AAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,WAAA,IAAA,GAAA,IAAA,WAAA,EAAA;AACA,cAAA,MAAA,CAAA,YAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;AAKA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,WAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,WAAA,IAAA,GAAA,IAAA,WAAA,EAAA;AACA,cAAA,MAAA,CAAA,WAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;AAKA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,kBAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,mBAAA,MAAA,CAAA,kBAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA;AACA,SA7BA,MA6BA,IAAA,oBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,EAAA;AAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,WAAA,IAAA,GAAA,IAAA,YAAA,EAAA;AACA,cAAA,MAAA,CAAA,YAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;AAKA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,WAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,IAAA,WAAA,IAAA,GAAA,IAAA,YAAA,EAAA;AACA,cAAA,MAAA,CAAA,WAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,WAJA;AAKA,UAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,kBAAA,EAAA,OAAA,CAAA,UAAA,GAAA;AAAA,mBAAA,MAAA,CAAA,kBAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AAAA,WAAA;AACA;AAnMA;;AAMA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,SAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AAAA,cAAA,CAAA;AA8LA;AACA,KA7jBA;AA8jBA,IAAA,iBA9jBA,6BA8jBA,IA9jBA,EA8jBA,QA9jBA,EA8jBA;AAAA;;AACA,UAAA;AACA,YAAA,UAAA,GAAA,CAAA,MAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,EAAA,KAAA,EAAA,MAAA,CAAA;AACA,YAAA,CAAA,GAAA,IAAA;AACA,QAAA,WAAA,CAAA,IAAA,EAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,MAAA,GAAA,MAAA,CAAA,SAAA,CAAA,MAAA,EAAA;AACA,iBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,SAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,aAAA,IAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,EAAA,IAAA,EAAA,EAAA;AACA,oBAAA,MAAA,CAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,KAAA,IAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,SAAA,GAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,IAAA,EAAA,QAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,SAAA,GAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,IAAA,EAAA,QAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,SAAA,GAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,IAAA,EAAA,QAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,UAAA,GAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,EAAA,QAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,UAAA,GAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,EAAA,QAAA,EAAA;AACA,iBANA,MAMA;AACA,kBAAA,CAAA,CAAA,UAAA,CAAA,MAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,CAAA,CAAA,GAAA,iBAAA;AACA;AACA;AACA,eAXA,MAWA;AACA,gBAAA,CAAA,CAAA,UAAA,CAAA,MAAA,IAAA,CAAA,KAAA,CAAA,CAAA,GAAA,CAAA,CAAA,GAAA,oBAAA;AACA;AACA;AACA;AACA,WAlBA,MAkBA;AACA,YAAA,CAAA,CAAA,UAAA,CAAA,iBAAA;AACA;AACA;AACA,SAvBA;AAwBA,OA3BA,CA2BA,OAAA,SAAA,EAAA;AAAA;AACA,aAAA,UAAA,CAAA,QAAA;AACA;AACA,KA7lBA;AA8lBA,IAAA,kBA9lBA,8BA8lBA,IA9lBA,EA8lBA,IA9lBA,EA8lBA;AACA,UAAA,GAAA,GAAA,IAAA;;AACA,UAAA,IAAA,CAAA,IAAA,CAAA,IAAA,SAAA,IAAA,IAAA,CAAA,IAAA,CAAA,IAAA,IAAA,IACA,IAAA,CAAA,IAAA,CAAA,IAAA,SADA,IACA,IAAA,CAAA,IAAA,CAAA,IAAA,IADA,IAEA,IAAA,CAAA,IAAA,CAAA,IAAA,SAFA,IAEA,IAAA,CAAA,IAAA,CAAA,IAAA,IAFA,IAGA,IAAA,CAAA,KAAA,CAAA,IAAA,SAHA,IAGA,IAAA,CAAA,KAAA,CAAA,IAAA,IAHA,IAIA,IAAA,CAAA,MAAA,CAAA,IAAA,SAJA,IAIA,IAAA,CAAA,MAAA,CAAA,IAAA,IAJA,EAIA;AACA,QAAA,GAAA,GAAA,KAAA;AACA,eAAA,GAAA;AACA,OAPA,MAOA;AACA,YAAA,KAAA,WAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,KAAA,CAAA,CAAA,EAAA;AACA,cAAA,SAAA,GAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;;AACA,cAAA,KAAA,CAAA,SAAA,CAAA,IAAA,SAAA,IAAA,IAAA,IAAA,SAAA,GAAA,CAAA,EAAA;AACA;AACA,YAAA,GAAA,GAAA,KAAA;AACA,mBAAA,GAAA;AACA;AACA;;AACA,YAAA,KAAA,WAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,KAAA,CAAA,CAAA,EAAA;AACA,cAAA,SAAA,GAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;;AACA,cAAA,KAAA,CAAA,SAAA,CAAA,IAAA,SAAA,IAAA,IAAA,IAAA,SAAA,GAAA,CAAA,EAAA;AACA;AACA,YAAA,GAAA,GAAA,KAAA;AACA,mBAAA,GAAA;AACA;AACA;;AACA,YAAA,KAAA,WAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,KAAA,CAAA,CAAA,EAAA;AACA,cAAA,SAAA,GAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;;AACA,cAAA,KAAA,CAAA,SAAA,CAAA,IAAA,SAAA,IAAA,IAAA,IAAA,SAAA,GAAA,CAAA,EAAA;AACA;AACA,YAAA,GAAA,GAAA,KAAA;AACA,mBAAA,GAAA;AACA;AACA;;AACA,YAAA,KAAA,WAAA,CAAA,UAAA,CAAA,OAAA,CAAA,IAAA,KAAA,CAAA,CAAA,EAAA;AACA,cAAA,UAAA,GAAA,UAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;;AACA,cAAA,KAAA,CAAA,UAAA,CAAA,IAAA,UAAA,IAAA,IAAA,IAAA,UAAA,GAAA,CAAA,EAAA;AACA;AACA,YAAA,GAAA,GAAA,KAAA;AACA,mBAAA,GAAA;AACA;AACA;;AACA,YAAA,KAAA,WAAA,CAAA,UAAA,CAAA,OAAA,CAAA,IAAA,KAAA,CAAA,CAAA,EAAA;AACA,cAAA,UAAA,GAAA,UAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;;AACA,cAAA,KAAA,CAAA,UAAA,CAAA,IAAA,UAAA,IAAA,IAAA,IAAA,UAAA,GAAA,CAAA,EAAA;AACA;AACA,YAAA,GAAA,GAAA,KAAA;AACA,mBAAA,GAAA;AACA;AACA;AACA;;AACA,aAAA,GAAA;AACA,KAlpBA;AAmpBA,IAAA,YAnpBA,wBAmpBA,IAnpBA,EAmpBA;AACA,UAAA,GAAA,GAAA,CAAA;;AACA,UAAA,IAAA,IAAA,SAAA,IAAA,IAAA,IAAA,IAAA,EACA;AACA,QAAA,GAAA,GAAA,CAAA;AACA,OAHA,MAGA;AACA,QAAA,GAAA,GAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,IAAA,CAAA,GAAA,OAAA,IAAA,OAAA;;AACA,YAAA,KAAA,CAAA,GAAA,CAAA,IAAA,GAAA,IAAA,IAAA,EAAA;AACA,UAAA,GAAA,GAAA,CAAA;AACA;AACA;;AACA,aAAA,GAAA;AACA,KA/pBA;AAgqBA;AACA,IAAA,cAjqBA,0BAiqBA,MAjqBA,EAiqBA,MAjqBA,EAiqBA;AACA;AACA,UAAA,OAAA,GAAA,QAAA,CAFA,CAEA;AAEA;;AACA,UAAA,UAAA,GAAA,IAAA,CAAA,GAAA,CAAA,MAAA,GAAA,MAAA,CAAA,CALA,CAOA;;AACA,aAAA,UAAA,GAAA,OAAA;AACA,KA1qBA;AA2qBA,IAAA,MA3qBA,kBA2qBA,IA3qBA,EA2qBA;AACA,MAAA,IAAA,CAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA;;AACA,UAAA,IAAA,CAAA,WAAA,IAAA,CAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA;AACA,OAFA,MAEA;AACA,aAAA,yBAAA,CAAA,IAAA;AACA,OANA,CAOA;AACA;AACA;AACA;AACA;;AACA,KAvrBA;AAwrBA,IAAA,iBAxrBA,6BAwrBA,KAxrBA,EAwrBA;AACA,UAAA,IAAA,GAAA,UAAA,CAAA,KAAA,CAAA;;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,KAAA;AACA,OAFA,MAEA;AACA,eAAA,IAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA;AACA;AA/rBA;AAnKA,CAAA", "sourcesContent": ["<template>\r\n  <div id=\"modularForm\">\r\n    <header>\r\n      <el-button type=\"success\" @click=\"goBack\">返回</el-button>\r\n      能源数据汇总查看/修改记录\r\n    </header>\r\n\r\n    <div id=\"modularForm_bg\" style=\"overflow-x: hidden;\">\r\n      <ul v-for=\"item in list\" :key=\"item.id\">\r\n        <li style=\"margin: 2rem;\">\r\n          <ul style=\"width: 50%; display: flex; color: #ffffff; justify-content: space-between; font-size: 1.5rem; line-height: 6rem; height: 6rem;\">\r\n            <li style=\"width: 30%;\"><span>{{ item.operateType == 1 ? item.reportTime:item.createTime }}</span></li>\r\n            <li style=\"width: 30%;\"><span>操作人：{{ item.createName }}</span></li>\r\n            <li style=\"width: 20%;\"><span>{{ item.operateType == 1 ?'上报':'修改'}} </span></li>\r\n            <li style=\"width: 20%;\">\r\n              <el-button type=\"primary\" @click.prevent=\"getObj(item)\">{{ !item.show?'查看数据':'收起数据' }}</el-button>\r\n              <!-- <el-button type=\"primary\" @click.prevent=\"getObj(item)\">{{ item.names }}</el-button> -->\r\n            </li>\r\n          </ul>\r\n          <div v-show=\"item.show == true\" style=\"color: #ffffff;\">\r\n\r\n\t<avue-crud v-show=\"item.show == true\"\r\n            ref=\"crud\"\r\n            :data=\"item.tableData\"\r\n            :table-loading=\"tableLoading\"\r\n            :option=\"tableOption\"\r\n          :cell-class-name=\"dataStyle\"\r\n            >\r\n            <!-- <template slot=\"groupDataForm\" slot-scope=\"{ row,index}\">\r\n                  <el-input :class=\"[row.groupEdit ? 'el_input_class' : '']\"\r\n                            v-model=\"row.groupData\"\r\n                            :disabled=\"disabledList.groupList.indexOf(index) == -1 ? false : true\"\r\n                            :type=\"noInputList.groupList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                            @change=\"handleGroupChange(row,index)\"\r\n                            @keydown.native=\"inputLimit\"\r\n                            :placeholder=\"formulaList.groupList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                            @mousewheel.native.prevent\r\n                  ></el-input>\r\n              </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                  <el-input :class=\"[row.groupEdit ? 'el_input_class' : '']\"\r\n                  ></el-input>\r\n              </template>\r\n              <!-- <template slot=\"stockDataForm\" slot-scope=\"{ row, index}\">\r\n                <el-input :class=\"[row.stockEdit ? 'el_input_class' : '']\"\r\n                          :disabled=\"disabledList.stockList.indexOf(index) == -1 ? false : true\"\r\n                          :type=\"noInputList.stockList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                          @change=\"handleStockChange(row,index)\"\r\n                          @keydown.native=\"inputLimit\"\r\n                          :placeholder=\"formulaList.stockList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                          @mousewheel.native.prevent\r\n                ></el-input>\r\n              </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                <el-input :class=\"[row.stockEdit ? 'el_input_class' : '']\"\r\n                ></el-input>\r\n              </template>\r\n              <!-- <template slot=\"largeDataForm\" slot-scope=\"{ row, index}\">\r\n                <el-input :class=\"[row.largeEdit ? 'el_input_class' : '']\"\r\n                          v-model=\"row.largeData\"\r\n                          :disabled=\"disabledList.largeList.indexOf(index) == -1 ? false : true\"\r\n                          :type=\"noInputList.largeList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                          @change=\"handleLargeChange(row,index)\"\r\n                          @keydown.native=\"inputLimit\"\r\n                          :placeholder=\"formulaList.largeList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                          @mousewheel.native.prevent\r\n                ></el-input>\r\n              </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                <el-input :class=\"[row.largeEdit ? 'el_input_class' : '']\"\r\n                ></el-input>\r\n              </template>\r\n              <!-- <template slot=\"mediumDataForm\" slot-scope=\"{ row, index }\">\r\n                <el-input :class=\"[row.mediumEdit ? 'el_input_class' : '']\"\r\n                          v-model=\"row.mediumData\"\r\n                          :disabled=\"disabledList.mediumList.indexOf(index) == -1 ? false : true\"\r\n                          :type=\"noInputList.mediumList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                          @change=\"handleMediumChange(row,index)\"\r\n                          @keydown.native=\"inputLimit\"\r\n                          :placeholder=\"formulaList.mediumList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                          @mousewheel.native.prevent\r\n                ></el-input>\r\n              </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                <el-input :class=\"[row.mediumEdit ? 'el_input_class' : '']\"\r\n                ></el-input>\r\n              </template>\r\n              <!-- <template slot=\"mobileDataForm\" slot-scope=\"{ row, index }\">\r\n                <el-input :class=\"[row.mobileEdit ? 'el_input_class' : '']\"\r\n                          v-model=\"row.mobileData\"\r\n                          :disabled=\"disabledList.mobileList.indexOf(index) == -1 ? false : true\"\r\n                          :type=\"noInputList.mobileList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                          @change=\"handleMobileChange(row,index)\"\r\n                          @keydown.native=\"inputLimit\"\r\n                          :placeholder=\"formulaList.mobileList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                          @mousewheel.native.prevent\r\n                ></el-input>\r\n            </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                <el-input :class=\"[row.mobileEdit ? 'el_input_class' : '']\"\r\n                ></el-input>\r\n            </template>\r\n          </avue-crud>\r\n          </div>\r\n\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\n// import emptyStrate from \"@/components/empty\";\r\nimport delects from \"@/components/delects/index\";\r\n// import {mapGetters} from \"vuex\";\r\n// import {dateFormat} from \"@/util/date\";\r\n// import { downloadFile } from \"@/util/ruoyi\";\r\nimport {energyList, energyUpdateList,getEnergyUpdateRecordList,energyDataUpdateList} from \"@/api/carbon/discharge/energy\";\r\nimport jituanEnergyReportLog from \"./jituanEnergyReportLog\";\r\nexport default {\r\n  components: {\r\n    // emptyStrate,\r\n    delects,\r\n    jituanEnergyReportLog,\r\n  },\r\n  data() {\r\n    return {\r\n      objName: '查看数据',\r\n      tableLoading: false,\r\n      isShow: false,\r\n      list: [],\r\n      tableOption: {\r\n        border: false,\r\n        index: false,\r\n        height: \"auto\",\r\n        calcHeight: 35,\r\n        stripe: true,\r\n        menuAlign: \"center\",\r\n        align: \"center\",\r\n        refreshBtn: false,\r\n        showClomnuBtn: false,\r\n        searchMenuSpan: 4,\r\n        searchSize: \"mini\",\r\n        card: true,\r\n        addBtn: false,\r\n        editBtn: false,\r\n        delBtn: false,\r\n        columnBtn: false,\r\n        searchBtn: false,\r\n        emptyBtn: false,\r\n        menu: false,\r\n        dialogWidth: 500,\r\n        dialogMenuPosition: \"center\",\r\n        dialogCustomClass: \"singleRowDialog\",\r\n        labelWidth: 100,\r\n        column: [\r\n          {\r\n            label: \"指标\",\r\n            prop: \"indicatorName\",\r\n            overHidden: true,\r\n            width: 750,\r\n            align: \"left\"\r\n          },\r\n          {\r\n            label: \"集团\",\r\n            prop: \"groupData\",\r\n            formatter:(val,value,label)=>{\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: false,\r\n          },\r\n          {\r\n            label: \"股份\",\r\n            prop: \"stockData\",\r\n            formatter:(val,value,label)=>{\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: false,\r\n          },\r\n          {\r\n            label: '数据中心',\r\n            children: [{\r\n              label: '大型',\r\n              prop: 'largeData',\r\n              formatter:(val,value,label)=>{\r\n                return this.formatDisplayData(value);\r\n              },\r\n              overHidden: true,\r\n              cell: true,\r\n              slot: true\r\n            }, {\r\n              label: '中小型',\r\n              prop: 'mediumData',\r\n              formatter:(val,value,label)=>{\r\n                return this.formatDisplayData(value);\r\n              },\r\n              cell: true,\r\n              slot: true,\r\n              overHidden: true\r\n            }]\r\n          },\r\n          {\r\n            label: \"移动业务\",\r\n            prop: \"mobileData\",\r\n            formatter:(val,value,label)=>{\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: false,\r\n          },\r\n        ],\r\n      },\r\n      companyName: undefined,\r\n      tableData: [],\r\n      // 原表格数据\r\n      originalData: [],\r\n      // 需改的下标数组\r\n      updateList:[],\r\n      energyData: [],\r\n      disabledList: {\r\n        groupList: [0, 4, 5, 7, 18, 23, 27, 57],\r\n        stockList: [0, 4, 5, 7, 18, 23, 27, 57],\r\n        largeList: [0, 4, 5, 6, 7, 8, 9, 10, 17, 18, 19, 20, 23, 27, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        mediumList: [0, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 23, 27, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        mobileList: [0, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 24, 25, 28, 29, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57],\r\n      },\r\n      formulaList: {\r\n        groupList: [0, 4, 5, 7, 18, 23, 27],\r\n        stockList: [0, 4, 5, 7, 18, 23, 27],\r\n        largeList: [0, 4, 23, 27],\r\n        mediumList: [0, 4, 11, 12, 13, 14, 15, 16, 23, 27],\r\n        mobileList: [0, 4, 7, 8, 9, 10],\r\n      },\r\n      noInputList: {\r\n        groupList: [57],\r\n        stockList: [57],\r\n        largeList: [5, 6, 7, 8, 9, 10, 17, 18, 19, 20, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        mediumList: [5, 6, 7, 8, 9, 10, 17, 18, 19, 20, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        mobileList: [2, 5, 6, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 24, 25, 28, 29, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57],\r\n      },\r\n      requireInputList: {\r\n        groupList: [1, 2, 3, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38],\r\n        stockList: [1, 2, 3, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38],\r\n        largeList: [1, 2, 3, 6, 11, 12, 13, 14, 15, 16, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 57],\r\n        mediumList: [1, 2, 3, 6, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 57],\r\n        mobileList: [1, 3, 21, 22, 23, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37],\r\n      },\r\n      noRequireInputList: {\r\n        groupList: [39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        stockList: [39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n      },\r\n      backgroundList: {\r\n        groupList: [],\r\n        stockList: [],\r\n        largeList: [],\r\n        mediumList: [],\r\n        mobileList: [],\r\n      },\r\n      formData: {\r\n        reportTime: undefined,\r\n        companyId: undefined,\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n\r\n  },\r\n  created() {\r\n    if (this.$route.query) {\r\n\r\n      this.formData.reportTime = this.$route.query.reportTime;\r\n      this.companyName = this.$route.query.companyName;\r\n      this.formData.companyId = this.$route.query.companyId;\r\n      // this.getList();\r\n      this.getUpdRecordList()\r\n    }\r\n  },\r\n  methods: {\r\ndataStyle({row,column,rowIndex,columnIndex}){\r\n      if(columnIndex === 1 && row.groupEdit){\r\n        return 'cell-color';\r\n      }\r\n      if(columnIndex === 2 && row.stockEdit){\r\n        return 'cell-color';\r\n      }\r\n      if(columnIndex === 3 && row.largeEdit){\r\n        return 'cell-color';\r\n      }\r\n      if(columnIndex === 4 && row.mediumEdit){\r\n        return 'cell-color';\r\n      }\r\n      if(columnIndex === 5 && row.mobileEdit){\r\n        return 'cell-color';\r\n      }\r\n    },\r\n    getUpdRecordList() {\r\n      getEnergyUpdateRecordList(Object.assign({companyId:this.formData.companyId,reportTime:this.formData.reportTime})).then((res) => {\r\n        if (res.data.code == 200) {\r\n          this.list = res.data.data\r\n          this.list.forEach(items => {\r\n            items.names = '查看数据';\r\n          })\r\n        }\r\n      });\r\n    },\r\n\r\n    //数据禁止输入e\r\n    inputLimit(event) {\r\n      if (event.key === 'e' || event.key === '-') {\r\n        event.returnValue = false;\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    goBack() {\r\n      this.$router.push({\r\n        path: \"/dataView/energy/index\",\r\n        query: {\r\n          reportTime: this.formData.reportTime,\r\n        },\r\n      });\r\n    },\r\n    // 查询默认列表\r\n    getList(item) {\r\n      this.tableLoading = true;\r\n      energyList(this.formData).then((res) => {\r\n        if (res.data.code == 200) {\r\n          this.tableLoading = false;\r\n          item.tableData = res.data.data;\r\n          this.checkIndicatorInput();\r\n          this.tableData.forEach((item) => {\r\n            item.unit = item.unitDescription + \"(\" + item.unitName + \")\";\r\n            item.$cellEdit = true;\r\n            item.groupData = item.groupData == null ? \"\\\\\" : item.groupData;\r\n            item.stockData = item.stockData == null ? \"\\\\\" : item.stockData;\r\n            item.largeData = item.largeData == null ? \"\\\\\" : item.largeData;\r\n            item.mediumData = item.mediumData == null ? \"\\\\\" : item.mediumData;\r\n            item.mobileData = item.mobileData == null ? \"\\\\\" : item.mobileData;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 查询修改的列表\r\n    getEnergyUpdateRecordList(item) {\r\n      this.tableLoading = true;\r\n      this.formData.updateRecordId = item.id\r\n      energyDataUpdateList(this.formData).then((res) => {\r\n        if (res.data.code == 200) {\r\n          this.tableLoading = false;\r\n          item.tableData = res.data.data;\r\n          this.checkIndicatorInput();\r\n          this.tableData.forEach((item) => {\r\n            item.unit = item.unitDescription + \"(\" + item.unitName + \")\";\r\n            item.$cellEdit = true;\r\n            item.groupData = item.groupData == null ? \"\\\\\" : item.groupData;\r\n            item.stockData = item.stockData == null ? \"\\\\\" : item.stockData;\r\n            item.largeData = item.largeData == null ? \"\\\\\" : item.largeData;\r\n            item.mediumData = item.mediumData == null ? \"\\\\\" : item.mediumData;\r\n            item.mobileData = item.mobileData == null ? \"\\\\\" : item.mobileData;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    saveReportData() {\r\n      if (this.validReportData() == false) {\r\n        this.alertError(\"请输入全部数据\");\r\n        return;\r\n      }\r\n      let data = {}\r\n      let saveList = [];\r\n      this.tableData.forEach((item) => {\r\n        let saveItem = {};\r\n        saveItem.reportTime = this.formData.reportTime;\r\n        saveItem.companyId = this.formData.companyId;\r\n        saveItem.reportFlag = \"2\";\r\n        saveItem.id = item.id;\r\n        saveItem.indicatorName = item.indicatorName;\r\n        saveItem.groupData = item.groupData == '\\\\' || item.groupData == '/' ? null : item.groupData;\r\n        saveItem.stockData = item.stockData == '\\\\' || item.stockData == '/' ? null : item.stockData;\r\n        saveItem.largeData = item.largeData == '\\\\' || item.largeData == '/' ? null : item.largeData;\r\n        saveItem.mediumData = item.mediumData == '\\\\' || item.mediumData == '/' ? null : item.mediumData;\r\n        saveItem.mobileData = item.mobileData == '\\\\' || item.mobileData == '/' ? null : item.mobileData;\r\n        saveList.push(saveItem);\r\n      });\r\n      data.companyId = this.formData.companyId\r\n      data.dischargeEnergyIndicatorVos = saveList\r\n      data.dischargeDataEnergyUpdateLogList = this.updateList\r\n      energyUpdateList(data).then((res) => {\r\n        if (res.data.code == 200) {\r\n          this.alertSuccess(\"操作成功\");\r\n          this.goBack();\r\n        }\r\n      });\r\n    },\r\n    //校验上报数据\r\n    validReportData() {\r\n      let ret = true;\r\n      this.backgroundList.groupList = [];\r\n      this.backgroundList.stockList = [];\r\n      this.backgroundList.largeList = [];\r\n      this.backgroundList.mediumList = [];\r\n      this.backgroundList.mobileList = [];\r\n      for (let i = 0; i < this.tableData.length; i++) {\r\n        if (this.tableData[i].groupData == undefined\r\n          || this.tableData[i].groupData == null\r\n          || this.tableData[i].groupData.length == 0) {\r\n          if (this.requireInputList.groupList.indexOf(i) > -1) {\r\n            this.backgroundList.groupList.push(i);\r\n            ret = false;\r\n          } else {\r\n            this.tableData[i].groupData = \"0\";\r\n          }\r\n        }\r\n        if (this.tableData[i].stockData == undefined\r\n          || this.tableData[i].stockData == null\r\n          || this.tableData[i].stockData.length == 0) {\r\n          if (this.requireInputList.stockList.indexOf(i) > -1) {\r\n            this.backgroundList.stockList.push(i);\r\n            ret = false;\r\n          } else {\r\n            this.tableData[i].stockData = \"0\";\r\n          }\r\n          ret = false;\r\n        }\r\n        if (this.tableData[i].largeData == undefined\r\n          || this.tableData[i].largeData == null\r\n          || this.tableData[i].largeData.length == 0) {\r\n          this.backgroundList.largeList.push(i);\r\n          ret = false;\r\n        }\r\n        if (this.tableData[i].mediumData == undefined\r\n          || this.tableData[i].mediumData == null\r\n          || this.tableData[i].mediumData.length == 0) {\r\n          this.backgroundList.mediumList.push(i);\r\n          ret = false;\r\n        }\r\n        if (this.tableData[i].mobileData == undefined\r\n          || this.tableData[i].mobileData == null\r\n          || this.tableData[i].mobileData.length == 0) {\r\n          this.backgroundList.mobileList.push(i);\r\n          ret = false;\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n    // 保存修改记录的方法\r\n    saveChangeIndex(data,index,indexName,num,id){\r\n      const predata = this.originalData[index][indexName]\r\n    // 若是修改了数据，则记录下标\r\n    if(this.areFloatsEqual(data,this.originalData[index][indexName])){\r\n        const filteredArray = this.updateList.filter(obj => obj.id !== id);\r\n        this.updateList = filteredArray\r\n      } else {\r\n        // 使用findIndex()方法查找数组中具有特定id的对象的索引\r\n        const index = this.updateList.findIndex(obj => obj.id === id);\r\n        // 如果索引大于等于0，则表示找到了对象，可以删除\r\n        if (index >= 0) {\r\n            // 使用splice()方法删除数组中指定索引的对象\r\n            this.updateList.splice(index, 1);\r\n        }\r\n        this.updateList.push({energyId:id,num:num,previousData:predata,nowData:data})\r\n      }\r\n    },\r\n\r\n    // handleGroupChange(row,index) {\r\n    //   this.saveChangeIndex(row.groupData,index,'groupData',1,row.id)\r\n    //   if (this.noInputList.groupList.indexOf(index) == -1 && this.formulaList.groupList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['groupData'].toString()) < 0) {\r\n    //       this.tableData[index]['groupData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['groupData'] != undefined && this.tableData[index]['groupData'] != '') {\r\n    //       let j = this.backgroundList.groupList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.groupList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     if (index == 6 || index == 11 || index == 17) {\r\n    //       this.tableData[5]['groupData'] = (this.cellToNumber(this.tableData[6]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[7]['groupData']) + this.cellToNumber(this.tableData[11]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[17]['groupData'])).toString();\r\n    //       this.tableData[4]['groupData'] = (this.cellToNumber(this.tableData[5]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[18]['groupData']) + this.cellToNumber(this.tableData[21]['groupData'])).toString();\r\n    //     } else if (index == 8 || index == 9 || index == 10) {\r\n    //       this.tableData[7]['groupData'] = (this.cellToNumber(this.tableData[8]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[9]['groupData']) + this.cellToNumber(this.tableData[10]['groupData'])).toString();\r\n    //       this.tableData[5]['groupData'] = (this.cellToNumber(this.tableData[6]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[7]['groupData']) + this.cellToNumber(this.tableData[11]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[17]['groupData'])).toString();\r\n    //       this.tableData[4]['groupData'] = (this.cellToNumber(this.tableData[5]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[18]['groupData']) + this.cellToNumber(this.tableData[21]['groupData'])).toString();\r\n    //     } else if (index == 19 || index == 20) {\r\n    //       this.tableData[18]['groupData'] = (this.cellToNumber(this.tableData[19]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[20]['groupData'])).toString();\r\n    //       this.tableData[4]['groupData'] = (this.cellToNumber(this.tableData[5]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[18]['groupData']) + this.cellToNumber(this.tableData[21]['groupData'])).toString();\r\n    //     } else if (index == 21) {\r\n    //       this.tableData[4]['groupData'] = (this.cellToNumber(this.tableData[5]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[18]['groupData']) + this.cellToNumber(this.tableData[21]['groupData'])).toString();\r\n    //     } else if (index == 24 || index == 25) {\r\n    //       this.tableData[23]['groupData'] = (this.cellToNumber(this.tableData[24]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[25]['groupData'])).toString();\r\n    //     } else if (index == 28 || index == 29) {\r\n    //       this.tableData[27]['groupData'] = (this.cellToNumber(this.tableData[28]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[29]['groupData'])).toString();\r\n    //     }\r\n    //     this.countData('groupData');\r\n    //   }\r\n    // },\r\n    // handleStockChange(row,index) {\r\n    //   this.saveChangeIndex(row.stockData,index,'stockData',2,row.id)\r\n    //   if (this.noInputList.stockList.indexOf(index) == -1 && this.formulaList.stockList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['stockData'].toString()) < 0) {\r\n    //       this.tableData[index]['stockData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['stockData'] != undefined && this.tableData[index]['stockData'] != '') {\r\n    //       let j = this.backgroundList.stockList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.stockList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     if (index == 6 || index == 11 || index == 17) {\r\n    //       this.tableData[5]['stockData'] = (this.cellToNumber(this.tableData[6]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[7]['stockData']) + this.cellToNumber(this.tableData[11]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[17]['stockData'])).toString();\r\n    //       this.tableData[4]['stockData'] = (this.cellToNumber(this.tableData[5]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[18]['stockData']) + this.cellToNumber(this.tableData[21]['stockData'])).toString();\r\n    //     } else if (index == 8 || index == 9 || index == 10) {\r\n    //       this.tableData[index]['mediumData'] = this.tableData[index]['stockData'];\r\n    //       this.tableData[index]['mobileData'] = this.tableData[index]['stockData'];\r\n    //       this.tableData[7]['stockData'] = (this.cellToNumber(this.tableData[8]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[9]['stockData']) + this.cellToNumber(this.tableData[10]['stockData'])).toString();\r\n    //       this.tableData[7]['mediumData'] = this.tableData[7]['stockData'];\r\n    //       this.tableData[4]['mediumData'] = this.tableData[7]['stockData'];\r\n    //       this.tableData[7]['mobileData'] = this.tableData[7]['stockData'];\r\n    //       this.tableData[4]['mobileData'] = this.tableData[7]['stockData'];\r\n    //       this.tableData[5]['stockData'] = (this.cellToNumber(this.tableData[6]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[7]['stockData']) + this.cellToNumber(this.tableData[11]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[17]['stockData'])).toString();\r\n    //       this.tableData[4]['stockData'] = (this.cellToNumber(this.tableData[5]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[18]['stockData']) + this.cellToNumber(this.tableData[21]['stockData'])).toString();\r\n    //       this.countData('mediumData');\r\n    //       this.countData('mobileData');\r\n    //     } else if (index > 12 && index < 17) {\r\n    //       this.tableData[index]['mediumData'] = this.tableData[index]['stockData'];\r\n    //       if (index == 13 || index == 15) {\r\n    //         this.tableData[11]['mediumData'] = (this.cellToNumber(this.tableData[13]['stockData']) +\r\n    //           this.cellToNumber(this.tableData[15]['stockData'])).toString();\r\n    //         this.tableData[4]['mediumData'] = this.tableData[11]['mediumData'];\r\n    //         this.countData('mediumData');\r\n    //       } else {\r\n    //         this.tableData[12]['mediumData'] = (this.cellToNumber(this.tableData[14]['stockData']) +\r\n    //           this.cellToNumber(this.tableData[16]['stockData'])).toString();\r\n    //       }\r\n\r\n    //     } else if (index == 19 || index == 20) {\r\n    //       this.tableData[18]['stockData'] = (this.cellToNumber(this.tableData[19]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[20]['stockData'])).toString();\r\n    //       this.tableData[4]['stockData'] = (this.cellToNumber(this.tableData[5]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[18]['stockData']) + this.cellToNumber(this.tableData[21]['stockData'])).toString();\r\n    //     } else if (index == 21) {\r\n    //       this.tableData[4]['stockData'] = (this.cellToNumber(this.tableData[5]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[18]['stockData']) + this.cellToNumber(this.tableData[21]['stockData'])).toString();\r\n    //     } else if (index == 24 || index == 25) {\r\n    //       this.tableData[23]['stockData'] = (this.cellToNumber(this.tableData[24]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[25]['stockData'])).toString();\r\n    //     } else if (index == 28 || index == 29) {\r\n    //       this.tableData[27]['stockData'] = (this.cellToNumber(this.tableData[28]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[29]['stockData'])).toString();\r\n    //     }\r\n    //     this.countData('stockData');\r\n    //   }\r\n    // },\r\n    // handleLargeChange(row,index) {\r\n    //   this.saveChangeIndex(row.largeData,index,'largeData',3,row.id)\r\n    //   if (this.noInputList.largeList.indexOf(index) == -1 && this.formulaList.largeList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['largeData'].toString()) < 0) {\r\n    //       this.tableData[index]['largeData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['largeData'] != undefined && this.tableData[index]['largeData'] != '') {\r\n    //       let j = this.backgroundList.largeList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.largeList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     if (index == 11) {\r\n    //       this.tableData[4]['largeData'] = this.tableData[11]['largeData'];\r\n\r\n    //     } else if (index == 24 || index == 25) {\r\n    //       this.tableData[23]['largeData'] = (this.cellToNumber(this.tableData[24]['largeData']) +\r\n    //         this.cellToNumber(this.tableData[25]['largeData'])).toString();\r\n    //     } else if (index == 28 || index == 29) {\r\n    //       this.tableData[27]['largeData'] = (this.cellToNumber(this.tableData[28]['largeData']) +\r\n    //         this.cellToNumber(this.tableData[29]['largeData'])).toString();\r\n    //     }\r\n    //     this.countData('largeData');\r\n    //   }\r\n    // },\r\n    // handleMediumChange(row,index) {\r\n    //   this.saveChangeIndex(row.mediumData,index,'mediumData',4,row.id)\r\n    //   if (this.noInputList.mediumList.indexOf(index) == -1 && this.formulaList.mediumList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['mediumData'].toString()) < 0) {\r\n    //       this.tableData[index]['mediumData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['mediumData'] != undefined && this.tableData[index]['mediumData'] != '') {\r\n    //       let j = this.backgroundList.mediumList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.mediumList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     if (index == 24 || index == 25) {\r\n    //       this.tableData[23]['mediumData'] = (this.cellToNumber(this.tableData[24]['mediumData']) +\r\n    //         this.cellToNumber(this.tableData[25]['mediumData'])).toString();\r\n    //     } else if (index == 28 || index == 29) {\r\n    //       this.tableData[27]['mediumData'] = (this.cellToNumber(this.tableData[28]['mediumData']) +\r\n    //         this.cellToNumber(this.tableData[29]['mediumData'])).toString();\r\n    //     }\r\n    //     this.countData('mediumData');\r\n    //   }\r\n    // },\r\n    // handleMobileChange(row,index) {\r\n    //   this.saveChangeIndex(row.mobileData,index,'mediumData',5,row.id)\r\n    //   if (this.noInputList.mobileList.indexOf(index) == -1 && this.formulaList.mobileList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['mobileData'].toString()) < 0) {\r\n    //       this.tableData[index]['mobileData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['mobileData'] != undefined && this.tableData[index]['mobileData'] != '') {\r\n    //       let j = this.backgroundList.mobileList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.mobileList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     this.countData('mobileData')\r\n    //   }\r\n    // },\r\n    countData(label) {\r\n      this.tableData[0][label] = (Math.round((\r\n          this.cellToNumber(this.tableData[1][label]) * this.tableData[1].coefficient +\r\n          this.cellToNumber(this.tableData[3][label]) * this.tableData[3].coefficient +\r\n          this.cellToNumber(this.tableData[4][label]) * this.tableData[4].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[21][label]) * this.tableData[21].coefficient +\r\n          this.cellToNumber(this.tableData[22][label]) * this.tableData[22].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[25][label]) * this.tableData[25].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[26][label]) * this.tableData[26].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[29][label]) * this.tableData[29].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[30][label]) * this.tableData[30].coefficient +\r\n          this.cellToNumber(this.tableData[31][label]) * this.tableData[31].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[32][label]) * this.tableData[32].coefficient +\r\n          this.cellToNumber(this.tableData[33][label]) +\r\n          this.cellToNumber(this.tableData[34][label]) * this.tableData[34].coefficient / 1000 -\r\n          this.cellToNumber(this.tableData[35][label])\r\n      ) * 1000000) / 1000000).toString();\r\n    },\r\n    //根据指标名称确定输入框状态\r\n    checkIndicatorInput() {\r\n      Object.keys(this.disabledList).forEach(key => this.disabledList[key] = []);\r\n      Object.keys(this.formulaList).forEach(key => this.formulaList[key] = []);\r\n      Object.keys(this.noInputList).forEach(key => this.noInputList[key] = []);\r\n      Object.keys(this.requireInputList).forEach(key => this.requireInputList[key] = []);\r\n      Object.keys(this.noRequireInputList).forEach(key => this.noRequireInputList[key] = []);\r\n      for (let i = 0; i < this.tableData.length; i ++) {\r\n        if ('1、能源消费总量(吨标煤)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          Object.keys(this.formulaList).forEach(key => this.formulaList[key].push(i));\r\n        } else if ('1.1、 煤炭(吨)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.requireInputList).forEach(key => this.requireInputList[key].push(i));\r\n        } else if ('1.1.1、其中发电用煤(吨)' == this.tableData[i].indicatorName) {\r\n          this.disabledList.mobileList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n          Object.keys(this.requireInputList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.requireInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.2、焦炭(吨)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.requireInputList).forEach(key => this.requireInputList[key].push(i));\r\n        } else if ('1.3、耗电量（总）(千瓦时)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          Object.keys(this.formulaList).forEach(key => this.formulaList[key].push(i));\r\n        } else if ('1.3.1、生产用房耗电量(千瓦时)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          this.formulaList.groupList.push(i);\r\n          this.formulaList.stockList.push(i);\r\n          this.noInputList.largeList.push(i);\r\n          this.noInputList.mediumList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n        } else if ('1.3.1.1、其中：通信机房耗电量(千瓦时)' == this.tableData[i].indicatorName) {\r\n          this.disabledList.largeList.push(i);\r\n          this.disabledList.mediumList.push(i);\r\n          this.disabledList.mobileList.push(i);\r\n          this.noInputList.largeList.push(i);\r\n          this.noInputList.mediumList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n          Object.keys(this.requireInputList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.requireInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.3.1.2、其中：基站耗电量(千瓦时)' == this.tableData[i].indicatorName) {  //7\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          Object.keys(this.formulaList).forEach(key => {\r\n            if (key != 'largeList' && key != 'mediumList') {\r\n              this.formulaList[key].push(i);\r\n            }\r\n          });\r\n          this.noInputList.largeList.push(i);\r\n          this.noInputList.mediumList.push(i);\r\n        } else if ('1.3.1.2.1、其中：铁塔公司基站耗电量（包括室内分布、室外站等） (千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.1.2.2、其中：第三方租赁基站耗电量（包括室内分布、室外站等） (千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.1.2.3、其中：自有产权基站耗电量（包括室内分布、室外站等） (千瓦时)' == this.tableData[i].indicatorName) { //8 - 10\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          this.formulaList.mobileList.push(i);\r\n          this.noInputList.largeList.push(i);\r\n          this.noInputList.mediumList.push(i);\r\n          this.requireInputList.groupList.push(i);\r\n          this.requireInputList.stockList.push(i);\r\n        } else if ('1.3.1.3、其中：数据中心耗电量(千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.1.3.1、其中：数据中心IT设备总耗电量（千瓦时）' == this.tableData[i].indicatorName||\r\n          '1.3.1.3.2、其中：对外IDC机房耗电量(千瓦时)' == this.tableData[i].indicatorName||\r\n          '1.3.1.3.2.1、其中：对外IDC机房IT设备耗电量（千瓦时)' == this.tableData[i].indicatorName||\r\n          '1.3.1.3.3、其中：自用业务平台和IT支撑用房耗电量(千瓦时)' == this.tableData[i].indicatorName||\r\n          '1.3.1.3.3.1、其中：自用业务平台IT设备耗电量（千瓦时）' == this.tableData[i].indicatorName) { //11 - 16\r\n          this.disabledList.mediumList.push(i);\r\n          this.disabledList.mobileList.push(i);\r\n          this.formulaList.mediumList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n          Object.keys(this.requireInputList).forEach(key => {\r\n            if (key != 'mediumList' && key != 'mobileList') {\r\n              this.requireInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.3.1.4、其中：接入局所及室外机柜耗电量(千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.2.1、其中：管理用房耗电量(千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.2.2、其中：渠道用房耗电量(千瓦时)' == this.tableData[i].indicatorName ||\r\n          '2.5、废水排放量(吨)' == this.tableData[i].indicatorName) { //17 19-20 38\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noInputList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.noInputList[key].push(i);\r\n            }\r\n          });\r\n          this.requireInputList.groupList.push(i);\r\n          this.requireInputList.stockList.push(i);\r\n        } else if ('1.3.2、非生产用房耗电量(千瓦时)' == this.tableData[i].indicatorName) { //18\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          this.formulaList.groupList.push(i);\r\n          this.formulaList.stockList.push(i);\r\n          Object.keys(this.noInputList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.noInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.3.3、 可再生能源使用量(千瓦时)（仅限填写自发自用绿电）' == this.tableData[i].indicatorName ||\r\n          '1.4、原油(吨)' == this.tableData[i].indicatorName) { //21 22\r\n          Object.keys(this.requireInputList).forEach(key => this.requireInputList[key].push(i));\r\n        } else if ('1.5、汽油消耗量(升)' == this.tableData[i].indicatorName) { //23\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.formulaList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.formulaList[key].push(i);\r\n            }\r\n          });\r\n          this.requireInputList.mobileList.push(i);\r\n        } else if ('1.5.1、其中：移动源（升）' == this.tableData[i].indicatorName ||\r\n          '1.5.2、其中：固定源（升）' == this.tableData[i].indicatorName ||\r\n          '1.7.1、其中：移动源（升）' == this.tableData[i].indicatorName ||\r\n          '1.7.2、其中：固定源（升）' == this.tableData[i].indicatorName) { //24 25 28 29\r\n          this.disabledList.mobileList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n          Object.keys(this.requireInputList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.requireInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.6、煤油(升)' == this.tableData[i].indicatorName ||\r\n          '1.8、燃料油(升)' == this.tableData[i].indicatorName ||\r\n          '1.9、液化石油气(吨）' == this.tableData[i].indicatorName ||\r\n          '2.0、天然气消耗量(立方米)' == this.tableData[i].indicatorName ||\r\n          '2.1、热力(十亿焦)' == this.tableData[i].indicatorName ||\r\n          '2.2、 其他能源(吨标准煤)' == this.tableData[i].indicatorName ||\r\n          '2.3、新水用量(吨)' == this.tableData[i].indicatorName ||\r\n          '2.4、节能量(吨标准煤)' == this.tableData[i].indicatorName ||\r\n          '2.4.1、其中：节电量(千瓦时)' == this.tableData[i].indicatorName) { //26  30-37\r\n          Object.keys(this.requireInputList).forEach(key => this.requireInputList[key].push(i));\r\n        } else if ('1.7、柴油消耗量(升)' == this.tableData[i].indicatorName) { //27\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.formulaList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.formulaList[key].push(i);\r\n            }\r\n          });\r\n          this.requireInputList.mobileList.push(i);\r\n        } else if ('2.6、一般固体废物产生量(吨)' == this.tableData[i].indicatorName ||\r\n          '2.7、一般固体废物综合利用量(吨)' == this.tableData[i].indicatorName ||\r\n          '2.8、综合利用往年贮存量(吨)' == this.tableData[i].indicatorName ||\r\n          '2.9、危险废物产生量(吨)' == this.tableData[i].indicatorName ||\r\n          '3.0、危险废物处置量(吨)' == this.tableData[i].indicatorName ||\r\n          '3.1、处置往年贮存量(吨)' == this.tableData[i].indicatorName ||\r\n          '3.2、土壤污染治理面积(公顷)' == this.tableData[i].indicatorName ||\r\n          '3.3、土壤污染需要治理面积(公顷)' == this.tableData[i].indicatorName ||\r\n          '3.4、矿山（或生态）修复治理面积(公顷)' == this.tableData[i].indicatorName ||\r\n          '3.5、矿山（或生态）需要修复治理面积(公顷)' == this.tableData[i].indicatorName ||\r\n          '3.6、废气治理设施数(套)' == this.tableData[i].indicatorName ||\r\n          '3.7、废气治理设施处理能力(立方米/月)' == this.tableData[i].indicatorName ||\r\n          '3.8、废水治理设施数(套)' == this.tableData[i].indicatorName ||\r\n          '3.9、废水治理设施处理能力(吨/月)' == this.tableData[i].indicatorName ||\r\n          '4.0、生态环境污染源(个)' == this.tableData[i].indicatorName ||\r\n          '4.1、生态环境风险点（个）' == this.tableData[i].indicatorName ||\r\n          '4.2、节能投入(元)' == this.tableData[i].indicatorName ||\r\n          '4.3、环保投入(元)' == this.tableData[i].indicatorName) { //39-56\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noInputList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.noInputList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noRequireInputList).forEach(key => this.noRequireInputList[key].push(i));\r\n        } else if ('4.4、数据中心标准机架数量' == this.tableData[i].indicatorName) { //57\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'largeList' && key != 'mediumList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noInputList).forEach(key => {\r\n            if (key != 'largeList' && key != 'mediumList') {\r\n              this.noInputList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noRequireInputList).forEach(key => this.noRequireInputList[key].push(i));\r\n        }\r\n      }\r\n    },\r\n    handleImportExcel(file, fileList) {\r\n      try{\r\n        let tableField = ['上报指标', '集团', '股份', '大型', '中小型', '移动业务'];\r\n        let m = this;\r\n        importExcel(file, tableField).then(res => {\r\n          if (res.length > this.tableData.length) {\r\n            for (let i = 0; i < this.tableData.length; i ++) {\r\n              if (this.tableData[i].indicatorName == res[i+1][\"上报指标\"].trim()) {\r\n                if (this.checkExcelLineData(res[i+1], i) == true) {\r\n                  this.tableData[i].groupData = res[i + 1]['集团'].toString();\r\n                  this.tableData[i].stockData = res[i + 1]['股份'].toString();\r\n                  this.tableData[i].largeData = res[i + 1]['大型'].toString();\r\n                  this.tableData[i].mediumData = res[i + 1]['中小型'].toString();\r\n                  this.tableData[i].mobileData = res[i + 1]['移动业务'].toString();\r\n                } else {\r\n                  m.alertError(\"第\" + Math.floor(i + 3) + \"行有非法数据数据，请检查文档！\");\r\n                  return;\r\n                }\r\n              } else {\r\n                m.alertError(\"第\" + Math.floor(i + 3) + \"行上报指标名称错误，请参考填报模板！\");\r\n                return;\r\n              }\r\n            }\r\n          } else {\r\n            m.alertError(\"数据行数过少，请参考填报模板！\");\r\n            return;\r\n          }\r\n        })\r\n      }catch(exception){             //抓住throw抛出的错误\r\n        this.alertError(\"数据格式错误\");\r\n      }\r\n    },\r\n    checkExcelLineData(data, line) {\r\n      let ret = true;\r\n      if (data['集团'] == undefined || data['集团'] == null\r\n        || data['股份'] == undefined || data['股份'] == null\r\n        || data['大型'] == undefined || data['大型'] == null\r\n        || data['中小型'] == undefined || data['中小型'] == null\r\n        || data['移动业务'] == undefined || data['移动业务'] == null) {\r\n        ret = false;\r\n        return ret;\r\n      } else {\r\n        if (this.noInputList.groupList.indexOf(line) == -1) {\r\n          let groupData = parseFloat(data['集团']);\r\n          if (isNaN(groupData) || groupData == null || groupData < 0) {\r\n            // if (isNaN(groupData) || groupData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.noInputList.stockList.indexOf(line) == -1) {\r\n          let stockData = parseFloat(data['股份']);\r\n          if (isNaN(stockData) || stockData == null || stockData < 0) {\r\n            // if (isNaN(stockData) || stockData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.noInputList.largeList.indexOf(line) == -1) {\r\n          let largeData = parseFloat(data['大型']);\r\n          if (isNaN(largeData) || largeData == null || largeData < 0) {\r\n            // if (isNaN(largeData) || largeData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.noInputList.mediumList.indexOf(line) == -1) {\r\n          let mediumData = parseFloat(data['中小型']);\r\n          if (isNaN(mediumData) || mediumData == null || mediumData < 0) {\r\n            // if (isNaN(mediumData) || mediumData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.noInputList.mobileList.indexOf(line) == -1) {\r\n          let mobileData = parseFloat(data['移动业务']);\r\n          if (isNaN(mobileData) || mobileData == null || mobileData < 0) {\r\n            // if (isNaN(mobileData) || mobileData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n    cellToNumber(cell) {\r\n      let num = 0;\r\n      if (cell == undefined || cell == null)\r\n      {\r\n        num = 0;\r\n      } else {\r\n        num = Math.round(parseFloat(cell)*1000000)/1000000;\r\n        if (isNaN(num) || num == null) {\r\n          num = 0;\r\n        }\r\n      }\r\n      return num;\r\n    },\r\n    // 判断两个值是否相等\r\n    areFloatsEqual(float1, float2) {\r\n    // 定义一个非常小的误差阈值\r\n    const epsilon = 0.000001; // 适当调整此值以满足你的需求\r\n\r\n    // 计算两个浮点数的差值\r\n    const difference = Math.abs(float1 - float2);\r\n\r\n    // 如果差值小于误差阈值，就认为两个浮点数相等\r\n    return difference < epsilon;\r\n    } ,\r\n    getObj(item) {\r\n      item.show = !item.show;\r\n      if(item.operateType == 1){\r\n        this.getList(item)\r\n      } else {\r\n        this.getEnergyUpdateRecordList(item)\r\n      }\r\n      // if(item.isShow) {\r\n      //   item.names = '收起数据';\r\n      // }else {\r\n      //   item.names = '查看数据';\r\n      // }\r\n    },\r\n    formatDisplayData(value) {\r\n      let data = parseFloat(value);\r\n      if (isNaN(value)) {\r\n        return value;\r\n      } else {\r\n        return data.toFixed(2);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n/deep/.el-table {\r\n  height: auto!important;\r\n  //$properties: max-height;\r\n  //@each $prop in $properties {\r\n  //  #{$prop}: unset!important;\r\n  //}\r\n  & > .el-table__body-wrapper {\r\n    height: auto!important;\r\n  }\r\n}\r\n#modularForm_bg::-webkit-scrollbar {\r\n    // display: none;\r\n    width: 2px;\r\n  }\r\n\r\n/* 滚动条轨道 */\r\n#modularForm_bg::-webkit-scrollbar-track {\r\n  background: #f1f1f1; /* 设置滚动条轨道的背景色 */\r\n}\r\n\r\n/* 滚动条滑块 */\r\n#modularForm_bg::-webkit-scrollbar-thumb {\r\n  background: #888; /* 设置滚动条滑块的背景色 */\r\n}\r\n\r\n/* 滚动条滑块在鼠标悬停时的样式 */\r\n#modularForm_bg::-webkit-scrollbar-thumb:hover {\r\n  background: #555; /* 设置鼠标悬停时滚动条滑块的背景色 */\r\n}\r\n\r\n/deep/.cell-color {\r\n  color:red !important;\r\n  cursor: pointer;\r\n}\r\n\r\n.foot_btn {\r\n  width: 88%;\r\n  height: 7vh;\r\n  position: fixed;\r\n  bottom: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #111d30;\r\n}\r\n\r\np {\r\n  margin: 0;\r\n}\r\n/deep/.avue-crud__menu {\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n.curd-header {\r\n  width: 97.6%;\r\n  height: 5vh;\r\n  margin: 2vh auto 0;\r\n  display: flex;\r\n  background: #12537a;\r\n  position: relative;\r\n  border-bottom: 0.1rem solid #04223b;\r\n  justify-content: space-between;\r\n  & > .reporting {\r\n    // width: 69rem;\r\n    display: flex;\r\n    margin-left: 1rem;\r\n    align-items: center;\r\n    p {\r\n      font-size: 1.4rem;\r\n      line-height: 5vh;\r\n      margin-right: 1rem;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n    /deep/.el-date-editor.el-input,\r\n    .el-date-editor.el-input__inner {\r\n      width: 20rem !important;\r\n    }\r\n  }\r\n  & > .companyName {\r\n    margin-right: 12rem;\r\n    font-size: 1.4rem;\r\n    line-height: 5vh;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #00ecc0;\r\n  }\r\n}\r\n\r\n.el_input_class {\r\n  /deep/.el-input__inner{\r\n    background: red;\r\n  }\r\n}\r\n.el-input.is-disabled /deep/ .el-input__inner {\r\n  background: #626f7a !important;\r\n}\r\n::v-deep input::-webkit-outer-spin-button,\r\n::v-deep input::-webkit-inner-spin-button {\r\n  -webkit-appearance: none !important;\r\n}\r\n::v-deep input[type='number'] {\r\n  -moz-appearance: textfield !important;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/view/carbon/discharge/energyview"}]}