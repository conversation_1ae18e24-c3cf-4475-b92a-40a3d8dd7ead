{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\statistics\\energymeter\\modal-list.vue?vue&type=style&index=0&id=6abbec27&lang=less&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\statistics\\energymeter\\modal-list.vue", "mtime": 1754285403055}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749178874080}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["modal-list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "modal-list.vue", "sourceRoot": "src/view/statistics/energymeter", "sourcesContent": ["<template>\r\n  <Modal v-model=\"showModal\" :title=\"title\" width=\"70%\">\r\n    <div class=\"charge-info common-wh\">\r\n      <div class=\"query-box\">\r\n        <Form ref=\"queryform\" :model=\"queryParams\" :label-width=\"100\">\r\n          <Row class=\"form-row\">\r\n            <div class=\"query-btns\">\r\n              <Button\r\n                type=\"success\"\r\n                class=\"queryBtn\"\r\n                icon=\"ios-search\"\r\n                @click=\"_onSearchHandle\"\r\n                >搜索\r\n              </Button>\r\n              <Button type=\"info\" class=\"queryBtn\" icon=\"ios-redo\" @click=\"_onResetHandle\"\r\n                >重置</Button\r\n              >\r\n            </div>\r\n          </Row>\r\n        </Form>\r\n      </div>\r\n\r\n      <cl-table\r\n        ref=\"clTable\"\r\n        :height=\"400\"\r\n        :query-params=\"queryParams\"\r\n        :columns=\"tableSet.columns\"\r\n        :loading=\"tableSet.loading\"\r\n        :total=\"tableSet.total\"\r\n        :pageSize=\"tableSet.pageSize\"\r\n        :data=\"tableList\"\r\n        :sum-columns=\"[]\"\r\n        @on-query=\"tableQuery\"\r\n        :searchable=\"false\"\r\n        :exportable=\"false\"\r\n      >\r\n      </cl-table>\r\n    </div>\r\n    <div slot=\"footer\">\r\n      <Button type=\"default\" class=\"cancelBtn\" @click=\"showModal = false\">取消</Button>\r\n    </div>\r\n  </Modal>\r\n</template>\r\n<script>\r\nimport { getUserByUserRole, getCountryByUserId } from \"@/api/basedata/ammeter\";\r\nimport { getAmmeterInfo } from \"@/api/statistics/index\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport { noEmpty } from \"@/libs/util\";\r\nimport axios from \"@/libs/api.request\";\r\n\r\nexport default {\r\n  props: [\"title\"],\r\n  data() {\r\n    return {\r\n      showModal: false,\r\n      btnloading: false, //确认提交\r\n      pageParams: {\r\n        type: \"\",\r\n      },\r\n      queryParams: {\r\n        //查询参数\r\n      },\r\n      queryedParams: {},\r\n      dicts: {\r\n        stationType: [],\r\n        company: [], //所属分公司\r\n        country: [], //所属部门\r\n        isAdmin: false,\r\n      },\r\n      listData: [],\r\n      tableSet: {\r\n        loading: false,\r\n        pageTotal: 0,\r\n        pageNum: 1,\r\n        pageSize: 10, //当前页\r\n        columns: [\r\n          {\r\n            title: \"部门名称\",\r\n            key: \"orgName\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"局站编码\",\r\n            key: \"stationcode\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"局站名称\",\r\n            key: \"stationname\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"电表编号\",\r\n            key: \"meterCode\",\r\n            align: \"center\",\r\n          },\r\n          {\r\n            title: \"项目名称\",\r\n            key: \"projectname\",\r\n            align: \"center\",\r\n          },\r\n        ],\r\n      },\r\n      tableList: [],\r\n      curMonth: {},\r\n      list_loading: true,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.dicts.state = blist(\"budget_audit_type\");\r\n    let arr2 = [];\r\n    for (let i = 0; i < 12; i++) {\r\n      arr2.push({\r\n        name: i + 1,\r\n        cons: null,\r\n        cnt: null,\r\n        consCnt: null,\r\n        noReach: false,\r\n      });\r\n    }\r\n    this.listData = arr2;\r\n  },\r\n  methods: {\r\n    //弹窗:打开\r\n    openModal(row) {\r\n      this.showModal = true;\r\n      this.pageParams = row;\r\n      this._onResetHandle(); //下拉选项\r\n    },\r\n    //表格-筛选-重置\r\n    _onResetHandle() {\r\n      this.$refs[\"queryform\"].resetFields();\r\n      this._onSearchHandle(); //搜索列表\r\n    },\r\n    //表格-筛选-搜索\r\n    _onSearchHandle() {\r\n      this.queryedParams = { ...this.queryParams };\r\n      this.$refs.clTable.query(this.queryedParams);\r\n    },\r\n    //查询\r\n    tableQuery(params) {\r\n      Object.assign(params, this.pageParams, {\r\n        pageNumber: params.pageNum,\r\n      });\r\n      delete params.pageNum;\r\n      this.tableSet.loading = true;\r\n      getAmmeterInfo(params).then((res) => {\r\n        this.tableSet.loading = false;\r\n        let data = res.data.rows;\r\n        this.tableSet.total = res.data.total;\r\n        this.tableList = data;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.charge-info {\r\n  font-weight: 400;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .list-box {\r\n    margin: 0 20px 10px 20px;\r\n    padding: 10px 10px 0 10px;\r\n    background: #f6f8fa;\r\n    .list-title {\r\n      font-size: 14px;\r\n      margin-bottom: 10px;\r\n      .title {\r\n        font-weight: bold;\r\n      }\r\n    }\r\n    .list-con {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      position: relative;\r\n      .top {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        .title {\r\n          font-size: 14px;\r\n          margin-bottom: 10px;\r\n        }\r\n      }\r\n      .list-item {\r\n        width: ~\"calc(25% - 10px)\";\r\n        height: 87px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        margin: 0 5px;\r\n        margin-bottom: 10px;\r\n        padding: 10px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        cursor: pointer;\r\n\r\n        .value {\r\n          text-align: center;\r\n          // margin: 6px 0;\r\n          font-size: 14px;\r\n          font-weight: bold;\r\n        }\r\n        .value-red {\r\n          color: red;\r\n        }\r\n        .rate {\r\n          display: flex;\r\n          .rate-1 {\r\n            width: 50%;\r\n            text-align: left;\r\n          }\r\n        }\r\n        .list-no {\r\n          text-align: center;\r\n          margin: auto;\r\n        }\r\n      }\r\n      .list-active {\r\n        background: #3581f442;\r\n      }\r\n    }\r\n  }\r\n  .list-title2 {\r\n    font-size: 14px;\r\n    margin: 0 20px;\r\n    font-weight: bold;\r\n  }\r\n  .query-btns {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n::v-deep .ivu-modal {\r\n  top: 50px !important;\r\n}\r\n::v-deep .ivu-modal-content .ivu-modal-body form {\r\n  padding: 10px 10px 0px 10px !important;\r\n}\r\n::v-deep .two-input .ivu-form-item-content {\r\n  display: flex;\r\n}\r\n</style>\r\n"]}]}