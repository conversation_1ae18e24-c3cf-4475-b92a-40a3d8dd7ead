{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\statistics\\energymeter\\list-station.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\statistics\\energymeter\\list-station.vue", "mtime": 1754285403055}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["list-station.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list-station.vue", "sourceRoot": "src/view/statistics/energymeter", "sourcesContent": ["<template>\r\n  <!-- 电表/局站分析报表 -->\r\n  <div class=\"page-class page-card common-wh\">\r\n    <div class=\"query-box\">\r\n      <Row :class=\"filterColl ? 'header-bar-show' : 'header-bar-hide'\">\r\n        <Form ref=\"queryform\" :model=\"queryParams\" :label-width=\"120\">\r\n          <Row class=\"form-row\">\r\n            <Col span=\"5\">\r\n              <FormItem label=\"数据部门:\" prop=\"company\">\r\n                <Select v-model=\"queryParams.company\" clearable>\r\n                  <Option\r\n                    v-for=\"(item, index) in dicts['company']\"\r\n                    :key=\"index\"\r\n                    :value=\"item.id\"\r\n                    >{{ item.name }}</Option\r\n                  >\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"5\">\r\n              <FormItem label=\"状态:\" prop=\"status\">\r\n                <Select v-model=\"queryParams.status\" clearable>\r\n                  <Option\r\n                    v-for=\"(item, index) in dicts['status']\"\r\n                    :key=\"index\"\r\n                    :value=\"item.typeCode\"\r\n                    >{{ item.typeName }}</Option\r\n                  >\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"5\">\r\n              <FormItem label=\"是否大工业用电:\" prop=\"isbigfactories\">\r\n                <Select v-model=\"queryParams.isbigfactories\" clearable>\r\n                  <Option\r\n                    v-for=\"(item, index) in dicts['isbigfactories']\"\r\n                    :key=\"index\"\r\n                    :value=\"item.typeCode\"\r\n                    >{{ item.typeName }}</Option\r\n                  >\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <div style=\"float: right; margin-right: 10px\">\r\n              <Button\r\n                style=\"margin-left: 5px; width: 69px\"\r\n                type=\"success\"\r\n                icon=\"ios-search\"\r\n                @click=\"_onSearchHandle\"\r\n                >搜索\r\n              </Button>\r\n              <Button\r\n                style=\"margin-left: 5px; width: 69px\"\r\n                type=\"info\"\r\n                icon=\"ios-redo\"\r\n                @click=\"_onResetHandle\"\r\n                >重置</Button\r\n              >\r\n            </div>\r\n          </Row>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon\r\n          :type=\"filterColl ? 'md-arrow-dropup' : 'md-arrow-dropdown'\"\r\n          size=\"20\"\r\n          @click=\"filterColl = !filterColl\"\r\n          :color=\"filterColl ? '#000' : '#1ab394'\"\r\n        ></icon>\r\n      </div>\r\n    </div>\r\n    <cl-table\r\n      ref=\"clTable\"\r\n      title=\"列表\"\r\n      :height=\"tableHeight\"\r\n      :query-params=\"queryParams\"\r\n      :columns=\"tableSet.columns\"\r\n      :loading=\"tableSet.loading\"\r\n      :total=\"tableSet.total\"\r\n      :pageSize=\"tableSet.pageSize\"\r\n      :showPage=\"tableSet.showPage\"\r\n      :data=\"tableList\"\r\n      :sum-columns=\"[]\"\r\n      @on-query=\"tableQuery\"\r\n      :searchable=\"false\"\r\n      :exportable=\"false\"\r\n    >\r\n      <div slot=\"buttons\" class=\"table-btns\">\r\n        <Button type=\"default\" @click=\"exportCsv\">导出</Button>\r\n      </div>\r\n    </cl-table>\r\n    <!-- 弹窗：明细 -->\r\n    <modal-list title=\"局站明细\" ref=\"list\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { getUserByUserRole } from \"@/api/basedata/ammeter.js\";\r\nimport { getStationList } from \"@/api/statistics/index\";\r\nimport pageFun from \"@/mixins/pageFun\";\r\nimport { blist, btext } from \"@/libs/tools\";\r\nimport axios from \"@/libs/api.request\";\r\nimport modalList from \"./modal-list\";\r\n\r\nexport default {\r\n  mixins: [pageFun],\r\n  props: [\"flag\"],\r\n  components: { modalList },\r\n  data() {\r\n    return {\r\n      //搜索面板\r\n      filterColl: true, //搜索面板展开\r\n      queryParams: {\r\n        //查询参数\r\n        company: null,\r\n        isbigfactories: null,\r\n        status: null,\r\n      },\r\n      queryedParams: {}, //搜索后的参数\r\n      dicts: {\r\n        company: [], //数据部门\r\n        isbigfactories: [], //是否大工业用电\r\n        status: [], //状态\r\n      },\r\n      //--搜索面板end--\r\n      tableSet: {\r\n        loading: false,\r\n        showPage: true,\r\n        pageTotal: 0,\r\n        pageNumber: 1,\r\n        pageSize: 10, //当前页\r\n        columns: [\r\n          {\r\n            title: \"地市\",\r\n            key: \"orgName\",\r\n            fixed: \"left\",\r\n            width: 150,\r\n            align: \"center\",\r\n          },\r\n        ],\r\n      },\r\n      tableList: [],\r\n      spinShow: false,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.handleHeight(); //table高度自定义\r\n    this.handleColumn();\r\n    this.getDicts(); //部门下拉\r\n  },\r\n  methods: {\r\n    handleColumn() {\r\n      let dicts = [\r\n        {\r\n          name: \"生产用房-通信机房\",\r\n          key: \"productionBuilding10001\",\r\n          stationtype: 10001,\r\n        },\r\n        {\r\n          name: \"生产用房-移动基站\",\r\n          key: \"productionBuilding10002\",\r\n          stationtype: 10002,\r\n        },\r\n        {\r\n          name: \"生产用房-数据中心-对外IDC机柜机房\",\r\n          key: \"productionBuilding10003\",\r\n          stationtype: 10003,\r\n        },\r\n        {\r\n          name: \"生产用房-数据中心-自用业务平台和IT支撑用房\",\r\n          key: \"productionBuilding10004\",\r\n          stationtype: 10004,\r\n        },\r\n        {\r\n          name: \"生产用房-接入局所及室外机柜\",\r\n          key: \"productionBuilding10005\",\r\n          stationtype: 10005,\r\n        },\r\n        {\r\n          name: \"生产用房-其他\",\r\n          key: \"productionBuildingOther\",\r\n          stationtype: -1,\r\n        },\r\n        {\r\n          name: \"非生产用房-管理用房\",\r\n          key: \"nonProductionBuilding20001\",\r\n          stationtype: 20001,\r\n        },\r\n        {\r\n          name: \"非生产用房-渠道用房\",\r\n          key: \"nonProductionBuilding20002\",\r\n          stationtype: 20002,\r\n        },\r\n        {\r\n          name: \"非生产用房-其他\",\r\n          key: \"nonProductionBuildingOther\",\r\n          stationtype: -2,\r\n        },\r\n      ];\r\n      let arr = [];\r\n      let that = this;\r\n      dicts.forEach((item) => {\r\n        arr.push({\r\n          title: item.name,\r\n          align: \"center\",\r\n          children: [\r\n            {\r\n              title: \"数量\",\r\n              align: \"center\",\r\n              width: 130,\r\n              key: `${item.key}`,\r\n              render: (h, params) => {\r\n                let column = params.column.key;\r\n                let row = params.row;\r\n                let info = h(\r\n                  \"u\",\r\n                  {\r\n                    style: {\r\n                      color: \"green\",\r\n                    },\r\n                    on: {\r\n                      click() {\r\n                        that.toOpenModal(row, item);\r\n                      },\r\n                    },\r\n                  },\r\n                  row[column]\r\n                );\r\n                let info2 = h(\"span\", {}, row[column]);\r\n                if (row.orgId) {\r\n                  return h(\"div\", [info]);\r\n                } else {\r\n                  return h(\"div\", [info2]);\r\n                }\r\n              },\r\n            },\r\n            {\r\n              title: \"占比\",\r\n              align: \"center\",\r\n              width: 100,\r\n              key: `${item.key}Percentage`,\r\n            },\r\n          ],\r\n        });\r\n      });\r\n      this.tableSet.columns = this.tableSet.columns.concat(arr);\r\n    },\r\n    //打开弹窗\r\n    toOpenModal(row, item) {\r\n      let params = {\r\n        company: row.orgId,\r\n        isbigfactories: this.queryedParams.isbigfactories,\r\n        status: this.queryedParams.status,\r\n        stationtype: item.stationtype,\r\n      };\r\n      this.$refs[\"list\"].openModal(params);\r\n    },\r\n    //表格-筛选-重置\r\n    _onResetHandle() {\r\n      this.$refs[\"queryform\"].resetFields();\r\n      this.queryParams.company = this.dicts.company[0] && this.dicts.company[0].id;\r\n      this._onSearchHandle(); //搜索列表\r\n    },\r\n    //表格-筛选-搜索\r\n    _onSearchHandle() {\r\n      this.queryedParams = { ...this.queryParams };\r\n      this.$refs.clTable.query(this.queryedParams);\r\n    },\r\n    //查询\r\n    tableQuery(params) {\r\n      Object.assign(params, {\r\n        company: params.company == \"-1\" ? \"\" : params.company,\r\n        pageNumber: params.pageNum,\r\n      });\r\n      delete params.pageNum;\r\n      this.tableSet.loading = true;\r\n      getStationList(params).then((res) => {\r\n        this.tableSet.loading = false;\r\n        let data = res.data.rows;\r\n        this.tableSet.total = res.data.total;\r\n        this.tableList = data;\r\n      });\r\n    },\r\n    //导出\r\n    exportCsv() {\r\n      if (this.tableSet.total == 0) {\r\n        this.$Message.warning(\"暂无数据可导出\");\r\n        return;\r\n      }\r\n      this.exportLoading();\r\n      let params = this.$refs.clTable.insideQueryParams;\r\n      params.pageNumber = 1;\r\n      params.pageSize = this.tableSet.total;\r\n      axios\r\n        .file({\r\n          url: \"/business/cost/extAndTransElec/export\",\r\n          method: \"get\",\r\n          params: params,\r\n        })\r\n        .then((res) => {\r\n          const content = res;\r\n          const blob = new Blob([content]);\r\n\r\n          let fileName = `电表/局站分析报表.xlsx`;\r\n          if (\"download\" in document.createElement(\"a\")) {\r\n            // 非IE下载\r\n            const elink = document.createElement(\"a\");\r\n            elink.download = fileName;\r\n            elink.style.display = \"none\";\r\n            elink.href = URL.createObjectURL(blob);\r\n            document.body.appendChild(elink);\r\n            elink.click();\r\n            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n            document.body.removeChild(elink);\r\n          } else {\r\n            // IE10+下载\r\n            navigator.msSaveBlob(blob, fileName);\r\n          }\r\n          this.$Spin.hide();\r\n        });\r\n    },\r\n    exportLoading() {\r\n      this.$Spin.show({\r\n        render: (h) => {\r\n          return h(\"div\", [\r\n            h(\"Progress\", {\r\n              style: {\r\n                width: \"800px\",\r\n              },\r\n            }),\r\n            h(\"div\", \"导出中，请勿刷新页面......\"),\r\n          ]);\r\n        },\r\n      });\r\n    },\r\n    //下拉选项\r\n    getDicts() {\r\n      this.dicts.status = blist(\"status\"); //状态\r\n      this.dicts.isbigfactories = [\r\n        {\r\n          typeCode: \"1\",\r\n          typeName: \"是\",\r\n        },\r\n        {\r\n          typeCode: \"0\",\r\n          typeName: \"否\",\r\n        },\r\n      ]; //是否大工业用电\r\n\r\n      //数据部门/所属分公司\r\n      getUserByUserRole().then((res) => {\r\n        let { companies, isProAdmin } = res.data;\r\n        if (isProAdmin) {\r\n          companies.unshift({\r\n            name: \"全省\",\r\n            id: \"-1\",\r\n          });\r\n        }\r\n        this.dicts.company = companies;\r\n        this._onResetHandle(); //搜索列表\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.page-class {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n</style>\r\n"]}]}