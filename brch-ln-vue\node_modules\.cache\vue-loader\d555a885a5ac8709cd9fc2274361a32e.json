{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addCoalAccount.vue?vue&type=template&id=dc653390&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addCoalAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}