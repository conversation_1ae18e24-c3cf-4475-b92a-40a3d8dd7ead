{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\budget\\budgetmanage\\modal-mon.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\budget\\budgetmanage\\modal-mon.vue", "mtime": 1754285403023}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": 1741317456427}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXM2Lm9iamVjdC5rZXlzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXM2Lm51bWJlci5jb25zdHJ1Y3RvciI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzNi5vYmplY3QuYXNzaWduIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS5pdGVyYWJsZSI7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCmltcG9ydCB7IGdldEJ1ZGdldE1hbmFNb25JbmZvLCBnZXRCdWRnZXRNYW5hTW9uRWRpdCB9IGZyb20gIkAvYXBpL2J1ZGdldC9pbmRleCI7CmltcG9ydCB7IGlzRW1wdHkgfSBmcm9tICJAL2xpYnMvdmFsaWRhdGUuanMiOwpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHNob3dNb2RhbDogZmFsc2UsCiAgICAgIHJ1bGVWYWxpZGF0ZToge30sCiAgICAgIGZvcm1EYXRhOiB7CiAgICAgICAgY2l0eTogIiIsCiAgICAgICAgLy/nlKjkuo7mj5DkuqQKICAgICAgICBidWRnZXRJZDogIiIsCiAgICAgICAgLy/nlKjkuo7mj5DkuqQKICAgICAgICB5ZWFyOiAiIiwKICAgICAgICBidWRnZXRBbW91bnQ6ICIiLAogICAgICAgIHN1YkJ1ZGdldEFtb3VudDogIiIsCiAgICAgICAgc2luZ2xlOiAiYXZlcmFnZSIKICAgICAgfSwKICAgICAgbGlzdEZvcm06IFtdCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIGZvciAodmFyIGkgPSAwOyBpIDwgMTI7IGkrKykgewogICAgICB0aGlzLmZvcm1EYXRhWyJidWRnZXRBbW91bnQiLmNvbmNhdChpICsgMSldID0gbnVsbDsKICAgICAgdGhpcy5saXN0Rm9ybS5wdXNoKHsKICAgICAgICBuYW1lOiAiIi5jb25jYXQoaSArIDEsICJcdTY3MDgiKSwKICAgICAgICBrZXk6ICJidWRnZXRBbW91bnQiLmNvbmNhdChpICsgMSkKICAgICAgfSk7CiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgdGVzdDogZnVuY3Rpb24gdGVzdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKCiAgICAgIHZhciBkYXRhID0gbnVsbDsKICAgICAgT2JqZWN0LmFzc2lnbih0aGlzLmZvcm1EYXRhKS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICBpZiAoL2J1ZGdldEFtb3VudC8udGVzdChrZXkpKSB7CiAgICAgICAgICBjb25zb2xlLmxvZygiZGRkIiwgX3RoaXMuZm9ybURhdGFba2V5XSk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgcmV0dXJuIGRhdGE7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+W8ueeqlzrmiZPlvIAKICAgIG9wZW5Nb2RhbDogZnVuY3Rpb24gb3Blbk1vZGFsKCkgewogICAgICB0aGlzLnNob3dNb2RhbCA9IHRydWU7CiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgfSwKICAgIGZvcm1JbnB1dENoYW5nZTogZnVuY3Rpb24gZm9ybUlucHV0Q2hhbmdlKCkgewogICAgICBpZiAodGhpcy5mb3JtRGF0YS5zaW5nbGUgPT0gImF2ZXJhZ2UiKSB7CiAgICAgICAgdmFyIGRhdGEgPSBOdW1iZXIoKHRoaXMuZm9ybURhdGEuYnVkZ2V0QW1vdW50IC8gMTIpLnRvRml4ZWQoMikpOwoKICAgICAgICBmb3IgKHZhciBrZXkgaW4gdGhpcy5mb3JtRGF0YSkgewogICAgICAgICAgaWYgKC9idWRnZXRBbW91bnQvLnRlc3Qoa2V5KSAmJiAvWzAtOV0vLnRlc3Qoa2V5KSkgewogICAgICAgICAgICB0aGlzLmZvcm1EYXRhW2tleV0gPSBkYXRhOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgdGhpcy5pbnB1dENoYW5nZSgpOwogICAgfSwKICAgIGlucHV0Q2hhbmdlOiBmdW5jdGlvbiBpbnB1dENoYW5nZSgpIHsKICAgICAgdmFyIHN1bSA9IG51bGw7CgogICAgICBmb3IgKHZhciBrZXkgaW4gdGhpcy5mb3JtRGF0YSkgewogICAgICAgIGlmICgvYnVkZ2V0QW1vdW50Ly50ZXN0KGtleSkgJiYgL1swLTldLy50ZXN0KGtleSkpIHsKICAgICAgICAgIHN1bSArPSBOdW1iZXIodGhpcy5mb3JtRGF0YVtrZXldKTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIGlmICh0aGlzLmZvcm1EYXRhLmJ1ZGdldEFtb3VudCkgewogICAgICAgIHRoaXMuZm9ybURhdGEuc3ViQnVkZ2V0QW1vdW50ID0gKE51bWJlcih0aGlzLmZvcm1EYXRhLmJ1ZGdldEFtb3VudCkgLSBzdW0pLnRvRml4ZWQoMik7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5mb3JtRGF0YS5zdWJCdWRnZXRBbW91bnQgPSAwOwogICAgICB9CiAgICB9LAogICAgLy/ojrflj5bmlbDmja4KICAgIGdldERhdGE6IGZ1bmN0aW9uIGdldERhdGEoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwoKICAgICAgdGhpcy5jbGVhckZvcm0oKTsKICAgICAgdmFyIHllYXIgPSBuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCk7CiAgICAgIGdldEJ1ZGdldE1hbmFNb25JbmZvKHllYXIpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIHZhciBkYXRhID0gcmVzLmRhdGE7CiAgICAgICAgT2JqZWN0LmtleXMoX3RoaXMyLmZvcm1EYXRhKS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICAgIF90aGlzMi5mb3JtRGF0YVtrZXldID0gZGF0YVtrZXldOwogICAgICAgIH0pOwogICAgICAgIHZhciBtb25zID0gW107CgogICAgICAgIGZvciAodmFyIGtleSBpbiBfdGhpczIuZm9ybURhdGEpIHsKICAgICAgICAgIGlmICgvYnVkZ2V0QW1vdW50Ly50ZXN0KGtleSkgJiYgL1swLTldLy50ZXN0KGtleSkpIHsKICAgICAgICAgICAgbW9ucy5wdXNoKF90aGlzMi5mb3JtRGF0YVtrZXldKTsKICAgICAgICAgIH0KICAgICAgICB9IC8v5Yik5pat5piv5ZCm5Z2H5YC844CB6Ieq5a6a5LmJCgoKICAgICAgICBfdGhpczIuZm9ybURhdGEuc2luZ2xlID0gbW9ucy5ldmVyeShmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuIGl0ZW0gPT0gbW9uc1swXTsKICAgICAgICB9KSA/ICJhdmVyYWdlIiA6ICJhdXRvIjsKCiAgICAgICAgX3RoaXMyLmlucHV0Q2hhbmdlKCk7CgogICAgICAgIF90aGlzMi4kZm9yY2VVcGRhdGUoKTsKICAgICAgfSk7CiAgICB9LAogICAgb25Nb2RhbE9LOiBmdW5jdGlvbiBvbk1vZGFsT0soKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwoKICAgICAgdGhpcy4kcmVmcy5teWZvcm0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAvL+mihOeul+aAu+WSjD4g5bm05bqm6aKE566XCiAgICAgICAgICBpZiAoX3RoaXMzLmZvcm1EYXRhLnN1YkJ1ZGdldEFtb3VudCA8IDApIHsKICAgICAgICAgICAgX3RoaXMzLiRNZXNzYWdlLndhcm5pbmcoIuWQhOaciOmihOeul+aAu+WSjD7lubTluqbpooTnrpcs6K+36LCD5pW0Iik7CgogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CgogICAgICAgICAgX3RoaXMzLmJ0bmxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgdmFyIHllYXIgPSBuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCk7CiAgICAgICAgICBPYmplY3QuYXNzaWduKF90aGlzMy5mb3JtRGF0YSwgewogICAgICAgICAgICB5ZWFyOiB5ZWFyCiAgICAgICAgICB9KTsKICAgICAgICAgIGdldEJ1ZGdldE1hbmFNb25FZGl0KF90aGlzMy5mb3JtRGF0YSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgIF90aGlzMy5idG5sb2FkaW5nID0gZmFsc2U7CgogICAgICAgICAgICBfdGhpczMuJGVtaXQoInJlZnJlc2giKTsKCiAgICAgICAgICAgIF90aGlzMy5jbGVhckZvcm0oKTsKCiAgICAgICAgICAgIF90aGlzMy4kTWVzc2FnZS5zdWNjZXNzKCLosIPmlbTmiJDlip8iKTsKCiAgICAgICAgICAgIF90aGlzMy5zaG93TW9kYWwgPSBmYWxzZTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgY2xlYXJGb3JtOiBmdW5jdGlvbiBjbGVhckZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbIm15Zm9ybSJdLnJlc2V0RmllbGRzKCk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "sources": ["modal-mon.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA,SAAA,oBAAA,EAAA,oBAAA,QAAA,oBAAA;AACA,SAAA,OAAA,QAAA,oBAAA;AAEA,eAAA;AACA,EAAA,IADA,kBACA;AACA,WAAA;AACA,MAAA,SAAA,EAAA,KADA;AAEA,MAAA,YAAA,EAAA,EAFA;AAGA,MAAA,QAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AACA;AACA,QAAA,QAAA,EAAA,EAFA;AAEA;AACA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,YAAA,EAAA,EAJA;AAKA,QAAA,eAAA,EAAA,EALA;AAMA,QAAA,MAAA,EAAA;AANA,OAHA;AAWA,MAAA,QAAA,EAAA;AAXA,KAAA;AAaA,GAfA;AAgBA,EAAA,OAhBA,qBAgBA;AACA,SAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA,WAAA,QAAA,uBAAA,CAAA,GAAA,CAAA,KAAA,IAAA;AACA,WAAA,QAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,YAAA,CAAA,GAAA,CAAA,WADA;AAEA,QAAA,GAAA,wBAAA,CAAA,GAAA,CAAA;AAFA,OAAA;AAIA;AACA,GAxBA;AAyBA,EAAA,QAAA,EAAA;AACA,IAAA,IADA,kBACA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,MAAA,CAAA,MAAA,CAAA,KAAA,QAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,KAAA,EAAA,KAAA,CAAA,QAAA,CAAA,GAAA,CAAA;AACA;AACA,OAJA;AAKA,aAAA,IAAA;AACA;AATA,GAzBA;AAoCA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,SAFA,uBAEA;AACA,WAAA,SAAA,GAAA,IAAA;AACA,WAAA,OAAA;AACA,KALA;AAMA,IAAA,eANA,6BAMA;AACA,UAAA,KAAA,QAAA,CAAA,MAAA,IAAA,SAAA,EAAA;AACA,YAAA,IAAA,GAAA,MAAA,CAAA,CAAA,KAAA,QAAA,CAAA,YAAA,GAAA,EAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA;;AACA,aAAA,IAAA,GAAA,IAAA,KAAA,QAAA,EAAA;AACA,cAAA,eAAA,IAAA,CAAA,GAAA,KAAA,QAAA,IAAA,CAAA,GAAA,CAAA,EAAA;AACA,iBAAA,QAAA,CAAA,GAAA,IAAA,IAAA;AACA;AACA;AACA;;AACA,WAAA,WAAA;AACA,KAhBA;AAiBA,IAAA,WAjBA,yBAiBA;AACA,UAAA,GAAA,GAAA,IAAA;;AACA,WAAA,IAAA,GAAA,IAAA,KAAA,QAAA,EAAA;AACA,YAAA,eAAA,IAAA,CAAA,GAAA,KAAA,QAAA,IAAA,CAAA,GAAA,CAAA,EAAA;AACA,UAAA,GAAA,IAAA,MAAA,CAAA,KAAA,QAAA,CAAA,GAAA,CAAA,CAAA;AACA;AACA;;AACA,UAAA,KAAA,QAAA,CAAA,YAAA,EAAA;AACA,aAAA,QAAA,CAAA,eAAA,GAAA,CACA,MAAA,CAAA,KAAA,QAAA,CAAA,YAAA,CAAA,GAAA,GADA,EAEA,OAFA,CAEA,CAFA,CAAA;AAGA,OAJA,MAIA;AACA,aAAA,QAAA,CAAA,eAAA,GAAA,CAAA;AACA;AACA,KA/BA;AAgCA;AACA,IAAA,OAjCA,qBAiCA;AAAA;;AACA,WAAA,SAAA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,GAAA,WAAA,EAAA;AACA,MAAA,oBAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,EAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,GAAA,IAAA,IAAA,CAAA,GAAA,CAAA;AACA,SAFA;AAGA,YAAA,IAAA,GAAA,EAAA;;AACA,aAAA,IAAA,GAAA,IAAA,MAAA,CAAA,QAAA,EAAA;AACA,cAAA,eAAA,IAAA,CAAA,GAAA,KAAA,QAAA,IAAA,CAAA,GAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA;AACA;AACA,SAVA,CAWA;;;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,MAAA,GAAA,IAAA,CAAA,KAAA,CAAA,UAAA,IAAA;AAAA,iBAAA,IAAA,IAAA,IAAA,CAAA,CAAA,CAAA;AAAA,SAAA,IAAA,SAAA,GAAA,MAAA;;AAEA,QAAA,MAAA,CAAA,WAAA;;AACA,QAAA,MAAA,CAAA,YAAA;AACA,OAhBA;AAiBA,KArDA;AAsDA,IAAA,SAtDA,uBAsDA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,eAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,iBAAA;;AACA;AACA;;AACA,UAAA,MAAA,CAAA,UAAA,GAAA,IAAA;AACA,cAAA,IAAA,GAAA,IAAA,IAAA,GAAA,WAAA,EAAA;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,QAAA,EAAA;AACA,YAAA,IAAA,EAAA;AADA,WAAA;AAGA,UAAA,oBAAA,CAAA,MAAA,CAAA,QAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA,UAAA,GAAA,KAAA;;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,SAAA;;AACA,YAAA,MAAA,CAAA,SAAA;;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,YAAA,MAAA,CAAA,SAAA,GAAA,KAAA;AACA,WANA;AAOA;AACA,OApBA;AAqBA,KA5EA;AA6EA,IAAA,SA7EA,uBA6EA;AACA,WAAA,KAAA,CAAA,QAAA,EAAA,WAAA;AACA;AA/EA;AApCA,CAAA", "sourcesContent": ["<template>\r\n  <Modal v-model=\"showModal\" title=\"调整每月预算\" :width=\"1000\">\r\n    <div class=\"page-class common-wh\">\r\n      <Form ref=\"myform\" :model=\"formData\" :label-width=\"120\" :rules=\"ruleValidate\">\r\n        <div class=\"form-title\">全年预算总额</div>\r\n        <Row>\r\n          <Col span=\"8\">\r\n            <FormItem label=\"年份:\" prop=\"year\">\r\n              <Input :clearable=\"true\" v-model=\"formData.year\" disabled>\r\n                <span slot=\"append\">元</span>\r\n              </Input>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"8\">\r\n            <FormItem label=\"年度预算:\" prop=\"budgetAmount\">\r\n              <Input :clearable=\"true\" v-model=\"formData.budgetAmount\" disabled>\r\n                <span slot=\"append\">元</span>\r\n              </Input>\r\n            </FormItem>\r\n          </Col>\r\n          <Col span=\"8\">\r\n            <FormItem label=\"剩余预算:\" prop=\"subBudgetAmount\">\r\n              <Input :clearable=\"true\" v-model=\"formData.subBudgetAmount\" disabled>\r\n                <span slot=\"append\">元</span>\r\n              </Input>\r\n            </FormItem>\r\n          </Col>\r\n        </Row>\r\n        <div class=\"form-title\">每月预算设置</div>\r\n        <RadioGroup v-model=\"formData.single\" @on-change=\"formInputChange\">\r\n          <Radio label=\"average\">年度预算均值</Radio>\r\n          <Radio label=\"auto\">自定义</Radio>\r\n        </RadioGroup>\r\n        <div class=\"list-view\">\r\n          <div class=\"list-item\" v-for=\"(item, index) in 3\" :key=\"'a' + index\">\r\n            <div class=\"label\">月份</div>\r\n            <div class=\"label\">预算</div>\r\n          </div>\r\n          <template v-for=\"(item, index) in listForm\">\r\n            <div class=\"list-item\" :key=\"index\">\r\n              <div class=\"label\">{{ item.name }}</div>\r\n              <div class=\"input\">\r\n                <InputNumber\r\n                  :min=\"0\"\r\n                  v-model=\"formData[item.key]\"\r\n                  :disabled=\"formData.budgetAmount && formData.single == 'average'\"\r\n                  @on-change=\"inputChange\"\r\n                >\r\n                </InputNumber>\r\n              </div>\r\n              <!-- <Input\r\n                class=\"input\"\r\n                type=\"number\"\r\n                :min=\"1\"\r\n                v-model=\"formData[item.key]\"\r\n                :disabled=\"formData.budgetAmount && formData.single == 'average'\"\r\n                @on-change=\"inputChange\"\r\n              > -->\r\n            </div>\r\n          </template>\r\n        </div>\r\n      </Form>\r\n    </div>\r\n    <div slot=\"footer\">\r\n      <Button type=\"default\" class=\"cancelBtn\" @click=\"showModal = false\">取消</Button>\r\n      <Button type=\"primary\" class=\"okBtn\" @click=\"onModalOK\">确认</Button>\r\n    </div>\r\n  </Modal>\r\n</template>\r\n<script>\r\nimport { getBudgetManaMonInfo, getBudgetManaMonEdit } from \"@/api/budget/index\";\r\nimport { isEmpty } from \"@/libs/validate.js\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      showModal: false,\r\n      ruleValidate: {},\r\n      formData: {\r\n        city: \"\", //用于提交\r\n        budgetId: \"\", //用于提交\r\n        year: \"\",\r\n        budgetAmount: \"\",\r\n        subBudgetAmount: \"\",\r\n        single: \"average\",\r\n      },\r\n      listForm: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    for (let i = 0; i < 12; i++) {\r\n      this.formData[`budgetAmount${i + 1}`] = null;\r\n      this.listForm.push({\r\n        name: `${i + 1}月`,\r\n        key: `budgetAmount${i + 1}`,\r\n      });\r\n    }\r\n  },\r\n  computed: {\r\n    test() {\r\n      let data = null;\r\n      Object.assign(this.formData).forEach((key) => {\r\n        if (/budgetAmount/.test(key)) {\r\n          console.log(\"ddd\", this.formData[key]);\r\n        }\r\n      });\r\n      return data;\r\n    },\r\n  },\r\n  methods: {\r\n    //弹窗:打开\r\n    openModal() {\r\n      this.showModal = true;\r\n      this.getData();\r\n    },\r\n    formInputChange() {\r\n      if (this.formData.single == \"average\") {\r\n        let data = Number((this.formData.budgetAmount / 12).toFixed(2));\r\n        for (let key in this.formData) {\r\n          if (/budgetAmount/.test(key) && /[0-9]/.test(key)) {\r\n            this.formData[key] = data;\r\n          }\r\n        }\r\n      }\r\n      this.inputChange();\r\n    },\r\n    inputChange() {\r\n      let sum = null;\r\n      for (let key in this.formData) {\r\n        if (/budgetAmount/.test(key) && /[0-9]/.test(key)) {\r\n          sum += Number(this.formData[key]);\r\n        }\r\n      }\r\n      if (this.formData.budgetAmount) {\r\n        this.formData.subBudgetAmount = (\r\n          Number(this.formData.budgetAmount) - sum\r\n        ).toFixed(2);\r\n      } else {\r\n        this.formData.subBudgetAmount = 0;\r\n      }\r\n    },\r\n    //获取数据\r\n    getData() {\r\n      this.clearForm();\r\n      let year = new Date().getFullYear();\r\n      getBudgetManaMonInfo(year).then((res) => {\r\n        let data = res.data;\r\n        Object.keys(this.formData).forEach((key) => {\r\n          this.formData[key] = data[key];\r\n        });\r\n        let mons = [];\r\n        for (let key in this.formData) {\r\n          if (/budgetAmount/.test(key) && /[0-9]/.test(key)) {\r\n            mons.push(this.formData[key]);\r\n          }\r\n        }\r\n        //判断是否均值、自定义\r\n        this.formData.single = mons.every((item) => item == mons[0]) ? \"average\" : \"auto\";\r\n\r\n        this.inputChange();\r\n        this.$forceUpdate();\r\n      });\r\n    },\r\n    onModalOK() {\r\n      this.$refs.myform.validate((valid) => {\r\n        if (valid) {\r\n          //预算总和> 年度预算\r\n          if (this.formData.subBudgetAmount < 0) {\r\n            this.$Message.warning(\"各月预算总和>年度预算,请调整\");\r\n            return;\r\n          }\r\n          this.btnloading = true;\r\n          let year = new Date().getFullYear();\r\n          Object.assign(this.formData, {\r\n            year,\r\n          });\r\n          getBudgetManaMonEdit(this.formData).then((res) => {\r\n            this.btnloading = false;\r\n            this.$emit(\"refresh\");\r\n            this.clearForm();\r\n            this.$Message.success(\"调整成功\");\r\n            this.showModal = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    clearForm() {\r\n      this.$refs[\"myform\"].resetFields();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.list-view {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-bottom: 10px;\r\n  .list-item {\r\n    display: flex;\r\n    align-items: center;\r\n    width: 33.3%;\r\n    .label {\r\n      height: 100%;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n    }\r\n    .label,\r\n    .input {\r\n      width: 50%;\r\n      height: 42px;\r\n      border: 1px solid #eee;\r\n      padding: 4px 10px;\r\n    }\r\n  }\r\n}\r\n::v-deep .ivu-radio-wrapper {\r\n  font-weight: normal;\r\n  font-size: 13px;\r\n  margin: 10px 0 20px 10px;\r\n  &:first-child {\r\n    margin-right: 50px;\r\n  }\r\n}\r\n</style>\r\n"], "sourceRoot": "src/view/budget/budgetmanage"}]}