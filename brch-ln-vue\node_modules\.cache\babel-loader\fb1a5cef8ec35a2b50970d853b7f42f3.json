{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addPreOilAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addPreOilAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["addPreOilAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiMA,OAAA,eAAA,MAAA,gCAAA;AACA,SACA,gBADA,EAEA,YAFA,EAGA,cAHA,QAIA,0BAJA;AAKA,OAAA,sBAAA,MAAA,6CAAA;AACA,OAAA,WAAA,MAAA,kCAAA;AACA,OAAA,UAAA,MAAA,iCAAA;AACA,SAAA,QAAA,EAAA,UAAA,QAAA,mCAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AACA,OAAA,aAAA,MAAA,iBAAA;AACA,SAAA,oBAAA,QAAA,+BAAA;AACA,OAAA,UAAA,MAAA,yBAAA;AACA,SAAA,aAAA,QAAA,sBAAA;AACA,SAAA,UAAA,QAAA,mDAAA;AACA,OAAA,iBAAA,MAAA,qBAAA;AACA,OAAA,SAAA,MAAA,gBAAA;AACA,OAAA,YAAA,MAAA,sCAAA;AACA,SAAA,WAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,kBAAA,QAAA,2BAAA;AACA,IAAA,KAAA,GAAA,QAAA,EAAA;AACA,eAAA;AACA,EAAA,IAAA,EAAA,kBADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,eAAA,EAAA,eAAA;AAAA,IAAA,UAAA,EAAA,UAAA;AAAA,IAAA,WAAA,EAAA,WAAA;AAAA,IAAA,sBAAA,EAAA,sBAAA;AAAA,IAAA,iBAAA,EAAA,iBAAA;AAAA,IAAA,aAAA,EAAA,aAAA;AAAA,IAAA,UAAA,EAAA,UAAA;AAAA,IAAA,YAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AAAA;;AACA,QAAA,KAAA,GAAA,SAAA,KAAA,CAAA,CAAA,QAAA;AAAA,UAAA,GAAA,QAAA,GAAA;AAAA,UAAA,KAAA,QAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA;AACA,UAAA,GAAA,GAAA,EAAA;;AACA,UAAA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,IAAA,IAAA,EAAA;AACA,QAAA,GAAA,GAAA,MAAA;AACA;;AACA,aAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,GAAA,EAAA;AACA,QAAA,EAAA,EAAA;AACA,UAAA,KADA,mBACA;AACA;AACA,gBAAA,GAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,WAAA,IAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,GAAA;AACA;AACA;AANA;AADA,OAAA,EASA,GATA,CAAA,CAAA,CAAA;AAUA,KAhBA;;AAiBA,WAAA;AACA,MAAA,MAAA,EAAA,EADA;AAEA,MAAA,OAAA,EAAA,EAFA;AAGA,MAAA,cAAA,EAAA,KAHA;AAIA,MAAA,WAAA,EAAA,KAJA;AAKA,MAAA,cAAA,EAAA,KALA;AAMA,MAAA,aAAA,EAAA,UANA;AAOA,MAAA,OAAA,EAAA,EAPA;AAQA,MAAA,QAAA,EAAA,KARA;AASA,MAAA,UAAA,EAAA,IATA;AASA;AACA,MAAA,SAAA,EAAA,CAAA,CAVA;AAUA;AACA,MAAA,YAAA,EAAA,CAAA,CAXA;AAWA;AACA,MAAA,OAAA,EAAA,EAZA;AAYA;AACA,MAAA,cAAA,EAAA,EAbA;AAcA,MAAA,gBAAA,EAAA,EAdA;AAeA,MAAA,aAAA,EAAA,EAfA;AAgBA,MAAA,aAAA,EAAA,EAhBA;AAiBA,MAAA,QAAA,EAAA,KAjBA;AAiBA;AACA,MAAA,UAAA,EAAA,EAlBA;AAmBA,MAAA,SAAA,EAAA,EAnBA;AAoBA,MAAA,WAAA,EAAA,EApBA;AAqBA,MAAA,OAAA,EAAA,KArBA;AAsBA,MAAA,OAAA,EAAA,IAtBA;AAsBA;AACA,MAAA,OAAA,EAAA,IAvBA;AAuBA;AACA,MAAA,WAAA,EAAA,IAxBA;AAwBA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IADA;AACA;AACA,QAAA,OAAA,EAAA,EAFA;AAEA;AACA,QAAA,OAAA,EAAA,EAHA;AAGA;AACA,QAAA,UAAA,EAAA,IAJA;AAIA;AACA,QAAA,cAAA,EAAA,CALA;AAMA,QAAA,WAAA,EAAA;AANA,OAzBA;AAkCA,MAAA,SAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,OAAA,EAAA,EAHA;AAIA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAFA,EAQA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,YAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SARA,EAcA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,cAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAdA,EAoBA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SApBA,EA0BA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA1BA,EAgCA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAhCA,EAsCA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,MAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAtCA,EAuCA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAvCA,CAJA;AA6CA,QAAA,IAAA,EAAA;AA7CA,OAlCA;AAiFA,MAAA,SAAA,EAAA,CAjFA;AAkFA,MAAA,OAAA,EAAA,CAlFA;AAmFA,MAAA,QAAA,EAAA,EAnFA,CAmFA;;AAnFA,KAAA;AAqFA,GA1GA;AA2GA,EAAA,OAAA,EAAA;AACA,IAAA,UADA,sBACA,GADA,EACA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA,EAAA,KAAA,EADA,CAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,UAAA,GAAA,CAAA,EAAA,EAAA;AACA,aAAA,KAAA,CAAA,eAAA,CAAA,MAAA,CAAA,GAAA,CAAA,EAAA,GAAA,EAAA;AACA,OAFA,MAEA;AACA,aAAA,SAAA,CAAA,aAAA;AACA,OApBA,CAqBA;AACA;;AACA,KAxBA;AAyBA,IAAA,YAzBA,0BAyBA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,YAAA,IAAA,CAAA,UAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,CAAA,CAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,IAAA;AACA,SAHA,MAGA;AACA,UAAA,kBAAA,CAAA,IAAA,CAAA,UAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA;AACA;AACA,WALA;AAMA;AACA;AACA,KAxCA;AAyCA;AACA,IAAA,oBA1CA,kCA0CA;AACA,UAAA,KAAA,UAAA,CAAA,OAAA,IAAA,IAAA,IAAA,KAAA,UAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AAAA;AACA;;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAJA,CAIA;AACA,KA/CA;AAgDA,IAAA,gBAhDA,4BAgDA,IAhDA,EAgDA;AACA,WAAA,UAAA,CAAA,OAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,UAAA,CAAA,WAAA,GAAA,IAAA,CAAA,IAAA,CAFA,CAGA;AACA,KApDA;AAqDA,IAAA,WArDA,yBAqDA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,WAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,EAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA;;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,IAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA;;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,MAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA;;AAEA,QAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,kBAAA;AACA,OAtBA;AAuBA,KA9EA;AA+EA,IAAA,UA/EA,wBA+EA;AACA,UAAA,KAAA,UAAA,CAAA,WAAA,IAAA,EAAA,EAAA;AACA,aAAA,UAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,WAAA,OAAA,GAAA,CAAA;AACA,WAAA,kBAAA;AACA,KArFA;AAsFA,IAAA,eAtFA,6BAsFA;AACA,WAAA,UAAA;AACA,KAxFA;AAyFA;AACA,IAAA,QA1FA,sBA0FA;AACA,UAAA,KAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,KAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,CAAA,GAAA,IAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA;;AACA,UAAA,CAAA,EAAA;AACA,aAAA,UAAA,CAAA,KAAA;AACA,OAFA,MAEA;AACA,aAAA,SAAA,CAAA,SAAA;AACA;AACA,KAvGA;AAwGA,IAAA,YAxGA,wBAwGA,SAxGA,EAwGA;AACA,UAAA,IAAA,GAAA,EAAA;AACA,WAAA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA;AACA,QAAA,SAAA,CAAA,GAAA,CAAA,UAAA,KAAA,EAAA;AACA,cAAA,KAAA,IAAA,KAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,SAJA;AAKA,OANA;AAOA,WAAA,MAAA,GAAA,IAAA;AACA,KAlHA;AAoHA;AACA,IAAA,UArHA,sBAqHA,IArHA,EAqHA;AAAA;;AACA,UAAA,CAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,GAAA,CAAA;AACA,YAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,YAAA,SAAA,GAAA,KAAA,UAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA;AACA,cAAA,IAAA,CAAA,EAAA,IAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,SAAA,GAAA,SAAA;AACA;;AACA,UAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,cAAA,GAAA,CAAA;AACA,UAAA,UAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,MAAA;AACA,SATA;AAUA,QAAA,IAAA,CAAA,GAAA,GAAA,CAAA;;AACA,YAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,GAAA;AACA;;AACA,YAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,cAAA,CAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,aAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,MADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;;AAKA,cAAA,MAAA,CAAA,kBAAA;AACA;AACA,WATA;AAUA;AACA;AACA,KAxJA;AAyJA,IAAA,gBAzJA,8BAyJA;AACA,UAAA,WAAA,GAAA,IAAA,IAAA,EAAA;AACA,UAAA,WAAA,GAAA,WAAA,CAAA,WAAA,EAAA;AACA,UAAA,YAAA,GAAA,WAAA,CAAA,QAAA,KAAA,CAAA;;AACA,UAAA,QAAA,KAAA,SAAA,CAAA,IAAA,EAAA;AACA,aAAA,SAAA,CAAA,IAAA,GAAA,EAAA;AACA;;AACA,WAAA,SAAA,CAAA,OAAA;AACA,WAAA,SAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AACA;AACA;AACA,QAAA,SAAA,EAAA,KAAA,UAAA,CAAA,SAAA,IAAA,CAAA,CAAA,IAAA,KAAA,UAAA,CAAA,SAAA,IAAA,SAAA,GAAA,WAAA,GAAA,EAAA,GAAA,YAAA,GAAA,KAAA,UAAA,CAAA,SAHA;AAIA,QAAA,UAAA,EAAA,EAJA;AAKA,QAAA,YAAA,EAAA,EALA;AAMA,QAAA,SAAA,EAAA,GANA;AAOA,QAAA,SAAA,EAAA,GAPA;AAQA,QAAA,SAAA,EAAA,GARA;AASA,QAAA,MAAA,EAAA;AATA,OAAA;AAWA,WAAA,SAAA,CAAA,OAAA,GAAA,KAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,UAAA,EAAA,QADA;AAEA,QAAA,YAAA,EAAA,QAFA;AAGA,QAAA,SAAA,EAAA,QAHA;AAIA,QAAA,SAAA,EAAA,QAJA;AAKA,QAAA,MAAA,EAAA;AALA,OAAA;AAQA,KArLA;AAsLA;AACA,IAAA,SAvLA,qBAuLA,GAvLA,EAuLA;AACA,WAAA,OAAA,CAAA,KAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,GAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA;AAKA,KA7LA;AA8LA,IAAA,UA9LA,sBA8LA,KA9LA,EA8LA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAEA;AARA,SAAA;AAUA;;AAEA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KAvNA;AAwNA,IAAA,cAxNA,0BAwNA,KAxNA,EAwNA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAEA;AARA,SAAA;AAUA;;AACA,WAAA,QAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KAhPA;AAiPA;AACA,IAAA,kBAlPA,gCAkPA;AAAA;;AACA,UAAA,QAAA,GAAA,KAAA,UAAA;AACA,MAAA,QAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA,MAAA,QAAA,CAAA,QAAA,GAAA,KAAA,QAAA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,4BADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,WAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,EAAA;AACA,cAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,WAFA;;AAGA,cAAA,QAAA,IAAA,EAAA;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,IAAA,GAAA,EAAA;AACA,WAFA,MAEA;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA;;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,IAAA,CAAA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,SAAA,CAAA,IAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,CAAA,CAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,CAAA,CAAA;AACA;AACA,OAjBA,EAiBA,KAjBA,CAiBA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OAnBA;AAoBA,KAhRA;AAiRA;AACA,IAAA,aAlRA,2BAkRA;AACA,WAAA,UAAA,GAAA;AACA,QAAA,SAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,KAAA,OAFA;AAGA,QAAA,UAAA,EAAA,IAHA;AAIA,QAAA,OAAA,EAAA,MAAA,CAAA,KAAA,OAAA,CAJA;AAKA,QAAA,cAAA,EAAA;AALA,OAAA;AAOA,WAAA,kBAAA;AACA,KA3RA;AA4RA;AACA,IAAA,SA7RA,qBA6RA,GA7RA,EA6RA;AACA,UAAA,SAAA,GAAA,GAAA,CAAA,SAAA;AACA,UAAA,SAAA,GAAA,GAAA,CAAA,SAAA;;AACA,UAAA,SAAA,IAAA,IAAA,IAAA,SAAA,IAAA,IAAA,EAAA;AACA,QAAA,GAAA,CAAA,SAAA,GAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA;AACA,KAnSA;AAoSA,IAAA,MApSA,oBAoSA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,WAAA;AACA;AACA;;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,oBAFA;AAGA,QAAA,IAAA,EAAA,gBAAA;AACA,cAAA,CAAA,GAAA,IAAA;AACA,cAAA,GAAA,GAAA,EAAA;AACA,cAAA,KAAA,GAAA,MAAA,CAAA,SAAA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,IAAA,GAAA,IAAA,CAAA,CAAA,CAAA;;AACA,gBAAA,IAAA,CAAA,EAAA,IAAA,IAAA,IAAA,IAAA,CAAA,EAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,kBAAA,IAAA,CAAA,OAAA,EAAA;AACA,gBAAA,CAAA,GAAA,KAAA;AACA;;AACA,cAAA,GAAA,IAAA,IAAA,CAAA,EAAA,GAAA,GAAA;AACA;AACA;;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,KAAA;;AACA,cAAA,CAAA,EAAA;AACA,gBAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,cAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,oBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,kBAAA,MAAA,CAAA,kBAAA;AACA;AACA,eALA;AAMA;AACA,WATA,MASA;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,4BAAA;AACA;AACA,SA7BA;AA8BA,QAAA,QAAA,EAAA,oBAAA,CACA;AA/BA,OAAA;AAiCA,KA3UA;AA4UA,IAAA,mBA5UA,+BA4UA,IA5UA,EA4UA;AACA,UAAA,IAAA,KAAA,SAAA,EAAA;AACA,aAAA,eAAA;AACA,OAFA,MAEA,IAAA,IAAA,KAAA,KAAA,EAAA;AACA,aAAA,kBAAA;AACA;AACA,KAlVA;AAmVA;AACA,IAAA,kBApVA,gCAoVA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,MAAA,YAAA,CAAA,KAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,aAAA;AACA,SAFA,MAEA;AACA,cAAA,GAAA,GAAA,EAAA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA;;AACA,UAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,EAAA,EAAA,EAAA,MAAA,CAAA,UAAA,CAAA,OAAA;AACA;AACA,OAZA;AAaA,KApWA;AAqWA,IAAA,eArWA,6BAqWA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,CAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,cAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SARA;;AASA,YAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,EAAA,EAAA,EAAA,KAAA,UAAA,CAAA,OAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,0BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,+BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,2BAAA;AACA;AACA;AACA,KA/XA;AAgYA,IAAA,qBAhYA,mCAgYA;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,WAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAAA,CAAA;AACA,KAlYA;AAmYA,IAAA,SAnYA,uBAmYA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,gBAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,MAAA,GAAA,IAAA,CAAA,MAAA;;AACA,cAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,KAAA;AACA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,EAAA,GAAA,GAAA;AACA,SANA;;AAOA,YAAA,CAAA,EAAA;AACA,UAAA,aAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,SADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;;AAKA,cAAA,MAAA,CAAA,kBAAA;AACA;AACA,WATA;AAUA,SAXA,MAWA;AACA,eAAA,SAAA,CAAA,mBAAA;AACA;AACA;AACA,KAhaA;AAiaA,IAAA,OAjaA,qBAiaA;AACA,UAAA,GAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,GAAA,CAAA,kBAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA,KAtaA;AAuaA,IAAA,QAvaA,sBAuaA;AACA,UAAA,KAAA,YAAA,IAAA,CAAA,EAAA;AACA,YAAA,GAAA,GAAA,KAAA,YAAA,CAAA,KAAA,YAAA,EAAA,IAAA;;AACA,YAAA,GAAA,EAAA;AACA,kBAAA,KAAA,YAAA;AACA;AACA;AACA;AACA,iBAAA,CAAA;AACA,mBAAA,oBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,iBAAA;AACA;;AACA,iBAAA,CAAA;AACA,mBAAA,iBAAA;AACA;AACA;AACA;AACA;AAfA;AAiBA;AACA;AACA,KA9bA;AA+bA,IAAA,oBA/bA,kCA+bA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,gBAAA;;AACA,UAAA,MAAA,GAAA,oBAAA,CAAA,IAAA,EAAA,GAAA,CAAA,CAHA,CAIA;AACA;AACA;;;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CARA,CASA;AACA,KAzcA;AA0cA,IAAA,iBA1cA,+BA0cA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,aAAA;;AACA,UAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,QAAA;AACA;;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KAndA;AAodA,IAAA,iBApdA,+BAodA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,aAAA;;AACA,UAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,QAAA;AACA;;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CARA,CASA;AACA,KA9dA;AA+dA,IAAA,SA/dA,uBA+dA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,UAAA;AACA,MAAA,IAAA,CAAA,MAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KApeA;AAqeA,IAAA,aAreA,2BAqeA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,cAAA;AACA,MAAA,IAAA,CAAA,UAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KA1eA;AA2eA,IAAA,UA3eA,sBA2eA,MA3eA,EA2eA;AACA,WAAA,OAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA;AACA,UAAA,UAAA,EAAA,QADA;AAEA,UAAA,YAAA,EAAA,QAFA;AAGA,UAAA,SAAA,EAAA,QAHA;AAIA,UAAA,SAAA,EAAA,QAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAAA;AAOA;AACA,KAtfA;AAufA;AACA,IAAA,UAxfA,sBAwfA,GAxfA,EAwfA,KAxfA,EAwfA,OAxfA,EAwfA,GAxfA,EAwfA;AACA,WAAA,gBAAA,GAAA,GAAA,CAAA,YAAA;AACA,WAAA,cAAA,GAAA,GAAA,CAAA,UAAA;AACA,WAAA,aAAA,GAAA,GAAA,CAAA,SAAA,IAAA,IAAA,IAAA,GAAA,CAAA,SAAA,KAAA,CAAA,GAAA,IAAA,GAAA,GAAA,CAAA,SAAA;AACA,WAAA,aAAA,GAAA,GAAA,CAAA,SAAA;AACA,WAAA,UAAA,GAAA,GAAA,CAAA,MAAA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,WAAA,YAAA,GAAA,OAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,CAAA,CAAA,KAAA,CAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAIA,KArgBA;AAsgBA;AACA,IAAA,QAvgBA,oBAugBA,IAvgBA,EAugBA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,YAAA;AACA,UAAA,GAAA,GAAA,EAAA;;AACA,UAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAA,KAAA,GAAA,CAAA;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OAHA,MAGA,IAAA,KAAA,GAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,EAAA;AACA;AACA,YAAA,KAAA,IAAA,IAAA,CAAA,QAAA,GAAA,CAAA,IAAA,KAAA,IAAA,IAAA,CAAA,SAAA,GAAA,CAAA,EAAA;AACA,UAAA,KAAA,GAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,KAAA;AACA;;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OARA,MAQA;AACA,QAAA,OAAA,IAAA,CAAA;AACA;;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,MAAA,GAAA,GAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,KAAA,CAAA;;AACA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,GAAA,CAAA,YAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,GAAA,CAAA,UAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,GAAA,CAAA,SAAA,IAAA,IAAA,IAAA,GAAA,CAAA,SAAA,KAAA,CAAA,GAAA,IAAA,GAAA,GAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,UAAA,GAAA,GAAA,CAAA,MAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,GAAA,CAAA,SAAA;AACA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA,KAtiBA;AAuiBA;AACA,IAAA,YAxiBA,wBAwiBA,MAxiBA,EAwiBA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,cAAA,MAAA;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,YAAA;AACA,UAAA,IAAA,GAAA,KAAA,cAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,cAAA;AACA,UAAA,IAAA,GAAA,KAAA,gBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,WAAA;AACA,UAAA,IAAA,GAAA,KAAA,aAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,WAAA;AACA,UAAA,IAAA,GAAA,KAAA,aAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,QAAA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA;AACA;AApBA;;AAsBA,aAAA;AAAA,QAAA,GAAA,EAAA,GAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA;AACA,KAlkBA;AAmkBA,IAAA,IAnkBA,kBAmkBA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,YAAA;;AACA,UAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAA,KAAA,GAAA,CAAA;AACA,QAAA,OAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OARA,MAQA;AACA,QAAA,IAAA,CAAA,QAAA;AACA,QAAA,IAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA,KAplBA;AAqlBA,IAAA,QArlBA,oBAqlBA,KArlBA,EAqlBA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,EAAA;;AACA,UAAA,KAAA,CAAA,MAAA,GAAA,GAAA,EAAA;AACA,eAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,GAAA,IAAA,KAAA;AACA;;AACA,aAAA,KAAA;AACA,KA3lBA;AA4lBA,IAAA,mBA5lBA,iCA4lBA,CAEA;AA9lBA,GA3GA;AA2sBA,EAAA,OA3sBA,qBA2sBA;AACA,SAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,SAAA,SAAA,CAAA,OAAA,GAAA,KAAA,SAAA,CAAA,UAAA;AACA,QAAA,IAAA,GAAA,IAAA;AACA,IAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,MAAA,eAAA,CAAA;AAAA,QAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA;AAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,IAAA,CAAA,WAAA;AACA,OAHA;AAIA,KATA;AAUA;AAztBA,CAAA", "sourcesContent": ["<style lang=\"less\">\r\n    .mytable .ivu-table-cell{\r\n        padding-left: 5px;\r\n        padding-right: 5px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: normal;\r\n        word-break: break-all;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .accountEs .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .accountEs .header-bar-show {\r\n        max-height: 300px;\r\n        padding-top: 14px;\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .accountEs .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n\r\n\r\n    .mytable .myspan{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block\r\n    }\r\n    .mytable .errorStle{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block;\r\n        color:red;\r\n    }\r\n    //  @on-change='accountnoChange'\r\n</style>\r\n<template>\r\n    <div>\r\n        <div class=\"accountEs\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.accountno\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{ item.name }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"关键字:\" prop=\"oilUseBody\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"accountObj.oilUseBody\" placeholder=\"用能主体关键字模糊查询\" :style=\"formItemWidth\" />\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.company\" @on-change=\"selectChange(accountObj.company)\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"accountObj.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly :style=\"formItemWidth\"/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <div  align=\"right\">\r\n                        <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n                        <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n                    </div>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <div>\r\n                <Row>\r\n                    <Col span=\"12\">\r\n                        <Page size=\"small\" :total=\"pageTotal\" :current=\"pageNum\" :page-size=\"pageSize\" show-elevator show-sizer show-total\r\n                              placement=\"top\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n                    </Col>\r\n                    <Col span=\"12\">\r\n                        <div align=\"right\" class=\"account\">\r\n                            <Button type=\"success\" @click=\"addNewOilAccount\">新增</Button>\r\n                            <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n                            <Button type=\"error\" @click=\"remove\">删除</Button>\r\n                            <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n                                <Button type='info' style=\"margin-left: 5px\">加入归集单\r\n                                    <Icon type='ios-arrow-down'></Icon>\r\n                                </Button>\r\n                                <DropdownMenu slot='list'>\r\n                                    <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n                                    <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n                                </DropdownMenu>\r\n                            </Dropdown>\r\n                            <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n                        </div>\r\n                    </Col>\r\n                </Row>\r\n            </div>\r\n            <Table ref=\"accountEsTable\"\r\n                   border\r\n                   :columns=\"tbAccount.tailColumn\"\r\n                   :data=\"tbAccount.data\"\r\n                   :key=\"tbAccount.dispKey\"\r\n                   class=\"mytable\">\r\n                <!--用油主体-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"oilUseBody\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=100 v-model=\"editOilUseBody\" :ref=\"'oilUseBody'+index+1\" type=\"text\" @on-blur=\"setOilUseBody\"\r\n                               v-if=\"editIndex === index && columnsIndex === 1\"/>\r\n                        <Tooltip placement=\"right\" max-width=\"200\" v-else>\r\n                            <span :class=\"myStyle[index].oilUseBody\" style=\"width: 60px\" @click=\"selectCall(row,index,1,'oilUseBody')\">\r\n                                {{ ellipsis(row.oilUseBody) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.oilUseBody }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--费用发生日-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"feeStartDate\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'feeStartDate'+index+2\" type=\"text\" v-model=\"editFeeStartDate\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 2\" />\r\n                    <span :class=\"myStyle[index].feeStartDate\" @click=\"selectCall(row,index,2,'feeStartDate')\" v-else>{{ row.feeStartDate }}</span>\r\n                </template>\r\n                <!--油量-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"oilAmount\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'oilAmount'+index+3\" type=\"text\" v-model=\"editOilAmount\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 3\"/>\r\n                    <span :class=\"myStyle[index].oilAmount\" @click=\"selectCall(row,index,3,'oilAmount')\" v-else>{{ row.oilAmount }}</span>\r\n                </template>\r\n                <!--金额-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"paidMoney\" v-if=\"row.total == null\">\r\n                    <Input :ref=\"'paidMoney'+index+4\" type=\"text\" v-model=\"editPaidMoney\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 4\"/>\r\n                    <span :class=\"myStyle[index].paidMoney\" @click=\"selectCall(row,index,4,'paidMoney')\" v-else>{{ row.paidMoney }}</span>\r\n                </template>\r\n                <!--备注-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=500 v-model=\"editremark\" :ref=\"'remark'+index+5\" type=\"text\" @on-blur=\"setremark\"\r\n                               v-if=\"editIndex === index && columnsIndex === 5\"/>\r\n                        <Tooltip placement=\"left\" max-width=\"600\" v-else>\r\n                            <span :class=\"myStyle[index].remark\" style=\"width: 60px\" @click=\"selectCall(row,index,5,'remark')\">\r\n                                {{ ellipsis(row.remark) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                    <div v-else>\r\n                        <Tooltip placement=\"bottom\" max-width=\"200\">\r\n                            <span>{{ ellipsis(row.remark)}}</span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n            </Table>\r\n            <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n        </div>\r\n        <div>\r\n            <add-bill-per ref=\"addBillPer\"\r\n                          v-on:refreshList=\"refresh\"></add-bill-per >\r\n            <completed-pre-modal ref=\"completedPre\" v-on:refreshList=\"refresh\"></completed-pre-modal>\r\n            <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n            <upload-file-modal ref=\"uploadFileModal\" v-on:onchange=\"change\"></upload-file-modal>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    import UploadFileModal from \"@/view/account/uploadFileModal\";\r\n    import {\r\n        removeOilAccount,\r\n        selectOilIds,\r\n        saveOilAccount\r\n    } from '@/api/coalHeatOilAccount';\r\n    import checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\n    import checkResult from \"@/view/account/check/checkResult\";\r\n    import alarmCheck from \"@/view/account/check/alarmCheck\";\r\n    import {getDates,testNumber,} from '@/view/account/powerAccountHelper';\r\n    import axios from '@/libs/api.request';\r\n    import SelectAmmeter from \"./selectAmmeter\";\r\n    import {_verify_FeeStartDate} from '@/view/account/PowerAccountEs';\r\n    import AddBillPer from \"./addPreOilBillPreModal\";\r\n    import {reJoinBillpre} from '@/api/accountBillPer';\r\n    import {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\n    import CompletedPreModal from \"./completedPreModal\";\r\n    import indexData from '@/config/index'\r\n    import CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import {getUserdata,getUserByUserRole,getCountrysdata,getCountryByUserId} from '@/api/basedata/ammeter.js'\r\n    let dates=getDates();\r\n    export default {\r\n        name: 'addPreOilAccount',\r\n        components: {UploadFileModal, alarmCheck, checkResult, checkResultAndResponse,CompletedPreModal, SelectAmmeter,AddBillPer,CountryModal},\r\n        data() {\r\n            let photo = (h, {row, index}) => {\r\n                let that = this\r\n                let str = ''\r\n                if (row.projectname != '小计' && row.projectname != '合计') {\r\n                    str = '上传附件'\r\n                }\r\n                return h(\"div\", [h(\"u\", {\r\n                    on: {\r\n                        click() {\r\n                            //打开弹出框\r\n                            if (row.projectname != '小计' && row.projectname != '合计') {\r\n                                that.uploadFile(row)\r\n                            }\r\n                        }\r\n                    }\r\n                }, str)]);\r\n            };\r\n            return {\r\n                submit:[],\r\n                submit2:[],\r\n                showCheckModel:false,\r\n                showJhModel:false,\r\n                showAlarmModel:false,\r\n                formItemWidth: widthstyle,\r\n                version:'',\r\n                dateList:dates,\r\n                filterColl: true,//搜索面板展开\r\n                editIndex: -1,//当前编辑行\r\n                columnsIndex:-1,//当前编辑列\r\n                myStyle:[],//样式\r\n                editOilUseBody:'',\r\n                editFeeStartDate:'',\r\n                editOilAmount:'',\r\n                editPaidMoney:'',\r\n                spinShow:false,//遮罩\r\n                editremark:'',\r\n                companies:[],\r\n                departments:[],\r\n                isAdmin:false,\r\n                company:null,//用户默认公司\r\n                country:null,//用户默认所属部门\r\n                countryName:null,//用户默认所属部门\r\n                accountObj:{\r\n                    accountno:dates[1].code,//期号,默认当前月\r\n                    company:\"\",//分公司\r\n                    country:\"\",//所属部门\r\n                    oilUseBody:null,//用能主体\r\n                    oilAccountType: 2,\r\n                    countryName: \"\",\r\n\r\n                },\r\n                tbAccount: {\r\n                    dispKey: 0,\r\n                    loading: false,\r\n                    columns: [],\r\n                    tailColumn: [\r\n                        {type: 'selection', width: 60, align: 'center',},\r\n                        {\r\n                            title: \"期号\",\r\n                            key: \"accountNo\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {\r\n                            title: \"用油主体\",\r\n                            slot: \"oilUseBody\",\r\n                            align: \"center\",\r\n                            width: 200,\r\n                        },\r\n                        {\r\n                            title: \"费用发生日\",\r\n                            slot: \"feeStartDate\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {\r\n                            title: \"油量(L)\",\r\n                            slot: \"oilAmount\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {\r\n                            title: \"单价(元/L)\",\r\n                            key: \"unitPrice\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {\r\n                            title: \"金额(元)\",\r\n                            slot: \"paidMoney\",\r\n                            align: \"center\",\r\n                            width: 120,\r\n                        },\r\n                        {title: \"附件\", align: \"center\", render: photo, width: 130},\r\n                        {title: \"备注\", slot: \"remark\",align: \"center\", width: 290},\r\n                    ],\r\n                    data: []\r\n                },\r\n                pageTotal: 0,\r\n                pageNum: 1,\r\n                pageSize: 10,//当前页\r\n            }\r\n        },\r\n        methods: {\r\n            uploadFile(row) {\r\n                console.log(row, \"row\");\r\n                // let id;\r\n                // if(!row.id2) {\r\n                //     editAmmeter('', 0).then(res => {\r\n                //         debugger\r\n                //         console.log(res, \"res\");\r\n                //         row.id2 = res.data.id;\r\n\r\n                //         this.id2 = res.data.id\r\n                //         // debugger\r\n                //         // this.fileParam.busiId = ;\r\n                //         this.$refs.uploadFileModal.choose(row.id2 + '');\r\n                //     })\r\n                // }else {\r\n\r\n                if(row.id) {\r\n                    this.$refs.uploadFileModal.choose(row.id + '');\r\n                }else {\r\n                    this.errorTips(\"请先保存后再上传文件！\");\r\n                }\r\n                // }\r\n                // console.log(row, \"row\");\r\n            },\r\n            selectChange(){\r\n                let that = this;\r\n                if (that.accountObj.company != undefined) {\r\n                    if(that.accountObj.company == \"-1\"){\r\n                        that.accountObj.country = -1;\r\n                        that.accountObj.countryName = null;\r\n                    }else{\r\n                        getCountryByUserId(that.accountObj.company).then(res => {\r\n                            if(res.data.departments.length != 0){\r\n                                that.accountObj.country = res.data.departments[0].id;\r\n                                that.accountObj.countryName = res.data.departments[0].name;\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            },\r\n            //选择所属部门开始\r\n            chooseResponseCenter() {\r\n                if(this.accountObj.company == null || this.accountObj.company == \"-1\" ){\r\n                    this.$Message.info(\"请先选择分公司\");return;\r\n                }\r\n                this.$refs.countryModal.choose(this.accountObj.company);//所属部门\r\n            },\r\n            getDataFromModal(data) {\r\n                this.accountObj.country = data.id;\r\n                this.accountObj.countryName = data.name;\r\n                //选择所属部门结束\r\n            },\r\n            getUserData(){\r\n                let that = this;\r\n                getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                    if(res.data.companies.length != 0){\r\n                        let companies = res.data.companies;\r\n                        if(res.data.companies[0].id == \"**********\"){\r\n                            companies = that.companies;\r\n                        }\r\n                        that.company = companies[0].id;\r\n                        that.accountObj.company = companies[0].id;\r\n                    }\r\n                    if(res.data.departments.length != 0){\r\n                        let departments = res.data.departments;\r\n                        if(res.data.companies[0].id == \"**********\" && that.departments.length != 0){\r\n                            departments = that.departments\r\n                        }\r\n                        that.country = departments[0].id;\r\n                        that.countryName = departments[0].name;\r\n                        that.accountObj.country = Number(departments[0].id);\r\n                        that.accountObj.countryName = departments[0].name;\r\n                    }\r\n\r\n                    that.pageNum = 1\r\n                    that.getAccountMessages();\r\n                });\r\n            },\r\n            searchList(){\r\n                if(this.accountObj.countryName == \"\"){\r\n                    this.accountObj.country = \"-1\";\r\n                }\r\n                this.pageNum = 1;\r\n                this.getAccountMessages()\r\n            },\r\n            accountnoChange(){\r\n                this.searchList()\r\n            },\r\n            //点击保存\r\n            preserve() {\r\n                let dataL = this.$refs.accountEsTable.getSelection();\r\n                let b = false;\r\n                let array = [];\r\n                for (let i = 0; i < dataL.length; i ++) {\r\n                    b = true;\r\n                    array.push(dataL[i])\r\n                }\r\n                if(b){\r\n                    this.submitData(array);\r\n                }else {\r\n                    this.errorTips('没有可保存数据')\r\n                }\r\n            },\r\n            submitChange(indexList){\r\n                let data=[];\r\n                this.submit2.map((item,index)=>{\r\n                    indexList.map((item2)=>{\r\n                        if(index==item2){\r\n                            data.push(item)\r\n                        }\r\n                    })\r\n                });\r\n                this.submit=data\r\n            },\r\n\r\n            //提交数据\r\n            submitData(data){\r\n                let a = [];\r\n                let that=this;\r\n                if(data != null && data.length > 0){\r\n                    let number = 0;\r\n                    let submitData = [];\r\n                    let str = '';\r\n                    let accountno = this.accountObj.accountno;\r\n                    data.forEach(function (item) {\r\n                        // 校验数据\r\n                        if(item.id == null){\r\n                            item.accountno = accountno\r\n                        }\r\n                        a.push(item.id);\r\n                        item.oilAccountType = 2;\r\n                        submitData.push(item);\r\n                        number ++;\r\n                    });\r\n                    that.ids=a;\r\n                    if(str.length > 0){\r\n                        this.errorTips(str)\r\n                    }\r\n                    if(submitData.length > 0){\r\n                        saveOilAccount(submitData).then((res) => {\r\n                            if (res.data.code == 0) {\r\n                                this.$Message.info({\r\n                                    content: '提示：成功保存 ' + res.data.num + ' 条数据',\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }\r\n                }\r\n            },\r\n            addNewOilAccount() {\r\n                const currentDate = new Date();\r\n                const currentYear = currentDate.getFullYear();\r\n                const currentMonth = currentDate.getMonth() + 1;\r\n                if (null == this.tbAccount.data) {\r\n                    this.tbAccount.data = [];\r\n                }\r\n                this.tbAccount.dispKey++;\r\n                this.tbAccount.data.unshift({\r\n                    // accountNo:dates[1].code,\r\n                    // accountNo: this.accountObj.accountno,\r\n                    accountNo: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined) ? currentYear+\"\"+currentMonth: this.accountObj.accountno,\r\n                    oilUseBody: \"\",\r\n                    feeStartDate:\"\",\r\n                    oilAmount: \"0\",\r\n                    unitPrice: \"0\",\r\n                    paidMoney:\"0\",\r\n                    remark:\"\",\r\n                });\r\n                this.tbAccount.loading = false;\r\n                this.myStyle.push({\r\n                    oilUseBody: 'myspan',\r\n                    feeStartDate: 'myspan',\r\n                    oilAmount: 'myspan',\r\n                    paidMoney: 'myspan',\r\n                    remark: 'myspan',\r\n\r\n                    });\r\n            },\r\n            //验证错误弹出提示框\r\n            errorTips(str){\r\n                this.$Notice.error({\r\n                    title: '提示',\r\n                    desc: str,\r\n                    duration: 10\r\n                });\r\n            },\r\n            handlePage(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageNum = value;\r\n                this.getAccountMessages();\r\n            },\r\n            handlePageSize(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n                this.pageSize = value;\r\n                this.getAccountMessages();\r\n            },\r\n            //向后台请求数据\r\n            getAccountMessages() {\r\n                let postData = this.accountObj;\r\n                postData.pageNum = this.pageNum;\r\n                postData.pageSize = this.pageSize;\r\n                let req = {\r\n                    url : \"/business/oil/account/list\",\r\n                    method : \"get\",\r\n                    params : postData\r\n                };\r\n                this.tbAccount.loading = true;\r\n                axios.request(req).then(res => {\r\n                    this.tbAccount.loading = false;\r\n                    if (res.data) {\r\n                        let data = res.data.rows;\r\n                        data.forEach(function (item) {\r\n                            item.editType = 0;\r\n                        });\r\n                        if (null == data) {\r\n                            this.tbAccount.data = {};\r\n                        } else {\r\n                            this.tbAccount.data = data;\r\n                        }\r\n                        this.pageTotal = res.data.total || 0;\r\n                        this.setMyStyle(this.tbAccount.data.length);\r\n                        this.editIndex = -1;\r\n                        this.columnsIndex = -1;\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            },\r\n            //重置\r\n            onResetHandle(){\r\n                this.accountObj = {\r\n                    accountno:null,\r\n                    company:this.company,\r\n                    oilUseBody:null,\r\n                    country:Number(this.country),\r\n                    oilAccountType:2,\r\n                }\r\n                this.getAccountMessages()\r\n            },\r\n            //计算单价\r\n            unitPrice(row){\r\n                let paidMoney = row.paidMoney;\r\n                let oilAmount = row.oilAmount;\r\n                if(paidMoney != null && oilAmount != null){\r\n                    row.unitpirce = paidMoney/oilAmount.toFixed(2);\r\n                }\r\n            },\r\n            remove(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                if(data == null || data.length === 0){\r\n                    this.errorTips(\"请选择要删除的数据\")\r\n                    return;\r\n                }\r\n                this.$Modal.confirm({\r\n                    title: '提示',\r\n                    content: '<p>是否确认删除选中信息？</p>',\r\n                    onOk: () => {\r\n                        let b = true;\r\n                        let ids = '';\r\n                        let total = this.pageTotal\r\n                        for(let i=0;i<data.length;i++){\r\n                            let item = data[i];\r\n                            if(item.id != null && item.id.length > 0){\r\n                                if(item.pabriid){\r\n                                    b = false;\r\n                                }\r\n                                ids += item.id + ',';\r\n                            }\r\n                        }\r\n                        this.pageTotal = total\r\n                        if(b){\r\n                            if(ids.length > 0){\r\n                                removeOilAccount(ids).then((res) => {\r\n                                    if (res.data.code == 0) {\r\n                                        this.$Message.success('删除成功');\r\n                                        this.getAccountMessages();\r\n                                    }\r\n                                });\r\n                            }\r\n                        }else {\r\n                            this.errorTips('选中信息中有信息还没有跟归集单解除关联，请先解除关联')\r\n                        }\r\n                    },\r\n                    onCancel: () => {\r\n                    }\r\n                });\r\n            },\r\n            openAddBillPerModal(name) {\r\n                if (name === 'current') {\r\n                    this.selectedAccount()\r\n                } else if (name === 'all') {\r\n                    this.selectedAllAccount()\r\n                }\r\n            },\r\n            //加入归集单，全部有效台账\r\n            selectedAllAccount(){\r\n                let that = this\r\n                that.spinShow = true;\r\n                selectOilIds(this.accountObj).then(res => {\r\n                    that.spinShow = false;\r\n                    if(res.data.length == 0){\r\n                        that.errorTips('无有效数据可加入归集单')\r\n                    }else {\r\n                        let ids = [];\r\n                        for(let i=0;i<res.data.rows.length;i++){\r\n                            let item = res.data.rows[i];\r\n                            ids.push(item.id)\r\n                        }\r\n                        that.$refs.addBillPer.initAmmeter(ids, 21,this.accountObj.country);\r\n                    }\r\n                });\r\n            },\r\n            selectedAccount(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = 1;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要加入归集单的台账')\r\n                } else {\r\n                    let ids = [];\r\n                    data.forEach(function (item) {\r\n                        if(item.status === 5){\r\n                            b = 3\r\n                        }\r\n                        if(item.status === 4){\r\n                            b=4;\r\n                        }\r\n                        ids.push(item.id)\r\n                    });\r\n                    if(b === 1){\r\n                        this.$refs.addBillPer.initAmmeter(ids,21,this.accountObj.country);\r\n                    }else if(b === 2) {\r\n                        this.errorTips('选中的台账中存在临时数据，请先保存再加入归集单！')\r\n                    }else if(b===3){\r\n                        this.errorTips('退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮')\r\n                    }else if(b===4){\r\n                        this.errorTips('选择的台账有已加入归集单的台账，不能加入其他归集单')\r\n                    }\r\n                }\r\n            },\r\n            openCompletedPreModal(){\r\n                this.$refs.completedPre.initAmmeter(this.accountObj.country,2);\r\n            },\r\n            againJoin(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = true;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要重新加入归集单的台账')\r\n                } else {\r\n                    let ids = '';\r\n                    data.forEach(function (item) {\r\n                        let status = item.status;\r\n                        if(status != 5){\r\n                            b = false;\r\n                        }\r\n                        ids+= item.id +','\r\n                    });\r\n                    if(b){\r\n                        reJoinBillpre(ids).then((res) =>{\r\n                            if(res.data.code==0){\r\n                                this.$Message.info({\r\n                                    content:'提示：操作成功' ,\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }else {\r\n                        this.errorTips('只有已退回的台账才能重新加入归集单')\r\n                    }\r\n                }\r\n            },\r\n            refresh(){\r\n                let obj = this\r\n                setTimeout(function () {\r\n                    obj.getAccountMessages()\r\n                },200);\r\n            },\r\n            validate(){\r\n                if(this.columnsIndex != 5){\r\n                    let val = this.enterOperate(this.columnsIndex).data;\r\n                    if(val) {\r\n                        switch (this.columnsIndex) {\r\n                            // case 1:\r\n                            //     this.validateFeeStartDate();\r\n                            //     break;\r\n                            case 2:\r\n                                this.validateFeeStartDate();\r\n                                break;\r\n                            case 3:\r\n                                this.validateOilAmount();\r\n                                break;\r\n                            case 4:\r\n                                this.validatePaidMoney();\r\n                                break;\r\n                            // case 5:\r\n                            //     this.validateFeeStartDate();\r\n                            //     break;\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            validateFeeStartDate(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editFeeStartDate;\r\n                let result = _verify_FeeStartDate(data,val);\r\n                // if(result){//失败就弹出提示内容，并将数据恢复初始化\r\n                //     this.errorTips(result)\r\n                // }else{\r\n                    data.feeStartDate = val;\r\n                    data.editType = 1;\r\n                // }\r\n            },\r\n            validateOilAmount(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOilAmount;\r\n                if (!testNumber(val)) {\r\n                    this.errorTips('请输入数字！');\r\n                }\r\n                data.oilAmount = val;\r\n                data.unitPrice = data.paidMoney/data.oilAmount;\r\n                data.editType = 1;\r\n            },\r\n            validatePaidMoney(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editPaidMoney;\r\n                if (!testNumber(val)) {\r\n                    this.errorTips('请输入数字！');\r\n                }\r\n                data.paidMoney = val;\r\n                data.unitPrice = data.paidMoney/data.oilAmount;\r\n                data.editType = 1;\r\n                // this.unitPrice(data)\r\n            },\r\n            setremark(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editremark;\r\n                data.remark = val;\r\n                data.editType = 1;\r\n            },\r\n            setOilUseBody(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOilUseBody;\r\n                data.oilUseBody = val;\r\n                data.editType = 1;\r\n            },\r\n            setMyStyle(length){\r\n                this.myStyle=[];\r\n                for(var i=0;i<length;i++){\r\n                    this.myStyle.push({\r\n                        oilUseBody: 'myspan',\r\n                        feeStartDate: 'myspan',\r\n                        oilAmount: 'myspan',\r\n                        paidMoney: 'myspan',\r\n                        remark: 'myspan',\r\n                    });\r\n                }\r\n            },\r\n            //span点击事件将span换成输入框并且获取焦点\r\n            selectCall(row,index,columns,str){\r\n                this.editFeeStartDate = row.feeStartDate;\r\n                this.editOilUseBody = row.oilUseBody;\r\n                this.editOilAmount = row.oilAmount == null || row.oilAmount===0?null:row.oilAmount;\r\n                this.editPaidMoney = row.paidMoney;\r\n                this.editremark = row.remark;\r\n                this.editIndex = index;\r\n                this.columnsIndex = columns;\r\n                let a=this;\r\n                setTimeout(function () {\r\n                    a.$refs[str+index+columns].focus();\r\n                },200);\r\n\r\n            },\r\n            //跳转到下一格\r\n            nextCell(data){\r\n                let index = data.editIndex;\r\n                let columns = data.columnsIndex;\r\n                let row = '';\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                }else if(index > -1 && columns === 5){\r\n                    //当跳转的最后一行最后一格的时候\r\n                    if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n                        index = 0;\r\n                    }else{\r\n                        index ++;\r\n                    }\r\n                    columns = 1;\r\n                }else{\r\n                    columns += 1;\r\n                }\r\n                data.editIndex = index;\r\n                data.columnsIndex = columns;\r\n                row = data.tbAccount.data[index];\r\n                if(row){\r\n                    data.editFeeStartDate = row.feeStartDate;\r\n                    data.editOilUseBody = row.oilUseBody;\r\n                    data.editOilAmount = row.oilAmount == null || row.oilAmount===0?null:row.oilAmount;\r\n                    data.editremark = row.remark;\r\n                    data.editPaidMoney = row.paidMoney;\r\n                }\r\n                setTimeout(function () {\r\n                    data.$refs[data.enterOperate(columns).str+index+columns].focus();\r\n                },200);\r\n            },\r\n            //根据列号返回对应的列名\r\n            enterOperate(number){\r\n                let str = '';\r\n                let data = null;\r\n                switch (number) {\r\n                    case 1:\r\n                        str = 'oilUseBody';\r\n                        data = this.editOilUseBody;\r\n                        break;\r\n                    case 2:\r\n                        str = 'feeStartDate';\r\n                        data = this.editFeeStartDate;\r\n                        break;\r\n                    case 3:\r\n                        str = 'oilAmount';\r\n                        data = this.editOilAmount;\r\n                        break;\r\n                    case 4:\r\n                        str = 'paidMoney';\r\n                        data = this.editPaidMoney;\r\n                        break;\r\n                    case 5:\r\n                        str = 'remark';\r\n                        data = this.editremark;\r\n                        break;\r\n                }\r\n                return {str:str,data:data};\r\n            },\r\n            pred(){\r\n                var lett = this;\r\n                let index = lett.editIndex;\r\n                let columns = lett.columnsIndex;\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                    lett.editIndex = index;\r\n                    lett.columnsIndex = columns;\r\n                    setTimeout(function () {\r\n                        lett.$refs[lett.enterOperate(columns).str+index+columns].focus();\r\n                    },200);\r\n                }else{\r\n                    lett.validate()\r\n                    lett.setremark()\r\n                    lett.nextCell(lett)\r\n                }\r\n            },\r\n            ellipsis (value) {\r\n                if (!value) return ''\r\n                if (value.length > 100) {\r\n                    return value.slice(0,100) + '...'\r\n                }\r\n                return value\r\n            },\r\n            handleUploadSuccess() {\r\n\r\n            },\r\n        },\r\n        mounted() {\r\n            this.version = indexData.version;\r\n            this.tbAccount.columns = this.tbAccount.tailColumn;\r\n            let that = this\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                    that.getUserData();\r\n                });\r\n            });\r\n        }\r\n    }\r\n</script>\r\n"], "sourceRoot": "src/view/account"}]}