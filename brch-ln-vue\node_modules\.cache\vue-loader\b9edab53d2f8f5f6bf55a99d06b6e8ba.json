{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\energyview\\original.vue?vue&type=style&index=0&id=534b1db6&lang=less&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\energyview\\original.vue", "mtime": 1754285403043}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749178874080}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KL2RlZXAvLmVsLXRleHRhcmVhIHsNCiAgd2lkdGg6IDM5cmVtICFpbXBvcnRhbnQ7DQogIGhlaWdodDogODBweCAhaW1wb3J0YW50Ow0KICAmID4gLmVsLXRleHRhcmVhX19pbm5lciB7DQogICAgaGVpZ2h0OiAxMjBweCAhaW1wb3J0YW50Ow0KICB9DQp9DQpwIHsNCiAgbWFyZ2luOiAwOw0KfQ0KDQovZGVlcC8gLmF2dWUtY3J1ZF9fbWVudSB7DQogIG1hcmdpbi1ib3R0b206IDAgIWltcG9ydGFudDsNCn0NCg0KdWwuc2NvcmVfYm94IHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogNi41dmg7DQogIGRpc3BsYXk6IGZsZXg7DQoNCiAgbGkgew0KICAgIHdpZHRoOiAxMy41cmVtOw0KICAgIGhlaWdodDogNi41dmg7DQogICAgcGFkZGluZzogMC40dmggMS41cmVtIDA7DQoNCiAgICAmID4gcDpudGgtb2YtdHlwZSgxKSB7DQogICAgICBmb250LXNpemU6IDJyZW07DQogICAgICBmb250LWZhbWlseTogSGFybW9ueU9TLVNhbnMtQmxhY2s7DQogICAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICB9DQoNCiAgICAmID4gcDpudGgtb2YtdHlwZSgyKSB7DQogICAgICBmb250LXNpemU6IDEuM3JlbTsNCiAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLU1lZGl1bSwgUGluZ0ZhbmcgU0M7DQogICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgY29sb3I6ICNmZmZmZmY7DQogICAgICBtYXJnaW4tdG9wOiAwLjV2aDsNCiAgICB9DQogIH0NCg0KICAmID4gbGk6bnRoLW9mLXR5cGUoMSkgew0KICAgIGJhY2tncm91bmQ6IHVybCgiLi4vLi4vLi4vLi4vYXNzZXRzL2NhcmJvbi90b3RhbFRhc2tTY29yZS5wbmciKTsNCiAgICBiYWNrZ3JvdW5kLXNpemU6IDEwMCUgMTAwJTsNCiAgfQ0KDQogICYgPiBsaTpudGgtb2YtdHlwZSgyKSB7DQogICAgbWFyZ2luLWxlZnQ6IDJyZW07DQogICAgYmFja2dyb3VuZDogdXJsKCIuLi8uLi8uLi8uLi9hc3NldHMvY2FyYm9uL2Fzc2Vzc21lbnNjb3JlLnBuZyIpOw0KICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlOw0KICB9DQoNCiAgJiA+IGxpOm50aC1vZi10eXBlKDMpIHsNCiAgICBtYXJnaW4tbGVmdDogMnJlbTsNCiAgICBiYWNrZ3JvdW5kOiB1cmwoIi4uLy4uLy4uLy4uL2Fzc2V0cy9jYXJib24vb3JhbmdlLnBuZyIpOw0KICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlOw0KICB9DQp9DQovZGVlcC8uZWwtZGlhbG9nX19mb290ZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBwYWRkaW5nLWJvdHRvbTogNHZoOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCn0NCg0KdWwuYXBwcmFpc2FsVGVtcGxhdGUgew0KICB3aWR0aDogOTklOw0KICBoZWlnaHQ6IDY4dmg7DQogIG1hcmdpbi10b3A6IDJ2aDsNCiAgbWFyZ2luLWxlZnQ6IDJyZW07DQoNCiAgJiA+IGxpIHsNCiAgICB3aWR0aDogMzkuNHJlbTsNCiAgICBoZWlnaHQ6IDE1dmg7DQogICAgZmxvYXQ6IGxlZnQ7DQogICAgcGFkZGluZzogMS41dmggMDsNCiAgICBtYXJnaW4tYm90dG9tOiAydmg7DQogICAgbWFyZ2luLXJpZ2h0OiAycmVtOw0KICAgIGJhY2tncm91bmQ6ICMxNDI4M2Y7DQogICAgcG9zaXRpb246IHJlbGF0aXZlOw0KDQogICAgaGVhZGVyIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgICAmID4gcDpudGgtb2YtdHlwZSgxKSB7DQogICAgICAgIHdpZHRoOiA2cHg7DQogICAgICAgIGhlaWdodDogMThweDsNCiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDM2MGRlZywgIzNjZmFlYSAwJSwgIzBlZGFhNCAxMDAlKTsNCiAgICAgICAgYm94LXNoYWRvdzogMXB4IDBweCA0cHggMHB4IHJnYmEoNTIsIDI1NSwgMTg2LCAwLjQ1KTsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogMHB4IDNweCAzcHggMHB4Ow0KICAgICAgfQ0KDQogICAgICAmID4gcDpudGgtb2YtdHlwZSgyKSB7DQogICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgbWFyZ2luLWxlZnQ6IDEuNHJlbTsNCiAgICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtTWVkaXVtLCBQaW5nRmFuZyBTQzsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgY29sb3I6ICNmZmZmZmY7DQogICAgICB9DQogICAgfQ0KDQogICAgJiA+IHA6bnRoLW9mLXR5cGUoMSkgew0KICAgICAgZm9udC1zaXplOiAxLjNyZW07DQogICAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1SZWd1bGFyLCBQaW5nRmFuZyBTQzsNCiAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICAgIG1hcmdpbi10b3A6IDIuM3ZoOw0KICAgICAgbWFyZ2luLWxlZnQ6IDJyZW07DQogICAgfQ0KDQogICAgJiA+IHA6bnRoLW9mLXR5cGUoMikgew0KICAgICAgZm9udC1zaXplOiAxLjNyZW07DQogICAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1SZWd1bGFyLCBQaW5nRmFuZyBTQzsNCiAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICAgIG1hcmdpbi10b3A6IDJ2aDsNCiAgICAgIG1hcmdpbi1sZWZ0OiAycmVtOw0KICAgIH0NCg0KICAgICYgPiBkaXYgew0KICAgICAgd2lkdGg6IDExcmVtOw0KICAgICAgaGVpZ2h0OiA5dmg7DQogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICByaWdodDogMC41cmVtOw0KICAgICAgYm90dG9tOiAyLjN2aDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCg0KICAgICAgJiA+IGltZyB7DQogICAgICAgIHdpZHRoOiA1LjFyZW07DQogICAgICAgIGhlaWdodDogNS40cmVtOw0KICAgICAgfQ0KDQogICAgICAmID4gcCB7DQogICAgICAgIGZvbnQtc2l6ZTogMTNweDsNCiAgICAgICAgbGV0dGVyLXNwYWNpbmc6IDAuMDVyZW07DQogICAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLVJlZ3VsYXIsIFBpbmdGYW5nIFNDOw0KICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICBjb2xvcjogIzAwZWRjMDsNCiAgICAgICAgbWFyZ2luLXRvcDogMC43dmg7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgJiA+IGxpOmhvdmVyIHsNCiAgICBiYWNrZ3JvdW5kOiAjMmQ0ODZhOw0KICB9DQp9DQoNCi5jdXJkLWhlYWRlciB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDUwcHg7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIG1hcmdpbi10b3A6IDF2aDsNCiAgYmFja2dyb3VuZDogI2Y2ZjhmYTsNCiAgY29sb3I6ICMzMDNiNTA7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCg0KICAuY29tbW9uZm9udCB7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIGxpbmUtaGVpZ2h0OiA1MHB4Ow0KICAgIGxldHRlci1zcGFjaW5nOiAxcHg7DQogICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtUmVndWxhciwgUGluZ0ZhbmcgU0M7DQogICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICBjb2xvcjogIzAwZWNjMDsNCiAgfQ0KDQogICYgPiAucmVwb3J0aW5nIHsNCiAgICB3aWR0aDogNjkwcHg7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBtYXJnaW4tbGVmdDogMTBweDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgcCB7DQogICAgICBAZXh0ZW5kIC5jb21tb25mb250Ow0KICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KDQogICAgICAmID4gc3BhbiB7DQogICAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICAgICAgfQ0KICAgIH0NCg0KICAgIC9kZWVwLyAuZWwtZGF0ZS1lZGl0b3IuZWwtaW5wdXQsDQogICAgLmVsLWRhdGUtZWRpdG9yLmVsLWlucHV0X19pbm5lciB7DQogICAgICB3aWR0aDogMjAwcHggIWltcG9ydGFudDsNCiAgICB9DQogIH0NCg0KICAmID4gLmNvbXBhbnlOYW1lIHsNCiAgICBAZXh0ZW5kIC5jb21tb25mb250Ow0KICAgIGxpbmUtaGVpZ2h0OiA1MHB4Ow0KICB9DQp9DQoNCi5mb290X2J0biB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQoudG90YWwtY2FyZC1iZy1ncmVlbiB7DQogIGJhY2tncm91bmQ6IHVybCgiLi4vLi4vLi4vLi4vYXNzZXRzL2NhcmJvbi93aWxkX2dyZWVuX3JlY3RhbmdsZS5wbmciKTsNCn0NCg0KLnRvdGFsLWNhcmQtYmcteWVsbG93IHsNCiAgYmFja2dyb3VuZDogdXJsKCIuLi8uLi8uLi8uLi9hc3NldHMvY2FyYm9uL3N1bW1lcl95ZWxsb3dfcmVjdGFuZ2xlLnBuZyIpOw0KfQ0KDQoudG90YWwtY2FyZC1iZy1yZWQgew0KICBiYWNrZ3JvdW5kOiB1cmwoIi4uLy4uLy4uLy4uL2Fzc2V0cy9jYXJib24vbWFpZGVuX3JlZF9yZWN0YW5nbGUucG5nIik7DQp9DQoNCi50b3RhbC1jYXJkLWJnLXZpb2xldCB7DQogIGJhY2tncm91bmQ6IHVybCgiLi4vLi4vLi4vLi4vYXNzZXRzL2NhcmJvbi92aW9sZXRfcmVjdGFuZ2xlLnBuZyIpOw0KfQ0KDQoudG90YWwtY2FyZC1iZy1ibHVlIHsNCiAgYmFja2dyb3VuZDogdXJsKCIuLi8uLi8uLi8uLi9hc3NldHMvY2FyYm9uL3NreV9ibHVlX3JlY3RhbmdsZS5wbmciKTsNCn0NCi50b3RhbC1jYXJkIHsNCiAgYmFja2dyb3VuZC1zaXplOiAxMDAlIDEwMCU7DQogIG1hcmdpbi1yaWdodDogMXJlbTsNCg0KICAmID4gLm51bWJlci10eXBlIHsNCiAgICBmb250LXNpemU6IDFyZW07DQogICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgZm9udC1mYW1pbHk6IEhhcm1vbnlPU19TYW5zX0JsYWNrOw0KICAgIGNvbG9yOiAjZmZmZmZmOw0KICAgIHBhZGRpbmc6IDAuNXJlbSAwIDAuN3JlbSAxLjVyZW07DQogIH0NCg0KICAmID4gLnRleHQtdHlwZSB7DQogICAgZm9udC1zaXplOiAxcmVtOw0KICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLU1lZGl1bSwgUGluZ0ZhbmcgU0M7DQogICAgY29sb3I6ICNmZmZmZmY7DQogICAgbGV0dGVyLXNwYWNpbmc6IDAuMXJlbTsNCiAgICBwYWRkaW5nOiAwIDAgMC42cmVtIDFyZW07DQogIH0NCn0NCg0KLnRvdGFsLWNhcmQtcm93IHsNCiAgbWFyZ2luOiAwOw0KICBtYXJnaW46IDAgMTBweCAxMHB4IDEwcHg7DQogIC5lbC1jb2wgew0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIH0NCn0NCi5wYWdlLWNsYXNzIHsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgcGFkZGluZzogMTBweCAyMHB4IDAgMjBweDsNCiAgaGVpZ2h0OiAxMDAlOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAuZm9ybS1oZWFkIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICB9DQogIC5hdnVlLWNydWQgew0KICAgIGZsZXg6IDE7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["original.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+yBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "original.vue", "sourceRoot": "src/view/carbon/discharge/energyview", "sourcesContent": ["<template>\r\n  <div class=\"page-class common-el\" id=\"modularForm\">\r\n    <el-form\r\n      class=\"form-head\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      :model=\"formData\"\r\n      label-width=\"80px\"\r\n    >\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <el-form-item label=\"上报年月：\">\r\n            <el-date-picker\r\n              v-model=\"formData.reportTime\"\r\n              value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n              placeholder=\"请选择日期\"\r\n              type=\"month\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"数据部门：\">\r\n            <el-select\r\n              v-model=\"formData.companyId\"\r\n              placeholder=\"请选择数据部门\"\r\n              clearable\r\n            >\r\n              <el-option\r\n                v-for=\"item in reportCompanyList\"\r\n                :label=\"item.companyName\"\r\n                :value=\"item.companyId\"\r\n                @click.native=\"deptChange(item)\"\r\n                :key=\"item.companyId\"\r\n              ></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button class=\"searchBtn\" @click=\"searchList\">搜索</el-button>\r\n            <el-button class=\"resetBtn\" @click=\"reset\">重置</el-button>\r\n            <el-button class=\"resetBtn\" @click=\"downloadOpt\" v-if=\"reportDialog.count > 0\"\r\n              >导出</el-button\r\n            >\r\n            <el-button class=\"searchBtn\" @click=\"reportToGroup\">上报到集团</el-button>\r\n            <el-button class=\"searchBtn\" @click=\"reportToGroupLog\">上报历史</el-button>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"total-card-row\">\r\n      <el-col :span=\"6\" style=\"padding: 0\">\r\n        <div\r\n          class=\"total-card total-card-bg-green\"\r\n          @click=\"handleReportCompany()\"\r\n          style=\"cursor: pointer\"\r\n        >\r\n          <div class=\"number-type\">\r\n            {{ this.reportDialog.count }}\r\n            <span\r\n              style=\"font-size: smaller; font-weight: normal\"\r\n              v-if=\"reportDialog.applyReturnCount > 0\"\r\n              >(退回申请{{ reportDialog.applyReturnCount }})</span\r\n            >\r\n          </div>\r\n          <div class=\"text-type\">已上报部门</div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"6\" style=\"padding: 0; cursor: pointer\">\r\n        <div class=\"total-card total-card-bg-yellow\" @click=\"handleUnReportCompany()\">\r\n          <div class=\"number-type\">{{ this.unReportDialog.count }}</div>\r\n          <div class=\"text-type\">未上报部门</div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"6\" style=\"padding: 0; cursor: pointer\">\r\n        <div class=\"total-card total-card-bg-red\">\r\n          <div class=\"number-type\">{{ this.reportDialog.reportRate }}%</div>\r\n          <div class=\"text-type\">上报率</div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <div class=\"curd-header\">\r\n      <div class=\"reporting\">\r\n        <p>\r\n          填报月份:<span>{{ formData.chnTime }}</span>\r\n        </p>\r\n      </div>\r\n      <p class=\"companyName\">{{ this.obj.deptName }}数据</p>\r\n    </div>\r\n    <avue-crud\r\n      ref=\"crud\"\r\n      :data=\"tableData\"\r\n      :table-loading=\"tableLoading\"\r\n      :option=\"tableOption\"\r\n    >\r\n    </avue-crud>\r\n    <!-- 弹窗, 已上报部门 -->\r\n    <el-dialog\r\n      :visible.sync=\"reportDialog.visible\"\r\n      title=\"已上报部门\"\r\n      width=\"65%\"\r\n      :append-to-body=\"true\"\r\n    >\r\n      <avue-crud\r\n        ref=\"report\"\r\n        :data=\"reportDialog.companyList\"\r\n        :option=\"reportDialog.tableOption\"\r\n      >\r\n        <template slot-scope=\"scope\" slot=\"menu\">\r\n          <el-button type=\"text\" @click=\"handleEdit(scope.row)\">修改上报数据 </el-button>\r\n          <el-button type=\"text\" @click=\"handleRejectData(scope.row)\"\r\n            >退回数据\r\n          </el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            v-if=\"scope.row.recordCount && scope.row.recordCount > 1\"\r\n            @click=\"modifyReport(scope.row)\"\r\n            >修改记录\r\n          </el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            v-if=\"scope.row.reportFlag == '4'\"\r\n            @click=\"handleRejectReason(scope.row)\"\r\n            >申请理由\r\n          </el-button>\r\n        </template>\r\n      </avue-crud>\r\n      <div class=\"foot_btn\">\r\n        <el-button type=\"success\" @click=\"reportDialog.visible = false\">关闭 </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 弹窗, 未上报部门 -->\r\n    <el-dialog\r\n      :visible.sync=\"unReportDialog.visible\"\r\n      title=\"未上报部门\"\r\n      width=\"60%\"\r\n      :append-to-body=\"true\"\r\n    >\r\n      <div style=\"margin: 0vh 2rem 2vh\">\r\n        <el-button type=\"success\" @click=\"handleRemind()\">提醒上报</el-button>\r\n      </div>\r\n      <avue-crud\r\n        ref=\"unReport\"\r\n        @selection-change=\"remindSelectionChange\"\r\n        :data=\"unReportDialog.companyList\"\r\n        :option=\"unReportDialog.tableOption\"\r\n      >\r\n      </avue-crud>\r\n    </el-dialog>\r\n\r\n    <!-- 导出提示弹窗 -->\r\n    <el-dialog\r\n      :visible.sync=\"downDialog.visible\"\r\n      :title=\"downDialog.title\"\r\n      width=\"28%\"\r\n      :append-to-body=\"true\"\r\n      class=\"common-el\"\r\n    >\r\n      <div>\r\n        <el-radio v-model=\"downOpts\" label=\"1\">导出选择部门数据</el-radio>\r\n      </div>\r\n      <div>\r\n        <el-radio v-model=\"downOpts\" label=\"2\">导出所有已上报部门数据</el-radio>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button class=\"cancelBtn\" @click=\"downDialog.visible = false\" type=\"success\"\r\n          >取消</el-button\r\n        >\r\n        <el-button class=\"okBtn\" type=\"info\" @click=\"downExcel()\">确定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!--退回理由弹框  -->\r\n    <rejectDialog\r\n      :dialog-show=\"rejectDialog.visible\"\r\n      :rejectId=\"rejectDialog.id\"\r\n      :mode=\"rejectDialog.mode\"\r\n      :content=\"rejectDialog.content\"\r\n      :title=\"rejectDialog.title\"\r\n      :tips=\"rejectDialog.tips\"\r\n      @dialogRes=\"rejectDialogClose\"\r\n      @dialogConfirm=\"rejectDialogConfirm\"\r\n    />\r\n\r\n    <!--退回确认弹框  -->\r\n    <el-dialog\r\n      width=\"28%\"\r\n      :visible.sync=\"rejectConfirmDialog.visible\"\r\n      title=\"退回确认\"\r\n      :append-to-body=\"true\"\r\n    >\r\n      <delects\r\n        @delectRow=\"rejectEnergyData\"\r\n        @cancel=\"rejectConfirmDialog.visible = false\"\r\n        content=\"是否确认退回当前数据？\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      width=\"50%\"\r\n      :visible.sync=\"reportLogDialog.visible\"\r\n      title=\"上报记录\"\r\n      :append-to-body=\"true\"\r\n    >\r\n      <jituan-energy-report-log></jituan-energy-report-log>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  energyAllList,\r\n  rejectData,\r\n  reportList,\r\n  downloadExcel,\r\n  reportToGroup,\r\n  applyReturn,\r\n} from \"@/api/carbon/discharge/energy\";\r\nimport { getRemindCount, remindSaveList } from \"@/api/carbon/discharge/notification\";\r\n// import store from \"@/store\";\r\n// import emptyStrate from \"@/components/empty\";\r\nimport { export_json_to_excel, getExcelColName } from \"@/api/carbon/excel/export\";\r\nimport delects from \"@/components/delects/index\";\r\n// import {mapGetters} from \"vuex\";\r\nimport { dateFormat } from \"@/api/carbon/date\";\r\nimport { downloadFile } from \"@/api/carbon/ruoyi\";\r\nimport jituanEnergyReportLog from \"./jituanEnergyReportLog\";\r\nimport rejectDialog from \"@/components/carbon/rejectDialog\";\r\nimport { getUserByUserRole } from \"@/api/basedata/ammeter\";\r\n\r\nexport default {\r\n  components: {\r\n    // emptyStrate,\r\n    delects,\r\n    jituanEnergyReportLog,\r\n    rejectDialog,\r\n  },\r\n  data() {\r\n    return {\r\n      role: {\r\n        report: undefined,\r\n        reportLog: undefined,\r\n      },\r\n      tmpDeptName: undefined,\r\n      obj: {\r\n        // 当前登录的公司名称\r\n        deptName: \"全省汇总\",\r\n        // 碳排放量\r\n      },\r\n      rejectDialog: {\r\n        title: \"退回理由\",\r\n        tips: \"请输入退回理由\",\r\n        visible: false,\r\n        id: undefined,\r\n        content: \"\",\r\n        mode: 1,\r\n      },\r\n      downDialog: {\r\n        visible: false,\r\n        title: \"导出提示\",\r\n      },\r\n      tableLoading: false,\r\n      rejectDataVisible: false,\r\n      reportLogDialog: {\r\n        visible: false,\r\n      },\r\n      rejectConfirmDialog: {\r\n        visible: false,\r\n        companyId: undefined,\r\n      },\r\n      reportCompanyList: [],\r\n      downOpts: \"1\",\r\n      reportDialog: {\r\n        visible: false,\r\n        companyList: [],\r\n        count: 0,\r\n        applyReturnCount: 0,\r\n        reportRate: 0.0,\r\n        tableOption: {\r\n          border: false,\r\n          height: \"50vh\",\r\n          index: true,\r\n          indexLabel: \"序号\",\r\n          indexWidth: 70,\r\n          stripe: true,\r\n          menuAlign: \"center\",\r\n          align: \"center\",\r\n          refreshBtn: false,\r\n          showClomnuBtn: false,\r\n          addBtn: false,\r\n          editBtn: false,\r\n          delBtn: false,\r\n          gridBtn: false,\r\n          columnBtn: false,\r\n          menuWidth: 265,\r\n          searchBtn: false,\r\n          emptyBtn: false,\r\n          column: [\r\n            {\r\n              label: \"部门名称\",\r\n              prop: \"companyName\",\r\n              overHidden: true,\r\n            },\r\n            {\r\n              label: \"上报时间\",\r\n              prop: \"createTime\",\r\n              overHidden: true,\r\n            },\r\n            {\r\n              label: \"提醒次数\",\r\n              prop: \"remindCount\",\r\n              width: 100,\r\n              overHidden: true,\r\n            },\r\n            {\r\n              label: \"申请退回\",\r\n              prop: \"reportFlag\",\r\n              formatter: (val, value, label) => {\r\n                return value == \"4\" ? \"是\" : \"否\";\r\n              },\r\n              width: 100,\r\n              overHidden: true,\r\n            },\r\n          ],\r\n        },\r\n      },\r\n      unReportDialog: {\r\n        visible: false,\r\n        companyList: [],\r\n        remindCompanyList: [],\r\n        count: 0,\r\n        tableOption: {\r\n          border: false,\r\n          index: true,\r\n          height: \"50vh\",\r\n          selection: true,\r\n          tip: false,\r\n          indexWidth: 70,\r\n          indexLabel: \"序号\",\r\n          stripe: true,\r\n          menuAlign: \"center\",\r\n          align: \"center\",\r\n          refreshBtn: false,\r\n          showClomnuBtn: false,\r\n          addBtn: false,\r\n          menu: false,\r\n          editBtn: false,\r\n          gridBtn: false,\r\n          delBtn: false,\r\n          columnBtn: false,\r\n          searchBtn: false,\r\n          emptyBtn: false,\r\n          column: [\r\n            {\r\n              label: \"部门名称\",\r\n              prop: \"companyName\",\r\n              overHidden: true,\r\n            },\r\n            {\r\n              label: \"提醒次数\",\r\n              prop: \"remindCount\",\r\n              overHidden: true,\r\n            },\r\n          ],\r\n        },\r\n      },\r\n      tableData: [],\r\n      energyData: [],\r\n      tableOption: {\r\n        border: false,\r\n        index: false,\r\n        height: \"auto\",\r\n        calcHeight: 35,\r\n        stripe: true,\r\n        menuAlign: \"center\",\r\n        align: \"center\",\r\n        refreshBtn: false,\r\n        showClomnuBtn: false,\r\n        searchMenuSpan: 4,\r\n        searchSize: \"mini\",\r\n        card: true,\r\n        addBtn: false,\r\n        editBtn: false,\r\n        delBtn: false,\r\n        columnBtn: false,\r\n        searchBtn: false,\r\n        emptyBtn: false,\r\n        gridBtn: false,\r\n        menu: false,\r\n        dialogWidth: 500,\r\n        dialogMenuPosition: \"center\",\r\n        dialogCustomClass: \"singleRowDialog\",\r\n        labelWidth: 100,\r\n        column: [\r\n          {\r\n            label: \"指标\",\r\n            prop: \"indicatorName\",\r\n            overHidden: true,\r\n            width: 500,\r\n            align: \"left\",\r\n          },\r\n          {\r\n            label: \"集团\",\r\n            prop: \"groupData\",\r\n            formatter: (val, value, label) => {\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: false,\r\n          },\r\n          {\r\n            label: \"股份\",\r\n            prop: \"stockData\",\r\n            formatter: (val, value, label) => {\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: false,\r\n          },\r\n          {\r\n            label: \"数据中心\",\r\n            children: [\r\n              {\r\n                label: \"大型\",\r\n                prop: \"largeData\",\r\n                formatter: (val, value, label) => {\r\n                  return this.formatDisplayData(value);\r\n                },\r\n                overHidden: true,\r\n                cell: true,\r\n                slot: true,\r\n              },\r\n              {\r\n                label: \"中小型\",\r\n                prop: \"mediumData\",\r\n                formatter: (val, value, label) => {\r\n                  return this.formatDisplayData(value);\r\n                },\r\n                cell: true,\r\n                slot: true,\r\n                overHidden: true,\r\n              },\r\n            ],\r\n          },\r\n          {\r\n            label: \"移动业务\",\r\n            prop: \"mobileData\",\r\n            formatter: (val, value, label) => {\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: false,\r\n          },\r\n        ],\r\n      },\r\n      formData: {\r\n        reportTime: undefined,\r\n        chnTime: undefined,\r\n        currentTime: undefined,\r\n      },\r\n      queryForm: {\r\n        reportTime: undefined,\r\n        companyId: undefined,\r\n        companyIds: [],\r\n      },\r\n      companyRemindList: [],\r\n      userInfo: {\r\n        deptId: undefined,\r\n        companyName: undefined,\r\n        isAdmin: false,\r\n      },\r\n    };\r\n  },\r\n  // computed: {\r\n  //   ...mapGetters([\"permissions\"])\r\n  // },\r\n  created() {\r\n    if (this.$route.query.reportTime) {\r\n      this.formData.currentTime = this.$route.query.reportTime;\r\n    } else {\r\n      let lastMonth = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);\r\n      this.formData.currentTime = dateFormat(lastMonth, \"yyyy-MM-01 0:00:00\");\r\n    }\r\n    this.formData.reportTime = this.formData.currentTime;\r\n    this.formData.chnTime = dateFormat(new Date(this.formData.currentTime), \"yyyy年MM月\");\r\n    this.initUserInfo();\r\n    this.getList();\r\n    // this.role.report = this.permissions[\"discharge_view_energy_report\"];\r\n    // this.role.reportLog = this.permissions[\"discharge_view_energy_report_log\"];\r\n  },\r\n  methods: {\r\n    reset() {\r\n      let lastMonth = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);\r\n      this.formData.currentTime = dateFormat(lastMonth, \"yyyy-MM-01 0:00:00\");\r\n      this.formData.chnTime = dateFormat(lastMonth, \"yyyy年MM月\");\r\n      this.formData.reportTime = this.formData.currentTime;\r\n      this.formData.companyId = undefined;\r\n      this.tmpDeptName = undefined;\r\n      this.obj.deptName = \"全省汇总\";\r\n      (this.queryForm = {\r\n        reportTime: undefined,\r\n        companyId: undefined,\r\n        companyIds: [],\r\n      }),\r\n        (this.downOpts = \"1\");\r\n      this.getList();\r\n    },\r\n    handleEdit(row) {\r\n      this.$router.push({\r\n        path: \"energy_edit\",\r\n        query: {\r\n          companyId: row.companyId,\r\n          companyName: row.companyName,\r\n          reportTime: this.formData.currentTime,\r\n        },\r\n      });\r\n    },\r\n    handleRejectData(row) {\r\n      this.rejectDialog.mode = 1;\r\n      this.rejectDialog.title = \"退回理由\";\r\n      this.rejectDialog.id = row.companyId;\r\n      if (row.reportFlag == \"4\") {\r\n        this.rejectDialog.content = \"主动申请退回\";\r\n      }\r\n      this.rejectDialog.visible = true;\r\n    },\r\n    handleRejectReason(row) {\r\n      this.rejectDialog.mode = 0;\r\n      this.rejectDialog.title = \"申请退回理由\";\r\n      this.rejectDialog.content = row.applyReason;\r\n      this.rejectDialog.visible = true;\r\n    },\r\n    modifyReport(row) {\r\n      this.$router.push({\r\n        path: \"/dataView/energy/modifyReport\",\r\n        query: {\r\n          companyId: row.companyId,\r\n          companyName: row.companyName,\r\n          reportTime: this.formData.currentTime,\r\n        },\r\n      });\r\n    },\r\n    rejectEnergyData() {\r\n      rejectData(\r\n        Object.assign({\r\n          companyId: this.rejectConfirmDialog.companyId,\r\n          returnReason: this.rejectDialog.content,\r\n          reportTime: this.formData.currentTime,\r\n        })\r\n      ).then((res) => {\r\n        if (res.data.code == 0) {\r\n          this.rejectDialog.content = \"\";\r\n          this.rejectDialog.visible = false;\r\n          this.rejectConfirmDialog.visible = false;\r\n          this.reportDialog.visible = false;\r\n          this.alertSuccess(\"操作成功\");\r\n          this.getList();\r\n        }\r\n      });\r\n    },\r\n    handleReportCompany() {\r\n      this.reportDialog.companyList.forEach((item) => {\r\n        item.remindCount = this.getCompanyRemindCount(item.companyId);\r\n      });\r\n      this.reportDialog.visible = true;\r\n    },\r\n    handleUnReportCompany() {\r\n      this.unReportDialog.companyList.forEach((item) => {\r\n        item.remindCount = this.getCompanyRemindCount(item.companyId);\r\n      });\r\n      this.unReportDialog.remindCompanyList = [];\r\n      this.unReportDialog.visible = true;\r\n    },\r\n    handleRemind() {\r\n      if (this.unReportDialog.remindCompanyList.length == 0) {\r\n        this.alertError(\"请选择需要提醒的部门\");\r\n        return;\r\n      }\r\n      remindSaveList(this.unReportDialog.remindCompanyList).then((res) => {\r\n        if (res.data.code == 0) {\r\n          this.unReportDialog.visible = false;\r\n          this.alertSuccess(\"提醒发送成功\");\r\n          this.getRemindCountList(this.formData.currentTime);\r\n        } else {\r\n          this.alertError(\"提醒发送失败\");\r\n        }\r\n      });\r\n    },\r\n    remindSelectionChange(list) {\r\n      this.unReportDialog.remindCompanyList = [].concat(list);\r\n    },\r\n    // 部门改变触发\r\n    deptChange(item) {\r\n      this.tmpDeptName = item.companyName;\r\n    },\r\n    //搜索数据\r\n    searchList() {\r\n      if (this.formData.reportTime == undefined) {\r\n        this.alertError(\"请选择上报年月\");\r\n        return;\r\n      }\r\n      if (this.tmpDeptName != undefined) {\r\n        this.obj.deptName = this.tmpDeptName;\r\n      }\r\n      this.formData.currentTime = this.formData.reportTime;\r\n      this.getList();\r\n    },\r\n    // 查询上报情况列表\r\n    reportList() {\r\n      reportList(this.queryForm).then((res) => {\r\n        if (res.data) {\r\n          for (let i = 0; i < res.data.length; i++) {\r\n            if (res.data[i].reportStatus.indexOf(\"已上报\") > -1) {\r\n              this.reportDialog.count += 1;\r\n              if (res.data[i].reportFlag == \"4\") {\r\n                this.reportDialog.applyReturnCount += 1;\r\n              }\r\n              this.reportDialog.companyList.push({\r\n                companyId: res.data[i].companyId,\r\n                companyName: res.data[i].companyName,\r\n                createTime: res.data[i].createTime,\r\n                reportTime: this.queryForm.currentTime,\r\n                reportFlag: res.data[i].reportFlag,\r\n                applyReason: res.data[i].applyReason,\r\n                returnReason: res.data[i].returnReason,\r\n                recordCount: res.data[i].recordCount,\r\n                remindCount: 0,\r\n              });\r\n              this.reportCompanyList.push({\r\n                companyId: res.data[i].companyId,\r\n                companyName: res.data[i].companyName,\r\n                createTime: res.data[i].createTime,\r\n                reportTime: this.queryForm.currentTime,\r\n                reportFlag: res.data[i].reportFlag,\r\n                applyReason: res.data[i].applyReason,\r\n                returnReason: res.data[i].returnReason,\r\n                recordCount: res.data[i].recordCount,\r\n                remindCount: 0,\r\n              });\r\n            } else if (res.data[i].reportStatus.indexOf(\"未上报\") > -1) {\r\n              this.unReportDialog.count += 1;\r\n              this.unReportDialog.companyList.push({\r\n                companyId: res.data[i].companyId,\r\n                reportTime: this.formData.currentTime,\r\n                companyName: res.data[i].companyName,\r\n                remindCount: 0,\r\n              });\r\n            }\r\n          }\r\n          this.reportDialog.companyList.sort(function (a, b) {\r\n            return b.createTime > a.createTime ? -1 : 1;\r\n          });\r\n          this.reportDialog.reportRate = (\r\n            (this.reportDialog.count * 100) /\r\n            (this.reportDialog.count + this.unReportDialog.count)\r\n          ).toFixed(2);\r\n        }\r\n      });\r\n    },\r\n    // 查询列表\r\n    getList() {\r\n      this.formData.chnTime = dateFormat(\r\n        new Date(this.formData.currentTime),\r\n        \"yyyy年MM月\"\r\n      );\r\n      this.tableLoading = true;\r\n      this.reportDialog.reportRate = 0;\r\n      this.reportDialog.count = 0;\r\n      this.reportDialog.applyReturnCount = 0;\r\n      this.unReportDialog.count = 0;\r\n      this.reportDialog.companyList = [];\r\n      this.unReportDialog.companyList = [];\r\n      this.reportCompanyList = [];\r\n      this.reportCompanyList.unshift({\r\n        companyId: undefined,\r\n        companyName: \"全省汇总\",\r\n        reportTime: this.formData.currentTime,\r\n      });\r\n      this.queryForm.reportTime = this.formData.currentTime;\r\n      this.queryForm.companyId = this.formData.companyId;\r\n      energyAllList(this.queryForm).then((res) => {\r\n        if (res.data) {\r\n          this.reportList();\r\n          this.tableData = res.data;\r\n          this.tableLoading = false;\r\n          this.getRemindCountList(this.formData.currentTime);\r\n        }\r\n      });\r\n    },\r\n    // 获取提醒列表\r\n    getRemindCountList(reportTime) {\r\n      getRemindCount(Object.assign({ reportTime: reportTime })).then((res) => {\r\n        if (res.data) {\r\n          this.companyRemindList = res.data;\r\n        }\r\n      });\r\n    },\r\n    getCompanyRemindCount(companyId) {\r\n      let ret = 0;\r\n      for (let i = 0; i < this.companyRemindList.length; i++) {\r\n        if (companyId == this.companyRemindList[i].companyId) {\r\n          ret = this.companyRemindList[i].remindCount;\r\n          return ret;\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n    rejectDialogClose() {\r\n      this.rejectDialog.content = \"\";\r\n      this.rejectDialog.visible = false;\r\n    },\r\n    rejectDialogConfirm(content) {\r\n      if (content.length == 0) {\r\n        this.alertError(\"退回理由不能为空\");\r\n        return;\r\n      }\r\n      this.rejectDialog.content = content;\r\n      this.rejectConfirmDialog.companyId = this.rejectDialog.id;\r\n      this.rejectConfirmDialog.visible = true;\r\n    },\r\n    formatDisplayData(value) {\r\n      let data = parseFloat(value);\r\n      if (isNaN(value)) {\r\n        return value;\r\n      } else {\r\n        return data.toFixed(2);\r\n      }\r\n    },\r\n    // 上报到集团日志\r\n    reportToGroupLog() {\r\n      this.reportLogDialog.visible = true;\r\n    },\r\n    // 上报到集团\r\n    reportToGroup() {\r\n      let currentDate = new Date(); // 获取当前日期时间戳\r\n      let reportTime = new Date(this.queryForm.reportTime).getTime(); // 获取报告日期时间戳\r\n      let lastDays =\r\n        new Date(currentDate.getFullYear(), currentDate.getMonth(), 0).getDate() + 10;\r\n      let oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数\r\n      let diffDays = Math.abs((currentDate.getTime() - reportTime) / oneDay); // 两个日期时间戳差值除以一天的毫秒数得到相差天数\r\n      // if (diffDays > lastDays) {\r\n      //   this.alertError(\"请于每月1-10号上报数据\");\r\n      //   return;\r\n      // }\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"正在上报数据，请稍侯...\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      reportToGroup({ reportTime: this.queryForm.reportTime, countType: \"0\" }).then(\r\n        (res) => {\r\n          loading.close();\r\n          if (res.data.code == 0) {\r\n            this.alertSuccess(\"操作成功\");\r\n          }\r\n        }\r\n      );\r\n    },\r\n    downloadOpt() {\r\n      this.downDialog.visible = true;\r\n    },\r\n    checkOpts() {\r\n      if (this.downOpts == \"2\") {\r\n        // 所有上报的部门数据\r\n        this.reportDialog.companyList.forEach((node) => {\r\n          if (node.companyId != undefined) {\r\n            this.queryForm.companyIds.push(node.companyId);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    initUserInfo() {\r\n      let that = this;\r\n      getUserByUserRole().then((res) => {\r\n        //根据权限获取分公司\r\n        if (res.data) {\r\n          that.userInfo.companyName = res.data.companies[0].name;\r\n          that.userInfo.deptId = res.data.companies[0].id;\r\n          that.userInfo.isAdmin = res.data.isCityAdmin || res.data.isSubAdmin;\r\n          // res.data.companies.forEach((item) => {\r\n          //   this.companyOption.push({ value: item.id, label: item.name });\r\n          // });\r\n          // if (this.companyOption && this.companyOption.length > 0) {\r\n          //   this.formData.companyId = this.companyOption[0].value;\r\n          //   this.getList();\r\n          // }\r\n        }\r\n      });\r\n    },\r\n    downExcel() {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"导出中\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      this.checkOpts();\r\n      this.queryForm.reportCompany = this.obj.deptName + \"数据\";\r\n      this.queryForm.reportMonth = this.formData.chnTime;\r\n      this.queryForm.downType = this.downOpts;\r\n      downloadExcel(this.queryForm).then((res) => {\r\n        downloadFile(res, this.formData.chnTime + this.obj.deptName + \"能源数据汇总.xls\");\r\n        loading.close();\r\n        this.downOpts = \"1\";\r\n        this.downDialog.visible = false;\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n/deep/.el-textarea {\r\n  width: 39rem !important;\r\n  height: 80px !important;\r\n  & > .el-textarea__inner {\r\n    height: 120px !important;\r\n  }\r\n}\r\np {\r\n  margin: 0;\r\n}\r\n\r\n/deep/ .avue-crud__menu {\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\nul.score_box {\r\n  width: 100%;\r\n  height: 6.5vh;\r\n  display: flex;\r\n\r\n  li {\r\n    width: 13.5rem;\r\n    height: 6.5vh;\r\n    padding: 0.4vh 1.5rem 0;\r\n\r\n    & > p:nth-of-type(1) {\r\n      font-size: 2rem;\r\n      font-family: HarmonyOS-Sans-Black;\r\n      color: #ffffff;\r\n    }\r\n\r\n    & > p:nth-of-type(2) {\r\n      font-size: 1.3rem;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #ffffff;\r\n      margin-top: 0.5vh;\r\n    }\r\n  }\r\n\r\n  & > li:nth-of-type(1) {\r\n    background: url(\"../../../../assets/carbon/totalTaskScore.png\");\r\n    background-size: 100% 100%;\r\n  }\r\n\r\n  & > li:nth-of-type(2) {\r\n    margin-left: 2rem;\r\n    background: url(\"../../../../assets/carbon/assessmenscore.png\");\r\n    background-size: 100% 100%;\r\n  }\r\n\r\n  & > li:nth-of-type(3) {\r\n    margin-left: 2rem;\r\n    background: url(\"../../../../assets/carbon/orange.png\");\r\n    background-size: 100% 100%;\r\n  }\r\n}\r\n/deep/.el-dialog__footer {\r\n  display: flex;\r\n  padding-bottom: 4vh;\r\n  justify-content: center;\r\n}\r\n\r\nul.appraisalTemplate {\r\n  width: 99%;\r\n  height: 68vh;\r\n  margin-top: 2vh;\r\n  margin-left: 2rem;\r\n\r\n  & > li {\r\n    width: 39.4rem;\r\n    height: 15vh;\r\n    float: left;\r\n    padding: 1.5vh 0;\r\n    margin-bottom: 2vh;\r\n    margin-right: 2rem;\r\n    background: #14283f;\r\n    position: relative;\r\n\r\n    header {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      & > p:nth-of-type(1) {\r\n        width: 6px;\r\n        height: 18px;\r\n        background: linear-gradient(360deg, #3cfaea 0%, #0edaa4 100%);\r\n        box-shadow: 1px 0px 4px 0px rgba(52, 255, 186, 0.45);\r\n        border-radius: 0px 3px 3px 0px;\r\n      }\r\n\r\n      & > p:nth-of-type(2) {\r\n        font-size: 16px;\r\n        margin-left: 1.4rem;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n\r\n    & > p:nth-of-type(1) {\r\n      font-size: 1.3rem;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n      margin-top: 2.3vh;\r\n      margin-left: 2rem;\r\n    }\r\n\r\n    & > p:nth-of-type(2) {\r\n      font-size: 1.3rem;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n      margin-top: 2vh;\r\n      margin-left: 2rem;\r\n    }\r\n\r\n    & > div {\r\n      width: 11rem;\r\n      height: 9vh;\r\n      position: absolute;\r\n      right: 0.5rem;\r\n      bottom: 2.3vh;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n\r\n      & > img {\r\n        width: 5.1rem;\r\n        height: 5.4rem;\r\n      }\r\n\r\n      & > p {\r\n        font-size: 13px;\r\n        letter-spacing: 0.05rem;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        font-weight: 400;\r\n        color: #00edc0;\r\n        margin-top: 0.7vh;\r\n      }\r\n    }\r\n  }\r\n\r\n  & > li:hover {\r\n    background: #2d486a;\r\n  }\r\n}\r\n\r\n.curd-header {\r\n  width: 100%;\r\n  height: 50px;\r\n  margin-bottom: 15px;\r\n  display: flex;\r\n  margin-top: 1vh;\r\n  background: #f6f8fa;\r\n  color: #303b50;\r\n  position: relative;\r\n\r\n  .commonfont {\r\n    font-size: 14px;\r\n    line-height: 50px;\r\n    letter-spacing: 1px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    font-weight: 400;\r\n    color: #00ecc0;\r\n  }\r\n\r\n  & > .reporting {\r\n    width: 690px;\r\n    display: flex;\r\n    margin-left: 10px;\r\n    align-items: center;\r\n\r\n    p {\r\n      @extend .commonfont;\r\n      margin-right: 10px;\r\n\r\n      & > span {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    /deep/ .el-date-editor.el-input,\r\n    .el-date-editor.el-input__inner {\r\n      width: 200px !important;\r\n    }\r\n  }\r\n\r\n  & > .companyName {\r\n    @extend .commonfont;\r\n    line-height: 50px;\r\n  }\r\n}\r\n\r\n.foot_btn {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.total-card-bg-green {\r\n  background: url(\"../../../../assets/carbon/wild_green_rectangle.png\");\r\n}\r\n\r\n.total-card-bg-yellow {\r\n  background: url(\"../../../../assets/carbon/summer_yellow_rectangle.png\");\r\n}\r\n\r\n.total-card-bg-red {\r\n  background: url(\"../../../../assets/carbon/maiden_red_rectangle.png\");\r\n}\r\n\r\n.total-card-bg-violet {\r\n  background: url(\"../../../../assets/carbon/violet_rectangle.png\");\r\n}\r\n\r\n.total-card-bg-blue {\r\n  background: url(\"../../../../assets/carbon/sky_blue_rectangle.png\");\r\n}\r\n.total-card {\r\n  background-size: 100% 100%;\r\n  margin-right: 1rem;\r\n\r\n  & > .number-type {\r\n    font-size: 1rem;\r\n    font-weight: bold;\r\n    font-family: HarmonyOS_Sans_Black;\r\n    color: #ffffff;\r\n    padding: 0.5rem 0 0.7rem 1.5rem;\r\n  }\r\n\r\n  & > .text-type {\r\n    font-size: 1rem;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    color: #ffffff;\r\n    letter-spacing: 0.1rem;\r\n    padding: 0 0 0.6rem 1rem;\r\n  }\r\n}\r\n\r\n.total-card-row {\r\n  margin: 0;\r\n  margin: 0 10px 10px 10px;\r\n  .el-col {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n.page-class {\r\n  background: #fff;\r\n  padding: 10px 20px 0 20px;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  .form-head {\r\n    margin-bottom: 10px;\r\n  }\r\n  .avue-crud {\r\n    flex: 1;\r\n  }\r\n}\r\n</style>\r\n"]}]}