{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addSelfPowerAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePageAccount\\addSelfPowerAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBtYXBTdGF0ZSB9IGZyb20gInZ1ZXgiOw0KaW1wb3J0IHsgZ2V0QXVkaXRSZXN1bHQsIGdldEF1ZGl0UmVzdWx0TmV3LCBnZXRBdWRpdFJlc3VsdE5ld19RWE0gfSBmcm9tICJAL2FwaS9hY2NvdW50IjsNCmltcG9ydCB7DQogIGFkZFNlbGZQb3dlckFjY291bnQsDQogIHRlbXBvcmFyeVN0b3JhZ2UsDQogIGdldEVsZWN0clF1b3RhLA0KICBlZGl0T3duLA0KICByZW1vdmVPd24sDQogIHNlbGVjdEJ5UGNpZCwNCiAgZ2V0VXNlciwNCiAgZ2V0RGVwYXJ0bWVudHMsDQogIHNlbGVjdEJ5QW1tZXRlcklkLA0KICBhY2NvdW50VG90YWwsDQogIHNlbGVjdENvbXBsZXRlZE1vbmV5LA0KICBzZWxlY3RJZHNCeVBhcmFtcywNCiAgcmVtb3ZlQWxsLA0KfSBmcm9tICJAL2FwaS9hY2NvdW50IjsNCmltcG9ydCB7IGdldENsYXNzaWZpY2F0aW9uLCBwb3dlcnJlc3VsdCB9IGZyb20gIkAvYXBpL2Jhc2VkYXRhL2FtbWV0ZXIuanMiOw0KaW1wb3J0IHsgYWdhaW5Kb2luIH0gZnJvbSAiQC9hcGkvYWNjb3VudEJpbGxQZXIiOw0KaW1wb3J0IHsgdmFsaWRDb250cmFjdExpc3QgfSBmcm9tICJAL2FwaS9jb250cmFjdCI7DQppbXBvcnQgY2hlY2tSZXN1bHRBbmRSZXNwb25zZSBmcm9tICJAL3ZpZXcvYWNjb3VudC9jaGVjay9jaGVja1Jlc3VsdEFuZFJlc3BvbnNlIjsNCmltcG9ydCBjaGVja1Jlc3VsdCBmcm9tICJAL3ZpZXcvYWNjb3VudC9jaGVjay9jaGVja1Jlc3VsdCI7DQppbXBvcnQgYWxhcm1DaGVjayBmcm9tICJAL3ZpZXcvYWNjb3VudC9jaGVjay9hbGFybUNoZWNrIjsNCmltcG9ydCB7DQogIGdldERhdGVzLA0KICBjdXREYXRlX3l5eXltbWRkLA0KICB0ZXN0TnVtYmVyLA0KICBHZXREYXRlRGlmZiwNCiAgc3RyaW5nVG9EYXRlLA0KICBnZXRDdXJyZW50RGF0ZSwNCn0gZnJvbSAiQC92aWV3L2FjY291bnQvcG93ZXJBY2NvdW50SGVscGVyIjsNCmltcG9ydCB7DQogIF92ZXJpZnlfU3RhcnREYXRlLA0KICBqdWRnZU51bWJlciwNCiAgX3ZlcmlmeV9FbmREYXRlLA0KICBfdmVyaWZ5X1ByZXZUb3RhbFJlYWRpbmdzLA0KICBfdmVyaWZ5X0N1clRvdGFsUmVhZGluZ3MsDQogIG90aGVyX25vX2FtbWV0ZXJvcl9wcm90b2NvbCwNCiAgc2VsZl9ub19hbW1ldGVyb3JfcHJvdG9jb2wsDQogIEhGTF9hbW1ldGVyb3IsDQogIGp1ZGdpbmdfZWRpdGFiaWxpdHksDQogIGp1ZGdpbmdfZWRpdGFiaWxpdHkxLA0KICBfdmVyaWZ5X01vbmV5LA0KICBfY2FsY3VsYXRlVXNlZFJlYWRpbmdzLA0KICBfY2FsY3VsYXRlVXNlZFJlYWRpbmdzRm9yVHlwZV8yLA0KICBfY2FsY3VsYXRlVG90YWxSZWFkaW5ncywNCiAgX2NhbGN1bGF0ZVVuaXRQcmljZUJ5VXNlZE1vbmV5LA0KICBfY2FsY3VsYXRlQWNjb3VudE1vbmV5LA0KICBfY2FsY3VsYXRlUXVvdGVyZWFkaW5nc3JhdGlvLA0KICByZXF1aXJlZEZpZWxkVmFsaWRhdG9yLA0KICBjb3VudFRheGFtb3VudCwNCiAgY2FsY3VsYXRlQWN0dWFsTW9uZXksDQogIGp1ZGdlX25lZ2F0ZSwNCiAganVkZ2VfcmVjb3ZlcnksDQogIGp1ZGdlX3liLA0KICB1bml0cGlyY2VNaW4sDQogIHVuaXRwaXJjZU1heCwNCiAgdW5pdHBpcmNlTWF4MSwNCn0gZnJvbSAiQC92aWV3L2FjY291bnQvUG93ZXJBY2NvdW50Q29udHJvbGxlciI7DQppbXBvcnQgeyB3aWR0aHN0eWxlIH0gZnJvbSAiQC92aWV3L2J1c2luZXNzL21zc0FjY291bnRiaWxsL21zc0FjY291bnRiaWxsZGF0YSI7DQppbXBvcnQgUXVlcnlQZW9wbGVNb2RhbCBmcm9tICJAL3ZpZXcvYWNjb3VudC9xdWVyeVBlb3BsZU1vZGFsIjsNCmltcG9ydCBVcGxvYWRGaWxlTW9kYWwgZnJvbSAiQC92aWV3L2FjY291bnQvdXBsb2FkRmlsZU1vZGFsIjsNCmltcG9ydCBBZGRCaWxsUGVyIGZyb20gIkAvdmlldy9hY2NvdW50L2FkZEJpbGxQcmVNb2RhbCI7DQppbXBvcnQgZXhjZWwgZnJvbSAiQC9saWJzL2V4Y2VsIjsNCmltcG9ydCB7IGJsaXN0IH0gZnJvbSAiQC9saWJzL3Rvb2xzIjsNCmltcG9ydCBheGlvcyBmcm9tICJAL2xpYnMvYXBpLnJlcXVlc3QiOw0KaW1wb3J0IGluZGV4RGF0YSBmcm9tICJAL2NvbmZpZy9pbmRleCI7DQoNCmltcG9ydCBwZXJtaXNzaW9uTWl4aW4gZnJvbSAiQC9taXhpbnMvcGVybWlzc2lvbiI7DQppbXBvcnQgcGFnZUZ1biBmcm9tICJAL21peGlucy9wYWdlRnVuIjsNCg0KbGV0IGRhdGVzID0gZ2V0RGF0ZXMoKTsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBtaXhpbnM6IFtwZXJtaXNzaW9uTWl4aW4sIHBhZ2VGdW5dLA0KDQogIGNvbXBvbmVudHM6IHsNCiAgICBhbGFybUNoZWNrLA0KICAgIGNoZWNrUmVzdWx0LA0KICAgIGNoZWNrUmVzdWx0QW5kUmVzcG9uc2UsDQogICAgVXBsb2FkRmlsZU1vZGFsLA0KICAgIFF1ZXJ5UGVvcGxlTW9kYWwsDQogICAgQWRkQmlsbFBlciwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICBsZXQgcmVuZGVyQ2F0ZWdvcnkgPSAoaCwgcGFyYW1zKSA9PiB7DQogICAgICB2YXIgY2F0ZWdvcnluYW1lID0gIiI7DQogICAgICBmb3IgKGxldCBpdGVtIG9mIHRoaXMuY2F0ZWdvcnlzKSB7DQogICAgICAgIGlmIChpdGVtLnR5cGVDb2RlID09IHBhcmFtcy5yb3cuY2F0ZWdvcnkpIHsNCiAgICAgICAgICBjYXRlZ29yeW5hbWUgPSBpdGVtLnR5cGVOYW1lOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gaCgiZGl2IiwgY2F0ZWdvcnluYW1lKTsNCiAgICB9Ow0KICAgIGxldCByZW5kZXJEaXJlY3RzdXBwbHlmbGFnID0gKGgsIHBhcmFtcykgPT4gew0KICAgICAgdmFyIGRpcmVjdHN1cHBseWZsYWcgPSAiIjsNCiAgICAgIGZvciAobGV0IGl0ZW0gb2YgdGhpcy5kaXJlY3RzdXBwbHlmbGFncykgew0KICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSBwYXJhbXMucm93LmRpcmVjdHN1cHBseWZsYWcpIHsNCiAgICAgICAgICBkaXJlY3RzdXBwbHlmbGFnID0gaXRlbS50eXBlTmFtZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGgoImRpdiIsIGRpcmVjdHN1cHBseWZsYWcpOw0KICAgIH07DQogICAgbGV0IHBob3RvID0gKGgsIHsgcm93LCBpbmRleCB9KSA9PiB7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICBsZXQgc3RyID0gIiI7DQogICAgICBpZiAocm93LnByb2plY3RuYW1lICE9ICLlsI/orqEiICYmIHJvdy5wcm9qZWN0bmFtZSAhPSAi5ZCI6K6hIikgew0KICAgICAgICBzdHIgPSAi5LiK5LygIjsNCiAgICAgIH0NCiAgICAgIHJldHVybiBoKCJkaXYiLCBbDQogICAgICAgIGgoDQogICAgICAgICAgInUiLA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG9uOiB7DQogICAgICAgICAgICAgIGNsaWNrKCkgew0KICAgICAgICAgICAgICAgIC8v5omT5byA5by55Ye65qGGDQogICAgICAgICAgICAgICAgaWYgKHJvdy5wcm9qZWN0bmFtZSAhPSAi5bCP6K6hIiAmJiByb3cucHJvamVjdG5hbWUgIT0gIuWQiOiuoSIpIHsNCiAgICAgICAgICAgICAgICAgIHRoYXQudXBsb2FkRmlsZShyb3cpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgfSwNCiAgICAgICAgICBzdHINCiAgICAgICAgKSwNCiAgICAgIF0pOw0KICAgIH07DQoNCiAgICByZXR1cm4gew0KICAgICAgaXNUOiB0cnVlLA0KICAgICAgbnVtYmVyMjogMCwNCiAgICAgIG5hbWU6ICIiLA0KICAgICAgaXNRdWVyeTogdHJ1ZSwNCiAgICAgIG51bWJlcjogMCwNCiAgICAgIGF1ZGl0UmVzdWx0TGlzdDogW10sDQogICAgICBzdWJtaXQ6IFtdLA0KICAgICAgc3VibWl0MjogW10sDQogICAgICBhbW1ldGVyaWRzOiBbXSwNCiAgICAgIHNob3dDaGVja01vZGVsOiBmYWxzZSwNCiAgICAgIHNob3dBbGFybU1vZGVsOiBmYWxzZSwNCiAgICAgIHNob3dKaE1vZGVsOiBmYWxzZSwNCiAgICAgIHZlcnNpb246IGluZGV4RGF0YS52ZXJzaW9uLA0KICAgICAgdmFsaXByaWNlOiB0cnVlLA0KICAgICAgZm9ybWF0QXJyYXk6IFsiIl0sDQogICAgICBmb3JtSXRlbVdpZHRoOiB3aWR0aHN0eWxlLA0KICAgICAgdGFibGVOYW1lOiAxLA0KICAgICAgZGF0ZUxpc3Q6IGRhdGVzLA0KICAgICAgY2F0ZWdvcnlzOiBbXSwgLy/mj4/ov7DnsbvlnosNCiAgICAgIHNwaW5TaG93OiBmYWxzZSwgLy/lr7zlhaXmlbDmja7pga7nvakNCiAgICAgIGZpbHRlckNvbGw6IHRydWUsIC8v5pCc57Si6Z2i5p2/5bGV5byADQogICAgICBzdGFydE1vZGFsOiBmYWxzZSwgLy/otbflp4vml7bpl7Tkv67mlLnmj5DnpLoNCiAgICAgIHFkTW9kYWw6IGZhbHNlLCAvL+i1t+W6puS/ruaUueaPkOekug0KICAgICAgZmJNb2RhbDogZmFsc2UsIC8v57+76KGo5o+Q56S6DQogICAgICBxeGZiTW9kYWw6IGZhbHNlLCAvL+WPlua2iOe/u+ihqOaPkOekug0KICAgICAgcmVhZG9ubHk6IGZhbHNlLCAvL+WwgeW5s+iwt+WPquivuw0KICAgICAgZWRpdEluZGV4OiAtMSwgLy/lvZPliY3nvJbovpHooYwNCiAgICAgIGNvbHVtbnNJbmRleDogLTEsIC8v5b2T5YmN57yW6L6R5YiXDQogICAgICBjbGFzc2lmaWNhdGlvbkRhdGE6IFtdLCAvL+eUqOeUteexu+Wei+agkQ0KICAgICAgY2xhc3NpZmljYXRpb25zOiBbXSwgLy/pgInmi6nnmoTnlKjnlLXnsbvlnovmoJENCiAgICAgIGRpcmVjdHN1cHBseWZsYWdzOiBbXSwNCiAgICAgIGVkaXRTdGFydERhdGU6ICIiLA0KICAgICAgZWRpdEVuZERhdGU6ICIiLA0KICAgICAgZWRpdFByZXZ0b3RhbHJlYWRpbmdzOiAiIiwNCiAgICAgIGVkaXRjdXJ0b3RhbHJlYWRpbmdzOiAiIiwNCiAgICAgIGVkaXR0cmFuc2Zvcm1lcnVsbGFnZTogIiIsDQogICAgICBlZGl0dGF4dGlja2V0bW9uZXk6ICIiLA0KICAgICAgZWRpdHRpY2tldG1vbmV5OiAiIiwNCiAgICAgIGVkaXR1bGxhZ2Vtb25leTogIiIsDQogICAgICBlZGl0cGVyY2VudDogIiIsDQogICAgICBlZGl0dGF4cmF0ZTogIiIsDQogICAgICBlZGl0cmVtYXJrOiAiIiwNCiAgICAgIGVkaXRwcmV2aGlnaHJlYWRpbmdzOiAwLA0KICAgICAgZWRpdHByZXZmbGF0cmVhZGluZ3M6IDAsDQogICAgICBlZGl0cHJldmxvd3JlYWRpbmdzOiAwLA0KICAgICAgZWRpdGN1cmhpZ2hyZWFkaW5nczogMCwNCiAgICAgIGVkaXRjdXJmbGF0cmVhZGluZ3M6IDAsDQogICAgICBlZGl0Y3VybG93cmVhZGluZ3M6IDAsDQogICAgICBlZGl0aGlnaHJlYWRpbmdzOiAwLA0KICAgICAgZWRpdGZsYXRyZWFkaW5nczogMCwNCiAgICAgIGVkaXRsb3dyZWFkaW5nczogMCwNCiAgICAgIHBhYnJpaWQ6ICIiLA0KICAgICAgcmVzQ2VudGVyTGlzdDogW10sDQogICAgICBDb21wYW55TGlzdDogW10sDQogICAgICBjb21wYW55TGlzdFNpemU6ICIiLA0KICAgICAgcmVzQ2VudGVyTGlzdFNpemU6ICIiLA0KICAgICAgaW5zaWRlRGF0YTogW10sIC8v5pWw5o2uDQogICAgICBteVN0eWxlOiBbXSwgLy/moLflvI8NCiAgICAgIHBhZ2VUb3RhbDogMCwNCiAgICAgIHBhZ2VOdW06IDEsDQogICAgICBwYWdlU2l6ZTogMTAsIC8v5b2T5YmN6aG1DQogICAgICBjdXJyZW50Um93OiB7fSwNCiAgICAgIG1ldGVyTW9kYWw6IGZhbHNlLA0KICAgICAgaWZNYXhkZWdyZWU6IG51bGwsDQogICAgICBhZ2FpbkpvaW5JZHM6ICIiLA0KICAgICAgZXhwb3J0OiB7DQogICAgICAgIHJ1bjogZmFsc2UsIC8v5piv5ZCm5q2j5Zyo5omn6KGM5a+85Ye6DQogICAgICAgIGRhdGE6ICIiLCAvL+WvvOWHuuaVsOaNrg0KICAgICAgICB0b3RhbFBhZ2U6IDAsIC8v5LiA5YWx5aSa5bCR6aG1DQogICAgICAgIGN1cnJlbnRQYWdlOiAwLCAvL+W9k+WJjeWkmuWwkemhtQ0KICAgICAgICBwZXJjZW50OiAwLA0KICAgICAgICBzaXplOiAxMDAwMDAwMCwNCiAgICAgIH0sDQogICAgICBBY2NvdW50cXVyOiB7IGFtbWV0ZXJpZDogIiIsIHN0YXJ0ZGF0ZTogIiIsIGVuZGRhdGU6ICIiIH0sDQogICAgICBhY2NvdW50T2JqOiB7DQogICAgICAgIGFjY291bnRubzogZGF0ZXNbMF0uY29kZSwgLy/mnJ/lj7cs6buY6K6k5b2T5YmN5pyIDQogICAgICAgIHN0YXRpb25OYW1lOiAiIiwgLy/lsYDnq5nlkI3np7ANCiAgICAgICAgcHJvamVjdG5hbWU6ICIiLCAvL+mhueebruWQjeensA0KICAgICAgICBwcmV2dG90YWxyZWFkaW5nczogbnVsbCwgLy/kuIrmnJ/mraLluqYNCiAgICAgICAgYW1tZXRlcmNvZGU6ICIiLCAvL+eUteihqOaIt+WPty/ljY/orq7nvJbnoIENCiAgICAgICAgc3RhdHVzOiAiIiwgLy/mmK/lkKbpgIDlm54NCiAgICAgICAgY29tcGFueTogIiIsDQogICAgICAgIGNvdW50cnk6ICIiLA0KICAgICAgICBlbGVjdHJvdHlwZTogIiIsIC8v55So55S157G75Z6LDQogICAgICAgIGFjY291bnRUeXBlOiAiMSIsIC8v5Y+w6LSm57G75Z6LDQogICAgICAgIHVzZXJJZDogIiIsDQogICAgICAgIHZlcnNpb246ICIiLA0KICAgICAgICBzdXBwbHlidXJlYXVhbW1ldGVyY29kZTogIiIsDQogICAgICAgIGRpcmVjdHN1cHBseWZsYWc6ICIiLA0KICAgICAgICBhbW1ldGVyaWQ6ICIiLA0KICAgICAgICBzdGFydGRhdGU6ICIiLA0KICAgICAgICBlbmRkYXRlOiAiIiwNCiAgICAgIH0sDQoNCiAgICAgIHVzZXJOYW1lOiAiIiwgLy/mn6Xor6LpgInmi6nkurrlkZjlkI3np7ANCiAgICAgIGF2ZW1vbmV5OiAwLA0KICAgICAgbm93ZGF0ZWRpZmY6IDAsDQogICAgICBhY2NvdW50VGI6IHsNCiAgICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICAgIGNvbHVtbnM6IFtdLA0KICAgICAgICBoZWFkQ29sdW1uOiBbDQogICAgICAgICAgeyB0eXBlOiAic2VsZWN0aW9uIiwgd2lkdGg6IDQwLCBhbGlnbjogImNlbnRlciIsIGZpeGVkOiAibGVmdCIgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIumhueebruWQjeensCIsDQogICAgICAgICAgICBzbG90OiAicHJvamVjdG5hbWUiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDYwLA0KICAgICAgICAgICAgZml4ZWQ6ICJsZWZ0IiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBoZWFkQ29sdW1uMjogWw0KICAgICAgICAgIHsgdHlwZTogInNlbGVjdGlvbiIsIHdpZHRoOiA0MCwgYWxpZ246ICJjZW50ZXIiLCBmaXhlZDogImxlZnQiIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLnqL3moLjnu5Pmnpzlj4rlj43ppogiLA0KICAgICAgICAgICAgZml4ZWQ6ICJsZWZ0IiwNCiAgICAgICAgICAgIGtleTogImFjdGlvbiIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICByZW5kZXI6IChoLCBwYXJhbXMpID0+IHsNCiAgICAgICAgICAgICAgdmFyIHRoYXQgPSB0aGlzOw0KICAgICAgICAgICAgICByZXR1cm4gaCgNCiAgICAgICAgICAgICAgICAiQnV0dG9uIiwNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICBwcm9wczogew0KICAgICAgICAgICAgICAgICAgICB0eXBlOiAicHJpbWFyeSIsDQogICAgICAgICAgICAgICAgICAgIHNpemU6ICJzbWFsbCIsDQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgc3R5bGU6IHsNCiAgICAgICAgICAgICAgICAgICAgImZvbnQtc2l6ZSI6ICIxMHB4IiwNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICBvbjogew0KICAgICAgICAgICAgICAgICAgICBjbGljaygpIHsNCiAgICAgICAgICAgICAgICAgICAgICB0aGF0LiRyZWZzLmNoZWtSZXN1bHRBbmRSZXNwb25zZS5wY2lkID0gcGFyYW1zLnJvdy5wY2lkOw0KICAgICAgICAgICAgICAgICAgICAgIHRoYXQuc2hvd0NoZWNrTW9kZWwgPSB0cnVlOw0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICLnqL3moLjnu5Pmnpzlj4rlj43ppogiDQogICAgICAgICAgICAgICk7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbWluV2lkdGg6IDEwMCwNCiAgICAgICAgICAgIG1heFdpZHRoOiAxNTAsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIumhueebruWQjeensCIsDQogICAgICAgICAgICBzbG90OiAicHJvamVjdG5hbWUiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDkwLA0KICAgICAgICAgICAgZml4ZWQ6ICJsZWZ0IiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICB0YWlsQ29sdW1uOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmnJ/lj7ciLA0KICAgICAgICAgICAga2V5OiAiYWNjb3VudG5vIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA2MCwNCiAgICAgICAgICAgIGZpeGVkOiAibGVmdCIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIueUqOeUteexu+WeiyIsDQogICAgICAgICAgICBrZXk6ICJlbGVjdHJvdHlwZW5hbWUiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDk0LA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLlrp7pmYXmma7npajlkKvnqI7ph5Hpop0o5YWDKSIsDQogICAgICAgICAgICBrZXk6ICJ0aWNrZXRtb25leSIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB3aWR0aDogNjAsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuWunumZheS4k+elqOWQq+eojumHkeminSjlhYMpIiwNCiAgICAgICAgICAgIGtleTogInRheHRpY2tldG1vbmV5IiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA2MCwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5oC755S16YePKOW6pikiLA0KICAgICAgICAgICAga2V5OiAidG90YWx1c2VkcmVhZGluZ3MiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDYwLA0KICAgICAgICAgIH0sDQogICAgICAgICAgeyB0aXRsZTogIuWxgOermSIsIGtleTogInN0YXRpb25OYW1lIiwgYWxpZ246ICJjZW50ZXIiLCB3aWR0aDogMTYwIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLnsbvlnovmj4/ov7AiLA0KICAgICAgICAgICAga2V5OiAiY2F0ZWdvcnluYW1lIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA5NCwNCiAgICAgICAgICAgIHJlbmRlcjogcmVuZGVyQ2F0ZWdvcnksDQogICAgICAgICAgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5YCN546HIiwga2V5OiAibWFnbmlmaWNhdGlvbiIsIGFsaWduOiAiY2VudGVyIiwgd2lkdGg6IDYwIH0sDQogICAgICAgICAgeyB0aXRsZTogIuWumuminSIsIGtleTogInF1b3RhcmVhZGluZ3MiLCBhbGlnbjogImNlbnRlciIsIHdpZHRoOiA2MCB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5rWu5Yqo5q+U77yIJe+8iSIsDQogICAgICAgICAgICBrZXk6ICJxdW90ZXJlYWRpbmdzcmF0aW8iLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDEwMCwNCiAgICAgICAgICAgIHNvcnRhYmxlOiB0cnVlLA0KICAgICAgICAgICAgc29ydE1ldGhvZDogKGEsIGIsIHR5cGUpID0+IHsNCiAgICAgICAgICAgICAgaWYgKHR5cGUgPT09ICJkZXNjIikgew0KICAgICAgICAgICAgICAgIHJldHVybiBwYXJzZUludChhKSA8IHBhcnNlSW50KGIpID8gMSA6IC0xOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHJldHVybiBwYXJzZUludChhKSA+IHBhcnNlSW50KGIpID8gMSA6IC0xOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLotbflp4vml6XmnJ8iLA0KICAgICAgICAgICAgc2xvdDogInN0YXJ0ZGF0ZSIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICBmaXhlZDogImxlZnQiLA0KICAgICAgICAgICAgd2lkdGg6IDc1LA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmiKrmraLml6XmnJ8iLA0KICAgICAgICAgICAgc2xvdDogImVuZGRhdGUiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDc1LA0KICAgICAgICAgICAgZml4ZWQ6ICJsZWZ0IiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5pys5pyf6LW35bqmIiwNCiAgICAgICAgICAgIHNsb3Q6ICJwcmV2dG90YWxyZWFkaW5ncyIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB3aWR0aDogNjAsDQogICAgICAgICAgICBmaXhlZDogImxlZnQiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmnKzmnJ/mraLluqYiLA0KICAgICAgICAgICAgc2xvdDogImN1cnRvdGFscmVhZGluZ3MiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDYwLA0KICAgICAgICAgICAgZml4ZWQ6ICJsZWZ0IiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi55S15o2fKOW6pikiLA0KICAgICAgICAgICAgc2xvdDogInRyYW5zZm9ybWVydWxsYWdlIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA1MCwNCiAgICAgICAgICAgIGZpeGVkOiAibGVmdCIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIueUqOeUtemHjyjluqYpIiwNCiAgICAgICAgICAgIHNsb3Q6ICJjdXJ1c2VkcmVhZGluZ3MiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDUwLA0KICAgICAgICAgICAgZml4ZWQ6ICJsZWZ0IiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi55S15Lu3KOWFgykiLA0KICAgICAgICAgICAgc2xvdDogInVuaXRwaXJjZSIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB3aWR0aDogNTAsDQogICAgICAgICAgICBmaXhlZDogImxlZnQiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLmma7npajlkKvnqI7ph5Hpop0o5YWDKSIsDQogICAgICAgICAgICBzbG90OiAiaW5wdXR0aWNrZXRtb25leSIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB3aWR0aDogNjAsDQogICAgICAgICAgICBmaXhlZDogImxlZnQiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLkuJPnpajlkKvnqI7ph5Hpop0o5YWDKSIsDQogICAgICAgICAgICBzbG90OiAiaW5wdXR0YXh0aWNrZXRtb25leSIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB3aWR0aDogNjAsDQogICAgICAgICAgICBmaXhlZDogImxlZnQiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLkuJPnpajnqI7njofvvIgl77yJIiwNCiAgICAgICAgICAgIHNsb3Q6ICJ0YXhyYXRlIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA2MCwNCiAgICAgICAgICAgIGZpeGVkOiAibGVmdCIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuS4k+elqOeojuminSIsDQogICAgICAgICAgICBrZXk6ICJ0YXhhbW91bnQiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDUwLA0KICAgICAgICAgICAgZml4ZWQ6ICJsZWZ0IiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5YW25LuWKOWFgykiLA0KICAgICAgICAgICAgc2xvdDogInVsbGFnZW1vbmV5IiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA2MCwNCiAgICAgICAgICAgIGZpeGVkOiAibGVmdCIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuWunue8tOi0ueeUqCjlhYMp5ZCr56iOIiwNCiAgICAgICAgICAgIGtleTogImFjY291bnRtb25leSIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB3aWR0aDogNjUsDQogICAgICAgICAgICBmaXhlZDogImxlZnQiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLlpIfms6giLA0KICAgICAgICAgICAgc2xvdDogInJlbWFyayIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB3aWR0aDogMTAwLA0KICAgICAgICAgICAgZml4ZWQ6ICJsZWZ0IiwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLliIblibLmr5TkvosoJSkiLCBhbGlnbjogImNlbnRlciIsIGtleTogInBlcmNlbnQiLCB3aWR0aDogODAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuWvueWklue7k+eul+exu+WeiyIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICBrZXk6ICJkaXJlY3RzdXBwbHlmbGFnIiwNCiAgICAgICAgICAgIHdpZHRoOiA4MCwNCiAgICAgICAgICAgIHJlbmRlcjogcmVuZGVyRGlyZWN0c3VwcGx5ZmxhZywNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBmaWxlQ29sdW1uOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLpmYTku7YiLA0KICAgICAgICAgICAgc2xvdDogImZpbGUiLA0KICAgICAgICAgICAgYWxpZ246ICJjZW50ZXIiLA0KICAgICAgICAgICAgd2lkdGg6IDUwLA0KICAgICAgICAgICAgZml4ZWQ6ICJsZWZ0IiwNCiAgICAgICAgICAgIHJlbmRlcjogcGhvdG8sDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgbG5Db2x1bW46IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuS+m+eUteWxgOeUteihqOe8luWPtyIsDQogICAgICAgICAgICBrZXk6ICJzdXBwbHlidXJlYXVhbW1ldGVyY29kZSIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB3aWR0aDogNjAsDQogICAgICAgICAgICBmaXhlZDogImxlZnQiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIHNjQ29sdW1uOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLnlLXooajmiLflj7cv5Y2P6K6u57yW56CBIiwNCiAgICAgICAgICAgIGtleTogImFtbWV0ZXJjb2RlIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA2MCwNCiAgICAgICAgICAgIGZpeGVkOiAibGVmdCIsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuS+m+eUteWxgOeUteihqOe8luWPtyIsDQogICAgICAgICAgICBrZXk6ICJzdXBwbHlidXJlYXVhbW1ldGVyY29kZSIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB3aWR0aDogNjAsDQogICAgICAgICAgICBmaXhlZDogImxlZnQiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQoNCiAgICAgICAgZGF0YTogW10sDQogICAgICAgIGV4cG9ydGNvbHVtbnM6IFsNCiAgICAgICAgICB7IHRpdGxlOiAi6ZSZ6K+v5L+h5oGvIiwga2V5OiAiZXJyb3IiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuazqOaEj+S/oeaBryIsIGtleTogImNhcmVmdWwiIH0sDQogICAgICAgICAgeyB0aXRsZTogIumhueebruWQjeensCIsIGtleTogInByb2plY3RuYW1lIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLmnJ/lj7ciLCBrZXk6ICJhY2NvdW50bm8iIH0sDQogICAgICAgICAgeyB0aXRsZTogIueUteihqC/ljY/orq5pZCIsIGtleTogImFtbWV0ZXJpZCIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5L6b55S15bGA55S16KGo57yW5Y+3Iiwga2V5OiAic3VwcGx5YnVyZWF1YW1tZXRlcmNvZGUiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5bGA56uZIiwga2V5OiAic3RhdGlvbk5hbWUiLCBhbGlnbjogImNlbnRlciIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi6LW35aeL5pel5pyfKOW/heWhqykiLCBrZXk6ICJzdGFydGRhdGUiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuaIquatouaXpeacnyjlv4XloaspIiwga2V5OiAiZW5kZGF0ZSIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5pys5pyf5bOw5q616LW35bqmKOWzsOW5s+iwt+W/heWhqykiLCBrZXk6ICJwcmV2aGlnaHJlYWRpbmdzIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLmnKzmnJ/lubPmrrXotbfluqYo5bOw5bmz6LC35b+F5aGrKSIsIGtleTogInByZXZmbGF0cmVhZGluZ3MiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuacrOacn+iwt+autei1t+W6pijls7DlubPosLflv4XloaspIiwga2V5OiAicHJldmxvd3JlYWRpbmdzIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLmnKzmnJ/otbfluqYo5pmu6YCa55S16KGo5b+F5aGrKSIsIGtleTogInByZXZ0b3RhbHJlYWRpbmdzIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLmnKzmnJ/ls7DmrrXmraLluqYo5bOw5bmz6LC35b+F5aGrKSIsIGtleTogImN1cmhpZ2hyZWFkaW5ncyIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5pys5pyf5bmz5q615q2i5bqmKOWzsOW5s+iwt+W/heWhqykiLCBrZXk6ICJjdXJmbGF0cmVhZGluZ3MiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuacrOacn+iwt+auteatouW6pijls7DlubPosLflv4XloaspIiwga2V5OiAiY3VybG93cmVhZGluZ3MiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuacrOacn+atouW6pijmma7pgJrnlLXooajlv4XloaspIiwga2V5OiAiY3VydG90YWxyZWFkaW5ncyIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi55S15o2fKOW6piko5Y+v5aGrKSIsIGtleTogInRyYW5zZm9ybWVydWxsYWdlIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLkuJPnpajlkKvnqI7ph5Hpop0o5YWDKSjlv4XloaspIiwga2V5OiAiaW5wdXR0YXh0aWNrZXRtb25leSIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5LiT56Wo56iO546H77yIJe+8iSjlv4XloaspIiwga2V5OiAidGF4cmF0ZSIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5LiT56Wo56iO6aKdIiwga2V5OiAidGF4YW1vdW50IiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLmma7npajlkKvnqI7ph5Hpop0o5YWDKSjlv4XloaspIiwga2V5OiAiaW5wdXR0aWNrZXRtb25leSIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5YW25LuWKOWFgyko5Y+v5aGrKSIsIGtleTogInVsbGFnZW1vbmV5IiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLlrp7nvLTotLnnlKgo5YWDKeWQq+eojiIsIGtleTogImFjY291bnRtb25leSIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi57G75Z6L5o+P6L+wIiwga2V5OiAiY2F0ZWdvcnluYW1lIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLlgI3njociLCBrZXk6ICJtYWduaWZpY2F0aW9uIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLlrprpop0iLCBrZXk6ICJxdW90YXJlYWRpbmdzIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLmta7liqjmr5TvvIgl77yJIiwga2V5OiAicXVvdGVyZWFkaW5nc3JhdGlvIiB9LA0KICAgICAgICAgIHsgdGl0bGU6ICLnlKjnlLXph48o5bqmKSIsIGtleTogImN1cnVzZWRyZWFkaW5ncyIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5oC755S16YePKOW6pikiLCBrZXk6ICJ0b3RhbHVzZWRyZWFkaW5ncyIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi55S15Lu3KOWFgykiLCBrZXk6ICJ1bml0cGlyY2UiIH0sDQogICAgICAgICAgeyB0aXRsZTogIuWkh+azqCIsIGtleTogInJlbWFyayIgfSwNCiAgICAgICAgICB7IHRpdGxlOiAi5YiG5Ymy5q+U5L6LKCUpIiwga2V5OiAicGVyY2VudCIgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgfTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGJ1dHRvbmxvYWQyKHYpIHsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwuYnV0dG9ubG9hZDIgPSB2Ow0KICAgIH0sDQogICAgaXNCdXR0b25sb2FkKHYpIHsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwuYnV0dG9ubG9hZCA9IHY7DQogICAgfSwNCiAgICBpc1Nob3dzKHQpIHsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwuc2hvdyA9IHQ7DQogICAgICBpZiAodGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5zaG93ID09IGZhbHNlKSB7DQogICAgICAgIHRoaXMubnVtYmVyMisrOw0KICAgICAgICB0aGlzLmlzVCA9IHQ7DQogICAgICB9IGVsc2UgaWYgKHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwuc2hvdyA9PSB0cnVlKSB7DQogICAgICAgIHRoaXMubnVtYmVyMiA9IDA7DQogICAgICAgIHRoaXMuaXNUID0gIXQ7DQogICAgICB9DQogICAgICBpZiAodGhpcy5pc1QgJiYgdGhpcy5udW1iZXIyIDwgMTApIHsNCiAgICAgICAgdGhpcy5pc1Nob3dzKHQpOw0KICAgICAgfQ0KICAgIH0sDQogICAgbmV4dENoZWNrKCkgew0KICAgICAgdGhpcy5zaG93QWxhcm1Nb2RlbCA9IHRydWU7DQogICAgICB0aGlzLmlzU2hvd3ModHJ1ZSk7DQogICAgICAvLyB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnNob3c9dHJ1ZQ0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGEgPSBbXTsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwucmVzdWx0TGlzdCA9IFtdOw0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGExID0gW107DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnJlc3VsdExpc3QxID0gW107DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnRhYmxlRGF0YTIgPSBbXTsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwucmVzdWx0TGlzdDIgPSBbXTsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwudGFibGVEYXRhMyA9IFtdOw0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5yZXN1bHRMaXN0MyA9IFtdOw0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGE0ID0gW107DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnJlc3VsdExpc3Q0ID0gW107DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnRhYmxlRGF0YTUgPSBbXTsNCiAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwucmVzdWx0TGlzdDUgPSBbXTsNCiAgICAgIC8vIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgdGhpcy5zaG93SmhNb2RlbCA9IGZhbHNlOw0KICAgICAgLy8gdGhpcy5zaG93QWxhcm1Nb2RlbD10cnVlOw0KICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5hY3RpdmVCdXR0b24gPSA2Ow0KICAgICAgLy8gfSwxMDApDQogICAgfSwNCiAgICBhbGFybUNsb3NlKCkgew0KICAgICAgLy8gd2luZG93Lmhpc3RvcnkuZ28oMCk7DQogICAgICB0aGlzLnNob3dBbGFybU1vZGVsID0gZmFsc2U7DQogICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnNob3cgPSBmYWxzZTsNCiAgICB9LA0KICAgIGNoZWNrQ2FuY2VsKCkgew0KICAgICAgdGhpcy5zaG93SmhNb2RlbCA9IGZhbHNlOw0KICAgIH0sDQogICAgYWxhcm1DaGVjaygpIHt9LA0KICAgIHNlbGVjdENoYW5nZSgpIHsNCiAgICAgIGdldERlcGFydG1lbnRzKHRoaXMuYWNjb3VudE9iai5jb21wYW55KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5yZXNDZW50ZXJMaXN0ID0gcmVzLmRhdGE7DQogICAgICAgIHRoaXMucmVzQ2VudGVyTGlzdFNpemUgPSByZXMuZGF0YS5sZW5ndGg7DQogICAgICAgIHRoaXMuYWNjb3VudE9iai5jb3VudHJ5ID0gcmVzLmRhdGFbMF0uaWQ7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOiuoeeul+WumuminQ0KICAgIGdldFF1b3RhOiAoYW1tZXRlcmlkLCBzdGFydGRhdGUsIGVuZGRhdGUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBpZiAoYW1tZXRlcmlkICYmIHN0YXJ0ZGF0ZSAmJiBlbmRkYXRlKSB7DQogICAgICAgIGdldEVsZWN0clF1b3RhKGFtbWV0ZXJpZCwgc3RhcnRkYXRlLCBlbmRkYXRlKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBpZiAoY2FsbGJhY2spIGNhbGxiYWNrKHJlcyk7DQogICAgICAgICAgZWxzZSBjYWxsYmFjaygpOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8v5Yig6Zmk5pe25qOA5p+l6YCA5Zue5Y+w6LSm5piv5ZCm6Kej6Zmk5LiO5b2S6ZuG5Y2V5YWz6IGUDQogICAgZ2V0VGVtOiAocGNpZCwgY2FsbGJhY2spID0+IHsNCiAgICAgIHNlbGVjdEJ5UGNpZChwY2lkKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKGNhbGxiYWNrKSBjYWxsYmFjayhyZXMpOw0KICAgICAgICBlbHNlIGNhbGxiYWNrKCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8v57+76aG15pe25YWI56Gu6K6k5pWw5o2u5piv5ZCm5L+d5a2YDQogICAgaGFuZGxlUGFnZSh2YWx1ZSkgew0KICAgICAgbGV0IGIgPSBmYWxzZTsNCiAgICAgIGxldCBkYXRhID0gdGhpcy5pbnNpZGVEYXRhOw0KICAgICAgbGV0IGFycmF5ID0gW107DQogICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0sIGluZGV4LCBpbnB1dCkgew0KICAgICAgICBpZiAoaXRlbS5lZGl0VHlwZSA9PSAxKSB7DQogICAgICAgICAgYiA9IHRydWU7DQogICAgICAgICAgYXJyYXkucHVzaChpdGVtKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICBpZiAoYikgew0KICAgICAgICB0aGlzLiRNb2RhbC5jb25maXJtKHsNCiAgICAgICAgICB0aXRsZTogIuaPkOekuiIsDQogICAgICAgICAgY29udGVudDogIjxwPuaCqOacieW3sue8lui+keS/oeaBr+i/mOayoeacieS/neWtmO+8jOaYr+WQpuS/neWtmO+8nzwvcD4iLA0KICAgICAgICAgIG9uT2s6ICgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuc3VibWl0RGF0YShhcnJheSk7DQogICAgICAgICAgfSwNCiAgICAgICAgICBvbkNhbmNlbDogKCkgPT4ge30sDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgdGhpcy5wYWdlTnVtID0gdmFsdWU7DQogICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgIH0sDQogICAgLy/mlLnlj5jooajmoLzlj6/mmL7npLrmlbDmja7mlbDph4/ml7blhYjnoa7orqTmlbDmja7mmK/lkKbkv53lrZgNCiAgICBoYW5kbGVQYWdlU2l6ZSh2YWx1ZSkgew0KICAgICAgbGV0IGIgPSBmYWxzZTsNCiAgICAgIGxldCBkYXRhID0gdGhpcy5pbnNpZGVEYXRhOw0KICAgICAgbGV0IGFycmF5ID0gW107DQogICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0sIGluZGV4LCBpbnB1dCkgew0KICAgICAgICBpZiAoaXRlbS5lZGl0VHlwZSA9PSAxKSB7DQogICAgICAgICAgYiA9IHRydWU7DQogICAgICAgICAgYXJyYXkucHVzaChpdGVtKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICBpZiAoYikgew0KICAgICAgICB0aGlzLiRNb2RhbC5jb25maXJtKHsNCiAgICAgICAgICB0aXRsZTogIuaPkOekuiIsDQogICAgICAgICAgY29udGVudDogIjxwPuaCqOacieW3sue8lui+keS/oeaBr+i/mOayoeacieS/neWtmO+8jOaYr+WQpuS/neWtmO+8nzwvcD4iLA0KICAgICAgICAgIG9uT2s6ICgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuc3VibWl0RGF0YShhcnJheSk7DQogICAgICAgICAgfSwNCiAgICAgICAgICBvbkNhbmNlbDogKCkgPT4ge30sDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgdGhpcy5wYWdlU2l6ZSA9IHZhbHVlOw0KICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICB9LA0KICAgIC8v5ZCR5ZCO5Y+w6K+35rGC5pWw5o2uDQogICAgZ2V0QWNjb3VudE1lc3NhZ2VzKCkgew0KICAgICAgdGhpcy5zZXRFbGVjdHJveVR5cGUoKTsNCiAgICAgIGxldCBwYXJhbXMgPSB0aGlzLmFjY291bnRPYmo7DQogICAgICBwYXJhbXMucGFnZU51bSA9IHRoaXMucGFnZU51bTsNCiAgICAgIHBhcmFtcy5wYWdlU2l6ZSA9IHRoaXMucGFnZVNpemU7DQogICAgICBsZXQgcmVxID0gew0KICAgICAgICB1cmw6ICIvYnVzaW5lc3MvYWNjb3VudC9zZWxmQWNjb3VudExpc3QiLA0KICAgICAgICBtZXRob2Q6ICJnZXQiLA0KICAgICAgICBwYXJhbXM6IHBhcmFtcywNCiAgICAgIH07DQogICAgICBsZXQgYXJyYXkgPSBbXTsNCiAgICAgIHRoaXMuYWNjb3VudFRiLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgYXhpb3MNCiAgICAgICAgLnJlcXVlc3QocmVxKQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgdGhpcy5hY2NvdW50VGIubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIGlmIChyZXMuZGF0YSkgew0KICAgICAgICAgICAgYXJyYXkgPSByZXMuZGF0YS5yb3dzOw0KICAgICAgICAgICAgYXJyYXkucHVzaCh0aGlzLnN1bnRvdGFsKGFycmF5KSk7IC8v5bCP6K6hDQogICAgICAgICAgICBhY2NvdW50VG90YWwodGhpcy5hY2NvdW50T2JqKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgLy/lkIjorqENCiAgICAgICAgICAgICAgbGV0IGFsbHRvdGFsID0gcmVzLmRhdGE7DQogICAgICAgICAgICAgIGFsbHRvdGFsLnRvdGFsID0gIuWQiOiuoSI7DQogICAgICAgICAgICAgIGFsbHRvdGFsLnByb2plY3RuYW1lID0gIuWQiOiuoSI7DQogICAgICAgICAgICAgIGFsbHRvdGFsLl9kaXNhYmxlZCA9IHRydWU7DQogICAgICAgICAgICAgIGFycmF5LnB1c2goYWxsdG90YWwpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB0aGlzLmluc2lkZURhdGEgPSBhcnJheTsNCiAgICAgICAgICAgIHRoaXMucGFnZVRvdGFsID0gcmVzLmRhdGEudG90YWwgfHwgMDsNCiAgICAgICAgICAgIC8vIGRlYnVnZ2VyDQogICAgICAgICAgICB0aGlzLnNldE5ld0ZpZWxkKHJlcy5kYXRhLnJvd3MpOw0KICAgICAgICAgICAgdGhpcy5zZXRNeVN0eWxlKHJlcy5kYXRhLnJvd3MubGVuZ3RoKTsNCiAgICAgICAgICAgIHRoaXMuZWRpdEluZGV4ID0gLTE7DQogICAgICAgICAgICB0aGlzLmNvbHVtbnNJbmRleCA9IC0xOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnIpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmxvZyhlcnIpOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8v5bCP6K6hDQogICAgc3VudG90YWwoYXJyYXkpIHsNCiAgICAgIGxldCBjdXJ1c2VkcmVhZGluZ3MgPSAwOw0KICAgICAgbGV0IHRyYW5zZm9ybWVydWxsYWdlID0gMDsNCiAgICAgIGxldCB0aWNrZXRtb25leSA9IDA7DQogICAgICBsZXQgdGF4dGlja2V0bW9uZXkgPSAwOw0KICAgICAgbGV0IHRheGFtb3VudCA9IDA7DQogICAgICBsZXQgdWxsYWdlbW9uZXkgPSAwOw0KICAgICAgbGV0IGFjY291bnRtb25leSA9IDA7DQogICAgICBsZXQgaW5wdXR0YXh0aWNrZXRtb25leSA9IDA7DQogICAgICBsZXQgaW5wdXR0aWNrZXRtb25leSA9IDA7DQogICAgICBhcnJheS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgIGlmIChpdGVtLmVmZmVjdGl2ZSA9PT0gMSkgew0KICAgICAgICAgIGN1cnVzZWRyZWFkaW5ncyArPSBpdGVtLmN1cnVzZWRyZWFkaW5nczsNCiAgICAgICAgICB0cmFuc2Zvcm1lcnVsbGFnZSArPSBpdGVtLnRyYW5zZm9ybWVydWxsYWdlOw0KICAgICAgICAgIHRpY2tldG1vbmV5ICs9IGl0ZW0udGlja2V0bW9uZXk7DQogICAgICAgICAgdGF4dGlja2V0bW9uZXkgKz0gaXRlbS50YXh0aWNrZXRtb25leTsNCiAgICAgICAgICB0YXhhbW91bnQgKz0gaXRlbS50YXhhbW91bnQ7DQogICAgICAgICAgaW5wdXR0YXh0aWNrZXRtb25leSArPSBpdGVtLmlucHV0dGF4dGlja2V0bW9uZXk7DQogICAgICAgICAgaW5wdXR0aWNrZXRtb25leSArPSBpdGVtLmlucHV0dGlja2V0bW9uZXk7DQogICAgICAgICAgdWxsYWdlbW9uZXkgKz0gaXRlbS51bGxhZ2Vtb25leTsNCiAgICAgICAgICBhY2NvdW50bW9uZXkgKz0gaXRlbS5hY2NvdW50bW9uZXk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgY3VydXNlZHJlYWRpbmdzOiBjdXJ1c2VkcmVhZGluZ3MsDQogICAgICAgIHRyYW5zZm9ybWVydWxsYWdlOiB0cmFuc2Zvcm1lcnVsbGFnZSwNCiAgICAgICAgdGlja2V0bW9uZXk6IHRpY2tldG1vbmV5LnRvRml4ZWQoMiksDQogICAgICAgIHRheHRpY2tldG1vbmV5OiB0YXh0aWNrZXRtb25leS50b0ZpeGVkKDIpLA0KICAgICAgICB0YXhhbW91bnQ6IHRheGFtb3VudC50b0ZpeGVkKDIpLA0KICAgICAgICBpbnB1dHRheHRpY2tldG1vbmV5OiBpbnB1dHRheHRpY2tldG1vbmV5LnRvRml4ZWQoMiksDQogICAgICAgIGlucHV0dGlja2V0bW9uZXk6IGlucHV0dGlja2V0bW9uZXkudG9GaXhlZCgyKSwNCiAgICAgICAgdWxsYWdlbW9uZXk6IHVsbGFnZW1vbmV5LnRvRml4ZWQoMiksDQogICAgICAgIGFjY291bnRtb25leTogYWNjb3VudG1vbmV5LnRvRml4ZWQoMiksDQogICAgICAgIHRvdGFsOiAi5bCP6K6hIiwNCiAgICAgICAgcHJvamVjdG5hbWU6ICLlsI/orqEiLA0KICAgICAgICBfZGlzYWJsZWQ6IHRydWUsDQogICAgICB9Ow0KICAgIH0sDQogICAgc2VhcmNoTGlzdCgpIHsNCiAgICAgIHRoaXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgIH0sDQogICAgb25SZXNldEhhbmRsZSgpIHsNCiAgICAgIHRoaXMuYWNjb3VudE9iaiA9IHsNCiAgICAgICAgYWNjb3VudG5vOiBkYXRlc1swXS5jb2RlLCAvL+acn+WPtyzpu5jorqTlvZPliY3mnIgNCiAgICAgICAgc3Vic3RhdGlvbjogIiIsIC8v5pSv5bGADQogICAgICAgIHByb2plY3RuYW1lOiAiIiwgLy/pobnnm67lkI3np7ANCiAgICAgICAgcHJldnRvdGFscmVhZGluZ3M6IG51bGwsIC8v5LiK5pyf5q2i5bqmDQogICAgICAgIGFtbWV0ZXJjb2RlOiAiIiwgLy/nlLXooajmiLflj7cv5Y2P6K6u57yW56CBDQogICAgICAgIGlzcmV0dXJuOiAiIiwgLy/mmK/lkKbpgIDlm54NCiAgICAgICAgY29tcGFueTogdGhpcy5Db21wYW55TGlzdFswXS5pZCwNCiAgICAgICAgdXNlcklkOiAiIiwNCiAgICAgICAgYWNjb3VudFR5cGU6ICIxIiwgLy/lj7DotKbnsbvlnosNCiAgICAgICAgc3VwcGx5YnVyZWF1YW1tZXRlcmNvZGU6ICIiLA0KICAgICAgfTsNCg0KICAgICAgdGhpcy5hY2NvdW50T2JqLnZlcnNpb24gPSBpbmRleERhdGEudmVyc2lvbjsNCiAgICAgIHRoaXMudXNlck5hbWUgPSAiIjsNCiAgICAgIHRoaXMuY2xhc3NpZmljYXRpb25zID0gW107DQogICAgICB0aGlzLnNlbGVjdENoYW5nZSgpOw0KICAgICAgdGhpcy5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICB9LA0KICAgIC8v5L+d5a2Y5Y+v57yW6L6R6KGo5qC855qE5Yid5aeL5YyW5pWw5o2uDQogICAgc2V0TmV3RmllbGQoZGF0YSkgew0KICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgIGl0ZW0ub2xkX3N0YXJ0ZGF0ZSA9IGl0ZW0uc3RhcnRkYXRlOw0KICAgICAgICBpdGVtLm9sZF9wcmV2dG90YWxyZWFkaW5ncyA9IGl0ZW0ucHJldnRvdGFscmVhZGluZ3M7DQogICAgICAgIGl0ZW0ubXVsdHRpbWVzID0gaXRlbS5tYWduaWZpY2F0aW9uOw0KICAgICAgICBpdGVtLm9sZF9lbmRkYXRlID0gaXRlbS5lbmRkYXRlOw0KICAgICAgICBpdGVtLm9sZF9jdXJ0b3RhbHJlYWRpbmdzID0gaXRlbS5jdXJ0b3RhbHJlYWRpbmdzOw0KICAgICAgICBpdGVtLm9sZF90cmFuc2Zvcm1lcnVsbGFnZSA9IGl0ZW0udHJhbnNmb3JtZXJ1bGxhZ2U7DQogICAgICAgIGl0ZW0ub2xkX3RheHRpY2tldG1vbmV5ID0gaXRlbS5pbnB1dHRheHRpY2tldG1vbmV5Ow0KICAgICAgICBpdGVtLm9sZF90aWNrZXRtb25leSA9IGl0ZW0uaW5wdXR0aWNrZXRtb25leTsNCiAgICAgICAgaXRlbS5vbGRfdWxsYWdlbW9uZXkgPSBpdGVtLnVsbGFnZW1vbmV5Ow0KICAgICAgICBpdGVtLm9sZF9wcmV2aGlnaHJlYWRpbmdzID0gaXRlbS5wcmV2aGlnaHJlYWRpbmdzOw0KICAgICAgICBpdGVtLm9sZF9wcmV2ZmxhdHJlYWRpbmdzID0gaXRlbS5wcmV2ZmxhdHJlYWRpbmdzOw0KICAgICAgICBpdGVtLm9sZF9wcmV2bG93cmVhZGluZ3MgPSBpdGVtLnByZXZsb3dyZWFkaW5nczsNCg0KICAgICAgICBpdGVtLm9sZF9jdXJoaWdocmVhZGluZ3MgPSBpdGVtLmN1cmhpZ2hyZWFkaW5nczsNCiAgICAgICAgaXRlbS5vbGRfY3VyZmxhdHJlYWRpbmdzID0gaXRlbS5jdXJmbGF0cmVhZGluZ3M7DQogICAgICAgIGl0ZW0ub2xkX2N1cmxvd3JlYWRpbmdzID0gaXRlbS5jdXJsb3dyZWFkaW5nczsNCiAgICAgICAgaXRlbS5vbGRfY3VydG90YWxyZWFkaW5ncyA9IGl0ZW0uY3VydG90YWxyZWFkaW5nczsNCg0KICAgICAgICBpdGVtLnZlcnNpb24gPSBpbmRleERhdGEudmVyc2lvbjsNCiAgICAgICAgaXRlbS5lZGl0VHlwZSA9IDA7DQogICAgICAgIGl0ZW0uaXNGUEcgPSBqdWRnaW5nX2VkaXRhYmlsaXR5MShpdGVtKTsNCiAgICAgICAgaXRlbS5pc1dCID0ganVkZ2luZ19lZGl0YWJpbGl0eShpdGVtKTsNCiAgICAgICAgaWYgKCFpdGVtLnJlbWFyaykgaXRlbS5yZW1hcmsgPSAiIjsNCiAgICAgICAgaWYgKCFpdGVtLmJ6KSBpdGVtLmJ6ID0gIiI7DQogICAgICAgIGl0ZW0udHJhbnNmb3JtZXJ1bGxhZ2UgPSBqdWRnZU51bWJlcihpdGVtLnRyYW5zZm9ybWVydWxsYWdlKTsNCiAgICAgICAgLy8gaXRlbS5zdXBwbHlidXJlYXVhbW1ldGVyY29kZSA9IGp1ZGdlTnVtYmVyKGl0ZW0uc3VwcGx5YnVyZWF1YW1tZXRlcmNvZGUpOw0KICAgICAgICBpdGVtLmlucHV0dGF4dGlja2V0bW9uZXkgPSBqdWRnZU51bWJlcihpdGVtLmlucHV0dGF4dGlja2V0bW9uZXkpOw0KICAgICAgICBpdGVtLmlucHV0dGlja2V0bW9uZXkgPSBqdWRnZU51bWJlcihpdGVtLmlucHV0dGlja2V0bW9uZXkpOw0KICAgICAgICBpdGVtLnRheHRpY2tldG1vbmV5ID0ganVkZ2VOdW1iZXIoaXRlbS50YXh0aWNrZXRtb25leSk7DQogICAgICAgIGl0ZW0udGlja2V0bW9uZXkgPSBqdWRnZU51bWJlcihpdGVtLnRpY2tldG1vbmV5KTsNCiAgICAgICAgaXRlbS51bGxhZ2Vtb25leSA9IGp1ZGdlTnVtYmVyKGl0ZW0udWxsYWdlbW9uZXkpOw0KICAgICAgICBpdGVtLmN1cnVzZWRyZWFkaW5ncyA9IGp1ZGdlTnVtYmVyKGl0ZW0uY3VydXNlZHJlYWRpbmdzKTsNCiAgICAgICAgaXRlbS5hY2NvdW50bW9uZXkgPSBqdWRnZU51bWJlcihpdGVtLmFjY291bnRtb25leSk7DQogICAgICAgIGlmICgoaXRlbS50YXhyYXRlID09IG51bGwgfHwgaXRlbS50YXhyYXRlID09IDApICYmIGl0ZW0udG90YWwgPT0gbnVsbCkgew0KICAgICAgICAgIGl0ZW0udGF4cmF0ZSA9ICIxMyI7DQogICAgICAgIH0NCiAgICAgICAgaWYgKGl0ZW0udGF4cmF0ZSAmJiBpdGVtLnRheGFtb3VudCA9PSBudWxsKSB7DQogICAgICAgICAgaXRlbS50YXhhbW91bnQgPSBjb3VudFRheGFtb3VudChpdGVtKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvL+iuoeeulyDnlKjnlLXph48s5oC755S16YePLOWNleS7tyzmgLvotLnnlKgs5rWu5Yqo5q+ULg0KICAgIGNhbGN1bGF0ZUFsbChyb3cpIHsNCiAgICAgIGNvbnNvbGUubG9nKHJvdywgInJvdyIpOw0KICAgICAgcm93LmN1cnVzZWRyZWFkaW5ncyA9IF9jYWxjdWxhdGVVc2VkUmVhZGluZ3Mocm93KTsNCiAgICAgIHJvdy50b3RhbHVzZWRyZWFkaW5ncyA9IF9jYWxjdWxhdGVUb3RhbFJlYWRpbmdzKHJvdyk7DQogICAgICBpZiAocm93LmlzY2hhbmdlYW1tZXRlciA9PSAxICYmIHJvdy5pc25ldyA9PSAxKSB7DQogICAgICAgIGlmIChyb3cub2xkYmlsbHBvd2VyID4gMCkgew0KICAgICAgICAgIGxldCB0b3RhbCA9IE1hdGguYWJzKHJvdy50b3RhbHVzZWRyZWFkaW5ncykgKyBNYXRoLmFicyhyb3cub2xkYmlsbHBvd2VyKTsNCiAgICAgICAgICBsZXQgY2F0ZWdvcnkgPSByb3cuY2F0ZWdvcnk7DQogICAgICAgICAgbGV0IGFtbWV0ZXJ1c2UgPSByb3cuYW1tZXRlcnVzZTsgLy/nlLXooajnlKjpgJQNCiAgICAgICAgICBpZiAoanVkZ2VfbmVnYXRlKGNhdGVnb3J5KSB8fCBqdWRnZV9yZWNvdmVyeShhbW1ldGVydXNlKSkgew0KICAgICAgICAgICAgdG90YWwgPSAtdG90YWw7DQogICAgICAgICAgfQ0KICAgICAgICAgIHJvdy50b3RhbHVzZWRyZWFkaW5ncyA9IHRvdGFsOw0KICAgICAgICB9DQogICAgICAgIGxldCByZW1hcmsgPSByb3cucmVtYXJrOw0KICAgICAgICBpZiAocmVtYXJrLmluZGV4T2YoIuaNouihqCIpID09IC0xKSB7DQogICAgICAgICAgcm93LnJlbWFyayArPSAi5o2i6KGo77yM57uT5riF5Y6f55S16KGo6K+75pWw44CQIiArIHJvdy5vbGRiaWxscG93ZXIgKyAi44CR77ybIjsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKHJvdy50aWNrZXRtb25leSB8fCByb3cudGF4dGlja2V0bW9uZXkpIHsNCiAgICAgICAgcm93LmFjY291bnRtb25leSA9IF9jYWxjdWxhdGVBY2NvdW50TW9uZXkocm93KTsNCiAgICAgICAgcm93LnVuaXRwaXJjZSA9IF9jYWxjdWxhdGVVbml0UHJpY2VCeVVzZWRNb25leShyb3cpOw0KICAgICAgfQ0KICAgICAgcm93LnF1b3RlcmVhZGluZ3NyYXRpbyA9IF9jYWxjdWxhdGVRdW90ZXJlYWRpbmdzcmF0aW8ocm93KTsNCiAgICB9LA0KICAgIC8v5pqC5a2YDQogICAgdGVtcG9yYXJ5U3RvcmFnZSgpIHsNCiAgICAgIGxldCBhcnJheSA9IFtdOw0KICAgICAgdGhpcy5pbnNpZGVEYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgaWYgKGl0ZW0uZWRpdFR5cGUgPT0gMSkgew0KICAgICAgICAgIGFycmF5LnB1c2goaXRlbSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgbGV0IGRhdGEgPSBhcnJheTsNCiAgICAgIC8vLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0NCiAgICAgIGlmIChkYXRhICE9IG51bGwgJiYgZGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgIGxldCBudW1iZXIgPSAwOw0KICAgICAgICBsZXQgc3VibWl0RGF0YSA9IFtdOw0KICAgICAgICBsZXQgbm8gPSB0aGlzLmFjY291bnRPYmouYWNjb3VudG5vOw0KICAgICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgIGxldCB5eXl5bW1kZCA9IGN1dERhdGVfeXl5eW1tZGQoaXRlbS5zdGFydGRhdGUpOw0KICAgICAgICAgIGl0ZW0uc3RhcnR5ZWFyID0geXl5eW1tZGQueXl5eTsNCiAgICAgICAgICBpdGVtLnN0YXJ0bW9udGggPSB5eXl5bW1kZC5tbTsNCiAgICAgICAgICB5eXl5bW1kZCA9IGN1dERhdGVfeXl5eW1tZGQoaXRlbS5lbmRkYXRlKTsNCiAgICAgICAgICBpdGVtLmVuZHllYXIgPSB5eXl5bW1kZC55eXl5Ow0KICAgICAgICAgIGl0ZW0uZW5kbW9udGggPSB5eXl5bW1kZC5tbTsNCiAgICAgICAgICBpdGVtLmFjY291bnRubyA9IG5vOw0KICAgICAgICAgIHN1Ym1pdERhdGEucHVzaChpdGVtKTsNCiAgICAgICAgICBudW1iZXIrKzsNCiAgICAgICAgfSk7DQogICAgICAgIGlmIChzdWJtaXREYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0ZW1wb3JhcnlTdG9yYWdlKHN1Ym1pdERhdGEpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgaWYgKHJlcy5kYXRhLm51bSA+IDApIHsNCiAgICAgICAgICAgICAgdGhhdC4kTWVzc2FnZS5pbmZvKHsNCiAgICAgICAgICAgICAgICBjb250ZW50OiAi5oiQ5Yqf5pqC5a2YIiArIHJlcy5kYXRhLm51bSArICLmnaHmlbDmja4iLA0KICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxMCwNCiAgICAgICAgICAgICAgICBjbG9zYWJsZTogdHJ1ZSwNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGF0LnBhZ2VOdW0gPSAxOw0KICAgICAgICAgICAgdGhhdC5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy/lm5vlt53og73ogJfnqL3moLjmtYHnqIsNCiAgICBwcmVzZXJ2ZVNjKCkgew0KICAgICAgbGV0IGFyciA9IFtdOw0KICAgICAgdGhpcy5hbW1ldGVyaWRzLmZvckVhY2goKGl0ZW0xKSA9PiB7DQogICAgICAgIGlmIChhcnIuaW5kZXhPZihpdGVtMSkgPT0gLTEpIHsNCiAgICAgICAgICBhcnIucHVzaChpdGVtMSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgdGhpcy4kcmVmcy5jaGVja1Jlc3VsdC5hbW1ldGVyaWRzID0gYXJyOw0KICAgICAgdGhpcy5zaG93SmhNb2RlbCA9IHRydWU7DQogICAgfSwNCiAgICBhc3luYyBwcmVzZXJ2ZSgpIHsNCiAgICAgIGxldCBkYXRhTCA9IHRoaXMuJHJlZnMuYWNjb3VudFRhYmxlLmdldFNlbGVjdGlvbigpOw0KDQogICAgICBsZXQgYiA9IGZhbHNlOw0KICAgICAgbGV0IGRhdGEgPSB0aGlzLmluc2lkZURhdGE7DQogICAgICBsZXQgYXJyYXkgPSBbXTsNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCg0KICAgICAgbGV0IHZlcnNpb24gPSBpbmRleERhdGEudmVyc2lvbjsNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGF0YUwubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgaWYgKGRhdGFMW2ldLmVkaXRUeXBlID09IDEpIHsNCiAgICAgICAgICBpZiAoInNjIiA9PSB2ZXJzaW9uICYmIGRhdGFMW2ldLmVsZWN0cm90eXBlICYmIGRhdGFMW2ldLmVsZWN0cm90eXBlID4gMTQwMCkgew0KICAgICAgICAgICAgaWYgKA0KICAgICAgICAgICAgICBkYXRhTFtpXS5zdGF0aW9uY29kZTVnciA9PSBudWxsIHx8DQogICAgICAgICAgICAgIGRhdGFMW2ldLnN0YXRpb25jb2RlNWdyID09IHVuZGVmaW5lZCB8fA0KICAgICAgICAgICAgICBkYXRhTFtpXS5zdGF0aW9uY29kZTVnciA9PSAiIg0KICAgICAgICAgICAgKSB7DQogICAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKA0KICAgICAgICAgICAgICAgICLnlLXooagv5Y2P6K6u57yW5Y+344CQIiArDQogICAgICAgICAgICAgICAgICBkYXRhTFtpXS5hbW1ldGVyY29kZSArDQogICAgICAgICAgICAgICAgICAi44CR77yM6aG555uu5ZCN56ew44CQIiArDQogICAgICAgICAgICAgICAgICBkYXRhTFtpXS5wcm9qZWN0bmFtZSArDQogICAgICAgICAgICAgICAgICAi44CR5YWz6IGU5bGA56uZ55qENUdS56uZ5Z2A5Li656m677yM6K+35a6M5ZaE5bGA56uZ5L+h5oGv77yM5oiW6ICFNUdS5pyJ5pWI5oCn5riF5Y2V5aSx5pWI77yM6K+36IGU57O75peg57q/566h55CG5ZGY44CCIg0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICAvL+aIquatouaXpeacn+agoemqjA0KICAgICAgICAgIGxldCByZXN1bHQgPSBhd2FpdCB0aGlzLmhhbmRsZUVuZERhdGUoZGF0YUxbaV0sIGRhdGFMW2ldLmVuZGRhdGUpOw0KICAgICAgICAgIGlmIChyZXN1bHQpIHsNCiAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKHJlc3VsdCk7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KICAgICAgICAgIGxldCBtYXhkZWdyZWUgPSBwYXJzZUludChkYXRhTFtpXS5tYXhkZWdyZWUpOyAvL+e/u+ihqOW6puaVsCzljbPnlLXooajnmoTmnIDlpKfluqbmlbANCiAgICAgICAgICBpZiAobWF4ZGVncmVlICE9IG51bGwgJiYgbWF4ZGVncmVlID4gMCkgew0KICAgICAgICAgICAgaWYgKGRhdGFMW2ldLmN1cnRvdGFscmVhZGluZ3MgPiBtYXhkZWdyZWUpIHsNCiAgICAgICAgICAgICAgdGhhdC5lcnJvclRpcHMoIuacrOacn+atouW6puS4jeiDveWkp+S6jue/u+ihqOWAvO+8miIgKyBtYXhkZWdyZWUpOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgYiA9IHRydWU7DQogICAgICAgICAgICAgIGFycmF5LnB1c2goZGF0YUxbaV0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBiID0gdHJ1ZTsNCiAgICAgICAgICAgIGFycmF5LnB1c2goZGF0YUxbaV0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLy8gfSk7DQogICAgICBpZiAoYikgew0KICAgICAgICB0aGlzLnN1Ym1pdERhdGEoYXJyYXkpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5lcnJvclRpcHMoIuayoeacieWPr+S/neWtmOaVsOaNriIpOw0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0Q29udHJhY3RJbmZvKHN0YXRpb25Db2RlKSB7DQogICAgICB0aGlzLmNvbnRyYWN0Q291bnQgPSAwOw0KICAgICAgYXdhaXQgdmFsaWRDb250cmFjdExpc3QoeyBzdGF0aW9uQ29kZTogc3RhdGlvbkNvZGUgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuZGF0YSkgew0KICAgICAgICAgIHRoaXMuY29udHJhY3RDb3VudCA9IHJlcy5kYXRhLmxlbmd0aDsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvL+aPkOS6pOaVsOaNrg0KICAgIHN1Ym1pdERhdGEoZGF0YSkgew0KICAgICAgLy8gdGhpcy5hbW1ldGVyaWRzPVtdOw0KICAgICAgbGV0IGEgPSBbXTsNCiAgICAgIGxldCBzdHIgPSAiIjsNCiAgICAgIGxldCB2ZXJzaW9uID0gaW5kZXhEYXRhLnZlcnNpb247DQogICAgICBpZiAoZGF0YSAhPSBudWxsICYmIGRhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICBsZXQgbnVtYmVyID0gMDsNCiAgICAgICAgbGV0IHN1Ym1pdERhdGEgPSBbXTsNCiAgICAgICAgbGV0IG5vID0gdGhpcy5hY2NvdW50T2JqLmFjY291bnRubzsNCiAgICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgICBsZXQgc3RyMSA9ICIiOw0KICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICBsZXQgb2JqID0gcmVxdWlyZWRGaWVsZFZhbGlkYXRvcihpdGVtKTsNCiAgICAgICAgICBpZiAob2JqLnJlc3VsdCkgew0KICAgICAgICAgICAgbGV0IHl5eXltbWRkID0gY3V0RGF0ZV95eXl5bW1kZChpdGVtLnN0YXJ0ZGF0ZSk7DQogICAgICAgICAgICBpdGVtLnN0YXJ0eWVhciA9IHl5eXltbWRkLnl5eXk7DQogICAgICAgICAgICBpdGVtLnN0YXJ0bW9udGggPSB5eXl5bW1kZC5tbTsNCiAgICAgICAgICAgIHl5eXltbWRkID0gY3V0RGF0ZV95eXl5bW1kZChpdGVtLmVuZGRhdGUpOw0KICAgICAgICAgICAgaXRlbS5lbmR5ZWFyID0geXl5eW1tZGQueXl5eTsNCiAgICAgICAgICAgIGl0ZW0uZW5kbW9udGggPSB5eXl5bW1kZC5tbTsNCiAgICAgICAgICAgIGEucHVzaChpdGVtLmFtbWV0ZXJpZCk7DQogICAgICAgICAgICBzdWJtaXREYXRhLnB1c2goaXRlbSk7DQogICAgICAgICAgICBudW1iZXIrKzsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgc3RyICs9DQogICAgICAgICAgICAgICLnlLXooagv5Y2P6K6u57yW5Y+35Li644CQIiArDQogICAgICAgICAgICAgIGl0ZW0uYW1tZXRlcmNvZGUgKw0KICAgICAgICAgICAgICAi44CR55qE5Y+w6LSm6aqM6K+B5rKh5pyJ6YCa6L+H77ya44CQIiArDQogICAgICAgICAgICAgIG9iai5zdHIgKw0KICAgICAgICAgICAgICAi44CR77ybIjsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGl0ZW0ubWFnbmlmaWNhdGlvbmVyciA9PSAyKSB7DQogICAgICAgICAgICBzdHIxICs9DQogICAgICAgICAgICAgICLnlLXooagv5Y2P6K6u57yW5Y+35Li644CQIiArDQogICAgICAgICAgICAgIGl0ZW0uYW1tZXRlcmNvZGUgKw0KICAgICAgICAgICAgICAi44CR55qE5Y+w6LSm5YCN546H44CQIiArDQogICAgICAgICAgICAgIGl0ZW0ubWFnbmlmaWNhdGlvbiArDQogICAgICAgICAgICAgICLjgJHkuI7nlLXooajlgI3njofjgJAiICsNCiAgICAgICAgICAgICAgaXRlbS5hbW1tdWx0dGltZXMgKw0KICAgICAgICAgICAgICAi44CR5LiN5LiA6Ie077yBICA8YnIgLz4gIjsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBpZiAoaXRlbS5wZXJjZW50ZXJyID09IDIpIHsNCiAgICAgICAgICAgIHN0cjEgKz0NCiAgICAgICAgICAgICAgIueUteihqC/ljY/orq7nvJblj7fkuLrjgJAiICsNCiAgICAgICAgICAgICAgaXRlbS5hbW1ldGVyY29kZSArDQogICAgICAgICAgICAgICLjgJHnmoTlj7DotKbliIblibLmr5TkvovjgJAiICsNCiAgICAgICAgICAgICAgaXRlbS5wZXJjZW50ICsNCiAgICAgICAgICAgICAgIuOAkeS4jueUteihqOWIhuWJsuavlOS+i+OAkCIgKw0KICAgICAgICAgICAgICBpdGVtLmFtbXBlcmNlbnQgKw0KICAgICAgICAgICAgICAi44CR5LiN5LiA6Ie077yBIDxiciAvPiAiOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICAgIHRoYXQuYW1tZXRlcmlkcyA9IGE7DQogICAgICAgIGlmIChzdHIubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoYXQuZXJyb3JUaXBzKHN0cik7DQogICAgICAgIH0NCiAgICAgICAgaWYgKHN0cjEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoYXQuJE5vdGljZS53YXJuaW5nKHsNCiAgICAgICAgICAgIHRpdGxlOiAi5rOo5oSPIiwNCiAgICAgICAgICAgIGRlc2M6IHN0cjEsDQogICAgICAgICAgICBkdXJhdGlvbjogMCwNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoc3VibWl0RGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgLy/lm5vlt53og73ogJfpnIDlgZrnqL3moLjmtYHnqIsNCiAgICAgICAgICB0aGlzLnN1Ym1pdCA9IHN1Ym1pdERhdGE7DQogICAgICAgICAgdGhpcy5zdWJtaXQyID0gc3VibWl0RGF0YTsNCiAgICAgICAgICBlZGl0T3duKHN1Ym1pdERhdGEpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgaWYgKHJlcy5kYXRhLm51bSA+IDApIHsNCiAgICAgICAgICAgICAgdGhhdC4kTWVzc2FnZS5pbmZvKHsNCiAgICAgICAgICAgICAgICBjb250ZW50OiAi5oiQ5Yqf5L+d5a2YIiArIHJlcy5kYXRhLm51bSArICLmnaHmlbDmja4iLA0KICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxMCwNCiAgICAgICAgICAgICAgICBjbG9zYWJsZTogdHJ1ZSwNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIGlmIChyZXMuZGF0YS5zdHIubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICB0aGF0LmVycm9yVGlwcyhyZXMuZGF0YS5zdHIpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhhdC5wYWdlTnVtID0gMTsNCiAgICAgICAgICAgIHRoYXQuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIHN1Ym1pdENoYW5nZShpbmRleExpc3QpIHsNCiAgICAgIGxldCBkYXRhID0gW107DQogICAgICB0aGlzLnN1Ym1pdDIubWFwKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICBpbmRleExpc3QubWFwKChpdGVtMikgPT4gew0KICAgICAgICAgIGlmIChpbmRleCA9PSBpdGVtMikgew0KICAgICAgICAgICAgZGF0YS5wdXNoKGl0ZW0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICAgIHRoaXMuc3VibWl0ID0gZGF0YTsNCiAgICB9LA0KICAgIGdldEF1ZGl0UmVzdWx0TmV3KGRhdGEpIHsNCiAgICAgIGxldCBhcnIgPSBbXTsNCiAgICAgIGRhdGEuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICBhcnIucHVzaChpdGVtLnBjaWQpOw0KICAgICAgfSk7DQogICAgICBsZXQgcGFyYW0gPSB7DQogICAgICAgIHBjaWRzOiBhcnIsDQogICAgICB9Ow0KICAgICAgZ2V0QXVkaXRSZXN1bHROZXdfUVhNKHBhcmFtKS50aGVuKChyZXMyKSA9PiB7DQogICAgICAgIHRoaXMuYXVkaXRSZXN1bHRMaXN0ID0gcmVzMi5kYXRhOw0KDQogICAgICAgIGlmICh0aGlzLmF1ZGl0UmVzdWx0TGlzdCAmJiB0aGlzLmF1ZGl0UmVzdWx0TGlzdC5sZW5ndGggPT0gMCkgew0KICAgICAgICAgIHRoaXMubnVtYmVyKys7DQogICAgICAgICAgdGhpcy5pc1F1ZXJ5ID0gdHJ1ZTsNCiAgICAgICAgICAvLyB9DQogICAgICAgIH0gZWxzZSBpZiAoZGF0YS5sZW5ndGggIT0gdGhpcy5hdWRpdFJlc3VsdExpc3QubGVuZ3RoKSB7DQogICAgICAgICAgdGhpcy5udW1iZXIrKzsNCiAgICAgICAgICB0aGlzLmlzUXVlcnkgPSB0cnVlOw0KICAgICAgICB9IGVsc2UgaWYgKGRhdGEubGVuZ3RoID09IHRoaXMuYXVkaXRSZXN1bHRMaXN0Lmxlbmd0aCkgew0KICAgICAgICAgIHRoaXMuaXNRdWVyeSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMubnVtYmVyID0gMDsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmlzUXVlcnkgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLm51bWJlciA9IDA7DQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuaXNRdWVyeSAmJiB0aGlzLm51bWJlciA8IDUpIHsNCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHRoaXMuZ2V0QXVkaXRSZXN1bHROZXcoZGF0YSksIDYwMDApOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuYXVkaXRSZXN1bHRMaXN0LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwucmVzdWx0TGlzdC5wdXNoKGl0ZW0ubXNnKTsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwudGFibGVEYXRhLnB1c2goaXRlbS5wb3dlckF1ZGl0RW50aXR5KTsNCg0KICAgICAgICAgICAgaWYgKGl0ZW0uc3RhdXRlID09ICLlpLHotKUiKSB7DQogICAgICAgICAgICAgIC8vIGlmKGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5tdXRpSnRsdGVDb2Rlcz09J+aYrycNCiAgICAgICAgICAgICAgLy8gfHwgaXRlbS5wb3dlckF1ZGl0RW50aXR5LmVsZWN0cmljaXR5UHJpY2VzPT0n5ZCmJw0KICAgICAgICAgICAgICAvLyB8fCBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuYWRkcmVzc0NvbnNpc3RlbmNlPT0n5ZCmJw0KICAgICAgICAgICAgICAvLyB8fCBpdGVtLnBvd2VyQXVkaXRFbnRpdHkucmVpbWJ1cnNlbWVudEN5Y2xlPT0n5ZCmJyB8fCBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZWxlY3RyaWNpdHlDb250aW51aXR5PT0n5ZCmJyB8fA0KICAgICAgICAgICAgICAvLyBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuc2hhcmVBY2N1cmFjeT09J+WQpicgfHwNCiAgICAgICAgICAgICAgLy8gaXRlbS5wb3dlckF1ZGl0RW50aXR5LmV4Y2x1c2l2ZUFjY3VyYWN5PT0n5ZCmJ3x8DQogICAgICAgICAgICAgIC8vIGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5wYXltZW50Q29uc2lzdGVuY2U9PSflkKYnKXsNCiAgICAgICAgICAgICAgaWYgKGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5tdXRpSnRsdGVDb2RlcyA9PSAi5pivIikgew0KICAgICAgICAgICAgICAgIC8v5LiA56uZ5aSa6KGoDQogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGE0LnB1c2goaXRlbS5wb3dlckF1ZGl0RW50aXR5KTsNCiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnJlc3VsdExpc3Q0LnB1c2goaXRlbS5tc2cpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIGlmIChpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZWxlY3RyaWNpdHlQcmljZXMgPT0gIuWQpiIpIHsNCiAgICAgICAgICAgICAgICAvL+WNleS7t+W8guW4uA0KICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwudGFibGVEYXRhNS5wdXNoKGl0ZW0ucG93ZXJBdWRpdEVudGl0eSk7DQogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5yZXN1bHRMaXN0NS5wdXNoKGl0ZW0ubXNnKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBpZiAoDQogICAgICAgICAgICAgICAgaXRlbS5wb3dlckF1ZGl0RW50aXR5LmFkZHJlc3NDb25zaXN0ZW5jZSA9PSAi5ZCmIiB8fA0KICAgICAgICAgICAgICAgIGl0ZW0ucG93ZXJBdWRpdEVudGl0eS5yZWltYnVyc2VtZW50Q3ljbGUgPT0gIuWQpiIgfHwNCiAgICAgICAgICAgICAgICBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZWxlY3RyaWNpdHlDb250aW51aXR5ID09ICLlkKYiIHx8DQogICAgICAgICAgICAgICAgaXRlbS5wb3dlckF1ZGl0RW50aXR5LnNoYXJlQWNjdXJhY3kgPT0gIuWQpiIgfHwNCiAgICAgICAgICAgICAgICAvLyBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZXhjbHVzaXZlQWNjdXJhY3k9PSflkKYnfHwNCiAgICAgICAgICAgICAgICBpdGVtLnBvd2VyQXVkaXRFbnRpdHkucGF5bWVudENvbnNpc3RlbmNlID09ICLlkKYiIHx8DQogICAgICAgICAgICAgICAgaXRlbS5wb3dlckF1ZGl0RW50aXR5LmZsdWN0dWF0ZUNvbnRpbnVpdHkgPT0gIuWQpiINCiAgICAgICAgICAgICAgKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGEyLnB1c2goaXRlbS5wb3dlckF1ZGl0RW50aXR5KTsgLy/lhbbku5blvILluLgNCiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnJlc3VsdExpc3QyLnB1c2goaXRlbS5tc2cpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICAgICAvLyBpdGVtLnBvd2VyQXVkaXRFbnRpdHkuZWxlY3RyaWNpdHlSYXRpb25hbGl0eSA9PSAi5pivIiAmJiAvL+eUtemHj+WQiOeQhuaApyjnnIHlhoXlpKfmlbDmja4pDQogICAgICAgICAgICAgICAgLy8gaXRlbS5wb3dlckF1ZGl0RW50aXR5LmV4Y2x1c2l2ZUFjY3VyYWN5ID09ICLmmK8iICYmIC8v5bGA56uZ54us5Lqr5YWx5Lqr6K6+572uDQogICAgICAgICAgICAgICAgaXRlbS5wb3dlckF1ZGl0RW50aXR5LnBlcmlvZGljQW5vbWFseSA9PSAi5pivIiAvL+WPsOi0puWRqOacn+WQiOeQhuaApw0KICAgICAgICAgICAgICApIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnRhYmxlRGF0YTEucHVzaChpdGVtLnBvd2VyQXVkaXRFbnRpdHkpOw0KICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwucmVzdWx0TGlzdDEucHVzaChpdGVtLm1zZyk7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC50YWJsZURhdGEzLnB1c2goaXRlbS5wb3dlckF1ZGl0RW50aXR5KTsNCiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnJlc3VsdExpc3QzLnB1c2goaXRlbS5tc2cpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZiAodGhpcy5hdWRpdFJlc3VsdExpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICB0aGlzLmF1ZGl0UmVzdWx0TGlzdFt0aGlzLmF1ZGl0UmVzdWx0TGlzdC5sZW5ndGggLSAxXS5wcm9ncmVzcyA9DQogICAgICAgICAgICAgICAgKCh0aGlzLmF1ZGl0UmVzdWx0TGlzdC5sZW5ndGggKiAxKSAvIGRhdGEubGVuZ3RoKSAqIDE7DQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnByb2Nlc3NEYXRhID0gTnVtYmVyKGl0ZW0ucHJvZ3Jlc3MpICogMTAwOw0KICAgICAgICAgICAgdGhpcy4kcmVmcy5zaG93QWxhcm1Nb2RlbC5zY3JvbGxMaXN0KCk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgc2F2ZSh2YWx1ZSkgew0KICAgICAgdmFyIHRoYXQgPSB0aGlzOw0KICAgICAgLy/ov5vluqbmnaHpobXpnaLpk77mjqV3ZWJzb2tldOWQjuiwg+eUqO+8jOS8oGpo5a2X5q615LiN6LWw5L+d5a2Y5rWB56iL77yM6LWw56i95qC45rWB56iLDQogICAgICBpZiAodmFsdWUgPT0gMSkgew0KICAgICAgICB0aGF0LnN1Ym1pdFswXS5qaCA9ICIxIjsNCiAgICAgICAgdGhhdC5zdWJtaXRbMF0ueW1tYyA9ICLoh6rmnInnlLXotLnlj7DotKYiOw0KICAgICAgICB0aGlzLmdldEF1ZGl0UmVzdWx0TmV3KHRoYXQuc3VibWl0KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGlmICh0aGF0LnN1Ym1pdFswXS5oYXNPd25Qcm9wZXJ0eSgiamgiKSkgew0KICAgICAgICAgIGRlbGV0ZSB0aGF0LnN1Ym1pdFswXS5qaDsNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5uYW1lID09ICJjdXJyZW50Iikgew0KICAgICAgICAgIGlmICh0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLmZyb21HdWlqaWRhbiA9PSAxKSB7DQogICAgICAgICAgICB0aGlzLnNlbGVjdGVkRlFLKCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRBY2NvdW50KCk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMubmFtZSA9PSAiYWxsIikgew0KICAgICAgICAgIGlmICh0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLmZyb21HdWlqaWRhbiA9PSAxKSB7DQogICAgICAgICAgICB0aGlzLnNlbGVjdGVkRlFLKCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRBbGxBY2NvdW50KCk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvL+WKoOWFpeW9kumbhuWNle+8jOWFqOmDqOmdnuW8uuaOpw0KICAgIHNlbGVjdGVkRlFLKCkgew0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgdGhhdC5zZXRFbGVjdHJveVR5cGUoKTsNCiAgICAgIHRoYXQuJHJlZnMuYWRkQmlsbFBlci5pbml0QW1tZXRlcigNCiAgICAgICAgdGhhdC4kcmVmcy5zaG93QWxhcm1Nb2RlbC5zZWxlY3RJZHMzLA0KICAgICAgICAxLA0KICAgICAgICB0aGlzLmFjY291bnRPYmouY291bnRyeQ0KICAgICAgKTsNCiAgICB9LA0KICAgIC8v5re75Yqg5L+u5pS55bOw5bmz6LC35YC855qE5aSH5rOoDQogICAgYWRkRnJlbWFyayhyb3cpIHsNCiAgICAgIGxldCBvbGQgPSByb3cub2xkX3ByZXZoaWdocmVhZGluZ3M7DQogICAgICBpZiAocm93LmlmTmV4dCkgew0KICAgICAgICByZXR1cm47DQogICAgICB9IGVsc2UgaWYgKA0KICAgICAgICByb3cuY3VyaGlnaHJlYWRpbmdzICE9IG51bGwgJiYNCiAgICAgICAgcm93LmN1cmhpZ2hyZWFkaW5ncyA+IDAgJiYNCiAgICAgICAgdGhpcy5lZGl0cHJldmhpZ2hyZWFkaW5ncyA+IHJvdy5jdXJoaWdocmVhZGluZ3MNCiAgICAgICkgew0KICAgICAgICB0aGlzLmVycm9yVGlwcygi6LW35aeL5bOw5YC85LiN6IO95aSn5LqO5oiq5q2i5bOw5YC8IiArIHJvdy5jdXJoaWdocmVhZGluZ3MpOw0KICAgICAgICB2YXIgdGhhdCA9IHRoaXM7DQogICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgew0KICAgICAgICAgIHRoYXQuZWRpdHByZXZoaWdocmVhZGluZ3MgPSBvbGQ7DQogICAgICAgIH0sIDIwMCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByb3cucHJldmhpZ2hyZWFkaW5ncyA9IHRoaXMuZWRpdHByZXZoaWdocmVhZGluZ3M7DQogICAgICAgIHJvdy5lZGl0VHlwZSA9IDE7DQogICAgICAgIHRoaXMuY2FsY3VsYXRlQWxsKHJvdyk7DQogICAgICAgIGlmIChyb3cub2xkX3ByZXZoaWdocmVhZGluZ3MgIT0gcm93LnByZXZoaWdocmVhZGluZ3MpIHsNCiAgICAgICAgICByb3cucmVtYXJrICs9DQogICAgICAgICAgICAi5pys5pyf6LW35aeL5bOw5YC8IOS7jiIgKw0KICAgICAgICAgICAgcm93Lm9sZF9wcmV2aGlnaHJlYWRpbmdzICsNCiAgICAgICAgICAgICLkv67mlLnkuLoiICsNCiAgICAgICAgICAgIHJvdy5wcmV2aGlnaHJlYWRpbmdzICsNCiAgICAgICAgICAgICI7ICI7DQogICAgICAgIH0NCiAgICAgICAgLy/kv67mlLlvcGZsYWfkv67mlLnotbfml6XmnJ8g5Y6f5p2l5pWw5a2X5YqgDQogICAgICAgIGxldCBvcGZsYWcgPSByb3cub3BmbGFnOw0KICAgICAgICBpZiAob3BmbGFnICE9IDMgJiYgb3BmbGFnICE9IDUgJiYgb3BmbGFnICE9IDcgJiYgb3BmbGFnICE9IDkpIHsNCiAgICAgICAgICByb3cub3BmbGFnID0gb3BmbGFnICsgMzsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy/otbflp4vlubPlgLzlj5jmjaLorrDlvZXlpIfms6gNCiAgICBhZGRQcmVtYXJrKHJvdykgew0KICAgICAgbGV0IG9sZCA9IHJvdy5vbGRfcHJldmZsYXRyZWFkaW5nczsNCiAgICAgIGlmIChyb3cuaWZOZXh0KSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0gZWxzZSBpZiAoDQogICAgICAgIHJvdy5jdXJmbGF0cmVhZGluZ3MgIT0gbnVsbCAmJg0KICAgICAgICByb3cuY3VyZmxhdHJlYWRpbmdzID4gMCAmJg0KICAgICAgICB0aGlzLmVkaXRwcmV2ZmxhdHJlYWRpbmdzID4gcm93LmN1cmZsYXRyZWFkaW5ncw0KICAgICAgKSB7DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLotbflp4vlubPlgLzkuI3og73lpKfkuo7miKrmraLlubPlgLwiICsgcm93LmN1cmZsYXRyZWFkaW5ncyk7DQoNCiAgICAgICAgdmFyIHRoYXQgPSB0aGlzOw0KICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICB0aGF0LmVkaXRwcmV2ZmxhdHJlYWRpbmdzID0gb2xkOw0KICAgICAgICB9LCAyMDApOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcm93LnByZXZmbGF0cmVhZGluZ3MgPSB0aGlzLmVkaXRwcmV2ZmxhdHJlYWRpbmdzOw0KICAgICAgICByb3cuZWRpdFR5cGUgPSAxOw0KICAgICAgICB0aGlzLmNhbGN1bGF0ZUFsbChyb3cpOw0KICAgICAgICBpZiAocm93Lm9sZF9wcmV2ZmxhdHJlYWRpbmdzICE9IHJvdy5wcmV2ZmxhdHJlYWRpbmdzKSB7DQogICAgICAgICAgcm93LnJlbWFyayArPQ0KICAgICAgICAgICAgIuacrOacn+i1t+Wni+W5s+WAvCDku44iICsNCiAgICAgICAgICAgIHJvdy5vbGRfcHJldmZsYXRyZWFkaW5ncyArDQogICAgICAgICAgICAi5L+u5pS55Li6IiArDQogICAgICAgICAgICByb3cucHJldmZsYXRyZWFkaW5ncyArDQogICAgICAgICAgICAiOyAiOw0KICAgICAgICB9DQogICAgICAgIC8v5L+u5pS5b3BmbGFn5L+u5pS56LW35pel5pyfIOWOn+adpeaVsOWtl+WKoA0KICAgICAgICBsZXQgb3BmbGFnID0gcm93Lm9wZmxhZzsNCiAgICAgICAgaWYgKG9wZmxhZyAhPSAzICYmIG9wZmxhZyAhPSA1ICYmIG9wZmxhZyAhPSA3ICYmIG9wZmxhZyAhPSA5KSB7DQogICAgICAgICAgcm93Lm9wZmxhZyA9IG9wZmxhZyArIDM7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8v6LW35aeL6LC35YC85Y+Y5o2i6K6w5b2V5aSH5rOoDQogICAgYWRkR3JlbWFyayhyb3cpIHsNCiAgICAgIGxldCBvbGQgPSByb3cub2xkX3ByZXZsb3dyZWFkaW5nczsNCiAgICAgIGlmIChyb3cuaWZOZXh0KSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0gZWxzZSBpZiAoDQogICAgICAgIHJvdy5jdXJsb3dyZWFkaW5ncyAhPSBudWxsICYmDQogICAgICAgIHJvdy5jdXJsb3dyZWFkaW5ncyA+IDAgJiYNCiAgICAgICAgdGhpcy5lZGl0cHJldmxvd3JlYWRpbmdzID4gcm93LmN1cmxvd3JlYWRpbmdzDQogICAgICApIHsNCiAgICAgICAgdGhpcy5lcnJvclRpcHMoIui1t+Wni+iwt+WAvOS4jeiDveWkp+S6juaIquatouiwt+WAvCIgKyByb3cuY3VybG93cmVhZGluZ3MpOw0KDQogICAgICAgIHZhciB0aGF0ID0gdGhpczsNCiAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7DQogICAgICAgICAgdGhhdC5lZGl0cHJldmxvd3JlYWRpbmdzID0gb2xkOw0KICAgICAgICB9LCAyMDApOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcm93LnByZXZsb3dyZWFkaW5ncyA9IHRoaXMuZWRpdHByZXZsb3dyZWFkaW5nczsNCiAgICAgICAgcm93LmVkaXRUeXBlID0gMTsNCiAgICAgICAgdGhpcy5jYWxjdWxhdGVBbGwocm93KTsNCiAgICAgICAgaWYgKHJvdy5vbGRfcHJldmxvd3JlYWRpbmdzICE9IHJvdy5wcmV2bG93cmVhZGluZ3MpIHsNCiAgICAgICAgICByb3cucmVtYXJrICs9DQogICAgICAgICAgICAi5pys5pyf6LW35aeL6LC35YC8IOS7jiIgKw0KICAgICAgICAgICAgcm93Lm9sZF9wcmV2bG93cmVhZGluZ3MgKw0KICAgICAgICAgICAgIuS/ruaUueS4uiIgKw0KICAgICAgICAgICAgcm93LnByZXZsb3dyZWFkaW5ncyArDQogICAgICAgICAgICAiOyAiOw0KICAgICAgICB9DQogICAgICAgIC8v5L+u5pS5b3BmbGFn5L+u5pS56LW35pel5pyfIOWOn+adpeaVsOWtl+WKoA0KICAgICAgICBsZXQgb3BmbGFnID0gcm93Lm9wZmxhZzsNCiAgICAgICAgaWYgKG9wZmxhZyAhPSAzICYmIG9wZmxhZyAhPSA1ICYmIG9wZmxhZyAhPSA3ICYmIG9wZmxhZyAhPSA5KSB7DQogICAgICAgICAgcm93Lm9wZmxhZyA9IG9wZmxhZyArIDM7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIHNldGN1cmhpZ2hyZWFkaW5ncyhyb3cpIHsNCiAgICAgIGlmIChyb3cuaWZOZXh0KSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5lZGl0Y3VyaGlnaHJlYWRpbmdzIDwgcm93LnByZXZoaWdocmVhZGluZ3MpIHsNCiAgICAgICAgdGhpcy5lcnJvclRpcHMoIuaIquatouWzsOWAvOS4jeiDveWwj+S6jui1t+Wni+WzsOWAvCIgKyByb3cucHJldmhpZ2hyZWFkaW5ncyk7DQoNCiAgICAgICAgdmFyIHRoYXQgPSB0aGlzOw0KICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICB0aGF0LmVkaXRjdXJoaWdocmVhZGluZ3MgPSByb3cuY3VyaGlnaHJlYWRpbmdzOw0KICAgICAgICB9LCAyMDApOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcm93LmN1cmhpZ2hyZWFkaW5ncyA9IHRoaXMuZWRpdGN1cmhpZ2hyZWFkaW5nczsNCiAgICAgICAgcm93LmVkaXRUeXBlID0gMTsNCiAgICAgICAgdGhpcy5jYWxjdWxhdGVBbGwocm93KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHNldGN1cmZsYXRyZWFkaW5ncyhyb3cpIHsNCiAgICAgIGlmIChyb3cuaWZOZXh0KSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5lZGl0Y3VyZmxhdHJlYWRpbmdzIDwgcm93LnByZXZmbGF0cmVhZGluZ3MpIHsNCiAgICAgICAgdGhpcy5lcnJvclRpcHMoIuaIquatouW5s+WAvOS4jeiDveWwj+S6jui1t+Wni+W5s+WAvCIgKyByb3cucHJldmZsYXRyZWFkaW5ncyk7DQoNCiAgICAgICAgdmFyIHRoYXQgPSB0aGlzOw0KICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICB0aGF0LmVkaXRjdXJmbGF0cmVhZGluZ3MgPSByb3cuY3VyZmxhdHJlYWRpbmdzOw0KICAgICAgICB9LCAyMDApOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcm93LmN1cmZsYXRyZWFkaW5ncyA9IHRoaXMuZWRpdGN1cmZsYXRyZWFkaW5nczsNCiAgICAgICAgcm93LmVkaXRUeXBlID0gMTsNCiAgICAgICAgdGhpcy5jYWxjdWxhdGVBbGwocm93KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHNldGN1cmxvd3JlYWRpbmdzKHJvdykgew0KICAgICAgbGV0IG5leHQgPSByb3cuaWZOZXh0Ow0KICAgICAgaWYgKHJvdy5pZk5leHQpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLmVkaXRjdXJsb3dyZWFkaW5ncyA8IHJvdy5wcmV2bG93cmVhZGluZ3MpIHsNCiAgICAgICAgdGhpcy5lcnJvclRpcHMoIuaIquatouiwt+WAvOS4jeiDveWwj+S6jui1t+Wni+iwt+WAvCIgKyByb3cucHJldmxvd3JlYWRpbmdzKTsNCiAgICAgICAgdmFyIHRoYXQgPSB0aGlzOw0KICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICB0aGF0LmVkaXRjdXJsb3dyZWFkaW5ncyA9IHJvdy5jdXJsb3dyZWFkaW5nczsNCiAgICAgICAgfSwgMjAwKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJvdy5jdXJsb3dyZWFkaW5ncyA9IHRoaXMuZWRpdGN1cmxvd3JlYWRpbmdzOw0KICAgICAgICByb3cuZWRpdFR5cGUgPSAxOw0KICAgICAgICB0aGlzLmNhbGN1bGF0ZUFsbChyb3cpOw0KICAgICAgfQ0KICAgIH0sDQogICAgc2V0RlBHKHJvdykgew0KICAgICAgbGV0IGl0ZW0gPSB7DQogICAgICAgIHByZXZoaWdocmVhZGluZ3M6IHJvdy5wcmV2aGlnaHJlYWRpbmdzLA0KICAgICAgICBwcmV2ZmxhdHJlYWRpbmdzOiByb3cucHJldmZsYXRyZWFkaW5ncywNCiAgICAgICAgcHJldmxvd3JlYWRpbmdzOiByb3cucHJldmxvd3JlYWRpbmdzLA0KICAgICAgICBjdXJoaWdocmVhZGluZ3M6IHJvdy5jdXJoaWdocmVhZGluZ3MsDQogICAgICAgIGN1cmZsYXRyZWFkaW5nczogcm93LmN1cmZsYXRyZWFkaW5ncywNCiAgICAgICAgY3VybG93cmVhZGluZ3M6IHJvdy5jdXJsb3dyZWFkaW5ncywNCiAgICAgICAgaGlnaHJlYWRpbmdzOiBwYXJzZUZsb2F0KHRoaXMuZWRpdGhpZ2hyZWFkaW5ncy50b0ZpeGVkKDIpKSwNCiAgICAgICAgZmxhdHJlYWRpbmdzOiBwYXJzZUZsb2F0KHRoaXMuZWRpdGZsYXRyZWFkaW5ncy50b0ZpeGVkKDIpKSwNCiAgICAgICAgbG93cmVhZGluZ3M6IHBhcnNlRmxvYXQodGhpcy5lZGl0bG93cmVhZGluZ3MudG9GaXhlZCgyKSksDQogICAgICAgIG1hZ25pZmljYXRpb246IHJvdy5tYWduaWZpY2F0aW9uLA0KICAgICAgfTsNCiAgICAgIGxldCBhbW91bnQgPSBfY2FsY3VsYXRlVXNlZFJlYWRpbmdzRm9yVHlwZV8yKGl0ZW0pOw0KDQogICAgICBpZiAoYW1vdW50IDwgMCkgew0KICAgICAgICAvL+iuoeeul+eUqOeUtemHjw0KICAgICAgICB0aGlzLmVycm9yVGlwcygNCiAgICAgICAgICAi6K6h566X55So55S16YeP5bCP5LqOMCgiICsNCiAgICAgICAgICAgIGFtb3VudCArDQogICAgICAgICAgICAiKSzor7fnoa7orqTls7DlubPosLfliqDlh4/nlLXph4/lgLzvvIEiICsNCiAgICAgICAgICAgICLliqDlh4/nlLXph48o5bOw5YC8KSIgKw0KICAgICAgICAgICAgcGFyc2VGbG9hdCh0aGlzLmVkaXRoaWdocmVhZGluZ3MudG9GaXhlZCgyKSkgKw0KICAgICAgICAgICAgIizliqDlh4/nlLXph48o5bmz5YC8KSIgKw0KICAgICAgICAgICAgcGFyc2VGbG9hdCh0aGlzLmVkaXRmbGF0cmVhZGluZ3MudG9GaXhlZCgyKSkgKw0KICAgICAgICAgICAgIizliqDlh4/nlLXph48o6LC35YC8KSIgKw0KICAgICAgICAgICAgcGFyc2VGbG9hdCh0aGlzLmVkaXRsb3dyZWFkaW5ncy50b0ZpeGVkKDIpKSArDQogICAgICAgICAgICAi5b2T5YmN55So55S16YePIiArDQogICAgICAgICAgICByb3cuY3VydXNlZHJlYWRpbmdzDQogICAgICAgICk7DQogICAgICAgIC8vIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHJvdy5oaWdocmVhZGluZ3MgPSBwYXJzZUZsb2F0KHRoaXMuZWRpdGhpZ2hyZWFkaW5ncy50b0ZpeGVkKDIpKTsNCiAgICAgIHJvdy5mbGF0cmVhZGluZ3MgPSBwYXJzZUZsb2F0KHRoaXMuZWRpdGZsYXRyZWFkaW5ncy50b0ZpeGVkKDIpKTsNCiAgICAgIHJvdy5sb3dyZWFkaW5ncyA9IHBhcnNlRmxvYXQodGhpcy5lZGl0bG93cmVhZGluZ3MudG9GaXhlZCgyKSk7DQogICAgICByb3cuZWRpdFR5cGUgPSAxOw0KICAgICAgdGhpcy5jYWxjdWxhdGVBbGwocm93KTsNCiAgICB9LA0KICAgIC8v5LiA6ZSu5Yig6Zmk5pWw5o2uDQogICAgZGVsZXRlQWxsKCkgew0KICAgICAgdGhpcy4kTW9kYWwuY29uZmlybSh7DQogICAgICAgIHRpdGxlOiAi5o+Q56S6IiwNCiAgICAgICAgY29udGVudDogIjxwPuehruWumuS4gOmUruWIoOmZpOWQl++8nzwvcD4iLA0KICAgICAgICBvbk9rOiAoKSA9PiB7DQogICAgICAgICAgdGhpcy5hY2NvdW50VGIubG9hZGluZyA9IHRydWU7DQogICAgICAgICAgbGV0IHBhcmFtcyA9IHRoaXMuYWNjb3VudE9iajsNCiAgICAgICAgICBwYXJhbXMucmVtb3ZlQWxsRmxhZyA9IHRydWU7DQogICAgICAgICAgZGVsZXRlIHBhcmFtcy5wYWdlU2l6ZTsNCiAgICAgICAgICBkZWxldGUgcGFyYW1zLnBhZ2VOdW07DQogICAgICAgICAgcmVtb3ZlQWxsKHBhcmFtcykudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICB0aGlzLmFjY291bnRUYi5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICBpZiAocmVzLmRhdGEubnVtID4gMCkgew0KICAgICAgICAgICAgICB0aGlzLiRNZXNzYWdlLnN1Y2Nlc3MoIuS4gOmUruWIoOmZpOaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLnNlYXJjaExpc3QoKTsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuZXJyb3IoIuS4gOmUruWIoOmZpOWksei0pSIpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICBvbkNhbmNlbDogKCkgPT4ge30sDQogICAgICB9KTsNCiAgICB9LA0KICAgIC8v5Yig6Zmk6KGM5pWw5o2uDQogICAgcmVtb3ZlKCkgew0KICAgICAgbGV0IHZlcnNpb24gPSBpbmRleERhdGEudmVyc2lvbjsNCiAgICAgIGxldCBkYXRhID0gdGhpcy4kcmVmcy5hY2NvdW50VGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICBpZiAoZGF0YSA9PSBudWxsIHx8IGRhdGEubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fpgInmi6nopoHliKDpmaTnmoTlj7DotKYiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgbGV0IGlkcyA9ICIiOw0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgbGV0IHN0ciA9ICIiOw0KICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgIGlmIChpdGVtLmlmTmV4dCkgew0KICAgICAgICAgIHN0ciArPQ0KICAgICAgICAgICAgIueUteihqC/ljY/orq7nvJblj7fkuLrjgJAiICsNCiAgICAgICAgICAgIGl0ZW0uYW1tZXRlcmNvZGUgKw0KICAgICAgICAgICAgIuOAkeW9k+acn+OAkCIgKw0KICAgICAgICAgICAgaXRlbS5hY2NvdW50bm8gKw0KICAgICAgICAgICAgIuacn+OAkeWPsOi0puS5i+WQjuW3suacieato+W8j+aVsOaNru+8jOS4jeiDveWIoOmZpO+8gSI7DQogICAgICAgIH0NCiAgICAgICAgaWRzICs9IGl0ZW0ucGNpZCArICIsIjsNCiAgICAgIH0pOw0KICAgICAgaWYgKGlkcy5sZW5ndGggPiAwICYmIHN0ci5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhhdC4kTW9kYWwuY29uZmlybSh7DQogICAgICAgICAgdGl0bGU6ICLmj5DnpLoiLA0KICAgICAgICAgIGNvbnRlbnQ6ICI8cD7mmK/lkKbnoa7orqTliKDpmaTpgInkuK3kv6Hmga/vvJ88L3A+IiwNCiAgICAgICAgICBvbk9rOiAoKSA9PiB7DQogICAgICAgICAgICByZW1vdmVPd24oaWRzKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLm51bSA+IDApIHsNCiAgICAgICAgICAgICAgICB0aGF0LiRNZXNzYWdlLmluZm8oew0KICAgICAgICAgICAgICAgICAgY29udGVudDogIuaIkOWKn+WIoOmZpCIgKyByZXMuZGF0YS5udW0gKyAi5p2h5pWw5o2uIiwNCiAgICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxMCwNCiAgICAgICAgICAgICAgICAgIGNsb3NhYmxlOiB0cnVlLA0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhLnN0ci5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgICAgdGhhdC5lcnJvclRpcHMocmVzLmRhdGEuc3RyKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB0aGF0LnNlYXJjaExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0sDQogICAgICAgICAgb25DYW5jZWw6ICgpID0+IHt9LA0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoYXQuZXJyb3JUaXBzKHN0cik7DQogICAgICB9DQogICAgfSwNCiAgICAvL3NwYW7ngrnlh7vkuovku7blsIZzcGFu5o2i5oiQ6L6T5YWl5qGG5bm25LiU6I635Y+W54Sm54K5DQogICAgc2VsZWN0Q2FsbChyb3csIGluZGV4LCBjb2x1bW5zLCBzdHIpIHsNCiAgICAgIHRoaXMuZWRpdFN0YXJ0RGF0ZSA9IHJvdy5zdGFydGRhdGU7DQogICAgICB0aGlzLmVkaXRFbmREYXRlID0gcm93LmVuZGRhdGU7DQogICAgICB0aGlzLmVkaXRQcmV2dG90YWxyZWFkaW5ncyA9DQogICAgICAgIHJvdy5wcmV2dG90YWxyZWFkaW5ncyA9PSBudWxsIHx8IHJvdy5wcmV2dG90YWxyZWFkaW5ncyA9PT0gMA0KICAgICAgICAgID8gbnVsbA0KICAgICAgICAgIDogcm93LnByZXZ0b3RhbHJlYWRpbmdzOw0KICAgICAgdGhpcy5lZGl0Y3VydG90YWxyZWFkaW5ncyA9DQogICAgICAgIHJvdy5jdXJ0b3RhbHJlYWRpbmdzID09IG51bGwgfHwgcm93LmN1cnRvdGFscmVhZGluZ3MgPT09IDANCiAgICAgICAgICA/IG51bGwNCiAgICAgICAgICA6IHJvdy5jdXJ0b3RhbHJlYWRpbmdzOw0KICAgICAgdGhpcy5lZGl0dHJhbnNmb3JtZXJ1bGxhZ2UgPQ0KICAgICAgICByb3cudHJhbnNmb3JtZXJ1bGxhZ2UgPT0gbnVsbCB8fCByb3cudHJhbnNmb3JtZXJ1bGxhZ2UgPT09IDANCiAgICAgICAgICA/IG51bGwNCiAgICAgICAgICA6IHJvdy50cmFuc2Zvcm1lcnVsbGFnZTsNCiAgICAgIHRoaXMuZWRpdHRheHRpY2tldG1vbmV5ID0NCiAgICAgICAgcm93LmlucHV0dGF4dGlja2V0bW9uZXkgPT0gbnVsbCB8fCByb3cuaW5wdXR0YXh0aWNrZXRtb25leSA9PT0gMA0KICAgICAgICAgID8gbnVsbA0KICAgICAgICAgIDogcm93LmlucHV0dGF4dGlja2V0bW9uZXk7DQogICAgICB0aGlzLmVkaXR0aWNrZXRtb25leSA9DQogICAgICAgIHJvdy5pbnB1dHRpY2tldG1vbmV5ID09IG51bGwgfHwgcm93LmlucHV0dGlja2V0bW9uZXkgPT09IDANCiAgICAgICAgICA/IG51bGwNCiAgICAgICAgICA6IHJvdy5pbnB1dHRpY2tldG1vbmV5Ow0KICAgICAgdGhpcy5lZGl0dWxsYWdlbW9uZXkgPQ0KICAgICAgICByb3cudWxsYWdlbW9uZXkgPT0gbnVsbCB8fCByb3cudWxsYWdlbW9uZXkgPT09IDAgPyBudWxsIDogcm93LnVsbGFnZW1vbmV5Ow0KICAgICAgdGhpcy5lZGl0dGF4cmF0ZSA9DQogICAgICAgIHJvdy50YXhyYXRlID09IG51bGwgfHwgcm93LnRheHJhdGUgPT09IDAgPyBudWxsIDogcGFyc2VJbnQocm93LnRheHJhdGUpICsgIiI7DQogICAgICB0aGlzLmVkaXRyZW1hcmsgPSByb3cuYno7DQogICAgICB0aGlzLmVkaXRJbmRleCA9IGluZGV4Ow0KICAgICAgdGhpcy5jb2x1bW5zSW5kZXggPSBjb2x1bW5zOw0KDQogICAgICBsZXQgYSA9IHRoaXM7DQogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgaWYgKGNvbHVtbnMgIT0gOCkgew0KICAgICAgICAgIGEuJHJlZnNbc3RyICsgaW5kZXggKyBjb2x1bW5zXS5mb2N1cygpOw0KICAgICAgICB9DQogICAgICB9LCAyMDApOw0KICAgIH0sDQogICAgLy/moLnmja7liJflj7fov5Tlm57lr7nlupTnmoTliJflkI0NCiAgICBlbnRlck9wZXJhdGUobnVtYmVyKSB7DQogICAgICBsZXQgc3RyID0gIiI7DQogICAgICBsZXQgZGF0YSA9IG51bGw7DQogICAgICBzd2l0Y2ggKG51bWJlcikgew0KICAgICAgICBjYXNlIDE6DQogICAgICAgICAgc3RyID0gInN0YXJ0ZGF0ZSI7DQogICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdFN0YXJ0RGF0ZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAyOg0KICAgICAgICAgIHN0ciA9ICJlbmRkYXRlIjsNCiAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0RW5kRGF0ZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAzOg0KICAgICAgICAgIHN0ciA9ICJwcmV2dG90YWxyZWFkaW5ncyI7DQogICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdFByZXZ0b3RhbHJlYWRpbmdzOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlIDQ6DQogICAgICAgICAgc3RyID0gImN1cnRvdGFscmVhZGluZ3MiOw0KICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRjdXJ0b3RhbHJlYWRpbmdzOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlIDU6DQogICAgICAgICAgc3RyID0gInRyYW5zZm9ybWVydWxsYWdlIjsNCiAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0dHJhbnNmb3JtZXJ1bGxhZ2U7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgNjoNCiAgICAgICAgICBzdHIgPSAiaW5wdXR0aWNrZXRtb25leSI7DQogICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdHRpY2tldG1vbmV5Ow0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlIDc6DQogICAgICAgICAgc3RyID0gImlucHV0dGF4dGlja2V0bW9uZXkiOw0KICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXR0YXh0aWNrZXRtb25leTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSA4Og0KICAgICAgICAgIHN0ciA9ICJ0YXhyYXRlIjsNCiAgICAgICAgICBkYXRhID0gdGhpcy5lZGl0dGF4cmF0ZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSA5Og0KICAgICAgICAgIHN0ciA9ICJ1bGxhZ2Vtb25leSI7DQogICAgICAgICAgZGF0YSA9IHRoaXMuZWRpdHVsbGFnZW1vbmV5Ow0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlIDEwOg0KICAgICAgICAgIHN0ciA9ICJyZW1hcmsiOw0KICAgICAgICAgIGRhdGEgPSB0aGlzLmVkaXRyZW1hcms7DQogICAgICAgICAgYnJlYWs7DQogICAgICB9DQogICAgICByZXR1cm4geyBzdHI6IHN0ciwgZGF0YTogZGF0YSB9Ow0KICAgIH0sDQogICAgLy/ovpPlhaXmlbDmja7pqozor4ENCiAgICB2YWxpZGF0ZSgpIHsNCiAgICAgIGlmICh0aGlzLmNvbHVtbnNJbmRleCA9PT0gMTApIHsNCiAgICAgICAgdGhpcy52YWxpZGF0ZVJlbWFyaygpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICBsZXQgdmFsID0gdGhpcy5lbnRlck9wZXJhdGUodGhpcy5jb2x1bW5zSW5kZXgpLmRhdGE7DQogICAgICBpZiAodmFsKSB7DQogICAgICAgIGlmICh0ZXN0TnVtYmVyKHZhbCkpIHsNCiAgICAgICAgICBzd2l0Y2ggKHRoaXMuY29sdW1uc0luZGV4KSB7DQogICAgICAgICAgICBjYXNlIDE6DQogICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVTdGFydGRhdGUoKTsNCiAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICBjYXNlIDI6DQogICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVFbmRkYXRlKCk7DQogICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgY2FzZSAzOg0KICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlUHJldnRvdGFscmVhZGluZ3MoKTsNCiAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICBjYXNlIDQ6DQogICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVDdXJ0b3RhbHJlYWRpbmdzKCk7DQogICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgY2FzZSA1Og0KICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlVHJhbnNmb3JtZXJ1bGxhZ2UoKTsNCiAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICBjYXNlIDY6DQogICAgICAgICAgICAgIHRoaXMudmFsaWRhdGVUaWNrZXRtb25leSgpOw0KICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgIGNhc2UgNzoNCiAgICAgICAgICAgICAgdGhpcy52YWxpZGF0ZVRheHRpY2tldG1vbmV5KCk7DQogICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgY2FzZSA5Og0KICAgICAgICAgICAgICB0aGlzLnZhbGlkYXRlVWxsYWdlbW9uZXkoKTsNCiAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fovpPlhaXmlbDlrZfvvIEiKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy/pqozor4HplJnor6/lvLnlh7rmj5DnpLrmoYblubbot7PovazliLDkuIvkuIDmoLwNCiAgICBlcnJvclRpcHMoc3RyKSB7DQogICAgICB0aGlzLiROb3RpY2UuZXJyb3Ioew0KICAgICAgICB0aXRsZTogIuaPkOekuiIsDQogICAgICAgIGRlc2M6IHN0ciwNCiAgICAgICAgZHVyYXRpb246IDEwLA0KICAgICAgfSk7DQogICAgfSwNCiAgICAvL+i3s+i9rOWIsOS4i+S4gOagvA0KICAgIG5leHRDZWxsKGRhdGEpIHsNCiAgICAgIGxldCBpbmRleCA9IGRhdGEuZWRpdEluZGV4Ow0KICAgICAgbGV0IGNvbHVtbnMgPSBkYXRhLmNvbHVtbnNJbmRleDsNCiAgICAgIGxldCByb3cgPSAiIjsNCiAgICAgIGlmIChpbmRleCA9PT0gLTEgJiYgY29sdW1ucyA9PT0gLTEpIHsNCiAgICAgICAgaW5kZXggPSAwOw0KICAgICAgICBjb2x1bW5zID0gMTsNCiAgICAgIH0gZWxzZSBpZiAoaW5kZXggPiAtMSAmJiBjb2x1bW5zID09PSAxMCkgew0KICAgICAgICAvL+W9k+i3s+i9rOeahOacgOWQjuS4gOihjOacgOWQjuS4gOagvOeahOaXtuWAmQ0KICAgICAgICBpZiAoaW5kZXggPj0gZGF0YS5wYWdlU2l6ZSAtIDEgfHwgaW5kZXggPj0gZGF0YS5wYWdlVG90YWwgLSAxKSB7DQogICAgICAgICAgaW5kZXggPSAwOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGluZGV4Kys7DQogICAgICAgIH0NCiAgICAgICAgY29sdW1ucyA9IDE7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByb3cgPSBkYXRhLmluc2lkZURhdGFbaW5kZXhdOw0KICAgICAgICAvL+aXoOihqOaIluWzsOW5s+iwt+ihqOeahOaXtuWAmQ0KICAgICAgICBpZiAocm93ICYmIChyb3cuaXNGUEcgfHwgcm93LmlzV0IpICYmIGNvbHVtbnMgPj0gMiAmJiBjb2x1bW5zIDw9IDQpIHsNCiAgICAgICAgICBpZiAocm93LmlzV0IpIHsNCiAgICAgICAgICAgIGNvbHVtbnMgKz0gNDsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgY29sdW1ucyArPSAzOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb2x1bW5zICs9IDE7DQogICAgICAgIH0NCiAgICAgICAgLy/mnInkuIvmnJ/nmoTlj7DotKbkuI3og73mlLkNCiAgICAgICAgaWYgKHJvdy5pZk5leHQpIHsNCiAgICAgICAgICBpZiAoY29sdW1ucyA8IDUpIHsNCiAgICAgICAgICAgIGNvbHVtbnMgPSA1Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgZGF0YS5lZGl0SW5kZXggPSBpbmRleDsNCiAgICAgIGRhdGEuY29sdW1uc0luZGV4ID0gY29sdW1uczsNCiAgICAgIHJvdyA9IGRhdGEuaW5zaWRlRGF0YVtpbmRleF07DQogICAgICBpZiAocm93KSB7DQogICAgICAgIGRhdGEuZWRpdFN0YXJ0RGF0ZSA9IHJvdy5zdGFydGRhdGU7DQogICAgICAgIGRhdGEuZWRpdEVuZERhdGUgPSByb3cuZW5kZGF0ZTsNCiAgICAgICAgZGF0YS5lZGl0UHJldnRvdGFscmVhZGluZ3MgPQ0KICAgICAgICAgIHJvdy5wcmV2dG90YWxyZWFkaW5ncyA9PSBudWxsIHx8IHJvdy5wcmV2dG90YWxyZWFkaW5ncyA9PT0gMA0KICAgICAgICAgICAgPyBudWxsDQogICAgICAgICAgICA6IHJvdy5wcmV2dG90YWxyZWFkaW5nczsNCiAgICAgICAgZGF0YS5lZGl0Y3VydG90YWxyZWFkaW5ncyA9DQogICAgICAgICAgcm93LmN1cnRvdGFscmVhZGluZ3MgPT0gbnVsbCB8fCByb3cuY3VydG90YWxyZWFkaW5ncyA9PT0gMA0KICAgICAgICAgICAgPyBudWxsDQogICAgICAgICAgICA6IHJvdy5jdXJ0b3RhbHJlYWRpbmdzOw0KICAgICAgICBkYXRhLmVkaXR0cmFuc2Zvcm1lcnVsbGFnZSA9DQogICAgICAgICAgcm93LnRyYW5zZm9ybWVydWxsYWdlID09IG51bGwgfHwgcm93LnRyYW5zZm9ybWVydWxsYWdlID09PSAwDQogICAgICAgICAgICA/IG51bGwNCiAgICAgICAgICAgIDogcm93LnRyYW5zZm9ybWVydWxsYWdlOw0KICAgICAgICBkYXRhLmVkaXR0YXh0aWNrZXRtb25leSA9DQogICAgICAgICAgcm93LmlucHV0dGF4dGlja2V0bW9uZXkgPT0gbnVsbCB8fCByb3cuaW5wdXR0YXh0aWNrZXRtb25leSA9PT0gMA0KICAgICAgICAgICAgPyBudWxsDQogICAgICAgICAgICA6IHJvdy5pbnB1dHRheHRpY2tldG1vbmV5Ow0KICAgICAgICBkYXRhLmVkaXR0aWNrZXRtb25leSA9DQogICAgICAgICAgcm93LmlucHV0dGlja2V0bW9uZXkgPT0gbnVsbCB8fCByb3cuaW5wdXR0aWNrZXRtb25leSA9PT0gMA0KICAgICAgICAgICAgPyBudWxsDQogICAgICAgICAgICA6IHJvdy5pbnB1dHRpY2tldG1vbmV5Ow0KICAgICAgICBkYXRhLmVkaXR1bGxhZ2Vtb25leSA9DQogICAgICAgICAgcm93LnVsbGFnZW1vbmV5ID09IG51bGwgfHwgcm93LnVsbGFnZW1vbmV5ID09PSAwID8gbnVsbCA6IHJvdy51bGxhZ2Vtb25leTsNCiAgICAgICAgZGF0YS5lZGl0dGF4cmF0ZSA9DQogICAgICAgICAgcm93LnRheHJhdGUgPT0gbnVsbCB8fCByb3cudGF4cmF0ZSA9PT0gMCA/IG51bGwgOiBwYXJzZUludChyb3cudGF4cmF0ZSkgKyAiIjsNCiAgICAgICAgZGF0YS5lZGl0cmVtYXJrID0gcm93LmJ6Ow0KICAgICAgfQ0KDQogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgaWYgKGNvbHVtbnMgIT0gOCkgew0KICAgICAgICAgIGRhdGEuJHJlZnNbZGF0YS5lbnRlck9wZXJhdGUoY29sdW1ucykuc3RyICsgaW5kZXggKyBjb2x1bW5zXS5mb2N1cygpOw0KICAgICAgICB9DQogICAgICB9LCAyMDApOw0KICAgIH0sDQogICAgLy/pqozor4Hotbflp4vml7bpl7QNCiAgICB2YWxpZGF0ZVN0YXJ0ZGF0ZSgpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy5pbnNpZGVEYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRTdGFydERhdGU7DQogICAgICBpZiAodmFsICE9IGRhdGEub2xkX3N0YXJ0ZGF0ZSkgew0KICAgICAgICAvLyDpqozor4Hotbflp4vml7bpl7Tmlrnms5UNCiAgICAgICAgbGV0IHJlc3VsdCA9IF92ZXJpZnlfU3RhcnREYXRlKGRhdGEsIHZhbCwgIuWPr+Wkp+S6jiIpOw0KICAgICAgICBpZiAocmVzdWx0KSB7DQogICAgICAgICAgLy/lpLHotKXlsLHlvLnlh7rmj5DnpLrlhoXlrrnvvIzlubblsIbmlbDmja7mgaLlpI3liJ3lp4vljJYNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcyhyZXN1bHQpOw0KICAgICAgICAgIHRoaXMubXlTdHlsZVt0aGlzLmVkaXRJbmRleF0uc3RhcnRkYXRlID0gImVycm9yU3RsZSI7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5teVN0eWxlW3RoaXMuZWRpdEluZGV4XS5zdGFydGRhdGUgPSAibXlzcGFuIjsNCiAgICAgICAgICB0aGlzLnN0YXJ0TW9kYWwgPSB0cnVlOw0KICAgICAgICB9DQogICAgICB9IGVsc2UgaWYgKHZhbCA9PSBkYXRhLm9sZF9zdGFydGRhdGUpIHsNCiAgICAgICAgZGF0YS5zdGFydGRhdGUgPSB2YWw7DQogICAgICB9DQogICAgfSwNCiAgICAvL+mqjOivgeaIquatouaXtumXtA0KICAgIGFzeW5jIHZhbGlkYXRlRW5kZGF0ZSgpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy5pbnNpZGVEYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRFbmREYXRlOw0KICAgICAgaWYgKHZhbCAhPSBkYXRhLm9sZF9lbmRkYXRlKSB7DQogICAgICAgIC8vIOmqjOivgeaIquatouaXpeacn+aWueazlQ0KICAgICAgICBsZXQgcmVzdWx0ID0gYXdhaXQgdGhpcy5oYW5kbGVFbmREYXRlKGRhdGEsIHZhbCk7DQogICAgICAgIGlmIChyZXN1bHQpIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcyhyZXN1bHQpOw0KICAgICAgICAgIHRoaXMubXlTdHlsZVt0aGlzLmVkaXRJbmRleF0uZW5kZGF0ZSA9ICJlcnJvclN0bGUiOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMubXlTdHlsZVt0aGlzLmVkaXRJbmRleF0uZW5kZGF0ZSA9ICJteXNwYW4iOw0KDQogICAgICAgICAgdGhpcy51cGRhdGVlbmRkYXRlKGRhdGEsIHZhbCk7DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSBpZiAodmFsID09IGRhdGEub2xkX2VuZGRhdGUpIHsNCiAgICAgICAgZGF0YS5lbmRkYXRlID0gdmFsOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy/miKrmraLml6XmnJ/lpITnkIYNCiAgICBhc3luYyBoYW5kbGVFbmREYXRlKGRhdGEsIHZhbCkgew0KICAgICAgLy/nm7TkvpvnlLXmnInkuIrkvKDml6XmnJ/miY3lj6/ku6Xkv67mlLnliLDmnIjlupUgZGlyZWN0c3VwcGx5ZmxhZyAx55u05L6bMui9rOS+mw0KICAgICAgbGV0IGZUeXBlID0gIiI7DQogICAgICBsZXQgY3VydmFsID0gc3RyaW5nVG9EYXRlKHZhbCk7IC8v6L6T5YWl5YC8DQogICAgICBsZXQgbm93ZGF0ZSA9IHN0cmluZ1RvRGF0ZShnZXRDdXJyZW50RGF0ZSgpKTsgLy/lvZPlpKkNCiAgICAgIGlmIChkYXRhLmRpcmVjdHN1cHBseWZsYWcgPT0gMSAmJiBjdXJ2YWwgPiBub3dkYXRlKSB7DQogICAgICAgIGxldCBmaWxlcyA9IGF3YWl0IGF4aW9zDQogICAgICAgICAgLnJlcXVlc3Qoew0KICAgICAgICAgICAgdXJsOiAiL2NvbW1vbi9hdHRhY2htZW50cy9saXN0IiwNCiAgICAgICAgICAgIG1ldGhvZDogInBvc3QiLA0KICAgICAgICAgICAgZGF0YTogew0KICAgICAgICAgICAgICBhcmVhQ29kZTogInNjIiwNCiAgICAgICAgICAgICAgYnVzaUFsaWFzOiAi6ZmE5Lu2KOWPsOi0pikiLA0KICAgICAgICAgICAgICBidXNpSWQ6IGRhdGEucGNpZCArICIiLA0KICAgICAgICAgICAgICBjYXRlZ29yeUNvZGU6ICJmaWxlIiwNCiAgICAgICAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgICAgICAgcGFnZVNpemU6IDIwLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIHJldHVybiByZXMuZGF0YS5yb3dzOw0KICAgICAgICAgIH0pOw0KICAgICAgICBpZiAoZmlsZXMubGVuZ3RoID09IDApIHsNCiAgICAgICAgICByZXR1cm4gIuaIquatouaXpeacn+mcgOWwj+S6juetieS6juW9k+WJjeaXtumXtO+8jOi2hei/h+W9k+WJjeaXtumXtOmcgOS4iuS8oOmZhOS7tiI7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgZlR5cGUgPSAi6ZmQ5Yi25pyf5Y+3IjsgLy/miKrmraLml6XmnJ/kuI3pmZDliLbmnJ/lj7fnmoTmnIDlkI7kuIDlpKnvvIjmnIjlupXvvIkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLy8g6aqM6K+B5oiq5q2i5pel5pyf5pa55rOVDQogICAgICBsZXQgcmVzdWx0ID0gX3ZlcmlmeV9FbmREYXRlKGRhdGEsIHZhbCwgZlR5cGUpOw0KICAgICAgcmV0dXJuIHJlc3VsdDsNCiAgICB9LA0KICAgIHVwZGF0ZWVuZGRhdGUoZGF0YSwgdmFsKSB7DQogICAgICBkYXRhLmVuZGRhdGUgPSB2YWw7DQogICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgIC8v6K6h566X5a6a6aKdDQogICAgICB0aGlzLmdldFF1b3RhKGRhdGEuYW1tZXRlcmlkLCBkYXRhLnN0YXJ0ZGF0ZSwgZGF0YS5lbmRkYXRlLCAocmVzdWx0KSA9PiB7DQogICAgICAgIGlmIChyZXN1bHQuZGF0YS5jb2RlID09PSAwKSB7DQogICAgICAgICAgZGF0YS5xdW90YXJlYWRpbmdzID0gTWF0aC5yb3VuZChyZXN1bHQuZGF0YS5tc2cpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGRhdGEucXVvdGFyZWFkaW5ncyA9IDA7DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5jYWxjdWxhdGVBbGwoZGF0YSk7DQogICAgICB9KTsNCiAgICAgIHRoaXMubm93ZGF0ZWRpZmYgPSBHZXREYXRlRGlmZihkYXRhLnN0YXJ0ZGF0ZSwgZGF0YS5lbmRkYXRlKTsNCiAgICB9LA0KICAgIC8v6aqM6K+B6LW35bqmDQogICAgdmFsaWRhdGVQcmV2dG90YWxyZWFkaW5ncygpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy5pbnNpZGVEYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRQcmV2dG90YWxyZWFkaW5nczsNCiAgICAgIHZhbCA9IHBhcnNlRmxvYXQodmFsKTsNCiAgICAgIGlmICh2YWwgIT0gZGF0YS5vbGRfcHJldnRvdGFscmVhZGluZ3MpIHsNCiAgICAgICAgLy/pqozor4ENCiAgICAgICAgbGV0IHJlc3VsdCA9IF92ZXJpZnlfUHJldlRvdGFsUmVhZGluZ3MoZGF0YSwgdmFsKTsNCiAgICAgICAgaWYgKHJlc3VsdC5zdHJpbmcpIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcyhyZXN1bHQuc3RyaW5nKTsNCiAgICAgICAgICB0aGlzLm15U3R5bGVbdGhpcy5lZGl0SW5kZXhdLnByZXZ0b3RhbHJlYWRpbmdzID0gImVycm9yU3RsZSI7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5teVN0eWxlW3RoaXMuZWRpdEluZGV4XS5wcmV2dG90YWxyZWFkaW5ncyA9ICJteXNwYW4iOw0KICAgICAgICAgIHRoaXMuaWZNYXhkZWdyZWUgPSByZXN1bHQuYjsNCiAgICAgICAgICB0aGlzLnFkTW9kYWwgPSB0cnVlOw0KICAgICAgICB9DQogICAgICB9IGVsc2UgaWYgKHZhbCA9PSBkYXRhLm9sZF9wcmV2dG90YWxyZWFkaW5ncykgew0KICAgICAgICBkYXRhLnByZXZ0b3RhbHJlYWRpbmdzID0gdmFsOw0KICAgICAgICB0aGlzLmNhbGN1bGF0ZUFsbChkYXRhKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8v6aqM6K+B5q2i5bqmDQogICAgdmFsaWRhdGVDdXJ0b3RhbHJlYWRpbmdzKCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLmluc2lkZURhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdGN1cnRvdGFscmVhZGluZ3M7DQoNCiAgICAgIGlmICh2YWwgIT0gZGF0YS5vbGRfY3VydG90YWxyZWFkaW5ncykgew0KICAgICAgICB2YWwgPSBwYXJzZUZsb2F0KHZhbCk7DQogICAgICAgIGxldCByZXN1bHQgPSBfdmVyaWZ5X0N1clRvdGFsUmVhZGluZ3MoZGF0YSwgdmFsKTsNCiAgICAgICAgaWYgKHJlc3VsdC5zdHJpbmcpIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcyhyZXN1bHQuc3RyaW5nKTsNCiAgICAgICAgICB0aGlzLm15U3R5bGVbdGhpcy5lZGl0SW5kZXhdLmN1cnRvdGFscmVhZGluZ3MgPSAiZXJyb3JTdGxlIjsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLm15U3R5bGVbdGhpcy5lZGl0SW5kZXhdLmN1cnRvdGFscmVhZGluZ3MgPSAibXlzcGFuIjsNCg0KICAgICAgICAgIHRoaXMudXBkYXRlQ3VydG90YWxyZWFkaW5ncyhkYXRhLCB2YWwsIHJlc3VsdCk7DQogICAgICAgIH0NCg0KICAgICAgICBsZXQgY29tcHV0cmVhZGluZyA9IDA7DQogICAgICAgIHRoaXMuQWNjb3VudHF1ci5hbW1ldGVyaWQgPSBkYXRhLmFtbWV0ZXJpZDsNCiAgICAgICAgdGhpcy5BY2NvdW50cXVyLnN0YXJ0ZGF0ZSA9IGRhdGEuc3RhcnRkYXRlOw0KICAgICAgICB0aGlzLkFjY291bnRxdXIuZW5kZGF0ZSA9IGRhdGEuZW5kZGF0ZTsNCiAgICAgIH0gZWxzZSBpZiAodmFsID09IGRhdGEub2xkX2N1cnRvdGFscmVhZGluZ3MpIHsNCiAgICAgICAgZGF0YS5jdXJ0b3RhbHJlYWRpbmdzID0gdmFsOw0KDQogICAgICAgIHRoaXMuY2FsY3VsYXRlQWxsKGRhdGEpOw0KICAgICAgfQ0KICAgIH0sDQogICAgdXBkYXRlQ3VydG90YWxyZWFkaW5ncyhkYXRhLCB2YWwsIHJlc3VsdCkgew0KICAgICAgZGF0YS5jdXJ0b3RhbHJlYWRpbmdzID0gdmFsOw0KICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICBsZXQgYiA9IHJlc3VsdC5iOw0KICAgICAgaWYgKGIgPT09IHRydWUpIHsNCiAgICAgICAgdGhpcy5mYk1vZGFsID0gdHJ1ZTsNCiAgICAgIH0gZWxzZSBpZiAoYiA9PT0gZmFsc2UpIHsNCiAgICAgICAgdGhpcy5xeGZiTW9kYWwgPSB0cnVlOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5jYWxjdWxhdGVBbGwoZGF0YSk7DQogICAgICB9DQogICAgICBpZiAoaW5kZXhEYXRhLnZlcnNpb24gPT0gInNjIikgew0KICAgICAgICAvL+mqjOivgeS4iuacn+WPsOi0puaYr+WQpuWujOaIkOaKpei0pg0KICAgICAgICBheGlvcw0KICAgICAgICAgIC5yZXF1ZXN0KHsNCiAgICAgICAgICAgIHVybDogIi9idXNpbmVzcy9hY2NvdW50U0MvdmFsT2xkQWNvdW50IiwNCiAgICAgICAgICAgIG1ldGhvZDogInBvc3QiLA0KICAgICAgICAgICAgcGFyYW1zOiB7IHBjaWQ6IGRhdGEucGNpZCwgYW1tZXRlcmlkOiBkYXRhLmFtbWV0ZXJpZCB9LA0KICAgICAgICAgIH0pDQogICAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgbGV0IG1zZyA9ICIiOw0KICAgICAgICAgICAgaWYgKHJlcy5kYXRhLm1zZykgbXNnID0gcmVzLmRhdGEubXNnOw0KICAgICAgICAgICAgaWYgKGRhdGEuc3RhcnRkYXRlLmVuZHNXaXRoKCIwMTAxIikpDQogICAgICAgICAgICAgIG1zZyArPSAi44CQ6K+l6LW35aeL5pel5pyf5piv6buY6K6k5YC877yM6K+35rOo5oSP5L+u5pS544CRIjsNCiAgICAgICAgICAgIGlmIChtc2cgIT0gIiIpDQogICAgICAgICAgICAgIHRoaXMuJE5vdGljZS53YXJuaW5nKHsNCiAgICAgICAgICAgICAgICB0aXRsZTogIuazqOaEjyIsDQogICAgICAgICAgICAgICAgZGVzYzogIueUteihqC/ljY/orq7jgJAiICsgZGF0YS5hbW1ldGVyY29kZSArICLjgJEiICsgbXNnLA0KICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAxMCwNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICBpZiAocmVzLmRhdGEuYWNjKSB7DQogICAgICAgICAgICAgIE9iamVjdC5hc3NpZ24oZGF0YSwgew0KICAgICAgICAgICAgICAgIHVuaXRwaXJjZW9sZDogcmVzLmRhdGEuYWNjLnVuaXRwaXJjZSwNCiAgICAgICAgICAgICAgICBjdXJ1c2VkcmVhZGluZ3NvbGQ6IHJlcy5kYXRhLmFjYy5jdXJ1c2VkcmVhZGluZ3MsDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy/pqozor4HnlLXmjZ8NCiAgICB2YWxpZGF0ZVRyYW5zZm9ybWVydWxsYWdlKCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLmluc2lkZURhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdHRyYW5zZm9ybWVydWxsYWdlOw0KICAgICAgbGV0IGZsYWcgPSBmYWxzZTsNCiAgICAgIGlmICh2YWwgIT0gZGF0YS5vbGRfdHJhbnNmb3JtZXJ1bGxhZ2UpIHsNCiAgICAgICAgaWYgKA0KICAgICAgICAgIHZhbCAhPSBkYXRhLm9sZF90cmFuc2Zvcm1lcnVsbGFnZSAmJg0KICAgICAgICAgIGluZGV4RGF0YS52ZXJzaW9uID09ICJzYyIgJiYNCiAgICAgICAgICBkYXRhLmN1cnVzZWRyZWFkaW5ncyA+IDAgJiYNCiAgICAgICAgICBwYXJzZUZsb2F0KHBhcnNlRmxvYXQodmFsKSAvIHBhcnNlRmxvYXQoZGF0YS5jdXJ1c2VkcmVhZGluZ3MpKSA+IDAuMSAmJg0KICAgICAgICAgIChkYXRhLmFtbWV0ZXJ1c2UgPT0gMSB8fCBbMSwgM10uaW5jbHVkZXMoZGF0YS5hbW1ldGVydXNlKSkNCiAgICAgICAgKQ0KICAgICAgICAgIGZsYWcgPSB0cnVlOw0KICAgICAgICBpZiAoZmxhZykgew0KICAgICAgICAgIGxldCByZXN1bHQgPSAi55S15o2f5LiO5a6e6ZmF55S16YeP5q+U5YC85bey57uP6LaF6L+HMTAl77yM5LiN5YWB6K645L+d5a2YIjsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcyhyZXN1bHQpOw0KDQogICAgICAgICAgdGhpcy5teVN0eWxlW3RoaXMuZWRpdEluZGV4XS50cmFuc2Zvcm1lcnVsbGFnZSA9ICJlcnJvclN0bGUiOw0KICAgICAgICAgIGRhdGEudHJhbnNmb3JtZXJ1bGxhZ2UgPSAwOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHZhbCA9IHBhcnNlRmxvYXQodmFsKTsNCiAgICAgICAgICBkYXRhLnRyYW5zZm9ybWVydWxsYWdlID0gdmFsOw0KICAgICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICAgIHRoaXMuY2FsY3VsYXRlQWxsKGRhdGEpOw0KICAgICAgICB9DQogICAgICB9IGVsc2UgaWYgKHZhbCA9PSBkYXRhLm9sZF90cmFuc2Zvcm1lcnVsbGFnZSkgew0KICAgICAgICBkYXRhLnRyYW5zZm9ybWVydWxsYWdlID0gdmFsOw0KICAgICAgICB0aGlzLmNhbGN1bGF0ZUFsbChkYXRhKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8v6aqM6K+B5LiT56WoDQogICAgdmFsaWRhdGVUYXh0aWNrZXRtb25leSgpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy5pbnNpZGVEYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXR0YXh0aWNrZXRtb25leTsNCiAgICAgIGlmICh2YWwgIT0gZGF0YS5vbGRfdGF4dGlja2V0bW9uZXkpIHsNCiAgICAgICAgdmFsID0gcGFyc2VGbG9hdCh2YWwpOw0KICAgICAgICBkYXRhLmlucHV0dGF4dGlja2V0bW9uZXkgPSBfdmVyaWZ5X01vbmV5KGRhdGEsIHZhbCk7DQogICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICBkYXRhLnRheHRpY2tldG1vbmV5ID0gY2FsY3VsYXRlQWN0dWFsTW9uZXkoZGF0YSwgdmFsKTsNCiAgICAgICAgZGF0YS50YXhhbW91bnQgPSBjb3VudFRheGFtb3VudChkYXRhKTsNCiAgICAgICAgdGhpcy5jYWxjdWxhdGVBbGwoZGF0YSk7DQogICAgICB9IGVsc2UgaWYgKHZhbCA9PSBkYXRhLm9sZF90YXh0aWNrZXRtb25leSkgew0KICAgICAgICBkYXRhLmlucHV0dGF4dGlja2V0bW9uZXkgPSB2YWw7DQogICAgICAgIGRhdGEudGF4dGlja2V0bW9uZXkgPSBjYWxjdWxhdGVBY3R1YWxNb25leShkYXRhLCB2YWwpOw0KICAgICAgICBkYXRhLnRheGFtb3VudCA9IGNvdW50VGF4YW1vdW50KGRhdGEpOw0KICAgICAgICB0aGlzLmNhbGN1bGF0ZUFsbChkYXRhKTsNCiAgICAgIH0NCiAgICAgIHRoaXMudmFsaWRhdGVVbml0UHJpY2UoZGF0YSk7DQogICAgICB0aGlzLnZhbGlkYXRlYXZlbW9uZXkoZGF0YSk7DQogICAgfSwNCiAgICAvL+mqjOivgeaZruelqA0KICAgIHZhbGlkYXRlVGlja2V0bW9uZXkoKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMuaW5zaWRlRGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICBsZXQgdmFsID0gdGhpcy5lZGl0dGlja2V0bW9uZXk7DQogICAgICBpZiAodmFsICE9IGRhdGEub2xkX3RpY2tldG1vbmV5KSB7DQogICAgICAgIHZhbCA9IHBhcnNlRmxvYXQodmFsKTsNCiAgICAgICAgZGF0YS5pbnB1dHRpY2tldG1vbmV5ID0gX3ZlcmlmeV9Nb25leShkYXRhLCB2YWwpOw0KICAgICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgICAgZGF0YS50aWNrZXRtb25leSA9IGNhbGN1bGF0ZUFjdHVhbE1vbmV5KGRhdGEsIHZhbCk7DQogICAgICAgIHRoaXMuY2FsY3VsYXRlQWxsKGRhdGEpOw0KICAgICAgfSBlbHNlIGlmICh2YWwgPT0gZGF0YS5vbGRfdGlja2V0bW9uZXkpIHsNCiAgICAgICAgZGF0YS5pbnB1dHRpY2tldG1vbmV5ID0gdmFsOw0KICAgICAgICBkYXRhLnRpY2tldG1vbmV5ID0gY2FsY3VsYXRlQWN0dWFsTW9uZXkoZGF0YSwgdmFsKTsNCiAgICAgICAgdGhpcy5jYWxjdWxhdGVBbGwoZGF0YSk7DQogICAgICB9DQogICAgICB0aGlzLnZhbGlkYXRlVW5pdFByaWNlKGRhdGEpOw0KICAgICAgdGhpcy52YWxpZGF0ZWF2ZW1vbmV5KGRhdGEpOw0KICAgIH0sDQogICAgLy/pqozor4Hlhbbku5botLnnlKgNCiAgICB2YWxpZGF0ZVVsbGFnZW1vbmV5KCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLmluc2lkZURhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdHVsbGFnZW1vbmV5Ow0KICAgICAgaWYgKHZhbCAhPSBkYXRhLm9sZF91bGxhZ2Vtb25leSkgew0KICAgICAgICB2YWwgPSBwYXJzZUZsb2F0KHZhbCk7DQogICAgICAgIGRhdGEudWxsYWdlbW9uZXkgPSBfdmVyaWZ5X01vbmV5KGRhdGEsIHZhbCk7DQogICAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgICAgICB0aGlzLmNhbGN1bGF0ZUFsbChkYXRhKTsNCiAgICAgIH0gZWxzZSBpZiAodmFsID09IGRhdGEub2xkX3VsbGFnZW1vbmV5KSB7DQogICAgICAgIGRhdGEudWxsYWdlbW9uZXkgPSB2YWw7DQogICAgICAgIHRoaXMuY2FsY3VsYXRlQWxsKGRhdGEpOw0KICAgICAgfQ0KICAgICAgdGhpcy52YWxpZGF0ZVVuaXRQcmljZShkYXRhKTsNCiAgICAgIHRoaXMudmFsaWRhdGVhdmVtb25leShkYXRhKTsNCiAgICB9LA0KICAgIC8v5aSH5rOoDQogICAgdmFsaWRhdGVSZW1hcmsoKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMuaW5zaWRlRGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICBsZXQgdmFsID0gdGhpcy5lZGl0cmVtYXJrOw0KICAgICAgZGF0YS5ieiA9IHZhbDsNCiAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgIH0sDQogICAgdmFsaWRhdGVhdmVtb25leShkYXRhKSB7DQogICAgICBsZXQgdmVyc2lvbiA9IGluZGV4RGF0YS52ZXJzaW9uOw0KICAgICAgaWYgKCJzYyIgPT0gdmVyc2lvbikgew0KICAgICAgICBsZXQgYWNjb3VudG1vbmV5ID0gZGF0YS5hY2NvdW50bW9uZXk7DQogICAgICAgIGxldCBhdmVvbGQgPSBqdWRnZU51bWJlcihkYXRhLmF2ZWFjY291bnRtb25leW9sZCk7DQogICAgICAgIGlmICh0aGlzLm5vd2RhdGVkaWZmID09IDApDQogICAgICAgICAgdGhpcy5ub3dkYXRlZGlmZiA9IEdldERhdGVEaWZmKGRhdGEuc3RhcnRkYXRlLCBkYXRhLmVuZGRhdGUpOw0KICAgICAgICBpZiAoYXZlb2xkICE9IDAgJiYgKGFjY291bnRtb25leSAvIHRoaXMubm93ZGF0ZWRpZmYpLnRvRml4ZWQoMikgLSBhdmVvbGQgPiAwKSB7DQogICAgICAgICAgaWYgKCgoYWNjb3VudG1vbmV5IC8gdGhpcy5ub3dkYXRlZGlmZikudG9GaXhlZCgyKSAtIGF2ZW9sZCkgLyBhdmVvbGQgPiAwLjMpDQogICAgICAgICAgICB0aGlzLiROb3RpY2Uud2FybmluZyh7DQogICAgICAgICAgICAgIHRpdGxlOiAi5rip6aao5o+Q56S6IiwNCiAgICAgICAgICAgICAgZGVzYzoNCiAgICAgICAgICAgICAgICAi55S16KGoL+WNj+iuruOAkCIgKw0KICAgICAgICAgICAgICAgIGRhdGEuYW1tZXRlcmNvZGUgKw0KICAgICAgICAgICAgICAgICLjgJEiICsNCiAgICAgICAgICAgICAgICAi5pel5Z2H55S16LS5546v5q+U5YC85bey57uP6LaF6L+HMzAl77yM6K+35rOo5oSP5aGr5YaZ5aSH5rOo6K+05piO77yBIiwNCiAgICAgICAgICAgICAgZHVyYXRpb246IDEwLA0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8v6aqM6K+B5Y2V5Lu3DQogICAgdmFsaWRhdGVVbml0UHJpY2UoZGF0YSkgew0KICAgICAgbGV0IHZlcnNpb24gPSBpbmRleERhdGEudmVyc2lvbjsNCiAgICAgIGxldCBjYXRlZ29yeSA9IGRhdGEuY2F0ZWdvcnk7IC8v55S16KGo5o+P6L+w57G75Z6LDQogICAgICBsZXQgZGlyZWN0c3VwcGx5ZmxhZyA9IGRhdGEuZGlyZWN0c3VwcGx5ZmxhZzsgLy8x55u05L6bMui9rOS+mw0KICAgICAgbGV0IGFtbWV0ZXJ1c2UgPSBkYXRhLmFtbWV0ZXJ1c2U7IC8v55S16KGo55So6YCUDQogICAgICBsZXQgdW5pdHBpcmNlID0gZGF0YS51bml0cGlyY2U7IC8v5Y+w6LSm5Y2V5Lu3DQogICAgICBpZiAoIWp1ZGdlX25lZ2F0ZShjYXRlZ29yeSkgJiYgIWp1ZGdlX3JlY292ZXJ5KGFtbWV0ZXJ1c2UpICYmIGp1ZGdlX3liKGNhdGVnb3J5KSkgew0KICAgICAgICBpZiAodW5pdHBpcmNlKSB7DQogICAgICAgICAgaWYgKHVuaXRwaXJjZSAhPSBudWxsICYmIHVuaXRwaXJjZSA8IHVuaXRwaXJjZU1heDEpIHsNCiAgICAgICAgICAgIC8vIGlmICh1bml0cGlyY2UgPCB1bml0cGlyY2VNaW4gfHwgdW5pdHBpcmNlID4gdW5pdHBpcmNlTWF4KSB7DQogICAgICAgICAgICB0aGlzLmVycm9yVGlwcygNCiAgICAgICAgICAgICAgIuWNleS7t+iMg+WbtOW/hemhu+Wkp+S6jjAuMeWFg++8jOatpOWPsOi0puWNleS7tzogIiArIHVuaXRwaXJjZSArICLkuI3lnKjojIPlm7TlhoXvvIzor7fnoa7orqTvvIEiDQogICAgICAgICAgICApOw0KICAgICAgICAgIH0NCiAgICAgICAgICAvLyBpZiAoZGlyZWN0c3VwcGx5ZmxhZyA9PSAxICYmIGFtbWV0ZXJ1c2UgPT0gMSkgew0KICAgICAgICAgIC8vICAgaWYgKHVuaXRwaXJjZSkgew0KICAgICAgICAgIC8vICAgICBpZiAodW5pdHBpcmNlID49IDAuMjUgJiYgdW5pdHBpcmNlIDwgMC41KSB7DQogICAgICAgICAgLy8gICAgICAgdGhpcy5lcnJvclRpcHMoDQogICAgICAgICAgLy8gICAgICAgICAi55u05L6b55S15Y2V5Lu3KCIgKw0KICAgICAgICAgIC8vICAgICAgICAgICB1bml0cGlyY2UgKw0KICAgICAgICAgIC8vICAgICAgICAgICAiKeOAkDAuMjU8PSIgKw0KICAgICAgICAgIC8vICAgICAgICAgICB1bml0cGlyY2UgKw0KICAgICAgICAgIC8vICAgICAgICAgICAiPDAuNeOAkSIgKw0KICAgICAgICAgIC8vICAgICAgICAgICAi6K+356Gu6K6k5Y2V5Lu35piv5ZCm5a2Y5Zyo6ZSZ6K+vIg0KICAgICAgICAgIC8vICAgICAgICk7DQogICAgICAgICAgLy8gICAgIH0gZWxzZSBpZiAodW5pdHBpcmNlIDwgMC4yNSB8fCB1bml0cGlyY2UgPiAxLjIpIHsNCiAgICAgICAgICAvLyAgICAgICB0aGlzLmVycm9yVGlwcygNCiAgICAgICAgICAvLyAgICAgICAgICLnm7TkvpvnlLXljZXku7coIiArIHVuaXRwaXJjZSArICIp44CQ5bCP5LqOMC4yNeaIluWkp+S6jjEuMjDjgJEiICsgIuWNleS7t++8jOivt+ehruiupO+8gSINCiAgICAgICAgICAvLyAgICAgICApOw0KICAgICAgICAgIC8vICAgICB9DQogICAgICAgICAgLy8gICB9DQogICAgICAgICAgLy8gfSBlbHNlIGlmIChkaXJlY3RzdXBwbHlmbGFnID09IDIpIHsNCiAgICAgICAgICAvLyAgIGlmICh1bml0cGlyY2UgPj0gMC4zICYmIHVuaXRwaXJjZSA8IDAuNikgew0KICAgICAgICAgIC8vICAgICB0aGlzLmVycm9yVGlwcygNCiAgICAgICAgICAvLyAgICAgICAi6L2s5L6b55S15Y2V5Lu3KCIgKw0KICAgICAgICAgIC8vICAgICAgICAgdW5pdHBpcmNlICsNCiAgICAgICAgICAvLyAgICAgICAgICIp44CQMC4zPD0iICsNCiAgICAgICAgICAvLyAgICAgICAgIHVuaXRwaXJjZSArDQogICAgICAgICAgLy8gICAgICAgICAiPDAuNuOAkSIgKw0KICAgICAgICAgIC8vICAgICAgICAgIuivt+ehruiupOWNleS7t+aYr+WQpuWtmOWcqOmUmeivryINCiAgICAgICAgICAvLyAgICAgKTsNCiAgICAgICAgICAvLyAgIH0gZWxzZSBpZiAodW5pdHBpcmNlIDwgMC4zKSB7DQogICAgICAgICAgLy8gICAgIHRoaXMuZXJyb3JUaXBzKA0KICAgICAgICAgIC8vICAgICAgICLovazkvpvnlLXljZXku7coIiArDQogICAgICAgICAgLy8gICAgICAgICB1bml0cGlyY2UgKw0KICAgICAgICAgIC8vICAgICAgICAgIinjgJAiICsNCiAgICAgICAgICAvLyAgICAgICAgIHVuaXRwaXJjZSArDQogICAgICAgICAgLy8gICAgICAgICAiPDAuM+OAkSIgKw0KICAgICAgICAgIC8vICAgICAgICAgIuWNleS7t+mUmeivr++8jOivt+ehruiupO+8gSINCiAgICAgICAgICAvLyAgICAgKTsNCiAgICAgICAgICAvLyAgIH0gZWxzZSBpZiAodW5pdHBpcmNlID4gMS41KSB7DQogICAgICAgICAgLy8gICAgIHRoaXMuZXJyb3JUaXBzKA0KICAgICAgICAgIC8vICAgICAgICLovazkvpvnlLXljZXku7coIiArDQogICAgICAgICAgLy8gICAgICAgICB1bml0cGlyY2UgKw0KICAgICAgICAgIC8vICAgICAgICAgIinjgJAiICsNCiAgICAgICAgICAvLyAgICAgICAgIHVuaXRwaXJjZSArDQogICAgICAgICAgLy8gICAgICAgICAiPjEuNeOAkSIgKw0KICAgICAgICAgIC8vICAgICAgICAgIuivt+ehruiupOWNleS7t+aYr+WQpuWtmOWcqOmUmeivryINCiAgICAgICAgICAvLyAgICAgKTsNCiAgICAgICAgICAvLyAgIH0NCiAgICAgICAgICAvLyB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIG9wZW5Nb2RhbChpbmRleCkgew0KICAgICAgdGhpcy5tZXRlck1vZGFsID0gdHJ1ZTsgLy/lvLnlh7rmoYbmmL7npLoNCiAgICAgIGxldCByb3cgPSB0aGlzLmluc2lkZURhdGFbaW5kZXhdOw0KICAgICAgaWYgKHJvdy5hY2NvdW50bm8gPT0gZGF0ZXNbMV0uY29kZSkgew0KICAgICAgICBsZXQgb2JqID0gew0KICAgICAgICAgIGFjY291bnRubzogZGF0ZXNbMF0uY29kZSwNCiAgICAgICAgICBhbW1ldGVyaWQ6IHJvdy5hbW1ldGVyaWQsDQogICAgICAgIH07DQogICAgICAgIHNlbGVjdEJ5QW1tZXRlcklkKG9iaikudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgcm93Lm5leHREYXRhID0gcmVzLmRhdGE7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgaWYgKHJvdykgew0KICAgICAgICBpZiAocm93LmlmTmV4dCkgew0KICAgICAgICAgIHRoaXMucmVhZG9ubHkgPSB0cnVlOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMucmVhZG9ubHkgPSBmYWxzZTsNCiAgICAgICAgfQ0KDQogICAgICAgIHRoaXMuY3VycmVudFJvdyA9IHJvdzsgLy/nu5nlvLnlh7rmoYbnu5HlrprmlbDmja4NCiAgICAgICAgdGhpcy5lZGl0cHJldmhpZ2hyZWFkaW5ncyA9DQogICAgICAgICAgcm93LnByZXZoaWdocmVhZGluZ3MgPT09IG51bGwgPyAwIDogcm93LnByZXZoaWdocmVhZGluZ3M7DQogICAgICAgIHRoaXMuZWRpdHByZXZmbGF0cmVhZGluZ3MgPQ0KICAgICAgICAgIHJvdy5wcmV2ZmxhdHJlYWRpbmdzID09PSBudWxsID8gMCA6IHJvdy5wcmV2ZmxhdHJlYWRpbmdzOw0KICAgICAgICB0aGlzLmVkaXRwcmV2bG93cmVhZGluZ3MgPSByb3cucHJldmxvd3JlYWRpbmdzID09PSBudWxsID8gMCA6IHJvdy5wcmV2bG93cmVhZGluZ3M7DQogICAgICAgIHRoaXMuZWRpdGN1cmhpZ2hyZWFkaW5ncyA9IHJvdy5jdXJoaWdocmVhZGluZ3MgPT09IG51bGwgPyAwIDogcm93LmN1cmhpZ2hyZWFkaW5nczsNCiAgICAgICAgdGhpcy5lZGl0Y3VyZmxhdHJlYWRpbmdzID0gcm93LmN1cmZsYXRyZWFkaW5ncyA9PT0gbnVsbCA/IDAgOiByb3cuY3VyZmxhdHJlYWRpbmdzOw0KICAgICAgICB0aGlzLmVkaXRjdXJsb3dyZWFkaW5ncyA9IHJvdy5jdXJsb3dyZWFkaW5ncyA9PT0gbnVsbCA/IDAgOiByb3cuY3VybG93cmVhZGluZ3M7DQogICAgICAgIGlmICh0aGlzLnZlcnNpb24gPT0gInNjIikgew0KICAgICAgICAgIHRoaXMuZWRpdGhpZ2hyZWFkaW5ncyA9IHJvdy5oaWdocmVhZGluZ3MgPT09IG51bGwgPyAwIDogcm93LmhpZ2hyZWFkaW5nczsNCiAgICAgICAgICB0aGlzLmVkaXRmbGF0cmVhZGluZ3MgPSByb3cuZmxhdHJlYWRpbmdzID09PSBudWxsID8gMCA6IHJvdy5mbGF0cmVhZGluZ3M7DQogICAgICAgICAgdGhpcy5lZGl0bG93cmVhZGluZ3MgPSByb3cubG93cmVhZGluZ3MgPT09IG51bGwgPyAwIDogcm93Lmxvd3JlYWRpbmdzOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBvcGVuQWRkQmlsbFBlck1vZGFsKG5hbWUpIHsNCiAgICAgIHRoaXMubmFtZSA9IG5hbWU7DQogICAgICBpZiAobmFtZSA9PT0gImN1cnJlbnQiKSB7DQogICAgICAgIC8v6ZyA6KaB56i95qC4DQogICAgICAgIC8vIGlmICh0aGlzLmhhc0J1dHRvblBlcm0oImpoc2QiKSkgew0KICAgICAgICAvLyAgIHRoaXMuYWRkUHJlc2VydmVHSigpOw0KICAgICAgICAvLyB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNlbGVjdGVkQWNjb3VudCgpOw0KICAgICAgICAvLyB9DQogICAgICB9IGVsc2UgaWYgKG5hbWUgPT09ICJhbGwiKSB7DQogICAgICAgIC8v6ZyA6KaB56i95qC4DQogICAgICAgIC8vIGlmICh0aGlzLmhhc0J1dHRvblBlcm0oImpoc2QiKSkgew0KICAgICAgICAvLyAgIHRoaXMuYWRkUHJlc2VydmVHSkFsbCgpOw0KICAgICAgICAvLyB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNlbGVjdGVkQWxsQWNjb3VudCgpOw0KICAgICAgICAvLyB9DQogICAgICB9DQogICAgfSwNCiAgICAvL+WKoOWFpeW9kumbhuWNle+8jOWFqOmDqOacieaViOWPsOi0pg0KICAgIHNlbGVjdGVkQWxsQWNjb3VudCgpIHsNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIHRoYXQuc2V0RWxlY3Ryb3lUeXBlKCk7DQogICAgICB0aGF0LnNwaW5TaG93ID0gdHJ1ZTsNCiAgICAgIHNlbGVjdElkc0J5UGFyYW1zKHRoaXMuYWNjb3VudE9iaikudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoYXQuc3BpblNob3cgPSBmYWxzZTsNCiAgICAgICAgaWYgKHJlcy5kYXRhLnN0cikgew0KICAgICAgICAgIHRoYXQuJE5vdGljZS53YXJuaW5nKHsNCiAgICAgICAgICAgIHRpdGxlOiAi5rOo5oSPIiwNCiAgICAgICAgICAgIGRlc2M6IHJlcy5kYXRhLnN0ciwNCiAgICAgICAgICAgIGR1cmF0aW9uOiAwLA0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICAgIGlmIChyZXMuZGF0YS5pZHMpIHsNCiAgICAgICAgICBpZiAocmVzLmRhdGEuaWRzLmxlbmd0aCA9PSAwKSB7DQogICAgICAgICAgICB0aGF0LmVycm9yVGlwcygi5peg5pyJ5pWI5pWw5o2u5Y+v5Yqg5YWl5b2S6ZuG5Y2VIik7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoYXQuJHJlZnMuYWRkQmlsbFBlci5pbml0QW1tZXRlcigNCiAgICAgICAgICAgICAgcmVzLmRhdGEuaWRzLA0KICAgICAgICAgICAgICAvLyB0aGlzLiRyZWZzLnNob3dBbGFybU1vZGVsLnNlbGVjdElkczEsDQogICAgICAgICAgICAgIDEsDQogICAgICAgICAgICAgIHRoaXMuYWNjb3VudE9iai5jb3VudHJ5DQogICAgICAgICAgICApOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGF0LmVycm9yVGlwcygi5peg5pyJ5pWI5pWw5o2u5Y+v5Yqg5YWl5b2S6ZuG5Y2VIik7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy/liqDlhaXlvZLpm4bljZUNCiAgICBhZGRQcmVzZXJ2ZUdKKCkgew0KICAgICAgbGV0IGRhdGFMID0gdGhpcy4kcmVmcy5hY2NvdW50VGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICBpZiAoZGF0YUwgPT0gbnVsbCB8fCBkYXRhTC5sZW5ndGggPT0gMCkgew0KICAgICAgICB0aGlzLmVycm9yVGlwcygi6K+36YCJ5oup6KaB5Yqg5YWl5b2S6ZuG5Y2V55qE5Y+w6LSmIik7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmFkZFN1Ym1pdERhdGFHSihkYXRhTCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8v5Yqg5YWl5b2S6ZuG5Y2VDQogICAgYWRkUHJlc2VydmVHSkFsbCgpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB0aGlzLmFjY291bnRPYmo7DQogICAgICBwYXJhbXMucGFnZU51bSA9IDE7DQogICAgICBwYXJhbXMucGFnZVNpemUgPSAyMDAwMDsNCiAgICAgIGxldCByZXEgPSB7DQogICAgICAgIHVybDogIi9idXNpbmVzcy9hY2NvdW50L3NlbGZBY2NvdW50TGlzdCIsDQogICAgICAgIG1ldGhvZDogImdldCIsDQogICAgICAgIHBhcmFtczogcGFyYW1zLA0KICAgICAgfTsNCiAgICAgIGxldCBhcnJheSA9IFtdOw0KICAgICAgdGhpcy5hY2NvdW50VGIubG9hZGluZyA9IHRydWU7DQogICAgICBheGlvcy5yZXF1ZXN0KHJlcSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMuYWNjb3VudFRiLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgbGV0IGRhdGFMID0gcmVzLmRhdGEucm93czsNCiAgICAgICAgdGhpcy5hZGRTdWJtaXREYXRhR0ooZGF0YUwpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvL+aPkOS6pOW9kumbhuWNleaVsOaNrg0KICAgIGFkZFN1Ym1pdERhdGFHSihkYXRhMSkgew0KICAgICAgbGV0IGEgPSBbXTsNCiAgICAgIGxldCBiID0gMTsNCiAgICAgIGxldCBkYXRhID0gZGF0YTEuZmlsdGVyKChpdGVtKSA9PiBpdGVtLmVmZmVjdGl2ZSA9PSAxKTsNCiAgICAgIGxldCBzdHIgPSAiIjsNCiAgICAgIGxldCBzdHIxID0gIiI7DQogICAgICBsZXQgdmVyc2lvbiA9IGluZGV4RGF0YS52ZXJzaW9uOw0KICAgICAgaWYgKGRhdGEgIT0gbnVsbCAmJiBkYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbGV0IG51bWJlciA9IDA7DQogICAgICAgIGxldCBzdWJtaXREYXRhID0gW107DQogICAgICAgIGxldCBubyA9IHRoaXMuYWNjb3VudE9iai5hY2NvdW50bm87DQogICAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgbGV0IG9iaiA9IHJlcXVpcmVkRmllbGRWYWxpZGF0b3IoaXRlbSk7DQogICAgICAgICAgaWYgKG9iai5yZXN1bHQpIHsNCiAgICAgICAgICAgIGxldCB5eXl5bW1kZCA9IGN1dERhdGVfeXl5eW1tZGQoaXRlbS5zdGFydGRhdGUpOw0KICAgICAgICAgICAgaXRlbS5zdGFydHllYXIgPSB5eXl5bW1kZC55eXl5Ow0KICAgICAgICAgICAgaXRlbS5zdGFydG1vbnRoID0geXl5eW1tZGQubW07DQogICAgICAgICAgICB5eXl5bW1kZCA9IGN1dERhdGVfeXl5eW1tZGQoaXRlbS5lbmRkYXRlKTsNCiAgICAgICAgICAgIGl0ZW0uZW5keWVhciA9IHl5eXltbWRkLnl5eXk7DQogICAgICAgICAgICBpdGVtLmVuZG1vbnRoID0geXl5eW1tZGQubW07DQogICAgICAgICAgICBhLnB1c2goaXRlbS5hbW1ldGVyaWQpOw0KICAgICAgICAgICAgc3VibWl0RGF0YS5wdXNoKGl0ZW0pOw0KICAgICAgICAgICAgbnVtYmVyKys7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHN0ciArPQ0KICAgICAgICAgICAgICAi55S16KGoL+WNj+iurue8luWPt+S4uuOAkCIgKw0KICAgICAgICAgICAgICBpdGVtLmFtbWV0ZXJjb2RlICsNCiAgICAgICAgICAgICAgIuOAkeeahOWPsOi0pumqjOivgeayoeaciemAmui/h++8muOAkCIgKw0KICAgICAgICAgICAgICBvYmouc3RyICsNCiAgICAgICAgICAgICAgIuOAke+8myI7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgaWYgKGl0ZW0ubWFnbmlmaWNhdGlvbmVyciA9PSAyKSB7DQogICAgICAgICAgICBzdHIxICs9DQogICAgICAgICAgICAgICLnlLXooagv5Y2P6K6u57yW5Y+35Li644CQIiArDQogICAgICAgICAgICAgIGl0ZW0uYW1tZXRlcmNvZGUgKw0KICAgICAgICAgICAgICAi44CR55qE5Y+w6LSm5YCN546H44CQIiArDQogICAgICAgICAgICAgIGl0ZW0ubWFnbmlmaWNhdGlvbiArDQogICAgICAgICAgICAgICLjgJHkuI7nlLXooajlgI3njofjgJAiICsNCiAgICAgICAgICAgICAgaXRlbS5hbW1tdWx0dGltZXMgKw0KICAgICAgICAgICAgICAi44CR5LiN5LiA6Ie077yBICA8YnIgLz4gIjsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBpZiAoaXRlbS5wZXJjZW50ZXJyID09IDIpIHsNCiAgICAgICAgICAgIHN0cjEgKz0NCiAgICAgICAgICAgICAgIueUteihqC/ljY/orq7nvJblj7fkuLrjgJAiICsNCiAgICAgICAgICAgICAgaXRlbS5hbW1ldGVyY29kZSArDQogICAgICAgICAgICAgICLjgJHnmoTlj7DotKbliIblibLmr5TkvovjgJAiICsNCiAgICAgICAgICAgICAgaXRlbS5wZXJjZW50ICsNCiAgICAgICAgICAgICAgIuOAkeS4jueUteihqOWIhuWJsuavlOS+i+OAkCIgKw0KICAgICAgICAgICAgICBpdGVtLmFtbXBlcmNlbnQgKw0KICAgICAgICAgICAgICAi44CR5LiN5LiA6Ie077yBIDxiciAvPiAiOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoaXRlbS5lZmZlY3RpdmUgIT0gMSkgew0KICAgICAgICAgICAgYiA9IDI7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChpdGVtLnN0YXR1cyAhPSAxKSB7DQogICAgICAgICAgICBiID0gMzsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKA0KICAgICAgICAgICAgInNjIiA9PSB2ZXJzaW9uICYmDQogICAgICAgICAgICBpdGVtLnVuaXRwaXJjZSA+IDIgJiYNCiAgICAgICAgICAgIChpdGVtLnVuaXRwaXJjZW9sZCA9PSBudWxsIHx8IGl0ZW0udW5pdHBpcmNlb2xkIDwgMikgJiYNCiAgICAgICAgICAgIHRoYXQudmFsaXByaWNlDQogICAgICAgICAgKSB7DQogICAgICAgICAgICBiID0gNDsNCiAgICAgICAgICAgIHN0ciArPSBpdGVtLmFtbWV0ZXJjb2RlICsgIiwiOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICAgIGlmIChiID09IDEpIHsNCiAgICAgICAgICBpZiAoc3RyMS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICB0aGlzLiROb3RpY2Uud2FybmluZyh7DQogICAgICAgICAgICAgIHRpdGxlOiAi5rOo5oSPIiwNCiAgICAgICAgICAgICAgZGVzYzogc3RyMSwNCiAgICAgICAgICAgICAgZHVyYXRpb246IDAsDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHN1Ym1pdERhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy5zdWJtaXQgPSBzdWJtaXREYXRhOw0KICAgICAgICAgICAgdGhpcy5zdWJtaXQyID0gc3VibWl0RGF0YTsNCiAgICAgICAgICAgIHRoaXMucHJlc2VydmVTYygpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIGlmIChiID09PSAyKSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoIumAieS4reeahOWPsOi0puS4reWtmOWcqOS4tOaXtuaVsOaNru+8jOivt+WFiOS/neWtmOWGjeWKoOWFpeW9kumbhuWNle+8gSIpOw0KICAgICAgICB9IGVsc2UgaWYgKGIgPT09IDMpIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6YCA5Zue55qE5Y+w6LSm5LiN6IO95Yqg5YWl5YW25a6D5b2S6ZuG5Y2V77yM6K+354K55Ye7W+mHjeaWsOWKoOWFpeW9kumbhuWNlV3mjInpkq4iKTsNCiAgICAgICAgfSBlbHNlIGlmIChiID09PSA0KSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoDQogICAgICAgICAgICAi55S16KGoL+WNj+iurue8luWPt+S4uuOAkCIgKw0KICAgICAgICAgICAgICBzdHIgKw0KICAgICAgICAgICAgICAi44CR55qE5Y+w6LSm5Y2V5Lu35bey57uP6LaF6L+HMuWFg++8jOivt+WPkU9B6YKu5Lu257uZ55yB5YWs5Y+45a6h5qC477yM6YCa6L+H5ZCO5omN5Y+v5Yqg5YWl5b2S6ZuG5Y2V77yBIg0KICAgICAgICAgICk7DQogICAgICAgIH0NCiAgICAgICAgdGhhdC5hbW1ldGVyaWRzID0gYTsNCiAgICAgICAgaWYgKHN0ci5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhhdC5lcnJvclRpcHMoc3RyKTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoc3RyMS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhhdC4kTm90aWNlLndhcm5pbmcoew0KICAgICAgICAgICAgdGl0bGU6ICLms6jmhI8iLA0KICAgICAgICAgICAgZGVzYzogc3RyMSwNCiAgICAgICAgICAgIGR1cmF0aW9uOiAwLA0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICAvL+WKoOWFpeW9kumbhuWNle+8jOW3sumAieaLqeeahOWPsOi0pg0KICAgIHNlbGVjdGVkQWNjb3VudCgpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy4kcmVmcy5hY2NvdW50VGFibGUuZ2V0U2VsZWN0aW9uKCk7DQogICAgICBsZXQgYiA9IDE7DQogICAgICBpZiAoZGF0YSA9PSBudWxsIHx8IGRhdGEubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy5lcnJvclRpcHMoIuivt+mAieaLqeimgeWKoOWFpeW9kumbhuWNleeahOWPsOi0piIpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGV0IGlkcyA9IFtdOw0KICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICBpZiAoaXRlbS5lZmZlY3RpdmUgIT0gMSkgew0KICAgICAgICAgICAgYiA9IDI7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChpdGVtLnN0YXR1cyA9PT0gNSkgew0KICAgICAgICAgICAgYiA9IDM7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChpdGVtLnN0YXR1cyA9PT0gNCkgew0KICAgICAgICAgICAgYiA9IDQ7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlkcy5wdXNoKGl0ZW0ucGNpZCk7DQogICAgICAgIH0pOw0KICAgICAgICBpZiAoYiA9PT0gMSkgew0KICAgICAgICAgIHRoaXMuJHJlZnMuYWRkQmlsbFBlci5pbml0QW1tZXRlcihpZHMsIDEsIHRoaXMuYWNjb3VudE9iai5jb3VudHJ5KTsNCiAgICAgICAgfSBlbHNlIGlmIChiID09PSAyKSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoIumAieS4reeahOWPsOi0puS4reWtmOWcqOS4tOaXtuaVsOaNru+8jOivt+WFiOS/neWtmOWGjeWKoOWFpeW9kumbhuWNle+8gSIpOw0KICAgICAgICB9IGVsc2UgaWYgKGIgPT09IDMpIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6YCA5Zue55qE5Y+w6LSm5LiN6IO95Yqg5YWl5YW25a6D5b2S6ZuG5Y2V77yM6K+354K55Ye7W+mHjeaWsOWKoOWFpeW9kumbhuWNlV3mjInpkq4iKTsNCiAgICAgICAgfSBlbHNlIGlmIChiID09PSA0KSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoIumAieaLqeeahOWPsOi0puacieW3suWKoOWFpeW9kumbhuWNleeahOWPsOi0pu+8jOS4jeiDveWKoOWFpeWFtuS7luW9kumbhuWNlSIpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBzZXRNeVN0eWxlKGxlbmd0aCkgew0KICAgICAgdGhpcy5teVN0eWxlID0gW107DQogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGxlbmd0aDsgaSsrKSB7DQogICAgICAgIHRoaXMubXlTdHlsZS5wdXNoKHsNCiAgICAgICAgICBzdGFydGRhdGU6ICJteXNwYW4iLA0KICAgICAgICAgIGVuZGRhdGU6ICJteXNwYW4iLA0KICAgICAgICAgIHByZXZ0b3RhbHJlYWRpbmdzOiAibXlzcGFuIiwNCiAgICAgICAgICBjdXJ0b3RhbHJlYWRpbmdzOiAibXlzcGFuIiwNCiAgICAgICAgICB0cmFuc2Zvcm1lcnVsbGFnZTogIm15c3BhbiIsDQogICAgICAgICAgaW5wdXR0YXh0aWNrZXRtb25leTogIm15c3BhbiIsDQogICAgICAgICAgaW5wdXR0aWNrZXRtb25leTogIm15c3BhbiIsDQogICAgICAgICAgdWxsYWdlbW9uZXk6ICJteXNwYW4iLA0KICAgICAgICAgIHRheHJhdGU6ICJteXNwYW4iLA0KICAgICAgICAgIHJlbWFyazogIm15c3BhbiIsDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgc3VibWl0Q2hhbmdlMShkYXRhKSB7DQogICAgICB0aGlzLnN1Ym1pdCA9IGRhdGE7DQogICAgfSwNCiAgICByZWZyZXNoKCkgew0KICAgICAgaWYgKHRoaXMuJHJlZnMuc2hvd0FsYXJtTW9kZWwuZnJvbUd1aWppZGFuICE9IDEpIHsNCiAgICAgICAgLy8gd2luZG93Lmhpc3RvcnkuZ28oMCk7DQogICAgICAgIGxldCBvYmogPSB0aGlzOw0KICAgICAgICBvYmouc2hvd0FsYXJtTW9kZWwgPSBmYWxzZTsNCiAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7DQogICAgICAgICAgb2JqLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgICB9LCAyMDApOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5zaG93QWxhcm1Nb2RlbCA9IHRydWU7DQogICAgICB9DQogICAgfSwNCiAgICBhZ2FpbkpvaW4oKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMuJHJlZnMuYWNjb3VudFRhYmxlLmdldFNlbGVjdGlvbigpOw0KICAgICAgbGV0IGIgPSB0cnVlOw0KICAgICAgdmFyIHRoYXQgPSB0aGlzOw0KICAgICAgaWYgKGRhdGEgPT0gbnVsbCB8fCBkYXRhLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fpgInmi6nopoHph43mlrDliqDlhaXlvZLpm4bljZXnmoTlj7DotKYiKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldCBhZ2FpbkpvaW5JZHMgPSAiIjsNCiAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgbGV0IHN0YXR1cyA9IGl0ZW0uc3RhdHVzOw0KICAgICAgICAgIGlmIChzdGF0dXMgIT0gNSkgew0KICAgICAgICAgICAgYiA9IGZhbHNlOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZ2FpbkpvaW5JZHMgKz0gaXRlbS5wY2lkICsgIiwiOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICAgIGlmIChiKSB7DQogICAgICAgICAgYWdhaW5Kb2luKGFnYWluSm9pbklkcykudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLmRhdGEuY29kZSA9PSAwKSB7DQogICAgICAgICAgICAgIHRoYXQuJE1lc3NhZ2UuaW5mbyh7DQogICAgICAgICAgICAgICAgY29udGVudDogIuaPkOekuu+8muaTjeS9nOaIkOWKnyIsDQogICAgICAgICAgICAgICAgZHVyYXRpb246IDEwLA0KICAgICAgICAgICAgICAgIGNsb3NhYmxlOiB0cnVlLA0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgdGhhdC5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGF0LmVycm9yVGlwcygi5Y+q5pyJ5bey6YCA5Zue55qE5Y+w6LSm5omN6IO96YeN5paw5Yqg5YWl5b2S6ZuG5Y2VIik7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIGJlZm9yZUxvYWREYXRhKGRhdGEsIHN0cikgew0KICAgICAgdmFyIGNvbHMgPSBbXSwNCiAgICAgICAga2V5cyA9IFtdOw0KICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCB0aGlzLmFjY291bnRUYi5leHBvcnRjb2x1bW5zLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbHMucHVzaCh0aGlzLmFjY291bnRUYi5leHBvcnRjb2x1bW5zW2ldLnRpdGxlKTsNCiAgICAgICAga2V5cy5wdXNoKHRoaXMuYWNjb3VudFRiLmV4cG9ydGNvbHVtbnNbaV0ua2V5KTsNCiAgICAgIH0NCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgdGl0bGU6IGNvbHMsDQogICAgICAgIGtleToga2V5cywNCiAgICAgICAgZGF0YTogZGF0YSwNCiAgICAgICAgYXV0b1dpZHRoOiB0cnVlLA0KICAgICAgICBmaWxlbmFtZTogc3RyLA0KICAgICAgfTsNCiAgICAgIGV4Y2VsLmV4cG9ydF9hcnJheV90b19leGNlbChwYXJhbXMpOw0KICAgICAgcmV0dXJuOw0KICAgIH0sDQogICAgZXhwb3J0Q3N2KG5hbWUpIHsNCiAgICAgIHRoaXMuc2V0RWxlY3Ryb3lUeXBlKCk7DQogICAgICBsZXQgcGFyYW1zID0gdGhpcy5hY2NvdW50T2JqOw0KDQogICAgICBpZiAobmFtZSA9PT0gImN1cnJlbnQiKSB7DQogICAgICAgIHBhcmFtcy5wYWdlTnVtID0gdGhpcy5wYWdlTnVtOw0KICAgICAgICBwYXJhbXMucGFnZVNpemUgPSB0aGlzLnBhZ2VTaXplOw0KICAgICAgfSBlbHNlIGlmIChuYW1lID09PSAiYWxsIikgew0KICAgICAgICBwYXJhbXMucGFnZU51bSA9IDE7DQogICAgICAgIHBhcmFtcy5wYWdlU2l6ZSA9IHRoaXMuZXhwb3J0LnNpemU7DQogICAgICB9DQogICAgICBsZXQgcmVxID0gew0KICAgICAgICB1cmw6ICIvYnVzaW5lc3MvYWNjb3VudC9leHBvcnR6eSIsDQogICAgICAgIG1ldGhvZDogImdldCIsDQogICAgICAgIHBhcmFtczogcGFyYW1zLA0KICAgICAgfTsNCiAgICAgIHRoaXMuc3BpblNob3cgPSB0cnVlOw0KICAgICAgYXhpb3MNCiAgICAgICAgLmZpbGUocmVxKQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgdGhpcy5zcGluU2hvdyA9IGZhbHNlOw0KICAgICAgICAgIGNvbnN0IGNvbnRlbnQgPSByZXM7DQogICAgICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtjb250ZW50XSk7DQogICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSAi6Ieq5pyJ5Y+w6LSm5a+85Ye65pWw5o2uIiArICIueGxzeCI7DQogICAgICAgICAgaWYgKCJkb3dubG9hZCIgaW4gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpKSB7DQogICAgICAgICAgICAvLyDpnZ5JReS4i+i9vQ0KICAgICAgICAgICAgY29uc3QgZWxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJhIik7DQogICAgICAgICAgICBlbGluay5kb3dubG9hZCA9IGZpbGVOYW1lOw0KICAgICAgICAgICAgZWxpbmsuc3R5bGUuZGlzcGxheSA9ICJub25lIjsNCiAgICAgICAgICAgIGVsaW5rLmhyZWYgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOw0KICAgICAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChlbGluayk7DQogICAgICAgICAgICBlbGluay5jbGljaygpOw0KICAgICAgICAgICAgVVJMLnJldm9rZU9iamVjdFVSTChlbGluay5ocmVmKTsgLy8g6YeK5pS+VVJMIOWvueixoQ0KICAgICAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChlbGluayk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIElFMTAr5LiL6L29DQogICAgICAgICAgICBuYXZpZ2F0b3IubXNTYXZlQmxvYihibG9iLCBmaWxlTmFtZSk7DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKGVycikgPT4gew0KICAgICAgICAgIGNvbnNvbGUubG9nKGVycik7DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgLy/kuJPnpajnqI7pop0NCiAgICBzZXR0YXhyYXRlKCkgew0KICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdHRheHJhdGU7DQogICAgICBsZXQgZGF0YSA9IHRoaXMuaW5zaWRlRGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICBsZXQgdGF4dGlja2V0bW9uZXkgPSBkYXRhLnRheHRpY2tldG1vbmV5Ow0KICAgICAgZGF0YS50YXhyYXRlID0gdmFsOw0KICAgICAgZGF0YS50YXhhbW91bnQgPSBjb3VudFRheGFtb3VudChkYXRhKTsNCiAgICAgIGRhdGEuZWRpdFR5cGUgPSAxOw0KICAgIH0sDQogICAgc3RhcnRNb2RhbE9rKCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLmluc2lkZURhdGFbdGhpcy5lZGl0SW5kZXhdOw0KICAgICAgbGV0IHZhbCA9IHRoaXMuZWRpdFN0YXJ0RGF0ZTsNCiAgICAgIGRhdGEuc3RhcnRkYXRlID0gdmFsOyAvL+S/ruaUuei1t+Wni+aXpeacnw0KICAgICAgZGF0YS5lZGl0VHlwZSA9IDE7DQogICAgICAvL+iuoeeul+WumuminQ0KICAgICAgdGhpcy5nZXRRdW90YShkYXRhLmFtbWV0ZXJpZCwgZGF0YS5zdGFydGRhdGUsIGRhdGEuZW5kZGF0ZSwgKHJlc3VsdCkgPT4gew0KICAgICAgICBpZiAocmVzdWx0LmRhdGEuY29kZSA9PT0gMCkgew0KICAgICAgICAgIGRhdGEucXVvdGFyZWFkaW5ncyA9IE1hdGgucm91bmQocmVzdWx0LmRhdGEubXNnKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBkYXRhLnF1b3RhcmVhZGluZ3MgPSAwOw0KICAgICAgICB9DQogICAgICAgIHRoaXMuY2FsY3VsYXRlQWxsKGRhdGEpOw0KICAgICAgfSk7DQogICAgICBsZXQgb3BmbGFnID0gZGF0YS5vcGZsYWc7DQogICAgICAvL+S/ruaUuW9wZmxhZ+S/ruaUuei1t+aXpeacnyDljp/mnaXmlbDlrZfliqANCiAgICAgIGlmIChvcGZsYWcgIT0gNCAmJiBvcGZsYWcgIT0gNiAmJiBvcGZsYWcgIT0gNyAmJiBvcGZsYWcgIT0gOSkgew0KICAgICAgICBkYXRhLm9wZmxhZyA9IG9wZmxhZyArIDQ7DQogICAgICB9DQogICAgICBkYXRhLnJlbWFyayArPSAi5pys5pyf6LW35aeL5pel5pyfIOS7jiIgKyBkYXRhLm9sZF9zdGFydGRhdGUgKyAi5L+u5pS55Li6IiArIHZhbCArICI7ICI7DQogICAgICB0aGlzLnN0YXJ0TW9kYWwgPSBmYWxzZTsNCiAgICAgIHRoaXMubmV4dENlbGwodGhpcyk7DQogICAgfSwNCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm5leHRDZWxsKHRoaXMpOw0KICAgIH0sDQogICAgaGN5enN0YXJ0ZGF0ZShsZXR0LCBpbmRleCkgew0KICAgICAgbGV0IGRhdGEgPSBsZXR0Lmluc2lkZURhdGFbaW5kZXhdOw0KICAgICAgbGV0IHZhbCA9IGxldHQuZWRpdFN0YXJ0RGF0ZTsNCiAgICAgIGlmICh2YWwpIHsNCiAgICAgICAgaWYgKHRlc3ROdW1iZXIodmFsKSkgew0KICAgICAgICAgIGlmICh2YWwgPT0gZGF0YS5vbGRfc3RhcnRkYXRlKSB7DQogICAgICAgICAgICBkYXRhLnN0YXJ0ZGF0ZSA9IHZhbDsNCg0KICAgICAgICAgICAgbGV0dC5uZXh0Q2VsbChsZXR0KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgbGV0dC52YWxpZGF0ZSgpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBsZXR0LmVycm9yVGlwcygi6K+36L6T5YWl5pWw5a2X77yBIik7DQogICAgICAgICAgbGV0dC5uZXh0Q2VsbChsZXR0KTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGV0dC5uZXh0Q2VsbChsZXR0KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHFkTW9kYWxPaygpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy5pbnNpZGVEYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGxldCB2YWwgPSB0aGlzLmVkaXRQcmV2dG90YWxyZWFkaW5nczsNCg0KICAgICAgZGF0YS5wcmV2dG90YWxyZWFkaW5ncyA9IHZhbDsNCiAgICAgIGxldCBiID0gdGhpcy5pZk1heGRlZ3JlZTsNCiAgICAgIGlmIChiID09PSB0cnVlKSB7DQogICAgICAgIHRoaXMuZmJNb2RhbCA9IHRydWU7DQogICAgICB9IGVsc2UgaWYgKGIgPT09IGZhbHNlKSB7DQogICAgICAgIHRoaXMucXhmYk1vZGFsID0gdHJ1ZTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuY2FsY3VsYXRlQWxsKGRhdGEpOw0KICAgICAgfQ0KDQogICAgICBkYXRhLmVkaXRUeXBlID0gMTsNCiAgICAgIGxldCBvcGZsYWcgPSBkYXRhLm9wZmxhZzsNCiAgICAgIC8v5aKe5YqgNCDkv67mlLnotbfml6XmnJ8g5Y6f5p2l5pWw5a2X5YqgDQogICAgICBpZiAob3BmbGFnICE9IDIgJiYgb3BmbGFnICE9IDUgJiYgb3BmbGFnICE9IDYgJiYgb3BmbGFnICE9IDkpIHsNCiAgICAgICAgZGF0YS5vcGZsYWcgPSBvcGZsYWcgKyAyOw0KICAgICAgfQ0KICAgICAgZGF0YS5yZW1hcmsgKz0gIuacrOacn+i1t+W6piDku44iICsgZGF0YS5vbGRfcHJldnRvdGFscmVhZGluZ3MgKyAi5L+u5pS55Li6IiArIHZhbCArICI7ICI7DQoNCiAgICAgIHRoaXMucWRNb2RhbCA9IGZhbHNlOw0KICAgICAgdGhpcy5uZXh0Q2VsbCh0aGlzKTsNCiAgICB9LA0KICAgIGZiTW9kYWxPaygpIHsNCiAgICAgIGxldCBkYXRhID0gdGhpcy5pbnNpZGVEYXRhW3RoaXMuZWRpdEluZGV4XTsNCiAgICAgIGRhdGEuaWZNYXhkZWdyZWUgPSB0cnVlOw0KICAgICAgdGhpcy5mYk1vZGFsID0gZmFsc2U7DQoNCiAgICAgIHRoaXMuY2FsY3VsYXRlQWxsKGRhdGEpOw0KICAgIH0sDQogICAgcXhmYk1vZGFsT2soKSB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMuaW5zaWRlRGF0YVt0aGlzLmVkaXRJbmRleF07DQogICAgICBkYXRhLmlmTWF4ZGVncmVlID0gZmFsc2U7DQogICAgICB0aGlzLnF4ZmJNb2RhbCA9IGZhbHNlOw0KDQogICAgICB0aGlzLmNhbGN1bGF0ZUFsbChkYXRhKTsNCiAgICB9LA0KICAgIGhjeXpwcmV2dG90YWxyZWFkaW5ncyhsZXR0LCBpbmRleCkgew0KICAgICAgbGV0IGRhdGEgPSBsZXR0Lmluc2lkZURhdGFbaW5kZXhdOw0KICAgICAgbGV0IHZhbCA9IGxldHQuZWRpdFByZXZ0b3RhbHJlYWRpbmdzOw0KICAgICAgaWYgKHZhbCkgew0KICAgICAgICBpZiAodGVzdE51bWJlcih2YWwpKSB7DQogICAgICAgICAgaWYgKHZhbCA9PSBkYXRhLm9sZF9wcmV2dG90YWxyZWFkaW5ncykgew0KICAgICAgICAgICAgZGF0YS5wcmV2dG90YWxyZWFkaW5ncyA9IHZhbDsNCiAgICAgICAgICAgIGxldHQubmV4dENlbGwobGV0dCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGxldHQudmFsaWRhdGUoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgbGV0dC5lcnJvclRpcHMoIuivt+i+k+WFpeaVsOWtl++8gSIpOw0KICAgICAgICAgIGxldHQubmV4dENlbGwobGV0dCk7DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldHQubmV4dENlbGwobGV0dCk7DQogICAgICB9DQogICAgfSwNCiAgICBxZGNhbmNlbCgpIHsNCiAgICAgIGlmICh0aGlzLmNvbHVtbnNJbmRleCA9PT0gNCkgew0KICAgICAgICBsZXQgZGF0YSA9IHRoaXMuaW5zaWRlRGF0YVt0aGlzLmVkaXRJbmRleF0ub2xkX3ByZXZ0b3RhbHJlYWRpbmdzOw0KICAgICAgICB0aGlzLmVkaXRQcmV2dG90YWxyZWFkaW5ncyA9IGRhdGE7DQogICAgICAgIHRoaXMuaW5zaWRlRGF0YVt0aGlzLmVkaXRJbmRleF0ucHJldnRvdGFscmVhZGluZ3MgPSBkYXRhOw0KDQogICAgICAgIHRoaXMuJHJlZnNbImN1cnRvdGFscmVhZGluZ3MiICsgdGhpcy5lZGl0SW5kZXggKyB0aGlzLmNvbHVtbnNJbmRleF0uZm9jdXMoKTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5jb2x1bW5zSW5kZXggPT09IDUpIHsNCiAgICAgICAgbGV0IGRhdGEgPSB0aGlzLmluc2lkZURhdGFbdGhpcy5lZGl0SW5kZXhdLm9sZF9jdXJ0b3RhbHJlYWRpbmdzOw0KICAgICAgICB0aGlzLmVkaXRjdXJ0b3RhbHJlYWRpbmdzID0gZGF0YTsNCiAgICAgICAgdGhpcy5pbnNpZGVEYXRhW3RoaXMuZWRpdEluZGV4XS5jdXJ0b3RhbHJlYWRpbmdzID0gZGF0YTsNCg0KICAgICAgICB0aGlzLiRyZWZzWyJ0cmFuc2Zvcm1lcnVsbGFnZSIgKyB0aGlzLmVkaXRJbmRleCArIHRoaXMuY29sdW1uc0luZGV4XS5mb2N1cygpOw0KICAgICAgfQ0KICAgIH0sDQogICAgYWNjb3VudG5vQ2hhbmdlKCkgew0KICAgICAgdGhpcy5zZWFyY2hMaXN0KCk7DQogICAgfSwNCiAgICBzZXRFbGVjdHJveVR5cGUoKSB7DQogICAgICBsZXQgdHlwZXMgPSB0aGlzLmNsYXNzaWZpY2F0aW9uczsNCiAgICAgIHRoaXMuYWNjb3VudE9iai5lbGVjdHJvdHlwZSA9IHR5cGVzW3R5cGVzLmxlbmd0aCAtIDFdOw0KICAgIH0sDQogICAgc2VsZigpIHsNCiAgICAgIHZhciBsZXR0ID0gdGhpczsNCiAgICAgIGlmIChsZXR0LnN0YXJ0TW9kYWwpIHsNCiAgICAgICAgbGV0dC5zdGFydE1vZGFsT2soKTsNCiAgICAgIH0gZWxzZSBpZiAobGV0dC5xZE1vZGFsKSB7DQogICAgICAgIGxldHQucWRNb2RhbE9rKCk7DQogICAgICB9IGVsc2UgaWYgKGxldHQuZmJNb2RhbCkgew0KICAgICAgICBsZXR0LmZiTW9kYWxPaygpOw0KICAgICAgfSBlbHNlIGlmIChsZXR0LnF4ZmJNb2RhbCkgew0KICAgICAgICBsZXR0LnF4ZmJNb2RhbE9rKCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgaW5kZXggPSBsZXR0LmVkaXRJbmRleDsNCiAgICAgICAgbGV0IGNvbHVtbnMgPSBsZXR0LmNvbHVtbnNJbmRleDsNCiAgICAgICAgaWYgKGluZGV4ID09PSAtMSAmJiBjb2x1bW5zID09PSAtMSkgew0KICAgICAgICAgIGluZGV4ID0gMDsNCiAgICAgICAgICBjb2x1bW5zID0gMTsNCiAgICAgICAgICBsZXR0LmVkaXRJbmRleCA9IGluZGV4Ow0KICAgICAgICAgIGxldHQuY29sdW1uc0luZGV4ID0gY29sdW1uczsNCiAgICAgICAgICBsZXR0LmVkaXRTdGFydERhdGUgPSBsZXR0Lmluc2lkZURhdGFbaW5kZXhdLnN0YXJ0ZGF0ZTsNCiAgICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICAgIGxldHQuJHJlZnNbbGV0dC5lbnRlck9wZXJhdGUoY29sdW1ucykuc3RyICsgaW5kZXggKyBjb2x1bW5zXS5mb2N1cygpOw0KICAgICAgICAgIH0sIDIwMCk7DQogICAgICAgIH0gZWxzZSBpZiAoY29sdW1ucyA9PT0gMSkgew0KICAgICAgICAgIGxldHQuaGN5enN0YXJ0ZGF0ZShsZXR0LCBpbmRleCk7DQogICAgICAgIH0gZWxzZSBpZiAoY29sdW1ucyA9PT0gMykgew0KICAgICAgICAgIGxldHQuaGN5enByZXZ0b3RhbHJlYWRpbmdzKGxldHQsIGluZGV4KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBsZXR0LnZhbGlkYXRlKCk7DQogICAgICAgICAgbGV0dC5uZXh0Q2VsbChsZXR0KTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlRm9ybWF0RXJyb3IoZmlsZSkgew0KICAgICAgdGhpcy5lcnJvclRpcHMoZmlsZS5uYW1lICsgIiDmoLzlvI/kuI3mraPnoa7jgILlj6rog73kuIrkvKDlkI7nvIDlkI3kuLogeGxz5oiW6ICFIHhsc3gg55qE5paH5Lu2Iik7DQogICAgfSwNCiAgICBoYW5kbGVQcm9ncmVzcyhldmVudCwgZmlsZSkgew0KICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKHsNCiAgICAgICAgY29udGVudDogZmlsZS5uYW1lICsgIiDmraPlnKjkuIrkvKDjgIIiLA0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVVcGxvYWRTdWNjZXNzKCkge30sDQogICAgb25FeGNlbFVwbG9hZChmaWxlKSB7DQogICAgICBpZiAoZmlsZS5zaXplID4gMTAyNCAqIDEwMjQgKiA1KSB7DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLmlofku7blpKflsI/otoXov4fpmZDliLbvvIEiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgaWYgKCFmaWxlKSB7DQogICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fpgInmi6nopoHkuIrkvKDnmoTmlofku7bvvIEiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgbGV0IGZpbGVOYW1lID0gZmlsZS5uYW1lLmxhc3RJbmRleE9mKCIuIik7IC8v5Y+W5Yiw5paH5Lu25ZCN5byA5aeL5Yiw5pyA5ZCO5LiA5Liq54K555qE6ZW/5bqmDQogICAgICBsZXQgZmlsZU5hbWVMZW5ndGggPSBmaWxlLm5hbWUubGVuZ3RoOyAvL+WPluWIsOaWh+S7tuWQjemVv+W6pg0KICAgICAgbGV0IGZpbGVGb3JtYXQgPSBmaWxlLm5hbWUuc3Vic3RyaW5nKGZpbGVOYW1lICsgMSwgZmlsZU5hbWVMZW5ndGgpOyAvL+aIqg0KICAgICAgaWYgKCJ4bHMiICE9IGZpbGVGb3JtYXQgJiYgInhsc3giICE9IGZpbGVGb3JtYXQpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgbGV0IHBhcmFtID0geyB2ZXJzaW9uOiBpbmRleERhdGEudmVyc2lvbiB9Ow0KICAgICAgbGV0IGV4Y2VsID0geyBmaWxlOiBmaWxlIH07DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICB0aGF0LnNwaW5TaG93ID0gdHJ1ZTsNCiAgICAgIGF4aW9zDQogICAgICAgIC5yZXF1ZXN0KHsNCiAgICAgICAgICB1cmw6ICIvYnVzaW5lc3MvYWNjb3VudC91cGxvYWRFeGNlbCIsDQogICAgICAgICAgbWV0aG9kOiAicG9zdCIsDQogICAgICAgICAgZGF0YTogT2JqZWN0LmFzc2lnbih7fSwgcGFyYW0sIGV4Y2VsKSwNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIHRoYXQuc3BpblNob3cgPSBmYWxzZTsNCiAgICAgICAgICBpZiAocmVzLmRhdGEubnVtYmVyID4gMCkgew0KICAgICAgICAgICAgdGhhdC4kTWVzc2FnZS5pbmZvKHsNCiAgICAgICAgICAgICAgY29udGVudDogIuaIkOWKn+WvvOWFpSIgKyByZXMuZGF0YS5udW1iZXIgKyAi5p2h5pWw5o2uIiwNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGF0LmVycm9yVGlwcygi5a+85YWl5pWw5o2u5aSx6LSl77yM6K+35qOA5p+l5pWw5o2u5piv5ZCm5aGr5YaZ5q2j56GuIik7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChyZXMuZGF0YS5saXN0KSB7DQogICAgICAgICAgICB0aGF0LmV4cG9ydC5ydW4gPSB0cnVlOw0KICAgICAgICAgICAgdGhhdC5iZWZvcmVMb2FkRGF0YShyZXMuZGF0YS5saXN0LCAi5a+85YWl5pWw5o2u5Y+N6aaIIik7DQogICAgICAgICAgICB0aGF0LnBhZ2VOdW0gPSAxOw0KICAgICAgICAgICAgdGhhdC5nZXRBY2NvdW50TWVzc2FnZXMoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgcmV0dXJuIGZhbHNlOw0KICAgIH0sDQogICAgZ2V0RGF0YUZyb21Nb2RhbChkYXRhLCBmbGFnKSB7DQogICAgICB0aGlzLmNob29zZVJlc3BvbnNlQ2VudGVyKGRhdGEpOw0KICAgIH0sDQogICAgY2hvb3NlUmVzcG9uc2VDZW50ZXIoZGF0YSkgew0KICAgICAgaWYgKCFkYXRhKSB7DQogICAgICAgIGlmICghdGhpcy5hY2NvdW50T2JqLmNvbXBhbnkpIHsNCiAgICAgICAgICB0aGlzLmVycm9yVGlwcygi6K+36YCJ5oup5omA5bGe5YiG5YWs5Y+4Iik7DQogICAgICAgIH0NCiAgICAgICAgaWYgKCF0aGlzLmFjY291bnRPYmouY291bnRyeSkgew0KICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCLor7fpgInmi6nmiYDlsZ7pg6jpl6giKTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLiRyZWZzLnF1ZXJ5UGVvcGxlLm1vZGFsLnBhcmFtcyA9IHsNCiAgICAgICAgICBkZXB0SWQ6IHRoaXMuYWNjb3VudE9iai5jb3VudHJ5LA0KICAgICAgICAgIGNvcG5JZDogdGhpcy5hY2NvdW50T2JqLmNvbXBhbnksDQogICAgICAgIH07IC8vIOW9k+WJjemDqOmXqOWSjOWIhuWFrOWPuA0KICAgICAgICB0aGlzLiRyZWZzLnF1ZXJ5UGVvcGxlLmNob29zZSgpOyAvL+S6uuWRmA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy51c2VyTmFtZSA9IGRhdGEubmFtZTsNCiAgICAgICAgdGhpcy5hY2NvdW50T2JqLnVzZXJJZCA9IGRhdGEuaWQ7DQogICAgICB9DQogICAgfSwNCiAgICBlbGxpcHNpcyhyb3cpIHsNCiAgICAgIGxldCB2YWx1ZSA9IHJvdy5yZW1hcmsgKyByb3cuYno7DQogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gIiI7DQogICAgICBpZiAodmFsdWUubGVuZ3RoID4gMykgew0KICAgICAgICByZXR1cm4gdmFsdWUuc2xpY2UoMCwgMykgKyAiLi4uIjsNCiAgICAgIH0NCiAgICAgIHJldHVybiB2YWx1ZTsNCiAgICB9LA0KICAgIHVwbG9hZEZpbGUocm93KSB7DQogICAgICB0aGlzLiRyZWZzLnVwbG9hZEZpbGVNb2RhbC5jaG9vc2Uocm93LnBjaWQgKyAiIik7DQogICAgfSwNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmhhbmRsZUhlaWdodCgpOyAvL3RhYmxl6auY5bqm6Ieq5a6a5LmJDQoNCiAgICB0aGlzLmFjY291bnRPYmoudmVyc2lvbiA9IGluZGV4RGF0YS52ZXJzaW9uOw0KICAgIHRoaXMuY2F0ZWdvcnlzID0gYmxpc3QoImFtbWV0ZXJDYXRlZ29yeSIpOw0KICAgIHRoaXMuZGlyZWN0c3VwcGx5ZmxhZ3MgPSBibGlzdCgiZGlyZWN0U3VwcGx5RmxhZyIpOw0KICAgIGdldFVzZXIoKS50aGVuKChyZXMpID0+IHsNCiAgICAgIGlmIChyZXMuZGF0YS5jb21wYW5pZXMgIT0gbnVsbCAmJiByZXMuZGF0YS5jb21wYW5pZXMubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLkNvbXBhbnlMaXN0ID0gcmVzLmRhdGEuY29tcGFuaWVzOw0KICAgICAgICB0aGlzLmNvbXBhbnlMaXN0U2l6ZSA9IHJlcy5kYXRhLmNvbXBhbmllcy5sZW5ndGg7DQogICAgICAgIC8v5Yid5aeL5YyW6buY6K6k5bGV56S655m76ZmG55So5oi355qE56ys5LiA5Liq5YiG5YWs5Y+45ZKM5YiG5YWs5Y+45LiL55qE6YOo6ZeoDQogICAgICAgIHRoaXMuYWNjb3VudE9iai5jb21wYW55ID0gdGhpcy5Db21wYW55TGlzdFswXS5pZDsNCiAgICAgICAgZ2V0RGVwYXJ0bWVudHModGhpcy5hY2NvdW50T2JqLmNvbXBhbnkpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIHRoaXMucmVzQ2VudGVyTGlzdCA9IHJlcy5kYXRhOw0KICAgICAgICAgIHRoaXMucmVzQ2VudGVyTGlzdFNpemUgPSByZXMuZGF0YS5sZW5ndGg7DQogICAgICAgICAgdGhpcy5hY2NvdW50T2JqLmNvdW50cnkgPSByZXMuZGF0YVswXS5pZDsNCg0KICAgICAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgdGhpcy5hY2NvdW50VGIuY29sdW1ucyA9IHRoaXMuYWNjb3VudFRiLmhlYWRDb2x1bW4yDQogICAgICAgIC5jb25jYXQodGhpcy5hY2NvdW50VGIuc2NDb2x1bW4pDQogICAgICAgIC5jb25jYXQodGhpcy5hY2NvdW50VGIudGFpbENvbHVtbik7DQogICAgICAvL+W8gOaUvuWFqOecgQ0KICAgICAgdGhpcy5hY2NvdW50VGIuY29sdW1ucyA9IHRoaXMuYWNjb3VudFRiLmNvbHVtbnMuY29uY2F0KHRoaXMuYWNjb3VudFRiLmZpbGVDb2x1bW4pOw0KICAgIH0pOw0KDQogICAgZ2V0Q2xhc3NpZmljYXRpb24oKS50aGVuKChyZXMpID0+IHsNCiAgICAgIC8v55So55S157G75Z6LDQogICAgICB0aGlzLmNsYXNzaWZpY2F0aW9uRGF0YSA9IHJlcy5kYXRhOw0KICAgIH0pOw0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC4uLm1hcFN0YXRlKHsNCiAgICAgIGxvZ2luSWQ6IChzdGF0ZSkgPT4gc3RhdGUudXNlci5sb2dpbklkLA0KICAgIH0pLA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["addSelfPowerAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addSelfPowerAccount.vue", "sourceRoot": "src/view/account/homePageAccount", "sourcesContent": ["<!--自有电费台账-->\r\n\r\n<template>\r\n  <div class=\"page-class page-card\">\r\n    <div class=\"query-box\">\r\n      <Row :class=\"filterColl ? 'header-bar-show' : 'header-bar-hide'\">\r\n        <Form ref=\"accountForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.accountno\"\r\n                  @on-change=\"accountnoChange\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"上期止度:\"\r\n                prop=\"prevtotalreadings\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <InputNumber\r\n                  v-model=\"accountObj.prevtotalreadings\"\r\n                  placeholder=\"请输入上期止度\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"局站名称:\" prop=\"stationName\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.stationName\"\r\n                  placeholder=\"请输入局站名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"项目名称:\" prop=\"projectname\" class=\"form-line-height\">\r\n                <cl-input\r\n                  v-model=\"accountObj.projectname\"\r\n                  placeholder=\"请输入项目名称\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\" v-if=\"'sc' == accountObj.version\">\r\n              <FormItem\r\n                label=\"电表/协议编码:\"\r\n                prop=\"ammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.ammetercode\"\r\n                  placeholder=\"请输入电表户号/协议编码\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"供电局电表编号:\"\r\n                prop=\"supplybureauammetercode\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <cl-input\r\n                  v-model=\"accountObj.supplybureauammetercode\"\r\n                  placeholder=\"请输入供电局电表编号\"\r\n                  :style=\"formItemWidth\"\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"用电类型:\" prop=\"classifications\" class=\"form-line-height\">\r\n                <Cascader\r\n                  clearable\r\n                  :data=\"classificationData\"\r\n                  v-model=\"classifications\"\r\n                  :style=\"formItemWidth\"\r\n                ></Cascader>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"是否退回：\" prop=\"status\" class=\"form-line-height\">\r\n                <Select clearable v-model=\"accountObj.status\" :style=\"formItemWidth\">\r\n                  <Option value=\"\">请选择</Option>\r\n                  <Option value=\"5\">是</Option>\r\n                  <Option value=\"1\">否</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize > 1\">\r\n              <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select\r\n                  v-model=\"accountObj.company\"\r\n                  @on-change=\"selectChange(accountObj.company)\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option v-for=\"item in CompanyList\" :value=\"item.id\" :key=\"item.id\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize == 1 && resCenterListSize > 1\">\r\n              <FormItem label=\"所属部门：\" prop=\"country\" class=\"form-line-height\">\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option v-for=\"item in resCenterList\" :value=\"item.id\" :key=\"item.id\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\" v-if=\"companyListSize > 1\">\r\n              <FormItem label=\"所属部门：\" prop=\"country\" class=\"form-line-height\">\r\n                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                  <Option v-for=\"item in resCenterList\" :value=\"item.id\" :key=\"item.id\"\r\n                    >{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize == 1 && resCenterListSize == 1\">\r\n              <FormItem label=\"台账填写人：\" prop=\"country\" class=\"form-line-height\">\r\n                <Input\r\n                  :style=\"formItemWidth\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"userName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  clearable\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\" v-if=\"companyListSize > 1 || resCenterListSize > 1\">\r\n              <FormItem label=\"台账填写人：\" prop=\"country\" class=\"form-line-height\">\r\n                <Input\r\n                  :style=\"formItemWidth\"\r\n                  icon=\"ios-archive\"\r\n                  v-model=\"userName\"\r\n                  placeholder=\"点击图标选择\"\r\n                  @on-click=\"chooseResponseCenter()\"\r\n                  readonly\r\n                  clearable\r\n                />\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem\r\n                label=\"对外结算类型：\"\r\n                prop=\"directsupplyflag\"\r\n                class=\"form-line-height\"\r\n              >\r\n                <Select\r\n                  clearable\r\n                  v-model=\"accountObj.directsupplyflag\"\r\n                  :style=\"formItemWidth\"\r\n                >\r\n                  <Option\r\n                    v-for=\"item in directsupplyflags\"\r\n                    :value=\"item.typeCode\"\r\n                    :key=\"item.typeCode\"\r\n                    >{{ item.typeName }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <div align=\"right\">\r\n            <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n            <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n            <Dropdown trigger=\"click\" @on-click=\"exportCsv\">\r\n              <Button type=\"default\" style=\"margin-left: 5px\"\r\n                >导出\r\n                <Icon type=\"ios-arrow-down\"></Icon>\r\n              </Button>\r\n              <DropdownMenu slot=\"list\">\r\n                <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n          </div>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon\r\n          :type=\"filterColl ? 'md-arrow-dropup' : 'md-arrow-dropdown'\"\r\n          size=\"20\"\r\n          @click=\"filterColl = !filterColl\"\r\n          :color=\"filterColl ? '#000' : '#1ab394'\"\r\n        ></icon>\r\n      </div>\r\n      <div>\r\n        <Modal\r\n          v-model=\"meterModal\"\r\n          title=\"峰平谷信息\"\r\n          width=\"50%\"\r\n          @on-ok=\"setFPG(currentRow)\"\r\n        >\r\n          <Form ref=\"meterForm\" :model=\"currentRow\" :label-width=\"80\" inline>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">本期起度</Col>\r\n              <Col span=\"8\" align=\"center\">本期止度</Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">加减</Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"峰:\" prop=\"prevhighreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editprevhighreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"addFremark(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"\" prop=\"curhighreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editcurhighreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"setcurhighreadings(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">\r\n                <FormItem label=\"\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"edithighreadings\"\r\n                    :readonly=\"readonly\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"平:\" prop=\"prevflatreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editprevflatreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"addPremark(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"\" prop=\"curflatreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editcurflatreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"setcurflatreadings(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">\r\n                <FormItem label=\"\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editflatreadings\"\r\n                    :readonly=\"readonly\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n            </Row>\r\n            <Row>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"谷:\" prop=\"prevlowreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editprevlowreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"addGremark(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\">\r\n                <FormItem label=\"\" prop=\"curlowreadings\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editcurlowreadings\"\r\n                    :readonly=\"readonly\"\r\n                    @on-change=\"setcurlowreadings(currentRow)\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n              <Col span=\"8\" align=\"center\" v-if=\"version == 'sc'\">\r\n                <FormItem label=\"\">\r\n                  <InputNumber\r\n                    :active-change=\"false\"\r\n                    v-model=\"editlowreadings\"\r\n                    :readonly=\"readonly\"\r\n                  />\r\n                </FormItem>\r\n              </Col>\r\n            </Row>\r\n          </Form>\r\n        </Modal>\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"startModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"startModalOk\"\r\n          @on-cancel=\"cancel\"\r\n          ><p>\r\n            是否确定更改本期起始日期？保存后从当前修改的起始日期前无法填入台帐！(可删除保存解除限制)\r\n          </p></Modal\r\n        >\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"qdModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"qdModalOk\"\r\n          @on-cancel=\"cancel\"\r\n          ><p>是否确定更改本期起度?</p></Modal\r\n        >\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"fbModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"fbModalOk\"\r\n          @on-cancel=\"qdcancel\"\r\n          ><p>是否翻表?</p></Modal\r\n        >\r\n        <Modal\r\n          class=\"mymodal\"\r\n          v-model=\"qxfbModal\"\r\n          title=\"提示\"\r\n          @on-ok=\"qxfbModalOk\"\r\n          @on-cancel=\"qdcancel\"\r\n          ><p>是否取消翻表?</p></Modal\r\n        >\r\n      </div>\r\n    </div>\r\n    <div class=\"cl-table\">\r\n      <Row class=\"button-bar\">\r\n        <div class=\"table-button\">\r\n          <Button type=\"success\" @click=\"preserve()\">保存</Button>\r\n          <Button type=\"error\" @click=\"remove()\">删除</Button>\r\n          <Button type=\"error\" @click=\"deleteAll()\">一键删除</Button>\r\n          <Upload\r\n            style=\"float: right\"\r\n            :on-format-error=\"handleFormatError\"\r\n            :before-upload=\"onExcelUpload\"\r\n            :on-progress=\"handleProgress\"\r\n            :on-success=\"handleUploadSuccess\"\r\n            :max-size=\"10240\"\r\n            action=\"_blank\"\r\n            accept=\".csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\r\n            :format=\"['xls', 'xlsx']\"\r\n          >\r\n            <Button icon=\"ios-cloud-upload\">导入Excel</Button>\r\n          </Upload>\r\n          <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n            <Button type=\"info\" style=\"margin-left: 5px\"\r\n              >加入归集单\r\n              <Icon type=\"ios-arrow-down\"></Icon>\r\n            </Button>\r\n            <DropdownMenu slot=\"list\">\r\n              <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n              <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n          <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n        </div>\r\n      </Row>\r\n      <Table\r\n        ref=\"accountTable\"\r\n        border\r\n        :columns=\"accountTb.columns\"\r\n        :data=\"insideData\"\r\n        class=\"mytable\"\r\n        :loading=\"accountTb.loading\"\r\n        :height=\"tableHeight\"\r\n      >\r\n        <div></div>\r\n        <!--项目名称-->\r\n        <template slot-scope=\"{ row }\" slot=\"projectname\">\r\n          <div v-if=\"row.status === 5\">\r\n            <span>{{ row.projectname }}</span\r\n            ><span style=\"color: red\">[退回]</span>\r\n          </div>\r\n          <span v-else>{{ row.projectname }}</span>\r\n        </template>\r\n        <!--起始时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"startdate\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'startdate' + index + 1\"\r\n              class=\"myinput\"\r\n              type=\"text\"\r\n              @on-blur=\"validate\"\r\n              v-model=\"editStartDate\"\r\n              v-if=\"editIndex === index && columnsIndex === 1\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].startdate\"\r\n              @click=\"selectCall(row, index, 1, 'startdate')\"\r\n              v-else\r\n              >{{ row.startdate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.startdate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--截止时间-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"enddate\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'enddate' + index + 2\"\r\n              type=\"text\"\r\n              v-model=\"editEndDate\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 2\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].enddate\"\r\n              @click=\"selectCall(row, index, 2, 'enddate')\"\r\n              v-else\r\n              >{{ row.enddate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.enddate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--起度-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"prevtotalreadings\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'prevtotalreadings' + index + 3\"\r\n              type=\"text\"\r\n              v-model=\"editPrevtotalreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 3 && !row.isFPG && !row.isWB\"\r\n            />\r\n            <span @click=\"openModal(index)\" v-else-if=\"row.isFPG\">峰平谷</span>\r\n            <span\r\n              :class=\"myStyle[index].prevtotalreadings\"\r\n              @click=\"selectCall(row, index, 3, 'prevtotalreadings')\"\r\n              v-else\r\n              >{{ row.prevtotalreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span @click=\"openModal(index)\" v-if=\"row.isFPG\">峰平谷</span>\r\n            <span v-else>{{ row.prevtotalreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--止度-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"curtotalreadings\">\r\n          <div v-if=\"row.total == null && !row.ifNext\">\r\n            <Input\r\n              :ref=\"'curtotalreadings' + index + 4\"\r\n              type=\"text\"\r\n              v-model=\"editcurtotalreadings\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 4 && !row.isFPG && !row.isWB\"\r\n            />\r\n            <span @click=\"openModal(index)\" v-else-if=\"row.isFPG\">峰平谷</span>\r\n            <span\r\n              :class=\"myStyle[index].curtotalreadings\"\r\n              style=\"overflow-wrap: break-word\"\r\n              @click=\"selectCall(row, index, 4, 'curtotalreadings')\"\r\n              v-else\r\n              >{{ row.curtotalreadings }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span @click=\"openModal(index)\" v-if=\"row.isFPG\">峰平谷</span>\r\n            <span v-else>{{ row.curtotalreadings }}</span>\r\n          </div>\r\n        </template>\r\n        <!--电损-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"transformerullage\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'transformerullage' + index + 5\"\r\n              type=\"text\"\r\n              v-model=\"edittransformerullage\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 5 && !row.isWB\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].transformerullage\"\r\n              @click=\"selectCall(row, index, 5, 'transformerullage')\"\r\n              v-else\r\n              >{{ row.transformerullage }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.transformerullage }}</span>\r\n          </div>\r\n        </template>\r\n        <template slot-scope=\"{ row }\" slot=\"curusedreadings\">\r\n          <Tooltip\r\n            v-if=\"row.total == null\"\r\n            placement=\"top\"\r\n            max-width=\"200\"\r\n            :content=\"'上期电量:' + row.curusedreadingsold\"\r\n            :disabled=\"\r\n              row.ammeteruse == '1' &&\r\n              row.curusedreadingsold &&\r\n              (row.curusedreadings - row.curusedreadingsold) / row.curusedreadings > 0.2\r\n                ? false\r\n                : true\r\n            \"\r\n          >\r\n            <span\r\n              :style=\"\r\n                row.ammeteruse == '1' &&\r\n                row.curusedreadingsold &&\r\n                (row.curusedreadings - row.curusedreadingsold) / row.curusedreadings > 0.2\r\n                  ? { color: 'orange', 'font-size': '14px' }\r\n                  : {}\r\n              \"\r\n              >{{ row.curusedreadings }}</span\r\n            >\r\n          </Tooltip>\r\n          <span v-else>{{ row.curusedreadings }}</span>\r\n        </template>\r\n        <template slot-scope=\"{ row }\" slot=\"unitpirce\">\r\n          <Tooltip\r\n            v-if=\"row.total == null\"\r\n            placement=\"top\"\r\n            max-width=\"200\"\r\n            :content=\"'上期单价:' + row.unitpirceold\"\r\n            :disabled=\"\r\n              row.ammeteruse == '1' &&\r\n              row.unitpirceold &&\r\n              (row.unitpirce - row.unitpirceold) / row.unitpirce > 0.2\r\n                ? false\r\n                : true\r\n            \"\r\n          >\r\n            <span\r\n              :style=\"\r\n                row.ammeteruse == '1' &&\r\n                row.unitpirceold &&\r\n                (row.unitpirce - row.unitpirceold) / row.unitpirce > 0.2\r\n                  ? { color: 'orange', 'font-size': '14px' }\r\n                  : {}\r\n              \"\r\n              >{{ row.unitpirce }}</span\r\n            >\r\n          </Tooltip>\r\n          <span v-else>{{ row.unitpirce }}</span>\r\n        </template>\r\n        <!--普票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputticketmoney' + index + 6\"\r\n              type=\"text\"\r\n              v-model=\"editticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 6\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputticketmoney\"\r\n              @click=\"selectCall(row, index, 6, 'inputticketmoney')\"\r\n              v-else\r\n              >{{ row.inputticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--专票-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"inputtaxticketmoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'inputtaxticketmoney' + index + 7\"\r\n              type=\"text\"\r\n              v-model=\"edittaxticketmoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 7\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].inputtaxticketmoney\"\r\n              @click=\"selectCall(row, index, 7, 'inputtaxticketmoney')\"\r\n              v-else\r\n              >{{ row.inputtaxticketmoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.inputtaxticketmoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--专票税率-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"taxrate\">\r\n          <div v-if=\"row.total == null\">\r\n            <Select\r\n              :ref=\"'taxrate' + index + 8\"\r\n              type=\"text\"\r\n              v-model=\"edittaxrate\"\r\n              @on-change=\"settaxrate\"\r\n              v-if=\"editIndex === index && columnsIndex === 8\"\r\n              transfer=\"true\"\r\n            >\r\n              <Option selected value=\"13\">13</Option>\r\n              <Option value=\"1\">1</Option>\r\n              <Option value=\"3\">3</Option>\r\n              <Option value=\"6\">6</Option>\r\n              <Option value=\"16\">16</Option>\r\n              <Option value=\"17\">17</Option>\r\n            </Select>\r\n            <span\r\n              :class=\"myStyle[index].taxrate\"\r\n              @click=\"selectCall(row, index, 8, 'taxrate')\"\r\n              v-else\r\n              >{{ row.taxrate }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.taxrate }}</span>\r\n          </div>\r\n        </template>\r\n        <!--其他-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"ullagemoney\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              :ref=\"'ullagemoney' + index + 9\"\r\n              type=\"text\"\r\n              v-model=\"editullagemoney\"\r\n              @on-blur=\"validate\"\r\n              v-if=\"editIndex === index && columnsIndex === 9\"\r\n            />\r\n            <span\r\n              :class=\"myStyle[index].ullagemoney\"\r\n              @click=\"selectCall(row, index, 9, 'ullagemoney')\"\r\n              v-else\r\n              >{{ row.ullagemoney }}</span\r\n            >\r\n          </div>\r\n          <div v-else>\r\n            <span>{{ row.ullagemoney }}</span>\r\n          </div>\r\n        </template>\r\n        <!--备注-->\r\n        <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n          <div v-if=\"row.total == null\">\r\n            <Input\r\n              v-model=\"editremark\"\r\n              :ref=\"'remark' + index + 10\"\r\n              type=\"text\"\r\n              @on-blur=\"validateRemark\"\r\n              v-if=\"editIndex === index && columnsIndex === 10\"\r\n            />\r\n            <Tooltip placement=\"bottom\" max-width=\"200\" v-else>\r\n              <span\r\n                :class=\"myStyle[index].remark\"\r\n                style=\"width: 60px\"\r\n                @click=\"selectCall(row, index, 10, 'remark')\"\r\n                >{{ ellipsis(row) }}</span\r\n              >\r\n              <div slot=\"content\">\r\n                {{ row.remark + row.bz }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n          <div v-else>\r\n            <Tooltip placement=\"bottom\" max-width=\"200\">\r\n              <span>{{ ellipsis(row) }}</span>\r\n              <div slot=\"content\">\r\n                {{ row.remark + row.bz }}\r\n              </div>\r\n            </Tooltip>\r\n          </div>\r\n        </template>\r\n      </Table>\r\n      <div class=\"table-page\">\r\n        <Page\r\n          size=\"small\"\r\n          :total=\"pageTotal\"\r\n          :current=\"pageNum\"\r\n          :page-size=\"pageSize\"\r\n          show-elevator\r\n          show-sizer\r\n          show-total\r\n          placement=\"top\"\r\n          @on-change=\"handlePage\"\r\n          @on-page-size-change=\"handlePageSize\"\r\n        ></Page>\r\n      </div>\r\n      <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n    </div>\r\n    <add-bill-per\r\n      ref=\"addBillPer\"\r\n      v-on:refreshList=\"refresh\"\r\n      @buttonload2=\"buttonload2\"\r\n      @isButtonload=\"isButtonload\"\r\n    ></add-bill-per>\r\n    <query-people-modal\r\n      ref=\"queryPeople\"\r\n      v-on:getDataFromModal=\"getDataFromModal\"\r\n    ></query-people-modal>\r\n    <upload-file-modal ref=\"uploadFileModal\"></upload-file-modal>\r\n\r\n    <!--    稽核modal-->\r\n    <Modal\r\n      v-model=\"showCheckModel\"\r\n      width=\"80%\"\r\n      title=\"稽核结果及反馈\"\r\n      :mask-closable=\"false\"\r\n    >\r\n      <check-result-and-response ref=\"chekResultAndResponse\"></check-result-and-response>\r\n    </Modal>\r\n    <Modal v-model=\"showJhModel\" width=\"80%\" title=\"稽核结果\" :mask-closable=\"false\">\r\n      <check-result ref=\"checkResult\"></check-result>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button type=\"primary\" @click=\"nextCheck\">已查阅</Button>\r\n        <Button type=\"text\" @click=\"checkCancel\">取消</Button>\r\n      </div>\r\n    </Modal>\r\n    <Modal\r\n      v-model=\"showAlarmModel\"\r\n      width=\"80%\"\r\n      title=\"台账预警稽核\"\r\n      :mask-closable=\"false\"\r\n      :closable=\"false\"\r\n      class=\"yjjh\"\r\n    >\r\n      <alarm-check\r\n        :ymmc=\"'自有电费台账'\"\r\n        ref=\"showAlarmModel\"\r\n        @submitChange=\"submitChange\"\r\n        @save=\"save\"\r\n        @close=\"alarmClose\"\r\n      ></alarm-check>\r\n      <div slot=\"footer\" style=\"text-align: center\">\r\n        <Button size=\"large\" @click=\"alarmClose\">关闭</Button>\r\n      </div>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { mapState } from \"vuex\";\r\nimport { getAuditResult, getAuditResultNew, getAuditResultNew_QXM } from \"@/api/account\";\r\nimport {\r\n  addSelfPowerAccount,\r\n  temporaryStorage,\r\n  getElectrQuota,\r\n  editOwn,\r\n  removeOwn,\r\n  selectByPcid,\r\n  getUser,\r\n  getDepartments,\r\n  selectByAmmeterId,\r\n  accountTotal,\r\n  selectCompletedMoney,\r\n  selectIdsByParams,\r\n  removeAll,\r\n} from \"@/api/account\";\r\nimport { getClassification, powerresult } from \"@/api/basedata/ammeter.js\";\r\nimport { againJoin } from \"@/api/accountBillPer\";\r\nimport { validContractList } from \"@/api/contract\";\r\nimport checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\nimport checkResult from \"@/view/account/check/checkResult\";\r\nimport alarmCheck from \"@/view/account/check/alarmCheck\";\r\nimport {\r\n  getDates,\r\n  cutDate_yyyymmdd,\r\n  testNumber,\r\n  GetDateDiff,\r\n  stringToDate,\r\n  getCurrentDate,\r\n} from \"@/view/account/powerAccountHelper\";\r\nimport {\r\n  _verify_StartDate,\r\n  judgeNumber,\r\n  _verify_EndDate,\r\n  _verify_PrevTotalReadings,\r\n  _verify_CurTotalReadings,\r\n  other_no_ammeteror_protocol,\r\n  self_no_ammeteror_protocol,\r\n  HFL_ammeteror,\r\n  judging_editability,\r\n  judging_editability1,\r\n  _verify_Money,\r\n  _calculateUsedReadings,\r\n  _calculateUsedReadingsForType_2,\r\n  _calculateTotalReadings,\r\n  _calculateUnitPriceByUsedMoney,\r\n  _calculateAccountMoney,\r\n  _calculateQuotereadingsratio,\r\n  requiredFieldValidator,\r\n  countTaxamount,\r\n  calculateActualMoney,\r\n  judge_negate,\r\n  judge_recovery,\r\n  judge_yb,\r\n  unitpirceMin,\r\n  unitpirceMax,\r\n  unitpirceMax1,\r\n} from \"@/view/account/PowerAccountController\";\r\nimport { widthstyle } from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nimport QueryPeopleModal from \"@/view/account/queryPeopleModal\";\r\nimport UploadFileModal from \"@/view/account/uploadFileModal\";\r\nimport AddBillPer from \"@/view/account/addBillPreModal\";\r\nimport excel from \"@/libs/excel\";\r\nimport { blist } from \"@/libs/tools\";\r\nimport axios from \"@/libs/api.request\";\r\nimport indexData from \"@/config/index\";\r\n\r\nimport permissionMixin from \"@/mixins/permission\";\r\nimport pageFun from \"@/mixins/pageFun\";\r\n\r\nlet dates = getDates();\r\n\r\nexport default {\r\n  mixins: [permissionMixin, pageFun],\r\n\r\n  components: {\r\n    alarmCheck,\r\n    checkResult,\r\n    checkResultAndResponse,\r\n    UploadFileModal,\r\n    QueryPeopleModal,\r\n    AddBillPer,\r\n  },\r\n  data() {\r\n    let renderCategory = (h, params) => {\r\n      var categoryname = \"\";\r\n      for (let item of this.categorys) {\r\n        if (item.typeCode == params.row.category) {\r\n          categoryname = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", categoryname);\r\n    };\r\n    let renderDirectsupplyflag = (h, params) => {\r\n      var directsupplyflag = \"\";\r\n      for (let item of this.directsupplyflags) {\r\n        if (item.typeCode == params.row.directsupplyflag) {\r\n          directsupplyflag = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", directsupplyflag);\r\n    };\r\n    let photo = (h, { row, index }) => {\r\n      let that = this;\r\n      let str = \"\";\r\n      if (row.projectname != \"小计\" && row.projectname != \"合计\") {\r\n        str = \"上传\";\r\n      }\r\n      return h(\"div\", [\r\n        h(\r\n          \"u\",\r\n          {\r\n            on: {\r\n              click() {\r\n                //打开弹出框\r\n                if (row.projectname != \"小计\" && row.projectname != \"合计\") {\r\n                  that.uploadFile(row);\r\n                }\r\n              },\r\n            },\r\n          },\r\n          str\r\n        ),\r\n      ]);\r\n    };\r\n\r\n    return {\r\n      isT: true,\r\n      number2: 0,\r\n      name: \"\",\r\n      isQuery: true,\r\n      number: 0,\r\n      auditResultList: [],\r\n      submit: [],\r\n      submit2: [],\r\n      ammeterids: [],\r\n      showCheckModel: false,\r\n      showAlarmModel: false,\r\n      showJhModel: false,\r\n      version: indexData.version,\r\n      valiprice: true,\r\n      formatArray: [\"\"],\r\n      formItemWidth: widthstyle,\r\n      tableName: 1,\r\n      dateList: dates,\r\n      categorys: [], //描述类型\r\n      spinShow: false, //导入数据遮罩\r\n      filterColl: true, //搜索面板展开\r\n      startModal: false, //起始时间修改提示\r\n      qdModal: false, //起度修改提示\r\n      fbModal: false, //翻表提示\r\n      qxfbModal: false, //取消翻表提示\r\n      readonly: false, //封平谷只读\r\n      editIndex: -1, //当前编辑行\r\n      columnsIndex: -1, //当前编辑列\r\n      classificationData: [], //用电类型树\r\n      classifications: [], //选择的用电类型树\r\n      directsupplyflags: [],\r\n      editStartDate: \"\",\r\n      editEndDate: \"\",\r\n      editPrevtotalreadings: \"\",\r\n      editcurtotalreadings: \"\",\r\n      edittransformerullage: \"\",\r\n      edittaxticketmoney: \"\",\r\n      editticketmoney: \"\",\r\n      editullagemoney: \"\",\r\n      editpercent: \"\",\r\n      edittaxrate: \"\",\r\n      editremark: \"\",\r\n      editprevhighreadings: 0,\r\n      editprevflatreadings: 0,\r\n      editprevlowreadings: 0,\r\n      editcurhighreadings: 0,\r\n      editcurflatreadings: 0,\r\n      editcurlowreadings: 0,\r\n      edithighreadings: 0,\r\n      editflatreadings: 0,\r\n      editlowreadings: 0,\r\n      pabriid: \"\",\r\n      resCenterList: [],\r\n      CompanyList: [],\r\n      companyListSize: \"\",\r\n      resCenterListSize: \"\",\r\n      insideData: [], //数据\r\n      myStyle: [], //样式\r\n      pageTotal: 0,\r\n      pageNum: 1,\r\n      pageSize: 10, //当前页\r\n      currentRow: {},\r\n      meterModal: false,\r\n      ifMaxdegree: null,\r\n      againJoinIds: \"\",\r\n      export: {\r\n        run: false, //是否正在执行导出\r\n        data: \"\", //导出数据\r\n        totalPage: 0, //一共多少页\r\n        currentPage: 0, //当前多少页\r\n        percent: 0,\r\n        size: ********,\r\n      },\r\n      Accountqur: { ammeterid: \"\", startdate: \"\", enddate: \"\" },\r\n      accountObj: {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        stationName: \"\", //局站名称\r\n        projectname: \"\", //项目名称\r\n        prevtotalreadings: null, //上期止度\r\n        ammetercode: \"\", //电表户号/协议编码\r\n        status: \"\", //是否退回\r\n        company: \"\",\r\n        country: \"\",\r\n        electrotype: \"\", //用电类型\r\n        accountType: \"1\", //台账类型\r\n        userId: \"\",\r\n        version: \"\",\r\n        supplybureauammetercode: \"\",\r\n        directsupplyflag: \"\",\r\n        ammeterid: \"\",\r\n        startdate: \"\",\r\n        enddate: \"\",\r\n      },\r\n\r\n      userName: \"\", //查询选择人员名称\r\n      avemoney: 0,\r\n      nowdatediff: 0,\r\n      accountTb: {\r\n        loading: false,\r\n        columns: [],\r\n        headColumn: [\r\n          { type: \"selection\", width: 40, align: \"center\", fixed: \"left\" },\r\n          {\r\n            title: \"项目名称\",\r\n            slot: \"projectname\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n        ],\r\n        headColumn2: [\r\n          { type: \"selection\", width: 40, align: \"center\", fixed: \"left\" },\r\n          {\r\n            title: \"稽核结果及反馈\",\r\n            fixed: \"left\",\r\n            key: \"action\",\r\n            align: \"center\",\r\n            render: (h, params) => {\r\n              var that = this;\r\n              return h(\r\n                \"Button\",\r\n                {\r\n                  props: {\r\n                    type: \"primary\",\r\n                    size: \"small\",\r\n                  },\r\n                  style: {\r\n                    \"font-size\": \"10px\",\r\n                  },\r\n                  on: {\r\n                    click() {\r\n                      that.$refs.chekResultAndResponse.pcid = params.row.pcid;\r\n                      that.showCheckModel = true;\r\n                    },\r\n                  },\r\n                },\r\n                \"稽核结果及反馈\"\r\n              );\r\n            },\r\n            minWidth: 100,\r\n            maxWidth: 150,\r\n          },\r\n          {\r\n            title: \"项目名称\",\r\n            slot: \"projectname\",\r\n            align: \"center\",\r\n            width: 90,\r\n            fixed: \"left\",\r\n          },\r\n        ],\r\n        tailColumn: [\r\n          {\r\n            title: \"期号\",\r\n            key: \"accountno\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"用电类型\",\r\n            key: \"electrotypename\",\r\n            align: \"center\",\r\n            width: 94,\r\n          },\r\n          {\r\n            title: \"实际普票含税金额(元)\",\r\n            key: \"ticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n          },\r\n          {\r\n            title: \"实际专票含税金额(元)\",\r\n            key: \"taxticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n          },\r\n          {\r\n            title: \"总电量(度)\",\r\n            key: \"totalusedreadings\",\r\n            align: \"center\",\r\n            width: 60,\r\n          },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\", width: 160 },\r\n          {\r\n            title: \"类型描述\",\r\n            key: \"categoryname\",\r\n            align: \"center\",\r\n            width: 94,\r\n            render: renderCategory,\r\n          },\r\n          { title: \"倍率\", key: \"magnification\", align: \"center\", width: 60 },\r\n          { title: \"定额\", key: \"quotareadings\", align: \"center\", width: 60 },\r\n          {\r\n            title: \"浮动比（%）\",\r\n            key: \"quotereadingsratio\",\r\n            align: \"center\",\r\n            width: 100,\r\n            sortable: true,\r\n            sortMethod: (a, b, type) => {\r\n              if (type === \"desc\") {\r\n                return parseInt(a) < parseInt(b) ? 1 : -1;\r\n              } else {\r\n                return parseInt(a) > parseInt(b) ? 1 : -1;\r\n              }\r\n            },\r\n          },\r\n          {\r\n            title: \"起始日期\",\r\n            slot: \"startdate\",\r\n            align: \"center\",\r\n            fixed: \"left\",\r\n            width: 75,\r\n          },\r\n          {\r\n            title: \"截止日期\",\r\n            slot: \"enddate\",\r\n            align: \"center\",\r\n            width: 75,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"本期起度\",\r\n            slot: \"prevtotalreadings\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"本期止度\",\r\n            slot: \"curtotalreadings\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"电损(度)\",\r\n            slot: \"transformerullage\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"用电量(度)\",\r\n            slot: \"curusedreadings\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"电价(元)\",\r\n            slot: \"unitpirce\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"普票含税金额(元)\",\r\n            slot: \"inputticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"专票含税金额(元)\",\r\n            slot: \"inputtaxticketmoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"专票税率（%）\",\r\n            slot: \"taxrate\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"专票税额\",\r\n            key: \"taxamount\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"其他(元)\",\r\n            slot: \"ullagemoney\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"实缴费用(元)含税\",\r\n            key: \"accountmoney\",\r\n            align: \"center\",\r\n            width: 65,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"备注\",\r\n            slot: \"remark\",\r\n            align: \"center\",\r\n            width: 100,\r\n            fixed: \"left\",\r\n          },\r\n          { title: \"分割比例(%)\", align: \"center\", key: \"percent\", width: 80 },\r\n          {\r\n            title: \"对外结算类型\",\r\n            align: \"center\",\r\n            key: \"directsupplyflag\",\r\n            width: 80,\r\n            render: renderDirectsupplyflag,\r\n          },\r\n        ],\r\n        fileColumn: [\r\n          {\r\n            title: \"附件\",\r\n            slot: \"file\",\r\n            align: \"center\",\r\n            width: 50,\r\n            fixed: \"left\",\r\n            render: photo,\r\n          },\r\n        ],\r\n        lnColumn: [\r\n          {\r\n            title: \"供电局电表编号\",\r\n            key: \"supplybureauammetercode\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n        ],\r\n        scColumn: [\r\n          {\r\n            title: \"电表户号/协议编码\",\r\n            key: \"ammetercode\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n          {\r\n            title: \"供电局电表编号\",\r\n            key: \"supplybureauammetercode\",\r\n            align: \"center\",\r\n            width: 60,\r\n            fixed: \"left\",\r\n          },\r\n        ],\r\n\r\n        data: [],\r\n        exportcolumns: [\r\n          { title: \"错误信息\", key: \"error\" },\r\n          { title: \"注意信息\", key: \"careful\" },\r\n          { title: \"项目名称\", key: \"projectname\" },\r\n          { title: \"期号\", key: \"accountno\" },\r\n          { title: \"电表/协议id\", key: \"ammeterid\" },\r\n          { title: \"供电局电表编号\", key: \"supplybureauammetercode\", align: \"center\" },\r\n          { title: \"局站\", key: \"stationName\", align: \"center\" },\r\n          { title: \"起始日期(必填)\", key: \"startdate\" },\r\n          { title: \"截止日期(必填)\", key: \"enddate\" },\r\n          { title: \"本期峰段起度(峰平谷必填)\", key: \"prevhighreadings\" },\r\n          { title: \"本期平段起度(峰平谷必填)\", key: \"prevflatreadings\" },\r\n          { title: \"本期谷段起度(峰平谷必填)\", key: \"prevlowreadings\" },\r\n          { title: \"本期起度(普通电表必填)\", key: \"prevtotalreadings\" },\r\n          { title: \"本期峰段止度(峰平谷必填)\", key: \"curhighreadings\" },\r\n          { title: \"本期平段止度(峰平谷必填)\", key: \"curflatreadings\" },\r\n          { title: \"本期谷段止度(峰平谷必填)\", key: \"curlowreadings\" },\r\n          { title: \"本期止度(普通电表必填)\", key: \"curtotalreadings\" },\r\n          { title: \"电损(度)(可填)\", key: \"transformerullage\" },\r\n          { title: \"专票含税金额(元)(必填)\", key: \"inputtaxticketmoney\" },\r\n          { title: \"专票税率（%）(必填)\", key: \"taxrate\" },\r\n          { title: \"专票税额\", key: \"taxamount\" },\r\n          { title: \"普票含税金额(元)(必填)\", key: \"inputticketmoney\" },\r\n          { title: \"其他(元)(可填)\", key: \"ullagemoney\" },\r\n          { title: \"实缴费用(元)含税\", key: \"accountmoney\" },\r\n          { title: \"类型描述\", key: \"categoryname\" },\r\n          { title: \"倍率\", key: \"magnification\" },\r\n          { title: \"定额\", key: \"quotareadings\" },\r\n          { title: \"浮动比（%）\", key: \"quotereadingsratio\" },\r\n          { title: \"用电量(度)\", key: \"curusedreadings\" },\r\n          { title: \"总电量(度)\", key: \"totalusedreadings\" },\r\n          { title: \"电价(元)\", key: \"unitpirce\" },\r\n          { title: \"备注\", key: \"remark\" },\r\n          { title: \"分割比例(%)\", key: \"percent\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    buttonload2(v) {\r\n      this.$refs.showAlarmModel.buttonload2 = v;\r\n    },\r\n    isButtonload(v) {\r\n      this.$refs.showAlarmModel.buttonload = v;\r\n    },\r\n    isShows(t) {\r\n      this.$refs.showAlarmModel.show = t;\r\n      if (this.$refs.showAlarmModel.show == false) {\r\n        this.number2++;\r\n        this.isT = t;\r\n      } else if (this.$refs.showAlarmModel.show == true) {\r\n        this.number2 = 0;\r\n        this.isT = !t;\r\n      }\r\n      if (this.isT && this.number2 < 10) {\r\n        this.isShows(t);\r\n      }\r\n    },\r\n    nextCheck() {\r\n      this.showAlarmModel = true;\r\n      this.isShows(true);\r\n      // this.$refs.showAlarmModel.show=true\r\n      this.$refs.showAlarmModel.tableData = [];\r\n      this.$refs.showAlarmModel.resultList = [];\r\n      this.$refs.showAlarmModel.tableData1 = [];\r\n      this.$refs.showAlarmModel.resultList1 = [];\r\n      this.$refs.showAlarmModel.tableData2 = [];\r\n      this.$refs.showAlarmModel.resultList2 = [];\r\n      this.$refs.showAlarmModel.tableData3 = [];\r\n      this.$refs.showAlarmModel.resultList3 = [];\r\n      this.$refs.showAlarmModel.tableData4 = [];\r\n      this.$refs.showAlarmModel.resultList4 = [];\r\n      this.$refs.showAlarmModel.tableData5 = [];\r\n      this.$refs.showAlarmModel.resultList5 = [];\r\n      // setTimeout(() => {\r\n      this.showJhModel = false;\r\n      // this.showAlarmModel=true;\r\n      this.$refs.showAlarmModel.activeButton = 6;\r\n      // },100)\r\n    },\r\n    alarmClose() {\r\n      // window.history.go(0);\r\n      this.showAlarmModel = false;\r\n      this.$refs.showAlarmModel.show = false;\r\n    },\r\n    checkCancel() {\r\n      this.showJhModel = false;\r\n    },\r\n    alarmCheck() {},\r\n    selectChange() {\r\n      getDepartments(this.accountObj.company).then((res) => {\r\n        this.resCenterList = res.data;\r\n        this.resCenterListSize = res.data.length;\r\n        this.accountObj.country = res.data[0].id;\r\n      });\r\n    },\r\n    // 计算定额\r\n    getQuota: (ammeterid, startdate, enddate, callback) => {\r\n      if (ammeterid && startdate && enddate) {\r\n        getElectrQuota(ammeterid, startdate, enddate).then((res) => {\r\n          if (callback) callback(res);\r\n          else callback();\r\n        });\r\n      }\r\n    },\r\n    //删除时检查退回台账是否解除与归集单关联\r\n    getTem: (pcid, callback) => {\r\n      selectByPcid(pcid).then((res) => {\r\n        if (callback) callback(res);\r\n        else callback();\r\n      });\r\n    },\r\n    //翻页时先确认数据是否保存\r\n    handlePage(value) {\r\n      let b = false;\r\n      let data = this.insideData;\r\n      let array = [];\r\n      data.forEach(function (item, index, input) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n      this.pageNum = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //改变表格可显示数据数量时先确认数据是否保存\r\n    handlePageSize(value) {\r\n      let b = false;\r\n      let data = this.insideData;\r\n      let array = [];\r\n      data.forEach(function (item, index, input) {\r\n        if (item.editType == 1) {\r\n          b = true;\r\n          array.push(item);\r\n        }\r\n      });\r\n      if (b) {\r\n        this.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>您有已编辑信息还没有保存，是否保存？</p>\",\r\n          onOk: () => {\r\n            this.submitData(array);\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      }\r\n      this.pageSize = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //向后台请求数据\r\n    getAccountMessages() {\r\n      this.setElectroyType();\r\n      let params = this.accountObj;\r\n      params.pageNum = this.pageNum;\r\n      params.pageSize = this.pageSize;\r\n      let req = {\r\n        url: \"/business/account/selfAccountList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      this.accountTb.loading = true;\r\n      axios\r\n        .request(req)\r\n        .then((res) => {\r\n          this.accountTb.loading = false;\r\n          if (res.data) {\r\n            array = res.data.rows;\r\n            array.push(this.suntotal(array)); //小计\r\n            accountTotal(this.accountObj).then((res) => {\r\n              //合计\r\n              let alltotal = res.data;\r\n              alltotal.total = \"合计\";\r\n              alltotal.projectname = \"合计\";\r\n              alltotal._disabled = true;\r\n              array.push(alltotal);\r\n            });\r\n            this.insideData = array;\r\n            this.pageTotal = res.data.total || 0;\r\n            // debugger\r\n            this.setNewField(res.data.rows);\r\n            this.setMyStyle(res.data.rows.length);\r\n            this.editIndex = -1;\r\n            this.columnsIndex = -1;\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //小计\r\n    suntotal(array) {\r\n      let curusedreadings = 0;\r\n      let transformerullage = 0;\r\n      let ticketmoney = 0;\r\n      let taxticketmoney = 0;\r\n      let taxamount = 0;\r\n      let ullagemoney = 0;\r\n      let accountmoney = 0;\r\n      let inputtaxticketmoney = 0;\r\n      let inputticketmoney = 0;\r\n      array.forEach(function (item) {\r\n        if (item.effective === 1) {\r\n          curusedreadings += item.curusedreadings;\r\n          transformerullage += item.transformerullage;\r\n          ticketmoney += item.ticketmoney;\r\n          taxticketmoney += item.taxticketmoney;\r\n          taxamount += item.taxamount;\r\n          inputtaxticketmoney += item.inputtaxticketmoney;\r\n          inputticketmoney += item.inputticketmoney;\r\n          ullagemoney += item.ullagemoney;\r\n          accountmoney += item.accountmoney;\r\n        }\r\n      });\r\n      return {\r\n        curusedreadings: curusedreadings,\r\n        transformerullage: transformerullage,\r\n        ticketmoney: ticketmoney.toFixed(2),\r\n        taxticketmoney: taxticketmoney.toFixed(2),\r\n        taxamount: taxamount.toFixed(2),\r\n        inputtaxticketmoney: inputtaxticketmoney.toFixed(2),\r\n        inputticketmoney: inputticketmoney.toFixed(2),\r\n        ullagemoney: ullagemoney.toFixed(2),\r\n        accountmoney: accountmoney.toFixed(2),\r\n        total: \"小计\",\r\n        projectname: \"小计\",\r\n        _disabled: true,\r\n      };\r\n    },\r\n    searchList() {\r\n      this.pageNum = 1;\r\n      this.getAccountMessages();\r\n    },\r\n    onResetHandle() {\r\n      this.accountObj = {\r\n        accountno: dates[0].code, //期号,默认当前月\r\n        substation: \"\", //支局\r\n        projectname: \"\", //项目名称\r\n        prevtotalreadings: null, //上期止度\r\n        ammetercode: \"\", //电表户号/协议编码\r\n        isreturn: \"\", //是否退回\r\n        company: this.CompanyList[0].id,\r\n        userId: \"\",\r\n        accountType: \"1\", //台账类型\r\n        supplybureauammetercode: \"\",\r\n      };\r\n\r\n      this.accountObj.version = indexData.version;\r\n      this.userName = \"\";\r\n      this.classifications = [];\r\n      this.selectChange();\r\n      this.getAccountMessages();\r\n    },\r\n    //保存可编辑表格的初始化数据\r\n    setNewField(data) {\r\n      data.forEach(function (item) {\r\n        item.old_startdate = item.startdate;\r\n        item.old_prevtotalreadings = item.prevtotalreadings;\r\n        item.multtimes = item.magnification;\r\n        item.old_enddate = item.enddate;\r\n        item.old_curtotalreadings = item.curtotalreadings;\r\n        item.old_transformerullage = item.transformerullage;\r\n        item.old_taxticketmoney = item.inputtaxticketmoney;\r\n        item.old_ticketmoney = item.inputticketmoney;\r\n        item.old_ullagemoney = item.ullagemoney;\r\n        item.old_prevhighreadings = item.prevhighreadings;\r\n        item.old_prevflatreadings = item.prevflatreadings;\r\n        item.old_prevlowreadings = item.prevlowreadings;\r\n\r\n        item.old_curhighreadings = item.curhighreadings;\r\n        item.old_curflatreadings = item.curflatreadings;\r\n        item.old_curlowreadings = item.curlowreadings;\r\n        item.old_curtotalreadings = item.curtotalreadings;\r\n\r\n        item.version = indexData.version;\r\n        item.editType = 0;\r\n        item.isFPG = judging_editability1(item);\r\n        item.isWB = judging_editability(item);\r\n        if (!item.remark) item.remark = \"\";\r\n        if (!item.bz) item.bz = \"\";\r\n        item.transformerullage = judgeNumber(item.transformerullage);\r\n        // item.supplybureauammetercode = judgeNumber(item.supplybureauammetercode);\r\n        item.inputtaxticketmoney = judgeNumber(item.inputtaxticketmoney);\r\n        item.inputticketmoney = judgeNumber(item.inputticketmoney);\r\n        item.taxticketmoney = judgeNumber(item.taxticketmoney);\r\n        item.ticketmoney = judgeNumber(item.ticketmoney);\r\n        item.ullagemoney = judgeNumber(item.ullagemoney);\r\n        item.curusedreadings = judgeNumber(item.curusedreadings);\r\n        item.accountmoney = judgeNumber(item.accountmoney);\r\n        if ((item.taxrate == null || item.taxrate == 0) && item.total == null) {\r\n          item.taxrate = \"13\";\r\n        }\r\n        if (item.taxrate && item.taxamount == null) {\r\n          item.taxamount = countTaxamount(item);\r\n        }\r\n      });\r\n    },\r\n    //计算 用电量,总电量,单价,总费用,浮动比.\r\n    calculateAll(row) {\r\n      console.log(row, \"row\");\r\n      row.curusedreadings = _calculateUsedReadings(row);\r\n      row.totalusedreadings = _calculateTotalReadings(row);\r\n      if (row.ischangeammeter == 1 && row.isnew == 1) {\r\n        if (row.oldbillpower > 0) {\r\n          let total = Math.abs(row.totalusedreadings) + Math.abs(row.oldbillpower);\r\n          let category = row.category;\r\n          let ammeteruse = row.ammeteruse; //电表用途\r\n          if (judge_negate(category) || judge_recovery(ammeteruse)) {\r\n            total = -total;\r\n          }\r\n          row.totalusedreadings = total;\r\n        }\r\n        let remark = row.remark;\r\n        if (remark.indexOf(\"换表\") == -1) {\r\n          row.remark += \"换表，结清原电表读数【\" + row.oldbillpower + \"】；\";\r\n        }\r\n      }\r\n      if (row.ticketmoney || row.taxticketmoney) {\r\n        row.accountmoney = _calculateAccountMoney(row);\r\n        row.unitpirce = _calculateUnitPriceByUsedMoney(row);\r\n      }\r\n      row.quotereadingsratio = _calculateQuotereadingsratio(row);\r\n    },\r\n    //暂存\r\n    temporaryStorage() {\r\n      let array = [];\r\n      this.insideData.forEach(function (item) {\r\n        if (item.editType == 1) {\r\n          array.push(item);\r\n        }\r\n      });\r\n      let data = array;\r\n      //--------------------------------------\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n          item.startyear = yyyymmdd.yyyy;\r\n          item.startmonth = yyyymmdd.mm;\r\n          yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n          item.endyear = yyyymmdd.yyyy;\r\n          item.endmonth = yyyymmdd.mm;\r\n          item.accountno = no;\r\n          submitData.push(item);\r\n          number++;\r\n        });\r\n        if (submitData.length > 0) {\r\n          temporaryStorage(submitData).then((res) => {\r\n            if (res.data.num > 0) {\r\n              that.$Message.info({\r\n                content: \"成功暂存\" + res.data.num + \"条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n            }\r\n            that.pageNum = 1;\r\n            that.getAccountMessages();\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //四川能耗稽核流程\r\n    preserveSc() {\r\n      let arr = [];\r\n      this.ammeterids.forEach((item1) => {\r\n        if (arr.indexOf(item1) == -1) {\r\n          arr.push(item1);\r\n        }\r\n      });\r\n      this.$refs.checkResult.ammeterids = arr;\r\n      this.showJhModel = true;\r\n    },\r\n    async preserve() {\r\n      let dataL = this.$refs.accountTable.getSelection();\r\n\r\n      let b = false;\r\n      let data = this.insideData;\r\n      let array = [];\r\n      let that = this;\r\n\r\n      let version = indexData.version;\r\n      for (let i = 0; i < dataL.length; i++) {\r\n        if (dataL[i].editType == 1) {\r\n          if (\"sc\" == version && dataL[i].electrotype && dataL[i].electrotype > 1400) {\r\n            if (\r\n              dataL[i].stationcode5gr == null ||\r\n              dataL[i].stationcode5gr == undefined ||\r\n              dataL[i].stationcode5gr == \"\"\r\n            ) {\r\n              this.errorTips(\r\n                \"电表/协议编号【\" +\r\n                  dataL[i].ammetercode +\r\n                  \"】，项目名称【\" +\r\n                  dataL[i].projectname +\r\n                  \"】关联局站的5GR站址为空，请完善局站信息，或者5GR有效性清单失效，请联系无线管理员。\"\r\n              );\r\n            }\r\n          }\r\n          //截止日期校验\r\n          let result = await this.handleEndDate(dataL[i], dataL[i].enddate);\r\n          if (result) {\r\n            this.errorTips(result);\r\n            return;\r\n          }\r\n          let maxdegree = parseInt(dataL[i].maxdegree); //翻表度数,即电表的最大度数\r\n          if (maxdegree != null && maxdegree > 0) {\r\n            if (dataL[i].curtotalreadings > maxdegree) {\r\n              that.errorTips(\"本期止度不能大于翻表值：\" + maxdegree);\r\n            } else {\r\n              b = true;\r\n              array.push(dataL[i]);\r\n            }\r\n          } else {\r\n            b = true;\r\n            array.push(dataL[i]);\r\n          }\r\n        }\r\n      }\r\n      // });\r\n      if (b) {\r\n        this.submitData(array);\r\n      } else {\r\n        this.errorTips(\"没有可保存数据\");\r\n      }\r\n    },\r\n    async getContractInfo(stationCode) {\r\n      this.contractCount = 0;\r\n      await validContractList({ stationCode: stationCode }).then((res) => {\r\n        if (res.data) {\r\n          this.contractCount = res.data.length;\r\n        }\r\n      });\r\n    },\r\n    //提交数据\r\n    submitData(data) {\r\n      // this.ammeterids=[];\r\n      let a = [];\r\n      let str = \"\";\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        let str1 = \"\";\r\n        data.forEach(function (item) {\r\n          let obj = requiredFieldValidator(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n        });\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          that.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n        if (submitData.length > 0) {\r\n          //四川能耗需做稽核流程\r\n          this.submit = submitData;\r\n          this.submit2 = submitData;\r\n          editOwn(submitData).then((res) => {\r\n            if (res.data.num > 0) {\r\n              that.$Message.info({\r\n                content: \"成功保存\" + res.data.num + \"条数据\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n            }\r\n\r\n            if (res.data.str.length > 0) {\r\n              that.errorTips(res.data.str);\r\n            }\r\n            that.pageNum = 1;\r\n            that.getAccountMessages();\r\n          });\r\n        }\r\n      }\r\n    },\r\n    submitChange(indexList) {\r\n      let data = [];\r\n      this.submit2.map((item, index) => {\r\n        indexList.map((item2) => {\r\n          if (index == item2) {\r\n            data.push(item);\r\n          }\r\n        });\r\n      });\r\n      this.submit = data;\r\n    },\r\n    getAuditResultNew(data) {\r\n      let arr = [];\r\n      data.forEach((item) => {\r\n        arr.push(item.pcid);\r\n      });\r\n      let param = {\r\n        pcids: arr,\r\n      };\r\n      getAuditResultNew_QXM(param).then((res2) => {\r\n        this.auditResultList = res2.data;\r\n\r\n        if (this.auditResultList && this.auditResultList.length == 0) {\r\n          this.number++;\r\n          this.isQuery = true;\r\n          // }\r\n        } else if (data.length != this.auditResultList.length) {\r\n          this.number++;\r\n          this.isQuery = true;\r\n        } else if (data.length == this.auditResultList.length) {\r\n          this.isQuery = false;\r\n          this.number = 0;\r\n        } else {\r\n          this.isQuery = false;\r\n          this.number = 0;\r\n        }\r\n        if (this.isQuery && this.number < 5) {\r\n          setTimeout(() => this.getAuditResultNew(data), 6000);\r\n        } else {\r\n          this.auditResultList.forEach((item) => {\r\n            this.$refs.showAlarmModel.resultList.push(item.msg);\r\n            this.$refs.showAlarmModel.tableData.push(item.powerAuditEntity);\r\n\r\n            if (item.staute == \"失败\") {\r\n              // if(item.powerAuditEntity.mutiJtlteCodes=='是'\r\n              // || item.powerAuditEntity.electricityPrices=='否'\r\n              // || item.powerAuditEntity.addressConsistence=='否'\r\n              // || item.powerAuditEntity.reimbursementCycle=='否' || item.powerAuditEntity.electricityContinuity=='否' ||\r\n              // item.powerAuditEntity.shareAccuracy=='否' ||\r\n              // item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n              // item.powerAuditEntity.paymentConsistence=='否'){\r\n              if (item.powerAuditEntity.mutiJtlteCodes == \"是\") {\r\n                //一站多表\r\n                this.$refs.showAlarmModel.tableData4.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList4.push(item.msg);\r\n              }\r\n              if (item.powerAuditEntity.electricityPrices == \"否\") {\r\n                //单价异常\r\n                this.$refs.showAlarmModel.tableData5.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList5.push(item.msg);\r\n              }\r\n              if (\r\n                item.powerAuditEntity.addressConsistence == \"否\" ||\r\n                item.powerAuditEntity.reimbursementCycle == \"否\" ||\r\n                item.powerAuditEntity.electricityContinuity == \"否\" ||\r\n                item.powerAuditEntity.shareAccuracy == \"否\" ||\r\n                // item.powerAuditEntity.exclusiveAccuracy=='否'||\r\n                item.powerAuditEntity.paymentConsistence == \"否\" ||\r\n                item.powerAuditEntity.fluctuateContinuity == \"否\"\r\n              ) {\r\n                this.$refs.showAlarmModel.tableData2.push(item.powerAuditEntity); //其他异常\r\n                this.$refs.showAlarmModel.resultList2.push(item.msg);\r\n              }\r\n              // }\r\n            } else {\r\n              if (\r\n                // item.powerAuditEntity.electricityRationality == \"是\" && //电量合理性(省内大数据)\r\n                // item.powerAuditEntity.exclusiveAccuracy == \"是\" && //局站独享共享设置\r\n                item.powerAuditEntity.periodicAnomaly == \"是\" //台账周期合理性\r\n              ) {\r\n                this.$refs.showAlarmModel.tableData1.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList1.push(item.msg);\r\n              } else {\r\n                this.$refs.showAlarmModel.tableData3.push(item.powerAuditEntity);\r\n                this.$refs.showAlarmModel.resultList3.push(item.msg);\r\n              }\r\n            }\r\n            if (this.auditResultList.length > 0) {\r\n              this.auditResultList[this.auditResultList.length - 1].progress =\r\n                ((this.auditResultList.length * 1) / data.length) * 1;\r\n            }\r\n            this.$refs.showAlarmModel.processData = Number(item.progress) * 100;\r\n            this.$refs.showAlarmModel.scrollList();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    save(value) {\r\n      var that = this;\r\n      //进度条页面链接websoket后调用，传jh字段不走保存流程，走稽核流程\r\n      if (value == 1) {\r\n        that.submit[0].jh = \"1\";\r\n        that.submit[0].ymmc = \"自有电费台账\";\r\n        this.getAuditResultNew(that.submit);\r\n      } else {\r\n        if (that.submit[0].hasOwnProperty(\"jh\")) {\r\n          delete that.submit[0].jh;\r\n        }\r\n        if (this.name == \"current\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAccount();\r\n          }\r\n        } else if (this.name == \"all\") {\r\n          if (this.$refs.showAlarmModel.fromGuijidan == 1) {\r\n            this.selectedFQK();\r\n          } else {\r\n            this.selectedAllAccount();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //加入归集单，全部非强控\r\n    selectedFQK() {\r\n      let that = this;\r\n      that.setElectroyType();\r\n      that.$refs.addBillPer.initAmmeter(\r\n        that.$refs.showAlarmModel.selectIds3,\r\n        1,\r\n        this.accountObj.country\r\n      );\r\n    },\r\n    //添加修改峰平谷值的备注\r\n    addFremark(row) {\r\n      let old = row.old_prevhighreadings;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (\r\n        row.curhighreadings != null &&\r\n        row.curhighreadings > 0 &&\r\n        this.editprevhighreadings > row.curhighreadings\r\n      ) {\r\n        this.errorTips(\"起始峰值不能大于截止峰值\" + row.curhighreadings);\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editprevhighreadings = old;\r\n        }, 200);\r\n      } else {\r\n        row.prevhighreadings = this.editprevhighreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n        if (row.old_prevhighreadings != row.prevhighreadings) {\r\n          row.remark +=\r\n            \"本期起始峰值 从\" +\r\n            row.old_prevhighreadings +\r\n            \"修改为\" +\r\n            row.prevhighreadings +\r\n            \"; \";\r\n        }\r\n        //修改opflag修改起日期 原来数字加\r\n        let opflag = row.opflag;\r\n        if (opflag != 3 && opflag != 5 && opflag != 7 && opflag != 9) {\r\n          row.opflag = opflag + 3;\r\n        }\r\n      }\r\n    },\r\n    //起始平值变换记录备注\r\n    addPremark(row) {\r\n      let old = row.old_prevflatreadings;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (\r\n        row.curflatreadings != null &&\r\n        row.curflatreadings > 0 &&\r\n        this.editprevflatreadings > row.curflatreadings\r\n      ) {\r\n        this.errorTips(\"起始平值不能大于截止平值\" + row.curflatreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editprevflatreadings = old;\r\n        }, 200);\r\n      } else {\r\n        row.prevflatreadings = this.editprevflatreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n        if (row.old_prevflatreadings != row.prevflatreadings) {\r\n          row.remark +=\r\n            \"本期起始平值 从\" +\r\n            row.old_prevflatreadings +\r\n            \"修改为\" +\r\n            row.prevflatreadings +\r\n            \"; \";\r\n        }\r\n        //修改opflag修改起日期 原来数字加\r\n        let opflag = row.opflag;\r\n        if (opflag != 3 && opflag != 5 && opflag != 7 && opflag != 9) {\r\n          row.opflag = opflag + 3;\r\n        }\r\n      }\r\n    },\r\n    //起始谷值变换记录备注\r\n    addGremark(row) {\r\n      let old = row.old_prevlowreadings;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (\r\n        row.curlowreadings != null &&\r\n        row.curlowreadings > 0 &&\r\n        this.editprevlowreadings > row.curlowreadings\r\n      ) {\r\n        this.errorTips(\"起始谷值不能大于截止谷值\" + row.curlowreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editprevlowreadings = old;\r\n        }, 200);\r\n      } else {\r\n        row.prevlowreadings = this.editprevlowreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n        if (row.old_prevlowreadings != row.prevlowreadings) {\r\n          row.remark +=\r\n            \"本期起始谷值 从\" +\r\n            row.old_prevlowreadings +\r\n            \"修改为\" +\r\n            row.prevlowreadings +\r\n            \"; \";\r\n        }\r\n        //修改opflag修改起日期 原来数字加\r\n        let opflag = row.opflag;\r\n        if (opflag != 3 && opflag != 5 && opflag != 7 && opflag != 9) {\r\n          row.opflag = opflag + 3;\r\n        }\r\n      }\r\n    },\r\n    setcurhighreadings(row) {\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (this.editcurhighreadings < row.prevhighreadings) {\r\n        this.errorTips(\"截止峰值不能小于起始峰值\" + row.prevhighreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editcurhighreadings = row.curhighreadings;\r\n        }, 200);\r\n      } else {\r\n        row.curhighreadings = this.editcurhighreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n      }\r\n    },\r\n    setcurflatreadings(row) {\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (this.editcurflatreadings < row.prevflatreadings) {\r\n        this.errorTips(\"截止平值不能小于起始平值\" + row.prevflatreadings);\r\n\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editcurflatreadings = row.curflatreadings;\r\n        }, 200);\r\n      } else {\r\n        row.curflatreadings = this.editcurflatreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n      }\r\n    },\r\n    setcurlowreadings(row) {\r\n      let next = row.ifNext;\r\n      if (row.ifNext) {\r\n        return;\r\n      } else if (this.editcurlowreadings < row.prevlowreadings) {\r\n        this.errorTips(\"截止谷值不能小于起始谷值\" + row.prevlowreadings);\r\n        var that = this;\r\n        setTimeout(function () {\r\n          that.editcurlowreadings = row.curlowreadings;\r\n        }, 200);\r\n      } else {\r\n        row.curlowreadings = this.editcurlowreadings;\r\n        row.editType = 1;\r\n        this.calculateAll(row);\r\n      }\r\n    },\r\n    setFPG(row) {\r\n      let item = {\r\n        prevhighreadings: row.prevhighreadings,\r\n        prevflatreadings: row.prevflatreadings,\r\n        prevlowreadings: row.prevlowreadings,\r\n        curhighreadings: row.curhighreadings,\r\n        curflatreadings: row.curflatreadings,\r\n        curlowreadings: row.curlowreadings,\r\n        highreadings: parseFloat(this.edithighreadings.toFixed(2)),\r\n        flatreadings: parseFloat(this.editflatreadings.toFixed(2)),\r\n        lowreadings: parseFloat(this.editlowreadings.toFixed(2)),\r\n        magnification: row.magnification,\r\n      };\r\n      let amount = _calculateUsedReadingsForType_2(item);\r\n\r\n      if (amount < 0) {\r\n        //计算用电量\r\n        this.errorTips(\r\n          \"计算用电量小于0(\" +\r\n            amount +\r\n            \"),请确认峰平谷加减电量值！\" +\r\n            \"加减电量(峰值)\" +\r\n            parseFloat(this.edithighreadings.toFixed(2)) +\r\n            \",加减电量(平值)\" +\r\n            parseFloat(this.editflatreadings.toFixed(2)) +\r\n            \",加减电量(谷值)\" +\r\n            parseFloat(this.editlowreadings.toFixed(2)) +\r\n            \"当前用电量\" +\r\n            row.curusedreadings\r\n        );\r\n        // return;\r\n      }\r\n      row.highreadings = parseFloat(this.edithighreadings.toFixed(2));\r\n      row.flatreadings = parseFloat(this.editflatreadings.toFixed(2));\r\n      row.lowreadings = parseFloat(this.editlowreadings.toFixed(2));\r\n      row.editType = 1;\r\n      this.calculateAll(row);\r\n    },\r\n    //一键删除数据\r\n    deleteAll() {\r\n      this.$Modal.confirm({\r\n        title: \"提示\",\r\n        content: \"<p>确定一键删除吗？</p>\",\r\n        onOk: () => {\r\n          this.accountTb.loading = true;\r\n          let params = this.accountObj;\r\n          params.removeAllFlag = true;\r\n          delete params.pageSize;\r\n          delete params.pageNum;\r\n          removeAll(params).then((res) => {\r\n            this.accountTb.loading = false;\r\n            if (res.data.num > 0) {\r\n              this.$Message.success(\"一键删除成功\");\r\n              this.searchList();\r\n            } else {\r\n              this.$Message.error(\"一键删除失败\");\r\n            }\r\n          });\r\n        },\r\n        onCancel: () => {},\r\n      });\r\n    },\r\n    //删除行数据\r\n    remove() {\r\n      let version = indexData.version;\r\n      let data = this.$refs.accountTable.getSelection();\r\n      if (data == null || data.length === 0) {\r\n        this.errorTips(\"请选择要删除的台账\");\r\n        return;\r\n      }\r\n      let ids = \"\";\r\n      let that = this;\r\n      let str = \"\";\r\n      data.forEach(function (item) {\r\n        if (item.ifNext) {\r\n          str +=\r\n            \"电表/协议编号为【\" +\r\n            item.ammetercode +\r\n            \"】当期【\" +\r\n            item.accountno +\r\n            \"期】台账之后已有正式数据，不能删除！\";\r\n        }\r\n        ids += item.pcid + \",\";\r\n      });\r\n      if (ids.length > 0 && str.length === 0) {\r\n        that.$Modal.confirm({\r\n          title: \"提示\",\r\n          content: \"<p>是否确认删除选中信息？</p>\",\r\n          onOk: () => {\r\n            removeOwn(ids).then((res) => {\r\n              if (res.data.num > 0) {\r\n                that.$Message.info({\r\n                  content: \"成功删除\" + res.data.num + \"条数据\",\r\n                  duration: 10,\r\n                  closable: true,\r\n                });\r\n              }\r\n\r\n              if (res.data.str.length > 0) {\r\n                that.errorTips(res.data.str);\r\n              }\r\n              that.searchList();\r\n            });\r\n          },\r\n          onCancel: () => {},\r\n        });\r\n      } else {\r\n        that.errorTips(str);\r\n      }\r\n    },\r\n    //span点击事件将span换成输入框并且获取焦点\r\n    selectCall(row, index, columns, str) {\r\n      this.editStartDate = row.startdate;\r\n      this.editEndDate = row.enddate;\r\n      this.editPrevtotalreadings =\r\n        row.prevtotalreadings == null || row.prevtotalreadings === 0\r\n          ? null\r\n          : row.prevtotalreadings;\r\n      this.editcurtotalreadings =\r\n        row.curtotalreadings == null || row.curtotalreadings === 0\r\n          ? null\r\n          : row.curtotalreadings;\r\n      this.edittransformerullage =\r\n        row.transformerullage == null || row.transformerullage === 0\r\n          ? null\r\n          : row.transformerullage;\r\n      this.edittaxticketmoney =\r\n        row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n          ? null\r\n          : row.inputtaxticketmoney;\r\n      this.editticketmoney =\r\n        row.inputticketmoney == null || row.inputticketmoney === 0\r\n          ? null\r\n          : row.inputticketmoney;\r\n      this.editullagemoney =\r\n        row.ullagemoney == null || row.ullagemoney === 0 ? null : row.ullagemoney;\r\n      this.edittaxrate =\r\n        row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n      this.editremark = row.bz;\r\n      this.editIndex = index;\r\n      this.columnsIndex = columns;\r\n\r\n      let a = this;\r\n      setTimeout(function () {\r\n        if (columns != 8) {\r\n          a.$refs[str + index + columns].focus();\r\n        }\r\n      }, 200);\r\n    },\r\n    //根据列号返回对应的列名\r\n    enterOperate(number) {\r\n      let str = \"\";\r\n      let data = null;\r\n      switch (number) {\r\n        case 1:\r\n          str = \"startdate\";\r\n          data = this.editStartDate;\r\n          break;\r\n        case 2:\r\n          str = \"enddate\";\r\n          data = this.editEndDate;\r\n          break;\r\n        case 3:\r\n          str = \"prevtotalreadings\";\r\n          data = this.editPrevtotalreadings;\r\n          break;\r\n        case 4:\r\n          str = \"curtotalreadings\";\r\n          data = this.editcurtotalreadings;\r\n          break;\r\n        case 5:\r\n          str = \"transformerullage\";\r\n          data = this.edittransformerullage;\r\n          break;\r\n        case 6:\r\n          str = \"inputticketmoney\";\r\n          data = this.editticketmoney;\r\n          break;\r\n        case 7:\r\n          str = \"inputtaxticketmoney\";\r\n          data = this.edittaxticketmoney;\r\n          break;\r\n        case 8:\r\n          str = \"taxrate\";\r\n          data = this.edittaxrate;\r\n          break;\r\n        case 9:\r\n          str = \"ullagemoney\";\r\n          data = this.editullagemoney;\r\n          break;\r\n        case 10:\r\n          str = \"remark\";\r\n          data = this.editremark;\r\n          break;\r\n      }\r\n      return { str: str, data: data };\r\n    },\r\n    //输入数据验证\r\n    validate() {\r\n      if (this.columnsIndex === 10) {\r\n        this.validateRemark();\r\n        return;\r\n      }\r\n      let val = this.enterOperate(this.columnsIndex).data;\r\n      if (val) {\r\n        if (testNumber(val)) {\r\n          switch (this.columnsIndex) {\r\n            case 1:\r\n              this.validateStartdate();\r\n              break;\r\n            case 2:\r\n              this.validateEnddate();\r\n              break;\r\n            case 3:\r\n              this.validatePrevtotalreadings();\r\n              break;\r\n            case 4:\r\n              this.validateCurtotalreadings();\r\n              break;\r\n            case 5:\r\n              this.validateTransformerullage();\r\n              break;\r\n            case 6:\r\n              this.validateTicketmoney();\r\n              break;\r\n            case 7:\r\n              this.validateTaxticketmoney();\r\n              break;\r\n            case 9:\r\n              this.validateUllagemoney();\r\n              break;\r\n          }\r\n        } else {\r\n          this.errorTips(\"请输入数字！\");\r\n        }\r\n      }\r\n    },\r\n    //验证错误弹出提示框并跳转到下一格\r\n    errorTips(str) {\r\n      this.$Notice.error({\r\n        title: \"提示\",\r\n        desc: str,\r\n        duration: 10,\r\n      });\r\n    },\r\n    //跳转到下一格\r\n    nextCell(data) {\r\n      let index = data.editIndex;\r\n      let columns = data.columnsIndex;\r\n      let row = \"\";\r\n      if (index === -1 && columns === -1) {\r\n        index = 0;\r\n        columns = 1;\r\n      } else if (index > -1 && columns === 10) {\r\n        //当跳转的最后一行最后一格的时候\r\n        if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n          index = 0;\r\n        } else {\r\n          index++;\r\n        }\r\n        columns = 1;\r\n      } else {\r\n        row = data.insideData[index];\r\n        //无表或峰平谷表的时候\r\n        if (row && (row.isFPG || row.isWB) && columns >= 2 && columns <= 4) {\r\n          if (row.isWB) {\r\n            columns += 4;\r\n          } else {\r\n            columns += 3;\r\n          }\r\n        } else {\r\n          columns += 1;\r\n        }\r\n        //有下期的台账不能改\r\n        if (row.ifNext) {\r\n          if (columns < 5) {\r\n            columns = 5;\r\n          }\r\n        }\r\n      }\r\n      data.editIndex = index;\r\n      data.columnsIndex = columns;\r\n      row = data.insideData[index];\r\n      if (row) {\r\n        data.editStartDate = row.startdate;\r\n        data.editEndDate = row.enddate;\r\n        data.editPrevtotalreadings =\r\n          row.prevtotalreadings == null || row.prevtotalreadings === 0\r\n            ? null\r\n            : row.prevtotalreadings;\r\n        data.editcurtotalreadings =\r\n          row.curtotalreadings == null || row.curtotalreadings === 0\r\n            ? null\r\n            : row.curtotalreadings;\r\n        data.edittransformerullage =\r\n          row.transformerullage == null || row.transformerullage === 0\r\n            ? null\r\n            : row.transformerullage;\r\n        data.edittaxticketmoney =\r\n          row.inputtaxticketmoney == null || row.inputtaxticketmoney === 0\r\n            ? null\r\n            : row.inputtaxticketmoney;\r\n        data.editticketmoney =\r\n          row.inputticketmoney == null || row.inputticketmoney === 0\r\n            ? null\r\n            : row.inputticketmoney;\r\n        data.editullagemoney =\r\n          row.ullagemoney == null || row.ullagemoney === 0 ? null : row.ullagemoney;\r\n        data.edittaxrate =\r\n          row.taxrate == null || row.taxrate === 0 ? null : parseInt(row.taxrate) + \"\";\r\n        data.editremark = row.bz;\r\n      }\r\n\r\n      setTimeout(function () {\r\n        if (columns != 8) {\r\n          data.$refs[data.enterOperate(columns).str + index + columns].focus();\r\n        }\r\n      }, 200);\r\n    },\r\n    //验证起始时间\r\n    validateStartdate() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editStartDate;\r\n      if (val != data.old_startdate) {\r\n        // 验证起始时间方法\r\n        let result = _verify_StartDate(data, val, \"可大于\");\r\n        if (result) {\r\n          //失败就弹出提示内容，并将数据恢复初始化\r\n          this.errorTips(result);\r\n          this.myStyle[this.editIndex].startdate = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].startdate = \"myspan\";\r\n          this.startModal = true;\r\n        }\r\n      } else if (val == data.old_startdate) {\r\n        data.startdate = val;\r\n      }\r\n    },\r\n    //验证截止时间\r\n    async validateEnddate() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editEndDate;\r\n      if (val != data.old_enddate) {\r\n        // 验证截止日期方法\r\n        let result = await this.handleEndDate(data, val);\r\n        if (result) {\r\n          this.errorTips(result);\r\n          this.myStyle[this.editIndex].enddate = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].enddate = \"myspan\";\r\n\r\n          this.updateenddate(data, val);\r\n        }\r\n      } else if (val == data.old_enddate) {\r\n        data.enddate = val;\r\n      }\r\n    },\r\n    //截止日期处理\r\n    async handleEndDate(data, val) {\r\n      //直供电有上传日期才可以修改到月底 directsupplyflag 1直供2转供\r\n      let fType = \"\";\r\n      let curval = stringToDate(val); //输入值\r\n      let nowdate = stringToDate(getCurrentDate()); //当天\r\n      if (data.directsupplyflag == 1 && curval > nowdate) {\r\n        let files = await axios\r\n          .request({\r\n            url: \"/common/attachments/list\",\r\n            method: \"post\",\r\n            data: {\r\n              areaCode: \"sc\",\r\n              busiAlias: \"附件(台账)\",\r\n              busiId: data.pcid + \"\",\r\n              categoryCode: \"file\",\r\n              pageNum: 1,\r\n              pageSize: 20,\r\n            },\r\n          })\r\n          .then((res) => {\r\n            return res.data.rows;\r\n          });\r\n        if (files.length == 0) {\r\n          return \"截止日期需小于等于当前时间，超过当前时间需上传附件\";\r\n        } else {\r\n          fType = \"限制期号\"; //截止日期不限制期号的最后一天（月底）\r\n        }\r\n      }\r\n      // 验证截止日期方法\r\n      let result = _verify_EndDate(data, val, fType);\r\n      return result;\r\n    },\r\n    updateenddate(data, val) {\r\n      data.enddate = val;\r\n      data.editType = 1;\r\n      //计算定额\r\n      this.getQuota(data.ammeterid, data.startdate, data.enddate, (result) => {\r\n        if (result.data.code === 0) {\r\n          data.quotareadings = Math.round(result.data.msg);\r\n        } else {\r\n          data.quotareadings = 0;\r\n        }\r\n        this.calculateAll(data);\r\n      });\r\n      this.nowdatediff = GetDateDiff(data.startdate, data.enddate);\r\n    },\r\n    //验证起度\r\n    validatePrevtotalreadings() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editPrevtotalreadings;\r\n      val = parseFloat(val);\r\n      if (val != data.old_prevtotalreadings) {\r\n        //验证\r\n        let result = _verify_PrevTotalReadings(data, val);\r\n        if (result.string) {\r\n          this.errorTips(result.string);\r\n          this.myStyle[this.editIndex].prevtotalreadings = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].prevtotalreadings = \"myspan\";\r\n          this.ifMaxdegree = result.b;\r\n          this.qdModal = true;\r\n        }\r\n      } else if (val == data.old_prevtotalreadings) {\r\n        data.prevtotalreadings = val;\r\n        this.calculateAll(data);\r\n      }\r\n    },\r\n    //验证止度\r\n    validateCurtotalreadings() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editcurtotalreadings;\r\n\r\n      if (val != data.old_curtotalreadings) {\r\n        val = parseFloat(val);\r\n        let result = _verify_CurTotalReadings(data, val);\r\n        if (result.string) {\r\n          this.errorTips(result.string);\r\n          this.myStyle[this.editIndex].curtotalreadings = \"errorStle\";\r\n        } else {\r\n          this.myStyle[this.editIndex].curtotalreadings = \"myspan\";\r\n\r\n          this.updateCurtotalreadings(data, val, result);\r\n        }\r\n\r\n        let computreading = 0;\r\n        this.Accountqur.ammeterid = data.ammeterid;\r\n        this.Accountqur.startdate = data.startdate;\r\n        this.Accountqur.enddate = data.enddate;\r\n      } else if (val == data.old_curtotalreadings) {\r\n        data.curtotalreadings = val;\r\n\r\n        this.calculateAll(data);\r\n      }\r\n    },\r\n    updateCurtotalreadings(data, val, result) {\r\n      data.curtotalreadings = val;\r\n      data.editType = 1;\r\n      let b = result.b;\r\n      if (b === true) {\r\n        this.fbModal = true;\r\n      } else if (b === false) {\r\n        this.qxfbModal = true;\r\n      } else {\r\n        this.calculateAll(data);\r\n      }\r\n      if (indexData.version == \"sc\") {\r\n        //验证上期台账是否完成报账\r\n        axios\r\n          .request({\r\n            url: \"/business/accountSC/valOldAcount\",\r\n            method: \"post\",\r\n            params: { pcid: data.pcid, ammeterid: data.ammeterid },\r\n          })\r\n          .then((res) => {\r\n            let msg = \"\";\r\n            if (res.data.msg) msg = res.data.msg;\r\n            if (data.startdate.endsWith(\"0101\"))\r\n              msg += \"【该起始日期是默认值，请注意修改】\";\r\n            if (msg != \"\")\r\n              this.$Notice.warning({\r\n                title: \"注意\",\r\n                desc: \"电表/协议【\" + data.ammetercode + \"】\" + msg,\r\n                duration: 10,\r\n              });\r\n            if (res.data.acc) {\r\n              Object.assign(data, {\r\n                unitpirceold: res.data.acc.unitpirce,\r\n                curusedreadingsold: res.data.acc.curusedreadings,\r\n              });\r\n            }\r\n          });\r\n      }\r\n    },\r\n    //验证电损\r\n    validateTransformerullage() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.edittransformerullage;\r\n      let flag = false;\r\n      if (val != data.old_transformerullage) {\r\n        if (\r\n          val != data.old_transformerullage &&\r\n          indexData.version == \"sc\" &&\r\n          data.curusedreadings > 0 &&\r\n          parseFloat(parseFloat(val) / parseFloat(data.curusedreadings)) > 0.1 &&\r\n          (data.ammeteruse == 1 || [1, 3].includes(data.ammeteruse))\r\n        )\r\n          flag = true;\r\n        if (flag) {\r\n          let result = \"电损与实际电量比值已经超过10%，不允许保存\";\r\n          this.errorTips(result);\r\n\r\n          this.myStyle[this.editIndex].transformerullage = \"errorStle\";\r\n          data.transformerullage = 0;\r\n        } else {\r\n          val = parseFloat(val);\r\n          data.transformerullage = val;\r\n          data.editType = 1;\r\n          this.calculateAll(data);\r\n        }\r\n      } else if (val == data.old_transformerullage) {\r\n        data.transformerullage = val;\r\n        this.calculateAll(data);\r\n      }\r\n    },\r\n    //验证专票\r\n    validateTaxticketmoney() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.edittaxticketmoney;\r\n      if (val != data.old_taxticketmoney) {\r\n        val = parseFloat(val);\r\n        data.inputtaxticketmoney = _verify_Money(data, val);\r\n        data.editType = 1;\r\n        data.taxticketmoney = calculateActualMoney(data, val);\r\n        data.taxamount = countTaxamount(data);\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_taxticketmoney) {\r\n        data.inputtaxticketmoney = val;\r\n        data.taxticketmoney = calculateActualMoney(data, val);\r\n        data.taxamount = countTaxamount(data);\r\n        this.calculateAll(data);\r\n      }\r\n      this.validateUnitPrice(data);\r\n      this.validateavemoney(data);\r\n    },\r\n    //验证普票\r\n    validateTicketmoney() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editticketmoney;\r\n      if (val != data.old_ticketmoney) {\r\n        val = parseFloat(val);\r\n        data.inputticketmoney = _verify_Money(data, val);\r\n        data.editType = 1;\r\n        data.ticketmoney = calculateActualMoney(data, val);\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_ticketmoney) {\r\n        data.inputticketmoney = val;\r\n        data.ticketmoney = calculateActualMoney(data, val);\r\n        this.calculateAll(data);\r\n      }\r\n      this.validateUnitPrice(data);\r\n      this.validateavemoney(data);\r\n    },\r\n    //验证其他费用\r\n    validateUllagemoney() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editullagemoney;\r\n      if (val != data.old_ullagemoney) {\r\n        val = parseFloat(val);\r\n        data.ullagemoney = _verify_Money(data, val);\r\n        data.editType = 1;\r\n        this.calculateAll(data);\r\n      } else if (val == data.old_ullagemoney) {\r\n        data.ullagemoney = val;\r\n        this.calculateAll(data);\r\n      }\r\n      this.validateUnitPrice(data);\r\n      this.validateavemoney(data);\r\n    },\r\n    //备注\r\n    validateRemark() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editremark;\r\n      data.bz = val;\r\n      data.editType = 1;\r\n    },\r\n    validateavemoney(data) {\r\n      let version = indexData.version;\r\n      if (\"sc\" == version) {\r\n        let accountmoney = data.accountmoney;\r\n        let aveold = judgeNumber(data.aveaccountmoneyold);\r\n        if (this.nowdatediff == 0)\r\n          this.nowdatediff = GetDateDiff(data.startdate, data.enddate);\r\n        if (aveold != 0 && (accountmoney / this.nowdatediff).toFixed(2) - aveold > 0) {\r\n          if (((accountmoney / this.nowdatediff).toFixed(2) - aveold) / aveold > 0.3)\r\n            this.$Notice.warning({\r\n              title: \"温馨提示\",\r\n              desc:\r\n                \"电表/协议【\" +\r\n                data.ammetercode +\r\n                \"】\" +\r\n                \"日均电费环比值已经超过30%，请注意填写备注说明！\",\r\n              duration: 10,\r\n            });\r\n        }\r\n      }\r\n    },\r\n    //验证单价\r\n    validateUnitPrice(data) {\r\n      let version = indexData.version;\r\n      let category = data.category; //电表描述类型\r\n      let directsupplyflag = data.directsupplyflag; //1直供2转供\r\n      let ammeteruse = data.ammeteruse; //电表用途\r\n      let unitpirce = data.unitpirce; //台账单价\r\n      if (!judge_negate(category) && !judge_recovery(ammeteruse) && judge_yb(category)) {\r\n        if (unitpirce) {\r\n          if (unitpirce != null && unitpirce < unitpirceMax1) {\r\n            // if (unitpirce < unitpirceMin || unitpirce > unitpirceMax) {\r\n            this.errorTips(\r\n              \"单价范围必须大于0.1元，此台账单价: \" + unitpirce + \"不在范围内，请确认！\"\r\n            );\r\n          }\r\n          // if (directsupplyflag == 1 && ammeteruse == 1) {\r\n          //   if (unitpirce) {\r\n          //     if (unitpirce >= 0.25 && unitpirce < 0.5) {\r\n          //       this.errorTips(\r\n          //         \"直供电单价(\" +\r\n          //           unitpirce +\r\n          //           \")【0.25<=\" +\r\n          //           unitpirce +\r\n          //           \"<0.5】\" +\r\n          //           \"请确认单价是否存在错误\"\r\n          //       );\r\n          //     } else if (unitpirce < 0.25 || unitpirce > 1.2) {\r\n          //       this.errorTips(\r\n          //         \"直供电单价(\" + unitpirce + \")【小于0.25或大于1.20】\" + \"单价，请确认！\"\r\n          //       );\r\n          //     }\r\n          //   }\r\n          // } else if (directsupplyflag == 2) {\r\n          //   if (unitpirce >= 0.3 && unitpirce < 0.6) {\r\n          //     this.errorTips(\r\n          //       \"转供电单价(\" +\r\n          //         unitpirce +\r\n          //         \")【0.3<=\" +\r\n          //         unitpirce +\r\n          //         \"<0.6】\" +\r\n          //         \"请确认单价是否存在错误\"\r\n          //     );\r\n          //   } else if (unitpirce < 0.3) {\r\n          //     this.errorTips(\r\n          //       \"转供电单价(\" +\r\n          //         unitpirce +\r\n          //         \")【\" +\r\n          //         unitpirce +\r\n          //         \"<0.3】\" +\r\n          //         \"单价错误，请确认！\"\r\n          //     );\r\n          //   } else if (unitpirce > 1.5) {\r\n          //     this.errorTips(\r\n          //       \"转供电单价(\" +\r\n          //         unitpirce +\r\n          //         \")【\" +\r\n          //         unitpirce +\r\n          //         \">1.5】\" +\r\n          //         \"请确认单价是否存在错误\"\r\n          //     );\r\n          //   }\r\n          // }\r\n        }\r\n      }\r\n    },\r\n    openModal(index) {\r\n      this.meterModal = true; //弹出框显示\r\n      let row = this.insideData[index];\r\n      if (row.accountno == dates[1].code) {\r\n        let obj = {\r\n          accountno: dates[0].code,\r\n          ammeterid: row.ammeterid,\r\n        };\r\n        selectByAmmeterId(obj).then((res) => {\r\n          row.nextData = res.data;\r\n        });\r\n      }\r\n      if (row) {\r\n        if (row.ifNext) {\r\n          this.readonly = true;\r\n        } else {\r\n          this.readonly = false;\r\n        }\r\n\r\n        this.currentRow = row; //给弹出框绑定数据\r\n        this.editprevhighreadings =\r\n          row.prevhighreadings === null ? 0 : row.prevhighreadings;\r\n        this.editprevflatreadings =\r\n          row.prevflatreadings === null ? 0 : row.prevflatreadings;\r\n        this.editprevlowreadings = row.prevlowreadings === null ? 0 : row.prevlowreadings;\r\n        this.editcurhighreadings = row.curhighreadings === null ? 0 : row.curhighreadings;\r\n        this.editcurflatreadings = row.curflatreadings === null ? 0 : row.curflatreadings;\r\n        this.editcurlowreadings = row.curlowreadings === null ? 0 : row.curlowreadings;\r\n        if (this.version == \"sc\") {\r\n          this.edithighreadings = row.highreadings === null ? 0 : row.highreadings;\r\n          this.editflatreadings = row.flatreadings === null ? 0 : row.flatreadings;\r\n          this.editlowreadings = row.lowreadings === null ? 0 : row.lowreadings;\r\n        }\r\n      }\r\n    },\r\n    openAddBillPerModal(name) {\r\n      this.name = name;\r\n      if (name === \"current\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJ();\r\n        // } else {\r\n        this.selectedAccount();\r\n        // }\r\n      } else if (name === \"all\") {\r\n        //需要稽核\r\n        // if (this.hasButtonPerm(\"jhsd\")) {\r\n        //   this.addPreserveGJAll();\r\n        // } else {\r\n        this.selectedAllAccount();\r\n        // }\r\n      }\r\n    },\r\n    //加入归集单，全部有效台账\r\n    selectedAllAccount() {\r\n      let that = this;\r\n      that.setElectroyType();\r\n      that.spinShow = true;\r\n      selectIdsByParams(this.accountObj).then((res) => {\r\n        that.spinShow = false;\r\n        if (res.data.str) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: res.data.str,\r\n            duration: 0,\r\n          });\r\n        }\r\n        if (res.data.ids) {\r\n          if (res.data.ids.length == 0) {\r\n            that.errorTips(\"无有效数据可加入归集单\");\r\n          } else {\r\n            that.$refs.addBillPer.initAmmeter(\r\n              res.data.ids,\r\n              // this.$refs.showAlarmModel.selectIds1,\r\n              1,\r\n              this.accountObj.country\r\n            );\r\n          }\r\n        } else {\r\n          that.errorTips(\"无有效数据可加入归集单\");\r\n        }\r\n      });\r\n    },\r\n    //加入归集单\r\n    addPreserveGJ() {\r\n      let dataL = this.$refs.accountTable.getSelection();\r\n      if (dataL == null || dataL.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        this.addSubmitDataGJ(dataL);\r\n      }\r\n    },\r\n\r\n    //加入归集单\r\n    addPreserveGJAll() {\r\n      let params = this.accountObj;\r\n      params.pageNum = 1;\r\n      params.pageSize = 20000;\r\n      let req = {\r\n        url: \"/business/account/selfAccountList\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      let array = [];\r\n      this.accountTb.loading = true;\r\n      axios.request(req).then((res) => {\r\n        this.accountTb.loading = false;\r\n        let dataL = res.data.rows;\r\n        this.addSubmitDataGJ(dataL);\r\n      });\r\n    },\r\n    //提交归集单数据\r\n    addSubmitDataGJ(data1) {\r\n      let a = [];\r\n      let b = 1;\r\n      let data = data1.filter((item) => item.effective == 1);\r\n      let str = \"\";\r\n      let str1 = \"\";\r\n      let version = indexData.version;\r\n      if (data != null && data.length > 0) {\r\n        let number = 0;\r\n        let submitData = [];\r\n        let no = this.accountObj.accountno;\r\n        let that = this;\r\n        data.forEach(function (item) {\r\n          let obj = requiredFieldValidator(item);\r\n          if (obj.result) {\r\n            let yyyymmdd = cutDate_yyyymmdd(item.startdate);\r\n            item.startyear = yyyymmdd.yyyy;\r\n            item.startmonth = yyyymmdd.mm;\r\n            yyyymmdd = cutDate_yyyymmdd(item.enddate);\r\n            item.endyear = yyyymmdd.yyyy;\r\n            item.endmonth = yyyymmdd.mm;\r\n            a.push(item.ammeterid);\r\n            submitData.push(item);\r\n            number++;\r\n          } else {\r\n            str +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账验证没有通过：【\" +\r\n              obj.str +\r\n              \"】；\";\r\n          }\r\n\r\n          if (item.magnificationerr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账倍率【\" +\r\n              item.magnification +\r\n              \"】与电表倍率【\" +\r\n              item.ammmulttimes +\r\n              \"】不一致！  <br /> \";\r\n          }\r\n\r\n          if (item.percenterr == 2) {\r\n            str1 +=\r\n              \"电表/协议编号为【\" +\r\n              item.ammetercode +\r\n              \"】的台账分割比例【\" +\r\n              item.percent +\r\n              \"】与电表分割比例【\" +\r\n              item.ammpercent +\r\n              \"】不一致！ <br /> \";\r\n          }\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status != 1) {\r\n            b = 3;\r\n          }\r\n          if (\r\n            \"sc\" == version &&\r\n            item.unitpirce > 2 &&\r\n            (item.unitpirceold == null || item.unitpirceold < 2) &&\r\n            that.valiprice\r\n          ) {\r\n            b = 4;\r\n            str += item.ammetercode + \",\";\r\n          }\r\n        });\r\n        if (b == 1) {\r\n          if (str1.length > 0) {\r\n            this.$Notice.warning({\r\n              title: \"注意\",\r\n              desc: str1,\r\n              duration: 0,\r\n            });\r\n          }\r\n          if (submitData.length > 0) {\r\n            this.submit = submitData;\r\n            this.submit2 = submitData;\r\n            this.preserveSc();\r\n          }\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\r\n            \"电表/协议编号为【\" +\r\n              str +\r\n              \"】的台账单价已经超过2元，请发OA邮件给省公司审核，通过后才可加入归集单！\"\r\n          );\r\n        }\r\n        that.ammeterids = a;\r\n        if (str.length > 0) {\r\n          that.errorTips(str);\r\n        }\r\n        if (str1.length > 0) {\r\n          that.$Notice.warning({\r\n            title: \"注意\",\r\n            desc: str1,\r\n            duration: 0,\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //加入归集单，已选择的台账\r\n    selectedAccount() {\r\n      let data = this.$refs.accountTable.getSelection();\r\n      let b = 1;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要加入归集单的台账\");\r\n      } else {\r\n        let ids = [];\r\n        data.forEach(function (item) {\r\n          if (item.effective != 1) {\r\n            b = 2;\r\n          }\r\n          if (item.status === 5) {\r\n            b = 3;\r\n          }\r\n          if (item.status === 4) {\r\n            b = 4;\r\n          }\r\n          ids.push(item.pcid);\r\n        });\r\n        if (b === 1) {\r\n          this.$refs.addBillPer.initAmmeter(ids, 1, this.accountObj.country);\r\n        } else if (b === 2) {\r\n          this.errorTips(\"选中的台账中存在临时数据，请先保存再加入归集单！\");\r\n        } else if (b === 3) {\r\n          this.errorTips(\"退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮\");\r\n        } else if (b === 4) {\r\n          this.errorTips(\"选择的台账有已加入归集单的台账，不能加入其他归集单\");\r\n        }\r\n      }\r\n    },\r\n    setMyStyle(length) {\r\n      this.myStyle = [];\r\n      for (var i = 0; i < length; i++) {\r\n        this.myStyle.push({\r\n          startdate: \"myspan\",\r\n          enddate: \"myspan\",\r\n          prevtotalreadings: \"myspan\",\r\n          curtotalreadings: \"myspan\",\r\n          transformerullage: \"myspan\",\r\n          inputtaxticketmoney: \"myspan\",\r\n          inputticketmoney: \"myspan\",\r\n          ullagemoney: \"myspan\",\r\n          taxrate: \"myspan\",\r\n          remark: \"myspan\",\r\n        });\r\n      }\r\n    },\r\n    submitChange1(data) {\r\n      this.submit = data;\r\n    },\r\n    refresh() {\r\n      if (this.$refs.showAlarmModel.fromGuijidan != 1) {\r\n        // window.history.go(0);\r\n        let obj = this;\r\n        obj.showAlarmModel = false;\r\n        setTimeout(function () {\r\n          obj.getAccountMessages();\r\n        }, 200);\r\n      } else {\r\n        this.showAlarmModel = true;\r\n      }\r\n    },\r\n    againJoin() {\r\n      let data = this.$refs.accountTable.getSelection();\r\n      let b = true;\r\n      var that = this;\r\n      if (data == null || data.length == 0) {\r\n        this.errorTips(\"请选择要重新加入归集单的台账\");\r\n      } else {\r\n        let againJoinIds = \"\";\r\n        data.forEach(function (item) {\r\n          let status = item.status;\r\n          if (status != 5) {\r\n            b = false;\r\n          } else {\r\n            againJoinIds += item.pcid + \",\";\r\n          }\r\n        });\r\n        if (b) {\r\n          againJoin(againJoinIds).then((res) => {\r\n            if (res.data.code == 0) {\r\n              that.$Message.info({\r\n                content: \"提示：操作成功\",\r\n                duration: 10,\r\n                closable: true,\r\n              });\r\n              that.getAccountMessages();\r\n            }\r\n          });\r\n        } else {\r\n          that.errorTips(\"只有已退回的台账才能重新加入归集单\");\r\n        }\r\n      }\r\n    },\r\n    beforeLoadData(data, str) {\r\n      var cols = [],\r\n        keys = [];\r\n      for (var i = 0; i < this.accountTb.exportcolumns.length; i++) {\r\n        cols.push(this.accountTb.exportcolumns[i].title);\r\n        keys.push(this.accountTb.exportcolumns[i].key);\r\n      }\r\n      const params = {\r\n        title: cols,\r\n        key: keys,\r\n        data: data,\r\n        autoWidth: true,\r\n        filename: str,\r\n      };\r\n      excel.export_array_to_excel(params);\r\n      return;\r\n    },\r\n    exportCsv(name) {\r\n      this.setElectroyType();\r\n      let params = this.accountObj;\r\n\r\n      if (name === \"current\") {\r\n        params.pageNum = this.pageNum;\r\n        params.pageSize = this.pageSize;\r\n      } else if (name === \"all\") {\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n      }\r\n      let req = {\r\n        url: \"/business/account/exportzy\",\r\n        method: \"get\",\r\n        params: params,\r\n      };\r\n      this.spinShow = true;\r\n      axios\r\n        .file(req)\r\n        .then((res) => {\r\n          this.spinShow = false;\r\n          const content = res;\r\n          const blob = new Blob([content]);\r\n          const fileName = \"自有台账导出数据\" + \".xlsx\";\r\n          if (\"download\" in document.createElement(\"a\")) {\r\n            // 非IE下载\r\n            const elink = document.createElement(\"a\");\r\n            elink.download = fileName;\r\n            elink.style.display = \"none\";\r\n            elink.href = URL.createObjectURL(blob);\r\n            document.body.appendChild(elink);\r\n            elink.click();\r\n            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n            document.body.removeChild(elink);\r\n          } else {\r\n            // IE10+下载\r\n            navigator.msSaveBlob(blob, fileName);\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          console.log(err);\r\n        });\r\n    },\r\n    //专票税额\r\n    settaxrate() {\r\n      let val = this.edittaxrate;\r\n      let data = this.insideData[this.editIndex];\r\n      let taxticketmoney = data.taxticketmoney;\r\n      data.taxrate = val;\r\n      data.taxamount = countTaxamount(data);\r\n      data.editType = 1;\r\n    },\r\n    startModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editStartDate;\r\n      data.startdate = val; //修改起始日期\r\n      data.editType = 1;\r\n      //计算定额\r\n      this.getQuota(data.ammeterid, data.startdate, data.enddate, (result) => {\r\n        if (result.data.code === 0) {\r\n          data.quotareadings = Math.round(result.data.msg);\r\n        } else {\r\n          data.quotareadings = 0;\r\n        }\r\n        this.calculateAll(data);\r\n      });\r\n      let opflag = data.opflag;\r\n      //修改opflag修改起日期 原来数字加\r\n      if (opflag != 4 && opflag != 6 && opflag != 7 && opflag != 9) {\r\n        data.opflag = opflag + 4;\r\n      }\r\n      data.remark += \"本期起始日期 从\" + data.old_startdate + \"修改为\" + val + \"; \";\r\n      this.startModal = false;\r\n      this.nextCell(this);\r\n    },\r\n    cancel() {\r\n      this.nextCell(this);\r\n    },\r\n    hcyzstartdate(lett, index) {\r\n      let data = lett.insideData[index];\r\n      let val = lett.editStartDate;\r\n      if (val) {\r\n        if (testNumber(val)) {\r\n          if (val == data.old_startdate) {\r\n            data.startdate = val;\r\n\r\n            lett.nextCell(lett);\r\n          } else {\r\n            lett.validate();\r\n          }\r\n        } else {\r\n          lett.errorTips(\"请输入数字！\");\r\n          lett.nextCell(lett);\r\n        }\r\n      } else {\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    qdModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      let val = this.editPrevtotalreadings;\r\n\r\n      data.prevtotalreadings = val;\r\n      let b = this.ifMaxdegree;\r\n      if (b === true) {\r\n        this.fbModal = true;\r\n      } else if (b === false) {\r\n        this.qxfbModal = true;\r\n      } else {\r\n        this.calculateAll(data);\r\n      }\r\n\r\n      data.editType = 1;\r\n      let opflag = data.opflag;\r\n      //增加4 修改起日期 原来数字加\r\n      if (opflag != 2 && opflag != 5 && opflag != 6 && opflag != 9) {\r\n        data.opflag = opflag + 2;\r\n      }\r\n      data.remark += \"本期起度 从\" + data.old_prevtotalreadings + \"修改为\" + val + \"; \";\r\n\r\n      this.qdModal = false;\r\n      this.nextCell(this);\r\n    },\r\n    fbModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      data.ifMaxdegree = true;\r\n      this.fbModal = false;\r\n\r\n      this.calculateAll(data);\r\n    },\r\n    qxfbModalOk() {\r\n      let data = this.insideData[this.editIndex];\r\n      data.ifMaxdegree = false;\r\n      this.qxfbModal = false;\r\n\r\n      this.calculateAll(data);\r\n    },\r\n    hcyzprevtotalreadings(lett, index) {\r\n      let data = lett.insideData[index];\r\n      let val = lett.editPrevtotalreadings;\r\n      if (val) {\r\n        if (testNumber(val)) {\r\n          if (val == data.old_prevtotalreadings) {\r\n            data.prevtotalreadings = val;\r\n            lett.nextCell(lett);\r\n          } else {\r\n            lett.validate();\r\n          }\r\n        } else {\r\n          lett.errorTips(\"请输入数字！\");\r\n          lett.nextCell(lett);\r\n        }\r\n      } else {\r\n        lett.nextCell(lett);\r\n      }\r\n    },\r\n    qdcancel() {\r\n      if (this.columnsIndex === 4) {\r\n        let data = this.insideData[this.editIndex].old_prevtotalreadings;\r\n        this.editPrevtotalreadings = data;\r\n        this.insideData[this.editIndex].prevtotalreadings = data;\r\n\r\n        this.$refs[\"curtotalreadings\" + this.editIndex + this.columnsIndex].focus();\r\n      } else if (this.columnsIndex === 5) {\r\n        let data = this.insideData[this.editIndex].old_curtotalreadings;\r\n        this.editcurtotalreadings = data;\r\n        this.insideData[this.editIndex].curtotalreadings = data;\r\n\r\n        this.$refs[\"transformerullage\" + this.editIndex + this.columnsIndex].focus();\r\n      }\r\n    },\r\n    accountnoChange() {\r\n      this.searchList();\r\n    },\r\n    setElectroyType() {\r\n      let types = this.classifications;\r\n      this.accountObj.electrotype = types[types.length - 1];\r\n    },\r\n    self() {\r\n      var lett = this;\r\n      if (lett.startModal) {\r\n        lett.startModalOk();\r\n      } else if (lett.qdModal) {\r\n        lett.qdModalOk();\r\n      } else if (lett.fbModal) {\r\n        lett.fbModalOk();\r\n      } else if (lett.qxfbModal) {\r\n        lett.qxfbModalOk();\r\n      } else {\r\n        let index = lett.editIndex;\r\n        let columns = lett.columnsIndex;\r\n        if (index === -1 && columns === -1) {\r\n          index = 0;\r\n          columns = 1;\r\n          lett.editIndex = index;\r\n          lett.columnsIndex = columns;\r\n          lett.editStartDate = lett.insideData[index].startdate;\r\n          setTimeout(function () {\r\n            lett.$refs[lett.enterOperate(columns).str + index + columns].focus();\r\n          }, 200);\r\n        } else if (columns === 1) {\r\n          lett.hcyzstartdate(lett, index);\r\n        } else if (columns === 3) {\r\n          lett.hcyzprevtotalreadings(lett, index);\r\n        } else {\r\n          lett.validate();\r\n          lett.nextCell(lett);\r\n        }\r\n      }\r\n    },\r\n    handleFormatError(file) {\r\n      this.errorTips(file.name + \" 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件\");\r\n    },\r\n    handleProgress(event, file) {\r\n      this.$Message.info({\r\n        content: file.name + \" 正在上传。\",\r\n      });\r\n    },\r\n    handleUploadSuccess() {},\r\n    onExcelUpload(file) {\r\n      if (file.size > 1024 * 1024 * 5) {\r\n        this.errorTips(\"文件大小超过限制！\");\r\n        return;\r\n      }\r\n      if (!file) {\r\n        this.errorTips(\"请选择要上传的文件！\");\r\n        return;\r\n      }\r\n      let fileName = file.name.lastIndexOf(\".\"); //取到文件名开始到最后一个点的长度\r\n      let fileNameLength = file.name.length; //取到文件名长度\r\n      let fileFormat = file.name.substring(fileName + 1, fileNameLength); //截\r\n      if (\"xls\" != fileFormat && \"xlsx\" != fileFormat) {\r\n        return;\r\n      }\r\n      let param = { version: indexData.version };\r\n      let excel = { file: file };\r\n      let that = this;\r\n      that.spinShow = true;\r\n      axios\r\n        .request({\r\n          url: \"/business/account/uploadExcel\",\r\n          method: \"post\",\r\n          data: Object.assign({}, param, excel),\r\n        })\r\n        .then((res) => {\r\n          that.spinShow = false;\r\n          if (res.data.number > 0) {\r\n            that.$Message.info({\r\n              content: \"成功导入\" + res.data.number + \"条数据\",\r\n            });\r\n          } else {\r\n            that.errorTips(\"导入数据失败，请检查数据是否填写正确\");\r\n          }\r\n          if (res.data.list) {\r\n            that.export.run = true;\r\n            that.beforeLoadData(res.data.list, \"导入数据反馈\");\r\n            that.pageNum = 1;\r\n            that.getAccountMessages();\r\n          }\r\n        });\r\n      return false;\r\n    },\r\n    getDataFromModal(data, flag) {\r\n      this.chooseResponseCenter(data);\r\n    },\r\n    chooseResponseCenter(data) {\r\n      if (!data) {\r\n        if (!this.accountObj.company) {\r\n          this.errorTips(\"请选择所属分公司\");\r\n        }\r\n        if (!this.accountObj.country) {\r\n          this.errorTips(\"请选择所属部门\");\r\n        }\r\n        this.$refs.queryPeople.modal.params = {\r\n          deptId: this.accountObj.country,\r\n          copnId: this.accountObj.company,\r\n        }; // 当前部门和分公司\r\n        this.$refs.queryPeople.choose(); //人员\r\n      } else {\r\n        this.userName = data.name;\r\n        this.accountObj.userId = data.id;\r\n      }\r\n    },\r\n    ellipsis(row) {\r\n      let value = row.remark + row.bz;\r\n      if (!value) return \"\";\r\n      if (value.length > 3) {\r\n        return value.slice(0, 3) + \"...\";\r\n      }\r\n      return value;\r\n    },\r\n    uploadFile(row) {\r\n      this.$refs.uploadFileModal.choose(row.pcid + \"\");\r\n    },\r\n  },\r\n  mounted() {\r\n    this.handleHeight(); //table高度自定义\r\n\r\n    this.accountObj.version = indexData.version;\r\n    this.categorys = blist(\"ammeterCategory\");\r\n    this.directsupplyflags = blist(\"directSupplyFlag\");\r\n    getUser().then((res) => {\r\n      if (res.data.companies != null && res.data.companies.length > 0) {\r\n        this.CompanyList = res.data.companies;\r\n        this.companyListSize = res.data.companies.length;\r\n        //初始化默认展示登陆用户的第一个分公司和分公司下的部门\r\n        this.accountObj.company = this.CompanyList[0].id;\r\n        getDepartments(this.accountObj.company).then((res) => {\r\n          this.resCenterList = res.data;\r\n          this.resCenterListSize = res.data.length;\r\n          this.accountObj.country = res.data[0].id;\r\n\r\n          this.getAccountMessages();\r\n        });\r\n      }\r\n      this.accountTb.columns = this.accountTb.headColumn2\r\n        .concat(this.accountTb.scColumn)\r\n        .concat(this.accountTb.tailColumn);\r\n      //开放全省\r\n      this.accountTb.columns = this.accountTb.columns.concat(this.accountTb.fileColumn);\r\n    });\r\n\r\n    getClassification().then((res) => {\r\n      //用电类型\r\n      this.classificationData = res.data;\r\n    });\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      loginId: (state) => state.user.loginId,\r\n    }),\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.page-class {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  .cl-table {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }\r\n  .button-bar {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n.yjjh > .ivu-modal-wrap > .ivu-modal {\r\n  top: 20px !important;\r\n}\r\n\r\n.mytable .ivu-table-cell {\r\n  padding-left: 1px;\r\n  padding-right: 1px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: normal;\r\n  word-break: break-all;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.mytable .myspan {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n}\r\n\r\n.mytable .errorStle {\r\n  width: 100%;\r\n  height: 20px;\r\n  display: block;\r\n  color: red;\r\n}\r\n\r\n.account .filter-divider {\r\n  margin: 0px;\r\n  text-align: center;\r\n}\r\n\r\n.account .header-bar-show {\r\n  max-height: 300px;\r\n  padding-top: 14px;\r\n  overflow: inherit;\r\n  border-bottom: 1px solid #e8eaec;\r\n}\r\n\r\n.account .header-bar-hide {\r\n  max-height: 0;\r\n  padding-top: 0;\r\n  overflow: hidden;\r\n  border-bottom: 0;\r\n}\r\n\r\n.mymodal p {\r\n  font-weight: bold;\r\n  font-size: 140%;\r\n  padding-left: 20px;\r\n}\r\n\r\n.account button {\r\n  margin-right: 10px;\r\n}\r\n::v-deep .cl-table .ivu-table-cell {\r\n  padding: 6px 4px !important;\r\n  .ivu-input {\r\n    padding: 4px !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}