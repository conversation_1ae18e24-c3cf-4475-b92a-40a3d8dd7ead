{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\PowerAccountList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\PowerAccountList.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCB7Ymxpc3R9IGZyb20gIkAvbGlicy90b29scyI7DQppbXBvcnQgYXhpb3MgZnJvbSAnQC9saWJzL2FwaS5yZXF1ZXN0JzsNCmltcG9ydCB7DQogIGdldENsYXNzaWZpY2F0aW9uLA0KICBnZXRDb3VudHJ5QnlVc2VySWQsDQogIGdldENvdW50cnlzZGF0YSwNCiAgZ2V0VXNlckJ5VXNlclJvbGUsDQogIGdldFVzZXJkYXRhDQp9IGZyb20gJ0AvYXBpL2Jhc2VkYXRhL2FtbWV0ZXIuanMnDQppbXBvcnQge2FsbEFjY291bnRUb3RhbH0gZnJvbSAnQC9hcGkvYWNjb3VudCc7DQppbXBvcnQge3dpZHRoc3R5bGV9IGZyb20gIkAvdmlldy9idXNpbmVzcy9tc3NBY2NvdW50YmlsbC9tc3NBY2NvdW50YmlsbGRhdGEiOw0KaW1wb3J0IENvdW50cnlNb2RhbCBmcm9tICJAL3ZpZXcvYmFzZWRhdGEvYW1tZXRlci9jb3VudHJ5TW9kYWwiOw0KaW1wb3J0IGV4Y2VsIGZyb20gJ0AvbGlicy9leGNlbCcNCmltcG9ydCBpbmRleERhdGEgZnJvbSAnQC9jb25maWcvaW5kZXgnDQppbXBvcnQgVXBsb2FkRmlsZU1vZGFsIGZyb20gIi4vdXBsb2FkRmlsZU1vZGFsIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiY2NvdW50TGlzdCIsDQogIGNvbXBvbmVudHM6IHtDb3VudHJ5TW9kYWwsIFVwbG9hZEZpbGVNb2RhbH0sDQogIGRhdGEoKSB7DQogICAgbGV0IG1hb3BhbyA9IChoLCBwYXJhbXMpID0+IHsNCiAgICAgIGxldCBzdHIgPSAnJw0KICAgICAgbGV0IGluZGV4ID0gcGFyYW1zLmluZGV4Ow0KICAgICAgaWYgKGluZGV4IDwgdGhpcy5wYWdlU2l6ZSAvIDIpIHsNCiAgICAgICAgc3RyID0gJ2JvdHRvbScNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHN0ciA9ICd0b3AnDQogICAgICB9DQoNCiAgICAgIHJldHVybiBoKCdkaXYnLCBbDQogICAgICAgIGgoJ1Rvb2x0aXAnLCB7DQogICAgICAgICAgcHJvcHM6IHtwbGFjZW1lbnQ6IHN0cn0NCiAgICAgICAgfSwgWw0KICAgICAgICAgIGgoJ3NwYW4nLCB7DQogICAgICAgICAgICBzdHlsZTogew0KICAgICAgICAgICAgICBkaXNwbGF5OiAnaW5saW5lLWJsb2NrJywNCiAgICAgICAgICAgICAgd2lkdGg6IHBhcmFtcy5jb2x1bW4uX3dpZHRoICogMC45ICsgJ3B4JywNCiAgICAgICAgICAgICAgb3ZlcmZsb3c6ICdoaWRkZW4nLA0KICAgICAgICAgICAgICB0ZXh0T3ZlcmZsb3c6ICdlbGxpcHNpcycsDQogICAgICAgICAgICAgIHdoaXRlU3BhY2U6ICdub3dyYXAnLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9LCBwYXJhbXMucm93LnJlbWFyayksDQogICAgICAgICAgaCgnc3BhbicsIHsNCiAgICAgICAgICAgIHNsb3Q6ICdjb250ZW50JywNCiAgICAgICAgICAgIHN0eWxlOiB7d2hpdGVTcGFjZTogJ25vcm1hbCcsIHdvcmRCcmVhazogJ2JyZWFrLWFsbCd9DQogICAgICAgICAgfSwgcGFyYW1zLnJvdy5yZW1hcmspDQogICAgICAgIF0pDQogICAgICBdKQ0KICAgIH0NCiAgICBsZXQgcmVuZGVyRGlyZWN0c3VwcGx5ZmxhZyA9IChoLCBwYXJhbXMpID0+IHsNCiAgICAgIHZhciBkaXJlY3RzdXBwbHlmbGFnID0gIiI7DQogICAgICBmb3IgKGxldCBpdGVtIG9mIHRoaXMuZGlyZWN0c3VwcGx5ZmxhZ3MpIHsNCiAgICAgICAgaWYgKGl0ZW0udHlwZUNvZGUgPT0gcGFyYW1zLnJvdy5kaXJlY3RzdXBwbHlmbGFnKSB7DQogICAgICAgICAgZGlyZWN0c3VwcGx5ZmxhZyA9IGl0ZW0udHlwZU5hbWU7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiBoKCJkaXYiLCBkaXJlY3RzdXBwbHlmbGFnKTsNCiAgICB9Ow0KICAgIGxldCByZW5kZXJTdGF0dXMgPSAoaCwge3JvdywgaW5kZXh9KSA9PiB7DQogICAgICB2YXIgc3RhdHVzID0gIiI7DQogICAgICBmb3IgKGxldCBpdGVtIG9mIHRoaXMuYWNjb3VudFN0YXR1cykgew0KICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSByb3cuc3RhdHVzKSB7DQogICAgICAgICAgc3RhdHVzID0gaXRlbS50eXBlTmFtZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGgoImRpdiIsIHN0YXR1cyk7DQogICAgfTsNCiAgICBsZXQgYmlsbFR5cGUgPSAoaCwge3JvdywgaW5kZXh9KSA9PiB7DQogICAgICB2YXIgdHlwZSA9ICcnDQogICAgICBpZiAocm93LnByb3BlcnR5ID09PSAyKSB7DQogICAgICAgIHR5cGUgPSAn6ZOB5aGUJw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdHlwZSA9ICfoh6rmnIknDQogICAgICB9DQogICAgICBpZiAocm93LmFjY291bnR0eXBlID09PSAyKSB7DQogICAgICAgIGlmIChyb3cuYWNjb3VudGVzdHlwZSA9PT0gMSkgew0KICAgICAgICAgIHR5cGUgKz0gJ+mihOS8sCcNCiAgICAgICAgfSBlbHNlIGlmIChyb3cuYWNjb3VudGVzdHlwZSA9PT0gMikgew0KICAgICAgICAgIHR5cGUgKz0gJ+aMgui0picNCiAgICAgICAgfSBlbHNlIGlmIChyb3cuYWNjb3VudGVzdHlwZSA9PT0gMykgew0KICAgICAgICAgIHR5cGUgKz0gJ+mihOS7mCcNCiAgICAgICAgfQ0KDQogICAgICB9DQogICAgICBpZiAocm93LnByb3BlcnR5ID09IG51bGwgfHwgcm93LmFjY291bnR0eXBlID09IG51bGwpIHsNCiAgICAgICAgdHlwZSA9ICcnDQogICAgICB9DQogICAgICByZXR1cm4gaCgiZGl2IiwgdHlwZSk7DQogICAgfQ0KDQogICAgbGV0IHJlbmRlckNhdGVnb3J5ID0gKGgsIHBhcmFtcykgPT4gew0KICAgICAgdmFyIGNhdGVnb3J5bmFtZSA9ICIiOw0KICAgICAgZm9yIChsZXQgaXRlbSBvZiB0aGlzLmNhdGVnb3J5cykgew0KICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSBwYXJhbXMucm93LmNhdGVnb3J5KSB7DQogICAgICAgICAgY2F0ZWdvcnluYW1lID0gaXRlbS50eXBlTmFtZTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGgoImRpdiIsIGNhdGVnb3J5bmFtZSk7DQogICAgfTsNCg0KICAgIGxldCByZW5kZXJBbW1ldGVydXNlID0gKGgsIHtyb3csIGluZGV4fSkgPT4gew0KICAgICAgdmFyIGFtbWV0ZXJ1c2UgPSAiIjsNCiAgICAgIGZvciAobGV0IGl0ZW0gb2YgdGhpcy5hbW1ldGVydXNlTGlzdCkgew0KICAgICAgICBpZiAoaXRlbS50eXBlQ29kZSA9PSByb3cuYW1tZXRlcnVzZSkgew0KICAgICAgICAgIGFtbWV0ZXJ1c2UgPSBpdGVtLnR5cGVOYW1lOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gaCgiZGl2IiwgYW1tZXRlcnVzZSk7DQogICAgfTsNCiAgICBsZXQgcGhvdG8gPSAoaCwge3JvdywgaW5kZXh9KSA9PiB7DQogICAgICBsZXQgdGhhdCA9IHRoaXMNCiAgICAgIGxldCBzdHIgPSAnJw0KICAgICAgaWYgKHJvdy5wcm9qZWN0bmFtZSAhPSAn5bCP6K6hJyAmJiByb3cucHJvamVjdG5hbWUgIT0gJ+WQiOiuoScpIHsNCiAgICAgICAgc3RyID0gJ+mZhOS7ticNCiAgICAgIH0NCiAgICAgIHJldHVybiBoKCJkaXYiLCBbaCgidSIsIHsNCiAgICAgICAgb246IHsNCiAgICAgICAgICBjbGljaygpIHsNCiAgICAgICAgICAgIC8v5omT5byA5by55Ye65qGGDQogICAgICAgICAgICBpZiAocm93LnByb2plY3RuYW1lICE9ICflsI/orqEnICYmIHJvdy5wcm9qZWN0bmFtZSAhPSAn5ZCI6K6hJykgew0KICAgICAgICAgICAgICB0aGF0LnVwbG9hZEZpbGUocm93KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSwgc3RyKV0pOw0KICAgIH07DQoNCiAgICBsZXQgY29sdW1uc19sbiA9IFsNCiAgICAgIHt0aXRsZTogJ+mhueebruWQjeensCcsIGtleTogJ3Byb2plY3RuYW1lJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogMTUwLH0sDQogICAgICB7dGl0bGU6ICfkvpvnlLXlsYDnlLXooajnvJblj7cnLCBrZXk6ICdzdXBwbHlidXJlYXVhbW1ldGVyY29kZScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDE1MCx9LA0KICAgICAge3RpdGxlOiAn5bGA56uZJywga2V5OiAnc3RhdGlvbk5hbWUnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiAxNTAsfSwNCiAgICAgIHt0aXRsZTogJ+i1hOa6kOWxgOermS/miL/lsYsv56uZ5Z2A57yW56CBJywga2V5OiAncmVzc3RhdGlvbmNvZGUnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiAxNTAsfSwNCiAgICAgIHt0aXRsZTogJ+WIhuWxgC/mlK/lsYAnLCBrZXk6ICdzdWJzdGF0aW9uJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogMTUwLH0sDQogICAgICB7dGl0bGU6ICflr7nlpJbnu5PnrpfnsbvlnosnLCBhbGlnbjogJ2NlbnRlcicsIGtleTogJ2RpcmVjdHN1cHBseWZsYWcnLCB3aWR0aDogODAsIHJlbmRlcjogcmVuZGVyRGlyZWN0c3VwcGx5ZmxhZ30sDQogICAgICB7dGl0bGU6ICfmnJ/lj7cnLCBrZXk6ICdhY2NvdW50bm8nLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn5omA5bGe5YiG5YWs5Y+4Jywga2V5OiAnY29tcGFueScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICfmiYDlsZ7pg6jpl6gnLCBrZXk6ICdjb3VudHJ5JywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+WAjeeOhycsIGtleTogJ21hZ25pZmljYXRpb24nLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn5a6a6aKdJywga2V5OiAncXVvdGFyZWFkaW5ncycsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICfmta7liqjmr5TvvIgl77yJJywga2V5OiAncXVvdGVyZWFkaW5nc3JhdGlvJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+i1t+Wni+aXpeacnycsIGtleTogJ3N0YXJ0ZGF0ZScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDEwMCx9LA0KICAgICAge3RpdGxlOiAn5oiq5q2i5pel5pyfJywga2V5OiAnZW5kZGF0ZScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDEwMCx9LA0KICAgICAge3RpdGxlOiAn5pys5pyf5bOw5q616LW35bqmJywga2V5OiAncHJldmhpZ2hyZWFkaW5ncycsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICfmnKzmnJ/lubPmrrXotbfluqYnLCBrZXk6ICdwcmV2ZmxhdHJlYWRpbmdzJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+acrOacn+iwt+autei1t+W6picsIGtleTogJ3ByZXZsb3dyZWFkaW5ncycsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICfmnKzmnJ/otbfluqYnLCBrZXk6ICdwcmV2dG90YWxyZWFkaW5ncycsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICfmnKzmnJ/ls7DmrrXmraLluqYnLCBrZXk6ICdjdXJoaWdocmVhZGluZ3MnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn5pys5pyf5bmz5q615q2i5bqmJywga2V5OiAnY3VyZmxhdHJlYWRpbmdzJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+acrOacn+iwt+auteatouW6picsIGtleTogJ2N1cmxvd3JlYWRpbmdzJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+acrOacn+atouW6picsIGtleTogJ2N1cnRvdGFscmVhZGluZ3MnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn55So55S16YePKOW6piknLCBrZXk6ICdjdXJ1c2VkcmVhZGluZ3MnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn55S15o2fKOW6piknLCBrZXk6ICd0cmFuc2Zvcm1lcnVsbGFnZScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICfmgLvnlLXph48o5bqmKScsIGtleTogJ3RvdGFsdXNlZHJlYWRpbmdzJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+eUteS7tyjlhYMpJywga2V5OiAndW5pdHBpcmNlJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+aZruelqOWQq+eojumHkeminSjlhYMpJywga2V5OiAnaW5wdXR0aWNrZXRtb25leScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDYwLH0sDQogICAgICB7dGl0bGU6ICfkuJPnpajlkKvnqI7ph5Hpop0o5YWDKScsIGtleTogJ2lucHV0dGF4dGlja2V0bW9uZXknLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA2MCx9LA0KICAgICAge3RpdGxlOiAn5a6e6ZmF5pmu56Wo5ZCr56iO6YeR6aKdKOWFgyknLCBrZXk6ICd0aWNrZXRtb25leScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICflrp7pmYXkuJPnpajlkKvnqI7ph5Hpop0o5YWDKScsIGtleTogJ3RheHRpY2tldG1vbmV5JywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+S4k+elqOeojueOh++8iCXvvIknLCBrZXk6ICd0YXhyYXRlJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogODAsfSwNCiAgICAgIHt0aXRsZTogJ+S4k+elqOeojuminScsIGtleTogJ3RheGFtb3VudCcsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDYwLH0sDQogICAgICB7dGl0bGU6ICflhbbku5Yo5YWDKScsIGtleTogJ3VsbGFnZW1vbmV5JywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+Wunue8tOi0ueeUqCjlhYMp5ZCr56iOJywga2V5OiAnYWNjb3VudG1vbmV5JywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+aAu+mHkemine+8iOS4jeWQq+eoju+8iScsIGtleTogJ3RvdGFsQkhTJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+WIhuWJsuavlOS+iyglKScsIGtleTogJ3BlcmNlbnQnLCB3aWR0aDogOTAsIGFsaWduOiAnY2VudGVyJ30sDQogICAgICB7dGl0bGU6ICflpIfms6gnLCBrZXk6ICdyZW1hcmsnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiAxNTAsIHJlbmRlcjogbWFvcGFvfSwNCiAgICAgIHt0aXRsZTogJ+exu+Wei+aPj+i/sCcsIGtleTogJ2NhdGVnb3J5bmFtZScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICfnlKjnlLXnsbvlnosnLCBrZXk6ICdlbGVjdHJvdHlwZW5hbWUnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5NCx9LA0KICAgICAge3RpdGxlOiAn55S16KGo55So6YCUJywga2V5OiAnYW1tZXRlcnVzZW5hbWUnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn5Y+w6LSm57G75Z6LJywga2V5OiAndHlwZScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICfnirbmgIEnLCBrZXk6ICdzdGF0dXMnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn5b2V5YWl5Lq6Jywga2V5OiAnaW5wdXRuYW1lJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+W9kumbhuWNleS6i+mhueWQjeensCcsIGtleTogJ25vdGUnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgIF0NCiAgICBsZXQgY29sdW1uc19zYyA9IFsNCiAgICAgIHt0aXRsZTogJ+mhueebruWQjeensCcsIGtleTogJ3Byb2plY3RuYW1lJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogMTUwLH0sDQogICAgICB7dGl0bGU6ICfnlLXooajmiLflj7cv5Y2P6K6u57yW56CBJywga2V5OiAnYW1tZXRlcmNvZGUnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiAxNTAsfSwNCiAgICAgIHt0aXRsZTogJ+WxgOermScsIGtleTogJ3N0YXRpb25OYW1lJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogMTUwLH0sDQogICAgICB7dGl0bGU6ICfotYTmupDlsYDnq5kv5oi/5bGLL+ermeWdgOe8lueggScsIGtleTogJ3Jlc3N0YXRpb25jb2RlJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogMTUwLH0sDQogICAgICB7dGl0bGU6ICfliIblsYAv5pSv5bGAJywga2V5OiAnc3Vic3RhdGlvbicsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDE1MCx9LA0KICAgICAge3RpdGxlOiAn5a+55aSW57uT566X57G75Z6LJywgYWxpZ246ICdjZW50ZXInLCBrZXk6ICdkaXJlY3RzdXBwbHlmbGFnJywgd2lkdGg6IDgwLCByZW5kZXI6IHJlbmRlckRpcmVjdHN1cHBseWZsYWd9LA0KICAgICAge3RpdGxlOiAn5pyf5Y+3Jywga2V5OiAnYWNjb3VudG5vJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+aJgOWxnuWIhuWFrOWPuCcsIGtleTogJ2NvbXBhbnknLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn5omA5bGe6YOo6ZeoJywga2V5OiAnY291bnRyeScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICflgI3njocnLCBrZXk6ICdtYWduaWZpY2F0aW9uJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+WumuminScsIGtleTogJ3F1b3RhcmVhZGluZ3MnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn5rWu5Yqo5q+U77yIJe+8iScsIGtleTogJ3F1b3RlcmVhZGluZ3NyYXRpbycsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICfotbflp4vml6XmnJ8nLCBrZXk6ICdzdGFydGRhdGUnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiAxMDAsfSwNCiAgICAgIHt0aXRsZTogJ+aIquatouaXpeacnycsIGtleTogJ2VuZGRhdGUnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiAxMDAsfSwNCiAgICAgIHt0aXRsZTogJ+acrOacn+WzsOautei1t+W6picsIGtleTogJ3ByZXZoaWdocmVhZGluZ3MnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn5pys5pyf5bOw5q615q2i5bqmJywga2V5OiAnY3VyaGlnaHJlYWRpbmdzJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+WzsOauteWKoOWHj+eUtemHjycsIGtleTogJ2hpZ2hyZWFkaW5ncycsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICfmnKzmnJ/lubPmrrXotbfluqYnLCBrZXk6ICdwcmV2ZmxhdHJlYWRpbmdzJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+acrOacn+W5s+auteatouW6picsIGtleTogJ2N1cmZsYXRyZWFkaW5ncycsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICflubPmrrXliqDlh4/nlLXph48nLCBrZXk6ICdmbGF0cmVhZGluZ3MnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn5pys5pyf6LC35q616LW35bqmJywga2V5OiAncHJldmxvd3JlYWRpbmdzJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+acrOacn+iwt+auteatouW6picsIGtleTogJ2N1cmxvd3JlYWRpbmdzJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+iwt+auteWKoOWHj+eUtemHjycsIGtleTogJ2xvd3JlYWRpbmdzJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+acrOacn+i1t+W6picsIGtleTogJ3ByZXZ0b3RhbHJlYWRpbmdzJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+acrOacn+atouW6picsIGtleTogJ2N1cnRvdGFscmVhZGluZ3MnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn55So55S16YePKOW6piknLCBrZXk6ICdjdXJ1c2VkcmVhZGluZ3MnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn55S15o2fKOW6piknLCBrZXk6ICd0cmFuc2Zvcm1lcnVsbGFnZScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICfmgLvnlLXph48o5bqmKScsIGtleTogJ3RvdGFsdXNlZHJlYWRpbmdzJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+eUteS7tyjlhYMpJywga2V5OiAndW5pdHBpcmNlJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+S4k+elqOWQq+eojumHkeminSjlhYMpJywga2V5OiAnaW5wdXR0YXh0aWNrZXRtb25leScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDYwLH0sDQogICAgICB7dGl0bGU6ICfmma7npajph5Hpop0o5YWDKScsIGtleTogJ2lucHV0dGlja2V0bW9uZXknLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA2MCx9LA0KICAgICAge3RpdGxlOiAn5pmu56Wo56iO6aKdKOWFgyknLCBrZXk6ICd0aWNrZXR0YXhhbW91bnQnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA2MCx9LA0KICAgICAge3RpdGxlOiAn5a6e6ZmF5pmu56Wo5ZCr56iO6YeR6aKdKOWFgyknLCBrZXk6ICd0aWNrZXRtb25leScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICflrp7pmYXkuJPnpajlkKvnqI7ph5Hpop0o5YWDKScsIGtleTogJ3RheHRpY2tldG1vbmV5JywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+eojueOh++8iCXvvIknLCBrZXk6ICd0YXhyYXRlJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogODAsfSwNCiAgICAgIHt0aXRsZTogJ+eojuminScsIGtleTogJ3RheGFtb3VudCcsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDYwLH0sDQogICAgICB7dGl0bGU6ICflhbbku5Yo5YWDKScsIGtleTogJ3VsbGFnZW1vbmV5JywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+Wunue8tOi0ueeUqCjlhYMp5ZCr56iOJywga2V5OiAnYWNjb3VudG1vbmV5JywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+aAu+mHkemine+8iOS4jeWQq+eoju+8iScsIGtleTogJ3RvdGFsQkhTJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+WIhuWJsuavlOS+iyglKScsIGtleTogJ3BlcmNlbnQnLCB3aWR0aDogOTAsIGFsaWduOiAnY2VudGVyJ30sDQogICAgICB7dGl0bGU6ICflpIfms6gnLCBrZXk6ICdyZW1hcmsnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiAxNTAsIHJlbmRlcjogbWFvcGFvfSwNCiAgICAgIHt0aXRsZTogJ+exu+Wei+aPj+i/sCcsIGtleTogJ2NhdGVnb3J5bmFtZScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICfnlKjnlLXnsbvlnosnLCBrZXk6ICdlbGVjdHJvdHlwZW5hbWUnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5NCx9LA0KICAgICAge3RpdGxlOiAn55S16KGo55So6YCUJywga2V5OiAnYW1tZXRlcnVzZW5hbWUnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn5Y+w6LSm57G75Z6LJywga2V5OiAndHlwZScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDkwLH0sDQogICAgICB7dGl0bGU6ICfnirbmgIEnLCBrZXk6ICdzdGF0dXMnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn5b2V5YWl5Lq6Jywga2V5OiAnaW5wdXRuYW1lJywgYWxpZ246ICdjZW50ZXInLCB3aWR0aDogOTAsfSwNCiAgICAgIHt0aXRsZTogJ+W9kumbhuWNleS6i+mhueWQjeensCcsIGtleTogJ25vdGUnLCBhbGlnbjogJ2NlbnRlcicsIHdpZHRoOiA5MCx9LA0KICAgICAge3RpdGxlOiAn572R566hQ+e9kee8luWPtycsIGtleTogJ25tY2NvZGUnLCB3aWR0aDogMTUwLCBhbGlnbjogJ2NlbnRlcid9LA0KICAgICAge3RpdGxlOiAn572R566h57yW5Y+3TDIuMUcnLCBrZXk6ICdubWwyMTAwJywgd2lkdGg6IDkwLCBhbGlnbjogJ2NlbnRlcid9LA0KICAgICAge3RpdGxlOiAn572R566h57yW5Y+3TDEuOEcnLCBrZXk6ICdubWwxODAwJywgd2lkdGg6IDkwLCBhbGlnbjogJ2NlbnRlcid9LA0KICAgICAge3RpdGxlOiAn572R566h57yW5Y+3QytMODAwTScsIGtleTogJ25tY2w4MDBtJywgd2lkdGg6IDkwLCBhbGlnbjogJ2NlbnRlcid9LA0KICAgICAge3RpdGxlOiAn5ZCI5ZCM5a+55pa5Jywga2V5OiAnY29udHJhY3RvdGhwYXJ0Jywgd2lkdGg6IDkwLCBhbGlnbjogJ2NlbnRlcid9LA0KICAgICAge3RpdGxlOiAn6LSi6L6F5oql6LSm5Y2V5Y+3Jywga2V5OiAnYmlsbElkJywgd2lkdGg6IDkwLCBhbGlnbjogJ2NlbnRlcid9LA0KICAgICAge3RpdGxlOiAn5oql6LSm5Lq6Jywga2V5OiAnZmlsbGlubmFtZScsIHdpZHRoOiA5MCwgYWxpZ246ICdjZW50ZXInfSwNCiAgICAgIHt0aXRsZTogJ+i0oui+hei0puacnycsIGtleTogJ2J1ZGdldHNldG5hbWUnLCB3aWR0aDogOTAsIGFsaWduOiAnY2VudGVyJ30sDQogICAgICB7dGl0bGU6ICfpmYTku7YnLCBzbG90OiAnZmlsZScsIGFsaWduOiAnY2VudGVyJywgd2lkdGg6IDYwLCByZW5kZXI6IHBob3RvfSwNCiAgICBdDQoNCiAgICByZXR1cm4gew0KICAgICAgZXhwb3J0bG9hZGluZzogZmFsc2UsDQogICAgICBmb3JtSXRlbVdpZHRoOiB3aWR0aHN0eWxlLA0KICAgICAgdmVyc2lvbjogJycsDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBzdGFydEFjY291bnRubzogJycsLy/otbflp4vlsYDnq5nnlLXotLnmnIjlj7DluJANCiAgICAgICAgZW5kQWNjb3VudG5vOiAnJywvL+aIquatouWxgOermeeUtei0ueaciOWPsOW4kA0KICAgICAgICBzdWJzdGF0aW9uOiAnJywvL+aUr+WxgA0KICAgICAgICBwcm9qZWN0bmFtZTogJycsLy/pobnnm67lkI3np7ANCiAgICAgICAgYW1tZXRlcmNvZGU6ICcnLC8v55S16KGo5oi35Y+3L+WNj+iurue8lueggQ0KICAgICAgICBhY2NvdW50VHlwZTogJycsLy/lj7DotKbnsbvlnosNCiAgICAgICAgY29tcGFueTogJycsLy/liIblhazlj7gNCiAgICAgICAgY291bnRyeTogJycsLy/miYDlsZ7pg6jpl6gNCiAgICAgICAgc3RhdHVzOiAnJywvL+eKtuaAgQ0KICAgICAgICBhbW1ldGVydXNlOiAnJywNCiAgICAgICAgY291bnRyeU5hbWU6ICcnLA0KICAgICAgICBzdXBwbHlidXJlYXVhbW1ldGVyY29kZTogJycsDQogICAgICAgIHJlc3N0YXRpb25jb2RlOiAnJywNCiAgICAgICAgZGlyZWN0c3VwcGx5ZmxhZzogJycsDQogICAgICAgIGlmZmluYW5jZTogMCwNCiAgICAgICAgc3RhcnRZZWFyOiAnJywNCiAgICAgICAgc3RhcnRNb250aDogJycsDQogICAgICAgIGVuZFllYXI6ICcnLA0KICAgICAgICBlbmRNb250aDogJycNCiAgICAgIH0sDQogICAgICBzdGFydERhdGVQaWNrZXI6ICcnLA0KICAgICAgZW5kRGF0ZVBpY2tlcjogJycsDQogICAgICBzdGFydENmenFQaWNrZXI6ICcnLA0KICAgICAgZW5kQ2Z6cVBpY2tlcjogJycsDQogICAgICBjb21wYW5pZXM6IFtdLA0KICAgICAgZGVwYXJ0bWVudHM6IFtdLA0KICAgICAgY29sdW1uc19sbjogY29sdW1uc19sbiwvL+i+veWugeihqOWktA0KICAgICAgY29sdW1uc19zYzogY29sdW1uc19zYywvL+Wbm+W3neihqOWktA0KICAgICAgaXNBZG1pbjogZmFsc2UsDQogICAgICBjYXRlZ29yeXM6IFtdLC8v5o+P6L+w57G75Z6LDQogICAgICBkaXJlY3RzdXBwbHlmbGFnczogW10sDQogICAgICBjb21wYW55OiBudWxsLC8v55So5oi36buY6K6k5YWs5Y+4DQogICAgICBjb3VudHJ5OiBudWxsLC8v55So5oi36buY6K6k5omA5bGe6YOo6ZeoDQogICAgICBjb3VudHJ5TmFtZTogbnVsbCwvL+eUqOaIt+m7mOiupOaJgOWxnumDqOmXqA0KICAgICAgY2xhc3NpZmljYXRpb25EYXRhOiBbXSwvL+eUqOeUteexu+Wei+agkQ0KICAgICAgY2xhc3NpZmljYXRpb25zOiBbXSwvL+mAieaLqeeahOeUqOeUteexu+Wei+agkQ0KICAgICAgZmlsdGVyQ29sbDogdHJ1ZSwvL+aQnOe0oumdouadv+WxleW8gA0KICAgICAgZXhwb3J0YWJsZTogZmFsc2UsDQogICAgICBhY2NvdW50U3RhdHVzOiBbXSwNCiAgICAgIGFtbWV0ZXJ1c2VMaXN0OiBbXSwNCiAgICAgIGJpbGx0eXBlczogW10sDQogICAgICBleHBvcnQ6IHsNCiAgICAgICAgcnVuOiBmYWxzZSwvL+aYr+WQpuato+WcqOaJp+ihjOWvvOWHug0KICAgICAgICBkYXRhOiAiIiwvL+WvvOWHuuaVsOaNrg0KICAgICAgICB0b3RhbFBhZ2U6IDAsLy/kuIDlhbHlpJrlsJHpobUNCiAgICAgICAgY3VycmVudFBhZ2U6IDAsLy/lvZPliY3lpJrlsJHpobUNCiAgICAgICAgcGVyY2VudDogMCwNCiAgICAgICAgc2l6ZTogMTAwMDAwMDANCiAgICAgIH0sDQogICAgICBzdWJ0b3RhbDogJycsDQogICAgICBhbGx0b3RhbDogJycsDQogICAgICB1cmw6ICdidXNpbmVzcy9hY2NvdW50L3NlbGVjdExpc3RCeVBhcmFtcycsDQogICAgICBsaXN0VGI6IHsNCiAgICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICAgIGNvbHVtbnM6IFtdLA0KICAgICAgICBkYXRhOiBbXQ0KICAgICAgfSwNCiAgICAgIGluc2lkZURhdGE6IFtdLA0KICAgICAgcGFnZVRvdGFsOiAwLA0KICAgICAgcGFnZU51bTogMSwNCiAgICAgIHBhZ2VTaXplOiAxMCwvL+W9k+WJjemhtQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIHNlbGVjdENoYW5nZSgpIHsNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIGlmICh0aGF0LnF1ZXJ5UGFyYW1zLmNvbXBhbnkgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgIGlmICh0aGF0LnF1ZXJ5UGFyYW1zLmNvbXBhbnkgPT0gIi0xIikgew0KICAgICAgICAgIHRoYXQucXVlcnlQYXJhbXMuY291bnRyeSA9IC0xOw0KICAgICAgICAgIHRoYXQucXVlcnlQYXJhbXMuY291bnRyeU5hbWUgPSBudWxsOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGdldENvdW50cnlCeVVzZXJJZCh0aGF0LnF1ZXJ5UGFyYW1zLmNvbXBhbnkpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuZGF0YS5kZXBhcnRtZW50cy5sZW5ndGggIT0gMCkgew0KICAgICAgICAgICAgICB0aGF0LnF1ZXJ5UGFyYW1zLmNvdW50cnkgPSByZXMuZGF0YS5kZXBhcnRtZW50c1swXS5pZDsNCiAgICAgICAgICAgICAgdGhhdC5xdWVyeVBhcmFtcy5jb3VudHJ5TmFtZSA9IHJlcy5kYXRhLmRlcGFydG1lbnRzWzBdLm5hbWU7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8v6YCJ5oup5omA5bGe6YOo6Zeo5byA5aeLDQogICAgY2hvb3NlUmVzcG9uc2VDZW50ZXIoKSB7DQogICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5jb21wYW55ID09IG51bGwgfHwgdGhpcy5xdWVyeVBhcmFtcy5jb21wYW55ID09ICItMSIpIHsNCiAgICAgICAgdGhpcy4kTWVzc2FnZS5pbmZvKCLor7flhYjpgInmi6nliIblhazlj7giKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy4kcmVmcy5jb3VudHJ5TW9kYWwuY2hvb3NlKHRoaXMucXVlcnlQYXJhbXMuY29tcGFueSk7Ly/miYDlsZ7pg6jpl6gNCiAgICB9LA0KICAgIGdldERhdGFGcm9tTW9kYWwoZGF0YSkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb3VudHJ5ID0gZGF0YS5pZDsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY291bnRyeU5hbWUgPSBkYXRhLm5hbWU7DQogICAgICAvL+mAieaLqeaJgOWxnumDqOmXqOe7k+adnw0KICAgIH0sDQogICAgLy/nv7vpobUNCiAgICBoYW5kbGVQYWdlKHZhbHVlKSB7DQogICAgICB0aGlzLnBhZ2VOdW0gPSB2YWx1ZTsNCiAgICAgIHRoaXMuZ2V0QWNjb3VudE1lc3NhZ2VzKCk7DQogICAgfSwNCiAgICAvL+aUueWPmOihqOagvOWPr+aYvuekuuaVsOaNruaVsOmHjw0KICAgIGhhbmRsZVBhZ2VTaXplKHZhbHVlKSB7DQogICAgICB0aGlzLnBhZ2VTaXplID0gdmFsdWU7DQogICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgIH0sDQogICAgLy/lkJHlkI7lj7Dor7fmsYLmlbDmja4NCiAgICBnZXRBY2NvdW50TWVzc2FnZXMoKSB7DQogICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5jb3VudHJ5TmFtZSA9PSAiIikgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvdW50cnkgPSAiLTEiOw0KICAgICAgfQ0KICAgICAgdGhpcy5zZXRFbGVjdHJveVR5cGUoKTsNCiAgICAgIGxldCBwYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOw0KICAgICAgcGFyYW1zLnBhZ2VOdW0gPSB0aGlzLnBhZ2VOdW07DQogICAgICBwYXJhbXMucGFnZVNpemUgPSB0aGlzLnBhZ2VTaXplOw0KICAgICAgbGV0IHJlcSA9IHsNCiAgICAgICAgdXJsOiB0aGlzLnVybCwNCiAgICAgICAgbWV0aG9kOiAiZ2V0IiwNCiAgICAgICAgcGFyYW1zOiBwYXJhbXMNCiAgICAgIH07DQogICAgICBsZXQgYXJyYXkgPSBbXTsNCiAgICAgIHRoaXMubGlzdFRiLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgYXhpb3MucmVxdWVzdChyZXEpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5saXN0VGIubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICBpZiAocmVzLmRhdGEpIHsNCiAgICAgICAgICBhcnJheSA9IHJlcy5kYXRhLnJvd3M7DQogICAgICAgICAgdGhpcy5zZXRkYXRhKGFycmF5KQ0KICAgICAgICAgIHRoaXMudG90YWxCSFMoYXJyYXkpDQogICAgICAgICAgYXJyYXkucHVzaCh0aGlzLnN1bnRvdGFsKGFycmF5KSkvL+Wwj+iuoQ0KICAgICAgICAgIHRoaXMucGFnZVRvdGFsID0gcmVzLmRhdGEudG90YWwgfHwgMA0KICAgICAgICAgIGFsbEFjY291bnRUb3RhbCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlcyA9PiB7Ly/lkIjorqENCiAgICAgICAgICAgIGxldCBhbGx0b3RhbCA9IHJlcy5kYXRhDQogICAgICAgICAgICBhbGx0b3RhbC50b3RhbCA9ICflkIjorqEnDQogICAgICAgICAgICBhbGx0b3RhbC5wcm9qZWN0bmFtZSA9ICflkIjorqEnDQogICAgICAgICAgICBhcnJheS5wdXNoKGFsbHRvdGFsKQ0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMuaW5zaWRlRGF0YSA9IGFycmF5DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKGVycik7DQogICAgICB9KTsNCiAgICB9LA0KICAgIHNldGRhdGEoYXJyYXkpIHsNCiAgICAgIGxldCBhY2NvdW50U3RhdHVzID0gdGhpcy5hY2NvdW50U3RhdHVzDQogICAgICBsZXQgY2F0ZWdvcnlzID0gdGhpcy5jYXRlZ29yeXMNCiAgICAgIGxldCBhbW1ldGVydXNlTGlzdCA9IHRoaXMuYW1tZXRlcnVzZUxpc3QNCiAgICAgIGxldCBiaWxsdHlwZXMgPSB0aGlzLmJpbGx0eXBlcw0KICAgICAgYXJyYXkuZm9yRWFjaChmdW5jdGlvbiAocm93KSB7DQogICAgICAgIHZhciBzdGF0dXMgPSAiIjsNCiAgICAgICAgZm9yIChsZXQgaSBvZiBhY2NvdW50U3RhdHVzKSB7DQogICAgICAgICAgaWYgKGkudHlwZUNvZGUgPT0gcm93LnN0YXR1cykgew0KICAgICAgICAgICAgc3RhdHVzID0gaS50eXBlTmFtZTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICByb3cuc3RhdHVzID0gc3RhdHVzDQoNCiAgICAgICAgdmFyIHR5cGUgPSAnJw0KICAgICAgICBmb3IgKGxldCBpdGVtIG9mIGJpbGx0eXBlcykgew0KICAgICAgICAgIGlmIChpdGVtLnR5cGVDb2RlID09IHJvdy5iaWxsdHlwZSkgew0KICAgICAgICAgICAgdHlwZSA9IGl0ZW0udHlwZU5hbWU7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICByb3cudHlwZSA9IHR5cGUNCg0KICAgICAgICB2YXIgY2F0ZWdvcnluYW1lID0gIiI7DQogICAgICAgIGZvciAobGV0IGl0ZW0gb2YgY2F0ZWdvcnlzKSB7DQogICAgICAgICAgaWYgKGl0ZW0udHlwZUNvZGUgPT0gcm93LmNhdGVnb3J5KSB7DQogICAgICAgICAgICBjYXRlZ29yeW5hbWUgPSBpdGVtLnR5cGVOYW1lOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHJvdy5jYXRlZ29yeW5hbWUgPSBjYXRlZ29yeW5hbWUNCg0KICAgICAgICB2YXIgYW1tZXRlcnVzZSA9ICIiOw0KICAgICAgICBmb3IgKGxldCBpdGVtIG9mIGFtbWV0ZXJ1c2VMaXN0KSB7DQogICAgICAgICAgaWYgKGl0ZW0udHlwZUNvZGUgPT0gcm93LmFtbWV0ZXJ1c2UpIHsNCiAgICAgICAgICAgIGFtbWV0ZXJ1c2UgPSBpdGVtLnR5cGVOYW1lOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgcm93LmFtbWV0ZXJ1c2VuYW1lID0gYW1tZXRlcnVzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHRvdGFsQkhTKGFycmF5KSB7DQogICAgICBhcnJheS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgIGxldCB0YXh0aWNrZXRtb25leSA9IGl0ZW0udGF4dGlja2V0bW9uZXk7Ly/kuJPnpajlkKvnqI7ph5Hpop0NCiAgICAgICAgbGV0IHRpY2tldG1vbmV5ID0gaXRlbS50aWNrZXRtb25leTsvL+aZruelqOWQq+eojumHkeminQ0KICAgICAgICBsZXQgdWxsYWdlbW9uZXkgPSBpdGVtLnVsbGFnZW1vbmV5Oy8v5YW25LuW6LS555SoDQogICAgICAgIGxldCB0YXhhbW91bnQgPSBpdGVtLnRheGFtb3VudDsvL+eojuminQ0KICAgICAgICBsZXQgdG90YWwgPSB0aWNrZXRtb25leSArICh0YXh0aWNrZXRtb25leSAtIHRheGFtb3VudCkgKyB1bGxhZ2Vtb25leQ0KICAgICAgICB0b3RhbCA9IHRvdGFsLnRvRml4ZWQoMikNCiAgICAgICAgaXRlbS50b3RhbEJIUyA9IHRvdGFsDQogICAgICB9KQ0KICAgIH0sDQogICAgLy/lsI/orqENCiAgICBzdW50b3RhbChhcnJheSkgew0KICAgICAgbGV0IGN1cnVzZWRyZWFkaW5ncyA9IDANCiAgICAgIGxldCB0cmFuc2Zvcm1lcnVsbGFnZSA9IDANCiAgICAgIGxldCB0aWNrZXRtb25leSA9IDANCiAgICAgIGxldCB0YXh0aWNrZXRtb25leSA9IDANCiAgICAgIGxldCB0YXhhbW91bnQgPSAwDQogICAgICBsZXQgdWxsYWdlbW9uZXkgPSAwDQogICAgICBsZXQgYWNjb3VudG1vbmV5ID0gMA0KICAgICAgbGV0IGlucHV0dGF4dGlja2V0bW9uZXkgPSAwDQogICAgICBsZXQgaW5wdXR0aWNrZXRtb25leSA9IDANCiAgICAgIGxldCB0aWNrZXR0YXhhbW91bnQgPSAwDQogICAgICBsZXQgdG90YWwgPSAwDQogICAgICBhcnJheS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgIGlmIChpdGVtLmVmZmVjdGl2ZSA9PT0gMSkgew0KICAgICAgICAgIGN1cnVzZWRyZWFkaW5ncyArPSBpdGVtLmN1cnVzZWRyZWFkaW5ncw0KICAgICAgICAgIHRyYW5zZm9ybWVydWxsYWdlICs9IGl0ZW0udHJhbnNmb3JtZXJ1bGxhZ2UNCiAgICAgICAgICB0aWNrZXRtb25leSArPSBpdGVtLnRpY2tldG1vbmV5DQogICAgICAgICAgdGF4dGlja2V0bW9uZXkgKz0gaXRlbS50YXh0aWNrZXRtb25leQ0KICAgICAgICAgIHRheGFtb3VudCArPSBpdGVtLnRheGFtb3VudA0KICAgICAgICAgIGlucHV0dGF4dGlja2V0bW9uZXkgKz0gaXRlbS5pbnB1dHRheHRpY2tldG1vbmV5DQogICAgICAgICAgaW5wdXR0aWNrZXRtb25leSArPSBpdGVtLmlucHV0dGlja2V0bW9uZXkNCiAgICAgICAgICB1bGxhZ2Vtb25leSArPSBpdGVtLnVsbGFnZW1vbmV5DQogICAgICAgICAgYWNjb3VudG1vbmV5ICs9IGl0ZW0uYWNjb3VudG1vbmV5DQogICAgICAgICAgdG90YWwgKz0gcGFyc2VGbG9hdChpdGVtLnRvdGFsQkhTKQ0KICAgICAgICAgIHRpY2tldHRheGFtb3VudCArPSBpdGVtLnRpY2tldHRheGFtb3VudA0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgY3VydXNlZHJlYWRpbmdzOiBjdXJ1c2VkcmVhZGluZ3MsDQogICAgICAgIHRyYW5zZm9ybWVydWxsYWdlOiB0cmFuc2Zvcm1lcnVsbGFnZSwNCiAgICAgICAgdGlja2V0bW9uZXk6IHRpY2tldG1vbmV5LnRvRml4ZWQoMiksDQogICAgICAgIHRheHRpY2tldG1vbmV5OiB0YXh0aWNrZXRtb25leS50b0ZpeGVkKDIpLA0KICAgICAgICB0YXhhbW91bnQ6IHRheGFtb3VudC50b0ZpeGVkKDIpLA0KICAgICAgICBpbnB1dHRheHRpY2tldG1vbmV5OiBpbnB1dHRheHRpY2tldG1vbmV5LnRvRml4ZWQoMiksDQogICAgICAgIGlucHV0dGlja2V0bW9uZXk6IGlucHV0dGlja2V0bW9uZXkudG9GaXhlZCgyKSwNCiAgICAgICAgdWxsYWdlbW9uZXk6IHVsbGFnZW1vbmV5LnRvRml4ZWQoMiksDQogICAgICAgIGFjY291bnRtb25leTogYWNjb3VudG1vbmV5LnRvRml4ZWQoMiksDQogICAgICAgIHRvdGFsQkhTOiB0b3RhbC50b0ZpeGVkKDIpLA0KICAgICAgICB0aWNrZXR0YXhhbW91bnQ6IHRpY2tldHRheGFtb3VudC50b0ZpeGVkKDIpLA0KICAgICAgICB0b3RhbDogJ+Wwj+iuoScsDQogICAgICAgIHByb2plY3RuYW1lOiAn5bCP6K6hJywNCiAgICAgICAgX2Rpc2FibGVkOiB0cnVlDQogICAgICB9DQogICAgfSwNCiAgICBfb25TZWFyY2hIYW5kbGUoKSB7DQogICAgICB0aGlzLnBhZ2VOdW0gPSAxDQogICAgICB0aGlzLmdldEFjY291bnRNZXNzYWdlcygpDQogICAgfSwNCiAgICBfb25SZXNldEhhbmRsZSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7Y29tcGFueTogbnVsbCwgY291bnRyeTogbnVsbCwgY291bnRyeU5hbWU6IG51bGx9Ow0KICAgICAgdGhpcy5zdGFydERhdGVQaWNrZXIgPSAnJzsNCiAgICAgIHRoaXMuZW5kRGF0ZVBpY2tlciA9ICcnOw0KICAgICAgdGhpcy5zdGFydENmenFQaWNrZXIgPSAnJzsNCiAgICAgIHRoaXMuZW5kQ2Z6cVBpY2tlciA9ICcnOw0KICAgICAgdGhpcy5jbGFzc2lmaWNhdGlvbnMgPSBbXTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY29tcGFueSA9IHRoaXMuY29tcGFueTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY291bnRyeSA9IE51bWJlcih0aGlzLmNvdW50cnkpOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb3VudHJ5TmFtZSA9IHRoaXMuY291bnRyeU5hbWU7DQogICAgfSwNCiAgICBzdGFydENoYW5nZSh5ZWFyKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN0YXJ0QWNjb3VudG5vID0geWVhcg0KICAgIH0sDQogICAgZW5kQ2hhbmdlKHllYXIpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kQWNjb3VudG5vID0geWVhcg0KICAgIH0sDQogICAgc3RhcnRDZnpxQ2hhbmdlKHllYXIpIHsNCiAgICAgIGlmKHllYXIpew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN0YXJ0WWVhciA9IHllYXIuc3Vic3RyaW5nKDAsNCk7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhcnRNb250aCA9IHllYXIuc3Vic3RyaW5nKDQsNik7DQogICAgICB9IGVsc2V7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhcnRZZWFyID0gJyc7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhcnRNb250aCA9ICcnOw0KICAgICAgfQ0KICAgIH0sDQogICAgZW5kQ2Z6cUNoYW5nZSh5ZWFyKSB7DQogICAgICBpZih5ZWFyKXsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmRZZWFyID0geWVhci5zdWJzdHJpbmcoMCw0KTsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmRNb250aCA9IHllYXIuc3Vic3RyaW5nKDQsNik7DQogICAgICB9IGVsc2V7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kWWVhciA9ICcnOw0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmVuZE1vbnRoID0gJyc7DQogICAgICB9DQogICAgfSwNCiAgICBzZXRFbGVjdHJveVR5cGUoKSB7DQogICAgICBsZXQgdHlwZXMgPSB0aGlzLmNsYXNzaWZpY2F0aW9uczsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZWxlY3Ryb3R5cGUgPSB0eXBlc1t0eXBlcy5sZW5ndGggLSAxXQ0KICAgIH0sDQogICAgYmVmb3JlTG9hZERhdGEoZGF0YSkgew0KICAgICAgdmFyIGNvbHMgPSBbXSwga2V5cyA9IFtdDQogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRoaXMubGlzdFRiLmNvbHVtbnMubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29scy5wdXNoKHRoaXMubGlzdFRiLmNvbHVtbnNbaV0udGl0bGUpDQogICAgICAgIGtleXMucHVzaCh0aGlzLmxpc3RUYi5jb2x1bW5zW2ldLmtleSkNCiAgICAgIH0NCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgdGl0bGU6IGNvbHMsDQogICAgICAgIGtleToga2V5cywNCiAgICAgICAgZGF0YTogZGF0YSwNCiAgICAgICAgYXV0b1dpZHRoOiB0cnVlLA0KICAgICAgICBmaWxlbmFtZTogJ+WPsOi0puWvvOWHuuaVsOaNricNCiAgICAgIH0NCiAgICAgIGV4Y2VsLmV4cG9ydF9hcnJheV90b19leGNlbChwYXJhbXMpDQogICAgICByZXR1cm4NCiAgICB9LA0KICAgIGV4cG9ydENzdihuYW1lKSB7DQogICAgICAvKiAgICAgICAgICAgICAgICB0aGlzLmVycm9yVGlwcygi5oql6LSm6auY5bOwMTMtMjXlj7fmmoLlgZzkvb/nlKgiKTsNCiAgICAgICAgICAgICAgICAgICAgICByZXR1cm47Ki8NCiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLmNvdW50cnlOYW1lID09ICIiKSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuY291bnRyeSA9ICItMSI7DQogICAgICB9DQogICAgICB0aGlzLnNldEVsZWN0cm95VHlwZSgpOw0KICAgICAgbGV0IHBhcmFtcyA9IHRoaXMucXVlcnlQYXJhbXM7DQogICAgICBpZiAobmFtZS5zcGxpdCgnXycpLmxlbmd0aCA+IDEpIHsNCiAgICAgICAgLy8g5a+85Ye65YyF5ZCr55S16KGoL+WNj+iuruS/oeaBrw0KICAgICAgICBwYXJhbXMubW9yZU1lcyA9IG5hbWUuc3BsaXQoJ18nKVsxXTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHBhcmFtcy5tb3JlTWVzID0gIiI7DQogICAgICB9DQogICAgICBuYW1lID0gbmFtZS5zcGxpdCgnXycpWzBdOw0KICAgICAgY29uc29sZS5sb2cobmFtZSk7DQogICAgICBjb25zb2xlLmxvZyhKU09OLnN0cmluZ2lmeShwYXJhbXMpKTsNCiAgICAgIGlmIChuYW1lID09PSAnY3VycmVudCcpIHsNCiAgICAgICAgcGFyYW1zLnBhZ2VOdW0gPSB0aGlzLnBhZ2VOdW07DQogICAgICAgIHBhcmFtcy5wYWdlU2l6ZSA9IHRoaXMucGFnZVNpemU7DQogICAgICAgIHBhcmFtcy5pZmZpbmFuY2UgPSAwOw0KICAgICAgfSBlbHNlIGlmIChuYW1lID09PSAnYWxsJykgew0KICAgICAgICBpZiAocGFyYW1zLmVuZEFjY291bnRubyA9PSBudWxsIHx8IHBhcmFtcy5lbmRBY2NvdW50bm8ubGVuZ3RoID09IDAgfHwgcGFyYW1zLnN0YXJ0QWNjb3VudG5vID09IG51bGwgfHwgcGFyYW1zLnN0YXJ0QWNjb3VudG5vLmxlbmd0aCA9PSAwKSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoIuWFqOmDqOWvvOWHuuaXtu+8jOW/hemhu+mAieaLqei1t+atouacn+WPtyIpOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy52ZXJzaW9uID09ICdzYycpDQogICAgICAgIHsgIGlmICgoTW9udGhkaWZmKHBhcmFtcy5lbmRBY2NvdW50bm8sIHBhcmFtcy5zdGFydEFjY291bnRubykpID4gNCkgew0KICAgICAgICAgICAgdGhpcy5lcnJvclRpcHMoIuWFqOmDqOWvvOWHuuaXtu+8jOS4gOasoeaAp+WPquiDveWvvOWHujTkuKrmnIjnmoTmlbDmja4iKTsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQogICAgICB9DQogICAgICAgIHBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgICAgcGFyYW1zLnBhZ2VTaXplID0gdGhpcy5leHBvcnQuc2l6ZTsNCiAgICAgICAgcGFyYW1zLmlmZmluYW5jZSA9IDA7DQogICAgICB9IGVsc2UgaWYgKG5hbWUgPT09ICdhbGxmaW5hbmNlJykgew0KICAgICAgICBpZiAocGFyYW1zLmVuZEFjY291bnRubyA9PSBudWxsIHx8IHBhcmFtcy5lbmRBY2NvdW50bm8ubGVuZ3RoID09IDAgfHwgcGFyYW1zLnN0YXJ0QWNjb3VudG5vID09IG51bGwgfHwgcGFyYW1zLnN0YXJ0QWNjb3VudG5vLmxlbmd0aCA9PSAwKSB7DQogICAgICAgICAgdGhpcy5lcnJvclRpcHMoIuWFqOmDqOWvvOWHuuaXtu+8jOW/hemhu+mAieaLqei1t+atouacn+WPtyIpOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy52ZXJzaW9uID09ICdzYycpIHsNCiAgICAgICAgICBpZiAoKE1vbnRoZGlmZihwYXJhbXMuZW5kQWNjb3VudG5vLCBwYXJhbXMuc3RhcnRBY2NvdW50bm8pKSA+IDQpIHsNCiAgICAgICAgICAgIHRoaXMuZXJyb3JUaXBzKCLlhajpg6jlr7zlh7rml7bvvIzkuIDmrKHmgKflj6rog73lr7zlh7o05Liq5pyI55qE5pWw5o2uIik7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgICAgcGFyYW1zLnBhZ2VTaXplID0gdGhpcy5leHBvcnQuc2l6ZTsNCiAgICAgICAgcGFyYW1zLmlmZmluYW5jZSA9IDE7DQogICAgICB9DQogICAgICBsZXQgcmVxID0gew0KICAgICAgICB1cmw6ICIvYnVzaW5lc3MvYWNjb3VudC9leHBvcnRjeCIsDQogICAgICAgIG1ldGhvZDogImdldCIsDQogICAgICAgIHBhcmFtczogcGFyYW1zDQogICAgICB9Ow0KICAgICAgdGhpcy5leHBvcnRsb2FkaW5nID0gdHJ1ZQ0KICAgICAgYXhpb3MuZmlsZShyZXEpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5leHBvcnRsb2FkaW5nID0gZmFsc2UNCiAgICAgICAgY29uc3QgY29udGVudCA9IHJlcw0KICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2NvbnRlbnRdKQ0KICAgICAgICBjb25zdCBmaWxlTmFtZSA9ICflj7DotKbmn6Xor6Llr7zlh7rmlbDmja4nICsgJy54bHN4JzsNCiAgICAgICAgaWYgKCdkb3dubG9hZCcgaW4gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpKSB7IC8vIOmdnklF5LiL6L29DQogICAgICAgICAgY29uc3QgZWxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJykNCiAgICAgICAgICBlbGluay5kb3dubG9hZCA9IGZpbGVOYW1lDQogICAgICAgICAgZWxpbmsuc3R5bGUuZGlzcGxheSA9ICdub25lJw0KICAgICAgICAgIGVsaW5rLmhyZWYgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpDQogICAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChlbGluaykNCiAgICAgICAgICBlbGluay5jbGljaygpDQogICAgICAgICAgVVJMLnJldm9rZU9iamVjdFVSTChlbGluay5ocmVmKSAvLyDph4rmlL5VUkwg5a+56LGhDQogICAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChlbGluaykNCiAgICAgICAgfSBlbHNlIHsgLy8gSUUxMCvkuIvovb0NCiAgICAgICAgICBuYXZpZ2F0b3IubXNTYXZlQmxvYihibG9iLCBmaWxlTmFtZSkNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgdGhpcy5leHBvcnRsb2FkaW5nID0gZmFsc2U7DQogICAgICAgIGNvbnNvbGUubG9nKGVycik7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldFVzZXJEYXRhKCkgew0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgZ2V0VXNlcmRhdGEoKS50aGVuKHJlcyA9PiB7Ly/lvZPliY3nmbvlvZXnlKjmiLfmiYDlnKjlhazlj7jlkozmiYDlsZ7pg6jpl6gNCiAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllcy5sZW5ndGggIT0gMCkgew0KICAgICAgICAgIGxldCBjb21wYW5pZXMgPSByZXMuZGF0YS5jb21wYW5pZXM7DQogICAgICAgICAgaWYgKHJlcy5kYXRhLmNvbXBhbmllc1swXS5pZCA9PSAiMjYwMDAwMDAwMCIpIHsNCiAgICAgICAgICAgIGNvbXBhbmllcyA9IHRoYXQuY29tcGFuaWVzOw0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGF0LmNvbXBhbnkgPSBjb21wYW5pZXNbMF0uaWQ7DQogICAgICAgICAgdGhhdC5xdWVyeVBhcmFtcy5jb21wYW55ID0gY29tcGFuaWVzWzBdLmlkOw0KICAgICAgICB9DQogICAgICAgIGlmIChyZXMuZGF0YS5kZXBhcnRtZW50cy5sZW5ndGggIT0gMCkgew0KICAgICAgICAgIGxldCBkZXBhcnRtZW50cyA9IHJlcy5kYXRhLmRlcGFydG1lbnRzOw0KICAgICAgICAgIGlmIChyZXMuZGF0YS5jb21wYW5pZXNbMF0uaWQgPT0gIjI2MDAwMDAwMDAiICYmIHRoYXQuZGVwYXJ0bWVudHMubGVuZ3RoICE9IDApIHsNCiAgICAgICAgICAgIGRlcGFydG1lbnRzID0gdGhhdC5kZXBhcnRtZW50cw0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGF0LmNvdW50cnkgPSBkZXBhcnRtZW50c1swXS5pZDsNCiAgICAgICAgICB0aGF0LmNvdW50cnlOYW1lID0gZGVwYXJ0bWVudHNbMF0ubmFtZTsNCiAgICAgICAgICB0aGF0LnF1ZXJ5UGFyYW1zLmNvdW50cnkgPSBOdW1iZXIoZGVwYXJ0bWVudHNbMF0uaWQpOw0KICAgICAgICAgIHRoYXQucXVlcnlQYXJhbXMuY291bnRyeU5hbWUgPSBkZXBhcnRtZW50c1swXS5uYW1lOw0KICAgICAgICB9DQoNCiAgICAgICAgdGhhdC5wYWdlTnVtID0gMQ0KICAgICAgICB0aGF0LmdldEFjY291bnRNZXNzYWdlcygpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRubygpIHsNCiAgICAgIGxldCBkYXRlID0gbmV3IERhdGUoKTsNCiAgICAgIC8v6I635Y+W5b2T5YmN5bm05pyIDQogICAgICBsZXQgeWVhciA9IGRhdGUuZ2V0RnVsbFllYXIoKTsNCiAgICAgIGxldCBtb250aCA9IGRhdGUuZ2V0TW9udGgoKSArIDE7DQogICAgICBtb250aCA9IChtb250aCA8IDEwID8gIjAiICsgbW9udGggOiBtb250aCk7DQogICAgICBsZXQgY3VyRGF0ZSA9ICh5ZWFyLnRvU3RyaW5nKCkgKyBtb250aC50b1N0cmluZygpKTsNCiAgICAgIHJldHVybiBjdXJEYXRlOw0KICAgIH0sDQogICAgLy/pqozor4HplJnor6/lvLnlh7rmj5DnpLrmoYblubbot7PovazliLDkuIvkuIDmoLwNCiAgICBlcnJvclRpcHMoc3RyKSB7DQogICAgICB0aGlzLiROb3RpY2UuZXJyb3Ioew0KICAgICAgICB0aXRsZTogJ+aPkOekuicsDQogICAgICAgIGRlc2M6IHN0ciwNCiAgICAgICAgZHVyYXRpb246IDEwDQogICAgICB9KTsNCiAgICB9LA0KICAgIHVwbG9hZEZpbGUocm93KSB7DQogICAgICB0aGlzLiRyZWZzLnVwbG9hZEZpbGVNb2RhbC5zaG93YWRkID0gZmFsc2U7DQogICAgICB0aGlzLiRyZWZzLnVwbG9hZEZpbGVNb2RhbC5jb2x1bW5zLnBvcCgpOw0KICAgICAgdGhpcy4kcmVmcy51cGxvYWRGaWxlTW9kYWwuY2hvb3NlKHJvdy5wY2lkICsgJycpOw0KICAgIH0sDQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5hY2NvdW50U3RhdHVzID0gYmxpc3QoImFjY291bnRTdGF0dXMiKTsNCiAgICB0aGlzLmFtbWV0ZXJ1c2VMaXN0ID0gYmxpc3QoJ2FtbWV0ZXJVc2UnKTsNCiAgICB0aGlzLmNhdGVnb3J5cyA9IGJsaXN0KCJhbW1ldGVyQ2F0ZWdvcnkiKTsNCiAgICB0aGlzLmRpcmVjdHN1cHBseWZsYWdzID0gYmxpc3QoImRpcmVjdFN1cHBseUZsYWciKTsNCiAgICB0aGlzLmJpbGx0eXBlcyA9IGJsaXN0KCJiaWxscHJlVHlwZSIpOw0KICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICB0aGF0LnN0YXJ0RGF0ZVBpY2tlciA9IHRoYXQuZ2V0bm8oKQ0KICAgIHRoYXQucXVlcnlQYXJhbXMuc3RhcnRBY2NvdW50bm8gPSB0aGF0LmdldG5vKCkNCiAgICBnZXRVc2VyQnlVc2VyUm9sZSgpLnRoZW4ocmVzID0+IHsvL+agueaNruadg+mZkOiOt+WPluWIhuWFrOWPuA0KICAgICAgdGhhdC5jb21wYW5pZXMgPSByZXMuZGF0YS5jb21wYW5pZXM7DQogICAgICBpZiAocmVzLmRhdGEuaXNDaXR5QWRtaW4gPT0gdHJ1ZSB8fCByZXMuZGF0YS5pc1Byb0FkbWluID09IHRydWUgfHwgcmVzLmRhdGEuaXNTdWJBZG1pbiA9PSB0cnVlKSB7DQogICAgICAgIHRoYXQuaXNBZG1pbiA9IHRydWU7DQogICAgICB9DQogICAgICBnZXRDb3VudHJ5c2RhdGEoe29yZ0NvZGU6IHJlcy5kYXRhLmNvbXBhbmllc1swXS5pZH0pLnRoZW4ocmVzID0+IHsvL+agueaNruadg+mZkOiOt+WPluaJgOWxnumDqOmXqA0KICAgICAgICB0aGF0LmRlcGFydG1lbnRzID0gcmVzLmRhdGE7DQogICAgICAgIHRoYXQuZ2V0VXNlckRhdGEoKTsNCiAgICAgIH0pOw0KICAgIH0pOw0KDQogICAgZ2V0Q2xhc3NpZmljYXRpb24oKS50aGVuKHJlcyA9PiB7Ly/nlKjnlLXnsbvlnosNCiAgICAgIHRoaXMuY2xhc3NpZmljYXRpb25EYXRhID0gcmVzLmRhdGE7DQogICAgfSk7DQogICAgdGhpcy52ZXJzaW9uID0gaW5kZXhEYXRhLnZlcnNpb24NCiAgICBpZiAodGhpcy52ZXJzaW9uID09ICdsbicpIHsNCiAgICAgIHRoaXMubGlzdFRiLmNvbHVtbnMgPSB0aGlzLmNvbHVtbnNfbG4NCiAgICB9IGVsc2UgaWYgKHRoaXMudmVyc2lvbiA9PSAnc2MnKSB7DQogICAgICB0aGlzLmxpc3RUYi5jb2x1bW5zID0gdGhpcy5jb2x1bW5zX3NjDQogICAgfQ0KDQogIH0NCn0NCg0KZnVuY3Rpb24gTW9udGhkaWZmKGEsIGIpIHsNCiAgdmFyIHllYXIxID0gYS5zdWJzdHJpbmcoMCwgNCk7DQogIHZhciB5ZWFyMiA9IGIuc3Vic3RyaW5nKDAsIDQpOw0KICB2YXIgbW9udGgxID0gYS5zdWJzdHJpbmcoNCwgNikucmVwbGFjZSgvMC8sICIiKTsNCiAgdmFyIG1vbnRoMiA9IGIuc3Vic3RyaW5nKDQsIDYpLnJlcGxhY2UoLzAvLCAiIik7DQogIHJldHVybiAoeWVhcjEgLSB5ZWFyMikgKiAxMiArIChtb250aDEgLSBtb250aDIpKzE7DQoNCn0NCg=="}, {"version": 3, "sources": ["PowerAccountList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "PowerAccountList.vue", "sourceRoot": "src/view/account", "sourcesContent": ["<style lang=\"less\">\r\n.accountbill .filter-divider {\r\n  margin: 0px;\r\n  text-align: center;\r\n}\r\n\r\n.accountbill .header-bar-show {\r\n  max-height: 300px;\r\n  /*padding-top: 14px;*/\r\n  overflow: inherit;\r\n  border-bottom: 1px solid #e8eaec;\r\n}\r\n\r\n.accountbill .header-bar-hide {\r\n  max-height: 0;\r\n  padding-top: 0;\r\n  overflow: hidden;\r\n  border-bottom: 0;\r\n}\r\n\r\n.accountbill .row {\r\n  height: 30px;\r\n  margin-bottom: -50px;\r\n}\r\n\r\n.form-line-height {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.mytable .ivu-table-cell {\r\n  padding-left: 1px;\r\n  padding-right: 1px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: normal;\r\n  word-break: break-all;\r\n  box-sizing: border-box;\r\n}\r\n\r\n#ipt_dep .ivu-icon:before {\r\n  font-size: 30px;\r\n}\r\n</style>\r\n<template>\r\n  <div>\r\n    <Spin size=\"large\" fix v-if=\"exportloading\"></Spin>\r\n    <div class=\"accountbill\">\r\n      <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n        <Form ref=\"formInline\" :model=\"queryParams\" :label-width=\"150\" inline>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"起始期号：\" prop=\"startAccountno\" class=\"form-line-height\">\r\n                <DatePicker v-model=\"startDatePicker\" type=\"month\" @on-change='startChange'\r\n                            placeholder=\"起始局站电费月台帐\" format=\"yyyyMM\" :style=\"formItemWidth\"></DatePicker>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"截止期号：\" prop=\"endAccountno\" class=\"form-line-height\">\r\n                <DatePicker v-model=\"endDatePicker\" type=\"month\" @on-change='endChange'\r\n                            placeholder=\"截止局站电费月台帐\" format=\"yyyyMM\" :style=\"formItemWidth\"></DatePicker>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"局站名称:\" prop=\"stationName\" class=\"form-line-height\">\r\n                <cl-input v-model=\"queryParams.stationName\" placeholder=\"请输入局站名称\"\r\n                          :style=\"formItemWidth\"/>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"项目名称:\" prop=\"projectname\" class=\"form-line-height\">\r\n                <cl-input v-model=\"queryParams.projectname\" placeholder=\"请输入项目名称\"\r\n                          :style=\"formItemWidth\"/>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\" v-if=\"'sc' == version\">\r\n              <FormItem label=\"电表户号/协议编码:\" prop=\"ammetercode\" class=\"form-line-height\">\r\n                <cl-input v-model=\"queryParams.ammetercode\" placeholder=\"请输入电表户号/协议编码\"\r\n                          :style=\"formItemWidth\"/>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"台账类型：\" prop=\"accountType\" class=\"form-line-height\">\r\n                <Select v-model=\"queryParams.accountType\" :style=\"formItemWidth\">\r\n                  <Option value=\"1\">自有</Option>\r\n                  <Option value=\"2\">铁塔</Option>\r\n                  <Option value=\"3\">自有预估</Option>\r\n                  <Option value=\"4\">铁塔预估</Option>\r\n                  <Option value=\"5\">自有挂账</Option>\r\n                  <Option value=\"6\">自有预付</Option>\r\n                  <Option value=\"7\">铁塔挂账</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"用电类型:\" prop=\"classifications\" class=\"form-line-height\">\r\n                <Cascader :style=\"formItemWidth\" :data=\"classificationData\"\r\n                          v-model=\"classifications\"></Cascader>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"电表用途：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select v-model=\"queryParams.ammeteruse\" :style=\"formItemWidth\">\r\n                  <Option v-for=\"item in ammeteruseList\" :value=\"item.typeCode\" :key=\"item.typeCode\">\r\n                    {{ item.typeName }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                <Select v-model=\"queryParams.company\" @on-change=\"selectChange(queryParams.company)\"\r\n                        :style=\"formItemWidth\">\r\n                  <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                  <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                <Input :clearable=true icon=\"ios-archive\" v-model=\"queryParams.countryName\"\r\n                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly\r\n                       :style=\"formItemWidth\">\r\n                </Input>\r\n              </FormItem>\r\n              <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n                <Select v-model=\"queryParams.country\" :style=\"formItemWidth\">\r\n                  <Option value=\"-1\">全部</Option>\r\n                  <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{ item.name }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"状态：\" prop=\"status\" class=\"form-line-height\">\r\n                <Select v-model=\"queryParams.status\" :style=\"formItemWidth\">\r\n                  <Option value=\"\">请选择</Option>\r\n                  <Option value=\"1\">未归集</Option>\r\n                  <Option value=\"4\">已归集</Option>\r\n                  <Option value=\"2\">报账中</Option>\r\n                  <Option value=\"3\">报账完成</Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"对外结算类型：\" prop=\"directsupplyflag\" class=\"form-line-height\">\r\n                <Select v-model=\"queryParams.directsupplyflag\" :style=\"formItemWidth\">\r\n                  <Option v-for=\"item in directsupplyflags\" :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\">{{\r\n                      item.typeName\r\n                    }}\r\n                  </Option>\r\n                </Select>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <Row>\r\n            <Col span=\"6\">\r\n\r\n              <FormItem label=\"资源局站/房屋/站址编码:\" prop=\"resstationcode\" class=\"form-line-height\">\r\n                <cl-input v-model=\"queryParams.resstationcode\" placeholder=\"请输入资源局站/房屋/站址编码\"\r\n                          :style=\"formItemWidth\"/>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"供电局电表编号:\" prop=\"supplybureauammetercode\" class=\"form-line-height\">\r\n                <cl-input v-model=\"queryParams.supplybureauammetercode\" placeholder=\"请输入供电局电表编号\"\r\n                          :style=\"formItemWidth\"/>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"起始财辅账期：\" prop=\"startCfzq\" class=\"form-line-height\">\r\n                <DatePicker v-model=\"startCfzqPicker\" type=\"month\" @on-change='startCfzqChange'\r\n                            placeholder=\"起始财辅账期\" format=\"yyyyMM\" :style=\"formItemWidth\"></DatePicker>\r\n              </FormItem>\r\n            </Col>\r\n            <Col span=\"6\">\r\n              <FormItem label=\"截止财辅账期：\" prop=\"endCfzq\" class=\"form-line-height\">\r\n                <DatePicker v-model=\"endCfzqPicker\" type=\"month\" @on-change='endCfzqChange'\r\n                            placeholder=\"截止财辅账期\" format=\"yyyyMM\" :style=\"formItemWidth\"></DatePicker>\r\n              </FormItem>\r\n            </Col>\r\n          </Row>\r\n          <div align=\"right\">\r\n            <Button style=\"margin-left: 5px;width:69px;\" type=\"success\" icon=\"ios-search\"\r\n                    @click=\"_onSearchHandle()\">搜索\r\n            </Button>\r\n            <Button style=\"margin-left: 5px;width:69px;\" type=\"info\" icon=\"ios-redo\"\r\n                    @click=\"_onResetHandle\">重置\r\n            </Button>\r\n            <Dropdown @on-click=\"exportCsv\">\r\n              <Button type='default' style=\"margin-left: 5px\">导出\r\n                <Icon type='ios-arrow-down'></Icon>\r\n              </Button>\r\n              <DropdownMenu slot='list' v-if=\"version == 'ln'\">\r\n                <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n\r\n              </DropdownMenu>\r\n              <DropdownMenu slot='list' style=\"text-align: center\" v-if=\"version == 'sc'\">\r\n                <Dropdown placement=\"left\">\r\n                  <DropdownItem name=\"current\">导出本页</DropdownItem>\r\n                  <DropdownMenu slot=\"list\">\r\n                    <DropdownItem name=\"current_more\">关联电表/协议</DropdownItem>\r\n                    <!-- <DropdownItem name=\"current\">仅台账信息</DropdownItem>-->\r\n                  </DropdownMenu>\r\n                </Dropdown>\r\n                <Dropdown placement=\"left\">\r\n                  <DropdownItem name=\"all\">导出全部</DropdownItem>\r\n                  <DropdownMenu slot=\"list\">\r\n                    <DropdownItem name=\"all_more\">关联电表/协议</DropdownItem>\r\n                    <!-- <DropdownItem name=\"all\">仅台账信息</DropdownItem>-->\r\n                  </DropdownMenu>\r\n                </Dropdown>\r\n                <!-- <Dropdown placement=\"left\">\r\n                  <DropdownItem name=\"allfinance\">导出全部(账期)</DropdownItem>\r\n                </Dropdown>-->\r\n              </DropdownMenu>\r\n            </Dropdown>\r\n          </div>\r\n        </Form>\r\n      </Row>\r\n      <div class=\"filter-divider\">\r\n        <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n              @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n      </div>\r\n    </div>\r\n    <div>\r\n      <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n      <upload-file-modal ref=\"uploadFileModal\"></upload-file-modal>\r\n    </div>\r\n    <div>\r\n      <Page size=\"small\" :total=\"pageTotal\" :current=\"pageNum\" :page-size=\"pageSize\" show-elevator show-sizer\r\n            show-total\r\n            placement=\"top\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n      <Table ref=\"listTable\"\r\n             border :loading=\"listTb.loading\"\r\n             :columns=\"listTb.columns\"\r\n             :data=\"insideData\"\r\n             class=\"mytable\"></Table>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport {blist} from \"@/libs/tools\";\r\nimport axios from '@/libs/api.request';\r\nimport {\r\n  getClassification,\r\n  getCountryByUserId,\r\n  getCountrysdata,\r\n  getUserByUserRole,\r\n  getUserdata\r\n} from '@/api/basedata/ammeter.js'\r\nimport {allAccountTotal} from '@/api/account';\r\nimport {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\nimport CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\nimport excel from '@/libs/excel'\r\nimport indexData from '@/config/index'\r\nimport UploadFileModal from \"./uploadFileModal\";\r\n\r\nexport default {\r\n  name: \"ccountList\",\r\n  components: {CountryModal, UploadFileModal},\r\n  data() {\r\n    let maopao = (h, params) => {\r\n      let str = ''\r\n      let index = params.index;\r\n      if (index < this.pageSize / 2) {\r\n        str = 'bottom'\r\n      } else {\r\n        str = 'top'\r\n      }\r\n\r\n      return h('div', [\r\n        h('Tooltip', {\r\n          props: {placement: str}\r\n        }, [\r\n          h('span', {\r\n            style: {\r\n              display: 'inline-block',\r\n              width: params.column._width * 0.9 + 'px',\r\n              overflow: 'hidden',\r\n              textOverflow: 'ellipsis',\r\n              whiteSpace: 'nowrap',\r\n            },\r\n          }, params.row.remark),\r\n          h('span', {\r\n            slot: 'content',\r\n            style: {whiteSpace: 'normal', wordBreak: 'break-all'}\r\n          }, params.row.remark)\r\n        ])\r\n      ])\r\n    }\r\n    let renderDirectsupplyflag = (h, params) => {\r\n      var directsupplyflag = \"\";\r\n      for (let item of this.directsupplyflags) {\r\n        if (item.typeCode == params.row.directsupplyflag) {\r\n          directsupplyflag = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", directsupplyflag);\r\n    };\r\n    let renderStatus = (h, {row, index}) => {\r\n      var status = \"\";\r\n      for (let item of this.accountStatus) {\r\n        if (item.typeCode == row.status) {\r\n          status = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", status);\r\n    };\r\n    let billType = (h, {row, index}) => {\r\n      var type = ''\r\n      if (row.property === 2) {\r\n        type = '铁塔'\r\n      } else {\r\n        type = '自有'\r\n      }\r\n      if (row.accounttype === 2) {\r\n        if (row.accountestype === 1) {\r\n          type += '预估'\r\n        } else if (row.accountestype === 2) {\r\n          type += '挂账'\r\n        } else if (row.accountestype === 3) {\r\n          type += '预付'\r\n        }\r\n\r\n      }\r\n      if (row.property == null || row.accounttype == null) {\r\n        type = ''\r\n      }\r\n      return h(\"div\", type);\r\n    }\r\n\r\n    let renderCategory = (h, params) => {\r\n      var categoryname = \"\";\r\n      for (let item of this.categorys) {\r\n        if (item.typeCode == params.row.category) {\r\n          categoryname = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", categoryname);\r\n    };\r\n\r\n    let renderAmmeteruse = (h, {row, index}) => {\r\n      var ammeteruse = \"\";\r\n      for (let item of this.ammeteruseList) {\r\n        if (item.typeCode == row.ammeteruse) {\r\n          ammeteruse = item.typeName;\r\n          break;\r\n        }\r\n      }\r\n      return h(\"div\", ammeteruse);\r\n    };\r\n    let photo = (h, {row, index}) => {\r\n      let that = this\r\n      let str = ''\r\n      if (row.projectname != '小计' && row.projectname != '合计') {\r\n        str = '附件'\r\n      }\r\n      return h(\"div\", [h(\"u\", {\r\n        on: {\r\n          click() {\r\n            //打开弹出框\r\n            if (row.projectname != '小计' && row.projectname != '合计') {\r\n              that.uploadFile(row)\r\n            }\r\n          }\r\n        }\r\n      }, str)]);\r\n    };\r\n\r\n    let columns_ln = [\r\n      {title: '项目名称', key: 'projectname', align: 'center', width: 150,},\r\n      {title: '供电局电表编号', key: 'supplybureauammetercode', align: 'center', width: 150,},\r\n      {title: '局站', key: 'stationName', align: 'center', width: 150,},\r\n      {title: '资源局站/房屋/站址编码', key: 'resstationcode', align: 'center', width: 150,},\r\n      {title: '分局/支局', key: 'substation', align: 'center', width: 150,},\r\n      {title: '对外结算类型', align: 'center', key: 'directsupplyflag', width: 80, render: renderDirectsupplyflag},\r\n      {title: '期号', key: 'accountno', align: 'center', width: 90,},\r\n      {title: '所属分公司', key: 'company', align: 'center', width: 90,},\r\n      {title: '所属部门', key: 'country', align: 'center', width: 90,},\r\n      {title: '倍率', key: 'magnification', align: 'center', width: 90,},\r\n      {title: '定额', key: 'quotareadings', align: 'center', width: 90,},\r\n      {title: '浮动比（%）', key: 'quotereadingsratio', align: 'center', width: 90,},\r\n      {title: '起始日期', key: 'startdate', align: 'center', width: 100,},\r\n      {title: '截止日期', key: 'enddate', align: 'center', width: 100,},\r\n      {title: '本期峰段起度', key: 'prevhighreadings', align: 'center', width: 90,},\r\n      {title: '本期平段起度', key: 'prevflatreadings', align: 'center', width: 90,},\r\n      {title: '本期谷段起度', key: 'prevlowreadings', align: 'center', width: 90,},\r\n      {title: '本期起度', key: 'prevtotalreadings', align: 'center', width: 90,},\r\n      {title: '本期峰段止度', key: 'curhighreadings', align: 'center', width: 90,},\r\n      {title: '本期平段止度', key: 'curflatreadings', align: 'center', width: 90,},\r\n      {title: '本期谷段止度', key: 'curlowreadings', align: 'center', width: 90,},\r\n      {title: '本期止度', key: 'curtotalreadings', align: 'center', width: 90,},\r\n      {title: '用电量(度)', key: 'curusedreadings', align: 'center', width: 90,},\r\n      {title: '电损(度)', key: 'transformerullage', align: 'center', width: 90,},\r\n      {title: '总电量(度)', key: 'totalusedreadings', align: 'center', width: 90,},\r\n      {title: '电价(元)', key: 'unitpirce', align: 'center', width: 90,},\r\n      {title: '普票含税金额(元)', key: 'inputticketmoney', align: 'center', width: 60,},\r\n      {title: '专票含税金额(元)', key: 'inputtaxticketmoney', align: 'center', width: 60,},\r\n      {title: '实际普票含税金额(元)', key: 'ticketmoney', align: 'center', width: 90,},\r\n      {title: '实际专票含税金额(元)', key: 'taxticketmoney', align: 'center', width: 90,},\r\n      {title: '专票税率（%）', key: 'taxrate', align: 'center', width: 80,},\r\n      {title: '专票税额', key: 'taxamount', align: 'center', width: 60,},\r\n      {title: '其他(元)', key: 'ullagemoney', align: 'center', width: 90,},\r\n      {title: '实缴费用(元)含税', key: 'accountmoney', align: 'center', width: 90,},\r\n      {title: '总金额（不含税）', key: 'totalBHS', align: 'center', width: 90,},\r\n      {title: '分割比例(%)', key: 'percent', width: 90, align: 'center'},\r\n      {title: '备注', key: 'remark', align: 'center', width: 150, render: maopao},\r\n      {title: '类型描述', key: 'categoryname', align: 'center', width: 90,},\r\n      {title: '用电类型', key: 'electrotypename', align: 'center', width: 94,},\r\n      {title: '电表用途', key: 'ammeterusename', align: 'center', width: 90,},\r\n      {title: '台账类型', key: 'type', align: 'center', width: 90,},\r\n      {title: '状态', key: 'status', align: 'center', width: 90,},\r\n      {title: '录入人', key: 'inputname', align: 'center', width: 90,},\r\n      {title: '归集单事项名称', key: 'note', align: 'center', width: 90,},\r\n    ]\r\n    let columns_sc = [\r\n      {title: '项目名称', key: 'projectname', align: 'center', width: 150,},\r\n      {title: '电表户号/协议编码', key: 'ammetercode', align: 'center', width: 150,},\r\n      {title: '局站', key: 'stationName', align: 'center', width: 150,},\r\n      {title: '资源局站/房屋/站址编码', key: 'resstationcode', align: 'center', width: 150,},\r\n      {title: '分局/支局', key: 'substation', align: 'center', width: 150,},\r\n      {title: '对外结算类型', align: 'center', key: 'directsupplyflag', width: 80, render: renderDirectsupplyflag},\r\n      {title: '期号', key: 'accountno', align: 'center', width: 90,},\r\n      {title: '所属分公司', key: 'company', align: 'center', width: 90,},\r\n      {title: '所属部门', key: 'country', align: 'center', width: 90,},\r\n      {title: '倍率', key: 'magnification', align: 'center', width: 90,},\r\n      {title: '定额', key: 'quotareadings', align: 'center', width: 90,},\r\n      {title: '浮动比（%）', key: 'quotereadingsratio', align: 'center', width: 90,},\r\n      {title: '起始日期', key: 'startdate', align: 'center', width: 100,},\r\n      {title: '截止日期', key: 'enddate', align: 'center', width: 100,},\r\n      {title: '本期峰段起度', key: 'prevhighreadings', align: 'center', width: 90,},\r\n      {title: '本期峰段止度', key: 'curhighreadings', align: 'center', width: 90,},\r\n      {title: '峰段加减电量', key: 'highreadings', align: 'center', width: 90,},\r\n      {title: '本期平段起度', key: 'prevflatreadings', align: 'center', width: 90,},\r\n      {title: '本期平段止度', key: 'curflatreadings', align: 'center', width: 90,},\r\n      {title: '平段加减电量', key: 'flatreadings', align: 'center', width: 90,},\r\n      {title: '本期谷段起度', key: 'prevlowreadings', align: 'center', width: 90,},\r\n      {title: '本期谷段止度', key: 'curlowreadings', align: 'center', width: 90,},\r\n      {title: '谷段加减电量', key: 'lowreadings', align: 'center', width: 90,},\r\n      {title: '本期起度', key: 'prevtotalreadings', align: 'center', width: 90,},\r\n      {title: '本期止度', key: 'curtotalreadings', align: 'center', width: 90,},\r\n      {title: '用电量(度)', key: 'curusedreadings', align: 'center', width: 90,},\r\n      {title: '电损(度)', key: 'transformerullage', align: 'center', width: 90,},\r\n      {title: '总电量(度)', key: 'totalusedreadings', align: 'center', width: 90,},\r\n      {title: '电价(元)', key: 'unitpirce', align: 'center', width: 90,},\r\n      {title: '专票含税金额(元)', key: 'inputtaxticketmoney', align: 'center', width: 60,},\r\n      {title: '普票金额(元)', key: 'inputticketmoney', align: 'center', width: 60,},\r\n      {title: '普票税额(元)', key: 'tickettaxamount', align: 'center', width: 60,},\r\n      {title: '实际普票含税金额(元)', key: 'ticketmoney', align: 'center', width: 90,},\r\n      {title: '实际专票含税金额(元)', key: 'taxticketmoney', align: 'center', width: 90,},\r\n      {title: '税率（%）', key: 'taxrate', align: 'center', width: 80,},\r\n      {title: '税额', key: 'taxamount', align: 'center', width: 60,},\r\n      {title: '其他(元)', key: 'ullagemoney', align: 'center', width: 90,},\r\n      {title: '实缴费用(元)含税', key: 'accountmoney', align: 'center', width: 90,},\r\n      {title: '总金额（不含税）', key: 'totalBHS', align: 'center', width: 90,},\r\n      {title: '分割比例(%)', key: 'percent', width: 90, align: 'center'},\r\n      {title: '备注', key: 'remark', align: 'center', width: 150, render: maopao},\r\n      {title: '类型描述', key: 'categoryname', align: 'center', width: 90,},\r\n      {title: '用电类型', key: 'electrotypename', align: 'center', width: 94,},\r\n      {title: '电表用途', key: 'ammeterusename', align: 'center', width: 90,},\r\n      {title: '台账类型', key: 'type', align: 'center', width: 90,},\r\n      {title: '状态', key: 'status', align: 'center', width: 90,},\r\n      {title: '录入人', key: 'inputname', align: 'center', width: 90,},\r\n      {title: '归集单事项名称', key: 'note', align: 'center', width: 90,},\r\n      {title: '网管C网编号', key: 'nmccode', width: 150, align: 'center'},\r\n      {title: '网管编号L2.1G', key: 'nml2100', width: 90, align: 'center'},\r\n      {title: '网管编号L1.8G', key: 'nml1800', width: 90, align: 'center'},\r\n      {title: '网管编号C+L800M', key: 'nmcl800m', width: 90, align: 'center'},\r\n      {title: '合同对方', key: 'contractothpart', width: 90, align: 'center'},\r\n      {title: '财辅报账单号', key: 'billId', width: 90, align: 'center'},\r\n      {title: '报账人', key: 'fillinname', width: 90, align: 'center'},\r\n      {title: '财辅账期', key: 'budgetsetname', width: 90, align: 'center'},\r\n      {title: '附件', slot: 'file', align: 'center', width: 60, render: photo},\r\n    ]\r\n\r\n    return {\r\n      exportloading: false,\r\n      formItemWidth: widthstyle,\r\n      version: '',\r\n      queryParams: {\r\n        startAccountno: '',//起始局站电费月台帐\r\n        endAccountno: '',//截止局站电费月台帐\r\n        substation: '',//支局\r\n        projectname: '',//项目名称\r\n        ammetercode: '',//电表户号/协议编码\r\n        accountType: '',//台账类型\r\n        company: '',//分公司\r\n        country: '',//所属部门\r\n        status: '',//状态\r\n        ammeteruse: '',\r\n        countryName: '',\r\n        supplybureauammetercode: '',\r\n        resstationcode: '',\r\n        directsupplyflag: '',\r\n        iffinance: 0,\r\n        startYear: '',\r\n        startMonth: '',\r\n        endYear: '',\r\n        endMonth: ''\r\n      },\r\n      startDatePicker: '',\r\n      endDatePicker: '',\r\n      startCfzqPicker: '',\r\n      endCfzqPicker: '',\r\n      companies: [],\r\n      departments: [],\r\n      columns_ln: columns_ln,//辽宁表头\r\n      columns_sc: columns_sc,//四川表头\r\n      isAdmin: false,\r\n      categorys: [],//描述类型\r\n      directsupplyflags: [],\r\n      company: null,//用户默认公司\r\n      country: null,//用户默认所属部门\r\n      countryName: null,//用户默认所属部门\r\n      classificationData: [],//用电类型树\r\n      classifications: [],//选择的用电类型树\r\n      filterColl: true,//搜索面板展开\r\n      exportable: false,\r\n      accountStatus: [],\r\n      ammeteruseList: [],\r\n      billtypes: [],\r\n      export: {\r\n        run: false,//是否正在执行导出\r\n        data: \"\",//导出数据\r\n        totalPage: 0,//一共多少页\r\n        currentPage: 0,//当前多少页\r\n        percent: 0,\r\n        size: ********\r\n      },\r\n      subtotal: '',\r\n      alltotal: '',\r\n      url: 'business/account/selectListByParams',\r\n      listTb: {\r\n        loading: false,\r\n        columns: [],\r\n        data: []\r\n      },\r\n      insideData: [],\r\n      pageTotal: 0,\r\n      pageNum: 1,\r\n      pageSize: 10,//当前页\r\n    }\r\n  },\r\n  methods: {\r\n    selectChange() {\r\n      let that = this;\r\n      if (that.queryParams.company != undefined) {\r\n        if (that.queryParams.company == \"-1\") {\r\n          that.queryParams.country = -1;\r\n          that.queryParams.countryName = null;\r\n        } else {\r\n          getCountryByUserId(that.queryParams.company).then(res => {\r\n            if (res.data.departments.length != 0) {\r\n              that.queryParams.country = res.data.departments[0].id;\r\n              that.queryParams.countryName = res.data.departments[0].name;\r\n            }\r\n          });\r\n        }\r\n      }\r\n    },\r\n    //选择所属部门开始\r\n    chooseResponseCenter() {\r\n      if (this.queryParams.company == null || this.queryParams.company == \"-1\") {\r\n        this.$Message.info(\"请先选择分公司\");\r\n        return;\r\n      }\r\n      this.$refs.countryModal.choose(this.queryParams.company);//所属部门\r\n    },\r\n    getDataFromModal(data) {\r\n      this.queryParams.country = data.id;\r\n      this.queryParams.countryName = data.name;\r\n      //选择所属部门结束\r\n    },\r\n    //翻页\r\n    handlePage(value) {\r\n      this.pageNum = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //改变表格可显示数据数量\r\n    handlePageSize(value) {\r\n      this.pageSize = value;\r\n      this.getAccountMessages();\r\n    },\r\n    //向后台请求数据\r\n    getAccountMessages() {\r\n      if (this.queryParams.countryName == \"\") {\r\n        this.queryParams.country = \"-1\";\r\n      }\r\n      this.setElectroyType();\r\n      let params = this.queryParams;\r\n      params.pageNum = this.pageNum;\r\n      params.pageSize = this.pageSize;\r\n      let req = {\r\n        url: this.url,\r\n        method: \"get\",\r\n        params: params\r\n      };\r\n      let array = [];\r\n      this.listTb.loading = true;\r\n      axios.request(req).then(res => {\r\n        this.listTb.loading = false;\r\n        if (res.data) {\r\n          array = res.data.rows;\r\n          this.setdata(array)\r\n          this.totalBHS(array)\r\n          array.push(this.suntotal(array))//小计\r\n          this.pageTotal = res.data.total || 0\r\n          allAccountTotal(this.queryParams).then(res => {//合计\r\n            let alltotal = res.data\r\n            alltotal.total = '合计'\r\n            alltotal.projectname = '合计'\r\n            array.push(alltotal)\r\n          });\r\n          this.insideData = array\r\n        }\r\n      }).catch(err => {\r\n        console.log(err);\r\n      });\r\n    },\r\n    setdata(array) {\r\n      let accountStatus = this.accountStatus\r\n      let categorys = this.categorys\r\n      let ammeteruseList = this.ammeteruseList\r\n      let billtypes = this.billtypes\r\n      array.forEach(function (row) {\r\n        var status = \"\";\r\n        for (let i of accountStatus) {\r\n          if (i.typeCode == row.status) {\r\n            status = i.typeName;\r\n            break;\r\n          }\r\n        }\r\n        row.status = status\r\n\r\n        var type = ''\r\n        for (let item of billtypes) {\r\n          if (item.typeCode == row.billtype) {\r\n            type = item.typeName;\r\n            break;\r\n          }\r\n        }\r\n\r\n        row.type = type\r\n\r\n        var categoryname = \"\";\r\n        for (let item of categorys) {\r\n          if (item.typeCode == row.category) {\r\n            categoryname = item.typeName;\r\n            break;\r\n          }\r\n        }\r\n        row.categoryname = categoryname\r\n\r\n        var ammeteruse = \"\";\r\n        for (let item of ammeteruseList) {\r\n          if (item.typeCode == row.ammeteruse) {\r\n            ammeteruse = item.typeName;\r\n            break;\r\n          }\r\n        }\r\n\r\n        row.ammeterusename = ammeteruse\r\n      })\r\n    },\r\n    totalBHS(array) {\r\n      array.forEach(function (item) {\r\n        let taxticketmoney = item.taxticketmoney;//专票含税金额\r\n        let ticketmoney = item.ticketmoney;//普票含税金额\r\n        let ullagemoney = item.ullagemoney;//其他费用\r\n        let taxamount = item.taxamount;//税额\r\n        let total = ticketmoney + (taxticketmoney - taxamount) + ullagemoney\r\n        total = total.toFixed(2)\r\n        item.totalBHS = total\r\n      })\r\n    },\r\n    //小计\r\n    suntotal(array) {\r\n      let curusedreadings = 0\r\n      let transformerullage = 0\r\n      let ticketmoney = 0\r\n      let taxticketmoney = 0\r\n      let taxamount = 0\r\n      let ullagemoney = 0\r\n      let accountmoney = 0\r\n      let inputtaxticketmoney = 0\r\n      let inputticketmoney = 0\r\n      let tickettaxamount = 0\r\n      let total = 0\r\n      array.forEach(function (item) {\r\n        if (item.effective === 1) {\r\n          curusedreadings += item.curusedreadings\r\n          transformerullage += item.transformerullage\r\n          ticketmoney += item.ticketmoney\r\n          taxticketmoney += item.taxticketmoney\r\n          taxamount += item.taxamount\r\n          inputtaxticketmoney += item.inputtaxticketmoney\r\n          inputticketmoney += item.inputticketmoney\r\n          ullagemoney += item.ullagemoney\r\n          accountmoney += item.accountmoney\r\n          total += parseFloat(item.totalBHS)\r\n          tickettaxamount += item.tickettaxamount\r\n        }\r\n      })\r\n      return {\r\n        curusedreadings: curusedreadings,\r\n        transformerullage: transformerullage,\r\n        ticketmoney: ticketmoney.toFixed(2),\r\n        taxticketmoney: taxticketmoney.toFixed(2),\r\n        taxamount: taxamount.toFixed(2),\r\n        inputtaxticketmoney: inputtaxticketmoney.toFixed(2),\r\n        inputticketmoney: inputticketmoney.toFixed(2),\r\n        ullagemoney: ullagemoney.toFixed(2),\r\n        accountmoney: accountmoney.toFixed(2),\r\n        totalBHS: total.toFixed(2),\r\n        tickettaxamount: tickettaxamount.toFixed(2),\r\n        total: '小计',\r\n        projectname: '小计',\r\n        _disabled: true\r\n      }\r\n    },\r\n    _onSearchHandle() {\r\n      this.pageNum = 1\r\n      this.getAccountMessages()\r\n    },\r\n    _onResetHandle() {\r\n      this.queryParams = {company: null, country: null, countryName: null};\r\n      this.startDatePicker = '';\r\n      this.endDatePicker = '';\r\n      this.startCfzqPicker = '';\r\n      this.endCfzqPicker = '';\r\n      this.classifications = [];\r\n      this.queryParams.company = this.company;\r\n      this.queryParams.country = Number(this.country);\r\n      this.queryParams.countryName = this.countryName;\r\n    },\r\n    startChange(year) {\r\n      this.queryParams.startAccountno = year\r\n    },\r\n    endChange(year) {\r\n      this.queryParams.endAccountno = year\r\n    },\r\n    startCfzqChange(year) {\r\n      if(year){\r\n        this.queryParams.startYear = year.substring(0,4);\r\n        this.queryParams.startMonth = year.substring(4,6);\r\n      } else{\r\n        this.queryParams.startYear = '';\r\n        this.queryParams.startMonth = '';\r\n      }\r\n    },\r\n    endCfzqChange(year) {\r\n      if(year){\r\n        this.queryParams.endYear = year.substring(0,4);\r\n        this.queryParams.endMonth = year.substring(4,6);\r\n      } else{\r\n        this.queryParams.endYear = '';\r\n        this.queryParams.endMonth = '';\r\n      }\r\n    },\r\n    setElectroyType() {\r\n      let types = this.classifications;\r\n      this.queryParams.electrotype = types[types.length - 1]\r\n    },\r\n    beforeLoadData(data) {\r\n      var cols = [], keys = []\r\n      for (var i = 0; i < this.listTb.columns.length; i++) {\r\n        cols.push(this.listTb.columns[i].title)\r\n        keys.push(this.listTb.columns[i].key)\r\n      }\r\n      const params = {\r\n        title: cols,\r\n        key: keys,\r\n        data: data,\r\n        autoWidth: true,\r\n        filename: '台账导出数据'\r\n      }\r\n      excel.export_array_to_excel(params)\r\n      return\r\n    },\r\n    exportCsv(name) {\r\n      /*                this.errorTips(\"报账高峰13-25号暂停使用\");\r\n                      return;*/\r\n      if (this.queryParams.countryName == \"\") {\r\n        this.queryParams.country = \"-1\";\r\n      }\r\n      this.setElectroyType();\r\n      let params = this.queryParams;\r\n      if (name.split('_').length > 1) {\r\n        // 导出包含电表/协议信息\r\n        params.moreMes = name.split('_')[1];\r\n      } else {\r\n        params.moreMes = \"\";\r\n      }\r\n      name = name.split('_')[0];\r\n      console.log(name);\r\n      console.log(JSON.stringify(params));\r\n      if (name === 'current') {\r\n        params.pageNum = this.pageNum;\r\n        params.pageSize = this.pageSize;\r\n        params.iffinance = 0;\r\n      } else if (name === 'all') {\r\n        if (params.endAccountno == null || params.endAccountno.length == 0 || params.startAccountno == null || params.startAccountno.length == 0) {\r\n          this.errorTips(\"全部导出时，必须选择起止期号\");\r\n          return;\r\n        }\r\n        if (this.version == 'sc')\r\n        {  if ((Monthdiff(params.endAccountno, params.startAccountno)) > 4) {\r\n            this.errorTips(\"全部导出时，一次性只能导出4个月的数据\");\r\n            return;\r\n          }\r\n      }\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n        params.iffinance = 0;\r\n      } else if (name === 'allfinance') {\r\n        if (params.endAccountno == null || params.endAccountno.length == 0 || params.startAccountno == null || params.startAccountno.length == 0) {\r\n          this.errorTips(\"全部导出时，必须选择起止期号\");\r\n          return;\r\n        }\r\n        if (this.version == 'sc') {\r\n          if ((Monthdiff(params.endAccountno, params.startAccountno)) > 4) {\r\n            this.errorTips(\"全部导出时，一次性只能导出4个月的数据\");\r\n            return;\r\n          }\r\n        }\r\n        params.pageNum = 1;\r\n        params.pageSize = this.export.size;\r\n        params.iffinance = 1;\r\n      }\r\n      let req = {\r\n        url: \"/business/account/exportcx\",\r\n        method: \"get\",\r\n        params: params\r\n      };\r\n      this.exportloading = true\r\n      axios.file(req).then(res => {\r\n        this.exportloading = false\r\n        const content = res\r\n        const blob = new Blob([content])\r\n        const fileName = '台账查询导出数据' + '.xlsx';\r\n        if ('download' in document.createElement('a')) { // 非IE下载\r\n          const elink = document.createElement('a')\r\n          elink.download = fileName\r\n          elink.style.display = 'none'\r\n          elink.href = URL.createObjectURL(blob)\r\n          document.body.appendChild(elink)\r\n          elink.click()\r\n          URL.revokeObjectURL(elink.href) // 释放URL 对象\r\n          document.body.removeChild(elink)\r\n        } else { // IE10+下载\r\n          navigator.msSaveBlob(blob, fileName)\r\n        }\r\n      }).catch(err => {\r\n        this.exportloading = false;\r\n        console.log(err);\r\n      });\r\n    },\r\n    getUserData() {\r\n      let that = this;\r\n      getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n        if (res.data.companies.length != 0) {\r\n          let companies = res.data.companies;\r\n          if (res.data.companies[0].id == \"**********\") {\r\n            companies = that.companies;\r\n          }\r\n          that.company = companies[0].id;\r\n          that.queryParams.company = companies[0].id;\r\n        }\r\n        if (res.data.departments.length != 0) {\r\n          let departments = res.data.departments;\r\n          if (res.data.companies[0].id == \"**********\" && that.departments.length != 0) {\r\n            departments = that.departments\r\n          }\r\n          that.country = departments[0].id;\r\n          that.countryName = departments[0].name;\r\n          that.queryParams.country = Number(departments[0].id);\r\n          that.queryParams.countryName = departments[0].name;\r\n        }\r\n\r\n        that.pageNum = 1\r\n        that.getAccountMessages();\r\n      });\r\n    },\r\n    getno() {\r\n      let date = new Date();\r\n      //获取当前年月\r\n      let year = date.getFullYear();\r\n      let month = date.getMonth() + 1;\r\n      month = (month < 10 ? \"0\" + month : month);\r\n      let curDate = (year.toString() + month.toString());\r\n      return curDate;\r\n    },\r\n    //验证错误弹出提示框并跳转到下一格\r\n    errorTips(str) {\r\n      this.$Notice.error({\r\n        title: '提示',\r\n        desc: str,\r\n        duration: 10\r\n      });\r\n    },\r\n    uploadFile(row) {\r\n      this.$refs.uploadFileModal.showadd = false;\r\n      this.$refs.uploadFileModal.columns.pop();\r\n      this.$refs.uploadFileModal.choose(row.pcid + '');\r\n    },\r\n  },\r\n  mounted() {\r\n    this.accountStatus = blist(\"accountStatus\");\r\n    this.ammeteruseList = blist('ammeterUse');\r\n    this.categorys = blist(\"ammeterCategory\");\r\n    this.directsupplyflags = blist(\"directSupplyFlag\");\r\n    this.billtypes = blist(\"billpreType\");\r\n    let that = this;\r\n    that.startDatePicker = that.getno()\r\n    that.queryParams.startAccountno = that.getno()\r\n    getUserByUserRole().then(res => {//根据权限获取分公司\r\n      that.companies = res.data.companies;\r\n      if (res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true) {\r\n        that.isAdmin = true;\r\n      }\r\n      getCountrysdata({orgCode: res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n        that.departments = res.data;\r\n        that.getUserData();\r\n      });\r\n    });\r\n\r\n    getClassification().then(res => {//用电类型\r\n      this.classificationData = res.data;\r\n    });\r\n    this.version = indexData.version\r\n    if (this.version == 'ln') {\r\n      this.listTb.columns = this.columns_ln\r\n    } else if (this.version == 'sc') {\r\n      this.listTb.columns = this.columns_sc\r\n    }\r\n\r\n  }\r\n}\r\n\r\nfunction Monthdiff(a, b) {\r\n  var year1 = a.substring(0, 4);\r\n  var year2 = b.substring(0, 4);\r\n  var month1 = a.substring(4, 6).replace(/0/, \"\");\r\n  var month2 = b.substring(4, 6).replace(/0/, \"\");\r\n  return (year1 - year2) * 12 + (month1 - month2)+1;\r\n\r\n}\r\n</script>"]}]}