{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addCoalAccount.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\addCoalAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\.babelrc", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["addCoalAccount.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SACA,iBADA,EAEA,WAFA,EAGA,eAHA,EAIA,yBAJA,EAKA,wBALA,EAMA,2BANA,EAOA,0BAPA,EAQA,aARA,EASA,mBATA,EAUA,oBAVA,EAWA,aAXA,EAYA,sBAZA,EAaA,uBAbA,EAcA,8BAdA,EAeA,sBAfA,EAgBA,4BAhBA,EAiBA,sBAjBA,EAkBA,eAlBA,EAmBA,cAnBA,EAoBA,oBApBA,EAqBA,YArBA,EAsBA,cAtBA,EAuBA,QAvBA,EAwBA,YAxBA,EAyBA,YAzBA,EA0BA,aA1BA,QA2BA,uCA3BA;AA4BA,SACA,eADA,EAEA,iBAFA,EAGA,aAHA,QAIA,0BAJA;AAKA,SAAA,YAAA,QAAA,4BAAA;AACA,OAAA,sBAAA,MAAA,6CAAA;AACA,OAAA,WAAA,MAAA,kCAAA;AACA,OAAA,UAAA,MAAA,iCAAA;AACA,SAAA,QAAA,EAAA,UAAA,QAAA,mCAAA;AACA,OAAA,KAAA,MAAA,oBAAA;AACA,OAAA,aAAA,MAAA,iBAAA;AACA,SAAA,oBAAA,QAAA,+BAAA;AACA,OAAA,UAAA,MAAA,uBAAA;AACA,SAAA,aAAA,QAAA,sBAAA;AACA,SAAA,MAAA,QAAA,cAAA;AACA,SAAA,UAAA,QAAA,mDAAA;AACA,OAAA,iBAAA,MAAA,qBAAA;AACA,OAAA,SAAA,MAAA,gBAAA;AACA,OAAA,YAAA,MAAA,sCAAA;AACA,SAAA,WAAA,EAAA,iBAAA,EAAA,eAAA,EAAA,kBAAA,EAAA,WAAA,QAEA,2BAFA;AAGA,OAAA,eAAA,MAAA,gCAAA;AACA,IAAA,KAAA,GAAA,QAAA,EAAA;AACA,eAAA;AACA,EAAA,IAAA,EAAA,gBADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,eAAA,EAAA,eAAA;AAAA,IAAA,UAAA,EAAA,UAAA;AAAA,IAAA,WAAA,EAAA,WAAA;AAAA,IAAA,sBAAA,EAAA,sBAAA;AAAA,IAAA,iBAAA,EAAA,iBAAA;AAAA,IAAA,aAAA,EAAA,aAAA;AAAA,IAAA,UAAA,EAAA,UAAA;AAAA,IAAA,YAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AAAA;;AACA,QAAA,KAAA,GAAA,SAAA,KAAA,CAAA,CAAA,QAAA;AAAA,UAAA,GAAA,QAAA,GAAA;AAAA,UAAA,KAAA,QAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA;AACA,UAAA,GAAA,GAAA,MAAA;AACA,aAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,GAAA,EAAA;AACA,QAAA,EAAA,EAAA;AACA,UAAA,KADA,mBACA;AACA;AACA;AACA,YAAA,IAAA,CAAA,UAAA,CAAA,GAAA,EAHA,CAIA;AACA;AANA;AADA,OAAA,EASA,GATA,CAAA,CAAA,CAAA;AAUA,KAbA;;AAcA,WAAA;AACA,MAAA,GAAA,EAAA,EADA;AAEA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,UAFA;AAGA,QAAA,YAAA,EAAA,MAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OAFA;AAQA,MAAA,MAAA,EAAA,EARA;AASA,MAAA,OAAA,EAAA,EATA;AAUA,MAAA,aAAA,EAAA,UAVA;AAWA,MAAA,OAAA,EAAA,EAXA;AAYA,MAAA,QAAA,EAAA,KAZA;AAaA,MAAA,UAAA,EAAA,IAbA;AAaA;AACA,MAAA,SAAA,EAAA,CAAA,CAdA;AAcA;AACA,MAAA,YAAA,EAAA,CAAA,CAfA;AAeA;AACA,MAAA,OAAA,EAAA,EAhBA;AAgBA;AACA,MAAA,eAAA,EAAA,EAjBA;AAkBA,MAAA,gBAAA,EAAA,EAlBA;AAmBA,MAAA,cAAA,EAAA,EAnBA;AAoBA,MAAA,eAAA,EAAA,EApBA;AAqBA,MAAA,kBAAA,EAAA,EArBA;AAsBA,MAAA,WAAA,EAAA,EAtBA;AAuBA,MAAA,YAAA,EAAA,EAvBA;AAwBA,MAAA,WAAA,EAAA,EAxBA;AAyBA,MAAA,YAAA,EAAA,EAzBA;AA0BA,MAAA,QAAA,EAAA,KA1BA;AA0BA;AACA,MAAA,UAAA,EAAA,EA3BA;AA4BA,MAAA,SAAA,EAAA,EA5BA;AA6BA,MAAA,SAAA,EAAA,EA7BA;AA8BA,MAAA,YAAA,EAAA,EA9BA;AA+BA,MAAA,WAAA,EAAA,EA/BA;AAgCA,MAAA,OAAA,EAAA,KAhCA;AAiCA,MAAA,OAAA,EAAA,IAjCA;AAiCA;AACA,MAAA,OAAA,EAAA,IAlCA;AAkCA;AACA,MAAA,WAAA,EAAA,IAnCA;AAmCA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,SAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IADA;AACA;AACA,QAAA,OAAA,EAAA,EAFA;AAEA;AACA,QAAA,OAAA,EAAA,EAHA;AAGA;AACA,QAAA,WAAA,EAAA,EAJA;AAIA;AACA,QAAA,WAAA,EAAA,CALA;AAMA,QAAA,QAAA,EAAA,CANA;AAOA,QAAA,YAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA;AARA,OApCA;AA+CA,MAAA,SAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,OAAA,EAAA,IAFA;AAGA,QAAA,OAAA,EAAA,EAHA;AAIA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,EAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAFA,EAQA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,aAFA;AAGA;AACA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,KAAA,EAAA;AALA,SARA,EAeA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,cAFA;AAGA;AACA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAfA,EAsBA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,IAAA,EAAA,YAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAtBA,EA4BA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA5BA,EAkCA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,IAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAlCA,EAwCA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,IAAA,EAAA,gBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAxCA,EA8CA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,IAAA,EAAA,aAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA9CA,EAoDA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SApDA,EA0DA;AACA,UAAA,KAAA,EAAA,OADA;AAEA;AACA,UAAA,IAAA,EAAA,UAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,KAAA,EAAA;AALA,SA1DA,EAiEA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,GAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAjEA,EAuEA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,gBAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SAvEA,EA6EA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,eAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SA7EA,EAmFA;AACA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SApFA,EAoFA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA;AACA,UAAA,MAAA,EAAA;AANA,SApFA,CAJA;AAiGA,QAAA,UAAA,EAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA;AACA,UAAA,MAAA,EAAA;AANA,SAAA,CAjGA;AAyGA,QAAA,IAAA,EAAA;AAzGA,OA/CA;AA0JA,MAAA,SAAA,EAAA,CA1JA;AA2JA,MAAA,OAAA,EAAA,CA3JA;AA4JA,MAAA,QAAA,EAAA,EA5JA,CA4JA;;AA5JA,KAAA;AA8JA,GAhLA;AAiLA,EAAA,OAAA;AACA,IAAA,UADA,sBACA,GADA,EACA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA,EAAA,KAAA,EADA,CAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;AAEA,UAAA,GAAA,CAAA,EAAA,EAAA;AACA,aAAA,KAAA,CAAA,eAAA,CAAA,MAAA,CAAA,GAAA,CAAA,EAAA,GAAA,EAAA;AACA,OAFA,MAEA;AACA,aAAA,SAAA,CAAA,aAAA;AACA,OAlBA,CAmBA;AACA;;AACA,KAtBA;AAuBA,IAAA,MAvBA,oBAuBA,CACA;AACA,KAzBA;AA0BA,IAAA,YA1BA,0BA0BA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,IAAA,SAAA,EAAA;AACA,YAAA,IAAA,CAAA,UAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,CAAA,CAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,IAAA;AACA,SAHA,MAGA;AACA,UAAA,kBAAA,CAAA,IAAA,CAAA,UAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,EAAA;AACA,cAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA;AACA;AACA,WALA;AAMA;AACA;AACA,KAzCA;AA0CA;AACA,IAAA,oBA3CA,kCA2CA;AACA,UAAA,KAAA,UAAA,CAAA,OAAA,IAAA,IAAA,IAAA,KAAA,UAAA,CAAA,OAAA,IAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,SAAA;AAAA;AACA;;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,MAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAJA,CAIA;AACA,KAhDA;AAiDA,IAAA,gBAjDA,4BAiDA,IAjDA,EAiDA;AACA,WAAA,UAAA,CAAA,OAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,UAAA,CAAA,WAAA,GAAA,IAAA,CAAA,IAAA,CAFA,CAGA;AACA,KArDA;AAsDA,IAAA,WAtDA,yBAsDA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,WAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,EAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA;;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,cAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,WAAA;;AACA,cAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,IAAA,YAAA,IAAA,IAAA,CAAA,WAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA;;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,OAAA,GAAA,MAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,WAAA,GAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA;;AAEA,QAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,kBAAA;AACA,OAtBA;AAuBA,KA/EA;AAgFA,IAAA,UAhFA,wBAgFA;AACA,UAAA,KAAA,UAAA,CAAA,WAAA,IAAA,EAAA,EAAA;AACA,aAAA,UAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,WAAA,OAAA,GAAA,CAAA;AACA,WAAA,kBAAA;AACA,KAtFA;AAuFA,IAAA,eAvFA,6BAuFA;AACA,WAAA,UAAA;AACA,KAzFA;AA0FA;AACA,IAAA,QA3FA,sBA2FA;AACA,UAAA,KAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,KAAA;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,KAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,CAAA,GAAA,IAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA;;AACA,UAAA,CAAA,EAAA;AACA,aAAA,UAAA,CAAA,KAAA;AACA,OAFA,MAEA;AACA,aAAA,SAAA,CAAA,SAAA;AACA;AACA,KAzGA;AA0GA,IAAA,YA1GA,wBA0GA,SA1GA,EA0GA;AACA,UAAA,IAAA,GAAA,EAAA;AACA,WAAA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA;AACA,QAAA,SAAA,CAAA,GAAA,CAAA,UAAA,KAAA,EAAA;AACA,cAAA,KAAA,IAAA,KAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,SAJA;AAKA,OANA;AAOA,WAAA,MAAA,GAAA,IAAA;AACA,KApHA;AAsHA;AACA,IAAA,UAvHA,sBAuHA,IAvHA,EAuHA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,EAAA,MAAA;AACA,UAAA,CAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,GAAA,CAAA;AACA,YAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,YAAA,SAAA,GAAA,KAAA,UAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA;AACA,cAAA,GAAA,GAAA,YAAA,CAAA,IAAA,CAAA;;AACA,cAAA,GAAA,CAAA,MAAA,EAAA;AACA,gBAAA,IAAA,CAAA,EAAA,IAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,SAAA,GAAA,SAAA;AACA;;AACA,YAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,YAAA,UAAA,CAAA,IAAA,CAAA,IAAA;AACA,YAAA,MAAA;AACA,WAPA,MAOA;AACA,YAAA,GAAA,IAAA,UAAA,IAAA,CAAA,EAAA,GAAA,cAAA,GAAA,GAAA,CAAA,GAAA,GAAA,IAAA;AACA;AACA,SAbA;AAcA,QAAA,IAAA,CAAA,GAAA,GAAA,CAAA;;AACA,YAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,GAAA;AACA;;AACA,YAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,UAAA,eAAA,CAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,aAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,MADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;;AAKA,cAAA,MAAA,CAAA,kBAAA;AACA;AACA,WATA;AAUA;AACA;AACA,KA/JA;AAgKA,IAAA,iBAhKA,+BAgKA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,UAAA,CAAA,SAAA,EAAA,2BAAA;AACA,UAAA,WAAA,GAAA,IAAA,IAAA,EAAA;AACA,UAAA,WAAA,GAAA,WAAA,CAAA,WAAA,EAAA;AACA,UAAA,YAAA,GAAA,WAAA,CAAA,QAAA,KAAA,CAAA;;AACA,UAAA,QAAA,KAAA,SAAA,CAAA,IAAA,EAAA;AACA,aAAA,SAAA,CAAA,IAAA,GAAA,EAAA;AACA;;AACA,WAAA,SAAA,CAAA,OAAA;AACA,WAAA,SAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AACA;AACA,QAAA,SAAA,EAAA,KAAA,UAAA,CAAA,SAAA,IAAA,CAAA,CAAA,IAAA,KAAA,UAAA,CAAA,SAAA,IAAA,SAAA,GAAA,WAAA,GAAA,EAAA,GAAA,YAAA,GAAA,KAAA,UAAA,CAAA,SAFA;AAGA,QAAA,WAAA,EAAA,EAHA;AAIA,QAAA,YAAA,EAAA,EAJA;AAKA,QAAA,UAAA,EAAA,CALA;AAMA,QAAA,SAAA,EAAA,CANA;AAOA,QAAA,WAAA,EAAA,CAPA;AAQA,QAAA,cAAA,EAAA,CARA;AASA,QAAA,SAAA,EAAA,CATA;AAUA,QAAA,QAAA,EAAA,CAVA;AAWA,QAAA,SAAA,EAAA,CAXA;AAYA;AACA;AACA;AACA,QAAA,WAAA,EAAA,EAfA;AAgBA,QAAA,cAAA,EAAA,EAhBA;AAiBA,QAAA,aAAA,EAAA,EAjBA;AAkBA,QAAA,MAAA,EAAA;AAlBA,OAAA;AAoBA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,SAAA,EAAA,QADA;AAEA,QAAA,OAAA,EAAA,QAFA;AAGA,QAAA,iBAAA,EAAA,QAHA;AAIA,QAAA,gBAAA,EAAA,QAJA;AAKA,QAAA,iBAAA,EAAA,QALA;AAMA,QAAA,mBAAA,EAAA,QANA;AAOA,QAAA,gBAAA,EAAA,QAPA;AAQA,QAAA,WAAA,EAAA,QARA;AASA,QAAA,OAAA,EAAA,QATA;AAUA,QAAA,eAAA,EAAA,QAVA;AAWA,QAAA,MAAA,EAAA,QAXA;AAYA,QAAA,WAAA,EAAA,QAZA;AAaA,QAAA,cAAA,EAAA;AAbA,OAAA;AAgBA,KA7MA;AA8MA;AACA,IAAA,SA/MA,qBA+MA,GA/MA,EA+MA;AACA,WAAA,OAAA,CAAA,KAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,GAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA;AAKA,KArNA;AAsNA,IAAA,UAtNA,sBAsNA,KAtNA,EAsNA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAEA;AARA,SAAA;AAUA;;AAEA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KA/OA;AAgPA,IAAA,cAhPA,0BAgPA,KAhPA,EAgPA;AAAA;;AACA,UAAA,CAAA,GAAA,KAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,IAAA,CAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,OALA;;AAMA,UAAA,CAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,CAAA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,2BAFA;AAGA,UAAA,IAAA,EAAA,gBAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,KAAA;AACA,WALA;AAMA,UAAA,QAAA,EAAA,oBAAA,CAEA;AARA,SAAA;AAUA;;AAEA,WAAA,QAAA,GAAA,KAAA;AACA,WAAA,kBAAA;AACA,KAzQA;AA0QA;AACA,IAAA,kBA3QA,gCA2QA;AAAA;;AACA,UAAA,QAAA,GAAA,KAAA,UAAA;AACA,MAAA,QAAA,CAAA,OAAA,GAAA,KAAA,OAAA;AACA,MAAA,QAAA,CAAA,QAAA,GAAA,KAAA,QAAA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,GAAA,EAAA,6BADA;AAEA,QAAA,MAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,WAAA,SAAA,CAAA,OAAA,GAAA,IAAA;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,EAAA;AACA,cAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,WAFA;AAGA,UAAA,MAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,IAAA,CAAA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,SAAA,CAAA,IAAA,CAAA,MAAA;;AAEA,UAAA,MAAA,CAAA,SAAA,GAAA,CAAA,CAAA;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,CAAA,CAAA;AACA;AACA,OAdA,EAcA,KAdA,CAcA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,OAhBA;AAiBA,KAtSA;AAuSA;AACA,IAAA,aAxSA,2BAwSA;AACA,WAAA,UAAA,GAAA;AACA,QAAA,SAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,KAAA,OAFA;AAGA,QAAA,WAAA,EAAA,IAHA;AAIA,QAAA,OAAA,EAAA,MAAA,CAAA,KAAA,OAAA,CAJA;AAKA,QAAA,WAAA,EAAA,IALA;AAMA,QAAA,QAAA,EAAA;AANA,OAAA;AAQA,WAAA,kBAAA;AACA,KAlTA;AAmTA;AACA,IAAA,SApTA,qBAoTA,GApTA,EAoTA;AACA,UAAA,WAAA,GAAA,GAAA,CAAA,WAAA;AACA,UAAA,cAAA,GAAA,GAAA,CAAA,cAAA;AACA,UAAA,UAAA,GAAA,GAAA,CAAA,UAAA;;AACA,UAAA,WAAA,IAAA,IAAA,IAAA,cAAA,IAAA,IAAA,EAAA;AACA,YAAA,KAAA,GAAA,IAAA;AACA,QAAA,KAAA,GAAA,WAAA,GAAA,cAAA;AACA,QAAA,GAAA,CAAA,SAAA,GAAA,KAAA,GAAA,UAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA;AACA,KA7TA;AA8TA,IAAA,MA9TA,oBA8TA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,WAAA;AACA;AACA;;AACA,WAAA,MAAA,CAAA,OAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,OAAA,EAAA,oBAFA;AAGA,QAAA,IAAA,EAAA,gBAAA;AACA,cAAA,CAAA,GAAA,IAAA;AACA,cAAA,GAAA,GAAA,EAAA;AACA,cAAA,KAAA,GAAA,MAAA,CAAA,SAAA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,IAAA,GAAA,IAAA,CAAA,CAAA,CAAA;;AACA,gBAAA,IAAA,CAAA,EAAA,IAAA,IAAA,IAAA,IAAA,CAAA,EAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,kBAAA,IAAA,CAAA,OAAA,EAAA;AACA,gBAAA,CAAA,GAAA,KAAA;AACA;;AACA,cAAA,GAAA,IAAA,IAAA,CAAA,EAAA,GAAA,GAAA;AACA;AACA;;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,KAAA;;AACA,cAAA,CAAA,EAAA;AACA,gBAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,cAAA,iBAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,oBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,kBAAA,MAAA,CAAA,kBAAA;AACA;AACA,eALA;AAMA;AACA,WATA,MASA;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,4BAAA;AACA;AACA,SA7BA;AA8BA,QAAA,QAAA,EAAA,oBAAA,CACA;AA/BA,OAAA;AAiCA,KArWA;AAsWA,IAAA,mBAtWA,+BAsWA,IAtWA,EAsWA;AACA,UAAA,IAAA,KAAA,SAAA,EAAA;AACA,aAAA,eAAA;AACA,OAFA,MAEA,IAAA,IAAA,KAAA,KAAA,EAAA;AACA,aAAA,kBAAA;AACA;AACA,KA5WA;AA6WA;AACA,IAAA,kBA9WA,gCA8WA;AAAA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,MAAA,aAAA,CAAA,KAAA,UAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,CAAA,aAAA;AACA,SAFA,MAEA;AACA,cAAA,GAAA,GAAA,EAAA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA,YAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA;;AACA,UAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,EAAA,EAAA,EAAA,MAAA,CAAA,UAAA,CAAA,OAAA;AACA;AACA,OAZA;AAaA,KA9XA;AA+XA,IAAA,eA/XA,6BA+XA;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,CAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,cAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,cAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,CAAA;AACA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SARA;;AASA,YAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,GAAA,EAAA,EAAA,EAAA,KAAA,UAAA,CAAA,OAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,0BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,+BAAA;AACA,SAFA,MAEA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,SAAA,CAAA,2BAAA;AACA;AACA;AACA,KAzZA;AA0ZA,IAAA,qBA1ZA,mCA0ZA;AACA,WAAA,KAAA,CAAA,YAAA,CAAA,WAAA,CAAA,KAAA,UAAA,CAAA,OAAA,EAAA,CAAA;AACA,KA5ZA;AA6ZA,IAAA,SA7ZA,uBA6ZA;AAAA;;AACA,UAAA,IAAA,GAAA,KAAA,KAAA,CAAA,cAAA,CAAA,YAAA,EAAA;AACA,UAAA,CAAA,GAAA,IAAA;;AACA,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,gBAAA;AACA,OAFA,MAEA;AACA,YAAA,GAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,MAAA,GAAA,IAAA,CAAA,MAAA;;AACA,cAAA,MAAA,IAAA,CAAA,EAAA;AACA,YAAA,CAAA,GAAA,KAAA;AACA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,EAAA,GAAA,GAAA;AACA,SANA;;AAOA,YAAA,CAAA,EAAA;AACA,UAAA,aAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,gBAAA,OAAA,EAAA,SADA;AAEA,gBAAA,QAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA;;AAKA,cAAA,MAAA,CAAA,kBAAA;AACA;AACA,WATA;AAUA,SAXA,MAWA;AACA,eAAA,SAAA,CAAA,mBAAA;AACA;AACA;AACA,KA1bA;AA2bA,IAAA,OA3bA,qBA2bA;AACA,UAAA,GAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,GAAA,CAAA,kBAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA,KAhcA;AAicA,IAAA,QAjcA,sBAicA;AACA,UAAA,GAAA,GAAA,KAAA,YAAA,CAAA,KAAA,YAAA,EAAA,IAAA;;AACA,UAAA,GAAA,EAAA;AACA,gBAAA,KAAA,YAAA;AACA,eAAA,CAAA;AACA,iBAAA,oBAAA;AACA;;AACA,eAAA,CAAA;AACA,iBAAA,kBAAA;AACA;;AACA,eAAA,CAAA;AACA,iBAAA,mBAAA;AACA;;AACA,eAAA,CAAA;AACA,iBAAA,sBAAA;AACA;;AACA,eAAA,EAAA;AACA,iBAAA,kBAAA;AACA;AAfA;AAiBA;AACA,KAtdA;AAudA,IAAA,oBAvdA,kCAudA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,gBAAA;;AACA,UAAA,MAAA,GAAA,oBAAA,CAAA,IAAA,EAAA,GAAA,CAAA;;AACA,UAAA,MAAA,EAAA;AAAA;AACA,aAAA,SAAA,CAAA,MAAA;AACA,OAFA,MAEA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,GAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA;AACA,KAjeA;AAkeA,IAAA,kBAleA,gCAkeA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,cAAA,EAAA,qBAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,cAAA;;AACA,UAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,QAAA;AACA,OANA,CAOA;AACA;AACA;;;AACA,MAAA,IAAA,CAAA,UAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,UAAA,SAAA,GAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,UAAA,GAAA,CAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AAGA,KAlfA;AAmfA;AACA,IAAA,iBApfA,6BAofA,IApfA,EAofA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,QAAA,CADA,CACA;;AACA,UAAA,UAAA,GAAA,IAAA,CAAA,UAAA,CAFA,CAEA;;AACA,UAAA,SAAA,GAAA,IAAA,CAAA,SAAA,CAHA,CAGA;;AACA,UAAA,CAAA,YAAA,CAAA,QAAA,CAAA,IAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,QAAA,CAAA,QAAA,CAAA,EAAA;AACA;AACA;AACA;AACA;AACA;AACA,YAAA,SAAA,EAAA;AACA,cAAA,SAAA,IAAA,IAAA,IAAA,SAAA,GAAA,aAAA,EAAA;AACA,iBAAA,SAAA,CACA,yBACA,SADA,GAEA,YAHA;AAKA;AACA;AACA;AACA,KAxgBA;AAygBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA,IAAA,YA9hBA,wBA8hBA,GA9hBA,EA8hBA;AACA,MAAA,GAAA,CAAA,eAAA,GAAA,sBAAA,CAAA,GAAA,CAAA;AACA,MAAA,GAAA,CAAA,iBAAA,GAAA,uBAAA,CAAA,GAAA,CAAA;;AACA,UAAA,GAAA,CAAA,eAAA,IAAA,CAAA,IAAA,GAAA,CAAA,KAAA,IAAA,CAAA,EAAA;AACA,YAAA,GAAA,CAAA,YAAA,GAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,iBAAA,GAAA,UAAA,CAAA,GAAA,CAAA,iBAAA,CAAA,GAAA,IAAA,CAAA,GAAA,CAAA,GAAA,CAAA,YAAA,CAAA;AACA;;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,MAAA;;AACA,YAAA,MAAA,CAAA,OAAA,CAAA,IAAA,KAAA,CAAA,CAAA,EAAA;AACA,UAAA,GAAA,CAAA,MAAA,IAAA,gBAAA,GAAA,CAAA,YAAA,GAAA,IAAA;AACA;AACA;;AACA,UAAA,GAAA,CAAA,WAAA,IAAA,GAAA,CAAA,cAAA,EAAA;AACA,QAAA,GAAA,CAAA,YAAA,GAAA,sBAAA,CAAA,GAAA,CAAA;AACA,QAAA,GAAA,CAAA,SAAA,GAAA,8BAAA,CAAA,GAAA,CAAA;AACA;;AACA,MAAA,GAAA,CAAA,kBAAA,GAAA,4BAAA,CAAA,GAAA,CAAA;AACA,KA/iBA;AAgjBA,IAAA,kBAhjBA,gCAgjBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,YAAA;;AACA,UAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,QAAA;AACA;;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,UAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,UAAA,SAAA,GAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,UAAA,GAAA,CAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,KA5jBA;AA6jBA;AACA,IAAA,mBA9jBA,iCA8jBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,eAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAA,CAAA,UAAA,EAAA,iCAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,eAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA,CAFA,CAGA;AACA;;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,UAAA,GAAA,CAAA,GAAA,CAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,WAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CATA,CAUA;;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CAXA,CAYA;AACA,OAbA,MAaA,IAAA,GAAA,IAAA,IAAA,CAAA,eAAA,EAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CADA,CAEA;AACA;AACA;AACA,OAtBA,CAuBA;;AACA,KAtlBA;AAulBA;AACA,IAAA,sBAxlBA,oCAwlBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,kBAAA;;AACA,UAAA,GAAA,IAAA,IAAA,CAAA,kBAAA,EAAA;AACA,QAAA,GAAA,GAAA,UAAA,CAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,aAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,WAAA,GAAA,CAAA,GAAA,IAAA,CAAA,cAAA,GAAA,CAAA,GAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,YAAA,SAAA,GAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,UAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAPA,CAQA;AACA;;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,eAAA,CAAA,IAAA,CAAA,CAVA,CAWA;AACA,OAZA,MAYA,IAAA,GAAA,IAAA,IAAA,CAAA,kBAAA,EAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,GAAA,CADA,CAEA;AACA;;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,eAAA,CAAA,IAAA,CAAA,CAJA,CAKA;AACA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,WAAA,EAAA,4BAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,cAAA,EAAA,iBAAA,EAvBA,CAwBA;AACA,KAjnBA;AAknBA;AACA,IAAA,WAnnBA,uBAmnBA,IAnnBA,EAmnBA;AACA,MAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAA,iBAAA;AAEA,QAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,aAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAA,OAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,gBAAA;AACA,QAAA,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAA,iBAAA;AACA,QAAA,IAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,mBAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,IAAA,CAAA,gBAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,IAAA,CAAA,WAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,gBAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,gBAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAA,eAAA;AAEA,QAAA,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAA,eAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAA,eAAA;AACA,QAAA,IAAA,CAAA,kBAAA,GAAA,IAAA,CAAA,cAAA;AACA,QAAA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAA,gBAAA;AAEA,QAAA,IAAA,CAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,QAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,KAAA,GAAA,oBAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,CAAA,IAAA,GAAA,mBAAA,CAAA,IAAA,CAAA;AACA,YAAA,CAAA,IAAA,CAAA,MAAA,EACA,IAAA,CAAA,MAAA,GAAA,EAAA;AACA,YAAA,CAAA,IAAA,CAAA,EAAA,EACA,IAAA,CAAA,EAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,iBAAA,GAAA,WAAA,CAAA,IAAA,CAAA,iBAAA,CAAA;AACA,QAAA,IAAA,CAAA,mBAAA,GAAA,WAAA,CAAA,IAAA,CAAA,mBAAA,CAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,WAAA,CAAA,IAAA,CAAA,gBAAA,CAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,WAAA,CAAA,IAAA,CAAA,cAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,IAAA,CAAA,WAAA,CAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,WAAA,CAAA,IAAA,CAAA,WAAA,CAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,WAAA,CAAA,IAAA,CAAA,eAAA,CAAA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,WAAA,CAAA,IAAA,CAAA,YAAA,CAAA;;AACA,YAAA,CAAA,IAAA,CAAA,OAAA,IAAA,IAAA,IAAA,IAAA,CAAA,OAAA,IAAA,CAAA,KAAA,IAAA,CAAA,KAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,YAAA,IAAA,CAAA,OAAA,IAAA,IAAA,CAAA,SAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,SAAA,GAAA,eAAA,CAAA,IAAA,CAAA;AACA;AACA,OA1CA;AA2CA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,EAAA,gBAAA;AACA,KAhqBA;AAiqBA,IAAA,SAjqBA,uBAiqBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,UAAA;AACA,MAAA,IAAA,CAAA,MAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KAtqBA;AAuqBA,IAAA,cAvqBA,4BAuqBA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,UAAA,GAAA,GAAA,KAAA,eAAA;AACA,MAAA,IAAA,CAAA,WAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KA5qBA;AA6qBA;AACA,IAAA,UA9qBA,wBA8qBA;AACA,UAAA,GAAA,GAAA,KAAA,WAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,MAAA,IAAA,CAAA,WAAA,GAAA,GAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,WAAA,EAAA,4BAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,cAAA,EAAA,iBAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,eAAA,CAAA,IAAA,CAAA,CANA,CAOA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,SAAA,EAAA,YAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KAxrBA;AAyrBA,IAAA,WAzrBA,yBAyrBA;AACA,UAAA,GAAA,GAAA,KAAA,YAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,MAAA,IAAA,CAAA,cAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KA9rBA;AA+rBA,IAAA,UA/rBA,wBA+rBA;AACA,UAAA,GAAA,GAAA,KAAA,WAAA;AACA,UAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,KAAA,SAAA,CAAA;AACA,MAAA,IAAA,CAAA,aAAA,GAAA,GAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,KApsBA;AAqsBA,IAAA,UArsBA,sBAqsBA,MArsBA,EAqsBA;AACA,WAAA,OAAA,GAAA,EAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA;AACA,UAAA,WAAA,EAAA,QADA;AAEA,UAAA,YAAA,EAAA,QAFA;AAGA,UAAA,UAAA,EAAA,QAHA;AAIA,UAAA,WAAA,EAAA,QAJA;AAKA,UAAA,cAAA,EAAA,QALA;AAMA,UAAA,WAAA,EAAA,QANA;AAOA,UAAA,QAAA,EAAA,QAPA;AAQA,UAAA,cAAA,EAAA,QARA;AASA,UAAA,aAAA,EAAA,QATA;AAUA,UAAA,MAAA,EAAA;AAVA,SAAA;AAYA;AACA,KArtBA;AAstBA;AACA,IAAA,UAvtBA,sBAutBA,GAvtBA,EAutBA,KAvtBA,EAutBA,OAvtBA,EAutBA,GAvtBA,EAutBA;AACA,WAAA,gBAAA,GAAA,GAAA,CAAA,YAAA;AACA,WAAA,eAAA,GAAA,GAAA,CAAA,WAAA;AACA,WAAA,cAAA,GAAA,GAAA,CAAA,UAAA,IAAA,IAAA,IAAA,GAAA,CAAA,UAAA,KAAA,CAAA,GAAA,IAAA,GAAA,GAAA,CAAA,UAAA;AACA,WAAA,YAAA,GAAA,GAAA,CAAA,cAAA;AACA,WAAA,WAAA,GAAA,GAAA,CAAA,aAAA;AACA,WAAA,eAAA,GAAA,GAAA,CAAA,WAAA;AACA,WAAA,kBAAA,GAAA,GAAA,CAAA,cAAA;AACA,WAAA,WAAA,GAAA,GAAA,CAAA,WAAA;AACA,WAAA,YAAA,GAAA,GAAA,CAAA,QAAA;AACA,WAAA,UAAA,GAAA,GAAA,CAAA,MAAA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,WAAA,YAAA,GAAA,OAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,CAAA,CAAA,KAAA,CAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAIA,KAzuBA;AA0uBA;AACA,IAAA,QA3uBA,oBA2uBA,IA3uBA,EA2uBA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,YAAA;AACA,UAAA,GAAA,GAAA,EAAA;;AACA,UAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAA,KAAA,GAAA,CAAA;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OAHA,MAGA,IAAA,KAAA,GAAA,CAAA,CAAA,IAAA,OAAA,KAAA,EAAA,EAAA;AACA;AACA,YAAA,KAAA,IAAA,IAAA,CAAA,QAAA,GAAA,CAAA,IAAA,KAAA,IAAA,IAAA,CAAA,SAAA,GAAA,CAAA,EAAA;AACA,UAAA,KAAA,GAAA,CAAA;AACA,SAFA,MAEA;AACA,UAAA,KAAA;AACA;;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OARA,MAQA;AACA,QAAA,OAAA,IAAA,CAAA;AACA;;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,MAAA,GAAA,GAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,KAAA,CAAA;;AACA,UAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,gBAAA,GAAA,GAAA,CAAA,YAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,GAAA,CAAA,WAAA;AACA,QAAA,IAAA,CAAA,cAAA,GAAA,GAAA,CAAA,UAAA,IAAA,IAAA,IAAA,GAAA,CAAA,UAAA,KAAA,CAAA,GAAA,IAAA,GAAA,GAAA,CAAA,UAAA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,GAAA,CAAA,cAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,aAAA;AACA,QAAA,IAAA,CAAA,eAAA,GAAA,GAAA,CAAA,WAAA;AACA,QAAA,IAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,cAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,WAAA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,GAAA,CAAA,QAAA;AACA,QAAA,IAAA,CAAA,UAAA,GAAA,GAAA,CAAA,MAAA;AACA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA,KA/wBA;AAgxBA;AACA,IAAA,YAjxBA,wBAixBA,MAjxBA,EAixBA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,cAAA,MAAA;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,aAAA;AACA,UAAA,IAAA,GAAA,KAAA,eAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,cAAA;AACA,UAAA,IAAA,GAAA,KAAA,gBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,YAAA;AACA,UAAA,IAAA,GAAA,KAAA,cAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,aAAA;AACA,UAAA,IAAA,GAAA,KAAA,eAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,gBAAA;AACA,UAAA,IAAA,GAAA,KAAA,kBAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,GAAA,GAAA,aAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA;AACA;;AACA,aAAA,EAAA;AACA,UAAA,GAAA,GAAA,UAAA;AACA,UAAA,IAAA,GAAA,KAAA,YAAA;AACA;;AACA,aAAA,EAAA;AACA,UAAA,GAAA,GAAA,gBAAA;AACA,UAAA,IAAA,GAAA,KAAA,YAAA;AACA;;AACA,aAAA,EAAA;AACA,UAAA,GAAA,GAAA,eAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA;AACA;;AACA,aAAA,EAAA;AACA,UAAA,GAAA,GAAA,QAAA;AACA,UAAA,IAAA,GAAA,KAAA,UAAA;AACA;AAxCA;;AA0CA,aAAA;AAAA,QAAA,GAAA,EAAA,GAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA;AACA,KA/zBA;AAg0BA,IAAA,IAh0BA,kBAg0BA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,YAAA;;AACA,UAAA,KAAA,KAAA,CAAA,CAAA,IAAA,OAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAA,KAAA,GAAA,CAAA;AACA,QAAA,OAAA,GAAA,CAAA;AACA,QAAA,IAAA,CAAA,SAAA,GAAA,KAAA;AACA,QAAA,IAAA,CAAA,YAAA,GAAA,OAAA;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,GAAA,GAAA,KAAA,GAAA,OAAA,EAAA,KAAA;AACA,SAFA,EAEA,GAFA,CAAA;AAGA,OARA,MAQA;AACA,QAAA,IAAA,CAAA,QAAA;AACA,QAAA,IAAA,CAAA,SAAA;AACA,QAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA;AACA,KAj1BA;AAk1BA,IAAA,QAl1BA,oBAk1BA,KAl1BA,EAk1BA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,EAAA;;AACA,UAAA,KAAA,CAAA,MAAA,GAAA,GAAA,EAAA;AACA,eAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,GAAA,IAAA,KAAA;AACA;;AACA,aAAA,KAAA;AACA,KAx1BA;AAy1BA,IAAA,mBAz1BA,iCAy1BA,CAEA,CA31BA;AA41BA,IAAA,iBA51BA,6BA41BA,IA51BA,EA41BA;AACA,WAAA,SAAA,CACA,IAAA,CAAA,IAAA,GAAA,gCADA;AAGA,KAh2BA;AAi2BA,IAAA,cAj2BA,0BAi2BA,KAj2BA,EAi2BA,IAj2BA,EAi2BA;AACA,WAAA,QAAA,CAAA,IAAA,CAAA;AACA,QAAA,OAAA,EAAA,IAAA,CAAA,IAAA,GAAA;AADA,OAAA;AAGA;AAr2BA,qFAs2BA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,MAAA;AACA,GAx2BA,qEA02BA,IA12BA,EA02BA;AAAA;;AACA,QAAA,CAAA,IAAA,EAAA;AACA,WAAA,OAAA,CAAA,KAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,YAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA;AAKA;AACA;;AACA,QAAA,QAAA,GAAA,IAAA,CAAA,IAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CATA,CASA;;AACA,QAAA,cAAA,GAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAVA,CAUA;;AACA,QAAA,UAAA,GAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA,QAAA,GAAA,CAAA,EAAA,cAAA,CAAA,CAXA,CAWA;;AACA,QAAA,SAAA,UAAA,IAAA,UAAA,UAAA,EAAA;AACA,WAAA,OAAA,CAAA,KAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,IAAA,CAAA,IAAA,GAAA,gCAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA;AAKA;AACA;;AACA,QAAA,KAAA,GAAA,EAAA;AACA,QAAA,KAAA,GAAA;AAAA,MAAA,IAAA,EAAA;AAAA,KAAA;AACA,QAAA,IAAA,GAAA,IAAA;AACA,IAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,IAAA,KAAA,CAAA,OAAA,CAAA;AACA,MAAA,GAAA,EAAA,+BADA;AAEA,MAAA,MAAA,EAAA,MAFA;AAGA,MAAA,IAAA,EAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,KAAA,EAAA,KAAA;AAHA,KAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,KAAA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,UAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,GADA;AAEA,UAAA,QAAA,EAAA,CAFA;AAGA,UAAA,QAAA,EAAA;AAHA,SAAA;AAMA,QAAA,IAAA,CAAA,IAAA,GAAA,KAAA;AACA;;AACA,MAAA,MAAA,CAAA,kBAAA;AACA,KAhBA,EAgBA,KAhBA,CAgBA,UAAA,GAAA,EAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,KAAA;AACA,MAAA,IAAA,CAAA,IAAA,GAAA,KAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,KApBA;AAqBA,WAAA,KAAA;AACA,GAx5BA,qEA25BA;AAAA;;AACA,QAAA,GAAA,GAAA;AACA,MAAA,GAAA,EAAA,sCADA;AAEA,MAAA,MAAA,EAAA,KAFA;AAGA,MAAA,YAAA,EAAA;AAHA,KAAA;AAKA,IAAA,KAAA,CAAA,IAAA,CAAA,GAAA,EACA,IADA,CACA,UAAA,GAAA,EAAA;AACA,MAAA,OAAA,CAAA,QAAA,GAAA,KAAA;AACA,UAAA,OAAA,GAAA,GAAA;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AACA,UAAA,QAAA,GAAA,eAAA;;AACA,UAAA,cAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA,EAAA;AACA;AACA,YAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,GAAA,CAAA;AACA,QAAA,KAAA,CAAA,QAAA,GAAA,QAAA;AACA,QAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,QAAA,KAAA,CAAA,IAAA,GAAA,GAAA,CAAA,eAAA,CAAA,IAAA,CAAA;AACA,QAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,KAAA;AACA,QAAA,GAAA,CAAA,eAAA,CAAA,KAAA,CAAA,IAAA,EARA,CAQA;;AACA,QAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,OAVA,MAUA;AACA;AACA,QAAA,SAAA,CAAA,UAAA,CAAA,IAAA,EAAA,QAAA;AACA;AACA,KApBA;AAqBA,GAt7BA,YAjLA;AAymCA,EAAA,OAzmCA,qBAymCA;AACA,SAAA,OAAA,GAAA,SAAA,CAAA,OAAA;AACA,SAAA,SAAA,CAAA,OAAA,GAAA,KAAA,SAAA,CAAA,UAAA;AACA,SAAA,SAAA,GAAA,MAAA,CAAA,UAAA,CAAA;AACA,SAAA,YAAA,GAAA,MAAA,CAAA,aAAA,CAAA;AACA,SAAA,UAAA,CAAA,WAAA,GAAA,KAAA,YAAA,CAAA,CAAA,EAAA,QAAA;AACA,SAAA,UAAA,CAAA,QAAA,GAAA,KAAA,SAAA,CAAA,CAAA,EAAA,QAAA;AACA,QAAA,IAAA,GAAA,IAAA;AACA,IAAA,iBAAA,GAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,MAAA,IAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,SAAA;;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,CAAA,UAAA,IAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,MAAA,eAAA,CAAA;AAAA,QAAA,OAAA,EAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA;AAAA,OAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,QAAA,IAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,IAAA,CAAA,WAAA;AACA,OAHA,EALA,CASA;AACA;AACA,KAXA;AAYA;AA7nCA,CAAA", "sourcesContent": ["<style lang=\"less\">\r\n    .mytable .ivu-table-cell{\r\n        padding-left: 5px;\r\n        padding-right: 5px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: normal;\r\n        word-break: break-all;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .accountEs .filter-divider {\r\n        margin: 0px;\r\n        text-align: center;\r\n    }\r\n    .accountEs .header-bar-show {\r\n        max-height: 300px;\r\n        padding-top: 14px;\r\n        overflow: inherit;\r\n        border-bottom: 1px solid #e8eaec;\r\n    }\r\n    .accountEs .header-bar-hide {\r\n        max-height: 0;\r\n        padding-top: 0;\r\n        overflow: hidden;\r\n        border-bottom: 0;\r\n    }\r\n\r\n\r\n    .mytable .myspan{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block\r\n    }\r\n    .mytable .errorStle{\r\n        width: 100%;\r\n        height: 20px;\r\n        display:block;\r\n        color:red;\r\n    }\r\n</style>\r\n<template>\r\n    <div>\r\n        <div class=\"accountEs\">\r\n            <Row :class=\"filterColl?'header-bar-show':'header-bar-hide'\">\r\n                <Form ref=\"accountEsForm\" :model=\"accountObj\" :label-width=\"120\" inline>\r\n                 <!-- @on-change='accountnoChange' -->\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"选择期号：\" prop=\"accountno\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.accountno\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in dateList\" :value=\"item.code\" :key=\"item.code\">{{ item.name }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"关键字:\" prop=\"projectName\" class=\"form-line-height\">\r\n                                <cl-input v-model=\"accountObj.coalUseBody\" placeholder=\"用能主体关键字模糊查询\" :style=\"formItemWidth\" />\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"用煤类型:\" prop=\"coalType\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.coalType\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in coalTypes\" :value=\"item.typeCode\" :key=\"item.typeCode\">{{ item.typeName }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"用途:\" prop=\"coalUseType\" class=\"form-line-height\">\r\n                                <Select clearable v-model=\"accountObj.coalUseType\" :style=\"formItemWidth\">\r\n                                    <Option v-for=\"item in coalUseTypes\" :value=\"item.typeCode\" :key=\"item.typeCode\">{{ item.typeName }}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <Row>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属分公司：\" prop=\"company\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.company\" @on-change=\"selectChange(accountObj.company)\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\" v-if=\"companies.length != 1\">全部</Option>\r\n                                    <Option v-for=\"item in companies\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                        <Col span=\"6\">\r\n                            <FormItem label=\"所属部门：\" prop=\"countryName\" v-if=\"isAdmin == true\" class=\"form-line-height\">\r\n                                <Input :clearable=true icon=\"ios-archive\" v-model=\"accountObj.countryName\"\r\n                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly :style=\"formItemWidth\"/>\r\n                            </FormItem>\r\n                            <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\" class=\"form-line-height\">\r\n                                <Select v-model=\"accountObj.country\" :style=\"formItemWidth\">\r\n                                    <Option value=\"-1\">全部</Option>\r\n                                    <Option v-for=\"item in departments\" :value=\"item.id\" :key=\"item.id\">{{item.name}}</Option>\r\n                                </Select>\r\n                            </FormItem>\r\n                        </Col>\r\n                    </Row>\r\n                    <div  align=\"right\">\r\n                        <Button type=\"success\" icon=\"ios-search\" @click=\"searchList\">搜索</Button>\r\n                        <Button type=\"info\" icon=\"ios-redo\" @click=\"onResetHandle()\">重置</Button>\r\n                    </div>\r\n                </Form>\r\n            </Row>\r\n            <div class=\"filter-divider\">\r\n                <icon :type=\"filterColl?'md-arrow-dropup':'md-arrow-dropdown'\" size=\"20\"\r\n                      @click=\"filterColl=!filterColl\" :color=\"filterColl?'#000':'#1ab394'\"></icon>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <div>\r\n                <Row>\r\n                    <Col span=\"12\">\r\n                        <Page size=\"small\" :total=\"pageTotal\" :current=\"pageNum\" :page-size=\"pageSize\" show-elevator show-sizer show-total\r\n                              placement=\"top\" @on-change=\"handlePage\" @on-page-size-change='handlePageSize'></Page>\r\n                    </Col>\r\n                    <Col span=\"12\">\r\n                        <div align=\"right\" class=\"account\">\r\n                            <Button type=\"primary\" @click=\"addNewCoalAccount\">新增</Button>\r\n                            <Button type=\"success\" @click=\"preserve\">保存</Button>\r\n                            <Button type=\"error\" @click=\"remove\">删除</Button>\r\n                            <Dropdown trigger=\"click\" @on-click=\"openAddBillPerModal\">\r\n                                <Button type='info' style=\"margin-left: 5px\">加入归集单\r\n                                    <Icon type='ios-arrow-down'></Icon>\r\n                                </Button>\r\n                                <DropdownMenu slot='list'>\r\n                                    <DropdownItem name=\"current\">已选择台账</DropdownItem>\r\n                                    <DropdownItem name=\"all\">全部台账</DropdownItem>\r\n                                </DropdownMenu>\r\n                            </Dropdown>\r\n                            <Button type=\"primary\" @click=\"againJoin\">重新加入归集单</Button>\r\n                            <Button type=\"primary\" @click=\"loadTemplate\">导入模板下载</Button>\r\n                            <Upload style=\"float:right;\" :on-format-error=\"handleFormatError\"\r\n                                    :before-upload='onExcelUpload' :on-progress=\"handleProgress\"\r\n                                    :on-success=\"handleUploadSuccess\" :max-size=\"10240\" action=\"_blank\"\r\n                                    accept=\".csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\r\n                                    :format=\"['xls','xlsx']\">\r\n                                <Button icon='ios-cloud-upload'>导入</Button>\r\n                            </Upload>\r\n                        </div>\r\n                    </Col>\r\n                </Row>\r\n            </div>\r\n            <Table ref=\"accountEsTable\"\r\n                   border\r\n                   :columns=\"tbAccount.tailColumn\"\r\n                   :data=\"tbAccount.data\"\r\n                   :key=\"tbAccount.dispKey\"\r\n                   class=\"mytable\">\r\n                <!--备注-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"remark\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=500 v-model=\"editremark\" :ref=\"'remark'+index+15\" type=\"text\" @on-blur=\"setremark\"\r\n                               v-if=\"editIndex === index && columnsIndex === 15\"/>\r\n                        <Tooltip placement=\"left\" max-width=\"600\" v-else>\r\n                            <span :class=\"myStyle[index].remark\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,15,'remark')\">{{ ellipsis(row.remark) }}</span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                    <div v-else>\r\n                        <Tooltip placement=\"bottom\" max-width=\"200\">\r\n                            <span>{{ ellipsis(row.remark)}}</span>\r\n                            <div slot=\"content\">\r\n                                {{ row.remark }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--用能主体--><!-- {{ ellipsis(row.coalUseBody) }} -->\r\n                <template slot-scope=\"{ row, index }\" slot=\"coalUseBody\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :maxlength=100 v-model=\"editCoalUseBody\" :ref=\"'coalUseBody'+index+2\" type=\"text\" max=\"\" @on-blur=\"setCoalUseBody\"\r\n                               v-if=\"editIndex === index && columnsIndex === 2\"/>\r\n                        <Tooltip placement=\"right\" max-width=\"200\" v-else>\r\n                            <span :class=\"myStyle[index].coalUseBody\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,2,'coalUseBody')\">\r\n\r\n                                {{ ellipsis(row.coalUseBody) }}\r\n                            </span>\r\n                            <div slot=\"content\">\r\n                                {{ row.coalUseBody }}\r\n                            </div>\r\n                        </Tooltip>\r\n                    </div>\r\n                </template>\r\n                <!--费用发生日-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"feeStartDate\">\r\n                    <div v-if=\"row.total == null\">\r\n                        <Input :ref=\"'feeStartDate'+index+3\" type=\"text\" v-model=\"editFeeStartDate\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 3\" />\r\n                        <span :class=\"myStyle[index].feeStartDate\" style=\"display: inline-block; width: 90px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,3,'feeStartDate')\" v-else>{{ row.feeStartDate }}</span>\r\n                    </div>\r\n                    <div v-else>\r\n                        <span>{{ row.feeStartDate }}</span>\r\n                    </div>\r\n                </template>\r\n                <!--煤炭数量-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"coalAmount\">\r\n                    <Input :ref=\"'coalAmount'+index+4\" type=\"text\" v-model=\"editCoalAmount\" @on-change=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 4\" />\r\n                    <span :class=\"myStyle[index].coalAmount\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,4,'coalAmount')\" v-else>{{ row.coalAmount }}</span>\r\n                </template>\r\n                <!--普票含税金额-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"ticketMoney\">\r\n                    <Input :ref=\"'ticketMoney'+index+6\" type=\"text\" v-model=\"editTicketMoney\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 6\"/>\r\n                    <span :class=\"myStyle[index].ticketMoney\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,6,'ticketMoney')\" v-else>{{ row.ticketMoney }}</span>\r\n                </template>\r\n                <!--专票含税金额-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"taxTicketMoney\">\r\n                    <Input :ref=\"'taxTicketMoney'+index+7\" type=\"text\" v-model=\"editTaxTicketMoney\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 7\" />\r\n                    <span :class=\"myStyle[index].taxTicketMoney\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,7,'taxTicketMoney')\" v-else>{{ row.taxTicketMoney }}</span>\r\n                </template>\r\n                <!--专票税率-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"taxRateShow\">\r\n\r\n                    <div v-if=\"row.total == null\">\r\n                    <Select :ref=\"'taxRateShow'+index+8\" type=\"text\" v-model=\"editTaxRate\" @on-change=\"settaxrate\"\r\n                            v-if=\"editIndex === index && columnsIndex === 8\" transfer=\"true\">\r\n                        <Option value=\"1\">1</Option>\r\n                        <Option value=\"3\">3</Option>\r\n                        <Option value=\"6\">6</Option>\r\n                        <Option selected value=\"13\">13</Option>\r\n                        <Option value=\"16\">16</Option>\r\n                        <Option value=\"17\">17</Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].taxRateShow\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,8,'taxRateShow')\"\r\n                    v-else>{{ row.taxRateShow }}</span>\r\n                </div>\r\n                    <div v-else>\r\n                        <span>{{ row.taxRateShow }}</span>\r\n                    </div>\r\n                </template>\r\n                <!--其他费用-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"otherFee\">\r\n                    <Input :ref=\"'otherFee'+index+10\" type=\"text\" v-model=\"editOtherFee\" @on-blur=\"validate\"\r\n                           v-if=\"editIndex === index && columnsIndex === 10\" />\r\n                    <span :class=\"myStyle[index].otherFee\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,10,'otherFee')\" v-else>{{ row.otherFee }}</span>\r\n                </template>\r\n                <!--类型-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"coalImportType\">\r\n                    <Select :ref=\"'coalImportType'+index+12\" type=\"text\" v-model=\"editCoalType\" @on-change=\"setCoalType\"\r\n                            v-if=\"editIndex === index && columnsIndex === 12\" transfer=\"true\">\r\n                        <Option value=\"煤炭\" label=\"煤炭\"></Option>\r\n                        <Option value=\"焦炭\" label=\"焦炭\"></Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].coalImportType\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,12,'coalImportType')\" v-else>{{ row.coalImportType }}</span>\r\n                </template>\r\n                <!--用途-->\r\n                <template slot-scope=\"{ row, index }\" slot=\"coalImportUse\"\r\n                          v-if=\"row.total == null\">\r\n                    <Select :ref=\"'coalImportUse'+index+13\" type=\"text\" v-model=\"editCoalUse\" @on-change=\"setCoalUse\"\r\n                            v-if=\"editIndex === index && columnsIndex === 13\" transfer=\"true\">\r\n                        <Option value=\"发电用煤\" label=\"发电用煤\"></Option>\r\n                        <Option value=\"取暖用煤\" label=\"取暖用煤\"></Option>\r\n                        <Option value=\"其他\" label=\"其他\"></Option>\r\n                    </Select>\r\n                    <span :class=\"myStyle[index].coalImportUse\" style=\"display: inline-block; width: 60px; height: 30px; line-height: 30px;\" @click=\"selectCall(row,index,13,'coalImportUse')\" v-else>{{ row.coalImportUse }}</span>\r\n                </template>\r\n            </Table>\r\n            <Spin size=\"large\" fix v-if=\"spinShow\"></Spin>\r\n        </div>\r\n        <div>\r\n            <add-bill-per ref=\"addBillPer\"\r\n                          v-on:refreshList=\"refresh\"></add-bill-per >\r\n            <completed-pre-modal ref=\"completedPre\" v-on:refreshList=\"refresh\"></completed-pre-modal>\r\n            <country-modal ref=\"countryModal\" v-on:getDataFromModal=\"getDataFromModal\"></country-modal>\r\n            <upload-file-modal ref=\"uploadFileModal\" v-on:onchange=\"change\"></upload-file-modal>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n// import {\r\n//   addAmmeter,\r\n//   listElectricType,\r\n//   editAmmeter,\r\n//   editAmmeterRecord,\r\n//   updateAmmeter,\r\n//   checkProjectNameExist,\r\n//   checkAmmeterByStation,\r\n//   getClassification,\r\n//   getClassificationId,\r\n//   getUserdata,\r\n//   checkClassificationLevel,\r\n//   listElectricTypeRatio,\r\n//   checkAmmeterExist,\r\n//   getUserByUserRole,\r\n//   getCountryByUserId,\r\n//   getCountrysdata, removeAttach, attchList, getBankCard\r\n// } from '@/api/basedata/ammeter.js'\r\nimport {\r\n    _verify_StartDate,\r\n    judgeNumber,\r\n    _verify_EndDate,\r\n    _verify_PrevTotalReadings,\r\n    _verify_CurTotalReadings,\r\n    other_no_ammeteror_protocol,\r\n    self_no_ammeteror_protocol,\r\n    HFL_ammeteror,\r\n    judging_editability,\r\n    judging_editability1,\r\n    _verify_Money,\r\n    _calculateUsedReadings,\r\n    _calculateTotalReadings,\r\n    _calculateUnitPriceByUsedMoney,\r\n    _calculateAccountMoney,\r\n    _calculateQuotereadingsratio,\r\n    requiredFieldValidator,\r\n    countTaxamount1,\r\n    countTaxamount,\r\n    calculateActualMoney,\r\n    judge_negate,\r\n    judge_recovery,\r\n    judge_yb,\r\n    unitpirceMin,\r\n    unitpirceMax,\r\n    unitpirceMax1\r\n} from '@/view/account/PowerAccountController';\r\n    import {\r\n        saveCoalAccount,\r\n        removeCoalAccount,\r\n        selectCoalIds\r\n    } from '@/api/coalHeatOilAccount';\r\n    import {verification} from '@/view/account/coalAccount';\r\n    import checkResultAndResponse from \"@/view/account/check/checkResultAndResponse\";\r\n    import checkResult from \"@/view/account/check/checkResult\";\r\n    import alarmCheck from \"@/view/account/check/alarmCheck\";\r\n    import {getDates,testNumber,} from '@/view/account/powerAccountHelper';\r\n    import axios from '@/libs/api.request';\r\n    import SelectAmmeter from \"./selectAmmeter\";\r\n    import {_verify_FeeStartDate} from '@/view/account/PowerAccountEs';\r\n    import AddBillPer from \"./addCoalBillPreModal\";\r\n    import {reJoinBillpre} from '@/api/accountBillPer';\r\n    import {blist1} from \"@/libs/tools\";\r\n    import {widthstyle} from \"@/view/business/mssAccountbill/mssAccountbilldata\";\r\n    import CompletedPreModal from \"./completedPreModal\";\r\n    import indexData from '@/config/index'\r\n    import CountryModal from \"@/view/basedata/ammeter/countryModal\";\r\n    import { getUserdata, getUserByUserRole, getCountrysdata, getCountryByUserId, editAmmeter\r\n\r\n    } from '@/api/basedata/ammeter.js'\r\n    import UploadFileModal from \"@/view/account/uploadFileModal\";\r\n    let dates=getDates();\r\n    export default {\r\n        name: 'addCoalAccount',\r\n        components: {UploadFileModal, alarmCheck, checkResult, checkResultAndResponse,CompletedPreModal, SelectAmmeter,AddBillPer,CountryModal},\r\n        data() {\r\n            let photo = (h, {row, index}) => {\r\n                let that = this\r\n                let str = '上传附件'\r\n                return h(\"div\", [h(\"u\", {\r\n                    on: {\r\n                        click() {\r\n                            //打开弹出框\r\n                            // if (row.id) {\r\n                                that.uploadFile(row)\r\n                            // }\r\n                        }\r\n                    }\r\n                }, str)]);\r\n            };\r\n            return {\r\n                id2: \"\",\r\n                fileParam:{\r\n                    busiId:\"\",\r\n                    busiAlias:\"附件(协议管理)\",\r\n                    categoryCode:\"file\",\r\n                    areaCode:\"ln\"\r\n                },\r\n                submit:[],\r\n                submit2:[],\r\n                formItemWidth: widthstyle,\r\n                version:'',\r\n                dateList:dates,\r\n                filterColl: true,//搜索面板展开\r\n                editIndex: -1,//当前编辑行\r\n                columnsIndex:-1,//当前编辑列\r\n                myStyle:[],//样式\r\n                editCoalUseBody:'',\r\n                editFeeStartDate:'',\r\n                editCoalAmount:'',\r\n                editTicketMoney:'',\r\n                editTaxTicketMoney:'',\r\n                editTaxRate:'',\r\n                editOtherFee:'',\r\n                editCoalUse:'',\r\n                editCoalType:'',\r\n                spinShow:false,//遮罩\r\n                editremark:'',\r\n                companies:[],\r\n                coalTypes: [],\r\n                coalUseTypes: [],\r\n                departments:[],\r\n                isAdmin:false,\r\n                company:null,//用户默认公司\r\n                country:null,//用户默认所属部门\r\n                countryName:null,//用户默认所属部门\r\n                accountObj:{\r\n                    accountno:dates[1].code,//期号,默认当前月\r\n                    company:\"\",//分公司\r\n                    country:\"\",//所属部门\r\n                    coalUseBody: \"\",//用煤主体\r\n                    coalUseType:1,\r\n                    coalType:1,\r\n                    feeStartDate: \"\",\r\n                    countryName: \"\",\r\n\r\n                },\r\n                tbAccount: {\r\n                    dispKey: 0,\r\n                    loading: true,\r\n                    columns: [],\r\n                    tailColumn: [\r\n                        {type: 'selection', width: 60, align: 'center',},\r\n                        {\r\n                            title: \"期号\",\r\n                            key: \"accountNo\",\r\n                            align: \"center\",\r\n                            width: 90,\r\n                        },\r\n                        {\r\n                            title: \"用能主体\",\r\n                            slot: \"coalUseBody\",\r\n                            // key: \"coalUseBody\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"费用发生日\",\r\n                            slot: \"feeStartDate\",\r\n                            // key: \"feeStartDate\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"煤炭用量(t)\",\r\n                            slot: \"coalAmount\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"单价(元/吨)\",\r\n                            key: \"unitPrice\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"普票含税金额(元)\",\r\n                            slot: \"ticketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票含税金额(元)\",\r\n                            slot: \"taxTicketMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票税率（%）\",\r\n                            slot: \"taxRateShow\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"专票税额\",\r\n                            key: \"taxAmount\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"其他(元)\",\r\n                            // key: \"otherFee\",\r\n                            slot: \"otherFee\",\r\n                            align: \"center\",\r\n                            width: 80,\r\n                        },\r\n                        {\r\n                            title: \"实缴费用(元)含税\",\r\n                            key: \"paidMoney\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        {\r\n                            title: \"类型\",\r\n                            slot: \"coalImportType\",\r\n                            align: \"center\",\r\n                            width: 60,\r\n                        },\r\n                        {\r\n                            title: \"用途\",\r\n                            slot: \"coalImportUse\",\r\n                            align: \"center\",\r\n                            width: 100,\r\n                        },\r\n                        // {title: \"附件\", slot: 'file', align: \"center\", render: photo, width: 100},\r\n                        {title: \"备注\", slot: \"remark\",align: \"center\", width: 150},{\r\n                        title: '附件',\r\n                        slot: 'file',\r\n                        align: 'center',\r\n                        width: 60,\r\n                        // fixed: 'right',\r\n                        render: photo\r\n                    }\r\n                    ],\r\n                    fileColumn: [{\r\n                        title: '附件',\r\n                        slot: 'file',\r\n                        align: 'center',\r\n                        width: 60,\r\n                        // fixed: 'right',\r\n                        render: photo\r\n                    },],\r\n                    data: []\r\n                },\r\n                pageTotal: 0,\r\n                pageNum: 1,\r\n                pageSize: 10,//当前页\r\n            }\r\n        },\r\n        methods: {\r\n            uploadFile(row) {\r\n                console.log(row, \"row\");\r\n                // let id;\r\n                // if(!row.id2) {\r\n                //     editAmmeter('', 0).then(res => {\r\n                //         console.log(res, \"res\");\r\n                //         row.id2 = res.data.id;\r\n\r\n                //         this.id2 = res.data.id\r\n                //         // this.fileParam.busiId = ;\r\n                //         this.$refs.uploadFileModal.choose(row.id2 + '');\r\n                //     })\r\n                // }else {\r\n\r\n                if(row.id) {\r\n                    this.$refs.uploadFileModal.choose(row.id + '');\r\n                }else {\r\n                    this.errorTips(\"请先保存后再上传文件！\");\r\n                }\r\n                // }\r\n                // console.log(row, \"row\");\r\n            },\r\n          change() {\r\n            // this.getAccountMessages();\r\n          },\r\n            selectChange(){\r\n                let that = this;\r\n                if (that.accountObj.company != undefined) {\r\n                    if(that.accountObj.company == \"-1\"){\r\n                        that.accountObj.country = -1;\r\n                        that.accountObj.countryName = null;\r\n                    }else{\r\n                        getCountryByUserId(that.accountObj.company).then(res => {\r\n                            if(res.data.departments.length != 0){\r\n                                that.accountObj.country = res.data.departments[0].id;\r\n                                that.accountObj.countryName = res.data.departments[0].name;\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            },\r\n            //选择所属部门开始\r\n            chooseResponseCenter() {\r\n                if(this.accountObj.company == null || this.accountObj.company == \"-1\" ){\r\n                    this.$Message.info(\"请先选择分公司\");return;\r\n                }\r\n                this.$refs.countryModal.choose(this.accountObj.company);//所属部门\r\n            },\r\n            getDataFromModal(data) {\r\n                this.accountObj.country = data.id;\r\n                this.accountObj.countryName = data.name;\r\n                //选择所属部门结束\r\n            },\r\n            getUserData(){\r\n                let that = this;\r\n                getUserdata().then(res => {//当前登录用户所在公司和所属部门\r\n                    if(res.data.companies.length != 0){\r\n                        let companies = res.data.companies;\r\n                        if(res.data.companies[0].id == \"**********\"){\r\n                            companies = that.companies;\r\n                        }\r\n                        that.company = companies[0].id;\r\n                        that.accountObj.company = companies[0].id;\r\n                    }\r\n                    if(res.data.departments.length != 0){\r\n                        let departments = res.data.departments;\r\n                        if(res.data.companies[0].id == \"**********\" && that.departments.length != 0){\r\n                            departments = that.departments\r\n                        }\r\n                        that.country = departments[0].id;\r\n                        that.countryName = departments[0].name;\r\n                        that.accountObj.country = Number(departments[0].id);\r\n                        that.accountObj.countryName = departments[0].name;\r\n                    }\r\n\r\n                    that.pageNum = 1\r\n                    that.getAccountMessages();\r\n                });\r\n            },\r\n            searchList(){\r\n                if(this.accountObj.countryName == \"\"){\r\n                    this.accountObj.country = \"-1\";\r\n                }\r\n                this.pageNum = 1\r\n                this.getAccountMessages()\r\n            },\r\n            accountnoChange(){\r\n                this.searchList()\r\n            },\r\n            //点击保存\r\n            preserve() {\r\n                let dataL = this.$refs.accountEsTable.getSelection();\r\n                console.log(\"dataL\", dataL);\r\n                let b = false;\r\n                let array = [];\r\n                for (let i = 0; i < dataL.length; i ++) {\r\n                    b = true;\r\n                    array.push(dataL[i])\r\n                }\r\n                if(b){\r\n                    this.submitData(array);\r\n                }else {\r\n                    this.errorTips('没有可保存数据')\r\n                }\r\n            },\r\n            submitChange(indexList){\r\n                let data=[];\r\n                this.submit2.map((item,index)=>{\r\n                    indexList.map((item2)=>{\r\n                        if(index==item2){\r\n                            data.push(item)\r\n                        }\r\n                    })\r\n                });\r\n                this.submit=data\r\n            },\r\n\r\n            //提交数据\r\n            submitData(data){\r\n                console.log(data, \"data\");\r\n                let a = [];\r\n                let that=this;\r\n                if(data != null && data.length > 0){\r\n                    let number = 0;\r\n                    let submitData = [];\r\n                    let str = '';\r\n                    let accountno = this.accountObj.accountno;\r\n                    data.forEach(function (item) {\r\n                        // 校验数据\r\n                        let obj = verification(item);\r\n                        if (obj.result) {\r\n                            if(item.id == null){\r\n                                item.accountno = accountno\r\n                            }\r\n                            a.push(item.id);\r\n                            submitData.push(item);\r\n                            number ++;\r\n                         }else{\r\n                             str += '台账号为【' +item.id + '】的台账验证没有通过：【' + obj.str + '】；';\r\n                         }\r\n                    });\r\n                    that.ids=a;\r\n                    if(str.length > 0){\r\n                        this.errorTips(str)\r\n                    }\r\n                    if(submitData.length > 0){\r\n                        saveCoalAccount(submitData).then((res) => {\r\n                             if (res.data.code == 0) {\r\n                                this.$Message.info({\r\n                                    content: '提示：成功保存 ' + res.data.num + ' 条数据',\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                             }\r\n                        })\r\n                    }\r\n                }\r\n            },\r\n            addNewCoalAccount() {\r\n                console.log(this.accountObj.accountno, \"this.accountObj.accountno\");\r\n                const currentDate = new Date();\r\n                const currentYear = currentDate.getFullYear();\r\n                const currentMonth = currentDate.getMonth() + 1;\r\n                if (null == this.tbAccount.data) {\r\n                    this.tbAccount.data = [];\r\n                }\r\n                this.tbAccount.dispKey++;\r\n                this.tbAccount.data.unshift({\r\n                    // accountNo:dates[1].code,\r\n                    accountNo: (this.accountObj.accountno == -1 || this.accountObj.accountno == undefined) ? currentYear+\"\"+currentMonth: this.accountObj.accountno,\r\n                    coalUseBody: \"\",\r\n                    feeStartDate:\"\",\r\n                    coalAmount: 0,\r\n                    unitPrice: 0,\r\n                    ticketMoney:0,\r\n                    taxTicketMoney:0,\r\n                    taxAmount: 0,\r\n                    otherFee: 0,\r\n                    paidMoney:0,\r\n                    // taxRateShow: 1,\r\n                    // coalImportType:\"煤炭\",\r\n                    // coalImportUse:\"发电用煤\",\r\n                    taxRateShow: \"\",\r\n                    coalImportType:\"\",\r\n                    coalImportUse:\"\",\r\n                    remark:\"\",\r\n                });\r\n                this.myStyle.push({\r\n                        startdate: 'myspan',\r\n                        enddate: 'myspan',\r\n                        prevtotalreadings: 'myspan',\r\n                        curtotalreadings: 'myspan',\r\n                        transformerullage: 'myspan',\r\n                        inputtaxticketmoney: 'myspan',\r\n                        inputticketmoney: 'myspan',\r\n                        ullagemoney: 'myspan',\r\n                        taxrate: 'myspan',\r\n                        tickettaxamount: 'myspan',\r\n                        remark: 'myspan',\r\n                        ticketMoney:\"myspan\",\r\n                        taxTicketMoney:\"myspan\",\r\n\r\n                    });\r\n            },\r\n            //验证错误弹出提示框\r\n            errorTips(str){\r\n                this.$Notice.error({\r\n                    title: '提示',\r\n                    desc: str,\r\n                    duration: 10\r\n                });\r\n            },\r\n            handlePage(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data;\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageNum = value;\r\n                this.getAccountMessages();\r\n            },\r\n            handlePageSize(value) {\r\n                let b = false;\r\n                let data = this.tbAccount.data;\r\n                let array = [];\r\n                data.forEach(function (item) {\r\n                    if(item.editType == 1){\r\n                        b = true;\r\n                        array.push(item)\r\n                    }\r\n                });\r\n                if(b){\r\n                    this.$Modal.confirm({\r\n                        title: '提示',\r\n                        content: '<p>您有已编辑信息还没有保存，是否保存？</p>',\r\n                        onOk: () => {\r\n                            this.submitData(array);\r\n                        },\r\n                        onCancel: () => {\r\n\r\n                        }\r\n                    });\r\n                }\r\n\r\n                this.pageSize = value;\r\n                this.getAccountMessages();\r\n            },\r\n            //向后台请求数据\r\n            getAccountMessages() {\r\n                const postData = this.accountObj;\r\n                postData.pageNum = this.pageNum;\r\n                postData.pageSize = this.pageSize;\r\n                let req = {\r\n                    url : \"/business/coal/account/list\",\r\n                    method : \"get\",\r\n                    params : postData\r\n                };\r\n                this.tbAccount.loading = true\r\n                axios.request(req).then(res => {\r\n                    this.tbAccount.loading = false\r\n                    if (res.data) {\r\n                        let data = res.data.rows;\r\n                        data.forEach(function (item) {\r\n                            item.editType = 0;\r\n                        });\r\n                        this.tbAccount.data = data;\r\n                        this.pageTotal = res.data.total || 0;\r\n                        this.setMyStyle(this.tbAccount.data.length);\r\n\r\n                        this.editIndex = -1;\r\n                        this.columnsIndex = -1;\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err);\r\n                });\r\n            },\r\n            //重置\r\n            onResetHandle(){\r\n                this.accountObj = {\r\n                    accountno:null,\r\n                    company:this.company,\r\n                    coalUseBody:null,\r\n                    country:Number(this.country),\r\n                    coalUseType:null,\r\n                    coalType:null,\r\n                }\r\n                this.getAccountMessages()\r\n            },\r\n            //计算单价\r\n            unitPrice(row){\r\n                let ticketMoney = row.ticketMoney;\r\n                let taxTicketMoney = row.taxTicketMoney;\r\n                let coalAmount = row.coalAmount;\r\n                if(ticketMoney != null || taxTicketMoney != null){\r\n                    let total = null;\r\n                    total = ticketMoney + taxTicketMoney;\r\n                    row.unitPrice = total/coalAmount.toFixed(2);\r\n                }\r\n            },\r\n            remove(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                if(data == null || data.length === 0){\r\n                    this.errorTips(\"请选择要删除的数据\")\r\n                    return;\r\n                }\r\n                this.$Modal.confirm({\r\n                    title: '提示',\r\n                    content: '<p>是否确认删除选中信息？</p>',\r\n                    onOk: () => {\r\n                        let b = true;\r\n                        let ids = '';\r\n                        let total = this.pageTotal\r\n                        for(let i=0;i<data.length;i++){\r\n                            let item = data[i];\r\n                            if(item.id != null && item.id.length > 0){\r\n                                if(item.pabriid){\r\n                                    b = false;\r\n                                }\r\n                                ids += item.id + ',';\r\n                            }\r\n                        }\r\n                        this.pageTotal = total\r\n                        if(b){\r\n                            if(ids.length > 0){\r\n                                removeCoalAccount(ids).then((res) => {\r\n                                    if (res.data.code == 0) {\r\n                                        this.$Message.success('删除成功');\r\n                                        this.getAccountMessages();\r\n                                    }\r\n                                });\r\n                            }\r\n                        }else {\r\n                            this.errorTips('选中信息中有信息还没有跟归集单解除关联，请先解除关联')\r\n                        }\r\n                    },\r\n                    onCancel: () => {\r\n                    }\r\n                });\r\n            },\r\n            openAddBillPerModal(name) {\r\n                if (name === 'current') {\r\n                    this.selectedAccount()\r\n                } else if (name === 'all') {\r\n                    this.selectedAllAccount()\r\n                }\r\n            },\r\n            //加入归集单，全部有效台账\r\n            selectedAllAccount(){\r\n                let that = this\r\n                that.spinShow = true;\r\n                selectCoalIds(this.accountObj).then(res => {\r\n                    that.spinShow = false;\r\n                    if(res.data.length == 0){\r\n                        that.errorTips('无有效数据可加入归集单')\r\n                    }else {\r\n                        let ids = [];\r\n                        for(let i=0;i<res.data.rows.length;i++){\r\n                            let item = res.data.rows[i];\r\n                            ids.push(item.id)\r\n                        }\r\n                        that.$refs.addBillPer.initAmmeter(ids, 20,this.accountObj.country);\r\n                    }\r\n                });\r\n            },\r\n            selectedAccount(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = 1;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要加入归集单的台账')\r\n                } else {\r\n                    let ids = [];\r\n                    data.forEach(function (item) {\r\n                        if(item.status === 5){\r\n                            b = 3\r\n                        }\r\n                        if(item.status === 4){\r\n                            b=4;\r\n                        }\r\n                        ids.push(item.id)\r\n                    });\r\n                    if(b === 1){\r\n                        this.$refs.addBillPer.initAmmeter(ids,20,this.accountObj.country);\r\n                    }else if(b === 2) {\r\n                        this.errorTips('选中的台账中存在临时数据，请先保存再加入归集单！')\r\n                    }else if(b===3){\r\n                        this.errorTips('退回的台账不能加入其它归集单，请点击[重新加入归集单]按钮')\r\n                    }else if(b===4){\r\n                        this.errorTips('选择的台账有已加入归集单的台账，不能加入其他归集单')\r\n                    }\r\n                }\r\n            },\r\n            openCompletedPreModal(){\r\n                this.$refs.completedPre.initAmmeter(this.accountObj.country,2);\r\n            },\r\n            againJoin(){\r\n                let data = this.$refs.accountEsTable.getSelection();\r\n                let b = true;\r\n                if(data == null || data.length == 0){\r\n                    this.errorTips('请选择要重新加入归集单的台账')\r\n                } else {\r\n                    let ids = '';\r\n                    data.forEach(function (item) {\r\n                        let status = item.status;\r\n                        if(status != 5){\r\n                            b = false;\r\n                        }\r\n                        ids+= item.id +','\r\n                    });\r\n                    if(b){\r\n                        reJoinBillpre(ids).then((res) =>{\r\n                            if(res.data.code==0){\r\n                                this.$Message.info({\r\n                                    content:'提示：操作成功' ,\r\n                                    duration: 10,\r\n                                    closable: true\r\n                                });\r\n                                this.getAccountMessages();\r\n                            }\r\n                        })\r\n                    }else {\r\n                        this.errorTips('只有已退回的台账才能重新加入归集单')\r\n                    }\r\n                }\r\n            },\r\n            refresh(){\r\n                let obj = this\r\n                setTimeout(function () {\r\n                    obj.getAccountMessages()\r\n                },200);\r\n            },\r\n            validate(){\r\n                let val = this.enterOperate(this.columnsIndex).data;\r\n                if(val) {\r\n                    switch (this.columnsIndex) {\r\n                        case 3:\r\n                            this.validateFeeStartDate();\r\n                            break;\r\n                        case 4:\r\n                            this.validateCoalAmount();\r\n                            break;\r\n                        case 6:\r\n                            this.validateTicketMoney();\r\n                            break;\r\n                        case 7:\r\n                            this.validateTaxTicketMoney();\r\n                            break;\r\n                        case 10:\r\n                            this.validateOtherMoney();\r\n                            break;\r\n                    }\r\n                }\r\n            },\r\n            validateFeeStartDate(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editFeeStartDate;\r\n                let result = _verify_FeeStartDate(data,val);\r\n                if(result){//失败就弹出提示内容，并将数据恢复初始化\r\n                    this.errorTips(result)\r\n                }else{\r\n                    data.feeStartDate = val;\r\n                    data.editType = 1;\r\n                }\r\n            },\r\n            validateCoalAmount(){\r\n                console.log(this.editCoalAmount, \"this.editCoalAmount\");\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editCoalAmount;\r\n                if (!testNumber(val)) {\r\n                    this.errorTips('请输入数字！');\r\n                }\r\n                // if (testNumber(val) == 0) {\r\n                //     this.errorTips('请输入数字！');\r\n                // }\r\n                data.coalAmount = val;\r\n                data.editType = 1;\r\n                let unitPrice = data.paidMoney?(data.paidMoney/data.coalAmount):0;\r\n                data.unitPrice = unitPrice.toFixed(2);\r\n\r\n\r\n            },\r\n            //验证单价\r\n            validateUnitPrice(data){\r\n                let category = data.category;//电表描述类型\r\n                let ammeteruse = data.ammeteruse;//电表用途\r\n                let unitpirce  = data.unitPrice;//台账单价\r\n                if(!judge_negate(category) && !judge_recovery(ammeteruse) && judge_yb(category)){\r\n                    // if(unitPrice){\r\n                    //     if(unitPrice < unitpirceMin || unitPrice > unitpirceMax){\r\n                    //         this.errorTips('集团要求单价范围在0.3~2元，此台账单价: '+ unitPrice +' 已超过范围，请确认！')\r\n                    //     }\r\n                    // }\r\n                    if (unitpirce) {\r\n                        if (unitpirce != null && unitpirce < unitpirceMax1) {\r\n                            this.errorTips(\r\n                            \"单价范围必须大于0.1元，此台账单价: \" +\r\n                                unitpirce +\r\n                                \"不在范围内，请确认！\"\r\n                            );\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            // validateTicketMoney(){\r\n            //     let data = this.tbAccount.data[this.editIndex];\r\n            //     let val = this.editTicketMoney;\r\n            //     if (!testNumber(val)) {\r\n            //         this.errorTips('请输入数字！');\r\n            //     }\r\n            //     data.ticketMoney = val;\r\n            //     data.editType = 1;\r\n\r\n            // },\r\n            // validateTaxTicketMoney(){\r\n            //     let data = this.tbAccount.data[this.editIndex];\r\n            //     let val = this.editTaxTicketMoney;\r\n            //     if (!testNumber(val)) {\r\n            //         this.errorTips('请输入数字！');\r\n            //     }\r\n            //     data.taxTicketMoney = val;\r\n            //     data.editType = 1;\r\n            // },\r\n\r\n            //计算 用电量,总电量,单价,总费用,浮动比.\r\n            calculateAll(row) {\r\n                row.curusedreadings = _calculateUsedReadings(row);\r\n                row.totalusedreadings = _calculateTotalReadings(row);\r\n                if(row.ischangeammeter == 1 && row.isnew == 1){\r\n                    if(row.oldbillpower>0){\r\n                        row.totalusedreadings = parseFloat(row.totalusedreadings)+Math.abs(row.oldbillpower)\r\n                    }\r\n                    let remark = row.remark\r\n                    if(remark.indexOf(\"换表\") == -1){\r\n                        row.remark+= \"换表，结清原电表读数【\"+row.oldbillpower+\"】；\"\r\n                    }\r\n                }\r\n                if (row.ticketMoney || row.taxTicketMoney) {\r\n                    row.accountmoney = _calculateAccountMoney(row);\r\n                    row.unitPrice = _calculateUnitPriceByUsedMoney(row);\r\n                }\r\n                row.quotereadingsratio = _calculateQuotereadingsratio(row);\r\n            },\r\n            validateOtherMoney(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editOtherFee;\r\n                if (!testNumber(val)) {\r\n                    this.errorTips('请输入数字！');\r\n                }\r\n                data.otherFee = val;\r\n                data.editType = 1;\r\n                let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                data.paidMoney = paidMoney.toFixed(2);\r\n                let unitPrice = data.coalAmount?(data.paidMoney/data.coalAmount):0;\r\n                data.unitPrice = unitPrice.toFixed(2);\r\n            },\r\n            //验证普票\r\n            validateTicketMoney() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTicketMoney;\r\n                console.log(data.paidMoney, data.coalAmount, \"data.paidMoney, data.coalAmount\");\r\n                if (val != data.old_ticketmoney) {\r\n                    val = parseFloat(val);\r\n                    data.ticketMoney = _verify_Money(data, val);\r\n                    // data.inputticketmoney = _verify_Money(data, val)\r\n                    // data.ticketmoney = calculateActualMoney(data,val)\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                    let unitPrice = data.coalAmount?(data.paidMoney/data.coalAmount*1):0;\r\n                    console.log(unitPrice, \"unitPrice\")\r\n                    data.unitPrice = unitPrice.toFixed(2);\r\n                    // let unitpirce = data.coalAmount?(data.paidMoney/data.coalAmount*1):0;\r\n                    data.editType = 1;\r\n                    // this.calculateAll(data);\r\n                } else if (val == data.old_ticketmoney) {\r\n                    data.ticketMoney = val;\r\n                    // data.inputticketmoney = val;\r\n                    // data.ticketmoney = calculateActualMoney(data,val)\r\n                    // this.calculateAll(data);\r\n                }\r\n                // this.validateUnitPrice(data)\r\n            },\r\n            //验证专票\r\n            validateTaxTicketMoney() {\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editTaxTicketMoney;\r\n                if (val != data.old_taxticketmoney) {\r\n                    val = parseFloat(val);\r\n                    data.taxTicketMoney = _verify_Money(data, val)\r\n                    data.editType = 1;\r\n                    let paidMoney = data.ticketMoney*1+data.taxTicketMoney*1+data.otherFee*1;\r\n                     data.paidMoney = paidMoney.toFixed(2);\r\n                    let unitPrice = data.coalAmount?(data.paidMoney/data.coalAmount):0;\r\n                    data.unitPrice = unitPrice.toFixed(2);\r\n                    // data.taxticketmoney = calculateActualMoney(data,val)\r\n                    // data.taxAmount = data.taxTicketMoney*data.taxRateShow;\r\n                    data.taxAmount = countTaxamount1(data);\r\n                    // this.calculateAll(data);\r\n                } else if (val == data.old_taxticketmoney) {\r\n                    data.taxTicketMoney = val;\r\n                    // data.taxticketmoney = calculateActualMoney(data,val)\r\n                    // data.taxAmount = (data.taxTicketMoney*1)*(data.taxRateShow*1);\r\n                    data.taxAmount = countTaxamount1(data);\r\n                    // this.calculateAll(data);\r\n                }\r\n                console.log(data.taxRateShow, \".taxRateShowdata5555555555\");\r\n                console.log(data.taxTicketMoney, \".taxTicketMoney\");\r\n                // this.validateUnitPrice(data)\r\n            },\r\n            //保存可编辑表格的初始化数据\r\n            setNewField(data) {\r\n                data.forEach(function (item) {\r\n                    item.old_startdate = item.startdate;\r\n                    item.old_prevtotalreadings = item.prevtotalreadings;\r\n\r\n                    item.multtimes = item.magnification\r\n                    item.old_enddate = item.enddate;\r\n                    item.old_curtotalreadings = item.curtotalreadings;\r\n                    item.old_transformerullage = item.transformerullage;\r\n                    item.old_taxticketmoney = item.inputtaxticketmoney;\r\n                    item.old_ticketmoney = item.inputticketmoney;\r\n                    item.old_ullagemoney = item.ullagemoney;\r\n                    item.old_prevhighreadings = item.prevhighreadings;\r\n                    item.old_prevflatreadings = item.prevflatreadings;\r\n                    item.old_prevlowreadings = item.prevlowreadings;\r\n\r\n                    item.old_curhighreadings = item.curhighreadings;\r\n                    item.old_curflatreadings = item.curflatreadings;\r\n                    item.old_curlowreadings = item.curlowreadings;\r\n                    item.old_curtotalreadings = item.curtotalreadings;\r\n\r\n                    item.version = indexData.version;\r\n                    item.editType = 0;\r\n                    item.isFPG = judging_editability1(item);\r\n                    item.isWB = judging_editability(item)\r\n                    if (!item.remark)\r\n                        item.remark = '';\r\n                    if (!item.bz)\r\n                        item.bz = '';\r\n                    item.transformerullage = judgeNumber(item.transformerullage)\r\n                    item.inputtaxticketmoney = judgeNumber(item.inputtaxticketmoney)\r\n                    item.inputticketmoney = judgeNumber(item.inputticketmoney)\r\n                    item.taxTicketMoney = judgeNumber(item.taxTicketMoney)\r\n                    item.ticketMoney = judgeNumber(item.ticketMoney)\r\n                    item.ullagemoney = judgeNumber(item.ullagemoney)\r\n                    item.curusedreadings = judgeNumber(item.curusedreadings)\r\n                    item.accountmoney = judgeNumber(item.accountmoney)\r\n                    if ((item.taxrate == null || item.taxrate == 0) && item.total == null) {\r\n                        item.taxrate = '13'\r\n                    }\r\n                    if (item.taxrate && item.taxAmount == null) {\r\n                        item.taxAmount = countTaxamount1(item);\r\n                    }\r\n                })\r\n                console.log(data, \"data5555555555\");\r\n            },\r\n            setremark(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editremark;\r\n                data.remark = val;\r\n                data.editType = 1;\r\n            },\r\n            setCoalUseBody(){\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                let val = this.editCoalUseBody;\r\n                data.coalUseBody = val;\r\n                data.editType = 1;\r\n            },\r\n            //专票税额\r\n            settaxrate() {\r\n                let val = this.editTaxRate;\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                data.taxRateShow = val;\r\n                console.log(data.taxRateShow, \".taxRateShowdata5555555555\");\r\n                console.log(data.taxTicketMoney, \".taxTicketMoney\");\r\n                data.taxAmount = countTaxamount1(data);\r\n                // data.taxAmount = (data.taxTicketMoney*1)*(data.taxRateShow*1);\r\n                console.log(data.taxAmount, \".taxAmount\");\r\n                data.editType = 1;\r\n            },\r\n            setCoalType() {\r\n                let val = this.editCoalType;\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                data.coalImportType = val;\r\n                data.editType = 1;\r\n            },\r\n            setCoalUse() {\r\n                let val = this.editCoalUse;\r\n                let data = this.tbAccount.data[this.editIndex];\r\n                data.coalImportUse = val;\r\n                data.editType = 1;\r\n            },\r\n            setMyStyle(length){\r\n                this.myStyle=[];\r\n                for(var i=0;i<length;i++){\r\n                    this.myStyle.push({\r\n                        coalUseBody:'myspan',\r\n                        feeStartDate:'myspan',\r\n                        coalAmount:'myspan',\r\n                        ticketMoney:'myspan',\r\n                        taxTicketMoney:'myspan',\r\n                        taxRateShow:'myspan',\r\n                        otherFee:'myspan',\r\n                        coalImportType:'myspan',\r\n                        coalImportUse:'myspan',\r\n                        remark:'myspan',\r\n                    });\r\n                }\r\n            },\r\n            //span点击事件将span换成输入框并且获取焦点\r\n            selectCall(row,index,columns,str){\r\n                this.editFeeStartDate = row.feeStartDate;\r\n                this.editCoalUseBody = row.coalUseBody;\r\n                this.editCoalAmount = row.coalAmount == null || row.coalAmount===0?null:row.coalAmount;\r\n                this.editCoalType = row.coalImportType;\r\n                this.editCoalUse = row.coalImportUse;\r\n                this.editTicketMoney = row.ticketMoney;\r\n                this.editTaxTicketMoney = row.taxTicketMoney;\r\n                this.editTaxRate = row.taxRateShow;\r\n                this.editOtherFee = row.otherFee;\r\n                this.editremark = row.remark;\r\n                this.editIndex = index;\r\n                this.columnsIndex = columns;\r\n                let a=this;\r\n                setTimeout(function () {\r\n                    a.$refs[str+index+columns].focus();\r\n                },200);\r\n\r\n            },\r\n            //跳转到下一格\r\n            nextCell(data){\r\n                let index = data.editIndex;\r\n                let columns = data.columnsIndex;\r\n                let row = '';\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                }else if(index > -1 && columns === 15){\r\n                    //当跳转的最后一行最后一格的时候\r\n                    if (index >= data.pageSize - 1 || index >= data.pageTotal - 1) {\r\n                        index = 0;\r\n                    }else{\r\n                        index ++;\r\n                    }\r\n                    columns = 1;\r\n                }else{\r\n                    columns += 1;\r\n                }\r\n                data.editIndex = index;\r\n                data.columnsIndex = columns;\r\n                row = data.tbAccount.data[index];\r\n                if(row){\r\n                    data.editFeeStartDate = row.feeStartDate;\r\n                    data.editCoalUseBody = row.coalUseBody;\r\n                    data.editCoalAmount = row.coalAmount == null || row.coalAmount===0?null:row.coalAmount;\r\n                    data.editCoalType = row.coalImportType;\r\n                    data.editCoalUse = row.coalImportUse;\r\n                    data.editTicketMoney = row.ticketMoney;\r\n                    data.editTaxTicketMoney = row.taxTicketMoney;\r\n                    data.editTaxRate = row.taxRateShow;\r\n                    data.editOtherFee = row.otherFee;\r\n                    data.editremark=row.remark;\r\n                }\r\n                setTimeout(function () {\r\n                    data.$refs[data.enterOperate(columns).str+index+columns].focus();\r\n                },200);\r\n            },\r\n            //根据列号返回对应的列名\r\n            enterOperate(number){\r\n                let str = '';\r\n                let data = null;\r\n                switch (number) {\r\n                    case 2:\r\n                        str = 'coalUseBody';\r\n                        data = this.editCoalUseBody;\r\n                        break;\r\n                    case 3:\r\n                        str = 'feeStartDate';\r\n                        data = this.editFeeStartDate;\r\n                        break;\r\n                    case 4:\r\n                        str = 'coalAmount';\r\n                        data = this.editCoalAmount;\r\n                        break;\r\n                    case 6:\r\n                        str = 'ticketMoney';\r\n                        data = this.editTicketMoney;\r\n                        break;\r\n                    case 7:\r\n                        str = 'taxTicketMoney';\r\n                        data = this.editTaxTicketMoney;\r\n                        break;\r\n                    case 8:\r\n                        str = 'taxRateShow';\r\n                        data = this.editTaxRate;\r\n                        break;\r\n                    case 10:\r\n                        str = 'otherFee';\r\n                        data = this.editOtherFee;\r\n                        break;\r\n                    case 12:\r\n                        str = 'coalImportType';\r\n                        data = this.editCoalType;\r\n                        break;\r\n                    case 13:\r\n                        str = 'coalImportUse';\r\n                        data = this.editCoalUse;\r\n                        break;\r\n                    case 15:\r\n                        str = 'remark';\r\n                        data = this.editremark;\r\n                        break;\r\n                }\r\n                return {str:str,data:data};\r\n            },\r\n            pred(){\r\n                var lett = this;\r\n                let index = lett.editIndex;\r\n                let columns = lett.columnsIndex;\r\n                if(index === -1 && columns === -1){\r\n                    index = 0;\r\n                    columns = 1;\r\n                    lett.editIndex = index;\r\n                    lett.columnsIndex = columns;\r\n                    setTimeout(function () {\r\n                        lett.$refs[lett.enterOperate(columns).str+index+columns].focus();\r\n                    },200);\r\n                }else{\r\n                    lett.validate()\r\n                    lett.setremark()\r\n                    lett.nextCell(lett)\r\n                }\r\n            },\r\n            ellipsis (value) {\r\n                if (!value) return ''\r\n                if (value.length > 100) {\r\n                    return value.slice(0,100) + '...'\r\n                }\r\n                return value\r\n            },\r\n            handleUploadSuccess() {\r\n\r\n            },\r\n            handleFormatError(file) {\r\n                this.errorTips(\r\n                    file.name + \" 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件\"\r\n                );\r\n            },\r\n            handleProgress(event, file) {\r\n                this.$Message.info({\r\n                    content: file.name + \" 正在上传。\",\r\n                });\r\n            },\r\n            handleUploadSuccess() {\r\n                console.log(\"上传成功\");\r\n            },\r\n            // 导入\r\n            onExcelUpload(file) {\r\n                if (!file) {\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: '请选择要上传的文件！',\r\n                        duration: 10\r\n                    });\r\n                    return\r\n                }\r\n                let fileName = file.name.lastIndexOf(\".\");//取到文件名开始到最后一个点的长度\r\n                let fileNameLength = file.name.length;//取到文件名长度\r\n                let fileFormat = file.name.substring(fileName + 1, fileNameLength);//截\r\n                if('xls' != fileFormat && 'xlsx' != fileFormat){\r\n                    this.$Notice.error({\r\n                        title: '提示',\r\n                        desc: file.name + ' 格式不正确。只能上传后缀名为 xls或者 xlsx 的文件',\r\n                        duration: 10\r\n                    });\r\n                    return;\r\n                }\r\n                let param = {}\r\n                let excel = {file: file}\r\n                let that = this\r\n                that.spinShow = true\r\n                axios.request({\r\n                    url: '/business/coal/account/import',\r\n                    method: 'post',\r\n                    data: Object.assign({}, param, excel)\r\n                }).then((res) => {\r\n                    that.spinShow = false\r\n                    if (res.data.str) {\r\n                        that.$Message.info({\r\n                            content: res.data.str,\r\n                            duration: 0,\r\n                            closable: true\r\n                        });\r\n\r\n                        that.show = false;\r\n                    }\r\n this.getAccountMessages();\r\n                }).catch(err => {\r\n                    that.spinShow = false\r\n                    that.show = false;\r\n                    console.log(err);\r\n                });\r\n                return false\r\n            },\r\n\r\n            // 导入模板下载\r\n            loadTemplate() {\r\n                let req = {\r\n                    url : \"/business/coal/account/template/load\",\r\n                    method : \"get\",\r\n                    responseType: 'blob',\r\n                };\r\n                axios.file(req)\r\n                    .then(res => {\r\n                        this.spinShow = false;\r\n                        const content = res;\r\n                        const blob = new Blob([content]);\r\n                        const fileName = \"用煤台账导入模板.xlsx\";\r\n                        if (\"download\" in document.createElement(\"a\")) {\r\n                            // 非IE下载\r\n                            const elink = document.createElement(\"a\");\r\n                            elink.download = fileName;\r\n                            elink.style.display = \"none\";\r\n                            elink.href = URL.createObjectURL(blob);\r\n                            document.body.appendChild(elink);\r\n                            elink.click();\r\n                            URL.revokeObjectURL(elink.href); // 释放URL 对象\r\n                            document.body.removeChild(elink);\r\n                        } else {\r\n                            // IE10+下载\r\n                            navigator.msSaveBlob(blob, fileName);\r\n                        }\r\n                });\r\n            }\r\n        },\r\n        mounted() {\r\n            this.version = indexData.version\r\n            this.tbAccount.columns = this.tbAccount.tailColumn;\r\n            this.coalTypes = blist1(\"coalType\");\r\n            this.coalUseTypes = blist1(\"coalUseType\");\r\n            this.accountObj.coalUseType = this.coalUseTypes[0].typeCode\r\n            this.accountObj.coalType = this.coalTypes[0].typeCode\r\n            let that = this\r\n            getUserByUserRole().then(res => {//根据权限获取分公司\r\n                that.companies = res.data.companies;\r\n                if(res.data.isCityAdmin == true || res.data.isProAdmin == true || res.data.isSubAdmin == true){\r\n                    that.isAdmin = true;\r\n                }\r\n                getCountrysdata({orgCode:res.data.companies[0].id}).then(res => {//根据权限获取所属部门\r\n                    that.departments = res.data;\r\n                    that.getUserData();\r\n                });\r\n                // this.tbAccount.tailColumn = this.tbAccount.tailColumn.concat(this.tbAccount.fileColumn)\r\n                // this.tbAccount.tailColumn.push(this.tbAccount.fileColumn[0])\r\n            });\r\n        }\r\n    }\r\n</script>\r\n"], "sourceRoot": "src/view/account"}]}