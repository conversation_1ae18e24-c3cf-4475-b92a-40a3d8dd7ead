{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\warnAnalysis.vue?vue&type=style&index=0&id=3995e70e&lang=less&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\assess\\assessReport\\components\\warnAnalysis.vue", "mtime": 1754285403030}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749178874080}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KI3N0cnVjdHVyZTNELWNoYXJ0cyB7DQogIGhlaWdodDogMzAwcHg7DQogIHdpZHRoOiA1M3JlbTsNCiAgbGV0dGVyLXNwYWNpbmc6IDAuMXJlbTsNCn0NCi50b3RhbF9wb3dlciB7DQogIHdpZHRoOiAxNnJlbTsNCiAgaGVpZ2h0OiAyLjV2aDsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICB0b3A6IDEwcHg7DQogIHJpZ2h0OiA1cmVtOw0KICBkaXNwbGF5OiBmbGV4Ow0KDQogICYgPiBwOm50aC1vZi10eXBlKDEpIHsNCiAgICBmb250LXNpemU6IDE1cHg7DQogICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtTWVkaXVtOw0KICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgY29sb3I6ICMzMDNiNTA7DQogICAgc3BhbiB7DQogICAgICBjb2xvcjogIzAwZWNjMDsNCiAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgIG1hcmdpbi1sZWZ0OiAwLjNyZW07DQogICAgICBmb250LWZhbWlseTogQWxpYmFiYS1QdUh1aVRpLUJvbGQ7DQogICAgfQ0KICB9DQp9DQoNCi9kZWVwLyBjYW52YXMgew0KICB6LWluZGV4OiA5ICFpbXBvcnRhbnQ7DQp9DQo="}, {"version": 3, "sources": ["warnAnalysis.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "warnAnalysis.vue", "sourceRoot": "src/view/carbon/assess/assessReport/components", "sourcesContent": ["<template>\r\n  <div class=\"water-eval-container\" style=\"position: relative\">\r\n    <div class=\"cityGreenLand-charts\" id=\"structure3D-charts\"></div>\r\n    <div class=\"total_power\">\r\n      <p>\r\n        考核预警值<span>{{ warnValue }}%</span>\r\n      </p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport \"echarts-gl\";\r\nexport default {\r\n  name: \"cityGreenLand\",\r\n  components: {},\r\n  data() {\r\n    return {\r\n      electrictTotal: \"\",\r\n      isFullscreen: false,\r\n      optionData: [\r\n        {\r\n          name: \"考核成绩高于预警值\",\r\n          value: 22116,\r\n        },\r\n        {\r\n          name: \"考核成绩低于预警值\",\r\n          value: 16616,\r\n        },\r\n      ],\r\n      nowStep: 0,\r\n      maxStep: 35,\r\n      height: 95,\r\n    };\r\n  },\r\n  props: {\r\n    electricStrcuctObj: {\r\n      type: Object,\r\n    },\r\n    warnValue: {\r\n      type: String,\r\n    },\r\n  },\r\n\r\n  watch: {\r\n    electricStrcuctObj: {\r\n      immediate: true,\r\n      handler(newVal, oldVal) {\r\n        if (newVal == undefined || newVal == null || newVal == \"\") return;\r\n        // 总数量\r\n        this.electrictTotal = newVal.electrictTotal;\r\n        // // 外购绿电\r\n        // this.$set(this.optionData[0], \"value\", newVal.outsourcingGreenPower);\r\n        // 外购火电\r\n        this.$set(this.optionData[0], \"value\", newVal.thanWarnNum);\r\n        this.$set(this.optionData[1], \"value\", newVal.lowWarnNum);\r\n        this.$nextTick(() => {\r\n          this.init();\r\n        });\r\n      },\r\n      deep: true, // 深度监听\r\n    },\r\n  },\r\n\r\n  methods: {\r\n    init() {\r\n      let _this = this;\r\n      //构建3d饼状图\r\n      let myChart = this.$echarts.init(document.getElementById(\"structure3D-charts\"));\r\n      // 传入数据生成 option\r\n      this.option = this.getPie3D(_this.optionData, 0.75);\r\n      myChart.setOption(this.option);\r\n      //是否需要label指引线，如果要就添加一个透明的2d饼状图并调整角度使得labelLine和3d的饼状图对齐，并再次setOption\r\n      this.option.series.push({\r\n        name: \"pie2d\",\r\n        type: \"pie\",\r\n        label: {\r\n          normal: {\r\n            position: \"inner\",\r\n            show: false,\r\n          },\r\n        },\r\n        labelLine: { show: false, length: 15, length2: 40 },\r\n        startAngle: 0, //起始角度，支持范围[0, 360]。\r\n        clockwise: false, //饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式\r\n        radius: [\"78%\", \"74%\"],\r\n        center: [\"50%\", \"50%\"],\r\n        data: _this.optionData,\r\n        itemStyle: {\r\n          opacity: 0,\r\n        },\r\n      });\r\n\r\n      myChart.setOption(this.option);\r\n      window.addEventListener(\"resize\", function () {\r\n        myChart.resize();\r\n      });\r\n      // this.bindListen(myChart);\r\n    },\r\n\r\n    getPie3D(pieData, internalDiameterRatio) {\r\n      //internalDiameterRatio:透明的空心占比\r\n      let that = this;\r\n      let series = [];\r\n      let sumValue = 1;\r\n      let startValue = 1;\r\n      let endValue = 1;\r\n      let legendData = [];\r\n      let legendBfb = [];\r\n      let k = 1 - internalDiameterRatio;\r\n      pieData.sort((a, b) => {\r\n        return b.value - a.value;\r\n      });\r\n      // 为每一个饼图数据，生成一个 series-surface 配置\r\n      for (let i = 0; i < pieData.length; i++) {\r\n        sumValue += pieData[i].value;\r\n        let seriesItem = {\r\n          name: typeof pieData[i].name === \"undefined\" ? `series${i}` : pieData[i].name,\r\n          type: \"surface\",\r\n          parametric: true,\r\n          wireframe: {\r\n            show: false,\r\n          },\r\n          pieData: pieData[i],\r\n          pieStatus: {\r\n            selected: false,\r\n            hovered: false,\r\n            k: k,\r\n          },\r\n          // center: [\"10%\", \"50%\"],\r\n        };\r\n\r\n        if (typeof pieData[i].itemStyle != \"undefined\") {\r\n          let itemStyle = {};\r\n\r\n          typeof pieData[i].itemStyle.opacity != \"undefined\"\r\n            ? (itemStyle.opacity = pieData[i].itemStyle.opacity)\r\n            : null;\r\n          seriesItem.itemStyle = itemStyle;\r\n        }\r\n        series.push(seriesItem);\r\n      }\r\n\r\n      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，\r\n      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。\r\n      legendData = [];\r\n      legendBfb = [];\r\n      for (let i = 0; i < series.length; i++) {\r\n        endValue = startValue + series[i].pieData.value;\r\n        series[i].pieData.startRatio = startValue / sumValue;\r\n        series[i].pieData.endRatio = endValue / sumValue;\r\n        series[i].parametricEquation = this.getParametricEquation(\r\n          series[i].pieData.startRatio,\r\n          series[i].pieData.endRatio,\r\n          false,\r\n          false,\r\n          k,\r\n          series[i].pieData.value\r\n        );\r\n        startValue = endValue;\r\n        let bfb = that.fomatFloat(series[i].pieData.value / sumValue, 4);\r\n        legendData.push({\r\n          name: series[i].name,\r\n          value: bfb,\r\n        });\r\n        legendBfb.push({\r\n          name: series[i].name,\r\n          value: series[i].pieData.value,\r\n        });\r\n      }\r\n      let boxHeight = this.getHeight3D(series, 15); //通过传参设定3d饼/环的高度，26代表26px\r\n      // 准备待返回的配置项，把准备好的 legendData、series 传入。\r\n      let option = {\r\n        color: [\"#00FEEB\", \"#FF8D33\", \" #94B1A8\"],\r\n        legend: {\r\n          orient: \"vertical\",\r\n          data: legendData,\r\n          type: \"plain\",\r\n          right: \"7%\",\r\n          top: \"1%\",\r\n          itemGap: 10,\r\n          itemWidth: 10,\r\n          itemHeight: 10,\r\n          textStyle: {\r\n            color: \"#A1E2FF\",\r\n            rich: {\r\n              name: {\r\n                align: \"left\",\r\n                width: 90,\r\n                padding: [0, 0, 0, 0],\r\n                fontSize: 13,\r\n                fontFamily: \"PingFangSC-Regular\",\r\n                color: \"#303b50\",\r\n              },\r\n\r\n              bfs: {\r\n                fontSize: 13,\r\n                fontFamily: \"PingFangSC-Regular\",\r\n                color: \"#303b50\",\r\n                // align: \"right\",\r\n                padding: [0, 0, 0, 30],\r\n              },\r\n\r\n              wan: {\r\n                fontSize: 12,\r\n                color: \"#4AE5E3\",\r\n                padding: [0, 0, 0, 1],\r\n              },\r\n            },\r\n          },\r\n          // data: [\r\n          //   {\r\n          //     name: \"外购绿电\",\r\n          //     icon: `image://${lightning}`,\r\n          //   },\r\n          //   { name: \"外购火电\", icon: `image://${outsourcing}` },\r\n          //   { name: \"自有新能源发电\", icon: `image://${newenergy}` },\r\n          // ],\r\n          show: true,\r\n          formatter: function (param) {\r\n            let item = legendBfb.filter((item) => item.name == param)[0];\r\n\r\n            return `{name|${item.name} } {bfs|  ${item.value} }`;\r\n          },\r\n        },\r\n\r\n        label: {\r\n          show: false,\r\n          textStyle: {\r\n            color: \"#000\",\r\n          },\r\n        },\r\n\r\n        xAxis3D: {\r\n          min: -1,\r\n          max: 1,\r\n        },\r\n        yAxis3D: {\r\n          min: -1,\r\n          max: 1,\r\n        },\r\n        zAxis3D: {\r\n          min: -1,\r\n          max: 1,\r\n        },\r\n        grid3D: {\r\n          show: false,\r\n          left: \"-25%\",\r\n          top: \"-10%\",\r\n          boxHeight: boxHeight, //圆环的高度\r\n          viewControl: {\r\n            //3d效果可以放大、旋转等，请自己去查看官方配置\r\n            alpha: 27, //角度\r\n            beta: 0,\r\n            distance: 220, //调整视角到主体的距离，类似调整zoom\r\n            rotateSensitivity: 0, //设置为0无法旋转\r\n            zoomSensitivity: 0, //设置为0无法缩放\r\n            panSensitivity: 0, //设置为0无法平移\r\n            autoRotate: true, //自动旋转\r\n          },\r\n        },\r\n        series: series,\r\n      };\r\n      return option;\r\n    },\r\n\r\n    //获取3d丙图的最高扇区的高度\r\n    getHeight3D(series, height) {\r\n      series.sort((a, b) => {\r\n        return b.pieData.value - a.pieData.value;\r\n      });\r\n      return (height * 20) / series[0].pieData.value;\r\n    },\r\n\r\n    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation\r\n    getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {\r\n      // 计算\r\n      let midRatio = (startRatio + endRatio) / 2;\r\n      let startRadian = startRatio * Math.PI * 2;\r\n      let endRadian = endRatio * Math.PI * 2;\r\n      let midRadian = midRatio * Math.PI * 2;\r\n      // 如果只有一个扇形，则不实现选中效果。\r\n      // if (startRatio === 0 && endRatio === 1) {\r\n      //   isSelected = false;\r\n      // }\r\n      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）\r\n      k = typeof k !== \"undefined\" ? k : 1 / 3;\r\n      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）\r\n      let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;\r\n      let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;\r\n      // 计算高亮效果的放大比例（未高亮，则比例为 1）\r\n      let hoverRate = isHovered ? 1.05 : 1;\r\n      // 返回曲面参数方程\r\n      return {\r\n        u: {\r\n          min: -Math.PI,\r\n          max: Math.PI * 3,\r\n          step: Math.PI / 32,\r\n        },\r\n        v: {\r\n          min: 0,\r\n          max: Math.PI * 2,\r\n          step: Math.PI / 20,\r\n        },\r\n        x: function (u, v) {\r\n          if (u < startRadian) {\r\n            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n          }\r\n          if (u > endRadian) {\r\n            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n          }\r\n          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;\r\n        },\r\n        y: function (u, v) {\r\n          if (u < startRadian) {\r\n            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n          }\r\n          if (u > endRadian) {\r\n            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;\r\n          }\r\n          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;\r\n        },\r\n        z: function (u, v) {\r\n          if (u < -Math.PI * 0.5) {\r\n            return Math.sin(u);\r\n          }\r\n          if (u > Math.PI * 2.5) {\r\n            return Math.sin(u) * h * 0.1;\r\n          }\r\n          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;\r\n        },\r\n      };\r\n    },\r\n\r\n    fomatFloat(num, n) {\r\n      var f = parseFloat(num);\r\n      if (isNaN(f)) {\r\n        return false;\r\n      }\r\n      f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n); // n 幂\r\n      var s = f.toString();\r\n      var rs = s.indexOf(\".\");\r\n      //判定如果是整数，增加小数点再补0\r\n      if (rs < 0) {\r\n        rs = s.length;\r\n        s += \".\";\r\n      }\r\n      while (s.length <= rs + n) {\r\n        s += \"0\";\r\n      }\r\n      return s;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n#structure3D-charts {\r\n  height: 300px;\r\n  width: 53rem;\r\n  letter-spacing: 0.1rem;\r\n}\r\n.total_power {\r\n  width: 16rem;\r\n  height: 2.5vh;\r\n  position: absolute;\r\n  top: 10px;\r\n  right: 5rem;\r\n  display: flex;\r\n\r\n  & > p:nth-of-type(1) {\r\n    font-size: 15px;\r\n    font-family: PingFangSC-Medium;\r\n    font-weight: 400;\r\n    color: #303b50;\r\n    span {\r\n      color: #00ecc0;\r\n      font-size: 16px;\r\n      margin-left: 0.3rem;\r\n      font-family: Alibaba-PuHuiTi-Bold;\r\n    }\r\n  }\r\n}\r\n\r\n/deep/ canvas {\r\n  z-index: 9 !important;\r\n}\r\n</style>\r\n"]}]}