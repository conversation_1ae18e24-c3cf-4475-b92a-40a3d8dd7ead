{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePagePylon\\addPylonPredAccount.vue?vue&type=template&id=350f1bfd&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\account\\homePagePylon\\addPylonPredAccount.vue", "mtime": *************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}