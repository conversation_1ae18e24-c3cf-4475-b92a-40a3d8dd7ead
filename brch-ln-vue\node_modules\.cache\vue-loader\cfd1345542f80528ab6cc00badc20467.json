{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\energyview\\modifyReport.vue?vue&type=style&index=0&id=38de0962&lang=less&scoped=true&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\carbon\\discharge\\energyview\\modifyReport.vue", "mtime": 1754285403042}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749178874080}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KL2RlZXAvLmVsLXRhYmxlIHsNCiAgaGVpZ2h0OiBhdXRvIWltcG9ydGFudDsNCiAgLy8kcHJvcGVydGllczogbWF4LWhlaWdodDsNCiAgLy9AZWFjaCAkcHJvcCBpbiAkcHJvcGVydGllcyB7DQogIC8vICAjeyRwcm9wfTogdW5zZXQhaW1wb3J0YW50Ow0KICAvL30NCiAgJiA+IC5lbC10YWJsZV9fYm9keS13cmFwcGVyIHsNCiAgICBoZWlnaHQ6IGF1dG8haW1wb3J0YW50Ow0KICB9DQp9DQojbW9kdWxhckZvcm1fYmc6Oi13ZWJraXQtc2Nyb2xsYmFyIHsNCiAgICAvLyBkaXNwbGF5OiBub25lOw0KICAgIHdpZHRoOiAycHg7DQogIH0NCg0KLyog5rua5Yqo5p2h6L2o6YGTICovDQojbW9kdWxhckZvcm1fYmc6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsNCiAgYmFja2dyb3VuZDogI2YxZjFmMTsgLyog6K6+572u5rua5Yqo5p2h6L2o6YGT55qE6IOM5pmv6ImyICovDQp9DQoNCi8qIOa7muWKqOadoea7keWdlyAqLw0KI21vZHVsYXJGb3JtX2JnOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQogIGJhY2tncm91bmQ6ICM4ODg7IC8qIOiuvue9rua7muWKqOadoea7keWdl+eahOiDjOaZr+iJsiAqLw0KfQ0KDQovKiDmu5rliqjmnaHmu5HlnZflnKjpvKDmoIfmgqzlgZzml7bnmoTmoLflvI8gKi8NCiNtb2R1bGFyRm9ybV9iZzo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIgew0KICBiYWNrZ3JvdW5kOiAjNTU1OyAvKiDorr7nva7pvKDmoIfmgqzlgZzml7bmu5rliqjmnaHmu5HlnZfnmoTog4zmma/oibIgKi8NCn0NCg0KL2RlZXAvLmNlbGwtY29sb3Igew0KICBjb2xvcjpyZWQgIWltcG9ydGFudDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KfQ0KDQouZm9vdF9idG4gew0KICB3aWR0aDogODglOw0KICBoZWlnaHQ6IDd2aDsNCiAgcG9zaXRpb246IGZpeGVkOw0KICBib3R0b206IDA7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBiYWNrZ3JvdW5kOiAjMTExZDMwOw0KfQ0KDQpwIHsNCiAgbWFyZ2luOiAwOw0KfQ0KL2RlZXAvLmF2dWUtY3J1ZF9fbWVudSB7DQogIG1hcmdpbi1ib3R0b206IDAgIWltcG9ydGFudDsNCn0NCg0KLmN1cmQtaGVhZGVyIHsNCiAgd2lkdGg6IDk3LjYlOw0KICBoZWlnaHQ6IDV2aDsNCiAgbWFyZ2luOiAydmggYXV0byAwOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBiYWNrZ3JvdW5kOiAjMTI1MzdhOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGJvcmRlci1ib3R0b206IDAuMXJlbSBzb2xpZCAjMDQyMjNiOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICYgPiAucmVwb3J0aW5nIHsNCiAgICAvLyB3aWR0aDogNjlyZW07DQogICAgZGlzcGxheTogZmxleDsNCiAgICBtYXJnaW4tbGVmdDogMXJlbTsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIHAgew0KICAgICAgZm9udC1zaXplOiAxLjRyZW07DQogICAgICBsaW5lLWhlaWdodDogNXZoOw0KICAgICAgbWFyZ2luLXJpZ2h0OiAxcmVtOw0KICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtUmVndWxhciwgUGluZ0ZhbmcgU0M7DQogICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgY29sb3I6ICNmZmZmZmY7DQogICAgfQ0KICAgIC9kZWVwLy5lbC1kYXRlLWVkaXRvci5lbC1pbnB1dCwNCiAgICAuZWwtZGF0ZS1lZGl0b3IuZWwtaW5wdXRfX2lubmVyIHsNCiAgICAgIHdpZHRoOiAyMHJlbSAhaW1wb3J0YW50Ow0KICAgIH0NCiAgfQ0KICAmID4gLmNvbXBhbnlOYW1lIHsNCiAgICBtYXJnaW4tcmlnaHQ6IDEycmVtOw0KICAgIGZvbnQtc2l6ZTogMS40cmVtOw0KICAgIGxpbmUtaGVpZ2h0OiA1dmg7DQogICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtTWVkaXVtLCBQaW5nRmFuZyBTQzsNCiAgICBmb250LXdlaWdodDogNTAwOw0KICAgIGNvbG9yOiAjMDBlY2MwOw0KICB9DQp9DQoNCi5lbF9pbnB1dF9jbGFzcyB7DQogIC9kZWVwLy5lbC1pbnB1dF9faW5uZXJ7DQogICAgYmFja2dyb3VuZDogcmVkOw0KICB9DQp9DQouZWwtaW5wdXQuaXMtZGlzYWJsZWQgL2RlZXAvIC5lbC1pbnB1dF9faW5uZXIgew0KICBiYWNrZ3JvdW5kOiAjNjI2ZjdhICFpbXBvcnRhbnQ7DQp9DQo6OnYtZGVlcCBpbnB1dDo6LXdlYmtpdC1vdXRlci1zcGluLWJ1dHRvbiwNCjo6di1kZWVwIGlucHV0Ojotd2Via2l0LWlubmVyLXNwaW4tYnV0dG9uIHsNCiAgLXdlYmtpdC1hcHBlYXJhbmNlOiBub25lICFpbXBvcnRhbnQ7DQp9DQo6OnYtZGVlcCBpbnB1dFt0eXBlPSdudW1iZXInXSB7DQogIC1tb3otYXBwZWFyYW5jZTogdGV4dGZpZWxkICFpbXBvcnRhbnQ7DQp9DQo="}, {"version": 3, "sources": ["modifyReport.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAg+BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "modifyReport.vue", "sourceRoot": "src/view/carbon/discharge/energyview", "sourcesContent": ["<template>\r\n  <div id=\"modularForm\">\r\n    <header>\r\n      <el-button type=\"success\" @click=\"goBack\">返回</el-button>\r\n      能源数据汇总查看/修改记录\r\n    </header>\r\n\r\n    <div id=\"modularForm_bg\" style=\"overflow-x: hidden;\">\r\n      <ul v-for=\"item in list\" :key=\"item.id\">\r\n        <li style=\"margin: 2rem;\">\r\n          <ul style=\"width: 50%; display: flex; color: #ffffff; justify-content: space-between; font-size: 1.5rem; line-height: 6rem; height: 6rem;\">\r\n            <li style=\"width: 30%;\"><span>{{ item.operateType == 1 ? item.reportTime:item.createTime }}</span></li>\r\n            <li style=\"width: 30%;\"><span>操作人：{{ item.createName }}</span></li>\r\n            <li style=\"width: 20%;\"><span>{{ item.operateType == 1 ?'上报':'修改'}} </span></li>\r\n            <li style=\"width: 20%;\">\r\n              <el-button type=\"primary\" @click.prevent=\"getObj(item)\">{{ !item.show?'查看数据':'收起数据' }}</el-button>\r\n              <!-- <el-button type=\"primary\" @click.prevent=\"getObj(item)\">{{ item.names }}</el-button> -->\r\n            </li>\r\n          </ul>\r\n          <div v-show=\"item.show == true\" style=\"color: #ffffff;\">\r\n\r\n\t<avue-crud v-show=\"item.show == true\"\r\n            ref=\"crud\"\r\n            :data=\"item.tableData\"\r\n            :table-loading=\"tableLoading\"\r\n            :option=\"tableOption\"\r\n          :cell-class-name=\"dataStyle\"\r\n            >\r\n            <!-- <template slot=\"groupDataForm\" slot-scope=\"{ row,index}\">\r\n                  <el-input :class=\"[row.groupEdit ? 'el_input_class' : '']\"\r\n                            v-model=\"row.groupData\"\r\n                            :disabled=\"disabledList.groupList.indexOf(index) == -1 ? false : true\"\r\n                            :type=\"noInputList.groupList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                            @change=\"handleGroupChange(row,index)\"\r\n                            @keydown.native=\"inputLimit\"\r\n                            :placeholder=\"formulaList.groupList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                            @mousewheel.native.prevent\r\n                  ></el-input>\r\n              </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                  <el-input :class=\"[row.groupEdit ? 'el_input_class' : '']\"\r\n                  ></el-input>\r\n              </template>\r\n              <!-- <template slot=\"stockDataForm\" slot-scope=\"{ row, index}\">\r\n                <el-input :class=\"[row.stockEdit ? 'el_input_class' : '']\"\r\n                          :disabled=\"disabledList.stockList.indexOf(index) == -1 ? false : true\"\r\n                          :type=\"noInputList.stockList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                          @change=\"handleStockChange(row,index)\"\r\n                          @keydown.native=\"inputLimit\"\r\n                          :placeholder=\"formulaList.stockList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                          @mousewheel.native.prevent\r\n                ></el-input>\r\n              </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                <el-input :class=\"[row.stockEdit ? 'el_input_class' : '']\"\r\n                ></el-input>\r\n              </template>\r\n              <!-- <template slot=\"largeDataForm\" slot-scope=\"{ row, index}\">\r\n                <el-input :class=\"[row.largeEdit ? 'el_input_class' : '']\"\r\n                          v-model=\"row.largeData\"\r\n                          :disabled=\"disabledList.largeList.indexOf(index) == -1 ? false : true\"\r\n                          :type=\"noInputList.largeList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                          @change=\"handleLargeChange(row,index)\"\r\n                          @keydown.native=\"inputLimit\"\r\n                          :placeholder=\"formulaList.largeList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                          @mousewheel.native.prevent\r\n                ></el-input>\r\n              </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                <el-input :class=\"[row.largeEdit ? 'el_input_class' : '']\"\r\n                ></el-input>\r\n              </template>\r\n              <!-- <template slot=\"mediumDataForm\" slot-scope=\"{ row, index }\">\r\n                <el-input :class=\"[row.mediumEdit ? 'el_input_class' : '']\"\r\n                          v-model=\"row.mediumData\"\r\n                          :disabled=\"disabledList.mediumList.indexOf(index) == -1 ? false : true\"\r\n                          :type=\"noInputList.mediumList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                          @change=\"handleMediumChange(row,index)\"\r\n                          @keydown.native=\"inputLimit\"\r\n                          :placeholder=\"formulaList.mediumList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                          @mousewheel.native.prevent\r\n                ></el-input>\r\n              </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                <el-input :class=\"[row.mediumEdit ? 'el_input_class' : '']\"\r\n                ></el-input>\r\n              </template>\r\n              <!-- <template slot=\"mobileDataForm\" slot-scope=\"{ row, index }\">\r\n                <el-input :class=\"[row.mobileEdit ? 'el_input_class' : '']\"\r\n                          v-model=\"row.mobileData\"\r\n                          :disabled=\"disabledList.mobileList.indexOf(index) == -1 ? false : true\"\r\n                          :type=\"noInputList.mobileList.indexOf(index) == -1 ? 'number' : 'text'\"\r\n                          @change=\"handleMobileChange(row,index)\"\r\n                          @keydown.native=\"inputLimit\"\r\n                          :placeholder=\"formulaList.mobileList.indexOf(index) == -1 ? '请输入数据' : '自动计算'\"\r\n                          @mousewheel.native.prevent\r\n                ></el-input>\r\n            </template> -->\r\n              <template slot-scope=\"{ row }\">\r\n                <el-input :class=\"[row.mobileEdit ? 'el_input_class' : '']\"\r\n                ></el-input>\r\n            </template>\r\n          </avue-crud>\r\n          </div>\r\n\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\n// import emptyStrate from \"@/components/empty\";\r\nimport delects from \"@/components/delects/index\";\r\n// import {mapGetters} from \"vuex\";\r\n// import {dateFormat} from \"@/util/date\";\r\n// import { downloadFile } from \"@/util/ruoyi\";\r\nimport {energyList, energyUpdateList,getEnergyUpdateRecordList,energyDataUpdateList} from \"@/api/carbon/discharge/energy\";\r\nimport jituanEnergyReportLog from \"./jituanEnergyReportLog\";\r\nexport default {\r\n  components: {\r\n    // emptyStrate,\r\n    delects,\r\n    jituanEnergyReportLog,\r\n  },\r\n  data() {\r\n    return {\r\n      objName: '查看数据',\r\n      tableLoading: false,\r\n      isShow: false,\r\n      list: [],\r\n      tableOption: {\r\n        border: false,\r\n        index: false,\r\n        height: \"auto\",\r\n        calcHeight: 35,\r\n        stripe: true,\r\n        menuAlign: \"center\",\r\n        align: \"center\",\r\n        refreshBtn: false,\r\n        showClomnuBtn: false,\r\n        searchMenuSpan: 4,\r\n        searchSize: \"mini\",\r\n        card: true,\r\n        addBtn: false,\r\n        editBtn: false,\r\n        delBtn: false,\r\n        columnBtn: false,\r\n        searchBtn: false,\r\n        emptyBtn: false,\r\n        menu: false,\r\n        dialogWidth: 500,\r\n        dialogMenuPosition: \"center\",\r\n        dialogCustomClass: \"singleRowDialog\",\r\n        labelWidth: 100,\r\n        column: [\r\n          {\r\n            label: \"指标\",\r\n            prop: \"indicatorName\",\r\n            overHidden: true,\r\n            width: 750,\r\n            align: \"left\"\r\n          },\r\n          {\r\n            label: \"集团\",\r\n            prop: \"groupData\",\r\n            formatter:(val,value,label)=>{\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: false,\r\n          },\r\n          {\r\n            label: \"股份\",\r\n            prop: \"stockData\",\r\n            formatter:(val,value,label)=>{\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: false,\r\n          },\r\n          {\r\n            label: '数据中心',\r\n            children: [{\r\n              label: '大型',\r\n              prop: 'largeData',\r\n              formatter:(val,value,label)=>{\r\n                return this.formatDisplayData(value);\r\n              },\r\n              overHidden: true,\r\n              cell: true,\r\n              slot: true\r\n            }, {\r\n              label: '中小型',\r\n              prop: 'mediumData',\r\n              formatter:(val,value,label)=>{\r\n                return this.formatDisplayData(value);\r\n              },\r\n              cell: true,\r\n              slot: true,\r\n              overHidden: true\r\n            }]\r\n          },\r\n          {\r\n            label: \"移动业务\",\r\n            prop: \"mobileData\",\r\n            formatter:(val,value,label)=>{\r\n              return this.formatDisplayData(value);\r\n            },\r\n            cell: true,\r\n            slot: true,\r\n            overHidden: false,\r\n          },\r\n        ],\r\n      },\r\n      companyName: undefined,\r\n      tableData: [],\r\n      // 原表格数据\r\n      originalData: [],\r\n      // 需改的下标数组\r\n      updateList:[],\r\n      energyData: [],\r\n      disabledList: {\r\n        groupList: [0, 4, 5, 7, 18, 23, 27, 57],\r\n        stockList: [0, 4, 5, 7, 18, 23, 27, 57],\r\n        largeList: [0, 4, 5, 6, 7, 8, 9, 10, 17, 18, 19, 20, 23, 27, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        mediumList: [0, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 23, 27, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        mobileList: [0, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 24, 25, 28, 29, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57],\r\n      },\r\n      formulaList: {\r\n        groupList: [0, 4, 5, 7, 18, 23, 27],\r\n        stockList: [0, 4, 5, 7, 18, 23, 27],\r\n        largeList: [0, 4, 23, 27],\r\n        mediumList: [0, 4, 11, 12, 13, 14, 15, 16, 23, 27],\r\n        mobileList: [0, 4, 7, 8, 9, 10],\r\n      },\r\n      noInputList: {\r\n        groupList: [57],\r\n        stockList: [57],\r\n        largeList: [5, 6, 7, 8, 9, 10, 17, 18, 19, 20, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        mediumList: [5, 6, 7, 8, 9, 10, 17, 18, 19, 20, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        mobileList: [2, 5, 6, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 24, 25, 28, 29, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57],\r\n      },\r\n      requireInputList: {\r\n        groupList: [1, 2, 3, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38],\r\n        stockList: [1, 2, 3, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38],\r\n        largeList: [1, 2, 3, 6, 11, 12, 13, 14, 15, 16, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 57],\r\n        mediumList: [1, 2, 3, 6, 21, 22, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 57],\r\n        mobileList: [1, 3, 21, 22, 23, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37],\r\n      },\r\n      noRequireInputList: {\r\n        groupList: [39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n        stockList: [39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56],\r\n      },\r\n      backgroundList: {\r\n        groupList: [],\r\n        stockList: [],\r\n        largeList: [],\r\n        mediumList: [],\r\n        mobileList: [],\r\n      },\r\n      formData: {\r\n        reportTime: undefined,\r\n        companyId: undefined,\r\n      },\r\n    };\r\n  },\r\n  computed: {\r\n\r\n  },\r\n  created() {\r\n    if (this.$route.query) {\r\n\r\n      this.formData.reportTime = this.$route.query.reportTime;\r\n      this.companyName = this.$route.query.companyName;\r\n      this.formData.companyId = this.$route.query.companyId;\r\n      // this.getList();\r\n      this.getUpdRecordList()\r\n    }\r\n  },\r\n  methods: {\r\ndataStyle({row,column,rowIndex,columnIndex}){\r\n      if(columnIndex === 1 && row.groupEdit){\r\n        return 'cell-color';\r\n      }\r\n      if(columnIndex === 2 && row.stockEdit){\r\n        return 'cell-color';\r\n      }\r\n      if(columnIndex === 3 && row.largeEdit){\r\n        return 'cell-color';\r\n      }\r\n      if(columnIndex === 4 && row.mediumEdit){\r\n        return 'cell-color';\r\n      }\r\n      if(columnIndex === 5 && row.mobileEdit){\r\n        return 'cell-color';\r\n      }\r\n    },\r\n    getUpdRecordList() {\r\n      getEnergyUpdateRecordList(Object.assign({companyId:this.formData.companyId,reportTime:this.formData.reportTime})).then((res) => {\r\n        if (res.data.code == 200) {\r\n          this.list = res.data.data\r\n          this.list.forEach(items => {\r\n            items.names = '查看数据';\r\n          })\r\n        }\r\n      });\r\n    },\r\n\r\n    //数据禁止输入e\r\n    inputLimit(event) {\r\n      if (event.key === 'e' || event.key === '-') {\r\n        event.returnValue = false;\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    goBack() {\r\n      this.$router.push({\r\n        path: \"/dataView/energy/index\",\r\n        query: {\r\n          reportTime: this.formData.reportTime,\r\n        },\r\n      });\r\n    },\r\n    // 查询默认列表\r\n    getList(item) {\r\n      this.tableLoading = true;\r\n      energyList(this.formData).then((res) => {\r\n        if (res.data.code == 200) {\r\n          this.tableLoading = false;\r\n          item.tableData = res.data.data;\r\n          this.checkIndicatorInput();\r\n          this.tableData.forEach((item) => {\r\n            item.unit = item.unitDescription + \"(\" + item.unitName + \")\";\r\n            item.$cellEdit = true;\r\n            item.groupData = item.groupData == null ? \"\\\\\" : item.groupData;\r\n            item.stockData = item.stockData == null ? \"\\\\\" : item.stockData;\r\n            item.largeData = item.largeData == null ? \"\\\\\" : item.largeData;\r\n            item.mediumData = item.mediumData == null ? \"\\\\\" : item.mediumData;\r\n            item.mobileData = item.mobileData == null ? \"\\\\\" : item.mobileData;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 查询修改的列表\r\n    getEnergyUpdateRecordList(item) {\r\n      this.tableLoading = true;\r\n      this.formData.updateRecordId = item.id\r\n      energyDataUpdateList(this.formData).then((res) => {\r\n        if (res.data.code == 200) {\r\n          this.tableLoading = false;\r\n          item.tableData = res.data.data;\r\n          this.checkIndicatorInput();\r\n          this.tableData.forEach((item) => {\r\n            item.unit = item.unitDescription + \"(\" + item.unitName + \")\";\r\n            item.$cellEdit = true;\r\n            item.groupData = item.groupData == null ? \"\\\\\" : item.groupData;\r\n            item.stockData = item.stockData == null ? \"\\\\\" : item.stockData;\r\n            item.largeData = item.largeData == null ? \"\\\\\" : item.largeData;\r\n            item.mediumData = item.mediumData == null ? \"\\\\\" : item.mediumData;\r\n            item.mobileData = item.mobileData == null ? \"\\\\\" : item.mobileData;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    saveReportData() {\r\n      if (this.validReportData() == false) {\r\n        this.alertError(\"请输入全部数据\");\r\n        return;\r\n      }\r\n      let data = {}\r\n      let saveList = [];\r\n      this.tableData.forEach((item) => {\r\n        let saveItem = {};\r\n        saveItem.reportTime = this.formData.reportTime;\r\n        saveItem.companyId = this.formData.companyId;\r\n        saveItem.reportFlag = \"2\";\r\n        saveItem.id = item.id;\r\n        saveItem.indicatorName = item.indicatorName;\r\n        saveItem.groupData = item.groupData == '\\\\' || item.groupData == '/' ? null : item.groupData;\r\n        saveItem.stockData = item.stockData == '\\\\' || item.stockData == '/' ? null : item.stockData;\r\n        saveItem.largeData = item.largeData == '\\\\' || item.largeData == '/' ? null : item.largeData;\r\n        saveItem.mediumData = item.mediumData == '\\\\' || item.mediumData == '/' ? null : item.mediumData;\r\n        saveItem.mobileData = item.mobileData == '\\\\' || item.mobileData == '/' ? null : item.mobileData;\r\n        saveList.push(saveItem);\r\n      });\r\n      data.companyId = this.formData.companyId\r\n      data.dischargeEnergyIndicatorVos = saveList\r\n      data.dischargeDataEnergyUpdateLogList = this.updateList\r\n      energyUpdateList(data).then((res) => {\r\n        if (res.data.code == 200) {\r\n          this.alertSuccess(\"操作成功\");\r\n          this.goBack();\r\n        }\r\n      });\r\n    },\r\n    //校验上报数据\r\n    validReportData() {\r\n      let ret = true;\r\n      this.backgroundList.groupList = [];\r\n      this.backgroundList.stockList = [];\r\n      this.backgroundList.largeList = [];\r\n      this.backgroundList.mediumList = [];\r\n      this.backgroundList.mobileList = [];\r\n      for (let i = 0; i < this.tableData.length; i++) {\r\n        if (this.tableData[i].groupData == undefined\r\n          || this.tableData[i].groupData == null\r\n          || this.tableData[i].groupData.length == 0) {\r\n          if (this.requireInputList.groupList.indexOf(i) > -1) {\r\n            this.backgroundList.groupList.push(i);\r\n            ret = false;\r\n          } else {\r\n            this.tableData[i].groupData = \"0\";\r\n          }\r\n        }\r\n        if (this.tableData[i].stockData == undefined\r\n          || this.tableData[i].stockData == null\r\n          || this.tableData[i].stockData.length == 0) {\r\n          if (this.requireInputList.stockList.indexOf(i) > -1) {\r\n            this.backgroundList.stockList.push(i);\r\n            ret = false;\r\n          } else {\r\n            this.tableData[i].stockData = \"0\";\r\n          }\r\n          ret = false;\r\n        }\r\n        if (this.tableData[i].largeData == undefined\r\n          || this.tableData[i].largeData == null\r\n          || this.tableData[i].largeData.length == 0) {\r\n          this.backgroundList.largeList.push(i);\r\n          ret = false;\r\n        }\r\n        if (this.tableData[i].mediumData == undefined\r\n          || this.tableData[i].mediumData == null\r\n          || this.tableData[i].mediumData.length == 0) {\r\n          this.backgroundList.mediumList.push(i);\r\n          ret = false;\r\n        }\r\n        if (this.tableData[i].mobileData == undefined\r\n          || this.tableData[i].mobileData == null\r\n          || this.tableData[i].mobileData.length == 0) {\r\n          this.backgroundList.mobileList.push(i);\r\n          ret = false;\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n    // 保存修改记录的方法\r\n    saveChangeIndex(data,index,indexName,num,id){\r\n      const predata = this.originalData[index][indexName]\r\n    // 若是修改了数据，则记录下标\r\n    if(this.areFloatsEqual(data,this.originalData[index][indexName])){\r\n        const filteredArray = this.updateList.filter(obj => obj.id !== id);\r\n        this.updateList = filteredArray\r\n      } else {\r\n        // 使用findIndex()方法查找数组中具有特定id的对象的索引\r\n        const index = this.updateList.findIndex(obj => obj.id === id);\r\n        // 如果索引大于等于0，则表示找到了对象，可以删除\r\n        if (index >= 0) {\r\n            // 使用splice()方法删除数组中指定索引的对象\r\n            this.updateList.splice(index, 1);\r\n        }\r\n        this.updateList.push({energyId:id,num:num,previousData:predata,nowData:data})\r\n      }\r\n    },\r\n\r\n    // handleGroupChange(row,index) {\r\n    //   this.saveChangeIndex(row.groupData,index,'groupData',1,row.id)\r\n    //   if (this.noInputList.groupList.indexOf(index) == -1 && this.formulaList.groupList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['groupData'].toString()) < 0) {\r\n    //       this.tableData[index]['groupData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['groupData'] != undefined && this.tableData[index]['groupData'] != '') {\r\n    //       let j = this.backgroundList.groupList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.groupList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     if (index == 6 || index == 11 || index == 17) {\r\n    //       this.tableData[5]['groupData'] = (this.cellToNumber(this.tableData[6]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[7]['groupData']) + this.cellToNumber(this.tableData[11]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[17]['groupData'])).toString();\r\n    //       this.tableData[4]['groupData'] = (this.cellToNumber(this.tableData[5]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[18]['groupData']) + this.cellToNumber(this.tableData[21]['groupData'])).toString();\r\n    //     } else if (index == 8 || index == 9 || index == 10) {\r\n    //       this.tableData[7]['groupData'] = (this.cellToNumber(this.tableData[8]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[9]['groupData']) + this.cellToNumber(this.tableData[10]['groupData'])).toString();\r\n    //       this.tableData[5]['groupData'] = (this.cellToNumber(this.tableData[6]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[7]['groupData']) + this.cellToNumber(this.tableData[11]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[17]['groupData'])).toString();\r\n    //       this.tableData[4]['groupData'] = (this.cellToNumber(this.tableData[5]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[18]['groupData']) + this.cellToNumber(this.tableData[21]['groupData'])).toString();\r\n    //     } else if (index == 19 || index == 20) {\r\n    //       this.tableData[18]['groupData'] = (this.cellToNumber(this.tableData[19]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[20]['groupData'])).toString();\r\n    //       this.tableData[4]['groupData'] = (this.cellToNumber(this.tableData[5]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[18]['groupData']) + this.cellToNumber(this.tableData[21]['groupData'])).toString();\r\n    //     } else if (index == 21) {\r\n    //       this.tableData[4]['groupData'] = (this.cellToNumber(this.tableData[5]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[18]['groupData']) + this.cellToNumber(this.tableData[21]['groupData'])).toString();\r\n    //     } else if (index == 24 || index == 25) {\r\n    //       this.tableData[23]['groupData'] = (this.cellToNumber(this.tableData[24]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[25]['groupData'])).toString();\r\n    //     } else if (index == 28 || index == 29) {\r\n    //       this.tableData[27]['groupData'] = (this.cellToNumber(this.tableData[28]['groupData']) +\r\n    //         this.cellToNumber(this.tableData[29]['groupData'])).toString();\r\n    //     }\r\n    //     this.countData('groupData');\r\n    //   }\r\n    // },\r\n    // handleStockChange(row,index) {\r\n    //   this.saveChangeIndex(row.stockData,index,'stockData',2,row.id)\r\n    //   if (this.noInputList.stockList.indexOf(index) == -1 && this.formulaList.stockList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['stockData'].toString()) < 0) {\r\n    //       this.tableData[index]['stockData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['stockData'] != undefined && this.tableData[index]['stockData'] != '') {\r\n    //       let j = this.backgroundList.stockList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.stockList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     if (index == 6 || index == 11 || index == 17) {\r\n    //       this.tableData[5]['stockData'] = (this.cellToNumber(this.tableData[6]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[7]['stockData']) + this.cellToNumber(this.tableData[11]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[17]['stockData'])).toString();\r\n    //       this.tableData[4]['stockData'] = (this.cellToNumber(this.tableData[5]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[18]['stockData']) + this.cellToNumber(this.tableData[21]['stockData'])).toString();\r\n    //     } else if (index == 8 || index == 9 || index == 10) {\r\n    //       this.tableData[index]['mediumData'] = this.tableData[index]['stockData'];\r\n    //       this.tableData[index]['mobileData'] = this.tableData[index]['stockData'];\r\n    //       this.tableData[7]['stockData'] = (this.cellToNumber(this.tableData[8]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[9]['stockData']) + this.cellToNumber(this.tableData[10]['stockData'])).toString();\r\n    //       this.tableData[7]['mediumData'] = this.tableData[7]['stockData'];\r\n    //       this.tableData[4]['mediumData'] = this.tableData[7]['stockData'];\r\n    //       this.tableData[7]['mobileData'] = this.tableData[7]['stockData'];\r\n    //       this.tableData[4]['mobileData'] = this.tableData[7]['stockData'];\r\n    //       this.tableData[5]['stockData'] = (this.cellToNumber(this.tableData[6]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[7]['stockData']) + this.cellToNumber(this.tableData[11]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[17]['stockData'])).toString();\r\n    //       this.tableData[4]['stockData'] = (this.cellToNumber(this.tableData[5]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[18]['stockData']) + this.cellToNumber(this.tableData[21]['stockData'])).toString();\r\n    //       this.countData('mediumData');\r\n    //       this.countData('mobileData');\r\n    //     } else if (index > 12 && index < 17) {\r\n    //       this.tableData[index]['mediumData'] = this.tableData[index]['stockData'];\r\n    //       if (index == 13 || index == 15) {\r\n    //         this.tableData[11]['mediumData'] = (this.cellToNumber(this.tableData[13]['stockData']) +\r\n    //           this.cellToNumber(this.tableData[15]['stockData'])).toString();\r\n    //         this.tableData[4]['mediumData'] = this.tableData[11]['mediumData'];\r\n    //         this.countData('mediumData');\r\n    //       } else {\r\n    //         this.tableData[12]['mediumData'] = (this.cellToNumber(this.tableData[14]['stockData']) +\r\n    //           this.cellToNumber(this.tableData[16]['stockData'])).toString();\r\n    //       }\r\n\r\n    //     } else if (index == 19 || index == 20) {\r\n    //       this.tableData[18]['stockData'] = (this.cellToNumber(this.tableData[19]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[20]['stockData'])).toString();\r\n    //       this.tableData[4]['stockData'] = (this.cellToNumber(this.tableData[5]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[18]['stockData']) + this.cellToNumber(this.tableData[21]['stockData'])).toString();\r\n    //     } else if (index == 21) {\r\n    //       this.tableData[4]['stockData'] = (this.cellToNumber(this.tableData[5]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[18]['stockData']) + this.cellToNumber(this.tableData[21]['stockData'])).toString();\r\n    //     } else if (index == 24 || index == 25) {\r\n    //       this.tableData[23]['stockData'] = (this.cellToNumber(this.tableData[24]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[25]['stockData'])).toString();\r\n    //     } else if (index == 28 || index == 29) {\r\n    //       this.tableData[27]['stockData'] = (this.cellToNumber(this.tableData[28]['stockData']) +\r\n    //         this.cellToNumber(this.tableData[29]['stockData'])).toString();\r\n    //     }\r\n    //     this.countData('stockData');\r\n    //   }\r\n    // },\r\n    // handleLargeChange(row,index) {\r\n    //   this.saveChangeIndex(row.largeData,index,'largeData',3,row.id)\r\n    //   if (this.noInputList.largeList.indexOf(index) == -1 && this.formulaList.largeList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['largeData'].toString()) < 0) {\r\n    //       this.tableData[index]['largeData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['largeData'] != undefined && this.tableData[index]['largeData'] != '') {\r\n    //       let j = this.backgroundList.largeList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.largeList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     if (index == 11) {\r\n    //       this.tableData[4]['largeData'] = this.tableData[11]['largeData'];\r\n\r\n    //     } else if (index == 24 || index == 25) {\r\n    //       this.tableData[23]['largeData'] = (this.cellToNumber(this.tableData[24]['largeData']) +\r\n    //         this.cellToNumber(this.tableData[25]['largeData'])).toString();\r\n    //     } else if (index == 28 || index == 29) {\r\n    //       this.tableData[27]['largeData'] = (this.cellToNumber(this.tableData[28]['largeData']) +\r\n    //         this.cellToNumber(this.tableData[29]['largeData'])).toString();\r\n    //     }\r\n    //     this.countData('largeData');\r\n    //   }\r\n    // },\r\n    // handleMediumChange(row,index) {\r\n    //   this.saveChangeIndex(row.mediumData,index,'mediumData',4,row.id)\r\n    //   if (this.noInputList.mediumList.indexOf(index) == -1 && this.formulaList.mediumList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['mediumData'].toString()) < 0) {\r\n    //       this.tableData[index]['mediumData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['mediumData'] != undefined && this.tableData[index]['mediumData'] != '') {\r\n    //       let j = this.backgroundList.mediumList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.mediumList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     if (index == 24 || index == 25) {\r\n    //       this.tableData[23]['mediumData'] = (this.cellToNumber(this.tableData[24]['mediumData']) +\r\n    //         this.cellToNumber(this.tableData[25]['mediumData'])).toString();\r\n    //     } else if (index == 28 || index == 29) {\r\n    //       this.tableData[27]['mediumData'] = (this.cellToNumber(this.tableData[28]['mediumData']) +\r\n    //         this.cellToNumber(this.tableData[29]['mediumData'])).toString();\r\n    //     }\r\n    //     this.countData('mediumData');\r\n    //   }\r\n    // },\r\n    // handleMobileChange(row,index) {\r\n    //   this.saveChangeIndex(row.mobileData,index,'mediumData',5,row.id)\r\n    //   if (this.noInputList.mobileList.indexOf(index) == -1 && this.formulaList.mobileList.indexOf(index) == -1) {\r\n    //     if (parseFloat(this.tableData[index]['mobileData'].toString()) < 0) {\r\n    //       this.tableData[index]['mobileData'] = \"0\";\r\n    //     }\r\n    //     if (this.tableData[index]['mobileData'] != undefined && this.tableData[index]['mobileData'] != '') {\r\n    //       let j = this.backgroundList.mobileList.indexOf(index);\r\n    //       if (j > -1) {\r\n    //         this.backgroundList.mobileList.splice(j, 1);\r\n    //       }\r\n    //     }\r\n    //     this.countData('mobileData')\r\n    //   }\r\n    // },\r\n    countData(label) {\r\n      this.tableData[0][label] = (Math.round((\r\n          this.cellToNumber(this.tableData[1][label]) * this.tableData[1].coefficient +\r\n          this.cellToNumber(this.tableData[3][label]) * this.tableData[3].coefficient +\r\n          this.cellToNumber(this.tableData[4][label]) * this.tableData[4].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[21][label]) * this.tableData[21].coefficient +\r\n          this.cellToNumber(this.tableData[22][label]) * this.tableData[22].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[25][label]) * this.tableData[25].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[26][label]) * this.tableData[26].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[29][label]) * this.tableData[29].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[30][label]) * this.tableData[30].coefficient +\r\n          this.cellToNumber(this.tableData[31][label]) * this.tableData[31].coefficient / 1000 +\r\n          this.cellToNumber(this.tableData[32][label]) * this.tableData[32].coefficient +\r\n          this.cellToNumber(this.tableData[33][label]) +\r\n          this.cellToNumber(this.tableData[34][label]) * this.tableData[34].coefficient / 1000 -\r\n          this.cellToNumber(this.tableData[35][label])\r\n      ) * 1000000) / 1000000).toString();\r\n    },\r\n    //根据指标名称确定输入框状态\r\n    checkIndicatorInput() {\r\n      Object.keys(this.disabledList).forEach(key => this.disabledList[key] = []);\r\n      Object.keys(this.formulaList).forEach(key => this.formulaList[key] = []);\r\n      Object.keys(this.noInputList).forEach(key => this.noInputList[key] = []);\r\n      Object.keys(this.requireInputList).forEach(key => this.requireInputList[key] = []);\r\n      Object.keys(this.noRequireInputList).forEach(key => this.noRequireInputList[key] = []);\r\n      for (let i = 0; i < this.tableData.length; i ++) {\r\n        if ('1、能源消费总量(吨标煤)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          Object.keys(this.formulaList).forEach(key => this.formulaList[key].push(i));\r\n        } else if ('1.1、 煤炭(吨)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.requireInputList).forEach(key => this.requireInputList[key].push(i));\r\n        } else if ('1.1.1、其中发电用煤(吨)' == this.tableData[i].indicatorName) {\r\n          this.disabledList.mobileList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n          Object.keys(this.requireInputList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.requireInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.2、焦炭(吨)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.requireInputList).forEach(key => this.requireInputList[key].push(i));\r\n        } else if ('1.3、耗电量（总）(千瓦时)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          Object.keys(this.formulaList).forEach(key => this.formulaList[key].push(i));\r\n        } else if ('1.3.1、生产用房耗电量(千瓦时)' == this.tableData[i].indicatorName) {\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          this.formulaList.groupList.push(i);\r\n          this.formulaList.stockList.push(i);\r\n          this.noInputList.largeList.push(i);\r\n          this.noInputList.mediumList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n        } else if ('1.3.1.1、其中：通信机房耗电量(千瓦时)' == this.tableData[i].indicatorName) {\r\n          this.disabledList.largeList.push(i);\r\n          this.disabledList.mediumList.push(i);\r\n          this.disabledList.mobileList.push(i);\r\n          this.noInputList.largeList.push(i);\r\n          this.noInputList.mediumList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n          Object.keys(this.requireInputList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.requireInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.3.1.2、其中：基站耗电量(千瓦时)' == this.tableData[i].indicatorName) {  //7\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          Object.keys(this.formulaList).forEach(key => {\r\n            if (key != 'largeList' && key != 'mediumList') {\r\n              this.formulaList[key].push(i);\r\n            }\r\n          });\r\n          this.noInputList.largeList.push(i);\r\n          this.noInputList.mediumList.push(i);\r\n        } else if ('1.3.1.2.1、其中：铁塔公司基站耗电量（包括室内分布、室外站等） (千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.1.2.2、其中：第三方租赁基站耗电量（包括室内分布、室外站等） (千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.1.2.3、其中：自有产权基站耗电量（包括室内分布、室外站等） (千瓦时)' == this.tableData[i].indicatorName) { //8 - 10\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          this.formulaList.mobileList.push(i);\r\n          this.noInputList.largeList.push(i);\r\n          this.noInputList.mediumList.push(i);\r\n          this.requireInputList.groupList.push(i);\r\n          this.requireInputList.stockList.push(i);\r\n        } else if ('1.3.1.3、其中：数据中心耗电量(千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.1.3.1、其中：数据中心IT设备总耗电量（千瓦时）' == this.tableData[i].indicatorName||\r\n          '1.3.1.3.2、其中：对外IDC机房耗电量(千瓦时)' == this.tableData[i].indicatorName||\r\n          '1.3.1.3.2.1、其中：对外IDC机房IT设备耗电量（千瓦时)' == this.tableData[i].indicatorName||\r\n          '1.3.1.3.3、其中：自用业务平台和IT支撑用房耗电量(千瓦时)' == this.tableData[i].indicatorName||\r\n          '1.3.1.3.3.1、其中：自用业务平台IT设备耗电量（千瓦时）' == this.tableData[i].indicatorName) { //11 - 16\r\n          this.disabledList.mediumList.push(i);\r\n          this.disabledList.mobileList.push(i);\r\n          this.formulaList.mediumList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n          Object.keys(this.requireInputList).forEach(key => {\r\n            if (key != 'mediumList' && key != 'mobileList') {\r\n              this.requireInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.3.1.4、其中：接入局所及室外机柜耗电量(千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.2.1、其中：管理用房耗电量(千瓦时)' == this.tableData[i].indicatorName ||\r\n          '1.3.2.2、其中：渠道用房耗电量(千瓦时)' == this.tableData[i].indicatorName ||\r\n          '2.5、废水排放量(吨)' == this.tableData[i].indicatorName) { //17 19-20 38\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noInputList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.noInputList[key].push(i);\r\n            }\r\n          });\r\n          this.requireInputList.groupList.push(i);\r\n          this.requireInputList.stockList.push(i);\r\n        } else if ('1.3.2、非生产用房耗电量(千瓦时)' == this.tableData[i].indicatorName) { //18\r\n          Object.keys(this.disabledList).forEach(key => this.disabledList[key].push(i));\r\n          this.formulaList.groupList.push(i);\r\n          this.formulaList.stockList.push(i);\r\n          Object.keys(this.noInputList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.noInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.3.3、 可再生能源使用量(千瓦时)（仅限填写自发自用绿电）' == this.tableData[i].indicatorName ||\r\n          '1.4、原油(吨)' == this.tableData[i].indicatorName) { //21 22\r\n          Object.keys(this.requireInputList).forEach(key => this.requireInputList[key].push(i));\r\n        } else if ('1.5、汽油消耗量(升)' == this.tableData[i].indicatorName) { //23\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.formulaList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.formulaList[key].push(i);\r\n            }\r\n          });\r\n          this.requireInputList.mobileList.push(i);\r\n        } else if ('1.5.1、其中：移动源（升）' == this.tableData[i].indicatorName ||\r\n          '1.5.2、其中：固定源（升）' == this.tableData[i].indicatorName ||\r\n          '1.7.1、其中：移动源（升）' == this.tableData[i].indicatorName ||\r\n          '1.7.2、其中：固定源（升）' == this.tableData[i].indicatorName) { //24 25 28 29\r\n          this.disabledList.mobileList.push(i);\r\n          this.noInputList.mobileList.push(i);\r\n          Object.keys(this.requireInputList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.requireInputList[key].push(i);\r\n            }\r\n          });\r\n        } else if ('1.6、煤油(升)' == this.tableData[i].indicatorName ||\r\n          '1.8、燃料油(升)' == this.tableData[i].indicatorName ||\r\n          '1.9、液化石油气(吨）' == this.tableData[i].indicatorName ||\r\n          '2.0、天然气消耗量(立方米)' == this.tableData[i].indicatorName ||\r\n          '2.1、热力(十亿焦)' == this.tableData[i].indicatorName ||\r\n          '2.2、 其他能源(吨标准煤)' == this.tableData[i].indicatorName ||\r\n          '2.3、新水用量(吨)' == this.tableData[i].indicatorName ||\r\n          '2.4、节能量(吨标准煤)' == this.tableData[i].indicatorName ||\r\n          '2.4.1、其中：节电量(千瓦时)' == this.tableData[i].indicatorName) { //26  30-37\r\n          Object.keys(this.requireInputList).forEach(key => this.requireInputList[key].push(i));\r\n        } else if ('1.7、柴油消耗量(升)' == this.tableData[i].indicatorName) { //27\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.formulaList).forEach(key => {\r\n            if (key != 'mobileList') {\r\n              this.formulaList[key].push(i);\r\n            }\r\n          });\r\n          this.requireInputList.mobileList.push(i);\r\n        } else if ('2.6、一般固体废物产生量(吨)' == this.tableData[i].indicatorName ||\r\n          '2.7、一般固体废物综合利用量(吨)' == this.tableData[i].indicatorName ||\r\n          '2.8、综合利用往年贮存量(吨)' == this.tableData[i].indicatorName ||\r\n          '2.9、危险废物产生量(吨)' == this.tableData[i].indicatorName ||\r\n          '3.0、危险废物处置量(吨)' == this.tableData[i].indicatorName ||\r\n          '3.1、处置往年贮存量(吨)' == this.tableData[i].indicatorName ||\r\n          '3.2、土壤污染治理面积(公顷)' == this.tableData[i].indicatorName ||\r\n          '3.3、土壤污染需要治理面积(公顷)' == this.tableData[i].indicatorName ||\r\n          '3.4、矿山（或生态）修复治理面积(公顷)' == this.tableData[i].indicatorName ||\r\n          '3.5、矿山（或生态）需要修复治理面积(公顷)' == this.tableData[i].indicatorName ||\r\n          '3.6、废气治理设施数(套)' == this.tableData[i].indicatorName ||\r\n          '3.7、废气治理设施处理能力(立方米/月)' == this.tableData[i].indicatorName ||\r\n          '3.8、废水治理设施数(套)' == this.tableData[i].indicatorName ||\r\n          '3.9、废水治理设施处理能力(吨/月)' == this.tableData[i].indicatorName ||\r\n          '4.0、生态环境污染源(个)' == this.tableData[i].indicatorName ||\r\n          '4.1、生态环境风险点（个）' == this.tableData[i].indicatorName ||\r\n          '4.2、节能投入(元)' == this.tableData[i].indicatorName ||\r\n          '4.3、环保投入(元)' == this.tableData[i].indicatorName) { //39-56\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noInputList).forEach(key => {\r\n            if (key != 'groupList' && key != 'stockList') {\r\n              this.noInputList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noRequireInputList).forEach(key => this.noRequireInputList[key].push(i));\r\n        } else if ('4.4、数据中心标准机架数量' == this.tableData[i].indicatorName) { //57\r\n          Object.keys(this.disabledList).forEach(key => {\r\n            if (key != 'largeList' && key != 'mediumList') {\r\n              this.disabledList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noInputList).forEach(key => {\r\n            if (key != 'largeList' && key != 'mediumList') {\r\n              this.noInputList[key].push(i);\r\n            }\r\n          });\r\n          Object.keys(this.noRequireInputList).forEach(key => this.noRequireInputList[key].push(i));\r\n        }\r\n      }\r\n    },\r\n    handleImportExcel(file, fileList) {\r\n      try{\r\n        let tableField = ['上报指标', '集团', '股份', '大型', '中小型', '移动业务'];\r\n        let m = this;\r\n        importExcel(file, tableField).then(res => {\r\n          if (res.length > this.tableData.length) {\r\n            for (let i = 0; i < this.tableData.length; i ++) {\r\n              if (this.tableData[i].indicatorName == res[i+1][\"上报指标\"].trim()) {\r\n                if (this.checkExcelLineData(res[i+1], i) == true) {\r\n                  this.tableData[i].groupData = res[i + 1]['集团'].toString();\r\n                  this.tableData[i].stockData = res[i + 1]['股份'].toString();\r\n                  this.tableData[i].largeData = res[i + 1]['大型'].toString();\r\n                  this.tableData[i].mediumData = res[i + 1]['中小型'].toString();\r\n                  this.tableData[i].mobileData = res[i + 1]['移动业务'].toString();\r\n                } else {\r\n                  m.alertError(\"第\" + Math.floor(i + 3) + \"行有非法数据数据，请检查文档！\");\r\n                  return;\r\n                }\r\n              } else {\r\n                m.alertError(\"第\" + Math.floor(i + 3) + \"行上报指标名称错误，请参考填报模板！\");\r\n                return;\r\n              }\r\n            }\r\n          } else {\r\n            m.alertError(\"数据行数过少，请参考填报模板！\");\r\n            return;\r\n          }\r\n        })\r\n      }catch(exception){             //抓住throw抛出的错误\r\n        this.alertError(\"数据格式错误\");\r\n      }\r\n    },\r\n    checkExcelLineData(data, line) {\r\n      let ret = true;\r\n      if (data['集团'] == undefined || data['集团'] == null\r\n        || data['股份'] == undefined || data['股份'] == null\r\n        || data['大型'] == undefined || data['大型'] == null\r\n        || data['中小型'] == undefined || data['中小型'] == null\r\n        || data['移动业务'] == undefined || data['移动业务'] == null) {\r\n        ret = false;\r\n        return ret;\r\n      } else {\r\n        if (this.noInputList.groupList.indexOf(line) == -1) {\r\n          let groupData = parseFloat(data['集团']);\r\n          if (isNaN(groupData) || groupData == null || groupData < 0) {\r\n            // if (isNaN(groupData) || groupData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.noInputList.stockList.indexOf(line) == -1) {\r\n          let stockData = parseFloat(data['股份']);\r\n          if (isNaN(stockData) || stockData == null || stockData < 0) {\r\n            // if (isNaN(stockData) || stockData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.noInputList.largeList.indexOf(line) == -1) {\r\n          let largeData = parseFloat(data['大型']);\r\n          if (isNaN(largeData) || largeData == null || largeData < 0) {\r\n            // if (isNaN(largeData) || largeData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.noInputList.mediumList.indexOf(line) == -1) {\r\n          let mediumData = parseFloat(data['中小型']);\r\n          if (isNaN(mediumData) || mediumData == null || mediumData < 0) {\r\n            // if (isNaN(mediumData) || mediumData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n        if (this.noInputList.mobileList.indexOf(line) == -1) {\r\n          let mobileData = parseFloat(data['移动业务']);\r\n          if (isNaN(mobileData) || mobileData == null || mobileData < 0) {\r\n            // if (isNaN(mobileData) || mobileData == null) {\r\n            ret = false;\r\n            return ret;\r\n          }\r\n        }\r\n      }\r\n      return ret;\r\n    },\r\n    cellToNumber(cell) {\r\n      let num = 0;\r\n      if (cell == undefined || cell == null)\r\n      {\r\n        num = 0;\r\n      } else {\r\n        num = Math.round(parseFloat(cell)*1000000)/1000000;\r\n        if (isNaN(num) || num == null) {\r\n          num = 0;\r\n        }\r\n      }\r\n      return num;\r\n    },\r\n    // 判断两个值是否相等\r\n    areFloatsEqual(float1, float2) {\r\n    // 定义一个非常小的误差阈值\r\n    const epsilon = 0.000001; // 适当调整此值以满足你的需求\r\n\r\n    // 计算两个浮点数的差值\r\n    const difference = Math.abs(float1 - float2);\r\n\r\n    // 如果差值小于误差阈值，就认为两个浮点数相等\r\n    return difference < epsilon;\r\n    } ,\r\n    getObj(item) {\r\n      item.show = !item.show;\r\n      if(item.operateType == 1){\r\n        this.getList(item)\r\n      } else {\r\n        this.getEnergyUpdateRecordList(item)\r\n      }\r\n      // if(item.isShow) {\r\n      //   item.names = '收起数据';\r\n      // }else {\r\n      //   item.names = '查看数据';\r\n      // }\r\n    },\r\n    formatDisplayData(value) {\r\n      let data = parseFloat(value);\r\n      if (isNaN(value)) {\r\n        return value;\r\n      } else {\r\n        return data.toFixed(2);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n/deep/.el-table {\r\n  height: auto!important;\r\n  //$properties: max-height;\r\n  //@each $prop in $properties {\r\n  //  #{$prop}: unset!important;\r\n  //}\r\n  & > .el-table__body-wrapper {\r\n    height: auto!important;\r\n  }\r\n}\r\n#modularForm_bg::-webkit-scrollbar {\r\n    // display: none;\r\n    width: 2px;\r\n  }\r\n\r\n/* 滚动条轨道 */\r\n#modularForm_bg::-webkit-scrollbar-track {\r\n  background: #f1f1f1; /* 设置滚动条轨道的背景色 */\r\n}\r\n\r\n/* 滚动条滑块 */\r\n#modularForm_bg::-webkit-scrollbar-thumb {\r\n  background: #888; /* 设置滚动条滑块的背景色 */\r\n}\r\n\r\n/* 滚动条滑块在鼠标悬停时的样式 */\r\n#modularForm_bg::-webkit-scrollbar-thumb:hover {\r\n  background: #555; /* 设置鼠标悬停时滚动条滑块的背景色 */\r\n}\r\n\r\n/deep/.cell-color {\r\n  color:red !important;\r\n  cursor: pointer;\r\n}\r\n\r\n.foot_btn {\r\n  width: 88%;\r\n  height: 7vh;\r\n  position: fixed;\r\n  bottom: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #111d30;\r\n}\r\n\r\np {\r\n  margin: 0;\r\n}\r\n/deep/.avue-crud__menu {\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n.curd-header {\r\n  width: 97.6%;\r\n  height: 5vh;\r\n  margin: 2vh auto 0;\r\n  display: flex;\r\n  background: #12537a;\r\n  position: relative;\r\n  border-bottom: 0.1rem solid #04223b;\r\n  justify-content: space-between;\r\n  & > .reporting {\r\n    // width: 69rem;\r\n    display: flex;\r\n    margin-left: 1rem;\r\n    align-items: center;\r\n    p {\r\n      font-size: 1.4rem;\r\n      line-height: 5vh;\r\n      margin-right: 1rem;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n    /deep/.el-date-editor.el-input,\r\n    .el-date-editor.el-input__inner {\r\n      width: 20rem !important;\r\n    }\r\n  }\r\n  & > .companyName {\r\n    margin-right: 12rem;\r\n    font-size: 1.4rem;\r\n    line-height: 5vh;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #00ecc0;\r\n  }\r\n}\r\n\r\n.el_input_class {\r\n  /deep/.el-input__inner{\r\n    background: red;\r\n  }\r\n}\r\n.el-input.is-disabled /deep/ .el-input__inner {\r\n  background: #626f7a !important;\r\n}\r\n::v-deep input::-webkit-outer-spin-button,\r\n::v-deep input::-webkit-inner-spin-button {\r\n  -webkit-appearance: none !important;\r\n}\r\n::v-deep input[type='number'] {\r\n  -moz-appearance: textfield !important;\r\n}\r\n</style>\r\n"]}]}