{"remainingRequest": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\changeAmmeter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\src\\view\\basedata\\ammeter\\changeAmmeter.vue", "mtime": 1754285403017}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\cl-project\\ln-nenghao\\brch-ln-vue\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRzdGF0aW9ub2xkIH0gZnJvbSAiQC9hcGkvYWxlcnRjb250cm9sL2FsZXJ0Y29udHJvbCI7DQppbXBvcnQgew0KICBsaXN0RWxlY3RyaWNUeXBlLA0KICBjaGVja0FtbWV0ZXJFeGlzdCwNCiAgZ2V0Q291bnRyeXNkYXRhLA0KICBhZGRBbW1ldGVyLA0KICBlZGl0QW1tZXRlciwNCiAgZWRpdEFtbWV0ZXJSZWNvcmQsDQogIHVwZGF0ZUFtbWV0ZXIsDQogIGNoZWNrUHJvamVjdE5hbWVFeGlzdCwNCiAgY2hlY2tBbW1ldGVyQnlTdGF0aW9uLA0KICBnZXRDbGFzc2lmaWNhdGlvbiwNCiAgZ2V0Q2xhc3NpZmljYXRpb25JZCwNCiAgZ2V0VXNlcmRhdGEsDQogIGNoZWNrQ2xhc3NpZmljYXRpb25MZXZlbCwNCiAgbGlzdEVsZWN0cmljVHlwZVJhdGlvLA0KICBjaGVja0Fjb3VudEJ5VXBkYXRlLA0KICBnZXRVc2VyQnlVc2VyUm9sZSwNCiAgZ2V0Q291bnRyeUJ5VXNlcklkLA0KICBhdHRjaExpc3QsDQogIHJlbW92ZUF0dGFjaCwNCiAgZ2V0Q2hhbmdlQW1tZXRlcmlkLA0KfSBmcm9tICJAL2FwaS9iYXNlZGF0YS9hbW1ldGVyLmpzIjsNCmltcG9ydCB7IGJsaXN0LCBidGV4dCB9IGZyb20gIkAvbGlicy90b29scyI7DQppbXBvcnQgU2VsZWN0RWxlY3RyaWNUeXBlIGZyb20gIi4vc2VsZWN0RWxlY3RyaWNUeXBlIjsNCmltcG9ydCBjb3VudHJ5TW9kYWwgZnJvbSAiLi9jb3VudHJ5TW9kYWwiOw0KaW1wb3J0IHN0YXRpb25Nb2RhbCBmcm9tICIuL3N0YXRpb25Nb2RhbCI7DQppbXBvcnQgeyBtYXBNdXRhdGlvbnMgfSBmcm9tICJ2dWV4IjsNCmltcG9ydCBXb3JrRmxvd0luZm9Db21wb25ldCBmcm9tICJAL3ZpZXcvYmFzaWMvc3lzdGVtL3dvcmtmbG93L3dvcmtGbG93SW5mb0NvbXBvbmV0IjsNCmltcG9ydCBBbW1ldGVyUHJvdG9jb2xMaXN0IGZyb20gIkAvdmlldy9iYXNlZGF0YS9xdW90YS9saXN0QW1tZXRlclByb3RvY29sIjsNCmltcG9ydCBjdXN0b21lckxpc3QgZnJvbSAiLi9jdXN0b21lck1vZGFsIjsNCmltcG9ydCBDaG9vc2VBbW1ldGVyTW9kZWwgZnJvbSAiQC92aWV3L2Jhc2VkYXRhL2FtbWV0ZXIvY2hvb3NlQW1tZXRlck1vZGVsIjsNCmltcG9ydCBheGlvcyBmcm9tICJAL2xpYnMvYXBpLnJlcXVlc3QiOw0KaW1wb3J0IHsgaXNFbXB0eSB9IGZyb20gIkAvbGlicy92YWxpZGF0ZSI7DQppbXBvcnQgYXR0YWNoRmlsZSBmcm9tICJAL3ZpZXcvYmFzZWRhdGEvYW1tZXRlci9hdHRhY2hGaWxlIjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogImNoYW5nZTFBbW1ldGVyIiwNCiAgY29tcG9uZW50czogew0KICAgIHN0YXRpb25Nb2RhbCwNCiAgICBjdXN0b21lckxpc3QsDQogICAgY291bnRyeU1vZGFsLA0KICAgIFNlbGVjdEVsZWN0cmljVHlwZSwNCiAgICBXb3JrRmxvd0luZm9Db21wb25ldCwNCiAgICBBbW1ldGVyUHJvdG9jb2xMaXN0LA0KICAgIENob29zZUFtbWV0ZXJNb2RlbCwNCiAgICBhdHRhY2hGaWxlLA0KICB9LA0KICBkYXRhKCkgew0KICAgIC8v5LiN6IO96L6T5YWl5rGJ5a2XDQogICAgY29uc3QgY2hlY2tEYXRhID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgaWYgKHZhbHVlKSB7DQogICAgICAgIGlmICgvW1x1NEUwMC1cdTlGQTVdL2cudGVzdCh2YWx1ZSkpIHsNCiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIue8lueggeS4jeiDvei+k+WFpeaxieWtlyEiKSk7DQogICAgICAgIH0gZWxzZSBpZiAoZXNjYXBlKHZhbHVlKS5pbmRleE9mKCIldSIpID49IDApIHsNCiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIue8lueggeS4jeiDvei+k+WFpeS4reaWh+Wtl+espiEiKSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9Ow0KICAgIGNvbnN0IHZhbGlkYXRvck51bWJlciA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCiAgICAgIGlmICh2YWx1ZS5sZW5ndGggPD0gMCkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuS4jeiDveS4uuepuiIpKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICB9DQogICAgfTsNCiAgICBjb25zdCB2YWxpZGF0b3JOdW1iZXJaZXJvID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgaWYgKHZhbHVlICE9IG51bGwgJiYgdmFsdWUgPT0gMCkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuWPquiDvei+k+WFpeWkp+S6jjDnmoTmlbAiKSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjYWxsYmFjaygpOw0KICAgICAgfQ0KICAgIH07DQogICAgY29uc3QgdmFsaWRhdG9yTnVtYmVyWmVybzEgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBpZiAodmFsdWUgIT0gbnVsbCAmJiB2YWx1ZSA8IDApIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLlj6rog73ovpPlhaXlpKfkuo7nrYnkuo4w55qE5pWwIikpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgIH0NCiAgICB9Ow0KICAgIGNvbnN0IHZhbGlkYXRlQ2xhc3NpZmljYXRpb25zID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgaWYgKHZhbHVlID09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PSBudWxsKSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi5LiN6IO95Li656m6IikpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaWYgKHZhbHVlLmxlbmd0aCA8PSAwKSB7DQogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLkuI3og73kuLrnqboiKSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGNhbGxiYWNrKCk7DQogICAgfTsNCiAgICBjb25zdCB2YWxpZGF0ZWx1bXBzdGFydGRhdGUgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBsZXQgZGF0YSA9IHRoaXMuYW1tZXRlcjsNCiAgICAgIGxldCBzdGFydCA9IGRhdGEubHVtcHN0YXJ0ZGF0ZTsNCiAgICAgIGxldCBlbmQgPSBkYXRhLmx1bXBlbmRkYXRlOw0KICAgICAgaWYgKHN0YXJ0ID09IG51bGwpIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLkuI3og73kuLrnqboiKSk7DQogICAgICB9DQogICAgICBpZiAoc3RhcnQgIT0gbnVsbCAmJiBlbmQgIT0gbnVsbCkgew0KICAgICAgICBpZiAoZW5kIDw9IHN0YXJ0KSB7DQogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLljIXlubLotbflp4vml6XmnJ/kuI3og73lpKfkuo7nrYnkuo7miKrmraLml6XmnJ8iKSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGNhbGxiYWNrKCk7DQogICAgfTsNCiAgICBjb25zdCB2YWxpZGF0ZWx1bXBlbmRkYXRlID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLmFtbWV0ZXI7DQogICAgICBsZXQgc3RhcnQgPSBkYXRhLmx1bXBzdGFydGRhdGU7DQogICAgICBsZXQgZW5kID0gZGF0YS5sdW1wZW5kZGF0ZTsNCiAgICAgIGlmIChlbmQgPT0gbnVsbCkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuS4jeiDveS4uuepuiIpKTsNCiAgICAgIH0NCiAgICAgIGlmIChzdGFydCAhPSBudWxsICYmIGVuZCAhPSBudWxsKSB7DQogICAgICAgIGlmIChlbmQgPD0gc3RhcnQpIHsNCiAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuWMheW5suaIquatouaXpeacn+S4jeiDveWwj+S6juetieS6jui1t+Wni+aXpeacnyIpKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9Ow0KICAgIC8v5pu05pS55qCH6aKY5ZCN56ew5Y+K5qC35byPDQogICAgbGV0IHJlbmRlckhlYWRlciA9IChoLCBwYXJhbXMpID0+IHsNCiAgICAgIGxldCB0ID0gaCgNCiAgICAgICAgInNwYW4iLA0KICAgICAgICB7DQogICAgICAgICAgc3R5bGU6IHsNCiAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICJub3JtYWwiLA0KICAgICAgICAgICAgY29sb3I6ICIjZWQ0MDE0IiwNCiAgICAgICAgICAgIGZvbnRTaXplOiAiMTJweCIsDQogICAgICAgICAgICBmb250RmFtaWx5OiAiU2ltU3VuIiwNCiAgICAgICAgICAgIG1hcmdpblJpZ2h0OiAiNHB4IiwNCiAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDEsDQogICAgICAgICAgICBkaXNwbGF5OiAiaW5saW5lLWJsb2NrIiwNCiAgICAgICAgICB9LA0KICAgICAgICB9LA0KICAgICAgICAiKiINCiAgICAgICk7DQogICAgICByZXR1cm4gaCgiZGl2IiwgW3QsIGgoInNwYW4iLCB7fSwgIuaJgOWNoOavlOS+iyglKSIpXSk7DQogICAgfTsNCiAgICByZXR1cm4gew0KICAgICAgcHJvcGVydHlyaWdodDogbnVsbCwgLy/lsYDnq5nkuqfmnYMNCiAgICAgIGlzUmVxdWlyZUZsYWc6IGZhbHNlLCAvL+WxgOermeaYr+WQpuW/heWhqw0KICAgICAgbW9kYWwxOiBmYWxzZSwNCiAgICAgIGNoZWNrU3RhdGlvblR5cGU6IG51bGwsDQogICAgICBpc2NoZWNrU3RhdGlvbjogZmFsc2UsIC8v5piv5ZCm6ZyA6KaB6aqM6K+B5bGA56uZ5Y+q6IO95YWz6IGUNeS4qg0KICAgICAgaXNvbGRjaGVja1N0YXRpb246IG51bGwsIC8v5Yik5pat55So5oi35YWz6IGU5bGA56uZ5rKh5pyJLOm7mOiupOayoeaciQ0KICAgICAgaXNDRENvbXBhbnk6IGZhbHNlLCAvL+aYr+WQpuaYr+aIkOmDveWIhuWFrOWPuA0KICAgICAgY29uZmlnVmVyc2lvbjogbnVsbCwgLy/niYjmnKwNCiAgICAgIHByb3BlcnR5TGlzdDogW10sDQogICAgICBwcm9wZXJ0eVJlYWRvbmx5OiB0cnVlLA0KDQogICAgICB3b3JrRmxvd1BhcmFtczoge30sDQogICAgICBoaXNQYXJhbXM6IHt9LA0KICAgICAgaXNTaG93RmxvdzogZmFsc2UsDQogICAgICBzaG93V29ya0Zsb3c6IGZhbHNlLA0KICAgICAgZmxvd05hbWU6IG51bGwsDQoNCiAgICAgIGlzRXJyb3I6IGZhbHNlLCAvL+eUqOeUteexu+Wei+avlOS+i+mqjOivgQ0KICAgICAgaXNFcnJvcjE6IGZhbHNlLCAvL+eUqOeUteexu+Wei+avlOS+i+mqjOivgQ0KDQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIGlzTG9hZGluZzogbnVsbCwNCg0KICAgICAgc2hvd01vZGVsOiBmYWxzZSwNCiAgICAgIGlzQ2xhc3NpZmljYXRpb246IGZhbHNlLA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgaXNFZGl0QnlDb3VudHJ5OiBmYWxzZSwNCiAgICAgIGlzQ2l0eUFkbWluOiBmYWxzZSwNCiAgICAgIGlzQWRtaW46IGZhbHNlLA0KICAgICAgY2hvb3NlSW5kZXg6IG51bGwsDQogICAgICBlbGVjdHJvUm93TnVtOiBudWxsLCAvL+WFs+iBlOeUqOeUteexu+Wei+eahOW9k+WJjeihjA0KICAgICAgZWxlY3RyaWNUeXBlTW9kZWw6IGZhbHNlLA0KICAgICAgY29tcGFuaWVzOiBbXSwNCiAgICAgIGRlcGFydG1lbnRzOiBbXSwNCiAgICAgIGNsYXNzaWZpY2F0aW9uRGF0YTogW10sIC8v55So55S157G75Z6LDQoNCiAgICAgIG9sZERhdGE6IFtdLA0KICAgICAgb2xkQ2F0ZWdvcnk6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkUGFja2FnZXR5cGU6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkUGF5cGVyaW9kOiAiIiwgLy/ljp/lp4vmlbDmja4NCiAgICAgIG9sZFBheXR5cGU6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkRWxlY3Ryb25hdHVyZTogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRFbGVjdHJvdmFsZW5jZW5hdHVyZTogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRFbGVjdHJvdHlwZTogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRTdGF0dXM6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkUHJvcGVydHk6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkQW1tZXRlcnR5cGU6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgb2xkU3RhdGlvbnN0YXR1czogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRTdGF0aW9udHlwZTogIiIsIC8v5Y6f5aeL5pWw5o2uDQogICAgICBvbGRBbW1ldGVydXNlOiAiIiwgLy/ljp/lp4vmlbDmja4NCiAgICAgIG9sZERpcmVjdHN1cHBseWZsYWc6ICIiLCAvL+WOn+Wni+aVsOaNrg0KICAgICAgYXR0YWNoRGF0YTogW10sDQogICAgICBmaWxlUGFyYW06IHsNCiAgICAgICAgYnVzaUlkOiAiIiwNCiAgICAgICAgYnVzaUFsaWFzOiAi6ZmE5Lu2KOWNj+iurueuoeeQhikiLA0KICAgICAgICBjYXRlZ29yeUNvZGU6ICJmaWxlIiwNCiAgICAgICAgYXJlYUNvZGU6ICJsbiIsDQogICAgICB9LA0KICAgICAgcnVsZVZhbGlkYXRlOiB7DQogICAgICAgIGlzZW50aXR5YW1tZXRlcjogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlLGJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIHByb2plY3RuYW1lOiBbDQogICAgICAgICAgLy/pobnnm67lkI3np7ANCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvdW50cnlOYW1lOiBbDQogICAgICAgICAgLy/miYDlsZ7pg6jpl6gNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvdW50cnk6IFsNCiAgICAgICAgICAvL+aJgOWxnumDqOmXqA0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHR5cGU6ICJudW1iZXIiLCB2YWxpZGF0b3I6IHZhbGlkYXRvck51bWJlciwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvbXBhbnk6IFt7IHJlcXVpcmVkOiB0cnVlLCB2YWxpZGF0b3I6IHZhbGlkYXRvck51bWJlciwgdHJpZ2dlcjogImJsdXIiIH1dLA0KICAgICAgICBwYXl0eXBlOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICB0eXBlOiAibnVtYmVyIiwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSxibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBwYXlwZXJpb2Q6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIHR5cGU6ICJudW1iZXIiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlLGJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGFtbWV0ZXJ1c2U6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIHR5cGU6ICJudW1iZXIiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlLGJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGFtbWV0ZXJ0eXBlOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICB0eXBlOiAibnVtYmVyIiwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSxibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBlbGVjdHJvdmFsZW5jZW5hdHVyZTogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgdHlwZTogIm51bWJlciIsDQogICAgICAgICAgICBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UsYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY2xhc3NpZmljYXRpb25zOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdmFsaWRhdG9yOiB2YWxpZGF0ZUNsYXNzaWZpY2F0aW9ucywgdHJpZ2dlcjogImNoYW5nZSxibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBtYWduaWZpY2F0aW9uOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHlwZTogIm51bWJlciIsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBwYXR0ZXJuOiAvXigoWzEtOV1cZHswLDE0fSl8MCkoXC5cZHswLDJ9KT8kLywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlj6rog73kv53nlZnkuKTkvY3lsI/mlbAiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGRpcmVjdHN1cHBseWZsYWc6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIHR5cGU6ICJudW1iZXIiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlLGJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIHByaWNlOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdHlwZTogIm51bWJlciIsDQogICAgICAgICAgICBwYXR0ZXJuOiAvXigoWzEtOV1cZHswLDE0fSl8MCkoXC5cZHswLDJ9KT8kLywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlj6rog73kv53nlZnkuKTkvY3lsI/mlbAiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIHBhY2thZ2V0eXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHlwZTogIm51bWJlciIsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29udHJhY3RPdGhQYXJ0OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9XSwNCiAgICAgICAgc3RhdGlvbk5hbWU6IFtdLA0KICAgICAgICBzdGF0dXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIHR5cGU6ICJudW1iZXIiLA0KICAgICAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgICAgICB0cmlnZ2VyOiAiY2hhbmdlLGJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIHRlbGVwaG9uZTogW3sgcGF0dGVybjogL14xXGR7MTB9JC8sIG1lc3NhZ2U6ICLmoLzlvI/kuI3mraPnoa4iLCB0cmlnZ2VyOiAiYmx1ciIgfV0sDQogICAgICAgIHBlcmNlbnQ6IFsNCiAgICAgICAgICB7IHR5cGU6ICJudW1iZXIiLCB2YWxpZGF0b3I6IHZhbGlkYXRvck51bWJlclplcm8sIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHBhdHRlcm46IC9eKChbMC05XVxkezAsMTJ9KSkoXC5cZHswLDR9KT8kLywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlj6rog73kv53nlZnlm5vkvY3lsI/mlbAiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGx1bXBzdGFydGRhdGU6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIHR5cGU6ICJkYXRlIiwNCiAgICAgICAgICAgIHZhbGlkYXRvcjogdmFsaWRhdGVsdW1wc3RhcnRkYXRlLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGx1bXBlbmRkYXRlOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsDQogICAgICAgICAgICB2YWxpZGF0b3I6IHZhbGlkYXRlbHVtcGVuZGRhdGUsDQogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgZmVlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgdHlwZTogIm51bWJlciIsIHZhbGlkYXRvcjogdmFsaWRhdG9yTnVtYmVyLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBwYXR0ZXJuOiAvXigoWzEtOV1cZHswLDE0fSl8MCkoXC5cZHswLDJ9KT8kLywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlj6rog73kv53nlZnkuKTkvY3lsI/mlbAiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIHN1cHBseWJ1cmVhdWFtbWV0ZXJjb2RlOiBbXSwNCiAgICAgICAgdHJhbnNkaXN0cmljb21wYW55OiBbXSwNCiAgICAgICAgdm9sdGFnZUNsYXNzOiBbXSwNCiAgICAgIH0sDQogICAgICBlbGVjdHJvOiB7DQogICAgICAgIGNvbHVtbnM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuW6j+WPtyIsDQogICAgICAgICAgICB0eXBlOiAiaW5kZXgiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgdGl0bGU6ICLnlKjnlLXnsbvlnosiLA0KICAgICAgICAgICAga2V5OiAidHlwZU5hbWUiLA0KICAgICAgICAgIH0sDQoNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuaJgOWNoOavlOS+iyglKSIsDQogICAgICAgICAgICBrZXk6ICJyYXRpbyIsDQogICAgICAgICAgICByZW5kZXJIZWFkZXI6IHJlbmRlckhlYWRlciwNCiAgICAgICAgICAgIHJlbmRlcjogKGgsIHBhcmFtcykgPT4gew0KICAgICAgICAgICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICAgICAgICAgIGxldCByYXRpbyA9IHBhcmFtcy5yb3cucmF0aW87DQogICAgICAgICAgICAgIGxldCBpc0Vycm9yMSA9IHBhcmFtcy5yb3cuaWRFcnJvcjE7DQogICAgICAgICAgICAgIGxldCBlcnJvciA9IGgoDQogICAgICAgICAgICAgICAgImxhYmVsIiwNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICBzdHlsZTogew0KICAgICAgICAgICAgICAgICAgICBjb2xvcjogIiNlZDQwMTQiLA0KICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogIjEycHgiLA0KICAgICAgICAgICAgICAgICAgICBmb250RmFtaWx5OiAiU2ltU3VuIiwNCiAgICAgICAgICAgICAgICAgICAgcGFkZGluZ1RvcDogIjZweCIsDQogICAgICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDEsDQogICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICJib2xkIiwNCiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogbnVsbCAhPSByYXRpbyA/ICJub25lIiA6ICJpbmxpbmUtYmxvY2siLA0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICLkuI3og73kuLrnqboiDQogICAgICAgICAgICAgICk7DQogICAgICAgICAgICAgIGxldCBlcnJvcjEgPSBoKA0KICAgICAgICAgICAgICAgICJsYWJlbCIsDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgc3R5bGU6IHsNCiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICIjZWQ0MDE0IiwNCiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICIxMnB4IiwNCiAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogIlNpbVN1biIsDQogICAgICAgICAgICAgICAgICAgIHBhZGRpbmdUb3A6ICI2cHgiLA0KICAgICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAxLA0KICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAiYm9sZCIsDQogICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGlzRXJyb3IxID09IHRydWUgPyAiaW5saW5lLWJsb2NrIiA6ICJub25lIiwNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAi6L6T5YWl5q+U5L6L5LiN5ZCI5qC86KaB5rGCIg0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICBsZXQgcmVzdWx0ID0gaCgiSW5wdXROdW1iZXIiLCB7DQogICAgICAgICAgICAgICAgc3R5bGU6IHsNCiAgICAgICAgICAgICAgICAgIGJvcmRlcjogbnVsbCA9PSByYXRpbyB8fCBpc0Vycm9yMSA9PSB0cnVlID8gIjFweCBzb2xpZCAjZWQ0MDE0IiA6ICIiLA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgcHJvcHM6IHsNCiAgICAgICAgICAgICAgICAgIHZhbHVlOiByYXRpbywNCiAgICAgICAgICAgICAgICAgIG1heDogMTAwLA0KICAgICAgICAgICAgICAgICAgbWluOiAwLjEsDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBvbjogew0KICAgICAgICAgICAgICAgICAgIm9uLWNoYW5nZSI6ICh2KSA9PiB7DQogICAgICAgICAgICAgICAgICAgIGlmICh2ID09IHVuZGVmaW5lZCB8fCB2ID09IG51bGwpIHsNCiAgICAgICAgICAgICAgICAgICAgICB0aGF0LmlzRXJyb3IgPSB0cnVlOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgIHRoYXQuaXNFcnJvciA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIC8v57uZZGF0YemHjeaWsOi1i+WAvA0KICAgICAgICAgICAgICAgICAgICAvLyBsZXQgcmVnID0gL14oPzpbMS05XT9cZHwxMDApJC87DQogICAgICAgICAgICAgICAgICAgIGxldCByZWcgPSAvXi0/KChbMS05XVswLTldKil8KChbMF1cLlxkezEsMn18WzEtOV1bMC05XSpcLlxkezEsMn0pKSkkLzsNCiAgICAgICAgICAgICAgICAgICAgaWYgKHYgIT0gdW5kZWZpbmVkICYmIHYgIT0gbnVsbCAmJiAhcmVnLnRlc3QodikpIHsNCiAgICAgICAgICAgICAgICAgICAgICBwYXJhbXMucm93LmlkRXJyb3IxID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgICB0aGF0LmlzRXJyb3IxID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICBwYXJhbXMucm93LmlkRXJyb3IxID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgdGhhdC5pc0Vycm9yMSA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIHBhcmFtcy5yb3cucmF0aW8gPSB2Ow0KICAgICAgICAgICAgICAgICAgICB0aGF0LmVsZWN0cm8uZGF0YVtwYXJhbXMucm93Ll9pbmRleF0gPSBwYXJhbXMucm93Ow0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgcmV0dXJuIGgoImRpdiIsIFtyZXN1bHQsIGVycm9yLCBlcnJvcjFdKTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogIuWFs+iBlOWxgOermSIsDQogICAgICAgICAgICBrZXk6ICJzdGF0aW9uTmFtZSIsDQogICAgICAgICAgICByZW5kZXI6IChoLCBwYXJhbXMpID0+IHsNCiAgICAgICAgICAgICAgbGV0IHN0YXRpb25OYW1lID0gcGFyYW1zLnJvdy5zdGF0aW9uTmFtZTsNCiAgICAgICAgICAgICAgbGV0IGRpc2FibGVkID0gcGFyYW1zLnJvdy5fZGlzYWJsZWQ7DQogICAgICAgICAgICAgIGlmIChkaXNhYmxlZCAhPSB1bmRlZmluZWQgJiYgZGlzYWJsZWQgPT0gdHJ1ZSkgew0KICAgICAgICAgICAgICAgIHJldHVybiBoKCJJbnB1dCIsIHsNCiAgICAgICAgICAgICAgICAgIHByb3BzOiB7DQogICAgICAgICAgICAgICAgICAgIHZhbHVlOiBzdGF0aW9uTmFtZSwNCiAgICAgICAgICAgICAgICAgICAgcmVhZG9ubHk6IHRydWUsDQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHJldHVybiBoKCJJbnB1dCIsIHsNCiAgICAgICAgICAgICAgICAgIHByb3BzOiB7DQogICAgICAgICAgICAgICAgICAgIHZhbHVlOiBzdGF0aW9uTmFtZSwNCiAgICAgICAgICAgICAgICAgICAgaWNvbjogImlvcy1hcmNoaXZlIiwNCiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICLngrnlh7vlm77moIfpgInmi6kiLA0KICAgICAgICAgICAgICAgICAgICByZWFkb25seTogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICBvbjogew0KICAgICAgICAgICAgICAgICAgICAib24tY2xpY2siOiAodikgPT4gew0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuY2hvb3NlUmVzcG9uc2VDZW50ZXIoMiwgcGFyYW1zLCBwYXJhbXMucm93Ll9pbmRleCk7DQogICAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGRhdGE6IFtdLA0KICAgICAgfSwNCiAgICAgIGFtbWV0ZXI6IHsNCiAgICAgICAgaWQ6IG51bGwsDQogICAgICAgIGNvdW50cnk6IG51bGwsDQogICAgICAgIGNvbXBhbnk6IG51bGwsDQogICAgICAgIGNvdW50cnlOYW1lOiAiIiwNCiAgICAgICAgZWxlY3RyaWNUeXBlczogW10sDQogICAgICAgIGVsZWN0cm86IFtdLA0KICAgICAgICBjbGFzc2lmaWNhdGlvbnM6IFtdLCAvL+eUqOeUteexu+Weiw0KICAgICAgfSwNCiAgICAgIGlzemd6T25seTogZmFsc2UsDQogICAgICBkaXNhYmxlZGlzemd6OiBmYWxzZSwNCiAgICAgIGVsZWN0cmljVHlwZTogew0KICAgICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgICAgZmlsdGVyOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgZm9ybUl0ZW1UeXBlOiAiaW5wdXQiLA0KICAgICAgICAgICAgcHJvcDogIm5hbWUiLA0KICAgICAgICAgICAgbGFiZWw6ICLnlKjnlLXnsbvlnosiLA0KICAgICAgICAgICAgd2lkdGg6IDEwMCwNCiAgICAgICAgICAgIHNpemU6ICJzbWFsbCIsDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29sdW1uczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi5bqP5Y+3IiwNCiAgICAgICAgICAgIHR5cGU6ICJpbmRleCIsDQogICAgICAgICAgICBhbGlnbjogImNlbnRlciIsDQogICAgICAgICAgICB3aWR0aDogNzAsDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aXRsZTogImlkIiwNCiAgICAgICAgICAgIGtleTogImlkIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA4MCwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRpdGxlOiAi55So55S157G75Z6LIiwNCiAgICAgICAgICAgIGtleTogInR5cGVOYW1lIiwNCiAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwNCiAgICAgICAgICAgIHdpZHRoOiA4MCwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgdG90YWw6IDAsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgIH0sDQogICAgICBvbGRBbW1ldGVySWQ6IHVuZGVmaW5lZCwgLy/mjaLooajljp/ooahJRA0KICAgIH07DQogIH0sDQoNCiAgbWV0aG9kczogew0KICAgIC4uLm1hcE11dGF0aW9ucyhbImNsb3NlVGFnIiwgImNsb3NlVGFnQnlOYW1lIl0pLA0KICAgIG9uTW9kYWxPSyh0eXBlKSB7DQogICAgICBjb25zb2xlLmxvZygiQEBAQEBAQEBAQEBAIik7DQogICAgICAvLyB0aGlzLmNoZWNrU3RhdGlvblR5cGUgPSB0eXBlOw0KICAgICAgLy8gaWYodHlwZSA9PSAxKXsNCiAgICAgIC8vICAgICB0aGlzLmlzTG9hZGluZyA9IDE7DQogICAgICAvLyB9ZWxzZXsNCiAgICAgIC8vICAgICB0aGlzLmlzTG9hZGluZyA9IDA7DQogICAgICAvLyB9DQogICAgICAvLyBpZih0aGlzLmxvYWRpbmcgPT0gdHJ1ZSl7DQogICAgICAvLyAgICAgcmV0dXJuIDsNCiAgICAgIC8vIH0NCiAgICAgIC8vIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsZXQgZmxhZyA9IGZhbHNlOw0KICAgICAgbGV0IGZsYWcxID0gZmFsc2U7DQogICAgICAvLyB0aGlzLmFtbWV0ZXIuZWxlY3RyaWNUeXBlcyA9IHRoaXMuZWxlY3Ryby5kYXRhOw0KICAgICAgdGhpcy4kcmVmcy5hbW1ldGVyLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBjb25zb2xlLmxvZygiMTExMTExMTExMSIpOw0KICAgICAgICAgIGZsYWcgPSB0cnVlOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIHRoaXMuJHJlZnMuYW1tZXRlcjEudmFsaWRhdGUoKHZhbGlkMSkgPT4gew0KICAgICAgICBpZiAodmFsaWQxKSB7DQogICAgICAgICAgZmxhZzEgPSB0cnVlOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIGlmIChmbGFnICYmIGZsYWcxICYmICF0aGlzLmlzRXJyb3IgJiYgIXRoaXMuaXNFcnJvcjEpIHsNCiAgICAgICAgdGhpcy5jaGVja0RhdGEodHlwZSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRNZXNzYWdlLmVycm9yKCLpqozor4HmsqHpgJrov4ciKTsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICAvL+mqjOivgeaVsOaNrg0KICAgIGNoZWNrRGF0YSh0eXBlKSB7DQogICAgICBsZXQgdHlwZXMgPSB0aGlzLmFtbWV0ZXIuY2xhc3NpZmljYXRpb25zOw0KICAgICAgdGhpcy5hbW1ldGVyLmVsZWN0cm90eXBlID0gdHlwZXNbdHlwZXMubGVuZ3RoIC0gMV07DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICBpZiAoDQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0dXMgPT09IDEgJiYNCiAgICAgICAgKHRoaXMuY29uZmlnVmVyc2lvbiA9PSAic2MiIHx8IHRoaXMuY29uZmlnVmVyc2lvbiA9PSAiU0MiKQ0KICAgICAgKSB7DQogICAgICAgIC8v5Zyo55So54q25oCB5LiL6aqM6K+B5bGA56uZ5Zyw5Z2A5LiN6IO95Li656m6DQogICAgICAgIGlmICgNCiAgICAgICAgICB0aGlzLmFtbWV0ZXIuc3RhdGlvbmFkZHJlc3MgPT0gbnVsbCB8fA0KICAgICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uYWRkcmVzcyA9PSB1bmRlZmluZWQNCiAgICAgICAgKSB7DQogICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7DQogICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsDQogICAgICAgICAgICBjb250ZW50OiAi5bGA56uZ5Zyw5Z2A5LiN6IO95Li656m677yM6K+35Zyo5bGA56uZ566h55CG57u05oqk6K+l5bGA56uZ5L+h5oGv77yBIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmNoZWNrU3RhdGlvbkVsZWMoKSkgew0KICAgICAgICAvL+mqjOivgeeUqOeUteexu+Wei+WSjOWxgOermeexu+Wei+aYr+WQpuWMuemFjQ0KICAgICAgICBpZiAodGhpcy5jaGVja0VsZWN0cmljVHlwZUl0ZW0oKSkgew0KICAgICAgICAgIC8vIGlmKHRoaXMuY29uZmlnVmVyc2lvbiAhPSAibG4iICYmIHRoaXMuY29uZmlnVmVyc2lvbiAhPSAiTE4iICkgew0KICAgICAgICAgIC8vICAgICBjaGVja0FtbWV0ZXJFeGlzdCh0aGlzLmFtbWV0ZXIuaWQsIHRoaXMuYW1tZXRlci5hbW1ldGVybmFtZSwgMCkudGhlbihyZXMgPT4gey8v6aqM6K+B55S16KGo5piv5ZCm5a2Y5ZyoDQogICAgICAgICAgLy8gICAgICAgICBsZXQgY29kZSA9IHJlcy5kYXRhLmNvZGU7DQogICAgICAgICAgLy8gICAgICAgICBpZiAoY29kZSA9PSAwKSB7DQogICAgICAgICAgLy8gICAgICAgICAgICAgdGhhdC5jaGVja2VkRGF0ZSh0eXBlKTsNCiAgICAgICAgICAvLyAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8gICAgICAgICAgICAgdGhhdC5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgLy8gICAgICAgICB9DQogICAgICAgICAgLy8gICAgIH0pOw0KICAgICAgICAgIC8vIH1lbHNlew0KICAgICAgICAgIHRoYXQuY2hlY2tlZERhdGUodHlwZSk7DQogICAgICAgICAgLy8gfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBjaGVja1N0YXRpb25FbGVjKCkgew0KICAgICAgbGV0IGVsZWN0cm90eXBlID0gdGhpcy5hbW1ldGVyLmVsZWN0cm90eXBlOw0KICAgICAgbGV0IHN0YXRpb250eXBlID0gdGhpcy5hbW1ldGVyLnN0YXRpb250eXBlOw0KICAgICAgaWYgKGVsZWN0cm90eXBlID09PSAxMTEgfHwgZWxlY3Ryb3R5cGUgPT09IDExMiB8fCBlbGVjdHJvdHlwZSA9PT0gMTEzKSB7DQogICAgICAgIGlmIChzdGF0aW9udHlwZSAhPT0gMTAwMDEpIHsNCiAgICAgICAgICB0aGlzLiRNb2RhbC53YXJuaW5nKHsNCiAgICAgICAgICAgIHRpdGxlOiAi5rip6aao5o+Q56S6IiwNCiAgICAgICAgICAgIGNvbnRlbnQ6ICLnlKjnlLXnsbvlnovlkozlsYDnq5nnsbvlnovkuI3ljLnphY3vvIzor7fnoa7orqQiLA0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIGlmIChlbGVjdHJvdHlwZSA9PT0gMTIxIHx8IGVsZWN0cm90eXBlID09PSAxMTIpIHsNCiAgICAgICAgaWYgKHN0YXRpb250eXBlICE9PSAxMDAwMyAmJiBzdGF0aW9udHlwZSAhPT0gMTAwMDQpIHsNCiAgICAgICAgICB0aGlzLiRNb2RhbC53YXJuaW5nKHsNCiAgICAgICAgICAgIHRpdGxlOiAi5rip6aao5o+Q56S6IiwNCiAgICAgICAgICAgIGNvbnRlbnQ6ICLnlKjnlLXnsbvlnovlkozlsYDnq5nnsbvlnovkuI3ljLnphY3vvIzor7fnoa7orqQiLA0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIGlmIChlbGVjdHJvdHlwZSA9PT0gMTMxIHx8IGVsZWN0cm90eXBlID09PSAxMzIgfHwgZWxlY3Ryb3R5cGUgPT09IDEzMykgew0KICAgICAgICBpZiAoc3RhdGlvbnR5cGUgIT09IDEwMDA1KSB7DQogICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7DQogICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsDQogICAgICAgICAgICBjb250ZW50OiAi55So55S157G75Z6L5ZKM5bGA56uZ57G75Z6L5LiN5Yy56YWN77yM6K+356Gu6K6kIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSBpZiAoDQogICAgICAgIGVsZWN0cm90eXBlID09PSAxNDExIHx8DQogICAgICAgIGVsZWN0cm90eXBlID09PSAxNDEyIHx8DQogICAgICAgIGVsZWN0cm90eXBlID09PSAxNDIxIHx8DQogICAgICAgIGVsZWN0cm90eXBlID09PSAxNDIyIHx8DQogICAgICAgIGVsZWN0cm90eXBlID09PSAxNDMxIHx8DQogICAgICAgIGVsZWN0cm90eXBlID09PSAxNDMyDQogICAgICApIHsNCiAgICAgICAgaWYgKHN0YXRpb250eXBlICE9PSAxMDAwMikgew0KICAgICAgICAgIHRoaXMuJE1vZGFsLndhcm5pbmcoew0KICAgICAgICAgICAgdGl0bGU6ICLmuKnppqjmj5DnpLoiLA0KICAgICAgICAgICAgY29udGVudDogIueUqOeUteexu+Wei+WSjOWxgOermeexu+Wei+S4jeWMuemFje+8jOivt+ehruiupCIsDQogICAgICAgICAgfSk7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAodGhpcy5jb25maWdWZXJzaW9uID09ICJzYyIgJiYgdGhpcy5hbW1ldGVyLnN0YXRpb25hZGRyZXNzY29kZSkgew0KICAgICAgICAvL+KAnDUx4oCd5byA5aS06ZOB5aGU56uZ5Z2A57yW56CB5o6n5Yi2DQogICAgICAgIGlmICgNCiAgICAgICAgICBbMTQxMSwgMTQxMl0uaW5jbHVkZXMoZWxlY3Ryb3R5cGUpICYmDQogICAgICAgICAgIXRoaXMuYW1tZXRlci5zdGF0aW9uYWRkcmVzc2NvZGUuc3RhcnRzV2l0aCgiNTEiKQ0KICAgICAgICApIHsNCiAgICAgICAgICB0aGlzLiRNb2RhbC53YXJuaW5nKHsNCiAgICAgICAgICAgIHRpdGxlOiAi5rip6aao5o+Q56S6IiwNCiAgICAgICAgICAgIGNvbnRlbnQ6ICLnlKjnlLXnsbvlnovlkozlsYDnq5nnq5nlnYDnvJbnoIHkuI3ljLnphY0oNTHlvIDlpLTkuLrpk4HloZTnq5nlnYDnvJbnoIEp77yM6K+356Gu6K6kIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiB0cnVlOw0KICAgIH0sDQogICAgb2tNb2RlbCgpIHsNCiAgICAgIC8v5LiN6aqM6K+B5Liq5pWwDQogICAgICB0aGlzLmlzb2xkY2hlY2tTdGF0aW9uID0gZmFsc2U7DQogICAgICB0aGlzLnNhdmVEYXRhKHRoaXMuY2hlY2tTdGF0aW9uVHlwZSk7IC8v5L+d5a2Y5pWw5o2uDQogICAgfSwNCiAgICBjYW5jZWxNb2RlbCgpIHsNCiAgICAgIHRoaXMuaXNvbGRjaGVja1N0YXRpb24gPSBudWxsOw0KICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7IHRpdGxlOiAi5rip6aao5o+Q56S6IiwgY29udGVudDogdGhpcy5lcnJvck1lc3NhZ2UgfSk7DQogICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICB9LA0KICAgIGNoZWNrZWREYXRlKHR5cGUpIHsNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIGNoZWNrUHJvamVjdE5hbWVFeGlzdCh0aGF0Lm9sZEFtbWV0ZXJJZCwgdGhhdC5hbW1ldGVyLnByb2plY3RuYW1lLCAwKS50aGVuKA0KICAgICAgICAocmVzKSA9PiB7DQogICAgICAgICAgLy/pqozor4Hpobnnm67lkI3np7DmmK/lkKblrZjlnKgNCiAgICAgICAgICBsZXQgY29kZSA9IHJlcy5kYXRhLmNvZGU7DQogICAgICAgICAgaWYgKGNvZGUgPT0gMCkgew0KICAgICAgICAgICAgaWYgKA0KICAgICAgICAgICAgICB0aGF0LmFtbWV0ZXIuc3RhdGlvbmNvZGUgIT0gdW5kZWZpbmVkICYmDQogICAgICAgICAgICAgIHRoYXQuYW1tZXRlci5zdGF0aW9uY29kZSAhPSBudWxsICYmDQogICAgICAgICAgICAgICh0aGF0LmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPT0gMTQxMSB8fCB0aGF0LmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPT0gMTQxMikNCiAgICAgICAgICAgICkgew0KICAgICAgICAgICAgICAvL+WIpOaWreaYr+WQpumTgeWhlA0KICAgICAgICAgICAgICBpZiAodGhhdC5wcm9wZXJ0eXJpZ2h0ID09IG51bGwpIHsNCiAgICAgICAgICAgICAgICAvL+WIpOaWreaYr+WQpumTgeWhlA0KICAgICAgICAgICAgICAgIGdldHN0YXRpb25vbGQodGhhdC5hbW1ldGVyLnN0YXRpb25jb2RlKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgICAgIC8v6aqM6K+B6aG555uu5ZCN56ew5piv5ZCm5a2Y5ZyoDQogICAgICAgICAgICAgICAgICB0aGF0LnByb3BlcnR5cmlnaHQgPSByZXMuZGF0YS5wcm9wZXJ0eXJpZ2h0Ow0KICAgICAgICAgICAgICAgICAgaWYgKHRoYXQucHJvcGVydHlyaWdodCAhPSAzKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJE1vZGFsLndhcm5pbmcoew0KICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiAi5rip6aao5o+Q56S6IiwNCiAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiAi55So55S157G75Z6L5ZKM5bGA56uZ57G75Z6L5oiW5Lqn5p2D5LiN5Yy56YWN77yM6K+356Gu6K6kIiwNCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgdGhhdC5pc0NoZWNrU3RhdGlvbih0eXBlKTsNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICBpZiAodGhhdC5wcm9wZXJ0eXJpZ2h0ICE9IDMpIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMuJE1vZGFsLndhcm5pbmcoew0KICAgICAgICAgICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsDQogICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6ICLnlKjnlLXnsbvlnovlkozlsYDnq5nnsbvlnovmiJbkuqfmnYPkuI3ljLnphY3vvIzor7fnoa7orqQiLA0KICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgdGhhdC5pc0NoZWNrU3RhdGlvbih0eXBlKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoYXQuaXNDaGVja1N0YXRpb24odHlwZSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICApOw0KICAgIH0sDQogICAgaXNDaGVja1N0YXRpb24odHlwZSkgew0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgLy/mjaLooaggdHlwZSA9IDMNCiAgICAgIGNoZWNrQW1tZXRlckJ5U3RhdGlvbih7DQogICAgICAgIGlkOiB0aGF0LmFtbWV0ZXIuaWQsDQogICAgICAgIHR5cGU6IDMsDQogICAgICAgIGVsZWN0cm90eXBlOiB0aGF0LmFtbWV0ZXIuZWxlY3Ryb3R5cGUsDQogICAgICAgIHN0YXRpb25jb2RlOiB0aGF0LmFtbWV0ZXIuc3RhdGlvbmNvZGUsDQogICAgICAgIGFtbWV0ZXJ1c2U6IHRoYXQuYW1tZXRlci5hbW1ldGVydXNlLA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGxldCBjb2RlID0gcmVzLmRhdGEuY29kZTsNCiAgICAgICAgaWYgKGNvZGUgPT0gImVycm9yIikgew0KICAgICAgICAgIHRoaXMuZXJyb3JNZXNzYWdlID0gcmVzLmRhdGEubXNnOw0KICAgICAgICAgIGlmICgNCiAgICAgICAgICAgIHRoYXQuaXNvbGRjaGVja1N0YXRpb24gPT0gbnVsbCAmJg0KICAgICAgICAgICAgdGhhdC5hbW1ldGVyLnN0YXRpb250eXBlID09IDEwMDAyICYmDQogICAgICAgICAgICByZXMuZGF0YS5mbGFnNQ0KICAgICAgICAgICkgew0KICAgICAgICAgICAgLy/nvJbovpHmlbDmja7ml7bliKTmlq3mmK/lkKbpgInmi6nlhbPogZTlsYDnq5nvvIzmsqHmnInlhbPogZTlvLnlh7rmmK/lkKblrqTliIYNCiAgICAgICAgICAgIHRoYXQubW9kYWwxID0gdHJ1ZTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhhdC4kTW9kYWwud2FybmluZyh7IHRpdGxlOiAi5rip6aao5o+Q56S6IiwgY29udGVudDogcmVzLmRhdGEubXNnIH0pOw0KICAgICAgICAgICAgdGhhdC5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoYXQuY2hlY2tPdGhlcih0eXBlKTsgLy/kv53lrZjmlbDmja4NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBjaGVja090aGVyKHR5cGUpIHsNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIC8vIGlmKHRoYXQuYW1tZXRlci5hbW1ldGVybmFtZSAhPSB1bmRlZmluZWQgfHwgdGhhdC5hbW1ldGVyLmFtbWV0ZXJuYW1lICE9IG51bGwpew0KICAgICAgLy8gICAgIGNoZWNrQW1tZXRlckV4aXN0KHRoYXQuYW1tZXRlci5pZCwgdGhhdC5hbW1ldGVyLmFtbWV0ZXJuYW1lLDApLnRoZW4ocmVzID0+IHsvL+mqjOivgeeUteihqOaYr+WQpuWtmOWcqA0KICAgICAgLy8gICAgICAgICBsZXQgY29kZSA9IHJlcy5kYXRhLmNvZGU7DQogICAgICAvLyAgICAgICAgIGlmIChjb2RlID09IDApIHsNCiAgICAgIC8vICAgICAgICAgICAgIHRoYXQuY2hlY2tlZEZpbGVzKHR5cGUpOw0KICAgICAgLy8gICAgICAgICB9ZWxzZXsNCiAgICAgIC8vICAgICAgICAgICAgIHRoYXQubG9hZGluZyA9IGZhbHNlOw0KICAgICAgLy8gICAgICAgICB9DQogICAgICAvLyAgICAgfSk7DQogICAgICAvLyB9ZWxzZXsNCiAgICAgIHRoYXQuY2hlY2tlZEZpbGVzKHR5cGUpOw0KICAgICAgLy8gfQ0KICAgIH0sDQogICAgY2hlY2tlZEZpbGVzKHR5cGUpIHsNCiAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgIHRoYXQuYW1tZXRlci50eXBlID0gMDsNCiAgICAgIGlmICh0aGF0LmF0dGFjaERhdGEubGVuZ3RoICE9IDApIHsNCiAgICAgICAgdGhhdC5hbW1ldGVyLmlzQXR0YWNoID0gMTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoYXQuYW1tZXRlci5pc0F0dGFjaCA9IDA7DQogICAgICB9DQogICAgICB0aGF0LnNhdmVEYXRhKHR5cGUpOyAvL+S/neWtmOaVsOaNrg0KICAgICAgLy8gaWYgKHRoYXQuYXR0YWNoRGF0YS5sZW5ndGggIT0gMCAmJiB0aGF0Lm11bHRpRmlsZXMubGVuZ3RoICE9IDApIHsNCiAgICAgIC8vICAgICBpZih0aGF0LnVwbG9hZCgpICE9IGZhbHNlKXsNCiAgICAgIC8vICAgICAgICAgdGhhdC5zYXZlRGF0YSh0eXBlKTsvL+S/neWtmOaVsOaNrg0KICAgICAgLy8gICAgIH07DQogICAgICAvLyB9ZWxzZXsNCiAgICAgIC8vICAgICB0aGF0LnNhdmVEYXRhKHR5cGUpOy8v5L+d5a2Y5pWw5o2uDQogICAgICAvLyB9DQogICAgfSwNCiAgICBzYXZlRGF0YSh0eXBlKSB7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICB0aGlzLmNsZWFyRGF0YUJ5Q29uZGl0aW9uKCk7DQogICAgICB0aGF0LmFtbWV0ZXIuY2F0ZWdvcnkgPSAxOyAvL+eUteihqA0KICAgICAgYWRkQW1tZXRlcih0aGF0LmFtbWV0ZXIpDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLmRhdGEgIT0gbnVsbCAmJiByZXMuZGF0YSAhPSAtMSAmJiByZXMuZGF0YS5zdWNjZXNzID09ICIxIikgew0KICAgICAgICAgICAgaWYgKHR5cGUgPT0gMSkgew0KICAgICAgICAgICAgICB0aGF0LnN0YXJ0RmxvdyhyZXMuZGF0YSk7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLmNsb3NlVGFnKHsgcm91dGU6IHRoaXMuJHJvdXRlIH0pOw0KICAgICAgICAgICAgICB0aGF0Lndhcm4oKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhhdC5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICB0aGF0LiROb3RpY2UuZXJyb3IoeyB0aXRsZTogIuaPkOekuiIsIGRlc2M6IHJlcy5kYXRhLm1zZywgZHVyYXRpb246IDEwIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnIpID0+IHsNCiAgICAgICAgICB0aGF0LmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICBjb25zb2xlLmxvZyhlcnIpOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8v5qC55o2u5p2h5Lu25Yik5pat5pWw5o2u5piv5ZCm6K+l5riF6ZmkDQogICAgY2xlYXJEYXRhQnlDb25kaXRpb24oKSB7DQogICAgICBpZiAodGhpcy5hbW1ldGVyLnByb3BlcnR5ICE9PSAyICYmIHRoaXMuYW1tZXRlci5wcm9wZXJ0eSAhPT0gNCkgew0KICAgICAgICAvL+ermeWdgOS6p+adg+W9kuWxnuS4uumTgeWhlCDmuIXpmaTliIblibLmr5TkvotjaGVja0FtbWV0ZXJCeVN0YXRpb27vvIzmmK/lkKbpk4HloZTmjIlSUlXljIXlubINCiAgICAgICAgdGhpcy5hbW1ldGVyLnBlcmNlbnQgPSBudWxsOw0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuYW1tZXRlci5hbW1ldGVydXNlICE9PSAzKSB7DQogICAgICAgIC8v55S16KGo55So6YCU5LiN5piv5Zue5pS255S16LS577yM5riF6Zmk54i255S16KGo5L+h5oGvDQogICAgICAgIHRoaXMuYW1tZXRlci5wYXJlbnRJZCA9IG51bGw7DQogICAgICAgIHRoaXMuYW1tZXRlci5jdXN0b21lcklkID0gbnVsbDsNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmFtbWV0ZXIuZGlyZWN0c3VwcGx5ZmxhZyAhPSAxKSB7DQogICAgICAgIC8v5Y+q5pyJ5a+55aSW57uT566X57G75Z6L5Li655u05L6b55S15omN5aGr5YaZ6K+l5a2X5q6177yM6L2s5L6b55S15LiN6ZyA5aGr5YaZDQogICAgICAgIHRoaXMuYW1tZXRlci5lbGVjdHJvdmFsZW5jZW5hdHVyZSA9IG51bGw7DQogICAgICB9DQogICAgICBpZiAoIXRoaXMuaXNDRENvbXBhbnkpIHsNCiAgICAgICAgLy/miJDpg73liIblhazlj7jmmL7npLrlkIjlkIzlr7nmlrnnrYnvvIzkuI3mmK/vvIzlsLHmuIXpmaTmlbDmja4NCiAgICAgICAgdGhpcy5hbW1ldGVyLmNvbnRyYWN0T3RoUGFydCA9IG51bGw7DQogICAgICAgIHRoaXMuYW1tZXRlci5ubUNjb2RlID0gbnVsbDsNCiAgICAgICAgdGhpcy5hbW1ldGVyLm5tTDIxMDAgPSBudWxsOw0KICAgICAgICB0aGlzLmFtbWV0ZXIubm1MMTgwMCA9IG51bGw7DQogICAgICAgIHRoaXMuYW1tZXRlci5ubUNsODAwbSA9IG51bGw7DQogICAgICB9DQogICAgfSwNCiAgICB3YXJuKCkgew0KICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7DQogICAgICAgIHRpdGxlOiAi5rip6aao5o+Q56S6IiwNCiAgICAgICAgY29udGVudDogIuS/neWtmOWQjueahOaVsOaNruimgeaPkOS6pOWuoeaJueaJjeiDveeUn+aViO+8gSIsDQogICAgICB9KTsNCiAgICB9LA0KICAgIHJlZnJlc2hEYXRhKCkgew0KICAgICAgdGhpcy5pbml0RGF0YSgpOw0KICAgIH0sDQogICAgaW5pdERhdGEoKSB7DQogICAgICB0aGlzLmNvdW50cnlOYW1lID0gIiI7DQogICAgICB0aGlzLmVsZWN0cm8uZGF0YSA9IFtdOw0KICAgICAgdGhpcy5vbGREYXRhID0gW107DQogICAgICB0aGlzLmlzQ2l0eUFkbWluID0gZmFsc2U7DQogICAgICB0aGlzLmlzQWRtaW4gPSBmYWxzZTsNCiAgICAgIHRoaXMuaXNFZGl0QnlDb3VudHJ5ID0gZmFsc2U7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnMuYW1tZXRlci5yZXNldEZpZWxkcygpOyAvLyB0aGlzLiRyZWZzLmFkZHVzZXJmb3JtLnJlc2V0RmllbGRzKCk7DQogICAgICAgIHRoaXMuJHJlZnMuYW1tZXRlcjEucmVzZXRGaWVsZHMoKTsgLy8gdGhpcy4kcmVmcy5hZGR1c2VyZm9ybS5yZXNldEZpZWxkcygpOw0KICAgICAgICB0aGlzLiRyZWZzLmFtbWV0ZXIyLnJlc2V0RmllbGRzKCk7IC8vIHRoaXMuJHJlZnMuYWRkdXNlcmZvcm0ucmVzZXRGaWVsZHMoKTsNCiAgICAgIH0pOw0KICAgICAgdGhpcy5zaG93TW9kZWwgPSBmYWxzZTsNCiAgICAgIHRoaXMuZWxlY3RyaWNUeXBlTW9kZWwgPSBmYWxzZTsNCiAgICB9LA0KICAgIG9uTW9kYWxDYW5jZWwoKSB7DQogICAgICB0aGlzLmluaXREYXRhKCk7DQogICAgfSwNCiAgICAvKuWIneWni+WMliovDQogICAgaW5pdEFtbWV0ZXIoaWQpIHsNCiAgICAgIHRoaXMub2xkQW1tZXRlcklkID0gaWQ7DQogICAgICB0aGlzLmluaXREYXRhKCk7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICBpZiAoaWQgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS555S16KGoIjsNCiAgICAgICAgdGhpcy5pc0VkaXRCeUNvdW50cnkgPSB0cnVlOw0KICAgICAgICAvL+iOt+WPluS4iuS4gOasoeS/ruaUueWOhuWPsg0KICAgICAgICBlZGl0QW1tZXRlclJlY29yZCh7IGlkOiBpZCwgdHlwZTogMSB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBpZiAocmVzLmRhdGEuaWQgIT0gdW5kZWZpbmVkICYmIHJlcy5kYXRhLmlkICE9IG51bGwpIHsNCiAgICAgICAgICAgIGlmIChudWxsICE9IHJlcy5kYXRhLm1heGRlZ3JlZSkgew0KICAgICAgICAgICAgICByZXMuZGF0YS5tYXhkZWdyZWUgPSBwYXJzZUZsb2F0KHJlcy5kYXRhLm1heGRlZ3JlZSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgICB0aGlzLnNldEFtbWV0ZXIoT2JqZWN0LmFzc2lnbih7fSwgcmVzLmRhdGEpKTsNCiAgICAgICAgICAgIGdldENoYW5nZUFtbWV0ZXJpZCgiIiwgMSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgIHRoYXQuYW1tZXRlci5pZCA9IHJlcy5kYXRhLmlkOw0KICAgICAgICAgICAgICB0aGF0LmZpbGVQYXJhbS5idXNpSWQgPSByZXMuZGF0YS5pZDsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgdGhpcy5saXN0RWxlY3RyaWNUeXBlUmF0aW8oaWQsIHJlcy5kYXRhLmlkLCByZXMuZGF0YS5zdGF0aW9uY29kZSk7DQogICAgICAgICAgICB0aGF0LmFtbWV0ZXIuaWQgPSBudWxsOw0KICAgICAgICAgICAgdGhhdC5hbW1ldGVyLm9sZEFtbWV0ZXJJZCA9IGlkOw0KICAgICAgICAgICAgdGhhdC5hbW1ldGVyLmFtbWV0ZXJuYW1lID0gbnVsbDsNCiAgICAgICAgICAgIHRoYXQuZmlsZVBhcmFtLmJ1c2lJZCA9IGlkOw0KICAgICAgICAgICAgZ2V0Q2xhc3NpZmljYXRpb25JZCh0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICB0aGlzLmFtbWV0ZXIuY2xhc3NpZmljYXRpb25zID0gcmVzLmRhdGE7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIGF0dGNoTGlzdCh7IGJ1c2lJZDogdGhhdC5maWxlUGFyYW0uYnVzaUlkIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICB0aGF0LmF0dGFjaERhdGEgPSBPYmplY3QuYXNzaWduKFtdLCByZXMuZGF0YS5yb3dzKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBlZGl0QW1tZXRlcihpZCkudGhlbigocmVzMSkgPT4gew0KICAgICAgICAgICAgICBpZiAobnVsbCAhPSByZXMxLmRhdGEubWF4ZGVncmVlKSB7DQogICAgICAgICAgICAgICAgcmVzMS5kYXRhLm1heGRlZ3JlZSA9IHBhcnNlRmxvYXQocmVzMS5kYXRhLm1heGRlZ3JlZSk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgdGhpcy5zZXRBbW1ldGVyKHJlczEuZGF0YSk7DQogICAgICAgICAgICAgIHRoaXMubGlzdEVsZWN0cmljVHlwZVJhdGlvKGlkLCBudWxsLCByZXMxLmRhdGEuc3RhdGlvbmNvZGUpOw0KICAgICAgICAgICAgICB0aGF0LmFtbWV0ZXIub2xkQW1tZXRlcklkID0gaWQ7DQogICAgICAgICAgICAgIHRoYXQuYW1tZXRlci5hbW1ldGVybmFtZSA9IG51bGw7DQogICAgICAgICAgICAgIHRoYXQuYW1tZXRlci5pZCA9IG51bGw7DQogICAgICAgICAgICAgIHRoYXQuZmlsZVBhcmFtLmJ1c2lJZCA9IGlkOw0KICAgICAgICAgICAgICBnZXRDbGFzc2lmaWNhdGlvbklkKHRoaXMuYW1tZXRlci5lbGVjdHJvdHlwZSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5hbW1ldGVyLmNsYXNzaWZpY2F0aW9ucyA9IHJlcy5kYXRhOw0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgYXR0Y2hMaXN0KHsgYnVzaUlkOiB0aGF0LmZpbGVQYXJhbS5idXNpSWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgdGhhdC5hdHRhY2hEYXRhID0gT2JqZWN0LmFzc2lnbihbXSwgcmVzLmRhdGEucm93cyk7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgICAgdGhpcy5nZXRVc2VyKCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOeUteihqCI7DQogICAgICAgIGVkaXRBbW1ldGVyKCIiLCAwKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLnNldEFtbWV0ZXIoT2JqZWN0LmFzc2lnbih7fSwgcmVzLmRhdGEpKTsNCiAgICAgICAgICB0aGlzLmdldFVzZXIoKTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICBnZXRDbGFzc2lmaWNhdGlvbigpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAvL+eUqOeUteexu+Weiw0KICAgICAgICB0aGlzLmNsYXNzaWZpY2F0aW9uRGF0YSA9IHJlcy5kYXRhOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBsaXN0RWxlY3RyaWNUeXBlUmF0aW8oaWQsIHJlY29yZElkLCBzdGF0aW9uY29kZSkgew0KICAgICAgbGlzdEVsZWN0cmljVHlwZVJhdGlvKHsgYW1tZXRlcklkOiBpZCwgYW1tZXRlclJlY29yZElkOiByZWNvcmRJZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgcmVzLmRhdGEucm93cy5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0uc3RhdGlvbklkID09IG51bGwgfHwgaXRlbS5zdGF0aW9uSWQgPT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICBpdGVtLnN0YXRpb25JZCA9IG51bGw7DQogICAgICAgICAgICBpdGVtLnN0YXRpb25OYW1lID0gbnVsbDsNCiAgICAgICAgICB9IGVsc2UgaWYgKGl0ZW0uc3RhdGlvbklkID09IHN0YXRpb25jb2RlKSB7DQogICAgICAgICAgICBpdGVtLl9kaXNhYmxlZCA9IHRydWU7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgICAgdGhpcy5lbGVjdHJvLmRhdGEgPSBPYmplY3QuYXNzaWduKFtdLCByZXMuZGF0YS5yb3dzKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgY2hhbmdlU3RhdHVzKCkgew0KICAgICAgaWYgKHRoaXMuYW1tZXRlci5zdGF0dXMgPT0gMSkgew0KICAgICAgICB0aGlzLmlzUmVxdWlyZUZsYWcgPSB0cnVlOw0KICAgICAgICB0aGlzLnJ1bGVWYWxpZGF0ZS5zdGF0aW9uTmFtZSA9IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF07DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmlzUmVxdWlyZUZsYWcgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5ydWxlVmFsaWRhdGUuc3RhdGlvbk5hbWUgPSBbDQogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHNlbGVjdENoYW5nZSgpIHsNCiAgICAgIGlmICh0aGlzLmFtbWV0ZXIuY29tcGFueSAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgaWYgKHRoaXMuYW1tZXRlci5jb21wYW55ID09ICIxMDAwMDg1Iikgew0KICAgICAgICAgIHRoaXMuaXNDRENvbXBhbnkgPSB0cnVlOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuaXNDRENvbXBhbnkgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgICBnZXRDb3VudHJ5QnlVc2VySWQodGhpcy5hbW1ldGVyLmNvbXBhbnkpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIHRoaXMuZGVwYXJ0bWVudHMgPSByZXMuZGF0YS5kZXBhcnRtZW50czsNCiAgICAgICAgICB0aGlzLmFtbWV0ZXIuY291bnRyeSA9IHJlcy5kYXRhLmRlcGFydG1lbnRzWzBdLmlkOw0KICAgICAgICAgIHRoaXMuYW1tZXRlci5jb3VudHJ5TmFtZSA9IHRoaXMuZGVwYXJ0bWVudHNbMF0ubmFtZTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBnZXRVc2VyKCkgew0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgZ2V0VXNlckJ5VXNlclJvbGUoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgLy/lvZPliY3nmbvlvZXnlKjmiLfmiYDlnKjlhazlj7gNCiAgICAgICAgdGhhdC5jb21wYW5pZXMgPSByZXMuZGF0YS5jb21wYW5pZXM7DQogICAgICAgIHRoYXQuaXNDaXR5QWRtaW4gPSByZXMuZGF0YS5pc0VkaXRBZG1pbjsNCiAgICAgICAgaWYgKA0KICAgICAgICAgIHJlcy5kYXRhLmlzQ2l0eUFkbWluID09IHRydWUgfHwNCiAgICAgICAgICByZXMuZGF0YS5pc1Byb0FkbWluID09IHRydWUgfHwNCiAgICAgICAgICByZXMuZGF0YS5pc1N1YkFkbWluID09IHRydWUNCiAgICAgICAgKSB7DQogICAgICAgICAgdGhhdC5pc0FkbWluID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgICBnZXRDb3VudHJ5c2RhdGEoeyBvcmdDb2RlOiByZXMuZGF0YS5jb21wYW5pZXNbMF0uaWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgLy/moLnmja7mnYPpmZDojrflj5bmiYDlsZ7pg6jpl6gNCiAgICAgICAgICB0aGF0LmRlcGFydG1lbnRzID0gcmVzLmRhdGE7DQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBzZXRPbGREYXRhKGRhdGEpIHsNCiAgICAgIHRoaXMub2xkQ2F0ZWdvcnkgPSBidGV4dCgiYW1tZXRlckNhdGVnb3J5IiwgZGF0YS5jYXRlZ29yeSwgInR5cGVDb2RlIiwgInR5cGVOYW1lIik7DQogICAgICB0aGlzLm9sZFBhY2thZ2V0eXBlID0gYnRleHQoDQogICAgICAgICJwYWNrYWdlVHlwZSIsDQogICAgICAgIGRhdGEucGFja2FnZXR5cGUsDQogICAgICAgICJ0eXBlQ29kZSIsDQogICAgICAgICJ0eXBlTmFtZSINCiAgICAgICk7DQogICAgICB0aGlzLm9sZFBheXBlcmlvZCA9IGJ0ZXh0KCJwYXlQZXJpb2QiLCBkYXRhLnBheXBlcmlvZCwgInR5cGVDb2RlIiwgInR5cGVOYW1lIik7DQogICAgICB0aGlzLm9sZFBheXR5cGUgPSBidGV4dCgicGF5VHlwZSIsIGRhdGEucGF5dHlwZSwgInR5cGVDb2RlIiwgInR5cGVOYW1lIik7DQogICAgICB0aGlzLm9sZEVsZWN0cm9uYXR1cmUgPSBidGV4dCgNCiAgICAgICAgImVsZWN0cm9OYXR1cmUiLA0KICAgICAgICBkYXRhLmVsZWN0cm9uYXR1cmUsDQogICAgICAgICJ0eXBlQ29kZSIsDQogICAgICAgICJ0eXBlTmFtZSINCiAgICAgICk7DQogICAgICB0aGlzLm9sZEVsZWN0cm92YWxlbmNlbmF0dXJlID0gYnRleHQoDQogICAgICAgICJlbGVjdHJvdmFsZW5jZU5hdHVyZSIsDQogICAgICAgIGRhdGEuZWxlY3Ryb3ZhbGVuY2VuYXR1cmUsDQogICAgICAgICJ0eXBlQ29kZSIsDQogICAgICAgICJ0eXBlTmFtZSINCiAgICAgICk7DQogICAgICB0aGlzLm9sZEVsZWN0cm90eXBlID0gYnRleHQoDQogICAgICAgICJlbGVjdHJvVHlwZSIsDQogICAgICAgIGRhdGEuZWxlY3Ryb3R5cGUsDQogICAgICAgICJ0eXBlQ29kZSIsDQogICAgICAgICJ0eXBlTmFtZSINCiAgICAgICk7DQogICAgICB0aGlzLm9sZFN0YXR1cyA9IGJ0ZXh0KCJzdGF0dXMiLCBkYXRhLnN0YXR1cywgInR5cGVDb2RlIiwgInR5cGVOYW1lIik7DQogICAgICB0aGlzLm9sZFByb3BlcnR5ID0gYnRleHQoInByb3BlcnR5IiwgZGF0YS5wcm9wZXJ0eSwgInR5cGVDb2RlIiwgInR5cGVOYW1lIik7DQogICAgICB0aGlzLm9sZEFtbWV0ZXJ0eXBlID0gYnRleHQoDQogICAgICAgICJhbW1ldGVyVHlwZSIsDQogICAgICAgIGRhdGEuYW1tZXRlcnR5cGUsDQogICAgICAgICJ0eXBlQ29kZSIsDQogICAgICAgICJ0eXBlTmFtZSINCiAgICAgICk7DQogICAgICB0aGlzLm9sZFN0YXRpb25zdGF0dXMgPSBidGV4dCgNCiAgICAgICAgInN0YXRpb25TdGF0dXMiLA0KICAgICAgICBkYXRhLnN0YXRpb25zdGF0dXMsDQogICAgICAgICJ0eXBlQ29kZSIsDQogICAgICAgICJ0eXBlTmFtZSINCiAgICAgICk7DQogICAgICB0aGlzLm9sZFN0YXRpb250eXBlID0gYnRleHQoDQogICAgICAgICJCVVJfU1RBTkRfVFlQRSIsDQogICAgICAgIGRhdGEuc3RhdGlvbnR5cGUsDQogICAgICAgICJ0eXBlQ29kZSIsDQogICAgICAgICJ0eXBlTmFtZSINCiAgICAgICk7DQogICAgICB0aGlzLm9sZERpcmVjdHN1cHBseWZsYWcgPSBidGV4dCgNCiAgICAgICAgImRpcmVjdFN1cHBseUZsYWciLA0KICAgICAgICBkYXRhLmRpcmVjdHN1cHBseWZsYWcsDQogICAgICAgICJ0eXBlQ29kZSIsDQogICAgICAgICJ0eXBlTmFtZSINCiAgICAgICk7DQogICAgICB0aGlzLm9sZEFtbWV0ZXJ1c2UgPSBidGV4dCgiYW1tZXRlclVzZSIsIGRhdGEuYW1tZXRlcnVzZSwgInR5cGVDb2RlIiwgInR5cGVOYW1lIik7DQogICAgfSwNCg0KICAgIHNldEFtbWV0ZXIoZm9ybSkgew0KICAgICAgLy/mjaLooajliJ3lp4vljJbmlbDmja7lvIDlp4stLS0tLQ0KICAgICAgaWYgKHRoaXMuY29uZmlnVmVyc2lvbiA9PSAibG4iIHx8IHRoaXMuY29uZmlnVmVyc2lvbiA9PSAiTE4iKSB7DQogICAgICAgIGZvcm0uc3VwcGx5YnVyZWF1YW1tZXRlcmNvZGUgPSBudWxsOw0KICAgICAgfQ0KICAgICAgaWYgKGZvcm0uY2F0ZWdvcnkgPT09IDEpIHsNCiAgICAgICAgZm9ybS5vbGRBbW1ldGVyTmFtZSA9IGZvcm0uYW1tZXRlcm5hbWU7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBmb3JtLm9sZEFtbWV0ZXJOYW1lID0gZm9ybS5wcm90b2NvbG5hbWU7DQogICAgICB9DQogICAgICBmb3JtLmlzY2hhbmdlYW1tZXRlciA9ICIxIjsNCiAgICAgIGZvcm0ub2xkQmlsbFBvd2VyID0gIiI7DQogICAgICAvL+aNouihqOWIneWni+WMluaVsOaNrue7k+adny0tLS0tDQogICAgICBpZiAoZm9ybS5zdGF0dXMgPT0gbnVsbCB8fCBmb3JtLnN0YXR1cyA9PT0gMSkgew0KICAgICAgICBmb3JtLnN0YXR1cyA9IDE7DQogICAgICAgIHRoaXMuaXNSZXF1aXJlRmxhZyA9IHRydWU7DQogICAgICAgIHRoaXMucnVsZVZhbGlkYXRlLnN0YXRpb25OYW1lID0gWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuaXNSZXF1aXJlRmxhZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLnJ1bGVWYWxpZGF0ZS5zdGF0aW9uTmFtZSA9IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiBmYWxzZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdOw0KICAgICAgfQ0KICAgICAgaWYgKGZvcm0uZWxlY3Ryb3ZhbGVuY2VuYXR1cmUgIT0gMSAmJiBmb3JtLmVsZWN0cm92YWxlbmNlbmF0dXJlICE9IDIpIHsNCiAgICAgICAgZm9ybS5lbGVjdHJvdmFsZW5jZW5hdHVyZSA9IG51bGw7DQogICAgICB9DQogICAgICAvLyBmb3JtLnByaWNlID0gZm9ybS5wcmljZQ0KICAgICAgLy8gZGVidWdnZXINCiAgICAgIGZvcm0uaXNzbWFydGFtbWV0ZXIgPSBmb3JtLmlzc21hcnRhbW1ldGVyID09IG51bGwgPyAiMCIgOiBmb3JtLmlzc21hcnRhbW1ldGVyICsgIiI7DQogICAgICBmb3JtLmlzZW50aXR5YW1tZXRlciA9DQogICAgICAgIGZvcm0uaXNlbnRpdHlhbW1ldGVyID09IG51bGwgPyBudWxsIDogZm9ybS5pc2VudGl0eWFtbWV0ZXIgKyAiIjsNCiAgICAgIGZvcm0uaXNhaXJjb25kaXRpb25pbmcgPQ0KICAgICAgICBmb3JtLmlzYWlyY29uZGl0aW9uaW5nID09IG51bGwgPyAiMCIgOiBmb3JtLmlzYWlyY29uZGl0aW9uaW5nICsgIiI7DQogICAgICBmb3JtLmlzbHVtcHN1bSA9IGZvcm0uaXNsdW1wc3VtID09IG51bGwgPyAiMCIgOiBmb3JtLmlzbHVtcHN1bSArICIiOw0KICAgICAgZm9ybS5pc3pneiA9IGZvcm0uaXN6Z3ogPT0gbnVsbCA/ICIwIiA6IGZvcm0uaXN6Z3ogKyAiIjsNCiAgICAgIGlmIChmb3JtLmlzemd6ID09ICIxIikgdGhpcy5kaXNhYmxlZGlzemd6ID0gdHJ1ZTsNCiAgICAgIHRoaXMuYW1tZXRlciA9IGZvcm07DQogICAgICBsZXQgZWxlY3Ryb3R5cGUgPSB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGU7DQogICAgICBpZiAoDQogICAgICAgIGVsZWN0cm90eXBlID09PSAxMTEgfHwNCiAgICAgICAgZWxlY3Ryb3R5cGUgPT09IDExMiB8fA0KICAgICAgICBlbGVjdHJvdHlwZSA9PT0gMTEzIHx8DQogICAgICAgIGVsZWN0cm90eXBlID09PSAyDQogICAgICApIHsNCiAgICAgICAgdGhpcy5pc0NsYXNzaWZpY2F0aW9uID0gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmICgNCiAgICAgICAgKGVsZWN0cm90eXBlICE9IG51bGwgJiYgZWxlY3Ryb3R5cGUgIT09IDE0MTEgJiYgZWxlY3Ryb3R5cGUgIT09IDE0MTIpIHx8DQogICAgICAgIHRoaXMuYW1tZXRlci5wcm9wZXJ0eSAhPT0gMg0KICAgICAgKSB7DQogICAgICAgIHRoaXMucHJvcGVydHlSZWFkb25seSA9IGZhbHNlOw0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuYW1tZXRlci5tYWduaWZpY2F0aW9uID09IG51bGwpIHsNCiAgICAgICAgdGhpcy5hbW1ldGVyLm1hZ25pZmljYXRpb24gPSAxOw0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuYW1tZXRlci5jb21wYW55ICE9IG51bGwpIHsNCiAgICAgICAgdGhpcy5hbW1ldGVyLmNvbXBhbnkgPSB0aGlzLmFtbWV0ZXIuY29tcGFueSArICIiOw0KICAgICAgICBpZiAodGhpcy5hbW1ldGVyLmNvbXBhbnkgPT0gIjEwMDAwODUiKSB7DQogICAgICAgICAgdGhpcy5pc0NEQ29tcGFueSA9IHRydWU7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmFtbWV0ZXIucHJvY2Vzc2luc3RJZCAhPSBudWxsKSB7DQogICAgICAgIHRoaXMuaXNTaG93RmxvdyA9IHRydWU7DQogICAgICB9DQogICAgICB0aGlzLmZsb3dOYW1lID0gdGhpcy5hbW1ldGVyLnByb2plY3RuYW1lOyAvL+eUqOS6juaPkOS6pOa1geeoi+S9v+eUqOWOn+mhueebruWQjeensA0KICAgICAgdGhpcy5zaG93TW9kZWwgPSB0cnVlOw0KICAgIH0sDQoNCiAgICAvL+S/ruaUueeUteihqOOAgeWNj+iurueahOeUqOeUteexu+Wei+aXtu+8jOWmgueUqOeUteexu+Wei+S4jeWGjeS4juWOn+WFiOmAieaLqeeahOWxgOermeeahOWxgOermeexu+Wei+WMuemFjeaXtu+8jOezu+e7n+iHquWKqOa4heepuuWOn+WFs+iBlOWxgOerme+8jOmcgOeUqOaIt+mHjeaWsOWGjeWFs+iBlOWxgOermeOAgg0KICAgIGNoYW5nZUNsYXNzaWZpY2F0aW9ucyh2YWx1ZSkgew0KICAgICAgdGhpcy5pc0NsYXNzaWZpY2F0aW9uID0gZmFsc2U7DQogICAgICBpZiAodmFsdWUubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy5jbGVhclN0YXRpb24oKTsNCiAgICAgICAgdGhpcy5hbW1ldGVyLnByb3BlcnR5ID0gbnVsbDsNCiAgICAgICAgdGhpcy5wcm9wZXJ0eVJlYWRvbmx5ID0gdHJ1ZTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucHJvcGVydHlSZWFkb25seSA9IGZhbHNlOw0KICAgICAgICB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPSB2YWx1ZVt2YWx1ZS5sZW5ndGggLSAxXTsNCiAgICAgICAgbGV0IGVsZWN0cm90eXBlID0gdGhpcy5hbW1ldGVyLmVsZWN0cm90eXBlOw0KICAgICAgICBpZiAoZWxlY3Ryb3R5cGUgPT09IDE0MTEgfHwgZWxlY3Ryb3R5cGUgPT09IDE0MTIpIHsNCiAgICAgICAgICAvL+aOp+WItuS6p+adg+W9kuWxng0KICAgICAgICAgIHRoaXMuYW1tZXRlci5wcm9wZXJ0eSA9IDI7DQogICAgICAgICAgdGhpcy5wcm9wZXJ0eVJlYWRvbmx5ID0gdHJ1ZTsNCiAgICAgICAgICB0aGlzLnJ1bGVWYWxpZGF0ZS5zdXBwbHlidXJlYXVhbW1ldGVyY29kZSA9IFsNCiAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICBdOw0KICAgICAgICB9IGVsc2UgaWYgKGVsZWN0cm90eXBlID09PSAxNDIxIHx8IGVsZWN0cm90eXBlID09PSAxNDIyKSB7DQogICAgICAgICAgdGhpcy5wcm9wZXJ0eVJlYWRvbmx5ID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5hbW1ldGVyLnByb3BlcnR5ID0gNDsNCiAgICAgICAgICB0aGlzLnJ1bGVWYWxpZGF0ZS5zdXBwbHlidXJlYXVhbW1ldGVyY29kZSA9IFsNCiAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICBdOw0KICAgICAgICB9IGVsc2UgaWYgKGVsZWN0cm90eXBlID09PSAxNDMxIHx8IGVsZWN0cm90eXBlID09PSAxNDMyKSB7DQogICAgICAgICAgdGhpcy5wcm9wZXJ0eVJlYWRvbmx5ID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5hbW1ldGVyLnByb3BlcnR5ID0gMTsNCiAgICAgICAgICB0aGlzLnJ1bGVWYWxpZGF0ZS5zdXBwbHlidXJlYXVhbW1ldGVyY29kZSA9IFsNCiAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICBdOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMucHJvcGVydHlSZWFkb25seSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuYW1tZXRlci5wcm9wZXJ0eSA9IG51bGw7DQogICAgICAgICAgdGhpcy5ydWxlVmFsaWRhdGUuc3VwcGx5YnVyZWF1YW1tZXRlcmNvZGUgPSBbDQogICAgICAgICAgICB7IHJlcXVpcmVkOiBmYWxzZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIF07DQogICAgICAgIH0NCiAgICAgICAgLy8gY2hlY2tDbGFzc2lmaWNhdGlvbkxldmVsKHRoaXMuYW1tZXRlci5lbGVjdHJvdHlwZSkudGhlbihyZXMgPT4gew0KICAgICAgICAvLyAgICAgbGV0IGNvZGUgPSByZXMuZGF0YS5tc2c7DQogICAgICAgIC8vICAgICBpZiAoY29kZSAhPT0gJzEnKSB7DQogICAgICAgIGxldCBzdGF0aW9udHlwZSA9IHRoaXMuYW1tZXRlci5zdGF0aW9udHlwZTsNCiAgICAgICAgaWYgKGVsZWN0cm90eXBlID09PSAxMTEgfHwgZWxlY3Ryb3R5cGUgPT09IDExMiB8fCBlbGVjdHJvdHlwZSA9PT0gMTEzKSB7DQogICAgICAgICAgdGhpcy5pc0NsYXNzaWZpY2F0aW9uID0gdHJ1ZTsNCiAgICAgICAgICBpZiAoc3RhdGlvbnR5cGUgIT09IDEwMDAxKSB7DQogICAgICAgICAgICB0aGlzLmNsZWFyU3RhdGlvbigpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIGlmIChlbGVjdHJvdHlwZSA9PT0gMTIxIHx8IGVsZWN0cm90eXBlID09PSAxMTIpIHsNCiAgICAgICAgICBpZiAoc3RhdGlvbnR5cGUgIT09IDEwMDAzICYmIHN0YXRpb250eXBlICE9PSAxMDAwNCkgew0KICAgICAgICAgICAgdGhpcy5jbGVhclN0YXRpb24oKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSBpZiAoZWxlY3Ryb3R5cGUgPT09IDEzMSB8fCBlbGVjdHJvdHlwZSA9PT0gMTMyIHx8IGVsZWN0cm90eXBlID09PSAxMzMpIHsNCiAgICAgICAgICBpZiAoc3RhdGlvbnR5cGUgIT09IDEwMDA1KSB7DQogICAgICAgICAgICB0aGlzLmNsZWFyU3RhdGlvbigpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIGlmIChlbGVjdHJvdHlwZSA9PT0gMTQxMSB8fCBlbGVjdHJvdHlwZSA9PT0gMTQxMikgew0KICAgICAgICAgIGlmICgNCiAgICAgICAgICAgIHN0YXRpb250eXBlICE9PSAxMDAwMiB8fA0KICAgICAgICAgICAgKHN0YXRpb250eXBlID09IDEwMDAyICYmIHRoaXMucHJvcGVydHlyaWdodCAhPT0gMykNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIHRoaXMuY2xlYXJTdGF0aW9uKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2UgaWYgKA0KICAgICAgICAgIGVsZWN0cm90eXBlID09PSAxNDIxIHx8DQogICAgICAgICAgZWxlY3Ryb3R5cGUgPT09IDE0MjIgfHwNCiAgICAgICAgICBlbGVjdHJvdHlwZSA9PT0gMTQzMSB8fA0KICAgICAgICAgIGVsZWN0cm90eXBlID09PSAxNDMyDQogICAgICAgICkgew0KICAgICAgICAgIGlmIChzdGF0aW9udHlwZSAhPT0gMTAwMDIpIHsNCiAgICAgICAgICAgIHRoaXMuY2xlYXJTdGF0aW9uKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2UgaWYgKGVsZWN0cm90eXBlID09PSAyKSB7DQogICAgICAgICAgdGhpcy5pc0NsYXNzaWZpY2F0aW9uID0gdHJ1ZTsNCiAgICAgICAgICAvLyAgICAgaWYoc3RhdGlvbnR5cGUgIT09IDIwMDAxKXsgdGhpcy5jbGVhclN0YXRpb24oKTt9DQogICAgICAgICAgLy8gfWVsc2UgaWYoZWxlY3Ryb3R5cGUgPT09IDMxIHx8IGVsZWN0cm90eXBlID09PSAzMiB8fCBlbGVjdHJvdHlwZSA9PT0gMzMpew0KICAgICAgICAgIC8vICAgICBpZihzdGF0aW9udHlwZSAhPT0gMjAwMDIgfHwgc3RhdGlvbnR5cGUgIT09IC0yKXsgdGhpcy5jbGVhclN0YXRpb24oKTt9DQogICAgICAgICAgLy8gfWVsc2UgaWYoZWxlY3Ryb3R5cGUgPT09IDQpew0KICAgICAgICAgIC8vICAgICBpZihzdGF0aW9udHlwZSAhPT0gLTEgfHwgc3RhdGlvbnR5cGUgIT09IC0yKXsgdGhpcy5jbGVhclN0YXRpb24oKTt9DQogICAgICAgIH0NCiAgICAgICAgLy8gICAgIH0NCiAgICAgICAgLy8gfSk7DQogICAgICAgIGlmICh0aGlzLmNvbmZpZ1ZlcnNpb24gPT0gInNjIiAmJiB0aGlzLmFtbWV0ZXIuc3RhdGlvbmFkZHJlc3Njb2RlKSB7DQogICAgICAgICAgLy/igJw1MeKAneW8gOWktOmTgeWhlOermeWdgOe8lueggeaOp+WItg0KICAgICAgICAgIGlmICgNCiAgICAgICAgICAgIFsxNDExLCAxNDEyXS5pbmNsdWRlcyhlbGVjdHJvdHlwZSkgJiYNCiAgICAgICAgICAgICF0aGlzLmFtbWV0ZXIuc3RhdGlvbmFkZHJlc3Njb2RlLnN0YXJ0c1dpdGgoIjUxIikNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIHRoaXMuY2xlYXJTdGF0aW9uKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBzZXRBdHRhY2hEYXRhKGRhdGEpIHsNCiAgICAgIHRoaXMubXVsdGlGaWxlcyA9IGRhdGEuZGF0YTsNCiAgICAgIHRoaXMucmVtb3ZlSWRzID0gZGF0YS5pZHM7DQogICAgICBpZiAodGhpcy5yZW1vdmVJZHMubGVuZ3RoICE9IDAgJiYgZGF0YS50eXBlID09ICJyZW1vdmUiKSB7DQogICAgICAgIHRoaXMucmVtb3ZlQXR0YWNoKCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnVwbG9hZCgpOw0KICAgICAgfQ0KICAgIH0sDQogICAgdXBsb2FkKCkgew0KICAgICAgaWYgKHRoaXMuYXR0YWNoRGF0YS5sZW5ndGggIT0gMCAmJiB0aGlzLm11bHRpRmlsZXMubGVuZ3RoICE9IDApIHsNCiAgICAgICAgLy8gdGhpcy4kTWVzc2FnZS5pbmZvKCLmj5DnpLo65LiK5Lyg5paH5Lu26L+H5aSn5Y+v6IO95a+86Ie05LiK5Lyg5aSx6LSl77yBIik7DQogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAgIGF4aW9zDQogICAgICAgICAgLnJlcXVlc3Qoew0KICAgICAgICAgICAgdXJsOiAiL2NvbW1vbi9hdHRhY2htZW50cy91cGxvYWRNdWx0aUZpbGUiLA0KICAgICAgICAgICAgbWV0aG9kOiAicG9zdCIsDQogICAgICAgICAgICBkYXRhOiB0aGlzLm11bHRpRmlsZXMsDQogICAgICAgICAgfSkNCiAgICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLmRhdGEuY29kZSAhPSAwKSB7DQogICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgICAgICAgYXR0Y2hMaXN0KHsgYnVzaUlkOiB0aGF0LmZpbGVQYXJhbS5idXNpSWQgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgIHRoYXQuYXR0YWNoRGF0YSA9IE9iamVjdC5hc3NpZ24oW10sIHJlcy5kYXRhLnJvd3MpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICByZW1vdmVBdHRhY2goKSB7DQogICAgICByZW1vdmVBdHRhY2goeyBpZHM6IHRoaXMucmVtb3ZlSWRzLmpvaW4oKSB9KS50aGVuKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIGNsZWFyU3RhdGlvbigpIHsNCiAgICAgIC8v5riF6Zmk5bGA56uZ5L+h5oGvDQogICAgICB0aGlzLmFtbWV0ZXIuc3RhdGlvbk5hbWUgPSBudWxsOw0KICAgICAgdGhpcy5hbW1ldGVyLnN0YXRpb25jb2RlID0gbnVsbDsNCiAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uc3RhdHVzID0gbnVsbDsNCiAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9udHlwZSA9IG51bGw7DQogICAgICB0aGlzLmFtbWV0ZXIuc3RhdGlvbmFkZHJlc3MgPSBudWxsOw0KICAgICAgdGhpcy5hbW1ldGVyLnN0YXRpb25hZGRyZXNzY29kZSA9IG51bGw7DQogICAgfSwNCiAgICAvL+mAieaLqeaJgOWxnumDqOmXqOW8gOWniw0KICAgIGNob29zZVJlc3BvbnNlQ2VudGVyKGluZGV4LCBwYXJhbXMsIGVsZWN0cm9Sb3dOdW0pIHsNCiAgICAgIHRoaXMuY2hvb3NlSW5kZXggPSBpbmRleDsNCiAgICAgIHRoaXMuZWxlY3Ryb1Jvd051bSA9IGVsZWN0cm9Sb3dOdW07DQogICAgICBpZiAoaW5kZXggPT0gMSB8fCBpbmRleCA9PSAyKSB7DQogICAgICAgIGxldCB0eXBlcyA9IHRoaXMuYW1tZXRlci5jbGFzc2lmaWNhdGlvbnM7DQogICAgICAgIGlmICh0eXBlcy5sZW5ndGggPT0gMCkgew0KICAgICAgICAgIHRoaXMuJE1vZGFsLndhcm5pbmcoeyB0aXRsZTogIua4qemmqOaPkOekuiIsIGNvbnRlbnQ6ICLor7flhYjpgInmi6nnlKjnlLXnsbvlnovvvIEiIH0pOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmFtbWV0ZXIuYW1tZXRlcnVzZSA9PSBudWxsKSB7DQogICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7IHRpdGxlOiAi5rip6aao5o+Q56S6IiwgY29udGVudDogIuivt+WFiOmAieaLqeeUteihqOeUqOmAlO+8gSIgfSk7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGlmICh0aGlzLmFtbWV0ZXIuY29tcGFueSA9PSBudWxsKSB7DQogICAgICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIuivt+WFiOmAieaLqeWIhuWFrOWPuCIpOw0KICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPSB0eXBlc1t0eXBlcy5sZW5ndGggLSAxXTsNCiAgICAgICAgICAvLyBpZih0aGlzLmNvbmZpZ1ZlcnNpb249PSdsbicgfHwgdGhpcy5jb25maWdWZXJzaW9uID09J0xOJyl7DQogICAgICAgICAgLy8gICAgIHRoaXMuJHJlZnMuc3RhdGlvbk1vZGFsTE4uaW5pdERhdGFMaXN0KHRoaXMuYW1tZXRlci5lbGVjdHJvdHlwZSwwLHRoaXMuYW1tZXRlci5hbW1ldGVydXNlLHBhcmFtcyk7Ly/lsYDnq5kNCiAgICAgICAgICAvLyB9ZWxzZXsNCiAgICAgICAgICAvL+aNouihqCB0eXBlID0gMw0KICAgICAgICAgIHRoaXMuJHJlZnMuc3RhdGlvbk1vZGFsLmFtbWV0ZXJpZCA9IHRoaXMuYW1tZXRlci5pZDsNCiAgICAgICAgICB0aGlzLiRyZWZzLnN0YXRpb25Nb2RhbC5pbml0RGF0YUxpc3QoDQogICAgICAgICAgICB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUsDQogICAgICAgICAgICAzLA0KICAgICAgICAgICAgdGhpcy5hbW1ldGVyLmFtbWV0ZXJ1c2UsDQogICAgICAgICAgICB0aGlzLmFtbWV0ZXIuY29tcGFueSwNCiAgICAgICAgICAgIHBhcmFtcw0KICAgICAgICAgICk7IC8v5bGA56uZDQogICAgICAgICAgLy8gfQ0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICBpZiAodGhpcy5hbW1ldGVyLmNvbXBhbnkgPT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbygi6K+35YWI6YCJ5oup5YiG5YWs5Y+4Iik7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICAgIHRoaXMuJHJlZnMuY291bnRyeU1vZGFsLmNob29zZSh0aGlzLmFtbWV0ZXIuY29tcGFueSk7IC8v5omA5bGe6YOo6ZeoDQogICAgICB9DQogICAgfSwNCiAgICBnZXREYXRhRnJvbU1vZGFsKGRhdGEsIGZsYWcpIHsNCiAgICAgIHRoaXMuYW1tZXRlci5jb3VudHJ5ID0gZGF0YS5pZDsNCiAgICAgIHRoaXMuYW1tZXRlci5jb3VudHJ5TmFtZSA9IGRhdGEubmFtZTsNCiAgICAgIC8vdGhpcy5jaG9vc2VSZXNwb25zZUNlbnRlcig0LCBkYXRhKTsNCiAgICAgIC8v6YCJ5oup5omA5bGe6YOo6Zeo57uT5p2fDQogICAgfSwNCiAgICAvL+iOt+WPluWxgOermeaVsOaNrg0KICAgIGdldERhdGFGcm9tU3RhdGlvbk1vZGFsKGRhdGEsIGZsYWcpIHsNCiAgICAgIHRoaXMuaXNjaGVja1N0YXRpb24gPSBmbGFnOw0KICAgICAgdGhpcy5pc29sZGNoZWNrU3RhdGlvbiA9IGZsYWc7DQogICAgICBpZiAodGhpcy5jaG9vc2VJbmRleCA9PSAyKSB7DQogICAgICAgIHRoaXMuZWxlY3Ryby5kYXRhW3RoaXMuZWxlY3Ryb1Jvd051bV0uc3RhdGlvbklkID0gZGF0YS5pZDsNCiAgICAgICAgdGhpcy5lbGVjdHJvLmRhdGFbdGhpcy5lbGVjdHJvUm93TnVtXS5zdGF0aW9uTmFtZSA9IGRhdGEuc3RhdGlvbm5hbWU7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnByb3BlcnR5cmlnaHQgPSBkYXRhLnByb3BlcnR5cmlnaHQ7DQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uTmFtZSA9IGRhdGEuc3RhdGlvbm5hbWU7DQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uY29kZSA9IGRhdGEuaWQ7DQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uc3RhdHVzID0gTnVtYmVyKA0KICAgICAgICAgIGRhdGEuc3RhdHVzID09IHVuZGVmaW5lZCA/IGRhdGEuU1RBVFVTIDogZGF0YS5zdGF0dXMNCiAgICAgICAgKTsNCiAgICAgICAgdGhpcy5hbW1ldGVyLnN0YXRpb250eXBlID0gTnVtYmVyKGRhdGEuc3RhdGlvbnR5cGUpOw0KICAgICAgICB0aGlzLmFtbWV0ZXIuc3RhdGlvbmFkZHJlc3MgPSBkYXRhLmFkZHJlc3M7DQogICAgICAgIC8vIGlmIChkYXRhLnN0YXRpb250eXBlID09IDEwMDAyICYmIGRhdGEucHJvcGVydHlyaWdodCA9PSAzKSB7Ly/lj6rmnInlvZPlsYDnq5nnsbvlnovkuLrigJjnlJ/kuqfnlKjmiL8t56e75Yqo5Z+656uZ4oCZ5LiU5Lqn5p2D5Li64oCY56ef55So4oCZ5pe277yM5a2Y5pS+56uZ5Z2A57yW56CBDQogICAgICAgIHRoaXMuYW1tZXRlci5zdGF0aW9uYWRkcmVzc2NvZGUgPSBkYXRhLnJlc3N0YXRpb25jb2RlOw0KICAgICAgICAvLyB9DQogICAgICAgIC8v6buY6K6k55Sf5oiQ5LiA5p2h5YWz6IGU55So55S157G75Z6LDQogICAgICAgIGxldCB0aGF0ID0gdGhpczsNCiAgICAgICAgbGlzdEVsZWN0cmljVHlwZSh7IGlkOiBkYXRhLnN0YXRpb250eXBlIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGxldCByZXN1bHQgPSB0aGF0LmVsZWN0cm8uZGF0YTsNCiAgICAgICAgICBsZXQgZWxlY3Ryb0RhdGEgPSBPYmplY3QuYXNzaWduKFtdLCByZXMuZGF0YS5yb3dzKTsNCiAgICAgICAgICBsZXQgY291bnQgPSAwOw0KICAgICAgICAgIGlmIChyZXN1bHQubGVuZ3RoID09IDApIHsNCiAgICAgICAgICAgIGNvdW50Kys7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHJlc3VsdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgICAgIGVsZWN0cm9EYXRhLmZvckVhY2goKGl0ZW0xKSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKGl0ZW0uaWQgPT09IGl0ZW0xLmlkKSB7DQogICAgICAgICAgICAgICAgICBlbGVjdHJvRGF0YVswXS5zdGF0aW9uSWQgPSBkYXRhLmlkOw0KICAgICAgICAgICAgICAgICAgZWxlY3Ryb0RhdGFbMF0uc3RhdGlvbk5hbWUgPSBkYXRhLnN0YXRpb25uYW1lOw0KICAgICAgICAgICAgICAgICAgZWxlY3Ryb0RhdGFbMF0uX2Rpc2FibGVkID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgIGxldCBpbmRleCA9IHJlc3VsdC5pbmRleE9mKGl0ZW0pOw0KICAgICAgICAgICAgICAgICAgcmVzdWx0LnNwbGljZShpbmRleCwgMSk7DQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgIGNvdW50Kys7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoY291bnQgPiAwKSB7DQogICAgICAgICAgICB0aGF0LmVsZWN0cm8uZGF0YSA9IE9iamVjdC5hc3NpZ24oW10sIHJlcy5kYXRhLnJvd3MpOw0KICAgICAgICAgICAgdGhhdC5lbGVjdHJvLmRhdGFbMF0uc3RhdGlvbklkID0gZGF0YS5pZDsNCiAgICAgICAgICAgIHRoYXQuZWxlY3Ryby5kYXRhWzBdLnN0YXRpb25OYW1lID0gZGF0YS5zdGF0aW9ubmFtZTsNCiAgICAgICAgICAgIHRoYXQuZWxlY3Ryby5kYXRhWzBdLl9kaXNhYmxlZCA9IHRydWU7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHJlc3VsdC51bnNoaWZ0KGVsZWN0cm9EYXRhWzBdKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKua3u+WKoOeUteihqOWFs+iBlOeUqOeUteexu+Wei+avlOeOhyovDQogICAgYWRkRWxlY3RyaWNUeXBlKCkgew0KICAgICAgdGhpcy4kcmVmcy5zZWxlY3RFbGVjdHJpY1R5cGUuaW5pdEVsZWN0cmljVHlwZSgpOw0KICAgIH0sDQoNCiAgICAvKuenu+mZpOmAieS4reeahOeUqOeUteexu+Wei+avlOeOhyovDQogICAgcmVtb3ZlRWxlY3RyaWNUeXBlKCkgew0KICAgICAgbGV0IHJvd3MgPSB0aGlzLiRyZWZzLmFtbWV0ZXJUYWJsZS5nZXRTZWxlY3Rpb24oKTsNCiAgICAgIGxldCBkYXRhcyA9IHRoaXMuZWxlY3Ryby5kYXRhOw0KICAgICAgcm93cy5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGlmIChpdGVtLl9pbmRleCAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICBkYXRhcy5zcGxpY2UoaXRlbS5faW5kZXgsIDEpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGRhdGFzLmZvckVhY2goKGRhdGEpID0+IHsNCiAgICAgICAgICAgIGlmIChkYXRhLmlkID09PSBpdGVtLmlkKSB7DQogICAgICAgICAgICAgIGxldCBpbmRleCA9IGRhdGFzLmluZGV4T2YoZGF0YSk7DQogICAgICAgICAgICAgIGRhdGFzLnNwbGljZShpbmRleCwgMSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgdGhpcy5lbGVjdHJvLmRhdGEgPSBkYXRhczsNCiAgICB9LA0KDQogICAgLyog6K6+572u55So55S157G75Z6L5YiX6KGoKi8NCiAgICBzZXRFbGVjdHJpY0RhdGE6IGZ1bmN0aW9uIChkYXRhKSB7DQogICAgICBsZXQgb3JpZ2luID0gdGhpcy5lbGVjdHJvLmRhdGE7DQogICAgICBpZiAob3JpZ2luLmxlbmd0aCA8IDEpIHsNCiAgICAgICAgdGhpcy5lbGVjdHJvLmRhdGEgPSBkYXRhOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGV0IHRlbSA9IGRhdGE7DQogICAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgb3JpZ2luLmxlbmd0aDsgaisrKSB7DQogICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhLmxlbmd0aDsgaSsrKSB7DQogICAgICAgICAgICBsZXQgdHlwZUlkID0NCiAgICAgICAgICAgICAgb3JpZ2luW2pdLmVsZWN0cm9UeXBlSWQgIT0gdW5kZWZpbmVkDQogICAgICAgICAgICAgICAgPyBvcmlnaW5bal0uZWxlY3Ryb1R5cGVJZA0KICAgICAgICAgICAgICAgIDogb3JpZ2luW2pdLmlkOw0KICAgICAgICAgICAgaWYgKGRhdGFbaV0uaWQgPT09IHR5cGVJZCkgew0KICAgICAgICAgICAgICB0ZW0uc3BsaWNlKHRlbS5pbmRleE9mKGRhdGFbaV0pLCAxKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5lbGVjdHJvLmRhdGEgPSB0aGlzLmVsZWN0cm8uZGF0YS5jb25jYXQodGVtKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy/nlKjnlLXnsbvlnovmr5TkvovmoKHpqowNCiAgICBjaGVja0VsZWN0cmljVHlwZUl0ZW0oKSB7DQogICAgICBsZXQgaXRlbXMgPSB0aGlzLmVsZWN0cm8uZGF0YTsNCiAgICAgIC8v5b2T4oCc55So55S157G75Z6L4oCd6YCJ5oup4oCcMTExIEHnsbvmnLrmpbzvvIjmnLrmiL/vvInvvIwxMTIgQuexu+acuualvO+8iOacuuaIv++8ie+8jDExMyBD57G75py65qW877yI5py65oi/77yJIOKAneaIluKAnDIg566h55CG5Yqe5YWs55So55S14oCd5pe277yM5omN6ZyA5aGr55So55S157G75Z6L5YiG5q+U5LiU5b+F5aGr77yM55So55S157G75Z6L5q+U5L6L5LmL5ZKM5b+F6aG7562J5LqOMTAwJQ0KICAgICAgaWYgKA0KICAgICAgICB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPT09IDExMSB8fA0KICAgICAgICB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPT09IDExMiB8fA0KICAgICAgICB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPT09IDExMyB8fA0KICAgICAgICB0aGlzLmFtbWV0ZXIuZWxlY3Ryb3R5cGUgPT09IDINCiAgICAgICkgew0KICAgICAgICBsZXQgc3VtUmF0aW8gPSBpdGVtcy5yZWR1Y2UoKHRvdGFsLCBpdGVtKSA9PiB7DQogICAgICAgICAgcmV0dXJuIHRvdGFsICsgaXRlbS5yYXRpbzsNCiAgICAgICAgfSwgMCk7DQogICAgICAgIGlmIChzdW1SYXRpbyAhPT0gMTAwKSB7DQogICAgICAgICAgdGhpcy4kTW9kYWwud2FybmluZyh7DQogICAgICAgICAgICB0aXRsZTogIua4qemmqOaPkOekuiIsDQogICAgICAgICAgICBjb250ZW50OiAi55So55S157G75Z6L5omA5Y2g5q+U5L6L5ZKM5b+F6aG75Li6MTAwJe+8jOW9k+WJjeWAvOS4uiIgKyBzdW1SYXRpbyArICIlIiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHJldHVybiB0cnVlOw0KICAgIH0sDQogICAgc2hvd0Zsb3coKSB7DQogICAgICB0aGlzLnNob3dXb3JrRmxvdyA9IHRydWU7DQogICAgICBsZXQgdGhhdCA9IHRoaXM7DQogICAgICB0aGlzLmhpc1BhcmFtcyA9IHsNCiAgICAgICAgYnVzaUlkOiB0aGF0LmFtbWV0ZXIuaWQsDQogICAgICAgIGJ1c2lUeXBlOiB0aGF0LmFtbWV0ZXIuYnVzaUFsaWFzLA0KICAgICAgICBwcm9jSW5zdElkOiB0aGF0LmFtbWV0ZXIucHJvY2Vzc2luc3RJZCwNCiAgICAgIH07DQogICAgfSwNCiAgICBzdGFydEZsb3coZGF0YSkgew0KICAgICAgbGV0IGJ1c2lUaXRsZSA9ICLnlLXooajmjaLooagoIiArIGRhdGEucHJvamVjdG5hbWUgKyAiKeWuoeaJuSI7DQogICAgICB0aGlzLndvcmtGbG93UGFyYW1zID0gew0KICAgICAgICBidXNpSWQ6IGRhdGEuaWQsDQogICAgICAgIGJ1c2lBbGlhczogIkFNTV9TV0lUQ0hfQU1NIiwNCiAgICAgICAgYnVzaVRpdGxlOiBidXNpVGl0bGUsDQogICAgICB9Ow0KICAgICAgbGV0IHRoYXQgPSB0aGlzOw0KICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7DQogICAgICAgIHRoYXQuJHJlZnMuY2x3ZmJ0bi5vbkNsaWNrKCk7DQogICAgICB9LCAyMDApOw0KICAgIH0sDQogICAgZG9Xb3JrRmxvdyhkYXRhKSB7DQogICAgICAvL+a1geeoi+Wbnuiwgw0KICAgICAgdGhpcy5jbG9zZVRhZyh7IHJvdXRlOiB0aGlzLiRyb3V0ZSB9KTsNCiAgICAgIGlmIChkYXRhID09IDApIHsNCiAgICAgICAgdGhpcy53YXJuKCk7DQogICAgICB9DQogICAgfSwNCiAgICAvKumAieaLqeeUteihqC/ljY/orq4qLw0KICAgIGFkZEFtbWV0ZXJQcm90b2NvbCgpIHsNCiAgICAgIHRoaXMuJHJlZnMuc2VsZWN0QW1tZXRlclByb3RvY29sLmluaXREYXRhTGlzdCgxLCB0aGlzLmFtbWV0ZXIuaWQpOw0KICAgIH0sDQogICAgLyog6YCJ5oup55S16KGo5oi35Y+3L+WNj+iurue8luWPtyovDQogICAgc2V0QW1tZXRlclByb3JvY29sRGF0YTogZnVuY3Rpb24gKGRhdGEpIHsNCiAgICAgIHRoaXMuYW1tZXRlci5wYXJlbnRJZCA9IGRhdGEuaWQ7DQogICAgICBpZiAoZGF0YS5wcm90b2NvbG5hbWUgIT0gbnVsbCAmJiBkYXRhLnByb3RvY29sbmFtZS5sZW5ndGggIT0gMCkgew0KICAgICAgICB0aGlzLmFtbWV0ZXIucGFyZW50Q29kZSA9IGRhdGEucHJvdG9jb2xuYW1lOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5hbW1ldGVyLnBhcmVudENvZGUgPSBkYXRhLmFtbWV0ZXJuYW1lOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyrpgInmi6nlrqLmiLcqLw0KICAgIGFkZEN1c3RvbWVyKCkgew0KICAgICAgdGhpcy4kcmVmcy5jdXN0b21lckxpc3QuY2hvb3NlKDIpOyAvL+aJk+W8gOaooeaAgeahhg0KICAgIH0sDQogICAgZ2V0RGF0YUZyb21DdXN0b21lck1vZGFsOiBmdW5jdGlvbiAoZGF0YSkgew0KICAgICAgdGhpcy5hbW1ldGVyLmN1c3RvbWVySWQgPSBkYXRhLmlkOw0KICAgICAgdGhpcy5hbW1ldGVyLmN1c3RvbWVyTmFtZSA9IGRhdGEubmFtZTsNCiAgICB9LA0KICAgIGlzemd6Y2hhbmdlKCkgew0KICAgICAgaWYgKHRoaXMuYW1tZXRlci5pc3pneiA9PSAiMSIpIHsNCiAgICAgICAgdGhpcy5hbW1ldGVyLmRpcmVjdHN1cHBseWZsYWcgPSAxOw0KICAgICAgICB0aGlzLmFtbWV0ZXIub2xkYW1tZXRlcm5hbWUgPSB0aGlzLmFtbWV0ZXIub2xkQW1tZXRlck5hbWU7DQogICAgICAgIHRoaXMuaXN6Z3pPbmx5ID0gdHJ1ZTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuaXN6Z3pPbmx5ID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICBjaG9vc2VvbGRhbW1ldGVybmFtZSgpIHsNCiAgICAgIGlmICh0aGlzLmRpc2FibGVkaXN6Z3opIHJldHVybjsNCiAgICAgIHRoaXMuJHJlZnMuY2hvb3NlQW1tZXRlck1vZGVsLm1vZGFsLnF1ZXJ5cGFyYW1zLnN0YXR1cyA9IDA7DQogICAgICB0aGlzLiRyZWZzLmNob29zZUFtbWV0ZXJNb2RlbC5tb2RhbC5xdWVyeXBhcmFtcy5hbW1ldGVydXNlID0gMTsNCiAgICAgIHRoaXMuJHJlZnMuY2hvb3NlQW1tZXRlck1vZGVsLm1vZGFsLnF1ZXJ5cGFyYW1zLnR5cGUgPSAzOw0KICAgICAgdGhpcy4kcmVmcy5jaG9vc2VBbW1ldGVyTW9kZWwubW9kYWwucXVlcnlwYXJhbXMuY29tcGFueSA9IHRoaXMuYW1tZXRlci5jb21wYW55Ow0KICAgICAgdGhpcy4kcmVmcy5jaG9vc2VBbW1ldGVyTW9kZWwubW9kYWwucXVlcnlwYXJhbXMuY291bnRyeSA9IHRoaXMuYW1tZXRlci5jb3VudHJ5Ow0KICAgICAgdGhpcy4kcmVmcy5jaG9vc2VBbW1ldGVyTW9kZWwubW9kYWwucXVlcnlwYXJhbXMuZGlyZWN0c3VwcGx5ZmxhZyA9IDI7DQogICAgICB0aGlzLiRyZWZzLmNob29zZUFtbWV0ZXJNb2RlbC5tb2RhbC5zaG93ID0gdHJ1ZTsNCiAgICAgIHRoaXMuJE1lc3NhZ2UuaW5mbygi5Y+M5Ye76YCJ5oup77yB77yBIik7DQogICAgfSwNCiAgICBnZXRBbW1ldGVyTW9kZWxNb2RhbChkYXRhKSB7DQogICAgICB0aGlzLmFtbWV0ZXIub2xkYW1tZXRlcm5hbWUgPSBkYXRhLm5hbWUgKyAiLCIgKyBkYXRhLmlkOw0KICAgIH0sDQogICAgcHJvamVjdE5hbWVDaGFuZ2UodmFsKSB7DQogICAgICAvLyB2YXIgcGF0dD0vXihbXlx1MDAwMC1cdTAwZmZdK+i3rykoW15cdTAwMDAtXHUwMGZmXSopKFswLTldKuWPtykoW15cdTAwMDAtXHUwMGZmXSvmpbznlLXooagpJC87DQogICAgICBpZiAoDQogICAgICAgICEvXi4qKFteXHUwMDAwLVx1MDBmZl0r6LevKS4qJC8udGVzdCh2YWwpICYmDQogICAgICAgICEvXi4qKFteXHUwMDAwLVx1MDBmZl0qKShbMC05XSrlj7cpLiokLy50ZXN0KHZhbCkgJiYNCiAgICAgICAgIS9eLiooW15cdTAwMDAtXHUwMGZmXSvmpbznlLXooagpLiokLy50ZXN0KHZhbCkNCiAgICAgICkgew0KICAgICAgICB0aGlzLiRNZXNzYWdlLmluZm8oIua4qemmqOaPkOekuu+8mumbhuWbouimgeaxguagvOW8j+S4uigqKui3ryoq5Y+3KirmpbznlLXooagpIik7DQogICAgICB9DQogICAgfSwNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAvL+ebtOaOpeS7juWJjeWPsOWPlg0KICAgIHRoaXMuY2F0ZWdvcnlzID0gew0KICAgICAgZGlyZWN0c3VwcGx5ZmxhZzogYmxpc3QoImRpcmVjdFN1cHBseUZsYWciKSwNCiAgICB9Ow0KICAgIHRoaXMucHJvcGVydHlMaXN0ID0gYmxpc3QoInByb3BlcnR5Iik7DQogICAgdGhpcy5pbml0QW1tZXRlcih0aGlzLiRyb3V0ZS5xdWVyeS5pZCk7DQoNCiAgICB0aGlzLmNvbmZpZ1ZlcnNpb24gPSB0aGlzLiRjb25maWcudmVyc2lvbjsNCiAgICBpZiAodGhpcy5jb25maWdWZXJzaW9uICE9ICJsbiIgJiYgdGhpcy5jb25maWdWZXJzaW9uICE9ICJMTiIpIHsNCiAgICAgIHRoaXMucnVsZVZhbGlkYXRlLmFtbWV0ZXJuYW1lLnB1c2goew0KICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgIH0pOw0KICAgICAgdGhpcy5ydWxlVmFsaWRhdGUuY3VzdG9tZXJOYW1lID0gew0KICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsDQogICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgIH07DQogICAgfQ0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["changeAmmeter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAggDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "changeAmmeter.vue", "sourceRoot": "src/view/basedata/ammeter", "sourcesContent": ["<template>\r\n  <div class=\"testaa\">\r\n    <!--重点就是下面的代码了-->\r\n    <Spin size=\"large\" fix v-if=\"loading\"></Spin>\r\n    <div solt=\"header\">\r\n      <Row>\r\n        <Col span=\"24\" style=\"text-align: right; right: 10px\">\r\n          <Button\r\n            type=\"success\"\r\n            :loading=\"isLoading == 0 ? loading : false\"\r\n            @click=\"onModalOK(0)\"\r\n            >保存</Button\r\n          >\r\n          <Button\r\n            type=\"primary\"\r\n            :loading=\"isLoading == 1 ? loading : false\"\r\n            @click=\"onModalOK(1)\"\r\n            >提交</Button\r\n          >\r\n          <!--                    <Button v-if=\"isShowFlow\" type=\"success\" @click=\"showFlow\">流程图</Button>-->\r\n        </Col>\r\n      </Row>\r\n    </div>\r\n    <Modal\r\n      v-model=\"modal1\"\r\n      title=\"温馨提示\"\r\n      okText=\"是\"\r\n      cancelText=\"否\"\r\n      @on-ok=\"okModel\"\r\n      @on-cancel=\"cancelModel\"\r\n    >\r\n      <p style=\"margin: 25px 0 25px 40px\">是否新型室分?</p>\r\n    </Modal>\r\n    <cl-wf-btn\r\n      ref=\"clwfbtn\"\r\n      :isStart=\"true\"\r\n      :params=\"workFlowParams\"\r\n      @on-ok=\"doWorkFlow\"\r\n      v-show=\"false\"\r\n    ></cl-wf-btn>\r\n    <!-- 查看流程 -->\r\n    <Modal v-model=\"showWorkFlow\" title=\"电表流程及审批意见跟踪表\" :width=\"800\">\r\n      <WorkFlowInfoComponet\r\n        :wfHisParams=\"hisParams\"\r\n        v-if=\"showWorkFlow\"\r\n      ></WorkFlowInfoComponet>\r\n    </Modal>\r\n    <select-electric-type\r\n      ref=\"selectElectricType\"\r\n      v-on:listenToSetElectricType=\"setElectricData\"\r\n    ></select-electric-type>\r\n    <country-modal\r\n      ref=\"countryModal\"\r\n      v-on:getDataFromModal=\"getDataFromModal\"\r\n    ></country-modal>\r\n    <station-modal\r\n      ref=\"stationModal\"\r\n      v-on:getDataFromStationModal=\"getDataFromStationModal\"\r\n    ></station-modal>\r\n    <ammeter-protocol-list\r\n      ref=\"selectAmmeterProtocol\"\r\n      v-on:listenToSetAmmeterProrocol=\"setAmmeterProrocolData\"\r\n    ></ammeter-protocol-list>\r\n    <customer-list\r\n      ref=\"customerList\"\r\n      v-on:getDataFromCustomerModal=\"getDataFromCustomerModal\"\r\n    ></customer-list>\r\n    <!--        <Modal v-model=\"showModel\" width=\"80%\" :title=\"title\">-->\r\n    <Card class=\"menu-card\">\r\n      <Collapse :value=\"['Panel1', 'Panel2', 'Panel3', 'Panel4', 'Panel6']\">\r\n        <Panel name=\"Panel1\"\r\n          >基本信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"项目名称：\" prop=\"projectname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.projectname\"\r\n                        placeholder=\"**路**号**楼电表\"\r\n                        @on-blur=\"projectNameChange\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.projectname != null &&\r\n                          oldData.projectname != ammeter.projectname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.projectname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <!--                                        <Col span=\"6\">-->\r\n                  <!--                                            <FormItem label=\"电表户号：\" prop=\"ammetername\">-->\r\n                  <!--                                                <cl-input :maxlength=30 v-model=\"ammeter.ammetername\"></cl-input>-->\r\n                  <!--                                                <label v-if=\"oldData.ammetername != null &&oldData.ammetername != ammeter.ammetername\" style=\"color: red;\">历史数据：{{oldData.ammetername}}</label>-->\r\n                  <!--                                            </FormItem>-->\r\n                  <!--                                        </Col>-->\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag === 1\">\r\n                    <FormItem\r\n                      label=\"供电局电表编号(下户户号)：\"\r\n                      prop=\"supplybureauammetercode\"\r\n                    >\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauammetercode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauammetercode != null &&\r\n                          oldData.supplybureauammetercode !=\r\n                            ammeter.supplybureauammetercode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauammetercode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag === 2\">\r\n                    <FormItem\r\n                      label=\"供电局电表编号(电表编号)：\"\r\n                      prop=\"supplybureauammetercode\"\r\n                    >\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauammetercode\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauammetercode != null &&\r\n                          oldData.supplybureauammetercode !=\r\n                            ammeter.supplybureauammetercode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauammetercode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否智能电表：\" prop=\"issmartammeter\">\r\n                      <RadioGroup v-model=\"ammeter.issmartammeter\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.issmartammeter != null &&\r\n                          oldData.issmartammeter != ammeter.issmartammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.issmartammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否实体电表：\" prop=\"isentityammeter\">\r\n                      <RadioGroup v-model=\"ammeter.isentityammeter\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.isentityammeter != null &&\r\n                          oldData.isentityammeter != ammeter.isentityammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.isentityammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\" v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n                    <FormItem label=\"电表编号：\">\r\n                      <cl-input\r\n                        disabled\r\n                        readonly\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.ammetername\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetername != null &&\r\n                          oldData.ammetername != ammeter.ammetername\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetername }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"configVersion != 'ln' && configVersion != 'LN'\">\r\n                    <FormItem label=\"电表编号：\" prop=\"ammetername\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.ammetername\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetername != null &&\r\n                          oldData.ammetername != ammeter.ammetername\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetername }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"电表用途：\" prop=\"ammeteruse\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.ammeteruse\"\r\n                        category=\"ammeterUse\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammeteruse != null &&\r\n                          oldData.ammeteruse != ammeter.ammeteruse\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldAmmeteruse }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"关联实际报账电表：\"\r\n                      prop=\"parentCode\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.parentCode\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addAmmeterProtocol\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.parentCode != null &&\r\n                          oldData.parentCode != ammeter.parentCode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.parentCode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"供电局名称：\" prop=\"supplybureauname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.supplybureauname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.supplybureauname != null &&\r\n                          oldData.supplybureauname != ammeter.supplybureauname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.supplybureauname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"电表类型2222：\" prop=\"ammetertype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.ammetertype\"\r\n                        category=\"ammeterType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetertype != null &&\r\n                          oldData.ammetertype != ammeter.ammetertype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldAmmetertype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"包干类型：\" prop=\"packagetype\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.packagetype\"\r\n                        category=\"packageType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      >\r\n                      </cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.packagetype != null &&\r\n                          oldData.packagetype != ammeter.packagetype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPackagetype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"所属分公司：\" prop=\"company\">\r\n                      <Select\r\n                        v-model=\"ammeter.company\"\r\n                        @on-change=\"selectChange(ammeter.company)\"\r\n                      >\r\n                        <Option\r\n                          v-for=\"item in companies\"\r\n                          :value=\"item.id\"\r\n                          :key=\"item.id\"\r\n                          >{{ item.name }}</Option\r\n                        >\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.company != null && oldData.company != ammeter.company\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.companyName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <!--                                            <FormItem label=\"所属部门\" prop=\"countryName\">-->\r\n                    <!--                                                <Input icon=\"ios-archive\" v-model=\"ammeter.countryName\"-->\r\n                    <!--                                                       placeholder=\"点击图标选择\" @on-click=\"chooseResponseCenter()\" readonly/>-->\r\n                    <!--                                            </FormItem>-->\r\n                    <FormItem\r\n                      label=\"所属部门：\"\r\n                      prop=\"countryName\"\r\n                      v-if=\"isAdmin == true\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-if=\"isCityAdmin == true || isEditByCountry == false\"\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseResponseCenter()\"\r\n                        readonly\r\n                      />\r\n                      <Input\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-else-if=\"isEditByCountry == true && isCityAdmin == false\"\r\n                        readonly\r\n                      />\r\n                      <!--                                                <label v-if=\"oldData.countryName != null &&oldData.countryName != ammeter.countryName\" style=\"color: red;\">历史数据：{{oldData.countryName}}</label>-->\r\n                    </FormItem>\r\n                    <FormItem label=\"所属部门：\" prop=\"country\" v-if=\"isAdmin == false\">\r\n                      <Select v-model=\"ammeter.country\" v-if=\"isEditByCountry == false\">\r\n                        <Option\r\n                          v-for=\"item in departments\"\r\n                          :value=\"item.id\"\r\n                          :key=\"item.id\"\r\n                          >{{ item.name }}</Option\r\n                        >\r\n                      </Select>\r\n                      <Input\r\n                        v-model=\"ammeter.countryName\"\r\n                        v-else-if=\"isEditByCountry == true\"\r\n                        readonly\r\n                      />\r\n                      <!--                                                <label v-if=\"oldData.countryName != null &&oldData.countryName != ammeter.countryName\" style=\"color: red;\">历史数据：{{oldData.countryName}}</label>-->\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"所属分局或支局：\" prop=\"substation\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.substation\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.substation != null &&\r\n                          oldData.substation != ammeter.substation\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.substation }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"详细地址：\" prop=\"address\">\r\n                      <cl-input :maxlength=\"100\" v-model=\"ammeter.address\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.address != null && oldData.address != ammeter.address\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.address }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费名称：\" prop=\"payname\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.payname\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.payname != null && oldData.payname != ammeter.payname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.payname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费类型：\" prop=\"payperiod\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.payperiod\"\r\n                        category=\"payPeriod\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.payperiod != null &&\r\n                          oldData.payperiod != ammeter.payperiod\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPayperiod }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"缴费经办人：\" prop=\"paymanager\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.paymanager\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.paymanager != null &&\r\n                          oldData.paymanager != ammeter.paymanager\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.paymanager }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"付费方式：\" prop=\"paytype\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.paytype\"\r\n                        category=\"payType\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.paytype != null && oldData.paytype != ammeter.paytype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldPaytype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"管理负责人：\" prop=\"ammetermanager\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.ammetermanager\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ammetermanager != null &&\r\n                          oldData.ammetermanager != ammeter.ammetermanager\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ammetermanager }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"单价(元)：\" prop=\"price\">\r\n                      <InputNumber :maxlength=\"20\" v-model=\"ammeter.price\"></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.price != null && oldData.price != ammeter.price\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.price }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"用电类型：\" prop=\"classifications\">\r\n                      <Cascader\r\n                        :data=\"classificationData\"\r\n                        :change-on-select=\"true\"\r\n                        v-model=\"ammeter.classifications\"\r\n                        @on-change=\"changeClassifications\"\r\n                      ></Cascader>\r\n                      <label\r\n                        v-if=\"\r\n                          ammeter.classifications !== undefined &&\r\n                          ammeter.classifications.length > 0\r\n                        \"\r\n                      >\r\n                        <label v-for=\"(item, i) in ammeter.classifications\" :key=\"i\">\r\n                          <label\r\n                            v-if=\"\r\n                              i === ammeter.classifications.length - 1 &&\r\n                              oldData.electrotype != null &&\r\n                              oldData.electrotype != item\r\n                            \"\r\n                            style=\"color: red\"\r\n                            >历史数据：{{ oldData.electrotypename }}</label\r\n                          >\r\n                        </label>\r\n                      </label>\r\n                      <label\r\n                        v-else-if=\"\r\n                          ammeter.classifications !== undefined &&\r\n                          ammeter.classifications.length <= 0\r\n                        \"\r\n                      >\r\n                        <label v-if=\"oldData.electrotype != null\" style=\"color: red\"\r\n                          >历史数据：{{ oldData.electrotypename }}</label\r\n                        >\r\n                      </label>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"倍率：\"\r\n                      prop=\"magnification\"\r\n                      :class=\"{ requireStar: true }\"\r\n                    >\r\n                      <InputNumber\r\n                        :min=\"1\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.magnification\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.magnification != null &&\r\n                          oldData.magnification != ammeter.magnification\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.magnification }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"对外结算类型：\" prop=\"directsupplyflag\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.directsupplyflag\"\r\n                        category=\"directSupplyFlag\"\r\n                        :disabled=\"iszgzOnly\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.directsupplyflag != null &&\r\n                          oldData.directsupplyflag != ammeter.directsupplyflag\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldDirectsupplyflag }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem label=\"电价性质：\" prop=\"electrovalencenature\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.electrovalencenature\"\r\n                        category=\"electrovalenceNature\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.electrovalencenature != null &&\r\n                          oldData.electrovalencenature != ammeter.electrovalencenature\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldElectrovalencenature }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"翻表读数(度)：\" prop=\"maxdegree\">\r\n                      <InputNumber\r\n                        :maxlength=\"20\"\r\n                        v-model=\"ammeter.maxdegree\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.maxdegree != null &&\r\n                          oldData.maxdegree != ammeter.maxdegree\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.maxdegree }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"状态：\" prop=\"status\">\r\n                      <cl-select\r\n                        v-model=\"ammeter.status\"\r\n                        @on-change=\"changeStatus\"\r\n                        category=\"status\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"oldData.status != null && oldData.status != ammeter.status\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStatus }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"占局(站)定额电量比例(%)：\" prop=\"quotapowerratio\">\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :min=\"1\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.quotapowerratio\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.quotapowerratio != null &&\r\n                          oldData.quotapowerratio != ammeter.quotapowerratio\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.quotapowerratio }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"合同对方：\" prop=\"contractOthPart\">\r\n                      <cl-input\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.contractOthPart\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.contractOthPart != null &&\r\n                          oldData.contractOthPart != ammeter.contractOthPart\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.contractOthPart }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-else-if=\"!isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\">\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}</Option\r\n                        >\r\n                      </Select>\r\n                      <!--                                            <cl-select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\"-->\r\n                      <!--                                                       category=\"property\"-->\r\n                      <!--                                                       labelField=\"typeName\" valueField=\"typeCode\"></cl-select>-->\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem label=\"站址产权归属：\" prop=\"property\">\r\n                      <Select v-model=\"ammeter.property\" :disabled=\"propertyReadonly\">\r\n                        <Option\r\n                          :disabled=\"\r\n                            ammeter.electrotype != 1411 &&\r\n                            ammeter.electrotype != 1412 &&\r\n                            item.typeCode == 2\r\n                              ? true\r\n                              : false\r\n                          \"\r\n                          v-for=\"item in propertyList\"\r\n                          :value=\"item.typeCode\"\r\n                          :key=\"item.typeCode\"\r\n                          >{{ item.typeName }}</Option\r\n                        >\r\n                      </Select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.property != null && oldData.property != ammeter.property\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldProperty }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否换表：\" prop=\"ischangeammeter\">\r\n                      <RadioGroup v-model=\"ammeter.ischangeammeter\">\r\n                        <Radio label=\"0\" disabled>\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\" disabled>\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ischangeammeter != null &&\r\n                          oldData.ischangeammeter != ammeter.ischangeammeter\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.ischangeammeter == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"原电表/协议编号：\" prop=\"oldAmmeterName\">\r\n                      <cl-input v-model=\"ammeter.oldAmmeterName\" readonly></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.oldAmmeterName != null &&\r\n                          oldData.oldAmmeterName != ammeter.oldAmmeterName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.oldAmmeterName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"原电表还需报账电量(度)：\" prop=\"oldBillPower\">\r\n                      <cl-input\r\n                        :maxlength=\"20\"\r\n                        v-model=\"ammeter.oldBillPower\"\r\n                        :placeholder=\"\r\n                          [2, 4].includes(ammeter.property) ? '需填写【分摊后电量】' : ''\r\n                        \"\r\n                      ></cl-input>\r\n                      <label style=\"color: red; margin-left: -100px; margin-top: 5px\"\r\n                        >注意：该电量将计入新表的总电量(分割后电量)，没有请填0</label\r\n                      >\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.oldBillPower != null &&\r\n                          oldData.oldBillPower != ammeter.oldBillPower\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.oldBillPower }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"!isCDCompany\">\r\n                    <FormItem\r\n                      label=\"分割比例(%)：\"\r\n                      prop=\"percent\"\r\n                      v-if=\"ammeter.property === 2 || ammeter.property === 4\"\r\n                    >\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.percent\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.percent != null && oldData.percent != ammeter.percent\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.percent }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\" v-if=\"isCDCompany\">\r\n                    <FormItem\r\n                      label=\"分割比例(%)：\"\r\n                      prop=\"percent\"\r\n                      v-if=\"ammeter.property === 2 || ammeter.property === 4\"\r\n                    >\r\n                      <InputNumber\r\n                        :max=\"100\"\r\n                        :maxlength=\"15\"\r\n                        v-model=\"ammeter.percent\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.percent != null && oldData.percent != ammeter.percent\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.percent }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"收款客户名称：\"\r\n                      prop=\"customerName\"\r\n                      v-if=\"ammeter.ammeteruse === 3\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.customerName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"addCustomer\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.customerName != null &&\r\n                          oldData.customerName != ammeter.customerName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.customerName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN')\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"是否铁塔包干：\" prop=\"islumpsum\">\r\n                      <RadioGroup\r\n                        v-model=\"ammeter.islumpsum\"\r\n                        @on-change=\"updatepackagetype\"\r\n                      >\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.islumpsum != null &&\r\n                          oldData.islumpsum != ammeter.islumpsum\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.islumpsum == \"0\" ? \"否\" : \"是\" }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"月包干电量(度)：\" prop=\"ybgPower\">\r\n                      <cl-input :maxlength=\"20\" v-model=\"ammeter.ybgPower\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.ybgPower != null && oldData.ybgPower != ammeter.ybgPower\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.ybgPower }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干起始日期：\" prop=\"lumpstartdate\">\r\n                      <cl-date-picker\r\n                        type=\"date\"\r\n                        placeholder=\"包干起始日期\"\r\n                        v-model=\"ammeter.lumpstartdate\"\r\n                        style=\"width: 160px\"\r\n                      ></cl-date-picker>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.lumpstartdate != null &&\r\n                          oldData.lumpstartdate != ammeter.lumpstartdate\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.lumpstartdate }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"包干截止日期：\" prop=\"lumpenddate\">\r\n                      <cl-date-picker\r\n                        type=\"date\"\r\n                        placeholder=\"包干截止日期\"\r\n                        v-model=\"ammeter.lumpenddate\"\r\n                        style=\"width: 160px\"\r\n                      ></cl-date-picker>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.lumpenddate != null &&\r\n                          oldData.lumpenddate != ammeter.lumpenddate\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.lumpenddate }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'ln' || configVersion == 'LN'\">\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.property == 2 &&\r\n                      (configVersion == 'ln' || configVersion == 'LN') &&\r\n                      ammeter.islumpsum == 1\r\n                    \"\r\n                  >\r\n                    <FormItem label=\"月包干电费：\" prop=\"fee\">\r\n                      <InputNumber\r\n                        :min=\"0\"\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.fee\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"oldData.fee != null && oldData.fee != ammeter.fee\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.fee }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc' || configVersion == 'SC'\">\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"输配电公司：\"\r\n                      prop=\"transdistricompany\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: true,\r\n                          type: 'number',\r\n                          message: '请选择',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <cl-select\r\n                        v-model=\"ammeter.transdistricompany\"\r\n                        category=\"transdistricompany\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.transdistricompany != null &&\r\n                          oldData.transdistricompany != ammeter.transdistricompany\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldtransdistricompany }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col\r\n                    span=\"6\"\r\n                    v-if=\"\r\n                      ammeter.directsupplyflag == 1 && ammeter.transdistricompany == 1\r\n                    \"\r\n                  >\r\n                    <FormItem\r\n                      label=\"电压等级：\"\r\n                      prop=\"voltageClass\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: true,\r\n                          type: 'number',\r\n                          message: '请选择',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <cl-select\r\n                        v-model=\"ammeter.voltageClass\"\r\n                        category=\"voltageClass\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.voltageClass != null &&\r\n                          oldData.voltageClass != ammeter.voltageClass\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldvoltageClass }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"configVersion == 'sc'\">\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"是否转改直：\"\r\n                      prop=\"iszgz\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.iszgz\" @on-change=\"iszgzchange\">\r\n                        <Radio label=\"0\" :disabled=\"disablediszgz\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"原转供电表编号：\"\r\n                      prop=\"oldammetername\"\r\n                      v-if=\"ammeter.iszgz == '1'\"\r\n                      :rules=\"[\r\n                        {\r\n                          required: ammeter.iszgz == '1',\r\n                          message: '不能为空',\r\n                          trigger: 'blur',\r\n                        },\r\n                      ]\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        :value=\"\r\n                          ammeter.oldammetername\r\n                            ? ammeter.oldammetername.split(',')[0]\r\n                            : null\r\n                        \"\r\n                        readonly\r\n                        :disabled=\"disablediszgz\"\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseoldammetername\"\r\n                      />\r\n                    </FormItem>\r\n                    <ChooseAmmeterModel\r\n                      ref=\"chooseAmmeterModel\"\r\n                      v-if=\"ammeter.iszgz == '1'\"\r\n                      v-on:getAmmeterModelModal=\"getAmmeterModelModal\"\r\n                    />\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"是否直购电：\"\r\n                      prop=\"directFlag\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.directFlag\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\" v-if=\"ammeter.directsupplyflag == 1\">\r\n                    <FormItem\r\n                      label=\"是否含办公：\"\r\n                      prop=\"officeFlag\"\r\n                      :rules=\"[{ required: true, message: '请选择', trigger: 'blur' }]\"\r\n                    >\r\n                      <RadioGroup v-model=\"ammeter.officeFlag\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel2\"\r\n          >关联局站信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter1\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem\r\n                      label=\"局(站)名称：\"\r\n                      prop=\"stationName\"\r\n                      :class=\"{ requireStar: isRequireFlag }\"\r\n                    >\r\n                      <Input\r\n                        icon=\"ios-archive\"\r\n                        v-model=\"ammeter.stationName\"\r\n                        readonly\r\n                        placeholder=\"点击图标选择\"\r\n                        @on-click=\"chooseResponseCenter(1)\"\r\n                      />\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationName != null &&\r\n                          oldData.stationName != ammeter.stationName\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationName }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)编码：\" prop=\"stationcode\">\r\n                      <cl-input readonly v-model=\"ammeter.stationcode\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationcode != null &&\r\n                          oldData.stationcode != ammeter.stationcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <!--                                            <FormItem label=\"局(站)状态\" prop=\"stationstatus\">-->\r\n                    <!--                                                <cl-select v-model=\"ammeter.stationstatus\"-->\r\n                    <!--                                                           category=\"status\"-->\r\n                    <!--                                                           labelField=\"typeName\" valueField=\"typeCode\"></cl-select>-->\r\n                    <!--                                                <label v-if=\"oldData.stationstatus != null &&oldData.stationstatus != ammeter.stationstatus\"-->\r\n                    <!--                                                       style=\"color: red;\">历史数据：{{oldStationstatus}}</label>-->\r\n                    <!--                                            </FormItem>-->\r\n                    <FormItem label=\"局(站)状态：\" prop=\"stationstatus\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.stationstatus\"\r\n                        filterable\r\n                        category=\"stationStatus\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationstatus != null &&\r\n                          oldData.stationstatus != ammeter.stationstatus\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStationstatus }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)类型：\" prop=\"stationtype\">\r\n                      <cl-select\r\n                        disabled\r\n                        v-model=\"ammeter.stationtype\"\r\n                        filterable\r\n                        category=\"BUR_STAND_TYPE\"\r\n                        labelField=\"typeName\"\r\n                        valueField=\"typeCode\"\r\n                      ></cl-select>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationtype != null &&\r\n                          oldData.stationtype != ammeter.stationtype\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldStationtype }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"局(站)地址：\" prop=\"stationaddress\">\r\n                      <cl-input readonly v-model=\"ammeter.stationaddress\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.stationaddress != null &&\r\n                          oldData.stationaddress != ammeter.stationaddress\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.stationaddress }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"资源局站id：\" prop=\"termname\">\r\n                      <cl-input\r\n                        readonly\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.termname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.termname != null &&\r\n                          oldData.termname != ammeter.termname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.termname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"是否有空调：\" prop=\"isairconditioning\">\r\n                      <RadioGroup v-model=\"ammeter.isairconditioning\">\r\n                        <Radio label=\"0\">\r\n                          <span>否</span>\r\n                        </Radio>\r\n                        <Radio label=\"1\">\r\n                          <span>是</span>\r\n                        </Radio>\r\n                      </RadioGroup>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.isairconditioning != null &&\r\n                          oldData.isairconditioning != ammeter.isairconditioning\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{\r\n                          oldData.isairconditioning == \"0\" ? \"否\" : \"是\"\r\n                        }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"核定电量：\" prop=\"vouchelectricity\">\r\n                      <InputNumber\r\n                        :maxlength=\"30\"\r\n                        v-model=\"ammeter.vouchelectricity\"\r\n                      ></InputNumber>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.vouchelectricity != null &&\r\n                          oldData.vouchelectricity != ammeter.vouchelectricity\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.vouchelectricity }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row v-if=\"isCDCompany\">\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管C网编号：\" prop=\"nmCcode\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmCcode\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmCcode != null && oldData.nmCcode != ammeter.nmCcode\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmCcode }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号L2.1G：\" prop=\"nmL2100\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmL2100\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmL2100 != null && oldData.nmL2100 != ammeter.nmL2100\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmL2100 }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号L1.8G：\" prop=\"nmL1800\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmL1800\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmL1800 != null && oldData.nmL1800 != ammeter.nmL1800\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmL1800 }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"网管编号C+L800M：\" prop=\"nmCl800m\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.nmCl800m\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.nmCl800m != null && oldData.nmCl800m != ammeter.nmCl800m\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.nmCl800m }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel3\"\r\n          >关联用电类型比例\r\n          <!--                        <span style=\"font-size: 10px;color:red\">（用电类型为：A类机楼、B类机楼、C类机楼、管理办公用电时必须关联用电类型比例）</span>-->\r\n          <div slot=\"content\" v-if=\"isClassification && ammeter.stationcode != null\">\r\n            <cl-table\r\n              ref=\"ammeterTable\"\r\n              strip\r\n              :columns=\"electro.columns\"\r\n              :data=\"electro.data\"\r\n              :searchable=\"false\"\r\n              :showPage=\"false\"\r\n              selectEnabled\r\n              selectMultiple\r\n            >\r\n              <div slot=\"buttons\">\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"md-add\"\r\n                  type=\"primary\"\r\n                  @click=\"addElectricType\"\r\n                  >新增\r\n                </Button>\r\n                <Button\r\n                  size=\"default\"\r\n                  icon=\"ios-trash-outline\"\r\n                  type=\"warning\"\r\n                  @click=\"removeElectricType\"\r\n                  >删除\r\n                </Button>\r\n              </div>\r\n            </cl-table>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel4\"\r\n          >业主信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <Form\r\n                :model=\"ammeter\"\r\n                ref=\"ammeter2\"\r\n                :rules=\"ruleValidate\"\r\n                :label-width=\"110\"\r\n                class=\"margin-right-width\"\r\n              >\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"联系人：\" prop=\"contractname\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.contractname\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.contractname != null &&\r\n                          oldData.contractname != ammeter.contractname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.contractname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"具体位置：\" prop=\"location\">\r\n                      <cl-input :maxlength=\"100\" v-model=\"ammeter.location\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.location != null && oldData.location != ammeter.location\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.location }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"办公电话：\" prop=\"officephone\">\r\n                      <cl-input :maxlength=\"15\" v-model=\"ammeter.officephone\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.officephone != null &&\r\n                          oldData.officephone != ammeter.officephone\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.officephone }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"移动电话：\" prop=\"telephone\">\r\n                      <cl-input :maxlength=\"15\" v-model=\"ammeter.telephone\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.telephone != null &&\r\n                          oldData.telephone != ammeter.telephone\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.telephone }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"对方单位：\" prop=\"userunit\">\r\n                      <cl-input :maxlength=\"50\" v-model=\"ammeter.userunit\"></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.userunit != null && oldData.userunit != ammeter.userunit\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.userunit }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款账户：\" prop=\"receiptaccountname\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.receiptaccountname\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccountname != null &&\r\n                          oldData.receiptaccountname != ammeter.receiptaccountname\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccountname }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款开户支行：\" prop=\"receiptaccountbank\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.receiptaccountbank\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccountbank != null &&\r\n                          oldData.receiptaccountbank != ammeter.receiptaccountbank\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccountbank }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                  <Col span=\"6\">\r\n                    <FormItem label=\"收款账号：\" prop=\"receiptaccounts\">\r\n                      <cl-input\r\n                        :maxlength=\"50\"\r\n                        v-model=\"ammeter.receiptaccounts\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"\r\n                          oldData.receiptaccounts != null &&\r\n                          oldData.receiptaccounts != ammeter.receiptaccounts\r\n                        \"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.receiptaccounts }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n                <Row>\r\n                  <Col span=\"24\">\r\n                    <FormItem label=\"说明：\" prop=\"memo\">\r\n                      <cl-input\r\n                        type=\"textarea\"\r\n                        :rows=\"3\"\r\n                        v-model=\"ammeter.memo\"\r\n                      ></cl-input>\r\n                      <label\r\n                        v-if=\"oldData.memo != null && oldData.memo != ammeter.memo\"\r\n                        style=\"color: red\"\r\n                        >历史数据：{{ oldData.memo }}</label\r\n                      >\r\n                    </FormItem>\r\n                  </Col>\r\n                </Row>\r\n              </Form>\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n        <Panel name=\"Panel6\"\r\n          >附件信息\r\n          <div slot=\"content\">\r\n            <Row class=\"form-panel\">\r\n              <attach-file\r\n                :param=\"fileParam\"\r\n                :attachData=\"attachData\"\r\n                v-on:setAttachData=\"setAttachData\"\r\n              />\r\n            </Row>\r\n          </div>\r\n        </Panel>\r\n      </Collapse>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getstationold } from \"@/api/alertcontrol/alertcontrol\";\r\nimport {\r\n  listElectricType,\r\n  checkAmmeterExist,\r\n  getCountrysdata,\r\n  addAmmeter,\r\n  editAmmeter,\r\n  editAmmeterRecord,\r\n  updateAmmeter,\r\n  checkProjectNameExist,\r\n  checkAmmeterByStation,\r\n  getClassification,\r\n  getClassificationId,\r\n  getUserdata,\r\n  checkClassificationLevel,\r\n  listElectricTypeRatio,\r\n  checkAcountByUpdate,\r\n  getUserByUserRole,\r\n  getCountryByUserId,\r\n  attchList,\r\n  removeAttach,\r\n  getChangeAmmeterid,\r\n} from \"@/api/basedata/ammeter.js\";\r\nimport { blist, btext } from \"@/libs/tools\";\r\nimport SelectElectricType from \"./selectElectricType\";\r\nimport countryModal from \"./countryModal\";\r\nimport stationModal from \"./stationModal\";\r\nimport { mapMutations } from \"vuex\";\r\nimport WorkFlowInfoComponet from \"@/view/basic/system/workflow/workFlowInfoComponet\";\r\nimport AmmeterProtocolList from \"@/view/basedata/quota/listAmmeterProtocol\";\r\nimport customerList from \"./customerModal\";\r\nimport ChooseAmmeterModel from \"@/view/basedata/ammeter/chooseAmmeterModel\";\r\nimport axios from \"@/libs/api.request\";\r\nimport { isEmpty } from \"@/libs/validate\";\r\nimport attachFile from \"@/view/basedata/ammeter/attachFile\";\r\nexport default {\r\n  name: \"change1Ammeter\",\r\n  components: {\r\n    stationModal,\r\n    customerList,\r\n    countryModal,\r\n    SelectElectricType,\r\n    WorkFlowInfoComponet,\r\n    AmmeterProtocolList,\r\n    ChooseAmmeterModel,\r\n    attachFile,\r\n  },\r\n  data() {\r\n    //不能输入汉字\r\n    const checkData = (rule, value, callback) => {\r\n      if (value) {\r\n        if (/[\\u4E00-\\u9FA5]/g.test(value)) {\r\n          callback(new Error(\"编码不能输入汉字!\"));\r\n        } else if (escape(value).indexOf(\"%u\") >= 0) {\r\n          callback(new Error(\"编码不能输入中文字符!\"));\r\n        } else {\r\n          callback();\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatorNumber = (rule, value, callback) => {\r\n      if (value.length <= 0) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatorNumberZero = (rule, value, callback) => {\r\n      if (value != null && value == 0) {\r\n        callback(new Error(\"只能输入大于0的数\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validatorNumberZero1 = (rule, value, callback) => {\r\n      if (value != null && value < 0) {\r\n        callback(new Error(\"只能输入大于等于0的数\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    const validateClassifications = (rule, value, callback) => {\r\n      if (value == undefined || value == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      } else {\r\n        if (value.length <= 0) {\r\n          callback(new Error(\"不能为空\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatelumpstartdate = (rule, value, callback) => {\r\n      let data = this.ammeter;\r\n      let start = data.lumpstartdate;\r\n      let end = data.lumpenddate;\r\n      if (start == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      }\r\n      if (start != null && end != null) {\r\n        if (end <= start) {\r\n          callback(new Error(\"包干起始日期不能大于等于截止日期\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    const validatelumpenddate = (rule, value, callback) => {\r\n      let data = this.ammeter;\r\n      let start = data.lumpstartdate;\r\n      let end = data.lumpenddate;\r\n      if (end == null) {\r\n        callback(new Error(\"不能为空\"));\r\n      }\r\n      if (start != null && end != null) {\r\n        if (end <= start) {\r\n          callback(new Error(\"包干截止日期不能小于等于起始日期\"));\r\n        }\r\n      }\r\n      callback();\r\n    };\r\n    //更改标题名称及样式\r\n    let renderHeader = (h, params) => {\r\n      let t = h(\r\n        \"span\",\r\n        {\r\n          style: {\r\n            fontWeight: \"normal\",\r\n            color: \"#ed4014\",\r\n            fontSize: \"12px\",\r\n            fontFamily: \"SimSun\",\r\n            marginRight: \"4px\",\r\n            lineHeight: 1,\r\n            display: \"inline-block\",\r\n          },\r\n        },\r\n        \"*\"\r\n      );\r\n      return h(\"div\", [t, h(\"span\", {}, \"所占比例(%)\")]);\r\n    };\r\n    return {\r\n      propertyright: null, //局站产权\r\n      isRequireFlag: false, //局站是否必填\r\n      modal1: false,\r\n      checkStationType: null,\r\n      ischeckStation: false, //是否需要验证局站只能关联5个\r\n      isoldcheckStation: null, //判断用户关联局站没有,默认没有\r\n      isCDCompany: false, //是否是成都分公司\r\n      configVersion: null, //版本\r\n      propertyList: [],\r\n      propertyReadonly: true,\r\n\r\n      workFlowParams: {},\r\n      hisParams: {},\r\n      isShowFlow: false,\r\n      showWorkFlow: false,\r\n      flowName: null,\r\n\r\n      isError: false, //用电类型比例验证\r\n      isError1: false, //用电类型比例验证\r\n\r\n      loading: false,\r\n      isLoading: null,\r\n\r\n      showModel: false,\r\n      isClassification: false,\r\n      title: \"\",\r\n      isEditByCountry: false,\r\n      isCityAdmin: false,\r\n      isAdmin: false,\r\n      chooseIndex: null,\r\n      electroRowNum: null, //关联用电类型的当前行\r\n      electricTypeModel: false,\r\n      companies: [],\r\n      departments: [],\r\n      classificationData: [], //用电类型\r\n\r\n      oldData: [],\r\n      oldCategory: \"\", //原始数据\r\n      oldPackagetype: \"\", //原始数据\r\n      oldPayperiod: \"\", //原始数据\r\n      oldPaytype: \"\", //原始数据\r\n      oldElectronature: \"\", //原始数据\r\n      oldElectrovalencenature: \"\", //原始数据\r\n      oldElectrotype: \"\", //原始数据\r\n      oldStatus: \"\", //原始数据\r\n      oldProperty: \"\", //原始数据\r\n      oldAmmetertype: \"\", //原始数据\r\n      oldStationstatus: \"\", //原始数据\r\n      oldStationtype: \"\", //原始数据\r\n      oldAmmeteruse: \"\", //原始数据\r\n      oldDirectsupplyflag: \"\", //原始数据\r\n      attachData: [],\r\n      fileParam: {\r\n        busiId: \"\",\r\n        busiAlias: \"附件(协议管理)\",\r\n        categoryCode: \"file\",\r\n        areaCode: \"ln\",\r\n      },\r\n      ruleValidate: {\r\n        isentityammeter: [\r\n          { required: true, message: \"不能为空\", trigger: \"change,blur\" },\r\n        ],\r\n        projectname: [\r\n          //项目名称\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        countryName: [\r\n          //所属部门\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        country: [\r\n          //所属部门\r\n          { required: true, type: \"number\", validator: validatorNumber, trigger: \"blur\" },\r\n        ],\r\n        company: [{ required: true, validator: validatorNumber, trigger: \"blur\" }],\r\n        paytype: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        payperiod: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        ammeteruse: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        ammetertype: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        electrovalencenature: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        classifications: [\r\n          { required: true, validator: validateClassifications, trigger: \"change,blur\" },\r\n        ],\r\n        magnification: [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        directsupplyflag: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            type: \"number\",\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        packagetype: [\r\n          { required: true, type: \"number\", message: \"不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contractOthPart: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\r\n        stationName: [],\r\n        status: [\r\n          {\r\n            required: true,\r\n            type: \"number\",\r\n            message: \"不能为空\",\r\n            trigger: \"change,blur\",\r\n          },\r\n        ],\r\n        telephone: [{ pattern: /^1\\d{10}$/, message: \"格式不正确\", trigger: \"blur\" }],\r\n        percent: [\r\n          { type: \"number\", validator: validatorNumberZero, trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([0-9]\\d{0,12}))(\\.\\d{0,4})?$/,\r\n            message: \"只能保留四位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lumpstartdate: [\r\n          {\r\n            required: true,\r\n            type: \"date\",\r\n            validator: validatelumpstartdate,\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        lumpenddate: [\r\n          {\r\n            required: true,\r\n            type: \"date\",\r\n            validator: validatelumpenddate,\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        fee: [\r\n          { required: true, type: \"number\", validator: validatorNumber, trigger: \"blur\" },\r\n          {\r\n            pattern: /^(([1-9]\\d{0,14})|0)(\\.\\d{0,2})?$/,\r\n            message: \"只能保留两位小数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        supplybureauammetercode: [],\r\n        transdistricompany: [],\r\n        voltageClass: [],\r\n      },\r\n      electro: {\r\n        columns: [\r\n          {\r\n            title: \"序号\",\r\n            type: \"index\",\r\n          },\r\n          {\r\n            title: \"用电类型\",\r\n            key: \"typeName\",\r\n          },\r\n\r\n          {\r\n            title: \"所占比例(%)\",\r\n            key: \"ratio\",\r\n            renderHeader: renderHeader,\r\n            render: (h, params) => {\r\n              let that = this;\r\n              let ratio = params.row.ratio;\r\n              let isError1 = params.row.idError1;\r\n              let error = h(\r\n                \"label\",\r\n                {\r\n                  style: {\r\n                    color: \"#ed4014\",\r\n                    fontSize: \"12px\",\r\n                    fontFamily: \"SimSun\",\r\n                    paddingTop: \"6px\",\r\n                    lineHeight: 1,\r\n                    fontWeight: \"bold\",\r\n                    display: null != ratio ? \"none\" : \"inline-block\",\r\n                  },\r\n                },\r\n                \"不能为空\"\r\n              );\r\n              let error1 = h(\r\n                \"label\",\r\n                {\r\n                  style: {\r\n                    color: \"#ed4014\",\r\n                    fontSize: \"12px\",\r\n                    fontFamily: \"SimSun\",\r\n                    paddingTop: \"6px\",\r\n                    lineHeight: 1,\r\n                    fontWeight: \"bold\",\r\n                    display: isError1 == true ? \"inline-block\" : \"none\",\r\n                  },\r\n                },\r\n                \"输入比例不合格要求\"\r\n              );\r\n              let result = h(\"InputNumber\", {\r\n                style: {\r\n                  border: null == ratio || isError1 == true ? \"1px solid #ed4014\" : \"\",\r\n                },\r\n                props: {\r\n                  value: ratio,\r\n                  max: 100,\r\n                  min: 0.1,\r\n                },\r\n                on: {\r\n                  \"on-change\": (v) => {\r\n                    if (v == undefined || v == null) {\r\n                      that.isError = true;\r\n                    } else {\r\n                      that.isError = false;\r\n                    }\r\n                    //给data重新赋值\r\n                    // let reg = /^(?:[1-9]?\\d|100)$/;\r\n                    let reg = /^-?(([1-9][0-9]*)|(([0]\\.\\d{1,2}|[1-9][0-9]*\\.\\d{1,2})))$/;\r\n                    if (v != undefined && v != null && !reg.test(v)) {\r\n                      params.row.idError1 = true;\r\n                      that.isError1 = true;\r\n                    } else {\r\n                      params.row.idError1 = false;\r\n                      that.isError1 = false;\r\n                    }\r\n                    params.row.ratio = v;\r\n                    that.electro.data[params.row._index] = params.row;\r\n                  },\r\n                },\r\n              });\r\n              return h(\"div\", [result, error, error1]);\r\n            },\r\n          },\r\n          {\r\n            title: \"关联局站\",\r\n            key: \"stationName\",\r\n            render: (h, params) => {\r\n              let stationName = params.row.stationName;\r\n              let disabled = params.row._disabled;\r\n              if (disabled != undefined && disabled == true) {\r\n                return h(\"Input\", {\r\n                  props: {\r\n                    value: stationName,\r\n                    readonly: true,\r\n                  },\r\n                });\r\n              } else {\r\n                return h(\"Input\", {\r\n                  props: {\r\n                    value: stationName,\r\n                    icon: \"ios-archive\",\r\n                    placeholder: \"点击图标选择\",\r\n                    readonly: true,\r\n                  },\r\n                  on: {\r\n                    \"on-click\": (v) => {\r\n                      this.chooseResponseCenter(2, params, params.row._index);\r\n                    },\r\n                  },\r\n                });\r\n              }\r\n            },\r\n          },\r\n        ],\r\n        data: [],\r\n      },\r\n      ammeter: {\r\n        id: null,\r\n        country: null,\r\n        company: null,\r\n        countryName: \"\",\r\n        electricTypes: [],\r\n        electro: [],\r\n        classifications: [], //用电类型\r\n      },\r\n      iszgzOnly: false,\r\n      disablediszgz: false,\r\n      electricType: {\r\n        loading: false,\r\n        filter: [\r\n          {\r\n            formItemType: \"input\",\r\n            prop: \"name\",\r\n            label: \"用电类型\",\r\n            width: 100,\r\n            size: \"small\",\r\n          },\r\n        ],\r\n        columns: [\r\n          {\r\n            title: \"序号\",\r\n            type: \"index\",\r\n            align: \"center\",\r\n            width: 70,\r\n          },\r\n          {\r\n            title: \"id\",\r\n            key: \"id\",\r\n            align: \"center\",\r\n            width: 80,\r\n          },\r\n          {\r\n            title: \"用电类型\",\r\n            key: \"typeName\",\r\n            align: \"center\",\r\n            width: 80,\r\n          },\r\n        ],\r\n        data: [],\r\n        total: 0,\r\n        pageSize: 10,\r\n      },\r\n      oldAmmeterId: undefined, //换表原表ID\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    ...mapMutations([\"closeTag\", \"closeTagByName\"]),\r\n    onModalOK(type) {\r\n      console.log(\"@@@@@@@@@@@@\");\r\n      // this.checkStationType = type;\r\n      // if(type == 1){\r\n      //     this.isLoading = 1;\r\n      // }else{\r\n      //     this.isLoading = 0;\r\n      // }\r\n      // if(this.loading == true){\r\n      //     return ;\r\n      // }\r\n      // this.loading = true;\r\n      let flag = false;\r\n      let flag1 = false;\r\n      // this.ammeter.electricTypes = this.electro.data;\r\n      this.$refs.ammeter.validate((valid) => {\r\n        if (valid) {\r\n          console.log(\"1111111111\");\r\n          flag = true;\r\n        }\r\n      });\r\n      this.$refs.ammeter1.validate((valid1) => {\r\n        if (valid1) {\r\n          flag1 = true;\r\n        }\r\n      });\r\n      if (flag && flag1 && !this.isError && !this.isError1) {\r\n        this.checkData(type);\r\n      } else {\r\n        this.$Message.error(\"验证没通过\");\r\n        this.loading = false;\r\n      }\r\n    },\r\n    //验证数据\r\n    checkData(type) {\r\n      let types = this.ammeter.classifications;\r\n      this.ammeter.electrotype = types[types.length - 1];\r\n      let that = this;\r\n      if (\r\n        this.ammeter.status === 1 &&\r\n        (this.configVersion == \"sc\" || this.configVersion == \"SC\")\r\n      ) {\r\n        //在用状态下验证局站地址不能为空\r\n        if (\r\n          this.ammeter.stationaddress == null ||\r\n          this.ammeter.stationaddress == undefined\r\n        ) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"局站地址不能为空，请在局站管理维护该局站信息！\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      if (this.checkStationElec()) {\r\n        //验证用电类型和局站类型是否匹配\r\n        if (this.checkElectricTypeItem()) {\r\n          // if(this.configVersion != \"ln\" && this.configVersion != \"LN\" ) {\r\n          //     checkAmmeterExist(this.ammeter.id, this.ammeter.ammetername, 0).then(res => {//验证电表是否存在\r\n          //         let code = res.data.code;\r\n          //         if (code == 0) {\r\n          //             that.checkedDate(type);\r\n          //         } else {\r\n          //             that.loading = false;\r\n          //         }\r\n          //     });\r\n          // }else{\r\n          that.checkedDate(type);\r\n          // }\r\n        }\r\n      }\r\n    },\r\n    checkStationElec() {\r\n      let electrotype = this.ammeter.electrotype;\r\n      let stationtype = this.ammeter.stationtype;\r\n      if (electrotype === 111 || electrotype === 112 || electrotype === 113) {\r\n        if (stationtype !== 10001) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      } else if (electrotype === 121 || electrotype === 112) {\r\n        if (stationtype !== 10003 && stationtype !== 10004) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      } else if (electrotype === 131 || electrotype === 132 || electrotype === 133) {\r\n        if (stationtype !== 10005) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      } else if (\r\n        electrotype === 1411 ||\r\n        electrotype === 1412 ||\r\n        electrotype === 1421 ||\r\n        electrotype === 1422 ||\r\n        electrotype === 1431 ||\r\n        electrotype === 1432\r\n      ) {\r\n        if (stationtype !== 10002) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站类型不匹配，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      if (this.configVersion == \"sc\" && this.ammeter.stationaddresscode) {\r\n        //“51”开头铁塔站址编码控制\r\n        if (\r\n          [1411, 1412].includes(electrotype) &&\r\n          !this.ammeter.stationaddresscode.startsWith(\"51\")\r\n        ) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型和局站站址编码不匹配(51开头为铁塔站址编码)，请确认\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    okModel() {\r\n      //不验证个数\r\n      this.isoldcheckStation = false;\r\n      this.saveData(this.checkStationType); //保存数据\r\n    },\r\n    cancelModel() {\r\n      this.isoldcheckStation = null;\r\n      this.$Modal.warning({ title: \"温馨提示\", content: this.errorMessage });\r\n      this.loading = false;\r\n    },\r\n    checkedDate(type) {\r\n      let that = this;\r\n      checkProjectNameExist(that.oldAmmeterId, that.ammeter.projectname, 0).then(\r\n        (res) => {\r\n          //验证项目名称是否存在\r\n          let code = res.data.code;\r\n          if (code == 0) {\r\n            if (\r\n              that.ammeter.stationcode != undefined &&\r\n              that.ammeter.stationcode != null &&\r\n              (that.ammeter.electrotype == 1411 || that.ammeter.electrotype == 1412)\r\n            ) {\r\n              //判断是否铁塔\r\n              if (that.propertyright == null) {\r\n                //判断是否铁塔\r\n                getstationold(that.ammeter.stationcode).then((res) => {\r\n                  //验证项目名称是否存在\r\n                  that.propertyright = res.data.propertyright;\r\n                  if (that.propertyright != 3) {\r\n                    this.$Modal.warning({\r\n                      title: \"温馨提示\",\r\n                      content: \"用电类型和局站类型或产权不匹配，请确认\",\r\n                    });\r\n                    this.loading = false;\r\n                  } else {\r\n                    that.isCheckStation(type);\r\n                  }\r\n                });\r\n              } else {\r\n                if (that.propertyright != 3) {\r\n                  this.$Modal.warning({\r\n                    title: \"温馨提示\",\r\n                    content: \"用电类型和局站类型或产权不匹配，请确认\",\r\n                  });\r\n                  this.loading = false;\r\n                } else {\r\n                  that.isCheckStation(type);\r\n                }\r\n              }\r\n            } else {\r\n              that.isCheckStation(type);\r\n            }\r\n          }\r\n        }\r\n      );\r\n    },\r\n    isCheckStation(type) {\r\n      let that = this;\r\n      //换表 type = 3\r\n      checkAmmeterByStation({\r\n        id: that.ammeter.id,\r\n        type: 3,\r\n        electrotype: that.ammeter.electrotype,\r\n        stationcode: that.ammeter.stationcode,\r\n        ammeteruse: that.ammeter.ammeteruse,\r\n      }).then((res) => {\r\n        let code = res.data.code;\r\n        if (code == \"error\") {\r\n          this.errorMessage = res.data.msg;\r\n          if (\r\n            that.isoldcheckStation == null &&\r\n            that.ammeter.stationtype == 10002 &&\r\n            res.data.flag5\r\n          ) {\r\n            //编辑数据时判断是否选择关联局站，没有关联弹出是否室分\r\n            that.modal1 = true;\r\n          } else {\r\n            that.$Modal.warning({ title: \"温馨提示\", content: res.data.msg });\r\n            that.loading = false;\r\n          }\r\n        } else {\r\n          that.checkOther(type); //保存数据\r\n        }\r\n      });\r\n    },\r\n    checkOther(type) {\r\n      let that = this;\r\n      // if(that.ammeter.ammetername != undefined || that.ammeter.ammetername != null){\r\n      //     checkAmmeterExist(that.ammeter.id, that.ammeter.ammetername,0).then(res => {//验证电表是否存在\r\n      //         let code = res.data.code;\r\n      //         if (code == 0) {\r\n      //             that.checkedFiles(type);\r\n      //         }else{\r\n      //             that.loading = false;\r\n      //         }\r\n      //     });\r\n      // }else{\r\n      that.checkedFiles(type);\r\n      // }\r\n    },\r\n    checkedFiles(type) {\r\n      let that = this;\r\n      that.ammeter.type = 0;\r\n      if (that.attachData.length != 0) {\r\n        that.ammeter.isAttach = 1;\r\n      } else {\r\n        that.ammeter.isAttach = 0;\r\n      }\r\n      that.saveData(type); //保存数据\r\n      // if (that.attachData.length != 0 && that.multiFiles.length != 0) {\r\n      //     if(that.upload() != false){\r\n      //         that.saveData(type);//保存数据\r\n      //     };\r\n      // }else{\r\n      //     that.saveData(type);//保存数据\r\n      // }\r\n    },\r\n    saveData(type) {\r\n      let that = this;\r\n      this.clearDataByCondition();\r\n      that.ammeter.category = 1; //电表\r\n      addAmmeter(that.ammeter)\r\n        .then((res) => {\r\n          if (res.data != null && res.data != -1 && res.data.success == \"1\") {\r\n            if (type == 1) {\r\n              that.startFlow(res.data);\r\n            } else {\r\n              this.closeTag({ route: this.$route });\r\n              that.warn();\r\n            }\r\n          } else {\r\n            that.loading = false;\r\n            that.$Notice.error({ title: \"提示\", desc: res.data.msg, duration: 10 });\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          that.loading = false;\r\n          console.log(err);\r\n        });\r\n    },\r\n    //根据条件判断数据是否该清除\r\n    clearDataByCondition() {\r\n      if (this.ammeter.property !== 2 && this.ammeter.property !== 4) {\r\n        //站址产权归属为铁塔 清除分割比例checkAmmeterByStation，是否铁塔按RRU包干\r\n        this.ammeter.percent = null;\r\n      }\r\n      if (this.ammeter.ammeteruse !== 3) {\r\n        //电表用途不是回收电费，清除父电表信息\r\n        this.ammeter.parentId = null;\r\n        this.ammeter.customerId = null;\r\n      }\r\n      if (this.ammeter.directsupplyflag != 1) {\r\n        //只有对外结算类型为直供电才填写该字段，转供电不需填写\r\n        this.ammeter.electrovalencenature = null;\r\n      }\r\n      if (!this.isCDCompany) {\r\n        //成都分公司显示合同对方等，不是，就清除数据\r\n        this.ammeter.contractOthPart = null;\r\n        this.ammeter.nmCcode = null;\r\n        this.ammeter.nmL2100 = null;\r\n        this.ammeter.nmL1800 = null;\r\n        this.ammeter.nmCl800m = null;\r\n      }\r\n    },\r\n    warn() {\r\n      this.$Modal.warning({\r\n        title: \"温馨提示\",\r\n        content: \"保存后的数据要提交审批才能生效！\",\r\n      });\r\n    },\r\n    refreshData() {\r\n      this.initData();\r\n    },\r\n    initData() {\r\n      this.countryName = \"\";\r\n      this.electro.data = [];\r\n      this.oldData = [];\r\n      this.isCityAdmin = false;\r\n      this.isAdmin = false;\r\n      this.isEditByCountry = false;\r\n      this.$nextTick(() => {\r\n        this.$refs.ammeter.resetFields(); // this.$refs.adduserform.resetFields();\r\n        this.$refs.ammeter1.resetFields(); // this.$refs.adduserform.resetFields();\r\n        this.$refs.ammeter2.resetFields(); // this.$refs.adduserform.resetFields();\r\n      });\r\n      this.showModel = false;\r\n      this.electricTypeModel = false;\r\n    },\r\n    onModalCancel() {\r\n      this.initData();\r\n    },\r\n    /*初始化*/\r\n    initAmmeter(id) {\r\n      this.oldAmmeterId = id;\r\n      this.initData();\r\n      let that = this;\r\n      if (id != undefined) {\r\n        this.title = \"修改电表\";\r\n        this.isEditByCountry = true;\r\n        //获取上一次修改历史\r\n        editAmmeterRecord({ id: id, type: 1 }).then((res) => {\r\n          if (res.data.id != undefined && res.data.id != null) {\r\n            if (null != res.data.maxdegree) {\r\n              res.data.maxdegree = parseFloat(res.data.maxdegree);\r\n            }\r\n            this.setAmmeter(Object.assign({}, res.data));\r\n            getChangeAmmeterid(\"\", 1).then((res) => {\r\n              that.ammeter.id = res.data.id;\r\n              that.fileParam.busiId = res.data.id;\r\n            });\r\n            this.listElectricTypeRatio(id, res.data.id, res.data.stationcode);\r\n            that.ammeter.id = null;\r\n            that.ammeter.oldAmmeterId = id;\r\n            that.ammeter.ammetername = null;\r\n            that.fileParam.busiId = id;\r\n            getClassificationId(this.ammeter.electrotype).then((res) => {\r\n              this.ammeter.classifications = res.data;\r\n            });\r\n            attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n              that.attachData = Object.assign([], res.data.rows);\r\n            });\r\n          } else {\r\n            editAmmeter(id).then((res1) => {\r\n              if (null != res1.data.maxdegree) {\r\n                res1.data.maxdegree = parseFloat(res1.data.maxdegree);\r\n              }\r\n              this.setAmmeter(res1.data);\r\n              this.listElectricTypeRatio(id, null, res1.data.stationcode);\r\n              that.ammeter.oldAmmeterId = id;\r\n              that.ammeter.ammetername = null;\r\n              that.ammeter.id = null;\r\n              that.fileParam.busiId = id;\r\n              getClassificationId(this.ammeter.electrotype).then((res) => {\r\n                this.ammeter.classifications = res.data;\r\n              });\r\n              attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n                that.attachData = Object.assign([], res.data.rows);\r\n              });\r\n            });\r\n          }\r\n        });\r\n        this.getUser();\r\n      } else {\r\n        this.title = \"添加电表\";\r\n        editAmmeter(\"\", 0).then((res) => {\r\n          this.setAmmeter(Object.assign({}, res.data));\r\n          this.getUser();\r\n        });\r\n      }\r\n      getClassification().then((res) => {\r\n        //用电类型\r\n        this.classificationData = res.data;\r\n      });\r\n    },\r\n    listElectricTypeRatio(id, recordId, stationcode) {\r\n      listElectricTypeRatio({ ammeterId: id, ammeterRecordId: recordId }).then((res) => {\r\n        res.data.rows.forEach((item) => {\r\n          if (item.stationId == null || item.stationId == undefined) {\r\n            item.stationId = null;\r\n            item.stationName = null;\r\n          } else if (item.stationId == stationcode) {\r\n            item._disabled = true;\r\n          }\r\n        });\r\n        this.electro.data = Object.assign([], res.data.rows);\r\n      });\r\n    },\r\n    changeStatus() {\r\n      if (this.ammeter.status == 1) {\r\n        this.isRequireFlag = true;\r\n        this.ruleValidate.stationName = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      } else {\r\n        this.isRequireFlag = false;\r\n        this.ruleValidate.stationName = [\r\n          { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n    },\r\n    selectChange() {\r\n      if (this.ammeter.company != undefined) {\r\n        if (this.ammeter.company == \"1000085\") {\r\n          this.isCDCompany = true;\r\n        } else {\r\n          this.isCDCompany = false;\r\n        }\r\n        getCountryByUserId(this.ammeter.company).then((res) => {\r\n          this.departments = res.data.departments;\r\n          this.ammeter.country = res.data.departments[0].id;\r\n          this.ammeter.countryName = this.departments[0].name;\r\n        });\r\n      }\r\n    },\r\n    getUser() {\r\n      let that = this;\r\n      getUserByUserRole().then((res) => {\r\n        //当前登录用户所在公司\r\n        that.companies = res.data.companies;\r\n        that.isCityAdmin = res.data.isEditAdmin;\r\n        if (\r\n          res.data.isCityAdmin == true ||\r\n          res.data.isProAdmin == true ||\r\n          res.data.isSubAdmin == true\r\n        ) {\r\n          that.isAdmin = true;\r\n        }\r\n        getCountrysdata({ orgCode: res.data.companies[0].id }).then((res) => {\r\n          //根据权限获取所属部门\r\n          that.departments = res.data;\r\n        });\r\n      });\r\n    },\r\n    setOldData(data) {\r\n      this.oldCategory = btext(\"ammeterCategory\", data.category, \"typeCode\", \"typeName\");\r\n      this.oldPackagetype = btext(\r\n        \"packageType\",\r\n        data.packagetype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldPayperiod = btext(\"payPeriod\", data.payperiod, \"typeCode\", \"typeName\");\r\n      this.oldPaytype = btext(\"payType\", data.paytype, \"typeCode\", \"typeName\");\r\n      this.oldElectronature = btext(\r\n        \"electroNature\",\r\n        data.electronature,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldElectrovalencenature = btext(\r\n        \"electrovalenceNature\",\r\n        data.electrovalencenature,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldElectrotype = btext(\r\n        \"electroType\",\r\n        data.electrotype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldStatus = btext(\"status\", data.status, \"typeCode\", \"typeName\");\r\n      this.oldProperty = btext(\"property\", data.property, \"typeCode\", \"typeName\");\r\n      this.oldAmmetertype = btext(\r\n        \"ammeterType\",\r\n        data.ammetertype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldStationstatus = btext(\r\n        \"stationStatus\",\r\n        data.stationstatus,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldStationtype = btext(\r\n        \"BUR_STAND_TYPE\",\r\n        data.stationtype,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldDirectsupplyflag = btext(\r\n        \"directSupplyFlag\",\r\n        data.directsupplyflag,\r\n        \"typeCode\",\r\n        \"typeName\"\r\n      );\r\n      this.oldAmmeteruse = btext(\"ammeterUse\", data.ammeteruse, \"typeCode\", \"typeName\");\r\n    },\r\n\r\n    setAmmeter(form) {\r\n      //换表初始化数据开始-----\r\n      if (this.configVersion == \"ln\" || this.configVersion == \"LN\") {\r\n        form.supplybureauammetercode = null;\r\n      }\r\n      if (form.category === 1) {\r\n        form.oldAmmeterName = form.ammetername;\r\n      } else {\r\n        form.oldAmmeterName = form.protocolname;\r\n      }\r\n      form.ischangeammeter = \"1\";\r\n      form.oldBillPower = \"\";\r\n      //换表初始化数据结束-----\r\n      if (form.status == null || form.status === 1) {\r\n        form.status = 1;\r\n        this.isRequireFlag = true;\r\n        this.ruleValidate.stationName = [\r\n          { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      } else {\r\n        this.isRequireFlag = false;\r\n        this.ruleValidate.stationName = [\r\n          { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n        ];\r\n      }\r\n      if (form.electrovalencenature != 1 && form.electrovalencenature != 2) {\r\n        form.electrovalencenature = null;\r\n      }\r\n      // form.price = form.price\r\n      // debugger\r\n      form.issmartammeter = form.issmartammeter == null ? \"0\" : form.issmartammeter + \"\";\r\n      form.isentityammeter =\r\n        form.isentityammeter == null ? null : form.isentityammeter + \"\";\r\n      form.isairconditioning =\r\n        form.isairconditioning == null ? \"0\" : form.isairconditioning + \"\";\r\n      form.islumpsum = form.islumpsum == null ? \"0\" : form.islumpsum + \"\";\r\n      form.iszgz = form.iszgz == null ? \"0\" : form.iszgz + \"\";\r\n      if (form.iszgz == \"1\") this.disablediszgz = true;\r\n      this.ammeter = form;\r\n      let electrotype = this.ammeter.electrotype;\r\n      if (\r\n        electrotype === 111 ||\r\n        electrotype === 112 ||\r\n        electrotype === 113 ||\r\n        electrotype === 2\r\n      ) {\r\n        this.isClassification = true;\r\n      }\r\n      if (\r\n        (electrotype != null && electrotype !== 1411 && electrotype !== 1412) ||\r\n        this.ammeter.property !== 2\r\n      ) {\r\n        this.propertyReadonly = false;\r\n      }\r\n      if (this.ammeter.magnification == null) {\r\n        this.ammeter.magnification = 1;\r\n      }\r\n      if (this.ammeter.company != null) {\r\n        this.ammeter.company = this.ammeter.company + \"\";\r\n        if (this.ammeter.company == \"1000085\") {\r\n          this.isCDCompany = true;\r\n        }\r\n      }\r\n      if (this.ammeter.processinstId != null) {\r\n        this.isShowFlow = true;\r\n      }\r\n      this.flowName = this.ammeter.projectname; //用于提交流程使用原项目名称\r\n      this.showModel = true;\r\n    },\r\n\r\n    //修改电表、协议的用电类型时，如用电类型不再与原先选择的局站的局站类型匹配时，系统自动清空原关联局站，需用户重新再关联局站。\r\n    changeClassifications(value) {\r\n      this.isClassification = false;\r\n      if (value.length == 0) {\r\n        this.clearStation();\r\n        this.ammeter.property = null;\r\n        this.propertyReadonly = true;\r\n      } else {\r\n        this.propertyReadonly = false;\r\n        this.ammeter.electrotype = value[value.length - 1];\r\n        let electrotype = this.ammeter.electrotype;\r\n        if (electrotype === 1411 || electrotype === 1412) {\r\n          //控制产权归属\r\n          this.ammeter.property = 2;\r\n          this.propertyReadonly = true;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else if (electrotype === 1421 || electrotype === 1422) {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = 4;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else if (electrotype === 1431 || electrotype === 1432) {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = 1;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: true, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        } else {\r\n          this.propertyReadonly = false;\r\n          this.ammeter.property = null;\r\n          this.ruleValidate.supplybureauammetercode = [\r\n            { required: false, message: \"不能为空\", trigger: \"blur\" },\r\n          ];\r\n        }\r\n        // checkClassificationLevel(this.ammeter.electrotype).then(res => {\r\n        //     let code = res.data.msg;\r\n        //     if (code !== '1') {\r\n        let stationtype = this.ammeter.stationtype;\r\n        if (electrotype === 111 || electrotype === 112 || electrotype === 113) {\r\n          this.isClassification = true;\r\n          if (stationtype !== 10001) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 121 || electrotype === 112) {\r\n          if (stationtype !== 10003 && stationtype !== 10004) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 131 || electrotype === 132 || electrotype === 133) {\r\n          if (stationtype !== 10005) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 1411 || electrotype === 1412) {\r\n          if (\r\n            stationtype !== 10002 ||\r\n            (stationtype == 10002 && this.propertyright !== 3)\r\n          ) {\r\n            this.clearStation();\r\n          }\r\n        } else if (\r\n          electrotype === 1421 ||\r\n          electrotype === 1422 ||\r\n          electrotype === 1431 ||\r\n          electrotype === 1432\r\n        ) {\r\n          if (stationtype !== 10002) {\r\n            this.clearStation();\r\n          }\r\n        } else if (electrotype === 2) {\r\n          this.isClassification = true;\r\n          //     if(stationtype !== 20001){ this.clearStation();}\r\n          // }else if(electrotype === 31 || electrotype === 32 || electrotype === 33){\r\n          //     if(stationtype !== 20002 || stationtype !== -2){ this.clearStation();}\r\n          // }else if(electrotype === 4){\r\n          //     if(stationtype !== -1 || stationtype !== -2){ this.clearStation();}\r\n        }\r\n        //     }\r\n        // });\r\n        if (this.configVersion == \"sc\" && this.ammeter.stationaddresscode) {\r\n          //“51”开头铁塔站址编码控制\r\n          if (\r\n            [1411, 1412].includes(electrotype) &&\r\n            !this.ammeter.stationaddresscode.startsWith(\"51\")\r\n          ) {\r\n            this.clearStation();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    setAttachData(data) {\r\n      this.multiFiles = data.data;\r\n      this.removeIds = data.ids;\r\n      if (this.removeIds.length != 0 && data.type == \"remove\") {\r\n        this.removeAttach();\r\n      } else {\r\n        this.upload();\r\n      }\r\n    },\r\n    upload() {\r\n      if (this.attachData.length != 0 && this.multiFiles.length != 0) {\r\n        // this.$Message.info(\"提示:上传文件过大可能导致上传失败！\");\r\n        this.loading = true;\r\n        axios\r\n          .request({\r\n            url: \"/common/attachments/uploadMultiFile\",\r\n            method: \"post\",\r\n            data: this.multiFiles,\r\n          })\r\n          .then((res) => {\r\n            if (res.data.code != 0) {\r\n              this.loading = false;\r\n            }\r\n            let that = this;\r\n            attchList({ busiId: that.fileParam.busiId }).then((res) => {\r\n              that.attachData = Object.assign([], res.data.rows);\r\n            });\r\n          });\r\n      }\r\n    },\r\n    removeAttach() {\r\n      removeAttach({ ids: this.removeIds.join() }).then(() => {});\r\n    },\r\n    clearStation() {\r\n      //清除局站信息\r\n      this.ammeter.stationName = null;\r\n      this.ammeter.stationcode = null;\r\n      this.ammeter.stationstatus = null;\r\n      this.ammeter.stationtype = null;\r\n      this.ammeter.stationaddress = null;\r\n      this.ammeter.stationaddresscode = null;\r\n    },\r\n    //选择所属部门开始\r\n    chooseResponseCenter(index, params, electroRowNum) {\r\n      this.chooseIndex = index;\r\n      this.electroRowNum = electroRowNum;\r\n      if (index == 1 || index == 2) {\r\n        let types = this.ammeter.classifications;\r\n        if (types.length == 0) {\r\n          this.$Modal.warning({ title: \"温馨提示\", content: \"请先选择用电类型！\" });\r\n          return;\r\n        } else if (this.ammeter.ammeteruse == null) {\r\n          this.$Modal.warning({ title: \"温馨提示\", content: \"请先选择电表用途！\" });\r\n          return;\r\n        } else {\r\n          if (this.ammeter.company == null) {\r\n            this.$Message.info(\"请先选择分公司\");\r\n            return;\r\n          }\r\n          this.ammeter.electrotype = types[types.length - 1];\r\n          // if(this.configVersion=='ln' || this.configVersion =='LN'){\r\n          //     this.$refs.stationModalLN.initDataList(this.ammeter.electrotype,0,this.ammeter.ammeteruse,params);//局站\r\n          // }else{\r\n          //换表 type = 3\r\n          this.$refs.stationModal.ammeterid = this.ammeter.id;\r\n          this.$refs.stationModal.initDataList(\r\n            this.ammeter.electrotype,\r\n            3,\r\n            this.ammeter.ammeteruse,\r\n            this.ammeter.company,\r\n            params\r\n          ); //局站\r\n          // }\r\n        }\r\n      } else {\r\n        if (this.ammeter.company == null) {\r\n          this.$Message.info(\"请先选择分公司\");\r\n          return;\r\n        }\r\n        this.$refs.countryModal.choose(this.ammeter.company); //所属部门\r\n      }\r\n    },\r\n    getDataFromModal(data, flag) {\r\n      this.ammeter.country = data.id;\r\n      this.ammeter.countryName = data.name;\r\n      //this.chooseResponseCenter(4, data);\r\n      //选择所属部门结束\r\n    },\r\n    //获取局站数据\r\n    getDataFromStationModal(data, flag) {\r\n      this.ischeckStation = flag;\r\n      this.isoldcheckStation = flag;\r\n      if (this.chooseIndex == 2) {\r\n        this.electro.data[this.electroRowNum].stationId = data.id;\r\n        this.electro.data[this.electroRowNum].stationName = data.stationname;\r\n      } else {\r\n        this.propertyright = data.propertyright;\r\n        this.ammeter.stationName = data.stationname;\r\n        this.ammeter.stationcode = data.id;\r\n        this.ammeter.stationstatus = Number(\r\n          data.status == undefined ? data.STATUS : data.status\r\n        );\r\n        this.ammeter.stationtype = Number(data.stationtype);\r\n        this.ammeter.stationaddress = data.address;\r\n        // if (data.stationtype == 10002 && data.propertyright == 3) {//只有当局站类型为‘生产用房-移动基站’且产权为‘租用’时，存放站址编码\r\n        this.ammeter.stationaddresscode = data.resstationcode;\r\n        // }\r\n        //默认生成一条关联用电类型\r\n        let that = this;\r\n        listElectricType({ id: data.stationtype }).then((res) => {\r\n          let result = that.electro.data;\r\n          let electroData = Object.assign([], res.data.rows);\r\n          let count = 0;\r\n          if (result.length == 0) {\r\n            count++;\r\n          } else {\r\n            result.forEach((item) => {\r\n              electroData.forEach((item1) => {\r\n                if (item.id === item1.id) {\r\n                  electroData[0].stationId = data.id;\r\n                  electroData[0].stationName = data.stationname;\r\n                  electroData[0]._disabled = true;\r\n                  let index = result.indexOf(item);\r\n                  result.splice(index, 1);\r\n                } else {\r\n                  count++;\r\n                }\r\n              });\r\n            });\r\n          }\r\n          if (count > 0) {\r\n            that.electro.data = Object.assign([], res.data.rows);\r\n            that.electro.data[0].stationId = data.id;\r\n            that.electro.data[0].stationName = data.stationname;\r\n            that.electro.data[0]._disabled = true;\r\n          } else {\r\n            result.unshift(electroData[0]);\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    /*添加电表关联用电类型比率*/\r\n    addElectricType() {\r\n      this.$refs.selectElectricType.initElectricType();\r\n    },\r\n\r\n    /*移除选中的用电类型比率*/\r\n    removeElectricType() {\r\n      let rows = this.$refs.ammeterTable.getSelection();\r\n      let datas = this.electro.data;\r\n      rows.forEach((item) => {\r\n        if (item._index != undefined) {\r\n          datas.splice(item._index, 1);\r\n        } else {\r\n          datas.forEach((data) => {\r\n            if (data.id === item.id) {\r\n              let index = datas.indexOf(data);\r\n              datas.splice(index, 1);\r\n            }\r\n          });\r\n        }\r\n      });\r\n      this.electro.data = datas;\r\n    },\r\n\r\n    /* 设置用电类型列表*/\r\n    setElectricData: function (data) {\r\n      let origin = this.electro.data;\r\n      if (origin.length < 1) {\r\n        this.electro.data = data;\r\n      } else {\r\n        let tem = data;\r\n        for (let j = 0; j < origin.length; j++) {\r\n          for (let i = 0; i < data.length; i++) {\r\n            let typeId =\r\n              origin[j].electroTypeId != undefined\r\n                ? origin[j].electroTypeId\r\n                : origin[j].id;\r\n            if (data[i].id === typeId) {\r\n              tem.splice(tem.indexOf(data[i]), 1);\r\n            }\r\n          }\r\n        }\r\n        this.electro.data = this.electro.data.concat(tem);\r\n      }\r\n    },\r\n\r\n    //用电类型比例校验\r\n    checkElectricTypeItem() {\r\n      let items = this.electro.data;\r\n      //当“用电类型”选择“111 A类机楼（机房），112 B类机楼（机房），113 C类机楼（机房） ”或“2 管理办公用电”时，才需填用电类型分比且必填，用电类型比例之和必须等于100%\r\n      if (\r\n        this.ammeter.electrotype === 111 ||\r\n        this.ammeter.electrotype === 112 ||\r\n        this.ammeter.electrotype === 113 ||\r\n        this.ammeter.electrotype === 2\r\n      ) {\r\n        let sumRatio = items.reduce((total, item) => {\r\n          return total + item.ratio;\r\n        }, 0);\r\n        if (sumRatio !== 100) {\r\n          this.$Modal.warning({\r\n            title: \"温馨提示\",\r\n            content: \"用电类型所占比例和必须为100%，当前值为\" + sumRatio + \"%\",\r\n          });\r\n          this.loading = false;\r\n          return false;\r\n        }\r\n      }\r\n      return true;\r\n    },\r\n    showFlow() {\r\n      this.showWorkFlow = true;\r\n      let that = this;\r\n      this.hisParams = {\r\n        busiId: that.ammeter.id,\r\n        busiType: that.ammeter.busiAlias,\r\n        procInstId: that.ammeter.processinstId,\r\n      };\r\n    },\r\n    startFlow(data) {\r\n      let busiTitle = \"电表换表(\" + data.projectname + \")审批\";\r\n      this.workFlowParams = {\r\n        busiId: data.id,\r\n        busiAlias: \"AMM_SWITCH_AMM\",\r\n        busiTitle: busiTitle,\r\n      };\r\n      let that = this;\r\n      setTimeout(function () {\r\n        that.$refs.clwfbtn.onClick();\r\n      }, 200);\r\n    },\r\n    doWorkFlow(data) {\r\n      //流程回调\r\n      this.closeTag({ route: this.$route });\r\n      if (data == 0) {\r\n        this.warn();\r\n      }\r\n    },\r\n    /*选择电表/协议*/\r\n    addAmmeterProtocol() {\r\n      this.$refs.selectAmmeterProtocol.initDataList(1, this.ammeter.id);\r\n    },\r\n    /* 选择电表户号/协议编号*/\r\n    setAmmeterProrocolData: function (data) {\r\n      this.ammeter.parentId = data.id;\r\n      if (data.protocolname != null && data.protocolname.length != 0) {\r\n        this.ammeter.parentCode = data.protocolname;\r\n      } else {\r\n        this.ammeter.parentCode = data.ammetername;\r\n      }\r\n    },\r\n    /*选择客户*/\r\n    addCustomer() {\r\n      this.$refs.customerList.choose(2); //打开模态框\r\n    },\r\n    getDataFromCustomerModal: function (data) {\r\n      this.ammeter.customerId = data.id;\r\n      this.ammeter.customerName = data.name;\r\n    },\r\n    iszgzchange() {\r\n      if (this.ammeter.iszgz == \"1\") {\r\n        this.ammeter.directsupplyflag = 1;\r\n        this.ammeter.oldammetername = this.ammeter.oldAmmeterName;\r\n        this.iszgzOnly = true;\r\n      } else {\r\n        this.iszgzOnly = false;\r\n      }\r\n    },\r\n    chooseoldammetername() {\r\n      if (this.disablediszgz) return;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.status = 0;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.ammeteruse = 1;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.type = 3;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.company = this.ammeter.company;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.country = this.ammeter.country;\r\n      this.$refs.chooseAmmeterModel.modal.queryparams.directsupplyflag = 2;\r\n      this.$refs.chooseAmmeterModel.modal.show = true;\r\n      this.$Message.info(\"双击选择！！\");\r\n    },\r\n    getAmmeterModelModal(data) {\r\n      this.ammeter.oldammetername = data.name + \",\" + data.id;\r\n    },\r\n    projectNameChange(val) {\r\n      // var patt=/^([^\\u0000-\\u00ff]+路)([^\\u0000-\\u00ff]*)([0-9]*号)([^\\u0000-\\u00ff]+楼电表)$/;\r\n      if (\r\n        !/^.*([^\\u0000-\\u00ff]+路).*$/.test(val) &&\r\n        !/^.*([^\\u0000-\\u00ff]*)([0-9]*号).*$/.test(val) &&\r\n        !/^.*([^\\u0000-\\u00ff]+楼电表).*$/.test(val)\r\n      ) {\r\n        this.$Message.info(\"温馨提示：集团要求格式为(**路**号**楼电表)\");\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    //直接从前台取\r\n    this.categorys = {\r\n      directsupplyflag: blist(\"directSupplyFlag\"),\r\n    };\r\n    this.propertyList = blist(\"property\");\r\n    this.initAmmeter(this.$route.query.id);\r\n\r\n    this.configVersion = this.$config.version;\r\n    if (this.configVersion != \"ln\" && this.configVersion != \"LN\") {\r\n      this.ruleValidate.ammetername.push({\r\n        required: true,\r\n        message: \"不能为空\",\r\n        trigger: \"blur\",\r\n      });\r\n      this.ruleValidate.customerName = {\r\n        required: true,\r\n        message: \"不能为空\",\r\n        trigger: \"blur\",\r\n      };\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.margin-right-width {\r\n  margin-right: 10px;\r\n}\r\n.testaa .ivu-row {\r\n  margin-left: 5px;\r\n  margin-right: 5px;\r\n}\r\n.testaa .requireStar .ivu-form-item-label:before {\r\n  content: \"*\";\r\n  display: inline-block;\r\n  margin-right: 4px;\r\n  line-height: 1;\r\n  font-family: SimSun;\r\n  font-size: 12px;\r\n  color: #ed4014;\r\n}\r\n</style>\r\n"]}]}