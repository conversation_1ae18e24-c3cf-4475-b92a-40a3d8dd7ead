const path = require('path')
const resolve = (dir) => {
  return path.join(__dirname, dir)
}
// 项目部署基础
// 默认情况下，我们假设你的应用将被部署在域的根目录下,
// 例如：https://www.my-app.com/dev-
// 默认：'/'
// 如果您的应用程序部署在子路径中，则需要在这指定子路径
// 例如：https://www.foobar.com/my-app/
// 需要将它改为'/my-app/'
const BASE_URL = process.env.NODE_ENV === 'production' ? '/' : '/'
module.exports = {
  publicPath: BASE_URL, // baseUrl  publicPath
  // webpack 配置.
  // 具体属性查看https://github.com/vuejs/vue-cli/blob/dev/docs/webpack.md
  // 如果你不需要使用eslint，把lintOnSave设为false即可
  lintOnSave: true,
  configureWebpack: (config) => {
    if (process.env.NODE_ENV === 'production') {
      config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true
      config.optimization.minimizer[0].options.terserOptions.compress.drop_debugger = true
    }
  },
  chainWebpack: (config) => {
    (config.entry.app = ['babel-polyfill', resolve('src/main.js')])
    config.resolve.alias
      .set('@', resolve('src')) // key,value自行定义，比如.set('@@', resolve('src/components'))
      .set('_c', resolve('src/components'))
      .set('_a', resolve('src/assets'))
  },
  // 设为false打包时不生成.map文件
  productionSourceMap: process.env.NODE_ENV === 'dev',
  // 它支持webPack-dev-server的所有选项
  devServer: {
    port: 8082,
    open: true,
    proxy: {
      '/energy-cost/': {
        // 必须和后台保持一致，不然sessionid会变
        // target:'http://scnh.paas.sc.ctc.com:80',
        // target:'http://136.96.135.20:80',
        // target: "http://127.0.0.1:8098",
        // target: 'http://172.16.47.127:80',

        // target: 'http://10.206.19.177:8081',
        // target: 'http://10.206.23.226:8098',//测试
        // target: 'http://136.96.228.54:18088',//信创
        // target: "http://10.206.14.150:8080", //李燕

        target: 'http://127.0.0.1:8080',//dai
        // target: 'http://10.206.14.73:8082',//chen1
        // target: 'http://10.206.14.165:8082',//chen2
        // target: 'http://10.206.20.36:8004',//ceshi
        logLevel: 'debug',
        secure: false,
        // pathRewrite: {'^/sccl-web': ''},//如果后台没设置basedir，替换为空
        // pathRewrite: {'^/energy-cost-new': '/energy-cost'},//如果后台没设置basedir，替换为空
        changeOrigin: true
      }
    }
  }
}
